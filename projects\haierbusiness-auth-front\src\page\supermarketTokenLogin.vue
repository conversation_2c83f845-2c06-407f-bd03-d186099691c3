<script setup lang="ts">
import { loginApi } from '@haierbusiness-front/apis';
import { PropType, ref } from 'vue';

import { message } from 'ant-design-vue';
import { ILoginResult, ILoginSearchParams } from '@haierbusiness-front/common-libs';


const props = defineProps({
    param: Object as PropType<ILoginSearchParams>
});

const emit = defineEmits<{
    (e: 'loginSuccess', result: ILoginResult): void
}>()

const loginSuccess = (result: ILoginResult) => {
    emit('loginSuccess', result)
};

const loading = ref(false);

(() => {
    const token = props.param?.urlSearch.get("token");
    if (!token) {
        message.error("token不能为空!")
        return;
    }

    loading.value = true;
    loginApi.supermarketTokenLogin({
      token: token
    }).then(it => {
        loginSuccess({ data: it })
    }).finally(() => {
        loading.value = false;
    })
})()
</script>

<template></template>
