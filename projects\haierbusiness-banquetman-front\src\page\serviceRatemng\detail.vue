<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import { serviceRatemngApi } from '@haierbusiness-front/apis';
import {
  BApplyListRecord,
  BApplyPerson,
  BanquetStatusEnum
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';

import router from '../../router'
// const router = useRouter()
const store = applicationStore();

const currentRouter = ref()
const route = ref(getCurrentRoute());
const id = route.value?.query?.id;
const detail = ref<any>();

const { loginUser } = storeToRefs(store);

const getDetail = (id: number) => {

  serviceRatemngApi.get(id).then((res) => {
    res.accountingVoucher = JSON.parse(res.accountingVoucher)
    detail.value = res;
  });
};

const showMore = ref(false)


onMounted(async () => {
  currentRouter.value = await router
})

const getInnerPerson = (list?:Array<BApplyPerson>) => {
  if (list && list.length > 0) {
   let resultList = list.filter(item => item.haierUser == true)
   return resultList.map(item => `${item.userName}(${item.userCode})`).join(',')
  }else {
     return ''
  }

}

const getOuterPerson = (list?:Array<BApplyPerson>) => {
  if (list && list.length > 0) {
    let resultList = list.filter(item => !item.haierUser)
    return resultList.map(item => item.userName).join(',')
  }else {
    return ''
  }
}

const downloadFile2 = (url:string, name:string) => {
      var xhr = new XMLHttpRequest();
      xhr.open('GET', url, true);
      xhr.responseType = 'blob';

      xhr.onload = function() {
        if (xhr.status === 200) {
          var blob = xhr.response;
          var a = document.createElement('a');
          var url = URL.createObjectURL(blob);
          a.href = url;
          a.download = name;
          a.click();
          URL.revokeObjectURL(url);
        }
      };

      xhr.send();
    }

const downLoadFile = (url:string, name:string) => {
  window.open(url)
  // downloadFile2(url,name)
}

watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);




</script>

<template>
  <div>
    <h-row justify="space-between" style="padding: 20px;">
      <h-col style="font-size: 18px;">服务费用归账详情</h-col>
      <h-col><h-button type="link" @click="currentRouter.back(-1)">返回</h-button></h-col>
    </h-row>
    <div style="background-color: #ffff;height: 100%;width: 100%;padding: 30px 60px;overflow: auto;">
      <h-descriptions title="订单信息" style="margin-bottom: 20px;" bordered>
        <h-descriptions-item label="账单编号">{{ detail?.statementCode }}</h-descriptions-item>
        <h-descriptions-item label="操作人">{{ detail?.handerName }}({{detail?.handerCode}})</h-descriptions-item>
        <h-descriptions-item label="操作时间">{{ detail?.updateTime }}</h-descriptions-item>
        <h-descriptions-item label="归账金额">{{ `${detail?.settleAmount}` }}</h-descriptions-item>
        <h-descriptions-item label="归账月份">{{ detail?.settleMonth }}</h-descriptions-item>
        <h-descriptions-item label="归账凭证">
          <div v-for="item in detail?.accountingVoucher" style="color: #2870ff; cursor: pointer;" @click="downLoadFile(item.filePath, item.fileName)">{{ item.fileName }}</div>
          </h-descriptions-item>
        <h-descriptions-item label="备注信息" :span="24">{{ detail?.remark }}</h-descriptions-item>
      </h-descriptions>

      <h-descriptions title="审批信息" style="margin-bottom: 20px;" bordered>
        <h-descriptions-item label="订单状态">{{detail?.orderStatus==1?"待审批":detail?.orderStatus==2?"已审批":"已取消"}}</h-descriptions-item>
        <h-descriptions-item label="审批人">{{ detail?.approverName }}</h-descriptions-item>
        <h-descriptions-item label="审批时间">{{detail?.approveTime}}</h-descriptions-item>
        <h-descriptions-item label="审批意见" :span="24">{{detail?.approveComment}}</h-descriptions-item>

      </h-descriptions>

      
    </div>


  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}
:deep(.ant-descriptions-item-label) {
  width: 200px;
}
:deep(.ant-descriptions-item-content) {
  width: 300px;
}
</style>
