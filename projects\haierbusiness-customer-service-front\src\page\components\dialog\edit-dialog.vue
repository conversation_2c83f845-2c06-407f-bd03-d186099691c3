<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  Switch as hSwitch,
  CheckboxGroup as hCheckboxGroup,
  Checkbox as hCheckbox,
  Image as hImage,
  message,
} from 'ant-design-vue';
import {
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
  MinusCircleOutlined,
  PlusCircleOutlined
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { computed, ref, watch } from 'vue';
import { workOrderApi } from '@haierbusiness-front/apis';
import type { Ref } from 'vue';
import { workOrderQuery } from '@haierbusiness-front/common-libs';
// import { de } from 'element-plus/es/locale';
// import { Codemirror } from 'vue-codemirror';
// import { javascript } from '@codemirror/lang-javascript';
// import { oneDark } from '@codemirror/theme-one-dark';
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil';
import { HeaderConstant } from '@haierbusiness-front/common-libs';
import workOrderDetail from "./workOrderDetail.vue"
// import Echarts from "./echarts.vue"
import type { UploadChangeParam, UploadFile } from 'ant-design-vue';
// const extensions = [javascript(), oneDark];
const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false);

interface Props {
  show: boolean;
  data: workOrderQuery;
  knowCenterOptions: any;
  id:any
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  id:null
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: workOrderQuery = {
  mobile: '',
  workName: '',
  userName:"",
  userNum:"",
  personName: '',
  personNum: '',
  businessNum: [""],
};

const rules: Record<string, Rule[]> = {
  mobile: [{ required: false, message: '请输入来电电话' }],
  workName: [{ required: true, message: '请输入工单名称' }],
  businessNum: [{ required: false, message: '请输入业务单号' }]
};

const addOne = () =>{
  indexData.value.businessNum.push("")
}

const del = (index:number) =>{
  indexData.value.businessNum.splice(index,1)
}

if(props.data?.businessNum){
  props.data.businessNum = props.data.businessNum.split(",")
}

const indexData: Ref<workOrderQuery> = ref(props.data ? props.data : defaultData);

watch(props, (newValue) => {
  indexData.value = ({ ...newValue.data } as workOrderQuery) || defaultData;
});

const emit = defineEmits(['cancel', 'ok']);

const visible = computed(() => props.show);

const handleOk = () => {
  from.value
    .validate()
    .then(() => {
      confirmLoading.value = true;
      let obj = JSON.parse(JSON.stringify(indexData.value))
      obj.businessNum = obj.businessNum.join()
      workOrderApi.edit(obj).then((res: any) => {
        message.success(`操作成功`);
        emit('ok', res, () => {
          confirmLoading.value = false;
        });
        confirmLoading.value = false;
      })
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};
</script>

<template>
  <h-modal
    v-model:visible="visible"
    :title="indexData.id ? '编辑工单' : '新增工单'"
    :width="600"
    @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
  >
    <h-form ref="from" :model="indexData" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules">
      <h-form-item label="工单标题" name="workName">
        <h-input :maxlength="50" v-model:value="indexData.workName" style="width: 100%" />
      </h-form-item>
      <h-form-item label="业务单号" name="businessNum">
        <div style="margin-bottom:10px;" v-for="(item,index) in indexData.businessNum" :key="index">
          <h-input :maxlength="50" v-model:value="indexData.businessNum[index]" style="width: 85%" /><MinusCircleOutlined style="margin:0 10px" @click="del(index)"  v-if="indexData.businessNum.length!=1"/><PlusCircleOutlined  v-if="index==indexData.businessNum.length-1" @click="addOne" style="margin-left:10px;" />
        </div>
      </h-form-item>
      <h-form-item label="来电电话" name="mobile">
        <h-input :maxlength="50" v-model:value="indexData.mobile" style="width: 100%" />
      </h-form-item>
      <h-form-item label="来电人姓名" name="userName">
        <h-input :maxlength="50" v-model:value="indexData.userName" style="width: 100%" />
      </h-form-item>
      <h-form-item label="来电人工号" name="userNum">
        <h-input :maxlength="50" v-model:value="indexData.userNum" style="width: 100%" />
      </h-form-item>
    </h-form>
  </h-modal>
</template>

<style lang="less" scoped>
.important {
  color: red;
}
.imgItem {
  position: relative;
  display: inline-block;
  margin-right: 4px;
  .deleteIcon {
    position: absolute;
    right: 0px;
    z-index: 9999;
  }
}
</style>
