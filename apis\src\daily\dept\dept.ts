import {
    IDailyDeptDeleteRequestDTO,
    IDailyDeptListRequestDTO,
    IDailyDeptResponse,
    IDailyDeptSaveRequestDTO,
    IDailyDeptUpdateRequestDTO
} from '@haierbusiness-front/common-libs'
import {get, post} from '../../request'


export const dailyDeptApi = {

    list: (params: IDailyDeptListRequestDTO): Promise<IDailyDeptResponse[]> => {
        return get('/daily/api/daily-dept/list', params)
    },

    save: (params: IDailyDeptSaveRequestDTO): Promise<void> => {
        return post('/daily/api/daily-dept/save', params)
    },

    update: (params: IDailyDeptUpdateRequestDTO): Promise<void> => {
        return post('/daily/api/daily-dept/update', params)
    },

    delete: (params: IDailyDeptDeleteRequestDTO): Promise<void> => {
        return post('/daily/api/daily-dept/delete', params)
    },
}