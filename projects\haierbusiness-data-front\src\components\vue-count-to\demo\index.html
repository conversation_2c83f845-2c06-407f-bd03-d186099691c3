<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>vue-count-to</title>
  <link rel="stylesheet" href="./index.css">
</head>

<body>
  <script src='https://unpkg.com/vue@2.2.6'></script>
  <script src="../dist/vue-count-to.min.js"></script>
  <a href="https://github.com/PanJiaChen/vue-countTo" class="github-corner" aria-label="View source on Github">
    <svg width="80" height="80" viewBox="0 0 250 250" style="fill:#4AB7BD; color:#fff; position: absolute; top: 0; border: 0; right: 0;"
      aria-hidden="true">
      <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
      <path d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2"
        fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
      <path d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z"
        fill="currentColor" class="octo-body"></path>
    </svg>
  </a>
  <div id="app">
    <template>
      <h1>Vue CountTo</h1>
      <div class='container'>
        <div class="example-item">
          <h3> simple example</h3>
          <code>&lt;count-to :startVal=&#x27;0&#x27; :endVal=&#x27;2017&#x27; :duration=4000&gt;&lt;/count-to&gt;</code>
          <count-to ref='example1' class='example1' :start-val=startVal1 :end-val='endVal1' :duration=4000></count-to>
          <div class='example-btn' @click='start1'>start</div>
          <div class='example-btn' @click='changeExampleEndVal'>change end-val</div>
          <div class='example-btn' @click='incrementalUpdate'>incremental update</div>
        </div>
        <div class="example-item">
          <h3> count down example</h3>
          <code> &lt;count-to :startVal=&#x27;2017&#x27; :endVal=&#x27;0&#x27; :duration=8000&gt;&lt;/count-to&gt;</code>
          <count-to ref='example2' class='example2' :start-val='2017' :end-val=0 :duration=8000></count-to>
          <div class='example-btn' @click='start2'>start</div>
        </div>
        <div class="example-item">
          <h3> custom example</h3>
          <count-to ref='example3' class='example3' :start-val='_startVal' :end-val='_endVal' :duration='_duration' :decimals='_decimals'
            :separator='_separator' :prefix='_prefix' :suffix='_suffix' :autoplay=false>
          </count-to>
          <div>
            <label class="label" for="startValInput">startVal:  <input type="number" v-model.number='setStartVal' name='startValInput' /></label>
            <label class="label" for="endValInput">endVal:  <input type="number" v-model.number='setEndVal' name='endVaInput' /></label>
            <label class="label" for="durationInput">duration:  <input type="number" v-model.number='setDuration' name='durationInput' /></label>
            <div class="startBtn example-btn" @click='start3'>start</div>
            <div class="pause-resume-btn example-btn" @click='pauseResume'>pause/resume</div>
            <br/>
            <label class="label" for="decimalsInput">decimals:  <input type="number" v-model.number='setDecimals' name='decimalsInput' /></label>
            <label class="label" for="separatorInput">separator:  <input v-model='setSeparator' name='separatorInput' /></label>
            <label class="label" for="prefixInput">prefix: <input v-model='setPrefix' name='prefixInput' /></label>
            <label class="label" for="suffixInput">suffix: <input v-model='setSuffix' name='suffixInput' /></label>
          </div>
          <code>&lt;count-to  :start-val=&#x27;{{_startVal}}&#x27; :end-val=&#x27;{{_endVal}}&#x27; :duration=&#x27;{{_duration}}&#x27; :decimals=&#x27;{{_decimals}}&#x27;
            :separator=&#x27;{{_separator}}&#x27; :prefix=&#x27;{{_prefix}}&#x27; :suffix=&#x27;{{_suffix}}&#x27; :autoplay=false&gt;</code>
        </div>
      </div>
    </template>
  </div>
  <script>
    var Main = {
      name: 'test',
      data() {
        return {
          setStartVal: 0,
          setEndVal: 2017,
          setDuration: 4000,
          setDecimals: 0,
          setSeparator: ',',
          setSuffix: ' rmb',
          setPrefix: '¥ ',
          startVal1:0,
          endVal1:2017
        }
      },
      computed: {
        _startVal() {
          if (this.setStartVal) {
            return this.setStartVal
          } else {
            return 0
          }
        },
        _endVal() {
          if (this.setEndVal) {
            return this.setEndVal
          } else {
            return 0
          }
        },
        _duration() {
          if (this.setDuration) {
            return this.setDuration
          } else {
            return 100
          }
        },
        _decimals() {
          if (this.setDecimals) {
            if (this.setDecimals < 0 || this.setDecimals > 20) {
              alert('digits argument must be between 0 and 20')
              return 0
            }
            return this.setDecimals
          } else {
            return 0
          }
        },
        _separator() {
          return this.setSeparator
        },
        _suffix() {
          return this.setSuffix
        },
        _prefix() {
          return this.setPrefix
        },
      },
      methods: {
        changeExampleEndVal(){
          this.endVal1=this.endVal1+1000
        },
        incrementalUpdate(){
          this.startVal1=this.endVal1
          this.endVal1=this.endVal1+1000
        },
        callback() {
          console.log('callback')
        },
        start1() {
          this.$refs.example1.start();
        },
        start2() {
          this.$refs.example2.start();
        },
        start3() {
          this.$refs.example3.start();
        },
        pauseResume() {
          this.$refs.example3.pauseResume();
        }
      }
    };
    var Ctor = Vue.extend(Main)
    new Ctor().$mount('#app')
  </script>
</body>

</html>
