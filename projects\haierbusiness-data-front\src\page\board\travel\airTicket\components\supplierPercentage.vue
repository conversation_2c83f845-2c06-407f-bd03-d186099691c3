<template>
    <div :style="{ height: props.height + 'vh' }" background="rgba(0,0,0,0)">
        <div :id="id" :style="{ height: props.height + 'vh' }"></div>
    </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import * as echarts from "echarts";
import { querySupplierPercentage } from "@haierbusiness-front/apis/src/data/board/travel";
import { circle2 as cicleOptions, colors } from "../../../data";
import { EventBus } from "../../../eventBus";
const props = defineProps({
    gngj: {
        type: [String, Number],
        default: "0",
    },
    height: {
        type: Number,
        default: 35,
    },
});
const id = ref("cicle-" + Date.now());
const loading = ref(false);
let chartDom, myChart;
const payTypeCheck = ref<string>("");

onMounted(() => {
    queryData();

    chartDom = document.getElementById(id.value);
    myChart = echarts.init(chartDom as any, "dark");

    myChart.on("click", (param) => {
        if (param.from != "supplier_name" && param.name != payTypeCheck.value) {
            payTypeCheck.value = param.name;
            EventBus.emit("refresh", {
                ...param,
                from: "supplier_name",
            });
        } else {
            payTypeCheck.value = "";
            EventBus.emit("refresh");
        }
    });
});
EventBus.on((event, params) => {
    if (event == "refresh") {
        if (!params) queryData();
        //同组件触发
        if (params && params.from != "pay_type" && params.from != "budget_source") {
            queryData(params);
        }
        if (params && params.from == "pay_type") {
            queryData().then(() => {
                myChart.dispatchAction({
                    type: "highlight",
                    seriesIndex: 0,
                    dataIndex: params.dataIndex,
                });
            });
        }
    }
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
    loading.value = true;
    const data = await querySupplierPercentage(
        { gngj: props.gngj },
        params ? params.data.name : null,
        params ? params.from : null
    );
    loading.value = false;
    const rows = [] as Array<{
        name: string;
        value: string | number;
    }>;
    data.rows.forEach((item, index) => {
        rows.push({
            value: item[1],
            name: item[0],
        });
    });
    const { series } = cicleOptions;
    series[0].color = colors;
    series[0].data = rows;
    myChart.setOption(cicleOptions);
};
</script>
<style scoped lang="less"></style>
