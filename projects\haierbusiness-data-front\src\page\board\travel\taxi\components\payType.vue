<template>
    <div style="height: 33vh" background="rgba(0,0,0,0)">
        <h-row>
            <h-col :span="14">
                <div :id="pieId" style="height: 20vh"></div>
            </h-col>
            <h-col :span="10">
                <div :id="circleId" style="height: 20vh"></div>
            </h-col>
        </h-row>
        <h-row>
            <h-col :span="14" flex class="tips">
                <div class="tips-main">
                    <div class="tip" v-for="(row, index) in payTypeRows" :key="row.name">
                        <span class="tip-dot" :style="{ borderColor: colors[index] }"></span>
                        <span class="tip-percent">{{
                            ((row.value / payTypeTotal) * 100).toFixed(0)
                        }}%</span>
                        <span class="tip-title">{{ row.name }}</span>
                    </div>
                </div>
            </h-col>
            <h-col :span="10" class="tips">
                <div>
                    <div class="tip" v-for="(row, index) in payTypeDetailRows" :key="row.name">
                        <span class="tip-dot" :style="{ borderColor: colors[index] }"></span>
                        <span class="tip-percent">{{
                            (
                                (row.value / payTypeDetailTotal) *
                                100
                            ).toFixed(0)
                        }}%</span>
                        <span class="tip-title">{{ row.name }}</span>
                    </div>
                </div>
            </h-col>
        </h-row>
    </div>
</template>
<script setup lang="ts">


import {
    Badge as hBadge,
    Progress as hProgress,
    Button as hButton,
    Col as hCol,
    DatePicker as hDatePicker,
    Form as hForm,
    FormItem as hFormItem,
    Input as hInput,
    Modal as hModal,
    Popconfirm as hPopconfirm,
    Popover as hPopover,
    RangePicker as hRangePicker,
    Row as hRow,
    Select as hSelect,
    SelectOption as hSelectOption,
    Table as hTable,
    Tag as hTag,
    message,
    TableProps
} from 'ant-design-vue';
import { onMounted, ref } from "vue";
import { pie, circle, colors } from "../../../data";
import * as echarts from "echarts";
import { queryTaxiBudgetType, queryTaxiType } from "@haierbusiness-front/apis/src/data/board/travel";
import { EventBus } from "../../../eventBus";
import { useRouter, useRoute } from "vue-router";
const route = useRoute();
const loading = ref(false);
const pieId = ref("pie-" + Date.now());
const circleId = ref("circle-" + Date.now());
const payTypeRows: any = ref([]);
const payTypeTotal = ref(0);
const payTypeDetailRows: any = ref([]);
const payTypeDetailTotal = ref(0);
let pieChartDom, pieChart, circleChartDom, circleChart;
const payTypeCheck = ref<string>("");

onMounted(() => {
    pieChartDom = document.getElementById(pieId.value);
    pieChart = echarts.init(pieChartDom as any, "dark");
    circleChartDom = document.getElementById(circleId.value);
    circleChart = echarts.init(circleChartDom as any, "dark");
    queryPie();
    queryCircle();
    circleChart.on("click", (param) => {
        if (param.from != "yskmmc" && param.name != payTypeCheck.value) {
            payTypeCheck.value = param.name;
            EventBus.emit("refresh", {
                ...param,
                from: "yskmmc",
            });
        } else {
            payTypeCheck.value = "";
            EventBus.emit("refresh");
        }
    });

    pieChart.on("click", (param) => {
        if (param.from != "budget_source" && param.name != payTypeCheck.value) {
            payTypeCheck.value = param.name;
            EventBus.emit("refresh", {
                ...param,
                from: "budget_source",
            });
        } else {
            payTypeCheck.value = "";
            EventBus.emit("refresh");
        }
    });
});
EventBus.on((event, params) => {
    if (event == "refresh") {
        //获取缓存中筛选的模块
        if (!params) {
            queryPie();
            queryCircle();
        }
        //同组件触发
        if (params && params.from != "budget_source") {
            queryPie(params);
            queryCircle(params);
        }
        //点击左边
        if (params && params.from == "budget_source") {
            queryPie().then(() => {
                pieChart.dispatchAction({
                    type: "highlight",
                    seriesIndex: 0,
                    dataIndex: params.dataIndex,
                });
            });
            queryCircle();
        }
        if (params && params.from == "yskmmc") {
            queryCircle().then(() => {
                circleChart.dispatchAction({
                    type: "highlight",
                    seriesIndex: 0,
                    dataIndex: params.dataIndex,
                });
            });
            queryPie();
        }
    }
});
//左侧饼图
const queryPie = async (params?: { data: { name: string }; from: string }) => {
    const data = await queryTaxiBudgetType(
        params ? params.data.name : null,
        params ? params.from : null
    );
    const payTypeData: any = [];
    let total = 0;
    data.rows?.forEach((item) => {
        total += item[1];
        payTypeData.push({
            name: item[0],
            value: item[1],
        });
    });
    payTypeTotal.value = total;
    payTypeRows.value = payTypeData;
    const { series } = pie;
    series[0].color = colors;
    series[0].data = payTypeData;
    pieChart.clear();
    pieChart.setOption(pie);
    return payTypeData;
};
const queryCircle = async (params?: { data: { name: string }; from: string }) => {
    loading.value = true;
    const data = await queryTaxiType(
        params ? params.data.name : null,
        params ? params.from : null
    );
    loading.value = false;
    const payTypeDetailData: any = [];
    let total = 0;
    data.rows.forEach((item) => {
        total += item[1];
        payTypeDetailData.push({
            name: item[0],
            value: item[1],
        });
    });
    payTypeDetailTotal.value = total;
    payTypeDetailRows.value = payTypeDetailData;
    const circle = JSON.parse(JSON.stringify(pie));
    const { series } = circle;
    series[0].color = colors;
    series[0].data = payTypeDetailData;
    circleChart.clear();
    circleChart.setOption(circle);
    return payTypeDetailData;
};
</script>
<style scoped lang="less">
.tips {
    display: flex;
    justify-content: center;
}

.tips-main {
    display: flex;
    flex-wrap: wrap;
    width: 24vh;
}

.tip {
    width: 12vh;

    &-dot {
        display: inline-block;
        width: 1vh;
        height: 1vh;
        border: 3px solid #ffd700;
        border-radius: 50%;
    }

    &-percent {
        font-size: 1.5vh;
        margin: 0 5px 0 7px;
    }

    &-title {
        font-size: 1vh;
    }
}
</style>
