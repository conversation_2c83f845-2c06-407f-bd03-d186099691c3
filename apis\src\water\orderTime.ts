import { download, get, post, filepost, originalGet } from '../request'

export const waterworkOrderTimeApi = {
    list: (params: any): Promise<void> => {
        return get('waterworks/api/torderTime/page', params)
    },
    listAll: (params: any): Promise<void> => {
        return get('waterworks/api/torderTime/list', params)
    },
    add: (params: any): Promise<void> => {
        return post('waterworks/api/torderTime/save', params)
    },
    update: (params: object): Promise<void> => {
        return post(`waterworks/api/torderTime/update`, params);
    },
    export: (params: any): Promise<void> => {
        return download('waterworks/api/torderTime/export', params)
    },
    delete: (ids: string): Promise<void> => {
        return post(`waterworks/api/torderTime/delete/${ids}`);
    },
}