import {
    Result,
    IPageResponse,
    IAdvertisementListRequest,
    IAdvertisementPriverResponse
} from '@haierbusiness-front/common-libs'
import { get, post } from '../request'
export const providerAdvertisementApi ={
        /**
     * 查询广告位列表
     */
    queryAdvertisement: (params: IAdvertisementListRequest): Promise<IPageResponse<IAdvertisementPriverResponse>> => {
        return get('/portal/api/admin-api/ad/provider/page', params)
    },
    query: (params: IAdvertisementListRequest): Promise<IPageResponse<IAdvertisementPriverResponse>> => {
        return get('/portal/api/app-api/ad/provider/page', params)
    },
    save: (params: IAdvertisementListRequest): Promise<Result> => {
        return post('/portal/api/admin-api/ad/provider/create', params)
    },
    edit: (params: IAdvertisementListRequest): Promise<Result> => {
        return post('/portal/api/admin-api/ad/provider/update', params)
    },
    remove: (id: number): Promise<Result> => {
        return get('/portal/api/admin-api/ad/provider/delete', { id })
    },
 
}