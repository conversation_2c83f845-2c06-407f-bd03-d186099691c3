<script lang="ts" setup>
import { Dayjs } from 'dayjs';
import { onMounted, reactive, ref, toRaw } from 'vue';
import type { UnwrapRef } from 'vue';
import type { Rule } from 'ant-design-vue/es/form';
import {
  Anchor as hAnchor,
  <PERSON>ton as hButton,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Form as hForm,
  Modal as hModal,
  Row as hRow,
  Col as hCol,
  FormItem as hFormItem,
  Cascader as hCascader,
  Input as hInput,
  InputGroup as hINputGroup
} from 'ant-design-vue';
import { IBudgetType, IBudgetForm, IUserListRequest, IUserInfo } from '@haierbusiness-front/common-libs';
import { SearchOutlined } from '@ant-design/icons-vue';
import { userApi } from '@haierbusiness-front/apis';

const visible = ref<boolean>(false);

const prop = defineProps({
  budgetModalOpen: Boolean,
})

const show = () => {
  visible.value = true;
};
defineExpose({
  show,
});
const formRef = ref();
const formState: UnwrapRef<IBudgetForm> = reactive({
  budgetType: '', // 预算归属
  settlementUnit: '', // 结算单位
  cost: '', // 费用
  money: '18,222', // 预算可用金额
  cos: '', // 卡奥斯项目
  rd: '', // 研发项目
});


const budgetTypeOptions = ref<Array<IBudgetType>>([
  {
    id: 1,
    name: '个人'
  },
  {
    id: 2,
    name: '部门'
  },
])

const rules: Record<string, Rule[]> = {

  budgetType: [{ required: true, message: '请选择结算单位', trigger: 'change' }],
  date1: [{ required: true, message: 'Please pick a date', trigger: 'change', type: 'object' }],
  type: [
    {
      type: 'array',
      required: true,
      message: 'Please select at least one activity type',
      trigger: 'change',
    },
  ],
  resource: [{ required: true, message: 'Please select activity resource', trigger: 'change' }],
  desc: [{ required: true, message: 'Please input activity form', trigger: 'blur' }],
};
const onSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      console.log('values', formState, toRaw(formState));
    })
    .catch((error) => {
      console.log('error', error);
    });
};
const resetForm = () => {
  formRef.value.resetFields();
};

const owner = ref([
  {
    value: 'persional',
    label: '个人',
    children: [
      {
        value: '1101',
        label: '小李',
      },
    ],
  },
  {
    value: 'deptment',
    label: '部门',
    children: [
      {
        value: '11',
        label: '研发部',
        children: [
          {
            value: '1102',
            label: '小王',
          },
        ],
      },
    ],
  },
]);

const labelCol = { span: 5 };
const wrapperCol = { span: 13 }


const chosePersonDialog = ref(false)

const searchParam = ref({
  username: '',
  usercode: ''
})
const userParams = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 10,
  keyWord: ''
})

const dataSource = ref<Array<IUserInfo>>([])

const columns = [
  {
    title: '账号',
    dataIndex: 'username',
    key: 'username',
  },
  {
    title: '姓名',
    dataIndex: 'nickName',
    key: 'nickName',
  },
  {
    title: '部门',
    dataIndex: 'departmentName',
    key: 'departmentName',
  },
  {
    title: '操作',
    key: 'action',
  },
]
const getUserList = () => {
  userApi.list(userParams.value).then(res => {
    dataSource.value = res.records
  })
}
onMounted(() => {
  getUserList()
})
</script>

<template>
  <h-modal :maskClosable="false" v-model:open="visible" title="选择预算" width="1000px">
    <h-form class="mt-30" ref="formRef" :model="formState" :wrapper-col="wrapperCol" :label-col="labelCol"
      :rules="rules">
      <h-row>
        <h-col :span="8">
          <h-form-item label="费用项目" :labelCol="{ span: 8, offset: 0 }" name="budgetType">
            <h-select show-search v-model:value="formState.budgetType" :fieldNames="{ label: 'name', value: 'id' }"
              class="my-selsect" :options="budgetTypeOptions" placeholder="选择费用项目" />
          </h-form-item>
        </h-col>
        <h-col :span="8">
          <h-form-item label="预算人" :labelCol="{ span: 8, offset: 0 }" name="settlementUnit">

            <h-input-group compact>
              <h-input v-model:value="formState.personName" placeholder="请选择预算人" readonly
                style="width: calc(100% - 40px)" />
              <h-button type="primary" @click="chosePersonDialog = true"><template #icon>
                  <SearchOutlined />
                </template></h-button>
            </h-input-group>
          </h-form-item>
        </h-col>
        <h-col :span="8" v-if="true">
          <h-form-item label="预算系统" :labelCol="{ span: 8, offset: 0 }" name="cos">
            <h-select show-search v-model:value="formState.cos" :fieldNames="{ label: 'name', value: 'id' }"
              class="my-selsect" :options="budgetTypeOptions" placeholder="选择城市" />
          </h-form-item>
        </h-col>
      </h-row>

      <h-row>
        <h-col :span="8">
          <h-form-item label="预算部门" :labelCol="{ span: 8, offset: 0 }" name="cost">
            <h-select show-search v-model:value="formState.cost" :fieldNames="{ label: 'name', value: 'id' }"
              class="my-selsect" :options="budgetTypeOptions" placeholder="选择城市" />
          </h-form-item>
        </h-col>
        <h-col :span="8">
          <h-form-item label="结算单位" :labelCol="{ span: 8, offset: 0 }" name="money">
            <h-input v-model:value="formState.money" disabled placeholder="" />
          </h-form-item>
        </h-col>
        <h-col :span="8">
          <h-form-item label="成本中心" :labelCol="{ span: 8, offset: 0 }" name="rd">
            <h-select show-search disabled v-model:value="formState.rd" :fieldNames="{ label: 'name', value: 'id' }"
              class="my-selsect" :options="budgetTypeOptions" placeholder="选择城市" />
          </h-form-item>
        </h-col>
      </h-row>

      <h-row>
        <h-col :span="8">
          <h-form-item label="本月可用预算" :labelCol="{ span: 8, offset: 0 }" name="cost">
            <h-input v-model:value="formState.money" disabled placeholder="" />
          </h-form-item>
        </h-col>
      </h-row>
    </h-form>

    <h-modal :maskClosable="false" :footer="null" v-model:open="chosePersonDialog" title="选择预算人" width="800px">
      <h-row>
        <!-- <h-col :span="4" style="text-align: right;padding-right: 10px; padding-top: 5px;">
            <label for="username">预算人名称:</label>
          </h-col>
          <h-col :span="4">
            <h-input id="username" v-model:value="searchParam.username" placeholder="" autocomplete="off"
              allow-clear />
          </h-col> -->

        <h-col :span="4" style="text-align: right;padding-right: 10px;padding-top: 5px;">
          <label for="usercode">预算人名称/工号:</label>
        </h-col>
        <h-col :span="4">
          <h-input id="usercode" v-model:value="userParams.keyWord" placeholder="" autocomplete="off" allow-clear />
        </h-col>

        <h-col :span="4" style="text-align: right">
          <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
            查询
          </h-button>
        </h-col>
      </h-row>
      <a-table :dataSource="dataSource" :columns="columns" />
    </h-modal>
  </h-modal>
</template>

<style scoped>
.mb-30 {
  margin-bottom: 30px;
}

.ant-row {
  margin-bottom: 20px;
}
</style>