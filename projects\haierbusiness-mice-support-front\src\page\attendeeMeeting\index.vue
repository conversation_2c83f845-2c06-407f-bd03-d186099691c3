<script lang="ts" setup>
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Upload as hUpload,
  message,
  Modal,
} from 'ant-design-vue';
import { ExclamationCircleOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { computed, createVNode, h, nextTick, onMounted, reactive, ref, RendererElement, RendererNode, VNode } from 'vue';
import { usePagination } from 'vue-request';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { fileApi, meetingAttendeeApi, userApi } from '@haierbusiness-front/apis';
import { attendeeMiceStatusConstant, IMeetingAttendee, IMeetingDetails, IUserListRequest } from '@haierbusiness-front/common-libs';
import Actions from '@haierbusiness-front/components/actions/Actions.vue';
import type { MenuInfo, MenuItemType } from 'ant-design-vue/lib/menu/src/interface'
import { useRoute, useRouter } from 'vue-router';
type Key = string | number;
const route = useRoute();
const router = useRouter();

const id = Number(route.query.id)

//弹框可见
const Visible = ref(false)
const importModel = ref(false)
const title = ref('')

const formRef = ref();

const confirmLoading = ref<boolean>(false);

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(meetingAttendeeApi.list);

const dataSource = computed(() => data.value?.list.records || []);


const handleAdd = () => {
  title.value = '新增参会人'
  attendeeMeeting.value = {}
  nextTick(() => {
    formRef.value?.resetFields()
  })
  Visible.value = true
  isEdit.value = false
}

const handleImport = () => {
  ConferenceDocuments.value = null
  importModel.value = true
}
//导出文件
const handleExport = async () => {
  if (!meetingDetails.value?.miceName) {
    message.error('会议信息获取失败，请刷新页面重试')
    return
  }

  uploadLoading.value = true
  try {
    const params = {
      miceInfoId: Number(id) || 0,
      miceInfoName: meetingDetails.value.miceName
    }
    await meetingAttendeeApi.export(params)
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  } finally {
    uploadLoading.value = false
  }
}
//导入文件
let ConferenceDocuments = ref<File | null>();
const uploadLoading = ref<boolean>(false);
const fileInputRef = ref<HTMLInputElement | null>(null);

// 触发文件选择
const triggerFileSelect = () => {
  if (fileInputRef.value) {
    fileInputRef.value.click();
  }
};

// 选择文件
const handleFileChange = (e: Event) => {
  const input = e.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    ConferenceDocuments.value = input.files[0];
  }
};

const handleFileImport = () => {
  if (!ConferenceDocuments.value) {
    message.error('请先选择文件');
    return;
  }

  uploadLoading.value = true;

  const formData = new FormData();
  formData.append("file", ConferenceDocuments.value);
  formData.append("miceInfoId", '1');

  meetingAttendeeApi.import(formData as any)
    .then(res => {
      message.success('导入成功');
      importModel.value = false;
      listApiRun({
        miceInfoId: id,
        pageNum: 1,
        pageSize: 50
      })
    })
    .catch((error) => {
      console.error('导入失败:', error);
      message.error('导入失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
}

const currentRouter = ref()

//会议详情
const meetingDetails = ref<IMeetingDetails>()
//标签列表
const groupLists = ref()

const meetingDetail = async () => {
  const meetingId = Number(id)
  if (!meetingId) {
    message.error('会议ID缺失')
    return
  }

  try {
    const response = await meetingAttendeeApi.details(meetingId)
    if (response) {
      meetingDetails.value = response
    }
  } catch (error) {
    console.error('获取参会人详情失败:', error)
    message.error('获取参会人详情失败')
  }
}
//参会人详情
const attendeeDetail = async (id: number) => {
  try {
    const response = await meetingAttendeeApi.attendeeDetails(id)
    if (response) {
      attendeeMeeting.value = response
      console.log(attendeeMeeting.value, "attendeeMeeting.value");
    }
    let tagList: string | string[] | undefined = []
    if(attendeeMeeting.value.tagName ){
      tagList = attendeeMeeting.value.tagName.split(',')
      attendeeMeeting.value.tagName = tagList
    }else{
      attendeeMeeting.value.tagName = tagList
    }
  } catch (error) {
    console.error('获取会议详情失败:', error)
    message.error('获取会议详情失败')
  }
}

const groupList = async (id: number) => {
  const params = {
    miceInfoId: id
  }
  try {
    const response = await meetingAttendeeApi.groupList(params)
    if (response) {
      groupLists.value = response
    }
  } catch (error) {
    console.error('获取标签列表失败:', error)
    message.error('获取标签列表失败')
  }
}
onMounted(async () => {
  currentRouter.value = await router
  await meetingDetail()
  const meetingId = Number(id)
  if (meetingId) {
    listApiRun({
      miceInfoId: meetingId,
      pageNum: 1,
      pageSize: 50
    })
  }
  await groupList(meetingId)
})

const columns: ColumnType[] = [
  {
    title: '序号',
    dataIndex: 'index',
    width: '80px',
    // sorter: (a, b) => a.index - b.index,
    align: 'center',
    customRender: ({ index }) => index + 1
  },
  {
    title: '参会人企业',
    dataIndex: 'enterpriseName',
    width: '280px',
    ellipsis: true,
  },
  {
    title: '姓名',
    dataIndex: 'nickName',
    width: '120px',
    ellipsis: true,
  },
  {
    title: '工号',
    dataIndex: 'userName',
    width: '200px',
    ellipsis: true,
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    width: '280px',
    ellipsis: true,
  },
  {
    title: '需要住宿',
    dataIndex: 'isStay',
    // sorter: true,
    width: '120px',
    ellipsis: true,
    customRender: ({ text }) => ({
      children: text === true ? '是' : '否',
      // props: { style: { color: text === true ? '#52C41A' : '#FF5533' } }
    }),
    customCell: (record) => {
      return {
        style: { color: record.isBreastPiece === true ? '#52C41A' : '#FF5533' }
      }
    }
  },
  {
    title: '身份证号',
    dataIndex: 'idCard',
    width: '280px',
    ellipsis: true,
  },
  {
    title: '标签',
    dataIndex: 'tagName',
    width: '160px',
    customRender: ({ text }) => {
      if (!text) {
        return
      }   // 处理null/undefined情况
      const textList = text ? text.toString().split(',').filter(Boolean) : [] // 确保字符串操作
      return h('div',
        textList.map((tag: string) =>
          h(hTag, {
            key: tag,
            color: tag == '会务组人员' ? 'green' : tag == '接待人员' ? 'geekblue' : tag == '普通参会人' ? '#ccc' : 'orange'
          }, tag)
        )
      )
    }

  },
  {
    title: '所属单位',
    dataIndex: 'companyName',
    width: '280px',
    ellipsis: true,
  },
  {
    title: '所属部门',
    dataIndex: 'departName',
    width: '280px',
    ellipsis: true,
  },
  {
    title: '禁忌与偏好',
    dataIndex: 'specialRequest',
    width: '280px',
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: '280px',
    ellipsis: true,
  },
  {
    title: '需座位牌',
    dataIndex: 'isSeatSign',
    width: '120px',
    ellipsis: true,
    customRender: ({ text }) => ({
      children: text === true ? '是' : '否',
      // props: { style: { color: text === true ? '#52C41A' : '#FF5533' } }
    }),
    customCell: (record) => {
      return {
        style: { color: record.isBreastPiece === true ? '#52C41A' : '#FF5533' }
      }
    }
  },
  {
    title: '需胸牌',
    dataIndex: 'isBreastPiece',
    width: '120px',
    ellipsis: true,
    customRender: ({ text }) => ({
      children: text === true ? '是' : '否',
      // props: { style: { color: text === true ? '#52C41A' : '#FF5533' } }
    }),
    customCell: (record) => {
      return {
        style: { color: record.isBreastPiece === true ? '#52C41A' : '#FF5533' }
      }
    }
  },
  {
    title: '参会人来源',
    dataIndex: 'userSource',
    width: '120px',
    ellipsis: true,
    customRender: ({ text }) => text == 1 ? '录入' : text == 2 ? '自主报名' : ' '
  },
  {
    title: '报名审批状态',
    dataIndex: 'approveState',
    width: '120px',
    ellipsis: true,
    customRender: ({ text }) => {
      return attendeeMiceStatusConstant.ofType(text)?.desc

    }
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '220px',
    fixed: 'right',
    align: 'center',
  },

];

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: false,
  total: data.value?.list.total,
  current: data.value?.list.pageNum,
  pageSize: data.value?.list.pageSize,
  style: { justifyContent: 'right' },
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
) => {
  loading.value = true
  const meetingId = Number(id)
  if (!meetingId) return
  try {
    listApiRun({
      miceInfoId: meetingId,
      pageNum: pag.current,
      pageSize: pag.pageSize
    })
    loading.value = false
  } catch (error) {

  } finally {
    loading.value = false
  }


};

const attendeeMeeting = ref<IMeetingAttendee>({})

const rules = {
  enterpriseName: [
    { required: true, message: '请选择', trigger: 'change' }
  ],
  nickName: [
    { required: true, message: '请输入', trigger: 'blur' }
  ],
  userName: [
    { required: true, message: '请输入', trigger: 'blur' }
  ],
  // phone: [
  //   { required: true, message: '请输入', trigger: 'blur' },
  //   { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的11位手机号码', trigger: 'change' }
  // ],
  // isStay: [
  //   { required: true, message: '请选择', trigger: 'change' },
  // ],
  idCard: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  // tagName: [
  //   { required: true, message: '请输入', trigger: 'blur' },
  // ],
  // companyName: [
  //   { required: true, message: '请输入', trigger: 'blur' },
  // ],
  // departName: [
  //   { required: true, message: '请输入', trigger: 'blur' },
  // ],
  // specialRequest: [
  //   { required: true, message: '请输入', trigger: 'blur' },
  // ],
  // remark: [
  //   { required: true, message: '请输入', trigger: 'blur' },
  //   { max: 500, message: '会议描述最多500个字符', trigger: 'blur' }
  // ],
  isSeatSign: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  isBreastPiece: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
};



const handleOk = () => {
  confirmLoading.value = true;
  const miceId = Number(id)
  formRef.value
    .validate()
    .then(() => {
      console.log(attendeeMeeting.value, "attendeeMeeting.value");
      const params = attendeeMeeting.value
      params.miceInfoId = miceId
      let tagNameList = attendeeMeeting.value.tagName
      if (tagNameList) {
        if (typeof tagNameList === 'string') {
          params.tagName = tagNameList.trim(); // 增加去空格处理
        } else if (Array.isArray(tagNameList)) {
          params.tagName = tagNameList.filter(Boolean).join(','); // 过滤空值
        }
      }
      console.log(params, "params");
      if (title.value == '新增参会人') {
        meetingAttendeeApi.save(params)
          .then(() => {
            // 成功处理
            confirmLoading.value = false;
            Visible.value = false
            listApiRun({
              miceInfoId: miceId,
              pageNum: 1,
              pageSize: 50
            })
            message.success('新增成功')
          })
          .catch((error) => {
            // 错误处理
            confirmLoading.value = false;
            message.success('新增失败')
          });
      } else {
        meetingAttendeeApi.edit(params)
          .then(() => {
            // 成功处理
            confirmLoading.value = false;
            Visible.value = false
            listApiRun({
              miceInfoId: miceId,
              pageNum: 1,
              pageSize: 50
            })
            message.success('编辑成功')
          })
          .catch((error) => {
            // 错误处理
            confirmLoading.value = false;
            message.success('编辑失败')
          });
      }

    })
    .catch((errors: any) => {
      console.error('表单验证失败:', errors);
      confirmLoading.value = false;
    });
};


const handleChange = (value: string | null | undefined) => {
  if (!value) return false;

  // 转换为数组并过滤空值/空白字符串
  const valueList = value.toString()
    .split(',')
    .map(item => item.trim())
    .filter(Boolean);

  console.log(valueList, "filtered valueList");

  valueList.forEach((item) => {
    const result = groupLists.value.find((tag: { tagName: string }) =>
      tag.tagName === item.trim()
    );
    if (!result) {
      groupAdd(item);
    }
  });
};


//新增标签
const groupAdd = async (tag: string) => {
  const meetingId = Number(id)
  const params = [{
    miceInfoId: id,
    tagName: tag
  }]
  try {
    const response = await meetingAttendeeApi.groupSave(params)
    groupList(meetingId)
  } catch (error) {
    console.error('新增标签失败:', error)
    message.error('新增标签失败')
  }
}

//审批通过
const Approved = (record: IMeetingAttendee) => {
  loading.value = true
  const params = {
    id: record.id,
    approveState: 20
  }
  meetingAttendeeApi.Approval(params)
    .then(() => {
      message.success('审批通过成功')
    })
    .catch(() => {
      message.success('审批通过失败')
    })
  listApiRun({
    miceInfoId: id,
    pageNum: 1,
    pageSize: 50
  })
  loading.value = false

}
//审批驳回
const approvalRejection = (record: IMeetingAttendee) => {
  loading.value = true
  const params = {
    id: record.id,
    approveState: 30
  }
  meetingAttendeeApi.Approval(params)
    .then(() => {
      message.success('驳回成功')
    })
    .catch(() => {
      message.success('驳回失败')
    })
  listApiRun({
    miceInfoId: id,
    pageNum: 1,
    pageSize: 50
  })
  loading.value = false
}

// 更多按钮数组
const computedOptions = (state: number, record: IMeetingAttendee) => {
  let options: MenuItemType[] = []
  if (state == 10) {
    options.push(
      {
        key: '1',
        label: '审核通过',
      },
      {
        key: '2',
        label: '审核驳回',
      }
    )
  }
  return options
}

// 点击数组中的菜单项
const handleMenuClick = (record: IMeetingAttendee, e: MenuInfo) => {
  if (e.key === '1') {
    Approved(record)
  }
  if (e.key === '2') {
    approvalRejection(record)
  }
}

const handleEdit = async (record: any) => {
  nextTick(() => {
    formRef.value?.clearValidate()
  })
  title.value = '编辑参会人'
  await attendeeDetail(record.id)
  Visible.value = true
  isEdit.value = true
}

const handleDelete = (id: number) => {
  Modal.confirm({
    title: '删除提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', { style: 'color:red;' }, '确定要删除'),
    onOk() {
      meetingAttendeeApi.remove(id)
        .then(() => {
          message.success('删除成功')
          listApiRun({
            miceInfoId: 1,
            pageNum: 1,
            pageSize: 50
          })
        })
    },
    onCancel() {
      console.log('Cancel');
    },
  });

}
//是否禁用编辑
const isEdit = ref(false)
const handleName = (res: string | undefined) => {
  nextTick(() => {
    formRef.value?.clearValidate()
  })
  if (res) {
    if (res == '海尔') {
      isEdit.value = true
      formRef.value?.clearValidate('nickName', 'phone', 'idCard', 'companyName', 'departName')
    } else if (res == '非海尔') {
      isEdit.value = false
      if (!isEdit.value) {
        message.warning('您不是海尔员工，请自行填写信息')
        return
      }
    }
  }
}

// 查询用户
const getUserList = async (username: string | undefined,) => {
  if (!attendeeMeeting.value.enterpriseName && username) {
    message.warning('请先选择所属企业')
    return
  }
  if (!isEdit.value) {
    return
  }

  if (!username) return
  const params = {
    pageNum: 1,
    pageSize: 20,
    username,
    enterpriseCode: 'haier'
  }
  try {
    const data = await userApi.list(params as IUserListRequest)
    console.log(data, "data");
    nextTick(() => {
      if (data.records?.length > 0) {
        attendeeMeeting.value.phone = data.records[0].phone
        attendeeMeeting.value.userName = data.records[0].username
        attendeeMeeting.value.nickName = data.records[0].nickName
        attendeeMeeting.value.idCard = data.records[0].id
        attendeeMeeting.value.departName = data.records[0].departmentName
        attendeeMeeting.value.companyName = data.records[0].enterpriseName
        attendeeMeeting.value.enterpriseCode = data.records[0].enterpriseCode
      } else {
        message.warning('工号错误，请重新输入')
        formRef.value?.clearValidate('nickName', 'phone', 'idCard', 'companyName', 'departName')
      }
    })

  } catch (error) {
    console.error('查询用户失败:', error)
  }
}

// 复制文本到剪贴板
const getCopy = async (text: string) => {
  const baseUrl = import.meta.env.VITE_MICE_BIDMAN_URL;
  // text = `${baseUrl}#/bidman/priceInquiry/inquiryOrderForm?id=${record.id}`;
  try {
    await navigator.clipboard.writeText(text);
    message.success('复制成功！');
  } catch (err) {
    message.error('复制失败');
  }
};
</script>

<template>
  <div class="container">
    <div class="content">
      <div class="nav">
        会议首页/{{ router.currentRoute?.value.meta.title }}
      </div>
      <div class="main">
        <div class="main-top">
          <div class="top-title">
            {{ router.currentRoute?.value.meta.title }}
          </div>
          <ul class="top-right">
            <li class="" style="margin-right: 10px;">
              <a-button type="primary" @click="handleAdd">添加</a-button>
            </li>
            <li>
              <a-button @click="handleImport" style="margin-right: 10px;">导入</a-button>
            </li>
            <li>
              <a-button @click="handleExport" style="margin-right: 10px;" :loading="uploadLoading">导出</a-button>
            </li>
            <li class="copy" style="margin-left: 10px;cursor: pointer;" v-if="meetingDetails?.signUpUrl"
              @click="getCopy(meetingDetails?.signUpUrl)"><img src="@/assets/image/meeting/copy.png" alt="">复制报名链接</li>
            <li v-else>
              <a-button @click="">生成报名链接</a-button>
            </li>
          </ul>
        </div>
        <div class="center-main">
          <div class="center-top">
            <div class="top-left">
              审核通过<span>{{ data?.approvePassCount }}</span>
            </div>
            <h-button style="margin-right: 10px;border-radius: 4px;">审核驳回<span>{{ data?.approveRejectCount
                }}</span></h-button>
            <div class="top-center">
              <p>待审核<span>{{ data?.approveWaitCount }}</span></p>
              <p class="line"></p>
              <p>全部<span>{{ data?.list.total }}</span></p>
            </div>
          </div>
          <div class="center-middle">
            <a-table :rowKey="(record: any) => record.id" :columns="columns" :data-source="dataSource"
              :loading="loading" :pagination="pagination" @change="handleTableChange($event as any)"
              :scroll="{ x: 1280 }">
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === '_operator'">
                  <div>
                    <h-button type="link" @click="handleEdit(record)">编辑</h-button>
                    <h-button type="link" @click="handleDelete(record.id)">删除</h-button>
                    <Actions :menu-options="computedOptions(record.approveState, record)"
                      :on-menu-click="(key) => handleMenuClick(record, key)"></Actions>
                  </div>
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </div>
    <Modal v-model:open="Visible" :title="title" width="800px" :confirm-loading="confirmLoading" @ok="handleOk">
      <h-form ref="formRef" :rules="rules" :model="attendeeMeeting" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }"
        hideRequiredMark="false">
        <h-form-item label="参会人企业：" name="enterpriseName">
          <h-select id="isStay" v-model:value="attendeeMeeting.enterpriseName" class="full-width" allow-clear
            placeholder="请选择参会人企业" @change="handleName(attendeeMeeting.enterpriseName)">
            <h-select-option :value="'海尔'">海尔</h-select-option>
            <h-select-option :value="'非海尔'">非海尔</h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="工号：" name="userName">
          <h-input v-model:value="attendeeMeeting.userName" placeholder="请输入工号" allow-clear
            @change="getUserList(attendeeMeeting.userName)" />
        </h-form-item>
        <h-form-item label="姓名：" name="nickName">
          <h-input v-model:value="attendeeMeeting.nickName" placeholder="请输入姓名" allow-clear :disabled="isEdit" />
        </h-form-item>
        <h-form-item label="手机号：" name="phone">
          <h-input v-model:value="attendeeMeeting.phone" placeholder="请输入手机号" :disabled="isEdit" allow-clear />
        </h-form-item>

        <h-form-item label="需要住宿：" name="isStay">
          <h-select id="isStay" v-model:value="attendeeMeeting.isStay" class="full-width" allow-clear
            placeholder="请选择是否住宿">
            <h-select-option :value="true">是</h-select-option>
            <h-select-option :value="false">否</h-select-option>
          </h-select>
        </h-form-item>

        <h-form-item label="身份证号：" name="idCard">
          <h-input v-model:value="attendeeMeeting.idCard" placeholder="请输入身份证号" :disabled="isEdit" allow-clear />
        </h-form-item>

        <h-form-item label="标签：" name="tagName">
          <h-select id="isStay" mode="tags" v-model:value="attendeeMeeting.tagName" class="full-width" allow-clear
            @change="handleChange" placeholder="请选择标签">
            <h-select-option v-for="item in groupLists" :key="item.id" :value="item.tagName">
              {{ item.tagName }}
            </h-select-option>
          </h-select>
        </h-form-item>

        <h-form-item label="所属单位：" name="companyName">
          <h-input v-model:value="attendeeMeeting.companyName" placeholder="请输入所属单位" :disabled="isEdit" allow-clear />
        </h-form-item>

        <h-form-item label="所属部门：" name="departName">
          <h-input v-model:value="attendeeMeeting.departName" placeholder="请输入所属部门" :disabled="isEdit" allow-clear />
        </h-form-item>

        <h-form-item label="禁忌与偏好：" name="specialRequest">
          <h-input v-model:value="attendeeMeeting.specialRequest" placeholder="请输入禁忌与偏好" allow-clear />
        </h-form-item>

        <h-form-item label="备注：" name="remark">
          <h-textarea v-model:value="attendeeMeeting.remark" :rows="4" :maxlength="500" show-count placeholder="请输入备注"
            allow-clear />
        </h-form-item>

        <h-form-item label="需座位牌：" name="isSeatSign">
          <h-select id="tagName" v-model:value="attendeeMeeting.isSeatSign" class="full-width" allow-clear placeholder="请选择是否需要">
            <h-select-option :value="true">是</h-select-option>
            <h-select-option :value="false">否</h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="需胸牌：" name="isBreastPiece">
          <h-select id="tagName" v-model:value="attendeeMeeting.isBreastPiece" class="full-width" allow-clear placeholder="请选择是否需要">
            <h-select-option :value="true">是</h-select-option>
            <h-select-option :value="false">否</h-select-option>
          </h-select>
        </h-form-item>
      </h-form>
    </Modal>

    <Modal v-model:open="importModel" title="导入参会人" width="35%" :confirm-loading="confirmLoading">
      <template #footer>
        <a-button key="back" @click="importModel = false">取消</a-button>
        <a-button key="submit" type="primary" @click="handleFileImport">导入</a-button>
      </template>
      <h-row :gutter="[16, 16]" style="margin-top: 20px;">
        <h-col :span="6" style="text-align: right;line-height: 32px;">
          上传文件：
        </h-col>
        <h-col :span="10">
          <input ref="fileInputRef" type="file" style="display: none;" accept=".xlsx,.xls,.csv"
            @change="handleFileChange" />
          <h-button type="primary" @click="triggerFileSelect" style="margin-right: 8px;">
            <UploadOutlined /> 选择文件
          </h-button>
        </h-col>
        <h-col :span="8" style="line-height: 32px;">
          <h-button type="link">下载导入模板</h-button>
        </h-col>
      </h-row>
      <h-row>
        <h-col :span="15" style="line-height: 32px;text-align: right;margin-top: 0px;">
          <span v-if="ConferenceDocuments">{{ ConferenceDocuments.name }}</span>
        </h-col>
      </h-row>
      <h-row>
        <h-col :span="24" style="line-height: 32px;text-align: center;margin-top: 20px;">
          <img src="../../assets/image/orderList/warn.png" alt=""
            style="width: 17px;height: 17px;vertical-align: middle;">导入新数据后将覆盖<span
            style="color: red;">来源为录入</span>的全部数据
        </h-col>

      </h-row>
    </Modal>
  </div>
</template>
<style lang="scss" scoped>
* {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
}

ul,
p {
  margin-bottom: 0;
}

ul li {
  list-style: none;
}

:deep(.ant-select) {
  min-width: 100px;
}

.container {
  height: auto;
  width: 100%;
  background: #F6F7F9;
}

.content {
  width: 1280px;
  height: auto;
  margin: 0 auto;

  .main {
    width: 100%;
    background-color: #fff;

    .main-top {
      width: 100%;
      border: 1px solid #E5E6EB;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 18px 24px;

      .top-title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 20px;
        color: #1D2129;
        line-height: 28px;
        text-align: left;
        font-style: normal;
      }

      .top-right {
        display: flex;
        align-items: center;
        margin-bottom: 0 !important;

        .copy {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #1868DB;
          line-height: 20px;
          text-align: right;
          font-style: normal;

          img {
            width: 16px;
            height: 16px;
          }
        }
      }

    }

    .center-main {
      padding: 20px 24px;

      .center-top {
        display: flex;

        span {
          margin-left: 2px;
        }
      }

      .top-left {
        width: 109px;
        height: 32px;
        background: #1868DB;
        border-radius: 4px;
        line-height: 32px;
        text-align: center;
        font-size: 14px;
        color: #fff;
        margin-right: 12px;
      }

      .top-center {
        width: 185px;
        height: 32px;
        background: #F2F3F5;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        color: #1D2129;
        line-height: 22px;
        text-align: center;
        font-style: normal;

        .line {
          width: 1px;
          height: 18px;
          background: #E7E7E7;
        }
      }

      .center-middle {
        margin-top: 20px;
      }
    }
  }
}

.nav {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #86909C;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  padding: 16px 0px;
}

.full-width {
  width: 100%;
}

.hide-empty-tags .ant-tag {
  display: none;
}

/* 覆盖浏览器自动填充样式 */
:deep(input:-internal-autofill-previewed,
  input:-internal-autofill-selected) {
  -webkit-text-fill-color: #2a2d33;
  /* 保持文字颜色 */
  background-color: #fff !important;
  transition: background-color 5000s ease-out 0.5s;
  /* 延迟背景色渲染 */
}
</style>