import { download, get, post } from '../request'
import { 
    INoticeContractExpirationFilter, 
    INoticeContractExpiration,
    IPageResponse, 
    Result } from '@haierbusiness-front/common-libs'


export const noticeContractExpirationApi = {
    list: (params: INoticeContractExpirationFilter): Promise<IPageResponse<INoticeContractExpiration>> => {
        return get('merchant/api/merchantContract/notifier/getPage', params)
    },

    get: (id: number): Promise<INoticeContractExpiration> => {
        return get('merchant/api/noticeContractExpiration/get', {
            id
        })
    },

    save: (params: INoticeContractExpiration): Promise<Result> => {
        return post('merchant/api/merchantContract/notifier/insert', params)
    },

    edit: (params: INoticeContractExpiration): Promise<Result> => {
        return post('merchant/api/merchantContract/notifier/updateByid', params)
    },

    remove: (id: number): Promise<Result> => {
        return post(`merchant/api/merchantContract/notifier/deleteByid?id=${id}`)
    },
}
