import * as Antdv from 'ant-design-vue';
import { App } from 'vue';
import { isFirstLetterCapital, camelToKebab } from '../utils';

interface AntdvMap {
  [propName: string]: any,
}
export const hAntdPlugin = {
  install(Vue: App) {
    const keys = Object.keys(Antdv).filter(isFirstLetterCapital);
    for (const key of keys) {
      Vue.component(`h-${camelToKebab(key)}`, (Antdv as AntdvMap)[key]);
    }
  }
}