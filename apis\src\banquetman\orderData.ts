import { downloadPost, get, post } from '../request'
import {
  BOrderListReq,
  BOrderListRes,
  IPageResponse,
  BOrderPageReq,
  BOrderPageRes
} from '@haierbusiness-front/common-libs'


export const banquetOrderApi = {
  list: (params: BOrderListReq): Promise<IPageResponse<BOrderListRes>> => {
    return post('banquet/api/banquetBooking/listTotal', params)
  },
  getPage: (params: BOrderPageReq): Promise<BOrderPageRes> => {
    return post('banquet/api/banquetBooking/page', params)
  },
  exportList: (params: BOrderListReq): Promise<BOrderListRes> => {
    return downloadPost('banquet/api/banquetBooking/exprotTotal', params)
  },
  exportPage: (params: BOrderListReq): Promise<BOrderListRes> => {
    return downloadPost('banquet/api/banquetBooking/exportCheckList', params)
  },
  updateCheckStatus: (params: BOrderPageRes): Promise<BOrderListRes> => {
    return post('banquet/api/banquetBooking/updateCheckStatus', params)
  },

}