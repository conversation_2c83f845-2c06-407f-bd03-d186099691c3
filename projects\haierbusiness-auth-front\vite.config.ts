import { loadEnv } from 'vite'

import vue from '@vitejs/plugin-vue'
import path from 'path';

const CWD = process.cwd();

export default ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);

  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
    },
    build: {
      target: "es2015",
    },
    plugins: [vue()],
    server: {
      port: 5173,
      proxy: {
        "/hb": {
          // target: "http://localhost:8080/hb",
          target: "https://businessmanagement-test.haier.net/hbweb/pay/hb",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb/, ""),
        },
      },
    }
  }
}
