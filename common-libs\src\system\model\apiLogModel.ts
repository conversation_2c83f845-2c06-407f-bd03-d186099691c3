import { IPageRequest } from "../../basic";

export interface IApiLogRequest extends IPageRequest {
    status?: string
    method?: string
    urlKey?: string
    headersKey?: string
    paramsKey?: string
    respHeadersKey?: string
    responseKey?: string
    createBy?: string
    type?: number
}

export interface IApiLogResponse {
    id?: number
    name?: string
    status?: string
    url?: string
    method?: string
    headers?: string
    params?: string
    respHeaders?: string
    response?: string
    gmtCreate?: string
    createBy?: string
}