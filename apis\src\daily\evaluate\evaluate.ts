import {
    IEvaluateListRequestDTO,
    IEvaluateListResponseDTO,
    IEvaluateSaveRequestDTO
} from '@haierbusiness-front/common-libs'
import { get, post } from '../../request'


export const evaluateApi = {

    list: (params: IEvaluateListRequestDTO): Promise<IEvaluateListResponseDTO[]> => {
        return get('/daily/api/evaluate/list', params)
    },

    saveOrUpdateByManger: (params: IEvaluateSaveRequestDTO): Promise<void> => {
        return post('/daily/api/evaluate/three-month', params)
    },

    saveOrUpdateByPlatform: (params: IEvaluateSaveRequestDTO): Promise<void> => {
        return post('/daily/api/evaluate/platform-month', params)
    },
}