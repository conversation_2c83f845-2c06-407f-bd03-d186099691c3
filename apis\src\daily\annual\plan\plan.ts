import {
    IAnnualPlanAddRequestDTO,
    IAnnualPlanDetailRequestDTO,
    IAnnualPlanSubmitRequestDTO,
    IAnnualPlanUpdateCloseRequestDTO,
    IAnnualPlanUpdateRequestDTO,
    IAnnualPlanDetailResponseDTO,
    IAnnualPlanListRequestDTO,
    IAnnualPlanListResponseDTO,
    IAnnualPlanSaveOrUpdateRequestDTO,

} from '@haierbusiness-front/common-libs'
import {get, post} from '../../../request'


export const dailyAnnualPlanApi = {

    save: (params: IAnnualPlanSaveOrUpdateRequestDTO): Promise<number> => {
        return post('/daily/api/annual-plan/save', params)
    },

    update: (params: IAnnualPlanUpdateRequestDTO): Promise<number> => {
        return post('/daily/api/annual-plan/update', params)
    },

    resetting: (params: IAnnualPlanSaveOrUpdateRequestDTO): Promise<number> => {
        return post('/daily/api/annual-plan/resetting', params)
    },

    add: (params: IAnnualPlanAddRequestDTO): Promise<number> => {
        return post('/daily/api/annual-plan/add', params)
    },

    updateClose: (params: IAnnualPlanUpdateCloseRequestDTO): Promise<number> => {
        return post('/daily/api/annual-plan/update-close', params)
    },

    submit: (params: IAnnualPlanSubmitRequestDTO): Promise<void> => {
        return post('/daily/api/annual-plan/submit', params)
    },

    list: (params: IAnnualPlanListRequestDTO): Promise<IAnnualPlanListResponseDTO[]> => {
        return get('/daily/api/annual-plan/list', params)
    },

    detail: (params: IAnnualPlanDetailRequestDTO): Promise<IAnnualPlanDetailResponseDTO> => {
        return get('/daily/api/annual-plan/detail', params)
    },
}