<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Input as HInput,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Switch as hSwitch,
  message,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { discountApi } from '@haierbusiness-front/apis';
import { IDiscountFilter, IDiscount } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables';
import EditDialog from './edit-dialog.vue';
import router from '../../router';
// const router = useRouter()

const currentRouter = ref();

onMounted(async () => {
  currentRouter.value = await router;
});

const columns: ColumnType[] = [
  {
    title: '供应商',
    dataIndex: 'merName',
    width: '150px',
    align: 'center',
    ellipsis: true,
    fixed: 'left',
  },
  {
    title: '供应商',
    dataIndex: 'merCode',
    width: '150px',
    align: 'center',
    ellipsis: true,
    fixed: 'left',
  },
  {
    title: '图标',
    dataIndex: 'icon',
    width: '120px',
    align: 'center',
  },
  {
    title: '分类',
    dataIndex: 'merType',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '折扣简述',
    dataIndex: 'discountDesc',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '折扣值',
    dataIndex: 'discountValue',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '折扣详情',
    dataIndex: 'details',
    width: '300px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '是否生效',
    dataIndex: 'isEnable',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '生效时间',
    dataIndex: 'takeEffectTime',
    width: '340px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '160px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '140px',
    fixed: 'right',
    align: 'center',
  },
];
const searchParam = ref<IDiscountFilter>({});
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(discountApi.page, {
  manual: false,
});

const {
  data: typeList,
  run: typeListApiRun,
  loading: typeListLoading,
} = useRequest(discountApi.getTypeList, {
  manual: false,
});

const reset = () => {
  searchParam.value = {};
};

// const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};
const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } = useEditDialog<IDiscount, IDiscount>(
  discountApi,
  '折扣',
  () =>
    listApiRun({
      ...searchParam.value,
      pageNum: data.value?.pageNum || 1,
      pageSize: data.value?.pageSize || 10,
    }),
);

const thisHandleEdit = (item: IDiscount) => {
  const currentData = {
    ...item,
  };
  handleEdit({ ...currentData });
};

// 删除
const { handleDelete } = useDelete(discountApi, () =>
  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize,
  }),
);

const beginAndEnd = ref<[Dayjs, Dayjs]>();
watch(
  () => beginAndEnd.value,
  (n: any, o: any) => {
    if (n) {
      searchParam.value.startTime = n[0];
      searchParam.value.endTime = n[1];
    } else {
      searchParam.value.startTime = undefined;
      searchParam.value.endTime = undefined;
    }
  },
);

const getTypeName = (id: number) => {
  if (typeList.value && typeList.value.length > 0) {
    const type = typeList.value.find((o) => o.id == id);
    if (type) return type.type;
  }
  return '';
};

const setEnable = async (checked: number, id: number) => {
  loadingId.value = id;
  const record = data.value?.records?.find((o) => o.id === id);
  if (record) {
    if (checked === 0) {
      const res = await discountApi.cancelEnable(id);
      if (!res.success) {
        message.error(res.message);
      }
    } else {
      const res = await discountApi.enable(id, record.merCode!, record.merType!.toString());
      if (!res.success) {
        message.error(res.message);
      }
    }
    record.isEnable = checked;
  }
  loadingId.value = 0;
};

const loadingId = ref<number>(0);
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="merName">商户名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="merName" v-model:value="searchParam.merName" placeholder="" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="merCode">商户code：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="merCode" v-model:value="searchParam.merCode" placeholder="" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="merType">类别：</label>
          </h-col>
          <h-col :span="4">
            <h-select style="width: 100%" v-model:value="searchParam.merType" allow-clear>
              <h-select-option v-for="(item, index) in typeList" :value="item.id" :key="index">{{
                item.type
              }}</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="isEnable">是否生效：</label>
          </h-col>
          <h-col :span="4">
            <h-select style="width: 100%" v-model:value="searchParam.isEnable" allow-clear>
              <h-select-option value="1">是</h-select-option>
              <h-select-option value="0">否</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="createTime">生效时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="beginAndEnd" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="12" style="text-align: left">
            <h-button type="primary" @click="handleCreate()"> <PlusOutlined /> 新增 </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="data?.records ?? []"
          :pagination="pagination"
          :loading="loading"
          @change="handleTableChange($event as any)"
          :scroll="{ x: 1500 }"
        >
          <template #bodyCell="{ column, record, text, index }">
            <template v-if="column.dataIndex === 'icon'">
              <img :src="text" style="width: 60px" />
            </template>
            <template v-if="column.dataIndex === 'merType'">
              {{ getTypeName(text) }}
            </template>
            <template v-if="column.dataIndex === 'isEnable'">
              <h-switch
                :checked="record.isEnable"
                :loading="loadingId === record.id"
                checked-children="生效"
                un-checked-children="未生效"
                :checkedValue="1"
                :unCheckedValue="0"
                @click="(checked) => setEnable(checked as number, record.id)"
              />
            </template>
            <template v-if="column.dataIndex === 'takeEffectTime'">
              {{ dayjs(record.startTime).format('YYYY-MM-DD HH:mm:ss') }} ~
              {{ dayjs(record.endTime).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="thisHandleEdit(record)">编辑</h-button>
              <h-button type="link" @click="handleDelete(record.id)">删除</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

    <div v-if="visible">
      <edit-dialog :show="visible" :data="editData" :typeList="typeList" @cancel="onDialogClose" @ok="handleOk">
      </edit-dialog>
    </div>
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}
</style>
