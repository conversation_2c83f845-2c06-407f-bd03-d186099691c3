<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Cascader as hCascader

} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons-vue';
import { banquetPolicyApi, banquetApi,download } from '@haierbusiness-front/apis';
import {
  IApplyFilter,
  IApply,
  BanquetStatusEnum,
  BanquetApproveStatusEnum
} from '@haierbusiness-front/common-libs';
import { getEnumOptions } from '@haierbusiness-front/utils';

import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import router from '../../../router'
import AddPolicy from './addPolicy.vue';
// const router = useRouter()

const currentRouter = ref()

onMounted(async () => {
    currentRouter.value = await router
    getCityList()
    handleTableChange({ current: 1, pageSize: 10 })
})

// 订单状态
const orderState = computed(() => {
  return getEnumOptions(BanquetStatusEnum, true);
});

const approveState = computed(() => {
  return getEnumOptions(BanquetApproveStatusEnum, true);
});

const goToAddPolicy = () => {
  currentRouter.value.push('/policy/addPolicy')
}


const columns: ColumnType[] = [
  
  {
    title: '就餐类型',
    dataIndex: 'sceneType',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '受限制用户',
    dataIndex: 'persons',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '受限制部门',
    dataIndex: 'depts',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '城市管控',
    dataIndex: 'citys',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '是否提前选择餐厅',
    dataIndex: 'cityScope',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },

  {
    title: '是否限制餐厅范围',
    dataIndex: 'restaurantScope',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },

  {
    title: '生效时间',
    dataIndex: 'createTime',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改时间',
    dataIndex: 'updateTime',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改人',
    dataIndex: 'updater',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },

  
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<IApplyFilter>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(banquetPolicyApi.list);

const {
  data:exportListData,
  run:exportListApiRun,
  loading:exportListLoading,
} = useRequest(banquetPolicyApi.exportList);

const reset = () => {
  cityCodeList.value = []

  searchParam.value = {}
  handleTableChange({ current: 1, pageSize: 10 })
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const goToDetail = (id: number) => {
  currentRouter.value.push({
    path: '/policy/addPolicy',
    query: {
      id: id
    }
  })
}
// 获取美团城市列表
const cityDict = ref<Array<any>>([])
const getCityList = () => {
  banquetApi.getCity().then(res => {
    cityDict.value = res
  })
}


const cityCodeList = ref([])
watch(() => cityCodeList.value, (n: any, o: any) => {
  if (n && n.length > 0) {
    searchParam.value.cityCode = [n[1] || n[0]]
  } else {
    searchParam.value.cityCode = undefined
  }
});

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
        
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="sceneType">就餐类型:</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value="searchParam.sceneType"
              allow-clear
              style="width: 100%"
              placeholder="请选择申请类型"
            >
              <h-select-option :value="1">宴请</h-select-option>
              <h-select-option :value="2">外卖</h-select-option>
            </h-select>
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="userCodeOrName">受限制用户:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="请输入" v-model:value="searchParam.userCodeOrName"  style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="dept">受限制部门:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="请输入" v-model:value="searchParam.dept"  style="width: 100%" allow-clear />
          </h-col>


        </h-row>


        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button  style="margin-right: 10px" type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>

            <h-button type="primary" :loading="exportListLoading" @click="exportListApiRun(searchParam);">
              导出
            </h-button>
          </h-col>
        </h-row>


        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
            <h-button type="primary"  @click="goToAddPolicy">
              新增限制
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">

            <template v-if="column.dataIndex === 'sceneType'">
              {{ record.sceneType == 1 ? '宴请' :'外卖' }}
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="goToDetail(record.id)">管理权限</h-button>

            </template>

            <template v-if="column.dataIndex === 'cityScope'">
              {{ record.advanceChooseRestaurant ? '是' :'否' }}
            </template>

            <template v-if="column.dataIndex === 'creatorName'">
              {{ record.creatorName }}({{ record.creator }})
            </template>

            <template v-if="column.dataIndex === 'updater'">
              {{ record.updaterName }}({{ record.updater }})
            </template>
        
            <template v-if="column.dataIndex === 'restaurantScope'">
              {{ record.restaurantScope ? '是' :'否' }}
            </template>
            
          </template>
        </h-table>
      </h-col>
    </h-row>
    

  </div>
</template>

<style scoped lang="less">

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

</style>
