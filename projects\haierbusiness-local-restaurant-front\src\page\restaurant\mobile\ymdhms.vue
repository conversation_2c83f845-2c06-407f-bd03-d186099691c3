<script setup lang="ts">
import { useAttrs, ref,watch } from "vue";
const emit = defineEmits(['update:modelValue', 'confirm']);
const attrs = useAttrs();
 
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  values: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: '请选择时间'
  },
  valueFormat: {
    type: String,
    default: 'YYYY-MM-DD HH:mm:ss'
  }
});
 
// 是否显示弹出层
const columns = ref([]);
const Mdays = ref('');
const Dindex = ref(null);
const picker = ref(null);
 
//  获取某年某月多少天
function getCountDays(year, month) {
  const day = new Date(year, month, 0);
  return day.getDate();
}
 
// 获取年月日时分秒列信息
function getColumns() {
  const strTime = props.values;
  let vModuleDate;
  if (props.values !== '') {
    vModuleDate = new Date(strTime.replace(/-/g, '/'));
  } else {
    vModuleDate = new Date(); // 没有传入时间则默认当前时刻
  }
  const Y = vModuleDate.getFullYear();
  const M = vModuleDate.getMonth();
  const D = vModuleDate.getDate();
  const h = vModuleDate.getHours();
  const m = vModuleDate.getMinutes();
  const s = vModuleDate.getSeconds();

  const year = {
    values: [],
    defaultIndex: 0
  }; // 获取前后十年数组
  year.values = [];
  const CurrentDay = new Date().getFullYear();
  for (let i = CurrentDay - 4; i < CurrentDay + 1; i += 1) {
    year.values.push(i);
  }
  year.defaultIndex = year.values.indexOf(Y);

  const yearArr = []
  year.values.forEach(item => {
    const temp = {
      text: item,
      value: item
    }
    yearArr.push(temp)
  })

 
  const month = {
    values: [],
    defaultIndex: 0
  };
  // 获取12月数组
  month.defaultIndex = M;
  month.values = Object.keys([...Array(13)]).map((item) => {
    if (+item + 1 <= 10) {
      return `0${item}`;
    }
    if (+item + 1 === 11) {
      return `${item}`;
    }
    return (+item + 0).toString();
  });
  month.values.splice(0, 1);

  const monthArr = []
  month.values.forEach(item => {
    const temp = {
      text: item,
      value: item
    }
    monthArr.push(temp)
  })

  const days = getCountDays(Y, Mdays.value === '' ? M + 1 : Mdays.value);
  const day = {
    values: [],
    defaultIndex: 0
  }; // 创建当月天数数组
  day.defaultIndex = Dindex.value === null ? D - 1 : Dindex.value;
  day.values = Object.keys([...Array(days + 1)]).map((item) => {
    if (+item + 1 <= 10) {
      return `0${item}`;
    }
    if (+item + 1 === 11) {
      return `${item}`;
    }
    return (+item + 0).toString();
  });
  day.values.splice(0, 1);

  const dayArr = []
  day.values.forEach(item => {
    const temp = {
      text: item,
      value: item
    }
    dayArr.push(temp)
  })

  const hour = {
    values: [],
    defaultIndex: 0
  }; // 创建小时数组
  hour.defaultIndex = h;
  hour.values = Object.keys([...Array(24)]).map((item) => {
    if (+item + 1 <= 10) {
      return `0${item}`;
    }
    if (+item + 1 === 11) {
      return `${item}`;
    }
    return (+item + 0).toString();
  });

  const hourArr = []
  hour.values.forEach(item => {
    const temp = {
      text: item,
      value: item
    }
    hourArr.push(temp)
  })

  const mi = {
    values: [],
    defaultIndex: 0
  }; // 创建分钟数组
  mi.defaultIndex = m;
  mi.values = Object.keys([...Array(60)]).map((item) => {
    if (+item + 1 <= 10) {
      return `0${item}`;
    }
    if (+item + 1 === 11) {
      return `${item}`;
    }
    return (+item + 0).toString();
  });

  const miArr = []
  mi.values.forEach(item => {
    const temp = {
      text: item,
      value: item
    }
    miArr.push(temp)
  })

  const ss = {
    values: [],
    defaultIndex: 0
  }; // 创建秒数数组
  ss.defaultIndex = s;
  ss.values = Object.keys([...Array(60)]).map((item) => {
    if (+item + 1 <= 10) {
      return `0${item}`;
    }
    if (+item + 1 === 11) {
      return `${item}`;
    }
    return (+item + 0).toString();
  });

  const ssArr = []
  ss.values.forEach(item => {
    const temp = {
      text: item,
      value: item
    }
    ssArr.push(temp)
  })

  // 设置默认选项当前年
  if (props.valueFormat.includes('YYYY')) {
    columns.value.push(yearArr);
  }
  if (props.valueFormat.includes('MM')) {
    columns.value.push(monthArr); // 获取当月的天数
  }
  if (props.valueFormat.includes('DD')) {
    columns.value.push(dayArr);
  }
  if (props.valueFormat.includes('HH')) {
    columns.value.push(hourArr);
  }
  if (props.valueFormat.includes('mm')) {
    columns.value.push(miArr);
  }
  if (props.valueFormat.includes('ss')) {
    columns.value.push(ssArr);
  }
}

// 改变年月时获取日期数据
function onChange(values) {
  // a为所有列备选项值的数组
  const days = getCountDays(values.selectedValues[0], values.selectedValues[1]);
  const newDays = {
    values: [],
  };
  newDays.values = Object.keys([...Array(days + 1)]).map((item) => {
    if (+item + 1 <= 10) {
      return `0${item}`;
    }
    if (+item + 1 === 11) {
      return `${item}`;
    }
    return (+item + 0).toString();
  });
  newDays.values.splice(0, 1);
  // 根据年、月获取日期的值
  const dayArr = []
  newDays.values.forEach(item => {
    const temp = {
      text: item,
      value: item
    }
    dayArr.push(temp)
  })
  columns.value[2] = dayArr
  // picker.value.setColumnIndex(2, values[2] - 1);
}
 
//  关闭弹框
function onCancel() {
  Mdays.value = '';
  Dindex.value = null;
  emit('update:modelValue', false);
}
 
// 时间选择器确定
function onConfirm(val:any) {
 
  emit('update:modelValue', false);
  emit('confirm', val);
}
 
watch(
  () => props.modelValue,
  (val) => {
    if (!val) emit('update:modelValue');
    columns.value = [];
    getColumns();
  }
);
 
watch(
  () => props.values,
  (val) => {
    if (val === '') {
      Mdays.value = '';
      Dindex.value = null;
    }
  }
);
</script>
 
<template>
  <!-- 弹出层 -->
  <van-popup :show="modelValue" round position="bottom" teleport="body" @close="onCancel">
    <!-- 时间选择 -->
    <van-picker
      v-bind="attrs"
      ref="picker"
      show-toolbar
      :title="title"
      :columns="columns"
      @change="onChange"
      @cancel="onCancel"
      @confirm="onConfirm"
    />
  </van-popup>
</template>
 
<style lang="less" scoped></style>