type keys = 'GUEST' | 'LOGGED' | 'SWAGGER' | 'FOR_LOGIN';

/**
 * 这是角色，角色不在系统中做任何判断，不要使用这个常量进行权限判断
 */
export const RoleSystemConstant = {
  GUEST: { "roleId": 1, "name": "访客" },
  LOGGED: { "roleId": 2, "name": "已登录权限" },
  SWAGGER: { "roleId": 3, "name": "swagger" },
  FOR_LOGIN: { "roleId": 4, "name": "登录资源" },


  of: (roleId?: number): { "roleId": number, "name": string } | null => {
    for (const key in RoleSystemConstant) {
      const item = RoleSystemConstant[key as keys];
      if (roleId === item.roleId) {
        return item;
      }
    }
    return null;
  },
  values: (filed: string): any[] => {
    return Object.values(RoleSystemConstant).map(it => (it as any)[filed]).filter(it => it != undefined)
  }
}