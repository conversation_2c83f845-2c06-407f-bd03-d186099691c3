<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  Switch as hSwitch,
  CheckboxGroup as hCheckboxGroup,
  Checkbox as hCheckbox,
  Image as hImage,
  message,
} from 'ant-design-vue';
import {
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { computed, ref, watch } from 'vue';
import { fileApi } from '@haierbusiness-front/apis';
import type { Ref } from 'vue';
import { IndicatorData, Datum } from '@haierbusiness-front/common-libs';
import { de } from 'element-plus/es/locale';
import { Codemirror } from 'vue-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { oneDark } from '@codemirror/theme-one-dark';
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil';
import { HeaderConstant } from '@haierbusiness-front/common-libs';
import Echarts from "./echarts.vue"
import type { UploadChangeParam, UploadFile } from 'ant-design-vue';
const extensions = [javascript(), oneDark];
const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false);

interface Props {
  show: boolean;
  data: IndicatorData;
  labelList: Array<Datum>;
  knowCenterOptions: any;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: IndicatorData = {
  reportName: '',
  echartsStatus: 1,
  id: null,
};

const rules: Record<string, Rule[]> = {
  reportName: [{ required: true, message: '请输入指标名称' }],
  queryCondition: [{ required: true, message: '请输入查询条件' }],
  echartsJson: [{ required: true, message: '请输入图标配置' }],
  indexReportContent: [{ required: true, message: '请输入指标详细说明' }],
  knowId: [{ required: true, message: '请选择知识词条' }],
  labelId: [{ required: true, message: '请选择标签' }],
  imagesUrl: [{ required: true, message: '请上传图片' }],
};

const indexData: Ref<IndicatorData> = ref(props.data ? props.data : defaultData);

watch(props, (newValue) => {
  indexData.value = ({ ...newValue.data } as IndicatorData) || defaultData;
});

const emit = defineEmits(['cancel', 'ok']);

const visible = computed(() => props.show);

const handleOk = () => {
  from.value
    .validate()
    .then(() => {
      if (indexData.value.labelId?.length) {
        indexData.value.labelId = indexData.value.labelId.toString();
      }
      confirmLoading.value = true;
      emit('ok', indexData.value, () => {
        confirmLoading.value = false;
      });
      confirmLoading.value = false;
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};
// 删除图片
const deleteImg = () => {
  indexData.value.imagesUrl = '';
};
const fileList = ref([]);
const beforeUpload = (file: UploadFile) => {
  const isLt5M = file.size && file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    message.error('文件大小不能超过5M!');
  }
  return isLt5M;
};
const handleChange = (info: UploadChangeParam) => {
  console.log(info);
  if (info.file.status === 'uploading') {
  }

  if (info.file.status === 'done') {
    console.log(info.file.response.data.path);
    // information.value.imgUrl = info.file.response.content.url;
    // fileList.value = info.fileList;
    indexData.value.imagesUrl = `https://businessmanagement-test.haier.net/${info.file.response.data.path}`;
  }
  if (info.file.status === 'error') {
    // loading.value = false;
    message.error('上传出错！');
  }
};
// 复制图片
const getclipboardImg = (type: number) => {
  // 首先要确保浏览器支持Clipboard API
  if (typeof navigator.clipboard !== 'undefined' && typeof ClipboardItem === 'function') {
    // 从剪贴板读取内容
    navigator.clipboard.read().then(async function (items) {
      console.log(items, 'item');
      for (let item of items) {
        console.log(item.types[0].indexOf('image'), '111');

        if (item.types[0].indexOf('image') !== -1) {
          const blob = await item.getType('image/png');
          // const url= URL.createObjectURL(blob);
          let formData = new FormData();
          formData.append('file', blob, 'image.png');
          fileApi.upload(formData).then((res: any) => {
            if (
              window.location.origin.indexOf('http://127.0.0.1') !== -1 ||
              window.location.origin.indexOf('http://localhost') !== -1
            ) {
              indexData.value.imagesUrl = `https://businessmanagement-test.haier.net/${res.path}`;
            } else {
              indexData.value.imagesUrl = `${window.location.origin}/${res.path}`;
            }
          });
        }
      }
    });
  } else {
    console.log('当前浏览器不支持Clipboard API');
  }
};
</script>

<template>
  <h-modal
    v-model:visible="visible"
    :title="indexData.id ? '编辑指标' : '新增指标'"
    :width="800"
    @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
  >
    <h-form ref="from" :model="indexData" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules">
      <h-form-item label="指标名称" name="reportName">
        <h-input v-model:value="indexData.reportName" style="width: 100%" />
      </h-form-item>
      <h-form-item label="查询条件" name="queryCondition">
        <!-- <h-input v-model:value="indexData.queryCondition" style="width: 100%" /> -->
        <codemirror
          v-model="indexData.queryCondition"
          placeholder="Code gose here..."
          :style="{ height: '180px' }"
          :autofocus="true"
          :indent-with-tab="true"
          :tabSize="2"
          :extensions="extensions"
        />
      </h-form-item>
      <h-form-item label="图表配置" name="echartsJson">
        <!-- <h-input v-model:value="indexData.echartsJson" style="width: 100%" /> -->
        <codemirror
          v-model="indexData.echartsJson"
          placeholder="Code gose here..."
          :style="{ height: '180px' }"
          :autofocus="true"
          :indent-with-tab="true"
          :tabSize="2"
          :extensions="extensions"
        />
      </h-form-item>
      <h-form-item v-if="indexData.echartsJson&&(indexData.echartsJson.indexOf('饼图')!=-1||indexData.echartsJson.indexOf('柱状图')!=-1||indexData.echartsJson.indexOf('分布图')!=-1)" label="">
          <Echarts style="height:300px;margin-left:124px;" :echartsJson="indexData.echartsJson"></Echarts>
      </h-form-item>
      <h-form-item label="指标详细说明" name="indexReportContent">
        <h-textarea v-model:value="indexData.indexReportContent" style="width: 100%" />
      </h-form-item>
      <h-form-item label="指标状态" name="echartsStatus">
        <!-- <h-switch v-model:checked="indexData.echartsStatus" :checkedValue="1" /> -->
        <h-select v-model:value="indexData.echartsStatus" style="width: 100%">
          <h-select-option :value="0">禁用</h-select-option>
          <h-select-option :value="1">启用</h-select-option>
        </h-select>
      </h-form-item>
      <h-form-item label="知识词条" name="knowId">
        <h-select v-model:value="indexData.knowId" style="width: 100%">
          <h-select-option v-for="item in props.knowCenterOptions" :value="item.id">{{
            item.entryName
          }}</h-select-option>
        </h-select>
      </h-form-item>
      <h-form-item label="标签" name="labelId">
        <h-checkbox-group v-model:value="indexData.labelId" name="checkboxgroup" style="width: 100%">
          <h-checkbox v-for="item in props.labelList" :value="item.id">{{ item.labelName }}</h-checkbox>
        </h-checkbox-group>
      </h-form-item>
      <h-form-item label="图片" name="imagesUrl">
        <a-upload
          v-if="!indexData.imagesUrl"
          v-model:file-list="fileList"
          name="file"
          list-type="picture-card"
          class="avatar-uploader"
          :show-upload-list="false"
          :action="'/hbweb/data/hb/common/api/file/upload'"
          :headers="{
            'Hb-Token': token,
          }"
          :before-upload="beforeUpload"
          @change="handleChange"
        >
          <div>
            <loading-outlined v-if="loading"></loading-outlined>
            <plus-outlined v-else></plus-outlined>
            <div class="ant-upload-text">上传图片</div>
          </div>
        </a-upload>
        <div class="imgItem" v-if="indexData.imagesUrl">
          <CloseCircleOutlined class="deleteIcon" @click.capture="deleteImg()" />
          <h-image :width="100" :height="100" :src="indexData.imagesUrl" />
        </div>
        <a v-if="!indexData.imagesUrl" @click="getclipboardImg(3)">获取剪切板图片</a>
      </h-form-item>
    </h-form>
  </h-modal>
</template>

<style lang="less" scoped>
.important {
  color: red;
}
.imgItem {
  position: relative;
  display: inline-block;
  margin-right: 4px;
  .deleteIcon {
    position: absolute;
    right: 0px;
    z-index: 9999;
  }
}
</style>
