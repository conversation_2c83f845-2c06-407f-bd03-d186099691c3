<template>
  <div style="height: 78.2vh" background="rgba(0,0,0,0)">
    <div id="map">
      <div class="map-desc">
        <el-tooltip placement="top" effect="light">
          <template #content>
            <p style="color: #fff">
              <b>地图省份颜色标注：</b><br />
              <template v-for="item in colors">
                <span class="square" :style="{ background: item.color }"></span>：订单数量为{{ item.min }}以上<br />
              </template>
              <span class="square"></span>：无订单
            </p>
          </template>
          <p>
            地图省份颜色标注：
            <InfoCircleOutlined />
          </p>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import 'element-plus/theme-chalk/dark/css-vars.css';
import { ElTooltip } from 'element-plus';
import { Scene, PolygonLayer, LineLayer, PointLayer, Popup } from '@antv/l7';
import { Mapbox } from '@antv/l7-maps';
import { ref, onMounted } from 'vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { queryOffsiteMapData, queryOffsiteCityData } from '@haierbusiness-front/apis/src/data/board/travel';
import { EventBus } from '../../eventBus';
import chinaData from '@/assets/geojson/china.json';
const loading = ref(false);
let areaCode = ['100000']; //地图坐标,默认中国地图
const colors = [
  {
    min: 20,
    color: '#012a9e',
  },
  {
    min: 10,
    color: '#0550ee',
  },
  {
    min: 0,
    color: '#29a3ff',
  },
];
let mapData = []; //地图上的业务数据
let chart;
const mapJson = {}; //地图数据
EventBus.on((event) => {
  if (event == 'refresh') {
    areaCode = ['100000'];
    queryData();
  }
});
const queryData = async () => {
  loading.value = true;
  const data = await queryOffsiteMapData();
  mapData = data.rows;
  await getMap();
  loading.value = false;
};

onMounted(() => {
  queryData();
});
//获取区域对应的数据
const getDataByCode = (code) => {
  const data = mapData.find((item) => item[1] == code);
  if (!data) return 0;
  return data[data.length - 1] || 0;
};

//获取区域对应的数据
const getDataByCodeTotal = (code) => {
  const data = mapData.find((item) => item[1] == code);
  if (!data) return 0 + '%';
  let total = 0;
  mapData.forEach((item) => {
    total = total + item[item.length - 1];
  });
  return ((data[data.length - 1] / total) * 100).toFixed(2) + '%' || 0 + '%';
};
//获取地图坐标
const getMapJson = async () => {
  const code = areaCode[areaCode.length - 1];
  if (!code) return;
  if (code == '100000') return chinaData;
  const localMap = localStorage['mapJson' + code];
  if (localMap) {
    mapJson[code] = JSON.parse(localMap);
  }
  if (!mapJson[code]) {
    const res = await (await fetch(`https://geo.datav.aliyun.com/areas_v2/bound/${code}_full.json`)).json();
    mapJson[code] = res;
    localStorage['mapJson' + code] = JSON.stringify(mapJson[code]);
  }
  return mapJson[code];
};
//生成地址
const getMap = async () => {
  const mapJson = await getMapJson();
  chart && chart.destroy();
  const scene = new Scene({
    id: 'map',
    logoVisible: false,
    map: new Mapbox({
      pitch: 0,
      style: 'blank',
      center: [116.368652, 39.93866],
      maxZoom: 7,
      minZoom: 1,
    }),
  });
  scene.on('loaded', () => {
    addLayer(scene, mapJson);
    chart = scene;
  });
};
//添加地图图层
const addLayer = (scene, mapJson) => {
  var labeldata = mapJson.features.map(function (fe) {
    if (fe.properties.centroid) {
      fe.properties.code = fe.properties.adcode;
      fe.properties.value = fe.properties.name;
      fe.properties.longitude = fe.properties.centroid[0] ? fe.properties.centroid[0] : '';
      fe.properties.latitude = fe.properties.centroid[1] ? fe.properties.centroid[1] : '';

      return fe.properties;
    }
  });
  //点图层
  const pointLayer = new PointLayer({ zIndex: 5 })
    .source(
      labeldata.filter((item) => item),
      {
        parser: {
          type: 'json',
          x: 'longitude',
          y: 'latitude',
        },
      },
    )
    .size(8)
    .shape('name', 'text')
    .color('#fff');
  scene.addLayer(pointLayer);
  const polygonLayer = new PolygonLayer({ autoFit: true })
    .source(mapJson)
    .color('adcode', (adcode) => {
      const data = getDataByCode(adcode);
      if (!data) return 'rgba(239,243,255,0.05)';
      if (data >= 20) {
        return '#012a9e';
      }
      if (data >= 10) {
        return '#0550ee';
      }
      return '#29a3ff';
    })
    .active(true)
    .shape('fill')
    .style({
      opacity: 1,
    });
  //图层边界
  const lineLayer = new LineLayer({ zIndex: 2 }).source(mapJson).color('rgb(93,112,146)').size(0.6).style({
    opacity: 1,
  });
  scene.addLayer(polygonLayer);
  scene.addLayer(lineLayer);
  addEvent(scene, polygonLayer, lineLayer);

  polygonLayer.on('mousemove', (param) => {
    if (param.feature.properties.name != payTypeCheck.value.name) {
      payTypeCheck.value = {
        name: param.feature.properties.name,
        adcode: param.feature.properties.adcode,
        lngLat: param.lngLat,
      };
      scene.removeLayer(lineLayerActive);
      showPop(scene, mapJson);
    }
  });
};
const payTypeCheck = ref<any>('');
let lineLayerActive;
const showPop = (scene, mapJson) => {
  if (payTypeCheck.value != '') {
    lineLayerActive = new LineLayer({ zIndex: 3 })
      .source({
        type: 'FeatureCollection',
        features: [],
      })
      .color('#ff6600')
      .size(1)
      .style({
        opacity: 1,
      });
    let mapJsonParam = {
      ...mapJson,
      features: mapJson.features.filter((item) => item.properties.adcode == payTypeCheck.value.adcode),
    };
    lineLayerActive.setData(mapJsonParam);
    scene.addLayer(lineLayerActive);

    const popup = new Popup({
      offsets: [0, 0],
      closeButton: false,
    })
      .setLnglat(payTypeCheck.value.lngLat)
      .setHTML(
        `<span>地区: ${payTypeCheck.value.name}</span><br><span>订单数量: ${getDataByCode(
          payTypeCheck.value.adcode,
        )}</span><br><span>订单占比: ${getDataByCodeTotal(payTypeCheck.value.adcode)}</span>`,
      );

    scene.addPopup(popup);
  }
};
const properties: any = ref();
const addEvent = (scene, polygonLayer, lineLayer) => {
  polygonLayer.on('click', (e) => {
    const { adcode } = e.feature.properties;
    if (areaCode.length == 1) {
      polygonLayer.off('click');
      areaCode.push(adcode);
      properties.value = e.feature.properties;
      changeMap(polygonLayer, lineLayer);
      setTimeout(() => {
        scene.on('zoomend', zoomend);
      }, 300);
    }
  });
};
//添加地图上的点
const addPoint = async () => {
  const code = areaCode[areaCode.length - 1];
  const data = await queryOffsiteCityData(code);
  const { rows } = data;
  if (!rows) return;
  const _data: any = [];
  rows?.forEach((item) => {
    if (!item[2]) return;
    let jwd = item[2] || '';
    jwd = jwd.split('/');
    _data.push({
      name: item[0],
      code: item[1],
      value: item[3],
      longitude: jwd[0] - 0,
      latitude: jwd[1] - 0,
    });
  });
  // //点图层
  const pointLayer = new PointLayer()
    .source(_data, {
      parser: {
        type: 'json',
        x: 'longitude',
        y: 'latitude',
      },
    })
    .shape('circle')
    .size('value', [10, 50])
    .color('color', (color) => {
      // if(color=='red')return '#E8684A'
      // if(color=='yellow')return '#F6BD16'
      // if(color=='blue')return '#5B8FF9'
      return '#edae5d';
    })
    // .animate({
    //     rings: 3,
    //     speed: 1
    // })
    .style({
      opacity: 0.5,
      strokeWidth: 1,
    });
  chart.addLayer(pointLayer);
  const popup = new Popup({
    offsets: [0, 0],
    closeButton: false,
  });
  pointLayer.on('mousemove', (e) => {
    const { feature } = e;
    popup.setLnglat(e.lngLat).setHTML(
      `<div style="text-align:left;padding:10px 10px 5px;min-width:195px;max-width:200px">
                        <p style="font-size:14px">${feature.name}<span style="color:#6ed1ff;font-size:12px"></span></p>
                        <p style="font-size:12px">订单数：${feature.value}</p>
                    </div>`,
    );
    chart.addPopup(popup);
  });
};
//切换成省份地图
const changeMap = async (polygonLayer, lineLayer) => {
  const mapJson = await getMapJson();
  polygonLayer.setData(mapJson);
  lineLayer.setData(mapJson);
  polygonLayer.fitBounds();
  lineLayer.fitBounds();
  //添加下钻场景
  addPoint();
};
const zoomend = (e) => {
  if (chart.getZoom() < 4) {
    areaCode.pop();
    getMap();
  }
};
</script>
<style scoped>
#map {
  width: 100%;
  height: 78vh;
  position: relative;
}

.map-desc {
  position: absolute;
  left: 10px;
  top: 10px;
  font-size: 14px;
  z-index: 99;
}

.tooltip-content {
  line-height: 1.5;
}

span.square,
span.circle {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin-right: 2px;
  background: rgba(239, 243, 255, 0.05);
}

span.circle {
  border-radius: 50%;
}
</style>
<style>
.l7-popup-anchor-bottom .l7-popup-tip {
  border-top-color: rgba(0, 11, 45, 0.8) !important;
}

.l7-popup-anchor-bottom .l7-popup-content {
  background: rgba(0, 11, 45, 0.8) !important;
}
</style>
