import {
    IPageResponse,
    ParkadeRecordReq,
    ParkadeListRes,
    ParkadeExportLogRes,
    ParkadeExportLogReq,
    CollectInfoByIdRes,
    OperationListRes,
    ConfirmParams,
    ISendCodeCaptcha
} from '@haierbusiness-front/common-libs'
import { get, post, download } from '../request'

export const parkadeApi = {
    /**
     * 车辆权益券订单记录列表
     */
    list: (params: ParkadeRecordReq): Promise<IPageResponse<ParkadeListRes>> => {
        return get('parkade/api/park/order/list', params)
    },

    /**
     * 导出车辆权益券订单记录列表
     */
    export: (params: ParkadeExportLogReq): Promise<IPageResponse<ParkadeListRes>> => {
        return download('parkade/api/park/order/parkOrderList/export', params)
    },

    /**
     * 导出记录列表
     */
    exportLogList: (params: any): Promise<IPageResponse<ParkadeExportLogRes>> => {
        return get('parkade/api/park/export-record/list', params)
    },

    /**
     * 车辆权益券确认列表
     */
    confirmList: (params: any): Promise<IPageResponse<ParkadeListRes>> => {
        return get('parkade/api/park/collect/list', params)
    },

    /**
     * 导出账单汇总
     */
    exportParkCollectList: (params: any): Promise<IPageResponse<ParkadeExportLogRes>> => {
        return download('parkade/api/park/collect/parkCollectList/export', params)
    },

     /**
     * 账单汇总
     */
     parkCollect: (params: any): Promise<IPageResponse<ParkadeListRes>> => {
        return post('parkade/api/park/collect/collect', params)
    },

    /**
     * 汇总单详情
     */
    collectInfoById: (params: any): Promise<IPageResponse<CollectInfoByIdRes>> => {
        return get('parkade/api/park/collect/collectInfoById', params)
    },

     /**
     * 汇总单操作记录
     */
     operationList: (params: any): Promise<IPageResponse<OperationListRes>> => {
        return get('parkade/api/park/collect/operationList', params)
    },

     /**
     * 子订单信息列表
     */
     subOrderList: (params: any): Promise<IPageResponse<OperationListRes>> => {
        return get('parkade/api/park/collect/subOrderList', params)
    },
     /**
     * 子订单信息导出
     */
     subOrderExport: (params: any): Promise<IPageResponse<OperationListRes>> => {
        return download('parkade/api/park/collect/subOrder/export', params)
    },

    /**
     * 账单确认
     */
      parkConfirm: (params: ConfirmParams): Promise<IPageResponse<any>> => {
        return post('parkade/api/park/collect/confirm', params)
    },

    /**
     * 维护内接单
     */
    settlement: (params: ConfirmParams): Promise<IPageResponse<any>> => {
        return post('parkade/api/park/collect/settlement', params)
    },

    /**
     * 获取园区列表
     */
    zoneList: (): Promise<IPageResponse<OperationListRes>> => {
        return get('parkade/api/parkade/zone/list')
    },

}