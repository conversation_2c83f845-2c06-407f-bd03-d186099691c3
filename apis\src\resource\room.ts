import { HotelSyncRecordRes,DistrictProviderMapPageReq } from '@haierbusiness-front/common-libs'
import { download, get, post,filepost,originalGet} from '../request'

export const roomApi = {
    // 房型转换规则列表
    getRoomTransformatRule: (params:any): Promise<void> => {
        return get('/hotel-mapping/api/roomTransformat/page', params)
    },
    // 新增 或 修改
    createRoomTransformatRule: (params:any): Promise<void> => {
        return post('/hotel-mapping/api/roomTransformat/createOrUpdate', params)
    },
    // 删除
    deleteRoomTransformatRule: (params:any): Promise<void> => {
        return post('/hotel-mapping/api/roomTransformat/delete', params)
    },
}   