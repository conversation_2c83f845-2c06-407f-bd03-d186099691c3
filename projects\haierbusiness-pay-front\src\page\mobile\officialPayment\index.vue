<script setup lang="ts">
import {showFailToast, <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>, Form as <PERSON><PERSON><PERSON>, Field as <PERSON><PERSON><PERSON>, CellGroup as VanCellGroup,showDialog, showSuccessToast, NoticeBar,Popup as VanPopup , closeToast, showLoadingToast} from 'vant';
import 'vant/es/notice-bar/style'
import {payApi, compositionPayApi, virtualPayApi,budgetHaierPayApi} from '@haierbusiness-front/apis';
import {IPayData, IQueryVirtualAccountsResponse, PaySourceConstant,IloginUser,IBudgetHaierTypesResponse, HaierBudgetTypeConstant } from '@haierbusiness-front/common-libs';
import {computed, PropType, ref} from 'vue';
import {removeStorageItem} from '@haierbusiness-front/utils';
import {useRequest} from 'vue-request'
import DeptGems1Budget from './deptBudget/deptGems1Budget.vue';
import DeptKems1Budget from './deptBudget/deptKems1Budget.vue';
import DeptKems2Budget from './deptBudget/deptKems2Budget.vue';
import DeptRrs1Budget from './deptBudget/deptRrs1Budget.vue';
import DeptRrs2Budget from './deptBudget/deptRrs2Budget.vue';
import DeptBcc1Budget from './deptBudget/deptBcc1Budget.vue';
import DeptBcc2Budget from './deptBudget/deptBcc2Budget.vue';
import DeptHbc1Budget from './deptBudget/deptHbc1Budget.vue';
import DeptHbc2Budget from './deptBudget/deptHbc2Budget.vue';
import DeptXwBudget from './deptBudget/deptXwBudget.vue';

// import userSelectM from 

interface Props {
  payData: IPayData,
  payTypes: number[],
  accounts: IQueryVirtualAccountsResponse[],
  loginUser:IloginUser
}

const payChild = ref()


const payComplete = () => {
    emit('payComplete', true)
}

const isPayLoading = (val:boolean) =>{
  loading.value = val
}

const props = withDefaults(defineProps<Props>(), {})

const payData = computed(() => props.payData)
const payTypes = computed(() => props.payTypes)

const emit = defineEmits(["setIsPayComplete",'payComplete']);

// 移动端支付相关
const checked = ref()
// 钱包底部弹出层
const show = ref(false)
const account = ref<number | null>()
const loading = ref(false)

const setAccountChecked = (value: string) => {
  accountChecked.value = value
  checked.value = '5'
  changeVirtualAccount()
}

const setAccount = (value: number | null) => {
  account.value = value
}

const setShow = (value: boolean) => {
  show.value = value
}

const setChecked = (value: string) => {
  checked.value = value
  accountChecked.value = ''
  virtualAccount.value = {}
}

//#region 企业支付相关

const virtualShow = ref(false)
const accountChecked = ref()

const setVirtualShow = (value: boolean) => {
  virtualShow.value = value
}

//#endregion

// 下拉弹窗 
const showPicker = ref<boolean>(false)

const payH5 = (type: string) => {
  compositionPayApi.h5(
      {
        orderCode: payData.value.orderCode,
        applicationCode: payData.value.applicationCode,
        payTypes: payData.value.payTypes,
        username: payData.value.username,
        providerCode: payData.value.providerCode,
        amount: Number(payData.value.amount),
        notifyUrl: payData.value.notifyUrl,
        orderDetailsUrl: payData.value.orderDetailsUrl,
        callbackUrl: payData.value.callbackUrl,
        description: payData.value.description,
        payload: payData.value?.payload,
        compositionPayType: type,
        paymentMethod: 2,
        paySource: PaySourceConstant.MOBILE.type
      },
      {
        applicationCode: payData.value.applicationCode,
        nonce: payData.value.hbNonce,
        timestamp: payData.value.hbTimestamp,
        sign: payData.value.sign,
        excludes: "compositionPayType,paymentMethod,paySource"
      }
  ).then(it => {
    if (it.url) {
      window.location.href = it.url
    }
  }).finally(() => {
    loading.value = false
  })
}

const virtualAccountsData = computed(() => props?.accounts || [])

const changeVirtualAccount = () => {
  const selectAccount = virtualAccountsData.value.filter(it => it.accountNo === accountChecked.value)
  virtualAccount.value = selectAccount[0]
}

const virtualAccount = ref<IQueryVirtualAccountsResponse>({})

const {
  data,
  run: payRun,
  loading: payLoading,
} = useRequest(
    virtualPayApi.pay, {
      onSuccess: () => {
        if (checked.value === '5') {
          showSuccessToast('支付成功')
          virtualShow.value = false
        }
        
        payH5Complete(true)
      }
    }
);
const pay = (captcha?: string) => {
  payRun(
      {
        accountNo: accountChecked.value,
        captcha: captcha ?? '',
        // - 通用参数
        orderCode: payData.value?.orderCode,
        applicationCode: payData.value?.applicationCode,
        payTypes: payData.value?.payTypes,
        username: payData.value?.username,
        providerCode: payData.value?.providerCode,
        amount: Number(payData.value?.amount),
        notifyUrl: payData.value?.notifyUrl,
        orderDetailsUrl: payData.value?.orderDetailsUrl,
        callbackUrl: payData.value?.callbackUrl,
        description: payData.value?.description,
        payload: payData.value?.payload,
        paymentMethod: 2,
        paySource: PaySourceConstant.MOBILE.type
      },
      {
        applicationCode: payData.value?.applicationCode,
        excludes: "accountNo,captcha,paymentMethod,paySource",
        nonce: payData.value?.hbNonce,
        timestamp: payData.value?.hbTimestamp,
        sign: payData.value?.sign,
      }
  )
}

const mobilePay = () => {
  payChild.value.pay()
  // payComplete()
}


// 查询支持的预算类型
const supportBudgetTyps = ref<IBudgetHaierTypesResponse[]>();
const currentBudgetType = ref<any>();
const currentBudgetTypeName = ref<any>('');

(() => {
    budgetHaierPayApi.searchBudgetTypes(
        {
            applicationCode: payData.value.applicationCode,
            businessType: payData.value?.businessType
        }
    ).then(it => {
        supportBudgetTyps.value = it
        currentBudgetType.value = supportBudgetTyps.value ? supportBudgetTyps.value[0].budgetType : ""
        currentBudgetTypeName.value = supportBudgetTyps.value ? supportBudgetTyps.value[0].budgetTypeName : ""

        if(currentBudgetType.value == HaierBudgetTypeConstant.DEPT_XW.code) {
          showLoadingToast('加载中...');
          setTimeout(() => {
            closeToast();
            payChild.value?.chose()
          }, 1000);
        }
    });
})();

const customFieldName = {
  text: 'budgetTypeName',
  value: 'budgetType',
};

// 全屏loading
const showLoading = ref(false)


// 选择预算类型
const onConfirm = ({ selectedOptions }) => {
    console.log(selectedOptions,"------------------")
    currentBudgetTypeName.value = selectedOptions[0]?.budgetTypeName
    currentBudgetType.value = selectedOptions[0]?.budgetType
    showPicker.value = false;
    // 如果是小微预算
    if (currentBudgetType.value === HaierBudgetTypeConstant.DEPT_XW.code) {
      showLoadingToast('加载中...');
      setTimeout(() => {
        closeToast();
        payChild.value?.chose()
      }, 1000);
    }
};

/**
 * 支付完成
 */
const payH5Complete = (result: boolean) => {
  emit('setIsPayComplete', result)
  let countdown = 3;
  removeStorageItem("pay-data-code" + payData.value.orderCode);
  if (payData.value.callbackUrl) {
    setTimeout(() => {
      window.location.href = payData.value.callbackUrl!!
    }, 2000);
  } else {
    setTimeout(() => {
      // 跳转白屏不美观,期望关闭标签页
      window.location.href = "about:blank";
      window.close();
    }, 2000);
  }
}

</script>

<template>
  <div class="mobile-container-offcial">
    <div class="price-container">
        <div class="order-no">
            订单号:{{ payData.orderCode }}
        </div>
        <div class="price-box"  >
            <p>{{ loginUser.nickName }}/{{ loginUser.username }}</p>
            <p class="tips">您好！订单已提交成功，请尽快付款</p>
            <div class="price">
                <div>应付金额 ：</div>
                <span class="rmb">¥</span>
                <span class="num">{{ Number(payData.amount).toFixed(2) || '???' }}</span>
            </div>
        </div>
    </div>
    <div class="pay-method">
      <div class="pay-font">预算信息</div>
      <van-form>
        <van-field
          v-model="currentBudgetTypeName"
          required
          is-link
          readonly
          input-align="right"
          name="currentBudgetType"
          label="预算类型"
          placeholder="点击选择预算类型"
          @click="showPicker = true"
        />
        </van-form>
        <!-- 不同类型的组件 -->
        <dept-gems-1-budget ref="payChild" :param="props.payData" :applicationCode="props.payData.applicationCode" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_GEMS_1.code" @isPayLoading="isPayLoading" @payComplete="payComplete" ></dept-gems-1-budget>
        <dept-kems-1-budget  ref="payChild" :param="props.payData" :applicationCode="props.payData.applicationCode" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_KEMS_1.code" @isPayLoading="isPayLoading" @payComplete="payComplete" ></dept-kems-1-budget>
        <dept-kems-2-budget  ref="payChild" :param="props.payData" :applicationCode="props.payData.applicationCode" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_KEMS_2.code" @isPayLoading="isPayLoading" @payComplete="payComplete" ></dept-kems-2-budget>
        <dept-bcc-1-budget  ref="payChild" :param="props.payData" :businessType="props.payData.businessType" :applicationCode="props.payData.applicationCode" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_BCC_1.code" @isPayLoading="isPayLoading" @payComplete="payComplete" ></dept-bcc-1-budget>
        <dept-bcc-2-budget  ref="payChild" :param="props.payData" :businessType="props.payData.businessType" :applicationCode="props.payData.applicationCode" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_BCC_2.code" @isPayLoading="isPayLoading" @payComplete="payComplete" ></dept-bcc-2-budget>
        <dept-rrs-1-budget  ref="payChild" :param="props.payData" :applicationCode="props.payData.applicationCode" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_RRS_1.code" @isPayLoading="isPayLoading" @payComplete="payComplete" ></dept-rrs-1-budget>
        <dept-rrs-2-budget  ref="payChild" :param="props.payData" :applicationCode="props.payData.applicationCode" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_RRS_2.code" @isPayLoading="isPayLoading" @payComplete="payComplete" ></dept-rrs-2-budget>
        <dept-hbc-1-budget  ref="payChild" :param="props.payData"  :applicationCode="props.payData.applicationCode" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_HBC_1.code" @isPayLoading="isPayLoading" @payComplete="payComplete" ></dept-hbc-1-budget>
        <dept-hbc-2-budget  ref="payChild" :param="props.payData"  :applicationCode="props.payData.applicationCode" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_HBC_2.code" @isPayLoading="isPayLoading" @payComplete="payComplete" ></dept-hbc-2-budget>
        <dept-xw-budget  ref="payChild" :param="props.payData"  :applicationCode="props.payData.applicationCode" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_XW.code" @isPayLoading="isPayLoading" @payComplete="payComplete" ></dept-xw-budget>

    </div>
    
    <div class="btn-container">
      <van-button type="primary" color="#2681FF" class="btn-style" round block @click="mobilePay" :loading="loading">支 付
      </van-button>
    </div>

    <!-- 支付类型弹窗  -->
    <van-popup v-model:show="showPicker" position="bottom">
      <van-picker
        title="预算类型"
        :columns="supportBudgetTyps"
        @confirm="onConfirm"
        @cancel="showPicker = false"
        :columns-field-names="customFieldName"
      />
    </van-popup>
  </div>
</template>

<style scoped lang="less">

.mobile-container-offcial{
  display: flex;
  flex-direction: column;
  background: #FAFBFD;
  min-height: 100vh;
  position: relative;
//   padding: 15px;
  box-sizing: border-box;
  overflow: hidden;
  .price-container {
    background: #FFFFFF;
    box-shadow: 0px 4px 4px 0px rgba(157,178,232,0.1);
    border-radius: 10px 10px 10px 10px;
    margin:15px;
    
    .order-no {
        height: 32px;
        background: linear-gradient(360deg, rgba(195, 211, 255, 0.3) 0%, rgba(38 129,255,0.3) 100%);
        border-radius: 10px 10px 0px 0px;
        line-height: 32px;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0,0,0,0.9);
        text-align: left;
        font-style: normal;
        text-transform: none;
        padding-left:16px;
    }
    .price-box{
        margin-top:11px;
        padding: 0 15px;
        p{
            padding:0;
            margin:0;
            font-weight: 400;
            font-size: 14px;
            color: rgba(0,0,0,0.7);
            line-height: 16px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-bottom: 4px;
        }
        .tips{
            margin-bottom: 7px;
        }
        .price {
            height:40px;
            display: flex;
            align-items: flex-end;
            border-top: 1px solid rgba(0,0,0,0.1);
            div{
                font-weight: 400;
                font-size: 14px;
                color: rgba(0,0,0,0.9);
                line-height: 16px;
                text-align: left;
                font-style: normal;
                text-transform: none;
                padding-bottom: 9px;
                margin-right:10px;
            }
            .rmb {
                font-weight: bold;
                font-size: 16px;
                color: #FF5533;
                line-height: 22px;
                text-align: left;
                font-style: normal;
                text-transform: none;
                margin-right: 3px;
                margin-bottom: 5px;
            }
            .num{
                font-weight: bold;
                font-size: 24px;
                line-height: 28px;
                color: #FF5533;
                text-align: left;
                font-style: normal;
                text-transform: none; 
                margin-bottom: 5px;
            }
        }
    }
  }

  .pay-method {
    display: flex;
    flex-direction: column;
    margin: 0 15px 100px 15px;
    background: #FFFFFF;box-shadow: 0px 4px 4px 0px rgba(157,178,232,0.1);
    border-radius: 10px 10px 10px 10px;
    flex:1;
    .pay-font {
        font-weight: bold;
        font-size: 16px;
        color: #000000;
        line-height: 32px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-top:8px;
        margin-left:14px;
    }
  }

  .btn-container {
    height: 70px;
    background: #FFFFFF;
    box-shadow: 0px -4px 4px 0px rgba(157,178,232,0.1);
    border-radius: 10px 10px 0px 0px;
    position: absolute;
    width: 100%;
    padding:15px;
    display: flex;
    align-items: center;
    justify-content: center;
    .btn-style {
      height: 40px;
      width: 184px;
      //border-radius: 19px;
    }
  }

  @supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
    .btn-container {
      bottom: calc(constant(safe-area-inset-bottom));
      bottom: calc(env(safe-area-inset-bottom));
    }
  }

}

</style>

<style>
:root:root {
  --van-button-primary-background: #0073E5;
  --van-radio-checked-icon-color: #0073E5;
  --van-password-input-background: #F2F2F2;
}
</style>