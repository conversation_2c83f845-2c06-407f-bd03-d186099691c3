<script setup lang="ts">
import { But<PERSON>, Drawer, Input, Radio, DatePicker, Select } from 'ant-design-vue';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { reactive, ref, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import { getCurrentRouter } from '@haierbusiness-front/utils';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import {

  ISubmitNode,
  IProcessNodeData,
  IFlowNode,
  INodeColumn,
} from '@haierbusiness-front/common-libs';
import dayjs from 'dayjs';

// 扩展 Window 接口以支持节点缓存数据
declare global {
  interface Window {
    processNodesData?: any;
    updateProcessNodeData?: (data: any) => void;
    deleteCacheData?: () => Promise<void>;
    clearCacheAfterSubmit?: () => Promise<void>;
  }
}

// 引入edit.vue组件，使用具名导入112312
import EditComp from './edit.vue';
import { message } from 'ant-design-vue';
import { processOrchestrationApi } from '@haierbusiness-front/apis';

const router = getCurrentRouter();

// 获取登录用户信息
const { loginUser } = storeToRefs(applicationStore());

// 加载状态
const loading = ref(false);

// 存储完整的API数据，避免重复请求
const apiData = ref<IProcessNodeData[]>([]);

// 流程节点列数组
const nodeColumns = ref<INodeColumn[]>([]);

// 获取流程编排数据
const fetchProcessData = async () => {
  try {
    loading.value = true;
    const res = await processOrchestrationApi.listDefineMeta({});
    if (res && Array.isArray(res)) {
      // 保存完整的API数据
      apiData.value = res;

      // 初始化流程节点 - 只显示第一个节点
      const rootNodeKey = 'DEMAND_SUBMIT';
      const rootNodeData = res.find((item) => item.key === rootNodeKey);

      if (rootNodeData) {
        // 初始化第一列，只包含第一个节点
        nodeColumns.value = [
          {
            id: 'column-1',
            nodes: [
              {
                id: '1',
                code: rootNodeData.key,
                name: rootNodeData.name,
                title: rootNodeData.nodeDescription?.role || '经办人',
                isSelected: true,
                hasExpanded: false,
                isEdited: false,
              },
            ],
            sourceNodeIndex: -1, // 第一列没有来源节点
            isCollapsed: true, // 第一列默认收起状态（只显示选中节点）
            isMainFlow: true, // 第一列是主流程
          },
        ];

        console.log('初始化第一个节点:', rootNodeData.name);
      }
    } else {
      console.log('API响应数据格式不正确或为空');
    }
  } catch (error) {
    console.error('获取流程编排数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 添加滚动到底部的函数
const scrollToBottom = () => {
  nextTick(() => {
    const processFlowElem = document.querySelector('.process-flow');
    if (processFlowElem) {
      processFlowElem.scrollLeft = processFlowElem.scrollWidth;
    }
  });
};

// 点击加号按钮的处理函数
const handleAddNode = (columnIndex: number, nodeIndex: number) => {
  const currentColumn = nodeColumns.value[columnIndex];
  const currentNode = currentColumn.nodes[nodeIndex];
  if (!currentNode) return;

  // 获取当前节点的API数据
  const currentNodeData = apiData.value.find((item) => item.key === currentNode.code);
  console.log(currentNodeData, "currentNodeData");

  if (!currentNodeData || !currentNodeData.nextNodes) {
    message.warning('当前节点没有可添加的下一级节点');
    return;
  }

  // 获取下一级可用的节点
  const nextNodeKeys = Object.values(currentNodeData.nextNodes)
    .map((info: any) => info.nextNode)
    .filter(Boolean);

  if (nextNodeKeys.length === 0) {
    message.warning('当前节点没有可添加的下一级节点');
    return;
  }

  // 检查是否已经存在下一列
  const nextColumnIndex = columnIndex + 1;
  const existingNextColumn = nodeColumns.value[nextColumnIndex];

  if (!existingNextColumn) {
    // 第一次点击：创建新列，包含所有可能的下一级节点
    const nextNodes: IFlowNode[] = [];

    nextNodeKeys.forEach((nextNodeKey, index) => {
      const nextNodeData = apiData.value.find((item) => item.key === nextNodeKey);
      if (nextNodeData) {
        nextNodes.push({
          id: `${Date.now()}-${index}`,
          code: nextNodeData.key,
          name: nextNodeData.name,
          title: nextNodeData.nodeDescription?.role || '经办人',
          isSelected: false, // 展开时没有默认选中
          hasExpanded: false,
          isEdited: false,
        });
      }
    });

    if (nextNodes.length > 0) {
      // 创建新列
      const newColumn: INodeColumn = {
        id: `column-${nextColumnIndex + 1}`,
        nodes: nextNodes,
        sourceNodeIndex: nodeIndex, // 记录来源节点的索引
        isCollapsed: false, // 新展开的列默认展开状态（显示所有节点）
        isMainFlow: true, // 新展开的列是主流程，可以继续扩展
      };

      // 在当前列后面插入新列
      nodeColumns.value = [
        ...nodeColumns.value.slice(0, nextColumnIndex),
        newColumn,
        ...nodeColumns.value.slice(nextColumnIndex)
      ];

      // 标记当前节点已展开
      currentNode.hasExpanded = true;

      console.log(
        '展开下一列，包含节点:',
        nextNodes.map((n) => n.name),
      );
    }
  } else {
    // 已存在下一列，在当前列后面插入新的重复列
    const nextNodes: IFlowNode[] = [];

    nextNodeKeys.forEach((nextNodeKey, index) => {
      const nextNodeData = apiData.value.find((item) => item.key === nextNodeKey);
      if (nextNodeData) {
        nextNodes.push({
          id: `${Date.now()}-${index}`,
          code: nextNodeData.key,
          name: nextNodeData.name,
          title: nextNodeData.nodeDescription?.role || '经办人',
          isSelected: false, // 展开时没有默认选中
          hasExpanded: false,
          isEdited: false,
        });
      }
    });

    if (nextNodes.length > 0) {
      // 创建新列
      const newColumn: INodeColumn = {
        id: `column-${Date.now()}`,
        nodes: nextNodes,
        sourceNodeIndex: nodeIndex,
        isCollapsed: false, // 新展开的列默认展开状态（显示所有节点）
        isMainFlow: true, // 修改：第二次及以后新增的列也是主流程，可以继续扩展
      };

      // 在当前列后面插入新列
      nodeColumns.value.splice(nextColumnIndex, 0, newColumn);

      console.log(
        '在当前列后面新增列，包含节点:',
        nextNodes.map((n) => n.name),
      );
    }
  }

  // 只有在真正新增列时才滚动到最右边
  // 如果是编辑模式且节点已经全部展开，则不需要滚动
  const shouldScroll = !processOrchestrationData.value?.isEditMode || !existingNextColumn;
  if (shouldScroll) {
    scrollToBottom();
  }
};

// 选择节点（在列中选择节点，并移动到顶部）
const selectNode = (columnIndex: number, nodeIndex: number) => {
  const column = nodeColumns.value[columnIndex];
  if (!column) return;

  // 如果点击的不是第一个节点，将其移动到顶部
  if (nodeIndex !== 0) {
    // 获取被点击的节点
    const selectedNode = column.nodes[nodeIndex];

    // 从原位置移除
    column.nodes.splice(nodeIndex, 1);

    // 插入到顶部
    column.nodes.unshift(selectedNode);

    console.log('将节点移动到顶部:', selectedNode.name);
  }

  // 设置第一个节点（顶部节点）为选中状态，其他节点不选中
  column.nodes.forEach((node, index) => {
    node.isSelected = index === 0;
  });

  // 选中后，隐藏其他节点，只显示选中的节点
  column.isCollapsed = true;

  console.log('当前列顶部节点:', column.nodes[0].name);
};

// 展开列，显示所有节点选项
const expandColumn = (columnIndex: number) => {
  const column = nodeColumns.value[columnIndex];
  if (!column) return;

  // 展开列，显示所有节点
  column.isCollapsed = false;

  // 不取消选中状态，保持当前选中的节点

  console.log('展开列，显示所有节点选项，保持当前选中状态');
};

// 删除列
const deleteColumn = (columnIndex: number) => {
  if (columnIndex === 0) {
    message.warning('第一列不能删除');
    return;
  }

  // 删除列及其后面的所有列
  nodeColumns.value.splice(columnIndex, 1);

  console.log('删除了列，当前列数量:', nodeColumns.value.length);
};

// 检查两列之间是否可以连接（基于顶部节点）
const canConnect = (fromColumnIndex: number, toColumnIndex: number): boolean => {
  if (fromColumnIndex >= nodeColumns.value.length || toColumnIndex >= nodeColumns.value.length) {
    return false;
  }

  const fromColumn = nodeColumns.value[fromColumnIndex];
  const toColumn = nodeColumns.value[toColumnIndex];

  // 获取源列的顶部节点（索引0）
  const fromNode = fromColumn.nodes[0];
  if (!fromNode) return false;

  // 获取目标列的顶部节点（索引0）
  const toNode = toColumn.nodes[0];
  if (!toNode) return false;

  // 获取源节点的API数据
  const fromNodeData = apiData.value.find((item) => item.key === fromNode.code);
  if (!fromNodeData || !fromNodeData.nextNodes) {
    return false;
  }

  // 检查目标节点是否在源节点的nextNodes中
  const nextNodeKeys = Object.values(fromNodeData.nextNodes)
    .map((info: any) => info.nextNode)
    .filter(Boolean);

  return nextNodeKeys.includes(toNode.code);
};

// 获取连接线样式
const getConnectionLineClass = (columnIndex: number): string => {
  if (columnIndex >= nodeColumns.value.length - 1) {
    return 'no-line'; // 最后一列没有连接线
  }

  const canConnectToNext = canConnect(columnIndex, columnIndex + 1);
  return canConnectToNext ? 'solid-line' : 'dashed-line';
};

// 弹框相关状态
const modalVisible = ref(false);
const confirmLoading = ref(false);
const currentEditNodeIndex = ref<{ columnIndex: number; nodeIndex: number }>({ columnIndex: 0, nodeIndex: 0 });

// 节点配置相关状态
const nodeConfigs = ref<Record<string, any>>({});
// 改为基于节点实例ID存储配置，而不是基于节点code
const nodeConfigsMap = ref<Record<string, Record<string, any>>>({});

// 检查checkFlag中指定的源是否存在
const checkSourceExistence = (sourceName: string, sourceType: number, apiData: any[]): boolean => {
  if (sourceType === 0) {
    return apiData.some((item) => item.key === sourceName);
  } else if (sourceType === 1) {
    return apiData.some((item) => {
      if (item.configs) {
        return Object.keys(item.configs).includes(sourceName);
      }
      return false;
    });
  }
  return false;
};

// 检查节点是否有可配置项
const hasConfigs = (nodeCode: string): boolean => {
  const nodeData = apiData.value.find((item) => item.key === nodeCode);
  return Boolean(nodeData && nodeData.configs && Object.keys(nodeData.configs).length > 0);
};

// 检查节点是否有下一级节点
const hasNextNodes = (nodeCode: string): boolean => {
  const nodeData = apiData.value.find((item) => item.key === nodeCode);
  if (!nodeData || !nodeData.nextNodes) {
    return false;
  }

  const nextNodeKeys = Object.values(nodeData.nextNodes)
    .map((info: any) => info.nextNode)
    .filter(Boolean);

  return nextNodeKeys.length > 0;
};
// 打开编辑弹框
const openEditModal = (columnIndex: number, nodeIndex: number) => {
  const column = nodeColumns.value[columnIndex];
  const node = column?.nodes[nodeIndex];
  if (!node) return;

  // 保存当前编辑的位置信息
  currentEditNodeIndex.value = { columnIndex, nodeIndex };
  const nodeId = node.id; // 使用节点实例ID
  const nodeCode = node.code;

  // 检查是否已经有保存的配置（基于节点实例ID）
  if (nodeConfigsMap.value[nodeId]) {
    nodeConfigs.value = nodeConfigsMap.value[nodeId];
  } else {
    nodeConfigs.value = {};

    // 从API获取节点配置
    if (nodeCode) {
      const nodeData = apiData.value.find((item) => item.key === nodeCode);
      if (nodeData && nodeData.configs) {
        const filteredConfigs: Record<string, any> = {};

        Object.keys(nodeData.configs).forEach((key) => {
          const config = nodeData.configs[key];
          let isConfigEnabled = true;

          // 检查checkFlag
          if (config.checkFlag) {
            const { sourceName, sourceType } = config.checkFlag;
            isConfigEnabled = checkSourceExistence(sourceName, sourceType, apiData.value);
          }

          if (isConfigEnabled) {
            filteredConfigs[key] = { ...config };

            // 初始化配置值为空，不设置默认值
            if (config.type === 'RADIO') {
              filteredConfigs[key].selectedValue = null;
            } else if (config.type === 'INPUT_NUMBER') {
              filteredConfigs[key].inputValue = null;
            } else if (config.type === 'DATE') {
              filteredConfigs[key].dateValue = null;
              filteredConfigs[key].dateString = null;
            } else if (config.type === 'DATETIME') {
              filteredConfigs[key].dateTimeValue = null;
              filteredConfigs[key].dateTimeString = null;
            } else if (config.type === 'TIME_OFFSET_SELECTOR') {
              filteredConfigs[key].offsetValue = null;
              filteredConfigs[key].timeValue = null;
              filteredConfigs[key].timeString = null;
              filteredConfigs[key].combinedValue = '';
            } else if (config.type === 'CHECKBOX') {
              filteredConfigs[key].selectedValues = [];
            }
          }
        });

        nodeConfigs.value = filteredConfigs;
        // 基于节点实例ID存储配置
        nodeConfigsMap.value[nodeId] = nodeConfigs.value;
      }
    }
  }

  modalVisible.value = true;
};

// 处理单选框选择
const handleRadioChange = (configKey: string, value: any) => {
  if (!nodeConfigs.value[configKey]) {
    nodeConfigs.value[configKey] = {};
  }
  nodeConfigs.value[configKey].selectedValue = value;
  console.log(`配置项 ${configKey} 值已更新为:`, value);
};

// 处理输入框值变化
const handleInputChange = (configKey: string, value: any) => {
  if (!nodeConfigs.value[configKey]) {
    nodeConfigs.value[configKey] = {};
  }
  nodeConfigs.value[configKey].inputValue = value;
  console.log(`配置项 ${configKey} 值已更新为:`, value);
};

// 处理日期选择
const handleDateChange = (configKey: string, value: any) => {
  if (!nodeConfigs.value[configKey]) {
    nodeConfigs.value[configKey] = {};
  }
  nodeConfigs.value[configKey].dateValue = value;
  nodeConfigs.value[configKey].dateString = value ? dayjs(value).format('YYYY-MM-DD') : null;
  console.log(`配置项 ${configKey} 日期已更新为:`, nodeConfigs.value[configKey].dateString);
};

// 处理日期时间选择
const handleDateTimeChange = (configKey: string, value: any) => {
  if (!nodeConfigs.value[configKey]) {
    nodeConfigs.value[configKey] = {};
  }
  nodeConfigs.value[configKey].dateTimeValue = value;
  nodeConfigs.value[configKey].dateTimeString = value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : null;
  console.log(`配置项 ${configKey} 日期时间已更新为:`, nodeConfigs.value[configKey].dateTimeString);
};

// 处理时间偏移选择器变化
const handleTimeOffsetChange = (configKey: string, type: 'offset' | 'time', value: any) => {
  if (!nodeConfigs.value[configKey]) {
    nodeConfigs.value[configKey] = {};
  }

  if (type === 'offset') {
    nodeConfigs.value[configKey].offsetValue = value;
  } else if (type === 'time') {
    nodeConfigs.value[configKey].timeValue = value;
    nodeConfigs.value[configKey].timeString = value ? dayjs(value).format('HH:mm') : null;
  }

  // 组合最终值：偏移量,时间（格式：数字,HH:mm 或者 只有数字）
  const offsetVal = nodeConfigs.value[configKey].offsetValue || '';
  const timeVal = nodeConfigs.value[configKey].timeString || '';

  // 如果有偏移量，组合值就是偏移量；如果还有时间，就是偏移量,时间
  if (offsetVal) {
    nodeConfigs.value[configKey].combinedValue = timeVal ? `${offsetVal},${timeVal}` : offsetVal;
  } else {
    nodeConfigs.value[configKey].combinedValue = '';
  }

  console.log(`配置项 ${configKey} 时间偏移已更新为:`, nodeConfigs.value[configKey].combinedValue);
};

// 处理多选框变化
const handleCheckboxChange = (configKey: string, value: any) => {
  if (!nodeConfigs.value[configKey]) {
    nodeConfigs.value[configKey] = {};
  }
  nodeConfigs.value[configKey].selectedValues = value;
  console.log(`配置项 ${configKey} 多选值已更新为:`, value);
};

// 处理弹框确认
const handleModalOk = () => {
  confirmLoading.value = true;

  const { columnIndex, nodeIndex } = currentEditNodeIndex.value;
  const column = nodeColumns.value[columnIndex];
  const node = column?.nodes[nodeIndex];
  if (!node) {
    confirmLoading.value = false;
    return;
  }

  // 🔍 必填校验
  const missingRequiredFields: string[] = [];

  Object.keys(nodeConfigs.value).forEach((key) => {
    const config = nodeConfigs.value[key];

    // 检查是否为必填配置
    if (config.required) {
      let hasValue = false;

      if (config.type === 'RADIO') {
        hasValue = Boolean(config.selectedValue);
      } else if (config.type === 'INPUT_NUMBER') {
        hasValue = Boolean(config.inputValue);
      } else if (config.type === 'DATE') {
        hasValue = Boolean(config.dateString);
      } else if (config.type === 'DATETIME') {
        hasValue = Boolean(config.dateTimeString);
      } else if (config.type === 'TIME_OFFSET_SELECTOR') {
        // 对于时间偏移选择器，只验证偏移量（天数）是必填的
        hasValue = Boolean(config.offsetValue);
      } else if (config.type === 'CHECKBOX') {
        hasValue = Boolean(config.selectedValues && config.selectedValues.length > 0);
      }

      if (!hasValue) {
        missingRequiredFields.push(config.name || key);
      }
    }
  });

  // 如果有必填项未填写，显示错误提示并停止提交
  if (missingRequiredFields.length > 0) {
    message.error(`请填写以下必填项：\n${missingRequiredFields.join('、')}`);
    confirmLoading.value = false;
    return;
  }

  // 收集配置数据
  const configData: Record<string, any> = {};

  Object.keys(nodeConfigs.value).forEach((key) => {
    const config = nodeConfigs.value[key];
    if (config.type === 'RADIO') {
      configData[key] = config.selectedValue;
    } else if (config.type === 'INPUT_NUMBER') {
      configData[key] = config.inputValue;
    } else if (config.type === 'DATE') {
      configData[key] = config.dateString;
    } else if (config.type === 'TIME_OFFSET_SELECTOR') {
      configData[key] = config.combinedValue;
    }
  });

  console.log('保存节点配置:', configData);

  // 保存节点配置到映射中（基于节点实例ID）
  nodeConfigsMap.value[node.id] = { ...nodeConfigs.value };

  // 标记节点为已编辑状态
  node.isEdited = true;

  // 显示成功提示并关闭弹窗
  message.success('配置已保存，将在提交时一并发送');
  setTimeout(() => {
    modalVisible.value = false;
    confirmLoading.value = false;
  }, 500);
};

// 处理弹框取消
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 添加控制显示的状态
const showNodesView = ref(false);
const processOrchestrationData = ref<Record<string, any> | null>(null);



// 接收edit组件数据的方法
const handleEditComplete = async (data: Record<string, any>) => {
  processOrchestrationData.value = data;
  showNodesView.value = true;
  console.log('接收到edit组件数据:', data);

  // 如果是编辑模式且有现有节点数据，展开所有节点
  if (data.isEditMode && data.existingNodes && Array.isArray(data.existingNodes) && data.existingNodes.length > 0) {
    nextTick(() => {
      expandNodesFromExistingData(data.existingNodes);
    });
  } else {
    // 新增模式下检查是否有缓存的节点数据需要恢复
    if (window.processNodesData) {
      const cachedNodeData = window.processNodesData;
      // 检查缓存数据是否属于当前用户
      const currentUsername = loginUser.value?.username || 'unknown';
      const cachedUsername = cachedNodeData.username || 'unknown';

      if (currentUsername === cachedUsername) {
        nodeColumns.value = cachedNodeData.nodeColumns || [];
        nodeConfigsMap.value = cachedNodeData.nodeConfigsMap || {};
        message.success('已恢复节点配置数据');
      } else {
        console.log('缓存数据不属于当前用户，不恢复');
      }

      // 清除全局缓存数据
      window.processNodesData = null;
    }

    // 切换视图后滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
  }
};

// 根据现有节点数据展开节点
const expandNodesFromExistingData = (existingNodes: any[]) => {
  try {
    console.log('开始展开现有节点:', existingNodes);

    // 按seq排序节点
    const sortedNodes = [...existingNodes].sort((a, b) => (a.seq || 0) - (b.seq || 0));

    // 清空现有节点列
    nodeColumns.value = [];

    // 为每个节点创建一列
    sortedNodes.forEach((nodeData, index) => {
      const nodeInfo = apiData.value.find((item) => item.key === nodeData.metaKey);

      if (nodeInfo) {
        const newColumn: INodeColumn = {
          id: `column-${index + 1}`,
          nodes: [
            {
              id: `${Date.now()}-${index}`,
              code: nodeData.metaKey,
              name: nodeData.nodeName || nodeInfo.name,
              title: nodeInfo.nodeDescription?.role || '经办人',
              isSelected: true,
              hasExpanded: index < sortedNodes.length - 1, // 除了最后一个节点都标记为已展开
              isEdited: false, // 初始化为未编辑状态
            },
          ],
          sourceNodeIndex: index > 0 ? 0 : -1, // 第一列没有来源节点
          isCollapsed: true, // 收起状态，只显示选中节点
          isMainFlow: true, // 主流程
        };

        nodeColumns.value.push(newColumn);

        // 处理节点配置：先初始化所有配置项，然后恢复已保存的值
        if (nodeInfo.configs && Object.keys(nodeInfo.configs).length > 0) {
          const nodeId = newColumn.nodes[0].id;
          const restoredConfigs: Record<string, any> = {};

          // 查找节点的原始配置定义
          const nodeConfigDef = nodeInfo.configs || {};

          // 先初始化所有配置项（包括未配置的）
          Object.keys(nodeConfigDef).forEach((key) => {
            const config = nodeConfigDef[key];
            let isConfigEnabled = true;

            // 检查checkFlag
            if (config.checkFlag) {
              const { sourceName, sourceType } = config.checkFlag;
              isConfigEnabled = checkSourceExistence(sourceName, sourceType, apiData.value);
            }

            if (isConfigEnabled) {
              restoredConfigs[key] = { ...config };

              // 初始化配置值为空
              if (config.type === 'RADIO') {
                restoredConfigs[key].selectedValue = null;
              } else if (config.type === 'INPUT_NUMBER') {
                restoredConfigs[key].inputValue = null;
              } else if (config.type === 'DATE') {
                restoredConfigs[key].dateValue = null;
                restoredConfigs[key].dateString = null;
              } else if (config.type === 'DATETIME') {
                restoredConfigs[key].dateTimeValue = null;
                restoredConfigs[key].dateTimeString = null;
              } else if (config.type === 'TIME_OFFSET_SELECTOR') {
                restoredConfigs[key].offsetValue = null;
                restoredConfigs[key].timeValue = null;
                restoredConfigs[key].timeString = null;
                restoredConfigs[key].combinedValue = '';
              } else if (config.type === 'CHECKBOX') {
                restoredConfigs[key].selectedValues = [];
              }
            }
          });

          // 然后恢复已保存的配置值（如果有）
          if (nodeData.configs && Array.isArray(nodeData.configs) && nodeData.configs.length > 0) {
            nodeData.configs.forEach((config: any) => {
              const configKey = config.metaKey;
              const configDef = restoredConfigs[configKey];

              if (configDef) {
                // 恢复配置值
                if (configDef.type === 'RADIO') {
                  restoredConfigs[configKey].selectedValue = config.configParam;
                } else if (configDef.type === 'INPUT_NUMBER') {
                  restoredConfigs[configKey].inputValue = config.configParam;
                } else if (configDef.type === 'DATE') {
                  restoredConfigs[configKey].dateString = config.configParam;
                  restoredConfigs[configKey].dateValue = config.configParam ? dayjs(config.configParam) : null;
                } else if (configDef.type === 'DATETIME') {
                  restoredConfigs[configKey].dateTimeString = config.configParam;
                  restoredConfigs[configKey].dateTimeValue = config.configParam ? dayjs(config.configParam) : null;
                } else if (configDef.type === 'TIME_OFFSET_SELECTOR') {
                  restoredConfigs[configKey].combinedValue = config.configParam;
                  // 分解组合值
                  if (config.configParam) {
                    const parts = config.configParam.split(',');
                    if (parts.length === 2) {
                      // 格式：偏移量,时间
                      restoredConfigs[configKey].offsetValue = parts[0];
                      restoredConfigs[configKey].timeString = parts[1];
                      restoredConfigs[configKey].timeValue = dayjs(`2000-01-01 ${parts[1]}`);
                    } else if (parts.length === 1) {
                      // 格式：只有偏移量
                      restoredConfigs[configKey].offsetValue = parts[0];
                      restoredConfigs[configKey].timeString = null;
                      restoredConfigs[configKey].timeValue = null;
                    }
                  }
                } else if (configDef.type === 'CHECKBOX') {
                  // 多选框的值是逗号分隔的字符串，需要转换为数组
                  restoredConfigs[configKey].selectedValues = config.configParam ? config.configParam.split(',') : [];
                }
              }
            });

            // 标记节点为已编辑状态（只有当确实有配置值时）
            newColumn.nodes[0].isEdited = true;
          }

          // 保存配置（包含所有配置项）
          nodeConfigsMap.value[nodeId] = restoredConfigs;
        }
      }
    });

    console.log('节点展开完成，共创建列数:', nodeColumns.value.length);

    // 编辑模式下不自动滚动到最右边，保持在起始位置
    // nextTick(() => {
    //   scrollToBottom();
    // });
  } catch (error) {
    console.error('展开现有节点失败:', error);
    message.error('展开现有节点失败');
  }
};

// 返回edit视图的方法
const backToEdit = () => {
  showNodesView.value = false;
};
// 修改提交方法，使用新的节点数据结构
const subBtn = () => {
  if (!processOrchestrationData.value) {
    message.error('缺少基础信息数据，请返回上一步填写');
    return;
  }

  // 打印每列的选中节点
  nodeColumns.value.forEach((column, index) => {
    const selectedNode = column.nodes.find((node) => node.isSelected);
    if (selectedNode) {
      console.log(`第${index + 1}列选中节点:`, {
        id: selectedNode.id,
        code: selectedNode.code,
        name: selectedNode.name,
        isEdited: selectedNode.isEdited,
        配置: nodeConfigsMap.value[selectedNode.id] || '无配置',
      });
    }
  });
  console.log('=== 数据检查完毕 ===');

  // 验证节点是否全部展开（检查是否还有未展开的节点）
  const validateNodesExpanded = () => {
    const missingNodes: string[] = [];

    // 检查是否至少有两个节点（一个起始节点是不够的）
    if (nodeColumns.value.length < 2) {
      missingNodes.push('流程过于简单，请至少添加一个后续节点');
      return missingNodes;
    }

    // 定义合法的流程终点节点（这些节点允许作为流程的结束）
    const validEndNodes = [
      'END', // 结束节点
      'MICE_COMPLETED', // 会议执行完成
      'SETTLEMENT_RECORDED', // 结算已记录
      'PLATFORM_INVOICE_CONFIRM', // 平台发票确认
      'VENDOR_INVOICE_ENTRY', // 供应商发票录入
      'PLATFORM_RECEIPT_UPLOAD', // 平台收据上传
    ];

    // 获取当前流程中所有选中的节点
    const selectedNodes: IFlowNode[] = [];
    nodeColumns.value.forEach((column) => {
      const selectedNode = column.nodes.find((node) => node.isSelected);
      if (selectedNode) {
        selectedNodes.push(selectedNode);
      }
    });

    // 检查最后一个节点是否为合法终点
    if (selectedNodes.length > 0) {
      const lastNode = selectedNodes[selectedNodes.length - 1];

      // 如果最后一个节点不是合法终点，且还有下级节点，则需要继续展开
      if (!validEndNodes.includes(lastNode.code) && hasNextNodes(lastNode.code)) {
        missingNodes.push(`流程未完整，节点"${lastNode.name}"还有下一级节点未展开，请继续添加节点完善流程`);
      }
    }

    // 检查流程完整性：验证每个已选择的节点是否都合理地连接到下一个节点
    for (let i = 0; i < selectedNodes.length - 1; i++) {
      const currentNode = selectedNodes[i];
      const nextNode = selectedNodes[i + 1];

      // 获取当前节点的API数据
      const currentNodeData = apiData.value.find((item) => item.key === currentNode.code);
      if (currentNodeData && currentNodeData.nextNodes) {
        const nextNodeKeys = Object.values(currentNodeData.nextNodes)
          .map((info: any) => info.nextNode)
          .filter(Boolean);

        // 如果当前节点有多个可选的下级节点，检查是否遗漏了重要分支
        if (nextNodeKeys.length > 1 && !nextNodeKeys.includes(nextNode.code)) {
          missingNodes.push(`节点"${currentNode.name}"到"${nextNode.name}"的连接可能不正确，请检查流程路径`);
        }
      }
    }

    // 检查关键业务节点是否遗漏
    const currentNodeCodes = selectedNodes.map((node) => node.code);
    const requiredBusinessNodes = [
      'DEMAND_SUBMIT', // 需求提交（必须）
      'SCHEME_SUBMIT', // 方案提交（通常必需）
    ];

    requiredBusinessNodes.forEach((requiredNode) => {
      if (!currentNodeCodes.includes(requiredNode)) {
        const nodeData = apiData.value.find((item) => item.key === requiredNode);
        if (nodeData) {
          missingNodes.push(`缺少关键业务节点"${nodeData.name}"，请检查流程完整性`);
        }
      }
    });

    return missingNodes;
  };

  // 验证适用用户 - 已禁用此验证
  // const missingUsers = validateConsumers();
  // if (missingUsers.length > 0) {
  //   message.error(`适用用户配置不完整:\n${missingUsers.join('\n')}`);
  //   return;
  // }

  // 验证节点是否全部展开
  const missingNodes = validateNodesExpanded();
  if (missingNodes.length > 0) {
    message.error(`流程节点不完整:\n${missingNodes.join('\n')}`);
    return;
  }

  // 验证节点连接性（检查是否有虚线连接）
  const validateNodeConnections = () => {
    const invalidConnections: string[] = [];

    // 检查相邻列之间的连接
    for (let i = 0; i < nodeColumns.value.length - 1; i++) {
      const currentColumn = nodeColumns.value[i];
      const nextColumn = nodeColumns.value[i + 1];

      // 获取当前列的选中节点
      const currentSelectedNode = currentColumn.nodes.find((node) => node.isSelected);
      const nextSelectedNode = nextColumn.nodes.find((node) => node.isSelected);

      if (currentSelectedNode && nextSelectedNode) {
        // 检查两个节点之间是否可以连接
        const canConnectNodes = canConnect(i, i + 1);
        if (!canConnectNodes) {
          invalidConnections.push(`"${currentSelectedNode.name}" 与 "${nextSelectedNode.name}" 之间无法连接`);
        }
      }
    }

    return invalidConnections;
  };

  // 验证节点连接性
  const invalidConnections = validateNodeConnections();
  if (invalidConnections.length > 0) {
    message.error(`流程节点连接无效，请检查以下连接:\n${invalidConnections.join('\n')}`);
    return;
  }

  // 🔍 验证接口返回 required: true 的配置项
  const validateRequiredConfigs = () => {
    const missingConfigs: string[] = [];
    console.log('🔍 开始验证必填配置项...');

    // 检查每个列的选中节点的配置
    nodeColumns.value.forEach((column, columnIndex) => {
      const selectedNode = column.nodes.find((node) => node.isSelected);
      if (selectedNode) {
        console.log(`📋 检查第${columnIndex + 1}列节点"${selectedNode.name}"`);

        // 首先检查该节点是否有必填配置项
        const nodeData = apiData.value.find(item => item.key === selectedNode.code);
        if (nodeData && nodeData.configs) {
          const requiredConfigKeys = Object.keys(nodeData.configs).filter(key =>
            nodeData.configs[key].required === true
          );

          if (requiredConfigKeys.length > 0) {
            console.log(`  该节点有${requiredConfigKeys.length}个必填配置项:`, requiredConfigKeys.map(key => nodeData.configs[key].name || key));

            // 检查用户是否已配置该节点
            const nodeConfig = nodeConfigsMap.value[selectedNode.id];

            if (!nodeConfig || Object.keys(nodeConfig).length === 0) {
              // 如果该节点有必填配置但用户未配置过，则所有必填项都未填写
              requiredConfigKeys.forEach(key => {
                const configDef = nodeData.configs[key];
                missingConfigs.push(`节点"${selectedNode.name}"的配置项"${configDef.name || key}"（未配置）`);
              });
            } else {
              // 检查已配置的项目中，必填项是否都有值
              requiredConfigKeys.forEach(configKey => {
                const config = nodeConfig[configKey];
                const configDef = nodeData.configs[configKey];

                if (!config) {
                  // 必填配置项不存在于用户配置中
                  missingConfigs.push(`节点"${selectedNode.name}"的配置项"${configDef.name || configKey}"（未配置）`);
                  return;
                }

                let hasValue = false;
                let displayValue = '';

                if (config.type === 'RADIO') {
                  hasValue = Boolean(config.selectedValue);
                  displayValue = config.selectedValue || '空';
                } else if (config.type === 'INPUT_NUMBER') {
                  hasValue = Boolean(config.inputValue !== null && config.inputValue !== undefined && config.inputValue !== '');
                  displayValue = config.inputValue || '空';
                } else if (config.type === 'DATE') {
                  hasValue = Boolean(config.dateString);
                  displayValue = config.dateString || '空';
                } else if (config.type === 'DATETIME') {
                  hasValue = Boolean(config.dateTimeString);
                  displayValue = config.dateTimeString || '空';
                } else if (config.type === 'TIME_OFFSET_SELECTOR') {
                  // 对于时间偏移选择器，只要有偏移量就算有值（时间可选）
                  hasValue = Boolean(config.offsetValue !== null && config.offsetValue !== undefined && config.offsetValue !== '');
                  displayValue = `天数=${config.offsetValue || '空'}, 时间=${config.timeString || '空'}`;
                } else if (config.type === 'CHECKBOX') {
                  hasValue = Boolean(config.selectedValues && Array.isArray(config.selectedValues) && config.selectedValues.length > 0);
                  displayValue = `已选${config.selectedValues?.length || 0}项`;
                }

                console.log(`  - ${config.name || configKey} (${config.type},必填): ${displayValue} -> ${hasValue ? '✅' : '❌'}`);

                if (!hasValue) {
                  missingConfigs.push(`节点"${selectedNode.name}"的配置项"${config.name || configKey}"`);
                }
              });
            }
          } else {
            console.log(`  该节点无必填配置项`);
          }
        } else {
          console.log(`  该节点无配置项定义`);
        }
      }
    });

    console.log(`🔍 验证完成，未填写的必填配置项数量: ${missingConfigs.length}`);
    if (missingConfigs.length > 0) {
      console.log('❌ 未填写的必填配置项:', missingConfigs);
    }
    return missingConfigs;
  };

  const missingConfigs = validateRequiredConfigs();
  if (missingConfigs.length > 0) {
    message.error(`⚠️ 提交失败！以下必填配置项未填写:\n\n${missingConfigs.join('\n')}\n\n请先完成配置后再提交。`);
    return;
  }

  // 构建符合后端接口的nodes数据结构
  const nodes: ISubmitNode[] = [];

  // 收集所有列的选中节点
  const selectedNodes: IFlowNode[] = [];
  for (const column of nodeColumns.value) {
    const selectedNode = column.nodes.find(node => node.isSelected == true);
    console.log(selectedNode,"selectedNode");
    
    if (selectedNode) {
      selectedNodes.push(selectedNode);
    }
  }


  // 按节点的业务流程顺序排序
  // const sortedNodes = selectedNodes.sort((a, b) => {
  //   const nodeOrder = {
  //     DEMAND_SUBMIT: 1,
  //     DEMAND_RECEIVE: 2,
  //     DEMAND_PRE_INTERACT: 3,
  //     DEMAND_CONFIRM: 4,
  //     DEMAND_APPROVAL: 5,
  //     DEMAND_PUSH: 6,
  //     DEMAND_RE_APPROVAL: 7,
  //     SCHEME_SUBMIT: 8,
  //     SCHEME_APPROVAL: 9,
  //     SCHEME_RE_APPROVAL: 10,
  //     SCHEME_CONFIRM: 11,
  //     BID_PUSH: 12,
  //     BIDDING: 13,
  //     BID_RESULT_CONFIRM: 14,
  //     PAYMENT_CONFIRM: 15,
  //     COST_APPROVAL: 16,
  //     MICE_PENDING: 17,
  //     MICE_EXECUTION: 18,
  //     MICE_COMPLETED: 19,
  //     BILL_CONFIRM: 20,
  //     BILL_APPROVAL: 21,
  //     BILL_RE_APPROVAL: 22,
  //     PLATFORM_INVOICE_ENTRY: 23,
  //     VENDOR_INVOICE_ENTRY: 24,
  //     INVOICE_CONFIRM: 25,
  //     PLATFORM_REFUND_RECEIPT_UPLOAD: 26,
  //     REFUND_CONFIRM: 27,
  //     PLATFORM_PAY_RECEIPT_UPLOAD: 28,
  //     PLATFORM_INVOICE_CONFIRM: 29,
  //     SETTLEMENT_PENDING: 30,
  //     SETTLEMENT_RECORDED: 31,
  //     END: 32,
  //   };

  //   const orderA = (nodeOrder as any)[a.code] || 999;
  //   const orderB = (nodeOrder as any)[b.code] || 999;

  //   return orderA - orderB;
  // });
  console.log(nodeColumns.value, "nodeColumns.value");
  console.log(selectedNodes, "nodeColumns.value");

  // 遍历所有选中的节点
  selectedNodes.forEach((node, index) => {
    // 构建节点对象
    const nodeObj: ISubmitNode = {
      metaKey: node.code,
      nodeName: node.name,
      seq: index + 1,
      configs: [],
    };

    // 获取节点的配置信息（使用节点实例ID）
    const nodeConfig = nodeConfigsMap.value[node.id];

    // 如果该节点有配置信息，添加到configs数组
    if (nodeConfig && Object.keys(nodeConfig).length > 0) {
      Object.keys(nodeConfig).forEach((key) => {
        const config = nodeConfig[key];
        let configParam = '';
        let type = '';
        let reverseDisplayParam = '';

        // 处理不同类型的配置
        if (config.type === 'RADIO') {
          configParam = config.selectedValue || '';
          type = config.name || '单选配置';

          // 生成回显参数
          if (config.selectedValue && config.value) {
            const selectedOption = config.value.find((opt: any) => opt.value === config.selectedValue);
            if (selectedOption) {
              reverseDisplayParam = JSON.stringify({
                selectedLabel: selectedOption.label,
                selectedValue: selectedOption.value,
              });
            }
          }
        } else if (config.type === 'INPUT_NUMBER') {
          configParam = config.inputValue || '';
          type = config.name || '数字输入配置';

          // 生成回显参数
          if (config.inputValue) {
            reverseDisplayParam = JSON.stringify({
              inputValue: config.inputValue,
              displayText: `${config.inputValue}`,
            });
          }
        } else if (config.type === 'DATE') {
          configParam = config.dateString || '';
          type = config.name || '日期配置';

          // 生成回显参数
          if (config.dateString) {
            reverseDisplayParam = JSON.stringify({
              dateString: config.dateString,
              displayText: config.dateString,
            });
          }
        } else if (config.type === 'DATETIME') {
          configParam = config.dateTimeString || '';
          type = config.name || '日期时间配置';

          // 生成回显参数
          if (config.dateTimeString) {
            reverseDisplayParam = JSON.stringify({
              dateTimeString: config.dateTimeString,
              displayText: config.dateTimeString,
            });
          }
        } else if (config.type === 'TIME_OFFSET_SELECTOR') {
          configParam = config.combinedValue || '';
          type = config.name || '时间偏移配置';

          // 生成回显参数
          if (config.combinedValue) {
            reverseDisplayParam = JSON.stringify({
              offsetValue: config.offsetValue,
              timeString: config.timeString,
              combinedValue: config.combinedValue,
              displayText: `${config.offsetValue}天 ${config.timeString}`,
            });
          }
        } else if (config.type === 'CHECKBOX') {
          // 多选框的值用逗号分隔
          configParam = (config.selectedValues || []).join(',');
          type = config.name || '多选配置';

          // 生成回显参数
          if (config.selectedValues && config.selectedValues.length > 0 && config.value) {
            const selectedOptions = config.value.filter((opt: any) => config.selectedValues.includes(opt.value));
            reverseDisplayParam = JSON.stringify({
              selectedValues: config.selectedValues,
              selectedLabels: selectedOptions.map((opt: any) => opt.label),
              displayText: selectedOptions.map((opt: any) => opt.label).join('、'),
            });
          }
        } else {
          // 其他类型的配置
          configParam = String(
            config.selectedValue || config.inputValue || config.dateString || config.combinedValue || '',
          );
          type = config.name || '通用配置';

          reverseDisplayParam = JSON.stringify({
            value: configParam,
            displayText: String(configParam),
          });
        }

        // 只有当配置有值时才添加到提交数据中
        if (configParam) {
          nodeObj.configs.push({
            type: type,
            metaKey: key,
            configParam: configParam,
            reverseDisplayParam: reverseDisplayParam,
          });
        }
      });
    }
    console.log(nodeObj, "nodeObj");

    nodes.push(nodeObj);
  });

  // 合并edit组件的数据和节点数据
  const combinedData = {
    ...processOrchestrationData.value,
    nodes,
    // 编辑模式下添加 sourceVerId 字段
    ...(processOrchestrationData.value?.id && processOrchestrationData.value?.verId
      ? {
        sourceVerId: processOrchestrationData.value.verId,
      }
      : {}),
  };

  // 打印完整提交数据
  console.log('完整提交数据:', combinedData);

  // // 根据是否有id决定调用新增还是编辑接口
  if ((combinedData as any).id) {
    // 有id，调用编辑接口
    processOrchestrationApi
      .save(combinedData as any)
      .then(async (res) => {
        message.success('更新成功！');
        // 提交成功后清除缓存数据
        await clearCacheAfterSubmit();
        router.push('/bidman/processOrchestration/index');
      })
      .catch((error) => {
        console.error('更新失败:', error);
        message.error('更新失败，请重试!');
      });
  } else {
    // 没有id，调用新增接口
    processOrchestrationApi
      .save(combinedData as any)
      .then(async (res) => {
        message.success('保存成功！');
        // 提交成功后清除缓存数据
        await clearCacheAfterSubmit();
        router.push('/bidman/processOrchestration/index');
      })
      .catch((error) => {
        console.error('保存失败:', error);
        message.error('保存失败，请重试!');
      });
  }
};

// 获取当前节点数据
const getCurrentNodeData = () => {
  return {
    nodeColumns: nodeColumns.value,
    nodeConfigsMap: nodeConfigsMap.value,
    timestamp: Date.now(),
    username: loginUser.value?.username || 'unknown' // 添加用户名，与edit.vue保持一致
  };
};

// 手动暂存（调用edit.vue的暂存功能）
const manualSave = async () => {
  // 只在新增模式下进行手动暂存
  if (processOrchestrationData.value?.id) {
    message.info('编辑模式下无需暂存');
    return;
  }

  try {
    // 更新全局节点数据，包含用户名信息
    const nodeData = getCurrentNodeData();
    if (window.updateProcessNodeData) {
      window.updateProcessNodeData(nodeData);
    }

    // 调用edit.vue的手动暂存功能
    if (window.manualSaveProcessData) {
      await window.manualSaveProcessData();
      console.log(`已为用户[${loginUser.value?.username || 'unknown'}]暂存流程节点数据`);
      message.success('暂存成功');
    } else {
      message.error('暂存功能未初始化');
    }
  } catch (error) {
    console.error('节点手动暂存失败:', error);
    message.error('节点暂存失败，请重试');
  }
};

// 提交成功后清除缓存数据
const clearCacheAfterSubmit = async () => {
  try {
    // 清除全局节点数据
    if (window.processNodesData) {
      window.processNodesData = null;
    }

    // 调用edit.vue的清除缓存功能
    // 通过全局方法调用edit.vue的clearCacheAfterSubmit方法
    if (window.clearCacheAfterSubmit) {
      await window.clearCacheAfterSubmit();
    }

    console.log('提交成功，已清除缓存数据');
  } catch (error) {
    console.error('清除缓存数据失败:', error);
  }
};

// 监听节点数据变化，自动更新到全局变量
watch([nodeColumns, nodeConfigsMap], () => {
  if (showNodesView.value && !processOrchestrationData.value?.id) {
    const nodeData = getCurrentNodeData(); // 包含用户名信息的节点数据
    if (window.updateProcessNodeData) {
      window.updateProcessNodeData(nodeData);
      console.log(`自动更新用户[${loginUser.value?.username || 'unknown'}]的流程节点数据`);
    }
  }
}, { deep: true });

onMounted(async () => {
  // 组件挂载时获取数据
  await fetchProcessData();
});
</script>

<template>
  <div class="page-container">
    <!-- 使用v-show控制显示哪个组件 -->
    <div v-show="!showNodesView" style="height: 100%">
      <!-- edit组件 -->
      <EditComp @edit-complete="handleEditComplete" />
    </div>

    <!-- 节点视图 -->
    <div v-show="showNodesView">
      <!-- 新的节点编排界面 -->
      <div class="content-wrapper">
        <div class="process-flow">
          <!-- 渲染流程节点列 -->
          <template v-for="(column, columnIndex) in nodeColumns" :key="column.id">
            <div class="process-step-container" :class="getConnectionLineClass(columnIndex)">
              <div class="process-step">
                <!-- 根据列的收起状态决定显示哪些节点 -->
                <template v-for="(node, nodeIndex) in column.nodes" :key="node.id">
                  <!-- 如果列是收起状态，只显示选中的节点；如果是展开状态，显示所有节点 -->
                  <div v-if="!column.isCollapsed || node.isSelected" class="node-container">
                    <div class="pro_list" :class="{
                      checked: node.isSelected,
                      edited: node.isEdited,
                    }" @click="selectNode(columnIndex, nodeIndex)">
                      <div class="pro_title">
                        {{ node.title }}
                        <span v-if="node.isEdited" class="edited-indicator">●</span>
                      </div>
                      <div class="pro_content">{{ node.name }}</div>
                      <!-- 只有选中的节点显示操作按钮 -->
                      <div class="node-actions-inner" v-if="node.isSelected">
                        <!-- 只有当节点有配置项时才显示编辑按钮 -->
                        <Button v-if="hasConfigs(node.code)" class="action-btn-inner edit-btn-inner" type="primary"
                          size="small" @click.stop="openEditModal(columnIndex, nodeIndex)" title="编辑">
                          <EditOutlined />
                        </Button>
                        <!-- 展开按钮，放在删除按钮旁边 -->
                        <Button v-if="column.isCollapsed && column.nodes.length > 1"
                          class="action-btn-inner expand-btn-inner" type="primary" size="small"
                          @click.stop="expandColumn(columnIndex)" title="展开选择其他节点">
                          <span style="color: white; font-size: 12px">⇄</span>
                        </Button>
                        <Button v-if="columnIndex > 0" class="action-btn-inner delete-btn-inner" type="primary" danger
                          size="small" @click.stop="deleteColumn(columnIndex)" title="删除整列">
                          <DeleteOutlined />
                        </Button>
                      </div>
                    </div>
                    <!-- 只在主流程列的选中节点且有下一级节点时显示加号按钮 -->
                    <div v-if="node.isSelected && hasNextNodes(node.code) && column.isMainFlow"
                      class="add-node-btn-wrapper">
                      <Button class="add-node-btn" type="primary" shape="circle" @click.stop="
                        handleAddNode(
                          columnIndex,
                          column.nodes.findIndex((n) => n.isSelected),
                        )
                        " title="添加下一列节点">
                        <PlusOutlined />
                      </Button>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="footer-container">
        <Button type="default" @click="backToEdit" style="margin-right: 10px">返回上一步</Button>
        <Button v-if="!processOrchestrationData?.id" @click="manualSave" style="margin-right: 10px">
          暂存
        </Button>
        <Button type="primary" @click="subBtn" class="submit-button">提交</Button>
      </div>

      <!-- 节点编辑弹框 -->
      <Drawer title="节点配置" :open="modalVisible" placement="bottom" height="400px" :closable="true"
        @close="handleModalCancel">
        <template #extra>
          <Button @click="handleModalCancel" class="cancel-btn">取消</Button>
          <Button type="primary" :loading="confirmLoading" @click="handleModalOk">确定</Button>
        </template>
        <div class="node-edit-form">
          <!-- 节点配置区域 -->
          <div v-if="Object.keys(nodeConfigs).length > 0" class="node-configs-section">
            <!-- 配置项容器 -->
            <div class="config-grid">
              <!-- 遍历nodeConfigs显示各配置项 -->
              <div v-for="(config, key) in nodeConfigs" :key="key" class="config-item">
                <!-- 单选框类型配置 -->
                <div v-if="config.type === 'RADIO'" class="form-row">
                  <div class="form-item">
                    <span class="form-label" :class="{ required: config.required }">{{ config.name }}:</span>
                    <div class="form-content">
                      <Radio.Group v-model:value="config.selectedValue"
                        @change="(e) => handleRadioChange(key, e.target.value)">
                        <Radio v-for="option in config.value" :key="option.value" :value="option.value">
                          {{ option.label }}
                        </Radio>
                      </Radio.Group>
                    </div>
                  </div>
                </div>

                <!-- 数字输入框类型配置 -->
                <div v-else-if="config.type === 'INPUT_NUMBER'" class="form-row">
                  <div class="form-item">
                    <span class="form-label" :class="{ required: config.required }">{{ config.name }}:</span>
                    <div class="form-content">
                      <Input v-model:value="config.inputValue" type="number" min="0" placeholder="请输入"
                        class="input-width" @change="(e) => handleInputChange(key, e.target.value)" />
                    </div>
                  </div>
                </div>

                <!-- 日期选择器类型配置 -->
                <div v-else-if="config.type === 'DATE'" class="form-row">
                  <div class="form-item">
                    <span class="form-label" :class="{ required: config.required }">{{ config.name }}:</span>
                    <div class="form-content">
                      <DatePicker v-model:value="config.dateValue" type="date" placeholder="请选择日期"
                        @change="(e) => handleDateChange(key, e)" />
                    </div>
                  </div>
                </div>

                <!-- 日期时间选择器类型配置 -->
                <div v-else-if="config.type === 'DATETIME'" class="form-row">
                  <div class="form-item">
                    <span class="form-label" :class="{ required: config.required }">{{ config.name }}:</span>
                    <div class="form-content">
                      <DatePicker v-model:value="config.dateTimeValue" show-time placeholder="请选择日期时间"
                        format="YYYY-MM-DD HH:mm:ss" @change="(e) => handleDateTimeChange(key, e)" />
                    </div>
                  </div>
                </div>

                <!-- 时间偏移选择器类型配置 -->
                <div v-else-if="config.type === 'TIME_OFFSET_SELECTOR'" class="form-row">
                  <div class="form-item">
                    <span class="form-label" :class="{ required: config.required }">{{ config.name }}:</span>
                    <div class="form-content">
                      <div class="time-offset-container">
                        <Input v-model:value="config.offsetValue" type="number" min="0" placeholder="请输入天数(必填)"
                          class="offset-input" @change="(e) => handleTimeOffsetChange(key, 'offset', e.target.value)" />
                        <span class="offset-separator">天</span>
                        <DatePicker v-model:value="config.timeValue" picker="time" format="HH:mm" placeholder="选择时间(可选)"
                          class="time-picker" @change="(e) => handleTimeOffsetChange(key, 'time', e)" />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 多选框类型配置 -->
                <div v-else-if="config.type === 'CHECKBOX'" class="form-row">
                  <div class="form-item">
                    <span class="form-label" :class="{ required: config.required }">{{ config.name }}:</span>
                    <div class="form-content">
                      <Select v-model:value="config.selectedValues" mode="multiple" placeholder="请选择"
                        class="input-width" @change="(value) => handleCheckboxChange(key, value)"
                        :options="config.value" :field-names="{ label: 'label', value: 'value' }" />
                    </div>
                  </div>
                </div>

                <!-- 配置说明 -->
                <div class="config-desc">{{ config.desc }}</div>
              </div>
            </div>
          </div>

          <!-- 无配置项时的提示 -->
          <div v-else class="no-config-tip">
            <div class="tip-content">该节点暂无可配置项</div>
          </div>
        </div>
      </Drawer>
    </div>
  </div>
</template>

<style scoped lang="less">
.page-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  position: relative;
}

.content-wrapper {
  flex: 1;
  padding: 40px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 140px);
  /* 减去头部和底部的高度 */
}

.footer-container {
  display: flex;
  justify-content: flex-end;
  padding: 12px 24px;
  border-top: 1px solid #f0f0f0;
  background-color: #fff;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  /* 确保底部按钮始终在最上层 */
}

.submit-button {
  width: 120px;
  height: 32px;
  border-radius: 4px;
}

.processTesting {
  width: 100%;
  overflow: auto;
  padding: 20px;
}

.process-flow {
  display: flex;
  align-items: flex-start;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 20px 0;
  min-width: 100%;
  flex: 1;
  /* 使用flex-1占满剩余空间 */
  scrollbar-width: thin;
  scrollbar-color: #1890ff #f0f0f0;
  scroll-behavior: smooth;
  /* 添加平滑滚动效果 */
  position: relative;
}

.process-flow::-webkit-scrollbar {
  height: 12px;
  /* 增加滚动条高度，使其更明显 */
}

.process-flow::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 6px;
  margin: 0 10px;
  /* 添加左右边距 */
}

.process-flow::-webkit-scrollbar-thumb {
  background-color: #1890ff;
  border-radius: 6px;
  border: 2px solid #f0f0f0;
  /* 添加边框效果 */
}

.process-flow::-webkit-scrollbar-thumb:hover {
  background-color: #40a9ff;
  /* 悬停时的颜色 */
}

/* 确保滚动条始终显示在底部 */
.process-flow::-webkit-scrollbar-corner {
  background: #f0f0f0;
}

.process-step-container {
  margin-right: 120px;
  position: relative;
  flex-shrink: 0;

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 85px;
    right: -110px;
    width: 100px;
    height: 3px;
    background-color: #1890ff;
    border-radius: 2px;
    transition: opacity 0.3s;
  }

  &.column-hidden::after {
    opacity: 0;
  }

  // 实线连接
  &.solid-line:not(:last-child)::after {
    background-color: #1890ff;
    border: none;
  }

  // 虚线连接
  &.dashed-line:not(:last-child)::after {
    background-color: transparent;
    border-top: 3px dashed #1890ff;
    border-radius: 0;
  }

  // 无连接线
  &.no-line:not(:last-child)::after {
    display: none;
  }
}

.process-step-container+.column-hidden {
  &::before {
    content: '';
    position: absolute;
    top: 40px;
    left: -40px;
    width: 30px;
    height: 3px;
    background-color: white;
    z-index: 2;
  }
}

.process-step {
  display: flex;
  flex-direction: column;
}

.node-container {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  position: relative;
}

.pro_list {
  width: 200px;
  height: 100px;
  text-align: center;
  border: 1px dashed #ccc;
  /* 默认虚线边框 */
  display: flex;
  flex-direction: column;
  background-color: transparent;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s;
  position: relative;

  &:hover {
    box-shadow: 0 4px 10px rgba(24, 144, 255, 0.2);
    transform: translateY(-2px);
  }

  &.checked,
  &[data-checked='true'] {
    border: 1px solid #1890ff;
    /* 选中时实线边框 */
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  }

  &.edited {
    border: 1px solid #52c41a;
    background-color: rgba(82, 196, 26, 0.05);

    .pro_title {
      background-color: #52c41a;
    }
  }

  &.checked.edited {
    border: 1px solid #1890ff;
    background-color: rgba(24, 144, 255, 0.05);

    .pro_title {
      background-color: #1890ff;
    }
  }

  .pro_title {
    height: 30px;
    line-height: 30px;
    background-color: #1890ff;
    color: white;
    border-bottom: 1px solid #ccc;
    font-weight: bold;
    border-radius: 4px 4px 0 0;
    position: relative;

    .edited-indicator {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      color: #fff;
      font-size: 12px;
      animation: pulse 2s infinite;
    }
  }

  .pro_content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
  }

  .node-actions-inner {
    display: flex;
    justify-content: center;
    gap: 8px;
    padding: 4px 0;
  }
}

.action-btn-inner {
  height: 24px;
  width: 40px;
  padding: 0 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;

  &.edit-btn-inner {
    background-color: #1890ff;
  }

  &.expand-btn-inner {
    background-color: #52c41a !important;
    /* 绿色背景 */

    &:hover {
      background-color: #73d13d !important;
      /* 悬停时的绿色 */
      transform: scale(1.05);
    }

    &:active {
      background-color: #389e0d !important;
      /* 点击时的深绿色 */
    }
  }

  &.switch-btn-inner {
    background-color: #52c41a;
  }

  &.delete-btn-inner {
    background-color: #ff4d4f;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }

  100% {
    opacity: 1;
  }
}

.node-edit-form {
  padding: 0 20px;
}

.node-configs-section {
  padding: 10px 0;
}

.config-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}

.config-item {
  width: calc(33.33% - 30px);
  margin: 0 15px 20px;
  padding-bottom: 16px;
  border-bottom: 1px dashed #f0f0f0;
}

.form-row {
  display: flex;
  align-items: center;
}

.form-item {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.form-label {
  width: 100%;
  text-align: left;
  line-height: 32px;
  padding-right: 8px;
  white-space: nowrap;
  color: #333;
  font-size: 14px;

  &.required::before {
    display: inline-block;
    margin-right: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
  }
}

.form-content {
  width: 100%;
}

.config-desc {
  color: #888;
  font-size: 12px;
  margin-top: 4px;
  margin-left: 0;
  line-height: 1.5;
}

.input-width {
  width: 100%;
}

.time-offset-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.offset-input {
  width: 80px;
  flex-shrink: 0;
}

.offset-separator {
  color: #666;
  font-size: 14px;
  white-space: nowrap;
  flex-shrink: 0;
}

.time-picker {
  flex: 1;
  min-width: 120px;
}

.cancel-btn {
  margin-right: 8px;
}

.form-row:nth-child(3),
.form-row:nth-child(4) {
  align-items: flex-start;

  .form-label {
    padding-top: 5px;
  }
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  width: calc(100% - 40px);
  border-top: 1px solid #f0f0f0;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
  display: none;
}

.column-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}

.collapse-column-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #faad14;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  line-height: 1;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.3s;

  &:hover {
    background-color: #ffc53d;
    transform: scale(1.1);
  }
}

.add-node-container {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

.add-node-btn-wrapper {
  position: absolute;
  right: -75px;
  top: 50%;
  transform: translateY(-50%);
}

.add-node-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
}

.no-config-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.tip-content {
  color: #999;
  font-size: 14px;
}

// 增加一个自动滚动到底部的动画类
@keyframes scrollRight {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(100%);
  }
}

.auto-scroll-right {
  animation: scrollRight 0.5s ease-out forwards;
}

/* 响应式设计 - 确保在不同屏幕尺寸下滚动条都在底部 */
@media screen and (max-height: 768px) {
  .content-wrapper {
    padding-bottom: 50px;
    height: calc(100vh - 120px);
  }
}

@media screen and (max-height: 600px) {
  .content-wrapper {
    padding-bottom: 70px;
    height: calc(100vh - 100px);
  }
}

@media screen and (min-height: 1080px) {
  .content-wrapper {
    height: calc(100vh - 160px);
  }
}

/* 确保滚动条在容器底部 */
.process-flow {
  /* 强制滚动条显示在容器底部 */
  scrollbar-gutter: stable;
}

/* 针对Firefox的滚动条样式 */
@-moz-document url-prefix() {
  .process-flow {
    scrollbar-width: thin;
    scrollbar-color: #1890ff #f0f0f0;
  }
}
</style>
