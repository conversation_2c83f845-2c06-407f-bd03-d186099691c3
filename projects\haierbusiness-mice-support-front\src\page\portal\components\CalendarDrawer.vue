<script setup lang="ts">
import { computed, ref } from 'vue';
import { CloseOutlined, DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import CalenderIconImg from '@/assets/image/home/<USER>'
import LikeImg from '@/assets/image/home/<USER>'
import LikeGrayImg from '@/assets/image/home/<USER>'
import { usePortalStore } from '../store';

type TableDataType = {
  key: string;
  name: string;
  time: string;
  address: string;
  area: string;
  countdown: number;
};

const props = defineProps({
  modelValue: {
    type: Boolean,
  }
});

const emit = defineEmits(['update:modelValue']);

const store = usePortalStore();
const spreadCity = ref(false);
const paginationCurrent = ref(1);
const dataSource = [
  {
    key: '1',
    name: '2024第十六届亚洲水技术展览会',
    time: '2024/12/11',
    address: '上海新国际博览中心',
    area: '3000㎡',
    countdown: 5,
  },
  {
    key: '2',
    name: '2024第十六届亚洲水技术展览会',
    time: '2024/12/10',
    address: '上海新国际博览中心',
    area: '3000㎡',
    countdown: 6,
  },
];

const columns = [
  {
    title: '大会名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true,
  },
  {
    title: '举办时间',
    dataIndex: 'time',
    key: 'time',
    sorter: (a: TableDataType, b: TableDataType) => new Date(a.time).getTime() - new Date(b.time).getTime(),
  },
  {
    title: '举办地点',
    dataIndex: 'address',
    key: 'address',
    width: 180,
  },
  {
    title: '场地面积',
    dataIndex: 'area',
    key: 'area',
  },
  {
    title: '距开幕',
    dataIndex: 'countdown',
    key: 'countdown',
    sorter: (a: TableDataType, b: TableDataType) => a.countdown - b.countdown,
  },
];

const activeCityId = ref('recommand')
const modelOpen = computed({
  get: () => props.modelValue,
  set: v => emit('update:modelValue', v)
});

const showAreaList = computed(() => spreadCity.value ? store.areaList : store.areaList.slice(0, 7))
</script>

<template>
  <h-drawer
    v-model:open="modelOpen"
    class="calender-drawer"
    placement="right"
    width="750"
    :closable="false"
    :bodyStyle="{ background: '#F7F8FC', display: 'flex', flexDirection: 'column' }"
  >
    <div class="city flex">
      <span class="label">所在省市：</span>
      <ul>
        <li>
          <span :class="{ selected: activeCityId === 'recommand' }" @click="activeCityId = 'recommand'">
            <img :src="activeCityId === 'recommand' ? LikeImg : LikeGrayImg" :style="{ width: '12px', height: '12px', marginTop: '-3px', marginRight: '5px' }" />
            <span>推荐</span>
          </span>
        </li>
        <li v-for="item of showAreaList" :key="item.id" @click="activeCityId = item.id">
          <span :class="{ selected: activeCityId === item.id }">{{ item.name }}</span>
        </li>
      </ul>
      <a @click="spreadCity = !spreadCity">
        <span>{{ spreadCity ? '收起' : '展开' }}</span>
        <DownOutlined v-show="!spreadCity" :style="{ fontSize: '12px', marginLeft: '4px' }" />
        <UpOutlined v-show="spreadCity" :style="{ fontSize: '12px', marginLeft: '4px' }" />
      </a>
    </div>
    <h-table class="table" :dataSource="dataSource" :columns="columns" :pagination="false">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'countdown'">
          <span :style="{ color: '#FF5533' }">{{ record.countdown }}</span>
          <span>天</span>
        </template>
      </template>
    </h-table>
    <div class="space" />
    <div class="flex-center">
      <h-pagination class="pagination" v-model:current="paginationCurrent" :total="150" show-less-items />
    </div>
    <template #title>
      <div class="flex acenter">
        <img :src="CalenderIconImg" :style="{ width: '20px', height: '20px', marginRight: '12px' }" />
        <span>大会日历</span>
      </div>
    </template>
    <template #extra>
      <CloseOutlined :style="{ cursor: 'pointer' }" @click="modelOpen = false" />
    </template>
  </h-drawer>
</template>

<style scoped lang="less">
  .calender-drawer {
    .city {
      background: #FFFFFF;
      box-shadow: 0px 2px 6px 0px rgba(0,0,0,0.06);
      border-radius: 8px;
      padding: 20px 12px 0 12px;
      .label {
        margin-right: 15px;
      }
      ul {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 0;
        li {
          cursor: pointer;
          width: 68px;
          height: 35px;
          text-align: center;
          color: #4E5969;
          span.selected {
            padding: 5px 7px;
            background: linear-gradient(180deg, #35A1EF 0%, #1868DB 100%);
            box-shadow: 0px 2px 6px 0px rgba(0,0,0,0.06);
            border-radius: 3px;
            color: #fff;
          }
        }
      }
    }
    .table {
      margin-top: 13px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0px 2px 6px 0px rgba(0,0,0,0.06);
      :deep(.ant-table) {
        border-radius: 8px;
        .ant-table-thead {
          tr {
            .ant-table-cell {
              background: #fff;
              color: #86909C;
              font-weight: 400;
              &:first-child {
                border-top-left-radius: 8px;
              }
              &:last-child {
                border-top-right-radius: 8px;
              }
              &::before {
                opacity: 0;
              }
            }
          }
        }
      }
    }
    .pagination {
      :deep(.ant-pagination-prev), :deep(.ant-pagination-next), :deep(.ant-pagination-item) {
        border: 1px solid rgba(0,0,0,0.15);
        .ant-pagination-item-link, a {
          color: rgba(0,0,0,0.65);
        }
      }
      :deep(.ant-pagination-item-active) {
        background: #1868DB;
        a {
          color: #fff;
        }
      }
      :deep(.ant-pagination-disabled) {
        .ant-pagination-item-link {
          color: rgba(0,0,0,0.25);
        }
      }
    }
    .space {
      flex: 1;
    }
  }
</style>
