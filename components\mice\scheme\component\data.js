const tempSchemeRes = [
  {
    id: 35,
    mainCode: 'RC20250528100855564028',
    miceId: 232,
    miceDemandId: 216,
    miceDemandPushId: 132,
    miceSchemeIds: '6,9,13',
    schemeCombinationTotalPrice: 6446,
    isExclude: true,
    excludeRemarks: '不大行',
    isSelected: null,
    unselectRemarks: null,
    isSpecified: false,
    specifiedRemarks: null,
    isTarget: false,
    targetReason: null,
    schemeDetails: [
      {
        id: 6,
        mainCode: 'RC20250528100855564028',
        miceId: 232,
        miceDemandId: 216,
        miceDemandPushId: 132,
        miceBiddingPushId: null,
        merchantId: 5,
        pdmMerchantPoolId: 1,
        pdmMerchantPoolName: '直签酒店服务商',
        pdmMerchantPoolItems: 1,
        pdmMerchantPoolGroupIds: '1',
        pdmMerchantPoolGroupNames: '本地测试资源组',
        merchantCode: 'V82685',
        merchantType: 2,
        merchantName: '天津市文光集团有限公司',
        contactMerchantName: null,
        contactMerchantPhone: null,
        merchantScore: 0,
        merchantContract: 'aaaaa',
        merchantPlatformUsageFeeRate: null,
        sourceId: null,
        blockchainId: null,
        msMarketPriceInquiryId: null,
        schemeTotalPrice: 6245,
        agreementTotalPrice: null,
        agreementSchemeTotalPrice: null,
        marketTotalPrice: null,
        marketSchemeTotalPrice: null,
        marketVerifyState: null,
        remarks: null,
        isExclude: true,
        excludeRemarks: '不大行',
        isSelected: null,
        unselectRemarks: null,
        isSpecified: null,
        specifiedRemarks: null,
        isTarget: null,
        submitDeadline: null,
        abandonType: null,
        abandonReason: null,
        isValid: null,
        schemeType: 0,
        schemeState: 1,
        submitterIp: '127.0.0.1',
        hotels: [
          {
            id: 7,
            decorationYear: '2005',
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            msMarketPriceInquiryId: null,
            miceDemandHotelId: 555,
            miceDemandPushHotelId: 2871,
            hotelName: '蓝海大酒店',
            cityId: null,
            cityName: null,
            districtId: null,
            districtName: null,
            wishId: null,
            wishAreaId: null,
            wishCode: null,
            level: 48,
          },
          {
            id: 8,
            mainCode: 'RC20250528100855564028',
            decorationYear: '2012',
            miceId: 232,
            miceSchemeId: 6,
            msMarketPriceInquiryId: null,
            miceDemandHotelId: 304,
            miceDemandPushHotelId: 3288,
            hotelName: '天海酒店',
            cityId: null,
            cityName: null,
            districtId: null,
            districtName: null,
            wishId: null,
            wishAreaId: null,
            wishCode: null,
            level: 15,
          },
        ],
        stays: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceDemandHotelId: 304,
            miceDemandStayId: 305,
            miceSchemeId: 6,
            miceSchemeHotelId: null,
            demandDate: '2025-05-28',
            roomType: 1,
            breakfastType: 1,
            personNum: 2,
            schemeRoomNum: 1,
            discrepancyReason: '人数与房间数不一致原因',
            schemeUnitPrice: 1,
            agreementProductId: 1,
            agreementUnitPrice: 1,
            marketUnitPrice: null,
            retailUnitPrice: 2,
            msMarketPriceInquiryDetailsId: null,
            sourceId: null,
            description: '描述测试',
          },
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceDemandHotelId: 304,
            miceDemandStayId: 305,
            miceSchemeId: 6,
            miceSchemeHotelId: null,
            demandDate: '2025-05-28',
            roomType: 1,
            breakfastType: 1,
            personNum: 2,
            schemeRoomNum: 1,
            discrepancyReason: '人数与房间数不一致原因',
            schemeUnitPrice: 1,
            agreementProductId: 1,
            agreementUnitPrice: 1,
            marketUnitPrice: null,
            retailUnitPrice: 2,
            msMarketPriceInquiryDetailsId: null,
            sourceId: null,
            description: '描述测试',
          },
        ],
        places: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceDemandHotelId: 126,
            miceDemandPlaceId: 75,
            miceSchemeId: 6,
            miceSchemeHotelId: null,
            demandDate: '2025-05-23',
            usageTime: 7,
            usagePurpose: 7,
            schemePersonNum: 10,
            area: 0,
            underLightFloor: 0,
            tableType: 1,
            hasLed: true,
            schemeLedNum: 10,
            schemeLedSource: null,
            guildhall: '嵩山厅',
            ledSpecs: '需要很好的',
            hasTea: true,
            teaEachTotalPrice: 20,
            teaDesc: '需要很好的',
            schemeUnitPlacePrice: 1,
            schemeUnitLedPrice: 1,
            schemeUnitTeaPrice: 1,
            msMarketPriceInquiryDetailsId: null,
            marketPriceUnitPrice: null,
            agreementProductId: 2,
            agreementUnitPrice: 500,
            retailUnitPrice: 800,
            description: '方案说明...',
            sourceId: null,
          },
        ],
        caterings: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            isInsideHotel: true,
            miceDemandHotelId: 304,
            miceDemandCateringId: 71,
            miceSchemeId: 6,
            miceSchemeHotelId: null,
            demandDate: '2025-05-23',
            cateringType: 1,
            cateringTime: 2,
            schemePersonNum: 10,
            demandUnitPrice: 1,
            isIncludeDrinks: true,
            schemeUnitPrice: 1,
            description: '用餐测试',
            sourceId: null,
          },
        ],
        vehicles: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandVehicleId: 60,
            demandDate: '2025-05-04',
            usageType: 1,
            usageTime: 1,
            seats: 2,
            schemeVehicleNum: 8,
            brand: 'qq',
            route: '机场,xx酒店',
            schemeUnitPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        attendants: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandAttendantId: 57,
            demandDate: '2025-05-04',
            type: 0,
            schemePersonNum: 2,
            duty: '摄影',
            schemeUnitPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        activities: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandActivityId: 53,
            demandDate: '2025-05-04',
            demandUnitPrice: 50000,
            schemePersonNum: 200,
            schemeUnitPrice: 30,
            description: '说明..',
            paths: ['http://www.baidu.com/nn'],
            sourceId: null,
          },
        ],
        insurances: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandInsuranceId: 50,
            demandDate: '2025-05-04',
            demandUnitPrice: 25,
            schemePersonNum: 200,
            productId: 1,
            productMerchantId: 1,
            insuranceName: '太平洋',
            insuranceContent: '意外险',
            schemeUnitPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        material: {
          id: 4,
          mainCode: 'RC20250528100855564028',
          miceId: 232,
          miceSchemeId: 6,
          miceDemandMaterialId: 45,
          demandTotalPrice: 800,
          schemeTotalPrice: 1,
          description: '说明..',
          sourceId: null,
          materialDetails: [
            {
              id: 4,
              mainCode: 'RC20250528100855564028',
              miceId: 232,
              miceSchemeId: 6,
              miceSchemeMaterialId: 4,
              miceDemandMaterialDetailsId: 45,
              type: 1,
              specs: '布展物料xxx',
              schemeMaterialNum: 2,
              demandUnitPrice: 1,
              schemeUnitPrice: 1,
              sourceId: null,
            },
          ],
        },
        traffic: {
          id: 4,
          mainCode: 'RC20250528100855564028',
          miceId: 232,
          miceSchemeId: 6,
          miceDemandTrafficId: 34,
          demandTotalPrice: 100,
          schemeTotalPrice: 1,
        },
        present: {
          id: 4,
          miceDemandPresentId: 41,
          unitPrice: 300,
          description: null,
          sourceId: null,
          presentDetails: [],
        },
        others: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandOtherId: 40,
            demandDate: '2025-05-04',
            demandTotalPrice: 100,
            itemName: null,
            num: 20,
            unit: '个',
            specs: '其他xx',
            schemeTotalPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        serviceFee: {
          id: 4,
          mainCode: 'RC20250528100855564028',
          miceId: 232,
          miceSchemeId: 6,
          serviceFeeLimitRate: 0,
          serviceFeeLimit: 1,
          schemeServiceFeeReal: 1,
          sourceId: null,
        },
        differences: [
          {
            id: 3,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            demandId: 216,
            schemeId: 6,
            demandStayIds: '126',
            schemeStayIds: '1988',
            differenceDate: '2025-05-28',
            demandTotalPerson: 11,
            schemeTotalPerson: null,
            demandRoomType: '1,3',
            schemeRoomType: '1',
            demandOneRooms: 1,
            schemeOneRooms: 1,
            demandTwoRooms: 0,
            schemeTwoRooms: 0,
            demandSuiteRooms: 1,
            schemeSuiteRooms: 0,
            reason: '差异原因',
          },
        ],
      },
      {
        id: 9,
        mainCode: 'RC20250528100855564028',
        miceId: 232,
        miceDemandId: 216,
        miceDemandPushId: 132,
        miceBiddingPushId: null,
        merchantId: 4780,
        pdmMerchantPoolId: 3,
        pdmMerchantPoolName: '礼品服务商',
        pdmMerchantPoolItems: 512,
        pdmMerchantPoolGroupIds: '1,2',
        pdmMerchantPoolGroupNames: '本地测试资源组',
        merchantCode: 'V9033141',
        merchantType: 3,
        merchantName: '青岛健力源餐饮管理有限公司',
        contactMerchantName: null,
        contactMerchantPhone: null,
        merchantScore: 64,
        merchantContract: 'JT2025031900001',
        merchantPlatformUsageFeeRate: null,
        sourceId: null,
        blockchainId: null,
        msMarketPriceInquiryId: null,
        schemeTotalPrice: 1,
        agreementTotalPrice: null,
        agreementSchemeTotalPrice: null,
        marketTotalPrice: null,
        marketSchemeTotalPrice: null,
        marketVerifyState: null,
        remarks: null,
        isExclude: false,
        excludeRemarks: null,
        isSelected: true,
        unselectRemarks: null,
        isSpecified: null,
        specifiedRemarks: null,
        isTarget: true,
        submitDeadline: null,
        abandonType: null,
        abandonReason: null,
        isValid: null,
        schemeType: 0,
        schemeState: 1,
        submitterIp: '127.0.0.1',
        hotels: [],
        stays: [],
        places: [],
        caterings: [],
        vehicles: [],
        attendants: [],
        activities: [],
        insurances: [],
        material: null,
        traffic: null,
        present: {
          id: 6,
          miceDemandPresentId: 41,
          unitPrice: 300,
          description: null,
          sourceId: null,
          presentDetails: [],
        },
        others: [],
        serviceFee: null,
        differences: [],
      },
      {
        id: 13,
        mainCode: 'RC20250528100855564028',
        miceId: 232,
        miceDemandId: 216,
        miceDemandPushId: 132,
        miceBiddingPushId: null,
        merchantId: 4787,
        pdmMerchantPoolId: 27,
        pdmMerchantPoolName: '保险服务商',
        pdmMerchantPoolItems: 64,
        pdmMerchantPoolGroupIds: '1,2',
        pdmMerchantPoolGroupNames: '本地测试资源组',
        merchantCode: 'V9127486',
        merchantType: 4,
        merchantName: '山西长江源酒店管理有限公司',
        contactMerchantName: null,
        contactMerchantPhone: null,
        merchantScore: 0,
        merchantContract: 'aaaaa',
        merchantPlatformUsageFeeRate: null,
        sourceId: 12,
        blockchainId: null,
        msMarketPriceInquiryId: null,
        schemeTotalPrice: 200,
        agreementTotalPrice: null,
        agreementSchemeTotalPrice: null,
        marketTotalPrice: null,
        marketSchemeTotalPrice: null,
        marketVerifyState: null,
        remarks: null,
        isExclude: false,
        excludeRemarks: null,
        isSelected: true,
        unselectRemarks: null,
        isSpecified: null,
        specifiedRemarks: null,
        isTarget: true,
        submitDeadline: null,
        abandonType: null,
        abandonReason: null,
        isValid: null,
        schemeType: 0,
        schemeState: 1,
        submitterIp: '127.0.0.1',
        hotels: [],
        stays: [],
        places: [],
        caterings: [],
        vehicles: [],
        attendants: [],
        activities: [],
        insurances: [
          {
            id: 7,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 13,
            miceDemandInsuranceId: 50,
            demandDate: '2025-05-04',
            demandUnitPrice: 25,
            schemePersonNum: 200,
            productId: 1,
            productMerchantId: 1,
            insuranceName: '太平洋',
            insuranceContent: '意外险',
            schemeUnitPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        material: null,
        traffic: null,
        present: null,
        others: [],
        serviceFee: null,
        differences: [],
      },
    ],
  },
  {
    id: 35,
    mainCode: 'RC20250528100855564028',
    miceId: 232,
    miceDemandId: 216,
    miceDemandPushId: 132,
    miceSchemeIds: '6,9,13',
    schemeCombinationTotalPrice: 6446,
    isExclude: true,
    excludeRemarks: '不大行',
    isSelected: null,
    unselectRemarks: null,
    isSpecified: false,
    specifiedRemarks: null,
    isTarget: false,
    targetReason: null,
    schemeDetails: [
      {
        id: 6,
        mainCode: 'RC20250528100855564028',
        miceId: 232,
        miceDemandId: 216,
        miceDemandPushId: 132,
        miceBiddingPushId: null,
        merchantId: 5,
        pdmMerchantPoolId: 1,
        pdmMerchantPoolName: '直签酒店服务商',
        pdmMerchantPoolItems: 1,
        pdmMerchantPoolGroupIds: '1',
        pdmMerchantPoolGroupNames: '本地测试资源组',
        merchantCode: 'V82685',
        merchantType: 2,
        merchantName: '天津市文光集团有限公司',
        contactMerchantName: null,
        contactMerchantPhone: null,
        merchantScore: 0,
        merchantContract: 'aaaaa',
        merchantPlatformUsageFeeRate: null,
        sourceId: null,
        blockchainId: null,
        msMarketPriceInquiryId: null,
        schemeTotalPrice: 6245,
        agreementTotalPrice: null,
        agreementSchemeTotalPrice: null,
        marketTotalPrice: null,
        marketSchemeTotalPrice: null,
        marketVerifyState: null,
        remarks: null,
        isExclude: true,
        excludeRemarks: '不大行',
        isSelected: null,
        unselectRemarks: null,
        isSpecified: null,
        specifiedRemarks: null,
        isTarget: null,
        submitDeadline: null,
        abandonType: null,
        abandonReason: null,
        isValid: null,
        schemeType: 0,
        schemeState: 1,
        submitterIp: '127.0.0.1',
        hotels: [
          {
            id: 7,
            decorationYear: '2022',
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            msMarketPriceInquiryId: null,
            miceDemandHotelId: 555,
            miceDemandPushHotelId: 2871,
            hotelName: '香格里拉酒店',
            cityId: null,
            cityName: null,
            districtId: null,
            districtName: null,
            wishId: null,
            wishAreaId: null,
            wishCode: null,
            level: 15,
          },
          {
            id: 8,
            mainCode: 'RC20250528100855564028',
            decorationYear: '2020',
            miceId: 232,
            miceSchemeId: 6,
            msMarketPriceInquiryId: null,
            miceDemandHotelId: 304,
            miceDemandPushHotelId: 3288,
            hotelName: '亚朵酒店',
            cityId: null,
            cityName: null,
            districtId: null,
            districtName: null,
            wishId: null,
            wishAreaId: null,
            wishCode: null,
            level: 15,
          },
        ],
        stays: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceDemandHotelId: 304,
            miceDemandStayId: 305,
            miceSchemeId: 6,
            miceSchemeHotelId: null,
            demandDate: '2025-05-28',
            roomType: 1,
            breakfastType: 1,
            personNum: 2,
            schemeRoomNum: 1,
            discrepancyReason: '人数与房间数不一致原因',
            schemeUnitPrice: 1,
            agreementProductId: 1,
            agreementUnitPrice: 1,
            marketUnitPrice: null,
            retailUnitPrice: 2,
            msMarketPriceInquiryDetailsId: null,
            sourceId: null,
            description: '描述测试',
          },
        ],
        places: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceDemandHotelId: 126,
            miceDemandPlaceId: 75,
            miceSchemeId: 6,
            miceSchemeHotelId: null,
            demandDate: '2025-05-23',
            usageTime: 7,
            usagePurpose: 7,
            schemePersonNum: 10,
            area: 0,
            underLightFloor: 0,
            tableType: 1,
            hasLed: true,
            schemeLedNum: 10,
            schemeLedSource: null,
            guildhall: '嵩山厅',
            ledSpecs: '需要很好的',
            hasTea: true,
            teaEachTotalPrice: 20,
            teaDesc: '需要很好的',
            schemeUnitPlacePrice: 1,
            schemeUnitLedPrice: 1,
            schemeUnitTeaPrice: 1,
            msMarketPriceInquiryDetailsId: null,
            marketPriceUnitPrice: null,
            agreementProductId: 2,
            agreementUnitPrice: 500,
            retailUnitPrice: 800,
            description: '方案说明...',
            sourceId: null,
          },
        ],
        caterings: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            isInsideHotel: true,
            miceDemandHotelId: 304,
            miceDemandCateringId: 71,
            miceSchemeId: 6,
            miceSchemeHotelId: null,
            demandDate: '2025-05-23',
            cateringType: 1,
            cateringTime: 2,
            schemePersonNum: 10,
            demandUnitPrice: 1,
            isIncludeDrinks: true,
            schemeUnitPrice: 1,
            description: '用餐测试',
            sourceId: null,
          },
        ],
        vehicles: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandVehicleId: 60,
            demandDate: '2025-05-04',
            usageType: 1,
            usageTime: 1,
            seats: 2,
            schemeVehicleNum: 8,
            brand: 'qq',
            route: '机场,xx酒店',
            schemeUnitPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        attendants: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandAttendantId: 57,
            demandDate: '2025-05-04',
            type: 0,
            schemePersonNum: 2,
            duty: '摄影',
            schemeUnitPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        activities: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandActivityId: 53,
            demandDate: '2025-05-04',
            demandUnitPrice: 50000,
            schemePersonNum: 200,
            schemeUnitPrice: 30,
            description: '说明..',
            paths: ['http://www.baidu.com/nn'],
            sourceId: null,
          },
        ],
        insurances: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandInsuranceId: 50,
            demandDate: '2025-05-04',
            demandUnitPrice: 25,
            schemePersonNum: 200,
            productId: 1,
            productMerchantId: 1,
            insuranceName: '太平洋',
            insuranceContent: '意外险',
            schemeUnitPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        material: {
          id: 4,
          mainCode: 'RC20250528100855564028',
          miceId: 232,
          miceSchemeId: 6,
          miceDemandMaterialId: 45,
          demandTotalPrice: 800,
          schemeTotalPrice: 1,
          description: '说明..',
          sourceId: null,
          materialDetails: [
            {
              id: 4,
              mainCode: 'RC20250528100855564028',
              miceId: 232,
              miceSchemeId: 6,
              miceSchemeMaterialId: 4,
              miceDemandMaterialDetailsId: 45,
              type: 1,
              specs: '布展物料xxx',
              schemeMaterialNum: 2,
              demandUnitPrice: 1,
              schemeUnitPrice: 1,
              sourceId: null,
            },
          ],
        },
        traffic: {
          id: 4,
          mainCode: 'RC20250528100855564028',
          miceId: 232,
          miceSchemeId: 6,
          miceDemandTrafficId: 34,
          demandTotalPrice: 100,
          schemeTotalPrice: 1,
        },
        present: {
          id: 4,
          miceDemandPresentId: 41,
          unitPrice: 300,
          description: null,
          sourceId: null,
          presentDetails: [],
        },
        others: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandOtherId: 40,
            demandDate: '2025-05-04',
            demandTotalPrice: 100,
            itemName: null,
            num: 20,
            unit: '个',
            specs: '其他xx',
            schemeTotalPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        serviceFee: {
          id: 4,
          mainCode: 'RC20250528100855564028',
          miceId: 232,
          miceSchemeId: 6,
          serviceFeeLimitRate: 0,
          serviceFeeLimit: 1,
          schemeServiceFeeReal: 1,
          sourceId: null,
        },
        differences: [
          {
            id: 3,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            demandId: 216,
            schemeId: 6,
            demandStayIds: '126',
            schemeStayIds: '1988',
            differenceDate: '2025-05-28',
            demandTotalPerson: 11,
            schemeTotalPerson: null,
            demandRoomType: '1,3',
            schemeRoomType: '1',
            demandOneRooms: 1,
            schemeOneRooms: 1,
            demandTwoRooms: 0,
            schemeTwoRooms: 0,
            demandSuiteRooms: 1,
            schemeSuiteRooms: 0,
            reason: '差异原因',
          },
        ],
      },
      {
        id: 9,
        mainCode: 'RC20250528100855564028',
        miceId: 232,
        miceDemandId: 216,
        miceDemandPushId: 132,
        miceBiddingPushId: null,
        merchantId: 4780,
        pdmMerchantPoolId: 3,
        pdmMerchantPoolName: '礼品服务商',
        pdmMerchantPoolItems: 512,
        pdmMerchantPoolGroupIds: '1,2',
        pdmMerchantPoolGroupNames: '本地测试资源组',
        merchantCode: 'V9033141',
        merchantType: 3,
        merchantName: '青岛健力源餐饮管理有限公司',
        contactMerchantName: null,
        contactMerchantPhone: null,
        merchantScore: 64,
        merchantContract: 'JT2025031900001',
        merchantPlatformUsageFeeRate: null,
        sourceId: null,
        blockchainId: null,
        msMarketPriceInquiryId: null,
        schemeTotalPrice: 1,
        agreementTotalPrice: null,
        agreementSchemeTotalPrice: null,
        marketTotalPrice: null,
        marketSchemeTotalPrice: null,
        marketVerifyState: null,
        remarks: null,
        isExclude: false,
        excludeRemarks: null,
        isSelected: true,
        unselectRemarks: null,
        isSpecified: null,
        specifiedRemarks: null,
        isTarget: true,
        submitDeadline: null,
        abandonType: null,
        abandonReason: null,
        isValid: null,
        schemeType: 0,
        schemeState: 1,
        submitterIp: '127.0.0.1',
        hotels: [],
        stays: [],
        places: [],
        caterings: [],
        vehicles: [],
        attendants: [],
        activities: [],
        insurances: [],
        material: null,
        traffic: null,
        present: {
          id: 6,
          miceDemandPresentId: 41,
          unitPrice: 300,
          description: null,
          sourceId: null,
          presentDetails: [],
        },
        others: [],
        serviceFee: null,
        differences: [],
      },
      {
        id: 13,
        mainCode: 'RC20250528100855564028',
        miceId: 232,
        miceDemandId: 216,
        miceDemandPushId: 132,
        miceBiddingPushId: null,
        merchantId: 4787,
        pdmMerchantPoolId: 27,
        pdmMerchantPoolName: '保险服务商',
        pdmMerchantPoolItems: 64,
        pdmMerchantPoolGroupIds: '1,2',
        pdmMerchantPoolGroupNames: '本地测试资源组',
        merchantCode: 'V9127486',
        merchantType: 4,
        merchantName: '山西长江源酒店管理有限公司',
        contactMerchantName: null,
        contactMerchantPhone: null,
        merchantScore: 0,
        merchantContract: 'aaaaa',
        merchantPlatformUsageFeeRate: null,
        sourceId: 12,
        blockchainId: null,
        msMarketPriceInquiryId: null,
        schemeTotalPrice: 200,
        agreementTotalPrice: null,
        agreementSchemeTotalPrice: null,
        marketTotalPrice: null,
        marketSchemeTotalPrice: null,
        marketVerifyState: null,
        remarks: null,
        isExclude: false,
        excludeRemarks: null,
        isSelected: true,
        unselectRemarks: null,
        isSpecified: null,
        specifiedRemarks: null,
        isTarget: true,
        submitDeadline: null,
        abandonType: null,
        abandonReason: null,
        isValid: null,
        schemeType: 0,
        schemeState: 1,
        submitterIp: '127.0.0.1',
        hotels: [],
        stays: [],
        places: [],
        caterings: [],
        vehicles: [],
        attendants: [],
        activities: [],
        insurances: [
          {
            id: 7,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 13,
            miceDemandInsuranceId: 50,
            demandDate: '2025-05-04',
            demandUnitPrice: 25,
            schemePersonNum: 200,
            productId: 1,
            productMerchantId: 1,
            insuranceName: '太平洋',
            insuranceContent: '意外险',
            schemeUnitPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        material: null,
        traffic: null,
        present: null,
        others: [],
        serviceFee: null,
        differences: [],
      },
    ],
  },
  {
    id: 35,
    mainCode: 'RC20250528100855564028',
    miceId: 232,
    miceDemandId: 216,
    miceDemandPushId: 132,
    miceSchemeIds: '6,9,13',
    schemeCombinationTotalPrice: 6446,
    isExclude: true,
    excludeRemarks: '不大行',
    isSelected: null,
    unselectRemarks: null,
    isSpecified: false,
    specifiedRemarks: null,
    isTarget: false,
    targetReason: null,
    schemeDetails: [
      {
        id: 6,
        mainCode: 'RC20250528100855564028',
        miceId: 232,
        miceDemandId: 216,
        miceDemandPushId: 132,
        miceBiddingPushId: null,
        merchantId: 5,
        pdmMerchantPoolId: 1,
        pdmMerchantPoolName: '直签酒店服务商',
        pdmMerchantPoolItems: 1,
        pdmMerchantPoolGroupIds: '1',
        pdmMerchantPoolGroupNames: '本地测试资源组',
        merchantCode: 'V82685',
        merchantType: 2,
        merchantName: '天津市文光集团有限公司',
        contactMerchantName: null,
        contactMerchantPhone: null,
        merchantScore: 0,
        merchantContract: 'aaaaa',
        merchantPlatformUsageFeeRate: null,
        sourceId: null,
        blockchainId: null,
        msMarketPriceInquiryId: null,
        schemeTotalPrice: 6245,
        agreementTotalPrice: null,
        agreementSchemeTotalPrice: null,
        marketTotalPrice: null,
        marketSchemeTotalPrice: null,
        marketVerifyState: null,
        remarks: null,
        isExclude: true,
        excludeRemarks: '不大行',
        isSelected: null,
        unselectRemarks: null,
        isSpecified: null,
        specifiedRemarks: null,
        isTarget: null,
        submitDeadline: null,
        abandonType: null,
        abandonReason: null,
        isValid: null,
        schemeType: 0,
        schemeState: 1,
        submitterIp: '127.0.0.1',
        hotels: [
          {
            id: 7,
            decorationYear: '2025',
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            msMarketPriceInquiryId: null,
            miceDemandHotelId: 555,
            miceDemandPushHotelId: 2871,
            hotelName: '七天酒店',
            cityId: null,
            cityName: null,
            districtId: null,
            districtName: null,
            wishId: null,
            wishAreaId: null,
            wishCode: null,
            level: 15,
          },
          {
            decorationYear: '2013',
            id: 8,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            msMarketPriceInquiryId: null,
            miceDemandHotelId: 304,
            miceDemandPushHotelId: 3288,
            hotelName: '四级酒店',
            cityId: null,
            cityName: null,
            districtId: null,
            districtName: null,
            wishId: null,
            wishAreaId: null,
            wishCode: null,
            level: 15,
          },
        ],
        stays: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceDemandHotelId: 304,
            miceDemandStayId: 305,
            miceSchemeId: 6,
            miceSchemeHotelId: null,
            demandDate: '2025-05-28',
            roomType: 1,
            breakfastType: 1,
            personNum: 2,
            schemeRoomNum: 1,
            discrepancyReason: '人数与房间数不一致原因',
            schemeUnitPrice: 1,
            agreementProductId: 1,
            agreementUnitPrice: 1,
            marketUnitPrice: null,
            retailUnitPrice: 2,
            msMarketPriceInquiryDetailsId: null,
            sourceId: null,
            description: '描述测试',
          },
        ],
        places: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceDemandHotelId: 126,
            miceDemandPlaceId: 75,
            miceSchemeId: 6,
            miceSchemeHotelId: null,
            demandDate: '2025-05-23',
            usageTime: 7,
            usagePurpose: 7,
            schemePersonNum: 10,
            area: 0,
            underLightFloor: 0,
            tableType: 1,
            hasLed: true,
            schemeLedNum: 10,
            schemeLedSource: null,
            guildhall: '嵩山厅',
            ledSpecs: '需要很好的',
            hasTea: true,
            teaEachTotalPrice: 20,
            teaDesc: '需要很好的',
            schemeUnitPlacePrice: 1,
            schemeUnitLedPrice: 1,
            schemeUnitTeaPrice: 1,
            msMarketPriceInquiryDetailsId: null,
            marketPriceUnitPrice: null,
            agreementProductId: 2,
            agreementUnitPrice: 500,
            retailUnitPrice: 800,
            description: '方案说明...',
            sourceId: null,
          },
        ],
        caterings: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            isInsideHotel: true,
            miceDemandHotelId: 304,
            miceDemandCateringId: 71,
            miceSchemeId: 6,
            miceSchemeHotelId: null,
            demandDate: '2025-05-23',
            cateringType: 1,
            cateringTime: 2,
            schemePersonNum: 10,
            demandUnitPrice: 1,
            isIncludeDrinks: true,
            schemeUnitPrice: 1,
            description: '用餐测试',
            sourceId: null,
          },
        ],
        vehicles: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandVehicleId: 60,
            demandDate: '2025-05-04',
            usageType: 1,
            usageTime: 1,
            seats: 2,
            schemeVehicleNum: 8,
            brand: 'qq',
            route: '机场,xx酒店',
            schemeUnitPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        attendants: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandAttendantId: 57,
            demandDate: '2025-05-04',
            type: 0,
            schemePersonNum: 2,
            duty: '摄影',
            schemeUnitPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        activities: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandActivityId: 53,
            demandDate: '2025-05-04',
            demandUnitPrice: 50000,
            schemePersonNum: 200,
            schemeUnitPrice: 30,
            description: '说明..',
            paths: ['http://www.baidu.com/nn'],
            sourceId: null,
          },
        ],
        insurances: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandInsuranceId: 50,
            demandDate: '2025-05-04',
            demandUnitPrice: 25,
            schemePersonNum: 200,
            productId: 1,
            productMerchantId: 1,
            insuranceName: '太平洋',
            insuranceContent: '意外险',
            schemeUnitPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        material: {
          id: 4,
          mainCode: 'RC20250528100855564028',
          miceId: 232,
          miceSchemeId: 6,
          miceDemandMaterialId: 45,
          demandTotalPrice: 800,
          schemeTotalPrice: 1,
          description: '说明..',
          sourceId: null,
          materialDetails: [
            {
              id: 4,
              mainCode: 'RC20250528100855564028',
              miceId: 232,
              miceSchemeId: 6,
              miceSchemeMaterialId: 4,
              miceDemandMaterialDetailsId: 45,
              type: 1,
              specs: '布展物料xxx',
              schemeMaterialNum: 2,
              demandUnitPrice: 1,
              schemeUnitPrice: 1,
              sourceId: null,
            },
          ],
        },
        traffic: {
          id: 4,
          mainCode: 'RC20250528100855564028',
          miceId: 232,
          miceSchemeId: 6,
          miceDemandTrafficId: 34,
          demandTotalPrice: 100,
          schemeTotalPrice: 1,
        },
        present: {
          id: 4,
          miceDemandPresentId: 41,
          unitPrice: 300,
          description: null,
          sourceId: null,
          presentDetails: [],
        },
        others: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandOtherId: 40,
            demandDate: '2025-05-04',
            demandTotalPrice: 100,
            itemName: null,
            num: 20,
            unit: '个',
            specs: '其他xx',
            schemeTotalPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        serviceFee: {
          id: 4,
          mainCode: 'RC20250528100855564028',
          miceId: 232,
          miceSchemeId: 6,
          serviceFeeLimitRate: 0,
          serviceFeeLimit: 1,
          schemeServiceFeeReal: 1,
          sourceId: null,
        },
        differences: [
          {
            id: 3,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            demandId: 216,
            schemeId: 6,
            demandStayIds: '126',
            schemeStayIds: '1988',
            differenceDate: '2025-05-28',
            demandTotalPerson: 11,
            schemeTotalPerson: null,
            demandRoomType: '1,3',
            schemeRoomType: '1',
            demandOneRooms: 1,
            schemeOneRooms: 1,
            demandTwoRooms: 0,
            schemeTwoRooms: 0,
            demandSuiteRooms: 1,
            schemeSuiteRooms: 0,
            reason: '差异原因',
          },
        ],
      },
      {
        id: 9,
        mainCode: 'RC20250528100855564028',
        miceId: 232,
        miceDemandId: 216,
        miceDemandPushId: 132,
        miceBiddingPushId: null,
        merchantId: 4780,
        pdmMerchantPoolId: 3,
        pdmMerchantPoolName: '礼品服务商',
        pdmMerchantPoolItems: 512,
        pdmMerchantPoolGroupIds: '1,2',
        pdmMerchantPoolGroupNames: '本地测试资源组',
        merchantCode: 'V9033141',
        merchantType: 3,
        merchantName: '青岛健力源餐饮管理有限公司',
        contactMerchantName: null,
        contactMerchantPhone: null,
        merchantScore: 64,
        merchantContract: 'JT2025031900001',
        merchantPlatformUsageFeeRate: null,
        sourceId: null,
        blockchainId: null,
        msMarketPriceInquiryId: null,
        schemeTotalPrice: 1,
        agreementTotalPrice: null,
        agreementSchemeTotalPrice: null,
        marketTotalPrice: null,
        marketSchemeTotalPrice: null,
        marketVerifyState: null,
        remarks: null,
        isExclude: false,
        excludeRemarks: null,
        isSelected: true,
        unselectRemarks: null,
        isSpecified: null,
        specifiedRemarks: null,
        isTarget: true,
        submitDeadline: null,
        abandonType: null,
        abandonReason: null,
        isValid: null,
        schemeType: 0,
        schemeState: 1,
        submitterIp: '127.0.0.1',
        hotels: [],
        stays: [],
        places: [],
        caterings: [],
        vehicles: [],
        attendants: [],
        activities: [],
        insurances: [],
        material: null,
        traffic: null,
        present: {
          id: 6,
          miceDemandPresentId: 41,
          unitPrice: 300,
          description: null,
          sourceId: null,
          presentDetails: [],
        },
        others: [],
        serviceFee: null,
        differences: [],
      },
      {
        id: 13,
        mainCode: 'RC20250528100855564028',
        miceId: 232,
        miceDemandId: 216,
        miceDemandPushId: 132,
        miceBiddingPushId: null,
        merchantId: 4787,
        pdmMerchantPoolId: 27,
        pdmMerchantPoolName: '保险服务商',
        pdmMerchantPoolItems: 64,
        pdmMerchantPoolGroupIds: '1,2',
        pdmMerchantPoolGroupNames: '本地测试资源组',
        merchantCode: 'V9127486',
        merchantType: 4,
        merchantName: '山西长江源酒店管理有限公司',
        contactMerchantName: null,
        contactMerchantPhone: null,
        merchantScore: 0,
        merchantContract: 'aaaaa',
        merchantPlatformUsageFeeRate: null,
        sourceId: 12,
        blockchainId: null,
        msMarketPriceInquiryId: null,
        schemeTotalPrice: 200,
        agreementTotalPrice: null,
        agreementSchemeTotalPrice: null,
        marketTotalPrice: null,
        marketSchemeTotalPrice: null,
        marketVerifyState: null,
        remarks: null,
        isExclude: false,
        excludeRemarks: null,
        isSelected: true,
        unselectRemarks: null,
        isSpecified: null,
        specifiedRemarks: null,
        isTarget: true,
        submitDeadline: null,
        abandonType: null,
        abandonReason: null,
        isValid: null,
        schemeType: 0,
        schemeState: 1,
        submitterIp: '127.0.0.1',
        hotels: [],
        stays: [],
        places: [],
        caterings: [],
        vehicles: [],
        attendants: [],
        activities: [],
        insurances: [
          {
            id: 7,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 13,
            miceDemandInsuranceId: 50,
            demandDate: '2025-05-04',
            demandUnitPrice: 25,
            schemePersonNum: 200,
            productId: 1,
            productMerchantId: 1,
            insuranceName: '太平洋',
            insuranceContent: '意外险',
            schemeUnitPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        material: null,
        traffic: null,
        present: null,
        others: [],
        serviceFee: null,
        differences: [],
      },
    ],
  },
  {
    id: 35,
    mainCode: 'RC20250528100855564028',
    miceId: 232,
    miceDemandId: 216,
    miceDemandPushId: 132,
    miceSchemeIds: '6,9,13',
    schemeCombinationTotalPrice: 6446,
    isExclude: true,
    excludeRemarks: '不大行',
    isSelected: null,
    unselectRemarks: null,
    isSpecified: false,
    specifiedRemarks: null,
    isTarget: false,
    targetReason: null,
    schemeDetails: [
      {
        id: 6,
        mainCode: 'RC20250528100855564028',
        miceId: 232,
        miceDemandId: 216,
        miceDemandPushId: 132,
        miceBiddingPushId: null,
        merchantId: 5,
        pdmMerchantPoolId: 1,
        pdmMerchantPoolName: '直签酒店服务商',
        pdmMerchantPoolItems: 1,
        pdmMerchantPoolGroupIds: '1',
        pdmMerchantPoolGroupNames: '本地测试资源组',
        merchantCode: 'V82685',
        merchantType: 2,
        merchantName: '天津市文光集团有限公司',
        contactMerchantName: null,
        contactMerchantPhone: null,
        merchantScore: 0,
        merchantContract: 'aaaaa',
        merchantPlatformUsageFeeRate: null,
        sourceId: null,
        blockchainId: null,
        msMarketPriceInquiryId: null,
        schemeTotalPrice: 6245,
        agreementTotalPrice: null,
        agreementSchemeTotalPrice: null,
        marketTotalPrice: null,
        marketSchemeTotalPrice: null,
        marketVerifyState: null,
        remarks: null,
        isExclude: true,
        excludeRemarks: '不大行',
        isSelected: null,
        unselectRemarks: null,
        isSpecified: null,
        specifiedRemarks: null,
        isTarget: null,
        submitDeadline: null,
        abandonType: null,
        abandonReason: null,
        isValid: null,
        schemeType: 0,
        schemeState: 1,
        submitterIp: '127.0.0.1',
        hotels: [
          {
            id: 7,
            decorationYear: '2015',
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            msMarketPriceInquiryId: null,
            miceDemandHotelId: 555,
            miceDemandPushHotelId: 2871,
            hotelName: '格林豪泰酒店',
            cityId: null,
            cityName: null,
            districtId: null,
            districtName: null,
            wishId: null,
            wishAreaId: null,
            wishCode: null,
            level: 15,
          },
          {
            decorationYear: '2003',
            id: 8,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            msMarketPriceInquiryId: null,
            miceDemandHotelId: 304,
            miceDemandPushHotelId: 3288,
            hotelName: '民宿',
            cityId: null,
            cityName: null,
            districtId: null,
            districtName: null,
            wishId: null,
            wishAreaId: null,
            wishCode: null,
            level: 15,
          },
        ],
        stays: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceDemandHotelId: 304,
            miceDemandStayId: 305,
            miceSchemeId: 6,
            miceSchemeHotelId: null,
            demandDate: '2025-05-28',
            roomType: 1,
            breakfastType: 1,
            personNum: 2,
            schemeRoomNum: 1,
            discrepancyReason: '人数与房间数不一致原因',
            schemeUnitPrice: 1,
            agreementProductId: 1,
            agreementUnitPrice: 1,
            marketUnitPrice: null,
            retailUnitPrice: 2,
            msMarketPriceInquiryDetailsId: null,
            sourceId: null,
            description: '描述测试',
          },
        ],
        places: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceDemandHotelId: 126,
            miceDemandPlaceId: 75,
            miceSchemeId: 6,
            miceSchemeHotelId: null,
            demandDate: '2025-05-23',
            usageTime: 7,
            usagePurpose: 7,
            schemePersonNum: 10,
            area: 0,
            underLightFloor: 0,
            tableType: 1,
            hasLed: true,
            schemeLedNum: 10,
            schemeLedSource: null,
            guildhall: '嵩山厅',
            ledSpecs: '需要很好的',
            hasTea: true,
            teaEachTotalPrice: 20,
            teaDesc: '需要很好的',
            schemeUnitPlacePrice: 1,
            schemeUnitLedPrice: 1,
            schemeUnitTeaPrice: 1,
            msMarketPriceInquiryDetailsId: null,
            marketPriceUnitPrice: null,
            agreementProductId: 2,
            agreementUnitPrice: 500,
            retailUnitPrice: 800,
            description: '方案说明...',
            sourceId: null,
          },
        ],
        caterings: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            isInsideHotel: true,
            miceDemandHotelId: 304,
            miceDemandCateringId: 71,
            miceSchemeId: 6,
            miceSchemeHotelId: null,
            demandDate: '2025-05-23',
            cateringType: 1,
            cateringTime: 2,
            schemePersonNum: 10,
            demandUnitPrice: 1,
            isIncludeDrinks: true,
            schemeUnitPrice: 1,
            description: '用餐测试',
            sourceId: null,
          },
        ],
        vehicles: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandVehicleId: 60,
            demandDate: '2025-05-04',
            usageType: 1,
            usageTime: 1,
            seats: 2,
            schemeVehicleNum: 8,
            brand: 'qq',
            route: '机场,xx酒店',
            schemeUnitPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        attendants: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandAttendantId: 57,
            demandDate: '2025-05-04',
            type: 0,
            schemePersonNum: 2,
            duty: '摄影',
            schemeUnitPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        activities: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandActivityId: 53,
            demandDate: '2025-05-04',
            demandUnitPrice: 50000,
            schemePersonNum: 200,
            schemeUnitPrice: 30,
            description: '说明..',
            paths: ['http://www.baidu.com/nn'],
            sourceId: null,
          },
        ],
        insurances: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandInsuranceId: 50,
            demandDate: '2025-05-04',
            demandUnitPrice: 25,
            schemePersonNum: 200,
            productId: 1,
            productMerchantId: 1,
            insuranceName: '太平洋',
            insuranceContent: '意外险',
            schemeUnitPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        material: {
          id: 4,
          mainCode: 'RC20250528100855564028',
          miceId: 232,
          miceSchemeId: 6,
          miceDemandMaterialId: 45,
          demandTotalPrice: 800,
          schemeTotalPrice: 1,
          description: '说明..',
          sourceId: null,
          materialDetails: [
            {
              id: 4,
              mainCode: 'RC20250528100855564028',
              miceId: 232,
              miceSchemeId: 6,
              miceSchemeMaterialId: 4,
              miceDemandMaterialDetailsId: 45,
              type: 1,
              specs: '布展物料xxx',
              schemeMaterialNum: 2,
              demandUnitPrice: 1,
              schemeUnitPrice: 1,
              sourceId: null,
            },
          ],
        },
        traffic: {
          id: 4,
          mainCode: 'RC20250528100855564028',
          miceId: 232,
          miceSchemeId: 6,
          miceDemandTrafficId: 34,
          demandTotalPrice: 100,
          schemeTotalPrice: 1,
        },
        present: {
          id: 4,
          miceDemandPresentId: 41,
          unitPrice: 300,
          description: null,
          sourceId: null,
          presentDetails: [],
        },
        others: [
          {
            id: 4,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 6,
            miceDemandOtherId: 40,
            demandDate: '2025-05-04',
            demandTotalPrice: 100,
            itemName: null,
            num: 20,
            unit: '个',
            specs: '其他xx',
            schemeTotalPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        serviceFee: {
          id: 4,
          mainCode: 'RC20250528100855564028',
          miceId: 232,
          miceSchemeId: 6,
          serviceFeeLimitRate: 0,
          serviceFeeLimit: 1,
          schemeServiceFeeReal: 1,
          sourceId: null,
        },
        differences: [
          {
            id: 3,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            demandId: 216,
            schemeId: 6,
            demandStayIds: '126',
            schemeStayIds: '1988',
            differenceDate: '2025-05-28',
            demandTotalPerson: 11,
            schemeTotalPerson: null,
            demandRoomType: '1,3',
            schemeRoomType: '1',
            demandOneRooms: 1,
            schemeOneRooms: 1,
            demandTwoRooms: 0,
            schemeTwoRooms: 0,
            demandSuiteRooms: 1,
            schemeSuiteRooms: 0,
            reason: '差异原因',
          },
        ],
      },
      {
        id: 9,
        mainCode: 'RC20250528100855564028',
        miceId: 232,
        miceDemandId: 216,
        miceDemandPushId: 132,
        miceBiddingPushId: null,
        merchantId: 4780,
        pdmMerchantPoolId: 3,
        pdmMerchantPoolName: '礼品服务商',
        pdmMerchantPoolItems: 512,
        pdmMerchantPoolGroupIds: '1,2',
        pdmMerchantPoolGroupNames: '本地测试资源组',
        merchantCode: 'V9033141',
        merchantType: 3,
        merchantName: '青岛健力源餐饮管理有限公司',
        contactMerchantName: null,
        contactMerchantPhone: null,
        merchantScore: 64,
        merchantContract: 'JT2025031900001',
        merchantPlatformUsageFeeRate: null,
        sourceId: null,
        blockchainId: null,
        msMarketPriceInquiryId: null,
        schemeTotalPrice: 1,
        agreementTotalPrice: null,
        agreementSchemeTotalPrice: null,
        marketTotalPrice: null,
        marketSchemeTotalPrice: null,
        marketVerifyState: null,
        remarks: null,
        isExclude: false,
        excludeRemarks: null,
        isSelected: true,
        unselectRemarks: null,
        isSpecified: null,
        specifiedRemarks: null,
        isTarget: true,
        submitDeadline: null,
        abandonType: null,
        abandonReason: null,
        isValid: null,
        schemeType: 0,
        schemeState: 1,
        submitterIp: '127.0.0.1',
        hotels: [],
        stays: [],
        places: [],
        caterings: [],
        vehicles: [],
        attendants: [],
        activities: [],
        insurances: [],
        material: null,
        traffic: null,
        present: {
          id: 6,
          miceDemandPresentId: 41,
          unitPrice: 300,
          description: null,
          sourceId: null,
          presentDetails: [],
        },
        others: [],
        serviceFee: null,
        differences: [],
      },
      {
        id: 13,
        mainCode: 'RC20250528100855564028',
        miceId: 232,
        miceDemandId: 216,
        miceDemandPushId: 132,
        miceBiddingPushId: null,
        merchantId: 4787,
        pdmMerchantPoolId: 27,
        pdmMerchantPoolName: '保险服务商',
        pdmMerchantPoolItems: 64,
        pdmMerchantPoolGroupIds: '1,2',
        pdmMerchantPoolGroupNames: '本地测试资源组',
        merchantCode: 'V9127486',
        merchantType: 4,
        merchantName: '山西长江源酒店管理有限公司',
        contactMerchantName: null,
        contactMerchantPhone: null,
        merchantScore: 0,
        merchantContract: 'aaaaa',
        merchantPlatformUsageFeeRate: null,
        sourceId: 12,
        blockchainId: null,
        msMarketPriceInquiryId: null,
        schemeTotalPrice: 200,
        agreementTotalPrice: null,
        agreementSchemeTotalPrice: null,
        marketTotalPrice: null,
        marketSchemeTotalPrice: null,
        marketVerifyState: null,
        remarks: null,
        isExclude: false,
        excludeRemarks: null,
        isSelected: true,
        unselectRemarks: null,
        isSpecified: null,
        specifiedRemarks: null,
        isTarget: true,
        submitDeadline: null,
        abandonType: null,
        abandonReason: null,
        isValid: null,
        schemeType: 0,
        schemeState: 1,
        submitterIp: '127.0.0.1',
        hotels: [],
        stays: [],
        places: [],
        caterings: [],
        vehicles: [],
        attendants: [],
        activities: [],
        insurances: [
          {
            id: 7,
            mainCode: 'RC20250528100855564028',
            miceId: 232,
            miceSchemeId: 13,
            miceDemandInsuranceId: 50,
            demandDate: '2025-05-04',
            demandUnitPrice: 25,
            schemePersonNum: 200,
            productId: 1,
            productMerchantId: 1,
            insuranceName: '太平洋',
            insuranceContent: '意外险',
            schemeUnitPrice: 1,
            description: '说明..',
            sourceId: null,
          },
        ],
        material: null,
        traffic: null,
        present: null,
        others: [],
        serviceFee: null,
        differences: [],
      },
    ],
  },
];
const tempDemandRes = {
  id: 216,
  mainCode: 'RC20250528100855564028',
  miceId: 232,
  miceName: '博览会',
  miceType: 5,
  cityIds: '59',
  cityNames: '青岛',
  startDate: '2025-05-28',
  endDate: '2025-05-29',
  isUrgent: false,
  contactUserCode: '24060822',
  contactUserName: '王忠明',
  contactUserPhone: '15615738641',
  contactUserEmail: '<EMAIL>',
  personTotal: 11,
  districtType: 0,
  demandItem: 255,
  calcTotalPrice: 150063,
  demandTotalPrice: 150063,
  remarks: '',
  demandType: 4,
  demandState: 1,
  demandRejectReason: null,
  sourceId: 215,
  stateGmtModified: '2025-06-08 00:23:53',
  pdMainId: 1,
  pdVerId: 1,
  pdVerName: '海尔标准流程',
  mainMiceId: null,
  miceServiceOrderId: null,
  miceServiceOrderCode: null,
  operatorCode: '24060822',
  operatorName: '王忠明',
  operatorPhone: '15615738641',
  operatorEmail: '<EMAIL>',
  intentionConsultantUserCode: 'A1005515',
  consultantUserCode: '22069502',
  consultantUserName: '王翠林',
  state: 900,
  processNode: 'SCHEME_APPROVAL',
  paymentState: 10,
  hotels: [
    {
      id: 304,
      sourceId: null,
      provinceId: '23',
      provinceName: '山东',
      cityId: '59',
      cityName: '青岛',
      districtIds: '428',
      districtNames: '崂山区',
      wishId: null,
      wishAreaId: null,
      wishCode: null,
      level: 3,
      latitude: '36.102342',
      longitude: '120.472939',
      distanceRange: null,
      centerMarker: '崂山区城市发展展示中心',
    },
    {
      id: 308,
      sourceId: null,
      provinceId: '23',
      provinceName: '山东',
      cityId: '59',
      cityName: '青岛',
      districtIds: '428',
      districtNames: '崂山区',
      wishId: null,
      wishAreaId: null,
      wishCode: null,
      level: 2,
      latitude: '36.102977',
      longitude: '120.470394',
      distanceRange: null,
      centerMarker: '丽达小街(崂山区世纪广场店)',
    },
  ],
  stays: [
    {
      id: 307,
      sourceId: null,
      miceDemandHotelId: 304,
      demandDate: '2025-05-28',
      roomType: 1,
      breakfastType: 1,
      personNum: 10,
      roomNum: 10,
      discrepancyReason: '',
      calcUnitPrice: 999,
    },
    {
      id: 308,
      sourceId: null,
      miceDemandHotelId: 304,
      demandDate: '2025-05-29',
      roomType: 3,
      breakfastType: 1,
      personNum: 10,
      roomNum: 10,
      discrepancyReason: '',
      calcUnitPrice: 17,
    },
  ],
  places: [
    {
      id: 187,
      sourceId: null,
      miceDemandHotelId: 305,
      demandDate: '2025-05-23',
      usageTime: 7,
      usagePurpose: 7,
      personNum: 10,
      area: null,
      underLightFloor: null,
      tableType: 4,
      hasLed: true,
      ledNum: 2,
      ledSpecs: '2套',
      hasTea: true,
      teaEachTotalPrice: 100,
      teaDesc: '下午茶',
      calcUnitPlacePrice: 97314,
      calcUnitLedPrice: 3590,
      calcUnitTeaPrice: 100,
    },
    {
      id: 188,
      sourceId: null,
      miceDemandHotelId: 305,
      demandDate: '2025-05-23',
      usageTime: 3,
      usagePurpose: 5,
      personNum: 10,
      area: null,
      underLightFloor: null,
      tableType: 5,
      hasLed: false,
      ledNum: null,
      ledSpecs: null,
      hasTea: null,
      teaEachTotalPrice: null,
      teaDesc: null,
      calcUnitPlacePrice: 28710,
      calcUnitLedPrice: 427,
      calcUnitTeaPrice: null,
    },
  ],
  caterings: [
    {
      miceDemandHotelId: 304,
      id: 182,
      sourceId: null,
      isInsideHotel: true,
      demandDate: '2025-05-23',
      cateringType: 1,
      cateringTime: 0,
      personNum: 10,
      demandUnitPrice: 200,
      isIncludeDrinks: true,
      calcUnitPrice: 200,
    },
    {
      miceDemandHotelId: null,
      id: 183,
      sourceId: null,
      isInsideHotel: false,
      demandDate: '2025-05-24',
      cateringType: 3,
      cateringTime: 0,
      personNum: 10,
      demandUnitPrice: 100,
      isIncludeDrinks: false,
      calcUnitPrice: 100,
    },
  ],
  vehicles: [
    {
      id: 147,
      sourceId: null,
      demandDate: '2025-05-04',
      usageType: 1,
      usageTime: 1,
      seats: 7,
      vehicleNum: 2,
      brand: '别克GL8',
      route: '崂山创牌,李沧万达',
      calcUnitPrice: 456,
    },
  ],
  attendants: [
    {
      id: 116,
      sourceId: null,
      demandDate: '2025-05-04',
      type: 3,
      personNum: 1,
      duty: '主持',
      calcUnitPrice: 676,
    },
  ],
  activities: [
    {
      id: 114,
      sourceId: null,
      demandDate: '2025-05-04',
      demandUnitPrice: 100,
      personNum: 10,
      description: '户外拓展活动',
      calcUnitPrice: 100,
      paths: [
        '{"name":"测试附件.xlsx","url":"https://businessmanagement-test.haier.net/hbweb/file/download/obs-swszh1/1748269537-测试附件.xlsx"}',
      ],
    },
  ],
  insurances: [
    {
      id: 111,
      sourceId: null,
      demandDate: '2025-05-04',
      demandUnitPrice: 11,
      personNum: 10,
      productId: 1,
      productMerchantId: 111,
      insuranceName: '平安保险11',
      insuranceContent: '平安保险内容1',
      calcUnitPrice: 11,
    },
  ],
  material: {
    id: 95,
    sourceId: null,
    demandTotalPrice: 1,
    calcTotalPrice: 1,
    materialDetails: [
      {
        id: 143,
        miceDemandMaterialId: 95,
        type: 0,
        specs: '12',
        num: 1,
        unitPrice: 1,
      },
    ],
  },
  traffic: null,
  presents: [],
  others: [],
};
export { tempSchemeRes, tempDemandRes };
