.bg {
  width: 100%;
  background: #02122c;
  // background-image: url(@/assets/image/bigscreen/bg-main.jpg);
  // background-size: 100% auto;
  // background-repeat:repeat-y;
  height: 100vh;
  overflow: hidden;
}
.main {
  width: 100%;
  height: 100vh;
  background-image: url(@/assets/image/bigscreen/bg.jpg);
  background-repeat: no-repeat;
  background-size: 100vw 100vh;
  background-origin: center;
  color: #fff;
  padding-left: 35px;
}
.picker {
  padding-right: 26px;
}
.title {
  margin-bottom: 15px;
  text-align: center;
  height: 7vh;
  line-height: 7vh;
  font-size: 3.6vh;
  font-weight: bold;
  text-shadow: 0px 2px 5px #000000;
}
// @media screen and (max-width:1500px){
//     .title{
//         height: 6vh;
//         line-height: 6vh;
//         font-size: 32px;
//     }
// }
.content {
  margin-top: 5vh;
  padding-bottom: 1vh;
  overflow-y: scroll;
  height: calc(93vh - 130px);
  padding-right: 35px;
}
.col-title {
  //   height: 9.6vh;
  background-image: url(@/assets/image/bigscreen/bg-title-m.png);
  //   background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: 0px 10%;
  padding: 1.5vh 2.5vh;
  color: #e2f5fe;
  font-size: 2vh;
  font-weight: 600;
  display: flex;
  justify-content: space-between;

  &-extra {
    position: relative;
    height: 3vh;
    margin-top: -0.3vh;
    span {
      display: inline-block;
      width: 52px;
      height: 3vh;
      background: rgba(56, 106, 184, 0.24);
      font-size: 12px;
      text-align: center;
      line-height: 3vh;
      cursor: pointer;
    }
    img.active {
      position: absolute;
      width: 52px;
      bottom: -3px;
      left: 0;
      transform: translateX(0);
      transition: all 0.3s;
    }
  }
  &-extra-width {
    position: relative;
    height: 3vh;
    margin-top: -0.3vh;
    span {
      display: inline-block;
      width: 35px;
      height: 3vh;
      background: rgba(56, 106, 184, 0.24);
      font-size: 12px;
      text-align: center;
      line-height: 3vh;
      cursor: pointer;
    }
    img.active {
      position: absolute;
      width: 35px;
      bottom: -3px;
      left: 0;
      transform: translateX(0);
      transition: all 0.3s;
    }
  }
}
.col-chart {
  margin: 1vh 0px;
  overflow-y: scroll;
  overflow-x: hidden;
  /* 滚动条轨道样式 */
}
::-webkit-scrollbar {
  width: 0px; /* 设置滚动条宽度 */
}

.col-title-l {
  background-image: url(@/assets/image/bigscreen/bg-title-l.png);
}

@media screen and (min-width: 1700px) {
  .col-title {
    // height: 9.6vh;

    background-image: url(@/assets/image/bigscreen/bg-title-m.png);
    // background-size: 100% 157%;
    background-repeat: no-repeat;
    padding: 1vh 2.5vh;
    color: #e2f5fe;
    font-size: 2.4vh;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
  }
  .col-title-l {
    padding-left: 30px;
  }
}
