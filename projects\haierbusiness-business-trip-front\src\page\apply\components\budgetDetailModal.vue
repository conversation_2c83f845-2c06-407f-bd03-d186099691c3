
<script lang="ts" setup>
import { Dayjs } from 'dayjs';
import { reactive, ref, toRaw } from 'vue';
import type { UnwrapRef } from 'vue';
import type { Rule } from 'ant-design-vue/es/form';
import {
  Anchor as hAnchor,
  <PERSON>ton as hButton,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Form as hForm,
  Modal as hModal,
  Row as hRow,
  Col as hCol,
  FormItem as hFormItem,
  Cascader as hCascader,
  Input as hInput,
} from 'ant-design-vue';
import { IBudgetType, IBudgetForm } from '@haierbusiness-front/common-libs';
import {
  QuestionCircleOutlined,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  InfoCircleOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';

interface IbudgetDetail {
  amountSum: string;
  travelUserName: string;
  travelUserNo: string;
  travelUserSyId: string;
}
interface Props {
  tableData?: IbudgetDetail; // 人员
}
const { tableData } = defineProps<Props>();
const columns = [
  {
    title: '人员',
    dataIndex: 'travelUserName',
    key: 'travelUserName',
    align: 'center',
    width: '160px',
  },
  {
    title: '费用预算',
    dataIndex: 'amountSum',
    key: 'amountSum',
    align: 'center',
  },
];

const visible = ref<boolean>(false);

const show = () => {
  visible.value = true;
};
defineExpose({
  show,
});
</script>

<template>
  <h-modal v-model:open="visible" title="预算明细" width="600px" :footer="null">
    <a-table :pagination="false" bordered :columns="columns" :data-source="tableData" :footer="false">
      <template #headerCell="{ column }">
        <template v-if="column.key === 'amountSum'">
          <span>
            {{ column.title }}
            <a-tooltip>
              <template #title>出差申请单费用预算可供机票、火车、酒店等合并使用</template>
              <QuestionCircleOutlined />
            </a-tooltip>
          </span>
        </template>
      </template>
    </a-table>
  </h-modal>
</template>

<style scoped>
.mb-30 {
  margin-bottom: 30px;
}
.ant-row {
  margin-bottom: 20px;
}
.mt-50 {
  margin-top: 50px;
}
</style>