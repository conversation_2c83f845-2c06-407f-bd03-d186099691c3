<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Cascader as hCascader

} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons-vue';
import { banquetReservationApi, banquetApi,download,cityApi } from '@haierbusiness-front/apis';
import {
  IApplyFilter,
  IApply,
  reservationStatusEnum,
  BanquetApproveStatusEnum,
  BanquetPayTypeNum,
  statementStatusNum
} from '@haierbusiness-front/common-libs';
import { getEnumOptions } from '@haierbusiness-front/utils';

import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
// import {useRoute} from 'router'
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import router from '../../router'
const route = ref(getCurrentRoute());

const currentRouter = ref()

onMounted(async () => {
    currentRouter.value = await router
    console.log(route)
    if(route.value?.query?.orderBookingCode){
      searchParam.value.orderBookingCode = route.value?.query?.orderBookingCode
    }else if(route.value?.query?.orderCode){
      searchParam.value.orderCode = route.value?.query?.orderCode
    }
    getCityList()
    handleTableChange({ current: 1, pageSize: 10 })

})

// 订单状态
const orderState = computed(() => {
  return getEnumOptions(reservationStatusEnum, true);
});

const approveState = computed(() => {
  return getEnumOptions(BanquetApproveStatusEnum, true);
});


// 支付类型
const payTypeOptions = computed(() => {
  return getEnumOptions(BanquetPayTypeNum, true);
});

// 汇总状态
const statementStatusOptions = computed(() => {
  return getEnumOptions(statementStatusNum, true);
});

const columns: ColumnType[] = [
  {
    title: '预订单号',
    dataIndex: 'orderBookingCode',
    width: '240px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '美团预订单号',
    dataIndex: 'mtBookingCode',
    width: '240px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '支付单号',
    dataIndex: 'payCode',
    width: '240px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '申请单号',
    dataIndex: 'orderCode',
    width: '240px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '申请类型',
    dataIndex: 'sceneType',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '签单人工号',
    dataIndex: 'signerCode',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '签单人姓名',
    dataIndex: 'signerName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '签到时间',
    dataIndex: 'mealTime',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '用餐城市-省',
    dataIndex: 'mealLocationProvince',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '用餐城市-市',
    dataIndex: 'mealLocationCity',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '餐厅名称',
    dataIndex: 'restaurantName',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '实际支付金额',
    dataIndex: 'actualPaymentAmount',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '退款金额',
    dataIndex: 'totalRefundAmount',
    width: '120px',
    align: 'center',
    ellipsis: true,
  }, 
  {
    title: '支付方式',
    dataIndex: 'secondBusinessName',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '支付类型',
    dataIndex: 'payType',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  // {
  //   title: '服务费率/%',
  //   dataIndex: 'mtServiceRate',
  //   width: '120px',
  //   align: 'center',
  //   ellipsis: true,
  // },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '汇总状态',
    dataIndex: 'statementStatus',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<IApplyFilter>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(banquetReservationApi.list);

const {
  data:exportListData,
  run:exportListApiRun,
  loading:exportListLoading,
} = useRequest(banquetReservationApi.exportList);

const reset = () => {
  cityCodeList.value = []

  searchParam.value = {}
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const goToDetail = (id: number) => {
  currentRouter.value.push({
    path: '/reservation/detail',
    query: {
      id: id
    }
  })
}
// 获取美团城市列表
const cityDict = ref<Array<any>>([])
const getCityList = () => {
  // banquetApi.getCity().then(res => {
  //   cityDict.value = res
  // })

  const params = {
    id: 1,
    providerCode: 'MT',
    subdistrict: 2
  }
  cityApi.getCityTree(params).then(res => {
    cityDict.value = res.children
  })
}

// 根据id 获取城市 code
const getCityCodeById = (id?:string | number) => {
  console.log(id)
  if (!id) {
    return ''
  }
  let code = ''
  cityDict.value?.forEach(province => {
    province?.children?.forEach(city => {
      // 香港 澳门单独处理
      if (id == city.id ||id==16||id==7) {
        if (city?.providerMapList && city?.providerMapList.length > 0) {
          code = city?.providerMapList[0]?.districtId
        }
      }
      
    });
  });
  return code
}



const statusToText = (orderStatus?:string|number, payStatus?:string|number) => {
  if (orderStatus == '0' && payStatus == '20') {
    return '已支付未核销'
  }else if (orderStatus == '0' && payStatus == '31') {
    return '未核销'
  }else if (orderStatus == '0' && payStatus == '32') {
    return '全额退款'
  }else if (orderStatus == '10' && payStatus == '20') {
    return '已核销'
  }else if (orderStatus == '10' && payStatus == '31') {
    return '已核销'
  }else if (orderStatus == '10' && payStatus == '32') {
    return '全额退款'
  }else if (orderStatus == '20' && payStatus == '20') {
    return '部分核销'
  }else if (orderStatus == '20' && payStatus == '31') {
    return '部分核销'
  }else if (orderStatus == '20' && payStatus == '32') {
    return '全额退款'
  }else if (!orderStatus && payStatus == '32') {
    return '全额退款'
  }else if (!orderStatus && payStatus == '20') {
    return '已支付'
  }else if (!orderStatus && payStatus == '31') {
    return '部分退款'
  }else {
    if (!orderStatus) {
      return '已支付'
    }
    return ''
  }
}


const cityCodeList = ref([])
watch(() => cityCodeList.value, (n: any, o: any) => {
  if (n && n.length > 0) {
    searchParam.value.mealLocationCityCode = getCityCodeById(n[1] || n[0])
  } else {
    searchParam.value.mealLocationCityCode = undefined
  }
});

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="orderBookingCode">预订单号:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="预订单号" v-model:value="searchParam.orderBookingCode"  style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="mtBookingCode">美团预订单号:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="美团预订单号" v-model:value="searchParam.mtBookingCode"  style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="orderCode">申请单号:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="申请单号" v-model:value="searchParam.orderCode"  style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="sceneType">申请类型:</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value="searchParam.sceneType"
              allow-clear
              style="width: 100%"
              placeholder="申请类型"
            >
              <h-select-option :value="1">宴请</h-select-option>
              <h-select-option :value="2">外卖</h-select-option>
            </h-select>
          </h-col>
        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="signerName">签单人工号/姓名:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="签单人工号/姓名" v-model:value="searchParam.signerName"  style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="mealTimes">签到时间:</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker :placeholder="['开始时间','结束时间']" v-model:value="searchParam.mealTimes" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>


          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="cityCode">用餐城市:</label>
          </h-col>
          <h-col :span="4">
            <h-cascader v-model:value="cityCodeList" show-search  :fieldNames="{label: 'name', value: 'id', children:'children'}" :options="cityDict" placeholder="城市" style="width: 100%" allow-clear />
          </h-col>
         

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="restaurantName">餐厅名称:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="餐厅名称" v-model:value="searchParam.restaurantName"  style="width: 100%" allow-clear />
          </h-col>


        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="sceneType">订单状态:</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value="searchParam.orderStatus"
              allow-clear
              style="width: 100%"
              placeholder="订单状态"
            >
              <h-select-option  v-for="(item, index) in orderState" :key="index" :value="item.value">{{
                item.label
              }}</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="sceneType">支付类型:</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value="searchParam.payType"
              allow-clear
              style="width: 100%"
              placeholder="支付类型"
            >
              <h-select-option  v-for="(item, index) in payTypeOptions" :key="index" :value="item.value">{{
                item.label
              }}</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="sceneType">汇总状态:</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value="searchParam.statementStatus"
              allow-clear
              style="width: 100%"
              placeholder="汇总状态"
            >
              <h-select-option  v-for="(item, index) in statementStatusOptions" :key="index" :value="item.value">{{
                item.label
              }}</h-select-option>
            </h-select>
          </h-col>
        </h-row>


        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button  style="margin-right: 10px" type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>

            <!-- <h-button type="primary" :loading="exportListLoading" @click="exportListApiRun(searchParam);">
              导出
            </h-button> -->
          </h-col>
        </h-row>


        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">

            <template v-if="column.dataIndex === 'sceneType'">
              {{ record.sceneType == 1 ? '宴请' :record.sceneType == 2?'外卖':'' }}
            </template>
            
            <template v-if="column.dataIndex === 'orderStatus'">
              {{statusToText(record.orderStatus,record.payStatus )}}
            </template>
            <template v-if="column.dataIndex === 'payType'">
              {{BanquetPayTypeNum[record.payType] }}
            </template>
            <template v-if="column.dataIndex === 'statementStatus'">
              {{statementStatusNum[record.statementStatus] }}
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="goToDetail(record.id)">查看详情</h-button>

            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    

  </div>
</template>

<style scoped lang="less">

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

</style>
