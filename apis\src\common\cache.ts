import { download, get, post, originalPost } from '../request';
import { CommonSaveObj, Result } from '@haierbusiness-front/common-libs';

// 需求提报
export const cacheApi = {
  // 临时缓存数据保存接口
  demandCommonCacheSave: (params: CommonSaveObj): Promise<Result> => {
    return post('common/api/common/temporary/cache/save', params);
  },

  // 临时缓存数据查询接口
  demandCommonCacheQuery: (params: CommonSaveObj): Promise<Result> => {
    return get('common/api/common/temporary/cache/queryByKey', params);
  },

  // 临时缓存数据根据key删除接口
  demandCommonCacheRemove: (params: CommonSaveObj): Promise<Result> => {
    return post('common/api/common/temporary/cache/removeByKey', params);
  },
};
