<script lang="ts" setup>
import {
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  InputNumber as hInputNumber,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  Checkbox as hCheckbox,
  Select as hSelect,
  Textarea as hTextarea,
  message
} from 'ant-design-vue';
import { computed, ref, watch, onMounted } from "vue";
import type { Ref } from "vue";
import type { IDomEditor } from "@wangeditor/editor";
import Editor from '@haierbusiness-front/components/editor/Editor.vue';
import {
  IAnnouncementNotice
} from '@haierbusiness-front/common-libs';
import {
  announcementNoticeStateOptions,
  announcementContentFormOptions,
  announcementEffectScopeOptions,
  AnnouncementContentForm
} from '@haierbusiness-front/common-libs';
import type { Rule } from 'ant-design-vue/lib/form';

interface Props {
  show: boolean;
  data: IAnnouncementNotice | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const from = ref();
const confirmLoading = ref(false);

// 使用IAnnouncementNotice接口定义默认数据
const defaultData: IAnnouncementNotice = {
  id: null,
  name: '',
  code: '',
  state: '正常', // 默认正常状态
  description: '',
  title: '',
  effectScope: undefined,
  contentForm: AnnouncementContentForm.TEXT, // 默认选择文本
  informContent: '',
  sort: 1,
  isWindow: false
};

// 选项数据，使用已有的枚举常量
const effectScopeOptions = announcementEffectScopeOptions;
const stateOptions = announcementNoticeStateOptions;
const contentFormOptions = announcementContentFormOptions;

// 表单验证规则，修正类型问题
const rules = {
  title: [
    { required: true, message: '标签名称必填' }
  ],
  effectScope: [
    { required: true, message: '通知作用范围必填' }
  ],
  contentForm: [
    { required: true, message: '内容形式必填' }
  ],
  informContent: [
    { required: true, message: '公告内容必填' }
  ],
  sort: [
    { required: true, message: '排序必填' },
    { type: 'number', min: 1, message: '排序必须大于0' } as Rule
  ],
  state: [
    { required: true, message: '状态必填' }
  ],
  isWindow: [
    { required: true, message: '是否弹框通知必填' }
  ]
};

const announcementNotice: Ref<IAnnouncementNotice> = ref(
  (props.data ? { ...props.data } : { ...defaultData })
);

// 监听props变化，当编辑数据变化时更新表单，添加immediate属性确保初始化时执行
watch(
  () => props.data,
  (newValue) => {
    console.log('props.data变化:', newValue);
    // 确保编辑时数据正确回显
    if (newValue) {
      console.log('编辑数据回显 - 原始数据:', newValue);
      console.log('编辑数据回显 - 公告内容:', newValue.informContent);

      // 深拷贝防止直接修改props
      announcementNotice.value = {
        ...newValue,
        // 确保关键字段正确回显
        id: newValue.id,
        title: newValue.title || '',
        contentForm: newValue.contentForm || AnnouncementContentForm.TEXT,
        effectScope: newValue.effectScope,
        state: newValue.state || '正常',
        informContent: newValue.informContent || '',
        sort: newValue.sort || 1,
        isWindow: typeof newValue.isWindow === 'boolean' ? newValue.isWindow : false
      };

      console.log('编辑数据回显 - 处理后:', announcementNotice.value);
    } else {
      announcementNotice.value = { ...defaultData };
      console.log('使用默认数据:', announcementNotice.value);
    }
  },
  { immediate: true, deep: true }
);

// 组件挂载时检查初始数据
onMounted(() => {
  if (props.data && Object.keys(props.data).length > 0) {
    announcementNotice.value = {
      ...props.data,
      informContent: props.data.informContent || ''
    };
  }
});

const emit = defineEmits(["cancel", "ok"]);

const visible = computed(() => props.show);

// 处理select值类型转换的函数
const handleNumberChange = (value: any, field: keyof IAnnouncementNotice) => {
  if (value !== undefined && value !== null) {
    // 对于state字段，它是字符串类型，不需要进行parseInt转换
    if (field === 'state') {
      announcementNotice.value[field] = value;
    } else {
      announcementNotice.value[field] = typeof value === 'string' ? parseInt(value) : value;
    }
  } else {
    announcementNotice.value[field] = undefined;
  }
};

// 富文本编辑器内容变化处理函数
const onEditorChange = (editor: IDomEditor) => {
  announcementNotice.value.informContent = editor.getHtml();
};

// 处理InputNumber值的转换函数
const handleInputNumberChange = (value: any) => {
  announcementNotice.value.sort = typeof value === 'number' ? value : undefined;
};

const handleOk = async () => {
  confirmLoading.value = true;
  try {
    await from.value.validate();

    // 确保获取到所有表单值
    const formValues = {
      id: announcementNotice.value.id,
      title: announcementNotice.value.title,
      effectScope: announcementNotice.value.effectScope,
      contentForm: announcementNotice.value.contentForm,
      informContent: announcementNotice.value.informContent,
      sort: announcementNotice.value.sort,
      state: announcementNotice.value.state,
      isWindow: announcementNotice.value.isWindow
    };

    console.log('表单数据:', formValues);

    // 将表单数据通过emit事件传递给父组件，由父组件统一处理API调用
    emit("ok", formValues, () => {
      confirmLoading.value = false;
    });
  } catch (error: any) {
    console.error('操作失败:', error);
    message.error('请输入必填信息');
    confirmLoading.value = false;
  }
};
</script>

<template>
  <h-modal :visible="visible" :title="announcementNotice.id ? '编辑公告通知' : '新增公告通知'" :width="800"
    @cancel="$emit('cancel')" :confirmLoading="confirmLoading" @ok="handleOk">
    <div class="edit-modal-content">
      <h-form ref="from" :model="announcementNotice" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }"
        :rules="rules">
        <h-form-item label="标题" name="title">
          <h-input :value="announcementNotice.title" @update:value="announcementNotice.title = $event"
            placeholder="请输入标题" style="max-width: 400px;" maxlength="200" show-count />
        </h-form-item>
        <h-form-item label="通知作用范围" name="effectScope">
          <h-select :value="announcementNotice.effectScope" @update:value="handleNumberChange($event, 'effectScope')"
            placeholder="请选择通知作用范围" :options="effectScopeOptions" allow-clear style="max-width: 400px;" />
        </h-form-item>
        <h-form-item label="内容形式" name="contentForm">
          <h-radio-group :value="announcementNotice.contentForm"
            @update:value="handleNumberChange($event, 'contentForm')">
            <h-radio v-for="option in contentFormOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </h-radio>
          </h-radio-group>
        </h-form-item>
        <h-form-item label="公告内容" name="informContent">
          <!-- 内容形式为文本(1)时显示富文本框 -->
          <template v-if="announcementNotice.contentForm === 1">
            <Editor height="350px" :modelValue="announcementNotice.informContent || ''" @change="onEditorChange"
              style="z-index: 20; max-width: 400px;" uploadUrl="/upload" />
          </template>
          <!-- 内容形式为链接(2)时显示普通文本框 -->
          <h-input v-else :value="announcementNotice.informContent"
            @update:value="announcementNotice.informContent = $event" placeholder="请输入链接地址" style="max-width: 400px;" />
        </h-form-item>
        <h-form-item label="排序" name="sort">
          <h-input-number :style="{ width: '400px' }" :value="announcementNotice.sort"
            @update:value="handleInputNumberChange($event)" placeholder="请输入排序" :min="1" />
        </h-form-item>
        <h-form-item label="状态" name="state">
          <h-select :value="announcementNotice.state" @update:value="handleNumberChange($event, 'state')"
            placeholder="请选择状态" :options="stateOptions" style="max-width: 400px;" />
        </h-form-item>
        <h-form-item label="是否弹窗" name="isWindow">
          <h-radio-group :value="announcementNotice.isWindow" @update:value="announcementNotice.isWindow = $event">
            <h-radio :value="true">是</h-radio>
            <h-radio :value="false">否</h-radio>
          </h-radio-group>
        </h-form-item>
      </h-form>
    </div>
  </h-modal>
</template>

<style lang="less" scoped>
.edit-modal {
  &-content {
    padding: 10px;
    .group-title {
      color: rgba(0, 0, 0, 0.88);
      font-weight: 600;
      line-height: 1.5;
      margin-bottom: 15px;
    }
  }
}
:deep(.ant-form-item-required::before) {
  display: none !important;
}
</style>