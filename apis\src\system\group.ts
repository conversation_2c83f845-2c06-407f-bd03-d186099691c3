import { IPageResponse, IGroupInfo, IGroupListRequest, IGroupSaveUpdateRequest, IGroupLinkRoleRequest, IGroupLinkUserRequest, IGroupDeleteRequest } from '@haierbusiness-front/common-libs'
import { get, post } from '../request'

export const groupApi = {


    /**
     * 获取组列表
     */
    list: (params: IGroupListRequest): Promise<IPageResponse<IGroupInfo>> => {
        return get<IPageResponse<IGroupInfo>>('system/api/group/list', params)
    },


    /**
     * 新增组
     */
    save: (params: IGroupSaveUpdateRequest): Promise<IGroupInfo> => {
        return post('system/api/group/save', params)
    },

    /**
     * 删除组
     */
    delete: (params: IGroupDeleteRequest): Promise<void> => {
        return post('system/api/group/delete', params)
    },

    /**
     * 关联角色
     */
    linkRole: (params: IGroupLinkRoleRequest): Promise<void> => {
        return post('system/api/group/link/role', params)
    },

    /**
     * 关联用户
     */
    linkUser: (params: IGroupLinkUserRequest): Promise<void> => {
        return post('system/api/group/link/user', params)
    },
}