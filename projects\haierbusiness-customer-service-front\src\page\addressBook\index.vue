<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Popover as hPopover,
  Card as hCard,
  Input as hInput,
  message,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  QuestionCircleOutlined,
  DeleteOutlined,
  MenuOutlined,
} from '@ant-design/icons-vue';
import { callCenterApi } from '@haierbusiness-front/apis';
import { AddressBookListRes, AddressBookListParams } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables';
import EditDialog from './edit-dialog.vue';
import { getCurrentRouter, routerParam } from '@haierbusiness-front/utils';
// import router from '../../../router';
const router = getCurrentRouter();

const currentRouter = ref();

const addressBook = ref<AddressBookListRes[]>([]);
const visible = ref<boolean>(false);
const editData = ref<any>({});
const searchForm = ref<any>({})
const columns = [
  {
    title: '工号',
    dataIndex: 'bookNum',
    width: '10%',
  },
  {
    title: '姓名',
    dataIndex: 'bookName',
    width: '10%',
  },
  {
    title: '手机/座机号',
    dataIndex: 'bookMobile',
    width: '15%',
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '10%',
  },
  {
    title: '创建人工号',
    dataIndex: 'createBy',
    width: '10%',
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '20%',
  },
  {
    title: '操作',
    dataIndex: 'operation',
  },
];

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(callCenterApi.list, {
  defaultParams: [{}],
  manual: false,
});


const dataSource = computed(
  () =>
    data.value?.records?.filter((item: any) => {
      return item;
    }) || [],
);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  listApiRun({
    ...searchForm.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

// 获取通讯录列表
const getaddressBookList = (keyWord?: string) => {
  callCenterApi.list(searchForm.value).then((res: any) => {
    console.log(res);
    addressBook.value = res;
  });
};

const handleCreate = () => {
  editData.value = {
    status:10
  }
  visible.value = true;
};

// 保存或者修改
const handleOk = (object: AddressBookListRes) => {
  callCenterApi.save(object).then((res) => {
    message.success('保存成功');
    visible.value = false;
    handleTableChange({ current:data.value?.pageNum, pageSize: 10 })
  });
};

// 编辑
const edit = (row: AddressBookListRes) => {
  editData.value = JSON.parse(JSON.stringify(row));
  visible.value = true;
};

// 确认删除
const confirmDel = (row: AddressBookListRes) => {
  console.log(row)
  callCenterApi.remove([row.id]).then((res: any) => {
    message.success('删除成功');
    handleTableChange({ current: data.value?.pageNum, pageSize: 10 })
  });
};

const onDialogClose = () => {
  visible.value = false;
};

const reset = () => {
  searchForm.value = {}
  handleTableChange({ current: 1, pageSize: 10 })
};

// 初始化
onMounted(async () => {
  currentRouter.value = await router;
  handleTableChange({ current: 1, pageSize: 10 })
});
</script>

<template>
  <div
    v-if="$route.matched.length < 3"
    style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto"
  >
    <h-card style="margin-bottom: 10px">
      <h-row :align="'middle'">
        <h-col :span="24">
          <h-row :align="'middle'" style="padding: 10px 10px 10px 10px">
            <h-col style="text-align: right; padding-right: 10px;width:60px;">
              <label for="createTime">工号：</label>
            </h-col>
            <h-col :span="4" style="text-align: right">
              <h-input allowClear v-model:value="searchForm.keyWord" placeholder="请输入工号" />
            </h-col>
            <h-col style="text-align: right; padding-right: 10px;width:80px;">
              <label for="createTime">姓名：</label>
            </h-col>
            <h-col :span="4" style="text-align: right">
              <h-input allowClear v-model:value="searchForm.userName" placeholder="请输入姓名" />
            </h-col>
            <h-col style="text-align: right; padding-right: 10px;width:100px;">
              <label for="createTime">手机/座机号：</label>
            </h-col>
            <h-col :span="4" style="text-align: right">
              <h-input allowClear v-model:value="searchForm.mobile" placeholder="请输入座机号" />
            </h-col>
          </h-row>
          <h-row>
            <h-col :span="24" style="text-align: right">
              <h-button type="primary" style="margin-right: 10px" @click="handleCreate()">
                <PlusOutlined /> 新增
              </h-button>
              <h-button style="margin-right: 10px" @click="reset">重置</h-button>
              <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })"> <SearchOutlined />查询 </h-button>
            </h-col>
          </h-row>
        </h-col>
      </h-row>
    </h-card>
    <h-table
    :loading="loading"
    :ellipsis="true"
    :row-key="(record) => record.id"
    :size="'small'"
    :pagination="pagination"
    :columns="columns"
    :data-source="dataSource"
    bordered
    @change="handleTableChange($event as any)">
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'operation'">
          <div class="editable-row-operations">
            <span>
              <a @click="edit(record)">编辑</a>
              <a-popconfirm
                title="确定要删除吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="confirmDel(record)"
                @cancel="cancel"
              >
                <a class="ml10" href="#">删除</a>
              </a-popconfirm>
            </span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'gmtCreate'">
          {{record.gmtCreate}}
        </template>
      </template>
    </h-table>
    <div v-if="visible">
      <edit-dialog
        :labelList="plainOptions"
        :knowCenterOptions="knowCenterOptions"
        :show="visible"
        :data="editData"
        @cancel="onDialogClose"
        @ok="handleOk"
      >
      </edit-dialog>
    </div>
  </div>
  <router-view></router-view>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.ml10 {
  margin-left: 10px;
}

// .pagination {
//   width: 100%;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   margin-top: 20px;
// }
</style>
