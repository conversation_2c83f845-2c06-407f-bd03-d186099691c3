<script setup lang="ts">
import { CommentOutlined, ShoppingCartOutlined, UserOutlined } from '@ant-design/icons-vue';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';
import { useCartStore } from '../store/goods-cart';
import { toRefs } from 'vue';

const router = useRouter();
const CartStore = useCartStore();
const { cartCount } = toRefs(CartStore);

const handleGoToCart = () => {
  router.push('/orderWater/cart-list');
};
</script>
<template>
  <a-float-button-group shape="square" :style="{ right: '36px', bottom: '324px' }">
    <a-float-button :badge="{ count: cartCount }" @click="handleGoToCart">
      <template #icon>
        <ShoppingCartOutlined :size="24" />
      </template>
    </a-float-button>
    <a-float-button>
      <template #icon>
        <UserOutlined :size="24" />
      </template>
    </a-float-button>
    <a-float-button>
      <template #icon>
        <CommentOutlined :size="24" />
      </template>
    </a-float-button>
    <a-back-top :visibility-height="200" />
  </a-float-button-group>
</template>
<style lang="less" scoped>
:deep(.ant-float-btn) {
  width: 64px;
  height: 64px;
  display: flex;
  justify-content: center;
  align-items: center;
  .ant-float-btn-body {
    width: 100%;
    height: 100%;
    background-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;

    .ant-float-btn-content {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;

      .anticon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
      }
    }

    &:hover {
      background-color: #f5f5f5;
    }
  }

  .anticon {
    font-size: 32px; // 增大图标尺寸
    color: #666;
  }
}

:deep(.ant-float-btn-primary) {
  .ant-float-btn-body {
    background-color: #1890ff;
    border-color: #1890ff;

    &:hover {
      background-color: #40a9ff;
    }
  }

  .anticon {
    color: #fff;
  }
}
</style>
