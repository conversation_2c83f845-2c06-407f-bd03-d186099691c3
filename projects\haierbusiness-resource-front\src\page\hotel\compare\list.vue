<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 0px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label>城市：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.spotCheckCity" placeholder="" autocomplete="off" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label>酒店名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.spotChecktHotelName" placeholder="" autocomplete="off" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label>入住日期：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker
              v-model:value="searchParam.spotCheckBookingDate"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label>房型：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.spotCheckRoomType" placeholder="" autocomplete="off" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 0px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label>抽检日期：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="searchParam.spotCheckDate" value-format="YYYY-MM-DD" style="width: 100%" />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label>状态：</label>
          </h-col>
          <h-col :span="4">
            <a-select v-model:value="searchParam.status" style="width: 100%" allowClear>
              <a-select-option :value="0">新增</a-select-option>
              <a-select-option :value="1">询价中</a-select-option>
              <a-select-option :value="2">询价完成</a-select-option>
              <a-select-option :value="3">已确认</a-select-option>
              <a-select-option :value="4">已取消</a-select-option>
              <a-select-option :value="999">询价失败</a-select-option>
            </a-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label>差异数据：</label>
          </h-col>
          <h-col :span="4">
            <a-select v-model:value="searchParam.queryDifferenceDataFlag" style="width: 100%" allowClear>
              <a-select-option :value="true">是</a-select-option>
              <a-select-option :value="false">否</a-select-option>
            </a-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button
              style="margin-right: 10px"
              type="primary"
              @click="handleTableChange({ current: 1, pageSize: 10 })"
            >
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </h-button>
            <h-button style="margin-right: 10px" type="primary" @click="exportList">
              <template #icon>
                <DownloadOutlined />
              </template>
              导出
            </h-button>
            <h-button type="primary" @click="exportAnalysisList">
              <template #icon>
                <BarChartOutlined />
              </template>
              生成分析报告
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: left">
            <h-button type="primary" style="margin-right: 10px" @click="add">
              <template #icon>
                <PlusOutlined />
              </template>
              新增询价
            </h-button>
            <h-button
              :disabled="!selectedRowKeys.length"
              type="primary"
              style="margin-right: 10px"
              @click="batchConfirmation(1)"
            >
              批量确认
            </h-button>
            <h-button
              :disabled="!selectedRowKeys.length"
              type="primary"
              style="margin-right: 10px"
              @click="batchConfirmation(3)"
            >
              批量取消
            </h-button>
            <h-button
              :disabled="!selectedRowKeys.length"
              type="primary"
              style="margin-right: 10px"
              @click="batchConfirmation(2)"
            >
              批量删除
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :row-key="(record: { id: string; }) => record.id"
          :size="'small'"
          :data-source="listData"
          :pagination="pagination"
          :loading="pageListLoading"
          :row-selection="rowSelection"
          :scroll="{ y: 510 }"
          bordered
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'status'">
              <a-tag color="orange" v-if="record.status == 0">新增</a-tag>
              <a-tag color="blue" v-if="record.status == 1">询价中</a-tag>
              <a-tag color="cyan" v-if="record.status == 2">询价完成</a-tag>
              <a-tag color="green" v-if="record.status == 3">已确认</a-tag>
              <a-tag color="warning" v-if="record.status == 4">已取消</a-tag>
              <a-tag color="error" v-if="record.status == 999">询价失败</a-tag>
            </template>
            <template v-if="column.dataIndex === 'xiechengPrice'">
              <div class="flex">
                <div class="left">
                  <div
                    v-if="
                      record.xiechengHaveResourcesType != 3 &&
                      record.xiechengHaveResourcesType != 4 &&
                      record.xiechengHaveResourcesType
                    "
                  >
                    <div>{{ record.xiechengPrice || '无价格' }}</div>
                    <div>{{ record.xiechengBreakfast || '' }}</div>
                    <div>{{ record.xiechengCancel || '' }}</div>
                  </div>
                  <div v-else>{{ record.xiechengHaveResourcesType ? '无价格' : '--' }}</div>
                  <div class="flexStart">
                    <a-tooltip placement="top" :mouseEnterDelay="0.5">
                      <template #title>
                        <span>编辑</span>
                      </template>
                      <EditOutlined
                        @click="editDate(record, 'xiecheng')"
                        style="color: #008dff; cursor: pointer; font-size: 14px"
                      />
                    </a-tooltip>
                    <a-tooltip placement="top" :mouseEnterDelay="0.5">
                      <template #title>
                        <span>查看图片</span>
                      </template>
                      <ZoomInOutlined
                        v-if="record.xiechengId && record.xiechengUrl.length"
                        style="color: #008dff; cursor: pointer; margin-left: 4px; font-size: 14px"
                        @click="setImgUrl(record.xiechengUrl)"
                      />
                    </a-tooltip>
                  </div>
                </div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'qiantaoPrice'">
              <div class="flex">
                <div class="left">
                  <div
                    v-if="
                      record.qiantaoHaveResourcesType != 3 &&
                      record.qiantaoHaveResourcesType != 4 &&
                      record.qiantaoHaveResourcesType
                    "
                  >
                    <div>{{ record.qiantaoPrice || '无价格' }}</div>
                    <div>{{ record.qiantaoBreakfast || '' }}</div>
                    <div>{{ record.qiantaoCancel || '' }}</div>
                  </div>
                  <div v-else>{{ record.qiantaoHaveResourcesType ? '无价格' : '--' }}</div>
                  <div class="flexStart">
                    <a-tooltip placement="top" :mouseEnterDelay="0.5">
                      <template #title>
                        <span>编辑</span>
                      </template>
                      <EditOutlined
                        @click="editDate(record, 'qiantao')"
                        style="color: #008dff; cursor: pointer; font-size: 14px"
                      />
                    </a-tooltip>
                    <a-tooltip placement="top" :mouseEnterDelay="0.5">
                      <template #title>
                        <span>查看图片</span>
                      </template>
                      <ZoomInOutlined
                        v-if="record.qiantaoId && record.qiantaoUrl.length"
                        style="color: #008dff; cursor: pointer; margin-left: 4px; font-size: 14px"
                        @click="setImgUrl(record.qiantaoUrl)"
                      />
                    </a-tooltip>
                  </div>
                </div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'xiechengB'">
              <div class="flex">
                <div class="left">
                  <div
                    v-if="
                      record.xiechengBHaveResourcesType != 3 &&
                      record.xiechengBHaveResourcesType != 4 &&
                      record.xiechengBHaveResourcesType
                    "
                  >
                    <div>{{ record.xiechengBPrice || '无价格' }}</div>
                    <div>{{ record.xiechengBBreakfast || '' }}</div>
                    <div>{{ record.xiechengBCancel || '' }}</div>
                  </div>
                  <div v-else>{{ record.xiechengBHaveResourcesType ? '无价格' : '--' }}</div>
                  <div class="flexStart">
                    <a-tooltip placement="top" :mouseEnterDelay="0.5">
                      <template #title>
                        <span>编辑</span>
                      </template>
                      <EditOutlined
                        @click="editDate(record, 'xiechengB')"
                        style="color: #008dff; cursor: pointer; font-size: 14px"
                      />
                    </a-tooltip>
                    <a-tooltip placement="top" :mouseEnterDelay="0.5">
                      <template #title>
                        <span>查看图片</span>
                      </template>
                      <ZoomInOutlined
                        v-if="record.xiechengBId && record.xiechengBUrl.length"
                        style="color: #008dff; cursor: pointer; margin-left: 4px; font-size: 14px"
                        @click="setImgUrl(record.xiechengBUrl)"
                      />
                    </a-tooltip>
                  </div>
                </div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'xiechengC'">
              <div class="flex">
                <div class="left">
                  <div
                    v-if="
                      record.xiechengCHaveResourcesType != 3 &&
                      record.xiechengCHaveResourcesType != 4 &&
                      record.xiechengCHaveResourcesType
                    "
                  >
                    <div>{{ record.xiechengCPrice || '无价格' }}</div>
                    <div>{{ record.xiechengCBreakfast || '' }}</div>
                    <div>{{ record.xiechengCCancel || '' }}</div>
                  </div>
                  <div v-else>{{ record.xiechengCHaveResourcesType ? '无价格' : '--' }}</div>
                  <div class="flexStart">
                    <a-tooltip placement="top" :mouseEnterDelay="0.5">
                      <template #title>
                        <span>编辑</span>
                      </template>
                      <EditOutlined
                        @click="editDate(record, 'xiechengC')"
                        style="color: #008dff; cursor: pointer; font-size: 14px"
                      />
                    </a-tooltip>
                    <a-tooltip placement="top" :mouseEnterDelay="0.5">
                      <template #title>
                        <span>查看图片</span>
                      </template>
                      <ZoomInOutlined
                        v-if="record.xiechengCId && record.xiechengCUrl.length"
                        style="color: #008dff; cursor: pointer; margin-left: 4px; font-size: 14px"
                        @click="setImgUrl(record.xiechengCUrl)"
                      />
                    </a-tooltip>
                  </div>
                </div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'qiantaoB'">
              <div class="flex">
                <div class="left">
                  <div
                    v-if="
                      record.qiantaoBHaveResourcesType != 3 &&
                      record.qiantaoBHaveResourcesType != 4 &&
                      record.qiantaoBHaveResourcesType
                    "
                  >
                    <div>{{ record.qiantaoBPrice || '无价格' }}</div>
                    <div>{{ record.qiantaoBBreakfast || '' }}</div>
                    <div>{{ record.qiantaoBCancel || '' }}</div>
                  </div>
                  <div v-else>{{ record.qiantaoBHaveResourcesType ? '无价格' : '--' }}</div>
                  <div class="flexStart">
                    <a-tooltip placement="top" :mouseEnterDelay="0.5">
                      <template #title>
                        <span>编辑</span>
                      </template>
                      <EditOutlined
                        @click="editDate(record, 'qiantaoB')"
                        style="color: #008dff; cursor: pointer; font-size: 14px"
                      />
                    </a-tooltip>
                    <a-tooltip placement="top" :mouseEnterDelay="0.5">
                      <template #title>
                        <span>查看图片</span>
                      </template>
                      <ZoomInOutlined
                        v-if="record.qiantaoBId && record.qiantaoBUrl.length"
                        style="color: #008dff; cursor: pointer; margin-left: 4px; font-size: 14px"
                        @click="setImgUrl(record.qiantaoBUrl)"
                      />
                    </a-tooltip>
                  </div>
                </div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'feizhuC'">
              <div class="flex">
                <div class="left">
                  <div
                    v-if="
                      record.feizhuCHaveResourcesType != 3 &&
                      record.feizhuCHaveResourcesType != 4 &&
                      record.feizhuCHaveResourcesType
                    "
                  >
                    <div>{{ record.feizhuCPrice || '无价格' }}</div>
                    <div>{{ record.feizhuCBreakfast || '' }}</div>
                    <div>{{ record.feizhuCCancel || '' }}</div>
                  </div>
                  <div v-else>{{ record.feizhuCHaveResourcesType ? '无价格' : '--' }}</div>
                  <div class="flexStart">
                    <a-tooltip placement="top" :mouseEnterDelay="0.5">
                      <template #title>
                        <span>编辑</span>
                      </template>
                      <EditOutlined
                        @click="editDate(record, 'feizhuC')"
                        style="color: #008dff; cursor: pointer; font-size: 14px"
                      />
                    </a-tooltip>
                    <a-tooltip placement="top" :mouseEnterDelay="0.5">
                      <template #title>
                        <span>查看图片</span>
                      </template>
                      <ZoomInOutlined
                        v-if="record.feizhuCId && record.feizhuCUrl.length"
                        style="color: #008dff; cursor: pointer; margin-left: 4px; font-size: 14px"
                        @click="setImgUrl(record.feizhuCUrl)"
                      />
                    </a-tooltip>
                  </div>
                </div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'tianxiafangcangB'">
              <div class="flex">
                <div class="left">
                  <div
                    v-if="
                      record.tianxiafangcangBHaveResourcesType != 3 &&
                      record.tianxiafangcangBHaveResourcesType != 4 &&
                      record.tianxiafangcangBHaveResourcesType
                    "
                  >
                    <div>{{ record.tianxiafangcangBPrice || '无价格' }}</div>
                    <div>{{ record.tianxiafangcangBBreakfast || '' }}</div>
                    <div>{{ record.tianxiafangcangBCancel || '' }}</div>
                  </div>
                  <div v-else>{{ record.tianxiafangcangBHaveResourcesType ? '无价格' : '--' }}</div>
                  <div class="flexStart">
                    <a-tooltip placement="top" :mouseEnterDelay="0.5">
                      <template #title>
                        <span>编辑</span>
                      </template>
                      <EditOutlined
                        @click="editDate(record, 'tianxiafangcangB')"
                        style="color: #008dff; cursor: pointer; font-size: 14px"
                      />
                    </a-tooltip>
                    <a-tooltip placement="top" :mouseEnterDelay="0.5">
                      <template #title>
                        <span>查看图片</span>
                      </template>
                      <ZoomInOutlined
                        v-if="record.tianxiafangcangBId && record.tianxiafangcangBUrl.length"
                        style="color: #008dff; cursor: pointer; margin-left: 4px; font-size: 14px"
                        @click="setImgUrl(record.tianxiafangcangBUrl)"
                      />
                    </a-tooltip>
                  </div>
                </div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'meituanC'">
              <div class="flex">
                <div class="left">
                  <div
                    v-if="
                      record.meituanCHaveResourcesType != 3 &&
                      record.meituanCHaveResourcesType != 4 &&
                      record.meituanCHaveResourcesType
                    "
                  >
                    <div>{{ record.meituanCPrice || '无价格' }}</div>
                    <div>{{ record.meituanCBreakfast || '' }}</div>
                    <div>{{ record.meituanCCancel || '' }}</div>
                  </div>
                  <div v-else>{{ record.meituanCHaveResourcesType ? '无价格' : '--' }}</div>
                  <div class="flexStart">
                    <a-tooltip placement="top" :mouseEnterDelay="0.5">
                      <template #title>
                        <span>编辑</span>
                      </template>
                      <EditOutlined
                        @click="editDate(record, 'meituanC')"
                        style="color: #008dff; cursor: pointer; font-size: 14px"
                      />
                    </a-tooltip>
                    <a-tooltip placement="top" :mouseEnterDelay="0.5">
                      <template #title>
                        <span>查看图片</span>
                      </template>
                      <ZoomInOutlined
                        v-if="record.meituanCId && record.meituanCUrl.length"
                        style="color: #008dff; cursor: pointer; margin-left: 4px; font-size: 14px"
                        @click="setImgUrl(record.meituanCUrl)"
                      />
                    </a-tooltip>
                  </div>
                </div>
              </div>
            </template>
            <!-- 备注 -->
            <template v-if="column.dataIndex === 'remark'">
              <div style="display: flex">
                <a-tooltip>
                  <template #title>{{ record.remark }}</template>
                  <div class="remark">{{ record.remark }}</div>
                </a-tooltip>
                <a-tooltip placement="top" :mouseEnterDelay="0.5">
                  <template #title>
                    <span>修改备注</span>
                  </template>
                  <EditOutlined
                    @click="editRemark(record)"
                    style="color: #008dff; cursor: pointer; font-size: 14px; margin-left: 5px"
                  />
                </a-tooltip>
              </div>
            </template>
          </template>
          <template #expandedRowRender="{ record }">
            <div class="childTable">
              <a-table
                :dataSource="record.childList"
                :columns="childColumns"
                bordered
                size="small"
                :pagination="false"
                :expand-column-width="100"
              >
                <template #bodyCell="{ column, record, text }">
                  <template v-if="column.dataIndex === 'price'">
                    <div>
                      {{ record.price }}
                      <a-tooltip placement="top" :mouseEnterDelay="0.5">
                        <template #title>
                          <span>编辑</span>
                        </template>
                        <EditOutlined
                          @click="rowEdit(record)"
                          style="color: #008dff; cursor: pointer; font-size: 14px"
                        />
                      </a-tooltip>
                      <a-tooltip placement="top" :mouseEnterDelay="0.5">
                        <template #title>
                          <span>查看图片</span>
                        </template>
                        <ZoomInOutlined
                          v-if="record.ossPicAddress"
                          style="color: #008dff; margin-left: 4px; cursor: pointer; font-size: 14px"
                          @click="setRowImgUrl(record.ossPicAddress)"
                        />
                      </a-tooltip>
                    </div>
                  </template>
                </template>
              </a-table>
            </div>
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>
  <!-- <div style="display: none"> -->
  <!-- <a-image-preview-group :preview="{ visible, onVisibleChange: setVisible }">
      <a-image v-for="item of imgUrlList" :width="200" :src="item" />
    </a-image-preview-group> -->
  <!-- <viewer v-if="viewer" :images="imgUrlList">
       <img v-for="src in imgUrlList" :src="src" :key="src">
    </viewer> -->
  <!-- </div> -->

  <a-modal v-model:open="open" title="修改信息" :maskClosable="false" :footer="null" @cancel="handleCancel">
    <a-form
      ref="formRef"
      :model="formState.list[0]"
      name="basic"
      :label-col="{ span: 7 }"
      :wrapper-col="{ span: 16 }"
      autocomplete="off"
      @finish="onFinish"
      @finishFailed="onFinishFailed"
    >
      <a-form-item
        label="是否有资源"
        name="haveResourcesType"
        :rules="[{ required: true, message: '请选择是否有资源' }]"
      >
        <a-select
          v-model:value="formState.list[0].haveResourcesType"
          @change="editHaveResourcesTypeChange"
          style="width: 100%"
        >
          <a-select-option :value="1">有房</a-select-option>
          <a-select-option :value="2">满房</a-select-option>
          <a-select-option :value="3">此酒店无价格</a-select-option>
          <a-select-option :value="4">此房型无价格</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item
        label="价格"
        name="price"
        :rules="[
          {
            required: !(
              formState.list[0].haveResourcesType == 2 ||
              formState.list[0].haveResourcesType == 3 ||
              formState.list[0].haveResourcesType == 4
            ),
            message: '请输入价格',
          },
        ]"
      >
        <a-input-number
          @change="(val) => changeNumber(val, formState.list[0].comparingPrices)"
          v-model:value="formState.list[0].price"
          :disabled="formState.list[0].haveResourcesType == 3 || formState.list[0].haveResourcesType == 4"
          style="width: 100%"
        />
      </a-form-item>
      <a-form-item
        label="早餐类型"
        name="breakfastType"
        :rules="[
          {
            required: !(
              formState.list[0].haveResourcesType == 2 ||
              formState.list[0].haveResourcesType == 3 ||
              formState.list[0].haveResourcesType == 4
            ),
            message: '请选择早餐类型',
          },
        ]"
      >
        <a-select
          v-model:value="formState.list[0].breakfastType"
          :disabled="formState.list[0].haveResourcesType == 3 || formState.list[0].haveResourcesType == 4"
          style="width: 100%"
        >
          <a-select-option :value="1">无早</a-select-option>
          <a-select-option :value="2">单早</a-select-option>
          <a-select-option :value="3">双早</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item
        label="取消规则"
        name="cancelType"
        :rules="[
          {
            required: !(
              formState.list[0].haveResourcesType == 2 ||
              formState.list[0].haveResourcesType == 3 ||
              formState.list[0].haveResourcesType == 4
            ),
            message: '请选择取消规则',
          },
        ]"
      >
        <a-select
          v-model:value="formState.list[0].cancelType"
          :disabled="formState.list[0].haveResourcesType == 3 || formState.list[0].haveResourcesType == 4"
          style="width: 100%"
        >
          <a-select-option :value="1">限时取消</a-select-option>
          <a-select-option :value="2">不可取消</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="截图" name="ossPicAddress">
        <a-image-preview-group>
          <div v-for="(item, index) of editImgList" class="imgItem" :key="index">
            <CloseCircleOutlined class="deleteIcon" @click="deleteImg(index)" />
            <img :width="100" :height="100" :src="item" @click="lookImg(item, index)" />
          </div>
        </a-image-preview-group>

        <a @click="getclipboardImg">获取剪切板图片</a>
      </a-form-item>
      <a-form-item
        v-if="
          formState.list[0].comparingPrices &&
          formState.list[0].comparingPrices != formState.list[0].price &&
          formState.list[0].price
        "
        label="差异类型原因"
        name="differenceReasonType"
        :rules="[
          {
            required: !(
              formState.list[0].haveResourcesType == 2 ||
              formState.list[0].haveResourcesType == 3 ||
              formState.list[0].haveResourcesType == 4
            ),
            message: '请选择差异类型原因',
          },
        ]"
      >
        <a-select v-model:value="formState.list[0].differenceReasonType" allowClear style="width: 100%">
          <a-select-option :value="1">优惠券原因</a-select-option>
          <a-select-option :value="2">支付类型原因（担保付/到店付）</a-select-option>
          <a-select-option :value="3">会员价原因</a-select-option>
          <a-select-option :value="4">胜意系统原因</a-select-option>
          <a-select-option :value="5">供应商原因</a-select-option>
          <a-select-option :value="10">其他</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item
        v-if="
          formState.list[0].comparingPrices &&
          formState.list[0].comparingPrices != formState.list[0].price &&
          formState.list[0].price
        "
        label="差异原因描述"
        name="differenceReasonDescription"
      >
        <a-textarea v-model:value="formState.list[0].differenceReasonDescription"> </a-textarea>
      </a-form-item>
      <a-form-item
        v-if="
          formState.list[0].comparingPrices &&
          formState.list[0].comparingPrices != formState.list[0].price &&
          formState.list[0].price
        "
        label="供应商原因"
        name="reason"
      >
        <a-radio-group v-model:value="formState.list[0].reasonType">
          <a-radio @click="reasonTypeChange(1)" :value="1">供应商原因</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        v-if="formState.list[0].status != 1 && formState.list[0].status != 3"
        :wrapper-col="{ offset: 8, span: 16 }"
      >
        <a-button type="primary" html-type="submit">确定</a-button>
        <a-button class="btn" @click="handleCancel">取消</a-button>
      </a-form-item>
    </a-form>
  </a-modal>
  <a-modal v-model:open="openEditRemark" title="修改备注" :maskClosable="false" :footer="null" @cancel="handleCancel">
    <a-form
      ref="formRef"
      :model="editRemarkRow"
      name="basic"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 16 }"
      autocomplete="off"
      @finish="editRemarkOnFinish"
      @finishFailed="onFinishFailed"
    >
      <a-form-item label="备注" name="price">
        <a-textarea v-model:value="editRemarkRow.remark" />
      </a-form-item>

      <a-form-item :wrapper-col="{ offset: 8, span: 16 }">
        <a-button type="primary" html-type="submit">确定</a-button>
        <a-button class="btn" @click="handleCancel">取消</a-button>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  Button as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
  List,
} from 'ant-design-vue';
import { api as viewerApi } from 'v-viewer';
import type { TableColumnsType, TableColumnType } from 'ant-design-vue';
import { fileApi } from '@haierbusiness-front/apis';
import {
  CloseCircleOutlined,
  SearchOutlined,
  ZoomInOutlined,
  EditOutlined,
  PlusOutlined,
  DownloadOutlined,
  ExclamationCircleOutlined,
  ReconciliationFilled,
  BarChartOutlined,
} from '@ant-design/icons-vue';
import { b2bWyyBalanceApi, SupplierApi } from '@haierbusiness-front/apis';

import { errorModal, getCurrentRouter, routerParam } from '@haierbusiness-front/utils';

import { computed, ref, watch, reactive, unref, createVNode } from 'vue';

import { onMounted } from 'vue';

const formState = reactive<any>({
  list: [
    {
      cancel: '',
      price: '',
      breakfast: '',
      id: '',
      serverType: 1,
      vendorType: 1,
      lowerPriceFlag: '',
      exclusiveResourcesFlag: '',
    },
  ],
});
const formRef = ref();
const imgUrlList = ref<Array<string>>([]);
const editImgList = ref<Array<string>>([]);
const pageListLoading = ref<boolean>(false);
const visible = ref<boolean>(false);
const setVisible = (value: boolean): void => {
  console.log(1222, value);
  visible.value = value;
};
const getclipboardImg = () => {
  // 首先要确保浏览器支持Clipboard API
  if (typeof navigator.clipboard !== 'undefined' && typeof ClipboardItem === 'function') {
    // 从剪贴板读取内容
    navigator.clipboard.read().then(async function (items) {
      console.log(items, 'item');
      for (let item of items) {
        console.log(item.types[0].indexOf('image'), '111');

        if (item.types[0].indexOf('image') !== -1) {
          const blob = await item.getType('image/png');
          const url = URL.createObjectURL(blob);

          let formData = new FormData();
          formData.append('file', blob, 'image.png');
          fileApi.upload(formData).then((res) => {
            if (
              window.location.origin.indexOf('http://127.0.0.1') !== -1 ||
              window.location.origin.indexOf('http://localhost') !== -1
            ) {
              editImgList.value.push(`https://businessmanagement-test.haier.net/${res.path}`);
            } else {
              editImgList.value.push(`${window.location.origin}/${res.path}`);
            }
          });
        }
      }
    });
  } else {
    console.log('当前浏览器不支持Clipboard API');
  }
};
const setImgUrl = (value: Array<string>): void => {
  imgUrlList.value = value;
  const $viewer = viewerApi({
    images: imgUrlList.value,
  });
};
const setRowImgUrl = (value: string): void => {
  imgUrlList.value = value.split(';');
  // setVisible(true);
  const $viewer = viewerApi({
    images: imgUrlList.value,
  });
};
const lookImg = (item:any, index:number) => {
  const $viewer = viewerApi({
    options: {
      initialViewIndex: index,
    },
    images: editImgList.value,
  });
};
const deleteImg = (index: number) => {
  editImgList.value.splice(index, 1);
};
const onFinish = (values: any) => {
  editVentor();
};

const onFinishFailed = (errorInfo: any) => {
  console.log('Failed:', errorInfo);
};
const router = getCurrentRouter();
const open = ref(false);
const renderTdBackgroundqiantao = (record: any) => {
  if (!record.qiantaoBId) {
    return;
  }
  if (record.qiantaoBRedFlag) {
    return {
      style: {
        // 行背景色
        'background-color': '#f5ccd3',
      },
    };
  }
};
const renderTdBackgroundxiecheng = (record: any) => {
  console.log(record);
  if (!record.xiechengBId) {
    return;
  }
  if (record.xiechengBRedFlag) {
    return {
      style: {
        // 行背景色
        'background-color': '#f5ccd3',
      },
    };
  }
};
const columns: TableColumnsType = [
  {
    title: '酒店名称',
    dataIndex: 'spotChecktHotelName',
    fixed: 'left',
    width: '200px',
    align: 'center',
    ellipsis: false,
  },
  {
    title: '抽检日期',
    dataIndex: 'spotCheckDate',
    fixed: 'left',
    width: '160px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '城市',
    dataIndex: 'spotCheckCity',
    fixed: 'left',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '入住日期',
    dataIndex: 'indate',
    width: '160px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '离店日期',
    dataIndex: 'outdate',
    width: '160px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '房型',
    dataIndex: 'spotCheckRoomType',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '商旅系统价格',
    children: [
      {
        title: '携程',
        dataIndex: 'xiechengPrice',
        key: 'xiecheng',
        width: 110,
      },
      {
        title: '千淘',
        dataIndex: 'qiantaoPrice',
        key: 'qiantao',
        width: 110,
      },
    ],
  },
  {
    title: 'B端渠道',
    children: [
      {
        title: '携程',
        dataIndex: 'xiechengB',
        key: 'xiechengB',
        width: 120,
        customCell: renderTdBackgroundxiecheng,
      },
      {
        title: '千淘',
        dataIndex: 'qiantaoB',
        key: 'qiantaoB',
        width: 120,
        customCell: renderTdBackgroundqiantao,
      },
      {
        title: '天下房仓',
        dataIndex: 'tianxiafangcangB',
        key: 'tianxiafangcangB',
        width: 120,
      },
    ],
  },
  {
    title: 'C端渠道',
    children: [
      {
        title: '携程',
        dataIndex: 'xiechengC',
        key: 'xiechengC',
        width: 120,
      },
      {
        title: '飞猪',
        dataIndex: 'feizhuC',
        key: 'feizhuC',
        width: 120,
      },
      {
        title: '美团',
        dataIndex: 'meituanC',
        key: 'meituanC',
        width: 120,
      },
    ],
  },
  {
    title: '抽检人',
    dataIndex: 'createName',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '下发任务时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
];
const childColumns: TableColumnType[] = [
  {
    title: '渠道',
    dataIndex: 'server',
    key: 'server',
    width: '100px',
    customCell: (record, index) => {
      return {
        rowSpan: record.rowSpan || 0,
      };
    },
  },
  {
    title: '供应商名称',
    dataIndex: 'vendor',
    key: 'vendor',
    width: '100px',
  },

  {
    title: '价格',
    dataIndex: 'price',
    key: 'price',
    width: '100px',
    customCell: (record, index) => {
      if (record.serverType == 2 && (record.vendorType == 1 || record.vendorType == 2)) {
        if (record.redFlag) {
          return {
            rowSpan: 1,
            style: {
              // 行背景色
              'background-color': '#f5ccd3',
            },
          };
        }
      }
      return {
        rowSpan: 1,
      };
    },
  },
  {
    title: '早餐类型',
    dataIndex: 'breakfast',
    key: 'breakfast',
    width: '100px',
  },
  {
    title: '取消类型',
    dataIndex: 'cancel',
    key: 'cancel',
    width: '100px',
  },
];
// 多选
const selectedRowKeys = ref<string[]>([]); // Check here to configure the default column

const onSelectChange = (changableRowKeys: string[]) => {
  selectedRowKeys.value = changableRowKeys;
};
// 修改单选
const reasonTypeChange = (value: string) => {
  if (formState.list[0].reasonType) {
    formState.list[0].reasonType = null;
  }
};
const rowSelection = computed(() => {
  return {
    selectedRowKeys: unref(selectedRowKeys),
    onChange: onSelectChange,
    hideDefaultSelections: true,
    selections: [hTable.SELECTION_ALL, hTable.SELECTION_INVERT, hTable.SELECTION_NONE],
  };
});

// 批量确认
const batchConfirmation = (num: number) => {
  if (selectedRowKeys.value.length == 0) {
    message.warn('请先选择数据');
    return;
  }
  // 1 批量确认 2 批量删除 3批量取消
  if (num == 1) {
    showbatchConfirm();
  } else if (num == 2) {
    showBatchDeleteConfirm();
  } else {
    showBatchCancelConfirm();
  }
};
// 批量确认确认框
const showbatchConfirm = () => {
  hModal.confirm({
    title: '确定要批量确认吗？',
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {
      const params = { ids: selectedRowKeys.value };
      SupplierApi.batchConfirm(params).then((res: any) => {
        console.log(res);
        if (res) {
          message.success('确认成功');
          getTableList({ current: pagination.value.current, pageSize: 10 });
          selectedRowKeys.value = [];
        }
      });
    },
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onCancel() {},
  });
};

// 批量删除确认框
const showBatchDeleteConfirm = () => {
  hModal.confirm({
    title: '确定要批量删除吗？',
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {
      const params = { ids: selectedRowKeys.value };
      SupplierApi.batchDelete(params).then((res: any) => {
        if (res) {
          message.success('删除成功');
          getTableList({ current: pagination.value.current, pageSize: 10 });
          selectedRowKeys.value = [];
        }
      });
    },
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onCancel() {},
  });
};

// 批量删除确认框
const showBatchCancelConfirm = () => {
  hModal.confirm({
    title: '确定要批量取消吗？',
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {
      const params = { ids: selectedRowKeys.value };
      SupplierApi.batchCancel(params).then((res: any) => {
        if (res) {
          message.success('取消成功');
          getTableList({ current: pagination.value.current, pageSize: 10 });
          selectedRowKeys.value = [];
        }
      });
    },
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onCancel() {},
  });
};
const searchParam = reactive<any>({
  status: '',
  spotCheckCity: '',
  spotChecktHotelName: '',
  spotCheckBookingDate: [],
  spotCheckRoomType: '',
  spotCheckDate: [],
  pageNum: 1,
  pageSize: 10,
});

const listData = ref<any>([]);

const add = () => {
  router.push({ path: '/hotel-analysis/add' });
};

const pagination = ref({
  showSizeChanger: true,
  showQuickJumper: true,
  total: 0,
  current: 1,
  pageSize: 10,
  position: ['bottomCenter'],
});
// 修改信息 是否有资源改变时
const editHaveResourcesTypeChange = (type: number) => {
  // 如果选择3 和 4 name清空其他值 并置灰
  if (type == 3 || type == 4) {
    formState.list[0].price = null;
    formState.list[0].breakfastType = null;
    formState.list[0].cancelType = null;
  }
};
const changeNumber = (val: number, comparingPrices: number) => {
  console.log(val, comparingPrices);
  if (val == comparingPrices) {
    // 价格相同
    formState.list[0].differenceReasonType = null;
    formState.list[0].differenceReasonDescription = null;
    formState.list[0].reasonType = null;
  }
};
const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  getTableList(pag);
};
const editDate = (row: { [x: string]: any; id: any }, key: string | string[]) => {
  console.log(`${key}Id`, row, '111');
  if (key.indexOf('B') !== -1) {
    formState.list[0].serverType = 2;
  } else if (key.indexOf('C') !== -1) {
    formState.list[0].serverType = 3;
  } else {
    formState.list[0].serverType = 1;
    // formState.list[0].lowerPriceFlag = row[`${key}LowerPriceFlag`] ? 1 : 0;
    // formState.list[0].exclusiveResourcesFlag = row[`${key}ExclusiveResourcesFlag`] ? 1 : 0;
  }
  if (key.indexOf('xiecheng') !== -1) {
    formState.list[0].vendorType = 1;
    if (formState.list[0].serverType == 2) {
      formState.list[0].comparingPrices = row.xiechengPrice;
    } else {
      formState.list[0].comparingPrices = null;
    }
  } else if (key.indexOf('qiantao') !== -1) {
    formState.list[0].vendorType = 2;
    if (formState.list[0].serverType == 2) {
      formState.list[0].comparingPrices = row.qiantaoPrice;
    } else {
      formState.list[0].comparingPrices = null;
    }
  } else if (key.indexOf('tianxiafangcang') !== -1) {
    formState.list[0].vendorType = 3;
  } else if (key.indexOf('feizhu') !== -1) {
    formState.list[0].vendorType = 5;
  } else if (key.indexOf('meituan') !== -1) {
    formState.list[0].vendorType = 4;
  }

  open.value = true;
  editImgList.value = row[`${key}Url`] ? JSON.parse(JSON.stringify(row[`${key}Url`])) : [];
  formState.list[0].ledgerSpotCheckId = row.id;
  formState.list[0].id = row[`${key}Id`];
  formState.list[0].breakfastType = row[`${key}BreakfastType`];
  formState.list[0].price = row[`${key}Price`];
  formState.list[0].cancelType = row[`${key}CancelType`];
  formState.list[0].haveResourcesType = row[`${key}HaveResourcesType`];
  formState.list[0].differenceReasonType = row[`${key}DifferenceReasonType`];
  formState.list[0].differenceReasonDescription = row[`${key}DifferenceReasonDescription`];
  formState.list[0].reasonType = row[`${key}ReasonType`];
  formState.list[0].status = row.status;
};
const rowEdit = (row: { [x: string]: any; id: any }) => {
  console.log(row, '-------------');
  open.value = true;
  (editImgList.value = row.ossPicAddress ? row.ossPicAddress.split(';') : []),
    (formState.list[0] = JSON.parse(JSON.stringify(row)));
};
const openEditRemark = ref<boolean>(false);
const editRemarkRow = ref<any>({});
const editRemark = (row: any) => {
  openEditRemark.value = true;
  editRemarkRow.value = JSON.parse(JSON.stringify(row));
  console.log(row, '------------');
};
const editRemarkOnFinish = () => {
  SupplierApi.updateRemark({ id: editRemarkRow.value.id, remark: editRemarkRow.value.remark }).then((res) => {
    message.success('修改成功');
    openEditRemark.value = false;
    formRef.value.resetFields();
    getTableList({ current: pagination.value.current, pageSize: 10 });
  });
};
const changePrice = (e: { target: { value: any } }) => {
  console.log(e.target.value);
  formState.list[0].price = e.target.value;
};

const handleOk = () => {
  open.value = false;
  console.log('修改成功');
};
const handleCancel = () => {
  open.value = false;
  openEditRemark.value = false;
  console.log('取消修改');
};
// 重置
const reset = () => {
  searchParam.spotCheckCity = '';
  searchParam.queryDifferenceDataFlag = null;
  (searchParam.spotChecktHotelName = ''),
    (searchParam.spotCheckBookingDate = []),
    (searchParam.spotCheckRoomType = ''),
    (searchParam.spotCheckDate = []),
    (searchParam.status = '');
  (searchParam.pageNum = 1),
    (searchParam.pageSize = 10),
    getTableList({ current: pagination.value.current, pageSize: 10 });
};
const getTableList = (pag: { current: number; pageSize: number }) => {
  pageListLoading.value = true;
  pagination.value.current = pag.current;
  pagination.value.pageSize = pag.pageSize;
  searchParam.pageNum = pag.current;
  searchParam.pageSize = pag.pageSize;
  let params: any = {
    spotCheckCity: searchParam.spotCheckCity,
    spotChecktHotelName: searchParam.spotChecktHotelName,
    spotCheckRoomType: searchParam.spotCheckRoomType,
    status: searchParam.status,
    pageNum: searchParam.pageNum,
    pageSize: searchParam.pageSize,
    queryDifferenceDataFlag: searchParam.queryDifferenceDataFlag,
  };
  searchParam.spotCheckBookingDate?.length
    ? (params['spotCheckBookingDate[0]'] = searchParam.spotCheckBookingDate[0])
    : null;
  searchParam.spotCheckBookingDate?.length
    ? (params['spotCheckBookingDate[1]'] = searchParam.spotCheckBookingDate[1])
    : null;

  searchParam.spotCheckDate?.length ? (params['spotCheckDate[0]'] = searchParam.spotCheckDate[0]) : null;
  searchParam.spotCheckDate?.length ? (params['spotCheckDate[1]'] = searchParam.spotCheckDate[1]) : null;

  SupplierApi.getSupplierList(params)
    .then((res: any) => {
      if (res) {
        const list = res.records.map((item: any) => {
          if (item.childList.length) {
            item.childList.forEach(
              (v: {
                vendor: string;
                serverType: number;
                price: any;
                breakfastType: any;
                breakfast: any;
                cancel: any;
                cancelType: any;
                ossPicAddress: string;
                id: any;
                haveResourcesType: any;
                exclusiveResourcesFlag: boolean;
                lowerPriceFlag: boolean;
                differenceReasonType: number;
                differenceReasonDescription: string;
                reasonType: number;
                redFlag: boolean;
                status: number;
              }) => {
                v.status = item.status;
                if (v.vendor == '携程' && v.serverType == 1) {
                  item = {
                    ...item,
                    xiechengPrice: v.price,
                    xiechengBreakfastType: v.breakfastType,
                    xiechengCancelType: v.cancelType,
                    xiechengBreakfast: v.breakfast,
                    xiechengCancel: v.cancel,
                    xiechengUrl: v.ossPicAddress ? v.ossPicAddress.split(';') : [],
                    xiechengHaveResourcesType: v.haveResourcesType,
                    xiechengId: v.id,
                    xiechengExclusiveResourcesFlag: v.exclusiveResourcesFlag,
                    xiechengLowerPriceFlag: v.lowerPriceFlag,
                  };
                }
                if (v.vendor == '携程' && v.serverType == 2) {
                  item = {
                    ...item,
                    xiechengBId: v.id,
                    xiechengBPrice: v.price,
                    xiechengBBreakfastType: v.breakfastType,
                    xiechengBCancelType: v.cancelType,
                    xiechengBBreakfast: v.breakfast,
                    xiechengBCancel: v.cancel,
                    xiechengBHaveResourcesType: v.haveResourcesType,
                    xiechengBUrl: v.ossPicAddress ? v.ossPicAddress.split(';') : [],
                    xiechengBDifferenceReasonDescription: v.differenceReasonDescription,
                    xiechengBDifferenceReasonType: v.differenceReasonType,
                    xiechengBReasonType: v.reasonType,
                    xiechengBRedFlag: v.redFlag,
                  };
                }
                if (v.vendor == '携程' && v.serverType == 3) {
                  item = {
                    ...item,
                    xiechengCId: v.id,
                    xiechengCPrice: v.price,
                    xiechengCBreakfastType: v.breakfastType,
                    xiechengCCancelType: v.cancelType,
                    xiechengCBreakfast: v.breakfast,
                    xiechengCCancel: v.cancel,
                    xiechengCHaveResourcesType: v.haveResourcesType,
                    xiechengCUrl: v.ossPicAddress ? v.ossPicAddress.split(';') : [],
                  };
                }

                if (v.vendor == '千淘' && v.serverType == 1) {
                  item = {
                    ...item,
                    qiantaoId: v.id,
                    qiantaoPrice: v.price,
                    qiantaoBreakfastType: v.breakfastType,
                    qiantaoCancelType: v.cancelType,
                    qiantaoBreakfast: v.breakfast,
                    qiantaoCancel: v.cancel,
                    qiantaoHaveResourcesType: v.haveResourcesType,
                    qiantaoUrl: v.ossPicAddress ? v.ossPicAddress.split(';') : [],
                    qiantaoExclusiveResourcesFlag: v.exclusiveResourcesFlag,
                    qiantaoLowerPriceFlag: v.lowerPriceFlag,
                  };
                }
                if (v.vendor == '千淘' && v.serverType == 2) {
                  item = {
                    ...item,
                    qiantaoBId: v.id,
                    qiantaoBPrice: v.price,
                    qiantaoBBreakfastType: v.breakfastType,
                    qiantaoBCancelType: v.cancelType,
                    qiantaoBBreakfast: v.breakfast,
                    qiantaoBCancel: v.cancel,
                    qiantaoBHaveResourcesType: v.haveResourcesType,
                    qiantaoBUrl: v.ossPicAddress ? v.ossPicAddress.split(';') : [],
                    qiantaoBDifferenceReasonDescription: v.differenceReasonDescription,
                    qiantaoBDifferenceReasonType: v.differenceReasonType,
                    qiantaoBReasonType: v.reasonType,
                    qiantaoBRedFlag: v.redFlag,
                  };
                }
                if (v.vendor == '飞猪' && v.serverType == 3) {
                  item = {
                    ...item,
                    feizhuCId: v.id,
                    feizhuCPrice: v.price,
                    feizhuCBreakfastType: v.breakfastType,
                    feizhuCCancelType: v.cancelType,
                    feizhuCBreakfast: v.breakfast,
                    feizhuCCancel: v.cancel,
                    feizhuCHaveResourcesType: v.haveResourcesType,
                    feizhuCUrl: v.ossPicAddress ? v.ossPicAddress.split(';') : [],
                  };
                }
                if (v.vendor == '天下房仓' && v.serverType == 1) {
                  item = {
                    ...item,
                    tianxiafangcangId: v.id,
                    tianxiafangcangPrice: v.price,
                    tianxiafangcangBreakfastType: v.breakfastType,
                    tianxiafangcangCancelType: v.cancelType,
                    tianxiafangcangBreakfast: v.breakfast,
                    tianxiafangcangCancel: v.cancel,
                    tianxiafangcangHaveResourcesType: v.haveResourcesType,
                    tianxiafangcangUrl: v.ossPicAddress ? v.ossPicAddress.split(';') : [],
                  };
                }
                if (v.vendor == '天下房仓' && v.serverType == 2) {
                  item = {
                    ...item,
                    tianxiafangcangBId: v.id,
                    tianxiafangcangBPrice: v.price,
                    tianxiafangcangBBreakfastType: v.breakfastType,
                    tianxiafangcangBCancelType: v.cancelType,
                    tianxiafangcangBBreakfast: v.breakfast,
                    tianxiafangcangBCancel: v.cancel,
                    tianxiafangcangBHaveResourcesType: v.haveResourcesType,
                    tianxiafangcangBUrl: v.ossPicAddress ? v.ossPicAddress.split(';') : [],
                  };
                }
                if (v.vendor == '美团' && v.serverType == 3) {
                  item = {
                    ...item,
                    meituanCId: v.id,
                    meituanCPrice: v.price,
                    meituanCBreakfastType: v.breakfastType,
                    meituanCCancelType: v.cancelType,
                    meituanCBreakfast: v.breakfast,
                    meituanCCancel: v.cancel,
                    meituanCHaveResourcesType: v.haveResourcesType,
                    meituanCUrl: v.ossPicAddress ? v.ossPicAddress.split(';') : [],
                  };
                }
              },
            );
          }
          return item;
        });
        listData.value = list;
        pagination.value.total = res.total;
        pageListLoading.value = false;
      }
    })
    .catch(() => {
      pageListLoading.value = false;
    });
};
const exportList = () => {
  let params: any = {
    spotCheckCity: searchParam.spotCheckCity,
    spotChecktHotelName: searchParam.spotChecktHotelName,

    spotCheckRoomType: searchParam.spotCheckRoomType,
  };
  searchParam.spotCheckBookingDate?.length
    ? (params['spotCheckBookingDate[0]'] = searchParam.spotCheckBookingDate[0])
    : null;
  searchParam.spotCheckBookingDate?.length
    ? (params['spotCheckBookingDate[1]'] = searchParam.spotCheckBookingDate[1])
    : null;
  searchParam.spotCheckDate?.length ? (params['spotCheckDate[0]'] = searchParam.spotCheckDate[0]) : null;
  searchParam.spotCheckDate?.length ? (params['spotCheckDate[1]'] = searchParam.spotCheckDate[1]) : null;
  SupplierApi.exportExcel(params);
};
const exportAnalysisList = () => {
  let params: any = {
    spotCheckCity: searchParam.spotCheckCity,
    spotChecktHotelName: searchParam.spotChecktHotelName,

    spotCheckRoomType: searchParam.spotCheckRoomType,
  };
  searchParam.spotCheckBookingDate?.length
    ? (params['spotCheckBookingDate[0]'] = searchParam.spotCheckBookingDate[0])
    : null;
  searchParam.spotCheckBookingDate?.length
    ? (params['spotCheckBookingDate[1]'] = searchParam.spotCheckBookingDate[1])
    : null;
  searchParam.spotCheckDate?.length ? (params['spotCheckDate[0]'] = searchParam.spotCheckDate[0]) : null;
  searchParam.spotCheckDate?.length ? (params['spotCheckDate[1]'] = searchParam.spotCheckDate[1]) : null;
  SupplierApi.exportAnalysisExcel(params);
};
const editVentor = () => {
  const params = {
    ...formState.list[0],
    ossPicAddress: editImgList.value.join(';'),
  };
  if (formState.list[0].id) {
    SupplierApi.updateVentor(params).then((res: any) => {
      if (res) {
        message.success('修改成功');
        open.value = false;
        formRef.value.resetFields();
        editImgList.value = [];
        getTableList({ current: pagination.value.current, pageSize: 10 });
      }
    });
  } else {
    SupplierApi.createVentor(params).then((res: any) => {
      if (res) {
        message.success('修改成功');
        open.value = false;
        formRef.value.resetFields();
        editImgList.value = [];
        getTableList({ current: pagination.value.current, pageSize: 10 });
      }
    });
  }
};
onMounted(() => {
  getTableList({ current: 1, pageSize: 10 });
});
</script>
<style scoped lang="less">
.btn {
  margin-left: 16px;
}
.childTable {
  width: 60%;
  padding: 10px;
}
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
.imgItem {
  position: relative;
  display: inline-block;
  margin-right: 4px;
  cursor: pointer;
  .deleteIcon {
    position: absolute;
    right: 0px;
    z-index: 10;
  }
}
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left {
    font-size: 12px;
    color: #008dff;
  }
}
.flexStart {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.remark {
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis;
  max-width: 150px;
}
</style>
