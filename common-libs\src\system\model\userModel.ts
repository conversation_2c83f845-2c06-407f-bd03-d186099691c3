export interface IUserInfo {
  id?: number;
  username?: string;
  phone?: string;
  gender?: number;
  _genderName?: string;
  state?: number;
  _stateName?: string;
  nickName?: string;
  email?: string;
  enterpriseCode?: string;
  enterpriseName?: string;
  lastLoginIp?: string;
  lastLoginTime?: string;
  loginFailedTime?: string;
  newUser?: boolean;
  expiredTime?: string;
  passwordExpiredTime?: string;
  gmtCreate?: string;
  createBy?: string;
  gmtModified?: string;
  lastModifiedBy?: string;
  departmentCode?: string;
  departmentName?: string;
  undertakeSuperiorName?: string;
  undertakeSuperiorCode?: string;
}

export interface IUserListRequest {
  keyWord?: string;
  pageNum?: number;
  pageSize?: number;
  groupId?: number;
  username?: string;
  phone?: string;
  gender?: number;
  state?: number;
  nickName?: string;
  enterpriseCode?: string;
  newUser?: boolean;
}

export interface IUserSaveUpdateRequest {
  id?: number;
  username?: string;
  password?: string;
  phone?: string;
  gender?: number;
  state?: number;
  nickName?: string;
  email?: string;
  enterpriseCode?: string;
  enterpriseName?: string;
  expiredTime?: string;
  passwordExpiredTime?: string;
}
