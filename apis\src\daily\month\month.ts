import {get, post} from '../../request'
import {
    ICalendarAPIFestivalInfoResponse,
    ICalendarAPIResponse,
    IMonthHolidayRequestDTO,
    IMonthPlanDetailRequestDTO,
    IMonthPlanDetailResponseDTO,
    IMonthPlanListRequestDTO,
    IMonthPlanListResponseDTO,
} from "@haierbusiness-front/common-libs";


export const dailyMonthPlanApi = {

    list: (params: IMonthPlanListRequestDTO): Promise<IMonthPlanListResponseDTO[]> => {
        return get('/daily/api/month-plan/list', params)
    },

    detail: (params: IMonthPlanDetailRequestDTO): Promise<IMonthPlanDetailResponseDTO> => {
        return get('/daily/api/month-plan/detail', params)
    },

    holiday: (params: IMonthHolidayRequestDTO): Promise<ICalendarAPIResponse[]> => {
        return get('/daily/api/month-plan/holiday', params)
    },
}