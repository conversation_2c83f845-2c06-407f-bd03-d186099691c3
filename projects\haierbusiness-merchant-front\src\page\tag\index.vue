<script setup lang="ts">
import { Button, Col, Row, Select, SelectOption, Table, Input } from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { tagApi } from '@haierbusiness-front/apis';
import { ITagFilter, ITag } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables';
import EditDialog from './edit-dialog.vue';
import router from '../../router';
// const router = useRouter()

const currentRouter = ref();

onMounted(async () => {
  currentRouter.value = await router;
});

const columns: ColumnType[] = [
  {
    title: '业务类型',
    dataIndex: 'businessType',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '标签名称',
    dataIndex: 'tagName',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '标签描述',
    dataIndex: 'description',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center',
  },
];
const searchParam = ref<ITagFilter>({});
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(tagApi.page, {
  manual: false,
});

//获取中台业务类型
const {
  data: businessTypeList,
  run: businessTypeApiRun,
  loading: businessTypeLoading,
} = useRequest(tagApi.getBusinessTypeList, {
  manual: false,
});

const reset = () => {
  searchParam.value = {};
  // 重新获取数据，回到第一页
  listApiRun({
    pageNum: 1,
    pageSize: data.value?.pageSize || 10,
  });
};

const dataSource = computed(() => data.value?.records || []);

// 添加分页配置
// 修改分页配置为计算属性
const pagination = computed(() => ({
  current: data.value?.pageNum || 1,
  pageSize: data.value?.pageSize || 10,
  total: data.value?.total || 0,
  showSizeChanger: true,
  showTotal: (total: number) => `共 ${total} 条`,
  pageSizeOptions: ['5', '10', '20', '50'],
}));

const handleTableChange = (pag: { current: number; pageSize: number }) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};
const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } = useEditDialog<ITag, ITag>(
  tagApi,
  '标签',
  () =>
    listApiRun({
      ...searchParam.value,
      pageNum: data.value?.pageNum || 1,
      pageSize: data.value?.pageSize || 10,
    }),
);

const thisHandleEdit = (item: ITag) => {
  const currentData = {
    ...item,
  };
  handleEdit({ ...currentData });
};

// 删除
const { handleDelete } = useDelete(tagApi, () =>
  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize,
  }),
);
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <a-row :align="'middle'">
      <a-col :span="24" style="margin-bottom: 10px">
        <a-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <a-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="tagName">标签名称：</label>
          </a-col>
          <a-col :span="4">
            <a-input id="tagName" v-model:value="searchParam.tagName" placeholder="" allow-clear />
          </a-col>
          <a-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="businessType">业务类型：</label>
          </a-col>
          <a-col :span="4">
            <a-select style="width: 100%" v-model:value="searchParam.businessType" allow-clear>
              <a-select-option v-for="(item, index) in businessTypeList" :key="index" :value="item.key">{{
                item.value
              }}</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="state">状态：</label>
          </a-col>
          <a-col :span="4">
            <a-select style="width: 100%" v-model:value="searchParam.state" allow-clear>
              <a-select-option :value="1">正常</a-select-option>
              <a-select-option :value="0">禁用</a-select-option>
            </a-select>
          </a-col>
        </a-row>
        <a-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <a-col :span="24" style="text-align: right">
            <a-button style="margin-right: 10px" @click="reset">重置</a-button>
            <a-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </a-button>
          </a-col>
        </a-row>
        <a-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <a-col :span="12" style="text-align: left">
            <a-button type="primary" @click="handleCreate()"> <PlusOutlined /> 新增 </a-button>
          </a-col>
        </a-row>
      </a-col>
      <a-col :span="24">
        <a-table
          :columns="columns"
          :row-key="(record) => record.id"
          :data-source="dataSource"
          :pagination="pagination"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'businessType'">
              {{
                record.businessType === 'QDDF'
                  ? '青岛订房'
                  : record.businessType === 'SLCL'
                  ? '商旅'
                  : record.businessType === 'QDDC'
                  ? '青岛订餐'
                  : record.businessType === 'MT'
                  ? '美团'
                  : '会展'
              }}
            </template>
            <template v-if="column.dataIndex === 'state'">
              {{ record.state === 1 ? '正常' : '禁用' }}
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <a-button type="link" @click="thisHandleEdit(record)">编辑</a-button>

              <a-button type="link" @click="handleDelete(record.id)">删除</a-button>
            </template>
          </template>
        </a-table>
      </a-col>
    </a-row>

    <div v-if="visible">
      <edit-dialog
        :show="visible"
        :data="editData"
        :businessTypeList="businessTypeList"
        @cancel="onDialogClose"
        @ok="handleOk"
      >
      </edit-dialog>
    </div>
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}
:deep(.ant-pagination) {
  justify-content: center !important;
}
</style>
