<script setup lang="ts">
import {
  Modal,
  Tag as hTag,
  Form as hForm,
  FormItem as hFormItem,
  But<PERSON> as hButton,
  Col as hCol,
  Row as hRow,
  Spin as hSpin,
  Table as hTable,
  DatePicker as hDatePicker,
  CollapsePanel as hCollapsePanel,
  Collapse as hCollapse,
  FormInstance,
  message,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import {
  IAnnualPlanSaveOrUpdateRequestDTO,
  AnnualPlanTypeStateConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
  IHaierAccountBillInfo,
} from '@haierbusiness-front/common-libs';
import { getCurrentRouter, resolveParam } from '@haierbusiness-front/utils';
import { ref, createVNode, PropType, computed, inject, watch } from 'vue';

import dayjs, { Dayjs } from 'dayjs';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import {
  EvaluateControlConstant,
  EvaluateTypeConstant,
  IAnnualPlanDetailRequestDTO,
  IAnnualPlanDetailResponseDTO,
  IAnnualPlanTypeListRequest,
  PlanTypeConstant,
} from '@haierbusiness-front/common-libs/src/daily';
import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
import detailYearTarget from './detailYearTarget.vue';
import detailYearDecompose from './detailYearDecompose.vue';
import detailYearPlan from './detailYearPlan.vue';
import evaluate from './evaluate.vue';

const { loginUser } = storeToRefs(applicationStore(globalPinia));

const router = getCurrentRouter();
const prop = defineProps({
  query: Object,
});

const data = ref<IAnnualPlanDetailResponseDTO>();
const dailyAnnualPlanDetailParam = ref<IAnnualPlanDetailRequestDTO>({
  id: prop?.query?.id,
  code: prop?.query?.code,
  evaluateControl: EvaluateControlConstant.ALL.code,
});
const frameModel = inject<any>('frameModel');
if (prop?.query?.frameModel) {
  frameModel.value = prop?.query?.frameModel;
}
const queryMonth = computed(() => parseInt(resolveParam(prop?.query?.month)));
const dailyAnnualPlanDetailApiRun = () => {
  dailyAnnualPlanApi.detail(dailyAnnualPlanDetailParam.value).then((it) => {
    data.value = it;
  });
};
dailyAnnualPlanDetailApiRun();

const evaluateParam = computed(() => {
  const result = {
    micro: {
      evaluates: [],
    },
    strategy: {
      evaluates: [],
    },
    finance: {
      evaluates: [],
    },
    control: {
      evaluates: [],
    },
    manpower: {
      evaluates: [],
    },
    platform: {
      evaluates: [],
    },
  };
  data.value?.monthPlans?.forEach((it) => {
    if (parseInt((it as any).month) === queryMonth.value) {
      it.evaluate?.forEach((ite) => {
        if (EvaluateTypeConstant.MICRO_MONTH.code === ite.type) {
          (result.micro.evaluates as any).push(ite);
        }
        if (EvaluateTypeConstant.THREE_STRATEGY.code === ite.type) {
          (result.strategy.evaluates as any).push(ite);
        }
        if (EvaluateTypeConstant.THREE_FINANCE.code === ite.type) {
          (result.finance.evaluates as any).push(ite);
        }
        if (EvaluateTypeConstant.THREE_CONTROL.code === ite.type) {
          (result.control.evaluates as any).push(ite);
        }
        if (EvaluateTypeConstant.THREE_MANPOWER.code === ite.type) {
          (result.manpower.evaluates as any).push(ite);
        }
        if (EvaluateTypeConstant.PLATFORM_MONTH.code === ite.type) {
          (result.platform.evaluates as any).push(ite);
        }
      });
    }
  });
  return result;
});

const collapseActiveKey = ref([1, 2, 3, 4, 5]);
{
  if (prop?.query?.fold) {
    collapseActiveKey.value = [];
  }
}

watch(
  () => prop!!.query!!.code,
  (n: any, o: any) => {
    (dailyAnnualPlanDetailParam.value.id = prop?.query?.id),
      (dailyAnnualPlanDetailParam.value.code = prop?.query?.code),
      (dailyAnnualPlanDetailParam.value.evaluateControl = EvaluateControlConstant.ALL.code),
      dailyAnnualPlanDetailApiRun();
  },
);
</script>

<template>
  <div
    style="
      background-color: #ffff;
      height: 100%;
      width: 100%;
      padding: 10px 10px 0px 10px;
      overflow-x: hidden;
      overflow-y: auto;
    "
  >
    <h-row :gutter="24">
      <h-col v-if="!prop?.query?.hideTitle" :span="24" style="text-align: center; margin: 14px 0">
        <div style="font-size: 20px; font-weight: 700">
          {{ data?.year || '----' }} 年
          <template v-if="queryMonth"> {{ queryMonth }} 月</template>
          - {{ data?.deptName || '------' }} - 小微目标
        </div>
      </h-col>
      <h-col :span="24">
        <detail-year-target :data="data" :fold="prop?.query?.fold"></detail-year-target>
      </h-col>
      <h-col :span="24">
        <detail-year-plan
          :data="data"
          :fold="prop?.query?.fold"
          :month="queryMonth"
          :show-temp="true"
        ></detail-year-plan>
      </h-col>
      <h-col :span="24">
        <h-collapse
          v-model:activeKey="collapseActiveKey"
          :bordered="false"
          style="background-color: white"
          :collapsible="'icon'"
        >
          <h-collapse-panel v-if="evaluateParam.strategy.evaluates.length > 0" key="1">
            <template #header>
              <div style="display: flex">
                <div style="height: 25px; width: 10px; background-color: rgb(0, 115, 229)" />
                <div style="font-size: 16px; font-weight: 600; margin-left: 10px">平台三自评价-战略</div>
              </div>
            </template>
            <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%; margin-bottom: 20px"></div>
            <evaluate v-if="data" :show="1" :save-param="evaluateParam.strategy" style="margin-bottom: 20px"></evaluate>
          </h-collapse-panel>
          <h-collapse-panel v-if="evaluateParam.control.evaluates.length > 0" key="2">
            <template #header>
              <div style="display: flex">
                <div style="height: 25px; width: 10px; background-color: rgb(0, 115, 229)" />
                <div style="font-size: 16px; font-weight: 600; margin-left: 10px">平台三自评价-风控</div>
              </div>
            </template>
            <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%; margin-bottom: 20px"></div>
            <evaluate v-if="data" :show="1" :save-param="evaluateParam.control" style="margin-bottom: 20px"></evaluate>
          </h-collapse-panel>
          <h-collapse-panel v-if="evaluateParam.finance.evaluates.length > 0" key="3">
            <template #header>
              <div style="display: flex">
                <div style="height: 25px; width: 10px; background-color: rgb(0, 115, 229)" />
                <div style="font-size: 16px; font-weight: 600; margin-left: 10px">平台三自评价-财务</div>
              </div>
            </template>
            <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%; margin-bottom: 20px"></div>
            <evaluate v-if="data" :show="1" :save-param="evaluateParam.finance" style="margin-bottom: 20px"></evaluate>
          </h-collapse-panel>
          <h-collapse-panel v-if="evaluateParam.manpower.evaluates.length > 0" key="4">
            <template #header>
              <div style="display: flex">
                <div style="height: 25px; width: 10px; background-color: rgb(0, 115, 229)" />
                <div style="font-size: 16px; font-weight: 600; margin-left: 10px">平台三自评价-人力</div>
              </div>
            </template>
            <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%; margin-bottom: 20px"></div>
            <evaluate v-if="data" :show="1" :save-param="evaluateParam.manpower" style="margin-bottom: 20px"></evaluate>
          </h-collapse-panel>
          <h-collapse-panel v-if="evaluateParam.platform.evaluates.length > 0" key="5">
            <template #header>
              <div style="display: flex">
                <div style="height: 25px; width: 10px; background-color: rgb(0, 115, 229)" />
                <div style="font-size: 16px; font-weight: 600; margin-left: 10px">平台评价</div>
              </div>
            </template>
            <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%; margin-bottom: 20px"></div>
            <evaluate
              v-if="data"
              :show="1"
              type="planform-evaluate"
              :save-param="evaluateParam.platform"
              style="margin-bottom: 20px"
            ></evaluate>
          </h-collapse-panel>
        </h-collapse>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
@import '../../../assets/css/main.less';
</style>
