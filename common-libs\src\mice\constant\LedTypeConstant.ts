type keys = 'DOMESTIC' | 'ABROAD';


export const LedTypeConstant = {
    DOMESTIC: { code: 1, desc: '酒店自有' },
    ABROAD: { code: 2, desc: '广告公司外搭' },

    ofType: (type?: number): { code: number, desc: string } | null => {
        for (const key in LedTypeConstant) {
          const item = LedTypeConstant[key as keys];
          if (type === item.code) {
            return item;
          }
        }
        return null;
      },
  
      toArray:() :({ code: number, desc: string } | undefined)[] => {
        const types = Object.keys(LedTypeConstant).map((i: string) => {
          if(i !== 'ofType' && i !== 'toArray') {
            return LedTypeConstant[i as keys]
          }
          return
        })
        const newTypes = types.filter(function (s) {
          return s && s; 
        })
        return newTypes
      },
}
