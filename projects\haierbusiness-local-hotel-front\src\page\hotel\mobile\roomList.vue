<template>
  <div class="room-list">
    <div :class="room.select ? 'active' : ''" class="room-item flex mb-20" v-for="room, index in roomList"
      label-position="left" :key="room.id">
      <div class="item-label" @click.stop="room.select = !room.select">
        <div class="item-label-title flex">
          <span class="mr-10 blue-text">{{ room.name }}</span>
          <span class="mr-10">{{ LocalHotelRoomTypeEnum[room.type] }}</span>
          <span>[{{ LocalHotelBedTypeEnum[room.bedType] }} {{ room.bedName }}]</span>
        </div>
        <div class="item-lable-more flex">
          <span>{{ LocalHotelBreakfastTypeEnum[room.breakfastType] }} (¥{{ room.breakfastPrice / 100 }})</span>
          <van-divider vertical class="shu" />
          <span>{{ room.hasInternet ? '有宽带' : '无宽带' }}</span>
        </div>
        <div class="item-label-price flex">
          <span class="item-price mr-5">¥<span>{{ room.firstDayPrice / 100 }}</span></span>
          <span class="mr-5 shouri">首日房价</span>
          <span class="blue-text ml-16 font-size-22" @click.stop="showRoomDayPrices(index)">查看每日房价 ></span>
        </div>
      </div>
      <div class="item-check">
        <van-checkbox v-model="room.select"></van-checkbox>
      </div>
    </div>

    <van-calendar title="每日房价" :max-date="new Date(checkOut)" :default-date="[new Date(checkIn), new Date(checkOut)]"
      readonly :show-confirm="false" v-model:show="showCalendar" type="range" :formatter="formatter" />

  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, onBeforeUpdate, watch } from 'vue';
import type { Ref } from 'vue';
import { localHotelApi } from '@haierbusiness-front/apis';

import { LocalHotelRoomTypeEnum, LocalHotelBreakfastTypeEnum, LocalHotelBedTypeEnum } from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { vShow } from 'vue';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { defineProps } from 'vue';

const props = defineProps<{
  roomList: Array<any>;
  checkIn: string;
  checkOut: string;
}>();

const showCalendar = ref(false)
const currentIndex = ref<number>(-1)
const showRoomDayPrices = (index: number) => {
  currentIndex.value = index
  showCalendar.value = true;
}

const formatter = (day) => {
  if (day.type == 'middle' || day.type === 'start' || day.type === 'end') {
    props.roomList[currentIndex.value].dayPrices.forEach(item => {
      if (new Date(`${item.dateTime} 00:00:00`).getTime() == day.date.getTime()) {
        day.bottomInfo = `¥${item.individualPrice / 100}`
      }

    })
  }

  if (day.type === 'start') {
    day.topInfo = '入住';
  } else if (day.type === 'end') {
    day.topInfo = '离店';
    day.bottomInfo = '退房';
  }
  return day;
};

</script>

<style lang='less' scoped>
@import url(./common.less);
</style>