import {
    Result,
    ILifeRequest,
    IPageResponse,
    ILifeResponse,
    ILifeAccount
} from '@haierbusiness-front/common-libs'
import { get, post } from '../request'
export const lifeListApi ={
    list: (params: ILifeRequest): Promise<IPageResponse<ILifeResponse>> => {
        return get("/portal/api/admin-api/banner/park-life/page", params)
    },
    get: (id: number): Promise<ILifeResponse> => {
        return get("/portal/api/admin-api/banner/park-life/get", {
            id
        })
    },
    detail: (id: number): Promise<ILifeAccount> => {
        return get('/portal/api/app-api/banner/park-life/get', { id })
    },
    life: (params: ILifeRequest): Promise<ILifeResponse[]> => {
        return get("/portal/api/app-api/banner/park-life/list", params)
    },
    save: (params: ILifeRequest): Promise<Result> => {
        return post('/portal/api/admin-api/banner/park-life/create', params)
    },
    edit: (params: ILifeRequest): Promise<Result> => {
        return post('/portal/api/admin-api/banner/park-life/update', params)
    },
    remove: (id: number): Promise<Result> => {
        return get('/portal/api/admin-api/banner/park-life/delete', { id })
    },
    onTimeChange : (dateRange: string[]) => {
        const times:string[] = []
        times.push(dateRange[0] + ' 00:00:00')
        times.push(dateRange[1] + ' 23:59:59')
        return times
        // searchKey.createTime = times
    }
 
}