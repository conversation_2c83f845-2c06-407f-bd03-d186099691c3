<template>
  <div class="order-log">
    <!-- <van-nav-bar :fixed="true" title="相册" left-arrow>
      <template #left>
        <van-icon name="arrow-left" color="#000" @click="goBack" />
      </template>
    </van-nav-bar> -->


    <van-row style="padding: 0 2%" justify="space-between">
      <template v-if="imgList.length > 0">
        <van-image
          v-for="(img, index) in imgList"
          :key="index"
          @click="showPreview(index)"
          class="mb-5"
          width="49%"
          style="height: 200px"
          fit="contain"
          :src="`${businessList}api/common/v1/file/download/${img.photoId || img.id}`"
        />
      </template>
      <div class="width100" v-else >
        <van-empty image="search" description="暂无数据" />
      </div>
    </van-row>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import type { Ref } from 'vue';

import { localHotelApi } from '@haierbusiness-front/apis';

import {
  RHotelParams,
  RpayType,
  ROrderPayMentStateEnum,
  ROrderPayMentStateEnumColor,
  ROrderApprovalStateEnum,
  ROrderApprovalStateEnumColor,
  ROrderStateEnum,
  ROrderStateEnumColor,
  RHotel,
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { Item } from 'ant-design-vue/es/menu';
const router = getCurrentRouter();

const route = ref(getCurrentRoute());

const hotelId = route.value?.query?.hotelId;
const type = route.value?.query?.type;


const businessList = import.meta.env.VITE_BUSINESSTRAVEL_URL;

import { showImagePreview } from 'vant';

const detail = ref<RHotel>();

const album = ref({
  room: [],
  restaurant: [],
  food: [],
});

const imgList = ref([]);
const getDetail = (hotelId: string) => {
  const params = {
    hotelId,
  };

  localHotelApi.hotelSingle(params).then((res) => {

    const data = {
      hotelId,
      includeEdit: 0,
      pageNum: 1,
      pageSize: 999,
    }
    imgList.value = res.data.album
   
  });
};


const showPreview = (index: number) => {
  showImagePreview({
    images: imgList.value.map((item) => `${businessList}api/common/v1/file/download/${item.photoId || item.id }`),
    startPosition: index,
  });
};

const goBack = () => {
  router.back(-1);
};

watch(
  () => hotelId,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);


</script>

<style lang="less" scoped>
@import url(./common.less);
.order-log {
  padding: 20px 0 20px;
  min-height: 100vh;
}
.short-cell {
  :deep(.van-cell__title) {
    flex: none;
    width: 60px;
  }
}
.my-radio {
  :deep(.van-radio__icon) {
    height: auto !important;
  }
}
.btn-com {
  width: 70px;
}
</style>