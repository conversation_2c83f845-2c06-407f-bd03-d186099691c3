import { RouteRecordRaw } from 'vue-router';

const modules = import.meta.glob('/src/page/**/*.vue');

import { baseRouterConstructor } from '@haierbusiness-front/utils';

const routes = [{
    path: '/',
    redirect: '/banquet/home'
},
{
    path: '/banquet',
    redirect: '/banquet/home',
    component: () => import('../page/index.vue'),
    children: [
        // 首页
        {
            path: '/banquet/home',
            name: 'home',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },

            component: () => import('../page/banquet/mobile/home.vue'),
        },
        // 我的
        {
            path: '/banquet/mine',
            name: 'mine',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },

            component: () => import('../page/banquet/mobile/mine.vue'),
        },
        // 通知通告
        {
            path: '/banquet/news/list',
            name: 'newsList',
            meta: {
                keepAlive: true //设置页面是否需要使用缓存
            },

            component: () => import('../page/banquet/mobile/news/list.vue'),
        },
        {
            path: '/banquet/news/detail',
            name: 'newsDetail',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },

            component: () => import('../page/banquet/mobile/news/detail.vue'),
        },
        // 申请单列表
        {
            path: '/banquet/apply/list',
            name: 'applyList',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },

            component: () => import('../page/banquet/mobile/apply/list.vue'),
        },
        {
            path: '/banquet/apply/detail',
            name: 'applyDetail',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },

            component: () => import('../page/banquet/mobile/apply/detail.vue'),
        },
        {
            path: '/banquet/apply/approval',
            name: 'applyApproval',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },

            component: () => import('../page/banquet/mobile/apply/approval.vue'),
        },
        // 退款订单列表
        {
            path: '/banquet/refund/list',
            name: 'refundList',
            meta: {
                keepAlive: true //设置页面是否需要使用缓存
            },

            component: () => import('../page/banquet/mobile/refund/list.vue'),
        },
        // 预订单列表
        {
            path: '/banquet/reservation/list',
            name: 'reservationList',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },

            component: () => import('../page/banquet/mobile/reservation/list.vue'),
        },
        {
            path: '/banquet/reservation/detail',
            name: 'reservationDetail',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },

            component: () => import('../page/banquet/mobile/reservation/detail.vue'),
        },
        // 搜索页
        {
            path: '/banquet/search',
            name: 'search',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },

            component: () => import('../page/banquet/mobile/search.vue'),
        },
      
        // 申请单
        {
            path: '/banquet/book',
            name: 'book',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },
        
            component: () => import('../page/banquet/mobile/book.vue'),
        },
    ]
},


];
// const router = baseRouterConstructor("haierbusiness-portalIndex", modules, flag, undefined, routes)
const router = baseRouterConstructor("haierbusiness-banquet", modules, true, undefined, routes)

export default router;
