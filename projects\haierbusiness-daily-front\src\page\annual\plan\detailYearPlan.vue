<script setup lang="ts">
import {
  Modal,
  Tag as hTag,
  Card as hCard,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  But<PERSON> as hButton,
  Col as hCol,
  Row as hRow,
  Spin as hSpin,
  Table as hTable,
  DatePicker as hDatePicker,
  CollapsePanel as hCollapsePanel,
  Collapse as hCollapse,
  FormInstance,
  message,
  TableColumnsType,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { PlusOutlined, SearchOutlined, DownOutlined, OrderedListOutlined } from '@ant-design/icons-vue';
import {
  AnnualPlanTypeStateConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
  IHaierAccountBillInfo,
  UserGroupSystemConstant,
} from '@haierbusiness-front/common-libs';
import { checkUserGroup, getCurrentRouter, resolveParam } from '@haierbusiness-front/utils';
import { ref, createVNode, PropType, computed } from 'vue';

import dayjs, { Dayjs } from 'dayjs';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import {
AnnualPlanItemStateConstant,
  AnnualPlanStateConstant,
  EvaluateControlConstant,
  IAnnualPlanDetailRequestDTO,
  IAnnualPlanDetailResponseDTO,
  IAnnualPlanItemDetailResponseDTO,
  IAnnualPlanTypeListRequest,
  PlanTypeConstant,
} from '@haierbusiness-front/common-libs/src/daily';
import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
import detailYearDecompose from './detailYearDecompose.vue';
import { cloneDeep } from 'lodash-es';
import { MonthPlanItemStateConstant } from '@haierbusiness-front/common-libs/src/daily/constant/monthPlanItemStateConstant';
const { loginUser } = storeToRefs(applicationStore(globalPinia));

const router = getCurrentRouter();
const prop = defineProps({
  data: Object as PropType<IAnnualPlanDetailResponseDTO>,
  showTemp: Boolean as PropType<Boolean>,
  month: Number as PropType<Number>,
  fold: Boolean as PropType<Boolean>,
});

const annualPlanDetails = computed(() => {
  let result: any[] = [];
  prop.data?.annualPlanItems?.map((it) => {
    let maxLength = 0;
    for (const key of Object.keys(it.monthPlanEnterItems as any)) {
      const values = (it.monthPlanEnterItems as unknown as [])?.[key as unknown as number] as [];
      if (values.length > maxLength) {
        maxLength = values.length;
      }
    }

    for (let j = 0; j < maxLength; j++) {
      const nit = cloneDeep(it);
      if (j === 0) {
        nit.rowSpan = maxLength;
      } else {
        nit.rowSpan = 0;
      }
      for (let i = 1; i <= 12; i++) {
        if (nit.monthPlanEnterItems[i][j]) {
          const stateKey = 'month_' + i + '_state';
          (nit as any)[stateKey] = nit.monthPlanEnterItems[i][j]['state'];

          const planDescKey = 'month_' + i + '_planDesc';
          (nit as any)[planDescKey] = nit.monthPlanEnterItems[i][j]['planDesc'];

          const planUnitKey = 'month_' + i + '_planUnit';
          (nit as any)[planUnitKey] = nit.monthPlanEnterItems[i][j]['planUnit'];

          const planValueKey = 'month_' + i + '_planValue';
          (nit as any)[planValueKey] = nit.monthPlanEnterItems[i][j]['planValue'];

          const typeNameKey = 'month_' + i + '_typeName';
          (nit as any)[typeNameKey] = nit.monthPlanEnterItems[i][j]['typeName'];

          const completePlanTimeKey = 'month_' + i + '_completePlanTime';
          (nit as any)[completePlanTimeKey] = nit.monthPlanEnterItems[i][j]['completePlanTime'];

          const completePlanValueKey = 'month_' + i + '_completePlanValue';
          (nit as any)[completePlanValueKey] = nit.monthPlanEnterItems[i][j]['completePlanValue'];

          const completePlanDescKey = 'month_' + i + '_completePlanDesc';
          (nit as any)[completePlanDescKey] = nit.monthPlanEnterItems[i][j]['completePlanDesc'];

          const completeRateKey = 'month_' + i + '_completeRate';
          (nit as any)[completeRateKey] = nit.monthPlanEnterItems[i][j]['completeRate'];

          const principalUsercodeKey = 'month_' + i + '_principalUsercode';
          (nit as any)[principalUsercodeKey] = nit.monthPlanEnterItems[i][j]['principalUsercode'];

          const principalUsernameKey = 'month_' + i + '_principalUsername';
          (nit as any)[principalUsernameKey] = nit.monthPlanEnterItems[i][j]['principalUsername'];

          const principalEvaluateAmount = 'month_' + i + '_evaluateAmount';
          (nit as any)[principalEvaluateAmount] = nit.monthPlanEnterItems[i][j]['evaluateAmount'];

          const principalEvaluateRemark = 'month_' + i + '_evaluateRemark';
          (nit as any)[principalEvaluateRemark] = nit.monthPlanEnterItems[i][j]['evaluateRemark'];
        }
      }
      result.push(nit);
    }
  });
  if (!prop.showTemp) {
    result = result.filter((it) => !it.temp);
  }
  return result;
});
const collapseActiveKey = ref([1]);
{
  if (prop?.fold) {
    collapseActiveKey.value = [];
  }
}
const showMicroEvaluate = (i: number) => {
  if (
    checkUserGroup(UserGroupSystemConstant.DAILY_PLATFORM_MANAGER.groupId) ||
    (checkUserGroup(UserGroupSystemConstant.DAILY_MICRO.groupId) &&
      prop.data?.deptCode === loginUser.value?.departmentCode)
  ) {
    return [
      {
        title: '评价金额',
        dataIndex: 'evaluateAmount',
        width: '200px',
        align: 'center',
        ellipsis: true,
        customRender: (record: any) => {
          const value = record?.record?.['month_' + i + '_evaluateAmount'];
          return value;
        },
      },
      {
        title: '评价备注',
        dataIndex: 'evaluateRemark',
        width: '320px',
        align: 'center',
        ellipsis: true,
        customRender: (record: any) => {
          const value = record?.record?.['month_' + i + '_evaluateRemark'];
          return value;
        },
      },
    ];
  } else {
    return [];
  }
};
const monthGenerateColumn = () => {
  const result: any[] = [];
  for (let i = 1; i <= 12; i++) {
    if (prop?.month && i !== prop?.month) {
      continue;
    }
    result.push({
      title: i + '月',
      align: 'center',
      children: [
        {
          title: '月度目标及达成路径',
          dataIndex: 'month_' + i + '_planResult',
          key: 'month_' + i + '_planResult',
          width: 240,
          align: 'center',
          customRender: (record: any) => {
            const pre = 'month_' + i + '_plan';
            const value = record?.record?.[pre + 'Value'];
            const unit = record?.record?.[pre + 'Unit'];
            const desc = record?.record?.[pre + 'Desc'];
            if (record.record.planType === PlanTypeConstant.QUANTIFY.code) {
              if (value && unit) {
                return value + ' / ' + unit;
              } else {
                return '';
              }
            } else if (record.record.planType === PlanTypeConstant.QUALITATIVE.code) {
              return desc;
            } else {
              return '未定义';
            }
          },
        },
        {
          title: '计划类型',
          dataIndex: 'month_' + i + '_typeName',
          key: 'month_' + i + '_typeName',
          width: 200,
          align: 'center',
        },
        {
          title: '计划状态',
          dataIndex: 'month_' + i + '_state',
          key: 'month_' + i + '_state',
          width: 200,
          align: 'center',
          customRender: (record: any) => {
            const state = record?.record?.['month_' + i + '_state'];
            return MonthPlanItemStateConstant.ofCode(state)?.desc;
          },
        },
        {
          title: '实际完成时间',
          dataIndex: 'month_' + i + '_completePlanTime',
          key: 'month_' + i + '_completePlanTime',
          width: 200,
          align: 'center',
        },
        {
          title: '完成效果',
          dataIndex: 'month_' + i + '_completePlanResult',
          key: 'month_' + i + '_completePlanResult',
          width: 200,
          align: 'center',
          customRender: (record: any) => {
            const pre = 'month_' + i + '_complete';
            const value = record?.record?.[pre + 'PlanValue'];
            const unit = record?.record?.['planUnit'];
            const rate = record?.record?.[pre + 'Rate'];
            const desc = record?.record?.[pre + 'PlanDesc'];
            if (record.record.planType === PlanTypeConstant.QUANTIFY.code) {
              if (value && unit) {
                return value + ' / ' + unit + ' -- ' + (rate / 0.01).toFixed(2) + '%';
              } else {
                return '';
              }
            } else if (record.record.planType === PlanTypeConstant.QUALITATIVE.code) {
              return desc;
            } else {
              return '未定义';
            }
          },
        },
        {
          title: '责任人',
          dataIndex: 'month_' + i + '_principalUsername',
          key: 'month_' + i + '_principalUsername',
          width: 200,
          align: 'center',
        },
        ...showMicroEvaluate(i),
      ],
    });
  }
  return result;
};

const columns: TableColumnsType = [
  {
    title: '操作',
    dataIndex: '_operator',
    key: '_operator',
    width: 120,
    fixed: 'left',
    align: 'center',
    ellipsis: true,
    customCell: (record) => {
      return { rowSpan: record.rowSpan };
    },
  },
  {
    title: '年度项目',
    dataIndex: 'name',
    key: 'name',
    width: 240,
    fixed: 'left',
    align: 'center',
    ellipsis: true,
    customCell: (record) => {
      return { rowSpan: record.rowSpan };
    },
  },
  {
    title: '项目分类',
    dataIndex: 'typeName',
    key: 'typeName',
    width: 120,
    align: 'center',
    ellipsis: true,
    customCell: (record) => {
      return { rowSpan: record.rowSpan };
    },
  },
  {
    title: '状态',
    dataIndex: 'state',
    key: 'state',
    width: 80,
    align: 'center',
    customCell: (record) => {
      return { rowSpan: record.rowSpan };
    },
    customRender: ({ value }) => {
      return AnnualPlanItemStateConstant.ofCode(value)?.desc;
    },
  },
  {
    title: '类型',
    dataIndex: 'planType',
    key: 'planType',
    width: 80,
    align: 'center',
    customCell: (record) => {
      return { rowSpan: record.rowSpan };
    },
    customRender: ({ value }) => {
      return PlanTypeConstant.ofCode(value)?.desc;
    },
  },
  {
    title: '项目年度目标',
    dataIndex: 'planResult',
    key: 'planResult',
    width: 200,
    align: 'center',
    customCell: (record) => {
      return { rowSpan: record.rowSpan };
    },
    customRender: ({ record }) => {
      if (record.planType === PlanTypeConstant.QUANTIFY.code) {
        return record.planValue + ' / ' + record.planUnit;
      } else if (record.planType === PlanTypeConstant.QUALITATIVE.code) {
        return record.planDesc;
      } else {
        return '未定义';
      }
    },
  },
  {
    title: '月度分解',
    align: 'center',
    children: monthGenerateColumn(),
  },
];
const monthGenerateFlag = ref(false);
const monthGenerateTitle = ref('');
const monthGenerateData = ref<IAnnualPlanItemDetailResponseDTO>({});
const showMonthGenerate = (record: IAnnualPlanItemDetailResponseDTO) => {
  monthGenerateData.value = record;
  monthGenerateFlag.value = true;
  monthGenerateTitle.value = '项目: [' + record.name + '] 月度分解';
};
</script>
<template>
  <h-modal v-model:open="monthGenerateFlag" :title="monthGenerateTitle">
    <template #footer>
      <a-button key="submit" type="primary" @click="monthGenerateFlag = false">关闭</a-button>
    </template>
    <detail-year-decompose :data="monthGenerateData" :month="prop?.month"></detail-year-decompose>
  </h-modal>
  <h-collapse
    v-model:activeKey="collapseActiveKey"
    :bordered="false"
    style="background-color: white"
    :collapsible="'icon'"
  >
    <h-collapse-panel key="1">
      <template #header>
        <div style="display: flex">
          <div style="height: 25px; width: 10px; background-color: rgb(0, 115, 229)" />
          <div style="font-size: 16px; font-weight: 600; margin-left: 10px">年度计划</div>
        </div>
      </template>
      <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%"></div>
      <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
        <h-table
          :columns="columns"
          :data-source="annualPlanDetails"
          bordered
          size="small"
          :rowKey="(record) => record.id"
          :scroll="{ x: 'calc(700px + 50%)', y: 400 }"
          :pagination="false"
        >
          <template #bodyCell="{ text, column, record }">
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="showMonthGenerate(record)">
                <template #icon>
                  <OrderedListOutlined />
                </template>
                构成
              </h-button>
            </template>
          </template>
        </h-table>
      </div>
    </h-collapse-panel>
  </h-collapse>
</template>

<style scoped lang="less">
@import '../../../assets/css/main.less';
</style>
