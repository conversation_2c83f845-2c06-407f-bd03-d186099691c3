<script setup lang="ts">
import { DatePicker as hDatePicker,Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem, Input as hInput, Table as hTable, BreadcrumbItem as hBreadcrumbItem, Breadcrumb as hBreadcrumb, LayoutSider as hLayoutSider, LayoutFooter as hLayoutFooter, LayoutContent as hLayoutContent, LayoutHeader as hLayoutHeader, Layout as hLayout, MenuItem as hMenuItem, MenuItemGroup as hMenuItemGroup, SubMenu as hSubMenu, Menu as hMenu, Divider as hDivider, Space as hSpace, Button as hButton, Col as hCol, Result as hResult, Row as hRow, TabPane as hTabPane, Tabs as hTabs, message, TableProps } from 'ant-design-vue';
import { computed, onMounted, ref } from 'vue';
import { UploadOutlined, UserOutlined, NotificationOutlined, AppstoreOutlined, MenuFoldOutlined, MenuUnfoldOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { userApi, enterpriseApi } from '@haierbusiness-front/apis';
import { usePagination, useRequest } from 'vue-request';
import { IUserSaveUpdateRequest, IUserListRequest } from '@haierbusiness-front/common-libs';
import { useDelete } from '@haierbusiness-front/composables'

const columns = [
  {
    title: '企业',
    dataIndex: 'enterprise',
    width: '10%',
  },
  {
    title: '最后登录时间',
    dataIndex: 'lastLoginTime',
    width: '10%',
  },
  {
    title: '账号',
    dataIndex: 'username',
    width: '10%',
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    width: '10%',
  },
  {
    title: '昵称',
    dataIndex: 'nickName',
    width: '10%',
  },
  {
    title: '性别',
    dataIndex: '_genderName',
    width: '5%',
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '5%',
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    width: '10%',
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '10%',
  },
  {
    title: '编辑',
    dataIndex: 'operator',
    width: '10%',
  },
];

const searchParam = ref<IUserListRequest>({ })

const reset = () => {
  searchParam.value = {}
}

const {
  data,
  run: userApiRun,
  loading,
  current,
  pageSize,
} = usePagination(userApi.list);

const {
  data: enterprises,
  run: userListApiRun
} = useRequest(enterpriseApi.list, {
  manual: false
});

const enterpriseSelect = computed(() => {
  return enterprises.value || []
})

const dataSource = computed(() => data.value?.records || []);
const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  userApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const enterpriseCodeChange = (value: number) => {
  const enterprise = enterpriseSelect.value.find(o => o.id === value)
  if(enterprise) {
    newItemForm.value.enterpriseName = enterprise.name
  }
}

// 编辑
const handleEdit = (value: IUserSaveUpdateRequest) => {
  visibleNew.value = true
  newItemForm.value = value
}

// 新增相关
const gotoSave = () => {
  visibleNew.value = true
  newItemForm.value = {}
}


// 新增表单相关
const userNewForm = ref()
const confirmLoading = ref(false)
const visibleNew = ref(false)
const newItemForm = ref<IUserSaveUpdateRequest>({})
const labelCol = { span: 5 }
const wrapperCol = { span: 14 }
const handleOk = () => {
  userNewForm.value.validate().then(
    () => {
      confirmLoading.value = true;
      userApi.save(
        newItemForm.value
      ).then(it => {
        message.success("操作成功！");
        visibleNew.value = false;
        handleTableChange({ current: 1, pageSize: 10 })
        newItemForm.value = {}
      }).finally(() => {
        confirmLoading.value = false;
      });
    }
  )
}


// 删除
// const { handleDelete } = useDelete(userApi, () => userApiRun({
//   ...searchParam.value,
//   pageNum: data.value?.pageNum,
//   pageSize: data.value?.pageSize,
// }))

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-modal v-model:visible="visibleNew" :title="'新增用户'" :confirm-loading="confirmLoading" @ok="handleOk">
      <h-form ref="userNewForm" :model="newItemForm" :label-col="labelCol" :wrapper-col="wrapperCol">
        <h-form-item label="用户名" name="username" :rules="[{ required: true, message: '请输入用户名!' }]">
          <h-input v-model:value="newItemForm.username" />
        </h-form-item>
        <h-form-item label="密码" name="password" :rules="[{ required: true, message: '请输入密码!' }]">
          <h-input v-model:value="newItemForm.password" />
        </h-form-item>
        <h-form-item label="昵称" name="nickName" :rules="[{ required: true, message: '请输入昵称!' }]">
          <h-input v-model:value="newItemForm.nickName" />
        </h-form-item>
        <h-form-item label="手机号" name="phone" :rules="[{ required: true, message: '请输入手机号!' }]">
          <h-input v-model:value="newItemForm.phone" />
        </h-form-item>
        <h-form-item label="邮箱" name="email">
          <h-input v-model:value="newItemForm.email" />
        </h-form-item>
        <h-form-item label="性别" name="gender" :rules="[{ required: true, message: '请选择性别!' }]">
          <h-select ref="select" v-model:value="newItemForm.gender" >
            <h-select-option :value="1">男</h-select-option>
            <h-select-option :value="0">女</h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="企业" name="enterpriseCode" :rules="[{ required: true, message: '请选择企业!' }]">
          <h-select ref="select" v-model:value="newItemForm.enterpriseCode" @change="(value) =>  enterpriseCodeChange(value as number)">
            <h-select-option v-for="(item, index) in enterpriseSelect" :value="item.id" :key="index">{{ item.name }}</h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="状态" name="state" :rules="[{ required: true, message: '请选择状态!' }]">
          <h-select ref="select" v-model:value="newItemForm.state" style="width: 100%;" allow-clear >
            <h-select-option :value="0" >可用</h-select-option>
            <h-select-option :value="1" >禁用</h-select-option>
            <h-select-option :value="2" >密码错误过多禁用</h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="过期时间" name="expiredTime" >
          <h-date-picker v-model:value="newItemForm.expiredTime" />
        </h-form-item>
      </h-form>
    </h-modal>
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="enterpriseCode">企业：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.enterpriseCode" style="width: 100%;" allow-clear >
              <h-select-option v-for="(item, index) in enterpriseSelect" :value="item.id" :key="index">{{ item.name }}</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="username">账号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="username" v-model:value="searchParam.username" placeholder="" autocomplete="off" />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="nickName">姓名：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="nickName" v-model:value="searchParam.nickName" placeholder="" autocomplete="off" />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchState">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.state" style="width: 100%;" allow-clear >
              <h-select-option :value="0" >可用</h-select-option>
              <h-select-option :value="1" >禁用</h-select-option>
              <h-select-option :value="2" >密码错误过多禁用</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :offset="20" :span="4" style="text-align: right;">
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined /> 查询
            </h-button>
            <h-button style="margin-left: 10px" @click="reset">重置</h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: left;">
            <h-button type="primary" @click="gotoSave">
              <PlusOutlined /> 新增用户
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :data-source="dataSource" :pagination="pagination"
          :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'enterprise' && record.enterpriseCode">
              {{ record.enterpriseName }}({{ record.enterpriseCode }})
            </template>
            <template v-if="column.dataIndex === 'state'">
              {{ record._stateName }}
            </template>
            <template v-if="column.dataIndex === 'gmtCreate'">
              {{ record.gmtCreate }}
            </template>
            <template v-if="column.dataIndex === 'operator'">
              <h-button type="link"  @click="handleEdit(record)">编辑</h-button>
              <!-- <h-button type="link"  @click="handleDelete(record.id)">删除</h-button> -->
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}
</style>
