<!-- FileUpload.vue -->
<template>
  <h-upload
    v-model:file-list="fileList"
    :list-type="listType"
    :max-count="maxCount"
    :custom-request="upload"
    :before-upload="beforeUpload"
    @preview="handlePreview"
    @remove="(file: any) => handleRemove(file)"
  >
    <h-button v-if="fileList.length < 1">
      <upload-outlined></upload-outlined>
      上传附件
    </h-button>
  </h-upload>
  <slot></slot>
</template>

<script setup lang="ts">
import { UploadOutlined } from '@ant-design/icons-vue'
import { ref } from 'vue'
import { Button as hButton, message, Upload as hUpload } from 'ant-design-vue'
import { fileApi } from '@haierbusiness-front/apis'

// 定义组件属性
interface Props {
  maxCount?: number // 最大上传数量
  maxSize?: number // 最大上传大小
  fileType?: string[]
  listType?: string
}

const props = withDefaults(defineProps<Props>(), {
  maxCount: 1,
  maxSize: 5,
  fileType: () => [],
  listType: 'text',
})
const emit = defineEmits<{
  (e: 'change', value: any[]): void
}>()

const baseUrl = import.meta.env.VITE_BUSINESS_URL
const fileList = ref<Array<any>>([])
const fileUrl = ref<Array<any>>([])
const loading = ref(false)

const upload = async (options: any) => {
  loading.value = true
  const formData = new FormData()
  formData.append('file', options.file)
  const res = await fileApi.upload(formData)
  const file = {
    ...options.file,
    name: options.file.name,
    url: baseUrl + res.path,
  }
  loading.value = false
  fileUrl.value = [...fileUrl.value, file]
  emit('change', fileUrl.value)

  options.onProgress(100)
  options.onSuccess(res, options.file) //这里必须加上这个，不然会一直显示一个正在上传中的框框
}

const beforeUpload = (file: any) => {
  const isJpgOrPng =
    file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg'

  if (!isJpgOrPng) {
    message.error('只能上传格式为png/jpg/jpeg的文件！')
    return false
  }

  if (file.size / 1024 / 1024 > props.maxSize) {
    message.error(`图片大小不能超过${props.maxSize}MB`)
    return false
  }

  return true
}

const handleRemove = (file: any) => {
  fileUrl.value = fileUrl.value.filter((item: any) => item.uid !== file.uid)
  emit('change', fileUrl.value)
}

const handlePreview = async (file: any) => {
  let item = fileUrl.value.find((item: any) => item.uid == file.uid)
  item && window.open(item.url)
}

const setFileList = (urls: any[]) => {
  fileUrl.value = urls.map((e, i) => {
    let uid = `${Date.now()}-${i}`
    return {
      uid,
      name: uid,
      url: e,
    }
  })
  fileList.value = fileUrl.value
}

defineExpose({
  setFileList,
})
</script>

<style scoped>
.imgShow {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-tips {
  margin-top: 8px;
  color: #999;
  font-size: 12px;
}

.important {
  color: red;
  font-weight: bold;
  padding: 0 2px;
}
</style>
