<script lang="ts" setup>
import { inject } from 'vue';
import { useRoute } from 'vue-router';
import { resolveParam } from '@haierbusiness-front/utils';

// 后台-订单列表
import OrderList from '@haierbusiness-front/components/mice/orderList/index.vue';

const route = useRoute();
const record = resolveParam(route.query.record);
const hideBtn = record?.hideBtn || '';

const frameModel = inject<any>('frameModel');
frameModel.value = hideBtn === '1' ? 1 : 0;
</script>
<template>
  <div class="mice-bid-order-list">
    <OrderList type="manage" />
  </div>
</template>
<style lang="less" scoped></style>
