<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  Textarea as hTextarea,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
  Modal
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { ExclamationCircleFilled, DownOutlined, ExclamationCircleOutlined, PlusOutlined, UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { applicationApi } from '@haierbusiness-front/apis';
import {
  IApplicationInfoRequest,
  IApplicationInfo,
  LoginTypeConstant,
  IApplicationInfoSaveRequest,
  IApplicationInfoUpdateRequest
} from '@haierbusiness-front/common-libs';
import { errorModal, routerParam } from '@haierbusiness-front/utils';
import Eloading from '@haierbusiness-front/components/loading/Eloading.vue';
import { computed, createVNode, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useRouter } from "vue-router";

const router = useRouter()
const columns: ColumnType[] = [
  {
    title: '应用编码',
    dataIndex: 'applicationCode',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '应用名称',
    dataIndex: 'applicationName',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '描述',
    dataIndex: 'description',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '公钥',
    dataIndex: 'secret',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '海尔应用编码(S码)',
    dataIndex: 'haierApplicationCode',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '默认登陆类型',
    dataIndex: 'loginType',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '180px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<IApplicationInfoRequest>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(applicationApi.list);


const reset = () => {
  searchParam.value = {}
}

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

// 修改相关
const updateApplicationForm = ref<IApplicationInfoUpdateRequest>({})

const gotoUpdate = (data: IApplicationInfo) => {
  visibleUpdate.value = true
  updateApplicationForm.value.id = data.id
  updateApplicationForm.value.applicationCode = data.applicationCode
  updateApplicationForm.value.applicationName = data.applicationName
  updateApplicationForm.value.resetSecret = false
  updateApplicationForm.value.description = data.description
  updateApplicationForm.value.state = data.state
  updateApplicationForm.value.haierApplicationCode = data.haierApplicationCode
  updateApplicationForm.value.loginType = data.loginType
  updateApplicationForm.value.type = data.type
}
const accountUpdateForm = ref()
const confirmUpdateLoading = ref(false)
const visibleUpdate = ref(false)

const updateLabelCol = { span: 8 }
const updateWrapperCol = { span: 12 }
const handleUpdateOk = () => {
  accountUpdateForm.value.validate().then(
    () => {
      confirmUpdateLoading.value = true;
      applicationApi.update(
        updateApplicationForm.value
      ).then(it => {
        updateApplicationForm.value = {}
        handleTableChange({ current: 1, pageSize: pageSize.value | 10 })
        message.success("修改应用成功!")
      }).finally(() => {
        setTimeout(() => {
          confirmUpdateLoading.value = false;
          visibleUpdate.value = false;
        }, 100);
      });
    }
  )

}

// 新增相关
const gotoNew = () => {
  visibleNew.value = true
}
const accountNewForm = ref()
const confirmLoading = ref(false)
const visibleNew = ref(false)
const newApplicationForm = ref<IApplicationInfoSaveRequest>({})
const labelCol = { span: 8 }
const wrapperCol = { span: 12 }
const handleOk = () => {
  Modal.confirm({
    title: '确认新增应用',
    icon: createVNode(ExclamationCircleFilled),
    content: createVNode('div', { style: 'color:red;' }, '新增应用后,将生成应用私钥, 请复制并妥善保管!'),
    onOk() {
      accountNewForm.value.validate().then(
        () => {
          confirmLoading.value = true;
          applicationApi.save(
            newApplicationForm.value
          ).then(it => {
            newApplicationForm.value = {}
            handleTableChange({ current: 1, pageSize: pageSize.value | 10 })
            Modal.success({
              title: '新增应用成功!',
              content: createVNode('div', { style: 'color:red;' }, '当前应用私钥如下,请妥善保管(请保存后关闭对话框)!\n\n' + it),
              okText: "关闭"
            });
          }).finally(() => {
            setTimeout(() => {
              confirmLoading.value = false;
              visibleNew.value = false;
            }, 100);
          });
        }
      )
    },
  });
}

const showSecret = (data: IApplicationInfo) => {
  Modal.confirm({
    title: '应用公钥',
    content: data.secret,
    okText: "关闭",
    cancelText: "重置秘钥对",
    cancelButtonProps: { danger: true },
    onCancel() {
      Modal.confirm({
        title: '确认重置秘钥对?',
        icon: createVNode(ExclamationCircleFilled),
        content: createVNode('div', { style: 'color:red;' }, '重置后将生成应用私钥, 请复制并妥善保管!'),
        onOk() {
          return applicationApi.update({
            id: data.id,
            applicationCode: data.applicationCode,
            applicationName: data.applicationName,
            resetSecret: true,
            description: data.description,
            state: !data.state,
            haierApplicationCode: data.haierApplicationCode,
            loginType: data.loginType,
            type: data.type
          })
            .then(it => {
              Modal.success({
                title: '秘钥重置成功!',
                content: createVNode('div', { style: 'color:red;' }, '当前应用新私钥如下,请妥善保管(请保存后关闭对话框)!\n\n' + it),
                okText: "关闭"
              });
              handleTableChange({ current: current.value, pageSize: pageSize.value })
            })
        }
      });
    },
  });
}

const changeState = (data: IApplicationInfo) => {
  return applicationApi.update({
    id: data.id,
    applicationCode: data.applicationCode,
    applicationName: data.applicationName,
    resetSecret: false,
    description: data.description,
    state: !data.state,
    haierApplicationCode: data.haierApplicationCode,
    loginType: data.loginType,
    type: data.type
  })
    .then(it => {
      message.success("操作成功")
      handleTableChange({ current: current.value, pageSize: pageSize.value })
    })
}

const deleteApplication = (data: IApplicationInfo) => {
  return applicationApi.delete({ id: data.id })
    .then(it => {
      message.success("操作成功")
      handleTableChange({ current: current.value, pageSize: pageSize.value })
    })
}
</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <!-- 新增应用弹窗开始 -->
    <h-modal v-model:open="visibleNew" :title="'新增应用'" :confirm-loading="confirmLoading" @ok="handleOk">
      <h-form ref="accountNewForm" :model="newApplicationForm" :label-col="labelCol" :wrapper-col="wrapperCol">
        <h-form-item label="应用编码" name="applicationCode" :rules="[{ required: true, message: '应用编码不能为空!' }]">
          <h-input v-model:value="newApplicationForm.applicationCode" />
        </h-form-item>
        <h-form-item label="应用名称" name="applicationName" :rules="[{ required: true, message: '应用名称不能为空!' }]">
          <h-input v-model:value="newApplicationForm.applicationName" />
        </h-form-item>
        <h-form-item label="默认登陆类型" name="loginType" :rules="[{ required: true, message: '默认登陆类型不能为空!' }]">
          <h-select ref="select" v-model:value="newApplicationForm.loginType" style="width: 100%" allow-clear>
            <h-select-option :value="LoginTypeConstant.LOCAL.key">{{
              LoginTypeConstant.LOCAL.name
            }}
            </h-select-option>
            <h-select-option :value="LoginTypeConstant.IAM_CODE.key">{{
              LoginTypeConstant.IAM_CODE.name
            }}
            </h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="应用类型" name="type" :rules="[{ required: true, message: '应用类型不能为空!' }]">
          <h-select ref="select" v-model:value="newApplicationForm.type" style="width: 100%" allow-clear>
            <h-select-option :value="1">业务系统</h-select-option>
            <h-select-option :value="2">基础系统</h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="海尔应用S码" name="haierApplicationCode" :rules="[{ required: true, message: '海尔应用S码不能为空!' }]">
          <h-input v-model:value="newApplicationForm.haierApplicationCode" />
        </h-form-item>
        <h-form-item label="描述" name="description">
          <h-textarea v-model:value="newApplicationForm.description" />
        </h-form-item>
      </h-form>
    </h-modal>
    <!-- 新增应用弹窗结束 -->

    <!-- 修改应用弹窗开始 -->
    <h-modal v-model:open="visibleUpdate" :title="'修改应用'" :confirm-loading="confirmUpdateLoading" @ok="handleUpdateOk">
      <h-form ref="accountUpdateForm" :model="updateApplicationForm" :label-col="updateLabelCol"
        :wrapper-col="updateWrapperCol">
        <h-form-item label="应用编码" name="applicationCode" :rules="[{ required: true, message: '应用编码不能为空!' }]">
          <h-input v-model:value="updateApplicationForm.applicationCode" :disabled="true" />
        </h-form-item>
        <h-form-item label="应用名称" name="applicationName" :rules="[{ required: true, message: '应用名称不能为空!' }]">
          <h-input v-model:value="updateApplicationForm.applicationName" />
        </h-form-item>
        <h-form-item label="默认登陆类型" name="loginType" :rules="[{ required: true, message: '默认登陆类型不能为空!' }]">
          <h-select ref="select" v-model:value="updateApplicationForm.loginType" style="width: 100%" allow-clear>
            <h-select-option :value="LoginTypeConstant.LOCAL.key">{{
              LoginTypeConstant.LOCAL.name
            }}
            </h-select-option>
            <h-select-option :value="LoginTypeConstant.IAM_CODE.key">{{
              LoginTypeConstant.IAM_CODE.name
            }}
            </h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="应用类型" name="type" :rules="[{ required: true, message: '应用类型不能为空!' }]">
          <h-select ref="select" v-model:value="updateApplicationForm.type" style="width: 100%" allow-clear>
            <h-select-option :value="1">业务系统</h-select-option>
            <h-select-option :value="2">基础系统</h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="海尔应用S码" name="haierApplicationCode" :rules="[{ required: true, message: '海尔应用S码不能为空!' }]">
          <h-input v-model:value="updateApplicationForm.haierApplicationCode" />
        </h-form-item>
        <h-form-item label="描述" name="description">
          <h-textarea v-model:value="updateApplicationForm.description" />
        </h-form-item>
      </h-form>
    </h-modal>
    <!-- 修改应用弹窗结束 -->
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchAccountCode">应用编码：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="searchAccountCode" v-model:value="searchParam.applicationCode" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchState">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.state" style="width: 100%" allow-clear>
              <h-select-option :value="1">有效</h-select-option>
              <h-select-option :value="0">无效</h-select-option>
            </h-select>
          </h-col>
        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :offset="18" :span="6" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
            <h-button type="primary" @click="gotoNew">
              <PlusOutlined />
              新增应用
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'state'">
              <h-tag v-if="record.state === false" color="default">
                无效
              </h-tag>
              <h-tag v-if="record.state === true" color="success">
                有效
              </h-tag>
            </template>
            <template v-if="column.dataIndex === 'loginType'">
              <div v-if="record.loginType === LoginTypeConstant.LOCAL.key" color="default">
                {{ LoginTypeConstant.LOCAL.name }}
              </div>
              <div v-if="record.loginType === LoginTypeConstant.IAM_CODE.key" color="success">
                {{ LoginTypeConstant.IAM_CODE.name }}
              </div>
            </template>
            <template v-if="column.dataIndex === 'secret'">
              <template v-if="record.id !== 1">
                <h-button type="link" @click="showSecret(record)">************</h-button>
              </template>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <template v-if="record.id !== 1">
                <h-button type="link" @click="gotoUpdate(record)">修改</h-button>
                <h-popconfirm v-if="record.state === true" title="确认更改为无效状态?" @confirm="changeState(record)">
                  <h-button type="link">置无效</h-button>
                </h-popconfirm>
                <h-popconfirm v-if="record.state === false" title="确认更改为有效状态?" @confirm="changeState(record)">
                  <h-button type="link">置有效</h-button>
                </h-popconfirm>
                <h-popconfirm title="确认删除当前应用?" @confirm="deleteApplication(record)">
                  <h-button type="link">删除</h-button>
                </h-popconfirm>
              </template>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
