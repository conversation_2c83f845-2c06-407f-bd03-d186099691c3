<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, reactive, watch } from 'vue';
import { throttle } from 'lodash';
interface OffsetTopMap {
  [propName: string]: number;
}
const { offsetTop, items } = defineProps({
  items: {
    type: Array,
    required: true
  },
  offsetTop: {
    type: Number,
    default: 0,
  }
})
const emit = defineEmits(['change'])

const itemMap = reactive<any>({});
const offsetTopMap = reactive<OffsetTopMap>({});
const offsetTopList = computed(() => {
  return Object.entries(offsetTopMap).filter(item => item[1]).sort((a,b) => a[1] - b[1])
})
const activeKey = ref();

const handleSelectAnchor = (item: { key: string }) => {
  if (offsetTopMap[item.key]) {
    window.scrollTo({
      top: offsetTopMap[item.key] - offsetTop,
      behavior: 'smooth'
    })
  }
}
const getOffsetTop = (item: { href: string }) => {
  const id = item.href.slice(1);
  const targetElement = document.getElementById(id);
  if (targetElement) {
    return targetElement.offsetTop
  }
}
const onScroll = throttle(() => {
  const currentScroll = window.scrollY + offsetTop;
  const currentOffsetTopList = offsetTopList.value;
  if (currentOffsetTopList.length && currentScroll >= currentOffsetTopList[0][1]) {
    for (let i = 0; i < currentOffsetTopList.length; i++) {
      if (currentScroll >= currentOffsetTopList[i][1] && (!currentOffsetTopList[i + 1] || currentScroll < currentOffsetTopList[i + 1][1])) {
        activeKey.value = currentOffsetTopList[i][0];
        break;
      }
    }
  } else {
    activeKey.value = ''
  }
}, 200)

onMounted(() => {
  for (const item of items) {
    const offsetTop = getOffsetTop(item)
    itemMap[item.key] = item;
    offsetTop && (offsetTopMap[item.key] = offsetTop);
    if (!!item.children?.length) {
      for (const childItem of item.children) {
        const offsetTop = getOffsetTop(childItem)
        itemMap[childItem.key] = childItem;
        offsetTop && (offsetTopMap[childItem.key] = offsetTop)
      }
    }
  }
  window.addEventListener('scroll', onScroll)
})
onUnmounted(() => {
  window.removeEventListener('scroll', onScroll);
})
watch(activeKey, async (newValue, oldValue) => {
  emit('change', newValue && itemMap[newValue].href)
})
</script>

<template>
  <div class="mice-bid-anchor">
    <a-timeline>
      <template v-for="item of items" :key="item.key">
        <a-timeline-item>
          <span class="mice-bid-anchor-title" @click="handleSelectAnchor(item)" :style="{ color: activeKey === item.key ? '#1868DB' : '#595959' }">{{ item.title }}</span>
          <template #dot>
            <div v-if="activeKey === item.key" class="mice-bid-anchor-dot-active">
              <div class="mice-bid-anchor-dot-active-blank" />
            </div>
            <div v-else class="mice-bid-anchor-dot" />
          </template>
        </a-timeline-item>
        <template v-if="!!item.children?.length">
          <a-timeline-item v-for="childItem of item.children" :key="childItem.key">
            <span class="mice-bid-anchor-title" @click="handleSelectAnchor(childItem)" :style="{ color: activeKey === childItem.key ? '#1868DB' : '#595959', paddingLeft: '12px' }">{{ childItem.title }}</span>
            <template #dot>
              <div v-if="activeKey === childItem.key" class="mice-bid-anchor-dot-active">
                <div class="mice-bid-anchor-dot-active-blank" />
              </div>
              <div v-else class="mice-bid-anchor-dot" />
            </template>
          </a-timeline-item>
        </template>
      </template>
    </a-timeline>
  </div>
</template>

<style lang="less">
  .mice-bid-anchor {
    >.ant-timeline {
      >.ant-timeline-item {
        padding-bottom: 36px;
        &:last-child {
          padding-bottom: 0;
        }
        .ant-timeline-item-tail {
          inset-block-start: 8px;
          height: 100%;
        }
        .ant-timeline-item-head {
          padding: 0;
        }
      }
      >.ant-timeline-item-last {
        .ant-timeline-item-content {
          min-height: 0;
        }
      }
    }
    .mice-bid-anchor-title {
      cursor: pointer;
    }
    .mice-bid-anchor-dot {
      width: 7px;
      height: 7px;
      border-radius: 7px;
      background: #E5E6EB;
    }
    .mice-bid-anchor-dot-active {
      border-radius: 15px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 15px;
      height: 15px;
      background: #1868DB;
      .mice-bid-anchor-dot-active-blank {
        border-radius: 7px;
        width: 7px;
        height: 7px;
        background: #fff;
      }
    }
  }
</style>
