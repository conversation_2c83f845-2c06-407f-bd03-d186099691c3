<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  But<PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { payApi, lifeListApi } from '@haierbusiness-front/apis';
import {
  IPaymentVirtualAccount,
  IWyyB2bAccountRequest,
  IWyyB2bCancelRequest,
  IWyyB2bConfirmBalanceOrderBudgetRequest,
  IWyyB2bConfirmRequest,
  IWyyB2bRevokeConfirmRequest,
  ILifeRequest
} from '@haierbusiness-front/common-libs';
import { errorModal, routerParam } from '@haierbusiness-front/utils';
import Eloading from '@haierbusiness-front/components/loading/ELoading.vue';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useRouter } from "vue-router";
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import EditDialog from './edit-dialog.vue'
import router from '../../router'

// const router = useRouter()

const currentRouter = ref()

onMounted(async () => {
    currentRouter.value = await router
})

const columns: ColumnType[] = [
  { title: '标题', dataIndex: 'adSubject', align: 'center', },
  { title: '图片', dataIndex: 'imgUrl', align: 'center', },
  { title: '作者', dataIndex: 'author', align: 'center' },
  { title: '状态', dataIndex: 'showStatus', align: 'center', },
  { title: '是否置顶', dataIndex: 'isTopping', align: 'center', },
  { title: '创建时间', dataIndex: 'gmtCreate', align: 'center', },
  { title: "操作", dataIndex: "operation", align: 'center', },
]
const searchParam = ref<ILifeRequest>({
})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize
} = usePagination(lifeListApi.list);
const onTimeChange = (dateRange: string[]) => {
  const times: string[] = []
  times.push(dateRange[0] + ' 00:00:00')
  times.push(dateRange[1] + ' 23:59:59')
  return times
}

const reset = () => {
  searchParam.value = {}
}

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  console.log(searchParam.value, 1111)
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const advancedSearchVisible = ref(false)
const gotoAdvancedSearch = () => {
  if (advancedSearchVisible.value) {
    advancedSearchVisible.value = false
  } else {
    advancedSearchVisible.value = true
  }
}
const startBeginAndEnd = ref<[Dayjs, Dayjs]>()
const beginAndEnd = ref<[Dayjs, Dayjs]>()
const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } =
  useEditDialog<ILifeRequest, ILifeRequest>(lifeListApi, "园区生活", () => listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize,
  }))
const thisHandleEdit = (item: IPaymentVirtualAccount) => {
  const currentData = {
    ...item
  };
  handleEdit({ ...currentData });
}

const edit = (id?: number) => {
  currentRouter.value.push({ path: "/portal/life/list/edit", query: { id: id } })
}

// 删除
const { handleDelete } = useDelete(lifeListApi, () => listApiRun({
  ...searchParam.value,
  pageNum: data.value?.pageNum,
  pageSize: data.value?.pageSize,
}))


</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchAccountCode">标题：</label>
          </h-col>
          <h-col :span="6">
            <h-input v-model:value="searchParam.adSubject" placeholder="标题" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchAccountCode">展示状态：</label>
          </h-col>
          <h-col :span="6">
            <h-select v-model:value="searchParam.showStatus" placeholder="展示状态" allow-clear style="width: 100%">
              <h-select-option value="1">展示</h-select-option>
              <h-select-option value="0">隐藏</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchAccountCode">创建日期：</label>
          </h-col>
          <h-col :span="6">
            <h-range-picker v-model:value="searchParam.createTime" value-format="YYYY-MM-DD" style="width: 100%" />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
            <h-button type="primary" @click="edit()">
              <PlusOutlined /> 新增
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex === 'gmtCreate'">
              {{ dayjs(text).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
            <template v-if="column.dataIndex == 'imgUrl'">
              <div class="img-con">
                <img :src="record.imgUrl" class="img" />
              </div>
            </template>
            <template v-if="column.dataIndex == 'showStatus'">
              <h-tag v-if="record.showStatus === 1" color="success">展示</h-tag>
              <h-tag v-else color="error">隐藏</h-tag>
            </template>
            <template v-if="column.dataIndex == 'isTopping'">
              {{ record.isTopping == 1 ? '是' : '否' }}
            </template>
            <template v-if="column.dataIndex === 'operation'">
              <h-button type="link" @click="edit(record.id)">编辑</h-button>
              <h-button type="link" @click="handleDelete(record.id)">删除</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    <!-- <div v-if="visible">
      <edit-dialog :show="visible" :data="editData" @cancel="onDialogClose" @ok="handleOk">
      </edit-dialog>
    </div> -->

  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 102.5px !important;
  height: 80px !important;
}
</style>
