<template>
  <div>
    <van-field
      required
      readonly
      :is-link="true"
      input-align="right"
      name="picker"
      :label="props.label"
      @click="showMainPersonList = true"
      error-message-align="right"
    >
    <!--       @click-right-icon.stop="onSwitch"
      right-icon="exchange" -->
    <template #input>
      <div>{{ props.value || props.palceholder || '请选择' }}</div>
    </template>
  </van-field>
    <!-- 业务申请人弹窗 -->
    <van-popup v-model:show="showMainPersonList" position="bottom">
      <div class="out-pop" style="height: 100vh">
        <van-sticky>
          <div style="background: #fff; padding-bottom: 10px">
            <van-nav-bar :title="'选择'+props.label"  left-arrow @click-left="closeMainPersonPop">
              <template #left>
                <van-icon name="arrow-left" color="#000" size="24" />
              </template>
              <template #right>
                <div class="color-main"></div>
              </template>
            </van-nav-bar>
            <van-cell-group inset style="margin: 10px ; background-color: #fff">
              <van-field
                @update:model-value="searchMainPerson"
                v-model="searchValue"
                style="background: #f6f7f9;padding-left: 20px;"
                placeholder="输入部门名称搜索"
              />
            </van-cell-group>
          </div>
        </van-sticky>
        <van-list
          :finished-text="mainPersonList.length ? '没有更多了' : ''"
        >
          <div
            v-if="mainPersonList.length == 0 && mainPersonFinished"
            style="height: 66vh"
            class="flex align-items-center justify-content-center"
          >
            <img class="img_empty" src="../assets/image/trip/empty.jpg" alt="" />
          </div>
          <template v-else>
            <van-cell v-for="(item, index) in deptAbCodeData" :key="index" @click="choseMainDept(item)">
              <div class="flex align-items-center ">
                <div
                  :class="index % 3 == 0 ? 'blue' : 'yellow'"
                  class="mr-10 ml-10 flex align-items-center justify-content-center img-name"
                >
                  {{ item?.financialName ? item?.financialName.slice(0,2) : '未知' }}
                </div>
                <div class="main">
                  <div class="user-name color-main">{{ item.financialName }}({{ item.performCode  || '未知' }})</div><br>
                  <div class="phone">{{ item.budgetCode || '未知' }} <br> 来源系统：{{ item.systemCode }}</div>
                </div>
              </div>
            </van-cell>
          </template>
        </van-list>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import type { Ref } from 'vue';
import { createVNode, onMounted, reactive, ref, watch, watchEffect, toRefs, computed } from 'vue';
import {
  showFailToast,
  Button as VanButton,
  Form as VanForm,
  Field as VanField,
  CellGroup as VanCellGroup,
  showDialog,
  showSuccessToast,
  NoticeBar,
  Popup as VanPopup,
  showToast
} from "vant";
import { userApi, organizationCenterApi } from '@haierbusiness-front/apis';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ITraveler, ICreatTrip,IAbCodeResponse } from '@haierbusiness-front/common-libs';
import { debounce, values } from 'lodash';
import { emit } from 'process';
import { DataType, usePagination, useRequest } from 'vue-request';

const store = applicationStore();

const { loginUser } = storeToRefs(store);

// props参数

interface Props {
  palceholder?: string; // 人员
  label?: string;
  value?: string;
  type?:string;
}
const props = defineProps<Props>();

const emit = defineEmits(['chose','choseMainInfo']);

const onSwitch =()=>{
  console.log(111111,"99999")
  
}


// 业务申请人相关
const searchValue = ref('');
const showMainInfoBox = ref(false);
const showMainPersonList = ref(false);
const mainPersonLoading = ref(false);
const mainPersonFinished = ref(false);
const mainPersonTotal = ref(0);
const mainPersonList = ref<Array<ITraveler>>([]);


const defaultParams: IUserListRequest = {
  pageNum: 0,
  pageSize: 20,
};

const params: Ref<IUserListRequest> = ref(defaultParams);
const closeMainPersonPop = () => {
  showMainPersonList.value = false;
};

const searchMainPerson =  debounce((val: string) => {
  if(val){
    searchAbCodeByDeptRun(val)
  }else{
    searchAbCodeByDeptRun("908889524222")
  }
});

// 获取主体信息
const getAbCodeByUser = async (userName:string) => {
  if(!userName) {
    return showToast('请补充预算信息！')
  }
  await searchAbCodeByUserRun({ userName:userName })
  showMainInfoBox.value = true
}

const {
  data: deptAbCodeData,
  run: searchAbCodeByDeptRun,
  loading: searchDeptLoading
} = useRequest(organizationCenterApi.getAbCodeByDept);

// 按人查询A码和B码
const {
  data: userAbCodeData,
  run: searchAbCodeByUserRun,
  loading: searchUserLoading
} = useRequest(organizationCenterApi.getAbCode);

const userAbCode = computed(() => {
  if(userAbCodeData.value && userAbCodeData.value.length > 0) {
    let list = [] as IAbCodeResponse[]
    userAbCodeData.value.map((item, index) => {
      const data: IAbCodeResponse = {
        ...item,
        id: index
      }
      list = [...list, data]
    })
    return list
  } else {
    return [] as IAbCodeResponse[]
  }
})

const choseMainDept = (item: ITraveler) => {
  showMainPersonList.value = false;
  emit('chose', item)
};

</script>

<style scoped lang='less'>
.flex {
  display: flex;
}
.flex-column {
  flex-direction: column;
}
.align-items-center {
  align-items: center;
}
.justify-content-center {
  justify-content: center;
}
.justify-content-between {
  justify-content: space-between;
}
.flex-warp {
  flex-wrap: wrap;
}

.mr-10 {
  margin-right: 10px !important;
}
.mr-20 {
  margin-right: 20px;
}
.color-main {
    color: #0073e5;
  }
.out-pop {
  .main {
    flex: 1;
  }
  .img-name {
    width: 30px;
    height: 30px;
    
    color: #fff;
    font-size: 10px;
    border-radius: 30px;
  }
  
  .blue {
    background-color: #0073e5;
  }
  .yellow {
    background-color: #faad14;
  }
  .add-person {
    margin: 16px;
    margin-bottom: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
    font-size: 12px;
  }
  .user-name {
    text-align: left;
    font-size: 12px;
  }
  .phone {
    text-align: left;
    color: #8c8c8c;
  }
}
</style>