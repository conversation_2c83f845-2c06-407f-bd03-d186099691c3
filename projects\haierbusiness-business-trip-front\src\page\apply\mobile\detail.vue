<template>
  <div class="mobile-box">
    <van-overlay
      :z-index="999"
      style="display: flex; align-items: center; justify-content: center"
      :show="fullScreenLoading"
    >
      <van-loading type="spinner" color="#1989fa" vertical> 加载中.... </van-loading>
    </van-overlay>

    <van-tabs v-model:active="active" offset-top="0" sticky v-if="changeNow?.id">
      <van-tab title="当前生效版本"></van-tab>
      <van-tab title="变更中版本"></van-tab>
      <van-tab v-if="oldApplyList && oldApplyList.length > 0">
        <template #title>
          <van-dropdown-menu>
            <van-dropdown-item title="历史变更">
              <van-cell v-for="(item, index) in oldApplyList" :key="index" center @click="goToDetail(item.id)">
                <a href="javascript:;">{{ `v${index + 1}` }}</a>
              </van-cell>
            </van-dropdown-item>
          </van-dropdown-menu>
        </template>
      </van-tab>
    </van-tabs>

    <div class="title flex justify-content-between align-items-center">
      <div class="flex align-items-center justify-content-center">
        <span class="shu"></span>
        <div class="text">基本信息</div>
      </div>
    </div>
    <van-cell-group inset>
      <van-cell title="申请单号" title-style="max-width:100px">
        <template #value>
          <div>{{ detail?.applyNo }}</div>
        </template>
      </van-cell>

      <van-cell title="申请时间">
        <template #value>
          <div>{{ detail?.gmtCreate }}</div>
        </template>
      </van-cell>

      <van-cell title="出差人">
        <template #value>
          <div>{{ getMainPerson(detail?.travelerList) }}</div>
        </template>
      </van-cell>
      <van-cell title="内部出行人">
        <template #label>
          <div>{{ getInPerson(detail?.travelerList) }}</div>
        </template>
      </van-cell>

      <van-cell title="外部出行人">
        <template #label>
          <div>{{ getOutPerson(detail?.travelerList) }}</div>
        </template>
      </van-cell>

      <van-cell title="预订方式">
        <template #value>
          <div>
            {{ detail?.travelReserveFlag == 1 ? '经商旅预订' : detail?.travelReserveFlag == 0 ? '自费垫付' : '' }}
          </div>
        </template>
      </van-cell>

      <!-- <van-cell title="不经商旅预订原因" v-if="detail?.travelReserveFlag != 1">
        <template #label>
          <div>{{ resonList?.find(item => detail?.unbookedReasonId == item.id)?.reasonInfo }}</div>
        </template>
      </van-cell> -->

      <van-cell title="出差事由">
        <template #label>
          <div>{{ detail?.travelReason }}</div>
        </template>
      </van-cell>

      <van-collapse v-model="activeNames3" v-if="detail?.travelReserveFlag == 1 && detail?.haierBudgetPayOccupyRequest">
        <van-collapse-item name="1" >
          <template #title>
            <div style="display: flex; width: 100%; justify-content: space-between">
              <div>预算信息</div>
              <div>
                <span class="font-color-grey" v-if="!detail?.haierBudgetPayOccupyRequest?.isQueryDept">
                  {{ detail?.haierBudgetPayOccupyRequest?.budgeterName || mainPerson()?.travelUserName }}的个人预算
                </span>
                <span class="font-color-grey" v-else>
                  {{ detail?.haierBudgetPayOccupyRequest?.budgetDepartmentName }}的部门预算
                </span>
              </div>
            </div>
          </template>
          <van-cell title="预算信息：" :value="detail?.haierBudgetPayOccupyRequest.budgeterName"></van-cell>
          <van-cell title="预算系统：" :value="detail?.haierBudgetPayOccupyRequest.budgetSystemCode"></van-cell>

          <van-cell title="执行主体：" :value="detail?.haierBudgetPayOccupyRequest.performName"></van-cell>
          <van-cell title="预算主体：" :value="detail?.haierBudgetPayOccupyRequest.budgetDepartmentName"></van-cell>
          <van-cell title="费用科目：" :value="detail?.haierBudgetPayOccupyRequest.feeItemName"></van-cell>
          <van-cell title="出账法人：" :value="detail?.haierBudgetPayOccupyRequest.accountCompanyName"></van-cell>
          <van-cell title="成本中心：" :value="detail?.haierBudgetPayOccupyRequest.costCenterName"></van-cell>
        </van-collapse-item>
      </van-collapse>

      <van-collapse v-model="activeNames2" v-if="detail?.travelReserveFlag == 1">
        <van-collapse-item name="1" @click="getMoneyDetailList2">
          <template #title>
            <div style="display: flex; width: 100%; justify-content: space-between">
              <div>费用预算</div>
              <div>
                <span>{{ `¥ ${detail?.amountSum || 0}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') }}</span>
                <span style="color: #3983e5">(总)</span>
                <!-- <span>{{
                  `¥ ${detail?.amountSum || 0 - detail?.realAmountSum || 0}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                }}</span>
                <span style="color: #52c41a">(可用)</span> -->
              </div>
            </div>
          </template>
          <van-cell-group>
            <van-cell
              :title="item.travelUserName"
              :value="`¥ ${item.amountSum || 0}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
              v-for="(item, index) in budgetDetailData"
              :key="index"
            >
            </van-cell>
          </van-cell-group>
        </van-collapse-item>
      </van-collapse>
    </van-cell-group>

    <div class="title flex justify-content-between align-items-center">
      <div class="flex align-items-center justify-content-center">
        <span class="shu"></span>
        <div class="text">行程信息</div>
      </div>
    </div>
    <div v-for="(trip, index) in detail?.tripList" :key="index">
      <van-cell-group inset class="mb-10" style="position: relative; overflow: visible">
        <div class="padding-16" style="position: relative">
          <div class="transparent-bg" style="width: 100%; height: 100%; position: absolute; top: 0; left: 0"></div>
          <div class="my_field_label flex justify-content-between align-items-center mb-20">
            <div class="left">
              <span class="mr-5" style="color: #898c8c">行程{{ index + 1 }}</span>
            </div>
          </div>

          <div class="flex flex-column" style="width: 100%">
            <div class="flex align-items-center mb-20">
              <div class="item-city">
                <span class="strong">{{ trip.beginCityName }}</span>
                <van-icon class="ml-5" name="arrow" />
              </div>
              <van-icon class="ml-10 mr-10" name="minus" />
              <div class="item-city">
                <span class="strong">{{ trip.endCityName }}</span>
                <van-icon class="ml-5" name="arrow" />
              </div>
            </div>

            <div class="flex justify-content-between mb-10">
              <div class="flex align-items-center range-font">
                <div class="item-city">
                  <span class="">{{ formatDate(trip.beginDate) }}</span>
                </div>
                <van-icon class="ml-5 mr-5" name="minus" />
                <div class="item-city">
                  <span class="">{{ formatDate(trip.endDate) }}</span>
                  <van-icon class="ml-5" name="arrow" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="trip.tripDetailMapList?.length > 0"
          class="show-more-box flex align-items-center justify-content-center"
          @click="trip.showMore = !trip.showMore"
        >
          <div
            style="width: 100%; height: 100%"
            :class="trip.showMore == true ? 'show-more-box-close' : 'show-more-box-open'"
          ></div>
        </div>
      </van-cell-group>
      <div v-if="trip.showMore">
        <!-- 费用信息 -->
        <van-cell-group inset class="mb-10" v-for="(tripDetail, ii) in trip?.tripDetailMapList" :key="ii">
          <van-cell title="费用名称" :value="tripDetail.productName"></van-cell>
          <van-cell
            v-if="tripDetail.productName == '火车'"
            title="出发站点"
            :value="tripDetail.startTrainName"
          ></van-cell>
          <van-cell
            v-if="tripDetail.productName == '火车'"
            title="到达站点"
            :value="tripDetail.endTrainName"
          ></van-cell>

          <van-collapse v-model="tripDetail.activeNames">
            <van-collapse-item name="1">
              <template #title>
                <div style="display: flex; width: 100%; justify-content: space-between">
                  <div>费用预算</div>
                  <div>{{ `¥ ${tripDetail.budgetAmount}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') }}</div>
                </div>
              </template>
              <div class="budge-detail-box">
                <div>出行人</div>
                <div>差标</div>
                <div>平台使用费</div>
                <!-- <div>保险费</div> -->
              </div>

              <div
                class="budge-detail-box"
                v-for="(budgeDetail, budgeIndex) in tripDetail?.budgetAmountDesc"
                :key="budgeIndex"
              >
                <div>{{ budgeDetail?.travelUserName }}</div>
                <div>{{ budgeDetail?.differentialStandard || 0 }}</div>
                <div>{{ budgeDetail?.serviceAmount || 0 }}</div>
                <!-- <div>{{ budgeDetail?.insuranceAmount || 0 }}</div> -->
              </div>
            </van-collapse-item>
          </van-collapse>

          <van-cell v-if="tripDetail?.excessiveFlag" title="超标原因">
            <template #value>
              {{ resonList?.find((e) => e.id == tripDetail.excessiveReasonId)?.reasonInfo }}
            </template>
          </van-cell>

          <van-cell v-if="tripDetail?.excessiveOtherReasonDesc" :value="tripDetail?.excessiveOtherReasonDesc" title="其他原因">
          </van-cell>

          <van-cell
            title="出行人"
            :value="tripDetail?.travelApplyTripDetailList.map((item) => item.travelUserName).join(',')"
          ></van-cell>
          <van-cell title="保险费">
            <template #value>{{
              `¥ ${tripDetail.insuranceAmount || 0} 元/人`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            }}</template>
          </van-cell>
        </van-cell-group>
      </div>
    </div>

    <div v-if="confirmTrip && confirmTrip.length > 0" class="title flex justify-content-between align-items-center">
      <div class="flex align-items-center justify-content-center">
        <span class="shu"></span>
        <div class="text">实际行程</div>
      </div>
    </div>
    <van-cell-group v-if="confirmTrip && confirmTrip.length > 0" inset class="mb-10">
      <van-collapse v-model="activeNames">
        <van-collapse-item
          :title="`${item.travelUserName}(${item.travelUserNo})`"
          :name="String(1 + index)"
          v-for="(item, index) in confirmTrip"
          :key="index"
        >
          <template #value> 
            <van-tag color="blue" v-if="item?.tripList[0]?.confirmUser == 'SYSTEM'">系统确认</van-tag>
            <van-tag color="green" v-else>手动确认</van-tag>
          </template>
          <template #label> 
            <div>
              <div>{{`${item?.tripList[0]?.confirmUserName}(${item?.tripList[0]?.confirmUser})`}}</div>
              <div>{{ item?.tripList[0]?.confirmDate }}</div>
            </div>
          </template>
          <van-cell v-for="(trip, i2) in item.tripList" :key="i2" :title="`第${i2 + 1}行程`">
            <template #label>
              <div class="flex" style="justify-content: space-between">
                <span>{{ trip.realBeginDate }} - {{ trip.realEndDate }}</span>
                <span>{{ trip.realBeginCityName }} - {{ trip.realEndCityName }}</span>
              </div>
            </template>
          </van-cell>
        </van-collapse-item>
      </van-collapse>
    </van-cell-group>

    <div class="title flex justify-content-between align-items-center">
      <div class="flex align-items-center justify-content-center">
        <span class="shu"></span>
        <div class="text">附件信息</div>
      </div>
    </div>

    <van-cell-group inset>
      <van-cell
        :title="item.fileName"
        v-for="(item, index) in detail?.fileList"
        :key="index"
        @click="handleClick(item.filePath)"
      >
      </van-cell>
    </van-cell-group>

    <!-- pdf文件预览 -->
    <van-popup :lazy-render="false" v-model:show="showPdfPop" position="bottom" :style="{ height: '100%' }">
      <van-nav-bar title="附件预览" left-arrow fixed :z-index="1000">
        <template #left>
          <van-icon name="arrow-left" color="#000" @click="showPdfPop = false" size="20" />
        </template>
      </van-nav-bar>
      <div ref="yszcpdfRef" style="height: 100%"></div>
    </van-popup>
  </div>
</template>

<script setup lang='ts'>
import { onMounted, ref, watch } from 'vue';
import baseInfo from './components/baseInfo.vue';
import planBudge from './components/planBudge.vue';
import fileUpload from './components/fileUpload.vue';
import { tripApi, reasonApi } from '@haierbusiness-front/apis';

import { IUserListRequest, ITraveler, IUserInfo, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import Pdfh5 from 'pdfh5';
import 'pdfh5/css/pdfh5.css';

const active = ref(0);
const route = ref(getCurrentRoute());

const activeNames = ref(['']);
const activeNames2 = ref(['']);
const activeNames3 = ref(['']);

const fullScreenLoading = ref(false);

const budgetDetailData = ref<any>([]);
// 获取具体费用明细
const getMoneyDetailList = () => {
  tripApi.memberAmountList(detail.value).then((res) => {
    budgetDetailData.value = res;
  });
};
const getMoneyDetailList2 = () => {
  if (budgetDetailData.value?.length < 1) {
    getMoneyDetailList();
  }
};
// 获取主出差人信息
const mainPerson = () => {
  return detail?.travelerList?.filter((item:any) => item.travelUserType == '0' && item.mainFlag == '1')[0];
};

const detail = ref<any>();

const formatDate = (date: string) => {
  if (!date) {
    return '';
  }
  const arr = date?.split('-');
  return arr.join('/');
};

const getMainPerson = (list?: Array<ITraveler>) => {
  if (!list || list.length < 1) {
    return '';
  }
  let mainPerson = list.filter((item) => item.mainFlag === '1')[0];
  return `${mainPerson?.travelUserName}(${mainPerson?.travelUserNo})`;
};
const getOutPerson = (list: Array<ITraveler>) => {
  if (!list || list.length < 1) {
    return '';
  }
  let listTemp:any = [];
  list.forEach((item) => {
    if (item.travelUserType != '0') {
      listTemp.push(`${item.travelUserName}`);
    }
  });
  return listTemp.join(',');
};

const getInPerson = (list: Array<ITraveler>) => {
  if (!list || list.length < 1) {
    return '';
  }
  let listTemp:any = [];
  list.forEach((item) => {
    if (item.mainFlag != '1' && item.travelUserType == '0') {
      listTemp.push(`${item.travelUserName}(${item.travelUserNo})`);
    }
  });
  return listTemp.join(',');
};

const applyId = route.value?.query?.id;

const bizId = route.value?.query?.bizId;

const getDetail = async (id: string) => {
  fullScreenLoading.value = true;
  const detailRes: any = await tripApi.queryDetailByApplyNo(id);

  detailRes?.tripList?.forEach((item:any) => {
    item.tripDetailMapList?.forEach((item2:any) => {
      item2.activeNames = [''];
    });
  });
  detail.value = detailRes;
  fullScreenLoading.value = false;
  // 获取实际行程
  getAllRealTrips(detailRes.travelerList);
};
const confirmTrip = ref<any>([]);
const getAllRealTrips = async (list: any) => {
  let results = await Promise.all(
    list.map((item:any) => {
      if (item.travelUserNo) {
        const params = {
          applyId: applyId,
          userCode: item.travelUserNo,
        };
        return tripApi.getRealTrip(params);
      } else {
        return '';
      }
    }),
  );
  results = results.filter((item) => item);
  console.log('12121212', results);

  results?.forEach((item, index) => {
    item.cityList = [];
    item?.tripList?.forEach((element: any, eindex: number) => {
      element.beginCityCode = element.realBeginCityCode;
      element.beginCityName = element.realBeginCityName;
      element.beginDate = element.realBeginDate;
      element.endCityCode = element.realEndCityCode;
      element.endCityName = element.realEndCityName;
      element.endDate = element.realEndDate;
      element.tripDetailMapList = [];

      if (eindex == 0) {
        item.cityList.push({
          cityCode: element.beginCityCode,
          city: element.beginCityName,
          syId: element.beginCityCodeSy,
          date: element.beginDate,
        });
      }
      item.cityList.push({
        cityCode: element.endCityCode,
        city: element.endCityName,
        syId: element.endCityCodeSy,
        date: element.endDate,
      });
    });
  });
  console.log('results', results);

  confirmTrip.value = results;
};

const showPdfPop = ref(false);
const yszcpdfRef = ref();

const handleClick = (url: any) => {
  const pdfh5Yszc = new Pdfh5(yszcpdfRef.value, {
    pdfurl: url,
  });

  pdfh5Yszc.on('complete', (status, msg, time) => {
    showPdfPop.value = true;
  });
};

const tabVersion = (key: number) => {};

const resonList = ref<Array<object>>([]);
// 获取未商旅预定原因
const getResonList = () => {
  reasonApi
    .list({
      pageNum: 1,
      reasonType: 20,
      pageSize: 100,
    })
    .then((res:any) => {
      resonList.value = res.records;
    });
};

watch(
  () => applyId,
  (val: string) => {
    if (val) {
      getDetail(bizId || val);
    }
  },
  {
    deep: true,
  },
);
const chosedNow = ref<string>('now');

watch(
  () => active.value,
  (val: number) => {
    if (val != 2) {
      let version = val == 0 ? 'now' : 'changeing';
      if (chosedNow.value == version) {
        return;
      }
      chosedNow.value = version;
      if (version == 'now') {
        getDetail(applyId);
      } else {
        getDetail(changeNow.value.id);
      }
    }
  },
);

watch(
  () => detail.value?.applyNo,
  (val?: string) => {
    if (val) {
      getDataList();
    }
  },
);
const businessTravel = import.meta.env.VITE_BUSINESS_TRIP_URL;
const goToDetail = (id: string) => {
  const url = businessTravel + '#' + '/mobile/detail?id=' + id;
  window.location.href = url;
};
const changeNow = ref<any>({});
const oldApplyList = ref<any>([]);

// 获取变更历史
const getDataList = () => {
  let params = {
    applyNo: detail.value?.applyNo,
    pageNum: 1,
    pageSize: 10,
  };
  tripApi.getDataList(params).then((res:any) => {
    changeNow.value = {};
    oldApplyList.value = [];
    res?.forEach((item:any) => {
      // changeStatus	变更状态 10正常 20变更中 30已作废  40历史版本
      if (item.changeStatus == '40') {
        oldApplyList.value.push(item);
      }
      if (item.changeStatus == '20') {
        changeNow.value = item;
      }
    });
  });
};

onMounted(async () => {
  getResonList();
  getDetail(applyId);
});
</script>
<style lang="less" scoped>
@import url(./components/mobile.less);
.mobile-box {
  padding-bottom: 40px;
  max-height: 100vh;
}
.show-more-box {
  width: 22px;
  height: 22px;
  background: #fff;
  position: absolute;
  bottom: -10px;
  left: calc(50% - 11px);
  border-radius: 10px;
  padding: 4px;
}
.show-more-box-open {
  background-image: url('@/assets/image/trip/open.png');
  background-size: 100% 100%;
}
.show-more-box-close {
  background-image: url('@/assets/image/trip/retract.png');
  background-size: 100% 100%;
}

.budge-detail-box {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  > div {
    text-align: center;
  }
}
:deep(.van-cell--clickable:active) {
  background-color: #fff;
}
</style>