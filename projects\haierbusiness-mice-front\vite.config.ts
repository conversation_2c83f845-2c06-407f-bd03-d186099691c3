import { loadEnv } from 'vite'

import vue from '@vitejs/plugin-vue'
import path from 'path';

const CWD = process.cwd();

export default ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);

  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
    },
    plugins: [vue()],
    build:{
      target:['es2015'],
      sourcemap: true
    },
    server: {
      port: 5184,
      proxy: {
        '/haiermice/businessmanagement/hb/businesstravel': {
          // target: 'http://*************:8081',
          target: 'http://localhost:8081',
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`/haiermice/businessmanagement/hb/businesstravel`), ''),
        },
        "/haiermice/businessmanagement/hb": {
          target: "https://businessmanagement-test.haier.net/hbweb/index/hb",
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`/haiermice/businessmanagement/hb`), ""),
        },
        "/hb": {
          //target: "http://localhost:8080/hb",
          target: "https://businessmanagement-test.haier.net/hbweb/pay/hb",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb/, ""),
        },
        "/upload": {
          target: "https://businessmanagement-test.haier.net/hbweb/upload",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/upload/, ""),
        },
      }
    },
  }
}
