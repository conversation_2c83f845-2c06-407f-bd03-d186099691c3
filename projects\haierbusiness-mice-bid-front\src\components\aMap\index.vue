<script lang="ts" setup>
// 地图选点
import { onMounted, shallowRef, ref, defineProps, defineEmits } from 'vue';
import AMapLoader from '@amap/amap-jsapi-loader';
import { message } from 'ant-design-vue';

const props = defineProps({
  lng: {
    type: String,
    default: '120.382215',
  },
  lat: {
    type: String,
    default: '36.067694',
  },
  showCloseBtn: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['aMapBack']);

const map = shallowRef(null);
const keyword = ref(null); // 位置关键词
const mapList = ref<Array>([]); // 地图下拉菜单
const coord = ref<String>(''); // 经纬度

let AMapObj, placeSearch, marker, geocoder;

const initMap = () => {
  AMapLoader.reset();

  AMapLoader.load({
    key: '25569e43d6c6bcfa4d39a1b920d8d2d1', //设置您的key
    version: '2.0',
    plugins: ['AMap.ToolBar', 'AMap.Driving', 'AMap.Scale', 'AMap.PlaceSearch', 'AMap.AutoComplete', 'AMap.Marker'],
    AMapUI: {
      version: '1.1',
      plugins: [],
    },
    Loca: {
      version: '2.0.0',
    },
  })
    .then((AMap) => {
      AMapObj = AMap;

      map.value = new AMap.Map('map_box', {
        // 设置地图容器id
        viewMode: '3D', // 是否为3D地图模式
        zoom: 16, // 初始化地图级别
        zooms: [2, 22],
        center: [props.lng, props.lat], // 青岛市
      });

      map.value.on('click', onMapClick);

      AMap.plugin(
        ['AMap.ToolBar', 'AMap.Scale', 'AMap.Geolocation', 'AMap.PlaceSearch', 'AMap.Geocoder', 'AMap.AutoComplete'],
        () => {
          // 缩放条
          const toolbar = new AMap.ToolBar();
          // 比例尺
          const scale = new AMap.Scale();

          // 定位
          const geolocation = new AMap.Geolocation({
            enableHighAccuracy: true, // 是否使用高精度定位，默认:true
            timeout: 10000, // 超过10秒后停止定位，默认：5s
            position: 'RT', // 定位按钮的停靠位置
            buttonOffset: new AMap.Pixel(10, 20), // 定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
            zoomToAccuracy: true, // 定位成功后是否自动调整地图视野到定位点
          });

          geocoder = new AMap.Geocoder({
            city: '全国',
          });

          map.value.addControl(geolocation);
          map.value.addControl(toolbar);
          map.value.addControl(scale);

          placeSearch = new AMap.PlaceSearch({
            city: '全国',
            pageSize: 20, // 单页显示结果条数
            pageIndex: 1, // 页码
            citylimit: false, // 是否强制限制在设置的城市内搜索
            autoFitView: true,
          });

          // 初始地点定位图标
          drawMarker({ lat: props.lat, lng: props.lng });
        },
      );
    })
    .catch((e) => {
      console.log('地图加载错误', e);
    });
};

// 搜索地图
const handleSearch = (str: String) => {
  placeSearch.search(str, (status, result) => {
    if (result && typeof result === 'object' && result.poiList) {
      const list = result.poiList.pois;

      list.forEach((item) => {
        item.value = item.name;
        item.label = item.name;
      });
      mapList.value = list;
    }
  });
};

// 点击地图
const onMapClick = (e) => {
  coord.value = e.lnglat.lng + ',' + e.lnglat.lat;

  geocoder.getAddress([e.lnglat.lng, e.lnglat.lat], (status, result) => {
    console.log('%c [ 点击地图 ]-98', 'font-size:13px; background:pink; color:#bf2c9f;', result.regeocode);
    if (status === 'complete' && result.info === 'OK') {
      // result为对应的地理位置详细信息
      keyword.value = result.regeocode.formattedAddress;
    }
  });
  drawMarker(e.lnglat);
};

// 点击搜索项
const handleSelect = (item) => {
  console.log('点击搜索项', item);
  drawMarker(item.location);
  if (item.location != null) {
    coord.value = item.location.lng + ',' + item.location.lat;
    keyword.value = item.name;
  }
};

// 绘制地点marker
const drawMarker = (location) => {
  if (location == null) return;
  let longitude = location.lng,
    latitude = location.lat;

  if (marker) {
    marker.setMap(null);
  }

  marker = new AMapObj.Marker({
    position: new AMapObj.LngLat(longitude, latitude),
    anchor: 'bottom-center',
  });

  marker.on('click', () => {
    coord.value = location;
  });

  map.value.add(marker);
  map.value.setZoomAndCenter(16, [longitude, latitude]);
};

onMounted(() => {
  initMap();
});

// 确认
const sureFn = () => {
  if (!coord.value) {
    message.error('请选择位置！');
    return;
  }

  emits('aMapBack', {
    positionStr: coord.value,
    addressValue: keyword.value,
    aMapShow: false,
  });
};

// 取消
const cancelFn = () => {
  emits('aMapBack', {
    positionStr: [],
    addressValue: '',
    aMapShow: false,
  });
};
</script>

<template>
  <div class="a_map_home">
    <div class="a_map_contoiner">
      <!-- 地图选点 -->
      <div id="map_box"></div>

      <div class="a_map_info_box">
        <a-select
          v-model:value="keyword"
          show-search
          placeholder="输入关键字搜索"
          style="width: 350px"
          :default-active-first-option="false"
          :show-arrow="false"
          :filter-option="false"
          :not-found-content="null"
          @search="handleSearch"
        >
          <a-select-option v-for="item in mapList" :key="item.id" :value="item.id" @click="handleSelect(item)">
            <div>
              <span class="mr5">{{ item.name }}</span>
              <span style="font-size: '10px'; color: #999">{{ item.address }}</span>
            </div>
          </a-select-option>
        </a-select>
      </div>

      <div class="a_map_sure" v-if="props.showCloseBtn">
        <a-button class="mr12" @click="cancelFn">关闭</a-button>
      </div>
      <div class="a_map_sure" v-else>
        <a-button class="mr12" @click="cancelFn">取消</a-button>
        <a-button type="primary" @click="sureFn">确认</a-button>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.a_map_home {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;

  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1011;

  .a_map_contoiner {
    width: 70%;
    height: 80%;
    margin: 0 auto;
    background-color: #fff;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
  }

  .a_map_info_box {
    position: absolute;
    top: calc(10% + 24px);
    left: calc(15% + 24px);
    width: 300px;
    background-color: #1f1f1f;
    display: flex;
    flex-direction: column;
  }

  .a_map_sure {
    position: absolute;
    right: calc(15% + 60px);
    bottom: calc(10% + 24px);
    /* padding: 24px; */
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
#map_box {
  width: 100%;
  height: 100%;
}
</style>

<style scoped>
:deep() .amap-logo {
  display: none !important;
}
:deep() .amap-copyright {
  display: none !important;
}
</style>
