<script lang="ts" setup>
import { computed, onMounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';
import outPersonModal from './components/outPersonModal.vue';
import budgetModal from './components/budgetModal.vue';
import budgetDetailModal from './components/budgetDetailModal.vue';
import travelStandardsModal from './components/travelStandardsModal.vue';
import budgetModalShow from './components/budgetModalShow.vue';

import baseInfo from './components/baseInfo.vue';
import planBudge from './components/planBudge.vue';
import fileUpload from './components/fileUpload.vue';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';

const route = ref(getCurrentRoute());

import {
  Anchor as hAnchor,
  But<PERSON> as hButton,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Row as hRow,
  Col as hCol,
  Dropdown as hDropdown,
  Menu as hMenu,
  MenuItem as hMenuItem,
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { tripApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { TabsProps } from 'ant-design-vue/es/tabs';

const store = applicationStore();
const { loginUser } = storeToRefs(store);
const planBudgeRef = ref('');
const tabPosition = ref<TabsProps['tabPosition']>('top');
const activeKey = ref();

const creatTripParma = ref<ICreatTrip>({
  tripList: [],
  travelerList: [
    {
      travelUserName: loginUser.value.nickName,
      travelUserSyId: loginUser.value?.username,
      travelUserDeptName: loginUser.value?.departmentName,
      travelUserDeptId: loginUser.value?.departmentCode,
      travelUserNo: loginUser.value?.username,
      username: loginUser.value?.username,
      travelUserType: '0',
      mainFlag: '1',
    },
  ],
  fileList: [],
  travelReason: undefined,
  travelReserveFlag: 1,
  travelUserName: loginUser.value.nickName,
});

const anchorItems = [
  {
    key: '1',
    href: '#base-info',
    title: '基本信息',
  },
  {
    key: '2',
    href: '#plan-budget',
    title: '行程计划与费用预算',
  },
  {
    key: '3',
    href: '#file',
    title: '附件',
  },
];

const chosedNow = ref<string>('now');
const tabVersion = (key: string) => {

let version = key == '1'? 'now' :'changeing'
if (chosedNow.value == version) {
  return;
}
chosedNow.value = version;
if (version == 'now') {
  reloadPage(applyId);
} else {
  reloadPage(changeNow.value.id);
}
};

// 用户选择
const params = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});
// 出差人
const name = ref('');
const userNameChange = (userInfo: IUserInfo) => {
  name.value = userInfo?.nickName ?? '';
};
const handleClick: AnchorProps['onClick'] = (e, link) => {
  e.preventDefault();
  console.log(link);
};

const info = ref();
const baseInfoCom = ref();

//#region 为了表单临时写的一些变量    后续需要删除整合到别的地方

const isTravel = ref(1);

// 上传附件
const handleChange = (info: UploadChangeParam) => {
  if (info.file.status !== 'uploading') {
    console.log(info.file, info.fileList);
  }
  if (info.file.status === 'done') {
    message.success(`${info.file.name} file uploaded successfully`);
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} file upload failed.`);
  }
};

const headers = {
  authorization: 'authorization-text',
};
// 上传附件结束

const checked = ref<boolean>(false);

//#endregion

// 打开预算弹窗
const budget = ref();
const openBudgetModal = () => {
  budget?.value.show();
};

// 打开预算明细弹窗
const budgetDetail = ref();
interface IbudgetDetail {
  amountSum: string;
  travelUserName: string;
  travelUserNo: string;
  travelUserSyId: string;
}
const budgetDetailData = ref<Array<IbudgetDetail>>([]);
const openBudgetDetailModal = (data: Array<IbudgetDetail>) => {
  budgetDetailData.value = data;
  budgetDetail?.value.show();
};

// 差旅标准弹窗
const travelStandards = ref();
const openTravelStandardslModal = () => {
  let codeList = creatTripParma.value?.travelerList?.filter((item:any) => item.travelUserType == 0).map(item2 => item2.travelUserNo)
  let nameList = creatTripParma.value?.travelerList?.filter((item:any) => item.travelUserType == 0).map(item2 => item2.travelUserName)

  // 判断经办人的工号是否在出差人工号列表里面
  if(codeList.indexOf(loginUser.value?.username)== -1 ) {
    codeList = [loginUser.value?.username, ...codeList]
    nameList = [loginUser.value?.nickName, ...nameList]
  }

  travelStandards?.value.show(codeList, nameList);
  // travelStandards?.value.show();
};

// 外部联系人弹窗
const outPerson = ref();
const outPersonOpen = () => {
  outPerson?.value.show();
};
// 创建申请单请求参数

// 出差人
const getNewInfo = (info: ITripInfo) => {
  creatTripParma.value = { ...creatTripParma.value, ...info };
};

// 根据部门、个人查询预算
const {
  data: applyCreatData,
  run: applyCreatiRun,
  loading: applyCreatLoading,
} = useRequest(tripApi.applyCreat, {
  defaultParams: [creatTripParma.value],
});
// 提交申请单
const addApplyForm = () => {
  applyCreatiRun(creatTripParma.value);
};

const changeNow = ref({});

const reloadPage = async (id: string) => {
  creatTripParma.value = await tripApi.queryDetailByApplyNo(id);
  // 数据回显处理
  baseInfoCom.value.chosedPerson = creatTripParma.value.travelerList?.filter((item) => item.travelUserType == '0' && item.mainFlag )[0];
  baseInfoCom.value.chosedInPersenList = creatTripParma.value.travelerList?.filter((item) => item.travelUserType == '0' && !item.mainFlag);

  creatTripParma.value.travelUserName = creatTripParma.value.travelerList.filter(
    (item) => item.mainFlag,
  )[0]?.travelUserName;
  creatTripParma.value.outPersonId = [];
  creatTripParma.value.travelerList.forEach((item) => {
    if (item.travelUserType == '1') {
      creatTripParma.value.outPersonId.push(item.travelUserSyId);
    }

    item.personIdList = [];
    item.personIdList = [...item.personIdList, item.travelUserSyId];
  });
  planBudgeRef.value.cityList = [];
  creatTripParma.value.tripList.forEach((item, index) => {
    if (index == 0) {
      planBudgeRef.value.cityList.push({
        cityCode: item.beginCityCode,
        city: item.beginCityName,
        syId: item.beginCityCodeSy,
        date: item.beginDate,
      });
    }
    planBudgeRef.value.cityList.push({
      cityCode: item.endCityCode,
      city: item.endCityName,
      syId: item.endCityCodeSy,
      date: item.endDate,
    });

    item?.tripDetailMapList?.forEach((tripMap) => {
      tripMap.personIdList = tripMap.travelApplyTripDetailList.map((tt) => tt.travelUserSyId);
    });
  });

  creatTripParma.value?.fileList?.forEach((file) => {
    file.name = file.fileName;
    file.thumbUrl = file.filePath;
  });
};
const applyId = route.value?.query?.id;
const bizId = route.value?.query?.bizId;

onMounted(async () => {
  creatTripParma.value = await tripApi.queryDetailByApplyNo(bizId || applyId);
  
  // 数据回显处理
  // 根据登陆人初始化数据
  baseInfoCom.value.chosedPerson = creatTripParma.value.travelerList?.filter((item) => item.travelUserType == '0' && item.mainFlag)[0];
  baseInfoCom.value.chosedInPersenList = creatTripParma.value.travelerList?.filter((item) => item.travelUserType == '0' && !item.mainFlag);

  creatTripParma.value.travelUserName = creatTripParma.value.travelerList.filter(
    (item) => item.mainFlag,
  )[0].travelUserName;
  creatTripParma.value.outPersonId = [];
  creatTripParma.value.travelerList.forEach((item) => {
    if (item.travelUserType == '1') {
      creatTripParma.value.outPersonId.push(item.travelUserSyId);
    }

    item.personIdList = [];
    item.personIdList = [...item.personIdList, item.travelUserSyId];
  });
  planBudgeRef.value.cityList;
  creatTripParma.value.tripList.forEach((item, index) => {
    if (index == 0) {
      planBudgeRef.value.cityList.push({
        cityCode: item.beginCityCode,
        city: item.beginCityName,
        syId: item.beginCityCodeSy,
        date: item.beginDate,
      });
    }
    planBudgeRef.value.cityList.push({
      cityCode: item.endCityCode,
      city: item.endCityName,
      syId: item.endCityCodeSy,
      date: item.endDate,
    });

    item?.tripDetailMapList?.forEach((tripMap) => {
      tripMap.personIdList = tripMap.travelApplyTripDetailList.map((tt) => tt.travelUserSyId);
    });
  });

  creatTripParma.value?.fileList?.forEach((file) => {
    file.name = file.fileName;
    file.thumbUrl = file.filePath;
  });

  if(!creatTripParma.value.haierBudgetPayOccupyRequest) {
    creatTripParma.value.haierBudgetPayOccupyRequest = {}
  }

  // 如果bizId存在 直接显示变更中的数据
  if (bizId) {
    activeKey.value = '2'
    chosedNow.value = 'changeing'
    changeNow.value = creatTripParma.value
  }
});
const businessTravel = import.meta.env.VITE_BUSINESS_TRIP_URL;
const goToDetail = (id: string) => {
  const url = businessTravel + '#' + '/detail?id=' + id;
  window.open(url);
};

//预算详情弹窗

const budgetDetailDialog = ref();
const openBudgetDetailShow = (res) => {
  budgetDetailDialog?.value.show(res || {});
};
</script>

<template>
  <div class="container">
    <div class="row flex">
      <div class="" style="position: relative">
        <!-- 变更单 -->
        <div class="change-title">
          <h-row class="mb-10">
            <h-col :span="6" style="color: #00000073">申请单号:</h-col>
            <h-col :span="18">{{ creatTripParma.applyNo }}</h-col>
          </h-row>
          <h-row class="mb-10">
            <h-col :span="6" style="color: #00000073">申请时间:</h-col>
            <h-col :span="18">{{ creatTripParma.gmtCreate }}</h-col>
          </h-row>
        </div>

        <div class="main-title">
          <!-- <img src="../../assets/image/trip/title.png" alt=""> -->
          <span>出差申请单</span>
        </div>
        <a-tabs
            v-if="bizId && changeNow?.id"
            @change="tabVersion"
            v-model:activeKey="activeKey"
            :tab-position="tabPosition"
            animated
            type="card"
          >
            <a-tab-pane key="1" tab="当前生效版本"></a-tab-pane>
            <a-tab-pane key="2" tab="变更中版本"></a-tab-pane>
        </a-tabs>
        <div class="apply-con flex">
          <!-- 驳回原因 -->
          <!-- status=10 && auditStatus = 40 && reatTripParma?.workFlowFailInfo -->
          <div
            class="whole-line reject"
            v-if="creatTripParma?.workFlowFailInfo && creatTripParma?.status == 10 && creatTripParma?.auditStatus == 40"
          >
            <h-row>
              <h-col :span="2">驳回原因:</h-col>
              <a-tooltip>
                <template #title>{{ creatTripParma?.workFlowFailInfo }}</template>
                <h-col :span="20">{{ creatTripParma?.workFlowFailInfo }}</h-col>
              </a-tooltip>
            </h-row>
          </div>
          <div
            class="reject-bg"
            v-if="creatTripParma?.workFlowFailInfo && creatTripParma?.status == 10 && creatTripParma?.auditStatus == 40"
          ></div>
          
          <!-- 基本信息 -->
          <base-info
            id="base-info"
            ref="baseInfoCom"
            :chosedNow="chosedNow"
            :isDetail="true"
            :creatTripParma="creatTripParma"
            @outPersonOpen="outPersonOpen"
            @showStandardOpen="openTravelStandardslModal"
          ></base-info>
          <!-- 行程与费用 -->
          <div id="plan-budget" class="whole-line">
            <plan-budge
              ref="planBudgeRef"
              :isDetail="true"
              :creatTripParma="creatTripParma"
              @openBudgetModal="openBudgetModal"
              @openBudgetDetailShow="openBudgetDetailShow"
              @showStandardOpen="openTravelStandardslModal"
              @showBudgetDetailModal="(data) => openBudgetDetailModal(data)"
            ></plan-budge>
          </div>
          <!-- 附件 -->
          <div id="file" class="whole-line block-con">
            <file-upload :isDetail="true" :creatTripParma="creatTripParma"></file-upload>
          </div>
        </div>
      </div>
      <div class="anchor-con flex">
        <h-anchor :items="anchorItems" @click="handleClick" />
      </div>

      <!-- 差旅标准弹窗 -->
      <travel-standards-modal ref="travelStandards" />
      <!-- 预算归属明细弹窗 -->
      <budgetModalShow ref="budgetDetailDialog" />

      <!--新增外部联系人 -->
      <out-person-modal ref="outPerson" />

      <!-- 预算归属弹窗 -->
      <budget-modal ref="budget" />

      <!-- 预算明细弹窗 -->
      <budget-detail-modal :tableData="budgetDetailData" ref="budgetDetail" />
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url(./components/trip.less);

:deep(.ant-tabs-nav) {
  margin: 0;
}
:deep(.ant-tabs-nav-wrap) {
  flex-direction: row-reverse;
}
</style>