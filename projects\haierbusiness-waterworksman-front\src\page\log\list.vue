<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { DownOutlined, ExclamationCircleOutlined, PlusOutlined, UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { apiLogApi } from '@haierbusiness-front/apis';
import {
  IApiLogRequest,
} from '@haierbusiness-front/common-libs';
import { errorModal, routerParam } from '@haierbusiness-front/utils';
import Eloading from '@haierbusiness-front/components/loading/Eloading.vue';
import dayjs, { Dayjs } from 'dayjs';
import { computed, onMounted, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useRouter } from "vue-router";

const router = useRouter()
const columns: ColumnType[] = [
{
    title: '用户',
    dataIndex: 'createBy',
    width: '240px',
    align: 'center',
    ellipsis: true,
    fixed: 'left',
  },
  // {
  //   title: 'name',
  //   dataIndex: 'name',
  //   width: '240px',
  //   align: 'center',
  //   ellipsis: true
  // },
  {
    title: '状态码',
    dataIndex: 'status',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: 'url',
    dataIndex: 'url',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: 'method',
    dataIndex: 'method',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: 'headers',
    dataIndex: 'headers',
    width: '360px',
    align: 'center',
    ellipsis: true
  },
  {
    title: 'params',
    dataIndex: 'params',
    width: '360px',
    align: 'center',
    ellipsis: true
  },
  {
    title: 'respHeaders',
    dataIndex: 'respHeaders',
    width: '360px',
    align: 'center',
    ellipsis: true
  },
  {
    title: 'response',
    dataIndex: 'response',
    width: '360px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '240px',
    align: 'center',
    ellipsis: true
  }
];
const searchParam = ref<IApiLogRequest>({ })
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(apiLogApi.list);

const reset = () => {
  searchParam.value = { }
}

const dataSource = computed(() => {
  return data.value?.records || []
});

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};
</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">

    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchState">状态码：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="searchAccountCode" v-model:value="searchParam.status" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchAccountCode">headersKey：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="searchAccountCode" v-model:value="searchParam.headersKey" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="paymentRecordCode">method：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="paymentRecordCode" v-model:value="searchParam.method" placeholder=""
              autocomplete="off" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="paymentRecordCode">urlKey：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="paymentRecordCode" v-model:value="searchParam.urlKey" placeholder=""
              autocomplete="off" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="paymentRecordCode">paramsKey：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="paymentRecordCode" v-model:value="searchParam.paramsKey" placeholder=""
              autocomplete="off" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="paymentRecordCode">respHeadersKey：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="paymentRecordCode" v-model:value="searchParam.respHeadersKey" placeholder=""
              autocomplete="off" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="paymentRecordCode">responseKey：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="paymentRecordCode" v-model:value="searchParam.responseKey" placeholder=""
              autocomplete="off" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">

          </template>
        </h-table>
      </h-col>
    </h-row>

  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
