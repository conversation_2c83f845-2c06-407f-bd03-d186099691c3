type keys = 'VALID' | 'INVALID' | 'MANY_ERRORS' ;

/**
 * 用户状态
 */
export const UserStateConstant = {
  VALID: { "state": 0, "name": "可用" },
  INVALID: { "state": 1, "name": "禁用" },
  MANY_ERRORS: { "state": 2, "name": "密码错误过多禁用" },


  of: (state?: number): { "state": number, "name": string } | null => {
    for (const key in UserStateConstant) {
      const item = UserStateConstant[key as keys];
      if (state === item.state) {
        return item;
      }
    }
    return null;
  }
}