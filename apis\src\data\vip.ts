import { get, post, download } from '../request'
import { IPageResponse, VipFilter, Result, VipType } from '@haierbusiness-front/common-libs'


export const vipApi = {
    /**
     * 查询vip列表
     */
    list: (params: VipFilter): Promise<IPageResponse<VipType>> => {
        return get('data/api/datart/filter-roster/page', params)
    },

    save: (params: VipFilter): Promise<Result> => {
        return post('data/api/datart/filter-roster/create', params)
    },
    create: (params: VipFilter): Promise<Result> => {
        return post('data/api/datart/filter-roster/create', params)
    },
    edit: (params: VipFilter): Promise<Result> => {
        return post('data/api/datart/filter-roster/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('data/api/datart/filter-roster/delete', { id })
    },
    download: (): Promise<void> => {
        return download('data/api/datart/filter-roster/get-import-template')
    },
}