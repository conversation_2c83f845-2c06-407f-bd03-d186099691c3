import { IApplicationInfoRequest, IApplicationInfo, Send_1Params, FileUploadResponse, IPageResponse, IApplicationInfoSaveRequest, IApplicationInfoUpdateRequest, IApplicationInfoDeleteRequest, IApplicationLinkResourceRequest } from '@haierbusiness-front/common-libs'
import { get, post, download } from '../request'

export const notificationApi = {

    /**
     * 查询模版列表
     */
    list: (params: Send_1Params): Promise<IPageResponse<IApplicationInfo>> => {
        return get('common/api/notification/searchList', params)
    },

    /**
     * 查询模版列表
     */
    getReadDetail: (params: Send_1Params): Promise<IPageResponse<IApplicationInfo>> => {
        return get('common/api/notification/getReadDetail', params)
    },


    /**
     * 新增模版
     */
    save: (params: Send_1Params): Promise<void> => {
        return post('common/api/notification/add', params)
    },

    /**
     * 修改模版
     */
    update: (params: IApplicationInfoUpdateRequest): Promise<void> => {
        return post('common/api/notification/update', params)
    },

    /**
     * 删除模版
     */
    delete: (params: IApplicationInfoDeleteRequest): Promise<void> => {
        return post('common/api/notification/delete', params)
    },

    /**
     * 上传飞书文件
     */
    upload: (params: FormData): Promise<FileUploadResponse> => {
        return post('common/api/notification/uploadImage', params, { 'content-type': 'multipart/form-data' })
    },

    /**
     * 获取预设模版列表
     */
    searchTemplate: (): Promise<IPageResponse<any>> => {
        return get('common/api/notification/searchTemplate')
    },

    /**
     * 查询详情
     */
    getInfoById: (id:string): Promise<IPageResponse<any>> => {
        return get('common/api/notification/getById',{id})
    },

    /**
     * 撤回消息 
     */
    getRevokeMessage: (id:string): Promise<IPageResponse<any>> => {
        return get('common/api/notification/revokeMessage',{notificationId:id})
    },

    /**
     * 终止消息 
     */
    getBreakSend: (id:string): Promise<IPageResponse<any>> => {
        return get('common/api/notification/breakSend',{id:id})
    },


    /**
     * 查询所有接收角色
     */
    getAllRole: (): Promise<IPageResponse<any>> => {
        return get('common/api/notification/getAllRole')
    },

    /**
     * 导出阅读明细
     */
    exportReadDetail: (params: any): Promise<void> => {
        return download('common/api/notification/exportReadDetail', params)
    },
}