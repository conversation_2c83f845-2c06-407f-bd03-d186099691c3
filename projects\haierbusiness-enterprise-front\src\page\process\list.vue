<script setup lang="ts">
import { DatePicker as hDatePicker,Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem, Input as hInput, Table as hTable, BreadcrumbItem as hBreadcrumbItem, Breadcrumb as hBreadcrumb, LayoutSider as hLayoutSider, LayoutFooter as hLayoutFooter, LayoutContent as hLayoutContent, LayoutHeader as hLayoutHeader, Layout as hLayout, MenuItem as hMenuItem, MenuItemGroup as hMenuItemGroup, SubMenu as hSubMenu, Menu as hMenu, Divider as hDivider, Space as hSpace, Button as hButton, Col as hCol, Result as hResult, Row as hRow, TabPane as hTabPane, Tabs as hTabs, message, TableProps } from 'ant-design-vue';
import { computed, onMounted, ref } from 'vue';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { userApi, enterpriseApi, processApi } from '@haierbusiness-front/apis';
import { usePagination, useRequest } from 'vue-request';
import { IProcessIno, IProcessListRequest } from '@haierbusiness-front/common-libs';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import EditDialog from './edit-dialog.vue'

const columns = [
  {
    title: '流程名称',
    dataIndex: 'name',
    width: '10%',
  },
  {
    title: '企业',
    dataIndex: 'enterpriseName',
    width: '10%',
  },
  {
    title: '是否可加审',
    dataIndex: 'additionable',
    width: '10%',
  },
  {
    title: '是否可撤回',
    dataIndex: 'revocable',
    width: '10%',
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '5%',
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '10%',
  },
  {
    title: '编辑',
    dataIndex: 'operator',
    width: '10%',
  },
];

const searchParam = ref<IProcessListRequest>({ })

const reset = () => {
  searchParam.value = {}
}

const {
  data,
  run: processApiRun,
  loading,
  current,
  pageSize,
} = usePagination(processApi.pages);

const {
  data: enterprises,
  run: userListApiRun
} = useRequest(enterpriseApi.list, {
  manual: false
});

const enterpriseSelect = computed(() => {
  return enterprises.value || []
})

const dataSource = computed(() => data.value?.records || []);
const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  processApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};


// 新增表单相关
const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } =
  useEditDialog<IProcessIno, IProcessIno>(processApi, "流程", () => processApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize,
}))

// const { handleDelete } = useDelete(processApi, () => processApiRun({
//   ...searchParam.value,
//   pageNum: data.value?.pageNum,
//   pageSize: data.value?.pageSize,
// }))
</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="username">流程名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="username" v-model:value="searchParam.name" placeholder="" autocomplete="off" />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="nickName">企业名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="nickName" v-model:value="searchParam.enterpriseName" placeholder="" autocomplete="off" />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchState">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.state" style="width: 100%;" allow-clear >
              <h-select-option :value="0" >无效</h-select-option>
              <h-select-option :value="1" >有效</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchState">是否可加审：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.additionable" style="width: 100%;" allow-clear >
              <h-select-option :value="0" >否</h-select-option>
              <h-select-option :value="1" >是</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchState">是否可撤回：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.revocable" style="width: 100%;" allow-clear >
              <h-select-option :value="0" >否</h-select-option>
              <h-select-option :value="1" >是</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :offset="20" :span="4" style="text-align: right;">
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined /> 查询
            </h-button>
            <h-button style="margin-left: 10px" @click="reset">重置</h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: left;">
            <h-button type="primary" @click="handleCreate">
              <PlusOutlined /> 新增流程
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :data-source="dataSource" :pagination="pagination" :size="'small'"
          :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'enterprise' && record.enterpriseCode">
              {{ record.enterpriseName }}({{ record.enterpriseCode }})
            </template>
            <template v-if="column.dataIndex === 'state'">
              {{ record.state ? '有效' : '无效' }}
            </template>
            <template v-if="column.dataIndex === 'additionable'">
              {{ record.additionable ? '是' : '否' }}
            </template>
            <template v-if="column.dataIndex === 'revocable'">
              {{ record.revocable ? '是' : '否' }}
            </template>
            <template v-if="column.dataIndex === 'operator'">
              <h-button type="link"  @click="handleEdit(record)">编辑</h-button>
              <!-- <h-button type="link"  @click="handleDelete(record.id)">删除</h-button> -->
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

  </div>

  <div v-if="visible">
    <edit-dialog
        :show="visible"
        :data="editData"
        @cancel="onDialogClose"
        @ok="handleOk"
    >
    </edit-dialog>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}
</style>
