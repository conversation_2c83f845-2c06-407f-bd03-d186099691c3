<template>
    <div background="rgba(0,0,0,0)">
        <Rank :data="rankData" unit="万元" />
    </div>
</template>
<script setup lang="ts">
import Rank from "../../components/rank.vue";
import { queryLocalHotelRank } from "@haierbusiness-front/apis/src/data/board/mice";
import { ref, onMounted } from "vue";
import { EventBus } from "../../eventBus";
const props = defineProps({
    type: {
        type: String,
        default: "青岛会议",
    },
});
const rankData = ref(
    [] as Array<{
        name: string;
        value: string | number;
    }>
);
const loading = ref(false);
EventBus.on((event, params) => {
    if (event == "refresh") queryData(params ? params : null);
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
    loading.value = true;
    const data = await queryLocalHotelRank(
        {
            type: props.type,
        },
        params ? params.data.name : null,
        params ? params.from : null
    );
    loading.value = false;
    const rows = [] as Array<{
        name: string;
        value: string | number;
    }>;
    data.rows.forEach((item) => {
        const value = (item[1] / 10000).toFixed(item[1] > 10000 ? 0 : 2);
        rows.push({
            name: item[0],
            value,
        });
    });
    rankData.value = rows;
};

onMounted(() => {
    queryData()
})
</script>
