<script setup>
  defineProps(['name', 'desc', 'icon', 'tag'])
</script>

<template>
  <div class="process-card">
    <img class="icon" :src="icon" />
    <div class="name">{{ name }}</div>
    <div class="desc">{{ desc }}</div>
    <div class="tag">{{ tag }}</div>
  </div>
</template>

<style scoped lang="less">
  .process-card {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(255, 255, 255, 0.8);
    box-shadow: 0px 3px 12px 0px rgba(1,12,51,0.07);
    border-radius: 16px;
    border: 1px solid rgba(24,104,219,0.1);
    backdrop-filter: blur(0px);
    padding: 38px 35px 24px;
    .icon {
      width: 36px;
      height: 36px;
    }
    .name {
      margin-top: 20px;
      font-weight: 500;
      font-size: 20px;
      color: #1D2129;
      line-height: 28px;
    }
    .desc {
      margin-top: 12px;
      font-size: 16px;
      color: #595959;
      line-height: 22px;
      text-align: center;
    }
    .tag {
      position: absolute;
      top: 0;
      left: 0;
      background-image: linear-gradient(to right, #1C70DE, #329DEE);
      color: #fff;
      padding: 5px 10px;
      border-radius: 12px 0;
      font-style: italic;
    }
  }
</style>
