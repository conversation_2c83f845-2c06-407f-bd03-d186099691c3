<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  Table as hTable,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON>on,
  Modal as hModal,
  Form as HForm,
  FormItem as HFormItem,
  Input as HInput,
  Switch as HSwitch,
  Select as HSelect,
  SelectOption as HSelectOption,
  Textarea as HTextarea,
  message,
} from 'ant-design-vue'
import { useRouter } from 'vue-router'
import {
  waterworkAddressApi as api,
  waterworkAreaApi,
  waterworkBuildingApi,
} from '@haierbusiness-front/apis'
import { filterableSelectOption } from '@/utils/select'
import { maskString } from '@/utils/common'
const router = useRouter()

const visibleModal = ref(false)
const loading = ref(false)
const editingId = ref<string | null>(null)
const formData = ref({
  userType: '',
  receiverName: '',
  userCode: '01506579',
  regionId: '',
  buildId: '',
  receiverPhone: '',
  receiverAddress: '',
  isDefault: '0',
})

const dataSource = ref({
  records: [],
  total: 0,
  pageNum: 1,
  pageSize: 10,
})

const tableProps = computed(() => ({
  dataSource: dataSource.value.records,
  columns: [
    {
      title: '收货人',
      dataIndex: 'receiverName',
      width: '15%',
    },
    {
      title: '联系电话',
      dataIndex: 'receiverPhone',
      width: '15%',
    },
    {
      title: '收货地址',
      dataIndex: 'receiverAddress',
      width: '25%',
    },
    {
      title: '是否设置为默认地址',
      dataIndex: 'isDefault',
      width: '15%',
      customRender: ({ text }: { text: number }) => (text ? '是' : '否'),
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: '20%',
    },
  ],
  pagination: {
    showSizeChanger: true,
    showQuickJumper: true,
    total: dataSource.value?.total,
    current: dataSource.value?.pageNum,
    pageSize: dataSource.value?.pageSize,
    style: { justifyContent: 'center' },
  },
}))

const options = ref({
  area: <any>[],
  building: <any>[],
})

onMounted(() => {
  getAddressList()
  getAreaList()
})

const getAddressList = async () => {
  let res = await api.list()
  dataSource.value = res
  console.log(res, '调用获取地址列表API')
}

const getAreaList = async () => {
  let res = await waterworkAreaApi.listAll({
    userCode: formData.value.userCode,
    status: 1,
  })
  options.value.area = res
  console.log(res, '调用获取区域')
}
const getBuildingList = async (regionId: any) => {
  let res = await waterworkBuildingApi.listAll({
    regionId,
    status: 1,
  })
  options.value.building = res
  console.log(res, '调用获取建筑')
}

const setDefaultAddress = async (checked: any, record: any) => {
  console.log(checked, '更改')
  loading.value = true
  try {
    await api.update({
      id: record.id,
      isDefault: checked,
    })
    message.success('操作成功')
    await getAddressList()
  } finally {
    loading.value = false
  }
}

const changeArea = async (val: any) => {
  console.log(val, '改变区域')
  formData.value.buildId = ''
  options.value.building = []
  getBuildingList(val)
}

/**
 * @新增相关
 * */
const formRef = ref()

const resetForm = () => {
  formData.value = {
    userType: '',
    receiverName: '',
    userCode: '01506579',
    regionId: '',
    buildId: '',
    receiverPhone: '',
    receiverAddress: '',
    isDefault: '0',
  }
  editingId.value = null
}

const handleEdit = (record: any) => {
  editingId.value = record.id
  formData.value = { ...record }
  visibleModal.value = true
}

const handleDelete = async (id: string) => {
  await new Promise((resolve, reject) => {
    hModal.confirm({
      title: '提示',
      content: '确定要删除这条数据吗?',
      onOk() {
        resolve(null)
      },
      onCancel() {
        reject()
      },
    })
  })
  loading.value = true
  try {
    await api.delete(id)
    message.success('操作成功')
    await getAddressList()
  } finally {
    loading.value = false
  }
}

const handleOk = async () => {
  await formRef.value.validate()
  let param = {
    ...formData.value,
  }
  if (formData.value.userType == '10') {
    param.regionId = ''
    param.buildId = ''
  }
  loading.value = true
  try {
    if (editingId.value) {
      param.id = editingId.value
      await api.update(param)
      message.success('更新成功')
    } else {
      await api.add(param)
      message.success('创建成功')
    }
    // console.log(param, '提交')
    visibleModal.value = false
    handleCancel()
    getAddressList()
  } catch (error) {
    message.error(editingId.value ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  visibleModal.value = false
  formRef.value.resetFields()
  resetForm()
}
</script>

<template>
  <div class="order-receiverAddress">
    <div>
      <h-button type="primary" @click="visibleModal = true">新增收货地址</h-button>
    </div>
    <h-table style="margin-top: 12px" v-bind="tableProps" :loading="loading">
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'action'">
          <h-button type="link" @click="handleEdit(record)">编辑</h-button>
          <h-button type="link" danger @click="handleDelete(record.id)">删除</h-button>
        </template>
        <template v-if="column.dataIndex == 'isDefault'">
          <h-switch
            v-model:checked="record.isDefault"
            checkedValue="1"
            unCheckedValue="0"
            @change="(e) => setDefaultAddress(e, record)"
          />
        </template>
        <template v-if="column.dataIndex == 'receiverPhone'">
          {{ maskString(text) }}
        </template>
      </template>
    </h-table>
  </div>

  <h-modal
    v-model:visible="visibleModal"
    :title="editingId ? '编辑地址' : '新增地址'"
    @ok="handleOk"
    @cancel="handleCancel"
    :ok-button-props="{ disabled: loading }"
    :cancel-button-props="{ disabled: loading }"
    :closable="false"
  >
    <h-form
      ref="formRef"
      :model="formData"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 16 }"
    >
      <h-form-item label="用户类型" name="userType" required>
        <h-select v-model:value="formData.userType" placeholder="请选择用户类型">
          <h-select-option value="10">月结</h-select-option>
          <h-select-option value="20">预算</h-select-option>
        </h-select>
      </h-form-item>
      <template v-if="formData.userType === '20'">
        <h-form-item label="园区" name="regionId" required>
          <h-select
            v-model:value="formData.regionId"
            style="width: 100%"
            v-bind="filterableSelectOption"
            @change="changeArea"
          >
            <h-select-option
              :value="item.id"
              v-for="item in options.area"
              :key="item.id"
              :label="item.regionNm"
            >
              {{ item.regionNm }}
            </h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="建筑" name="buildId" required>
          <h-select
            v-model:value="formData.buildId"
            style="width: 100%"
            v-bind="filterableSelectOption"
          >
            <h-select-option
              :value="item.id"
              v-for="item in options.building"
              :key="item.id"
              :label="item.buildNm"
            >
              {{ item.buildNm }}
            </h-select-option>
          </h-select>
        </h-form-item>
      </template>
      <h-form-item label="收货人" name="receiverName" required>
        <h-input v-model:value="formData.receiverName" placeholder="请输入收货人姓名" />
      </h-form-item>
      <h-form-item label="联系电话" name="receiverPhone" required>
        <h-input v-model:value="formData.receiverPhone" placeholder="请输入联系电话" />
      </h-form-item>
      <h-form-item label="收货地址" name="receiverAddress" required>
        <h-textarea
          v-model:value="formData.receiverAddress"
          placeholder="请输入详细地址"
        />
      </h-form-item>
      <h-form-item label="是否设置为默认地址" name="isDefault">
        <!-- <h-switch v-model:value="formData.isDefault" /> -->
        <h-switch
          v-model:checked="formData.isDefault"
          checkedValue="1"
          unCheckedValue="0"
        />
      </h-form-item>
    </h-form>
  </h-modal>
</template>

<style lang="less" scoped>
.order-receiverAddress {
  width: 100%;
  height: 100%;
}
</style>
