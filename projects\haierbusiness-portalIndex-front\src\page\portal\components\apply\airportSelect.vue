<script setup lang="ts">
import { onMounted, ref, watch,onUnmounted } from 'vue';
import type { CascaderProps } from 'ant-design-vue';
import { Cascader } from 'ant-design-vue';
import airportSelect from '@haierbusiness-front/components/airportSelect/index.vue';

interface Props {
    travelType?: number
    leftNum?: string
    form?: any
}

const props = withDefaults(defineProps<Props>(), {
    travelType: 1,
    form:{}
});

const options: CascaderProps['options'] = [
    {
        value: 'sd',
        label: '山东',
        children: [
            {
                value: 'qd',
                label: '青岛',
                children: [
                    {
                        value: 'jdgjjc',
                        label: '胶东国际机场T1 航站楼'
                    }
                ]
            },
            {
                value: 'jn',
                label: '济南',
                children: [
                    {
                        value: 'yqjc',
                        label: '遥墙机场T1 航站楼'
                    }
                ]
            },
            {
                value: 'yt',
                label: '烟台',
                children: [
                    {
                        value: 'pljc',
                        label: '蓬莱机场'
                    }
                ]
            },
        ],
    },
    {
        value: 'bj',
        label: '北京',
        children: [
            {
                value: 'dxjc',
                label: '大兴机场'
            },
            {
                value: 't1',
                label: '首都国际机场T1 航站楼'
            },
            {
                value: 't2',
                label: '首都国际机场T2 航站楼'
            },
            {
                value: 't3',
                label: '首都国际机场T3 航站楼'
            }
        ]
    },
    {
        value: 'js',
        label: '江苏',
        children: [
            {
                value: 'dz',
                label: '南京',
                children: [
                    {
                        value: 'lkjct1',
                        label: '禄口机场T1 航站楼'
                    },
                    {
                        value: 'lkjct2',
                        label: '禄口机场T2 航站楼'
                    }
                ]
            },
        ]
    },
]

const value = ref<string[]>([]);

const eliminateSearchProvinceIds = [7,16,17]

const emit = defineEmits(['chosedTerminal'])


interface Props {
    form?: any
    showInternational?: boolean
}

// 单程，返程
const leftNum = ref(props.leftNum)
const showInternational = ref(props.showInternational)
const form = ref(props.form)


watch(props, (newValue) => {
    leftNum.value = newValue.leftNum ?? '1'
    form.value = props.form
    showInternational.value = props.showInternational
})

const chosedTerminal = (city: any,airport:any,terminal:any ) => {
    emit('chosedTerminal', city,airport,terminal)
};

</script>


<template>
    <div class="apply-airport-component">
        <div class="ticket-item-city" >
            <div class="ticket-item" >
                <div class="item-labels">机场</div>
                <airport-select
                  class="mr-10 city-box-index"
                  placeholder="出发机场"
                  width="100%"
                  :defaultValue="form"
                  :value="form?.terminalText"
                  :showInternational="showInternational"
                  :eliminateSearchProvinceIds="eliminateSearchProvinceIds"
                  :bordered="false"
                  @chosedCity="chosedTerminal"
                >
                </airport-select>
            </div>
        </div>
    </div>
    
</template>

<style lang="less" scoped>
@import url('./common.less');


</style>

<style>
.apply-airport-component .ant-select-selector {
  border:none !important;
  box-shadow: none !important;
  height: 22px !important;
  padding: 0px !important;
  
}

.apply-airport-component .ant-select-selection-placeholder {
    font-size: 16px !important;
    color: rgba(0,0,0,0.35) !important;
    
    padding-inline-end: 25px !important;
    display: flex;
    align-items: center;
}

.apply-airport-component .ant-select-selection-item {
    display: flex;
    align-items: center;
    font-size: 16px !important;
    
    color: rgba(0,0,0,0.85);
}

.apply-airport-component .ant-select-item-option-content {
  
}

.apply-airport-component .ant-cascader {
    width: 100%;
}

.ant-cascader-menus {
    
}
</style>
