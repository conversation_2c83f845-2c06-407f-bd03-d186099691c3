<script lang="ts" setup>
import { computed, createVNode, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';

import {
  Anchor as hAnchor,
  <PERSON><PERSON> as hButton,
  Spin as hSpin,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Checkbox as hCheckbox,
  Form as hForm,
  FormItem as hFormItem,

  Row as hRow,
  Col as hCol,
  Popconfirm as hPopconfirm,
  Upload as hUpload,
  Input as hInput,
  Switch as hSwitch,
  Modal,
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  DownloadOutlined,
  VerticalAlignBottomOutlined,
  ArrowUpOutlined,
  VerticalAlignTopOutlined,
  ExclamationCircleFilled,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  SendOutlined,
  PhoneOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IUserListRequest, IUserInfo,TTicket,TErrorMessage, THotel, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { download, tripApi, teamListApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import relativeTime from 'dayjs/plugin/relativeTime';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';

import { CityResponse, CityItem, TCteateTeam } from '@haierbusiness-front/common-libs';
import { fileApi } from '@haierbusiness-front/apis';
import type { TableColumnType } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';

const HotelTemp = new URL('@/assets/template/team_hotel_template.xlsx', import.meta.url).href;
const TicketTemp = new URL('@/assets/template/team_tickets_template.xlsx', import.meta.url).href;

const router = useRouter();


const route = ref(getCurrentRoute());

const id = route.value?.query?.id;

dayjs.extend(relativeTime);

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const labelCol = { span: 6 };
const wrapperCol = { span: 18 };



const ticketColumns = [
  {
    title: '票号',
    dataIndex: 'ticketNum',
    width: 150,
  },
  {
    title: '乘机人',
    className: 'column-money',
    dataIndex: 'passenger',
    width: 100,
  },
  {
    title: '起飞时间',
    dataIndex: 'departureTime',
    width: 200,
  },

  {
    title: '航班号',
    dataIndex: 'flightNum',
    width: 100,
  },

  {
    title: '订单号',
    dataIndex: 'orderNum',
    width: 200,
  },

  {
    title: '团队价(含税)',
    dataIndex: 'teamPrice',
    fixed: 'right',
    width: 100,
  },

  {
    title: '非团队价(含税)',
    dataIndex: 'nonTeamPrice',
    fixed: 'right',
    width: 120,
  },
];

const hotelColumns = [
  // 序号列
  {
    title: '序号',
    dataIndex: 'serialNumber', // 这个dataIndex仅作为标识，不对应实际数据字段
    width: '80px', // 可以自定义宽度
    scopedSlots: { customRender: 'bodyCell' }, // 对应模板中的具名插槽
  },
  {
    title: '酒店名称',
    dataIndex: 'hotelName',
    width: 300,
  },

  {
    title: '房型',
    dataIndex: 'roomType',
    width: 120,
  },

  {
    title: '入住人',
    dataIndex: 'occupant',
    width: 150,
  },

  {
    title: '预订入住日期',
    dataIndex: 'checkInTime',
    width: 120,
  },

  {
    title: '预订离店日期',
    dataIndex: 'checkOutTime',
    width: 120,
  },

  {
    title: '入住天数',
    dataIndex: 'liveDays',
    width: 100,
  },

  {
    title: '订单号',
    dataIndex: 'orderNum',
    width: 200,
  },

  {
    title: '团队价(含税)',
    dataIndex: 'teamPrice',
    fixed: 'right',
    width: 100,
  },

  {
    title: '非团队价(含税)',
    dataIndex: 'nonTeamPrice',
    fixed: 'right',
    width: 120,
  },
];

// 接收订单
const receive = () => {
  Modal.confirm({
    title: '确认要接收此团队订单吗?',
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {
      teamListApi.receive(detail.value).then((res) => {
        getDetail(id);
      });
    },
    onCancel() {},
  });
};

const detail = ref<TCteateTeam>({});

const hodelList = ref<Array<object>>([])
const ticketList = ref<Array<object>>([])


const getDetail = (id: string) => {
  teamListApi.teamDetail(id).then((res) => {
    if (res?.teamDestineHotel?.fileName) {
      hodelList.value = [{
        name: res.teamDestineHotel.fileName,
        url: baseUrl + res.teamDestineHotel.filePath,
      }]
    }
    
    if (res?.teamDestinePlaneTicket?.fileName) {
      ticketList.value = [{
        name: res.teamDestinePlaneTicket.fileName,
        url: baseUrl + res.teamDestinePlaneTicket.filePath,
      }]
    }

    // 酒店明细附件列表
    if(res.teamDestineHotel?.fileMapList?.length > 0) {
      res.teamDestineHotel.fileMapList.forEach(element => {
        element.name = element.fileName
        element.url = element.filePath
      });
    }
    // 酒店明细附件列表
    if(res.teamDestinePlaneTicket?.fileMapList?.length > 0) {
      res.teamDestinePlaneTicket.fileMapList.forEach(element => {
        element.name = element.fileName
        element.url = element.filePath
      });
    }

    detail.value = res;


    detail.value.teamDestineHotel = detail.value.teamDestineHotel ? detail.value.teamDestineHotel : {}
    detail.value.teamDestinePlaneTicket = detail.value.teamDestinePlaneTicket ? detail.value.teamDestinePlaneTicket : {}
    hotelFlag.value = detail.value.teamDestineHotel.ticketFlag ? true : false
    ticketFlag.value = detail.value.teamDestinePlaneTicket.ticketFlag ? true : false
    
    
  });
};

const baseUrl = import.meta.env.VITE_BUSINESS_URL;

//-------------- 机票明细上传相关
const ticketBtnLoading = ref(false)



// 上传机票明细
const uploadTicket = (options: any) => {
  const formData = new FormData();
  formData.append('file', options.file);
  ticketBtnLoading.value = true
  teamListApi
    .ticketImport(formData)
    .then((it) => {
      // 错误信息
      if (it?.planeTicketDetailErrorList?.length > 0 ) {
        xlsErrList.value = it.planeTicketDetailErrorList
        errMessageModel.value = true
      }

        const detailTemp:TTicket = it
        detail.value.teamDestinePlaneTicket = {...detail.value.teamDestinePlaneTicket, ...detailTemp}
        detail.value.teamDestinePlaneTicket.ticketFlag = 1
        ticketFlag.value =true
      ticketBtnLoading.value = false
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .catch((err) => {
      console.log(9999, err)
      ticketList.value =[]
    })
    .finally(() => {
      ticketBtnLoading.value = false
    });
};

// 上传机票附件
const uploadTicketFile = (options:any) => {
  
  const formData = new FormData();
  formData.append('file', options.file);
  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;
      options.file.url = baseUrl + it.path;
      options.file.fileName = options.file.name;
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .finally(() => {
    });

}


//-------------- 酒店明细上传相关
const hotelBtnLoading = ref(false)

const xlsErrList = ref<Array<TErrorMessage>>([])

const errMessageModel = ref<boolean>(false)
const beforeSubmitModel = ref<boolean>(false)


// 上传酒店明细
const uploadHotel = (options: any) => {
  hotelBtnLoading.value = true
  const formData = new FormData();
  formData.append('file', options.file);
  teamListApi
    .hotelImport(formData)
    .then((it) => {

      // 酒店错误信息
      if (it?.hotelDetailErrorList?.length > 0 ) {
        xlsErrList.value = it.hotelDetailErrorList
        errMessageModel.value = true
      }

        const hotelDetailTemp:THotel = it
        detail.value.teamDestineHotel = {...detail.value.teamDestineHotel, ...hotelDetailTemp}
        // 明细导入后自动改成已出票
        detail.value.teamDestineHotel.ticketFlag = 1
        hotelFlag.value =true

      hotelBtnLoading.value = false
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .catch(() => {
      hodelList.value =[]
    })
    .finally(() => {
      hotelBtnLoading.value = false
    });
};

// 上传机票附件
const uploadHotelFile = (options:any) => {
  
  const formData = new FormData();
  formData.append('file', options.file);
  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;
      options.file.url = baseUrl + it.path;
      options.file.fileName = options.file.name;
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .finally(() => {
    });

}

// 勾选机票出票
const ticketFlag = ref(false)
const changeTicketFlag = (checked: boolean | string | number) => {
  const flagTemp: TTicket = {
    ticketFlag: checked ? 1 : 0
  }
  detail.value.teamDestinePlaneTicket = {...detail.value.teamDestinePlaneTicket , ...flagTemp}
  // detail.value.teamDestinePlaneTicket.remarks = ""

}

// 勾选酒店出票
const hotelFlag = ref(false)
const changeHotelFlag = (checked: boolean | string | number) => {
  const flagTemp: THotel = {
    ticketFlag: checked ? 1 : 0
  }
  detail.value.teamDestineHotel = {...detail.value.teamDestineHotel , ...flagTemp}

  // detail.value.teamDestineHotel.remarks = ""
}

const lastSubmitTime = ref<string>();

const save = () => {
  teamListApi.saveDetail(detail.value).then((res) => {
      message.success('保存成功!');
      lastSubmitTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
    });
};
const submitLoading = ref<boolean>(false)

const submit = () => {
  submitLoading.value = true;

  beforeSubmitModel.value = false
      teamListApi.submit(detail.value).then((res) => {
        message.success('提交成功!');
        submitLoading.value = false;
        goToDetail()
      }).catch(() => {
        submitLoading.value = false;
      })
 
  
}

const goToDetail = () => {
  router.push({ path: "/teamList"} )
};

watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);
</script>


<template>
  <div class="container">
    <div class="row flex">
      <div class="change-title" v-if="detail.destineNo">
        <h-row class="mb-10">
          <h-col :span="6" style="color: #00000073">需求单号:</h-col>
          <h-col :span="18">{{ detail.destineNo }}</h-col>
        </h-row>
        <h-row class="mb-10">
          <h-col :span="6" style="color: #00000073">申请时间:</h-col>
          <h-col :span="18">{{ detail.gmtCreate }}</h-col>
        </h-row>
      </div>

      <div class="main-title">
        <!-- <img src="../../assets/image/trip/title.png" alt="" /> -->
        <span>团队订票订房需求-订单预订</span>
      </div>
      <div class="apply-con flex">
        <h-form
          class="mt-30"
          ref="formRef"
          :model="detail"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          labelAlign="left"
        >
          <!-- 基本信息 -->

          <div class="title whole-line" id="base-info">需求</div>

          <h-row>
            <h-col :span="12">
              <h-form-item name="createName">
                <template #label>
                  <span
                    >经办人<a-tooltip>
                      <template #title>需求经办人</template>
                      <question-circleOutlined class="icon" /> </a-tooltip
                  ></span>
                </template>
                <div>{{ detail?.createName }}/{{ detail?.createBy }}</div>
              </h-form-item>
            </h-col>

            <h-col :span="12">
              <h-form-item name="contactUserName">
                <template #label>
                  <span
                    >联系人<a-tooltip>
                      <template #title>订单联系人</template>
                      <question-circleOutlined class="icon" /> </a-tooltip
                  ></span>
                </template>
                <div>
                  {{ detail?.contactUserName }}/{{ detail?.contactUserCode }}( <span><PhoneOutlined /></span>
                  {{ detail?.contactUserPhone }})
                </div>
              </h-form-item>
            </h-col>

            <h-col :span="12">
              <h-form-item name="endCityName">
                <template #label>
                  <span
                    >目的城市<a-tooltip>
                      <template #title>行程目的城市</template>
                      <question-circleOutlined class="icon" /> </a-tooltip
                  ></span>
                </template>
                <div>
                  {{ detail?.beginCityName }} <span>-</span> {{ detail?.endCityName }}
                </div>
              </h-form-item>
            </h-col>

            <h-col :span="12">
              <h-form-item name="beginDate">
                <template #label>
                  <span
                    >行程时间<a-tooltip>
                      <template #title>购票行程时间</template>
                      <question-circleOutlined class="icon" /> </a-tooltip
                  ></span>
                </template>
                <div>
                  {{ detail?.beginDate }}<span>-</span> {{ detail?.endDate }}
                </div>
              </h-form-item>
            </h-col>

            <h-col :span="12">
              <h-form-item name="evectionType">
                <template #label>
                  <span
                    >出差类型<a-tooltip>
                      <template #title>团队出差类型</template>
                      <question-circleOutlined class="icon" /> </a-tooltip
                  ></span>
                </template>
                <div>{{ detail?.evectionType == 0 ? '因公' : '因私' }}</div>
              </h-form-item>
            </h-col>

            <h-col :span="12">
              <h-form-item name="travelerFileName">
                <template #label>
                  <span
                    >出行人<a-tooltip>
                      <template #title>订单出行人</template>
                      <question-circleOutlined class="icon" /> </a-tooltip
                  ></span>
                </template>
                <div class="color-main pointer">
                 
                  <a :href="detail?.travelerFileUrl"  v-if=" detail?.travelerFileName " class="color-main pointer" ><DownloadOutlined />  {{ detail?.travelerFileName}}</a>
                </div>
              </h-form-item>
            </h-col>
          </h-row>

          <a-divider />

          <!-- 产品类型 -->
          <h-form-item
            :labelCol="{ span: 3 }"
            :wrapperCol="{ span: 21 }"
            v-if="detail?.destineInfo"
          >
            <template #label>
              <span
                >产品类型<a-tooltip>
                  <template #title>行程产品类型</template>
                  <question-circleOutlined class="icon" /> </a-tooltip
              ></span>
            </template>
            <!-- 机票 -->
            <h-row
              class="background-eee"
              v-if="detail?.destineInfo?.indexOf('0') > -1 || detail?.destineInfo?.indexOf('1') > -1"
            >
              <img class="product-icon" src="../../assets/image/trip/plane.png" alt="" />
              <span>{{ !detail?.teamDestinePlaneTicket?.planeTicketType ? '国内机票' : '国际机票' }}</span>
              <a-divider type="vertical" />
              <span>{{ detail?.teamDestinePlaneTicket?.voyageType ? '往返' : '单程' }}</span>
              <a-divider type="vertical" />
              <span>{{ detail?.teamDestinePlaneTicket?.travelPriod ? '下午' : '上午' }}出行</span>
              <a-divider type="vertical" />
              <span>{{ detail?.teamDestinePlaneTicket?.travelerNum || 0 }}人出行(成人)</span>
              <a-divider type="vertical" />
              <div style="word-break: break-all;">{{ detail?.teamDestinePlaneTicket?.otherInfo || '无其他需求' }}</div>

            </h-row>

            <!-- 酒店 -->
            <h-row class="background-eee" v-if="detail.destineInfo?.indexOf('2') > -1">
              <img class="product-icon" src="../../assets/image/trip/hotel.png" alt="" />
              <span>酒店</span>
              <a-divider type="vertical" />
              <span>{{ detail?.teamDestineHotel?.kingRoomNum || 0 }} 间大床房</span>
              <a-divider type="vertical" />
              <span> {{ detail?.teamDestineHotel?.doubleRoomNum || 0 }} 间双床房</span>
              <a-divider type="vertical" />
              <div style="word-break: break-all;">{{ detail?.teamDestineHotel?.otherInfo || '无其他需求' }}</div>

            </h-row>
          </h-form-item>

          <!-- 订单预订 -->
          <div class="title whole-line" id="base-info">订单预订</div>
          <!-- 国内机票明细 -->
          <h-form-item
            :labelCol="{ span: 3 }"
            :wrapperCol="{ span: 21 }"
            v-if="detail?.destineInfo?.indexOf('0') > -1 || detail?.destineInfo?.indexOf('1') > -1"
          >
            <template #label>
              <span
                >{{ !detail?.teamDestinePlaneTicket?.planeTicketType ? '国内机票' : '国际机票'}}<a-tooltip>
                  <template #title>{{
                    !detail?.teamDestinePlaneTicket?.planeTicketType ? '国内机票' : '国际机票'
                  }}</template>
                  <question-circleOutlined class="icon" /> </a-tooltip
              ></span>
            </template>
            <h-row>
              <h-col :span="7">
                <h-form-item >
                  <template #label>
                    <span>团队价合计(含税)</span>
                  </template>
                  <div>¥ {{ detail?.teamDestinePlaneTicket?.teamPriceTotal || 0 }}</div>
                </h-form-item>
              </h-col>

              <h-col :span="7">
                <h-form-item >
                  <template #label>
                    <span>非团队价合计(含税)</span>
                  </template>
                  <div>¥ {{ detail?.teamDestinePlaneTicket?.nonTeamAmount || 0 }}</div>
                </h-form-item>
              </h-col>

              <h-col :span="6">
                <h-form-item >
                  <template #label>
                    <span>节省费用</span>
                  </template>
                  <div>¥ {{ detail?.teamDestinePlaneTicket?.saveAmount || 0 }}</div>
                </h-form-item>
              </h-col>

              
            </h-row>

            <a-table
              :columns="ticketColumns"
              :data-source="detail?.teamDestinePlaneTicket?.planeTicketDetailList"
              bordered
              size="small"
              :pagination="false"
              :scroll="{ x: 1000, y: 400 }"
            >
              <template #bodyCell="{ column, text }">
                <template v-if="column.dataIndex === 'name'">
                  <a>{{ text }}</a>
                </template>
              </template>
              <!-- <template #footer>备注</template> -->
            </a-table>

            <h-row class="mt-10">
              <h-col :span="20" class="flex">
                <h-button class="mr-10" >
                  <template #icon><VerticalAlignBottomOutlined /></template>

                  <a :href="TicketTemp" download="团队票机票导入模板">
                  下载模板
                  </a>
                  
                </h-button>

                <h-upload name="tixketFileMx" v-model:file-list="ticketList"  :custom-request="uploadTicket" :max-count="1">
                  <h-button class="mr-10" :loading="ticketBtnLoading" >
                    <VerticalAlignTopOutlined />
                    <span class="font-size-14">明细导入</span>
                  </h-button>
                </h-upload>

                <h-upload name="tixketFileFj" v-model:file-list="detail.teamDestinePlaneTicket.fileMapList" :custom-request="uploadTicketFile">
                  <h-button class="mr-10">
                    <VerticalAlignTopOutlined />
                    <span class="font-size-14">上传附件</span>
                  </h-button>
                </h-upload>
              </h-col>
              

              <h-col :span="4">
                <h-form-item name="ticketFlag" label="是否预订">
                  <h-switch v-model:checked="ticketFlag" @change="changeTicketFlag" checked-children="是" un-checked-children="否" />
                </h-form-item>
              </h-col>

              
            </h-row>
            <h-row class="mt-10" v-if="!ticketFlag">
              <h-col :span="24" >
                <h-form-item name="ticketFlag" label="备注">
                   <h-input v-model:value="detail.teamDestinePlaneTicket.remarks"></h-input>
                </h-form-item>
              </h-col>
            </h-row>
          </h-form-item>

          <!-- 酒店明细 -->
          <h-form-item
            v-if="detail?.destineInfo?.indexOf('2') > -1"
            :labelCol="{ span: 3 }"
            :wrapperCol="{ span: 21 }"
          >
            <template #label>
              <span
                >酒店<a-tooltip>
                  <template #title>酒店</template>
                  <question-circleOutlined class="icon" /> </a-tooltip
              ></span>
            </template>
            <h-row>
              <h-col :span="7">
                <h-form-item >
                  <template #label>
                    <span>团队价合计(含税)</span>
                  </template>
                  <div>¥ {{detail?.teamDestineHotel?.teamPriceTotal || 0}}</div>
                </h-form-item>
              </h-col>

              <h-col :span="7">
                <h-form-item >
                  <template #label>
                    <span>非团队价合计(含税)</span>
                  </template>
                  <div>¥ {{detail?.teamDestineHotel?.nonTeamAmount || 0}}</div>
                </h-form-item>
              </h-col>

              <h-col :span="6">
                <h-form-item >
                  <template #label>
                    <span>节省费用</span>
                  </template>
                  <div>¥ {{detail?.teamDestineHotel?.saveAmount || 0}}</div>
                </h-form-item>
              </h-col>

              
            </h-row>

            <a-table
              size="small"
              :columns="hotelColumns"
              :data-source="detail?.teamDestineHotel?.hotelDetailList"
              bordered
              :pagination="false"
              :scroll="{ x: 1200, y: 400 }"
            >
              <template #bodyCell="{ column, text, record, index }">
                <template v-if="column.dataIndex === 'serialNumber'">
                  <a>{{ index + 1 }}</a>
                </template>

              </template>
              <!-- <template #footer>备注</template> -->
            </a-table>

            <h-row class="mt-10">
              <h-col :span="20" class="flex">

              <h-button class="mr-10" >
                <template #icon><VerticalAlignBottomOutlined /></template>
                
                <a :href="HotelTemp" download="团队票酒店导入模板">
                  下载模板
                  </a>
              </h-button>

              <h-upload name="file" v-model:file-list="hodelList" :custom-request="uploadHotel" :max-count="1">
                <h-button class="mr-10" :loading="hotelBtnLoading">
                  <VerticalAlignTopOutlined />
                  <span class="font-size-14">明细导入</span>
                </h-button>
              </h-upload>

              <h-upload name="file"  v-model:file-list="detail.teamDestineHotel.fileMapList" :custom-request="uploadHotelFile">
                <h-button class="mr-10">
                  <VerticalAlignTopOutlined />
                  <span class="font-size-14">上传附件</span>
                </h-button>
              </h-upload>
            </h-col>

              <h-col :span="4">
                <h-form-item name="hotelFlag" label="是否预订">
                  <h-switch v-model:checked="hotelFlag" @change="changeHotelFlag" checked-children="是" un-checked-children="否" />
                </h-form-item>
              </h-col>

              
            </h-row>

            <h-row class="mt-10" v-if="!hotelFlag">
              <h-col :span="24">
                <h-form-item name="ticketFlag" label="备注">
                   <h-input v-model:value="detail.teamDestineHotel.remarks"></h-input>
                </h-form-item>
              </h-col>
            </h-row>
          </h-form-item>
        </h-form>
      </div>
    </div>
    <a-affix :offset-bottom="0" id="affix-bottom" class="affix-bottom">
      <div class="save-box flex">
        <div class="box-center">
          <div class="save-box-left font-size-14"></div>
          <div class="save-box-right flex">
            <div class="auto-text font-size-14 mr-20 font-color" v-if="lastSubmitTime">
                <check-circle-two-tone two-tone-color="#52c41a" />
                {{ dayjs(lastSubmitTime).fromNow() }}保存
              </div>
            <div class="save-btns">
                <!-- <h-popconfirm title="确定取消编辑并返回列表页吗?" ok-text="确定" cancel-text="取消" @confirm="goToDetail">
                  <h-button size="small" class="my-button mr-10">返回</h-button>
                </h-popconfirm> -->
                <h-button size="small" class="my-button mr-10" @click="goToDetail">返回</h-button>

                <h-button size="small" class="my-button mr-10" @click="save">保存</h-button>

                <h-button size="small" :loading="submitLoading" class="my-button" type="primary" @click="beforeSubmitModel=true" >提交</h-button>
              </div>
          </div>
        </div>
      </div>
    </a-affix>
    <!-- 上传错误信息提示框 -->
    <a-modal v-model:open="errMessageModel" class="my-modal"  :maskClosable="false" title="导入数据错误,请修改后重新导入" >
      <template #footer>
        <a-button type="primary" @click="errMessageModel = false">确认</a-button>
      </template>
      <div class="errmessage-box">
        <p v-for="item,index in xlsErrList" :key="index"><span style="color: red;">第{{item.num || item.lineNum}}行</span>数据错误,{{item.errorMessage}}</p>
      </div>
    </a-modal>

    <!-- 提交前的确认框 -->
    <a-modal v-model:open="beforeSubmitModel" class="my-modal" @ok="submit"  :maskClosable="false" title="提交确认" >
      
      <div class="errmessage-box">
        <p>订单提交后无法修改，请确认订单信息及是否预订状态</p>
        <p v-if="(detail?.destineInfo?.indexOf('0') > -1 || detail?.destineInfo?.indexOf('1') > -1) && !detail?.teamDestinePlaneTicket?.planeTicketDetailList"><span style="color: red;">{{ !detail?.teamDestinePlaneTicket?.planeTicketType ? '国内机票' : '国际机票' }}</span>未导入价格信息,请确认预订状态</p>
        <p v-if="(detail?.destineInfo?.indexOf('2') > -1) && !detail?.teamDestineHotel?.hotelDetailList"><span style="color: red;">酒店</span>未导入价格信息,请确认预订状态</p>
      </div>
    </a-modal>

  </div>
</template>

<style lang="less" scoped>
@import url(./components/trip.less);

.change-title {
  position: absolute;
  right: 0;
  top: -80px;
  width: 300px;

  .ant-col-6,
  .ant-col-18 {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #000000d9;
  }
}
.mt-5 {
  margin-top: 5px;
}

.ml-5 {
  margin-left: 5px;
}
.color-eee {
  color: #a8a7a7;
}

.com-box {
  background: #f5f5f5;
  padding: 20px;
  margin-top: 20px;

  .whole-line {
    height: 40px;
    font-weight: 600;
  }
  .info-box {
    display: flex;
    align-items: flex-start;
    padding: 10px 20px;
    border: 1px solid #ffdb5c;
    background: #fffbd8;
  }
  :deep(.ant-form-item-control-input-content) {
    display: flex;
    align-items: center;
  }
}
.title {
  height: 60px;
}
.download-btn {
  color: #408cff;
  cursor: pointer;
}
:deep(.city-chose-box) {
  width: 200px !important;
}
:deep(.mr-10) {
  margin-right: 10px;
}

.background-eee {
  padding: 10px;
  background: #f4f4f4;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

  .errmessage-box {
    max-height: 300px; 
    overflow-y: auto
  }
</style>