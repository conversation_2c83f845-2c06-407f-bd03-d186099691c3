
<template>
    <div style="height:33vh">
        <div :id="pieId" style="height:25vh"></div>
        <div class="tips">
            <div class="tip" v-for="(row, index) in payTypeRows" :key="row.name">
                <span class="tip-dot" :style="{ borderColor: colors[index] }"></span>
                <span class="tip-percent">{{ (row.value / payTypeTotal * 100).toFixed(0) }}%</span>
                <span class="tip-title">{{ row.name }}</span>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { pie, colors } from "../../data"
import * as echarts from "echarts";
import { queryOrderingFoodPayType } from "@haierbusiness-front/apis/src/data/board";
import { EventBus } from "../../eventBus";
const loading = ref(false);
const pieId = ref("pie-" + Date.now());
const payTypeRows = ref([]);
const payTypeTotal = ref(0);

let pieChartDom, pieChart;
onMounted(() => {
    pieChartDom = document.getElementById(pieId.value);
    pieChart = echarts.init(pieChartDom as any, "dark");
    // queryPie();
})
EventBus.on((event) => {
    if (event == "refresh") {
        queryPie();
    }
})
//左侧饼图
const queryPie = async () => {
    loading.value = true;
    const data = await queryOrderingFoodPayType();
    loading.value = false;
    const payTypeData = [];
    let total = 0;
    data.rows?.forEach(item => {
        total += item[1];
        payTypeData.push({
            name: item[0],
            value: item[1]
        })
    })
    payTypeTotal.value = total;
    payTypeRows.value = payTypeData;
    const { series } = pie;
    series[0].color = colors;
    series[0].data = payTypeData;
    delete series[0].radius;
    pieChart.setOption(pie)
}
</script>
<style scoped lang="less">
.tips {
    display: flex;
    justify-content: center;
}

.tips-main {
    display: flex;
    flex-wrap: wrap;
    width: 200px
}

.tip {
    width: 100px;

    &-dot {
        display: inline-block;
        width: 10px;
        height: 10px;
        border: 3px solid #FFD700;
        border-radius: 50%;
    }

    &-percent {
        font-size: 20px;
        margin: 0 5px 0 7px;
    }

    &-title {
        font-size: 12px;
    }
}

@media screen and (max-width:1500px) {
    .tip {
        width: 80px;

        &-dot {
            display: inline-block;
            width: 6px;
            height: 6px;
            border: 2px solid #FFD700;
            border-radius: 50%;
        }

        &-percent {
            font-size: 14px;
            margin: 0 3px 0 5px;
        }

        &-title {
            font-size: 10px;
        }
    }
}</style>