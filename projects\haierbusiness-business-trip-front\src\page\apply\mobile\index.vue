<template>
  <div class="mobile-box">
    <van-overlay :z-index="9999" style="display: flex; align-items: center;justify-content: center;" :show="fullScreenLoading" >
      <van-loading type="spinner" color="#1989fa" vertical >
        加载中....
      </van-loading>
    </van-overlay>

    <van-form @submit="onSubmit" >

      <!-- 基本信息 -->
      <base-info ref="baseInfoCom" :creatTripParma="creatTripParma"></base-info>

      <!-- 行程计划与费用 -->
      <plan-budge @show="showFullScreenLoading" @hide="hideFullScreenLoading" ref="planBudgeRef" :creatTripParma="creatTripParma"></plan-budge>

      <!-- 附件 -->
      <file-upload @show="showFullScreenLoading" @hide="hideFullScreenLoading" :creatTripParma="creatTripParma"></file-upload>

      <!-- 隐私政策 -->
      <van-row class="mb-20 mt-20" style="padding: 0 20px;">
        
        <van-field :rules="[{ required: true, message: '请阅读并同意隐私政策' }]" >
          <template #input>
            <van-checkbox v-model="checked" class="font-size-13">
              <span class="add-color">已经阅读并同意</span>
              <span class="color-main" @click.stop="showYszc"> 乘机提醒 / 商旅系统隐私政策</span>
            </van-checkbox>
          </template>

        </van-field>

      </van-row>

      <!-- 提交 -->
      <van-sticky :z-index="999" :offset-bottom="0" position="bottom">
        <div class="bottom-order-btn">
          <van-button class="btn btn-background mr-20" @click="saveInfo">保存</van-button>
          <van-button :loading="submitBtnLoading" class="btn" native-type="submit" type="primary">提交</van-button>
        </div>
      </van-sticky>
    </van-form>

    <van-dialog v-model:show="yszcDialog" title="乘机提醒/商旅系统隐私政策" show-cancel-button @confirm="confirmYszc">
      <p class="font-size-14 "  style="text-align: center; margin-top: 10px;">文明乘机提醒</p>
      <p class="font-size-14 "  style="text-indent: 3ch; padding:0 20px;">在航空器上强占座位、辱骂殴打他人、妨碍机组正常履行职责、霸占航空器、破坏机上设施设备等行为，扰乱公共秩序、危害公共安全。构成违反治安管理行为的，公安机关将依法进行处罚；情节严重的，可能被追究刑事责任，请您遵规守法、文明乘机！</p>
      <p class="font-size-14 "  style="text-align: center; margin-top: 10px;">随身行李告知</p>
      <p class="font-size-14 "  style="text-indent: 3ch; padding:0 20px;">为确保飞行安全和航班准点运行，请您携带符合标准的随身行李乘机（随身行李限额为：头等舱旅客限带2件，每件不得超过10公斤；豪华公务舱、公务舱/超级经济舱、经济舱旅客限带1件，每件不得超过8公斤；国内航班每件行李体积不得超过20cm×40cm×55cm；国际/地区航班每件行李体积不得超过25cm×45cm×56cm且三边之和小于等于115cm。） 超过规定的随身行李需重新安排安检及托运，将导致行李无法与您同机抵达，由此产生的相关损失将由您自行承担。请您在乘机前再次确认携带的随身行李符合件数、重量和尺寸标准，感谢您的支持和配合，祝您旅途愉快！</p>

      <div class="font-size-13 " style="margin-left: 20px;margin-bottom: 20px;">
        <van-icon name="info-o" />
        <span class="add-color">已经阅读并同意</span>
        <span class="color-main" @click.stop="showPdf"> 商旅系统隐私政策</span>
      </div>
    </van-dialog>

    <van-dialog :zIndex="9999" v-model:show="repeatDialog" title="行程重复提醒" show-cancel-button @cancel="cancelRepeat" @confirm="confirmRepeat">
      <p class="font-size-14"  style="text-align: left; padding: 10px; margin-top: 10px;">在同一时间段您有多个相同的行程,请您确认后再提交!</p>
      <div style="max-height: 300px; padding: 10px; overflow-y: scroll;">
        <p class="font-size-12" v-for="item,index in conflictRecords" :key="index">{{ `${index + 1 }、${item}` }}</p>
      </div>
    </van-dialog>

    <van-floating-bubble   axis="xy" magnetic="x" v-model:offset="offsetHeight"  icon="label-o" @click="goToList" />

     <!-- 隐私政策 -->
    <van-popup :lazy-render="false" v-model:show="showYszcPopup" position="bottom" :style="{ height: '100%' }">
      <van-nav-bar
        title="商旅系统隐私政策"
        left-arrow
        fixed
        :z-index="1000"
      >
        <template #left>
          <van-icon name="arrow-left" color="#000" @click="showYszcPopup=false" size="20" />
        </template>
      </van-nav-bar>
      <div ref="yszcpdfRef" style="height:100%;"></div> 
    </van-popup>
  </div>
  
</template>

<script setup lang='ts'>
import { onMounted, ref } from 'vue';
import baseInfo from './components/baseInfo.vue';
import planBudge from './components/planBudge.vue';
import fileUpload from './components/fileUpload.vue';
import { tripApi } from '@haierbusiness-front/apis';
import { showSuccessToast,showFailToast,showConfirmDialog } from 'vant';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, getCurrentRoute } from "@haierbusiness-front/utils";
import Pdfh5 from "pdfh5";

import "pdfh5/css/pdfh5.css";

const route = ref(getCurrentRoute());
const router = getCurrentRouter();

const store = applicationStore();

const { loginUser } = storeToRefs(store);

const checked = ref<boolean>(false);
const submitBtnLoading = ref<boolean>(false);

// 创建申请单请求参数
const creatTripParma = ref<any>({
  tripList: [],
  travelerList: [
    {
      travelUserName: loginUser.value?.nickName,
      travelUserSyId: loginUser.value?.username,
      travelUserDeptName: loginUser.value?.departmentName || loginUser.value?.enterpriseName,
      travelUserDeptId: loginUser.value?.departmentCode,
      travelUserNo: loginUser.value?.username,
      username: loginUser.value?.username,
      travelUserType: '0',
      mainFlag: '1',
    },
  ],

  operUserId: loginUser.value?.id,
  operUserNo: loginUser.value?.username,
  operUserName: loginUser.value?.nickName,
  operDeptId: loginUser.value?.departmentCode,
  operDeptName: loginUser.value?.departmentName,

  fileList: [],
  travelReason: undefined,
  travelReserveFlag: 1,
  travelUserName: loginUser.value?.nickName,
});

const checked2 = ref(false);

const fullScreenLoading = ref(false)

const showFullScreenLoading = () => {
  fullScreenLoading.value = true

}
const hideFullScreenLoading = () => {
  fullScreenLoading.value = false

}
// 添加悬浮窗方便跳转列表页
const offsetHeight = {x: document.body.clientWidth * 300 / 375, y: 500}
const goToList = () => {
  showConfirmDialog({
    title: '提示',
    message:
      '确认跳转列表页吗,未保存的数据会丢失!',
  })
    .then(() => {
      router.push('/mobile/applyList')

    })
    .catch(() => {
      // on cancel
    });
}

// 展示隐私政策
const yszcDialog = ref(false)
const showYszc = () => {
  yszcDialog.value = true;
}

const confirmYszc = () => {
  yszcDialog.value = false;
  checked.value = true
}

const showYszcPopup = ref(false)



const pdfUrl = new URL('@/assets/slyszc.pdf', import.meta.url).href

const yszcpdfRef = ref()
const showPdf = () => {
  const pdfh5Yszc = new Pdfh5(yszcpdfRef.value, {
    pdfurl: pdfUrl,
  });
 
  pdfh5Yszc.on("complete", (status, msg, time) => { 
    showYszcPopup.value=true
  });
};





const applyId = route.value?.query?.id;

const baseInfoCom = ref();
const planBudgeRef = ref();

onMounted(async() => {
  // 如果存在id 是修改操作
  if (applyId) {
    let res:any  = await tripApi.queryDetailByApplyNo(applyId);
    // 数据回显处理
    res.travelUserName = res.travelerList.filter(
      (item:any) => item.mainFlag == '1',
    )[0].travelUserName;

    // 根据登陆人初始化数据
    baseInfoCom.value.chosedPerson = res.travelerList?.filter((item:any) => item.travelUserType == '0' && item.mainFlag == '1')[0];
    baseInfoCom.value.chosedInPersenList = res.travelerList?.filter((item:any) => item.travelUserType == '0' && item.mainFlag !='1');
    baseInfoCom.value.chosedOutPersenList = res.travelerList?.filter((item:any) => item.travelUserType == '1');

    res.outPersonId = [];
    res.travelerList.forEach((item:any) => {
      if (item.travelUserType == '1') {
        res.outPersonId.push(item.travelUserSyId);
      }

      item.personIdList = [];
      item.personIdList = [...item.personIdList, item.travelUserSyId];
    });
    planBudgeRef.value.cityList = []
    res?.tripList?.forEach((item:any, index:number) => {
      if (index == 0) {
        planBudgeRef.value.cityList.push({
          cityCode: item.beginCityCode,
          city: item.beginCityName,
          syId: item.beginCityCodeSy,
          date: item.beginDate,
        });
      }
      planBudgeRef.value.cityList.push({
        cityCode: item.endCityCode,
        city: item.endCityName,
        syId: item.endCityCodeSy,
        date: item.endDate,
      });

      item?.tripDetailMapList?.forEach((tripMap:any) => {
        tripMap.personIdList = tripMap.travelApplyTripDetailList.map((tt:any) => tt.travelUserSyId);
      });
    });
    if(!res.fileList) {
      res.fileList = []
    }
    res?.fileList?.forEach((file:any) => {
      file.name = file.fileName;
      file.thumbUrl = file.filePath;
    });

    creatTripParma.value = res
  }
})

// 行程重复
const repeatDialog = ref<boolean>(false)
const conflictRecords = ref<any>([])

// 确认有重复的行程数据然后提交
const confirmRepeat = () => {

  fullScreenLoading.value = true
  creatTripParma.value.conflictConfirmed = 1
  tripApi.applySubmit(creatTripParma.value).then((res) => {
     // 提交后弹出流程弹窗
    fullScreenLoading.value = false
    router.push('/mobile/applyList')
  }).catch(err => {
    fullScreenLoading.value = false
  })
}

const cancelRepeat = () => {
  fullScreenLoading.value = false
  repeatDialog.value = false
}


const onSubmit = () => {
  if(!creatTripParma.value?.haierBudgetPayOccupyRequest?.leftAmt && creatTripParma.value.travelReserveFlag == 1) {
    planBudgeRef.value.budgetErrorMessage = '请选择预算'
    return
  }else {
    planBudgeRef.value.budgetErrorMessage = ''
  }
  if(creatTripParma.value?.tripList?.length < 1) {
    planBudgeRef.value.planErrorMessage = '请选择行程'
    return
  }else {
    planBudgeRef.value.planErrorMessage = ''
    let tripList = creatTripParma.value.tripList;
    let tripDetailMapListAll:any = [];
    let temp = true;
    // 因公行程进行行程判断
    if(creatTripParma.value.travelReserveFlag == 1) {
          // 行程中未添加出行人、出行方式
      tripList?.forEach((trip:any) => {
          if (!trip?.tripDetailMapList) {
            trip['tripDetailMapList'] = [];
          }
          tripDetailMapListAll = [...tripDetailMapListAll, ...trip?.tripDetailMapList];

          trip?.tripDetailMapList?.forEach((detail:any) => {
            if (!detail?.travelApplyTripDetailList || detail?.travelApplyTripDetailList.length < 1) {
              temp = false;
              showFailToast('请先选择出差人员!');
              return;
            }
            if (!detail.productCode) {
              temp = false;
              showFailToast('请先选择出差费用产品!');
              return;
            }
            if(detail.budgetAmount == 0) {
              temp = false;
              showFailToast('预算不能为0!');
              return;
            }
            if (detail.excessiveFlag && !detail.excessiveReasonId && detail.productCode != '03' && detail.productCode != '05') {
              temp = false;
              detail['errorFlag'] = true;
              showFailToast('超标费用请选择超标原因!');
              return;
            }
          });
        });

        if (tripDetailMapListAll.length < 1) {
          temp = false;
          showFailToast('总行程至少添加一项费用预算!');
          return;
        }

        if (creatTripParma.value?.amountSum > creatTripParma.value?.haierBudgetPayOccupyRequest?.leftAmt) {
          temp = false;
          showFailToast('预算不足,请重新修改后再次提交!');
        }
        if (!temp) {
          return;
        }
      }
    }

  

  fullScreenLoading.value = true
  creatTripParma.value.conflictConfirmed = 0

  tripApi.applySubmit(creatTripParma.value).then((res:any) => {
    // 如果有异常数据 展示确认弹窗
    if(res.conflictRecords) {
      // fullScreenLoading.value = false
      repeatDialog.value = true
      conflictRecords.value = JSON.parse(res.conflictRecords)
    }else {
      fullScreenLoading.value = false
      router.push('/mobile/applyList')
    }

  }).catch(err => {
    fullScreenLoading.value = false
  })
}

const saveInfo = () => {
   // 判断是否已经自动保存 如果以保存走修改
   if (creatTripParma.value.id) {
      tripApi.applyUpdate(creatTripParma.value).then((res) => {
        
      });
    } else {
      tripApi.applyCreat(creatTripParma.value).then((res:any) => {
        creatTripParma.value.id = res.id;
        creatTripParma.value.applyNo = res.applyNo;
        creatTripParma.value.gmtCreate = res.gmtCreate;
        
      });
    }
    showSuccessToast('保存成功!')
}



</script>

<style lang="less" scoped>
@import url(./components/mobile.less);
</style>