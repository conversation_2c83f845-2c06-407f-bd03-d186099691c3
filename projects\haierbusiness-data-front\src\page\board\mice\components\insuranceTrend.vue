<template>
    <div :style="{ height: height + 'vh' }" background="rgba(0,0,0,0)">
        <bar-line from="ChangeAndRefund" v-if="loaded" :height="height" :legend="legend" :x-axis="xAxis" :y-axis="yAxis"
            :series="series" />
    </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import BarLine from "../../components/barLine.vue";
import { queryLocalInranceTrend } from "@haierbusiness-front/apis/src/data/board/mice";
import { EventBus } from "../../eventBus";
const props = defineProps({
    dateType: {
        type: Number,
        default: 1,
    },
    height: {
        type: Number,
        default: 33,
    },
    type: {
        type: String,
        default: "青岛会议",
    },
});
const loaded = ref(false);
const loading = ref(false);
const legend = ["金额", "订单数"];
const xAxis = ref([]);
const yAxis = [
    {
        type: "value",
        name: "万元",
        splitNumber: 5,
        axisLabel: {
            formatter(value) {
                return value / 10000;
            },
        },
    },
    {
        type: "value",
        name: "订单数",
        splitNumber: 5,
    },
];
const series = ref([]);
onMounted(() => {
    queryData();
})
EventBus.on((event, params) => {
    if (event == "refresh") queryData(params ? params : null);
});
const getFunctionColumns = () => {
    if (props.dateType == 0) {
        return {
            alias: "gmt_create（按年）",
            snippet: "AGG_DATE_YEAR([gmt_create])",
        };
    }
    if (props.dateType == 1) {
        return {
            alias: "gmt_create（按月）",
            snippet: "AGG_DATE_MONTH([gmt_create])",
        };
    }
    return {
        alias: "gmt_create（按日）",
        snippet: "AGG_DATE_DAY([gmt_create])",
    };
};
const queryData = async (params?: { data: { name: string }; from: string }) => {
    loading.value = true;
    const functionColumns = [getFunctionColumns()];
    const data = await queryLocalInranceTrend(
        {
            type: props.type,
            functionColumns,
        },
        params ? params.data.name : null,
        params ? params.from : null
    );
    loading.value = false;
    const barData: any = [];
    const lineData: any = [];
    const xData: any = [];
    data.rows.forEach((item, index) => {
        xData.push(item[0]);
        barData.push(item[2] || 0);
        lineData.push(item[1] || 0);
    });
    xAxis.value = xData;
    series.value = [
        {
            name: "金额",
            type: "bar",
            color: "rgba(0,240,255,0.4)",
            itemStyle: {
                borderColor: "#00F0FF",
            },
            data: barData,
        },
        {
            name: "订单数",
            type: "line",
            color: "#FFD700",
            smooth: true,
            yAxisIndex: 1,
            symbol: "none",
            data: lineData,
        },
    ] as any;
    loaded.value = true;
};
watch(() => props.dateType, queryData);
</script>
