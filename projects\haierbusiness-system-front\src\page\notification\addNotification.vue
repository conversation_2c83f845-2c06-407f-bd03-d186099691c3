<script setup lang="ts">
import {
  DatePicker as hDatePicker,
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Table as hTable,
  BreadcrumbItem as hBreadcrumbItem,
  Breadcrumb as hBreadcrumb,
  LayoutSider as hLayoutSider,
  LayoutFooter as hLayoutFooter,
  LayoutContent as hLayoutContent,
  LayoutHeader as hLayoutHeader,
  Layout as hLayout,
  MenuItem as hMenuItem,
  MenuItemGroup as hMenuItemGroup,
  SubMenu as hSubMenu,
  Menu as hMenu,
  Divider as hDivider,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Result as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Tabs as hTabs,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  Textarea as hTextarea,
  Card as hCard,
  Upload as hUpload,
  message,
  TableProps
} from "ant-design-vue";
import { computed, onMounted, ref } from "vue";
import {
  UploadOutlined,
  UserOutlined,
  NotificationOutlined,
  AppstoreOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PlusOutlined,
  SearchOutlined
} from "@ant-design/icons-vue";
import { notificationApi } from "@haierbusiness-front/apis";
import { usePagination, useRequest } from "vue-request";
import Card from "../card/index.vue"
import dayjs from 'dayjs'
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';
import {
  Send_1Params,
  SearchTemplateRes,
  notificationMethodConstant,
  IUserListRequest
} from "@haierbusiness-front/common-libs";
import router from "../../router";
const currentRouter = ref<any>(null);
const fileList = ref([]);
const uploadLoading = ref<boolean>(false);
const {
  data,
  run: notificationApiRun,
  loading,
  current,
  pageSize
} = usePagination(notificationApi.list);
const roleList = ref<any>([]);
const templateList = ref<SearchTemplateRes[]>([]);
const nickName = ref<Array<string>>([]);
const params = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});
const dataSource = computed(() => data.value?.records || []);
const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: "center" }
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any
) => {
  notificationApiRun({
    pageNum: pag.current,
    pageSize: pag.pageSize
  });
};

// 新增相关
const gotoSave = () => {
  visibleNew.value = true;
};

// 新增表单相关
const userNewForm = ref();
const confirmLoading = ref(false);
const visibleNew = ref(false);
const addForm = ref<Send_1Params>({
  sendType: "1"
});
const labelCol = { span: 5 };
const wrapperCol = { span: 14 };
const handleOk = () => {
  userNewForm.value.validate().then(() => {
    confirmLoading.value = true;
    notificationApi
      .save(addForm.value)
      .then((res:any) => {
        message.success("保存成功");
        currentRouter.value.push("/notification/list");
        fileList.value = [];
        addForm.value = {}
        nickName.value = []
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  });
};

// 上传飞书文件
const upload = (options: any) => {
  uploadLoading.value = true;
  const formData = new FormData();
  console.log(options.file.size);
  if (options.file.size > 10000 * 1024) {
    message.error("文件大小不能超过10m");
    fileList.value = [];
    return;
  }
  formData.append("file", options.file);
  notificationApi
    .upload(formData)
    .then(it => {
      console.log(it);
      options.file.filePath = it.imageBase64;
      options.file.fileName = options.file.name;
      addForm.value.imageFeishuId = it.imageKey;
      addForm.value.imageBase64 = it.imageBase64;
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 获取预设模版列表
const getTemplate = () => {
  notificationApi.searchTemplate().then(res => {
    templateList.value = res;
  });
};

// 二进制转化方法
const computedMethod = (num: number | undefined) => {
  if (!num) {
    return;
  }

  const list = notificationMethodConstant.toNumberArray();
  console.log(list);
  // method
  const array: number[] = [];
  list.map(item => {
    if ((num & item!) != 0) {
      array.push(item!);
    }
  });
  return array;
};

// 获取所有接收角色
const getAllRole = () => {
  notificationApi.getAllRole().then(res => {
    roleList.value = res;
  });
};

// 点击查看
const toLink = (url: string) => {
  if(url.indexOf('https://')!=-1 || url.indexOf('http://')!=-1){
    window.open(url, "_blank");
  }else{
    window.open('https://'+url, "_blank");
  }
};

const disabledDate = (current:any) =>{
  return current && current < dayjs().startOf('day');
}

const range = (start, end)=>{
      const result = []
      for (let i = start; i < end; i++) {
        result.push(i)
      }
      return result
}
  // 选择时间不大于当前时间禁用已过期时间
const disabledDateTime=(current:any)=>{
      if(dayjs().format('YYYY-MM-DD')==dayjs(current).format('YYYY-MM-DD')){
        let nowHour = new Date().getHours()
        let nowMinutes = new Date().getMinutes()
        return {
          disabledHours: () => range(0, nowHour),
          disabledMinutes: () => range(0, nowMinutes),
          disabledSeconds: () => [0, 60],
        }
      }
}
const userNameChange = (userInfo: Array<IUserInfo>) => {
  if (!userInfo.length) {
    addForm.value.fixUserDTOList = []
  }
  const array: Array<string> = [];
  addForm.value.fixUserDTOList = []
  userInfo.forEach((item: any, index: number) => {
    if (item.nickName && item.username) {
      array.push(item.nickName + '/' + item.username);
      addForm.value.fixUserDTOList?.push(
        {
          username: item.username,
          nickName: item.nickName
        }
      )
    } else {
      array.push(
        nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[0] +
        '/' +
        nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[1],
      );
      addForm.value.fixUserDTOList?.push(
        {
          username: nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[1],
          nickName: nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[0]
        }
      )
    }

  })
  nickName.value = array;

}

onMounted(async () => {
  currentRouter.value = await router;
  getTemplate();
  getAllRole();
});
</script>

<template>
  <div
    style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;"
  >
    <div class="title">应用机器人消息通知</div>
    <h-form
      ref="userNewForm"
      :model="addForm"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      @finish="handleOk"
    >
      <h-form-item label="通知标题" name="title" :rules="[{ required: true, message: '请输入通知标题' }]">
        <h-input v-model:value="addForm.title" />
      </h-form-item>
      <h-form-item
        label="接收角色"
        name="receiverRole"
        :rules="[{ required: true, message: '请输入接收角色' }]"
      >
        <h-select ref="select" v-model:value="addForm.receiverRole">
          <h-select-option v-for="item in roleList" :value="item.roleCode">{{item.roleName}}</h-select-option>
        </h-select>
      </h-form-item>
      <h-form-item name="fixUserDTOList" label="接收人"  :rules="[{ required: true, message: '请选择接收人' }]" v-if="addForm?.receiverRole == 'Fix'">
          <user-select
            ref="userSelectRef"
            :value="nickName"
            :multiple="true"
            :params="params"
            @change="(userInfo: IUserInfo) => userNameChange(userInfo)"
          />
        </h-form-item>
      <h-form-item
        label="发送时间"
        name="scheduledTime"
        :rules="[{ required: addForm.sendType==2?true:false, message: '请选择发送时间', trigger: 'blur' }]"
      >
        <h-radio-group v-model:value="addForm.sendType">
          <h-radio value="1">立即发送</h-radio>
          <h-radio value="2">定时发送</h-radio>
        </h-radio-group>
        <h-date-picker
          valueFormat="YYYY-MM-DD HH:mm:ss"
          show-time
          :disabledDate="disabledDate"
          :disabledTime="disabledDateTime"
          v-if="addForm.sendType==2"
          v-model:value="addForm.scheduledTime"
        />
      </h-form-item>
      <h-form-item
        label="通知模版"
        name="templateCode"
        :rules="[{ required: true, message: '请选择通知模版' }]"
      >
        <h-select ref="select" v-model:value="addForm.templateCode">
          <h-select-option v-for="item in templateList" :value="item.code">{{item.description}}</h-select-option>
        </h-select>
      </h-form-item>
      <h-form-item
        v-if="['ROBOT_COMMON1','ROBOT_COMMON2','ROBOT_COMMON3','ROBOT_COMMON4'].includes(addForm.templateCode)"
        label="正文内容"
        name="content"
        :rules="[{ required: true, message: '请输入正文内容' }]"
      >
        <h-textarea v-model:value="addForm.content" />
      </h-form-item>
      <h-form-item
        v-if="['ROBOT_COMMON1','ROBOT_COMMON3','ROBOT_COMMON5','ROBOT_COMMON7'].includes(addForm.templateCode)"
        label="跳转链接"
        name="linkUrl"
        :rules="[{ required: true, message: '请输入跳转链接' }]"
      >
        <h-input v-model:value="addForm.linkUrl" />
      </h-form-item>
      <h-form-item
        v-if="['ROBOT_COMMON1','ROBOT_COMMON4','ROBOT_COMMON5','ROBOT_COMMON6'].includes(addForm.templateCode)"
        label="上传图片"
        name="imageFeishuId"
        extra='尽量避免长图, 建议使用比例：4:3，分辨率：1280*960"的图片显示效果更佳'
        :rules="[{ required: true, message: '请选择图片' , trigger: 'blur'}]"
      >
        <h-upload
          name="tixketFileMx"
          v-model:file-list="fileList"
          :custom-request="upload"
          :max-count="1"
          list-type="picture-card"
        >
          <div v-if="fileList.length<1" >
            <plus-outlined />
            <div style="margin-top: 8px">上传</div>
          </div>
        </h-upload>
      </h-form-item>
      <h-form-item v-if="addForm.templateCode" label="卡片预览">
        <!-- <h-card :title="addForm.title" hoverable style="width: 620px;">
          <template #cover>
            <pre class="contentBox" v-html="addForm.content"></pre>
            <div v-if="addForm.imageBase64" class="imgBox">
              <img :src="'data:image/png;base64,'+addForm.imageBase64" alt />
            </div>
          </template>
          <a-card-meta>
            <template #description>
              <h-button
                type="primary"
                ghost
                @click="toLink(addForm.linkUrl)"
                v-if="addForm.linkUrl"
              >查看</h-button>
            </template>
          </a-card-meta>
        </h-card> -->
       <Card style="margin-left:24px;" :addForm="addForm" />
      </h-form-item>
      <h-form-item :wrapper-col="{ offset: 8, span: 16 }">
        <h-button type="primary" :loading="confirmLoading" html-type="submit">提交</h-button>
      </h-form-item>
    </h-form>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.title {
  width: 100%;
  text-align: center;
  font-size: 28px;
  margin: 20px 0;
  font-weight: 600;
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}
.contentBox {
  word-break: break-all;
}
:deep(.ant-card-bordered .ant-card-cover) {
      padding: 16px 16px 0 16px;
}
:deep(.ant-card){
    border-radius: 8px;
}
:deep(.ant-card-head) {
    background-color: #e0e9ff;
    color: #1456f0;
    border-radius: 8px 8px 0 0;
    font-size: 16px;
    line-height: 24px;
    padding: 0px 12px;
}
:deep(.ant-card-head-title){
 white-space: normal !important;
}
.imgBox {
  width: 100%;
  img {
    width: 100%;
  }
}
</style>
