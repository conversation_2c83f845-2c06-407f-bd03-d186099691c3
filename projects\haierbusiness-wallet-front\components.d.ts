/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    VanButton: typeof import('vant/es')['Button']
    VanCalendar: typeof import('vant/es')['Calendar']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanCol: typeof import('vant/es')['Col']
    VanDropdownItem: typeof import('vant/es')['DropdownItem']
    VanDropdownMenu: typeof import('vant/es')['DropdownMenu']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanIcon: typeof import('vant/es')['Icon']
    VanList: typeof import('vant/es')['List']
    VanLoading: typeof import('vant/es')['Loading']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanNoticeBar: typeof import('vant/es')['NoticeBar']
    VanRow: typeof import('vant/es')['Row']
    VanTab: typeof import('vant/es')['Tab']
    VanTabs: typeof import('vant/es')['Tabs']
  }
}
