import { Dayjs } from 'dayjs';

export class ICbBillData {
    cbBillResponse?: ICbBillResponse
}

export class ICbBillResponse {
  code?: string
  cbCode?: string
  schemeId?: number
  cbStayBills?: Array<IStayBill>
  cbCateringBills?: Array<ICbCateringBill>
  cbVehicleBills?: Array<ICbVehicleBill>
  cbPlaceBills?: Array<ICbPlaceBill>
  cbPresentBills?: Array<ICbPresentBill>
  cbServiceBills?: Array<ICbServiceBill>
  cbOtherBills?: Array<ICbOtherBill>
  insuranceBills?: Array<ICbInsuranceBill>
  settlementBillPath?: string
  settlementBillPresentPath?: string
}

// #region 补充项目

export class IBaseBill {
    // 账单合计
    _billTotal?: number
    // 详情合计
    // _detailTotal?: number
    // 补充项目
    _otherTotal?: number
    // 方案合计
    _biddingTotal?: number

    // 补充项目
    otherList?: Array<IBillSupplements>
    invoices?: Array<any>
    memos?: Array<any>
    contracts?: Array<any>
    others?: Array<any>
}

//#endregion

//#region 住宿账单

// 住宿账单
export class IStayBill extends IBaseBill {
    infoId?: number
    supplier?: string
    level?: number
    address?: string
    bidSize?: string
    biddingPrice?: number
    roomType?: string
    roomNum?: number
    hasBreakfast?: number
    checkInDate?: string
    checkOutDate?: string
    actualPrice?: number
    actualNights?: number
    nights?: number
    remark?: string
    billType?: number
    // 住宿详情
    detailList?: Array<IStayBillDetail>
}

// 住宿详情
export class IStayBillDetail {
    key?: string
    billId?: number
    roomType?: string
    price?: number
    nights?: number
    schemeBillId?: number
    checkInDate?: string | Dayjs
}

// 补充项目
export class IBillSupplements {
    billId?: number
    key?: string
    projectName?: string
    price?: number
    num?: number
    billType?: number
    projectDate?: string | Dayjs
    type?: number
    desc?: string
}

//#endregion

//#region 餐饮账单

export class ICbCateringBill extends IBaseBill {
    key?: string
    infoId?: number
    supplier?: string
    eatDate?: string
    eatTime?: number
    cateType?: number
    cateTypeOther?: string
    personNum?: number
    biddingPrice?: number
    actualPrice?: number
    actualNum?: number
    billType?: number
}

export class ICbCateringBillDetail {
    billId?: number
    cateringType?: string
    projectName?: string
    price?: number
    personNum?: number
    tablePersonName?: string
    amount?: number
    schemeBillId?: number
    key?: string
}

//#endregion

//#region 用车账单

export class ICbVehicleBill extends IBaseBill {
    infoId?: number
    supplier?: string
    requireDate?: string
    vehicleType?: number
    routes?: Array<string>
    billId?: number
    vehicleNum?: number
    biddingPrice?: number
    actualPrice?: number
    actualNum?: number
    billType?: number
    duration?: number
    usageWay?: number
    usageTime?: number
    detailList?: Array<ICbVehicleBillDetail>
}

export class ICbVehicleBillDetail {
    key?: string
    billId?: number
    timeFrame?: string
    carType?: string
    carNo?: string
    driver?: string
    kmNum?: number
    kmPrice?: number
    toll?: number
    other?: string
    otherPrice?: number
    practical?: string
    accumulativePrice?: number
    amount?: number
    schemeBillId?: number
    remark?: string
    brand?: string
    carAge?: string
    seats?: string
}

//#endregion

//#region 会场账单

export class ICbPlaceBill extends IBaseBill {
    infoId?: number
    supplier?: string
    holdDate?: string
    holdTime?: number
    area?: number
    floor?: number
    layout?: number
    layoutTimeLimit?: string
    layoutTimeLimitBegin?: string
    layoutTimeLimitEnd?: string
    tableType?: string
    ledModel?: string
    ledSource?: number
    capacity?: number
    teaBreak?: number
    teaPrice?: number
    placeName?: string
    billId?: number
    placeNum?: number
    biddingPrice?: number
    teaUse?: number
    teaNumber?: number
    teaUnitPrice?: number
    buildCost?: number
    meetingNum?: number
    actualPrice?: number
    actualNum?: number
    billTeaNumber?: number
    billTeaUnitPrice?: number
    billBuildCost?: number
    billType?: number
    

    _facilityTotal?: number
    // 会场设施 列表
    facility?: Array<ICoPlaceFacilityInfo>
    // 会场设施明细
    facilityDetailList?: Array<ICbPlaceBillDetailFacility>
}

export class ICoPlaceFacilityInfo {
    id?: number
    coCode?: string
    cpiId?: number
    name?: string
    num?: number
    price?: number
}

export class ICbPlaceBillDetailHall {
    key?: string
    billId?:number
    name?: number
    area?: number
    num?: number
    unitPrice?: number
    teaNumber?: number
    teaUnitPrice?: number
    constructPrice?: number
    meetingNum?: number
    amount?: number
    schemeBillId?: number
    remark?: string
}

export class ICbPlaceBillDetailFacility {
    key?: string
    billId?: number
    hall?: string
    num?: number
    unitPrice?: number
    amount?: number
    project?: string
    schemeBillId?: number
    remark?: string
}

//#endregion

//#region 礼品和其他账单公共字段 

export class ICbCommonBill extends IBaseBill {
    infoId?: number
    name?: string
    requireDate?: string
    unit?: string
    requireNum?: number
    biddingPrice?: number
    actualPrice?: number
    actualNum?: number
    remark?: string
    billType?: number

    detailList?: Array<ICbCommonBillDetail>
}

export class ICbCommonBillDetail {
    key?: string
    price?: number
    number?: number
    name?: string
}

//#endregion

//#region 礼品账单

export class ICbPresentBill extends ICbCommonBill {
    
}

//#endregion

//#region 管理费账单

export class ICbServiceBill {
    id?: number
    schemeId?: number
    ratio?: number
    biddingPrice?: number
    desc?: string
    actualPrice?: number

}

//#endregion

//#region 其他

export class ICbOtherBill extends ICbCommonBill {

}

//#endregion

//#region 保险账单

export class ICbInsuranceBill {
    uploadedInsuredNum?: number
    effectiveInsuredNum?: number
    insuranceStatus?: number
    policyNo?: string
    policyFile?: string
    correctNo?: string
    correctFile?: string
    syncFlag?: number
    insuranceStartDate?: string
    insuranceEndDate?: string
    insuredDays?: number
    actualPrice?: number
    totalPrice?: number
    schemeInsuredNum?: number
    schemeBiddingPrice?: number
    schemeInsuredDays?: number
    billType?: number
}

//#endregion

//#region 账单保存

export class IFileData {
    path?: string
    type?: number 
}

export class ISaveBaseData {
    actualPrice?: number
    actualNum?: number
    billType?: number

    otherList?: Array<IBillSupplements>
    filePathList?: Array<IFileData>
}

export class IBillSaveData {
    code?: string
    isStaging?: number
    settlementBillPath?: string
    cbStayBills?: Array<IStaySaveData>
    cbCateringBills?: Array<ICateringSaveData>
    cbVehicleBills?: Array<IVehicleSaveData>
    cbPlaceBills?: Array<IPlaceSaveData>
    cbPresentBills?: Array<IPresentSaveData>
    cbOtherBills?: Array<IOtherSaveData>
    cbServiceBills?: Array<IServiceSaveData>
}

export class IStaySaveData extends ISaveBaseData {
    stayInfoId?: number
    actualNights?: number
    detailList?: Array<IStayBillDetail>
}

export class ICateringSaveData extends ISaveBaseData {
    cateringInfoId?: number
}

export class IVehicleSaveData extends ISaveBaseData {
    vehicleInfoId?: number
    detailList?: Array<ICbVehicleBillDetail>
}
export class IPlaceSaveData extends ISaveBaseData {
    placeInfoId?: number
    meetingNum?: number
    billTeaNumber?: number
    billTeaUnitPrice?: number
    billBuildCost?: number
    // 会场设施明细
    facilityDetailList?: Array<ICbPlaceBillDetailFacility>
}

export class IPresentSaveData extends ISaveBaseData {
    presentInfoId?: number
}

export class IOtherSaveData extends ISaveBaseData {
    otherInfoId?: number
}

export class IServiceSaveData {
    actualPrice?: number
}

//#endregion


//#region 附件检查

export class IFileCheckList {
    type?: string
    billIndex?: number
    fileType?: 'INVOICE' | 'MEMO' | 'CONTRACT' | 'OTHER'
    index?: number
    url?: string
    isCheck?: boolean
}

//#endregion

