<script setup lang="ts">
import { Card as hCard, Descriptions as hDescriptions, DescriptionsItem as hDescriptionsItem } from 'ant-design-vue';
import { useRequest } from 'vue-request';
import { miceApi } from '@haierbusiness-front/apis';
import { computed, onMounted, ref, watch } from 'vue';
import { IConferenceOrder } from "@haierbusiness-front/common-libs";
import dayjs, { Dayjs } from 'dayjs';

interface Props {
    show: boolean
    code: string
    flag: number
}

const props = withDefaults(defineProps<Props>(), {
    show: false,
    code: '',
    flag: 0
})

const show = ref(props.show || false)
const flag = ref(props.flag)

const {
  data,
  run: conferenceOrderApiRun
} = useRequest(miceApi.list);

onMounted(() => {

})

watch(props, (newValue) => {
    if(newValue.code) {
        conferenceOrderApiRun(props.code, props.flag)
    }
});

const conferenceOrder = computed(()=> {
    return data.value?.data.conference as IConferenceOrder ?? {} as IConferenceOrder
})

</script>

<template>
    <h-card size="small" title="会议基本需求" style="width: 100%" :headStyle="{'font-weight': 'bold'}">
        <h-descriptions class="descriptions-item-label" :column="{ lg: 3, sm: 2, xs: 1 }">
            <h-descriptions-item label="会议单号">{{ conferenceOrder.code }}</h-descriptions-item>
            <h-descriptions-item label="会议名称">{{ conferenceOrder.subject }}</h-descriptions-item>
            <h-descriptions-item label="会议城市">{{ conferenceOrder.city }}</h-descriptions-item>
            <h-descriptions-item label="会议开始日期">{{ conferenceOrder.startDate && dayjs(conferenceOrder.startDate).format("YYYY-MM-DD") }}</h-descriptions-item>
            <h-descriptions-item label="会议结束日期">{{ conferenceOrder.finishDate && dayjs(conferenceOrder.finishDate).format("YYYY-MM-DD") }}</h-descriptions-item>
            <h-descriptions-item label="会议人数">{{ conferenceOrder.personNum }}</h-descriptions-item>
            <template v-if="show">
                <h-descriptions-item label="经办人">{{ conferenceOrder.handler }}</h-descriptions-item>
                <h-descriptions-item label="经办人电话">{{ conferenceOrder.handlerPhone }}</h-descriptions-item>
                <h-descriptions-item label="经办人邮箱">{{ conferenceOrder.handlerEmail }}</h-descriptions-item>
                <h-descriptions-item label="会议负责人">{{ conferenceOrder.incharge }}</h-descriptions-item>
                <h-descriptions-item label="负责人电话">{{ conferenceOrder.inchargePhone }}</h-descriptions-item>
                <h-descriptions-item label="负责人邮箱">{{ conferenceOrder.inchargeEmail }}</h-descriptions-item>
            </template>
            <h-descriptions-item label="会议类型">{{ conferenceOrder.coType }}</h-descriptions-item>
            <h-descriptions-item label="互动结束原因" :span="2">{{ conferenceOrder.endReason }}</h-descriptions-item>
            <h-descriptions-item label="备注" :span="3">{{ conferenceOrder.remark }}</h-descriptions-item>
        </h-descriptions>
    </h-card>
</template>

<style scoped lang="less">

</style>
<style lang="less">
    .descriptions-item-label {
        .ant-descriptions-item-label {
            display: block;
            min-width: 120px;
            text-align: right;
        }
    }
</style>