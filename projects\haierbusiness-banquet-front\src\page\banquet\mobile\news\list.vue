<template>

  <div class="order-list" style="padding-top: 20px; min-height: 100vh;">
  
    <!-- <van-nav-bar  class="top-bg-color" :fixed="true"  title="通知通告" left-arrow>
      <template #left>
        <van-icon name="arrow-left" color="#000" @click="goBack" />
      </template>
    </van-nav-bar> -->

    <van-list v-model:loading="newsLoading" :finished="newsFinished" :finished-text="newsList.length ? '没有更多了' : ''"
      @load="loadnewsList" class="van-list-box">
     
            <van-cell-group  v-for="(item, index) in newsList" :key="index" @click="goToDetail(item.id)" inset class="mb-10 cell-group-shadow ">
              <van-cell  :label="item?.createTime">
                <template #title>
                  <van-text-ellipsis :content="item?.title" />

                </template>
              </van-cell>
            </van-cell-group>

    </van-list>

    <van-empty v-if="!newsLoading && newsList.length == 0" description="暂无数据" />

    <!-- 时间选择 -->
    <van-popup @click.stop v-model:show="showTimePicker" :close-on-click-overlay="false" position="bottom">
      <van-date-picker title="选择日期" v-model="currentDate" :min-date="minDate" :max-date="maxDate" @confirm="confirmTime"
        @cancel="showTimePicker = false" />
    </van-popup>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onActivated } from 'vue';

import {
  IUserListRequest,
  ROrderPayMentStateEnum,
  ROrderPayMentStateEnumColor,
  ROrderApprovalStateEnum,
  ROrderApprovalStateEnumColor,
  ROrderStateEnum,
  ROrderStateEnumColor,
  BanquetStatusEnumMobile,
  BanquetStateTagColorMap,
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { banquetApi } from '@haierbusiness-front/apis';
import { ROrderParams } from '@haierbusiness-front/common-libs';
import type { Ref } from 'vue';

import dayjs from 'dayjs';

import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { showConfirmDialog, showSuccessToast } from 'vant';
const store = applicationStore();

const { loginUser } = storeToRefs(store);

const router = getCurrentRouter();
const route = ref();


const goBack = () => {
  router.back(-1);
};


// 订单列表
const newsLoading = ref<boolean>(false);
const newsFinished = ref<boolean>(false);
const newsList = ref([])
const newsTotal = ref<number>(0);
const defaultParams = ref({

  pageNum: 0,
  pageSize: 20,
  
});


const loadnewsList = () => {
  defaultParams.value.pageNum++;
  banquetApi.getNotification(defaultParams.value).then((res) => {
    // 加载状态结束
    newsLoading.value = false;
    newsList.value = [...newsList.value, ...res.records];
    newsTotal.value = res.total ?? 0;
    // 数据全部加载完成
    if (newsList.value.length >= newsTotal.value) {
      newsFinished.value = true;
    }
  });
};


// 详情
const goToDetail = (id: string) => {
  router.push({ path: '/banquet/news/detail', query: { id: id } });
}; 


</script>

<style lang='less' scoped>
@import url(../common.less);
:deep(.van-cell__title) {
  font-weight: bold;
  // font-size: 10px;
  color: #000000;
}
:deep(.van-cell__label) {
  font-family: Open Sans, Open Sans;
  color: rgba(0,0,0,0.7);
}

:deep(.van-dropdown-menu__bar) {
  //  background: var(--van-dropdown-menu-background);
  box-shadow: none;
}

.list-search {
  :deep(.van-search__action) {
    // padding: 0;
  }
}

.list-search-btn {
  width: 110px;
}

.my-radio {
  :deep(.van-radio__icon) {
    height: auto !important;
  }
}

.btn-com {
  width: 70px;
}

.input-border {
  border: 1px solid #dcdee0;
  border-radius: 20px;
  padding: 2px 10px;
}

.van-list-box {
  padding: 16px 10px;
}

.order-item {
  height: 260px;

  .order-item-left {
    width: 10px;
    height: 100%;
    background: url('@/assets/image/banquet/order/step.png') no-repeat;
    background-size: cover;
  }

  .order-item-right {
    flex: 1;
    flex-direction: column;

    .order-item-title-left {
      font-size: 10px;
      color: rgba(0,0,0,0.5);
    }
    .order-content {
      :deep(.van-cell__title) {
        color: rgba(20,21,3,0.6);      }
      :deep(.van-cell__value) {
        color: rgba(20,21,3,0.8);     
      }
      :deep(.van-cell__label) {
        color: rgba(20,21,3,0.8);  
      }
      
    }

    .order-title-cell {
      :deep(.van-cell__title) {
        color: rgba(0,0,0,0.9) !important;
      }
    }
    
  }
}
</style>