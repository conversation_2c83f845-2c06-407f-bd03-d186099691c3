<script lang="ts" setup>
import { Menu as hMenu, MenuItem as hMenuItem, But<PERSON> as hButton, Row as hRow, Col as hCol, Input as hInput, Table as hTable, Tag as hTag} from 'ant-design-vue';
import { computed, ref, watch, onMounted } from "vue";
import type { Ref } from "vue";
import {
    IPaymentVirtualAccount, IApplicationInfo
} from '@haierbusiness-front/common-libs';
import { useRequest, usePagination } from 'vue-request';
import { virtualPayApi, applicationApi } from '@haierbusiness-front/apis';
import { UserSwitchOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import UserDialog from './user-dialog.vue'
import { useRoute } from "vue-router";

const route = useRoute()

// 授权应用
const appVisible = ref(false)
const accountNo = ref<string>('')
const enterpriseCode = ref<string>('')

onMounted(() => {
    accountNo.value = route.query.accountNo?.toString() || ''
    enterpriseCode.value = route.query.enterpriseCode?.toString() || ''
    userApiRun({ accountNo: accountNo.value, pageNum: 1, pageSize: 10 })
})


watch(() => route.query, () =>{
    accountNo.value = route.query.accountNo?.toString() || ''
})

// 右上  应用下的人
const {
    data: userDataSource,
    run: userApiRun,
    loading,
    current: userListCurrent,
    pageSize: userListPageSize,
} = usePagination(virtualPayApi.authorizedUserList)

const userPagination = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: userDataSource.value?.total,
    current: userDataSource.value?.pageNum,
    pageSize: userDataSource.value?.pageSize,
    style: { justifyContent: 'center' },
}));

const handleAuth = () => {

}

// 用户
const columns = [
    {
        title: '账号',
        dataIndex: 'username',
    },
    {
        title: '姓名',
        dataIndex: 'nickName',
    },
    {
        title: '性别',
        dataIndex: 'gender',
    },
    {
        title: '电话',
        dataIndex: 'phone',
    },
    {
        title: '邮箱',
        dataIndex: 'email',
    },
    {
        title: '授权应用',
        dataIndex: 'applicationInfoList',
    },
]



const onAppDialogShow = () => {
  appVisible.value = true
}

const onAppDialogClose = () => {
  appVisible.value = false
}

</script>

<template>
    <div>
        <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
            <h-row style="height:100%">
                <h-col :span="24" style="height:100%">
                    <h-row >
                        <h-col :span="24" style="padding: 10px 0 10px 0;">
                            <h-button type="primary" @click="onAppDialogShow()">
                                <UserSwitchOutlined /> 授权用户
                            </h-button>
                        </h-col>
                        <h-col :span="24" >
                            <h-table :columns="columns" :row-key="(record: IPaymentVirtualAccount) => record.id?.toString()" size="small" :data-source="userDataSource?.records" :pagination="userPagination" :loading="loading">
                                    <template #bodyCell="{ text, column, record }">
                                        <template v-if="column.dataIndex === 'applicationInfoList'">
                                            <h-tag style="margin-left: 10px;" color="blue" :key="index" v-for="(item, index) in text">{{ item.applicationName }}</h-tag>
                                        </template>
                                        <template v-if="column.dataIndex === 'state'">
                                            {{ record._stateName }}
                                        </template>
                                    </template>
                                </h-table>
                        </h-col>
                    </h-row>
                </h-col>
            </h-row>
        </div>

        <div v-if="appVisible">
            <user-dialog
                :show="appVisible"
                :account-no="accountNo"
                :enterprise-code="enterpriseCode"
                @cancel="onAppDialogClose"
                @ok="onAppDialogClose"
            >
            </user-dialog>
        </div>
    </div>
    
</template>


<style lang="less" scoped>
.important {
    color: red;
}
</style>
  