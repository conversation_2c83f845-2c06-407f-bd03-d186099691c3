import { HotelSyncRecordRes,DistrictProviderMapPageReq } from '@haierbusiness-front/common-libs'
import { download, get, post,filepost,originalGet} from '../request'

export const addressApi = {
    // 城市列表
    getDistrictList: (params:any): Promise<void> => {
        return get('/common/api/district/list', params)
    },
    // 供应商行政区划
    getProviderDistrictList: (params:DistrictProviderMapPageReq): Promise<void> => {
        return get('/common/api/districtProviderMap/page', params)
    },
    // 关联 供应商行政区划信息映射国旅行政区划
    districtProviderMapDistrict: (params:DistrictProviderMapPageReq): Promise<void> => {
        return get('/common/api/districtProviderMap/districtProviderMapDistrict', params)
    },
    // 解除供应商映射
    districtProviderRelieveMap: (params:DistrictProviderMapPageReq): Promise<void> => {
        return get('/common/api/districtProviderMap/districtProviderRelieveMap', params)
    },
    // 更新地址字段
    update: (params:DistrictProviderMapPageReq): Promise<void> => {
        return post('/common/api/district/update', params)
    },
    // 行政区划资源
    getDistrictTrees: (params:DistrictProviderMapPageReq): Promise<void> => {
        return get('/common/api/district/trees', params)
    },
}   