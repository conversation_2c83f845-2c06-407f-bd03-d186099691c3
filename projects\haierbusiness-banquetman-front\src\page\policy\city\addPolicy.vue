<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem,
  Form as hForm,
  FormItem as hFormItem,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  Cascader as hCascader,
  Card as hCard,
  TreeSelect as hTreeSelect,
  Modal as hModal,
  Input as hInput,
  Icon as hIcon
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, DownOutlined, UpOutlined,CloseOutlined } from '@ant-design/icons-vue';
import { banquetPolicyApi, departmentApi, banquetApi, cityApi } from '@haierbusiness-front/apis';
import {
  BApplyListRecord,
  BApplyPerson,
  BanquetStatusEnum,
  IUserListRequest,
  IUserInfo,
  BPolicyReq
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';
import type { ShowSearchType } from 'ant-design-vue/es/cascader';
import { Cascader } from 'ant-design-vue';

import router from '../../../router'
// const router = useRouter()
const store = applicationStore();

const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router
  getCityList()
  getDepartmentList()

  // 获取缓存数据
  if (localStorage.getItem('banquet_policy_form')) {
    formState.value = JSON.parse(localStorage.getItem('banquet_policy_form'))
  }
})
const route = ref(getCurrentRoute());
const id = route.value?.query?.id;

const { loginUser } = storeToRefs(store);

const rules = {
  persons: [
    {
      required: true,
      message: '请选择用户',
      trigger: 'blur',
    },
  ],
  cityCodes: [
    {
      required: true,
      message: '请选择城市',
      trigger: 'blur',
    },
  ],
}


const formState = ref<BPolicyReq>(
  {
    sceneType: 1,
    xzType: 1,
    cityScope: false,
    advanceChooseRestaurant: false,
    restaurantScope: false,
    persons: [],
    depts: [],
    citys: [],
    restaurants: []
  }
);

// 申请人 
// 用户选择
const params = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});
// 申请人
const nickName = ref<Array<string>>([]);

const departmentCodes = ref<Array<string>>([]);

const userNameChange = (userInfo: Array<IUserInfo>) => {
  if (!userInfo.length) {
    formState.value.persons = []
  }
  console.log(99999, userInfo)
  const array: Array<string> = [];
  formState.value.persons = []
  userInfo.forEach((item: any, index: number) => {
    if (item.nickName && item.username) {
      array.push(item.nickName + '/' + item.username);
      formState.value.persons?.push(
        {
          policyObjectCode: item.username,
          policyObjectName: item.nickName
        }
      )
    } else {
      array.push(
        nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[0] +
        '/' +
        nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[1],
      );
      formState.value.persons?.push(
        {
          policyObjectCode: nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[1],
          policyObjectName: nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[0]
        }
      )
    }

  })
  nickName.value = array;

}


// 获取海尔部门列表
const departmentList = ref()
const getDepartmentList = () => {
  departmentApi.departmentTree({
    enterpriseCode: 'haier'
  }).then(res => {
    departmentList.value = res[0].children[0].children
  })
}

const changeDepartment = (value,label) => {
  formState.value.depts = []
  value?.forEach((item,index) => {
    formState.value.depts.push({
      policyObjectCode: item,
      policyObjectName: label[index],
    })
  })
}



// 获取美团城市列表
const cityDict = ref<Array<any>>([])
const getCityList = () => {
  // banquetApi.getCity().then(res => {
  //   cityDict.value = res
  // })

  const params = {
    id: 1,
    providerCode: 'MT',
    subdistrict: 2
  }
  cityApi.getCityTree(params).then(res => {
    cityDict.value = res.children
  })
}
const changeCityScope = () =>{
  formState.value.citys = []
}

const changeCity = (value, selectedOptions) => {
  console.log(value, selectedOptions)
  formState.value.citys = []
  selectedOptions?.forEach(item => {
    formState.value.citys.push({
      // item.providerMapList[0]?.districtId,
      policyObjectCode: item[item.length - 1]?.providerMapList[0]?.districtId,
      policyObjectName: item[item.length - 1]?.providerMapList[0]?.districtName,
      mealLocationProvinceCode: item[0]?.providerMapList ?  item[0]?.providerMapList[0]?.districtId : item[item.length - 1]?.providerMapList[0]?.districtId,
      mealLocationProvince: item[0]?.providerMapList ? item[0]?.providerMapList[0]?.districtName : item[item.length - 1]?.providerMapList[0]?.districtName,
    })
  })
}

const userSelectRef = ref()
// 修改时获取详情
const getDetailByid = (id: number) => {
  banquetPolicyApi.get(id).then(res => {
    formState.value = res
    const firstData:any[] = []
    // 如果管控人员
    if (res?.persons && res?.persons.length) {
      formState.value.xzType = 1
      nickName.value = res?.persons?.map(item => {

        firstData.push(item.policyObjectCode)
        
        return item.policyObjectName + '/' + item.policyObjectCode
      })
    } else {
      formState.value.xzType = 2
    }
    if (firstData.length > 0) {
      userSelectRef.value.setFirstData(firstData)
    }
    
    // 如果管控城市
    if (res.cityScope) {
      formState.value.cityCodes = JSON.parse(res?.jsonFormat)?.cityCodes
    }
    departmentCodes.value = JSON.parse(res?.jsonFormat)?.departmentCodes
  })
}

// 新增餐厅信息
const showRestaurantModal = ref({
  restaurantId: '',
  restaurantName: ''
})
const showModal = ref<boolean>(false)
const addRestaurantInfo = () => {
  showModal.value  = true
}

const saveRules = {
  restaurantId: [
    {
      required: true,
      message: '请输入餐厅id',
      trigger: 'blur',
    },
  ],
  restaurantName: [
    {
      required: true,
      message: '请输入餐厅名称',
      trigger: 'blur',
    },
  ],
}

const saveReataurantInfo = () => {
  formState.value.restaurants = [...formState.value.restaurants, {
      policyObjectCode: showRestaurantModal.value.restaurantId,
      policyObjectName: showRestaurantModal.value.restaurantName
    }]

    showModal.value  = false
    showRestaurantModal.value = {
    restaurantId: '',
    restaurantName: ''
  }
}

const clearInfo = () => {
  showModal.value  = false
  showRestaurantModal.value = {
    restaurantId: '',
    restaurantName: ''
  }
}

// 修改限制用户、部门
const xzTypeChange = () => {
  // 清空选择的用户、部门数据
  formState.value.persons = []
  formState.value.depts = []
  nickName.value = []
  departmentCodes.value = []
}





watch(
  () => id,
  (val: number) => {
    if (val) {
      getDetailByid(val);
    }
  },
  {
    immediate: true,
  },
);

const cancel = () => {
  currentRouter.value.back(-1);  
  localStorage.removeItem('banquet_policy_form')
}

const delRestaurant =(index:number) => {
  formState.value?.restaurants?.splice(index, 1)
}

const changeAdvanceChooseRestaurant = (val:any) =>{
  if(!val.target.value){
    formState.value.restaurantScope = false
    formState.value.restaurants = []
  }
}

// 提交或者保存
const onFinish = () => {
  formState.value.jsonFormat = JSON.stringify(
    {
      cityCodes:formState.value.cityCodes || [],
      departmentCodes: departmentCodes.value || []
    }
  )
  banquetPolicyApi.saveOrUpdate(formState.value).then(res => {
    // message.success(`提交成功`);
    currentRouter.value.push('/policy/list');
  })
}

</script>


<template>
  <div>
    <h-row justify="space-between" style="padding: 20px;">
      <h-col style="font-size: 18px;">新增政策</h-col>
      <h-col>
        <h-button type="link" @click="currentRouter.back(-1)">返回</h-button>
      </h-col>
    </h-row>
    <div
      style="background-color: #ffff;height: 100%;width: 1200px;padding: 50px;margin: 0 auto; overflow: auto;"
    >
      <h-form
        :model="formState"
        name="basic"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 14 }"
        autocomplete="off"
        labelAlign="left"
        @finish="onFinish"
        :rules="rules"
      >
        <h-form-item name="sceneType" label="就餐类型">
          <h-radio-group v-model:value="formState.sceneType">
            <h-radio :value="1">宴请</h-radio>
            <!-- <h-radio :value="2">外卖</h-radio> -->
          </h-radio-group>
        </h-form-item>

        <h-form-item name="xzType" label="受限制用户">
          <h-radio-group v-model:value="formState.xzType" @change="xzTypeChange">
            <h-radio :value="1">用户</h-radio>
            <h-radio :value="2">部门</h-radio>
          </h-radio-group>
        </h-form-item>

        <h-form-item name="persons" label="选择用户" v-if="formState.xzType == 1">
          <user-select
            ref="userSelectRef"
            :value="nickName"
            :multiple="true"
            :params="params"
            @change="(userInfo: IUserInfo) => userNameChange(userInfo)"
          />
        </h-form-item>

        <h-form-item name="department" label="选择部门" v-if="formState.xzType == 2">
          <h-tree-select
            treeNodeFilterProp="name"
            v-model:value="departmentCodes"
            @change="changeDepartment"
            style="width: 100%"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            placeholder="请选择部门"
            allow-clear
            multiple
            :tree-data="departmentList"
            :fieldNames="{ label: 'name', value: 'code', children: 'children' }"
          ></h-tree-select>
        </h-form-item>

        <h-card title="城市管控" style="width: 100%; margin-bottom: 30px;">
          <h-form-item name="cityScope" label="是否限制城市">
            <h-radio-group @change="changeCityScope" v-model:value="formState.cityScope">
              <h-radio :value="false">不限制城市</h-radio>
              <h-radio :value="true">指定可选城市</h-radio>
            </h-radio-group>
          </h-form-item>

          <h-form-item name="cityCodes" label="选择城市" v-if="formState.cityScope">
            <h-cascader
              @change="changeCity"
              v-model:value="formState.cityCodes"
              :show-checked-strategy="Cascader.SHOW_CHILD"
              show-search
              multiple
              :fieldNames="{label: 'name', value: 'id', children:'children'}"
              :options="cityDict"
              placeholder="请选择城市"
              style="width: 100%"
              allow-clear
            />
          </h-form-item>
        </h-card>

        <h-card title="餐厅限制" style="width: 100%;margin-bottom: 30px;">
          <h-form-item name="advanceChooseRestaurant" label="是否提前选择餐厅">
            <h-radio-group @change="changeAdvanceChooseRestaurant" v-model:value="formState.advanceChooseRestaurant">
              <h-radio :value="true">是</h-radio>
              <h-radio :value="false">否</h-radio>
            </h-radio-group>
          </h-form-item>

          <h-form-item name="restaurantScope" label="是否限制餐厅范围">
            <h-radio-group v-model:value="formState.restaurantScope">
              <h-radio :value="true">是</h-radio>
              <h-radio :value="false">否</h-radio>
            </h-radio-group>
          </h-form-item>

          <h-form-item
            name="radio-group"
            label="选择餐厅"
            v-if="formState.restaurantScope && formState.advanceChooseRestaurant"
          >
            <h-row>
              <h-col :span="20" style="margin-right: 12px">
                <div style="border: 1px solid #d9d9d9; padding: 10px; min-height: 200px">
                  <h-tag v-for="item,index in formState.restaurants" :key="index">
                    <div>
                      {{item.policyObjectName}}
                      <CloseOutlined
                        style="font-size: 12px; cursor: pointer;"
                        @click="delRestaurant(index)"
                      />
                    </div>
                  </h-tag>
                </div>
              </h-col>
              <h-col>
                <h-button type="primary" @click="addRestaurantInfo">添加餐厅</h-button>
              </h-col>
            </h-row>
          </h-form-item>
        </h-card>

        <h-form-item :wrapper-col="{ offset: 8, span: 16 }">
          <h-button style="margin-right: 20px;" @click="cancel">取消</h-button>
          <h-button type="primary" html-type="submit">提交</h-button>
        </h-form-item>
      </h-form>

      <h-modal v-model:open="showModal" title="新增餐厅信息" :footer="null">
        <h-form
          :model="showRestaurantModal"
          :label-col="{ span: 5 }"
          :wrapper-col="{ span: 19 }"
          autocomplete="off"
          @finish="saveReataurantInfo"
          :rules="saveRules"
        >
          <h-form-item name="restaurantName" label="餐厅名称">
            <h-input
              v-model:value="showRestaurantModal.restaurantName"
              placeholder="餐厅名称"
              style="height: 100%; "
            />
          </h-form-item>
          <h-form-item name="restaurantId" label="餐厅id">
            <h-input
              v-model:value="showRestaurantModal.restaurantId"
              placeholder="餐厅id"
              style="height: 100%; "
            />
          </h-form-item>

          <h-form-item :wrapper-col="{ offset: 8, span: 16 }">
            <h-button style="margin-right: 20px;" @click="clearInfo">取消</h-button>
            <h-button type="primary" html-type="submit">确定</h-button>
          </h-form-item>
        </h-form>
      </h-modal>
    </div>
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

:deep(.ant-descriptions-item-label) {
  width: 200px;
}

:deep(.ant-descriptions-item-content) {
  width: 300px;
}
</style>
