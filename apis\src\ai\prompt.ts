import {get, post} from '../request'
import {AiPromptDO, IAiPromptListRequest} from "@haierbusiness-front/common-libs/src/ai";


export const promptApi = {
    // 查询所有AI提示词
    list: (param: IAiPromptListRequest): Promise<AiPromptDO[]> => {
        return get('/ai/api/ai/prompt/list', param)
    },

    // 根据ID查询AI提示词
    getById: (id: number): Promise<AiPromptDO> => {
        return get('/ai/api/ai/prompt/get', {id})
    },

    // 新增AI提示词
    save: (data: AiPromptDO): Promise<boolean> => {
        return post('/ai/api/ai/prompt/save', data)
    },

    // 更新AI提示词
    updateById: (data: AiPromptDO): Promise<boolean> => {
        return post('/ai/api/ai/prompt/update', data)
    },

    // 删除AI提示词
    removeById: (id: number): Promise<boolean> => {
        return post('/ai/api/ai/prompt/delete', {id})
    }
}

