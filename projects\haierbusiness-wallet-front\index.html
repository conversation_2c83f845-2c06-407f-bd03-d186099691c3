<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <script crossorigin="anonymous" src="https://r.haier.net/assets/prod/dts-fe/kitchengod/0.2.55/index.js?scode=S03256"></script>
    <title>海尔商务云支付中台</title>
  </head>
  <body>
    <script>
        window.__Konph = {
          "popup-js": {
              "dom-id": "helpCenterBtn",
              "one-url-name": "popup/prod",/*测试：popup/test*//*生产：popup/prod*/
              "apply-code": "S03256",/*替换系统码*/
              "scene-code": "240415798886",/*替换场景码*/
              "user-code": "",
              "map": encodeURI('{"帮助中心":["in","bzzx"]}'),
              "mode": "mobile",/*按需接入*/
              "delay-time": 3,
              "is-draggable":"1",
              "url-list": [],
              "tip-name": "帮助"
          }
        };
    </script>

    <div id="helpCenterBtn"></div> 
    <div id="app"></div>
    <!--生产--> 
    <script src="https://r.haier.net/assets/overlay/dts-fe/online-popup/index.js"></script>
    
    <!--测试--> 
    <!-- <script src="https://r.haier.net/assets/daily/dts-fe/online-popup/3.0.1/index.js"></script> -->
    <script crossorigin="anonymous" src="https://r.haier.net/assets/prod/dts-fe/ihaier2runtime/1.0.7/index.js"></script>
    <script type="module" src="/src/main.ts"></script>
    <!-- <script src="//r.haier.net/assets/overlay/dts-fe/common-assets/vconsole/3.10.1/vconsole.min.js"></script>
    <script>
      // VConsole 默认会挂载到 `window.VConsole` 上
      var vConsole = new window.VConsole();
    </script> -->
  </body>
</html>
