<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Cascader as hCascader,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  Textarea as hTextarea,
  Upload as hUpload
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons-vue';
import { banquetOrderApi, fileApi, download, banquetApi } from '@haierbusiness-front/apis';
import {
  IApplyFilter,
  IApply,
  BanquetStatusEnum,
  BanquetApproveStatusEnum,
  BOrderPageRes
} from '@haierbusiness-front/common-libs';
import { getEnumOptions } from '@haierbusiness-front/utils';

import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import type { Rule } from 'ant-design-vue/es/form';
import type { UploadProps } from 'ant-design-vue';


import router from '../../router'
// const router = useRouter()

const currentRouter = ref()

const orderQuery = ref()

onMounted(async () => {
  currentRouter.value = await router
  orderQuery.value = JSON.parse(localStorage?.getItem('order_query'))
  handleTableChange({ current: 1, pageSize: 10 })

})

const columns: ColumnType[] = [
  {
    title: '预订单号',
    dataIndex: 'orderBookingCode',
    width: '240px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '美团预订单号',
    dataIndex: 'mtBookingCode',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '关联申请单',
    dataIndex: 'orderCode',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '对账单号',
    dataIndex: 'settleCode',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },


  {
    title: '签字人工号',
    dataIndex: 'signerCode',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },

  {
    title: '签字人名称',
    dataIndex: 'signerName',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '就餐时间',
    dataIndex: 'mealTime',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '订餐类型',
    dataIndex: 'sceneType',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '餐厅名称',
    dataIndex: 'restaurantName',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '实际餐费金额',
    dataIndex: 'actualPaymentAmount',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '核对状态',
    dataIndex: 'checkStatus',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },

  {
    title: '核对时间',
    dataIndex: 'checkTime',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },

  {
    title: '核对人信息',
    dataIndex: 'checkerName',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },

  {
    title: '核对备注',
    dataIndex: 'checkRemark',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },

  {
    title: '关闭时间',
    dataIndex: 'closeTime',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },

  {
    title: '关闭人信息',
    dataIndex: 'closerName',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  
  {
    title: '异常处理备注',
    dataIndex: 'exceptionResult',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  
  {
    title: '附件信息',
    dataIndex: 'exceptionAttachment',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<IApplyFilter>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(banquetOrderApi.getPage);

const {
  data: exportListData,
  run: exportListApiRun,
  loading: exportListLoading,
} = useRequest(banquetOrderApi.exportPage);

const reset = () => {
  searchParam.value = {}
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    ...orderQuery.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

// 订单核对
const showCheckDialog = ref<boolean>(false)

const checkOrderData = ref<BOrderPageRes>()
const checkOrderForm = ref()



const orderRules: Record<string, Rule[]> = {
  checkRemark: [
    { required: true, message: '请输入核对备注', trigger: 'change' },
  ],
  checkStatus: [
    { required: true, message: '请选择核对状态', trigger: 'blur' },
  ],
  exceptionAttachment: [
    { required: true, message: '请上传证明材料', trigger: 'change' },
  ],
  exceptionAttachmentName: [
    { required: true, message: '请上传证明材料', trigger: 'change' },
  ],
  exceptionResult: [
    { required: true, message: '请输入处理结果', trigger: 'blur' },
  ],

}

const openCheckOrder = (record?: BOrderPageRes) => {
  checkOrderData.value = JSON.parse(JSON.stringify(record))
  showCheckDialog.value = true

}
const checkOrder = () => {
  checkOrderForm.value.validate().then(() => {
    banquetOrderApi.updateCheckStatus(checkOrderData.value).then(res => {
      showCheckDialog.value = false
      listApiRun({
        ...searchParam.value,
        ...orderQuery.value,
        pageNum: current.value,
        pageSize: pageSize.value,
      });
    })
  }).catch(() => {

  })

}


// 异常处理
const errorForm = ref()

const showErrorDialog = ref<boolean>(false)
const openUpdateError = (record?: BOrderPageRes) => {
  checkOrderData.value = JSON.parse(JSON.stringify(record))
  if (checkOrderData.value?.exceptionAttachment) {
    fileList.value = [
      {
        name: checkOrderData.value.exceptionAttachmentName,
        url: checkOrderData.value.exceptionAttachment,
        uid: '2112'
      }
    ]
  }
  showErrorDialog.value = true
}

const updateError = () => {
  errorForm.value.validate().then(() => {
    checkOrderData.value.checkStatus = 3
    banquetOrderApi.updateCheckStatus(checkOrderData.value).then(res => {
      showErrorDialog.value = false
      handleTableChange({ current: 1, pageSize: 10 })
    })
  }).catch(() => {

  })
}

// 上传
const fileList = ref<UploadProps['fileList']>()
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const uploadLoading = ref(false)
const upload = (options: any) => {
  uploadLoading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);
  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;
      options.file.fileName = options.file.name;

      checkOrderData.value.exceptionAttachment = baseUrl + it.path;
      checkOrderData.value.exceptionAttachmentName = options.file.name;
      // options.
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

const removeFile = () => {
  checkOrderData.value.exceptionAttachment = ''
  checkOrderData.value.exceptionAttachmentName = ''
}


const downLoadFile = (url:string, name:string) => {
  window.open(url)
  // 
}

const toList = (code,type) => {
  if(type==1){
    currentRouter.value.push({
      path: '/reservation',
      query:{
        orderBookingCode:code
      }
    })
  }else if(type==2){
    currentRouter.value.push({
      path: '/apply',
      query:{
        orderCode:code
      }
    })
  }else{
    currentRouter.value.push({
      path: '/statementAccount',
      query:{
        settleCode:code
      }
    })
  }
}

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="orderBookingCode">预订单号:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="预订单号" v-model:value="searchParam.orderBookingCode" style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="mtBookingCode">美团预订单号:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="美团预订单号" v-model:value="searchParam.mtBookingCode" style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="orderCode">关联申请单号:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="关联申请单号" v-model:value="searchParam.orderCode" style="width: 100%" allow-clear />
          </h-col>

          <!-- <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="settleCode">对账单号:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="对账单号" v-model:value="searchParam.settleCode"  style="width: 100%" allow-clear />
          </h-col> -->

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="sceneType">订单类型:</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.sceneType" allow-clear style="width: 100%"
              placeholder="请选择申请类型">
              <h-select-option :value="1">宴请</h-select-option>
              <h-select-option :value="2">外卖</h-select-option>
            </h-select>
          </h-col>
        </h-row>


        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">



          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="signerCode">签单人工号:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="签单人工号" v-model:value="searchParam.signerCode" style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="signerName">签单人姓名:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="签单人姓名" v-model:value="searchParam.signerName" style="width: 100%" allow-clear />
          </h-col>


          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="closerName">关闭人:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="关闭人" v-model:value="searchParam.closerName" style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="checkerName">核对人:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="核对人" v-model:value="searchParam.checkerName" style="width: 100%" allow-clear />
          </h-col>

        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">



          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="checkStatus">核对状态:</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.checkStatus" allow-clear style="width: 100%"
              placeholder="请选择核对状态">
              <h-select-option :value="0">未核对</h-select-option>
              <h-select-option :value="1">核对正常</h-select-option>
              <h-select-option :value="2">核对异常</h-select-option>

            </h-select>
          </h-col>



        </h-row>


        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button style="margin-right: 10px" type="primary"
              @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>

            <h-button type="primary" :loading="exportListLoading"
              @click="exportListApiRun({ ...searchParam, ...orderQuery });">
              导出
            </h-button>
          </h-col>
        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-descriptions title="筛选条件" :column="5" size="small">
            <h-descriptions-item label="餐厅名称">{{ orderQuery?.restaurantName }}</h-descriptions-item>
            <h-descriptions-item label="对账单号" v-if="orderQuery?.settleCode">{{ orderQuery?.settleCode
              }}</h-descriptions-item>
            <h-descriptions-item label="申请时间" v-if="orderQuery?.applicationTimes">{{ `${orderQuery?.applicationTimes[0]
              } - ${orderQuery?.applicationTimes[1]}` }}</h-descriptions-item>
            <h-descriptions-item label="支付时间" v-if="orderQuery?.payTime">{{ `${orderQuery?.payTime[0]} -
              ${orderQuery?.payTime[1]}` }}</h-descriptions-item>
            <h-descriptions-item label="对账时间" v-if="orderQuery?.settleTimes">
              {{ `${orderQuery?.settleTimes[0]} - ${orderQuery?.settleTimes[1]}` }}
            </h-descriptions-item>
          </h-descriptions>
        </h-row>


        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'orderBookingCode'">
              <a @click="toList(record.orderBookingCode,1)">{{record.orderBookingCode}}</a>
            </template>
            <template v-if="column.dataIndex === 'orderCode'">
              <a @click="toList(record.orderCode,2)">{{record.orderCode}}</a>
            </template>
            <template v-if="column.dataIndex === 'settleCode'">
              <a @click="toList(record.settleCode,3)">{{record.settleCode}}</a>
            </template>
            <!-- 核对人 -->
            <template v-if="column.dataIndex === 'checkerName'">
              <div v-if="record.checkerName">{{ `${record.checkerName}(${record.checkerCode})` }}</div>
            </template>
            <!-- 关闭人 -->
            <template v-if="column.dataIndex === 'closerName'">
              <div v-if="record.closerName">{{ `${record.closerName}(${record.closerCode})` }}</div>
            </template>

            <!-- 核对状态 -->
            <template v-if="column.dataIndex === 'checkStatus'">
              <div>{{ record.checkStatus == 0 ? '未核对' : record.checkStatus == 1 ? '核对正常' : '核对异常' }}</div>
            </template>

            <!-- 订餐类型 -->
            <template v-if="column.dataIndex === 'sceneType'">
              <div>{{ record.sceneType == 1 ? '宴请' : '外卖' }}</div>
            </template>
            <!-- exceptionAttachment -->
            <template v-if="column.dataIndex === 'exceptionAttachment'">
              <a v-if="record.exceptionAttachment" :href="record.exceptionAttachment" target="_blank">{{record.exceptionAttachmentName }}</a>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" v-if="record.checkStatus == 0" @click="openCheckOrder(record)">订单核对</h-button>
              <h-button type="link" v-if="record.checkStatus == 2" @click="openUpdateError(record)">异常处理</h-button>

            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>


    <!-- 订单核对 -->
    <h-modal v-model:open="showCheckDialog" width="1200px" title="订单核对" @ok="checkOrder">
      <div style="padding: 20px">
        <h-descriptions style="margin-bottom: 30px" title="订单信息" bordered size="small">
          <h-descriptions-item label="预订单号">{{ checkOrderData?.orderBookingCode }}</h-descriptions-item>
          <h-descriptions-item label="美团预订单号">{{ checkOrderData?.mtBookingCode }}</h-descriptions-item>
          <h-descriptions-item label="关联申请单">{{ checkOrderData?.orderCode }}</h-descriptions-item>

          <h-descriptions-item label="对账单号">{{ checkOrderData?.settleCode }}</h-descriptions-item>
          <h-descriptions-item label="签单人工号">{{ checkOrderData?.signerCode }}</h-descriptions-item>
          <h-descriptions-item label="签单人名称">{{ checkOrderData?.signerName }}</h-descriptions-item>

          <!-- <h-descriptions-item label="预订单号">{{ checkOrderData?.orderBookingCode }}</h-descriptions-item>
          <h-descriptions-item label="美团预订单号">{{ checkOrderData?.mtBookingCode }}</h-descriptions-item>
          <h-descriptions-item label="关联申请单">{{ checkOrderData?.orderCode }}</h-descriptions-item> -->

          <h-descriptions-item label="订餐类型">{{ checkOrderData?.sceneType == 1 ? '宴请' : '外卖' }}</h-descriptions-item>
          <h-descriptions-item label="就餐城市">{{ checkOrderData?.mealLocationProvince }}{{
            checkOrderData?.mealLocationCity
          }}</h-descriptions-item>
          <h-descriptions-item label="餐厅名称">{{ checkOrderData?.restaurantName }}</h-descriptions-item>

          <h-descriptions-item label="餐厅电话">{{ checkOrderData?.restaurantPhone }}</h-descriptions-item>
          <h-descriptions-item label="餐厅地址">{{ checkOrderData?.restaurantLocation }}</h-descriptions-item>
          <h-descriptions-item label="签到人信息">{{ checkOrderData?.checkinPersonName ?
            `${checkOrderData?.checkinPersonName}(${checkOrderData?.checkinPersonCode})` : '' }}</h-descriptions-item>

          <h-descriptions-item label="签到时间">{{ checkOrderData?.checkinTime }}</h-descriptions-item>
          <h-descriptions-item label="签到地址">{{ checkOrderData?.checkinLocation }}</h-descriptions-item>
          <h-descriptions-item label="支付人信息">{{ checkOrderData?.payerPersonName ?
            `${checkOrderData?.payerPersonName}(${checkOrderData?.payerCode})` : '' }}</h-descriptions-item>

          <h-descriptions-item label="支付时间">{{ checkOrderData?.payTime }}</h-descriptions-item>
          <h-descriptions-item label="实际支付金额">{{ checkOrderData?.actualPaymentAmount }}</h-descriptions-item>
          <h-descriptions-item label="水票信息"> <div style="color: #2870ff; cursor: pointer;" v-if="checkOrderData?.waterTicketInformation" @click="downLoadFile(checkOrderData?.waterTicketInformation, '水票信息')">水票信息</div></h-descriptions-item>
        </h-descriptions>

        <h-form ref="checkOrderForm" :model="checkOrderData" :rules="orderRules">
          <h-row
            style="   margin-bottom: 10px; color: rgba(0, 0, 0, 0.88); font-weight: 600; font-size: 16px;">订单核对</h-row>
          <h-form-item name="checkStatus" label="核对结果">
            <h-radio-group v-model:value="checkOrderData.checkStatus">
              <h-radio :value="1">核对正常</h-radio>
              <h-radio :value="2">核对异常</h-radio>
            </h-radio-group>
          </h-form-item>

          <h-form-item name="checkRemark" label="核对备注">
            <h-textarea v-model:value="checkOrderData.checkRemark" />
          </h-form-item>

        </h-form>
      </div>

    </h-modal>

    <!-- 异常验证 -->
    <h-modal v-model:open="showErrorDialog" width="1200px" title="异常验证" @ok="updateError">
      <div style="padding: 20px">
        <h-descriptions style="margin-bottom: 30px" title="订单信息" bordered size="small">
          <h-descriptions-item label="预订单号">{{ checkOrderData?.orderBookingCode }}</h-descriptions-item>
          <h-descriptions-item label="美团预订单号">{{ checkOrderData?.mtBookingCode }}</h-descriptions-item>
          <h-descriptions-item label="关联申请单">{{ checkOrderData?.orderCode }}</h-descriptions-item>

          <h-descriptions-item label="对账单号">{{ checkOrderData?.settleCode }}</h-descriptions-item>
          <h-descriptions-item label="签字人工号">{{ checkOrderData?.signerCode }}</h-descriptions-item>
          <h-descriptions-item label="签字人名称">{{ checkOrderData?.signerName }}</h-descriptions-item>

          <!--<h-descriptions-item label="预订单号">{{ checkOrderData?.orderBookingCode }}</h-descriptions-item>
          <h-descriptions-item label="美团预订单号">{{ checkOrderData?.mtBookingCode }}</h-descriptions-item>
          <h-descriptions-item label="关联申请单">{{ checkOrderData?.orderCode }}</h-descriptions-item>> -->

          <h-descriptions-item label="订餐类型">{{ checkOrderData?.sceneType == 1 ? '宴请' : '外卖' }}</h-descriptions-item>
          <h-descriptions-item label="就餐城市">{{ checkOrderData?.mealLocationProvince }}{{
            checkOrderData?.mealLocationCity
          }}</h-descriptions-item>
          <h-descriptions-item label="餐厅名称">{{ checkOrderData?.restaurantName }}</h-descriptions-item>

          <h-descriptions-item label="餐厅电话">{{ checkOrderData?.restaurantPhone }}</h-descriptions-item>
          <h-descriptions-item label="餐厅地址">{{ checkOrderData?.restaurantLocation }}</h-descriptions-item>
          <h-descriptions-item label="签单人信息">{{ checkOrderData?.checkinPersonName ?
            `${checkOrderData?.checkinPersonName}(${checkOrderData?.checkinPersonCode})` : '' }}</h-descriptions-item>

          <h-descriptions-item label="签到时间">{{ checkOrderData?.checkinTime }}</h-descriptions-item>
          <h-descriptions-item label="签到地址">{{ checkOrderData?.checkinLocation }}</h-descriptions-item>
          <h-descriptions-item label="支付人信息">{{ checkOrderData?.payerPersonName ?
            `${checkOrderData?.payerPersonName}(${checkOrderData?.payerCode})` : '' }}</h-descriptions-item>

          <h-descriptions-item label="支付时间">{{ checkOrderData?.payTime }}</h-descriptions-item>
          <h-descriptions-item label="实际支付金额">{{ checkOrderData?.actualPaymentAmount }}</h-descriptions-item>
          <h-descriptions-item label="水票信息"> <div style="color: #2870ff; cursor: pointer;" @click="downLoadFile(checkOrderData?.waterTicketInformation, '水票信息')">水票信息</div></h-descriptions-item>
        </h-descriptions>

        <h-descriptions style="margin-bottom: 30px" title="核对信息" bordered size="small">
          <h-descriptions-item label="核对人信息">{{ checkOrderData?.checkerName ?
            `${checkOrderData?.checkerName}(${checkOrderData?.checkerCode})` : '' }}</h-descriptions-item>
          <h-descriptions-item label="核对时间">{{ checkOrderData?.checkTime }}</h-descriptions-item>
          <h-descriptions-item label="核对结果">{{ checkOrderData?.checkStatus == 2 ? '核对异常' : '核对正常'
            }}</h-descriptions-item>
          <h-descriptions-item label="核对备注信息">{{ checkOrderData?.checkRemark }}</h-descriptions-item>

        </h-descriptions>

        <h-form ref="errorForm" :model="checkOrderData" :rules="orderRules">
          <h-row
            style=" margin-bottom: 10px; color: rgba(0, 0, 0, 0.88); font-weight: 600; font-size: 16px;">异常处理</h-row>
          <h-form-item name="exceptionAttachment" label="附件证明">
            <h-upload name="tixketFileMx" v-model:file-list="fileList" :custom-request="upload" :max-count="1" @remove="removeFile">
              <h-button class="mr-10">
                <VerticalAlignTopOutlined />
                <span class="font-size-14">上传附件</span>
              </h-button>
            </h-upload>
          </h-form-item>

          <h-form-item name="exceptionResult" label="处理结果">
            <h-textarea v-model:value="checkOrderData.exceptionResult" />
          </h-form-item>
        </h-form>
      </div>

    </h-modal>
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

:deep(.ant-descriptions-item-label) {
  // width: 140px;
}

:deep(.ant-descriptions-item-content) {
  width: 300px;
}
</style>
