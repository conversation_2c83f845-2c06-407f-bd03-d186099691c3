<script setup lang="ts">
import {showFailToast, But<PERSON> as VanButt<PERSON>, showDialog} from 'vant';
import 'vant/es/toast/style'
import 'vant/es/button/style'
import 'vant/es/dialog/style'
import d from '@/assets/image/demo/d.png';
import xx from '@/assets/image/demo/xx.png';
import yszy from '@/assets/image/demo/yszy.png';
import logo from '@/assets/image/logo.png';
import complete from '@/assets/image/complete.png'
import payBanner from '@/assets/image/payBanner.jpeg';
import {
  Divider as hDivider,
  Space as hSpace,
  But<PERSON> as hButton,
  Col as hCol,
  Result as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Tabs as hTabs,
  message
} from 'ant-design-vue';
import {computed, onMounted, ref, watch} from 'vue';
import {payApi, compositionPayApi, virtualPayApi} from '@haierbusiness-front/apis';
import {
  HeaderConstant,
  IPageResponse,
  IPayData,
  IPaymentRecord,
  PayStatusConstant,
  PayTypeConstant
} from '@haierbusiness-front/common-libs';
import {
  loadDataFromLocal,
  saveDataToLocal,
  removeStorageItem,
  isMobile,
  resolveToken
} from '@haierbusiness-front/utils';
import VirtualPay from './pc/virtualPay.vue';
import BudgetHaierPay from './pc/budgetHaierPay.vue';
import CompositionPay from './pc/compositionPay.vue';
import coinHaierPay from './pc/coinHaierPay.vue';
import travelCoinHaierPay from './pc/travelCoinHaierPay.vue';
import MobilePayCon from './mobile/index.vue' //因私支付组件 
import OfficialPayment from './mobile/officialPayment/index.vue' //因公支付组件
import {useRequest} from 'vue-request';

const loginUser = ref(resolveToken() || {})

const deviceType = ref('')

const isPayComplete = ref(false)
const loadCompleted = ref(false)

const setIsPayComplete = (value: boolean) => {
  isPayComplete.value = value
}

const finishedPayRecord = ref<IPaymentRecord | null>(new IPaymentRecord())

const urlSearchParams = new URLSearchParams(window.location.search);

/**
 * 获取支付参数
 */
const payData = (() => {
  const map = new Map();
  for (let a of urlSearchParams.entries()) {
    const v = map.get(a[0]);
    if (v) {
      map.set(a[0], [...v, a[1]]);
    } else {
      map.set(a[0], a[1]);
    }
  }
  const payData = Object.fromEntries(map) as unknown as IPayData
  if (payData.orderCode) {
    // 如果除了单号还存在金额,判断为刷新操作 ,从storage中取内容
    if (!payData.amount) {
      const localPayData = loadDataFromLocal("pay-data-code" + payData.orderCode)
      if (localPayData) {
        return localPayData;
      }
    } else {
      // 存在单号外内容判断为新跳转来的请求
      saveDataToLocal("pay-data-code" + payData.orderCode, payData);
      history.replaceState({}, document.title, window.location.origin + window.location.pathname + "?orderCode=" + payData.orderCode + window.location.hash);
    }
  }
  return payData;
})()

/**
 * 立即跳转
 */
const gotoCallback = () => {
  // window.location.replace = payData.callbackUrl!!
  window.location.replace(payData.callbackUrl!!)
}

/**
 * 支付完成
 */
const payComplete = (result: boolean) => {
  isPayComplete.value = result
  let countdown = 3;
  removeStorageItem("pay-data-code" + payData.orderCode);
  if (payData.callbackUrl) {
    setTimeout(() => {
      window.location.href = payData.callbackUrl!!
    }, 3000);
  } else {
    setTimeout(() => {
      // 跳转白屏不美观,期望关闭标签页
      window.location.href = "about:blank";
      window.close();
    }, 3000);
  }
  redirectTitle.value = countdown + "秒后跳转"
  setInterval(() => {
    if (countdown <= 0) {
      redirectTitle.value = countdown + "正在跳转"
    } else {
      countdown--;
      redirectTitle.value = countdown + "秒后跳转"
    }
  }, 1000);
}

const payTypes = ref<number[]>([] as number[])
const redirectTitle = ref("3秒后跳转")
const activeKey = ref()
const buffer = ref(false)

// 缓存两秒,解决一闪而过的 找不到支付方式提示
setTimeout(() => {
  buffer.value = true
}, 2000);


// 预查询公司支付账户是否存在
const {
  data: queryVirtualAccountsData,
  run: queryVirtualAccountsRun,
  loading: queryVirtualAccountsLoading,
} = useRequest(
    virtualPayApi.queryVirtualAccounts,
    {
      onSuccess: (data) => {
        if ((!data || data.length === 0) && activeKey.value === 5) {
          activeKey.value = payTypes.value[0]
        }
      }
    }
);

onMounted(() => {
  console.log("*********")
  deviceType.value = isMobile() ? 'MOBILE' : 'PC'
  if (isMobile()) {
    if (!payData.orderCode) {
      showFailToast("支付参数不正确!缺少订单号!");
      return;
    }
  } else {
    if (!payData.orderCode) {
      message.error("支付参数不正确!缺少订单号!")
      return;
    }
  }


  // 获取已支付成功记录
  payApi.searchPaidRecord(
      {
        "businessCode": payData.orderCode
      }
  ).then(it => {
    if (!it) {
      // 获取可用支付类型
      payApi.payTypes(
          {
            "applicationCode": payData.applicationCode,
            "username": payData.username,
            "enterpriseCode": payData.enterpriseCode,
            "payTypes": payData.payTypes,
            "businessType": payData.businessType
          }
      ).then(it => {
        finishedPayRecord.value = null;
        payTypes.value = it;

        activeKey.value = it.some(o => o === 5) ? 5 : it?.[0]

        loadCompleted.value = true;
        queryVirtualAccountsRun({
          applicationCode: payData.applicationCode,
          username: payData.username,
        })
      });
    } else {
      finishedPayRecord.value = it;
      loadCompleted.value = true;
    }
  })
});

const virtualAccountsData = computed(() => {
  return queryVirtualAccountsData.value || []
})
</script>

<template>
  <div v-if="deviceType == 'PC'">
    <div v-if="isPayComplete" class="masking">
      <div class="result-alert">
        <h-result status="success" title="支付完成！" :sub-title="redirectTitle">
          <template #extra>
            <h-button v-if="payData.callbackUrl" key="go" type="primary" @click="gotoCallback()">立刻跳转</h-button>
          </template>
        </h-result>
      </div>
    </div>
    <div class="base-container pay-container">
      <h-row class="header">
        <h-col :span="8" :offset="5">
          <img :src="logo" class="logo-img"/>
        </h-col>
      </h-row>
      <h-row class="banner">
        <h-col :span="24">
          <div class="float-left">
            <img :src="payBanner" class="banner-img"/>
          </div>
          <div class="float-left pay-title">
            <h3>
              <span class="chinese-font">
                便捷支付方式
              </span>
              <br>
              <span class="english-font">
                Convenient payment Methods
              </span>
            </h3>
          </div>
        </h-col>
      </h-row>
      <div v-if="loadCompleted">
        <div v-if="finishedPayRecord">
          <h-row class="finished-order">
            <h-col :span="14" :offset="5">
              <h-result status="success" title="订单已支付完成">
                <template #extra>
                  <h-row class="finished-order-row">
                    <h-col :span="20" :offset="2" alient>
                      <h-divider/>
                    </h-col>
                    <h-col v-if="finishedPayRecord.businessCode" :span="3" :offset="7" style="text-align: right;">
                      订单号&nbsp;:&nbsp;&nbsp;
                    </h-col>
                    <h-col v-if="finishedPayRecord.businessCode" :span="13" style="text-align: left;">
                      {{ finishedPayRecord.businessCode }}
                    </h-col>
                    <h-col v-if="finishedPayRecord.businessCode" :span="3" :offset="7" style="text-align: right;">
                      支付方式&nbsp;:&nbsp;&nbsp;
                    </h-col>
                    <h-col v-if="finishedPayRecord.businessCode" :span="13" style="text-align: left;">
                      {{ PayTypeConstant.ofType(finishedPayRecord.payType)?.name }}
                    </h-col>
                    <h-col v-if="finishedPayRecord.businessCode" :span="3" :offset="7" style="text-align: right;">
                      支付状态&nbsp;:&nbsp;&nbsp;
                    </h-col>
                    <h-col v-if="finishedPayRecord.businessCode" :span="13" style="text-align: left;">
                      {{ PayStatusConstant.ofType(finishedPayRecord.state)?.name }}
                    </h-col>
                    <h-col v-if="finishedPayRecord.code" :span="3" :offset="7" style="text-align: right;">
                      中台交易流水号&nbsp;:&nbsp;&nbsp;
                    </h-col>
                    <h-col v-if="finishedPayRecord.code" :span="13" style="text-align: left;">
                      {{ finishedPayRecord.code }}
                    </h-col>
                    <h-col v-if="finishedPayRecord.providerOrderCode" :span="3" :offset="7" style="text-align: right;">
                      第三方流水号&nbsp;:&nbsp;&nbsp;
                    </h-col>
                    <h-col v-if="finishedPayRecord.providerOrderCode" :span="13" style="text-align: left;">
                      {{ finishedPayRecord.providerOrderCode }}
                    </h-col>
                    <h-col v-if="finishedPayRecord.secondProviderOrderCode" :span="3" :offset="7"
                           style="text-align: right;">
                      第三方单号2&nbsp;:&nbsp;&nbsp;
                    </h-col>
                    <h-col v-if="finishedPayRecord.secondProviderOrderCode" :span="13" style="text-align: left;">
                      {{ finishedPayRecord.secondProviderOrderCode }}
                    </h-col>
                    <h-col v-if="finishedPayRecord.createBy" :span="3" :offset="7" style="text-align: right;">
                      创建人&nbsp;:&nbsp;&nbsp;
                    </h-col>
                    <h-col v-if="finishedPayRecord.createBy" :span="13" style="text-align: left;">
                      {{ finishedPayRecord.createBy }}
                    </h-col>
                    <h-col v-if="finishedPayRecord.owner" :span="3" :offset="7" style="text-align: right;">
                      支付人&nbsp;:&nbsp;&nbsp;
                    </h-col>
                    <h-col v-if="finishedPayRecord.owner" :span="13" style="text-align: left;">
                      {{ finishedPayRecord.owner }}
                    </h-col>
                    <h-col v-if="finishedPayRecord.gmtCreate" :span="3" :offset="7" style="text-align: right;">
                      创建时间&nbsp;:&nbsp;&nbsp;
                    </h-col>
                    <h-col v-if="finishedPayRecord.gmtCreate" :span="13" style="text-align: left;">
                      {{ finishedPayRecord.gmtCreate }}
                    </h-col>
                    <h-col v-if="finishedPayRecord.payTime" :span="3" :offset="7" style="text-align: right;">
                      发起支付时间&nbsp;:&nbsp;&nbsp;
                    </h-col>
                    <h-col v-if="finishedPayRecord.payTime" :span="13" style="text-align: left;">
                      {{ finishedPayRecord.payTime }}
                    </h-col>
                    <h-col v-if="finishedPayRecord.successPayTime" :span="3" :offset="7" style="text-align: right;">
                      交易成功时间&nbsp;:&nbsp;&nbsp;
                    </h-col>
                    <h-col v-if="finishedPayRecord.successPayTime" :span="13" style="text-align: left;">
                      {{ finishedPayRecord.successPayTime }}
                    </h-col>
                    <h-col :span="3" :offset="7" style="text-align: right;">
                      交易金额&nbsp;:&nbsp;&nbsp;
                    </h-col>
                    <h-col :span="13" style="text-align: left;">
                      {{ finishedPayRecord.amount }} 元
                    </h-col>
                    <h-col :span="18" :offset="3" style="margin-top: 3vh;">
                      <h-button type="primary" @click="gotoCallback">返&nbsp;&nbsp;&nbsp;回</h-button>
                    </h-col>
                  </h-row>
                </template>
              </h-result>
            </h-col>
          </h-row>
        </div>
        <div v-else>
          <h-row class="order-detail">
            <h-col :span="14" :offset="5">
              <h-row class="order-detail-table">
                <h-col :span="12">
                  <h-row class="remind">
                    <span>{{ loginUser.nickName }}/{{ loginUser.username }}，您好！订单已提交成功，请尽快付款</span>
                  </h-row>
                  <h-row class="order-code">
                    <span>订单号:{{ payData.orderCode }}</span>
                  </h-row>
                </h-col>
                <h-col :span="12" class="amount-layout">
                  <span class="describe">应付金额: </span>
                  <span class="amount"><span class="symbol">¥</span>{{
                      Number(payData.amount).toFixed(2) || '???'
                    }}</span>
                </h-col>
              </h-row>
            </h-col>
          </h-row>
          <h-row class="pay-parameter">
            <h-col :span="14" :offset="5">
              <div v-if="buffer && (!payTypes || payTypes.length === 0)" class="not-pay-types">
                找不到任何支付方式!
              </div>
              <h-tabs v-model:activeKey="activeKey">
                <h-tab-pane v-if="virtualAccountsData && virtualAccountsData.length > 0 && payTypes?.includes(5)"
                            :key="5"
                            tab="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;公司支付&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;">
                  <virtual-pay :param="payData" :accounts="virtualAccountsData" @payComplete="payComplete"/>
                </h-tab-pane>
                <h-tab-pane v-if="payTypes?.includes(1)" :key="1"
                            tab="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;企业预算&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;">
                  <budget-haier-pay :username="loginUser.username" :param="payData" @payComplete="payComplete"/>
                </h-tab-pane>
                <h-tab-pane v-if="payTypes?.includes(2)" :key="2"
                            tab="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;聚合支付&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;">
                  <composition-pay :param="payData" @payComplete="payComplete"/>
                </h-tab-pane>
                <h-tab-pane v-if="payTypes?.includes(3)" :key="3"
                            tab="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;线下支付&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;">
                  <img :src="xx"/>
                </h-tab-pane>
                <h-tab-pane v-if="payTypes?.includes(4)" :key="4"
                            tab="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;超市福利积分&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;">
                  <coin-haier-pay :param="payData" @payComplete="payComplete"/>
                </h-tab-pane>
                <!-- 因为支付顺序问题,5公司支付调整在上面 -->
                <h-tab-pane v-if="payTypes?.includes(6)" :key="6"
                            tab="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;机票福利积分&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;">
                  <travel-coin-haier-pay :param="payData" @payComplete="payComplete"/>
                </h-tab-pane>
              </h-tabs>
            </h-col>
          </h-row>
        </div>
      </div>
    </div>
  </div>
  <div v-else>
    <template v-if="loadCompleted">
      <div v-if="isPayComplete || finishedPayRecord" class="mobile-container">
      <div class="complete-logo">
        <div class="png">
          <img class="picture" :src="complete" />
        </div>
      </div>
      <div class="success-font">
        <span class="success">支付成功</span>
      </div>
      <div class="order-container">
        <div class="order-no">
          订单号：{{ payData.orderCode }}
        </div>
        <div class="money">
          <span>¥</span>
          <span class="amount">{{ Number(payData.amount).toFixed(2) || '???' }}</span>
        </div>
      </div>
      <div class="btn-container">
        <!-- <van-button type="primary" class="btn-style" round block @click="mobilePay">完成</van-button> -->
        <div class="btn">
          <van-button plain block @click="gotoCallback()" class="btn-style" type="success">完成</van-button>
        </div>
      </div>
      </div>
      <div v-else>
        <official-payment v-if="payTypes?.includes(1)" :loginUser="loginUser" :pay-data="payData" :pay-types="payTypes" :accounts="virtualAccountsData" @payComplete="setIsPayComplete"/>
        <mobile-pay-con v-else :pay-data="payData" :pay-types="payTypes" :accounts="virtualAccountsData" @setIsPayComplete="setIsPayComplete"/>
      </div>
    </template>
  </div>
</template>

<style scoped lang="less">
.masking {
  height: 100vh;
  width: 100vw;
  background-color: rgba(7, 7, 7, 0.74);
  float: left;
  position: absolute;
  z-index: 100;

  .result-alert {
    padding: auto;
    height: 40vh;
    width: 40vw;
    margin: auto;
    margin-top: 20vh;
    background-color: #ffffff;
    border-radius: 5%;
  }
}

.pay-container {
  overflow: auto;
  overflow-x: hidden;
}

.header {
  height: 5vh;

  .logo-img {
    margin-top: 1.2vh;
    margin-bottom: 1.3vh;
    height: 2.5vh;
  }
}

.banner {
  height: 22vh;

  .pay-title {
    position: absolute;
    left: 25vw;
    top: 7vh;

    .chinese-font {
      font-size: 4vh;
      color: #ffffffd9;
    }

    .english-font {
      font-size: 2vh;
      color: #c9c4c4;
    }
  }

  .banner-img {
    height: 22vh;
    width: 100vw;
    object-fit: fill;
  }

}

.finished-order {
  .finished-order-row {
    line-height: 2.5vh;
  }
}

.order-detail {
  .order-detail-table {
    margin: 0.5vh;
    margin-bottom: 1.5vh;
    border: 0.1vh solid #d5e3ed;
    height: 10.5vh;
    background-color: rgba(178 191 203 / 12%);

    .remind {
      margin-top: 1vh;
      margin-left: 1vw;
      color: rgba(34, 64, 121, 0.829);
      font-size: 2vh;
    }

    .order-code {
      margin-top: 2vh;
      margin-left: 1vw;
      color: rgba(34, 34, 36, 0.897);
      font-size: 1.5vh;
      word-break: break-all
    }

    .amount-layout {
      text-align: right;
      padding-top: 3vh;
      padding-right: 2vw;

      .describe {
        font-size: 1.5vh;
      }

      .amount {
        color: red;
        font-size: 3.5vh;
        font-weight: 700;

        .symbol {
          font-size: 2vh;
        }
      }
    }
  }
}

.pay-parameter {
  padding: 1vh;
  text-align: center;
}

.not-pay-types {
  height: 25vh;
  line-height: 25vh;
  color: rgb(255, 6, 6);
  font-size: 2vh;
}

.mobile-container {
  display: flex;
  width: 100%;
  flex-direction: column;
  background-color: #F5F5F5;
  min-height: 100vh;
  position: relative;

  .complete-logo {
    display: flex;
    width: 100%;
    justify-content: center;
    padding-top: 50px;

    .png {
      display: flex;
      width: 33%;

      .picture {
        width: 100%;
        height: 100%;
      }
    }
  }

  .success-font {
    display: flex;
    justify-content: center;
    padding-top: 8px;

    .success {
      font-size: 24px;
      font-weight: bold;
    }
  }

  .order-container {
    display: flex;
    width: 100%;
    flex-direction: column;
    padding-top: 80px;
    font-size: 20px;
    font-weight: bold;
    align-items: center;

    .order-no {
      display: flex;
      padding: 0 30px;
      word-break: break-all;
    }

    .money {
      display: flex;
      align-items: baseline;

      .amount {
        font-size: 42px;
        padding-left: 5px;
      }
    }
  }

  .price-container {
    display: flex;
    width: 100%;
    padding: 30px 0;
    justify-content: center;
    flex-direction: column;
    align-items: center;

    .price {
      display: flex;
      flex-direction: row;
      font-size: 42px;
      font-weight: bold;
      align-items: baseline;

      .rmb {
        font-size: 28px;
      }
    }

    .order-no {
      display: flex;
      font-size: 14px;
      color: #969799;
    }
  }

  .pay-method {
    display: flex;
    width: 100%;
    flex-direction: column;

    .pay-font {
      display: flex;
      padding: 0 0 10px 16px;
      font-weight: bold;
      color: #646565;
    }


  }

  .btn-container {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 10px 16px;
    display: flex;
    justify-content: center;


    .btn {
      display: flex;
      width: 50%;
      padding-bottom: 80px;

      .btn-style {
        height: 35px;
        //border-radius: 19px;
      }
    }

    .btn-style {
      height: 40px;
      //border-radius: 19px;
    }
  }

}
</style>
