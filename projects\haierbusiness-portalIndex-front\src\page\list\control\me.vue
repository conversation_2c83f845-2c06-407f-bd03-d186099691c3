<script lang="ts" setup>
import {
  Button as hButton, Col as hCol, Form as hForm, FormItem as hFormItem, Input as hInput, RangePicker as hRangePicker, Row as hRow, Select as hSelect,
  SelectOption as hSelectOption, Tag as hTag, Spin as hSpin, Empty as hEmpty, Space as hSpace, Pagination as hPagination, message, Descriptions, DescriptionsItem
} from 'ant-design-vue';
import { IProcessRecordListRequest, ProcessResultStateConstant, IUserListRequest, ProcessTodoStateConstant, IUserInfo } from "@haierbusiness-front/common-libs";
import { computed, onMounted, reactive, ref, watch } from "vue";
import { processApi } from '@haierbusiness-front/apis';
import { usePagination } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from "@haierbusiness-front/utils/src/store/store"
import { storeToRefs } from 'pinia';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue'

const { loginUser } = storeToRefs(applicationStore(globalPinia))

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(processApi.list, {
  defaultParams: [
    {
        approveState: 1,
        type: 1
    }
  ],
  manual: false
})

const dataSource = computed(() => data.value?.records || []);

const labelCol = {
    span:8
}
const wrapperCol = { 
    span: 16
}

const from = ref()

const searchKey = reactive<IProcessRecordListRequest>({
  code: '',
  approveState: 1,
  type: 1,
  pageNum: 1,
  pageSize: 10,
});

const onFilterChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
    listApiRun({
        ...searchKey,
        pageNum: pag.current,
        pageSize: pag.pageSize,
    });
};

const handleReset = () => {
    searchKey.ownerId = ''
    name.value = ''
    from.value && from.value.resetFields()
    onFilterChange({ current: 1, pageSize: 10 })
}

const pagination = computed(() => ({
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
}));

const baseUrl = import.meta.env.VITE_BUSINESS_URL

const handle = (code: string | undefined) => {
    if(!code) {
        message.error('出错了，请联系管理员！')
        return
    }
    const url = baseUrl + `hbweb/process/?code=${code}#/details`
    window.open(url)
}

const todoStateSelect = ProcessTodoStateConstant.toArray()

// 用户选择
const params = ref<IUserListRequest>({
    pageNum: 1,
    pageSize: 20
})

const name = ref('')

const userNameChange = (userInfo: IUserInfo) => {
    searchKey.ownerId = userInfo?.username ?? ''
    name.value = userInfo?.nickName ?? ''
}

const onApproveStateChange = () => {
    onFilterChange({ current: 1, pageSize: 10 })
}

</script>

<template>
    <div class="wyyContainer">
        <h-form ref="from" :model="searchKey" @finish="onFilterChange({ current: 1, pageSize: 10 })" style="width: 100%;" :label-col="labelCol" :wrapper-col="wrapperCol">
            <h-row :gutter="24">
                <h-col :span="8">
                    <h-form-item has-feedback label="审批状态" name="approveState" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
                        <a-radio-group v-model:value="searchKey.approveState" button-style="solid" @change="onApproveStateChange">
                            <template v-for="(item, index) in todoStateSelect" :key="index">
                                <a-radio-button :value="item?.type">{{ item?.name }}</a-radio-button>
                            </template>
                        </a-radio-group>
                    </h-form-item>
                </h-col>
                <h-col :span="8">
                    <h-form-item has-feedback label="单号" name="code">
                        <h-input
                        v-model:value="searchKey.code"
                        placeholder="审批单号/业务单号"
                        allow-clear
                        />
                    </h-form-item>
                </h-col>
                <h-col :span="8">
                    <h-form-item has-feedback label="审批人" name="applicantUser">
                        <user-select :value="name" placeholder="审批人" :params="params" @change="(userInfo: IUserInfo) =>  userNameChange(userInfo)" />
                    </h-form-item>
                </h-col>
            </h-row>
            <h-row justify="center">
                <div class="flexCon">
                <h-space>
                    <h-button @click="handleReset">重置</h-button>
                    <h-button type="primary"  html-type="submit">查询</h-button>
                </h-space>
                </div>
            </h-row>
        </h-form>

        <h-spin :spinning="loading">
            <div class="list" v-if="dataSource && dataSource.length > 0">
                <div
                class="order-container"
                v-for="(item, index) in dataSource"
                :key="index"
                >
                    <div class="order-header">
                        <div class="order-header-left">
                            <span>审批单号：</span>
                            <span>{{ item.recordCode }} </span>
                            <span style="margin-left: 10px;">业务单号： </span>
                            <span>{{ item.businessCode }} </span>
                            <span style="margin-left: 50px;">申请人： </span>
                            <span>{{ item.applicantName }} </span>
                        </div>
                        <div class="order-header-right">
                            <h-tag color="processing" v-if="item.approveState === 1">待审批</h-tag>
                            <h-tag color="success" v-if="item.approveState === 2">已审批</h-tag>
                            <h-tag color="error" v-if="item.approveState === 3">已撤回</h-tag>
                        </div>
                    </div>
                    <div class="order-body">
                        <Descriptions :column="3" size="small">
                            <DescriptionsItem label="标题" :span="3">{{ item.title }}</DescriptionsItem>
                            <DescriptionsItem label="待办状态">{{ ProcessResultStateConstant.ofType(item.resultState)?.name }}</DescriptionsItem>
                            <DescriptionsItem label="审批完成时间">{{ item.completeTime }}</DescriptionsItem>
                            <DescriptionsItem label="审批创建时间">{{ item.gmtCreate }}</DescriptionsItem>
                            <!-- <DescriptionsItem label="审批状态">{{ ProcessTodoStateConstant.ofType(item.approveState)?.name }}</DescriptionsItem> -->
                            <DescriptionsItem label="流程名称" :span="3">{{ item.pdName }}</DescriptionsItem>
                            <DescriptionsItem label="描述" :span="3">{{ item.description }}</DescriptionsItem>
                            <!-- <DescriptionsItem label="处理端">{{ ProcessDealFromConstant.ofType(item.dealFrom)?.name }}</DescriptionsItem> -->
                            <!-- <DescriptionsItem label="发起审批应用">{{ item.applicationCode }}</DescriptionsItem> -->
                        </Descriptions>
                    </div>
                    <div class="order-footer">
                        <h-button v-if="loginUser?.username === item.ownerId && item.approveState === 1" type="primary" size="small" @click="handle(item.recordCode)">去处理</h-button>
                        <h-button v-else type="primary" size="small" @click="handle(item.recordCode)">去查看</h-button>
                    </div>
                </div>
                
            </div>

            <div class="page" v-show="pagination.total && pagination.total > 0">
                <h-pagination
                v-model:current="pagination.current"
                show-size-changer
                show-quick-jumper
                :total="pagination.total"
                @change="(page, pageSize) => onFilterChange({ current:page, pageSize})"
                />
            </div>

            <div class="empty" v-if="!dataSource || dataSource.length === 0">
                <h-empty />
            </div>
        </h-spin>
    </div>

</template>

<style lang="less" scoped>
  .empty {
    margin-top: 50px;
    border: 1px solid #f0f0f0;
    padding: 42px 24px 50px;
  }
  
.container {
    display: flex;
    width: 100%;
    flex-direction: column;

    .search {
        display: flex;
        width: 100%;
    }
}

.list {
    display: flex;
    margin-top: 20px;
    width: 100%;
    flex-direction: column;

    .order-container {
        display: flex;
        flex-direction: column;
        width: 100%;
        margin-bottom: 20px;

        .order-header {
            width: 100%;
            background-color: #f5f5f5;
            height: 43px;
            line-height: 43px;
            padding-left: 20px;
            color: #333;
            font-size: 12px;
            border: 1px solid #eaeaea;
            display: flex;
            justify-content: space-between;

            .order-header-left {
                display: flex;
            }
            .order-header-right {
                display: flex;
                align-items: center;
            }
        }
        .order-body {
            display: flex;
            width: 100%;
            border: 1px solid #eaeaea;
            border-top: 0;
            flex-direction: row;
            padding: 10px 20px;

            .first {
                display: flex;
                flex: 3;
                padding: 24px 10px;

                
            }
            .second {
                display: flex;
                flex: 2;
                border-left: 1px solid #eaeaea;
                padding: 24px 10px;
            }
            .three {
                display: flex;
                flex: 2;
                border-left: 1px solid #eaeaea;
                padding: 24px 10px;
            }
            .last {
                display: flex;
                flex: 3;
                border-left: 1px solid #eaeaea;
                padding: 24px 10px;
            }

            .second,.three {
                .title {
                    width: 80px;
                    text-align: right;
                }

                .value {
                    padding-left: 5px;
                    width: calc(100% - 80px);
                }
            }

            .first,.last {
                .title {
                    width: 100px;
                    text-align: right;
                }

                .value {
                    padding-left: 5px;
                    width: calc(100% - 100px);
                }
            }

            
            
        }
        .order-footer {
            width: 100%;
            background-color: #f5f5f5;
            height: 43px;
            line-height: 43px;
            padding-right: 8px;
            color: #333;
            font-size: 12px;
            border-left: 1px solid #eaeaea;
            border-right: 1px solid #eaeaea;
            border-bottom: 1px solid #eaeaea;
            display: flex;
            flex-direction: row-reverse;
            align-items: center;
        }
    }

}

.wyyContainer {
  display: flex;
  width: 100%;
  flex-direction: column;
  .search {
    display: flex;
    width: 100%;
  }

  .list {
    display: flex;
    margin-top: 20px;
    width: 100%;
    flex-direction: column;
  
    .order-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      margin-bottom: 20px;
  
      .order-header {
        width: 100%;
        background-color: #f5f5f5;
        height: 43px;
        line-height: 43px;
        padding-left: 20px;
        color: #333;
        font-size: 12px;
        border: 1px solid #eaeaea;
        display: flex;
        justify-content: space-between;
  
        .order-header-left {
          display: flex;
        }
        .order-header-right {
          display: flex;
          align-items: center;
        }
      }
      .order-body {
        //   display: flex;
        //   flex-direction: row;
        width: 100%;
        border: 1px solid #eaeaea;
        border-top: 0;
        .img {
          img {
            width: 100px;
            height: 100px;
          }
        }
      }
    }
  }
}

.page {
    display: flex;
    width: 100%;
    flex-direction: row-reverse;
    margin-bottom: 20px;
}
</style>