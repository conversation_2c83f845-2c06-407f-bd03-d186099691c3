<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  Switch as hSwitch,
  CheckboxGroup as hCheckboxGroup,
  Checkbox as hCheckbox,
  Image as hImage,
  message,
} from 'ant-design-vue';
import {
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { computed, ref, watch, onMounted } from 'vue';
import { fileApi, workOrderApi } from '@haierbusiness-front/apis';
import type { Ref } from 'vue';
import { workOrderQuery } from '@haierbusiness-front/common-libs';
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil';
import { HeaderConstant } from '@haierbusiness-front/common-libs';
import type { UploadChangeParam, UploadFile } from 'ant-design-vue';
const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false);
import workOrderDetail from './workOrderDetail.vue';

interface Props {
  show: boolean;
  data: workOrderQuery;
  knowCenterOptions: any;
  id: number;
  callHistoryInfo: any;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const from = ref();
const confirmLoading = ref(false);
const loading = ref<boolean>(false);
const defaultData: workOrderQuery = {
  mobile: '',
  workName: '',
  userName: '',
  userNum: '',
  personName: '',
  personNum: '',
  businessNum: '',
};
const workOrderList = ref<any>([]);
const editDetail = ref<any>({});

const indexData: Ref<workOrderQuery> = ref(props.data ? props.data : defaultData);

watch(props, (newValue) => {
  indexData.value = ({ ...newValue.data } as workOrderQuery) || defaultData;
});

const emit = defineEmits(['cancel', 'ok']);

const visible = computed(() => props.show);
const detailVisible = ref<boolean>(false);

const handleOk = () => {
  from.value
    .validate()
    .then(() => {
      confirmLoading.value = true;
      emit('ok', indexData.value, () => {
        confirmLoading.value = false;
      });
      confirmLoading.value = false;
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};
const cancel = () => {
  detailVisible.value = false;
};

const getWorkOrderList = () => [
  (loading.value = true),
  // 根据id 获取详情
  workOrderApi
    .list({ orderStatus: 10, pageSize: 9999 })
    .then((res: any) => {
      workOrderList.value = res.records;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    }),
];

// 点击关联
const toCorrelation = (row: any) => {
  editDetail.value = row;
  detailVisible.value = true;
};

onMounted(() => {
  getWorkOrderList();
});
</script>

<template>
  <!-- 工单明细弹窗 -->
  <h-modal
    v-model:visible="visible"
    title="未闭环工单列表"
    :width="800"
    @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    :footer="null"
  >
    <a-list style="height:480px;overflow:auto;" :loading="loading" size="small" v-if="workOrderList" :data-source="workOrderList">
      <template #renderItem="{ item,index }">
        <a-list-item v-if="index==0" class="header">
          <div class="listItem">工单编号</div>
          <div class="listItem">工单标题</div>
          <div class="listItem">创建人</div>
          <div class="listItem">创建时间</div>
          <div class="listItem1">操作</div>
        </a-list-item>
        <a-list-item>
          <div class="listItem">{{ item.workTableNum }}</div>
          <div class="listItem">{{ item.workName }}</div>
          <div style="padding-left:20px;" class="listItem">{{ item.createName }}</div>
          <div class="listItem">{{ item.gmtCreate }}</div>
          <template #actions>
            <a style="margin-right:15px;" @click="toCorrelation(item)" key="list-loadmore-edit">关联</a>
            <!-- <a key="list-loadmore-more">more</a> -->
          </template>
        </a-list-item>
      </template>
    </a-list>
  </h-modal>
  <workOrderDetail
    @cancel="cancel"
    v-if="detailVisible"
    :show="detailVisible"
    :callHistoryInfo="props.callHistoryInfo"
    :id="editDetail.id"
  ></workOrderDetail>
</template>

<style lang="less" scoped>
.important {
  color: red;
}
.imgItem {
  position: relative;
  display: inline-block;
  margin-right: 4px;
  .deleteIcon {
    position: absolute;
    right: 0px;
    z-index: 9999;
  }
}
.ant-form-item {
  margin-bottom: 0;
}
.Detail {
  margin-left: 55px;
  margin-top: 15px;
  span {
    margin-right: 10px;
  }
  .title {
    font-weight: 600;
    font-size: 15px;
  }
}
  .listItem{
    width:180px;
  }
  .header{
    .listItem{
      margin-right: 10px;
      font-weight: bold;
    }
    .listItem1{
      width:60px;
      font-weight: bold;
    }
  }
  .flex{
    display: flex;
    justify-content:space-around;
    .listItem{
      // width:120px;
      // text-align: center;
    }
  }
</style>
