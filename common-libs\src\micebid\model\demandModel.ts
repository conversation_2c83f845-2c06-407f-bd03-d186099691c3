import { IPageRequest } from '../../basic';
import { Dayjs } from 'dayjs';

// 需求提报
export interface DemandSubmitObj {
  /*上一版本id,当初次调用接口时为null,当后续变更时,为前一版本的id */
  sourceId?: number;

  /*主表id */
  miceId?: number;

  /*会议名称 */
  miceName?: string;

  /*会议类型 发布会等enum:[[{"code":1,"desc":"开盘会"},{"code":2,"desc":"客户会"},{"code":3,"desc":"培训会"},{"code":4,"desc":"发布会"},{"code":5,"desc":"展览会"},{"code":6,"desc":"内部沟通会"},{"code":7,"desc":"招商会"}]],可用值:1,2,3,4,5,6,7 */
  miceType?: number;

  /*需求开始时间 */
  startDate?: Dayjs | null;

  /*需求结束时间 */
  endDate?: Dayjs | null;

  /*会议对接人工号 */
  contactUserCode?: string;

  /*会议对接人姓名 */
  contactUserName?: string;

  /*会议对接人手机号 */
  contactUserPhone?: string;

  /*会议对接人邮箱 */
  contactUserEmail?: string;

  /*总人数 */
  personTotal?: number;

  /*会议地点enum:[[{"code":0,"desc":"国内"},{"code":1,"desc":"国际"}]],可用值:0,1 */
  districtType?: number;

  /*测算总金额 ,传入后和后端计算金额做校验 */
  calcTotalPrice?: number;

  /*需求总金额 */
  demandTotalPrice?: number;

  /*是否加急 */
  isUrgent?: boolean;

  /*需求备注 */
  remarks?: string;

  /*需求酒店信息 */
  hotels?: Array<HotelsArr>;

  /*需求住宿信息 */
  stays?: Array<StaysArr>;

  /*需求会场信息 */
  places?: Array<PlacesArr>;

  /*需求用餐信息 */
  caterings?: Array<CateringsArr>;

  /*需求用车信息 */
  vehicles?: Array<VehiclesArr>;

  /*需求服务人员信息 */
  attendants?: Array<AttendantsArr>;

  /*需求拓展活动信息 */
  activities?: Array<ActivitiesArr>;

  /*需求保险信息 */
  insurances?: Array<InsurancesArr>;

  /*需求布展物料信息 */
  material?: Array<MaterialsArr>;

  /*需求交通信息 */
  traffic?: Array<TrafficsArr>;

  /*需求礼品信息 */
  presents?: Array<PresentsArr>;

  /*需求其他信息 */
  others?: Array<OthersArr>;
}

// 需求酒店信息
export interface HotelsArr {
  /*临时需求酒店id,用来构建关联关系 */
  tempDemandHotelId: number;
  sourceId?: number;

  /*酒店所在城市id */
  cityId: number;

  /*酒店所在城市名称 */
  cityName: string;

  /*酒店所在区域id,支持多区域,逗号分割 */
  districtIds: string;

  /*酒店所在区域名称,支持多区域,逗号分割 */
  districtNames: string;

  /*酒店等级:bitmapenum:[[{"code":1,"desc":"3星级"},{"code":2,"desc":"4星级"},{"code":4,"desc":"5星级"}]],可用值:1,2,4 */
  level: number;

  /*需求中心点经度 */
  latitude?: string;

  /*需求中心点纬度 */
  longitude?: string;

  /*需求范围:单位米(可选) */
  distanceRange?: number;

  /*需求中心的地标名称 */
  centerMarker?: string;

  // 前端自定义参数
  provinceName?: string;
  provinceId?: string;
  // cityList?: Array<>;
  // cityOptions?: Array<>;
  errTipShow?: boolean;
}

// 需求住宿信息
export interface StaysArr {
  /*临时需求酒店id,用来构建关联关系 */
  tempDemandHotelId: number;

  /*需求日期 */
  demandDate: string;

  /*房型enum:[[{"code":1,"desc":"大床房"},{"code":2,"desc":"双床房"},{"code":3,"desc":"套房"}]],可用值:1,2,3 */
  roomType: number;

  /*早餐类型enum:[[{"code":0,"desc":"无早"},{"code":1,"desc":"含早"}]],可用值:0,1 */
  breakfastType: number;

  /*人数 */
  personNum: number;

  /*入住房间数 */
  roomNum: number;

  /*人数与房间数不一致原因 */
  discrepancyReason: string;

  /*自动测算单价 */
  calcUnitPrice: number;
}

// 需求会场信息
export interface PlacesArr {
  /*临时需求酒店id,用来构建关联关系 */
  tempDemandHotelId: number;

  /*需求日期 */
  demandDate: string;

  /*使用时间 上午/下午/晚间 bitmapenum:[[{"code":1,"desc":"上午"},{"code":2,"desc":"下午"},{"code":4,"desc":"晚间"}]],可用值:1,2,4 */
  usageTime: number;

  /*使用用途 会议举行/布展搭建/会议撤场 bitmapenum:[[{"code":1,"desc":"会议举行"},{"code":2,"desc":"布展搭建"},{"code":4,"desc":"会议撤场"}]],可用值:1,2,4 */
  usagePurpose: number;

  /*人数 */
  personNum: number;

  /*面积 */
  area?: number;

  /*灯下层高 */
  underLightFloor?: number;

  /*摆台形式enum:[[{"code":1,"desc":"U型式"},{"code":2,"desc":"董事会式"},{"code":3,"desc":"剧院式"},{"code":4,"desc":"海岛式"},{"code":5,"desc":"酒会式"},{"code":6,"desc":"课桌式"},{"code":7,"desc":"鱼骨式"}]],可用值:1,2,3,4,5,6,7 */
  tableType: number;

  /*是否需要led */
  hasLed?: boolean;

  /*led数量 */
  ledNum?: number;

  /*led规格说明 */
  ledSpecs?: string;

  /*是否需要茶歇 */
  hasTea?: boolean;

  /*茶歇标准/每人 */
  teaEachTotalPrice?: number;

  /*茶歇说明 */
  teaDesc?: string;

  /*自动测算会场单价 */
  calcUnitPlacePrice: number;

  /*自动测算led单价 */
  calcUnitLedPrice?: number;

  /*自动测算茶歇单价 */
  calcUnitTeaPrice?: number;
}

// 需求用餐信息
export interface CateringsArr {
  /*临时需求酒店id,用来构建关联关系 */
  tempDemandHotelId: number;

  /*是否酒店提供用餐 */
  isInsideHotel: boolean;

  /*需求日期 */
  demandDate: string;

  /*用餐类型enum:[[{"code":0,"desc":"桌餐"},{"code":1,"desc":"自助"},{"code":2,"desc":"盒饭"}]],可用值:0,1,2 */
  cateringType: number;

  /*用餐时间 午餐/晚餐enum:[[{"code":0,"desc":"午餐"},{"code":1,"desc":"晚餐"}]],可用值:0,1 */
  cateringTime: number;

  /*人数 */
  personNum: number;

  /*用餐标准 */
  demandUnitPrice: number;

  /*是否包含酒水 */
  isIncludeDrinks: boolean;

  /*自动测算单价 */
  calcUnitPrice: number;
}

// 需求用车信息
export interface VehiclesArr {
  /*需求日期 */
  demandDate: string;

  /*使用方式 单趟/包车enum:[[{"code":0,"desc":"单趟"},{"code":1,"desc":"包车"}]],可用值:0,1 */
  usageType: number;

  /*使用时长 半天/全天enum:[[{"code":0,"desc":"半天"},{"code":1,"desc":"全天"}]],可用值:0,1 */
  usageTime: number;

  /*座位数 */
  seats: number;

  /*车辆数量 */
  vehicleNum: number;

  /*品牌 */
  brand?: string;

  /*路线,多程逗号分隔 */
  route: string;

  /*自动测算单价 */
  calcUnitPrice: number;
}

// 需求服务人员信息
export interface AttendantsArr {
  /*需求日期 */
  demandDate: string;

  /*人员类型enum:[[{"code":0,"desc":"摄影师"},{"code":1,"desc":"导游"},{"code":2,"desc":"翻译"},{"code":3,"desc":"主持人"},{"code":4,"desc":"服务人员"},{"code":5,"desc":"其他"},{"code":6,"desc":"会务平台服务人员"}]],可用值:0,1,2,3,4,5,6 */
  type: number;

  /*人数 */
  personNum: number;

  /*工作范围 */
  duty?: string;

  /*自动测算单价 */
  calcUnitPrice: number;
}

// 需求拓展活动信息
export interface ActivitiesArr {
  /*需求日期 */
  demandDate: string;

  /*费用标准 */
  demandUnitPrice: number;

  /*人数 */
  personNum: number;

  /*活动说明 */
  description?: string;

  /*自动测算单价 */
  calcUnitPrice: number;

  /*附件路径 */
  path?: string;
}

// 需求保险信息
export interface InsurancesArr {
  /*需求日期 */
  demandDate: string;

  /*需求单价 */
  demandUnitPrice: number;

  /*参保人数 */
  personNum: number;

  /*保险产品id(以互动时为准,需求时只为意向) */
  productId: number;

  /*产品所属商户id */
  productMerchantId: number;

  /*险种名称 */
  insuranceName: string;

  /*险种条目 */
  insuranceContent: string;

  /*自动测算单价 */
  calcUnitPrice: number;
}

// 需求布展物料信息
export interface MaterialsArr {
  /*费用标准/总 */
  demandTotalPrice: number;

  /*自动测算总价 */
  calcTotalPrice: number;

  /*需求布展物料明细 */
  materialDetails: Array<materialDetailsArr>;
}
// 需求布展物料明细
export interface materialDetailsArr {
  /*需求布展物料表id */
  miceDemandMaterialId: number;

  /*物料类型 枚举enum:[[{"code":0,"desc":"条幅"},{"code":1,"desc":"投影仪"},{"code":2,"desc":"音响"},{"code":3,"desc":"麦克"},{"code":4,"desc":"灯光"}]],可用值:0,1,2,3,4 */
  type: number;

  /*规格说明 */
  specs?: string;

  /*数量 */
  num: number;

  /*单价 */
  unitPrice: number;

  /*测算单价 */
  calcUnitPrice: number;
}

// 需求交通信息
export interface TrafficsArr {
  /*需求总金额 */
  demandTotalPrice: number;

  /*自动测算总价 */
  calcTotalPrice: number;

  /*需求交通测算信息 */
  trafficCalcs: Array<trafficCalcsArr>;
}
// 需求交通测算信息
export interface trafficCalcsArr {
  /*需求交通id */
  miceDemandTrafficId: number;

  /*总金额 */
  totalPrice: number;

  /*测算时间 */
  calcDate: string;

  /*自动测算总价 */
  calcTotalPrice: string;

  /*需求交通测算明细信息 */
  trafficCalcDetails: Array<trafficCalcDetailsArr>;
}
// 需求交通测算明细信息
export interface trafficCalcDetailsArr {
  /*需求交通id */
  miceDemandTrafficId: number;

  /*需求测算id */
  miceDemandTrafficCalcId: number;

  /*交通类型 飞机/火车enum:[[{"code":0,"desc":"火车"},{"code":1,"desc":"机票"}]],可用值:0,1 */
  type: number;

  /*航班号/车次号 */
  numberCode: string;

  /*出发地城市id */
  departureCityId: number;

  /*出发地城市名称 */
  departureCityName: string;

  /*出发地机场/车站代码 */
  departureSiteCode: string;

  /*出发地机场/车站代码名称 */
  departureSiteName: string;

  /*出发时间 */
  departureDate: string;

  /*到达地城市id */
  arrivalCityId: number;

  /*到达地城市名称 */
  arrivalCityName: string;

  /*到达地机场/车站代码 */
  arrivalSiteCode: string;

  /*到达地机场/车站代码名称 */
  arrivalSiteName: string;

  /*到达时间 */
  arrivalDate: string;

  /*行程数量 */
  num: number;

  /*行程单价 */
  unitPrice: number;

  /*总费用 */
  totalPrice: number;
}

// 需求礼品信息
export interface PresentsArr {
  /*送达日期 */
  deliveryDate: string;

  /*费用标准 */
  demandTotalPrice: number;

  /*礼品数量 */
  personNum: number | null;

  /*单位 */
  unit: string;

  /*礼品说明 */
  personSpecs?: string;

  /*礼品产品id(以互动时为准,需求时只为意向) */
  productId: number;

  /*产品所属商户id */
  productMerchantId: number;

  /*选择类型 0:手动 1:自动 */
  optionType?: number;

  /*产品名称,当未选择产品时可自由修改 */
  productName?: string;

  /*自动测算单价 */
  calcUnitPrice?: number;


  /*单价 */
  unitPrice?: number | null;

  calcTotalPrice?: number;

  selectedGiftPrice?: number;

  
}

// 需求其他信息
export interface OthersArr {
  /*需求日期 */
  demandDate: string;

  /*数量 */
  num: number;

  /*单位 */
  unit: string;

  /*规格描述 */
  specs?: string;

  /*费用标准 */
  demandTotalPrice: number;

  /*自动测算总价 */
  calcTotalPrice?: number;
}

// 地图选点
export class aMapBack {
  positionStr?: string;
  addressValue?: string;
  aMapShow?: boolean;
}
//
//
//
export class DiscountFilter extends IPageRequest {
  merName?: string;
  merCode?: string;
  merType?: string;
  isEnable?: number;
  startTime?: string;
  endTime?: string;
}

export class Discount {
  id?: number | null;
  createBy?: string;
  createName?: string;
  lastModifiedBy?: string;
  lastModifiedName?: string;
  gmtCreate?: string;
  gmtModified?: string;
  merName?: string;
  merCode?: string;
  icon?: string;
  merType?: number;
  discountDesc?: string;
  details?: string;
  isEnable?: number;
  discountValue?: string;
  isDelete?: number;
  startTime?: string;
  endTime?: string;
  userList?: Array<ChargeUser>;
}

export class ChargeUser {
  name?: string;
  code?: string;
  email?: string;
  phone?: string;
}

export interface DemandSubConfirm {
  miceId?: string;
  demandRejectReason?: string;
}
