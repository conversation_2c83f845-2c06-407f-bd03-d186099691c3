import { downloadPost, get, post } from '../request'
import { 
    IApplyFilter, 
    IApply,
    IPageResponse, 
    Result } from '@haierbusiness-front/common-libs'


export const banquetNewsApi = {
    // 公告可见状态修改
    updateNotification: (params): Promise<IApply> => {
        return get('banquet/api/admin/notification/update', params)
    },
    saveNotification: (params: IApplyFilter): Promise<IApply> => {
        return post('banquet/api/admin/notification/saveNotification', params)
    },
    list: (params: IApplyFilter): Promise<IPageResponse<IApply>> => {
        return post('banquet/api/admin/notification/page', params)
    },
    
}

// 温馨提示
export const promiseKnowApi = {
    // 修改承诺
    saveOrUpdateOther: (params: any): Promise<IApply> => {
        return post('banquet/api/admin/notification/saveOrUpdateOther', params)
    },
    // 获取承诺须知 /admin/notification/getPromise
    getPromise: (): Promise<IApply> => {
        return get('banquet/api/admin/notification/getPromise')
    },
}

// 温馨提示
export const tipsApi = {
    // 修改承诺
    saveOrUpdateOther: (params: any): Promise<IApply> => {
        return post('banquet/api/admin/notification/saveOrUpdateOther', params)
    },
    // 获取温馨提示 /admin/notification/getPromise
    getTips: (): Promise<IApply> => {
        return get('banquet/api/admin/notification/getTips')
    },
}

// 隐私承诺
export const pivacyApi = {
    // 修改承诺
    saveOrUpdateOther: (params: any): Promise<IApply> => {
        return post('banquet/api/admin/notification/saveOrUpdateOther', params)
    },
    // 获取温馨提示 /admin/notification/getPromise
    getPrivacy: (): Promise<IApply> => {
        return get('banquet/api/admin/notification/getPrivacy')
    },
    // 获取温馨提示 /admin/notification/getPromise
    getBannerList: (): Promise<any> => {
        return get('banquet/api//admin/notification/getHomepagePic')
    },
}

// 首页图片
export const imgApi = {
    // 修改图片
    saveOrUpdateOther: (params: any): Promise<IApply> => {
        return post('banquet/api/admin/notification/saveOrUpdateOther', params)
    },
    saveOrUpdatePic: (params: any): Promise<IApply> => {
        return post('banquet/api/admin/notification/saveOrUpdatePic', params)
    },
    getHomepagePic: (): Promise<IApply> => {
        return get('banquet/api/admin/notification/getHomepagePic')
    },
}



// 账户维护 
export const banquetBalanceApi = {
    // 修改图片
    updateAccount: (params: any): Promise<IApply> => {
        return post('banquet/api/banquetBalance/updateAccount', params)
    },
    // 获取温馨提示 /admin/notification/getPromise
    getbanquetBalanceInfo: (): Promise<IApply> => {
        return post('banquet/api/banquetBalance/info')
    },
}
