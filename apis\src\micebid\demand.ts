import { get, post, originalPost, originalGet } from '../request';
import {
  DemandSubmitObj,
  DemandSubConfirm,
  Result,
  DemandCalcStayObj,
  DemandCalcPlaceObj,
  DemandCalcCateringObj,
  DemandCalcVehicleObj,
  DemandCalcAttendantObj,
  DemandCalcActivityObj,
  DemandCalcInsuranceObj,
  DemandCalcMaterialObj,
  DemandCalcTrafficObj,
  DemandCalcPresentObj,
  DemandCalcOtherObj,
  IPageResponse,
} from '@haierbusiness-front/common-libs';

// 需求提报
export const demandApi = {
  // 需求提报-提交
  demandUserSubmit: (params: DemandSubmitObj, errorNotify?: ((error: any) => any) | null): Promise<Result> => {
    return originalPost('/mice-bid/api/mice/demand/user/submit', params, undefined, errorNotify);
  },

  // 需求互动提报-提交
  demandPlatformSubmit: (params: DemandSubmitObj, errorNotify?: ((error: any) => any) | null): Promise<Result> => {
    return originalPost('/mice-bid/api/mice/demand/platform/submit', params, undefined, errorNotify);
  },

  // 查询需求订单详情
  demandUserDetails: (params: DemandSubmitObj, errorNotify?: ((error: any) => any) | null): Promise<Result> => {
    return originalGet('/mice-bid/api/mice/demand/user/details', params, undefined, errorNotify) as Promise<Result>;
  },

  // 需求确认
  demandUserConfirm: (params: DemandSubConfirm, errorNotify?: ((error: any) => any) | null): Promise<Result> => {
    return originalPost('/mice-bid/api/mice/demand/user/confirm', params, undefined, errorNotify);
  },

  // 需求驳回
  demandUserReject: (params: DemandSubConfirm, errorNotify?: ((error: any) => any) | null): Promise<Result> => {
    return originalPost('/mice-bid/api/mice/demand/user/reject', params, undefined, errorNotify);
  },

  // 住宿需求价格测算
  priceCalcStay: (params: DemandCalcStayObj): Promise<Result> => {
    return get('/mice-bid/api/mice/demand/price/calc/stay', params);
  },

  // 会场需求价格测算
  priceCalcPlace: (params: DemandCalcPlaceObj): Promise<Result> => {
    return get('/mice-bid/api/mice/demand/price/calc/place', params);
  },

  // 用餐需求价格测算
  priceCalcCatering: (params: DemandCalcCateringObj): Promise<Result> => {
    return get('/mice-bid/api/mice/demand/price/calc/catering', params);
  },

  // 用车需求价格测算
  priceCalcVehicle: (params: DemandCalcVehicleObj): Promise<Result> => {
    return get('/mice-bid/api/mice/demand/price/calc/vehicle', params);
  },

  // 服务人员需求价格测算
  priceCalcAttendant: (params: DemandCalcAttendantObj): Promise<Result> => {
    return get('/mice-bid/api/mice/demand/price/calc/attendant', params);
  },

  // 拓展活动需求价格测算
  priceCalcActivity: (params: DemandCalcActivityObj): Promise<Result> => {
    return get('/mice-bid/api/mice/demand/price/calc/activity', params);
  },

  // 保险需求价格测算
  priceCalcInsurance: (params: DemandCalcInsuranceObj): Promise<Result> => {
    return get('/mice-bid/api/mice/demand/price/calc/insurance', params);
  },

  // 布展物料需求价格测算
  priceCalcMaterial: (params: DemandCalcMaterialObj): Promise<Result> => {
    return post('/mice-bid/api/mice/demand/price/calc/material', params);
  },

  // 交通需求价格测算
  priceCalcTraffic: (params: DemandCalcTrafficObj): Promise<Result> => {
    return get('/mice-bid/api/mice/demand/price/calc/traffic', params);
  },

  // 礼品需求价格测算
  priceCalcPresent: (params: DemandCalcPresentObj): Promise<Result> => {
    return get('/mice-bid/api/mice/demand/price/calc/present', params);
  },

  // 其他需求价格测算
  priceCalcOther: (params: DemandCalcOtherObj): Promise<Result> => {
    return get('/mice-bid/api/mice/demand/price/calc/other', params);
  },

  // 保险产品首页列表
  insuranceProductList: (params: DemandCalcInsuranceObj): Promise<IPageResponse<DemandCalcInsuranceObj>> => {
    return get('/mice-bid/api/mice/merchant/product/insurance/home-page', params);
  },
};
