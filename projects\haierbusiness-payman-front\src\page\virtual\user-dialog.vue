<script lang="ts" setup>
import { Menu as hMenu, MenuItem as hMenuItem, Modal as hModal, Row as hRow, Col as hCol, Input as hInput, Table as hTable, Button as hButton, message } from 'ant-design-vue';
import { computed, ref, watch, onMounted } from "vue";
import type { Ref } from "vue";
import {
    IPaymentVirtualAccount, IApplicationInfo
} from '@haierbusiness-front/common-libs';
import { useRequest, usePagination } from 'vue-request';
import { virtualPayApi, applicationApi, userApi } from '@haierbusiness-front/apis';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { ColumnType } from 'ant-design-vue/lib/table';
import dayjs from 'dayjs';

interface Props {
    show: boolean
    accountNo: string
    enterpriseCode: string
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  accountNo: '',
  enterpriseCode: ''
});

const from = ref();
const confirmLoading = ref(false);

const emit = defineEmits(["cancel", "ok"]);

const visible = computed(() => props.show);

onMounted(async () => {
    const it = await applicationApi.list({})
    if (it && it.records && it.records.length > 0) {
        applicationList.value = it.records;
    }
    const data = await virtualPayApi.accountTypes(props.accountNo)
    if (data  && data.length > 0) {
        currentApplicationList.value = data.map(item => {
            return item.applicationCode!
        });
    }

    const apps = applicationList.value?.filter(o => currentApplicationList.value.includes(o.applicationCode!))
    if (!apps || apps.length === 0)
        return []

    onSelectApplication({ key: apps![0].applicationCode })
    selectedApplicationKeys.value = [apps![0].applicationCode]

    list.value = apps

    await userApiRun({ accountNo: props.accountNo, applicationCode:  selectedApplicationKeys.value, pageNum: 1, pageSize: 10 })

    await enterpriseUserApiRun({ enterpriseCode: props.enterpriseCode, pageNum: 1, pageSize: 10 })
})

// 选择应用
const onSelectApplication = (obj: any) => {
  if (applicationList.value) {
    for (let i of applicationList.value) {
      if (obj.key === i.applicationCode) {
        selectApplicationCode.value = i.applicationCode
        userApiRun({ accountNo: props.accountNo, applicationCode:  i.applicationCode, needPage: true })
      }
    }
  }
};

// 左侧
const applicationList = ref<IApplicationInfo[] | undefined>([])
const currentApplicationList = ref<string[]>([])
const selectedApplicationKeys = ref()
const selectApplicationCode = ref()

const list = ref<IApplicationInfo[] | undefined>([])

// 右上  应用下的人
const {
    data: userDataSource,
    run: userApiRun,
    loading,
    current: userListCurrent,
    pageSize: userListPageSize,
} = usePagination(virtualPayApi.authorizedAppUserList)

const userPagination = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: userDataSource.value?.total,
    current: userDataSource.value?.pageNum,
    pageSize: userDataSource.value?.pageSize,
    style: { justifyContent: 'center' },
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
    userApiRun({
    // ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
}

// 应用授权用户
const userColumns: ColumnType[] = [
    {
        title: '账号',
        dataIndex: 'username',
    },
    {
        title: '姓名',
        dataIndex: 'nickName',
    },
    {
        title: '性别',
        dataIndex: 'gender',
    },
    {
        title: '电话',
        dataIndex: 'phone',
    },
    {
        title: '邮箱',
        dataIndex: 'email',
    },
    {
        title: '操作',
        dataIndex: 'operator',
        width: '10%',
        align: 'center'
    }
]

// 用户
const columns: ColumnType[] = [
    {
        title: '账号',
        dataIndex: 'username',
    },
    {
        title: '姓名',
        dataIndex: 'nickName',
    },
    {
        title: '性别',
        dataIndex: 'gender',
    },
    {
        title: '电话',
        dataIndex: 'phone',
    },
    {
        title: '邮箱',
        dataIndex: 'email',
    },
    {
        title: '操作',
        dataIndex: 'operator',
        width: '10%',
        align: 'center'
    }
]

// 右下  企业下的人
const {
  data,
  run: enterpriseUserApiRun,
  loading: userLoading,
  current,
  pageSize,
} = usePagination(userApi.list)

const handleEnterpriseTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
    enterpriseUserApiRun({
    // ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
}

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const addUser = (id: string) => {
    virtualPayApi.configUserByApp({
        accountNo: props.accountNo,
        applicationCode: selectedApplicationKeys.value![0],
        userCodeList: [id]
    }).then(() => {
        message.success('添加成功！')
        userApiRun({ accountNo: props.accountNo, applicationCode:  selectedApplicationKeys.value, pageNum: 1, pageSize: 10 })
    })
}

</script>

<template>
    <h-modal
      v-model:visible="visible"
      title="授权应用"
      :width="1200"
      @cancel="$emit('cancel')"
      :confirmLoading="confirmLoading"
      @ok="$emit('ok')"
    >
        <h-row style="height:600px">
            <h-col :span="3" style="height:100%">
                <div style="height:100% ">
                    <div style="height:40px;border-right: 10px solid #f0f2f5;padding: 5px 20px 10px  20px  ;font-size: 18px;font-weight: 600;border-bottom: 1px solid #f0f0f0;">
                        应用
                    </div>
                    <div style="height:calc(100% - 40px);border-right: 10px solid #f0f2f5; overflow-y: auto;">
                        <h-menu v-if="list" v-model:selectedKeys="selectedApplicationKeys" @click="onSelectApplication"
                        style="height:100%;width: 99%;border: none;overflow-x: hidden;overflow-y: auto;" mode="inline">
                            <h-menu-item :key="i.applicationCode" v-for="i of list">
                                {{ i.applicationName }}
                            </h-menu-item>
                        </h-menu>
                    </div>
                </div>
            </h-col>
            <h-col :span="21" style="height:100%">
                <h-row style="margin-bottom: 10px;height: calc(50% - 10px);width: 100%;">
                    <h-col :span="24" style="height:100%;">
                        <div style="height:40px;padding: 5px 20px 10px  20px  ;font-size: 18px;font-weight: 600;border-bottom: 1px solid #f0f0f0;background-color: #ffff;">
                            <h-row>
                                应用授权用户
                            </h-row>
                        </div>
                        <div style="height:calc(100% - 40px); overflow-y: auto;padding: 5px 20px 10px  20px  ;background-color: #ffff;">
                            <h-table :columns="userColumns" :row-key="(record: IPaymentVirtualAccount) => record.id?.toString()" size="small" 
                                :data-source="userDataSource?.records" :pagination="userPagination" :loading="loading"
                                @change="handleTableChange($event as any)">
                                <template #bodyCell="{ column, record }">
                                    <template v-if="column.dataIndex === 'enterprise' && record.enterpriseCode">
                                        {{ record.enterpriseName }}({{ record.enterpriseCode }})
                                    </template>
                                    <template v-if="column.dataIndex === 'state'">
                                        {{ record._stateName }}
                                    </template>
                                    <template v-if="column.dataIndex === 'operator'">
                                        <div style="display: flex; width: 100%; justify-content: center;">
                                            <h-button type="primary" shape="circle" style="float: right;" danger>
                                                <template #icon>
                                                    <DeleteOutlined />
                                                </template>
                                            </h-button>
                                        </div>
                                    </template>
                                </template>
                            </h-table>
                        </div>
                    </h-col>
                </h-row>
                <h-row style="margin-bottom: 10px;height: calc(50% - 10px);width: 100%;">
                    <h-col :span="24" style="height:100%;">
                        <div style="height:40px;padding: 5px 20px 10px  20px  ;font-size: 18px;font-weight: 600;border-bottom: 1px solid #f0f0f0;background-color: #ffff;">
                            <h-row>
                                企业用户
                            </h-row>
                        </div>
                        <div style="height:calc(100% - 40px); overflow-y: auto;padding: 5px 20px 10px  20px  ;background-color: #ffff;">
                            <h-table :columns="columns" :row-key="(record: IPaymentVirtualAccount) => record.id?.toString()" size="small" 
                                :data-source="dataSource" :pagination="pagination" :loading="userLoading"
                                @change="handleEnterpriseTableChange($event as any)">
                                <template #bodyCell="{ column, record }">
                                    <template v-if="column.dataIndex === 'enterprise' && record.enterpriseCode">
                                        {{ record.enterpriseName }}({{ record.enterpriseCode }})
                                    </template> 
                                    <template v-if="column.dataIndex === 'state'">
                                        {{ record._stateName }}
                                    </template>
                                    <template v-if="column.dataIndex === 'operator'">
                                        <div style="display: flex; width: 100%; justify-content: center;">
                                            <h-button type="primary" shape="circle" style="float: right;"  @click="addUser(record.username)"> 
                                                <template #icon>
                                                    <PlusOutlined />
                                                </template>
                                            </h-button>
                                        </div>
                                    </template>
                                </template>
                            </h-table>
                        </div>
                    </h-col>
                </h-row>
            </h-col>
            
        
        </h-row>
    </h-modal>
</template>


<style lang="less" scoped>
.important {
    color: red;
}
</style>
  