<script lang="ts" setup>
import {
  Modal as hModal, Form as hForm, FormItem as hFormItem,
  Input as hInput, Textarea as hTextarea,
  message,InputNumber as hInputNumber,
} from 'ant-design-vue';
import { computed, nextTick, onMounted, ref, watch } from "vue";
import type { Ref } from "vue";
import {
  INoticeContractExpiration,
  INoticeContractExpirationFilter
} from '@haierbusiness-front/common-libs';
import UserSelectPerson from '@haierbusiness-front/components/user/UserSelectPerson.vue';
import { tagApi } from '@haierbusiness-front/apis';
import { useRequest } from 'vue-request';
import { on } from 'node:events';

interface Props {
  show: boolean;
  data: INoticeContractExpiration | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});
// 存储选中的管理员和顾问
const selectedManagers = ref<any[]>([]);
const selectedManagerUsernames = ref<any[]>([]);

// 存储原始的管理员和顾问ID
const originalManagerIds = ref<number[]>([]);

// 存储需要删除的用户ID
const userDelIdList = ref<number[]>([]);
const from = ref();
const confirmLoading = ref(false);
const childRef = ref()
// 设置管理员最大展示数量
const maxManagerCount = ref<number>(3);

const defaultData: INoticeContractExpiration = {
  //业务类型
  businessType: undefined,
  //通知人信息
  sceneContractNotifierDTOList: {
    nickName: '',
    email: '',
    userName: '',
    id:'',
  },
  //合同过期通知前置时间天
  expiredNoticeDay: '',
};

const rules = {
  businessType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
  expiredNoticeDay: [
    { required: true, message: '请输入提前通知天数', trigger: 'blur' },
  ],
};

const noticeContractExpiration = ref<INoticeContractExpiration>({
  ...(props.data ? props.data : defaultData)
});


let emailList = ref<string[]>([]);

watch(
  () => props.data,
  (newVal) => {
    console.log(newVal, "newVal");
    const notifierlist: any = ref([])
    if (newVal) {
      notifierlist.value = newVal.sceneContractNotifierDTOList
    }
    console.log(notifierlist.value, 'notifierlist');
    const transformedList = notifierlist.value.map((item: { userName: string; nickName: string; email: string; id: number | string; }) => ({
      username: item.userName,
      nickName: item.nickName,
      email: item.email,
      id: item.id || null
    }));
    console.log(transformedList, "transformedList");

    selectedManagerUsernames.value = transformedList.map((manager: { nickName: string; }) => manager.nickName || '')
    selectedManagers.value = transformedList
    console.log(selectedManagers.value, selectedManagerUsernames.value, "selectedManagers.value");

    nextTick(() => {
      childRef.value?.setFirstData(selectedManagers.value)
    })
    emailList.value = transformedList.map((manager: { email: string; }) => manager.email || '')
  },
  {
    immediate: true,
    deep: true,
  }
)



const emit = defineEmits(["cancel", "ok"]);

const visible = computed(() => props.show);

const handleOk = async () => {
  confirmLoading.value = true;
  
  // 1. 必选字段验证
  if (!selectedManagers.value?.length) {
    message.error('请选择产品线管理员');
    confirmLoading.value = false;
    return;
  }

  // 2. 数据转换
  noticeContractExpiration.value.sceneContractNotifierDTOList = 
    selectedManagers.value.map(item => ({
      userName: item.username,
      nickName: item.nickName,
      email: item.email,
      id: item.id || null
    }));

  // 3. 表单验证与提交
  try {
    await from.value.validate();
    emit("ok", noticeContractExpiration.value);
  } finally {
    confirmLoading.value = false;
  }
};


//通知人相关处理

// 用于UserSelect组件的参数
const userSelectParams = ref<INoticeContractExpirationFilter>({
  pageNum: 1,
  pageSize: 20
});

// 处理管理员选择变更
const handleManagerChange = (users: any) => {
  console.log('管理员选择变更:', users);

  // 确保users是数组
  const userArray = Array.isArray(users) ? users : [users].filter(Boolean);

  // 创建新的管理员和昵称数组
  const newManagers: any[] = [];
  const newManagerUsernames: string[] = [];

  // 处理每个用户对象
  userArray.forEach(user => {
    // 如果用户是对象格式
    if (typeof user === 'object' && user !== null) {
      // 保留用户对象的所有原始属性
      const manager = { ...user };
      newManagers.push(manager);
      // 使用昵称
      const nickName = manager.nickName || '';
      newManagerUsernames.push(nickName);
    }
  });

  // 找出被删除的管理员
  const deletedManagers = selectedManagers.value.filter(manager => {
    return !newManagers.some(m => {
      if (manager.id && m.id) {
        return manager.id === m.id;
      }
      return manager.username === m.username;
    });
  });

  // 处理被删除的管理员
  deletedManagers.forEach(manager => {
    // 只有当管理员有ID且ID在原始列表中时，才添加到删除列表
    if (manager.id && originalManagerIds.value.includes(manager.id)) {
      const idToAdd = Number(manager.id);
      if (!userDelIdList.value.includes(idToAdd) && !isNaN(idToAdd)) {
        userDelIdList.value.push(idToAdd);
      }
    }
  });

  // 更新管理员列表
  selectedManagers.value = newManagers;
  selectedManagerUsernames.value = newManagerUsernames;
  emailList.value = newManagers.map((manager: { email: string; }) => manager.email || '')
  childRef.value?.setFirstData(selectedManagers.value)
};

//获取中台业务类型
const {
  data: businessTypeList,
  run: businessTypeApiRun,
  loading: businessTypeLoading,
} = useRequest(tagApi.getBusinessTypeList, {
  manual: false,
  onSuccess: (data) => console.log('接口返回数据:', data)
});

</script>

<template>
  <h-modal v-model:visible="visible" :title="noticeContractExpiration.id ? '编辑合同到期通知人管理' : '新增合同到期通知人管理'" :width="600"
    @cancel="$emit('cancel')" :confirmLoading="confirmLoading" @ok="handleOk">
    <h-form ref="from" :model="noticeContractExpiration" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }"
      :rules="rules">
      <h-form-item label="业务类型" name="businessType">
        <a-select style="width: 100%" v-model:value="noticeContractExpiration.businessType" allow-clear
          placeholder="请选择业务类型">
          <a-select-option v-for="(item, index) in businessTypeList" :key="index" :value="item.key">{{
            item.value
          }}</a-select-option>
        </a-select>
      </h-form-item>
      <h-form-item label="通知人" name="selectedManagerUsernames">
        <UserSelectPerson v-model:value="selectedManagerUsernames" :params="userSelectParams" :multiple="true"
          :max-tag-count="maxManagerCount" :max-count="maxManagerCount" placeholder="请选择产品线管理员"
          @change="handleManagerChange" ref="childRef" />
      </h-form-item>
      <h-form-item label="邮箱" name="emailList">
        <h-input v-model:value="emailList" placeholder="请输入邮箱" :title="emailList" readonly />
      </h-form-item>
      <h-form-item label="提前通知天数" name="expiredNoticeDay">
        <h-input-number type="number" v-model:value="noticeContractExpiration.expiredNoticeDay" placeholder="请输入提前通知天数"
          :min="1" :precision="0" style="width: 100%;"/>
      </h-form-item>
    </h-form>
  </h-modal>
</template>


<style lang="less" scoped>
.important {
  color: red;
}

:deep(.ant-form-item-required::before) {
  display: none !important;
}
</style>