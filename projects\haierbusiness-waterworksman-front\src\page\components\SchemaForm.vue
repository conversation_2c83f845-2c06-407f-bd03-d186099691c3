<!-- Ant Design Vue -->

<template>
  <h-row :gutter="props.gutter">
    <template v-for="item in props.fieldList">
      <h-col v-bind="item.span ? { span: item.span } : { ...props.column }">
        <h-form-item
          v-if="!item.isShow"
          :label="item.label"
          :name="item.name"
          :rules="item.rules"
          v-bind="{ ...item.formItemProps, ...props.formItemProps }"
          :colon="true"
        >
          <slot :name="item.name" :data="item">
            <template v-if="props.readonly || item.readonly || !item.type">
              <span>{{ formData[item.name] || item.valueText || '-' }}</span>
            </template>
            <template v-else>
              <component :is="item.type" v-model:value="formData[item.name]" v-bind="{ ...item.fieldProps }">{{
                item.valueText
              }}</component>
            </template>
          </slot>
        </h-form-item>
      </h-col>
    </template>
    <div style="display: flex; justify-content: right; flex: 1">
      <slot name="search"></slot>
    </div>
  </h-row>
</template>

<script setup lang="ts">
import { Col as hCol, FormItem as hFormItem, Row as hRow } from 'ant-design-vue';
import type { ComputedRef } from 'vue';
export type FieldType = {
  label: string; // 表单标签名
  name: string; // 表单字段名
  key: string; // 表单字段key
  type?: string; // 组件类型
  span?: number; // 组件宽度
  rules?: any[]; // 表单验证规则
  valueText?: string; // 文本值 -- readonly场景 不使用formData 可以使用此属性替代
  isShow?: boolean | undefined | ComputedRef<boolean>; // 是否显示
  readonly?: boolean; // 是否只读
  fieldProps?: Record<string, any>; // 组件属性
  formItemProps?: Record<string, any>; // FormItem组件属性
};
/**
 * 表单字段类型定义
 * @typedef {Object} FieldType
 * @property {string} label - 表单标签名
 * @property {string} name - 表单字段名
 * @property {string} key - 表单字段key
 * @property {string} type - 组件类型(input/select等)
 * @property {number} [span] - 栅格占据的列数
 * @property {Array} [rules] - 表单验证规则
 * @property {string} [valueText] - 显示的文本值
 * @property {boolean|ComputedRef<boolean>} [isShow] - 是否显示字段
 * @property {Record<string, any>} [fieldProps] - 组件属性
 * @property {Record<string, any>} [formItemProps] - FormItem组件属性
 */

/**
 * 动态表单组件
 * @component
 * @name DynamicForm
 * @property {Object} formData - 表单数据对象(必需)
 * @property {FieldType[]} fieldList - 字段配置数组
 * @property {number|Array<number>} [gutter=16] - 栅格间隔
 * @property {Object} [column] - 响应式栅格配置
 *  - [column.xs] - <768px 时的栅格数
 *  - [column.sm] - ≥768px 时的栅格数
 *  - [column.md] - ≥992px 时的栅格数
 *  - [column.lg] - ≥1200px 时的栅格数
 *  - [column.xl] - ≥1920px 时的栅格数
 */
const props = withDefaults(
  defineProps<{
    formData: any;
    column?: Partial<Record<'xs' | 'sm' | 'md' | 'lg' | 'xl', number>>;
    gutter?: number | [number, number];
    fieldList: FieldType[];
    readonly?: boolean;
    formItemProps?: Record<string, any>;
  }>(),
  {
    formType: 'default',
    fieldList: () => [],
    gutter: 16,
    column: () => ({ xl: 6, lg: 6, md: 8, sm: 12, xs: 24 }),
    readonly: false,
  },
);
</script>
