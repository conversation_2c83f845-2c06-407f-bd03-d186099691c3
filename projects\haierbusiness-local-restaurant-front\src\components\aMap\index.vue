<script setup  lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';
import AMapLoader from '@amap/amap-jsapi-loader';
import { showSuccessToast, showFailToast } from 'vant';

const map = ref(null);

onMounted(() => {
  window._AMapSecurityConfig = {
    securityJsCode: '610db7cc7881574494e34fd00b13ab97',
  };
  AMapLoader.load({
    key: '25569e43d6c6bcfa4d39a1b920d8d2d1', // 申请好的Web端开发者Key，首次调用 load 时必填
    version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    plugins: ['AMap.Scale'], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
  })
    .then((AMap) => {
      map.value = new AMap.Map('container', {
        // 设置地图容器id
        viewMode: '3D', // 是否为3D地图模式
        zoom: 16, // 初始化地图级别
        center: [116.397428, 39.90923], // 初始化地图中心点位置
      });

      // 监听地图点击事件
      map.value.on('click', (ev) => {
        console.log(999999, ev);
        //触发事件的对象
        var target = ev.target;
        //触发事件的地理坐标，AMap.LngLat 类型
        var lnglat = ev.lnglat;
        //触发事件的像素坐标，AMap.Pixel 类型
        var pixel = ev.pixel;
        //触发事件类型
        var type = ev.type;

        toApp(positionNow.value, addressNow.value);
      });
    })
    .catch((e) => {
      console.log(e);
    });
});

onUnmounted(() => {
  map.value?.destroy();
});

/*
   根据坐标移动地图中心
   position : [x,y] 坐标
   immediately 是否立即移动
   duration 动画时间
*/
const moveMapCenter = (position: Array<string | number>, immediately: Boolean = false, duration: number = 1000) => {
  map.value.setCenter(position, immediately, duration);
};

const positionNow = ref<Array<string | number>>([]);
const addressNow = ref<string>('');
/*
   添加位置坐标点
   position : [x,y] 坐标
   imgUrl 完整的图片url
*/
const addMark = (imgUrl: string, position: Array<string | number>, content: string) => {
  positionNow.value = position;
  addressNow.value = content;

  const marker = new AMap.Marker({
    position: position,
    map: map.value,
  });
  marker.setLabel({
    direction: 'top',
    offset: new AMap.Pixel(10, 0), // 设置文本标注偏移量
    content: '<div>' + content + '</div>', // 设置文本标注内容
  });

  map.value.add(marker);
};

/*
  点击地图拉起app导航
*/
const toApp = (position: Array<string | number>, address: string) => {
  console.log(position,address )
  let phoneType = '';
  const agent = navigator.userAgent.toLowerCase();

  if (agent.indexOf('iphone') != -1 || agent.indexOf('ipad') != -1) {
    phoneType = 'iosamap://path';
  } else {
    phoneType = 'androidamap://route/plan';
  }

  //地址逆解析插件
  let geolocation = null;
  AMap.plugin('AMap.Geolocation', () => {
    geolocation = new AMap.Geolocation({
      city: '010', //城市设为北京，默认：“全国”
      radius: 1000, //范围，默认：500,
      extensions: 'all',
    });
  });

  // const geolocation = new AMap.Geolocation();
  geolocation.getCurrentPosition((status, result) => {
    if (status == 'complete') {
      let { lat, lng } = result;
      let app_url = `${phoneType}?sourceApplication=order&sid=&slat=${lat}&slon=${lng}&sname=我的位置&did=&dlat=${position[1]}&dlon=${position[0]}&dname=${address}&dev=0&t=0`
      window.location.href = app_url;
    } else {
      showFailToast('打开高德地图失败，请检查您的手机设置');
    }
  });
};

defineExpose({
  moveMapCenter,
  addMark,
});
</script>

<template>
  <div class="map-box">
    <div id="container"></div>
    <slot></slot>
  </div>
</template>

<style  lang="less">
#container {
  width: 100%;
  height: 80px;
}
.map-box {
  position: relative;
}
.icon-mark-box {
  width: 100px;
  display: flex;
  align-items: center;
  flex-direction: column;
  .icon-mark-img {
    width: 20px;
    height: 20px;
  }
  .icon-mark-text {
    display: flex;
    font-size: 10px;
  }
}
</style>
