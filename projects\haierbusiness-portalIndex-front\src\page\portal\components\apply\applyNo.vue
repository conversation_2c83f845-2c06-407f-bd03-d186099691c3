<script setup lang="ts">
import { onMounted, ref, watch,onUnmounted } from 'vue';
import { tripApi } from '@haierbusiness-front/apis';
import {
  IDataListItem,
} from '@haierbusiness-front/common-libs';
import dayjs from "dayjs";

interface Props {
    travelType?: number
    leftNum?: string
}

const props = withDefaults(defineProps<Props>(), {
    travelType: 1
});

const emit = defineEmits(["change"]);


// 单程，返程
const travelType = ref(props.travelType)

watch(props, (newValue) => {
    travelType.value = newValue.travelType
})
const dataList = ref<Array<IDataListItem>>([]);

// 获取申请单列表
const getApplyPage = () => {
    tripApi.getApplyPage({
        pageNum: 1,
        pageSize: 999,
        beginDate: dayjs().format('YYYY-MM-DD'),
        status: "30",
        auditStatus: "30"
    }).then(res => {
        dataList.value = res?.records || [];
    })
}

const changeNo = (id: string) => {
    const code = dataList.value.find(item => item.id == id)?.applyNo
    emit('change', id, code)
}

onMounted(() => {
    getApplyPage()
})



</script>


<template>
    <div class="apply-no-component">
        <div class="ticket-item" :class="{ 'international-left-width': travelType === 3 }">
            <div class="item-labels">差旅申请</div>
            <div class="item-num">
                <a-select class="apply-no" optionFilterProp="applyNo" show-search allowClear  @change="changeNo" size="small" :fieldNames="{ label: 'applyNo', value: 'id'}" :options="dataList" placeholder="请选择差旅申请单">
                </a-select>
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
@import url('./common.less');

.apply-no {
    width:100%; 
    height: 22px;
}

</style>

<style>
.apply-no-component .ant-select-selector {
  border:none !important;
  box-shadow: none !important;
  height: 22px !important;
  padding: 0px !important;
  
}

.apply-no-component .ant-select-selection-placeholder {
  font-size: 16px !important;
  color: rgba(0,0,0,0.35) !important;
  
  padding-inline-end: 25px !important;
}

.apply-no-component .ant-select-selection-item {
  font-size: 16px !important;
  
  color: rgba(0,0,0,0.85);
}

.apply-no-component .ant-select-item-option-content {
  
}
</style>
