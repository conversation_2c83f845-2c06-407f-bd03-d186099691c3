<script setup lang="ts">
import conferenceDetail from '../../components/conference/detail/conference_detail_base.vue'
import { isMobile } from '@haierbusiness-front/utils';
import { computed, onMounted, ref } from 'vue';
import router from '../../router'

const target = ref<'PC' | 'MOBILE'>('PC')
const currentRouter = ref()
const code = ref<string>('')

onMounted(async() => {
    currentRouter.value = await router
    if(isMobile()) {
        target.value = 'MOBILE'
    } else {
        target.value = 'PC'
    }
    code.value = currentRouter.value.currentRoute.query?.code ?? ''
})

</script>

<template>
    <div class="content">
        <div class="detail">
            <conference-detail :show="false" :code="code" :flag="0" />
        </div>
    </div>
</template>

<style lang="less" scoped>
.content {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: 20px;
}

.detail {
    width: 1200px;
}

@media screen and (min-width: 0px) and (max-width: 1199px) {
    .content {
        display: flex;
        justify-content: center;
        width: 100%;
        margin-top: 0px;
    }
    .detail {
        width: 100%;
    }
}

@media screen and (max-width: 767px) {
    .content {
        display: flex;
        justify-content: center;
        width: 100%;
        margin-top: 0px;
    }

    .detail {
        width: 100%;
    }
}

</style>