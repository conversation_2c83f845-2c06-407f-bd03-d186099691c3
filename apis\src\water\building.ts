import { download, get, post, filepost, originalGet } from '../request'

export const waterworkBuildingApi = {
    list: (params: any): Promise<void> => {
        return get('waterworks/api/tbuild/page', params)
    },
    listAll: (params: any): Promise<void> => {
        return get('waterworks/api/tbuild/list', params)
    },
    add: (params: any): Promise<void> => {
        return post('waterworks/api/tbuild/save', params)
    },
    update: (params: object): Promise<void> => {
        return post(`waterworks/api/tbuild/update`, params);
    },
    export: (params: any): Promise<void> => {
        return download('waterworks/api/tbuild/export', params)
    },
    delete: (ids: string): Promise<void> => {
        return post(`waterworks/api/tbuild/delete/${ids}`);
    },
}