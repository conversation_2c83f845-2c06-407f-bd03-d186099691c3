<template>
  <div id="map-airline" background="rgba(0,0,0,0)"></div>
</template>
<script lang="ts" setup>
import chinaData from '@/assets/geojson/china.json';
import * as echarts from 'echarts';
import { onMounted, ref } from 'vue';
import { queryAirlineMapData } from '@haierbusiness-front/apis/src/data/board/travel';
import { EventBus } from '../../../eventBus';
import { chinaMapOptions, planePath } from '../../../data';
const loading = ref(false);
let chartDom, myChart;
//所有飞线起点终点的集合
let lines_coord: any = [
  /*
   * 写法一：标准写法对象：key:coords    value:经纬度的数组
   */
  // { coords: [[112.549248, 37.857014], [121.509062, 25.044332]] },
  // { coords: [[114.502461, 38.045474], [121.509062, 25.044332]] },
  // { coords: [[102.549248, 37.857014], [101.509062, 25.044332]] },
  // { coords: [[124.502461, 42.045474], [101.509062, 25.044332]] },
  /*
   * 写法二：数组形式，默认该点位的数组为key:coords 的值
   */
  // [[112.549248, 37.857014],[121.509062, 25.044332]],
  // [[114.502461, 38.045474], [121.509062, 25.044332]]
];
onMounted(() => {
  echarts.registerMap('china', chinaData as any);
  chartDom = document.getElementById('map-airline');
  myChart = echarts.init(chartDom as any, 'dark');
});
EventBus.on((event, params) => {
  if (event == 'refresh') {
    lines_coord = [];
    queryData(params ? params : null);
  }
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
  loading.value = true;
  const data = await queryAirlineMapData(params ? params.data.name : null, params ? params.from : null);
  console.log('data', data);
  const { rows } = data;
  rows.forEach((item) => {
    if (item[2] && item[3] && item[4] && item[5]) {
      lines_coord.push([
        [item[2], item[3]],
        [item[4], item[5]],
      ]);
    }
  });
  init();
  loading.value = false;
};

onMounted(() => {
  queryData();
});
const init = () => {
  myChart.setOption({
    ...chinaMapOptions,
    series: [
      // {
      //     // effectScatter画散点【起点】
      //     type: 'effectScatter',
      //     coordinateSystem: 'geo',
      //     zlevel: 2,
      //     symbolSize: 6,
      //     rippleEffect: {
      //         period: 3, brushType: 'stroke', scale: 3
      //     },
      //     itemStyle: {
      //         color: '#FFB800',
      //         opacity: 0.7
      //     },
      //     data: coord.slice(2)
      // },
      // {
      //     // 画中心散点【终点】,这里是为了区分起点终点不同样式，所以分开写，如果二者样式一样那就直接合在一起写就好了
      //     type: 'effectScatter',
      //     coordinateSystem: 'geo',
      //     zlevel: 2,
      //     symbolSize: 10,
      //     rippleEffect: {
      //         period: 4, brushType: 'stroke', scale: 4
      //     },
      //     itemStyle: {
      //         color: '#FF5722',
      //         opacity: 1
      //     },
      //     data: coord.slice(0, 2)
      // },
      //这里设了2条不同效果的飞线是为了讲他们叠加起来，满足飞机后面白色的喷气动画效果
      {
        type: 'lines',
        zlevel: 1,
        effect: {
          show: true,
          period: 5,
          trailLength: 0.7,
          color: '#fff',
          symbolSize: 3,
        },
        lineStyle: {
          normal: {
            color: '#FFB800',
            width: 0,
            curveness: 0.2,
          },
        },
        data: lines_coord,
      },
      {
        // lines画线
        type: 'lines',
        coordinateSystem: 'geo',
        zlevel: 2,
        symbol: ['none', 'none'], //设置飞线的起点终点处的绘制图形
        symbolSize: 3,
        effect: {
          show: true,
          period: 5, //箭头指向速度，值越小速度越快
          trailLength: 0, //特效尾迹长度[0,1]值越大，尾迹越长重
          symbol: planePath, //飞机图标
          symbolSize: 10, //图标大小
          color: '#01AAED',
        },
        itemStyle: {
          normal: {
            borderWidth: 1,
            lineStyle: {
              type: 'solid',
              shadowBlur: 10,
            },
          },
        },
        lineStyle: {
          //飞线的样式
          normal: {
            width: 1.2,
            opacity: 0.6,
            curveness: 0.2,
            color: '#FFB800',
          },
        },
        data: lines_coord,
      },
    ],
  });
};
</script>
<style scoped>
#map-airline {
  height: 81.5vh;
}
</style>
