<template>
  <a-tabs v-model:activeKey="activeKey">
    <a-tab-pane key="1" tab="PC端">
      <h-card :title="addForm.title" hoverable style="width: 640px;">
        <template #cover>
          <pre class="contentBox" v-html="addForm.content"></pre>
          <div v-if="addForm.imageBase64" class="imgBox">
            <img :src="'data:image/png;base64,'+addForm.imageBase64" alt />
          </div>
        </template>
        <a-card-meta>
          <template #description>
            <h-button
              style="border-radius:6px;"
              type="primary"
              ghost
              @click="toLink(addForm.linkUrl)"
              v-if="addForm.linkUrl"
            >查看</h-button>
          </template>
        </a-card-meta>
      </h-card>
    </a-tab-pane>
    <a-tab-pane key="2" tab="移动端" force-render>
      <h-card :title="addForm.title" hoverable class="mobile" style="width: 300px;">
        <template #cover>
          <pre class="contentBox" v-html="addForm.content"></pre>
          <div v-if="addForm.imageBase64" class="imgBox">
            <img :src="'data:image/png;base64,'+addForm.imageBase64" alt />
          </div>
        </template>
        <a-card-meta>
          <template #description>
            <h-button
              style="border-radius:6px;"
              type="primary"
              block
              ghost
              @click="toLink(addForm.linkUrl)"
              v-if="addForm.linkUrl"
            >查看</h-button>
          </template>
        </a-card-meta>
      </h-card>
    </a-tab-pane>
  </a-tabs>
</template>

<script setup lang="ts">
import { Card as hCard, Button as hButton } from "ant-design-vue";
import { computed, ref, watch, onMounted } from "vue";
interface Props {
  addForm: any;
}
const activeKey = ref<string>('1')
const props = withDefaults(defineProps<Props>(), {
  addForm: {}
});
// 点击查看
const toLink = (url: string) => {
  if(url.indexOf('https://')!=-1 || url.indexOf('http://')!=-1){
    window.open(url, "_blank");
  }else{
    window.open('https://'+url, "_blank");
  }
};
</script>

<style scoped lang="less">
.contentBox {
  word-break: break-all;
  font-size: 14px;
  color: #464545;
  font-family: "Microsoft YaHei", Arail, "Times New Roman";
  white-space: pre-wrap;
}
.mobile :deep(.ant-card-head) {
  min-height: 40px!important;
  font-size: 14px;
}
.imgBox {
  width: 100%;
  img {
    width: 100%;
  }
}
</style>