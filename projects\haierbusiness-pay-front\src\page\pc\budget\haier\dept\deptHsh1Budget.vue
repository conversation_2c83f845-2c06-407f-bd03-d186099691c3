<script setup lang="ts">
import { computed, onMounted, PropType, reactive, ref, watch, h  } from 'vue';
import {
  Table as hTable,
  Divider as hDivider,
  Select as hSelect,
  SelectOption as hSelectOption,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  RadioButton as hRadioButton,
  Modal as hModal,
  Input as hInput,
  InputSearch as hInputSearch,
  Loading as hLoading,
  Space as hSpace,
  Button as hButton,
  Row as hRow,
  Col as hCol,
  Tabs as hTabs,
  TabPane as hTabPane,
  Image as hImage,
  Tooltip as hTooltip,
  message,
  TableProps,
  InputGroup as hInputGroup,
} from 'ant-design-vue';
import { SearchOutlined, SwapOutlined } from '@ant-design/icons-vue';
import { IPayData } from '@haierbusiness-front/common-libs/src/pay/model/basicModel';
import { budgetHaierPayApi } from '@haierbusiness-front/apis/src/pay/budgetHaierPay';
import {
  HaierBudgetSourceConstant,
  IUserInfo,
  IUserListRequest,
  PaySourceConstant,
  QueryBudgetInfoRes,
  IAbCodeResponse,
  IBudgetHaierHBC2Response,
  IAbCodeDeptResponse
} from '@haierbusiness-front/common-libs';
import { DataType, usePagination, useRequest } from 'vue-request';
import { userApi } from '@haierbusiness-front/apis';
import { Key, TablePaginationConfig } from 'ant-design-vue/lib/table/interface';
import { isMobile } from '@haierbusiness-front/utils';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue'

const props = defineProps<{ budgetType?: string; param?: IPayData }>();

const emit = defineEmits<{
  (e: 'payComplete', isPayComplete: boolean): void;
}>();

// 个人or部门
const budgetBelong = ref(0)

// 预算系统
const budgetSystem = ref()

// 项目名称
const budgetDepartmentCode = ref()
const budgetDepartmentName = ref()
// 预算人
const budgeterCode = ref()
const budgeterName = ref()
// 业务场景
const sceneThreeCode = ref()
const sceneThreeName = ref()
watch(sceneThreeCode, (newValue, oldValue) => {
  sceneThreeName.value = sceneThreeOptions.value.find((it) => {
    return it.value === newValue;
  })?.label;
});


const payLoading = ref<boolean>(false)

const sceneThreeOptions = ref<any>([
  {label:'会务接待',value:'162712529740529669'},
  {label:'会务场地',value:'162712529740529670'},
  {label:'机票',value:'162712529740529668'}
])
const onSearch = () => {
  budgetHaierPayApi
    .searchHBC2Budget({
      applicationCode: props.param?.applicationCode,
      budgeterCode: userName.value,
      budgetDepartmentCode: budgetGroupCode.value,
      isQueryDept: budgetBelong.value,
      haierBudgetType: props.budgetType,
      businessType: props.param?.businessType,
    })
    .then((res) => {
      // 费用科目
      res.map((item, index) => {
        item.id = index + 1
      })
      budgets.value = res
      // 是否是多个
      if(res.length > 0) {
        const data = res[0]
        leftAmt.value = data.leftAmt
        feeItem.value = data.feeItem
        feeItemName.value = data.feeItemName
        budgetSystem.value = data.systemCode
        accountCode.value = data.accountCode
        getBillAndCostCenters(performCode.value, data.feeItem ?? '')
      } else {
        message.error('未查询到预算！')
      }
    })
    .finally(() => {
      userLoading.value = false;
    })
};

const payComplete = () => {
  emit('payComplete', true);
};

const pay = () => {
  if (budgetBelong.value === 0 && !budgeterCode.value) {
    message.error('请输入预算人!');
    return;
  }
  payLoading.value = true;
  budgetHaierPayApi
    .occupyBudget(
      {
        haierBudgetType: props.budgetType,
        budgeterCode: budgeterCode.value,
        budgeterName: budgeterName.value,
        budgetDepartmentCode: budgetDepartmentCode.value,
        budgetDepartmentName: budgetDepartmentName.value,
        sceneThreeCode:sceneThreeCode.value,
        sceneThreeName:sceneThreeName.value,
        // - 通用参数
        extJsonParam: props.param?.extJsonParam,
        orderCode: props.param?.orderCode,
        applicationCode: props.param?.applicationCode,
        payTypes: props.param?.payTypes,
        username: props.param?.username,
        providerCode: props.param?.providerCode,
        amount: Number(props.param?.amount),
        orderDetailsUrl: props.param?.orderDetailsUrl,
        notifyUrl: props.param?.notifyUrl,
        callbackUrl: props.param?.callbackUrl,
        description: props.param?.description,
        payload: props.param?.payload,
        businessType: props.param?.businessType,
        processId: props.param?.processId,
        startApproveFlag: props.param?.startApproveFlag,
        enterpriseCode: props.param?.enterpriseCode,
        paySource: isMobile() ? PaySourceConstant.MOBILE.type : PaySourceConstant.PC.type,
        paymentMethod: 2
      },
      {
        applicationCode: props.param?.applicationCode,
        excludes:
          'paySource,haierBudgetType,budgeterCode,budgeterName,budgetDepartmentCode,budgetDepartmentName,paymentMethod,startApproveFlag,sceneThreeCode,sceneThreeName',
        nonce: props.param?.hbNonce,
        timestamp: props.param?.hbTimestamp,
        sign: props.param?.sign,
      },
    )
    .then((it) => {
      payComplete();
    })
    .finally(() => {
      payLoading.value = false;
    });
};

const visibleDeptSearch = ref(false)


const deptTableColumns = [
  {
    title: '',
    dataIndex: '',
    width: '7%',
  },
  {
    title: '部门编码',
    dataIndex: 'budgetDepartmentCode',
    width: '45%',
  },
  {
    title: '部门名称',
    dataIndex: 'budgetDepartmentName',
    width: '45%',
  }
]
// 选人组件
const userName = ref()
const nickName = ref()
// 部门名称
const deptName = ref()
// 出账法人
const legalPersonOptions = ref<Array<{legalPerson: string, legalPersonName: string, costCenter: string, costCenterName: string, id: number}>>([]);


const userNameChange = (userInfo: IUserInfo | undefined) => {
    if (!userInfo) {
      nickName.value = ''
      userName.value = ''
      budgeterCode.value = ''
      budgeterName.value = ''
      return
    }
    userName.value = userInfo.username
    nickName.value = userInfo.nickName
    budgeterCode.value = userInfo.username
    budgeterName.value = userInfo.nickName
    getDept()
}

const deptList = ref<any>([])

// 根据人员请求部门
const getDept = ()=>{
  budgetHaierPayApi.budgetQueryList({budgeterCode:budgeterCode.value,haierBudgetType: props.budgetType}).then(res=>{
    deptList.value = res
    if(res&&res.length){
      budgetDepartmentName.value = res[0].budgetDepartmentName
      budgetDepartmentCode.value = res[0].budgetDepartmentCode
      userSelectedRowKeys.value = [res[0].budgetDepartmentCode]
      userSelectedRowRecord.value =  res[0]
    }
  })
}

// 点击切换预算部门
const onSwitch = ()=>{
  visibleDeptSearch.value = true
}
// 选择部门
const onDeptSearchOk = () =>{
  console.log(userSelectedRowRecord.value,"999999999999999")
  if(!userSelectedRowRecord.value?.budgetDepartmentCode){
    message.error('请选择预算部门')
  }else{
    budgetDepartmentName.value = userSelectedRowRecord.value.budgetDepartmentName
    budgetDepartmentCode.value = userSelectedRowRecord.value.budgetDepartmentCode
    visibleDeptSearch.value = false
  }
}

// 用户选择
const params = ref<IUserListRequest>({
    enterpriseCode: 'haier',
    pageNum: 1,
    pageSize: 20
})
const userSelectedRowKeys = ref([])
const userSelectedRowRecord = ref({})

const selectUserSelection: TableProps['rowSelection'] = {
  type: 'radio',
  selectedRowKeys: userSelectedRowKeys as any,
  onChange: (selectedRowKeys: Key[], selectedRows: DataType[]) => {
    userSelectedRowKeys.value = selectedRowKeys;
    userSelectedRowRecord.value = selectedRows[0] as unknown;
  },
};
</script>
<template>
  <!-- 切换部门 -->
  <h-modal v-model:visible="visibleDeptSearch" :title="'部门查询选择'" style="width: 1000px" @ok="onDeptSearchOk">
    <h-row :align="'middle'">
      <h-col :span="24">
        <h-table
          :columns="deptTableColumns"
          :row-key="(record) => record.budgetDepartmentCode"
          :row-selection="selectUserSelection"
          size="small"
          :data-source="deptList"
          :pagination="false"
        >
        </h-table>
      </h-col>
    </h-row>
  </h-modal>
  <h-row :align="'middle'">
    <h-col span="2" class="line-top" style="font-size: 12px"> 预算人： </h-col>
    <h-col span="6" class="line-top" style="text-align: left">
      <h-row>
        <h-col span="24">
          <div class="row">
            <user-select 
              class="budget-input"
              :value="nickName" 
              :params="params" 
              size="large"
              @change="(userInfo: IUserInfo | undefined) =>  userNameChange(userInfo)" />
            <a-button type="primary" :icon="h(SearchOutlined)" style="width:46px;" :size="'large'" @click="getAbCode" />
          </div>
        </h-col>
      </h-row>
    </h-col>
    <h-col span="2" class="line-top" style="font-size: 12px"> 预算部门： </h-col>
    <h-col span="6" class="line-top" style="text-align: left">
      <div class="row">
        <h-input
          :disabled="true"
          v-model:value="budgetDepartmentName"
          placeholder=""
          :size="'large'"
          class="budget-input-readonly"
          style="width: 100%;"
        />
        <h-tooltip title="切换预算">
          <h-button type="primary" @click="onSwitch" style="width:46px;"  :size="'large'" :disabled="!(deptList && deptList.length > 0)">
            <template #icon><SwapOutlined /></template>
          </h-button>
        </h-tooltip>
      </div>
    </h-col>
    <h-col span="2" class="line-top" style="font-size: 12px"> 业务场景： </h-col>
    <h-col span="6" class="line-top" style="text-align: left">
      <h-select v-model:value="sceneThreeCode" :size="'large'" style="width: 100%;" class="budget-input" @change="sceneThreeChange" :options="sceneThreeOptions"></h-select>
    </h-col>
  </h-row>
  <h-row>
    <h-divider></h-divider>
  </h-row>
  <h-row style="line-height: 14vh" :align="'middle'">
    <h-col span="2" offset="11">
      <h-button type="primary" style="width: 100%" @click="pay" :loading="payLoading" size="large"
        >&nbsp;提&nbsp;&nbsp;交 &nbsp;
      </h-button>
    </h-col>
  </h-row>
</template>

<style scoped lang="less">
.row {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.line-top {
  margin-top: 4vh;
}

.budget-input {
  width: 12vw;
}

.budget-input-readonly {
  width: 12vw;
  background-color: rgb(245, 245, 245);
  color: #2e2e2e;
}

::v-deep .row .ant-select-selector{
  height: 40px;
}
::v-deep .row .ant-select-selection-item{
  line-height: 40px;
}
</style>
