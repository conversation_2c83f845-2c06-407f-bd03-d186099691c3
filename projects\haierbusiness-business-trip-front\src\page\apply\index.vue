<script lang="ts" setup>
import { computed, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';
import outPersonModal from './components/outPersonModal.vue';
import budgetModal from './components/budgetModal.vue';
import budgetDetailModal from './components/budgetDetailModal.vue';
import travelStandardsModal from './components/travelStandardsModal.vue';

import baseInfo from './components/baseInfo.vue';
import planBudge from './components/planBudge.vue';
import fileUpload from './components/fileUpload.vue';
import approvalProcess from './approvalProcess.vue';

import 'animate.css';

import {
  Anchor as hAnchor,
  Button as hButton,
  Spin as hSpin,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Checkbox as hCheckbox,
  Form as hForm,
  FormItem as hFormItem,
  Row as hRow,
  Col as hCol,
  Popconfirm as hPopconfirm,
  Modal as hModal,
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, message } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  CheckCircleTwoTone,
  ExclamationCircleOutlined,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { tripApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import relativeTime from 'dayjs/plugin/relativeTime';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from "@haierbusiness-front/utils";

const route = ref(getCurrentRoute());

dayjs.extend(relativeTime);

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const anchorItems = [
  {
    key: '1',
    href: '#base-info',
    title: '基本信息',
  },
  {
    key: '2',
    href: '#plan-budget',
    title: '行程计划与费用预算',
  },
  {
    key: '3',
    href: '#file',
    title: '附件',
  },
];

// 用户选择
const params = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});
// 出差人
const name = ref('');
const userNameChange = (userInfo: IUserInfo) => {
  name.value = userInfo?.nickName ?? '';
};
const handleClick: AnchorProps['onClick'] = (e, link) => {
  e.preventDefault();
  console.log(link);
};

const info = ref();

//#region 为了表单临时写的一些变量    后续需要删除整合到别的地方

const isTravel = ref(1);


const headers = {
  authorization: 'authorization-text',
};
// 上传附件结束

const checked = ref<boolean>(false);

const pdfUrl = new URL('@/assets/slyszc.pdf', import.meta.url).href

const showPdf = () => {
  window.open(pdfUrl)
}

//#endregion

// 打开预算弹窗
const budget = ref();
const openBudgetModal = () => {
  budget?.value.show();
};

// 打开预算明细弹窗
const budgetDetail = ref();
interface IbudgetDetail {
  amountSum: string;
  travelUserName: string;
  travelUserNo: string;
  travelUserSyId: string;
}
const budgetDetailData = ref<Array<IbudgetDetail>>([]);
const openBudgetDetailModal = (data: Array<IbudgetDetail>) => {
  budgetDetailData.value = data;
  budgetDetail?.value.show();
};

// 差旅标准弹窗
const travelStandards = ref();
const openTravelStandardslModal = (val:any) => {
  let codeList = creatTripParma.value?.travelerList?.filter((item:any) => item.travelUserType == 0).map(item2 => item2.travelUserNo)
  let nameList = creatTripParma.value?.travelerList?.filter((item:any) => item.travelUserType == 0).map(item2 => item2.travelUserName)

  // 判断经办人的工号是否在出差人工号列表里面
  if(codeList.indexOf(loginUser.value?.username)== -1 ) {
    codeList = [loginUser.value?.username, ...codeList]
    nameList = [loginUser.value?.nickName, ...nameList]
  }

  travelStandards?.value.show(codeList, nameList);
};

// 外部联系人弹窗
const outPerson = ref();
const outPersonOpen = () => {
  outPerson?.value.show();
};

const getOutPersonList = () => {
  baseInfoCom?.value.getOutPersonList();
};
// 创建申请单请求参数
const creatTripParma = ref<ICreatTrip>({
  tripList: [],
  travelerList: [
    {
      travelUserName: loginUser.value?.nickName,
      travelUserSyId: loginUser.value?.username,
      travelUserDeptName: loginUser.value?.departmentName || loginUser.value?.enterpriseName,
      travelUserDeptId: loginUser.value?.departmentCode,
      travelUserNo: loginUser.value?.username,
      username: loginUser.value?.username,
      travelUserType: '0',
      mainFlag: '1',
    },
  ],
  operUserId: loginUser.value?.id,
  operUserNo: loginUser.value?.username,
  operUserName: loginUser.value?.nickName,
  operDeptId: loginUser.value?.departmentCode,
  operDeptName: loginUser.value?.departmentName,

  fileList: [],
  travelReason: undefined,
  travelReserveFlag: 1,
  travelUserName: loginUser.value.nickName,
  haierBudgetPayOccupyRequest: {}
});

// watch(creatTripParma, (newValue, oldValue) => {
//   // 初次加载不验证
//   if(oldValue) {
//     // 如果更换出差人和外部出行人，需要检查行程中是否存在费用明细
//     // 如果存在，需要从费用明细中将已经删除的外部出行人和出差人去掉

//     // 先比较变得是否是出差人
//     const traveler = oldValue.travelerList?.find(o => o.travelUserType == '0')
//     const isSame = newValue.travelerList?.find(o => o.travelUserType === traveler?.travelUserSyId)
//     if(!isSame) {
//       // 出差人不是同一个，并且费用明细不为空，需要给出提示

//     }
//   }
// }, { immediate: true })

const getParent = () => {
  return document.getElementById('save-box')
}

const showRemind  = ref<boolean>(false)
const remind = ref<boolean>(false)
const animateRemind= () => {
    remind.value = true;
      setTimeout(() => {
        remind.value = false;
      }, 1000);
    }

const changeChecked = (checkedValue: object) => {
  
  showRemind.value = !checkedValue.target.checked
}

interface FormState {
  checked: boolean;
}

const formChecked: UnwrapRef<FormState> = ref({
  checked: false,
});

// 提交申请单
const {
  data: applyCreatData,
  run: applyCreatiRun,
  loading: applyCreatLoading,
} = useRequest(tripApi.applyCreat, {
  defaultParams: [creatTripParma.value],
});
// 修改申请单
const {
  data: applyUpdateData,
  run: applyUpdateiRun,
  loading: applyUpdateLoading,
} = useRequest(tripApi.applyUpdate, {
  defaultParams: [creatTripParma.value],
});
const checkedForm = ref();
const baseInfoCom = ref();
const planBudgeRef = ref();
const businessList = import.meta.env.VITE_BUSINESS_INDEX_URL;
const tripUrl = import.meta.env.VITE_BUSINESS_TRIP_URL;



// 


const goToDetail = () => {
  const url = businessList + '#' + '/card-order/trip';
  window.open(url,"_self");
}

// 展示隐私政策
const yszcDialog = ref(false)
const showYszc = () => {
  yszcDialog.value = true;
}

const handleOk = () => {
  yszcDialog.value = false;
  formChecked.value.checked = true
  showRemind.value = false
}

// 查询城市列表
const cityOptions = ref({});

// 保存申请单
const lastSubmitTime = ref<string>();
const applySubmit = () => {
  // 判断是否已经自动保存 如果以保存走修改
  if (creatTripParma.value.id) {
    tripApi.applyUpdate(creatTripParma.value).then((res) => {
      lastSubmitTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
    });
  } else {
    tripApi.applyCreat(creatTripParma.value).then((res) => {
      lastSubmitTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
      creatTripParma.value.id = res.id;
      creatTripParma.value.applyNo = res.applyNo;
      creatTripParma.value.gmtCreate = res.gmtCreate;
      
    });
  }
  hMessage.success('保存成功!');

};
const timer = ref();
const applyId = route.value?.query?.id;

onMounted(async () => {
  // 如果存在id 是修改操作
  if (applyId) {
    creatTripParma.value = await tripApi.queryDetailByApplyNo(applyId);
    cityOptions.value = await tripApi.district();
    // 数据回显处理
    
    creatTripParma.value.travelUserName = creatTripParma.value.travelerList.filter(
      (item) => item.mainFlag == '1',
    )[0].travelUserName;

    // 根据登陆人初始化数据
    baseInfoCom.value.chosedPerson = creatTripParma.value.travelerList?.filter((item) => item.travelUserType == '0' && item.mainFlag == '1')[0];
    baseInfoCom.value.chosedInPersenList = creatTripParma.value.travelerList?.filter((item) => item.travelUserType == '0' && item.mainFlag != '1');

    creatTripParma.value.outPersonId = [];
    creatTripParma.value.travelerList.forEach((item) => {
      if (item.travelUserType == '1') {
        creatTripParma.value.outPersonId.push(item.travelUserSyId);
      }

      item.personIdList = [];
      item.personIdList = [...item.personIdList, item.travelUserSyId];
    });

    creatTripParma.value?.tripList?.forEach((item, index) => {
    if (index == 0) {
      planBudgeRef.value.cityList.push({
        cityCode: item.beginCityCode,
        city: item.beginCityName,
        syId: item.beginCityCodeSy,
        date: item.beginDate,
      });
    }
    planBudgeRef.value.cityList.push({
      cityCode: item.endCityCode,
      city: item.endCityName,
      syId: item.endCityCodeSy,
      date: item.endDate,
    });

    item?.tripDetailMapList?.forEach((tripMap) => {
      tripMap.personIdList = tripMap.travelApplyTripDetailList.map((tt) => tt.travelUserSyId);
    });
  });

    creatTripParma.value?.fileList?.forEach((file) => {
      file.name = file.fileName;
      file.thumbUrl = file.filePath;
    });
    if(!creatTripParma.value.haierBudgetPayOccupyRequest) {
      creatTripParma.value.haierBudgetPayOccupyRequest = {}
    }
  }

  // 两分钟自动保存
  // timer.value = setInterval(() => {
  //   applySubmit();
  // }, 1000 * 60 * 2);
});

onUnmounted(() => {
  clearInterval(timer.value);
});
// 行程重复
const repeatDialog = ref<boolean>(false)
const confirmLoading = ref<boolean>(false)
const conflictRecords = ref<any>([])

// 提交申请单
// 是否确认 flag
// 添加时间验证,如果时间验证重复弹窗让用户确认,确认后再次提交
const addApplyForm = () => {
  confirmVisable.value = false
  spinning.value = true

  creatTripParma.value.conflictConfirmed = 0

  tripApi.applySubmit(creatTripParma.value).then((res) => {
    clearInterval(timer.value);

    // 如果有异常数据 展示确认弹窗
    if(res.conflictRecords) {
      repeatDialog.value = true
      conflictRecords.value = JSON.parse(res.conflictRecords)
    }else {
      // 提交后弹出流程弹窗
      spinning.value = false
      // 如果是自费则不需要展示审批页
      if(creatTripParma.value.travelReserveFlag == 1) {
        processCode.value = res.processCode
        showProcess.value = true
      }else {
        goToOrderList()
      }
    }

  }).catch(err => {
    spinning.value = false
  })
};

const processCode = ref<string>("")

// 确认有重复的行程数据然后提交
const confirmRepeat = () => {
  confirmLoading.value = true
  spinning.value = true
  creatTripParma.value.conflictConfirmed = 1
  tripApi.applySubmit(creatTripParma.value).then((res) => {
     // 提交后弹出流程弹窗
    spinning.value = false
    confirmLoading.value = false
    repeatDialog.value = false

    // 如果是自费则不需要展示审批页
    if(creatTripParma.value.travelReserveFlag == 1) {
      processCode.value = res.processCode
      showProcess.value = true
    }else {
      goToOrderList()
    }


  }).catch(err => {
    spinning.value = false
    confirmLoading.value = false
    repeatDialog.value = false
  })
}

const cancelRepeat = () => {
  spinning.value = false
  repeatDialog.value = false
}

// 审批弹窗
const showProcess = ref(false)

const confirmVisable= ref<boolean>(false)

const goToOrderList = () => {
  const url = businessList + '#' + '/card-order/trip';
  window.open(url,"_self");
}

const beforeSubmit = async() => {

  // 提交前表单验证
  baseInfoCom.value
    .onSubmit()
    .then( async () => {
      if (formChecked.value.checked) {
        // 如果自费不用验证费用
        if (creatTripParma?.value.travelReserveFlag) {
          let planRes = await planBudgeRef.value.onSubmit();
          if (!planRes) {
            document?.getElementById('plan-budget')?.scrollIntoView();
            return;
          }
          
          let isEditMoney = planBudgeRef.value.isEditPlaneMoney();
          if(isEditMoney) {
            confirmVisable.value=  true
            return
          }
        }

        addApplyForm()

      } else {
        showRemind.value = true
        animateRemind()
        document?.getElementById('affix-bottom')?.scrollIntoView();
      }
    })
    .catch(() => {
      document?.getElementById('base-info')?.scrollIntoView();
    });

  
}
const cancel = () => {
  confirmVisable.value=  false
}

const chosedBudget = (val:any) => {
  creatTripParma.value.haierBudgetPayOccupyRequest = val
}
const spinning = ref<boolean>(false)
const changeSpinning = (val: boolean) => {
  spinning.value= val
}
</script>

<template>
  <h-spin size="large" style="min-height: 100vh; position:absolute; z-index: 9999;" class="loading-box flex-center"  :spinning="spinning">
    <div class="container">
      <div class="row flex">
        <div class="change-title" v-if="creatTripParma.applyNo">
          <h-row class="mb-10">
            <h-col :span="6" style="color: #00000073">申请单号:</h-col>
            <h-col :span="18">{{ creatTripParma.applyNo }}</h-col>
          </h-row>
          <h-row class="mb-10">
            <h-col :span="6" style="color: #00000073">申请时间:</h-col>
            <h-col :span="18">{{ creatTripParma.gmtCreate }}</h-col>
          </h-row>
        
        </div>

        <div class="main-title">
          <!-- <img src="../../assets/image/trip/title.png" alt="" /> -->
          <span>出差申请单</span>
        </div>
        <div class="apply-con flex">
          <!-- 驳回原因 -->
          <!-- status=10 && auditStatus = 40 && reatTripParma?.workFlowFailInfo -->
          <div class="whole-line reject" v-if="creatTripParma?.workFlowFailInfo && creatTripParma?.status == 10 && creatTripParma?.auditStatus == 40" >
            <h-row >
              <h-col :span="2">驳回原因:</h-col>
              <a-tooltip>
                <template #title>{{creatTripParma?.workFlowFailInfo }}</template>
                <h-col :span="20">{{ creatTripParma?.workFlowFailInfo }}</h-col>
              </a-tooltip>
            </h-row>
          </div>
          <div class="reject-bg"  v-if="creatTripParma?.workFlowFailInfo && creatTripParma?.status == 10 && creatTripParma?.auditStatus == 40"></div>
          <!-- 基本信息 -->
          <base-info
            id="base-info"
            ref="baseInfoCom"
            :creatTripParma="creatTripParma"
            @outPersonOpen="outPersonOpen"
            @showStandardOpen="openTravelStandardslModal"
          ></base-info>
          <!-- 行程与费用 -->
          <div class="whole-line">
            <plan-budge
              id="plan-budget"
              ref="planBudgeRef"
              @loading="changeSpinning"
              :creatTripParma="creatTripParma"
              @openBudgetModal="openBudgetModal"
              @showStandardOpen="openTravelStandardslModal"
              @showBudgetDetailModal="(data) => openBudgetDetailModal(data)"
            ></plan-budge>
          </div>
          <!-- 附件 -->
          <div id="file" class="whole-line block-con">
            <file-upload :creatTripParma="creatTripParma"></file-upload>
          </div>
        </div>
        <div class="anchor-con flex">
          <h-anchor :items="anchorItems" @click="handleClick" />
        </div>

        <!-- 差旅标准弹窗 -->
        <travel-standards-modal ref="travelStandards" />

        <!--新增外部联系人 -->
        <out-person-modal @getOutPersonList="getOutPersonList" ref="outPerson" />

        <!-- 预算归属弹窗 -->
        <budget-modal ref="budget" :source="creatTripParma?.originApp" :haierBudgetPayOccupyRequest="creatTripParma?.haierBudgetPayOccupyRequest" @chosed="chosedBudget"/>

        <!-- 预算明细弹窗 -->
        <budget-detail-modal :tableData="budgetDetailData" ref="budgetDetail" />

        <h-modal :width="1000"  :keyboard="false" :closable='true' @cancel="goToOrderList" :maskClosable="false"  v-model:open="showProcess"  title="审批流程" >
          <approval-process :processCode="processCode" ></approval-process>
          <template #footer>
            <h-button key="submit" type="primary" @click="goToOrderList">确定</h-button>
          </template>
        </h-modal>

      </div>
      <a-affix :offset-bottom="0" id="affix-bottom" class="affix-bottom">
        <div class="save-box flex" id="save-box">
          <div class="box-center">
            <div class="save-box-left font-size-14">
              <h-checkbox class="font-size-14 flex-center" v-model:checked="formChecked.checked" @change="changeChecked" required name="date1">
                <span class="font-size-14 font-color">已阅读并同意</span>
                <h-button type="link" @click.stop="showYszc">乘机提醒/商旅系统隐私政策</h-button>
                <!-- <span class="primary-color font-size-14" @click.stop=""></span> -->
              </h-checkbox>
              <span v-if="showRemind" :class="remind ? 'animate__animated animate__shakeX' : ''" class=" font-size-12 error-text">请阅读并勾选系统隐私政策</span>
            </div>
            <div class="save-box-right flex">
              <div class="auto-text font-size-14 mr-20 font-color" v-if="lastSubmitTime">
                <check-circle-two-tone two-tone-color="#52c41a" />
                {{ dayjs(lastSubmitTime).fromNow() }}自动保存
              </div>
              <div class="save-btns">
                <h-popconfirm
                  title="确定取消编辑并返回列表页吗?"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="goToDetail"
                >
                  <h-button size="small" class="my-button  mr-10">取消</h-button>
                </h-popconfirm>
                <h-button size="small" class="my-button  mr-10" @click="applySubmit">保存</h-button>

                <a-popconfirm :autoAdjustOverflow='false' :getPopupContainer="getParent" trigger="click"  title="费用预算金额可根据实际需求调整!" :arrowPointAtCenter="true" :open="confirmVisable" ok-text="继续提交" cancel-text="返回修改"  @confirm="addApplyForm" @cancel="cancel">
                  <h-button size="small" class="my-button my-submit-button" type="primary"  @click="beforeSubmit">提交</h-button>
                </a-popconfirm>
              </div>
            </div>
          </div>
        </div>
      </a-affix>
      
    </div>

    <h-modal v-model:open="yszcDialog" title="乘机提醒/商旅系统隐私政策" @ok="handleOk">
      <p style="text-align: center;">文明乘机提醒</p>
      <p style="text-indent: 3ch;">在航空器上强占座位、辱骂殴打他人、妨碍机组正常履行职责、霸占航空器、破坏机上设施设备等行为，扰乱公共秩序、危害公共安全。构成违反治安管理行为的，公安机关将依法进行处罚；情节严重的，可能被追究刑事责任，请您遵规守法、文明乘机！</p>
      <p style="text-align: center;">随身行李告知</p>
      <p style="text-indent: 3ch;">为确保飞行安全和航班准点运行，请您携带符合标准的随身行李乘机（随身行李限额为：头等舱旅客限带2件，每件不得超过10公斤；豪华公务舱、公务舱/超级经济舱、经济舱旅客限带1件，每件不得超过8公斤；国内航班每件行李体积不得超过20cm×40cm×55cm；国际/地区航班每件行李体积不得超过25cm×45cm×56cm且三边之和小于等于115cm。） 超过规定的随身行李需重新安排安检及托运，将导致行李无法与您同机抵达，由此产生的相关损失将由您自行承担。请您在乘机前再次确认携带的随身行李符合件数、重量和尺寸标准，感谢您的支持和配合，祝您旅途愉快！</p>

      <div>
        <ExclamationCircleOutlined />
        我已阅读并同意<h-button type="link" @click.stop="showPdf">商旅系统隐私政策.pdf</h-button>

      </div>
    </h-modal>


    <h-modal :z-index="99999" v-model:open="repeatDialog" :maskClosable="false" title="行程重复提醒" cancelText="重新修改" okText="继续提交" :confirm-loading="confirmLoading" @ok="confirmRepeat" @cancel="cancelRepeat">
      <p style="text-align: left;">在同一时间段您有多个相同的行程,请您确认后再提交!</p>

      <div style="max-height: 400px; overflow-y: scroll;">
        <p  v-for="item,index in conflictRecords" :key="index">{{ `${index + 1 }、${item}` }}</p>
      </div>

    </h-modal>


  </h-spin>
</template>

<style lang="less" scoped>
@import url(./components/trip.less);

.change-title {
  position: absolute;
  right: 0;
  top: -80px;
  width: 300px;
  
  .ant-col-6 , .ant-col-18{
    font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #000000d9;
  }
}
.loading-box {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}
</style>