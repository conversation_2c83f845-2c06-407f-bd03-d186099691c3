// 缓存城市数据用
import { defineStore } from "pinia"
import { CityOption } from '@haierbusiness-front/common-libs';


type CityState = {
    city: Array<CityOption>,
}

export const useCityStore = defineStore("city", {
    state: (): CityState => {
        return {
            city: [],
        }
    },
    actions: {
        setDate(city: CityState["city"]) {
            this.city = city;
        },
    }
})