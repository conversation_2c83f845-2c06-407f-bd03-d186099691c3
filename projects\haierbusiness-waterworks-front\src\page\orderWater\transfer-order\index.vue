<script setup lang="ts">
import { Empty, Button as hButton, Input as hInput, message } from 'ant-design-vue';
import { computed, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { SearchOutlined } from '@ant-design/icons-vue';
const router = useRouter();
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE; // 空状态

// 订单状态映射
const orderStatusMap = {
  10: '待配送',
  20: '待收货',
  30: '待评价',
};

const orderList = ref([
  {
    id: 1,
    name: '5加仑',
    price: 35,
    count: 1,
    status: '10',
    orderTime: '2025-02-24 12:12:54',
    orderNo: 'SS51132489580',
    applyNo: 'WT51132489580',
    applyDepartment: '商旅小微',
    settlementCompany: '青岛国际旅行社有限公司',
  },
]);

/**
 * 筛选相关 -- 顶部筛选
 * */
const activeKey = ref<string>(''); // 筛选
const searchValue = ref<string>(''); // 搜索框

/**
 * 分页相关逻辑
 * */
const current1 = ref(1);
const total = ref(1);
const handleTableChange = (page: number, pageSize: number) => {
  console.log(page, pageSize);
};

/**
 * 表单操作按钮相关
 * */
const handleCancelOrder = (orderId: number) => {
  message.success('取消订单' + orderId);
  console.log('取消订单', orderId);
};
const handleReceiveOrder = (orderId: number) => {
  message.success('收货' + orderId);
  console.log('收货', orderId);
};
const handleEvaluateOrder = (orderId: number) => {
  message.success('去评价' + orderId);
  console.log('去评价', orderId);
};
const handleViewOrder = (orderId: number) => {
  message.success('查看订单' + orderId);
  console.log('查看订单', orderId);
};

/**
 * 初始化逻辑
 * */
// 临时测试用 -- 待删
const tempData = [
  {
    id: 1,
    name: '5加仑',
    price: 35,
    count: 1,
    status: '10',
    orderTime: '2025-02-24 12:12:54',
    orderNo: 'SS51132489580',
    applyNo: 'WT51132489580',
    applyDepartment: '商旅小微',
    settlementCompany: '青岛国际旅行社有限公司',
  },
  {
    id: 2,
    name: '330ml瓶装水',
    price: 25,
    count: 1,
    status: '20',
    orderTime: '2025-02-24 12:12:54',
    orderNo: 'SS51132489580',
    applyNo: 'WT51132489580',
    applyDepartment: '商旅小微',
    settlementCompany: '青岛国际旅行社有限公司',
  },
  {
    id: 3,
    name: '2加仑',
    price: 15,
    count: 1,
    status: '30',
    orderTime: '2025-06-1 8:30:54',
    orderNo: 'SS51132489580',
    applyNo: 'WT51132489580',
    applyDepartment: '集团IT',
    settlementCompany: '青岛国际旅行社有限公司',
  },
];

const handleSearch = (page?: number, pageSize?: number) => {
  const condition = {
    page: page || 1,
    pageSize: pageSize || 10,
    keyWord: searchValue.value,
    state: activeKey.value,
  };
  console.log('搜索条件', condition);
  total.value = 500;
  orderList.value = tempData;
};

onMounted(() => {
  handleSearch();
});

watch(activeKey, () => {
  handleSearch();
});
</script>

<template>
  <div class="transfer-order">
    <a-breadcrumb class="breadcrumb">
      <a-breadcrumb-item @click="router.push('/')"><a>首页</a></a-breadcrumb-item>
      <a-breadcrumb-item><a>个人中心</a></a-breadcrumb-item>
      <a-breadcrumb-item>送水单</a-breadcrumb-item>
    </a-breadcrumb>

    <div class="top-control">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="" tab="全部"></a-tab-pane>
        <a-tab-pane key="10" tab="待配送"></a-tab-pane>
        <a-tab-pane key="20" tab="待收货"></a-tab-pane>
        <a-tab-pane key="30" tab="待评价"></a-tab-pane>
        <a-tab-pane key="40" tab="已完成"></a-tab-pane>
      </a-tabs>
      <div>
        <h-input
          class="search-input"
          v-model:value="searchValue"
          @keydown.enter="() => handleSearch()"
          @blur="() => handleSearch()"
          placeholder="请输入订单号/商品名称搜索"
        >
          <template #suffix>
            <search-outlined style="color: rgba(0, 0, 0, 0.45)" />
          </template>
        </h-input>
      </div>
    </div>

    <div class="table">
      <div class="tr tr-border" style="border: 1px solid #8cbbec">
        <div class="th flex-3" style="justify-content: center">商品信息</div>
        <div class="th flex-1">单价（元）</div>
        <div class="th flex-1">数量</div>
        <div class="th flex-1">金额（元）</div>
        <div class="th flex-1">订单状态</div>
        <div class="th flex-1" style="justify-content: center">操作</div>
      </div>
      <template v-if="orderList.length > 0">
        <div class="row" v-for="(item, index) in orderList" :key="index">
          <div class="tr tr-border" style="width: 100%">
            <div class="td flex-auto table-item-header">{{ item.orderTime }}</div>
            <div class="td flex-auto table-item-header"><span>送水单：</span>{{ item.orderNo }}</div>
            <div class="td flex-auto table-item-header"><span>申请单：</span>{{ item.applyNo }}</div>
            <div class="td flex-auto table-item-header"><span>申请部门：</span>{{ item.applyDepartment }}</div>
            <div class="td flex-auto table-item-header"><span>结算公司：</span>{{ item.settlementCompany }}</div>
            <!-- 空撑起宽度 -->
            <div class="td flex-1"></div>
            <div class="td flex-1"></div>
          </div>
          <div class="tr">
            <!-- 商品信息 -->
            <div class="td flex-3">
              <img src="../../../assets/image/demo/yszy.png" alt="" />
              <span style="margin-left: 16px">{{ item.name }}</span>
            </div>
            <!-- 单价 -->
            <div class="td flex-1">{{ item.price }}</div>
            <!-- 数量 -->
            <div class="td flex-1 number-controls">{{ item.count }}</div>
            <!-- 金额 -->
            <div class="td flex-1 text-blue">￥ {{ item.price * item.count }}</div>
            <!-- 订单状态 -->
            <div class="td flex-1">{{ orderStatusMap[item.status as unknown as keyof typeof orderStatusMap] }}</div>
            <!-- 操作 -->
            <div class="td flex-1 border-left" style="display: flex; flex-direction: column; justify-content: center">
              <template v-if="item.status === '10'">
                <h-button type="link" @click="handleCancelOrder(item.id)">取消订单</h-button>
              </template>
              <template v-else-if="item.status === '20'">
                <h-button type="primary" @click="handleReceiveOrder(item.id)">收货</h-button>
                <h-button type="link" @click="handleViewOrder(item.id)">查看订单</h-button>
              </template>
              <template v-else-if="item.status === '30'">
                <h-button @click="handleEvaluateOrder(item.id)">去评价</h-button>
              </template>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <a-empty :image="simpleImage" />
      </template>

      <!-- 分页 -->
      <div v-if="orderList.length > 0" style="margin-bottom: 24px">
        <a-pagination v-model:current="current1" show-quick-jumper :total="total" @change="handleTableChange" />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.transfer-order {
  width: 100%;
  height: 100%;

  .breadcrumb {
    margin-bottom: 16px;
    :deep(.a-breadcrumb-item) {
      font-size: 14px;
    }
  }
  .top-control {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    .search-input {
      width: 320px;
    }
    // 去除底部外边距
    :deep(.ant-tabs-nav) {
      margin-bottom: 0;
    }
    // 隐藏tabs底部的边
    :deep(.ant-tabs-nav::before) {
      border-bottom: none !important;
    }
  }

  .table {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
    .tr-border {
      background-color: #f2f9ff;
    }
    .table-item-header {
      font-size: 12px;
      span {
        color: #909399;
      }
    }

    .row {
      width: 100%;
      display: flex;
      flex-direction: column;
      border: 1px solid #8cbbec;
    }
    .tr {
      width: 100%;
      display: flex;
      padding: 12px;
    }

    .th {
      display: flex;
      align-items: center;
      font-size: 14px;
    }
    .td {
      display: flex;
      align-items: center;

      font-size: 13px;
      img {
        width: 100px;
        height: 100px;
        object-fit: cover;
      }
    }
    .border-left {
      border-left: 1px solid #eef2f7;
    }
    .number-controls {
      gap: 8px;
      .decrease-btn,
      .increase-btn {
        height: 30px;
        width: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      :deep(.ant-input-number-input) {
        text-align: center;
        width: 80px;
      }
    }
    .bottom-info {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 48px;
    }

    .submit-btn {
      width: 100px;
      height: 100%;
      align-items: center;
      text-align: center;
    }
  }
}
.text-blue {
  color: #2a82db;
}
.flex-auto {
  flex: auto;
}
.flex-1 {
  flex: 1;
}
.flex-2 {
  flex: 2;
}
.flex-3 {
  flex: 3;
}
.center {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
