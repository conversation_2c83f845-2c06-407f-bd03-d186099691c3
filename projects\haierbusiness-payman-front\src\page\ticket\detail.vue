<script lang="ts" setup>
import { computed, createVNode, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';


import {
  Anchor as hAnchor,
  <PERSON><PERSON> as hButton,
  Spin as hSpin,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Checkbox as hCheckbox,
  Form as hForm,
  FormItem as hFormItem,
  Row as hRow,
  Col as hCol,
  Popconfirm as hPopconfirm,
  Upload as hUpload,
  Input as hInput,
  InputNumber as hInputNumber,
  Modal as hModal,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem,
  message
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  DownloadOutlined,
  VerticalAlignBottomOutlined,
  ExclamationCircleFilled,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  SendOutlined,
  PhoneOutlined,
  VerticalAlignTopOutlined,
  DeleteOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { download, tripApi, teamListApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import relativeTime from 'dayjs/plugin/relativeTime';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';

import { InvoicingApi,fileApi } from '@haierbusiness-front/apis';
import type { TableColumnType } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import type { UploadProps } from 'ant-design-vue';


const route = ref(getCurrentRoute());
const router = useRouter();

const id = route.value?.query?.id;
const change = route.value?.query?.change;
const downLoadLoading = ref<boolean>(false)
const uploadModalShow = ref<boolean>(false)
const uploadLoading = ref<boolean>(false)
const confirmLoading = ref<boolean>(false)
const data = ref<any>({
  rechargeId:id
})

dayjs.extend(relativeTime);

const store = applicationStore();
const { loginUser } = storeToRefs(store); 
const detail = ref<any>({})

const getInfo=()=>[
  InvoicingApi.info({id}).then((res:any)=>{
    detail.value = res
    if(res.billResponse){
      data.value = res.billResponse
      data.value.rechargeId = id
    }
  })
]

// 修改或者保存 
const save = ()=>{
  if(!data.value.productName){
    message.warning('请输入商品名称')
    return
  }
  if(!data.value.taxCode){
    message.warning('请输入税收分类编码')
    return
  }
  if(!data.value.rate&&data.value.rate!=0){
    message.warning('请输入税率')
    return
  }
  if(!data.value.num&&data.value.rate!=0){
    message.warning('请输入商品数量')
    return
  }
  if(!data.value.unitPrice&&data.value.rate!=0){
    message.warning('请输入商品单价')
    return
  }
  if(!data.value.standards){
    message.warning('请输入商品规格')
    return
  }
  if(!data.value.saleUnit&&data.value.rate!=0){
    message.warning('请输入售卖单位')
    return
  }
  if((data.value.num * data.value.unitPrice)!=detail.value.budgetAmount){
    message.warning('商品总价需要与充值金额一致')
    return
  }
  data.value.amountSum = data.value.num * data.value.unitPrice
  InvoicingApi.saveOrUpdate(data.value).then(res=>{
    message.success('保存成功')
    router.push({ path: "/payman/ticket" })
  })
}
const goBack=()=>{
  router.push({ path: "/payman/ticket" })
}

const getStatus=(status:string)=>{
  if(status==0){
    return '待完善'
  }else if(status==10){
    return '待汇总'
  }else if(status==20){
    return '已汇总'
  }else if(status==30){
    return '取消汇总'
  }else if(status==40){
    return '已确认'
  }else if(status==50){
    return '已结算'
  }
}

const getrechangeStatus=(status:number)=>{
  if(status==10){
    return '待提交'
  }else if(status==20){
    return '已提交'
  }else if(status==30){
    return '支付中'
  }else if(status==40){
    return '已关闭'
  }else if(status==90){
    return '已完成'
  }
}

const fileList = ref<any>()

const uploadPeople = (options: any) => {
  console.log('🚀 ~ uploadPeople ~ options:', options);
  // uploadLoading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);
  InvoicingApi
    .InvoicingImport(formData)
    .then((it) => {
      data.value = it
      data.value.rechargeId = id
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .catch((e) => {
      console.error('🚀 ~ .catch ~ e:', e);
      options.onCancel;
      // teamForm.value.fileList = [];
    })
    .finally(() => {
      // uploadLoading.value = false;
    });
};
// 下载模版
const downloadTemp = () =>{
  downLoadLoading.value = true
  InvoicingApi.downloadTemplate().then(res=>{
    console.log(res)
    downLoadLoading.value = false
  })
}

const handleRemove: UploadProps['onRemove'] = file => {
  const index = fileList.value.indexOf(file);
  const newFileList = fileList.value.slice();
  newFileList.splice(index, 1);
  fileList.value = newFileList;
};

const beforeUpload: UploadProps['beforeUpload'] = (file:any) => {
  console.log(file)
  if (!(file.type === 'application/pdf' || file.type === 'application/PDF')) {
   message.warning('只能上传pdf文件');
   return hUpload.LIST_IGNORE;
 }
 const isLt5M = file.size / 1024 / 1024 < 3;
  if (!isLt5M) {
    hMessage.error('文件大小不能超过3M');
    return hUpload.LIST_IGNORE;
  }
  return true;
}

const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const upload = (options: any) => {
  uploadLoading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);
  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path
      options.file.fileName = options.file.name
      options.file.url = baseUrl + it.path
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

const uploadModalOpen =()=>{
  fileList.value=[]
  uploadModalShow.value = true
}

const handleOk = () =>{
  console.log(fileList.value)
  if(fileList.value&&fileList.value.length&&fileList.value[0].filePath){
    confirmLoading.value = true
    const data = {
      rechargeId:id,
      fileName:fileList.value[0].fileName,
      url:fileList.value[0].filePath
    }
    InvoicingApi.fileUpdate(data).then(res => {
      message.success("替换成功!");
      confirmLoading.value = false
      uploadModalShow.value = false
      getInfo();
    }).catch(()=>{
      confirmLoading.value = false
    })
  }else{
    message.warning('请上传方案报告');
  }
}


const getrechangeBusinessFlag = (status:number | string)=>{
  if(status==1){
    return '是'
  }else if(status==0){
    return '否'
  }else {
    return ''
  }
}

onMounted(()=>{
  // 获取详情
  getInfo()
})


</script>


<template>
  <div class="container">
    <div class="row">
      <div class="apply-con">

        <h-descriptions title="订单详情" class="mb-10" >
          <h-descriptions-item :label-style="{width: '100px'}" label="订单编号">{{detail.orderCode }}</h-descriptions-item>
          <!-- <h-descriptions-item :label-style="{width: '100px'}" label="流水单号">18100000001810000000</h-descriptions-item> -->
          <h-descriptions-item :label-style="{width: '100px'}" label="平台支付单号">{{detail.payCode }}</h-descriptions-item>
          <h-descriptions-item :label-style="{width: '100px'}" label="申请人">{{detail.applyName }}</h-descriptions-item>
          <h-descriptions-item :label-style="{width: '100px'}" label="申请时间">
            {{detail.applyTime }}
          </h-descriptions-item>
          <h-descriptions-item :label-style="{width: '100px'}" label="充值金额">{{detail.budgetAmount }}元</h-descriptions-item>
          <h-descriptions-item v-if="data.createName" :label-style="{width: '100px'}" label="提交人">{{data.createName }}</h-descriptions-item>
          <h-descriptions-item :label-style="{width: '100px'}" label="提交时间">{{detail.gmtCreate }}</h-descriptions-item>
          <h-descriptions-item :label-style="{width: '100px'}" label="结算状态">{{ getStatus(detail.balanceStatus) }}</h-descriptions-item>
          <h-descriptions-item :label-style="{width: '100px'}" label="订单状态">{{ getrechangeStatus(detail.status)  }}</h-descriptions-item>
          <h-descriptions-item :label-style="{width: '100px'}" label="是否商旅">{{ getrechangeBusinessFlag(detail.businessFlag)  }}</h-descriptions-item>

          <h-descriptions-item :label-style="{width: '100px'}" label="费用科目">{{detail.feeItemName }}</h-descriptions-item>
          <h-descriptions-item :label-style="{width: '100px'}" label="预算部门">{{detail.budgetDepartmentName }}</h-descriptions-item>
          <h-descriptions-item :label-style="{width: '100px'}" label="方案报告">
            <a :href="detail.fileUrl">{{detail.fileName}}</a>
            <h-button @click="uploadModalOpen" size="small" style="margin-left:16px;">替换</h-button> 
          </h-descriptions-item>

        </h-descriptions>
        <div v-if="change==1" class="buttonBox">
          <a-button type="primary" class="mr-20" :loading="downLoadLoading" @click="downloadTemp()">
                  <template #icon><VerticalAlignBottomOutlined /></template>
                  下载模板
          </a-button>
          <h-form-item-rest>
            <h-upload name="file" :custom-request="uploadPeople" :max-count="1" accept=".xls,.xlsx">
              <h-button type="primary" class="mr-20" :loading="ticketBtnLoading">
                <VerticalAlignTopOutlined />
                <span class="font-size-14">数据导入</span>
              </h-button>
            </h-upload>
          </h-form-item-rest>
        </div>
        <h-descriptions layout="vertical" class="mb-10" bordered :column="7" size="small">
          <h-descriptions-item label="商品名称">
            <h-input :disabled="change==0" v-model:value="data.productName"></h-input>
          </h-descriptions-item>
          <h-descriptions-item label="税收分类编码"><h-input :disabled="change==0"  v-model:value="data.taxCode"></h-input></h-descriptions-item>
          <h-descriptions-item label="税率"><a-input-number :disabled="change==0" :max="10" :min="0"  precision="2" v-model:value="data.rate"></a-input-number></h-descriptions-item>
          <h-descriptions-item label="数量" >
            <a-input-number :disabled="change==0"  :precision="0"  :min="0" v-model:value="data.num"></a-input-number>
          </h-descriptions-item>
          <h-descriptions-item label="商品单价(含税)"><a-input-number :precision="2" :disabled="change==0"  v-model:value="data.unitPrice"></a-input-number></h-descriptions-item>
          <h-descriptions-item label="规格"><h-input :disabled="change==0"  v-model:value="data.standards"></h-input></h-descriptions-item>
          <h-descriptions-item label="售卖单位"><h-input :disabled="change==0"  v-model:value="data.saleUnit"></h-input></h-descriptions-item>
        </h-descriptions>

        <h-row style="justify-content: flex-end;" class="mt-10">
          <h-col v-if="data.unitPrice&&data.num">
            商品总价:{{data.num * data.unitPrice}}元
            <p style="margin-top:5px;color:red;" v-if="change==1&&(data.num * data.unitPrice)!=detail.budgetAmount">商品总价需要与充值金额一致</p>
          </h-col>
        </h-row>
        <h-row v-if="change==1" style="justify-content: center;margin-top:60px;" class="mt-60">
          <h-button @click="save" type="primary" >
            完成
          </h-button>
          <h-button @click="goBack" style="margin-left:50px;">
            取消
          </h-button>
        </h-row>
      </div>
    </div>
    <!-- 彈窗 -->
    <h-modal v-model:open="uploadModalShow"  :confirm-loading="confirmLoading" title="替换方案报告" @ok="handleOk">
      <h-form
        style="width:400px;"
        layout="vertical"
        name="basic"
        :label-col="{ span: 2 }"
        :wrapper-col="{ span: 22 }"
        autocomplete="off"
      >
        <h-form-item label="">
          <h-upload style="width:50%;" v-model:fileList="fileList"  :custom-request="upload" :max-count="1" :accept="'.pdf,.PDF'" :before-upload="beforeUpload" @remove="handleRemove">
            <h-button>
              <upload-outlined></upload-outlined>
              上传方案报告
            </h-button>
          </h-upload>
        </h-form-item>
      </h-form>

    </h-modal>
  </div>
</template>

<style lang="less" scoped>
@import url(../recharge/recharge.less);
  .container{
    width: 100%;
    // height: 100vh;
    background: #fff;
    .row{
      width: 100%;
      margin-top: 0;
      .apply-con{
        width: 100%;
        padding-top:20px;
        height: calc(100vh - 200px);
      }
    }
}
.buttonBox{
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}
.change-title {
  position: absolute;
  right: 0;
  top: -80px;
  width: 300px;

  .ant-col-6,
  .ant-col-18 {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #000000d9;
  }
}

.mt-5 {
  margin-top: 5px;
}

.ml-5 {
  margin-left: 5px;
}

.color-eee {
  color: #a8a7a7;
}

.com-box {
  background: #f5f5f5;
  padding: 20px;
  margin-top: 20px;

  .whole-line {
    height: 40px;
    font-weight: 600;
  }

  .info-box {
    display: flex;
    align-items: flex-start;
    padding: 10px 20px;
    border: 1px solid #ffdb5c;
    background: #fffbd8;
  }

  :deep(.ant-form-item-control-input-content) {
    display: flex;
    align-items: center;
  }
}

.title {
  height: 60px;
}

.download-btn {
  color: #408cff;
  cursor: pointer;
}

:deep(.city-chose-box) {
  width: 200px !important;
}

:deep(.mr-10) {
  margin-right: 10px;
}

.background-eee {
  padding: 10px;
  background: #f4f4f4;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>