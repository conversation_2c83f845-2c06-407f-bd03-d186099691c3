<template>
  <div class="mobile-box">
    <div class="tipBox">
      <img :src="ling" alt />
      <span>发布带物需求，为您匹配顺路同事，协助携带物品</span>
    </div>
    <van-form label-width="100px" @submit="onSubmit">
      <!-- 基本信息 -->
      <van-cell-group inset title="基本信息">
        <van-field
          required
          label="电话"
          v-model="formState.creatorPhone"
          :rules="[{ required: true, message: '请输入电话' }]"
          label-align="left"
          input-align="right"
          placeholder="联系电话"
        ></van-field>
        <van-field
          label-width="120px"
          autocomplete="off"
          required
          readonly
          input-align="right"
          error-message-align="right"
          :rules="[{ required: true, message: '请选择起始时间' }]"
          placeholder="请选择起始时间"
          @click="openTimePicker('begin')"
          v-model="formState.expectTimeFrom"
        >
          <template #label>
            <div>期望送达时间</div>
          </template>

          <template #input>
            <div v-if="formState?.expectTimeFrom">
              {{ formState?.expectTimeFrom?formState?.expectTimeFrom.slice(0, -3):"" }}
              <van-icon
                v-if="formState?.expectTimeFrom"
                name="cross"
                color="#ee0a24"
                @click.stop="formState.expectTimeFrom = ''"
              />
            </div>
            <div v-else style="color:#c9cacd">请选择起始时间</div>
          </template>
        </van-field>
        <van-field
          autocomplete="off"
          required
          readonly
          input-align="right"
          error-message-align="right"
          label
          :rules="[{ required: true, message: '请选择截止时间' }, { validator: endTimeValidator, message: '截止时间不能早于起始时间!' }]"
          placeholder="请选择截止时间"
          @click="openTimePicker('end')"
          v-model="formState.expectTimeTo"
        >
          <template #input>
            <div v-if="formState?.expectTimeTo">
              {{ formState?.expectTimeTo?formState?.expectTimeTo.slice(0, -3):"" }}
              <van-icon
                v-if="formState?.expectTimeTo"
                name="cross"
                color="#ee0a24"
                @click.stop="formState.expectTimeTo = ''"
              />
            </div>
            <div v-else style="color:#c9cacd">请选择截止时间</div>
          </template>
        </van-field>
      </van-cell-group>
      <van-cell-group inset title="收发信息">
        <van-field label="出发地址" disabled></van-field>
        <van-field
          label="城市"
          @click.stop="showCityPop('begin', formState.beginCityCode)"
          readonly
          required
          :rules="[{ required: true, message: '请选择出发地' }]"
          is-link
          name="beginCityCode"
          placeholder="请选择"
          input-align="right"
          v-model="formState.fromCityName"
        ></van-field>

        <van-field
          required
          label="详细信息"
          v-model="formState.pickupAddress"
          :rules="[{ required: true, message: '请输入详细信息' }]"
          label-align="left"
          input-align="right"
          placeholder="详细信息"
        ></van-field>
      </van-cell-group>
      <van-cell-group inset class="serviceBox">
        <van-field label="送达地址" disabled></van-field>
        <van-field
          label="城市"
          @click.stop="showCityPop('end', formState.destCityCode)"
          readonly
          required
          :rules="[{ required: true, message: '请选择出发地' }]"
          is-link
          name="beginCityCode"
          placeholder="请选择"
          input-align="right"
          v-model="formState.destCityName"
        ></van-field>
        <van-field
          required
          label="详细信息"
          v-model="formState.destAddress"
          :rules="[{ required: true, message: '请输入详细信息' }]"
          label-align="left"
          input-align="right"
          placeholder="详细信息"
        ></van-field>
      </van-cell-group>
      <van-cell-group inset title="物品信息">
        <van-field
          required
          label="物品类型"
          readonly
          v-model="formState.objectName"
          :rules="[{ required: true, message: '请选择物品类型' }]"
          label-align="left"
          input-align="right"
          is-link
          placeholder="请选择"
          @click="showWpPicker=true"
        ></van-field>
        <van-field
          required
          readonly
          label="物品重量"
          v-model="formState.objectWeightRangeName"
          :rules="[{ required: true, message: '请选择物品重量' }]"
          label-align="left"
          input-align="right"
          is-link
          placeholder="请选择"
          @click='showWtPicker=true'
        ></van-field>
        <van-field
          required
          label="物品描述"
          v-model="formState.objectDesc"
          type="textarea"
          :rules="[{ required: true, message: '请输入物品描述' }]"
          label-align="top"
          input-align="left"
          placeholder="请简短描述物品尺寸、运送注意事项等信息"
        ></van-field>
        <van-field name="uploader" label>
          <template #input>
            <van-uploader  multiple :max-count="9" :after-read="afterRead" v-model="fileList" />
          </template>
        </van-field>
      </van-cell-group>
      <van-col class="tips">
          温馨提示：带物请保证合法合规，不要让您的同事承担带物的法律风险，也别忘了感谢愿意帮助您的同事~
      </van-col>
      <van-row class="createBtn" justify="space-between" flex>
        <van-checkbox class="checkBox" iconSize="14px" v-model="checked"> <span class="tipsContent">已阅读并接受</span> <span @click.stop="LoadYszcPdf" class="dwxz">《隐私政策》</span> <span class="tipsContent">和</span> <span @click.stop="LoadFwxyPdf" class="dwxz">《服务协议》</span> </van-checkbox>
        <van-col :span="24">
          <van-button block type="primary" :loading="submitLoading" native-type="submit">立即发布</van-button>
        </van-col>
      </van-row>
    </van-form>
    <!-- 城市选择 -->
    <van-popup v-model:show="showCityPicker" style="height:60vh;overflow:hidden;"  position="bottom">
      <van-sticky :offset-top="0">
        <van-nav-bar title="城市选择可搜索">
          <template #right>
            <van-icon @click="closeCityChosePop" name="cross" size="18" />
          </template>
        </van-nav-bar>
        <van-search
          autocomplete="off"
          :show-action="searchCityList.length > 0"
          @cancel="onCityClear"
          @blur="onCitySearch"
          @clear="onCityClear"
          v-model="cityName"
          placeholder="请输入城市"
        />
      </van-sticky>

      <van-list
        style="height: calc(60vh - 100px); overflow-y: scroll;" 
        v-if="searchCityList.length > 0"
        v-model:loading="cityLoading"
        :finished="cityFinished"
        finished-text="没有更多了"
        @load="onLoadCity"
      >
        <van-cell
          @click="chosedCityItem(item)"
          v-for="item in searchCityList"
          :key="item"
          :title="item.name"
          :value="`${item.continentsName}/${item.countryName}/${item.provinceName}`"
        />
      </van-list>

      <van-cascader
        v-else
        v-model="chosedCity"
        :show-header="false"
        :options="cityDict"
        :field-names="{ text: 'name', value: 'id', children: 'children' }"
        @finish="finishCityChose"
        @change="onChange"
      />
    </van-popup>

    <!-- 时间选择 -->
    <van-popup v-model:show="showTimePicker" position="bottom" :overlay-style="{ zIndex: 1000 }">
      <van-picker-group
        v-if="showTimePicker"
        title="期望送达时间"
        :tabs="['选择日期', '选择时间']"
        @confirm="onConfirm"
        @cancel="onCancel"
      >
        <van-date-picker  v-model="startCurrentDate"  :max-date="maxDate" />
        <van-time-picker  v-model="startCurrentTime" />
      </van-picker-group>
    </van-popup>

    <!-- 物品类型 -->
    <van-popup v-model:show="showWpPicker" position="bottom">
      <van-picker title="选择物品类型" :columns="thingsList" @confirm="onWpConfirm" @cancel="onWpCancel" @change="onWpChange" />
    </van-popup>

    <!-- 物品重量 -->
    <van-popup v-model:show="showWtPicker" position="bottom">
      <van-picker title="选择物品类型" :columns="weightList" @confirm="onWtConfirm" @cancel="onWtCancel" @change="onWtChange" />
    </van-popup>
    <!-- 隐私政策 -->
    <van-popup :lazy-render="false" v-model:show="showYszcPopup" position="bottom" :style="{ height: '100%' }">
      <van-nav-bar
        title="创客帮平台隐私政策"
        left-arrow
        fixed
        :z-index="1000"
      >
        <template #left>
          <van-icon name="arrow-left" color="#000" @click="showYszcPopup=false" size="20" />
        </template>
      </van-nav-bar>
      <div ref="yszcpdfRef" style="height:100%;"></div> 
    </van-popup>

     <!-- 服务协议 -->
     <van-popup :lazy-render="false" v-model:show="showFwxyPopup" position="bottom" :style="{ height: '100%' }">
      <van-nav-bar
        title="创客帮平台服务协议"
        left-arrow
        fixed
        :z-index="1000"
      >
        <template #left>
          <van-icon name="arrow-left" color="#000" @click="showFwxyPopup=false" size="20" />
        </template>
      </van-nav-bar>
      <div ref="fwxypdfRef" style="height:100%;"></div> 
    </van-popup>
  </div>
</template>

<script setup lang='ts'>

import {
  IUserListRequest,
  TCteateTeam,
  IHelperThingsTypeEnum,
  IHelperWeightTypeEnum,
  IUserInfo,
  ICity,
  ITripInfo,
  ICreatTrip,
  ITraveler,
  TTicket,
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { debounce, values } from 'lodash';
import { cityApi } from '@haierbusiness-front/apis';
import { helperApi, tripApi, fileApi, download, teamListApi } from '@haierbusiness-front/apis';

import { createVNode, onMounted, reactive, ref, watch, watchEffect, toRefs,computed } from 'vue';
import ling from "@/assets/image/helper/ling.png"
import type { Ref } from 'vue';
import { Item } from 'ant-design-vue/es/menu';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { showSuccessToast, showFailToast, CascaderOption, showLoadingToast,DatetimePicker, showToast,showDialog } from 'vant';
import { getEnumOptions } from '@haierbusiness-front/utils';

import Pdfh5 from "pdfh5";
import "pdfh5/css/pdfh5.css";


// pdf h5
const showYszcPopup = ref(false)
const showFwxyPopup = ref(false)

const yszcpdf = new URL('@/assets/yszc.pdf', import.meta.url).href
const fwxypdf = new URL('@/assets/fwxy.pdf', import.meta.url).href

const fwxypdfRef = ref(null);

const yszcpdfRef = ref(null);
const LoadYszcPdf = () => {
  const pdfh5Yszc = new Pdfh5(yszcpdfRef.value, {
    pdfurl: yszcpdf,
  });
 
  pdfh5Yszc.on("complete", (status, msg, time) => { 
    showYszcPopup.value=true
  });
};

const LoadFwxyPdf = () => {

  const pdfh5Fwxy = new Pdfh5(fwxypdfRef.value, {
    pdfurl: fwxypdf,
  });
  pdfh5Fwxy.on("complete", (status, msg, time) => { 
    showFwxyPopup.value=true
  });
}


const router = getCurrentRouter()
const route = ref(getCurrentRoute()); 

const startCurrentDate = ref<any>()
const startCurrentTime = ref<any>()

const id = route.value?.query?.id;
const checked = ref<boolean>(false)

const getDetail = (id: string) => {
  if (!id) {
    return
  }
  helperApi.getById(id).then((res: any) => {
    formState.value = res
    formState.value.objectName = IHelperThingsTypeEnum[formState.value.objectType]
    formState.value.objectWeightRangeName = IHelperWeightTypeEnum[formState.value.objectWeightRange]
    formState.value.createUserName=loginUser.value?.nickName, //联系人名称
    formState.value.creatorPhone=loginUser.value?.phone//联系人电话
    // 初始化数据
    // formState.value.objectWeightRange = String(formState.value.objectWeightRange)
    // formState.value.objectType = res.objectType
    // formState.value.id = ''
    formState.value.files?.forEach(item => {
      item.url = item.fileUrl
      item.name = item.fileName
    })
    fileList.value = formState.value.files
    // addDemand()
    // addLoading.value = false
  })
};

const store = applicationStore();



watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);

const { loginUser } = storeToRefs(store);
// 选择时间确认
const onConfirm = () =>{
  if (choseTimeType.value == 'begin') {
    showTimePicker.value = false
    formState.value.expectTimeFrom = startCurrentDate.value.join("-") +' '+ startCurrentTime.value.join(":") + ':00'
  }else{
    showTimePicker.value = false
    formState.value.expectTimeTo = startCurrentDate.value.join("-") +' '+ startCurrentTime.value.join(":") + ':00'
  }
  console.log(startCurrentDate.value,startCurrentTime.value)
}
const onCancel = () =>{
  showTimePicker.value = false
}
// 重量选择
const showWtPicker = ref(false)
const weightList = computed(() => {
  let list = getEnumOptions(IHelperWeightTypeEnum, true)
  list.forEach(item => {
    item.text = item.label
  })
  return list
});

const onWtConfirm = ({ selectedValues }) => {
  formState.value.objectWeightRange = selectedValues[0]
  formState.value.objectWeightRangeName = IHelperWeightTypeEnum[formState.value.objectWeightRange]
  showWtPicker.value = false
}

const onWtCancel = () => {
  formState.value.objectType = ''
  showWtPicker.value = false
};

const onWtChange = () => {

}



// 物品选择
const showWpPicker = ref(false)
// 物品类型
const thingsList = computed(() => {
  let list = getEnumOptions(IHelperThingsTypeEnum, true)
  list.forEach(item => {
    item.text = item.label
  })
  return list
});

const onWpConfirm = ({ selectedValues }) => {
  formState.value.objectType = selectedValues[0]
  formState.value.objectName = IHelperThingsTypeEnum[formState.value.objectType]
  showWpPicker.value = false
}

const onWpCancel = () => {
  formState.value.objectType = ''
  showWpPicker.value = false
};

const onWpChange = () => {

}


// 城市选择
const showCityPicker = ref<boolean>(false);
const cityDict = ref<Array<ICity>>([])

// 城市选择相关
const cityName = ref('')
const cityLoading = ref<boolean>(false)
const cityFinished = ref<boolean>(false)
const internationalFlag = ref<number>(0)

const cityPageNum = ref<number>(1)
const cityPageSize = ref<number>(10)
const searchCityList = ref([])

const onLoadCity = () => {
  const params = {
    name: cityName.value,
    pageSize: cityPageSize.value,
    pageNum: cityPageNum.value,
    level: 'city',
    internationalFlag: internationalFlag.value
  }
  cityApi.getCityList(params).then(res => {
    searchCityList.value = [...searchCityList.value, ...res.records]
    cityLoading.value = false;
    if (searchCityList.value.length >= res.total) {
      cityFinished.value = true
    } else {
      cityFinished.value = false
      cityPageNum.value++
    }
  })
}

const closeCityChosePop = () => {
  if (cityChoseType.value == 'begin') {
    formState.value.fromCityCode = ''
    formState.value.fromCityName = ''
  } else {
    formState.value.destCityCode = ''
    formState.value.destCityName = ''
  }
  showCityPicker.value = false;
  cityName.value = ''
  onCityClear()
}
const onCitySearch = (val: string) => {
  searchCityList.value = []
  cityPageNum.value = 1
  onLoadCity()
}
const onCityClear = () => {
  cityPageNum.value = 1
  searchCityList.value = []
}
const chosedCity = ref();
const cityChoseType = ref<string>('');
const showCityPop = (type: string, code: string | number) => {
  chosedCity.value = Number(code);

  cityChoseType.value = type;
  showCityPicker.value = true;
};

const chosedCityItem = (item) => {
  if (cityChoseType.value == 'begin') {
    formState.value.fromCityCode = item.id;
    formState.value.fromCityName = item.name;
  } else {
    formState.value.destCityCode = item.id;
    formState.value.destCityName = item.name;
  }
  onCityClear()
  cityName.value = ''
  showCityPicker.value = false
}

const finishCityChose = ({ selectedOptions }: any) => {
  console.log('1111', selectedOptions);

  if (cityChoseType.value == 'begin') {
    formState.value.fromCityCode = selectedOptions[selectedOptions.length - 1].id;
    formState.value.fromCityName = selectedOptions[selectedOptions.length - 1].name;
  } else {
    formState.value.destCityCode = selectedOptions[selectedOptions.length - 1].id;
    formState.value.destCityName = selectedOptions[selectedOptions.length - 1].name;
  }

  showCityPicker.value = false;
};




// 请求参数
const formState = ref<any>({
  createUserName: loginUser.value?.nickName, //联系人名称
  creatorPhone:loginUser.value?.phone, //联系人电话
});

// 文件上传
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const fileList = ref([]);

const afterRead = (options: any) => {
  if(Array.isArray(options)){
    options.forEach(item=>{
      const formData = new FormData();
      formData.append('file', item.file);
      fileApi
      .upload(formData)
        .then((file) => {
          item.url = baseUrl + file.path
          item.fileUrl = baseUrl + file.path
          item.fileName = item.file.name
        })
        .catch(() => {
          delFile()
        })
        .finally(() => {
          // uploadLoading.value = false;
        });
    })
    console.log(fileList.value)
  }else{
  const formData = new FormData();
  formData.append('file', options.file);
  fileApi
  .upload(formData)
    .then((file) => {
      options.url = baseUrl + file.path
      options.fileUrl = baseUrl + file.path
      options.fileName = options.file.name
      console.log(file,fileList.value)
    })
    .catch(() => {
      delFile()
    })
    .finally(() => {
      // uploadLoading.value = false;
    });
  }
};

const downloadFile = (url: string) => {
  window.open(url);
};

const delFile = () => {
  formState.value.travelerFileUrl = '';
  formState.value.travelerFileName = '';
  fileList.value = [];
};

// 时间选择
// 当前时间加十天
const tenDays = () => {
  // 创建一个新的Date对象，表示当前日期和时间
  const currentDate = new Date();

  // 使用setDate方法加上10天
  currentDate.setDate(currentDate.getDate() + 10);

  return currentDate
}

const showTimePicker = ref<boolean>(false);

const addYear = () => {
  // 创建一个新的Date对象，表示当前日期和时间
  const currentDate = new Date();

  currentDate.setFullYear(currentDate.getFullYear() + 1);

  return currentDate
}

const minDate = ref();
minDate.value = new Date()
const maxDate = ref();
maxDate.value = addYear()


const choseTimeType = ref('');




const openTimePicker = (type: string) => {
  choseTimeType.value = type;

  startCurrentDate.value = []
  startCurrentTime.value = []
  if (formState.value.expectTimeFrom) {
    const minDateArr = formState.value.expectTimeFrom.split(' ')[0].split('-');
    minDate.value = new Date(parseInt(minDateArr[0]), parseInt(minDateArr[1]) - 1, parseInt(minDateArr[2]));
  }
  if (formState.value.expectTimeTo) {
    const maxDateArr = formState.value.expectTimeTo.split(' ')[0].split('-');
    maxDate.value = new Date(parseInt(maxDateArr[0]), parseInt(maxDateArr[1]) - 1, parseInt(maxDateArr[2]));
  }
  if (type == 'begin') {
    minDate.value = new Date();
    const BeginDateNow = formState.value.expectTimeFrom?.split(' ')[0];
    startCurrentDate.value = BeginDateNow?.split('-');
  } else {
    maxDate.value = addYear()
    const EndDateNow = formState.value.expectTimeTo?.split(' ')[0];

    startCurrentDate.value = EndDateNow?.split('-');
  }

  showTimePicker.value = true;
};


const submitLoading = ref<boolean>(false)
const onSubmit = (values: any) => {
  if(!checked.value){
    showToast('请阅读并同意《隐私政策》和《服务协议》')
    return
  }
  submitLoading.value = true
      formState.value.id=''
      formState.value.piggybackStatus=10
      formState.value.files = fileList.value
      helperApi.create(formState.value).then((res: any) => {
          submitLoading.value = false
          showSuccessToast('发布成功')
          goBack()
        }).catch(() => {
          submitLoading.value = false
    })
    
};
const getCityList = () => {
  tripApi.district().then(res => {
    cityDict.value = res.children;
  })
}
// 返回首页
const goBack = () => {
  router.push({
    path:'/mobile/index',
  })
}

onMounted(async () => {
  getCityList()
  showDialog({
  title: '声明',
  messageAlign: 'left',
  confirmButtonText:"我知道了",
  message: `       本平台仅为集团内员工提供信息交流的平台，帮助匹配带物需求与同行程资源，是否达成带物意向由员工相互沟通确定，本平台对带物双方均不加以控制，亦不介入带物的过程，带物过程中产生的一切纠纷由员工自行协商解决，与本平台无关，本平台不参与调解。发布需求即同意并遵守本声明。`,
  }).then(() => {
    // on close
  });
});

</script>

<style lang="less" scoped>
@import url(./components/mobile.less);
.van-form{
  height: calc(100vh - 40px);
  overflow: auto;
}
.tips{
  padding:8px 20px;
  font-size: 12px;
  color: rgba(0,0,0,0.85);
  line-height: 18px;
  text-align: left;
  font-style: normal;
}
.tipBox {
  display: flex;
  align-items: center;
  padding: 0 15px;
  img {
    width: 22px;
    height: 22px;
  }
  span {
    font-weight: 400;
    font-size: 14px;
    color: #2681ff;
    line-height: 24px;
    text-align: left;
    font-style: normal;
    margin-left: 5px;
  }
  height: 40px;
  background: #f0f5ff;
}
.serviceBox {
  margin-top: 15px;
}
.createBtn{
  position:sticky;
  background-color: #f6f7f9;
  bottom: 0;
  width:100%;
  padding: 0px 16px 20px 16px;
  .checkBox{
    padding:6px 0;
    .tipsContent{
      font-size: 12px;
      color: rgba(0,0,0,0.45);
    }
    .dwxz{
      color:#0073E5;
      font-size: 12px;
    }
  }
}
</style>