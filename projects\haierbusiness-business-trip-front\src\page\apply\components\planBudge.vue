
<script lang="ts" setup>
import { tripApi, reasonApi,cityApi,rechargeApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { ConfigProvider, message, Modal } from 'ant-design-vue';
import icon12306 from './icon12306.vue';
import { computed, onMounted, reactive, ref, watch, watchEffect, toRefs, createVNode } from 'vue';
import type { Ref, UnwrapRef } from 'vue';
import { CityResponse, CityItem } from '@haierbusiness-front/common-libs';

import {
  QuestionCircleOutlined,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  SearchOutlined,
  InfoCircleOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
  ExclamationCircleOutlined,
} from '@ant-design/icons-vue';
import {
  Anchor as hAnchor,
  <PERSON><PERSON> as hButton,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  Input as hInput,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  InputNumber as hInputNumber,
  Form as hForm,
  FormItem as hFormItem,
  Tag as hTag,
  Tooltip as hTooltip,
  Cascader as hCascader,
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import {
  IUserListRequest,
  IUserInfo,
  ICity,
  ICreatTrip,
  IPerson,
  ITripInfo,
  ITripList,
  ITraveler,
  IProduct,
  ITripDetailMap,
  MemberBudgetParams,
} from '@haierbusiness-front/common-libs';
import { cloneDeep, difference } from 'lodash-es';
import type { CascaderProps } from 'ant-design-vue';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';
import trainChose from '@haierbusiness-front/components/trainChose/index.vue';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
const emit = defineEmits(['showStandardOpen', 'openBudgetModal', 'showBudgetDetailModal', 'loading', 'openBudgetDetailShow']);

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const showStandardOpen = (trip:any) => {
  emit('showStandardOpen');
};
const openBudgetModal = () => {
  emit('openBudgetModal');
};

const openBudgetDetailShow = () => {
  emit('openBudgetDetailShow', props.creatTripParma.haierBudgetPayOccupyRequest);
};


// 获取每人明细

const showBudgetDetailModal = async () => {
  const amountList = await tripApi.memberAmountList(props.creatTripParma);
  let count = 0;
  if (amountList && amountList.length > 0) {
    amountList.forEach((item) => {
      count += item.amountSum;
    });

    amountList.push({
      amountSum: count,
      travelUserName: '合计',
    });
  }

  emit('showBudgetDetailModal', amountList);
};
const cityName = ref<string>('');
// 根据id递归获取城市名
const getCityName = (code: string, options) => {
  options.forEach((item) => {
    if (item.districts && item.districts.length) {
      getCityName(code, item.districts);
    } else {
      if (code === item.adcode) {
        cityName.value = item.name;
      }
    }
  });
  return cityName.value;
};

const labelCol = { span: 3 };
const wrapperCol = { span: 20 };

// 起点
const beginTime = ref<string>('');

// 终点
const endTime = ref<string>('');

interface Props {
  creatTripParma?: any;
  isDetail?: boolean;
  isChange?: boolean;
}
const props = defineProps<Props>();
// 获取主出差人信息
const mainPerson = () => {
  return props.creatTripParma.travelerList?.filter((item) => item.travelUserType == '0' && item.mainFlag == '1')[0];
};

const resonList = ref<Array<object>>([]);

onMounted(async () => {

  reasonApi.list({ pageNum: 1, reasonType: 20, pageSize: 50 }).then((res:any) => {
    resonList.value = res.records;
  });

  // 创建申请单的时候 默认展示两个城市选择
  if(cityList.value.length == 0 && !props.isDetail  && !props.isChange) {
    showAddCity()
  }
});

const cityList = ref<Array<ICity>>([]);

const stringName = (list: Array<ITraveler>) => {
  if (list && list.length > 0) {
    return list.map((item) => item.travelUserName).join('、');
  } else {
    return '-';
  }
};


// 判断行程中是否有飞机票未修改情况
const isEditPlaneMoney = () => {
  let temp = false;
  let tripList = props?.creatTripParma?.tripList;
  tripList?.forEach((trip) => {
    trip?.tripDetailMapList?.forEach((detail) => {
      // 飞机校验机票是否修改
      if (detail.productCode == '01' && detail.budgetAmount == detail.budgetAmountBz) {
        temp = true;
      }
    });
  });
  return temp;
};
const formRef = ref();

// 表单验证
const onSubmit = async () => {
  const res = await formRef.value.validate();
  // debugger
  if (res) {
    let temp = true;
      let tripList = props?.creatTripParma?.tripList;
      // 未添加行程
      if (!tripList || tripList?.length < 1) {
        temp = false;
        hMessage.warning('请先添加行程再进行后续操作!');
      } else {
        let tripDetailMapListAll:any = [];
        // 行程中未添加出行人、出行方式
        tripList?.forEach((trip:any) => {
          if (!trip?.tripDetailMapList) {
            trip['tripDetailMapList'] = [];
          }
          tripDetailMapListAll = [...tripDetailMapListAll, ...trip?.tripDetailMapList];

          trip?.tripDetailMapList?.forEach((detail:any) => {
            if (!detail?.travelApplyTripDetailList || detail?.travelApplyTripDetailList.length < 1) {
              temp = false;
              hMessage.warning('请先选择出差人员!');
              return;
            }
            if (!detail.productCode) {
              temp = false;
              hMessage.warning('请先选择出差费用产品!');
              return;
            }
            if (detail.excessiveFlag && !detail.excessiveReasonId && detail.productCode != '03' && detail.productCode != '05') {
              temp = false;
              detail['errorFlag'] = true;
              hMessage.warning('超标费用请选择超标原因!');
              return;
            }
          });
        });

        if (tripDetailMapListAll.length < 1) {
          temp = false;
          hMessage.warning('总行程至少添加一项费用预算!');
          return;
        }

        if (props?.creatTripParma?.amountSum > props.creatTripParma?.haierBudgetPayOccupyRequest?.leftAmt) {
          temp = false;
          hMessage.warning('预算不足,请重新修改后再次提交!');
        }
      }
      //

      return temp;
  }else {
    return false
  }
      

};

defineExpose({
  cityList,
  getCityName,
  onSubmit,
  isEditPlaneMoney,
});
// const tripList = ref<Array<ITripList>>([]);

const newOptions = (
  traveler: ITraveler[],
  detail: ITripDetailMap[],
  productCode: string | undefined,
  currentIndex: number,
) => {
  if (!productCode) {
    return traveler;
  }
  let detailIds: string[] = [];
  detail.map((item, index) => {
    if (item.personIdList && productCode === '01' && item.productCode == '02' && index !== currentIndex) {
      detailIds = [...detailIds, ...item.personIdList];
    }
    if (item.personIdList && productCode === '02' && item.productCode == '01' && index !== currentIndex) {
      detailIds = [...detailIds, ...item.personIdList];
    }
    if (item.personIdList && productCode === item.productCode && index !== currentIndex) {
      detailIds = [...detailIds, ...item.personIdList];
    }
  });

  let newTravelers: Array<any> = [];
  traveler.map((item) => {
    if (detailIds.includes(item.travelUserSyId!)) {
      const newTraveler = {
        ...item,
        disabled: true,
      };
      newTravelers.push(newTraveler);
    } else {
      newTravelers.push(item);
    }
  });
  return newTravelers;
};

const changeTraveler = (trip, item, list) => {
  let tempList = ref<Array<ITraveler>>([]);
  props.creatTripParma.travelerList?.forEach((i) => {
    list.forEach((j) => {
      if (i.travelUserSyId === j) {
        tempList.value.push(i);
      }
    });
  });
  item.travelApplyTripDetailList = JSON.parse(JSON.stringify(tempList.value));
  if (!item.productCode) {
    return;
  }
  getMemberBudget(trip, item, true);
};

const changeInsurance = (trip, item) => {
  // getMemberBudget(trip, item, true);
  getAllBugde();
};

let cityItem: ITripList = {
  beginCityName: '',
  beginCityCodeSy: '',
  endCityCodeSy: '',
  endCityName: '',
  beginDate: '',
  endDate: '',
  tripDetailMapList: [],
};


// 获取差旅费
const memberBudgetParams = ref<MemberBudgetParams>({});

// 更换出行方式时根据 方式去查询 保险、服务费
const choseNewType = (trip: ITripList, item: ITripDetailMap, currentIndex: number) => {
  // 保险费
  item.insuranceAmount = travelList.value.filter(
    (insurance) => insurance.productCode === item.productCode,
  )[0].insuranceAmount;
  item.productName = travelList.value.filter((product) => product.productCode === item.productCode)[0].productName;

  // 去掉本行程中相同产品下已经选择的人员
  let ids: string[] = [];
  trip.tripDetailMapList?.map((detail, index) => {
    // 选择的飞机 选择火车的人员也不能选择
    if (item.productCode == '01' && detail.productCode == '02' && index != currentIndex && detail.personIdList) {
      ids = [...ids, ...detail.personIdList];
    }
    // 选择的火车 选择飞机的人员也不能选择
    if (item.productCode == '02' && detail.productCode == '01' && index != currentIndex && detail.personIdList) {
      ids = [...ids, ...detail.personIdList];
    }
    if (item.productCode == detail.productCode && index != currentIndex && detail.personIdList) {
      ids = [...ids, ...detail.personIdList];
    }
  });
  if (trip.tripDetailMapList) {
    trip.tripDetailMapList[currentIndex].personIdList = difference(
      trip.tripDetailMapList[currentIndex].personIdList,
      ids,
    );
  }

  // 选择时判断是否已经添加同类出行方式
  let tmpArr = [];
  item.travelApplyTripDetailList = [];
  item?.personIdList?.forEach((id) => {
    tmpArr = props.creatTripParma?.travelerList?.filter((map) => id == map?.travelUserSyId);
    item.travelApplyTripDetailList = JSON.parse(JSON.stringify([...item.travelApplyTripDetailList, ...tmpArr]));
  });
  getMemberBudget(trip, item, true);

  // 如果选择火车 获取火车站点
  if (item.productCode == '02') {
    // 查询火车三字码列表
    // tripApi.queryTrainStationList('1').then((res) => {
    //   item.tarinList = res || [];
    // });
    setTimeout(() => {
      chosedTrainStation(trip, currentIndex);
    }, 1000);
  }
};

// 选择起始站
const chosedBeginTrainStation = (item, record) => {
  record.startTrainCode = item.stationCode
  record.startTrainName = item.stationName

}
const chosedEndTrainStation = (item,record) => {
  record.endTrainCode = item.stationCode
  record.endTrainName = item.stationName
}

// 点击查看火车站 获取火车站点
const chosedTrainStation = (trip: ITripList, currentIndex: number) => {
  trip.tripDetailMapList[currentIndex]['popVis'] = !trip.tripDetailMapList[currentIndex]['popVis']
};


const addCity = () => {
  let leg: number = cityList.value.length;
  if (leg > 0) {
    if (!newEndCity.value || !endTime.value) {
      hMessage.warning('请先完善行程信息!');
      return;
    }
    if (newEndCity.value.citycode == newBeginCity.value.citycode) {
      Modal.confirm({
        title: '警告',
        icon: createVNode(ExclamationCircleOutlined),
        content: createVNode('div', { style: 'color:origin;' }, '出发城市与目的城市一致,您确定吗?'),
        onOk() {},
      });
    }
    if (newEndCity.value.citycode == cityList.value[leg - 1].cityCode) {
      Modal.confirm({
        title: '提醒',
        icon: createVNode(ExclamationCircleOutlined),
        content: createVNode('div', { style: 'color:origin;' }, '出发城市与目的城市一致!'),
        onOk() {},
      });
    }
    cityItem = {
      endCityCodeSy: newEndCity.value?.providerMapList[0].districtId,
      beginCityCodeSy: cityList.value[leg - 1].syId,
      beginCityCode: cityList.value[leg - 1].cityCode,
      endCityCode: newEndCity.value.citycode,
      beginCityName: cityList.value[leg - 1].city,
      endCityName: newEndCity.value.name,
      beginDate: cityList.value[leg - 1].date,
      endDate: endTime.value,
      tripDetailMapList: [],
      detailMap: true,
    };
    props?.creatTripParma?.tripList.push(cityItem);
    cityList.value.push({
      cityCode: newEndCity.value.citycode,
      city: newEndCity.value.name,
      syId: newEndCity.value?.providerMapList[0].districtId,
      date: endTime.value,
      active: false,
    });
    newEndCity.value = {
      name: '',
      citycode: '',
      syId: '',
      airport: '',
      trainStation: '',
    };
    endTime.value = '';
    showNextAddCity.value = false;
  } else {
    // 一次添加两个
    if (!newBeginCity.value.citycode || !beginTime.value) {
      return;
    }
    if (!newEndCity.value.citycode || !endTime.value) {
      return;
    }

    if (newEndCity.value.citycode == newBeginCity.value.citycode) {
      Modal.confirm({
        title: '提醒',
        icon: createVNode(ExclamationCircleOutlined),
        content: createVNode('div', { style: 'color:origin;' }, '出发城市与目的城市一致!'),
        onOk() {},
      });
    }

    cityList.value.push({
      cityCode: newBeginCity.value.citycode,
      city: newBeginCity.value.name,
      date: beginTime.value,
      syId: newBeginCity.value?.providerMapList[0].districtId,
      active: false,
    });
    cityItem = {
      endCityCodeSy: newEndCity.value?.providerMapList[0].districtId,
      beginCityCodeSy: newBeginCity.value?.providerMapList[0].districtId,
      beginCityCode: newBeginCity.value.citycode,
      endCityCode: newEndCity.value.citycode,
      beginCityName: newBeginCity.value.name,
      endCityName: newEndCity.value.name,
      beginDate: beginTime.value,
      endDate: endTime.value,
      tripDetailMapList: [],
      detailMap: true,
    };
    if (!props.creatTripParma.tripList) {
      props.creatTripParma.tripList = [];
      props.creatTripParma.tripList.push(cityItem);
    } else {
      props.creatTripParma.tripList.push(cityItem);
    }
    cityList.value.push({
      cityCode: newEndCity.value.citycode,
      city: newEndCity.value.name,
      date: endTime.value,
      syId: newEndCity.value?.providerMapList[0].districtId,
      active: false,
    });

    newBeginCity.value = {
      name: '',
      citycode: '',
      syId: '',
    };
    beginTime.value = '';
    newEndCity.value = {
      name: '',
      citycode: '',
      syId: '',
      airport: '',
      trainStation: '',
    };
    endTime.value = '';
  }
  isChosedTimeRange.value = false;
  isChosedCity.value = false;
  // 总行程的出发地、目的地、出发时间、到达时间、syid
  props.creatTripParma.beginDate = cityList.value[0].date;
  props.creatTripParma.beginCityCodeSy = cityList.value[0].syId;
  props.creatTripParma.beginCityCode = cityList.value[0].cityCode;
  props.creatTripParma.beginCityName = cityList.value[0].city;

  props.creatTripParma.endCityCode = cityList.value[cityList.value.length - 1].cityCode;
  props.creatTripParma.endDate = cityList.value[cityList.value.length - 1].date;
  props.creatTripParma.endCityCodeSy = cityList.value[cityList.value.length - 1].syId;
  props.creatTripParma.endCityName = cityList.value[cityList.value.length - 1].city;
};

// 默认展示时间
const defaultPickerValue = (val: string) => {
  if (val) {
    return dayjs(val)

  }else {
    return dayjs()
  }
}

const isChosedTimeRange = ref<boolean>(false);
const isChosedCity = ref<boolean>(false);

// 时间选择完毕后自动保存行程
const rangeChange = (date: Dayjs | string, dateString: string) => {
  if (dateString) {
    isChosedTimeRange.value = true;
  }
  if (isChosedCity.value && isChosedTimeRange.value) {
    addCity();
  }
};

const newEndCity = ref<CityItem>({
  name: '',
  citycode: '',
  syId: '',
  airport: '',
  trainStation: '',
});
const newBeginCity = ref<CityItem>({
  name: '',
  citycode: '',
  syId: '',
});
const chosedEditCity = (city: CityItem, index: number, i: number) => {
  if (!city.citycode) {
    return;
  }
  cityListArr.value[i][index].city = city.name;
  cityListArr.value[i][index].cityCode = city.citycode;
  cityListArr.value[i][index].syId = city?.providerMapList[0]?.districtId;
  saveDetailCity(cityListArr.value[i][index], index, i);
};
const chosedCity = (city: CityItem) => {
  newEndCity.value = city;
  addEndCity();
};
const chosedBeginCity = (city: CityItem) => {
  newBeginCity.value = city;
  addBeginCity();
};
const addBeginCity = () => {
  if (newBeginCity.value) {
    isChosedCity.value = true;
  }
  if (isChosedCity.value && isChosedTimeRange.value) {
    addCity();
  }
};
const addEndCity = () => {
  if (newEndCity.value) {
    isChosedCity.value = true;
  }
  if (isChosedCity.value && isChosedTimeRange.value) {
    addCity();
  }
};

// 获取最后行程时间作为禁选时间
const disabledDate = (current: Dayjs) => {
  return current && current < dayjs(dayjs(props?.creatTripParma.endDate).format());
};

// 开始时间作为禁选时间
const disabledDate2 = (current: Dayjs) => {
  return current && current > dayjs(dayjs(endTime.value).format());
};
// 开始时间作为禁选时间
const disabledDate3 = (current: Dayjs) => {
  return current && current < dayjs(dayjs(beginTime.value).format());
};

// 开始时间作为禁选时间
const disabledDate4 = (current: Dayjs) => {
  if (disabledDate11.value && disabledDate111.value) {
    return (
      (current && current < dayjs(dayjs(disabledDate11.value).format())) ||
      (current && current > dayjs(dayjs(disabledDate111.value).format()))
    );
  } else if (disabledDate11.value) {
    return current && current < dayjs(dayjs(disabledDate11.value).format());
  } else if (disabledDate111.value) {
    return current && current > dayjs(dayjs(disabledDate111.value).format());
  }
  return current;
};

const changeMoney = (trip: ITripList, item: ITripDetailMap) => {
  if (item.budgetAmount > (item.budgetAmountBz || 0)) {
    item.excessiveFlag = 1;
    item.errorFlag = true;
  } else {
    item.excessiveFlag = 0;
    item.excessiveReasonId = null
    item.errorFlag = false;
  }
};

const getMemberBudget = (trip: ITripList, item: any, reGet: boolean) => {
  // debugger
  if(item?.personIdList?.length < 1) {
    hMessage.warning('请先选择人员')
    return
  }
  emit('loading', true)
  memberBudgetParams.value = {
    operatorUserCode: loginUser.value?.username,
    operatorUserDeptId: loginUser.value?.departmentCode,

    beginCityCode: trip.beginCityCode,
    endCityCode: trip.endCityCode,
    beginCityName: trip.beginCityName,
    endCityName: trip.endCityName,
    startDate: trip.beginDate,
    endDate: trip.endDate,
    productNo: item.productCode,
    insuranceFlag: item.insuranceFlag,
    memberList: item.travelApplyTripDetailList,
    // budgetAmountBz: item.budgetAmount == item.budgetAmountBz ? 0 : item.budgetAmount,
    budgetAmountBz: reGet ? 0 : item.budgetAmount == item.budgetAmountBz ? 0 : (item.budgetAmount|| 0),

    excessiveFlag: item.excessiveFlag ? 1 : 0,
  };
  tripApi.memberBudget(memberBudgetParams.value).then((res:any) => {
    emit('loading', false)

    // 如果输入了最小值
    if (res.minAmountBz) {
      item.budgetAmount = res.minAmountBz;
      message.warning(`最低费用预算为${item.budgetAmount}!`);
    } else {
      item.budgetAmount = res?.budgetAmountSum || 0; // 可更改预算
      item.budgetAmountBz = res?.budgetAmountSumBz || 0; // 预算的最大值
    }

    item.budgetAmountDesc = res?.memberBudgetList;

    if (item.budgetAmount > item.budgetAmountBz) {
      item.excessiveFlag = 1;
    } else {
      item.excessiveFlag = 0;
      item.excessiveReasonId = null;
    }

    // props?.creatTripParma?.travelerList.forEach((ii) => {
    //   res?.memberList?.forEach((jj) => {
    //     if (ii.travelUserSyId == jj.travelUserSyId) {
    //       ii.differentialStandard = jj.differentialStandard;
    //     }
    //   });
    // });
    item?.travelApplyTripDetailList.forEach((ii) => {
      res?.memberList?.forEach((jj) => {
        if (ii.travelUserSyId == jj.travelUserSyId) {
          ii.differentialStandard = jj.differentialStandard;
        }
      });
    });

    getAllBugde();
  }).catch(err => {
    emit('loading', false)
    getAllBugde();

  })
};

const saveEditCity = (city: ICity) => {
  if (!city.cityCode || !city.date) {
    return;
  }

  city.active = false;
};

// 转换时间格式  2.1
const formateTime = (time: string) => {
  return `${new Date(time).getFullYear()}/${new Date(time).getMonth() + 1}/${new Date(time).getDate()}`;
};


// 可输入表格相关
const columns = [
  { align: 'center', title: '产品名称', dataIndex: 'productCode', key: 'productCode', width: '120px', fixed: 'left' },
  { align: 'center', title: '出差人员', dataIndex: 'personIdList', key: 'personIdList', width: '260px' },
  { align: 'center', title: '费用预算', dataIndex: 'budgetAmount', key: 'budgetAmount', width: '140px' },
  { align: 'center', title: '超标原因', ellipsis: true, dataIndex: 'excessiveReasonId', key: 'excessiveReasonId' },
  {
    align: 'center',
    title: '保险(非必选)',
    dataIndex: 'insuranceFlag',
    key: 'insuranceFlag',
    width: '100px',
    fixed: 'right',
  },
  { align: 'center', title: '操作', dataIndex: 'operation', key: 'operation', width: '100px', fixed: 'right' },
];
const detailColumns = [
  { align: 'center', title: '费用名称', dataIndex: 'productCode', key: 'productCode', width: '120px', fixed: 'left' },
  { align: 'center', title: '出差人员', dataIndex: 'personIdList', key: 'personIdList', width: '260px' },
  { align: 'center', title: '费用预算', dataIndex: 'budgetAmount', key: 'budgetAmount', width: '140px' },
  { align: 'center', title: '超标原因', ellipsis: true, dataIndex: 'excessiveReasonId', key: 'excessiveReasonId' },
  {
    align: 'center',
    title: '保险(非必选)',
    dataIndex: 'insuranceFlag',
    key: 'insuranceFlag',
    width: '100px',
    fixed: 'right',
  },
];
const descColumns = [
  { align: 'center', title: '出差人员', dataIndex: 'travelUserName', key: 'travelUserName', width: '300px' },
  { align: 'center', title: '费用名称', dataIndex: 'productName', key: 'productName', width: '300px' },
  { align: 'center', title: '差标', dataIndex: 'differentialStandard', key: 'differentialStandard', width: '300px' },
  { align: 'center', title: '平台使用费', dataIndex: 'serviceAmount', key: 'serviceAmount', width: '300px' },
  // { align: 'center', title: '保险费', dataIndex: 'insuranceAmount', key: 'insuranceAmount', width: '300px' },
];



const allowBugde = ref<Array<ITripDetailMap>>([]);

// 计算所有行程的总费用
const getAllBugde = () => {
  allowBugde.value = [];
  props.creatTripParma.amountSum = 0;
  // 所有费用
  props.creatTripParma.tripList.forEach((item) => {
    allowBugde.value = [...allowBugde.value, ...(item.tripDetailMapList || [])];
  });
  allowBugde.value.forEach((item) => {
    if(item.insuranceFlag && item.insuranceAmount) {
      props.creatTripParma.amountSum += item.insuranceAmount * item.personIdList?.length
    }
    props.creatTripParma.amountSum += item.budgetAmount ?? 0;
  });
};

const deleteItem = (record:ITripDetailMap, i: number) => {
  if (record.id) {
    props.creatTripParma.tripList[i].tripDetailMapList?.forEach((item:any, index:number) => {
      if (item.id == record.id) {
        props.creatTripParma.tripList[i].tripDetailMapList.splice(index, 1);
        getAllBugde();
      }
    });
  } else {
    props.creatTripParma.tripList[i].tripDetailMapList?.forEach((item:any, index:number) => {
      if (item.key == record.key) {
        props.creatTripParma.tripList[i].tripDetailMapList.splice(index, 1);
        getAllBugde();
      }
    });
  }
};

// 单点登录跳转第三方 -----------------------------
// 根据选择产品名称跳转第三方预定页面查看预定信息
const TRIP_SINGLE = import.meta.env.VITE_BUSINESS_TRIP_SINGLE;

// 传入对象生成拼接字符串
const createUrlParams = (obj: { [key: string]: any }): string  => {
  const params = new URLSearchParams();
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      params.append(key, obj[key]?.toString());
    }
  }
  return params.toString();
}
// 格式化出行人id部门id数据  格式：用户id:部门id
const formatPerson = (list?: any[]) => {
  return list?.map(item => `${item.travelUserNo || item.username || item.travelUserSyId }:${item.travelUserDeptId || item.departmentCode  || ""}`)?.join(',')
}

// 根据城市id获取机场数据
const getAirportList =  async(cityIds: string) => {
  const res = await cityApi.getAirportByCityId({ cityIds })
  return res
}
// 根据城市id获取车站数据
const getTrainStationByCityId =  async(cityIds: string) => {
  const res = await cityApi.getTrainStationByCityId({ cityIds })
  return res
}

// 根据城市id获取航站楼数据
const getAirportList2 =  async (cityIds: string) => {
  const res = await cityApi.getAirportByCityId({ cityIds, isNeedAirportTerminal: true,domesticInternationalType: 1  })
  return res
}

const goToThirdParty = async(trip:any, record:ITripDetailMap) => {
  if (!record.productCode) {
    hMessage.error('请选择费用产品!')
    return
  }
  console.log('----- 跳转预定页面 >>>>',trip, record.productCode );

  // record.productCode 01 国内飞机  02 火车  04 酒店

  let params = {}

  let url = ''
  switch (record.productCode) {
    case '01':
    // 1、飞机首先根据城市 id获取 出发机场、到达机场编号
    const beginAirport = await getAirportList(trip.beginCityCode)
    const endAirport = await getAirportList(trip.endCityCode)
    if (beginAirport[0].threeCharacterCode && endAirport[0].threeCharacterCode) {
      params = {
        skipType: '010010',
        czly: 'jcx',
        ygys:  props.creatTripParma?.travelReserveFlag, // 因公因私 1因公 2因私
        ccsqdh:  props.creatTripParma?.applyNo || '',
        xclx: 1, // 行程类型 1单程 2往返 3多程
        sqdlx: 0, // 申请单类型  0为出差申请单
        cxr: record?.travelApplyTripDetailList?.map(item => item.travelUserName).join(','), // 出行人
        cxr_hid: formatPerson(record?.travelApplyTripDetailList),  // 出行人id，格式：用户id:部门id
        cfcs:  trip?.beginCityName, 
        cfcs_hid:  beginAirport[0].threeCharacterCode,
        cfcs_iscity: 1,
        cfrq:  trip?.beginDate || dayjs().format('YYYY-MM-DD'),
        ddcs:  trip?.endCityName,
        ddcs_hid:  endAirport[0].threeCharacterCode,
        ddcs_iscity: 1,// 到达城市识别标识 识别城市还是机场 1城市 0机场
        ddrq:  trip?.endDate,
        cxrlx: covertLx(record?.travelApplyTripDetailList), // 出行人类型 1内部员工 2外部人员
      }
      url = TRIP_SINGLE + 'fcc/ticket/lticketbook/search/single/index.html?' + createUrlParams(params)


    }else { 
      hMessage.error('出发城市或到达城市没有机场,请重新选择!')
      return
    }

    break;

    case '02':
    // 2、火车首先根据城市 id获取 出发、到达站点编号
    const beginTrain = await getTrainStationByCityId(trip.beginCityCode)
    const endTrain = await getTrainStationByCityId(trip.endCityCode)
    if (beginTrain[0].threeCharacterCode && endTrain[0].threeCharacterCode) {
      params = {
        skipType: '010014',
        cfcs: trip?.beginCityName,
        cfcs_hid: beginTrain[0]?.threeCharacterCode,
        ddcs: trip?.endCityName,
        ddcs_hid: endTrain[0]?.threeCharacterCode,
        cfrq: trip?.beginDate,
        ccsqdh: props.creatTripParma?.applyNo || '',
        // ccsqdcx: props.creatTripParma?.applyNo ? 1 : '',
        ygys: 1,
        xclx: 1,
        sfkqjcx: 1
      }
      url = TRIP_SINGLE + 'view/fcc/train/book/search.html?' + createUrlParams(params)

    }else {
      hMessage.error('出发城市或到达城市没有火车站,请重新选择!')
      return
    }
    
    break;

    case '03':

    break;

    
    case '04':
    params = {
      
      skipType: '010013',
      onlySearch:1,
      ygys: 1, // 因公因私 1因公 2因私
      ccsqdh:props.creatTripParma?.applyNo || '',
      
      cxr: record?.travelApplyTripDetailList?.map(item => item.travelUserName).join(','), // 出行人
      cxr_hid: formatPerson(record?.travelApplyTripDetailList),   // 出行人id，格式：用户id:部门id
      cxrlx: covertLx(record?.travelApplyTripDetailList), // 出行人类型 1内部员工 2外部人员
      cxrzj: await convertZJ(record?.travelApplyTripDetailList), //出行人职级

      cfcs: trip?.endCityName, 
      cfcs_hid: trip?.endCityCodeSy,
      
      cfrq: trip?.beginDate,
      ddrq: trip?.endDate,
      
    }
    debugger
    url = TRIP_SINGLE + 'fcc/hotel/list/list.html?' + createUrlParams(params)

    break;

    case '05':

    // 根据城市查询航站楼信息
    const endCityHzl = await getAirportList2(trip.endCityCode)
    params = {
      skipType: '010012',
      cxr: record?.travelApplyTripDetailList?.map(item => item.travelUserName).join(','), // 出行人
      cxr_hid: formatPerson(record?.travelApplyTripDetailList),
      sqdlx: 0,
      ygys: props.creatTripParma.travelReserveFlag == 1 ? 1 : 2, // 因公因私 1因公 2因私
      ydsj: trip?.endDate,
      fwzdmc: `${endCityHzl[0].airportList[0].name}${endCityHzl[0].airportList[0].airportTerminalList[0].terminal}航站楼`,
      fwzdid: endCityHzl[0].airportList[0].vetechAirportBh,
      hzl: endCityHzl[0].airportList[0].airportTerminalList[0].terminal,
      fwcsid: trip?.endCityCodeSy,
      fwcsmc: `${endCityHzl[0].airportList[0].name}${endCityHzl[0].airportList[0].airportTerminalList[0].terminal}航站楼`,
    }
    url = TRIP_SINGLE + 'fcc/airservice/book/search.html?' + createUrlParams(params)

    break;
  }
  console.log(url)
  window.open(url,'_blank')
}


const showFirstAddCity = ref<boolean>(false);
const showNextAddCity = ref<boolean>(false);

const covertLx = (list:any) => {
  if(!list || !list.length) {
    return ''
  }
  const res = list.map((item:any) => {
    if(item.travelUserNo) {
      return 1
    }else {
      return 2
    }
  })
  return res.join(',')
}
// 根据工号查询职级
const getDirectLineByUserCode = async (username:any) => {
  const res = await rechargeApi.getDirectLine({username: username})
  console.log('----根据工号查询职级------>', res.brandCode)
  return res
}
// 根据出行人转换成职级字符串
const convertZJ = async (list?: any) => {
  if(!list || !list.length) {
    return ''
  }
  let usernameList:any = []
  list.forEach((item: any) => {
      usernameList.push(item.travelUserNo || item.username)
  })
  // 使用 Promise.all 并行执行异步操作
  const results = await Promise.all(usernameList.map((username:string) => {
    if (username) {
      return getDirectLineByUserCode(username)
    }else {
      return ''
    }
  }));
  let zjstr = results.map(res => res?.brandCode || '');
  console.log('----根据出行人转换成职级字符串------>', zjstr)
  return zjstr.join(',')
  
}

const showAddCity = () => {
  if (cityList.value?.length > 0) {
    if (showNextAddCity.value) {
      message.warning('请先完善当前城市信息!');
      return;
    }
    showNextAddCity.value = true;
  } else {
    if (showFirstAddCity.value) {
      message.warning('请先完善当前城市信息!');
      return;
    }
    showFirstAddCity.value = true;
  }
};

let i = 0;
const tripItem = ref<ITripList>({});

const showNotice = ref(false)

// 添加费用
const addMoney = (index: number) => {
  if (props.isDetail) {
    return;
  }

  // 如果未添加返程信息,弹窗询问是否需要添加
  if(props.creatTripParma.beginCityCode != props.creatTripParma.endCityCode && !showNotice.value && !props.isChange) {
    Modal.confirm({
      title: '提示',
      icon: createVNode(ExclamationCircleOutlined),
      content: createVNode('div', { style: 'color:red;' }, '您未填写返回出发地的行程计划，需要为您加上吗？'),
      okText: '加上吧',
      cancelText: '不了',
      onOk() {
        cityList.value = [...cityList.value, {
          cityCode: props.creatTripParma.beginCityCode,
          city: props.creatTripParma.beginCityName,
          syId: props.creatTripParma.beginCityCodeSy,
          date: props.creatTripParma.endDate,
        }]
        cityItem = {
          endCityCodeSy: props.creatTripParma.beginCityCodeSy,
          endCityCode: props.creatTripParma.beginCityCode,
          endCityName: props.creatTripParma.beginCityName,
          endDate: props.creatTripParma.endDate,

          beginCityCodeSy: cityList.value[cityList.value.length-2].syId,
          beginCityCode: cityList.value[cityList.value.length-2].cityCode,
          beginCityName: cityList.value[cityList.value.length-2].city,
          beginDate: cityList.value[cityList.value.length-2].date,

          tripDetailMapList: [],
          detailMap: true,
        };

        if (!props.creatTripParma.tripList) {
          props.creatTripParma.tripList = [];
          props.creatTripParma.tripList.push(cityItem);
        } else {
          props.creatTripParma.tripList.push(cityItem);
        }

        props.creatTripParma.endCityCode = cityList.value[cityList.value.length - 1].cityCode;
        props.creatTripParma.endDate = cityList.value[cityList.value.length - 1].date;
        props.creatTripParma.endCityCodeSy = cityList.value[cityList.value.length - 1].syId;
        props.creatTripParma.endCityName = cityList.value[cityList.value.length - 1].city;
        
        showNotice.value =  true
      },
      onCancel() {
        showNotice.value =  true
      },
      class: 'test',
    });
  }

  tripItem.value = {
    key: i,
    personIdList: props.creatTripParma.travelerList.map((item) => item.travelUserSyId),
    travelApplyTripDetailList: JSON.parse(JSON.stringify(props?.creatTripParma?.travelerList)),
    productName: undefined,
    productCode: undefined, //
    budgetAmount: 0, // 总金额
    excessiveReasonId: undefined, // 超标原因
    excessiveFlag: 0,
    insuranceFlag: true, // 是否购买保险
  };
  props.creatTripParma.tripList[index].tripDetailMapList
    ? (props.creatTripParma.tripList[index].tripDetailMapList = [
        ...props.creatTripParma.tripList[index].tripDetailMapList,
        tripItem.value,
      ])
    : (props.creatTripParma.tripList[index].tripDetailMapList = [tripItem.value]);

  i++;
  getAllBugde();
};
// 删除行程
const delTrip = (index: number) => {
  // 如果恰好只有一个行程 全部删除
  if(cityList.value?.length == 2) {
    cityList.value = [];
    props.creatTripParma.tripList = [];
    props.creatTripParma['beginDate'] =''
    props.creatTripParma['endDate'] =''

    props.creatTripParma['beginCityCode'] =''
    props.creatTripParma['beginCityCodeSy'] =''
    props.creatTripParma['beginCityName'] =''
    props.creatTripParma['endCityCode'] =''
    props.creatTripParma['endCityCodeSy'] =''
    props.creatTripParma['endCityName'] =''
    return
  }

  // 删除中间城市 清空后边城市的行程
  if (index + 1 !== props.creatTripParma.tripList?.length) {
    props.creatTripParma.tripList[index + 1].tripDetailMapList = [];
  }

  cityList.value.splice(index + 1, 1);
  props?.creatTripParma?.tripList.splice(index, 1);

  // 删除中间城市导致 起始地-终点错误
  cityList.value.forEach((i: ICity, ii: number) => {
    if (ii > 0) {
      props.creatTripParma.tripList[ii - 1].beginCityName = cityList.value[ii - 1].city;
      props.creatTripParma.tripList[ii - 1].endCityName = cityList.value[ii].city;
      props.creatTripParma.tripList[ii - 1].beginCityCode = cityList.value[ii - 1].cityCode;
      props.creatTripParma.tripList[ii - 1].endCityCode = cityList.value[ii].cityCode;
      props.creatTripParma.tripList[ii - 1].beginCityCodeSy = cityList.value[ii - 1].syId;
      props.creatTripParma.tripList[ii - 1].endCityCodeSy = cityList.value[ii].syId;
      if (ii > 1) {
        props.creatTripParma.tripList[ii - 1].beginDate = cityList.value[ii - 1].date;
      } else {
        props.creatTripParma.tripList[ii - 1].beginDate = cityList.value[ii - 1].date;
      }
    }
  });

  if (cityList.value.length > 1) {
    props.creatTripParma['beginDate'] = cityList.value[0].date;
    props.creatTripParma['endDate'] = cityList.value[cityList.value.length - 1]?.date;
  } else {
    props.creatTripParma['beginDate'] = cityList.value[0].date;
    props.creatTripParma['endDate'] = cityList.value[cityList.value.length - 1].date;
  }

  getAllBugde();
};

const disabledDate1 = ref<string>('');
const disabledDate11 = ref<string>('');
const disabledDate111 = ref<string>('');

// 编辑时控制不可选择时间
const editCity = (city: ICity, index: number, i: number) => {
  const nullIndex = cityList.value.findIndex(item => !item.date)
  if (nullIndex > -1 ) {

    hMessage.warning(`请先完善前往${cityList.value[nullIndex].city}的时间信息!`)
    return
  }
  // debugger
  cityListArr.value.forEach((list) => {
    list.forEach((ll) => {
      ll.active = false;
    });
  });
  if (props.isDetail) {
    return;
  }
  // 记录修改第一个不可选择时间
  if (index == 0 && i == 0) {
    if (cityList.value.length > 1) {
      disabledDate11.value = '';
      disabledDate111.value = cityList.value[1].date;
    }
  } else if (i * 4 + index + 1 == cityList.value.length) {
    // 记录最后一个不可选择时间
    disabledDate11.value = cityList.value[cityList.value.length - 2].date;
    disabledDate111.value = '';
  } else {
    disabledDate11.value = cityList.value[index - 1].date;
    disabledDate111.value = cityList.value[index + 1].date;
  }

  city.active = true;
};

// 修改出发地目的地
const saveDetailCity = (city: ICity, index: number, i: number) => {
  if (!city.date || !city.cityCode) {
    return;
  }
  // 数组总长度
  let legAll = i * 4 + index + 1;

  if (index == 0 && i == 0 && cityList.value.length > 1) {
    props.creatTripParma['beginDate'] = city.date;
    props.creatTripParma['beginCityName'] = city.city;
    props.creatTripParma['beginCityCode'] = city.cityCode;
    props.creatTripParma['beginCityCodeSy'] = city.syId;
    props.creatTripParma.tripList[0].beginCityName = city.city;
    props.creatTripParma.tripList[0].beginCityCode = city.cityCode;
    props.creatTripParma.tripList[0].beginCityCodeSy = city.syId;
    props.creatTripParma.tripList[0]['beginDate'] = city.date;

    props.creatTripParma.tripList[0]['tripDetailMapList'] = []

  } else if (legAll == cityList.value.length) {
    props.creatTripParma['endDate'] = city.date;
    props.creatTripParma['endCityName'] = city.city;
    props.creatTripParma['endCityCode'] = city.cityCode;
    props.creatTripParma['endCityCodeSy'] = city.syId;

    props.creatTripParma.tripList[legAll - 2]['endCityName'] = city.city;
    props.creatTripParma.tripList[legAll - 2]['endCityCodeSy'] = city.syId;
    props.creatTripParma.tripList[legAll - 2]['endDate'] = city.date;
    props.creatTripParma.tripList[legAll - 2]['endCityCode'] = city.cityCode;

    props.creatTripParma.tripList[legAll - 2]['tripDetailMapList'] = []

  } else {
    props.creatTripParma.tripList[legAll - 1]['beginCityName'] = city.city;
    props.creatTripParma.tripList[legAll - 1]['beginDate'] = city.date;
    props.creatTripParma.tripList[legAll - 1]['beginCityCode'] = city.cityCode;
    props.creatTripParma.tripList[legAll - 1]['beginCityCodeSy'] = city.syId;

    props.creatTripParma.tripList[legAll - 2]['endCityName'] = city.city;
    props.creatTripParma.tripList[legAll - 2]['endDate'] = city.date;
    props.creatTripParma.tripList[legAll - 2]['endCityCode'] = city.cityCode;
    props.creatTripParma.tripList[legAll - 2]['endCityCodeSy'] = city.syId;

    props.creatTripParma.tripList[legAll - 1]['tripDetailMapList'] = []
    props.creatTripParma.tripList[legAll - 2]['tripDetailMapList'] = []

  }
  getAllBugde();

  city.active = false;
};

// 查询出行方式
const {
  data: travelList,
  run: travelListRun,
  loading: travelListLoading,
} = useRequest(tripApi.dataList, {
  defaultParams: [],
  manual: false,
});

interface budge {
  workNo: string;
  budgetCode: string;
  performCode: string;
}
const budgeParams = ref<budge>({
  workNo: '',
  budgetCode: '',
  performCode: '',
});

// 根据部门.个人查询预算
// const {
//   data: budgeQueryData,
//   run: budgeQueryiRun,
//   loading: budgeQueryLoading,
// } = useRequest(tripApi.budgeQuery, {
//   defaultParams: [budgeParams.value],
//   onSuccess: () => {
//     // detailsApiRun({ todoId: todoId.value, code: code.value })
//   },
//   manual: false,
// });

// [[{}],[{}]]

const cityListArr = ref<Array<Array<ICity>>>([]);

const eliminateSearchProvinceIds = [7,16,17]

watch(
  cityList,
  (newVal, oldVal) => {
    cityListArr.value = [];
    let groups = Math.ceil(newVal.length / 4);

    let cityListTemp:any = [];

    newVal.forEach((item, index) => {
      // 一组

      if (groups == 1) {
        cityListTemp = [...cityListTemp, item];
      } else {
        cityListTemp = [...cityListTemp, item];
        if ((index + 1) % 4 == 0) {
          cityListArr.value = [...cityListArr.value, cityListTemp];
          cityListTemp = [];
        }
      }

      if (index + 1 == newVal.length) {
        if (groups == 1 && index == 3) {
          cityListArr.value = [...cityListArr.value, cityListTemp, []];
        } else {
          cityListArr.value = [...cityListArr.value, cityListTemp];
        }
      }
    });

    // 详情页摘掉最后一个空对象
    if (props.isDetail && cityListArr.value.length > 1) {
      if (
        cityListArr.value[cityListArr.value.length - 1] &&
        cityListArr.value[cityListArr.value.length - 1].length == 0
      )
        cityListArr.value.pop();
    }
  },
  {
    deep: true,
  },
);
// 根据原因id返回是否要展示填写具体原因

const isShowReasonText = (id: number|string ) => {
  if(!id) return false
  return resonList.value.find(item => item.id == id).reasonInfo == '其他原因'
}

// watch(
//   () => props.creatTripParma?.haierBudgetPayOccupyRequest?.leftAmt,
//   (newVal, oldVal) => {
//     formData.value.mainPersonMoney = newVal;
//   }
// )

// 表格结束
</script>
<template>
  <div class="whole-line" style="margin-top: 20px">
    <div class="title whole-line">行程计划与费用预算</div>

    <h-form
      labelAlign="left"
      ref="formRef"
      class="mt-30"
      :label-col="labelCol"
      :model="props.creatTripParma?.haierBudgetPayOccupyRequest"
      :wrapper-col="wrapperCol"
      :disabled="props.isDetail"
    >
      <h-form-item name="budgetSystemCode" :required="props.isChange? false : true" :rules="[{ required: props.isChange? false : true, message: '请选择费用预算' }]"  v-if="creatTripParma.travelReserveFlag ">
        <template #label>
          <span
            >费用预算<a-tooltip>
              <template #title>选择部门国内差旅费预算承接人。</template>
              <question-circleOutlined class="icon" /> </a-tooltip
          ></span>
        </template>
        <div class="flex font-color" v-if="!props.isDetail && !props.isChange">
          预算可用余额
          <span class="font-color-black mr-10 ml-10">
            {{ props.creatTripParma?.haierBudgetPayOccupyRequest?.leftAmt ? `¥ ${props.creatTripParma?.haierBudgetPayOccupyRequest?.leftAmt}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : '0' }}</span
          >

          <span class="font-color-grey" v-if="!props.creatTripParma?.haierBudgetPayOccupyRequest?.isQueryDept" > {{ props.creatTripParma?.haierBudgetPayOccupyRequest?.budgeterName || mainPerson()?.travelUserName }}的个人预算 </span>
          <span class="font-color-grey" v-else> {{ props.creatTripParma?.haierBudgetPayOccupyRequest?.budgetDepartmentName }}的部门预算 </span>

          <span>,</span>
          <span class="primary-color pointer" @click="openBudgetModal"
            >选择其他预算</span
          >
        </div>
        <div class="" v-else>
          <span class="font-color-grey" v-if="!props.creatTripParma?.haierBudgetPayOccupyRequest?.isQueryDept" > {{ props.creatTripParma?.haierBudgetPayOccupyRequest?.budgeterName || mainPerson()?.travelUserName }}的个人预算 </span>
          <span class="font-color-grey" v-else> {{ props.creatTripParma?.haierBudgetPayOccupyRequest?.budgetDepartmentName }}的部门预算 </span>
          <span>,</span>
          <span class="primary-color pointer" @click="openBudgetDetailShow"
            >查看详情</span
          >
        </div>
      </h-form-item>
      <h-form-item ref="amountSum" :class="props.isDetail || props.isChange ? '':'create-amount-form'"  name="amountSum" v-if="creatTripParma.travelReserveFlag">
        
        <template #label v-if="props.isDetail || props.isChange">
          <span
            >费用预算<a-tooltip>
              <template #title>费用预算</template>
              <question-circleOutlined class="icon" /> </a-tooltip
          ></span>
        </template>


        <div class="flex font-color">
          费用预算合计
          <span class="font-color-black mr-10 ml-10">
            {{ `¥ ${props.creatTripParma.amountSum || 0}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') }}</span
          >
          <span class="primary-color pointer detail ml-10" @click="showBudgetDetailModal">查看每人明细</span>
        </div>
      </h-form-item>

      <h-form-item ref="plan" plan="name" required>
        <template #label>
          <span
            >行程计划<a-tooltip>
              <template #title>按照出差任务选择始发、目的地以及中转地，务必包含全部途径地及停留时间。事前做好差旅行为计划，提前规划行程（国内机票建议提前5天预订出票），在满足出行需求的前提下，确保成本最优；</template>
              <question-circleOutlined class="icon" /> </a-tooltip
          ></span>
        </template>

        <div class="travel">
          <div class="flex city-list" :class="i % 2 == 0 ? '' : 'reverse'" v-for="(list, i) in cityListArr" :key="i">
            <div
              class="flex block list-item"
              
              @blur="city.active = false"
              v-for="(city, index) in list"
              :key="index"
            >
              <div class="pict flex">
                <environment-filled class="primary-color font-size-24" />
                <div
                  v-if="!(props.isDetail && i + 1 == cityListArr.length && index + 1 == list.length && i % 2 == 0)"
                  class="dashed-line"
                ></div>
              </div>
              <!-- 鼠标双击变为可编辑 -->
              <template v-if="city.active">
                <div class="city mt-10">
                  <city-chose
                    :showInternational="false"
                    :value="city.city"
                    :bordered="false"
                    :index="index"
                    :i="i"
                    :eliminateSearchProvinceIds="eliminateSearchProvinceIds"
                    @chosedCity="chosedEditCity"
                  ></city-chose>
                </div>
                <div class="date mt-10 date-select flex-center">
                  <div v-if="!(i == 0 && index == 0)">{{ formateTime(disabledDate11) }} -</div>
                  <h-date-picker
                    @blur="saveEditCity(city)"
                    :disabled-date="disabledDate4"
                    @change="saveDetailCity(city, index, i)"
                    size="small"
                    valueFormat="YYYY-MM-DD"
                    :bordered="false"
                    v-model:value="city.date"
                  />
                </div>
              </template>

              <template v-else>
                <div class="city mt-10">
                  {{ city.city }} 
                  <EditOutlined v-if="!props.isDetail" @click="editCity(city, index, i)" style="color: rgba(0, 0, 0, 0.45)" />
                </div>
                <div class="date mt-10" v-if="i == 0 && index == 0">
                  {{ formateTime(city.date) }}
                  <EditOutlined v-if="!props.isDetail" @click="editCity(city, index, i)" style="color: rgba(0, 0, 0, 0.45)" />
                </div>
                <div class="date mt-10" style="min-width: 120px" v-else>
                  {{
                    index == 0 ? formateTime(cityListArr[i - 1][3].date) : formateTime(cityListArr[i][index - 1].date)
                  }}
                  - {{ formateTime(city.date) }}
                  <EditOutlined v-if="!props.isDetail" @click="editCity(city, index, i)" style="color: rgba(0, 0, 0, 0.45)" />
                </div>
              </template>
            </div>
            <div
              v-if="i + 1 != cityListArr.length"
              :class="i % 2 == 0 ? 'inflection-point-right' : 'inflection-point-left'"
            ></div>
            <!-- 新增途径点城市 后面的 -->
            <template v-if="!props.isDetail && i + 1 == cityListArr.length && showNextAddCity">
              <div class="flex block list-item">
                <div class="pict flex">
                  <!-- <environment-filled class="eee-color font-size-24" /> -->
                  <environment-two-tone class="font-size-24" />
                  <div class="dashed-line"></div>
                </div>
                <div class="city mt-10">
                  <city-chose
                    class="city-select"
                    :showInternational="false"
                    :value="newEndCity.name"
                    :eliminateSearchProvinceIds="eliminateSearchProvinceIds"
                    :bordered="false"
                    @chosedCity="(city:CityItem) => {chosedCity(city)}"
                  ></city-chose>
                </div>
                <div class="date mt-10 date-select flex-center">
                  <div>{{ formateTime(props?.creatTripParma.endDate) }} - </div>
                  <h-date-picker
                    style="padding-left: 0;padding-right: 0;"
                    @change="rangeChange"
                    :disabled-date="disabledDate"
                    size="small"
                    valueFormat="YYYY-MM-DD"
                    :bordered="false"
                    v-model:value="endTime"
                    :defaultPickerValue="defaultPickerValue(cityList[cityList.length-1]?.date)"
                  />

                </div>
              </div>
            </template>
            <div v-if="!(i % 2 == 0) && !props.isDetail && i + 1 == cityListArr.length" class="dashed-line2"></div>

            <h-button
              type="dashed"
              size="small"
              class="flex-center border-radios button-padding add-btn2 primary-color"
              style="padding: 0 8px !important"
              @click="showAddCity"
              v-if="!props.isDetail && i + 1 == cityListArr.length"
            >
              <template #icon>
                <plus-outlined class="font-size-12" />
              </template>
              <span class="font-size-12">添加行程</span>
            </h-button>
          </div>

          <!-- 第一次添加新增两个城市 -->
          <template v-if="!cityList.length && !props.isDetail && showFirstAddCity">
            <!-- 新增途径点城市 第一个 -->
            <div class="flex block">
              <div class="pict flex">
                <environment-two-tone class="font-size-24" />
                <div class="dashed-line"></div>
              </div>
              <div class="city mt-10">
                <city-chose
                  class="city-select"
                  :placeholder="'出发城市'"
                  :showInternational="false"
                  :eliminateSearchProvinceIds="eliminateSearchProvinceIds"
                  :value="newBeginCity?.name"
                  :bordered="false"
                  @chosedCity="(city:CityItem) => {chosedBeginCity(city)}"
                ></city-chose>
              </div>
              <div class="date date-select mt-10">
                <h-date-picker class="" placeholder="出发日期" @change="rangeChange" :disabled-date="disabledDate2" :defaultPickerValue="defaultPickerValue(endTime)" size="small" valueFormat="YYYY-MM-DD" :bordered="false" v-model:value="beginTime" />
              </div>
            </div>
            <!-- 新增途径点城市 后面的 -->
            <div class="flex block">
              <div class="pict flex">
                <!-- <environment-filled class="eee-color font-size-24" /> -->
                <environment-two-tone class="font-size-24" />
                <div class="dashed-line"></div>
              </div>
              <div class="city mt-10">
                <city-chose
                  class="city-select"
                  :showInternational="false"
                  :eliminateSearchProvinceIds="eliminateSearchProvinceIds"
                  :value="newEndCity.name"
                  :bordered="false"
                  @chosedCity="(city:CityItem) => {chosedCity(city)}"
                ></city-chose>
              </div>
              <div class="date date-select mt-10">
                <h-date-picker
                  @change="rangeChange"
                  :disabled-date="disabledDate3"
                  :defaultPickerValue="defaultPickerValue(beginTime)"
                  size="small"
                  valueFormat="YYYY-MM-DD"
                  :bordered="false"
                  v-model:value="endTime"
                />
              </div>
            </div>
          </template>
          <h-button
            type="dashed"
            size="small"
            class="flex-center border-radios ml-10 button-padding add-btn2 primary-color"
            @click="showAddCity"
            v-if="!cityList.length && !props.isDetail"
          >
            <template #icon>
              <plus-outlined class="font-size-12" />
            </template>
            <span class="font-size-12">添加行程</span>
          </h-button>
        </div>
        <div class="travel-list">
          <!-- 行程费用 -->
          <div class="travel-item" v-for="(trip, index) in props.creatTripParma.tripList" :key="index">
            <div class="travel-item-head mt-20 flex">
              <div class="head-left flex">
                <div class="head-left-num mr-10">第 {{ index + 1 }} 行程</div>
                <div class="flex font-size-14">
                  {{ trip.beginCityName }} - {{ trip.endCityName }}
                  <div class="shu ml-10 mr-10"></div>
                  {{ formateTime(trip.beginDate) }} - {{ formateTime(trip.endDate) }}
                </div>
              </div>
              <div class="head-right flex">
                <div
                  v-if="!props.isDetail && props.creatTripParma.travelReserveFlag"
                  class="add-btn primary-color font-size-14 pl-8 pr-8 pointer"
                  @click="showStandardOpen(trip)"
                >
                  <span class="font-size-14">查看差旅标准</span>
                </div>
                <div class="shu" v-if="!props.isDetail && props.creatTripParma.travelReserveFlag"></div>

                <div
                  v-if="!props.isDetail && props.creatTripParma.travelReserveFlag"
                  class="add-btn primary-color pl-8 pr-8 pointer"
                  @click="addMoney(index)"
                >
                  <span class="font-size-14">添加费用</span>
                </div>

                <div class="shu" v-if="!props.isDetail && props.creatTripParma.travelReserveFlag"></div>
                <a-popconfirm
                  title="删除行程无法恢复,确定删除吗?"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="delTrip(index)"
                >
                  <div v-if="!props.isDetail" class="add-btn primary-color pl-8 pr-8 pointer">
                    <span class="font-size-14">删除行程</span>
                  </div>
                </a-popconfirm>
              </div>
            </div>
            <div class="travel-item-content mt-10" v-show="props.creatTripParma.travelReserveFlag">
              <ConfigProvider>
                <template #renderEmpty>
                  <div class="my-table-empty pointer" @click="addMoney(index)">
                    <template v-if="props.isDetail">
                      <span>暂无数据</span>
                    </template>
                    <template v-else>
                      <PlusOutlined class="mr-10" />
                      <span>添加费用</span>
                    </template>
                  </div>
                </template>
                <h-table
                  :columns="props.isDetail ? detailColumns : columns"
                  :data-source="trip.tripDetailMapList"
                  size="small"
                  :pagination="false"
                  bordered
                >
                  <template #headerCell="{ column }">
                    <template v-if="column.key === 'excessiveReasonId'">
                      <span>
                        {{ column.title }}
                        <a-tooltip>
                          <template #title>差旅预订超标原因，便于后续差旅数据分析及差旅管理</template>
                          <question-circleOutlined class="icon" />
                        </a-tooltip>
                      </span>
                    </template>

                    <template v-if="column.key === 'budgetAmount'">
                      <span>
                        {{ column.title }}
                        <a-tooltip>
                          <template #title>按照个人差旅标准计算默认金额，可修改金额，结算以实际订单金额为准，剩余预算将在出差申请单行程确认后释放。机票预算：默认差标舱位全票价金额；火车票预算：默认出行当日差标坐席最高价；酒店预算：入住城市差标。</template>
                          <question-circleOutlined class="icon" />
                        </a-tooltip>
                      </span>
                    </template>
                  </template>

                  <template #bodyCell="{ column, text, record, index: detailIndex }">
                    <!--超标原因 -->
                    <template v-if="column.dataIndex === 'excessiveReasonId'">
                      <h-tooltip v-if="record.excessiveFlag">
                        <template #title>
                          <span>{{
                            resonList.filter((item) => item.id == record.excessiveReasonId)[0]?.reasonInfo || '-'
                          }}</span>
                        </template>
                        <div style="display: flex; " v-if="!props.isDetail && record.productCode != '03'  && record.productCode != '05'">
                          <h-select
                            class="cb-select"
                            :disabled="!record.excessiveFlag"
                            style="width: 100%"
                            :bordered="false"
                            size="small"
                            v-model:value="record[column.dataIndex]"
                            @change="record.excessiveOtherReasonDesc = null"
                            :options="resonList.filter((item) => item.productCode == record.productCode)"
                            :placeholder="record.errorFlag ? '请选择超标原因' : ''"
                            :fieldNames="{ label: 'reasonInfo', value: 'id' }"
                          />
                          <!-- 如果用户选择超标原因为其他原因,增加手填 -->
                          <h-input placeholder="请填写其他原因" style="width: 100%" v-if="isShowReasonText(record.excessiveReasonId)" v-model:value="record.excessiveOtherReasonDesc" />
                        </div>
                        <div v-else class="text-hidden font-size-14">
                          {{ resonList.filter((item) => item.id == record.excessiveReasonId)[0]?.reasonInfo || '-' }}
                          {{ record.excessiveOtherReasonDesc && record.productCode != '03'  && record.productCode != '05' ? `,${record.excessiveOtherReasonDesc}` : ''  }}
                        </div>
                        
                      </h-tooltip>
                    </template>

                    <!-- 费用预算 -->
                    <template v-if="column.dataIndex === 'budgetAmount'">
                      <a-popover
                       
                        color="white"
                        placement="topLeft"
                        v-if="!props.isDetail"
                      >
                        <template #title>
                          <div v-if="record.budgetAmountDesc" style="width: 500px">
                            <h-table
                              style="margin: 0"
                              :columns="descColumns"
                              :data-source="record.budgetAmountDesc"
                              size="small"
                              :pagination="false"
                              bordered
                            >
                            </h-table>
                          </div>
                          <div v-else>暂无</div>
                        </template>
                        <div style="padding-left: 10px">
                          <h-input-number
                            :bordered="false"
                            style="width: 100%"
                            size="small"
                            @change="changeMoney(trip, record)"
                            @blur="getMemberBudget(trip, record, false)"
                            :controls="false"
                            v-model:value="record[column.dataIndex]"
                            :formatter="(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                            :parser="(value) => value.replace(/\¥\s?|(,*)/g, '')"
                          >
                          <template #addonAfter>
                            <!-- <a-tag class="my-tag" :bordered="false" v-if="record.excessiveFlag" color="error"
                            >超标</a-tag> -->
                            <div class="font-size-12" v-if="record.excessiveFlag && record.productCode != '03'  && record.productCode != '05'" style="color: red;">超标</div>
                            <EditOutlined v-else style="color: rgba(0, 0, 0, 0.45)" />
                          </template>
                          </h-input-number>
                          
                        </div>
                      </a-popover>
                      <div v-else style="padding: 0 10px" class="font-size-14">
                        <a-popover color="white" 
                        placement="topLeft">
                          <template #title>
                            <div v-if="record.budgetAmountDesc" style="width: 500px">
                              <h-table
                                style="margin: 0"
                                :columns="descColumns"
                                :data-source="record.budgetAmountDesc"
                                size="small"
                                :pagination="false"
                                bordered
                              >
                              </h-table>
                            </div>
                          <div v-else>暂无</div>
                          </template>
                          <div style="padding-left: 10px">
                            {{ `¥ ${record.budgetAmount}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') }}
                            <a-tag class="my-tag" :bordered="false" v-show="record.excessiveFlag && record.productCode != '03'  && record.productCode != '05'" color="error"
                              >超标</a-tag
                            >
                          </div>
                        </a-popover>
                      </div>
                    </template>

                    <!-- 费用名称/项目 -->
                    <template v-else-if="column.dataIndex === 'productCode'">
                      <h-select
                        v-if="!props.isDetail"
                        v-model:value="record[column.dataIndex]"
                        style="width: 100%"
                        size="small"
                        :bordered="false"
                        placeholder="请选择"
                        @change="choseNewType(trip, record, detailIndex)"
                      >
                        <h-select-option
                          v-for="(pitem, pindex) in travelList"
                          :value="pitem.productCode"
                          :label="pitem.productName"
                          :key="pindex"
                        >
                          <div class="product-option">
                            <img
                              class="product-icon"
                              :src="pitem.iconUrl ? pitem.iconUrl : '../../../assets/image/trip/plane.png'"
                              alt=""
                            />
                            <span class="product-text font-size-14">{{ pitem.productName }}</span>
                          </div>
                        </h-select-option>
                      </h-select>

                      <div class="product-option" v-else>
                        <img
                          class="product-icon ml-10"
                          :src="travelList?.filter((item) => item.productCode == record.productCode)[0]?.iconUrl"
                          alt=""
                        />
                        <span class="product-text font-size-14">
                          {{
                            travelList?.filter((item) => item.productCode == record.productCode)[0]?.productName
                          }}</span
                        >
                      </div>
                    </template>
                    <!-- 出差人员 -->
                    <template v-else-if="column.dataIndex === 'personIdList'">
                      <!-- <h-tag v-for="tag, index in record.travelApplyTripDetailList">{{tag.travelUserName}}</h-tag> -->

                      <h-tooltip v-if="props.isDetail">
                        <template #title>
                          <span>{{ stringName(record.travelApplyTripDetailList) }}</span>
                        </template>
                        <div class="text-hidden font-size-14">{{ stringName(record.travelApplyTripDetailList) }}</div>
                      </h-tooltip>

                      
                      <h-select
                        v-else
                        :fieldNames="{ label: 'travelUserName', value: 'travelUserSyId' }"
                        v-model:value="record[column.dataIndex]"
                        style="width: 100%"
                        size="small"
                        :bordered="false"
                        :max-tag-count="2"
                        mode="multiple"
                        :options="
                          newOptions(
                            props.creatTripParma.travelerList,
                            trip.tripDetailMapList,
                            record.productCode,
                            detailIndex,
                          )
                        "
                        @change="changeTraveler(trip, record, record[column.dataIndex])"
                      >
                        <template #tagRender="{ value, label, closable, onClose, option }">
                          <a-tooltip>
                            <!-- <template #title>{{ label[0].children }}</template> -->
                            <template #title>{{ label }}</template>

                            <div class="tag">
                              <span class="font-size-14 font-color mr-5">{{
                                label.length > 3 ? label.substring(0, 3) + '...' : label
                              }}</span>
                              <close-outlined class="close font-size-12 font-color-close" @click="onClose" v-if="closable" />
                            </div>
                          </a-tooltip>
                        </template>

                        <template #maxTagPlaceholder="omittedValues">
                          <a-tooltip>
                            <template #title>
                              <div
                                v-if="omittedValues && omittedValues.length > 0"
                                v-for="(item, index) in omittedValues"
                                :key="index"
                              >
                                {{ item.label }}
                              </div>
                            </template>
                            <div class="tag">
                              <span class="font-size-14 font-color">+ {{ omittedValues.length }}... </span>
                            </div>
                          </a-tooltip>
                        </template>
                      </h-select>
                    </template>

                    <!-- 保险 -->
                    <template v-else-if="column.dataIndex === 'insuranceFlag'">
                      <template
                        v-if="
                          record.productCode &&
                          travelList?.filter((item) => item.productCode === record.productCode)[0].insuranceAmount > 0
                        "
                      >
                        <a-checkbox
                          v-model:checked="record[column.dataIndex]"
                          size="small"
                          @change="changeInsurance(trip, record, record[column.dataIndex])"
                          >¥{{
                            travelList?.filter((item) => item.productCode === record.productCode)[0].insuranceAmount
                          }}</a-checkbox
                        >
                      </template>

                      <template v-else>
                        <div>-</div>
                      </template>
                    </template>

                    <template v-else-if="column.dataIndex === 'operation' && !props.isDetail">
                      <div class="editable-row-operations">
                        <span>
                          <a-popover
                            v-if="record.productCode == '02'"
                            style="z-index:888"
                            title="选择火车站点"
                            trigger="click"
                            :overlayStyle="{ width: '300px','z-index':555 }"
                            :open="record.popVis"
                            :mouseLeaveDelay="3"
                          >
                            <template #content>
                              <a-row style="margin-bottom: 10px">
                                <a-col :span="6" class="flex-center">始发站:</a-col>
                                <a-col :span="18">
                                  <train-chose :value="record.startTrainName" :cityId="trip.beginCityCode" @chosed="chosedBeginTrainStation($event,record)"></train-chose>
                                </a-col>
                              </a-row>
                              <a-row>
                                <a-col :span="6" class="flex-center">终点站:</a-col>
                                <a-col :span="18">
                                  <train-chose :value="record.endTrainName" :cityId="trip.endCityCode" @chosed="chosedEndTrainStation($event,record)"></train-chose>
                                </a-col>
                              </a-row>
                              <a-row class="mt-10 flex" style="flex-direction: row-reverse;">
                                <a-col :span="6" class="flex-center">
                                  <a-button type="primary" @click="record.popVis = false">确定</a-button>
                                </a-col>
                              </a-row>
                            </template>
                            <icon12306 @click="chosedTrainStation(trip, detailIndex)" class="chose-position mr-10" />
                          </a-popover>
                          <SearchOutlined v-if="record.productCode != '03'" class="mr-10" @click="goToThirdParty(trip,record)" />
                          <a-popconfirm title="确定删除此条费用记录?" @confirm="deleteItem(record, index)">
                            <DeleteOutlined />
                          </a-popconfirm>
                        </span>
                      </div>
                    </template>
                  </template>

                  <template #summary v-if="trip?.tripDetailMapList?.length > 0 && !props.isDetail">
                    <a-table-summary fixed="bottom">
                      <a-table-summary-row>
                        <a-table-summary-cell :col-span="6">
                          <div class="my-table-empty pointer" @click="addMoney(index)">
                            <PlusOutlined class="mr-10" />
                            <span>添加费用</span>
                          </div>
                        </a-table-summary-cell>
                      </a-table-summary-row>
                    </a-table-summary>
                  </template>
                </h-table>
              </ConfigProvider>
            </div>
          </div>
        </div>
      </h-form-item>
    </h-form>
  </div>
</template>

<style lang="less" scoped>
@import url('./trip.less');

:deep(.ant-popover) {
  z-index: 888 !important; /* 根据需要调整 */
}

.my-tag {
  margin: 0px;
  position: absolute;
  right: 0;
  padding: 0 4px;
  font-size: 10px;
}
:deep(.ant-table-cell) {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.font-size-16 {
  font-size: 16px;
}
:deep(.ant-select-selection-item) {
  background: rgba(0, 0, 0, 0) !important;
  border: none !important;
}

.travel-item {
  :deep(.ant-select-selection-item) {
    background: rgba(0, 0, 0, 0) !important;
    border: none !important;
    font-size: 14px;
    padding: 0 !important;
  }
  :deep(.ant-input-number-input, .ant-input-borderless, .ant-checkbox-wrapper) {
    font-size: 14px;
  }
  :deep(.ant-checkbox-inner) {
    transform: scale(0.8);
  }
  :deep(.ant-input) {
    background: rgba(0, 0, 0, 0) !important;
    border: none !important;
  }
  :deep(.ant-input:focus) {
    background: rgba(0, 0, 0, 0) !important;
    box-shadow: none !important;
  }
}

.travel {
  flex-wrap: wrap;
}
.reverse {
  flex-direction: row-reverse;
}
.list-item {
  width: 236px;
  min-height: 100px;
}
.list-item > .city {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.list-item > .date {
}
.list-item-max {
  flex: 1;
  min-width: 260px;
  max-width: 260px;
  position: relative;
}
.city-list {
  width: 100%;
  position: relative;
  margin-bottom: 16px;
}
.text-hidden {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
  padding: 0 10px;
}
.inflection-point-right {
  border: 2px dashed #0073e5;
  width: 24px;
  min-height: 118px;
  border-left: none;
  position: absolute;
  top: 12px;
  right: -24px;
  border-radius: 0 8px 8px 0;
}
.inflection-point-left {
  border: 2px dashed #0073e5;
  min-height: 118px;
  border-right: none;
  position: absolute;
  top: 12px;
  width: 60px;
  left: -60px;
  border-radius: 8px 0 0 8px;
}
.dashed-line2 {
  margin-top: 12px;
  margin-left: 3px;
  border-top: 2px dashed #0073e5;
  height: 50%;
  max-width: 40px;
  min-width: 10px;
}
.chose-position {
  // position: absolute;
  // left: -8px;
  // top: 14px;
}
:deep(.ant-select-selection-item) {
}
:deep(.city-chose-input) {
  padding: 0 0 0 8px !important;
}
.budget-tooltip {
  :deep(.ant-tooltip-content) {
    width: 400px !important;
  }
  :deep(.ant-tooltip-inner, .ant-spin-container) {
    width: 400px !important;
  }
  :deep(.ant-popover-title) {
    width: 400px !important;
  }
}

:deep(.ant-input-number-group-addon) {
  border: none;
  background: rgba(0, 0, 0, 0);
}

.create-amount-form {
  .font-color {
    margin-left: 142px;
  }
  :deep(.ant-form-item-explain-error) {
    margin-left: 142px;
  }

}
.cb-select {
  :deep(.ant-select-selection-placeholder) {
    color:red;
  }
}

.city-select {
  :deep(.ant-input::placeholder) {
    color: #0073E5;
  }
}
.date-select {
  :deep(.ant-picker-input > input::placeholder) {
    color: #0073E5;
  }
}


</style>