<template>
  <div class="container">
    <h-form
      ref="from"
      :model="searchKey"
      @finish="onReFilterChange"
      style="width: 100%"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <h-row :gutter="24">
        <h-col :span="8">
          <h-form-item has-feedback label="订单编号" name="destineNo">
            <h-input v-model:value="searchKey.destineNo" placeholder="订单编号" allow-clear />
          </h-form-item>
        </h-col>

        <h-col :span="8">
          <h-form-item :auto-link="false" has-feedback label="出发地" name="beginCityCode">
            <city-chose :value="chosedBeginCityName" @chosedCity="chosedBeginCity"></city-chose>
          </h-form-item>
        </h-col>
        <h-col :span="8">
          <h-form-item :auto-link="false" has-feedback label="目的地" name="endCityCode">
            <city-chose :value="chosedEndCityName" @chosedCity="chosedEndCity"></city-chose>
          </h-form-item>
        </h-col>

        <h-col :span="8">
          <h-form-item has-feedback label="产品类型" name="destineInfo">
            <h-select v-model:value="searchKey.destineInfo" placeholder="产品类型">
              <h-select-option value="">全部</h-select-option>
              <h-select-option v-for="(item, index) in destineList" :key="index" :value="item.value">{{
                item.label
              }}</h-select-option>
            </h-select>
          </h-form-item>
        </h-col>

        <h-col :span="16">
          <h-form-item
            has-feedback
            label="行程日期"
            name="timeRange"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 20 }"
          >
            <h-range-picker
              v-model:value="searchKey.timeRange"
              value-format="YYYY-MM-DD"
              @change="onTimeChange"
              style="width: 100%"
            />
          </h-form-item>
        </h-col>

        <h-col :span="8">
          <h-form-item has-feedback label="出差类型" name="evectionType">
            <h-select v-model:value="searchKey.evectionType" placeholder="出差类型">
              <h-select-option value="">全部</h-select-option>
              <h-select-option v-for="(item, index) in evectionTypeList" :key="index" :value="item.value">{{
                item.label
              }}</h-select-option>
            </h-select>
          </h-form-item>
        </h-col>

        <h-col :span="16">
          <h-form-item
            has-feedback
            label="下单日期"
            name="cteatTimeRange"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 20 }"
          >
            <h-range-picker
              v-model:value="searchKey.cteatTimeRange"
              value-format="YYYY-MM-DD"
              @change=" onCreateTimeChange"
              style="width: 100%"
            />
          </h-form-item>
        </h-col>

        <h-col :span="8">
          <h-form-item has-feedback label="订单执行人" name="userNameZx">

            <user-select
              :value="searchKey.userNameZx"
              :params="params"
              @change="(userInfo: IUserInfo) =>  userNameChange(userInfo)"
            />

          </h-form-item>
        </h-col>

        <h-col :span="8">
          <h-form-item has-feedback label="订单状态" name="destineStatus">
            <h-select v-model:value="searchKey.destineStatus" placeholder="订单状态">
              <h-select-option value="">全部</h-select-option>
              <h-select-option v-for="(item, index) in teamState" :key="index" :value="item.value">{{
                item.label
              }}</h-select-option>
            </h-select>
          </h-form-item>
        </h-col>
      </h-row>
      <h-row justify="center">
        <div class="flexCon">
          <h-space>
            <h-button @click="handleReset">重置</h-button>
            <h-button type="primary" html-type="submit">查询</h-button>
          </h-space>
        </div>
      </h-row>
    </h-form>

    <h-spin :spinning="dataLoading">
      <template v-if="dataList && dataList.length > 0">
        <div class="banner-contain list" v-for="(data, index) in dataList" :key="index">
          <div class="banner-contain-bottom">
            <div class="banner-contain-data">
              <div>
                <div class="my-title">
                  {{ data.destineNo }}
                </div>
                <div class="data-time">
                  出差类型: {{data.evectionType ? '因私' : '因公'}}
                </div>
                
              </div>
              <div class="data-btn">
                <!-- 订单状态 -->
                <a-tooltip>
                  <template #title>订单状态</template>
                  <h-tag :color="teamListStateTagColorMap[data.destineStatus] || 'blue'">
                    <span>{{ TeamListStatusEnum[data.destineStatus] || '' }} <QuestionCircleOutlined /></span>
                  </h-tag>
                </a-tooltip>
                
              </div>
            </div>
            <!-- 个人信息 -->

            <a-descriptions class="banner-detail">
              <a-descriptions-item label="行程日期">{{formatDate(data.beginDate) || ''}} - {{formatDate(data.endDate) || ''}}</a-descriptions-item>
              <a-descriptions-item label="产品类型">{{ toLabel(data.destineInfo) }}</a-descriptions-item>
              <a-descriptions-item label="订单执行人">{{ (data.transactorName) || '-' }}</a-descriptions-item>

              <a-descriptions-item label="下单日期">{{formatDate2(data.gmtCreate)}}</a-descriptions-item>
              <a-descriptions-item label="起止城市">{{data.beginCityName }} - {{data.endCityName }}</a-descriptions-item>
              
              <!-- <a-descriptions-item label="节省金额">¥{{ data.saveAmount }}</a-descriptions-item> -->
            </a-descriptions>

            <div class="btn-contain">
             
           
              <a-button
                class="mr-10"
                size="small"
                v-if="data.destineStatus == '10'"
                :icon="h(EditOutlined)"
                @click="goToSubmit(data.id)"
                >继续提交</a-button
              >
              <a-button
                class="mr-10"
                type="primary"
                size="small"
                :icon="h(ContainerOutlined)"
                @click="goToDetail(data.id)"
                >详情</a-button
              >

              <a-button
                v-if="data.destineStatus!='90' && data.destineStatus!='30' && data.destineStatus!='40' && data.destineStatus != '10'"
                class="mr-10"
                type="primary"
                size="small"
                danger
                :icon="h(StopOutlined)"
                @click="recallTeam(data.id)"
                >撤回</a-button
              >
             
            </div>
        
          </div>
        </div>
      </template>
      <div class="page" v-show="pagination.total && pagination.total > 0">
        <h-pagination
          v-model:current="pagination.current"
          show-size-changer
          show-quick-jumper
          :total="pagination.total"
          @change="onPageChange"
        />
      </div>

      <div class="empty" v-if="!dataList || dataList.length === 0">
        <h-empty />
      </div>
    </h-spin>
  </div>
</template>
<script setup lang="ts">
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';
import {
  Button as hButton,
  Col as hCol,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Tag as hTag,
  Spin as hSpin,
  Empty as hEmpty,
  Space as hSpace,
  Pagination as hPagination,
  Checkbox as hCheckbox,
  message,
  FormItemRest as hFormItemRest,
} from 'ant-design-vue';
import {
  TripApprovalStatus,
  TripDocumentStatus,
  TeamListStatusEnum,
  teamListStateTagColorMap,
  TripChangeApprovalStatus,
  TripChangeApprovalStatusToTagColor,
  TripChangeStatus,
  TripBudgeStatus,
  TGetListParams,
  TCteateTeam,
  IUserInfo,
  ITraveler,
  TripApprovalStatusToTagColor,
  TripDocumentStatusToTagColor,
  TripChangeStatusToTagColor,
  TripBudgeStatusToTagColor,
  IDataListItem,
  ICity,
  CityItem,
  IUserListRequest,
} from '@haierbusiness-front/common-libs';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';

import { useOrderSearch } from '@haierbusiness-front/composables';
import { computed, onMounted, reactive, ref, watch, h } from 'vue';
import type { LocalrestType, LocalrestFilter } from '@haierbusiness-front/common-libs';
import { localrestApi } from '@haierbusiness-front/apis';
import {
  PaymentTypeEnum,
  RestaurantOrderStateEnum,
  orderStateTagColorMap,
  LocalHotelPaymentTypeEnum,
  RestaurantOrderApprovalStateEnum,
  approvalStateTagColorMap,
  RestaurantOrderPayMentStateEnum,
  paymentStateTagColorMap,
} from '@haierbusiness-front/common-libs';
import { getEnumOptions } from '@haierbusiness-front/utils';
// import { useOrderStore } from "@/store"
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { teamApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import type { PaginationProps } from 'ant-design-vue';
import dayjs from 'dayjs';
import { Modal } from 'ant-design-vue';
import { createVNode } from 'vue';

import {
  QuestionCircleOutlined,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  UndoOutlined,
  HighlightOutlined,
  ExclamationCircleOutlined,
  ShareAltOutlined,
  DollarOutlined,
  TagFilled,
  SearchOutlined,
  MoreOutlined,
  DownOutlined,
  ContainerOutlined,
  StopOutlined,
} from '@ant-design/icons-vue';
import { userInfo } from 'os';


const { loginUser } = storeToRefs(applicationStore(globalPinia));

const route = useRoute();

const labelCol = {
  span: 8,
};
const wrapperCol = {
  span: 16,
};

// 用户选择
const params = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});

const userNameChange = (userInfo: IUserInfo) => {

  searchKey.userNameZx = userInfo?.nickName || ''
  searchKey.transactorCode = userInfo?.username || ''
}

// 2024-01-02 转换为 2024年01月02日
const formatDate = (time:string |undefined) => {
  if (!time) {
    return ""
  }
  return dayjs(time).format('YYYY年MM月DD日') 
}

// 2024-01-02 转换为 2024年01月02日
const formatDate2 = (time:string |undefined) => {
  if (!time) {
    return ""
  }
  return dayjs(time).format('YYYY年MM月DD日 HH:mm:ss') 
}


const evectionTypeList = [
  {
    label: '因公',
    value: 0
  },
  {
    label: '因私',
    value: 1
  },
]

const destineList = [
  {
    label: '国内机票',
    value: 0
  },
  {
    label: '国际机票',
    value: 1
  },
  {
    label: '酒店',
    value: 2
  },
]
const productList = ref([
  {
    label: '国内机票',
    plain: true,
    value: 0,
    disabled: false,
  },
  {
    label: '国际机票',
    plain: true,
    value: 1,
    disabled: false,
  },
  {
    label: '酒店',
    plain: true,
    value: 2,
  },
]);
const toLabel = (str: string) => {
  if (!str) {
    return '-'
  }
  const strList = str.split(',')
  const name:Array<string> = []
  strList.forEach(item => {
    productList.value.forEach(ele => {
      if (item == ele.value) {
        name.push(ele.label)
      }
    });
  })
  return name.join('、')
}

const recallTeam = (id: string| undefined) => {
  const param:TCteateTeam = {
    id
  }

  Modal.confirm({
    title: '您确认吗?',
    icon: createVNode(ExclamationCircleOutlined),
    content: '确认要撤销这条团队票申请吗?',
    okText: '确认',
    cancelText: '取消',
    onOk() {
      teamApi.recallTeam(param).then((res) => {
        getTeamList()
      });
    },
    onCancel() {
      console.log('Cancel');
    },
  });
  
}

const from = ref();
// 本地跳转用于调试 跳转变更单 跳转详情
const teamDetail = import.meta.env.VITE_BUSINESS_TEAM_URL;

const goToDetail = (id: string) => {
  const url = teamDetail + '#' + '/pc/teamDetail?id=' + id;
  window.open(url);
};
const goToSubmit = (id: string) => {
  const url = teamDetail + '#' + '/pc/addTeam?id=' + id;
  window.open(url);
};


const chosedBeginCityName = ref<string>('');
const chosedEndCityName = ref<string>('');

const chosedBeginCity = (city: CityItem, index: number, i: number) => {
  searchKey.beginCityCode = city.citycode;
  chosedBeginCityName.value = city.name;
};

const chosedEndCity = (city: CityItem, index: number, i: number) => {
  searchKey.endCityCode = city.citycode;
  chosedEndCityName.value = city.name;
};


const businesstravel = import.meta.env.VITE_BUSINESSTRAVEL_URL;
const handle = (code: string) => {
  const url = businesstravel + '/localrest/#/personal/orderlist?cocode=' + code;
  window.open(url);
};



// 预订列表
const reserveList = ref([
  {
    start: '北京',
    startTime: '3月7号',
    last: '上海',
    lastTime: '10月19号',
  },
  {
    start: '北京',
    startTime: '3月7号',
    last: '上海',
    lastTime: '10月19号',
  },
  {
    start: '北京',
    startTime: '3月7号',
    last: '上海',
    lastTime: '10月19号',
  },
]);



// banner 路程信息
const stepData = ref([
  {
    name: '青岛',
    timer: '3月7号',
    isTrain: true,
    isFly: true,
  },
  {
    name: '上海',
    timer: '3月7日~3月8日',
    isTrain: true,
    isFly: false,
  },
  {
    name: '呼和浩特',
    timer: '3月20日',
    isTrain: false,
    isFly: false,
  },
]);

const searchKey = reactive<TGetListParams>({
  destineNo: '', //订单单号
  evectionType:'',
  beginDate: '',
  endDate: '',
  destineInfo: '',
  beginCityCode: '', //出发地
  endCityCode: '', // 目的地
  createBeginDate: '',
  destineStatus: '',
  userName: '',
  
  createEndDate: '',
  pageNum: 1,
  pageSize: 10,
});
const pagination = reactive<PaginationProps>({
  current: 1,
  total: 0,
  pageSize: 10,
});


const onReFilterChange = () => {
  searchKey.pageNum = 1;
  searchKey.pageSize = 10;
  pagination.pageSize = 10;
  pagination.current = 1;
  getTeamList()
};

const getTeamList = async() => {
  dataLoading.value = true;

  const res = await teamApi.pageList(searchKey);
  pagination.total = res?.total || 0;
  dataList.value = res?.records || [];
  dataLoading.value = false;
}

onMounted(() => {
  getTeamList()
});



const dataList = ref<Array<TCteateTeam>>([]);

const dataLoading = ref<boolean>(false)


const onPageChange = (page: number, pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = page;
  searchKey.pageNum = page;
  searchKey.pageSize = pageSize;
  getTeamList()
};

const handleReset = () => {
  from.value && from.value.resetFields();
  if (!searchKey.applyTime || searchKey.applyTime.length != 2) {
    searchKey.beginDate = '';
    searchKey.endDate = '';
  }
  chosedEndCityName.value = '';
  chosedBeginCityName.value = '';
  searchKey.beginCityCode = '';
  searchKey.endCityCode = '';
  searchKey.transactorCode= ""
  onReFilterChange();
};

const onCreateTimeChange = (dateRange: string[]) => {
  
  if (dateRange && dateRange.length === 2) {
    searchKey.createBeginDate = dateRange[0];
    searchKey.createEndDate = dateRange[1];
  } else {
    searchKey.createBeginDate = '';
    searchKey.createEndDate = '';
  }
};

const onTimeChange = (dateRange: string[]) => {
  if (dateRange && dateRange.length === 2) {
    searchKey.beginDate = dateRange[0];
    searchKey.endDate = dateRange[1];
  } else {
    searchKey.beginDate = '';
    searchKey.endDate = '';
  }
};

// 单据状态
const documentState = computed(() => {
  return getEnumOptions(TripDocumentStatus, true);
});

// 订单状态
const teamState = computed(() => {
  return getEnumOptions(TeamListStatusEnum, true);
});







</script>

<style scoped lang="less">
.empty {
  margin-top: 50px;
  border: 1px solid #f0f0f0;
  padding: 42px 24px 50px;
}
.container {
  display: flex;
  width: 100%;
  flex-direction: column;

  .search {
    display: flex;
    width: 100%;
  }
}

.list {
  display: flex;
  margin-top: 20px;
  width: 100%;
  flex-direction: column;

  .order-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-bottom: 20px;

    .order-header {
      width: 100%;
      background-color: #f5f5f5;
      height: 43px;
      line-height: 43px;
      padding-left: 20px;
      color: #333;
      font-size: 12px;
      border: 1px solid #eaeaea;
      display: flex;
      justify-content: space-between;

      .order-header-left {
        display: flex;
      }
      .order-header-right {
        display: flex;
        align-items: center;
      }
    }
    .order-body {
      display: flex;
      width: 100%;
      border: 1px solid #eaeaea;
      border-top: 0;
      flex-direction: row;

      .first {
        display: flex;
        flex: 3;
        padding: 24px 10px;
      }
      .second {
        display: flex;
        flex: 2;
        border-left: 1px solid #eaeaea;
        padding: 24px 10px;
      }
      .three {
        display: flex;
        flex: 2;
        border-left: 1px solid #eaeaea;
        padding: 24px 10px;
      }
      .last {
        display: flex;
        flex: 3;
        border-left: 1px solid #eaeaea;
        padding: 24px 10px;
      }

      .second,
      .three {
        .title {
          width: 80px;
          text-align: right;
          line-height: 20px;
        }

        .value {
          padding-left: 5px;
          width: calc(100% - 80px);
          line-height: 20px;
        }
      }

      .first,
      .last {
        .title {
          width: 100px;
          text-align: right;
          line-height: 20px;
        }

        .value {
          padding-left: 5px;
          width: calc(100% - 100px);
          line-height: 20px;
        }
      }
    }
    .order-footer {
      width: 100%;
      background-color: #f5f5f5;
      height: 43px;
      line-height: 43px;
      padding-right: 8px;
      color: #333;
      font-size: 12px;
      border-left: 1px solid #eaeaea;
      border-right: 1px solid #eaeaea;
      border-bottom: 1px solid #eaeaea;
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
    }
  }
}

.page {
  display: flex;
  width: 100%;
  flex-direction: row-reverse;
  margin-bottom: 20px;
}

.banner-contain {
  width: 100%;
  padding-top: 16px;
  margin-bottom: 20px;
  box-sizing: border-box;
  // background-image: url('@/assets/image/banner/baner-contain-bac.png');
  background-size: 100% 100%;
  .ban-con-title {
    display: flex;
    justify-content: space-between;
    padding-right: 24px;
    position: relative;
    .ban-title-left {
      font-weight: 600;
      font-size: 18px;
      color: #3983e5;
      padding-left: 24px;
      .ban-border {
        position: absolute;
        top: 0;
        left: 2px;
        width: 4px;
        height: 21px;
        background: linear-gradient(180deg, #3983e5 0%, #6bb9f4 100%);
      }
    }
    .ban-title-right {
      /* font-weight: 500; */
      font-size: 14px;
      color: #3983e5;
      display: flex;
      align-items: center;
      img {
        width: 18px;
        height: 18px;
      }
    }
  }
  .banner-contain-bottom {
    width: 100%;
    background-color: #fff;
    border-radius: 8px;
    .banner-contain-data {
      background-color: #f5f5f5;
      display: flex;
      height: 40px;
      align-items: center;
      padding: 0 12px;
      color: #262626;
      justify-content: space-between;
      font-weight: 400;
      border-bottom: 1px solid #eaeaea;
      width: 100%;
      background-color: #f5f5f5;
      height: 43px;
      line-height: 43px;
      padding-left: 20px;
      color: #333;
      font-size: 12px;
      border: 1px solid #eaeaea;
      div {
        display: flex;
        align-items: center;
      }
      .my-title {
        font-size: 14px;
        font-weight: 600;
        margin-right: 10px;
      }
      .data-one {
        width: 195px;
        height: 28px;
        border-radius: 2px;
        box-sizing: border-box;
        padding-left: 8px;
        :deep(.ant-select-selector) {
          width: 195px;
          height: 28px !important;
          line-height: 28px;
          background: #fafafa;
          border: 0;
        }
        :deep(.ant-select-selection-item) {
          line-height: 28px;
          font-weight: 500;
          box-shadow: 0 0 0 0 rgba(0, 0, 0, 0) !important;
        }
      }
      .data-time {
        font-size: 13px;
        margin-left: 20px;
        img {
          width: 16px;
          height: 16px;
          margin-right: 2px;
        }
        .peice-mar {
          margin-right: 8px;
        }
      }
      .data-btn {
        display: flex;
        margin-left: 33px;
        .data-sta {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 78px;
          height: 22px;
          font-size: 12px;
          color: #10a710;
          background: #f6ffed;
          border-radius: 2px;
          border: 1px solid #b7eb8f;
          img {
            width: 12px;
            height: 12px;
          }
        }
        .data-tra {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 52px;
          height: 22px;
          background: #f0f9ff;
          border-radius: 2px;
          border: 1px solid #a1d1ff;
          font-size: 12px;
          color: #0073e5;
          margin-left: 4px;
        }
      }
    }
    .banner-detail {
      padding: 20px 40px;
      background: #ffffff;
      box-sizing: border-box;
      font-weight: 400;
      display: flex;
      padding-right: 16px;
      font-family: '';
      display: flex;
      width: 100%;
      border: 1px solid #eaeaea;
      border-top: 0;
      flex-direction: row;
      .detail-info-i {
        display: flex;
        font-size: 14px;
        color: #8c8c8c;
        height: 20px;
        align-items: center;
        margin-bottom: 5px;

        .info-i-l {
          display: flex;
          align-items: center;
          width: 84px;
        }
        img {
          width: 16px;
          height: 16px;
        }
        .names {
          color: #262626;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .info-main {
          width: 200px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-family: '';
          /* margin-left: 8px; */
        }
      }
    }
    .banner-step {
      overflow: auto;
      display: flex;
      width: 800px;
      height: 72px;
      background: #fafafa;
      border-radius: 4px;
      display: flex;
      box-sizing: border-box;
      padding: 0 30px;
      color: rgba(0, 0, 0, 0.85);
      .step-item {
        display: flex;
        min-width: 200px;
        padding-top: 15px;
      }
      .step-item-start {
        min-width: 60px;
      }
      .step-name {
        // min-width: 145px;
        width: 45px;
        position: relative;
        > span {
          font-size: 14px;
        }
        .step-c {
          position: absolute;
          color: #8c8c8c;
          bottom: 32px;
          left: 0;
          width: 100px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          .city-name {
            flex: 1;
            overflow: hidden;
            display: inline-block;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .step-t {
          position: absolute;
          color: #8c8c8c;
          bottom: 14px;
          left: 0;
          width: 140px;
        }
      }
      .idol {
        border-bottom: 1px dashed #d9d9d9;
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        height: 11px;
        max-width: 100%;
        margin: 0 23px;
        min-width: 100px;
        .idol-img {
          position: relative;
          top: 3px;
          /* position: absolute;
                  top: -2px;
                  left: 50%;
                  transform: translateX(-50%); */
        }
        img {
          width: 24px;
          height: 24px;
          margin-left: 4px;
        }
      }
    }
    .hotal-img {
      width: 24px;
      height: 24px;
      margin-left: 4px;
    }
  }

  .btn-contain {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 16px;
    width: 100%;
    background-color: #f5f5f5;
    height: 43px;
    // line-height: 43px;
    padding-right: 8px;
    color: #333;
    font-size: 12px;
    border-left: 1px solid #eaeaea;
    border-right: 1px solid #eaeaea;
    border-bottom: 1px solid #eaeaea;
    display: flex;
    align-items: center;
    .btn {
      cursor: pointer;
      // width: 76px;
      // height: 24px;
      // background: linear-gradient(180deg, #6bb9f4 0%, #3983e5 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: #ffffff;
      font-weight: 400;
      border-radius: 3px;
      img {
        width: 14px;
        height: 14px;
        margin-right: 4px;
      }
    }
    .btn-yu {
      position: relative;
      .card-list {
        position: absolute;
        top: 28px;
        right: -10px;
        z-index: 999;
        width: 340px;
        /* height: 202px; */
        background: #ffffff;
        box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08),
          0px 3px 6px -4px rgba(0, 0, 0, 0.12);
        border-radius: 8px;
        padding: 20px;
        box-sizing: border-box;

        .triangle-con {
          position: absolute;
          width: 70px;
          height: 15px;
          top: -15px;
          right: 10px;
        }

        .triangle {
          float: right;
          margin-right: 10px;
          margin-top: 5px;
        }
        .card-list-item {
          display: flex;
          // justify-content: space-between;
          flex-direction: row;
          border-bottom: 1px solid #eee;
          padding-bottom: 17px;
          margin-top: 12px;
          &:first-child {
            margin-top: 0px;
          }
          &:last-child {
            border-bottom: 0px;
            padding-bottom: 0px;
          }
          /* align-items: center; */
          .item-l {
            width: 34px;
            height: 34px;
            margin-right: 0;
          }
          .banner-step {
            margin-left: 12px;
            width: 210px;
            height: 30px;
            overflow: inherit;
            display: flex;
            box-sizing: border-box;
            flex-direction: row;
            justify-content: start;
            padding: 0;
            background: #fff;
            .step-name_ {
              width: 75px;
              position: relative;
              font-family: '';
              span {
                overflow: hidden;
                white-space: nowrap;
                display: inline-block;
                width: 100%;
                text-overflow: ellipsis;
              }
              .step-t {
                bottom: 0px;
                position: absolute;
                font-size: 12px;
                color: #8c8c8c;
                left: 0;
                width: 55px;
                font-weight: 400;
              }
            }
            .idol_ {
              width: 40px;
              border-bottom: 1px dashed #d9d9d9;
              position: relative;
              margin: 0 7px;
              top: -6px;
            }
          }
          .btn-step_ {
            width: 44px;
            height: 24px;
            background: rgba(107, 185, 244, 0.1);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(57, 131, 229, 0.8);
            font-size: 14px;
          }
          .btn-reserve {
            background: linear-gradient(180deg, #6bb9f4 0%, #3983e5 100%);
            border-radius: 12px;
            color: #fff;
          }
        }
      }
      .card-more-ban {
        width: 82px;
        // height: 100px;
        background: #ffffff;
        box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08),
          0px 3px 6px -4px rgba(0, 0, 0, 0.12);
        border-radius: 4px;
        position: absolute;
        top: 30px;
        right: -10px;
        padding: 6px 17px 0;
        box-sizing: border-box;
        z-index: 99;

        .triangle-con {
          position: absolute;
          width: 70px;
          height: 15px;
          top: -15px;
          right: 9px;
        }

        .triangle {
          float: right;
          margin-right: 10px;
          margin-top: 5px;
        }

        // .triangle{
        //   position: absolute;
        //   width:20px;
        //   height: 20px;
        //   top: -15px;
        //   right: 20px;
        // }
        .more-ban-items:hover {
          color: rgba(255, 77, 79, 1);
        }
        .more-ban-items {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;
          height: 30px;
          line-height: 30px;
          color: rgba(0, 0, 0, 0.85);
          img {
            width: 14px;
            height: 14px;
          }
        }
      }
    }

    .btn-et:hover {
      border: 1px solid #2793f2;
    }

    .btn-et {
      border: 1px solid #3983e5;
      background: #fff;
      margin-left: 6px;
      color: #3983e5;
    }
    .btnMouseInter {
      color: #40a9ff;
      border: 1px solid #40a9ff;
    }
  }

  .add-apply:hover {
    background: linear-gradient(180deg, #3983e5 0%, #6bb9f4 100%);
  }

  .add-apply {
    width: 202px;
    height: 48px;
    margin-top: 8px;
    margin: 8px auto 0;
    background: linear-gradient(180deg, #6bb9f4 0%, #3983e5 100%);
    box-shadow: 0px 2px 8px 0px rgba(60, 134, 230, 0.26);
    border-radius: 24px;
    font-weight: 500;
    font-size: 18px;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    img {
      width: 28px;
      height: 28px;
      margin-right: 8px;
    }
  }
}
.mr-10 {
  margin-right: 10px;
}
</style>