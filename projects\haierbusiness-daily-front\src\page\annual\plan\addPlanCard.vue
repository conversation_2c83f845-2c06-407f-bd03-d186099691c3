<script setup lang="ts">
import {getCurrentRouter, routerParam} from '@haierbusiness-front/utils';
import {IAnnualPlanTypeListResponse} from "@haierbusiness-front/common-libs";

import {ref, createVNode, PropType, watch, computed} from 'vue';

const router = getCurrentRouter()

const prop = defineProps({
  existYears: Object as PropType<Object>,
})


const gotoUpdate = () => {
  router.push({ path: "/daily/annual-plan/update", query: { existYears: routerParam(prop?.existYears), type: 'add'  }  } )
}
</script>

<template>
  <div class="card" style=" border: 1px dashed #888888;
          display: flex;
  justify-content: center;
  align-items: center;
  color: #626161;
  font-size: 14px" @click="gotoUpdate" >
    +添加
  </div>
</template>

<style scoped lang="less">
.card {
  box-shadow: 0 0 2px rgba(189, 250, 255, 0.7);
  border: 0.5px solid #eaeaea;
  border-radius: 5px;
  text-align: center;
  height: calc(37vh);
  min-height: 290px;
  width: 95%;
  margin: 10px auto;

  &:hover {
    box-shadow: 0 0 10px rgba(189, 250, 255, 0.7);
    border: 0.5px solid rgba(189, 250, 255, 0.7);
    cursor:pointer;
  }

  .content {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    height: 90%;
  }

  .footer {
    padding-top: 2px;
    text-align: center;
    font-weight: 700;
    font-size: 16px;
    border-top: 0.5px solid #eaeaea;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    background-color: rgb(247, 249, 250);
    height: 10%;
  }
}
</style>