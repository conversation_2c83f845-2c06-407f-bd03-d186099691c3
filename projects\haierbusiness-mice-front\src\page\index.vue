<script setup lang="ts">
import BLayout from '@haierbusiness-front/components/layout/BlankLayout.vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';

</script>

<template>
    <div style="height:100vh;min-height:280px">
        <b-layout />
    </div>
</template>

<style lang="less">
    .top-title {
        text-align: center;
        font-size: 30px;
        font-weight: bold;
        line-height: 50px;
        margin-top: 10px;
    }

    .flex {
        display: flex;
    }

    .align-items-center {
        align-items: center;
    }
</style>

<style>


</style>
