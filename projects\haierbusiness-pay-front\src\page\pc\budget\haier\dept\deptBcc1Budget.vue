<script setup lang="ts">
import { computed, onMounted, PropType, reactive, ref, watch,getCurrentInstance, ComponentInternalInstance  } from 'vue'
import { Table as hTable, Divider as hDivider, Select as hSelect, RadioGroup as hRadioGroup, Radio as hRadio, RadioButton as hRadioButton, Modal as hModal, Input as hInput, InputSearch as hInputSearch, Loading as hLoading, Space as hSpace, Button as hButton, Row as hRow, Col as hCol, Tabs as hTabs, TabPane as hTabPane, Image as hImage, message, TablePaginationConfig } from 'ant-design-vue';
import { IPayData } from '@haierbusiness-front/common-libs/src/pay/model/basicModel';
import { budgetHaierPayApi, budgetHaierPayBccSupportApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import {
  HaierBudgetSourceConstant,
  IHaierAccountCompany,
  IHaierBudgetDepartment,
  IHaierBudgetDeptBccAccountsRequest,
  IHaierBudgetDeptBccBudgetItemRequest,
  IHaierBudgetDeptBccBudgetItemResponse,
  IHaierBudgetDeptBccDcProjectRequest,
  IHaierBudgetDeptBccDepartmentsRequest,
  IHaierBudgetDeptBccProjectRequest,
  IHaierBudgetDeptBccWBSRequest,
  IHaierDcProjectItem,
  IHaierProject,
  IHaierWbs,
  PaySourceConstant
} from '@haierbusiness-front/common-libs';
import { Key } from 'ant-design-vue/lib/_util/type';
import {isMobile} from "@haierbusiness-front/utils";
const props = defineProps<{ budgetType?: string, param?: IPayData }>()

const emit = defineEmits<{
  (e: 'payComplete', isPayComplete: boolean): void
}>()

// 预算部门
const {
  data: searchDepartmentsData,
  run: searchDepartmentsRun,
  loading: searchDepartmentsLoading,
  current: searchDepartmentsCurrent,
  pageSize: searchDepartmentsPageSize,
  totalPage: searchDepartmentsTotalPage,
} = usePagination(
    budgetHaierPayBccSupportApi.searchDepartments
);
const searchDepartmentsParams = ref<IHaierBudgetDeptBccDepartmentsRequest>({})
const handleSearchDepartmentsRun = (
    pag: { current: number; pageSize: number },
) => {
  if (!searchDepartmentsParams.value?.code && !searchDepartmentsParams.value?.name) {
    message.warn("结算部门编码、名称任填一项!")
    return
  }
  searchDepartmentsRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
    ...searchDepartmentsParams.value
  });
};
const visibleDepartment = ref()
const showSearchDepartment = () => {
  visibleDepartment.value = true
}
const confirmSelectDepartment = () => {
  visibleDepartment.value = false
}
const changeSelectDepartment = (key: Key[], records: IHaierBudgetDepartment[]) => {
  departmentsSelectedRowKeys.value = key
  departmentsSelectedRowRecord.value = records[0]
  departmentsSelectedRowValue.value = records[0].name
  // 重置
  searchAccountsData.value = undefined
  accountsSelectedRowKeys.value = undefined
  accountsSelectedRowRecord.value = undefined
  accountsSelectedRowValue.value = undefined
  feeItem.value = undefined
  budgetItemsSelectedRowKeys.value = undefined
  budgetItemsSelectedRowValue.value = undefined
  projectsSelectedRowKeys.value = undefined
  projectsSelectedRowValue.value = undefined
  wbsSelectedRowKeys.value = undefined
  wbsSelectedRowValue.value = undefined
  dcProjectsSelectedRowKeys.value = undefined
  dcProjectsSelectedRowValue.value = undefined
  dcItemsSelectedRowKeys.value = undefined
  dcItemsSelectedRowRecord.value = undefined
  dcItemsSelectedRowValue.value = undefined
}
const departmentsSelectedRowKeys = ref()
const departmentsSelectedRowRecord = ref<IHaierBudgetDepartment>()
const departmentsSelectedRowValue = ref()
const departmentsTableColumns = [
  {
    title: '',
    dataIndex: '',
    width: '5%',
  },
  {
    title: '预算部门编码',
    dataIndex: 'code',
    width: '35%',
  },
  {
    title: '预算部门名称',
    dataIndex: 'name',
    width: '60%',
  },
];
const userDepartmentsPagination = computed<TablePaginationConfig>(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: searchDepartmentsData.value?.total,
  current: searchDepartmentsData.value?.pageNum,
  pageSize: searchDepartmentsData.value?.pageSize,
  size: 'default',
  style: {justifyContent: 'center'},

}));

// 结算单位
const {
  data: searchAccountsData,
  run: searchAccountsRun,
  loading: searchAccountsLoading,
  current: searchAccountsCurrent,
  pageSize: searchAccountsPageSize,
  totalPage: searchAccountsTotalPage,
} = usePagination(
    budgetHaierPayBccSupportApi.searchAccounts
);
const searchAccountsParams = ref<IHaierBudgetDeptBccAccountsRequest>({})
const handleSearchAccountsRun = (
    pag: { current: number; pageSize: number },
) => {
  if (!departmentsSelectedRowKeys.value) {
    message.warn("请先选择预算部门!")
    return
  }
  searchAccountsRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
    ...searchAccountsParams.value,
    bdCode: departmentsSelectedRowKeys.value
  });
};
const visibleAccount = ref()
const showSearchAccount = () => {
  visibleAccount.value = true
  if (departmentsSelectedRowKeys.value && !searchAccountsData.value) {
    handleSearchAccountsRun({current: 1, pageSize: 10})
  }
}
const confirmSelectAccount = () => {
  visibleAccount.value = false
  // 查询关联内容
  searchProjectsRun({
    pageNum: 1,
    pageSize: 10,
    ...searchProjectsParams.value,
    companyCode: accountsSelectedRowKeys.value
  });

  searchWbsRun({
    pageNum: 1,
    pageSize: 10,
    ...searchWbsParams.value,
    companyCode: accountsSelectedRowKeys.value
  });

  searchDcProjectsRun({
    pageNum: 1,
    pageSize: 10,
    ...searchDcProjectsParams.value,
    accountCode: accountsSelectedRowKeys.value
  });
}
const changeSelectAccount = (key: Key[], records: IHaierAccountCompany[]) => {
  accountsSelectedRowKeys.value = key
  accountsSelectedRowRecord.value = records[0]
  accountsSelectedRowValue.value = records[0].name
  // 重置
  feeItem.value = undefined
  budgetItemsSelectedRowKeys.value = undefined
  budgetItemsSelectedRowRecord.value = undefined
  budgetItemsSelectedRowValue.value = undefined
  projectsSelectedRowKeys.value = undefined
  projectsSelectedRowValue.value = undefined
  wbsSelectedRowKeys.value = undefined
  wbsSelectedRowValue.value = undefined
  dcProjectsSelectedRowKeys.value = undefined
  dcProjectsSelectedRowValue.value = undefined
  dcItemsSelectedRowKeys.value = undefined
  dcItemsSelectedRowRecord.value = undefined
  dcItemsSelectedRowValue.value = undefined
}
const accountsSelectedRowKeys = ref()
const accountsSelectedRowRecord = ref<IHaierAccountCompany>()
const accountsSelectedRowValue = ref()
const accountsTableColumns = [
  {
    title: '',
    dataIndex: '',
    width: '5%',
  },
  {
    title: '结算单位编码',
    dataIndex: 'code',
    width: '35%',
  },
  {
    title: '结算单位名称',
    dataIndex: 'name',
    width: '60%',
  },
];
const userAccountsPagination = computed<TablePaginationConfig>(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: searchAccountsData.value?.total,
  current: searchAccountsData.value?.pageNum,
  pageSize: searchAccountsData.value?.pageSize,
  size: 'default',
  style: {justifyContent: 'center'},

}));

// 费用科目
const {
  data: searchFeeItemsData,
  run: searchFeeItemsRun,
  loading: searchFeeItemsLoading,
} = useRequest(
    budgetHaierPayApi.searchFeeItems, {
      manual: false,
      defaultParams: [
        {
          applicationCode: props.param?.applicationCode,
          budgetSysCode: HaierBudgetSourceConstant.BCC.code,
          businessType:props.param?.businessType,
        }
      ]
    }
);
const feeItemOptions = computed(() => {
  if (searchFeeItemsData.value) {
    return searchFeeItemsData.value.map(it => {
      return {"value": it.itemCode, "label": it.itemName}
    })
  } else {
    return []
  }
});

const feeItem = ref()
const feeItemName = ref();
watch(feeItem, (newValue, oldValue) => {
  feeItemName.value = feeItemOptions.value.find(it => {
    return it.value === newValue
  })?.label
});
const changeFeeItem = () => {
  // 重置
  budgetItemsSelectedRowKeys.value = undefined
  budgetItemsSelectedRowRecord.value = undefined
  budgetItemsSelectedRowValue.value = undefined
  projectsSelectedRowKeys.value = undefined
  projectsSelectedRowValue.value = undefined
  wbsSelectedRowKeys.value = undefined
  wbsSelectedRowValue.value = undefined
  dcProjectsSelectedRowKeys.value = undefined
  dcProjectsSelectedRowValue.value = undefined
  dcItemsSelectedRowKeys.value = undefined
  dcItemsSelectedRowRecord.value = undefined
  dcItemsSelectedRowValue.value = undefined
}

// 预算立项
const {
  data: searchBudgetItemsData,
  run: searchBudgetItemsRun,
  loading: searchBudgetItemsLoading,
} = useRequest(
    budgetHaierPayBccSupportApi.searchBudgetItems
);

const handleSearchBudgetItemsRun = () => {
  if (!departmentsSelectedRowKeys.value) {
    message.warn("请先选择预算部门!")
    return
  }
  if (!feeItem.value) {
    message.warn("请先选择费用科目!")
    return
  }
  if (!props.param?.applicationCode) {
    message.warn("获取不到业务系统编码!")
    return
  }
  searchBudgetItemsRun({
    bdCode: departmentsSelectedRowKeys.value[0],
    feeItem: feeItem.value,
    applicationCode: props.param?.applicationCode,
    businessType: props.param?.businessType
  })
}
const visibleBudgetItem = ref()
const showSearchBudgetItems = () => {
  if (!departmentsSelectedRowKeys.value) {
    message.warn("请先选择预算部门!")
    return
  }
  if (!feeItem.value) {
    message.warn("请先选择费用科目!")
    return
  }
  if (!props.param?.applicationCode) {
    message.warn("获取不到业务系统编码!")
    return
  }
  visibleBudgetItem.value = true
  handleSearchBudgetItemsRun()
}
const confirmSelectBudgetItem = () => {
  visibleBudgetItem.value = false
}
const changeSelectBudgetItem = (key: Key[], records: IHaierBudgetDeptBccBudgetItemResponse[]) => {
  budgetItemsSelectedRowKeys.value = key
  budgetItemsSelectedRowRecord.value = records[0]
  budgetItemsSelectedRowValue.value = records[0].itemName
  // 重置
  projectsSelectedRowKeys.value = undefined
  projectsSelectedRowValue.value = undefined
  wbsSelectedRowKeys.value = undefined
  wbsSelectedRowValue.value = undefined
  dcProjectsSelectedRowKeys.value = undefined
  dcProjectsSelectedRowValue.value = undefined
  dcItemsSelectedRowKeys.value = undefined
  dcItemsSelectedRowRecord.value = undefined
  dcItemsSelectedRowValue.value = undefined
}
const budgetItemsSelectedRowKeys = ref()
const budgetItemsSelectedRowRecord = ref<IHaierBudgetDeptBccBudgetItemResponse>()
const budgetItemsSelectedRowValue = ref()
const budgetItemsTableColumns = [
  {
    title: '',
    dataIndex: '',
    width: '5%',
  },
  {
    title: '预算立项编码',
    dataIndex: 'itemCode',
    width: '35%',
  },
  {
    title: '预算立项名称',
    dataIndex: 'itemName',
    width: '60%',
  },
];

// 研发项目
const {
  data: searchProjectsData,
  run: searchProjectsRun,
  loading: searchProjectsLoading,
  current: searchProjectsCurrent,
  pageSize: searchProjectsPageSize,
  totalPage: searchProjectsTotalPage,
} = usePagination(
    budgetHaierPayBccSupportApi.searchProjects
);
const searchProjectsParams = ref<IHaierBudgetDeptBccProjectRequest>({})
const handleSearchProjectsRun = (
    pag: { current: number; pageSize: number },
) => {
  if (!accountsSelectedRowKeys.value) {
    message.warn("请先选择结算单位!")
    return
  }
  searchProjectsRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
    ...searchProjectsParams.value,
    companyCode: accountsSelectedRowKeys.value
  });
};
const visibleProject = ref()
const showSearchProject = () => {
  visibleProject.value = true
  if (departmentsSelectedRowKeys.value && !searchProjectsData.value) {
    handleSearchProjectsRun({current: 1, pageSize: 10})
  }
}
const confirmSelectProject = () => {
  visibleProject.value = false
}
const changeSelectProject = (key: Key[], records: IHaierProject[]) => {
  projectsSelectedRowKeys.value = key
  projectsSelectedRowValue.value = records[0].name
  // 重置
  wbsSelectedRowKeys.value = undefined
  wbsSelectedRowValue.value = undefined
  dcProjectsSelectedRowKeys.value = undefined
  dcProjectsSelectedRowValue.value = undefined
  dcItemsSelectedRowKeys.value = undefined
  dcItemsSelectedRowRecord.value = undefined
  dcItemsSelectedRowValue.value = undefined
}
const projectsSelectedRowKeys = ref()
const projectsSelectedRowValue = ref()
const projectsTableColumns = [
  {
    title: '',
    dataIndex: '',
    width: '5%',
  },
  {
    title: '研发项目编码',
    dataIndex: 'code',
    width: '35%',
  },
  {
    title: '研发项目名称',
    dataIndex: 'name',
    width: '60%',
  },
];
const userProjectsPagination = computed<TablePaginationConfig>(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: searchProjectsData.value?.total,
  current: searchProjectsData.value?.pageNum,
  pageSize: searchProjectsData.value?.pageSize,
  size: 'default',
  style: {justifyContent: 'center'},

}));

// WBS编码
const {
  data: searchWbsData,
  run: searchWbsRun,
  loading: searchWbsLoading,
  current: searchWbsCurrent,
  pageSize: searchWbsPageSize,
  totalPage: searchWbsTotalPage,
} = usePagination(
    budgetHaierPayBccSupportApi.searchWbs
);
const searchWbsParams = ref<IHaierBudgetDeptBccWBSRequest>({})
const handleSearchWbsRun = (
    pag: { current: number; pageSize: number },
) => {
  if (!accountsSelectedRowKeys.value) {
    message.warn("请先选择结算单位!")
    return
  }
  searchWbsRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
    ...searchWbsParams.value,
    companyCode: accountsSelectedRowKeys.value
  });
};
const visibleWbs = ref()
const showSearchWbs = () => {
  visibleWbs.value = true
  if (departmentsSelectedRowKeys.value && !searchWbsData.value) {
    handleSearchWbsRun({current: 1, pageSize: 10})
  }
}
const confirmSelectWbs = () => {
  visibleWbs.value = false
}
const changeSelectWbs = (key: Key[], records: IHaierWbs[]) => {
  wbsSelectedRowKeys.value = key
  wbsSelectedRowValue.value = records[0].name
  // 重置
  dcProjectsSelectedRowKeys.value = undefined
  dcProjectsSelectedRowValue.value = undefined
  dcItemsSelectedRowKeys.value = undefined
  dcItemsSelectedRowRecord.value = undefined
  dcItemsSelectedRowValue.value = undefined
}
const wbsSelectedRowKeys = ref()
const wbsSelectedRowValue = ref()
const wbsTableColumns = [
  {
    title: '',
    dataIndex: '',
    width: '5%',
  },
  {
    title: 'WBS编码',
    dataIndex: 'code',
    width: '35%',
  },
  {
    title: 'WBS名称',
    dataIndex: 'name',
    width: '60%',
  },
];
const userWbsPagination = computed<TablePaginationConfig>(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: searchWbsData.value?.total,
  current: searchWbsData.value?.pageNum,
  pageSize: searchWbsData.value?.pageSize,
  size: 'default',
  style: {justifyContent: 'center'},

}));

// 地产项目
const {
  data: searchDcProjectsData,
  run: searchDcProjectsRun,
  loading: searchDcProjectsLoading,
  current: searchDcProjectsCurrent,
  pageSize: searchDcProjectsPageSize,
  totalPage: searchDcProjectsTotalPage,
} = usePagination(
    budgetHaierPayBccSupportApi.searchDcProjects
);
const searchDcProjectsParams = ref<IHaierBudgetDeptBccDcProjectRequest>({})
const handleSearchDcProjectsRun = (
    pag: { current: number; pageSize: number },
) => {
  if (!accountsSelectedRowKeys.value) {
    message.warn("请先选择结算单位!")
    return
  }
  searchDcProjectsRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
    ...searchDcProjectsParams.value,
    accountCode: accountsSelectedRowKeys.value
  });
};
const visibleDcProjects = ref()
const showSearchDcProjects = () => {
  visibleDcProjects.value = true
  if (departmentsSelectedRowKeys.value && !searchDcProjectsData.value) {
    handleSearchDcProjectsRun({current: 1, pageSize: 10})
  }
}
const confirmSelectDcProjects = () => {
  visibleDcProjects.value = false
  // 查询关联项目
  searchDcItemsRun({
    pageNum: 1,
    pageSize: 10,
    ...searchDcItemsParams.value,
    projectCode: dcProjectsSelectedRowKeys.value
  });
}
const changeSelectDcProjects = (key: Key[], records: IHaierDcProjectItem[]) => {
  dcProjectsSelectedRowKeys.value = key
  dcProjectsSelectedRowValue.value = records[0].name
  // 重置
  dcItemsSelectedRowKeys.value = undefined
  dcItemsSelectedRowRecord.value = undefined
  dcItemsSelectedRowValue.value = undefined
}
const dcProjectsSelectedRowKeys = ref()
const dcProjectsSelectedRowValue = ref()
const dcProjectsTableColumns = [
  {
    title: '',
    dataIndex: '',
    width: '5%',
  },
  {
    title: '地产项目编码',
    dataIndex: 'code',
    width: '35%',
  },
  {
    title: '地产项目名称',
    dataIndex: 'name',
    width: '60%',
  },
];
const userDcProjectsPagination = computed<TablePaginationConfig>(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: searchDcProjectsData.value?.total,
  current: searchDcProjectsData.value?.pageNum,
  pageSize: searchDcProjectsData.value?.pageSize,
  size: 'default',
  style: {justifyContent: 'center'},

}));

// 地产分期
const {
  data: searchDcItemsData,
  run: searchDcItemsRun,
  loading: searchDcItemsLoading,
  current: searchDcItemsCurrent,
  pageSize: searchDcItemsPageSize,
  totalPage: searchDcItemsTotalPage,
} = usePagination(
    budgetHaierPayBccSupportApi.searchDcItems
);

const searchDcItemsParams = ref<IHaierBudgetDeptBccDcProjectRequest>({})
const handleSearchDcItemsRun = (
    pag: { current: number; pageSize: number },
) => {
  if (!dcProjectsSelectedRowKeys.value) {
    message.warn("请先选择地产项目!")
    return
  }
  searchDcItemsRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
    ...searchDcItemsParams.value,
    projectCode: dcProjectsSelectedRowKeys.value
  });
};
const visibleDcItems = ref()
const showSearchDcItems = () => {
  visibleDcItems.value = true
  if (departmentsSelectedRowKeys.value && !searchDcItemsData.value) {
    handleSearchDcItemsRun({current: 1, pageSize: 10})
  }
}
const confirmSelectDcItems = () => {
  visibleDcItems.value = false
}
const changeSelectDcItems = (key: Key[], records: IHaierDcProjectItem[]) => {
  dcItemsSelectedRowKeys.value = key
  dcItemsSelectedRowRecord.value = records[0]
  dcItemsSelectedRowValue.value = records[0].name
}
const dcItemsSelectedRowKeys = ref()
const dcItemsSelectedRowRecord = ref<IHaierDcProjectItem>()
const dcItemsSelectedRowValue = ref()
const dcItemsTableColumns = [
  {
    title: '',
    dataIndex: '',
    width: '5%',
  },
  {
    title: '地产项目编码',
    dataIndex: 'code',
    width: '35%',
  },
  {
    title: '地产项目名称',
    dataIndex: 'name',
    width: '60%',
  },
];
const userDcItemsPagination = computed<TablePaginationConfig>(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: searchDcItemsData.value?.total,
  current: searchDcItemsData.value?.pageNum,
  pageSize: searchDcItemsData.value?.pageSize,
  size: 'default',
  style: {justifyContent: 'center'},

}));


// 去支付(占用预算)
const payComplete = () => {
  emit('payComplete', true)
}

const {
  data: payData,
  run: payRun,
  loading: payLoading,
} = useRequest(
    budgetHaierPayApi.occupyBudget, {
      onSuccess: () => {
        payComplete()
      }
    }
);
const pay = () => {
  // const { setupState } = <ComponentInternalInstance>getCurrentInstance()
  console.log(testObj.value)
  const Obj = {}
  Object.keys(testObj.value).forEach(key =>{
    if(typeof testObj.value[key]!='function'&&typeof testObj.value[key]!='undefined'&&key!='testObj'){
        Obj[key] = testObj.value[key]
      }
    }
  )
  console.log(Obj,"010203")
  window.localStorage.setItem("obj",JSON.stringify(Obj))
  // 必填项
  if (!departmentsSelectedRowKeys.value) {
    message.warn("请先选择预算部门!")
    return;
  }
  if (!accountsSelectedRowKeys.value) {
    message.warn("请先选择结算单位!")
    return;
  }
  if (!feeItem.value) {
    message.warn("请先选择费用科目!")
    return;
  }
  if (!budgetItemsSelectedRowKeys.value) {
    message.warn("请先选择预算立项!")
    return;
  }

  // 特殊情况必填项
  if (departmentsSelectedRowRecord.value?.funcCode === 'E' && accountsSelectedRowRecord.value?.funcCode === 'E' && !projectsSelectedRowKeys.value) {
    message.warn("请先选择研发项目!")
    return;
  }
  if (accountsSelectedRowKeys.value == 3200 && !wbsSelectedRowKeys.value) {
    message.warn("请先选择WBS编码!")
    return;
  }
  // 地产项目以及地产分期不在前台做校验,预算占用时后台校验

  // 调用占用预算
  payRun(
      {
        haierBudgetType: props.budgetType,
        budgetDepartmentCode: departmentsSelectedRowKeys.value[0],
        budgetDepartmentName: departmentsSelectedRowValue.value,
        accountCompanyCode: accountsSelectedRowKeys.value[0],
        accountCompanyName: accountsSelectedRowValue.value,
        feeItem: feeItem.value,
        feeItemName: feeItemName.value,
        itemCode: budgetItemsSelectedRowKeys.value[0],
        itemName: budgetItemsSelectedRowValue.value,
        // 非必填
        projectCode: projectsSelectedRowKeys.value ? projectsSelectedRowKeys.value[0] : null,
        projectName: projectsSelectedRowValue.value,
        wbsCode: wbsSelectedRowKeys.value ? wbsSelectedRowKeys.value[0] : null,
        wbsName: wbsSelectedRowValue.value,
        dcProjectCode: dcProjectsSelectedRowKeys.value ? dcProjectsSelectedRowKeys.value[0] : null,
        dcProjectName: dcProjectsSelectedRowValue.value,
        dcItemCode: dcItemsSelectedRowRecord.value ? dcItemsSelectedRowRecord.value.guid : null,
        dcItemName: dcItemsSelectedRowValue.value,
        saleType: budgetItemsSelectedRowRecord.value?.saleType,

        // - 通用参数
        orderCode: props.param?.orderCode,
        applicationCode: props.param?.applicationCode,
        payTypes: props.param?.payTypes,
        username: props.param?.username,
        providerCode: props.param?.providerCode,
        amount: Number(props.param?.amount),
        orderDetailsUrl: props.param?.orderDetailsUrl,
        notifyUrl: props.param?.notifyUrl,
        callbackUrl: props.param?.callbackUrl,
        description: props.param?.description,
        payload: props.param?.payload,
        paySource: isMobile() ? PaySourceConstant.MOBILE.type : PaySourceConstant.PC.type,
        paymentMethod: 2,
        businessType: props.param?.businessType,

      },
      {
        applicationCode: props.param?.applicationCode,
        excludes: "paySource,haierBudgetType,feeItem,feeItemName,budgeterCode,budgeterName,budgetDepartmentCode,budgetDepartmentName,accountCompanyCode,accountCompanyName,itemCode,itemName,projectCode,projectName,wbsCode,wbsName,dcProjectCode,dcProjectName,dcItemCode,dcItemName,saleType,paymentMethod",
        nonce: props.param?.hbNonce,
        timestamp: props.param?.hbTimestamp,
        sign: props.param?.sign,
      }
  )
};
const testObj = ref<any>({})
onMounted(()=>{
  // const { proxy,appContext,setupState } = <ComponentInternalInstance>getCurrentInstance()
  //   testObj.value = setupState
  // if(window.localStorage.getItem('obj')){
  //   const saveObj = JSON.parse(window.localStorage.getItem('obj'))
  //   Object.keys(saveObj).forEach(key =>{
  //   if(typeof saveObj[key]!='function'&&typeof saveObj[key]!='undefined'&&key!='testObj'&&typeof saveObj[key]!='object'){
  //     setupState[key]= saveObj[key]
  //     }
  //   }
  //   )
  // }
})

</script>
<template>
  <h-modal v-model:visible="visibleDepartment" :title="'预算部门查询选择'" style="width: 1000px;"
           @ok="confirmSelectDepartment">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 20px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="5" style="text-align: right;padding-right: 10px;">
            <label for="searchDepartmentsParamsCode">预算部门编码：</label>
          </h-col>
          <h-col :span="5">
            <h-input id="searchDepartmentsParamsCode" placeholder="" autocomplete="off"
                     v-model:value="searchDepartmentsParams.code"/>
          </h-col>
          <h-col :span="5" style="text-align: right;padding-right: 10px;">
            <label for="searchDepartmentsParamsName">预算部门名称：</label>
          </h-col>
          <h-col :span="5">
            <h-input id="searchDepartmentsParamsName" placeholder="" autocomplete="off"
                     v-model:value="searchDepartmentsParams.name"/>
          </h-col>
          <h-col :span="3" style="text-align: right;padding-right: 10px;">
            <h-button type="primary"
                      @click="handleSearchDepartmentsRun({ current: 1, pageSize: 10 })">查&nbsp;&nbsp;&nbsp;询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="departmentsTableColumns" :row-key="record => record.code" size="small"
                 :row-selection="{ type: 'radio', selectedRowKeys: departmentsSelectedRowKeys, onChange: changeSelectDepartment }"
                 :data-source="searchDepartmentsData?.records" :pagination="userDepartmentsPagination"
                 :loading="searchDepartmentsLoading" @change="handleSearchDepartmentsRun($event as any)">
        </h-table>
      </h-col>
    </h-row>
  </h-modal>
  <h-modal v-model:visible="visibleAccount" :title="'结算单位查询选择'" style="width: 1000px;"
           @ok="confirmSelectAccount">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 20px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="5" style="text-align: right;padding-right: 10px;">
            <label for="searchAccountsParamsAccountCode">结算单位编码：</label>
          </h-col>
          <h-col :span="5">
            <h-input id="searchAccountsParamsAccountCode" placeholder="" autocomplete="off"
                     v-model:value="searchAccountsParams.accountCode"/>
          </h-col>
          <h-col :span="5" style="text-align: right;padding-right: 10px;">
            <label for="searchAccountsParamsAccountName">结算单位名称：</label>
          </h-col>
          <h-col :span="5">
            <h-input id="searchAccountsParamsAccountName" placeholder="" autocomplete="off"
                     v-model:value="searchAccountsParams.accountName"/>
          </h-col>
          <h-col :span="3" style="text-align: right;padding-right: 10px;">
            <h-button type="primary"
                      @click="handleSearchAccountsRun({ current: 1, pageSize: 10 })">查&nbsp;&nbsp;&nbsp;询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="accountsTableColumns" :row-key="record => record.code" size="small"
                 :row-selection="{ type: 'radio', selectedRowKeys: accountsSelectedRowKeys, onChange: changeSelectAccount }"
                 :data-source="searchAccountsData?.records" :pagination="userAccountsPagination"
                 :loading="searchAccountsLoading" @change="handleSearchAccountsRun($event as any)">
        </h-table>
      </h-col>
    </h-row>
  </h-modal>
  <h-modal v-model:visible="visibleBudgetItem" :title="'预算立项查询选择'" style="width: 1000px;"
           @ok="confirmSelectBudgetItem">
    <h-row :align="'middle'">
      <h-col :span="24">
        <h-table :columns="budgetItemsTableColumns" :row-key="record => record.itemCode" size="small"
                 :row-selection="{ type: 'radio', selectedRowKeys: budgetItemsSelectedRowKeys, onChange: changeSelectBudgetItem }"
                 :data-source="searchBudgetItemsData" :loading="searchBudgetItemsLoading" :pagination="false">
        </h-table>
      </h-col>
    </h-row>
  </h-modal>
  <h-modal v-model:visible="visibleProject" :title="'研发项目查询选择'" style="width: 1000px;"
           @ok="confirmSelectProject">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 20px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="5" style="text-align: right;padding-right: 10px;">
            <label for="searchProjectsParamsCode">研发项目编码：</label>
          </h-col>
          <h-col :span="5">
            <h-input id="searchProjectsParamsCode" placeholder="" autocomplete="off"
                     v-model:value="searchProjectsParams.code"/>
          </h-col>
          <h-col :span="5" style="text-align: right;padding-right: 10px;">
            <label for="searchProjectsParamsName">研发项目名称：</label>
          </h-col>
          <h-col :span="5">
            <h-input id="searchProjectsParamsName" placeholder="" autocomplete="off"
                     v-model:value="searchProjectsParams.name"/>
          </h-col>
          <h-col :span="3" style="text-align: right;padding-right: 10px;">
            <h-button type="primary"
                      @click="handleSearchProjectsRun({ current: 1, pageSize: 10 })">查&nbsp;&nbsp;&nbsp;询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="projectsTableColumns" :row-key="record => record.code" size="small"
                 :row-selection="{ type: 'radio', selectedRowKeys: projectsSelectedRowKeys, onChange: changeSelectProject }"
                 :data-source="searchProjectsData?.records" :pagination="userProjectsPagination"
                 :loading="searchProjectsLoading" @change="handleSearchProjectsRun($event as any)">
        </h-table>
      </h-col>
    </h-row>
  </h-modal>
  <h-modal v-model:visible="visibleWbs" :title="'WBS编码查询选择'" style="width: 1000px;" @ok="confirmSelectWbs">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 20px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="5" style="text-align: right;padding-right: 10px;">
            <label for="searchWbsParamsCode">WBS编码：</label>
          </h-col>
          <h-col :span="5">
            <h-input id="searchWbsParamsCode" placeholder="" autocomplete="off"
                     v-model:value="searchWbsParams.code"/>
          </h-col>
          <h-col :span="5" style="text-align: right;padding-right: 10px;">
            <label for="searchWbsParamsName">WBS名称：</label>
          </h-col>
          <h-col :span="5">
            <h-input id="searchWbsParamsName" placeholder="" autocomplete="off"
                     v-model:value="searchWbsParams.name"/>
          </h-col>
          <h-col :span="3" style="text-align: right;padding-right: 10px;">
            <h-button type="primary"
                      @click="handleSearchWbsRun({ current: 1, pageSize: 10 })">查&nbsp;&nbsp;&nbsp;询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="wbsTableColumns" :row-key="record => record.code" size="small"
                 :row-selection="{ type: 'radio', selectedRowKeys: wbsSelectedRowKeys, onChange: changeSelectWbs }"
                 :data-source="searchWbsData?.records" :pagination="userWbsPagination" :loading="searchWbsLoading"
                 @change="handleSearchWbsRun($event as any)">
        </h-table>
      </h-col>
    </h-row>
  </h-modal>
  <h-modal v-model:visible="visibleDcProjects" :title="'地产项目查询选择'" style="width: 1000px;"
           @ok="confirmSelectDcProjects">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 20px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="5" style="text-align: right;padding-right: 10px;">
            <label for="searchDcProjectsParamsCode">地产项目编码：</label>
          </h-col>
          <h-col :span="5">
            <h-input id="searchDcProjectsParamsCode" placeholder="" autocomplete="off"
                     v-model:value="searchDcProjectsParams.code"/>
          </h-col>
          <h-col :span="5" style="text-align: right;padding-right: 10px;">
            <label for="searchDcProjectsParamsParamsName">地产项目名称：</label>
          </h-col>
          <h-col :span="5">
            <h-input id="searchDcProjectsParamsParamsName" placeholder="" autocomplete="off"
                     v-model:value="searchDcProjectsParams.name"/>
          </h-col>
          <h-col :span="3" style="text-align: right;padding-right: 10px;">
            <h-button type="primary"
                      @click="handleSearchDcProjectsRun({ current: 1, pageSize: 10 })">查&nbsp;&nbsp;&nbsp;询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="dcProjectsTableColumns" :row-key="record => record.code" size="small"
                 :row-selection="{ type: 'radio', selectedRowKeys: dcProjectsSelectedRowKeys, onChange: changeSelectDcProjects }"
                 :data-source="searchDcProjectsData?.records" :pagination="userDcProjectsPagination"
                 :loading="searchDcProjectsLoading" @change="handleSearchDcProjectsRun($event as any)">
        </h-table>
      </h-col>
    </h-row>
  </h-modal>
  <h-modal v-model:visible="visibleDcItems" :title="'地产分期查询选择'" style="width: 1000px;"
           @ok="confirmSelectDcItems">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 20px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="5" style="text-align: right;padding-right: 10px;">
            <label for="searchDcItemsParamsCode">地产项目编码：</label>
          </h-col>
          <h-col :span="5">
            <h-input id="searchDcItemsParamsCode" placeholder="" autocomplete="off"
                     v-model:value="searchDcItemsParams.code"/>
          </h-col>
          <h-col :span="5" style="text-align: right;padding-right: 10px;">
            <label for="searchDcItemsParamsParamsName">地产项目名称：</label>
          </h-col>
          <h-col :span="5">
            <h-input id="searchDcItemsParamsParamsName" placeholder="" autocomplete="off"
                     v-model:value="searchDcItemsParams.name"/>
          </h-col>
          <h-col :span="3" style="text-align: right;padding-right: 10px;">
            <h-button type="primary"
                      @click="handleSearchDcItemsRun({ current: 1, pageSize: 10 })">查&nbsp;&nbsp;&nbsp;询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="dcItemsTableColumns" :row-key="record => record.code" size="small"
                 :row-selection="{ type: 'radio', selectedRowKeys: dcItemsSelectedRowKeys, onChange: changeSelectDcItems }"
                 :data-source="searchDcItemsData?.records" :pagination="userDcItemsPagination"
                 :loading="searchDcItemsLoading" @change="handleSearchDcItemsRun($event as any)">
        </h-table>
      </h-col>
    </h-row>
  </h-modal>

  <h-row style="margin-top: 4vh;text-align: right;" :align="'middle'">
    <h-col span="2" style="font-size: 12px;">
      <span style="color: red;">*</span>预算部门：
    </h-col>
    <h-col span="6" style="text-align: left;">
      <h-input-search :value="departmentsSelectedRowValue" placeholder="" :size="'large'" class="budget-input"
                      enter-button @search="showSearchDepartment"/>
    </h-col>
    <h-col span="2" style="font-size: 12px">
      <span style="color: red;">*</span>结算单位：
    </h-col>
    <h-col span="6" style="text-align: left;">
      <h-input-search :value="accountsSelectedRowValue" placeholder="" :size="'large'" class="budget-input"
                      enter-button @search="showSearchAccount"/>
    </h-col>
    <h-col span="2" style="font-size: 12px">
      <span style="color: red;">*</span>费用科目：
    </h-col>
    <h-col span="6" style="text-align: left;">
      <h-select v-model:value="feeItem" :size="'large'" class="budget-input" :options="feeItemOptions"
                @change="changeFeeItem"></h-select>
    </h-col>
    <h-col span="2" style="font-size: 12px">
      <span style="color: red;">*</span>预算立项：
    </h-col>
    <h-col span="6" style="text-align: left;">
      <h-input-search :value="budgetItemsSelectedRowValue" placeholder="" :size="'large'" class="budget-input"
                      enter-button @search="showSearchBudgetItems"/>
    </h-col>
    <h-col span="2" v-if="searchProjectsData?.records&&searchProjectsData?.records.length" style="font-size: 12px">
      研发项目：
    </h-col>
    <h-col span="6" v-if="searchProjectsData?.records&&searchProjectsData?.records.length" style="text-align: left;">
      <h-input-search :value="projectsSelectedRowValue" placeholder="" :size="'large'" class="budget-input"
                      enter-button @search="showSearchProject"/>
    </h-col>
    <h-col span="2" v-if="searchWbsData?.records&&searchWbsData?.records.length" style="font-size: 12px">
      WBS编码： 
    </h-col>
    <h-col span="6" v-if="searchWbsData?.records&&searchWbsData?.records.length" style="text-align: left;">
      <h-input-search :value="wbsSelectedRowValue" placeholder="" :size="'large'" class="budget-input" enter-button
                      @search="showSearchWbs"/>
    </h-col>
    <h-col span="2" v-if="searchDcProjectsData?.records&&searchDcProjectsData?.records.length" style="font-size: 12px">
      地产项目：
    </h-col>
    <h-col span="6" v-if="searchDcProjectsData?.records&&searchDcProjectsData?.records.length" style="text-align: left;">
      <h-input-search :value="dcProjectsSelectedRowValue" placeholder="" :size="'large'" class="budget-input"
                      enter-button @search="showSearchDcProjects"/>
    </h-col>
    <h-col span="2" v-if="searchDcItemsData?.records&&searchDcItemsData?.records.length" style="font-size: 12px">
      地产分期：
    </h-col>
    <h-col span="6" v-if="searchDcItemsData?.records&&searchDcItemsData?.records.length" style="text-align: left;">
      <h-input-search :value="dcItemsSelectedRowValue" placeholder="" :size="'large'" class="budget-input"
                      enter-button @search="showSearchDcItems"/>
    </h-col>
  </h-row>
  <h-row>
    <h-divider></h-divider>
  </h-row>
  <h-row style="line-height: 14vh;" :align="'middle'">
    <h-col span="2" offset="11">
      <h-button type="primary" style="width: 100%;" @click="pay" :loading="payLoading"
                size="large">&nbsp;支&nbsp;&nbsp;付&nbsp;
      </h-button>
    </h-col>
  </h-row>
</template>

<style scoped lang="less">
.budget-input {
  width: 12vw;
}

.budget-input-readonly {
  width: 12vw;
  background-color: rgb(245, 245, 245);
  color: #2e2e2e;
}
:deep(.ant-col){
  margin-bottom: 40px;;
}
</style>
