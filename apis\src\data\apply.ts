
import { get, post } from '../request'
import { IPageResponse, ApplyFilter, Result, VipType } from '@haierbusiness-front/common-libs'
import { reportPPTExport } from "./board"


export const applyApi = {
    /**
     * 查询vip列表
     */
    list: (params: ApplyFilter): Promise<IPageResponse<VipType>> => {
        return get('data/api/datart/filter-roster/page', params)
    },

    save: (params: ApplyFilter): Promise<Result> => {
        return post('data/api/datart/filter-roster/create', params)
    },

    edit: (params: ApplyFilter): Promise<Result> => {
        return post('data/api/datart/filter-roster/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('data/api/datart/filter-roster/delete', { id })
    },

    postByIdAndVerificatSignature: (params: ApplyFilter): Promise<Result> => {
        return post('data/api/datart/permission-approve/getMyPermissionList', params)
    },


    postPermissionApprove: (params: any): Promise<Result> => {
        return post('data/api/datart/permission-approve/create', params, { 'content-type': 'multipart/form-data' })
    },


    getPermissionApprove: (params: any): Promise<any> => {
        return post('data/api/datart/permission-approve/getByIdAndVerificatSignature', params)
    },

    download: (filter: any): Promise<any> => {
        const res: any = reportPPTExport(filter);
        return res
    },
}