<script setup lang="ts">
import {
  Modal,
  Tag as hTag,
  <PERSON><PERSON> as hButton,
  <PERSON>confirm as hPopconfirm,
  Col as hCol,
  TabPane as hTabPane,
  Tabs as hTabs,
  Row as hRow,
  Card as hCard,
  Table as hTable,
  Textarea as hTextarea,
  Input as hInput,
  DatePicker as hDatePicker,
  Select as hSelect,
  CollapsePanel as hCollapsePanel,
  FormItem as hFormItem,
  InputNumber as hInputNumber,
  Collapse as hCollapse,
} from 'ant-design-vue';
import { PlusOutlined, SearchOutlined, RightOutlined, MinusCircleOutlined } from '@ant-design/icons-vue';
import {
  IAnnualPlanSaveOrUpdateRequestDTO,
  AnnualPlanTypeStateConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
  IHaierAccountBillInfo,
} from '@haierbusiness-front/common-libs';
import { getCurrentRouter, mapToObj, resolveParam } from '@haierbusiness-front/utils';
import { ref, createVNode, PropType, computed, h, watch } from 'vue';

import detailYearDecompose from './detailYearDecompose.vue';
import dayjs, { Dayjs } from 'dayjs';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import {
  IAnnualPlanItemDetailResponseDTO,
  IAnnualPlanTypeListRequest,
  IAnnualPlanUpdateItemRequestDTO,
  IMonthPlanDetailItemResponseDTO,
  PlanTypeConstant,
} from '@haierbusiness-front/common-libs/src/daily';
import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
import updateYearTarget from './updateYearTarget.vue';
import monthPlan from './monthPlan.vue';

const { loginUser } = storeToRefs(applicationStore(globalPinia));

const prop = defineProps({
  dailyType: Object as PropType<IAnnualPlanTypeListResponse[]>,
  formParam: Object as PropType<IAnnualPlanSaveOrUpdateRequestDTO>,
  current: Object as PropType<IAnnualPlanUpdateItemRequestDTO>,
  currentIndex: Number as PropType<number>,
  type: String as PropType<String>,
  month: String as PropType<String>,
});
const collapseActiveKey = ref([1]);

const planType = PlanTypeConstant.toArray();
const monthPlanActiveKey = ref(
  parseInt(String((prop?.month == undefined || prop?.month == 'undefined' ? '1' : prop?.month) || '1')),
);

const monthData = computed(() => {
  const result = new IAnnualPlanItemDetailResponseDTO();
  result.year = prop?.formParam?.year;
  const monthPlanEnterItems = new Map<number, IMonthPlanDetailItemResponseDTO[]>();
  prop?.current?.monthPlans?.forEach((it) => {
    const key = it.month as unknown as number;

    const items: IMonthPlanDetailItemResponseDTO[] = [];
    const exist = monthPlanEnterItems.get(key);
    if (exist) {
      items.push(...exist);
    }
    items.push({
      principalUsername: it.principalUsername,
      planType: prop?.current?.planType,
      planUnit: prop?.current?.planUnit,
      planValue: it.planValue,
      planDesc: it.planDesc,
      isDeleted: it.isDeleted,
    } as any);
    monthPlanEnterItems.set(key, items);
  });
  result.monthPlanEnterItems = mapToObj(monthPlanEnterItems);
  return result;
});

const changeDailyType = () => {
  prop.current!!.typeName = prop?.dailyType?.find((i) => i.id === prop.current?.typeId)?.name;
};
const usedAmount = computed(() => {
  if (prop.current?.monthPlans && prop.current?.monthPlans.length > 0) {
    return prop.current?.monthPlans
      ?.filter((it) => !it.isDeleted)
      ?.map((it: any) => {
        return parseFloat(it.planValue || 0);
      })
      ?.reduce((acc: number, it: number) => acc + it);
  } else {
    return 0;
  }
});

const decomposeActiveKey = ref();

watch(
  () => prop?.month,
  (n: any, o: any) => {
    monthPlanActiveKey.value = parseInt(
      String((prop?.month == undefined || prop?.month == 'undefined' ? '1' : prop?.month) || '1'),
    );
  },
  { deep: true },
);
const isDisabled = () => {
  return (
    prop?.type === 'adjust' ||
    prop?.type === 'summarize' ||
    prop?.type === 'evaluate' ||
    prop?.type === 'planform-evaluate'
  );
};
</script>

<template>
  <h-row :align="'middle'" style="margin: 24px 24px">
    <h-col :span="24" style="text-align: left">
      <h-collapse
        v-model:activeKey="collapseActiveKey"
        style="background-color: rgb(250, 250, 250)"
        :collapsible="'icon'"
      >
        <template #expandIcon="{ isActive }">
          <right-outlined :rotate="isActive ? 90 : 0" />
        </template>
        <h-collapse-panel key="1" class="cover-ant-collapse">
          <template #header>
            <div style="font-size: 15px; font-weight: 500; width: 100%">
              <h-row :align="'middle'">
                <h-col :span="3" style="text-align: right">
                  <span class="required">项目名称：</span>
                </h-col>
                <h-col :span="4">
                  <h-input v-model:value="current!!.name" placeholder="请输入项目名称" :disabled="isDisabled()" />
                </h-col>
                <h-col :span="3" :offset="1" style="text-align: right">
                  <span class="required">项目类型：</span>
                </h-col>
                <h-col :span="7">
                  <h-select
                    :disabled="isDisabled()"
                    v-model:value="current!!.typeId"
                    :options="prop.dailyType"
                    :fieldNames="{ label: 'name', value: 'id' }"
                    placeholder="请选择年度项目类型"
                    allowClear
                    style="width: 50%"
                    @change="changeDailyType"
                    notFoundContent="请先选择年份"
                  ></h-select>
                </h-col>
                <h-col v-if="!isDisabled() && prop.type !== 'resetting'" :span="6" style="text-align: right">
                  <h-popconfirm title="确认删除当前计划?" @confirm="$emit('delete', current)">
                    <h-button type="link" danger>
                      <template #icon>
                        <MinusCircleOutlined :style="{ fontSize: '20px' }" />
                      </template>
                    </h-button>
                  </h-popconfirm>
                </h-col>
              </h-row>
            </div>
          </template>
          <h-col :span="22" :offset="2">
            <h-row :gutter="24">
              <h-col :span="6">
                <h-form-item
                  label="目标类型"
                  :name="['items',currentIndex!!, 'planType']"
                  :rules="{
                    required: true,
                    message: '请选择目标类型!',
                  }"
                >
                  <h-select
                    :disabled="isDisabled()"
                    v-model:value="current!!.planType"
                    :options="(planType as any)"
                    :fieldNames="{ label: 'desc', value: 'code' }"
                    placeholder="请选择目标类型"
                    allowClear
                  ></h-select>
                </h-form-item>
              </h-col>
              <h-col :span="24"></h-col>
              <template v-if="current!!.planType === PlanTypeConstant.QUANTIFY.code">
                <h-col :span="6">
                  <h-form-item
                    label="目标效果"
                    :name="['items',currentIndex!!, 'planValue']"
                    :rules="{
                      required: true,
                      message: '请填写达成效果!',
                    }"
                  >
                    <h-input-number
                      style="width: 100%"
                      :disabled="isDisabled()"
                      :precision="2"
                      :decimalPlaces="2"
                      v-model:value="current!!.planValue"
                      placeholder="计划量（目标量）"
                      type="number"
                    />
                  </h-form-item>
                </h-col>
                <h-col :span="2">
                  <h-form-item
                    :name="['items',currentIndex!!, 'planUnit']"
                    :rules="{
                      required: true,
                      message: '请填写单位!',
                    }"
                  >
                    <h-input v-model:value="current!!.planUnit" :disabled="isDisabled()" placeholder="单位" readOnly />
                  </h-form-item>
                </h-col>
                <h-col v-if="!isDisabled()" :span="16" style="margin-top: 4px">
                  未分配目标：{{ parseFloat(((current!!.planValue || 0) - usedAmount).toFixed(2)) }}/{{
                    current!!.planUnit
                  }}
                </h-col>
              </template>
              <h-col :span="24">
                <h-form-item
                  v-if="current!!.planType === PlanTypeConstant.QUALITATIVE.code"
                  label="目标效果"
                  :name="['items',currentIndex!!, 'planDesc']"
                  :rules="{
                    required: true,
                    message: '请填写达成效果!',
                  }"
                >
                  <h-textarea
                    placeholder="请输入目标完成效果"
                    :disabled="isDisabled()"
                    v-model:value="current!!.planDesc"
                    :rows="3"
                  />
                </h-form-item>
              </h-col>
              <h-col :span="24">
                <h-form-item
                  label="月度分解"
                  :rules="{
                    required: true,
                    message: '月度分解不能为空!',
                  }"
                >
                  <h-row :gutter="24">
                    <h-col :span="24">
                      <h-tabs
                        v-if="month == undefined || month == 'undefined'"
                        v-model:activeKey="monthPlanActiveKey"
                        type="card"
                        :centered="true"
                        :tabBarStyle="{ 'margin-bottom': 0 }"
                      >
                        <template v-for="i in 12" :key="i">
                          <h-tab-pane>
                            <template #tab>
                              <div style="width: 32px; text-align: center">{{ i }}月</div>
                            </template>
                          </h-tab-pane>
                        </template>
                      </h-tabs>
                      <template v-else>
                        <div
                          style="
                            width: 100%;
                            text-align: left;
                            padding-left: 20px;
                            padding-top: 4px;
                            padding-bottom: 10px;
                            font-size: 14px;
                            font-weight: 700;
                          "
                        >
                          {{ formParam?.year }} 年 {{ month }}月计划分解
                        </div>
                      </template>
                      <month-plan
                        :annual-plan="current"
                        :month="monthPlanActiveKey"
                        :year="formParam?.year"
                        :plan-index="currentIndex"
                        :type="type"
                      ></month-plan>
                    </h-col>
                  </h-row>
                </h-form-item>
              </h-col>
              <h-col :span="24">
                <h-form-item
                  :colon="false"
                  class="decompose"
                  label="&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;"
                >
                  <h-row :gutter="24">
                    <h-col :span="24">
                      <h-collapse v-model:activeKey="decomposeActiveKey">
                        <h-collapse-panel key="1" header="分解跟踪">
                          <detail-year-decompose :data="monthData"></detail-year-decompose>
                        </h-collapse-panel>
                      </h-collapse>
                    </h-col>
                  </h-row>
                </h-form-item>
              </h-col>
            </h-row>
          </h-col>
        </h-collapse-panel>
      </h-collapse>
    </h-col>
  </h-row>
</template>

<style lang="less">
@import '../../../assets/css/main.less';

.cover-ant-collapse {
  .ant-collapse-expand-icon {
    height: 31px !important;
  }
  .decompose {
    .ant-collapse-expand-icon {
      height: 20px !important;
    }
  }
}
</style>
