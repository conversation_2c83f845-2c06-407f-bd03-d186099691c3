<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Popover as hPopover,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  QuestionCircleOutlined,
  DeleteOutlined,
  MenuOutlined,
} from '@ant-design/icons-vue';
import { smartBrainApi, knowCenterApi } from '@haierbusiness-front/apis';
import { ISmartBrainFilter, ISmartBrain, Datum } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables';
import EditDialog from './edit-dialog.vue';
import { getCurrentRouter, routerParam } from '@haierbusiness-front/utils';
// import router from '../../../router';
const router = getCurrentRouter();

const currentRouter = ref();

const plainOptions = ref<Datum[]>([]);

const knowCenterOptions = ref<Datum[]>([]);
const searchParam = ref<ISmartBrainFilter>({
  labelIds: [],
  labelIds1: [],
  labelIds2: [],
});

// 新增弹窗
const { visible, editData, handleCreate, onDialogClose, handleOk } = useEditDialog<ISmartBrain, ISmartBrain>(
  smartBrainApi,
  '指标',
  () =>
    listApiRun({
      labelIds: [...searchParam.value.labelIds, ...searchParam.value.labelIds1, ...searchParam.value.labelIds2],
      pageNo: data.value?.pageNum || 1,
      pageSize: data.value?.pageSize || 9999,
    }),
);
const handleEdit = (item: any) => {
  item.knowId = item.knowCenterVo.id;
  item.labelId = item.labelVo.map((v: any) => {
    return v.id;
  });
  editData.value = JSON.parse(JSON.stringify(item));
  getKnowCenterList()
  visible.value = true;
};
const { data, run: listApiRun, loading, current, pageSize } = usePagination(smartBrainApi.list);

const reset = () => {
  searchParam.value.labelIds = [];
  searchParam.value.labelIds1 = [];
  searchParam.value.labelIds2 = [];
  handleTableChange({ current: 1, pageSize: 9999 });
};

const dataSource = computed(() => data.value?.list || []);

// 删除
const { handleDelete } = useDelete(smartBrainApi, () =>
  listApiRun({
    labelIds: [...searchParam.value.labelIds, ...searchParam.value.labelIds1, ...searchParam.value.labelIds2],
    pageNo: data.value?.pageNum,
    pageSize: data.value?.pageSize,
  }),
);

watch(current, () => {
  handleTableChange({ current: current.value, pageSize: 9999 });
});

// 请求标签list列表
const getBrainKnowLabelList = () => {
  smartBrainApi.getBrainKnowLabelList().then((res: Array<Datum>) => {
    plainOptions.value.push(...res);
  });
};
// 获取知识中心列表
const getKnowCenterList = () => {
  knowCenterApi
    .getKnowCenterList({
      pageNo: 1,
      pageSize: 9999,
    })
    .then((res: any) => {
      knowCenterOptions.value = res.list;
    });
};

// 查询
const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  listApiRun({
    labelIds: [...searchParam.value.labelIds, ...searchParam.value.labelIds1, ...searchParam.value.labelIds2],
    pageNo: pag.current,
    pageNum: pag.current,

    pageSize: pag.pageSize,
  });
};

// 跳转可信度评分
const toCredibilityRating = (item: any) => {
  router.push({
    path: '/data/smartBrain/IndicatorMng/credibilityRating',
    query: {
      id: item.id,
    },
  });
};

// 初始化
onMounted(async () => {
  currentRouter.value = await router;
  getBrainKnowLabelList();
  getKnowCenterList();
  handleTableChange({ current: 1, pageSize: 9999 });
  console.log(data);
});
</script>

<template>
  <div v-if="$route.matched.length < 3" style="height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <a-card>
      <h-row :align="'middle'">
        <h-col :span="24">
          <h-row :align="'middle'" style="padding: 10px 10px 10px 10px">
          <h-col :span="1.5" style="text-align: right; padding-right: 10px">
              <label for="createTime">属性分类：</label>
            </h-col>
            <h-col :span="4">
              <h-select allowClear mode="multiple" v-model:value="searchParam.labelIds" style="width: 100%">
                <h-select-option v-for="item in plainOptions.filter((v:any)=>{return v?.labelType == 0})" :value="item.id">{{
                  item.labelName
                }}</h-select-option>
              </h-select>
            </h-col>
            <h-col :span="2" style="text-align: right; padding-right: 10px">
              <label for="createTime">业务分类：</label>
            </h-col>
            <h-col :span="4">
              <h-select allowClear mode="multiple" v-model:value="searchParam.labelIds2" style="width: 100%">
                <h-select-option v-for="item in plainOptions.filter((v:any)=>{return v?.labelType == 2})" :value="item.id">{{
                  item.labelName
                }}</h-select-option>
              </h-select>
            </h-col>
            <h-col :span="2" style="text-align: right; padding-right: 10px">
              <label for="createTime">其他标签：</label>
            </h-col>
            <h-col :span="4">
              <h-select allowClear mode="multiple" v-model:value="searchParam.labelIds1" style="width: 100%">
                <h-select-option v-for="item in plainOptions.filter((v:any)=>{return v?.labelType == 1})" :value="item.id">{{
                  item.labelName
                }}</h-select-option>
              </h-select>
            </h-col>
            <h-col :span="6" style="text-align: right">
              <h-button type="primary" style="margin-right: 10px" @click="handleCreate()">
                <PlusOutlined /> 新增
              </h-button>
              <h-button style="margin-right: 10px" @click="reset">重置</h-button>
              <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 9999 })">
                <SearchOutlined />查询
              </h-button>
            </h-col>
          </h-row>
        </h-col>
      </h-row>
    </a-card>
    <!-- 指标列表 -->
    <div v-loading="loading" class="indexListBox">
      <!-- 指标item -->
      <div v-for="(item, index) in dataSource" :key="item.id" class="indexItemBox">
        <div class="indexItemHeader">
          <div class="headerLeft">
            <a-progress
              @click="toCredibilityRating(item)"
              type="dashboard"
              :percent="item.credibilityVo?.checkScore"
              status="normal"
              :size="30"
            />
            <span>可信度</span>
          </div>
          <div class="reportName">
            {{ item.reportName }}
            <h-popover title="指标详情" trigger="click">
              <template #content>
                <span style="display: block;margin-top:10px;">1.指标定义</span>
                <div style="max-width: 800px;margin-top:3px;white-space: pre-line;">
                  {{ item.knowCenterVo.entryName }} : {{ item.knowCenterVo.entryContent }}
                </div>
                <span style="display: block;margin-top:10px;">2.其他描述</span>
                <div style="width: 500px;margin-top:3px;white-space: pre-line;">{{ item.indexReportContent }}</div>
              </template>
              <QuestionCircleOutlined class="QuestionCircleOutlined" style="color: #7f7f7f" />
            </h-popover>
          </div>
          <div class="headerRight">
            <h-tag v-for="(v, index) in item.labelVo" v-show="index < 1" color="#1677ff">{{ v.labelName }}</h-tag>
            <h-popover title="更多标签">
              <template #content>
                <h-tag v-for="(v, index) in item.labelVo" v-show="index >= 1" color="#1677ff">{{ v.labelName }}</h-tag>
              </template>
              <MenuOutlined v-if="item.labelVo.length >= 2" style="color: #1677ff" />
            </h-popover>
            <EditOutlined style="color: #1677ff; margin-left: 5px" @click="handleEdit(item)" />
            <DeleteOutlined style="color: #1677ff; margin-left: 5px" @click="handleDelete(item.id)"></DeleteOutlined>
          </div>
        </div>
        <div class="indexItemBottom">
          <!-- <Echarts v-if="item.dataSearch==0" :queryCondition="item.queryCondition" :echartsJson="item.echartsJson" :dataType="item.dataSearch" :height="24" :id="index" />
          <Rank  v-if="item.dataSearch==2" :queryCondition="item.queryCondition" :echartsJson="item.echartsJson" :height="24"></Rank> -->
          <img style="width: 100%;height:100%;" :src="item.imagesUrl" alt="" />
        </div>
      </div>
    </div>
    <!-- <div class="pagination">
      <a-pagination v-model:current="current" v-model:pageSize="pageSize" :total="data?.total" show-less-items />
    </div> -->
    <div v-if="visible">
      <EditDialog
        :labelList="plainOptions"
        :knowCenterOptions="knowCenterOptions"
        :show="visible"
        :data="editData"
        @cancel="onDialogClose"
        @ok="handleOk"
      >
      </EditDialog>
    </div>
  </div>
  <router-view></router-view>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.indexListBox {
  min-height: calc(100vh - 300px);
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  .indexItemBox {
    display: flex;
    flex-flow: column;
    width: 32.5%;
    min-height: 30vh;
    height: 30vh;
    background: inherit;
    background-color: rgba(255, 255, 255, 1);
    border: none;
    border-radius: 0px;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    margin: 12px 1.2% 0 0;
    &:nth-child(3n) {
      margin-right: 0;
    }
    .indexItemBottom {
      flex: 1;
      height: calc(30vh - 48px);
      overflow: hidden;
      padding: 0 16px;
    }
    .indexItemHeader {
      width: 100%;
      position: relative;
      height: 48px;
      background: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 10px;
      border-bottom: 1px solid #ccc;
      .reportName {
        text-align: center;
        font-weight: 900;
        font-size: 14px;
        color: #000;
        position: relative;
        z-index: 10;
        .QuestionCircleOutlined{
          position: absolute;
          right:-16px;
          top:1px;
        }
      }
      .headerLeft {
        position: absolute;
        left:16px;
        width: 10%;
        cursor: pointer;
        span {
          position: absolute;
          font-weight: 900;
          font-style: oblique;
          font-size: 12px;
          transform: scale(0.8);
          color: #000000;
          bottom: -4px;
          // width: 30px;
          left: -4px;
          font-family: 'HarmonyBold';
        }
      }
      .headerRight {
        position: absolute;
        right:10px;
        width: 35%;
        text-align: right;
        z-index:2;
      }
    }
  }
}
.pagination {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.5);
}
.ant-popover-inner-content{
  span{
    font-weight: 600;
    font-size: 12px;
  }
  div{
    font-size: 12px;
  }
}
img {
  image-rendering: -webkit-optimize-contrast;
}
 :deep(.ant-progress-text){
  transform: translateY(-50%) scale(0.85)!important;
 }
</style>
