<!-- 服务商考核详情 -->
<script setup lang="ts">
import {
    Row as hRow,
    Col as hCol,
    Table as hTable,
    Button as hButton,
    Radio as hRadio,
    Input as hInput,
    DatePicker as hDatePicker,
    message,
    Input as hTextArea,
    Upload as hUpload,
} from 'ant-design-vue';
import { ref, h, computed, onMounted, provide, inject } from 'vue';
import ProcessingDetail from './processing-detail.vue';
import type { ColumnType } from 'ant-design-vue/lib/table/interface';
import dayjs from 'dayjs';
import { serviceExamApi, fileApi } from '@haierbusiness-front/apis';
import { UploadOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue';
import { ServiceExam, ViolationRecord, TablePagination, ProcessingRecord, PaymentRecord, UploadFile, ExamineStateEnum, PunishmentStatusMap, ReceiptRecord } from '@haierbusiness-front/common-libs';
import ReceiptSelect from '@haierbusiness-front/components/mice/receipt/ReceiptSelect.vue';
import { useRoute, useRouter } from 'vue-router';
import { resolveParam, routerParam } from '@haierbusiness-front/utils';


// 获取路由参数
const route = useRoute();
const router = useRouter();
const record = resolveParam(route.query.record as string);
console.log('record', record);
const hideBtn = record?.hideBtn || '';
const localUrl = window.location.href;
// 获取并设置frameModel
const frameModel = inject<any>('frameModel');
if (frameModel) {
    frameModel.value = hideBtn === '1' ? 1 : 0;
}
const businessMiceBid = import.meta.env.VITE_MICE_BID_URL + '#';
const id = computed(() => record?.id || (route.query.id as string));
const isEdit = computed(() => route.query.isEdit === 'true');
const loading = ref(false);
const data = ref<ViolationRecord | null>(null);
const processingRecords = ref<ProcessingRecord[]>([]);
const processingPagination = ref<TablePagination>({
    current: 1,
    pageSize: 3,
    total: 0,
});

// 检查URL并跳转的函数
// const checkAndRedirect = () => {
//     // 如果URL中有record参数，则进行跳转
//     if (route.query.record) {
//         const url =
//             (localUrl.includes('/localhost') ? 'http://localhost:5161/#' : businessMiceBid) +
//             '/bidman/serviceExam/serviceExamDetails?record=' +
//             routerParam(route.query.record);
//         console.log('跳转到:', url);
//         window.location.href = url;
//         return true;
//     }
//     return false;
// };

// 返回列表页
const handleBack = () => {
    router.push('/bidman/serviceExam');
};

// 处理相关状态
const processingOpinion = ref<string>('');
const fileList = ref<UploadFile[]>([]);
// const uploadLoading = ref<boolean>(false);
// const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 文件上传
// const uploadRequest = (options: any) => {
//     uploadLoading.value = true;
//     const formData = new FormData();
//     formData.append('file', options.file);

//     fileApi.upload(formData)
//         .then((it) => {
//             options.file.filePath = baseUrl + it.path;
//             options.file.fileName = options.file.name;
//             options.onProgress(100);
//             options.onSuccess(it, options.file);
//         })
//         .catch((error) => {
//             console.error('上传失败:', error);
//             message.error('文件上传失败，请重试');
//         })
//         .finally(() => {
//             uploadLoading.value = false;
//         });
// };

// 移除文件
// const handleRemove = (file: UploadFile) => {
//     const index = fileList.value.indexOf(file);
//     if (index !== -1) {
//         fileList.value.splice(index, 1);
//     }
// };

// 处理结果选择
const processingResult = ref<number | null>(null);
const handleRadioChange = (e: { target: { value: number } }) => {
    processingResult.value = e.target.value;
};

// 收款记录相关
const selectedPaymentRecord = ref<string>('');
const paymentRecordVisible = ref(false);
const paymentSearchParams = ref({
    payer: '',
    paymentTime: null as dayjs.Dayjs | null,
});

// 收款记录选择
const receiptSelectVisible = ref(false);
const selectedReceipt = ref<ReceiptRecord | null>(null);

const handleSelectReceipt = (receipt: ReceiptRecord) => {
    selectedReceipt.value = receipt;
    console.log('selectedReceipt', receipt);

    // 从originalData中获取时间戳和单号
    if (receipt.originalData) {
        const budat = receipt.originalData.budat || '';
        const belnr = receipt.originalData.belnr || '';

        // 如果有时间戳，转换为年份
        if (budat) {
            try {
                const date = new Date(Number(budat));
                const year = date.getFullYear();
                // 使用年份+单号作为订单号
                selectedPaymentRecord.value = `${year}${belnr}`;
            } catch (error) {
                console.error('时间格式转换失败:', error);
                // 如果转换失败，使用原始的sapReceiveNo
                selectedPaymentRecord.value = receipt.sapReceiveNo || '';
            }
        } else {
            // 如果没有时间戳，使用原始的sapReceiveNo
            selectedPaymentRecord.value = receipt.sapReceiveNo || '';
        }
    } else {
        // 如果没有originalData，使用原始的sapReceiveNo
        selectedPaymentRecord.value = receipt.sapReceiveNo || '';
    }

    console.log('已选择收款记录:', receipt);
};

// 保存处理结果
const handleSave = async () => {
    if (!isEdit.value) {
        handleBack();
        return;
    }

    if (!processingResult.value) {
        message.error('请选择处理结果');
        return;
    }

    // 当选择"通过"时，必须选择收款记录
    if (processingResult.value === 1 && !selectedPaymentRecord.value) {
        message.error('请选择收款记录');
        return;
    }

    if (!processingOpinion.value) {
        message.error('请输入处理意见');
        return;
    }

    // 仅当选择"通过"时，才检查是否上传了处理凭证
    /* if (processingResult.value === 1 && (!fileList.value || fileList.value.length === 0)) {
        message.error('请上传处理凭证');
        return;
    } */

    try {
        loading.value = true;
        const processingRecord = processingRecords.value.find(record => record.result === undefined || record.result === null);
        const params: ServiceExam = {
            id: processingRecord?.id || 0,
            mdrId: data.value?.id,
            result: processingResult.value,
            disposeIdea: processingOpinion.value,
            // path: fileList.value.map(file => file.filePath).filter((path): path is string => path !== undefined),
            acceptCode: processingResult.value === 1 ? selectedPaymentRecord.value : ''
        };
        console.log('params:', params);
        const res = await serviceExamApi.saveMerchant(params);
        console.log(res, 'res');

        if (res === null || res.success) {
            message.success('保存成功');
            router.push('/bidman/serviceExam/list');
        } else {
            message.error(res.message || '保存失败，请重试');
        }
    } catch (error) {
        console.error('保存失败:', error);
        message.error('保存失败，请重试');
    } finally {
        loading.value = false;
    }
};

// 处理详情弹窗相关
const processingDetailVisible = ref(false);
const currentProcessingRecord = ref<ProcessingRecord | null>(null);

// 查看处理详情
const showProcessingDetail = async (record: ProcessingRecord) => {
    try {
        const details = await serviceExamApi.getProcessingDetail(record.id);
        if (details) {
            const processingDetails: ProcessingRecord = {
                id: details.id || 0,
                operateType: (details as ProcessingRecord).operateType || 0,
                illustrate: (details as ProcessingRecord).illustrate || '',
                applyTime: (details as ProcessingRecord).applyTime || '',
                result: (details as ProcessingRecord).result || 0,
                disposeIdea: (details as ProcessingRecord).disposeIdea || '',
                disposeTime: (details as ProcessingRecord).disposeTime || '',
                path: (details as ProcessingRecord).path || ''
            };
            currentProcessingRecord.value = processingDetails;
            processingDetailVisible.value = true;
        }
    } catch (error) {
        console.error('Failed to fetch processing details:', error);
    }
};

// 关闭处理详情弹窗
const handleProcessingDetailClose = () => {
    processingDetailVisible.value = false;
    currentProcessingRecord.value = null;
};

// 处理记录分页变化
const handleProcessingTableChange = (pagination: any) => {
    processingPagination.value.current = pagination.current;
    fetchProcessingRecords();
};

// 过滤处理记录（只显示已处理的记录）
const filteredProcessingRecords = computed(() => {
    return processingRecords.value;
    // 只显示已处理的记录
    // return processingRecords.value.filter(record => record.result !== undefined && record.result !== null);
});

// 违规状态文本
const examineStateValue = computed(() => {
    if (!data.value?.state) return '';
    return PunishmentStatusMap[data.value.state as ExamineStateEnum] || '';
});

// 获取考核详情数据
const fetchDetails = async () => {
    if (!id.value) return;

    try {
        loading.value = true;
        const details = await serviceExamApi.get(Number(id.value));
        data.value = details;
        // 获取处理记录
        await fetchProcessingRecords();
    } catch (error) {
        console.error('获取详情失败:', error);
        message.error('获取详情失败，请重试');
    } finally {
        loading.value = false;
    }
};

// 获取处理记录
const fetchProcessingRecords = async () => {
    if (!id.value) return;

    try {
        const result = await serviceExamApi.listProcessing({
            id: Number(id.value),
            pageNum: processingPagination.value.current,
            pageSize: processingPagination.value.pageSize
        });
        processingRecords.value = result.records || [];
        processingPagination.value.total = result.total || 0;
    } catch (error) {
        console.error('获取处理记录失败:', error);
    }
};

// 表格列定义
const columns: ColumnType<ProcessingRecord>[] = [
    {
        title: '序号',
        dataIndex: 'id',
        width: 80,
        align: 'center',
    },
    {
        title: '操作类型',
        dataIndex: 'operateType',
        width: 120,
        align: 'center',
        customRender: ({ text }) => {
            return text === 10 ? '违规处理' : text;
        }
    },
    {
        title: '说明',
        dataIndex: 'illustrate',
        width: 200,
        align: 'center',
    },
    {
        title: '申请时间',
        dataIndex: 'applyTime',
        width: 160,
        align: 'center',
    },
    {
        title: '结果',
        dataIndex: 'result',
        width: 120,
        align: 'center',
        customRender: ({ text }) => {
            if (text === 1) return '通过';
            if (text === 2) return '驳回';
            return text;
        }
    },
    {
        title: '处理意见',
        dataIndex: 'disposeIdea',
        width: 200,
        align: 'center',
    },
    {
        title: '处理时间',
        dataIndex: 'disposeTime',
        width: 160,
        align: 'center',
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 100,
        fixed: 'right',
        align: 'center',
    },
];

// 付款记录表格列定义
const paymentColumns: ColumnType<PaymentRecord>[] = [
    {
        title: '收款单号',
        dataIndex: 'paymentCode',
        width: 120,
        align: 'center',
    },
    {
        title: '付款单位',
        dataIndex: 'payer',
        width: 150,
        align: 'center',
    },
    {
        title: '付款时间',
        dataIndex: 'paymentTime',
        width: 150,
        align: 'center',
    },
    {
        title: '付款金额',
        dataIndex: 'amount',
        width: 120,
        align: 'center',
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 100,
        align: 'center',
        customRender: ({ record }: { record: PaymentRecord }) => {
            return h('div', [
                h('h-button', {
                    type: 'link',
                    onClick: () => {
                        selectedPaymentRecord.value = record.paymentCode;
                        paymentRecordVisible.value = false;
                    }
                }, '选择')
            ]);
        },
    },
];

const paymentRecords = ref<PaymentRecord[]>([]);

const handlePaymentSearch = () => {
    // 实现搜索逻辑
};

const handlePaymentReset = () => {
    paymentSearchParams.value = {
        payer: '',
        paymentTime: null,
    };
};

// 组件加载时获取数据
onMounted(() => {
    // 检查是否需要跳转，如果需要跳转则不加载数据
    // if (!checkAndRedirect()) {
    fetchDetails();
    // }
});
</script>

<template>
    <div class="violation-details-container" :class="{ 'wide-padding': hideBtn === '1' }">
        <div class="violation-details" v-if="data">
            <h2 class="title">海尔会务会展系统关于{{ data?.title }}未提报方案违规通知</h2>
            <h3><span></span>违规单号</h3>
            <div class="Modify">
                <h-row class="info-row">
                    <h-col :span="8">
                        <span class="label">违规单号：</span>
                        <span class="value">{{ data?.examineCode }}</span>
                    </h-col>
                    <h-col :span="8">
                        <span class="label">关联单号：</span>
                        <span class="value">{{ data?.mainCode }}</span>
                    </h-col>
                    <h-col :span="8">
                        <span class="label">通知时间：</span>
                        <span class="value">{{ data?.gmtCreate }}</span>
                    </h-col>
                </h-row>
                <h-row class="info-row">
                    <h-col :span="8">
                        <span class="label">违规状态：</span>
                        <span class="value">{{ examineStateValue }}</span>

                    </h-col>
                    <h-col :span="8">
                        <span class="label">处理截止时间：</span>
                        <span class="value">{{ data?.violationDisposeEndTime }}</span>
                    </h-col>
                </h-row>
                <h-row class="info-row" v-if="data?.rejectReason">
                    <h-col :span="8">
                        <span class="label">审核驳回理由：</span>
                        <span class="value">{{ data.rejectReason }}</span>
                    </h-col>
                </h-row>
            </div>

            <div class="violation-details-section">
                <h3 class="section-title"><span></span>违规细节</h3>
                <div class="detail-item">
                    <span class="label">考核条目名称：</span>
                    <span class="value">{{ data?.entry }}</span>
                </div>
                <div class="detail-item">
                    <span class="label">考核条目细节：</span>
                    <span class="value">{{ data?.details }}</span>
                </div>
                <div class="detail-item">
                    <span class="label">违规类型：</span>
                    <span class="value">{{ data?.type === 1 ? '违规' : data?.type === 2 ? '整改' : '-' }}</span>
                </div>
                <div class="detail-item">
                    <span class="label">违规措施：</span>
                    <span class="value">
                        {{ data?.score && data.score < 0 ? `扣${Math.abs(data.score)}分` : '' }} {{ data?.score &&
                            data.score > 0 ? `加${data.score}分` : '' }}
                            {{ data?.score && data?.fine ? '，' : '' }}
                            {{ data?.fine && data.fine < 0 ? `罚款${Math.abs(data.fine)}元` : '' }} {{ data?.fine &&
                                data.fine > 0 ? `奖励${data.fine}元` : '' }}
                                {{ !data?.score && !data?.fine ? '-' : '' }}
                    </span>
                </div>
            </div>

            <div class="violation-details-section">
                <h3 class="section-title"><span></span>违规描述</h3>
                <div class="detail-item">
                    <span class="label">描述：</span>
                    <span class="value">{{ data?.violationDesc }}</span>
                </div>
                <div class="detail-item">
                    <span class="label">违规时间：</span>
                    <span class="value">{{ data?.violationTime }}</span>
                </div>
                <div class="detail-item">
                    <span class="label">见证性材料：</span>
                    <span class="value">
                        <template v-if="data?.path && data.path.length > 0">
                            <div v-for="(file, index) in data.path" :key="index" class="detail-item">
                                <a :href="file" target="_blank" class="file-link">
                                    文件{{ index + 1 }}
                                </a>
                            </div>
                        </template>
                        <span v-else>-</span>
                    </span>
                </div>
            </div>

            <div class="violation-details-section">
                <h3 class="section-title"><span></span>处理记录</h3>
                <h-table :columns="columns" :data-source="filteredProcessingRecords" :pagination="processingPagination"
                    :scroll="{ y: 300 }" size="small" @change="handleProcessingTableChange">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex === 'operation'">
                            <h-button type="link" @click="showProcessingDetail(record)">查看</h-button>
                        </template>
                    </template>
                </h-table>
            </div>
            <div v-if="(data?.state === 20) && isEdit" class="violation-details-section">
                <h3 class="section-title"><span></span>违规处理</h3>
                <div class="detail-item">
                    <span class="label">结果：</span>
                    <span class="value">
                        <a-radio-group v-model:value="processingResult" class="radio-group" @change="handleRadioChange">
                            <a-radio :value="1">通过</a-radio>
                            <a-radio :value="2">驳回</a-radio>
                        </a-radio-group>
                    </span>
                </div>
                <div class="detail-item">
                    <span class="label">收款记录：</span>
                    <span class="value">
                        {{ selectedPaymentRecord || '-' }}
                        <h-button type="link" @click="receiptSelectVisible = true">选择</h-button>
                    </span>
                </div>
                <div class="detail-item">
                    <span class="label">处理意见：</span>
                    <span class="value">
                        <h-text-area v-model:value="processingOpinion" :rows="4" :maxlength="200" show-count
                            placeholder="请输入处理意见" style="width: 30%;" />
                    </span>
                </div>
                <!-- <div class="detail-item">
                    <span class="label">处理凭证：</span>
                    <span class="value">
                        <h-upload v-model:fileList="fileList" :custom-request="uploadRequest" :multiple="true"
                            :max-count="1" @remove="handleRemove"
                            accept=".pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx" :show-upload-list="true">
                            <h-button>
                                <upload-outlined></upload-outlined>
                                上传凭证
                            </h-button>
                        </h-upload>
                    </span>
                </div> -->

                <div style="text-align: center; margin-top: 20px;">
                    <h-button v-if="isEdit && data?.state === 20" type="primary" @click="handleSave"
                        :loading="loading">保存</h-button>
                </div>
            </div>
        </div>

        <processing-detail :visible="processingDetailVisible" :data="currentProcessingRecord"
            @cancel="handleProcessingDetailClose" />

        <receipt-select :visible="receiptSelectVisible" @update:visible="receiptSelectVisible = $event"
            @select="handleSelectReceipt" @cancel="receiptSelectVisible = false" />
    </div>
</template>

<style scoped lang="less">
.violation-details-container {
    background-color: #fff;
    height: 100%;
    width: 100%;
    overflow: auto;

    &.wide-padding {
        padding: 20px 300px;
    }
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;

    .back-button {
        margin-right: 16px;
    }
}

.violation-details {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    padding: 30px;

    .title {
        text-align: center;
        font-weight: bold;
        margin-bottom: 30px;
    }

    .Modify {
        padding: 20px;
        background-color: #f9f9f9;
        border-radius: 6px;
        margin-bottom: 20px;
    }

    .info-row {
        margin-bottom: 20px;
    }

    .label {
        font-weight: bold;
        margin-right: 12px;
    }

    .value {
        color: #666;
    }

    .violation-details-section {
        margin-top: 30px;
        border-top: 1px solid #f0f0f0;
        padding-top: 20px;

        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .detail-item {
            margin-bottom: 16px;
            line-height: 1.6;
        }
    }

    .file-link {
        color: #1890ff;
        text-decoration: none;
        margin-right: 6px;
    }

    .radio-group {
        display: inline-flex;
        gap: 30px;
    }
}

.payment-record-search {
    margin-bottom: 16px;

    .label {
        font-weight: bold;
        margin-right: 8px;
    }

    .date-picker {
        width: 100%;
    }

    .search-buttons {
        text-align: right;

        button {
            margin-right: 8px;
        }
    }
}

.payment-table {
    margin-top: 16px;
}

h3 {
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;

    span {
        display: inline-block;
        width: 4px;
        height: 20px;
        margin-right: 10px;
        background: #1868DB;
    }
}
</style>