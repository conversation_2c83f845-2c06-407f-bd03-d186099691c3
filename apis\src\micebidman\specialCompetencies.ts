import { download, get, post } from '../request'
import { 
    ISpecialCompetenciesFilter, 
    ISpecialCompetencies,
    IPageResponse, 
    Result } from '@haierbusiness-front/common-libs'


export const specialCompetenciesApi = {
    list: (params: ISpecialCompetenciesFilter): Promise<IPageResponse<ISpecialCompetencies>> => {
        return get('mice-bid/api/mice/special/power/page', params)
    },

    get: (id: number): Promise<ISpecialCompetencies> => {
        return get('merchant/api/specialCompetencies/get', {
            id
        })
    },

    save: (params: ISpecialCompetencies): Promise<Result> => {
        return post('merchant/api/specialCompetencies/save', params)
    },

    edit: (params: ISpecialCompetencies): Promise<Result> => {
        return post('merchant/api/specialCompetencies/update', params)
    },
    details: (id: number): Promise<ISpecialCompetencies> => {
        return get('mice-bid/api/mice/special/power/view', {id})
    },

    remove: (id: number): Promise<Result> => {
        return post(`mice-bid/api/mice/special/power/delete?id=${id}`,)
    },
}
