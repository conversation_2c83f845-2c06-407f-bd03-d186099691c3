import { download, get, post } from '../request'
import { 
    I{{ properCase modelName }}Filter, 
    I{{ properCase modelName }},
    IPageResponse, 
    Result } from '@haierbusiness-front/common-libs'


export const {{ camelCase modelName }}Api = {
    list: (params: I{{ properCase modelName }}Filter): Promise<IPageResponse<I{{ properCase modelName }}>> => {
        return get('merchant/api/{{ camelCase modelName }}/list', params)
    },

    get: (id: number): Promise<I{{ properCase modelName }}> => {
        return get('merchant/api/{{ camelCase modelName }}/get', {
            id
        })
    },

    save: (params: I{{ properCase modelName }}): Promise<Result> => {
        return post('merchant/api/{{ camelCase modelName }}/save', params)
    },

    edit: (params: I{{ properCase modelName }}): Promise<Result> => {
        return post('merchant/api/{{ camelCase modelName }}/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('merchant/api/{{ camelCase modelName }}/delete', { id })
    },
}
