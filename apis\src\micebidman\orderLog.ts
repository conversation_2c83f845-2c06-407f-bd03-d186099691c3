import { IPageResponse, MiceBidManOrderLogList,MiceBidManMemorandum, Result, MiceBidManMemorandumfilter } from '@haierbusiness-front/common-libs';
import { download, get, post, originalPost } from '../request';

export const miceBidManOrderLogListApi = {
  //会议日志
  LogList: (params = {}): Promise<IPageResponse<MiceBidManOrderLogList>> => {
    return get('/mice-bid/api/common/log/record/info', params);
  },
  //会议备忘录
  memorandum: (params = {}): Promise<IPageResponse<MiceBidManMemorandumfilter>> => {
    return get('/mice-bid/api/mice/memo/page', params);
  },
  //备忘录新增
  add: (params = {}): Promise<IPageResponse<MiceBidManMemorandum>> => {
    return post('/mice-bid/api/mice/memo/add', params);
  },
  //备忘录编辑
  edit: (params = {}): Promise<MiceBidManMemorandum> => {
    return post('/mice-bid/api/mice/memo/update', params);
  },
  //备忘录完成
  treated: (params:{id:number,completeExplain:string}): Promise<MiceBidManMemorandum> => {
    return post('/mice-bid/api/mice/memo/treated', params);
  },
  //备忘录详情
  details: (id:number): Promise<MiceBidManMemorandum> => {
    return get('/mice-bid/api/mice/memo/detail', {id});
  },
  //备忘录删除
  delete: (id:number): Promise<Result> => {
    return post(`/mice-bid/api/mice/memo/delete?id=${id}`);
  },
  
};