<script setup lang="ts">
import {
  BreadcrumbItem as hBreadcrumbItem,
  Breadcrumb as hBreadcrumb,
  LayoutSider as hLayoutSider,
  LayoutFooter as hLayoutFooter,
  Layout<PERSON>ontent as hLayoutContent,
  LayoutHeader as hLayoutHeader,
  Layout as hLayout,
  MenuItem as hMenuItem,
  MenuItemGroup as hMenuItemGroup,
  SubMenu as hSubMenu,
  Menu as hMenu,
  Divider as hDivider,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Result as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Tabs as hTabs,
  message,
  RadioGroup as hRadioGroup,
  RadioButton as hRadioButton,
  Table as hTable,
  Modal as hModal,
  Tree as hTree,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Switch as hSwitch,
  Select  as hSelect,
  SelectOption as hSelectOption,
  Image as hImage,
  Progress as hProgress,
} from 'ant-design-vue';
import { onMounted, ref, computed, watch,createVNode  } from 'vue';
import {
  UploadOutlined,
  UserOutlined,
  NotificationOutlined,
  AppstoreOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';
import EManage from '@haierbusiness-front/components/manange/EManange.vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { hotelApi,addressApi } from '@haierbusiness-front/apis';
import { GetMappingHotelListRes,HotelSyncRecordRes,GetHotelSyncRes,asyncStatus,aggregationStatus } from '@haierbusiness-front/common-libs'
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from "@haierbusiness-front/utils";
import { DataType, usePagination, useRequest } from 'vue-request';
import type { TreeProps } from 'ant-design-vue';
const route = ref(getCurrentRoute());
const store = applicationStore();
const { resource } = storeToRefs(store);


// 根据id 获取同步信息 getHotelSync
const info = ref<GetHotelSyncRes>({})
const numberInfo = ref<any>({})
const getInfo = ()=>{
  hotelApi.getHotelSync({hotelSyncId:route.value.query.id}).then((res:GetHotelSyncRes)=>{
    info.value = res
  })
}

const getNumberInfo = ()=>{
  hotelApi.hotelSyncRecordStatistics({hotelSyncId:route.value.query.id}).then((res:any)=>{
    numberInfo.value = res
  })
}

const columnsFormng = [
  {
    title: '名称',
    dataIndex: 'name',
    width:"250px",
    align:"center",
    key: 'name',
  },
  {
    title: '编码',
    dataIndex: 'providerHotelCode',
    key: 'providerHotelCode',
    align:"center",
  },
  {
    title: '地址',
    dataIndex: 'address',
    key: 'address',
    width:"250px",
    align:"center",
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    align:"center",
    key: 'phone',
  },
  {
    title: '品牌',
    dataIndex: 'brandName',
    key: 'brandName',
    align:"center",
    ellipsis: true,
  },
  {
    title: '城市',
    dataIndex: 'cityName',
    align:"center",
    key: 'cityName',
    ellipsis: true,
  },
  {
    title: '供应商城市',
    dataIndex: 'mappingCityName',
    align:"center",
    key: 'mappingCityName',
    ellipsis: true,
  },
  {
    title: '区域',
    dataIndex: 'regionName',
    align:"center",
    key: 'regionName',
    ellipsis: true,
  },
  {
    title: '供应商区域',
    dataIndex: 'mappingRegionName',
    align:"center",
    key: 'mappingRegionName',
    ellipsis: true,
  },
  {
    title: '类型',
    dataIndex: 'type',
    align:"center",
    key: 'type',
  },
]
const reset = () =>{
  searchParam.value = {
    hotelSyncId:route.value.query.id
  }
  listApiRun({
    ...searchParam.value,
    pageNum: current.value,
    pageSize: pageSize.value,
  });
}
// 酒店分页
const searchParam = ref<HotelSyncRecordRes>({
  hotelSyncId:route.value.query.id
})
const {
  data:dataList,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(hotelApi.hotelSyncRecordPage);

const dataSource = computed(() => dataList.value?.records || []);

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const pagination = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: dataList.value?.total,
    current: dataList.value?.pageNum,
    pageSize: dataList.value?.pageSize,
    style: { justifyContent: 'center' },
}));

// 获取国内城市下拉列表
const hotelList = ref<any>([])
const getDistrictList = ()=>{
  addressApi.getDistrictList({code:'CN',level:'city'}).then(res=>{
    hotelList.value = res.records
  })
}

const filterOption = (input: string, option: any) => {
  return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const brandFilterOption = (input: string, option: any) => {
  return option.brandName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};


// 获取区域
const areaList = ref<any>([])
const getAreaList = (value:number)=>{
  addressApi.getDistrictList({code:'CN',level:'district',cityId:value}).then(res=>{
    areaList.value = res.records
  })
}

// 选择完城市 获取区域
const cityChange = (value:number) =>{
  console.log(value)
  if(value){
    getAreaList(value)
  }else{
    areaList.value = []
  }
}

// 获取品牌下拉列表
const BrandList = ref<any>([])
const getBrandList = ()=>{
  hotelApi.getHotelBrandProviderMapList({type:2}).then(res=>{
    BrandList.value = res.records
  })
}
const searchList = (value:any)=>{
  searchParam.value.type = value
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize:10,
  });
}

onMounted(()=>{
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize:10,
  });
  getInfo()
  getNumberInfo()
  getDistrictList()
  getBrandList()
})

</script>

<template>
  <div class="pageBox">
    <div class="headerBox">
        <h-form :labelCol="{span:5, offset: 1}"  style="width:100%;">
        <h-row>
          <h-col :span="6">
            <h-form-item label="编码">
              <h-input allow-clear  v-model:value="searchParam.providerHotelCode" placeholder="供应商编码" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="酒店名称">
              <h-input allow-clear  v-model:value="searchParam.name" placeholder="酒店名称" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="电话">
              <h-input allow-clear  v-model:value="searchParam.phone" placeholder="电话" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="品牌">
              <h-select ref="city" show-search :fieldNames="{label:'brandName',value:'id'}" :options="BrandList" :filter-option="brandFilterOption" v-model:value="searchParam.mappingBrandId" style="width: 100%" allow-clear />
            </h-form-item>
          </h-col>
        </h-row>
        <h-row>
          <h-col :span="6">
            <h-form-item label="地址">
              <h-input allow-clear  v-model:value="searchParam.address" placeholder="地址" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="城市">
              <h-select ref="city" @change="cityChange" show-search :fieldNames="{label:'name',value:'id'}" :options="hotelList" :filter-option="filterOption" v-model:value="searchParam.mappingCityId" style="width: 100%" allow-clear />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="区域">
              <h-select ref="city" :disabled="!searchParam.mappingCityId" show-search :fieldNames="{label:'name',value:'id'}" :options="areaList" :filter-option="filterOption" v-model:value="searchParam.mappingRegionId" style="width: 100%" allow-clear />
            </h-form-item>
          </h-col>
        </h-row>
        <h-row>
        <h-col :span="20" style="text-align: left;line-height: 32px;font-weight:600;">
          <span :span="2">供应商:{{info.providerCodeName}}</span>
          <span style="margin-left:20px;" :span="4">同步状态：
            <!-- {{ asyncStatus[info.syncStatus]}} -->
            <a-tag v-if="info.syncStatus==20" color="green">{{asyncStatus[info.syncStatus]}}</a-tag>
            <a-tag v-else color="red">{{asyncStatus[info.syncStatus]}}</a-tag>
          </span>
          <span style="margin-left:20px;" :span="4">聚合状态：
            <a-tag v-if="info.mappingStatus==20" color="green">{{aggregationStatus[info.mappingStatus]}}</a-tag>
            <a-tag v-else color="red">{{aggregationStatus[info.mappingStatus]}}</a-tag>
          </span>
          <span style="margin-left:20px;" :span="4">开始时间：{{ info.startTime}}</span>
          <span style="margin-left:20px;" :span="4">结束时间：{{ info.startTime}}</span>
          <a-tag color="default" @click="searchList(1)" style="margin-left:20px;color:green;  cursor: pointer;" :span="4">新增数量：{{ numberInfo.addNum}}</a-tag>
          <a-tag color="default" @click="searchList(2)" style="margin-left:20px;color:#9e479f;  cursor: pointer;" :span="4">更新数量：{{ numberInfo.updateNum}}</a-tag>
          <a-tag color="default" @click="searchList(3)" style="margin-left:20px;color:red;  cursor: pointer;" :span="4">下架数量：{{ numberInfo.delistingNum}}</a-tag>
        </h-col>
          <h-col :span="4" style="text-align: right;">
          <h-button style="margin-right: 10px" @click="reset">重置</h-button>
          <h-button
              type="primary"
              @click="handleTableChange({ current: 1, pageSize: 10 })"
            >
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-form>
        <!-- </h-row> -->
    </div>
    <div  class="contentBox">
      <h-table  :columns="columnsFormng" :size="'small'"  :scroll="{ x: 1550,y:'calc(100vh - 440px)' }" :data-source="dataSource" :loading="loading" :pagination="pagination" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'type'">
              <!-- {{ record.type == 1?'新增':record.type==2?'酒店信息修改':'酒店信息删除' }} -->
              <a-tag v-if="record.type==2" color="green">修改</a-tag>
              <a-tag v-if="record.type==3" color="red">下架</a-tag>
              <a-tag v-if="record.type==1" color="orange">新增</a-tag>
            </template>
            <template v-if="column.dataIndex === 'address'">
            <div :title="record.address" class="multi-line-ellipsis">
              {{ record.address }}
            </div>
          </template>
          <template v-if="column.dataIndex === 'name'">
            <div :title="record.name" class="multi-line-ellipsis">
              {{ record.name }}
            </div>
          </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button @click="addMapping(record)" type="link">映射</h-button>
            </template>
          </template>
        </h-table>
    </div>
  </div>
</template>

<style scoped lang="less">
.pageBox {
  padding: 0 8px;
//   height: calc(100vh - 100px);
//   overflow:auto;
  .headerBox {
    width: 100%;
    // height: 60px;
    background: #fff;
    display: flex;
    align-items: center;
    padding: 24px;
  }
  .inputBox {
    display: flex;
    align-items: center;
    // margin-left: 60px;
  }
  .contentBox {
    margin-top: 16px;
    background: #fff;
    height: calc(100vh - 320px);
  }
}
.modalHeaderBox{
  width: 100%;
  // display: flex;
}
</style>
