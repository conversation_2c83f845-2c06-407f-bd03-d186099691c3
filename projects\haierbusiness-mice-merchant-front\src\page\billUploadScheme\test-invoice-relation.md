# 发票/水单关联功能测试说明

## 功能概述 📋
实现了发票和水单与各类项目（住宿、会场、用餐、用车、服务人员、活动）的关联功能。

## 测试步骤 🧪

### 1. 准备测试数据
确保有以下数据：
- 发票数据：包含 `tempId` 字段
- 水单数据：包含 `tempId` 字段  
- 方案数据：包含 stays, places, caterings, vehicles, attendants, activities

### 2. 测试发票关联
1. 点击发票信息中的"查看>>"按钮
2. 弹出关联账单弹框
3. 选择要关联的项目（住宿、会场等）
4. 点击"确认添加"
5. 验证选中项目的 `invoiceTempId` 字段被正确设置

### 3. 测试水单关联
1. 点击水单信息中的"查看>>"按钮
2. 弹出关联账单弹框
3. 选择要关联的项目
4. 点击"确认添加"
5. 验证选中项目的 `statementTempId` 字段被正确设置

## 预期结果 ✅

### 发票关联后的数据结构
```javascript
schemePlanObj.value = {
  stays: [
    {
      id: 1,
      invoiceTempId: "invoice_1703123456_123", // ✅ 被关联
      statementTempId: null,
      // ... 其他字段
    }
  ],
  places: [
    {
      id: 2,
      invoiceTempId: "invoice_1703123456_123", // ✅ 被关联
      statementTempId: null,
      // ... 其他字段
    }
  ]
  // ... 其他类型数据
}
```

### 水单关联后的数据结构
```javascript
schemePlanObj.value = {
  stays: [
    {
      id: 1,
      invoiceTempId: "invoice_1703123456_123", // 保持不变
      statementTempId: "statement_1703123456_456", // ✅ 新关联
      // ... 其他字段
    }
  ]
  // ... 其他类型数据
}
```

## 关键修改点 🔧

1. **handleUpdateStaysInvoiceId 方法**：支持所有类型数据的关联
2. **RelatedBillDialog 组件**：按类型分组选中的项目ID
3. **数据传递**：使用 schemePlanObj 而不是 demandDetail
4. **类型判断**：使用 tempId 而不是 id 进行判断

## 注意事项 ⚠️

1. 确保发票和水单数据都有唯一的 `tempId`
2. 关联关系是多对多的（一个发票可以关联多个项目，一个项目可以同时关联发票和水单）
3. 数据提交时需要包含 `invoiceTempId` 和 `statementTempId` 字段
