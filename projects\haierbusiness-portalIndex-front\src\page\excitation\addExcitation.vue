<script lang="ts" setup>
import { computed, createVNode, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';
import Big from 'big.js';

import {
  <PERSON>chor as hAnchor,
  <PERSON><PERSON> as hButton,
  Spin as hSpin,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  UploadProps,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Checkbox as hCheckbox,
  Form as hForm,
  FormItem as hFormItem,
  FormItemRest as hFormItemRest,
  Row as hRow,
  Col as hCol,
  Popconfirm as hPopconfirm,
  Upload as hUpload,
  Input as hInput,
  Modal as hModal,
  InputNumber as hInputNumber,
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  DownloadOutlined,
  VerticalAlignBottomOutlined,
  ExclamationCircleFilled,
  EnvironmentFilled,
  InfoCircleOutlined,
  PlusOutlined,
  ZoomInOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  SendOutlined,
  PhoneOutlined,
  VerticalAlignTopOutlined,
  DeleteOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { download, tripApi, teamListApi, rechargeApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import relativeTime from 'dayjs/plugin/relativeTime';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';

import { CityResponse, CityItem, TCteateTeam, RRechargeParmas } from '@haierbusiness-front/common-libs';
import { fileApi } from '@haierbusiness-front/apis';
import type { TableColumnType } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import Countdown from 'vue3-countdown'
import { radioGroupProps } from 'ant-design-vue/es/radio/Group';
import Index from '../index.vue';

const route = ref(getCurrentRoute());
const router = useRouter();

const id = route.value?.query?.id;

dayjs.extend(relativeTime);

const store = applicationStore();
const { loginUser } = storeToRefs(store);
console.log('🚀 ~ loginUser:', loginUser, loginUser.value.phone);

const labelCol = { span: 3 };
const wrapperCol = { span: 21 };
const spinning = ref<boolean>(false);
let notifyFlag = ref(true);
const toCheck = ref(false);

const rechargeForm = ref<any>({
  personList: [],
  failList: [],
  payType: 0,
  checked: false,
  amount: 0,
  incentiveMessage: '',
});
let tableSourceData = reactive([]);
let tableSourceDataError = reactive([]);

const validateTrue = (_: any, value: boolean) =>
  value === true ? Promise.resolve() : Promise.reject(new Error('请确认明细数据'));

const rules: Record<string, Rule[]> = {
  payType: [{ required: true, message: '请选择支付类型', trigger: 'change' }],
  checked: [{ required: true, validator: validateTrue, trigger: 'change' }],
  personList: [{ required: true, message: '请上传PDF报告文件', trigger: 'change' }],
};

const tableRules : Record<string, Rule[]> = {
  userName: [{ required: true, message: '请选择下发员工', trigger: 'change' }],
  userCode: [{ required: true,  message: '请选择下发员工', trigger: 'change' }],
  amount: [{ required: true, message: '请填写下发金额', trigger: 'change' }],
};

const formRef = ref();

// 弹窗
const open = ref<boolean>(false);
const errorOpen = ref<boolean>(false);

const loading = ref<boolean>(false);
const searchLoading = ref<boolean>(false);
const codeCount = ref(0)

const handleOk = () => {
  loading.value = true
  console.log('🚀 ~ .handleOk ~ res:', formRef.value);
  const params = {
    incentiveMessage: rechargeForm.value.incentiveMessage,
    users: rechargeForm.value.personList,
    notifyFlag: notifyFlag.value,
    phone: loginUser.value?.phone, // '***********',
    code: rechargeForm.value?.code,
    fileUrl: rechargeForm.value.fileUrl,
    paymentAccount: rechargeForm.value.paymentAccount,
    paymentAccountName: rechargeForm.value.paymentAccountName,
  };
  rechargeApi.issueSave(params).then((res) => {
    console.log('🚀 ~ rechargeApi.issueSave ~ res:', res);
    open.value = false;
    
    hMessage.success('提交成功,激励下发中');
    setTimeout(()=> {
      router.push({ path: '/card-order/excitation' });
      loading.value = false
    }, 2000)
  }).catch(err => {
    loading.value = false
  })
};
const handleErrorOk = () => {
  errorOpen.value = false;
};
const cancel = () => {
  open.value = false;
};


const changeSwitch = (val:boolean) => {
}

const onSearch = () => {
  if(searchLoading.value) {
    return
  }

  searchLoading.value = true;
  codeCount.value++

  rechargeApi.sendCodeCaptcha({ phone: loginUser?.value.phone }).then((res) => {
    console.log('🚀 ~ rechargeApi.sendCodeCaptcha ~ res:', res);
    
    hMessage.success('验证码已发送');
    setTimeout(() => {
      searchLoading.value = false;
    }, 60 * 1000); 
     
  }).catch(err => {
    searchLoading.value = false;
  })
 
};

const downloadTemp = () => {
  rechargeApi.downloadTemplate().then((res) => {
    console.log('🚀 ~ rechargeApi.downloadTemplate ~ res:', res);
  });
};

// 上传相关

const uploadLoading = ref(false);
const beforeUpload = (file) => {

}
const uploadPeople = (options: any) => {
  console.log('🚀 ~ uploadPeople ~ options:', options);
  uploadLoading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);
  rechargeApi
    .peopleImport(formData)
    .then((it) => {
      console.log('🚀 ~ .then ~ it:', it);
      rechargeForm.value.personList = it.successUsers;
      rechargeForm.value.fileUrl = it.fileUrl;
      rechargeForm.value.amountSum = it.amountSum;
      rechargeForm.value.failList = it.failUser;
      
      rechargeForm.value.incentiveMessage = it.incentiveMessage;
      
      options.onProgress(100);
      options.onSuccess(it, options.file); 
      if (it.failUser.length > 0) {
        errorOpen.value = true;

        return;
      }

      //这里必须加上这个，不然会一直显示一个正在上传中的框框
      toCheck.value = false;
    })
    .catch((e) => {
      console.error('🚀 ~ .catch ~ e:', e);
      options.onCancel;
      // teamForm.value.fileList = [];
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};
// 提交
const onSubmit = () => {
  if (!toCheck.value) {
    hModal.error({
      content: '提交前请先确认明细数据',
      okText: '去确认',
    });
    return;
  }

  if (rechargeForm?.value?.amountSum > rechargeForm?.value?.amount) {
    hModal.error({
      content: '当前账户余额不足，请确认下发金额',
      okText: '确认',
    });
    return;
  }

  localStorage.setItem('incentiveMessage', rechargeForm.value.incentiveMessage);
  formRef.value
    .validate()
    .then(() => {
      if (!rechargeForm?.value?.personList || rechargeForm.value.personList.length == 0) {
        hModal.error({
          content: '人员明细为空，请导入数据',
          okText: '确认',
        });
        return;
      }
      open.value = true;
    })
    .catch((error: any) => {
      console.error('🚀 ~ .catch ~ error:', error);
    });
};
// 清空
const resetList = () => {
  fileList.value = []
  rechargeForm.value.personList = [];
  rechargeForm.value.amountSum = 0;
};

// 下载人员明细模板

const detail = ref<TCteateTeam>({});

const getDetail = (id: string) => {};

const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    align: 'center',
    width: 60

  },
  {
    title: '员工姓名',
    dataIndex: 'userName',
    align: 'center',
    width: 200
  },
  {
    title: '员工工号',
    dataIndex: 'userCode',
    align: 'center',
    width: 200
  },

  {
    title: '下发金额',
    dataIndex: 'amount',
    align: 'center',
    width: 200
  },
  
];
const columnsHandle = [
  {
    title: '序号',
    dataIndex: 'index',
    align: 'center',
  },
  {
    title: '员工姓名',
    dataIndex: 'userName',
    align: 'center',
    width: 240
  },
  {
    title: '员工工号',
    dataIndex: 'userCode',
    align: 'center',
    width: 240
  },

  {
    title: '下发金额',
    dataIndex: 'amount',
    align: 'center',
    width: 200
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 100
  },
]; 
const columnsError = [
  {
    title: '序号',
    dataIndex: 'index',
    align: 'center',
  },
  {
    title: '员工姓名',
    dataIndex: 'userName',
    align: 'center',
  },
  {
    title: '员工工号',
    dataIndex: 'userCode',
    align: 'center',
  },

  {
    title: '下发金额',
    dataIndex: 'amount',
    align: 'center',
  },
  {
    title: '失败原因',
    dataIndex: 'failReason',
    align: 'center',
  },
];
const tableKey = ref(0);
const tableKeyError = ref(0);

watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);
watch(
  () => rechargeForm.value.personList,
  (newValue) => {
    console.log('New table data:', newValue);

    tableSourceData = newValue;
    tableSourceData.forEach((item:any,index:number)=>{
      item.index = index+1
    })
    console.log('🚀 ~ tableSourceData:', tableSourceData);
    tableKey.value += 1; // 改变 key 值以强制重新渲染表格
  },
);
watch(
  () => rechargeForm.value.failList,
  (newValue) => {
    console.log('New table data error:', newValue);

    tableSourceDataError = newValue;
    console.log('🚀 ~ tableSourceDataError:', tableSourceDataError);
    tableKeyError.value += 1; // 改变 key 值以强制重新渲染表格
  },
);
const getAccountNo = () => {
  rechargeApi.getAndInitTradeUnionAccount().then((res) => {
    console.log(res, '-------------------------------');
    rechargeForm.value.paymentAccount = res.accountNo;
    rechargeForm.value.paymentAccountName = res.accountName;
    rechargeForm.value.amount = res.amount;
    console.log('rechargeForm', rechargeForm);
  });
};

watch(
  () => rechargeForm.value.amountSum,
  (val) => {
    rechargeForm.value.surplusAmount = new Big(rechargeForm.value.amount || 0).minus(rechargeForm.value.amountSum || 0)
  }
)

watch(
  () => rechargeForm.value.amount,
  (val) => {
    rechargeForm.value.surplusAmount = new Big(rechargeForm.value.amount || 0).minus(rechargeForm.value.amountSum || 0)
  }
)

onMounted(() => {
  toCheck.value = false;
  getAccountNo();
  if (localStorage.getItem('incentiveMessage')) {
    rechargeForm.value.incentiveMessage = localStorage.getItem('incentiveMessage');
  }
});

const pagination = computed(() => ({
  total: tableSourceData?.length || 0, // 总条数
  showTotal: (total:number) => `共 ${total} 条`, // 显示总条数
  pageSize: 10, // 每页显示的条数
  showSizeChanger: true, // 是否可以改变每页显示的条数
  // showQuickJumper: true, // 是否可以快速跳转到指定页
}));

const fileList = ref<UploadProps['fileList']>([
]);

const addType = ref<number>(1)
const radioChange = () => {
  resetList()
}

const addPersonData = () => {
  let param = {}
  if (rechargeForm.value?.personList.length == 0) {
    param = {
      index: 1
    }
  }else {
    param.index = rechargeForm.value?.personList[rechargeForm.value?.personList.length-1].index + 1
  }
  rechargeForm.value?.personList?.push(param)
  
}
const userNameChange = (userInfo: IUserInfo, record:any) => {
  record.userCode = userInfo?.username;
  record.userName = userInfo?.nickName;
};
// 用户选择
const userParams = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});

const changeAmount = () => {
  let amountSum = 0
  rechargeForm.value.personList.forEach((item:any) => {
    amountSum = new Big(amountSum).plus(new Big(item.amount))
  })
  
  rechargeForm.value.amountSum = amountSum
  rechargeForm.value.surplusAmount = new Big(rechargeForm.value.amount || 0).minus(rechargeForm.value.amountSum || 0)
}

const deleteRecord = (index:number) => {
  rechargeForm.value.personList.splice(index,1)
  rechargeForm.value.personList.forEach((item:any,index:number)=>{
    item.index = index+1
  })
  changeAmount()
}
const cancelRecord = () => {
  
}
</script>

<template>
  <div class="container">
    <div class="row flex">
      <div class="apply-con flex">
        <h-row justify="center" align="middle" style="padding: 40px; font-size: 20px; font-weight: 600">
          商务云支付下发
        </h-row>
        <h-form
          class="mt-30"
          ref="formRef"
          :model="rechargeForm"
          :label-col="labelCol"
          :rules="rules"
          :wrapper-col="wrapperCol"
          labelAlign="left"
        >
          <!-- 导入方式 -->
          <h-form-item name="addType">
            <template #label>
              <span>导入方式
                <a-tooltip>
                  <template #title>可选择通过模板导入或者手动录入</template>
                  <question-circleOutlined class="icon" /> </a-tooltip
              ></span>
            </template>
            <a-radio-group v-model:value="addType" @change="radioChange">
              <a-radio :value="1">模板导入</a-radio>
              <a-radio :value="2">手动录入</a-radio>
            </a-radio-group>
          </h-form-item>
          <!-- 人员明细 -->
          <h-form-item :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
            <template #label>
              <span
                >人员明细
                <a-tooltip>
                  <template #title>人员明细</template>
                  <question-circleOutlined class="icon" />
                </a-tooltip>
            </span>
            </template>
            <h-row style="margin-bottom: 20px">
              <h-col :span="18" class="flex">
                <a-button v-if="addType==2" type="primary" class="mr-20" @click="addPersonData()">
                  <template #icon><PlusOutlined /></template>
                  添加数据
                </a-button>
                <a-button v-if="addType==1" type="primary" class="mr-20" @click="downloadTemp()">
                  <template #icon><VerticalAlignBottomOutlined /></template>
                  下载模板
                </a-button>
                <h-form-item-rest v-if="addType==1">
                  <h-upload  v-model:file-list="fileList" name="file" :custom-request="uploadPeople" :before-upload="beforeUpload" :max-count="1" accept=".xls,.xlsx">
                    <div style="display: flex;flex-direction: column;">
                      <h-button type="primary" class="mr-20" :loading="ticketBtnLoading">
                        <VerticalAlignTopOutlined />
                        <span class="font-size-14">数据导入</span>
                        
                        <a-tooltip>
                          <template #title>
                            <div>
                              <p>1、数据导入后会全部替换</p>
                              <p>2、导入人员名单最大不能超过800条</p>
                            </div>
                          </template>
                          <InfoCircleOutlined class="mr-5" />
                        </a-tooltip>
                      </h-button>
                      <!-- <span class="mt-5" style="font-size: 12px; color: red;"><InfoCircleOutlined class="mr-5" />数据导入后会全部替换</span> -->
                    </div>
                    
                  </h-upload>
                </h-form-item-rest>
                <h-popconfirm
                  title="确定要清空数据吗?"
                  ok-text="确认"
                  cancel-text="取消"
                  @confirm="resetList"
                  :disabled="!rechargeForm?.personList?.length > 0"
                >
                  <a-button type="primary" :disabled="!rechargeForm?.personList?.length > 0">
                    <template #icon> <DeleteOutlined /></template>

                    清空数据</a-button
                  >
                </h-popconfirm>
              </h-col>
              <h-col :span="6" style="display: flex; align-items: center; flex-flow: row-reverse">
                <span style="margin-left: 5px; font-weight: 600">{{ toCheck ? '已确认' : '待确认' }}</span>

                <a-switch v-model:checked="toCheck" @change="changeSwitch" />
              </h-col>
            </h-row>
            

              <a-table
                class="ant-table-striped"
                :row-class-name="(_record, index) => (toCheck ? 'table-striped' : null)"
                :disabled="toCheck"
                :columns="addType == 2 ? columnsHandle : columns"
                :key="tableKey"
                :data-source="tableSourceData"
                ref="tableRef"
                size="small"
                bordered
                :pagination="pagination"
              > 
                <template #bodyCell="{ column, text, record, index }">
                  <template v-if="addType==2">
                    <template v-if="column.dataIndex === 'userName'">
                      <a-form-item  style="margin-top:24px" :name="['personList', index, 'userName']" :rules="[{ required: true, message: '请选择下发用户' }]">
                        <user-select
                          :disabled="toCheck"
                          :value="record.userName"
                          placeholder="请选择"
                          :params="userParams"
                          @change="(userInfo: IUserInfo) =>  userNameChange(userInfo, record)"
                          class="whole-line font-size-14"
                        />
                      </a-form-item>
                    </template>

                    <template v-if="column.dataIndex === 'userCode'">
                      <a-form-item  style="margin-top:24px" :name="['personList', index, 'userCode']" :rules="[{ required: true, message: '请选择下发用户' }]">
                        <h-input  placeholder="请选择员工" class="whole-line" disabled v-model:value="record.userCode"  />
                      </a-form-item>
                    </template>

                    
                    <template v-if="column.dataIndex === 'amount'">
                      <a-form-item  style="margin-top:24px" :name="['personList', index, 'amount']" :rules="[{ required: true, message: '请输入下发金额' }]">
                        <h-input-number @change="changeAmount" :disabled="toCheck" placeholder="下发金额" class="whole-line " :controls="false" v-model:value="record.amount" :precision="2" :min="0.01"  />
                      </a-form-item>
                    </template>

                    <template v-if="column.dataIndex === 'action'">
                      <a-popconfirm
                        title="确认要删除这条数据吗?"
                        ok-text="确认"
                        cancel-text="取消"
                        @confirm="deleteRecord(index)"
                        @cancel="cancelRecord"
                      > 
                        <h-button style="margin: 24px 0" type="link" >删除</h-button>
                      </a-popconfirm>
                    </template>
                  </template>
                </template>
              </a-table>
          </h-form-item>

          <!-- 账户余额 -->
          <h-form-item name="payType">
            <template #label>
              <span
                >原余额<a-tooltip>
                  <template #title>账户余额</template>
                  <question-circleOutlined class="icon" /> </a-tooltip
              ></span>
            </template>
            <div>{{ rechargeForm?.amount }} 元</div>
          </h-form-item>
          <!-- 下发金额 -->
          <h-form-item name="payType">
            <template #label>
              <span
                >本次下发金额<a-tooltip>
                  <template #title>导入数据总额</template>
                  <question-circleOutlined class="icon" /> </a-tooltip
              ></span>
            </template>
            <div>{{ rechargeForm?.amountSum ?? 0 }} 元</div>
          </h-form-item>

          <!-- 账户余额 -->
          <h-form-item name="payType">
            <template #label>
              <span
                >下发后余额<a-tooltip>
                  <template #title>提交本次下发后帐户余额</template>
                  <question-circleOutlined class="icon" /> </a-tooltip
              ></span>
            </template>
            <div>{{ rechargeForm?.surplusAmount }} 元</div>
          </h-form-item>

          
          <!-- 是否通知 -->
          <h-form-item name="payType">
            <template #label>
              <span
                >是否通知<a-tooltip>
                  <template #title>可选下发后是否通过ihaier通知</template>
                  <question-circleOutlined class="icon" /> </a-tooltip
              ></span>
            </template>
            <h-radio-group v-model:value="notifyFlag">
              <h-radio :value="true">是</h-radio>
              <h-radio :value="false">否</h-radio>
            </h-radio-group>
          </h-form-item>

          <!-- 激励留言 -->
          <h-form-item
            name="incentiveMessage"
            class="message-area"
            :rules="[{ required: notifyFlag ? true : false, message: '请填写激励信息' }]"
            v-if="notifyFlag"
          >
            <template #label>
              <span
                >激励留言<a-tooltip>
                  <template #title>该留言将会直接通过ihaier机器人发送至被激励员工,请谨慎填写</template>
                  <question-circleOutlined class="icon" /> </a-tooltip
              ></span>
            </template>
            <h-textarea
              
              :maxlength="200"
              show-count
              placeholder="该留言将会直接通过ihaier机器人发送至被激励员工,请谨慎填写"
              v-model:value="rechargeForm.incentiveMessage"
              :auto-size="{ minRows: 3, maxRows: 5 }"
              allow-clear
            />
            <!-- <span class="ml100" style="color: red">该留言将直接发送至被激励员工，请谨慎填写。</span> -->
          </h-form-item>

          <!-- 注意事项 -->
          <h-form-item name="checked">
            <h-checkbox v-model:checked="rechargeForm.checked">
              <span style="color: red">注意事项:数据提交后不可撤回及修改,提交前请先确认人员明细数据是否正确</span>
            </h-checkbox>
          </h-form-item>
          <!--  -->
          <h-form-item :wrapper-col="{ offset: 10 }">
            <h-button type="primary" @click="onSubmit">提交</h-button>
            <h-button style="margin-left: 50px" @click="router.go(-1)">取消</h-button>
          </h-form-item>
        </h-form>

        <h-modal :destroyOnClose="false" style="width: 650px" :maskClosable="false" v-model:open="open" title="短信验证" @ok="handleOk">
          <template #footer>
            <h-button key="back" @click="cancel">返回</h-button>
            <h-button key="submit" type="primary" :loading="loading" @click="handleOk">提交</h-button>
          </template>

          <h-row class="mb-10">订单提交后不支持修改及撤回操作,请确认信息后提交</h-row>
          <h-row class="mb-10">
            <h-col :span="12"> 人员数据:{{ rechargeForm?.personList?.length ?? 0 }}人 </h-col>
            <h-col :span="12"> 下发金额合计: {{ rechargeForm?.amountSum }}元 </h-col>
          </h-row>
          <h-row class="mb-10" style="word-break: break-all;">激励留言:{{ rechargeForm?.incentiveMessage }}</h-row>
          <h-row>
            <h-col :span="12"> 手机号:{{ loginUser.phone }} </h-col>
            <h-col :span="12">
              <a-input-search
                v-model:value="rechargeForm.code"
                placeholder="输入验证码"
                size="mini"
                @search="onSearch"
                
              >
              <template #enterButton>
                <a-button type="primary" >
                  <span v-if="!searchLoading">
                    <span v-if="codeCount > 0">重新获取</span>
                    <span v-else>获取验证码</span>
                  </span>
                  <span v-else>
                    <countdown :time="60 * 1000" format="ss" >
                      <template #="{ resolved }">
                        
                        <span class="countdown-item">{{ resolved.ss }}秒后可重新发送</span>
                      </template>
                    </countdown>
                  </span>
                </a-button>

              </template>
              </a-input-search>
            </h-col>
          </h-row>
        </h-modal>
        <h-modal
          :destroyOnClose="true"
          :maskClosable="false"
          v-model:open="errorOpen"
          title="错误信息"
          @ok="handleErrorOk"
        >
          <template #footer>
            <h-button key="submit" type="primary" :loading="loading" @click="handleErrorOk">确认</h-button>
          </template>

          <a-table
            :columns="columnsError"
            :key="tableKey"
            :data-source="tableSourceDataError"
            ref="tableRefError"
            size="small"
            bordered
          >
          </a-table>
        </h-modal>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url(./recharge.less);

.change-title {
  position: absolute;
  right: 0;
  top: -80px;
  width: 300px;

  .ant-col-6,
  .ant-col-18 {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #000000d9;
  }
}
.mt-5 {
  margin-top: 5px;
}

.ml-5 {
  margin-left: 5px;
}
.color-eee {
  color: #a8a7a7;
}

.com-box {
  background: #f5f5f5;
  padding: 20px;
  margin-top: 20px;

  .whole-line {
    height: 40px;
    font-weight: 600;
  }
  .info-box {
    display: flex;
    align-items: flex-start;
    padding: 10px 20px;
    border: 1px solid #ffdb5c;
    background: #fffbd8;
  }
  :deep(.ant-form-item-control-input-content) {
    display: flex;
    align-items: center;
  }
}
.title {
  height: 60px;
}
.download-btn {
  color: #408cff;
  cursor: pointer;
}
:deep(.city-chose-box) {
  width: 200px !important;
}
:deep(.mr-10) {
  margin-right: 10px;
}

.background-eee {
  padding: 10px;
  background: #f4f4f4;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.ant-table-striped :deep(.table-striped) td {
  background-color: #fafafa;
  color: #999;
}

.message-area :deep(.ant-form-item-explain-error) {
  margin-top: -20px;
}

:deep(.ant-form-item-explain-error) {
  text-align: left;
}

</style>
