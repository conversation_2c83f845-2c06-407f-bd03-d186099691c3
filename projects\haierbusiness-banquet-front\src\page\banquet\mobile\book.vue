<script lang="ts" setup>

import { onMounted, onUnmounted, ref, watch, reactive, computed, nextTick } from 'vue';
import type { Ref } from 'vue';
import { BSaveParams, MApplicationType, BPoliciesRes, BanquetApplicationTypeEnum, ICity, IUserInfo } from '@haierbusiness-front/common-libs';
import { BHomepagePicRes } from '@haierbusiness-front/common-libs';
import { banquetApi, cityApi, fileApi } from '@haierbusiness-front/apis';
import { tripApi, userApi, teamApi } from '@haierbusiness-front/apis';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute, getEnumOptions, guid } from '@haierbusiness-front/utils';
import UserSelectM from './userSelectM.vue';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import { showSuccessToast, showFailToast, showToast } from 'vant';
import { showImagePreview } from 'vant';
import { showDialog } from 'vant';

dayjs.extend(isSameOrAfter)


const store = applicationStore();

const { loginUser } = storeToRefs(store);

const router = getCurrentRouter();


const route = ref(getCurrentRoute());
// const id = route.value?.query?.id;
// const restaurantId = route.value?.query?.restaurantId;
// const type = route.value?.query?.type;

const { id,restaurantId,type, startDate, endDate, reason, amount,remark, sceneType } = route.value?.query



// 根据id查询详情
const getDetail = (id: string) => {
  if (!id) {
    return
  }
  banquetApi.getApplyDetail({ id }).then(res => {
    form.value = res

    form.value.innerPerson = []
    form.value.outerPerson = []
    // form.value?.persons?.forEach(item => {
    //   if (item.haierUser) {
    //     form.value.innerPerson = [...form.value.innerPerson, item]
    //   } else {
    //     form.value.outerPerson = [...form.value.outerPerson, item]
    //   }
    // })
    // form.value.fileList = [
    //   {
    //     file: form.value.fileName,
    //     url: form.value.fileUrl
    //   }
    // ]
    form.value.restaurantInfo = {}
    form.value.restaurantInfo.restaurantId = form.value.restaurantId
    form.value.restaurantInfo.restaurantName = form.value.restaurantName


    form.value.name = loginUser.value?.nickName
    form.value.code = loginUser.value?.username
    form.value.phone = loginUser.value?.phone //联系电话

    form.value.estimatedMealTimeEnd = form.value.estimatedMealTimeEnd && form.value.estimatedMealTimeEnd.length >= 18 ? form.value.estimatedMealTimeEnd.substring(0, 10) : form.value.estimatedMealTimeEnd
    form.value.estimatedMealTimeStart = form.value.estimatedMealTimeStart && form.value.estimatedMealTimeStart.length >= 18 ? form.value.estimatedMealTimeStart.substring(0, 10) : form.value.estimatedMealTimeStart

    // 如果是再来一单,去掉id、时间
    if (type) {
      form.value.id = ''
      form.value.estimatedMealTimeStart = ''
      form.value.estimatedMealTimeEnd = ''

    }
  })
}

watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);
const restaurantLoading = ref(false)

// 根据restaurantId查询详情
const getRestaurantDetail = (id: string) => {
  if (!id) {
    return
  }
  restaurantLoading.value = true
  banquetApi.getRestaurant({
    restaurantId: id
  }).then(res => {
    restaurantLoading.value = false
    if (!res) {
      showFailToast('未查到餐厅详细信息,请联系管理员!');
      return
    }
    form.value.restaurantInfo = {
      restaurantId: '',
      restaurantName: '',
      restaurantAddress: ''
    }
    banquetApi.getPolicies().then(policiesRes => {
      policies.value = policiesRes

      // 如果被餐厅管控,选择的餐厅不在管控餐厅中做出提示
      if (policies.value?.restrictedRestaurant && policies.value?.restrictedRestaurant?.length > 0) {
        const codeList = policies.value?.restrictedRestaurant.map(item => item.policyObjectCode)
        codeList.indexOf(res.restaurantId) < 0 ? showFailToast('选择的餐厅不在管控餐厅列表中，请重新选择!') : ''
      } else {
        form.value.restaurantInfo.restaurantId = res?.restaurantId
        form.value.restaurantInfo.restaurantName = res?.restaurantName
        form.value.restaurantInfo.restaurantAddress = res?.restaurantAddress
      }
    })

  }).catch(err => {
    restaurantLoading.value = false
  })
}

watch(
  () => restaurantId,
  (val: string) => {
    getRestaurantDetail(val);
  },
  {
    immediate: true,
  },
);

const cityListDialog = ref<boolean>(false)


const addIcon = new URL('@/assets/image/banquet/order/add.png', import.meta.url).href
const deleteIcon = new URL('@/assets/image/banquet/order/delete.png', import.meta.url).href


const form = ref<BSaveParams>({
  name: loginUser.value?.nickName, //经办人
  code: loginUser.value?.username,
  phone: loginUser.value?.phone, //联系电话
  innerPerson: [
    {
      userName: loginUser.value?.nickName,
      userCode: loginUser.value?.username,
      phone: loginUser.value?.phone,
      haierUser: true
    }
  ],
  sceneType: sceneType || 1,
  signerName: loginUser.value?.nickName,
  signerCode: loginUser.value?.username,
  outerPerson: [],
  restaurantInfo: {
  },

  estimatedMealTimeStart:startDate || '',
  estimatedMealTimeEnd:endDate || '',
  banquetReason:reason || '',
  budgetAmount:amount || '',
  remark: remark || ''
})



const btnLoading = ref<boolean>(false);
const worningDialog = ref<boolean>(false)

const confirmWorning = () => {
  form.value.checkedInfo = true
  worningDialog.value = false
}

const onSubmit = (type: string) => {
  if (!form.value.checkedInfo) {
    worningDialog.value = true
    return false
  }

  btnLoading.value = true
  form.value.orderStatus = type
  // type 10保存草稿 20提交申请 
  // 将内部外部就餐人整合起来
  // form.value.banquetPersonInfos = [...form.value.innerPerson, ...form.value.outerPerson]
  // form.value.banquetPersonInfos.forEach(item => {
  //   if (item.username) {
  //     item.haierUser = true
  //   }
  // });

  // 判断选择的城市是否在管控城市列表中
  if (form.value.mealLocationCityCode && !policies.value?.restrictedCity.find(item => item.policyObjectCode == form.value.mealLocationCityCode)) {
    showFailToast({
      message: '选择的城市不在管控城市列表中，无法提交申请!',
      iconSize: '0px'
    })
    btnLoading.value = false
    return
  }

  // 修改
  if (form.value.id) {

    let upadteForm = JSON.parse(JSON.stringify(form.value))
    upadteForm.estimatedMealTimeEnd = upadteForm.estimatedMealTimeEnd && upadteForm.estimatedMealTimeEnd.length < 18 ? `${upadteForm.estimatedMealTimeEnd} 23:59:59` : upadteForm.estimatedMealTimeEnd
    upadteForm.estimatedMealTimeStart = upadteForm.estimatedMealTimeStart && upadteForm.estimatedMealTimeStart.length < 18 ? `${upadteForm.estimatedMealTimeStart} 00:00:00` : upadteForm.estimatedMealTimeStart
    if (!upadteForm?.restaurantInfo?.restaurantId) {
      upadteForm.restaurantInfo = null
    }
    banquetApi.update(upadteForm).then((res: any) => {
      localStorage.removeItem('banquet_book_form')

      if (res) {
        window.location.replace(res)
        // router.push('/banquet/apply/list?status=')
      } else {
        router.push('/banquet/apply/list?status=')
      }
      btnLoading.value = false
    }).catch((err: any) => {
      btnLoading.value = false
    })
  } else {
    let createFrom = JSON.parse(JSON.stringify(form.value))
    createFrom.estimatedMealTimeEnd = createFrom.estimatedMealTimeEnd && createFrom.estimatedMealTimeEnd.length < 18 ? `${createFrom.estimatedMealTimeEnd} 23:59:59` : createFrom.estimatedMealTimeEnd
    createFrom.estimatedMealTimeStart = createFrom.estimatedMealTimeStart && createFrom.estimatedMealTimeStart.length < 18 ? `${createFrom.estimatedMealTimeStart} 00:00:00` : createFrom.estimatedMealTimeStart

    // 如果未选择餐厅 将对象整体设为null
    if (!createFrom?.restaurantInfo?.restaurantId) {
      createFrom.restaurantInfo = null
    }

    // 提交
    banquetApi.save(createFrom).then((res: any) => {
      btnLoading.value = false
      localStorage.removeItem('banquet_book_form')
      if (res) {
        window.location.replace(res)
      } else {
        router.push('/banquet/apply/list?status=')
      }
    }).catch((err: any) => {
      btnLoading.value = false
    })
  }

};

const showTips = () => {
  showDialog({
    title: '预计就餐日期',
    message: '就餐日期范围最长可选择10天,请按需填写;为避免买单操作超期,系统截止日期次日6点前执行前一日的对公支付操作;',
  });
}

const onFailed = (values) => {
  scrollToErrorField()
}

// 页面滚动到表单验证失败的地方
const scrollToErrorField = () => {
  const firstErrorField = document.querySelector('.van-field__error-message') // 使用类名选择第一个错误元素
  if (firstErrorField) {
    firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
}

const eatingPersons = computed(() => {

  return form.value.innerPerson?.length + form.value.outerPerson?.length
})

// 申请类型选择弹窗
const showTypePicker = ref<boolean>(false);
const columnsCl = computed(() => {
  return getEnumOptions(BanquetApplicationTypeEnum, true);
});

const onConfirmCl = ({ selectedOptions }: any) => {
  showTypePicker.value = false;
  form.value.sceneType = selectedOptions[0].value;
};

// 就餐日期选择
const minDate = ref((dayjs()).toDate());
const maxDate = ref(new Date(2025, 10, 1));
const choseTimeType = ref('')
const currentRq = ref<Array<string>>([]);

// 判断选择时间是否早于明天 
const startTimeValidator = (value) => {
  if (dayjs(value).isBefore(dayjs(), 'day')) {
    return false
  }
  return true
}

const endTimeValidator = (value) => {
  if (dayjs(value).isBefore(dayjs(form.value.estimatedMealTimeStart), 'day')) {
    return false
  }
  return true
}
// 申请单的时间需要加限制，开始和结束日期区间最多是10天
const openTimePicker = (type: string) => {
  choseTimeType.value = type;
  currentRq.value = [];
  minDate.value = (dayjs()).toDate();
  maxDate.value = new Date(2026, 0, 1);
  // 判断当前选择的日期是否有过期日期, 清空数据
  if (dayjs().isAfter(dayjs(form.value.estimatedMealTimeStart).add(1, 'day')) || dayjs().isAfter(dayjs(form.value.estimatedMealTimeEnd).add(1, 'day'))) {
    form.value.estimatedMealTimeStart = ''
    form.value.estimatedMealTimeEnd = ''
  }


  if (type == 'begin') {
    if (form.value.estimatedMealTimeStart) {
      if (dayjs().isBefore(dayjs(form.value.estimatedMealTimeStart))) {
        currentRq.value = form.value.estimatedMealTimeStart?.split('-');
      } else {
        currentRq.value = []
      }
    }
    if (form.value.estimatedMealTimeEnd) {
      maxDate.value = (dayjs(form.value.estimatedMealTimeEnd)).toDate();
      minDate.value = (dayjs(form.value.estimatedMealTimeEnd).subtract(9, 'day')).toDate();
      // 最早只能选今天
      if (dayjs().isAfter(dayjs(minDate.value))) {
        minDate.value = (dayjs()).toDate();
      }
    }

  } else {
    if (form.value.estimatedMealTimeEnd) {
      if (dayjs().isBefore(dayjs(form.value.estimatedMealTimeEnd))) {
        currentRq.value = form.value.estimatedMealTimeEnd?.split('-');
      } else {
        currentRq.value = []
      }
    }
    if (form.value.estimatedMealTimeStart) {
      minDate.value = (dayjs(form.value.estimatedMealTimeStart)).toDate();
      maxDate.value = (dayjs(form.value.estimatedMealTimeStart).add(9, 'day')).toDate();

    }
  }
  showRqPicker.value = true;

};

const openTimePicker2 = (type: string) => {
  choseTimeType.value = type;
  currentRq.value = [];
  if (form.value.estimatedMealTimeStart) {
    if (dayjs().isBefore(dayjs(form.value.estimatedMealTimeStart))) {
      const minDateArr = form.value.estimatedMealTimeStart.split('-');
      minDate.value = new Date(parseInt(minDateArr[0]), parseInt(minDateArr[1]) - 1, parseInt(minDateArr[2]));
    }
  }
  if (form.value.estimatedMealTimeEnd) {
    if (dayjs().isBefore(dayjs(form.value.estimatedMealTimeEnd))) {
      const maxDateArr = form.value.estimatedMealTimeEnd.split('-');
      maxDate.value = new Date(parseInt(maxDateArr[0]), parseInt(maxDateArr[1]) - 1, parseInt(maxDateArr[2]));
    }
  }

  if (type == 'begin') {
    minDate.value = (dayjs()).toDate();
    // 如果是过期时间不回显时间
    if (dayjs().isBefore(dayjs(form.value.estimatedMealTimeStart))) {
      currentRq.value = form.value.estimatedMealTimeStart?.split('-');
    } else {
      currentRq.value = []
    }
  } else {
    maxDate.value = new Date(2026, 0, 1);
    if (dayjs().isBefore(dayjs(form.value.estimatedMealTimeEnd))) {
      currentRq.value = form.value.estimatedMealTimeEnd?.split('-');
    } else {
      currentRq.value = []
    }
  }

  showRqPicker.value = true;
};

const showRqPicker = ref<boolean>(false);
const confirmRq = ({ selectedValues }: any) => {
  if (choseTimeType.value == 'begin') {
    form.value.estimatedMealTimeStart = selectedValues.join('-');
    // form.value.estimatedMealTimeStart = `${form.value.estimatedMealTimeStart} 00:00:00`
  } else {
    form.value.estimatedMealTimeEnd = selectedValues.join('-');
    // form.value.estimatedMealTimeEnd = `${form.value.estimatedMealTimeEnd} 00:00:00`
  }
  showRqPicker.value = false;
};

// 根据code获取城市id
const getCityIdBycode = (code?: string | number) => {
  if (!code) {
    return ''
  }
  let id = ''
  cityDict.value.children?.forEach(province => {
    province?.children?.forEach(city => {
      if (city?.providerMapList && city?.providerMapList.length > 0) {
        if (city?.providerMapList[0]?.districtId == code) {
          id = city.id
        }
      }
    });
  });
  return id
}

// 根据code 获取城市经纬度

const getCityLngBycode = (code?: string | number) => {
  let local = {
    lng: '',
    lat: ''
  }
  if (!code) {
    return local
  }

  cityDict.value.children?.forEach(province => {
    province?.children?.forEach(city => {
      if (city?.providerMapList && city?.providerMapList.length > 0) {
        if (city?.providerMapList[0]?.districtId == code) {
          // id = city.id
          local.lat = city.lat
          local.lng = city.lng

        }
      }
    });
  });
  return local
}

// 就餐城市选择
const chosedCity = ref();
const showCityPicker = ref<boolean>(false);
const showCityPop = (code?: string | number) => {
  // 根据 code 获取城市id
  chosedCity.value = getCityIdBycode(code)
  showCityPicker.value = true;
};
const finishCityChose = ({ selectedOptions }: any) => {
  // 判断选择的城市是否在管控城市列表中
  if (selectedOptions[selectedOptions.length - 1].providerMapList[0].districtId && !policies.value?.restrictedCity.find(item => item.policyObjectCode == selectedOptions[selectedOptions.length - 1].providerMapList[0].districtId)) {
    showFailToast({
      message: '选择的城市不在管控城市列表中，无法提交申请!',
      iconSize: '0px'
    })
    btnLoading.value = false
    return
  }

  form.value.mealLocationProvinceCode = selectedOptions[0].providerMapList[0].districtId;
  form.value.mealLocationProvince = selectedOptions[0].providerMapList[0].districtName;

  form.value.mealLocationCityCode = selectedOptions[selectedOptions.length - 1].providerMapList[0].districtId;
  form.value.mealLocationCity = selectedOptions[selectedOptions.length - 1].providerMapList[0].districtName;

  form.value.mealLocationCityAreaCode = selectedOptions[selectedOptions.length - 1].areaCode;

  form.value.lng = selectedOptions[selectedOptions.length - 1].lng
  form.value.lat = selectedOptions[selectedOptions.length - 1].lat

  showCityPicker.value = false;
};

// 选择业务申请人
// 出行人相关
const chosedPerson = (item: IUserInfo) => {
  form.value.signerCode = item.username;
  form.value.signerName = item.nickName;
  // 选择签单人跟当前登陆人不一致
  if (form.value.code != form.value.signerCode) {
    showDialog({
      message: '申请单预算生效后,该预算仅签单人可使用,请仔细核对签单人信息!',
    });
  }
};
// 跳转美团餐厅选择页
const goToMeituan = () => {
  if (!form.value.sceneType) {
    showFailToast('请先选择申请类型');
    return
  }
  // 判断是否限制的可选餐厅范围
  if (policies.value?.restrictedRestaurant) {
    showRestaurantPicker.value = true
    return
  }

  // 是否判断需要提前选择城市
  if (policies.value?.restrictedCity) {
    if (!form.value.mealLocationCityCode || !form.value.mealLocationCity) {
      showFailToast('请先选择就餐城市');
      return
    }
  }

  if (!form.value.lng || !form.value.lat) {
    let local = getCityLngBycode(form.value.mealLocationCityCode)
    form.value.lng = local.lng
    form.value.lat = local.lat
  }

  restaurantLoading.value = true

  // 判断当前所在城市是不是选择的城市,如果相同则置空经纬度
  AMap.plugin(['AMap.Geocoder', 'AMap.Geolocation'], function () {

    var geocoder = new AMap.Geocoder({
      // city 指定进行编码查询的城市，支持传入城市名、adcode 和 citycode
      city: '010'
    })

    // 1、获取当前经纬度坐标 需要https协议
    // 2、当前经纬度坐标逆解析出详细地址
    // 3、对比选择的城市与解析地址是否一致
    // 4、若一致不穿经纬度参数给美团,使用美团的默认定位

    if ("geolocation" in navigator) {
      navigator.geolocation.getCurrentPosition((position) => {
        const latitude = position.coords.latitude;
        const longitude = position.coords.longitude;
        var lnglat = [longitude, latitude]

        geocoder.getAddress(lnglat, function (status, result) {
          if (status === 'complete' && result.info === 'OK') {
            // result为对应的地理位置详细信息
            if (form.value?.mealLocationCityAreaCode == result.regeocode.addressComponent.citycode) {
              form.value.lng = position.coords.longitude;
              form.value.lat = position.coords.latitude
            }
            getMeituanUrl()

          }
        })

      }, (error) => {
        getMeituanUrl()

      });
    } else {
      console.log('您的浏览器不支持地理定位功能。');
      getMeituanUrl()

    }


  })




}

const getMeituanUrl = () => {
  const params = {
    type: form.value.sceneType == 1 ? 'STAFF_CY' : 'STAFF_WM',
    bizParam: {
      // 生成唯一uuid
      budgetKey: `mo-${guid()}`,
      restaurantType: '1',
      location: {
        longitude: form.value.lng,
        latitude: form.value.lat,
        geotype: 'gcj02', // gcj02
        // address: '山东省德州市陵城区'
        address: form.value.mealLocationProvince + form.value.mealLocationCity
      },
      lockCityType: form.value.lng ? 1 : 0
    },

  }
  banquetApi.clientInvokeLogin(params).then(res => {
    localStorage.setItem('banquet_book_form', JSON.stringify(form.value))
    window.location.href = res
    setTimeout(() => {
      restaurantLoading.value = false
    }, 2000);
  }).catch(err => {
    restaurantLoading.value = false
  })
}

// 餐厅
const showRestaurantPicker = ref<boolean>(false)


const confirmRestaurant = ({ selectedOptions }: any) => {
  showRestaurantPicker.value = false;
  form.value.restaurantInfo = {
    restaurantId: '',
    restaurantName: '',
  }
  form.value.restaurantInfo.restaurantId = selectedOptions[0].policyObjectCode;
  form.value.restaurantInfo.restaurantName = selectedOptions[0].policyObjectName;
  getRestaurantDetail(form.value.restaurantInfo.restaurantId)
};

// 就餐人信息
const showPersons = ref<boolean>(false);
// 选择内部就餐人
const chosedInnerPerson = (chosedVal: IUserInfo, item: IUserInfo, index: string | number) => {
  form.value?.innerPerson?.splice(index, 1, {
    userName: chosedVal.nickName,
    userCode: chosedVal.username,
    phone: chosedVal.phone,
    haierUser: true
  })
}
const addInnerPerson = () => {
  form.value.innerPerson.push({
    userName: null,
    userCode: null,
    phone: null,
    haierUser: true
  })
}

const delInnerPerson = (index) => {
  form.value?.innerPerson?.splice(index, 1)
}

// 添加外部就餐人
const addOutPerson = () => {
  form.value.outerPerson.push({
    userName: null,
  })
}

const delOutPerson = (index) => {
  form.value?.outerPerson?.splice(index, 1)
}

const closeChosePersonDialog = () => {

  form.value.innerPerson = form.value.innerPerson.filter(item => item.userName !== null && item.userName !== '' && item.userName !== undefined);
  form.value.outerPerson = form.value.outerPerson.filter(item => item.userName !== null && item.userName !== '' && item.userName !== undefined);

  if (form.value.innerPerson.length == 0 && form.value.outerPerson.length == 0) {
    showFailToast('至少选择一名就餐人!');
    return
  }

  showPersons.value = false;
};

const policies = ref<BPoliciesRes>()
// 获取当前用户受管控信息
const getPolicies = async () => {
  policies.value = await banquetApi.getPolicies()
}

const validateValue = (value) => {
  if (value == 0) {
    return false
  } else {
    return true
  }
};

const maxValue = ref(1000000)
const numberFixedDigit = (e) => {
  const numericValue = parseFloat(e.target.value);
  if (!isNaN(numericValue) && numericValue > maxValue.value) {
    if (numericValue.toString().indexOf(".") !== -1) {
      form.value.budgetAmount = e.target.value.replace(/(\d)\.(.*)/, (match, p1, p2) => `.${p2}`);
    } else {
      form.value.budgetAmount = e.target.value.substring(0, e.target.value.length - 1);
    }
  }

  e.target.value = e.target.value.replace(/[^\d.]/g, "");  //清除“数字”和“.”以外的字符
  e.target.value = e.target.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
  e.target.value = e.target.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
  e.target.value = e.target.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');//只能输入两个小数
  e.target.value = e.target.value.replace(/^\./g, ''); //首位不能输入“.”
  if (e.target.value.indexOf(".") < 0 && e.target.value != "") {//如果没有小数点，首位不能为0，如01、02...
    e.target.value = parseFloat(e.target.value);
  }
  nextTick(() => {
    form.value.budgetAmount = e.target.value
  })
}


// 文件上传
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

const beforeRead = (file) => {
  if (file.type !== 'image/jpeg' && file.type !== 'image/png') {
    showToast('请上传 jpg/png 格式图片');
    return false;
  }
  return true;
}
const afterRead = (options: any) => {

  const formData = new FormData();
  formData.append('file', options.file);
  fileApi
    .upload(formData)
    .then((it) => {
      form.value.fileUrl = baseUrl + it.path;
      form.value.fileName = options.file.name;
    })
    .catch(() => {
      delFile()
    })
    .finally(() => {
    });
};
const delFile = () => {
  form.value.fileUrl = '';
  form.value.fileName = '';
  form.value.fileList = [];
};

const downloadFile = (url: string) => {
  showImagePreview([
    url
  ]);
  // window.open(url);
};

const showCnxz = ref<boolean>(false)
const showYsxy = ref<boolean>(false)
// 获取承诺须知
const promiseVal = ref<BHomepagePicRes>()
const getPromise = () => {
  banquetApi.getPromise().then(res => {
    promiseVal.value = res
  })
}

// 获取隐私承诺
const privacyVal = ref<BHomepagePicRes>()
const getPrivacy = () => {

  banquetApi.getPrivacy().then(res => {
    privacyVal.value = res
    // privacyVal.value.content = JSON.parse(privacyVal.value?.content)

  })
}

// 获取美团城市列表
const cityDict = ref<Array<ICity>>()

const getCityList = async () => {
  const params = {
    id: 1,
    providerCode: 'MT',
    subdistrict: 2
  }
  getPolicies()

  cityDict.value = await cityApi.getCityTree(params)

  if (!policies.value?.restrictedCity || policies.value?.restrictedCity?.length == 0) {
    return
  }

  // 所有省份数组
  const priceArray = policies.value?.restrictedCity?.map(res => {
    return res.mealLocationProvinceCode
  })
  // 所有市区数组
  const cityArray = policies.value?.restrictedCity?.map(res => {
    return res.policyObjectCode
  })
  cityDict.value.children = cityDict.value.children?.filter(item => {
    return item?.providerMapList && item?.providerMapList.length ? priceArray?.includes(item?.providerMapList[0]?.districtId) : ''
  })
  // 根据管控信息 展示 选择城市
  setDisabledCity(cityDict.value.children, cityArray)

  // 获取当前位置并判断是否在管控政策中
  if (!id) {
    // 查看是否有城市管控
    if (cityArray && cityArray.length > 0) {
      // 首先解析出当前位置的城市id
      AMap.plugin(['AMap.Geocoder', 'AMap.Geolocation'], function () {
        var geocoder = new AMap.Geocoder({
          city: '010'
        })

        if ("geolocation" in navigator) {
          navigator.geolocation.getCurrentPosition((position) => {
            const latitude = position.coords.latitude;
            const longitude = position.coords.longitude;
            var lnglat = [longitude, latitude]

            console.log('当前位置经纬度-------', lnglat)

            geocoder.getAddress(lnglat, function (status, result) {
              if (status === 'complete' && result.info === 'OK') {
                // result为对应的地理位置详细信息
                const positionNowCode = result.regeocode.addressComponent.citycode
                const positionNowadCode = result.regeocode.addressComponent.adcode.slice(0, -2) + '00'

                console.log('当前位置结果-------', result)

                console.log('管控城市列表-------', cityArray)

                console.log('positionNowCode-------', positionNowCode)

                console.log('positionNowadCode-------', positionNowadCode)

                if (cityArray?.includes(positionNowadCode)) {
                  // 当前位置在管控城市中
                  // 获取当前城市信息
                  const cityInfo = policies.value?.restrictedCity?.find(item => item.policyObjectCode == positionNowadCode)
                  form.value.mealLocationCityCode = cityInfo?.policyObjectCode
                  form.value.mealLocationCity = cityInfo?.policyObjectName
                  form.value.mealLocationProvince = cityInfo?.mealLocationProvince
                  form.value.mealLocationProvinceCode = cityInfo?.mealLocationProvinceCode

                  form.value.mealLocationCityAreaCode = positionNowCode

                }


              }
            })

          }, (error) => {

          });
        } else {
          console.log('您的浏览器不支持地理定位功能。');

        }
      })
    }


  }
}


const setDisabledCity = (list, cityList) => {

  list?.forEach(item => {
    let temp = []
    item?.children.forEach(item2 => {
      if (cityList?.includes(item2?.providerMapList[0]?.districtId)) {
        temp.push(item2)
      }
    })
    item.children = temp
  })

}

onMounted(async () => {
  // 获取缓存数据
  if (localStorage.getItem('banquet_book_form')) {
    form.value = JSON.parse(localStorage.getItem('banquet_book_form'))
    console.log('form.value------------', form.value)
  }

  getPrivacy()
  getPromise()
  // 获取省市 及用户管控信息
  getCityList()


});


const goBack = () => {
  localStorage.removeItem('banquet_book_form')
  router.back(-1);
};

const copyIcon = new URL('@/assets/image/banquet/mine/copy.png', import.meta.url).href
// 复制餐厅信息
const copyInfo = async () => {
  if (!form.value?.restaurantInfo?.restaurantName || !form.value?.restaurantInfo?.restaurantId) {
    showFailToast('还没选择餐厅!');
    return
  }
  // 复制结果
  let copyResult = true
  const text = `餐厅名称: ${form.value?.restaurantInfo?.restaurantName} ,餐厅id: ${form.value?.restaurantInfo?.restaurantId}`
  if (!!window.navigator.clipboard) {
    // 利用clipboard将文本写入剪贴板（这是一个异步promise）
    await window.navigator.clipboard.writeText(text).then((res) => {
      showSuccessToast('复制成功!')
    }).catch((err) => {
      console.log('复制失败--采取第二种复制方案', err);
      // clipboard方式复制失败 则采用document.execCommand()方式进行尝试
      copyResult = copyContent2(text)
    })
  } else {
    // 不支持clipboard方式 则采用document.execCommand()方式
    copyResult = copyContent2(text)
  }
  // 返回复制操作的最终结果
  return copyResult;

}

const copyContent2 = (text) => {
  // 复制结果
  let copyResult = true
  // 创建一个input元素
  let inputDom = document.createElement('textarea');
  // 设置为只读 防止移动端手机上弹出软键盘  
  inputDom.setAttribute('readonly', 'readonly');
  // 给input元素赋值
  inputDom.value = text;
  // 将创建的input添加到body
  document.body.appendChild(inputDom);
  // 选中input元素的内容
  inputDom.select();
  // 执行浏览器复制命令
  // 复制命令会将当前选中的内容复制到剪切板中（这里就是创建的input标签中的内容）
  // Input要在正常的编辑状态下原生复制方法才会生效
  const result = document.execCommand('copy')
  // 判断是否复制成功
  if (result) {
    showSuccessToast('复制成功!')
  } else {
    showFailToast('复制失败')
    copyResult = false
  }
  // 复制操作后再将构造的标签 移除
  document.body.removeChild(inputDom);
  // 返回复制操作的最终结果
  return copyResult;
}

</script>

<template>
  <div>
    <!-- <van-nav-bar :fixed="true" title="新增申请单" left-arrow>
      <template #left>
        <van-icon name="arrow-left" color="#000" @click="goBack" />
      </template>
</van-nav-bar> -->
    <div class="reservation-content">
      <van-form @submit="onSubmit('20')" @failed="onFailed">
        <van-cell-group inset class="">
          <van-field autocomplete="off" required input-align="right" error-message-align="right" label="经办人">
            <template #input>
              <div>{{ `${form.name}(${form.code})` }}</div>
            </template>
          </van-field>

          <van-field autocomplete="off" input-align="right" error-message-align="right" v-model.trim="form.phone"
            :rules="[{ required: true, message: '请输入手机号' }, { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式错误！' }]"
            required type="tel" label="联系方式" placeholder="请填写手机号" />
          <van-field autocomplete="off" required readonly input-align="right" error-message-align="right"
           label="申请类型" placeholder="请选择申请类型"
            >
            <template #input>
              <van-radio-group class="flex" v-model="form.sceneType">
                <van-radio :name="1" class="my-radio">
                  <template #icon="{ checked }">
                    <van-button class="btn-com btn" size="small" round
                      :type="checked ? 'primary' : 'default'">宴请</van-button>
                  </template>
                </van-radio>
                <van-radio :name="2" class="my-radio ml-5">
                  <template #icon="{ checked }">
                    <van-button class="btn-com btn " size="small" round
                      :type="checked ? 'primary' : 'default'">外卖</van-button>
                  </template>
                </van-radio>
              </van-radio-group>
            </template>
          </van-field>

          <van-field label-width="130px" autocomplete="off" required readonly input-align="right"
            error-message-align="right"
            :rules="[{ required: true, message: '请选择起始日期' }, { validator: startTimeValidator, message: '起始日期不能早于今天!' }]"
            placeholder="请选择起始日期" @click="openTimePicker('begin')" v-model="form.estimatedMealTimeStart">

            <template #label>
              <div>预计就餐日期 <van-icon name="warning-o" color="#ee0a24" @click.stop="showTips" />
              </div>
            </template>

            <template #input>
              <div v-if="form?.estimatedMealTimeStart">{{ form?.estimatedMealTimeStart }} <van-icon
                  v-if="form?.estimatedMealTimeStart" name="cross" color="#ee0a24"
                  @click.stop="form.estimatedMealTimeStart = ''" />
              </div>
              <div v-else style="color:#c9cacd">请选择起始日期</div>

            </template>
          </van-field>


          <van-field autocomplete="off" required readonly input-align="right" error-message-align="right" label=""
            :rules="[{ required: true, message: '请选择截止日期' }, { validator: endTimeValidator, message: '截止日期不能早于起始日期!' }]"
            placeholder="请选择截止日期" @click="openTimePicker('end')" v-model="form.estimatedMealTimeEnd">

            <template #input>
              <div v-if="form?.estimatedMealTimeEnd">{{ form?.estimatedMealTimeEnd }} <van-icon
                  v-if="form?.estimatedMealTimeEnd" name="cross" color="#ee0a24"
                  @click.stop="form.estimatedMealTimeEnd = ''" />
              </div>
              <div v-else style="color:#c9cacd">请选择起始日期</div>

            </template>
          </van-field>

          <!--  -->
          <van-field v-if="policies?.restrictedCity && policies?.restrictedCity?.length > 0 && form.sceneType == 1"
            label="就餐城市" @click.stop="showCityPop(form?.mealLocationCityCode)" error-message-align="right" readonly
            required :rules="[{ required: true, message: '请选择就餐城市' }]" is-link name="mealLocationCityCode"
            placeholder="请选择就餐城市" input-align="right" v-model="form.mealLocationCity">
            <template #label>
              <div class="flex align-items-center">就餐城市 <van-icon name="more-o" style="margin-left: 3px"
                  @click.stop="cityListDialog = true" />
              </div>
            </template>
          </van-field>

          <van-field v-if="(policies?.advanceChooseRestaurant || restaurantId) && form.sceneType == 1"
            @click.stop="goToMeituan" error-message-align="right" readonly :required="policies?.advanceChooseRestaurant"
            :rules="[{ required: policies?.advanceChooseRestaurant, message: '请选择餐厅' }]" name="restaurantName"
            placeholder="请选择餐厅" input-align="right" v-model="form.restaurantInfo.restaurantName">

            <template #label>
              <div class="flex align-items-center">餐厅信息 <van-icon style="margin-left: 3px" :name="copyIcon" size="14"
                  @click.stop="copyInfo" />
              </div>
            </template>

          </van-field>

          <van-field label="餐厅地址" placeholder="请选择餐厅" @click.stop="goToMeituan" readonly
            v-if="(policies?.advanceChooseRestaurant || restaurantId) && form.sceneType == 1" autocomplete="off"
            input-align="right" error-message-align="right" v-model="form.restaurantInfo.restaurantAddress" />


          <van-field autocomplete="off" readonly required input-align="right" error-message-align="right" label="签单人信息">
            <template #input>
              <div>{{ `${form.signerName}(${form.signerCode})` }}</div>
            </template>
          </van-field>
          <!-- <user-select-m palceholder="请选择签单人" label="签单人信息" :value="`${form.signerName}(${form.signerCode})`"
            @chose="chosedPerson"></user-select-m> -->

          <van-field autocomplete="off" rows="3" type="textarea" maxlength="300" show-word-limit
            v-model.trim="form.banquetReason" label-align="top" label="申请事由" placeholder="请填写申请事由" />

          <van-field autocomplete="off" required input-align="right" placeholder="请输入金额"
            :rules="[{ required: true, message: '请输入金额' }, { validator: validateValue, message: '金额不能为0' }]"
            error-message-align="right" v-model.trim="form.budgetAmount" type="number" label="申请金额(元)"
            @input="numberFixedDigit" />

          <!-- <van-field label="预算选择" label-align="left" input-align="right" @click="showMainPersonList = true" readonly
            is-link placeholder="请选择" v-model="form.contactUserName">
          </van-field> -->


        </van-cell-group>

        <!-- <van-cell-group inset class="mt-10">
          <van-field autocomplete="off" style="font-weight: 600;" required input-align="right" 
            type="text" label="就餐人信息" readonly  @click="showPersons = true"/>
         
          <van-field label="就餐人数" label-align="left" input-align="right" readonly is-link @click="showPersons = true"
            placeholder="请选择就餐人" v-model.trim="eatingPersons" />

        </van-cell-group> -->

        <van-cell-group inset class="mt-10 mb-20">


          <van-field name="travelerFileUrl" label-width="40%" label-align="left" input-align="right">
            <template #label>
              <span>
                <span>附件信息</span><br />
                <span class="font-size-8 " style="color: red;">仅支持png/jpg格式文件</span>
              </span>
            </template>
            <template #input>
              <van-uploader :before-read="beforeRead" :after-read="afterRead" :preview-image="false"
                v-model="form.fileList" :max-count="1" accept=".png, .jpg">
                <!-- <van-button size="small" icon="plus" type="primary">上传文件</van-button> -->
                <div class="color-eee font-size-10">
                  选择附件
                  <van-icon class="ml-10" name="arrow" />
                </div>

              </van-uploader>
              <div class="width100 flex align-items-center justify-content-between"
                @click.stop="downloadFile(form.fileUrl)" v-if="form.fileUrl">
                <!-- <van-text-ellipsis :content="form.fileName" /> -->
                <div>{{ form.fileName }}</div>
                <van-icon @click.stop="delFile" class="ml-10" name="cross" style="color: red" />
              </div>
            </template>

          </van-field>



          <van-field autocomplete="off" rows="3" type="textarea" maxlength="300" show-word-limit
            v-model.trim="form.remark" label-align="top" label="备注信息" placeholder="请填写备注信息" />

        </van-cell-group>



        <van-sticky position="bottom">
          <van-field autocomplete="off" readonly required input-align="left" error-message-align="left">
            <template #input>
              <van-checkbox v-model="form.checkedInfo">
                <div style="font-size: 12px;">
                  我已经阅读并同意 <span style="color: #1482ff;" @click.stop="showCnxz = true">承诺须知</span><span> 和
                  </span><span style="color: #1482ff;" @click.stop="showYsxy = true">隐私协议</span>
                </div>
              </van-checkbox>
            </template>
          </van-field>

          <div class="bottom-order-btn">
            <van-button :loading="btnLoading" class="btn btn-background mr-20" @click="onSubmit('10')">保存草稿</van-button>
            <van-button :loading="btnLoading" native-type="submit" class="btn" type="primary">提交申请</van-button>
          </div>
        </van-sticky>


      </van-form>

    </div>

    <!-- 申请类型选择 -->
    <van-popup v-model:show="showTypePicker" round position="bottom">
      <van-picker title="选择申请类型" :columns="columnsCl" :columns-field-names="{ text: 'label', value: 'value' }"
        @cancel="showTypePicker = false" @confirm="onConfirmCl" />
    </van-popup>

    <!-- 就餐日期选择 -->
    <van-popup v-model:show="showRqPicker" round position="bottom">
      <van-date-picker v-model="currentRq" title="选择日期" :min-date="minDate" :max-date="maxDate" @confirm="confirmRq"
        @cancel="showRqPicker = false" />
    </van-popup>

    <!-- 城市选择 -->
    <van-popup v-model:show="showCityPicker" position="bottom">
      <van-cascader v-model="chosedCity" :show-header="false" :options="cityDict?.children"
        :field-names="{ text: 'name', value: 'id', children: 'children' }" @finish="finishCityChose" />
    </van-popup>

    <!-- 就餐人弹窗 -->
    <van-popup v-model:show="showPersons" position="bottom">
      <div class="out-pop" style="height: 100vh">
        <van-sticky>
          <div style="background: #fff; padding-bottom: 10px">
            <van-nav-bar title="就餐人信息" left-arrow @click-left="closeChosePersonDialog">
              <template #left>
                <van-icon name="arrow-left" color="#000" size="24" />
              </template>
              <template #right>
                <div class="color-main"></div>
              </template>
            </van-nav-bar>
          </div>
        </van-sticky>

        <van-cell-group inset class="mt-10 cell-group-shadow ">
          <van-cell title="内部就餐人" style="font-weight: 600;">
          </van-cell>

          <van-cell v-for="item, index in form.innerPerson" :key="index">
            <template #value>
              <div class="flex align-items-center">
                <user-select-m style="flex: 1;" class="my-person-select" palceholder="请选择"
                  :value="item?.userName ? `${item?.userName}(${item?.userCode})` : ''"
                  @chose="(chosedVal) => { return chosedInnerPerson(chosedVal, item, index) }"></user-select-m>
                <van-icon @click.stop="delInnerPerson(index)" :name="deleteIcon" size="24" />
              </div>

            </template>
          </van-cell>

          <van-cell @click="addInnerPerson">
            <template #title>
              <div class="flex align-items-center">
                <van-icon :name="addIcon" size="24" />
                <span class="ml-10">添加就餐人</span>
              </div>
            </template>
          </van-cell>

        </van-cell-group>

        <van-cell-group inset class="mt-10 cell-group-shadow ">
          <van-cell title="外部就餐人" style="font-weight: 600;">
          </van-cell>

          <van-cell v-for="item, index in form.outerPerson" :key="index">
            <template #title>
              <van-field autocomplete="off" class="my-person-select" style="padding-left: 0;" v-model="item.userName"
                placeholder="请输入" />
            </template>
            <template #value>
              <van-icon @click.stop="delOutPerson(index)" :name="deleteIcon" size="24" />
            </template>
          </van-cell>


          <van-cell @click.stop="addOutPerson">
            <template #title>
              <div class="flex align-items-center">
                <van-icon :name="addIcon" size="24" />
                <span class="ml-10">添加就餐人</span>
              </div>
            </template>
          </van-cell>

        </van-cell-group>

        <van-sticky position="bottom">
          <div class="bottom-order-btn">
            <van-button class="btn" style="width: 75% !important;" round type="primary"
              @click="closeChosePersonDialog">保存</van-button>
          </div>
        </van-sticky>

      </div>
    </van-popup>

    <!-- 全屏loading -->
    <van-overlay z-index="99999" :show="restaurantLoading">
      <div class="flex align-items-center justify-content-center" style="height: 100%" @click.stop>
        <van-loading type="spinner" />
      </div>
    </van-overlay>

    <!-- 就餐餐厅选择 -->
    <van-popup v-model:show="showRestaurantPicker" round position="bottom">
      <van-picker title="选择就餐餐厅"
        :columns-field-names="{ text: 'policyObjectName', value: 'policyObjectCode', children: 'children' }"
        :columns="policies?.restrictedRestaurant" @confirm="confirmRestaurant" @cancel="showRestaurantPicker = false" />
    </van-popup>

    <!-- 承诺须知 -->
    <van-action-sheet :closeable="false" v-model:show="showCnxz" title="承诺须知">
      <div class="action-main flex flex-column align-items-center" style="overflow: hidden;">
        <div style="overflow: auto;     max-height: 300px;" class=" action-content mb-20 width100"
          v-html="promiseVal?.content"></div>
        <van-button style="width: 200px;" @click="showCnxz = false" round type="primary">我已知晓</van-button>
      </div>
    </van-action-sheet>

    <!-- 隐私协议 -->
    <van-action-sheet :closeable="false" v-model:show="showYsxy" title="隐私协议">
      <div class="action-main flex flex-column align-items-center">
        <!-- <iframe :src="privacyVal?.content[0].filePath" frameborder="0" style="min-height: 500px"></iframe> -->
        <div style="overflow: auto;     max-height: 300px;" class=" action-content mb-20 width100"
          v-html="privacyVal?.content"></div>
        <van-button style="width: 200px;" @click="showYsxy = false" round type="primary">我已知晓</van-button>
      </div>
    </van-action-sheet>

    <!-- 被管控城市弹窗 -->
    <van-dialog v-model:show="cityListDialog" title="管控城市">
      <van-cell-group inset style="max-height: 260px; overflow-y: scroll;">
        <van-cell :title="item.mealLocationProvince" :value="item.policyObjectName"
          v-for="item, index in policies?.restrictedCity" :key="index" />
      </van-cell-group>


    </van-dialog>

    <!-- 承诺须知、隐私协议 -->
    <van-dialog v-model:show="worningDialog" title="承诺须知及隐私协议" confirm-button-text="同意并确认" @confirm="confirmWorning">
      <div style="font-size: 14px; padding: 20px">
        请先阅读并同意 <span style="color: #1482ff;" @click.stop="showCnxz = true">承诺须知</span><span> 和
        </span><span style="color: #1482ff;" @click.stop="showYsxy = true">隐私协议</span>
      </div>
    </van-dialog>




  </div>
</template>


<style lang='less' scoped>
@import url(./common.less);

.my-person-select {
  :deep(.van-field) {
    padding-left: 0;
  }

  :deep(.van-field__control--right) {
    justify-content: flex-start;
  }

  :deep(.van-cell__right-icon) {
    display: none;
  }
}

.reservation-content {
  padding-top: 20px;
  background: #f8f8f8;
  min-height: 100vh;
}

.submit-dialog-content {
  padding: 20px;
  color: red;
  display: flex;
}

:deep(.van-field__label--required:before) {
  position: absolute;
  left: 6px;
}

.action-main {
  padding: 10px;

  :deep(img) {
    max-width: 100% !important;
    height: auto !important;
  }
}

.out-pop {
  .main {
    flex: 1;
  }

  .img-name {
    width: 40px;
    height: 40px;

    color: #fff;
    font-size: 14px;
    border-radius: 40px;
  }

  .blue {
    background-color: #0073e5;
  }

  .yellow {
    background-color: #faad14;
  }

  .add-person {
    margin: 16px;
    margin-bottom: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
    font-size: 14px;
  }

  .user-name {
    text-align: left;
    font-size: 12px;
  }

  .phone {
    text-align: left;
    color: #8c8c8c;
  }
}
</style>