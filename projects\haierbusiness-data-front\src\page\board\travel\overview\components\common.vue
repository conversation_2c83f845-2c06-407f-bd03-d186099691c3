<template>
    <h-col v-for="(item, i) in props.data" :span="(item == 'Accumulative'  ||item == 'CommonData')? 24 : 6" :key="i">

        <template v-if="item == 'Accumulative' || item =='CommonData'">
            <component :is="components.get(item)" :date-type="active" :height="25"></component>
        </template>
        <template v-else-if="item == 'AirTicket1'">
            <div class="col-title"> {{ getName(item) }}
            </div>
            <div class="col-chart">
                <component :is="components.get('AirTicket')" :date-type="active" :height="25" gngj="1"></component>
            </div>
        </template>
        <template v-else-if="item == 'AirTicket0'">
            <div class="col-title"> {{ getName(item) }}
            </div>
            <div class="col-chart">
                <component :is="components.get('AirTicket')" :date-type="active" :height="25" gngj="0"></component>
            </div>
        </template>
        <template v-else>
            <div class="col-title"> {{ getName(item) }}
            </div>
            <div class="col-chart">
                <component :is="components.get(item)" :date-type="active" :height="25"></component>

            </div>
        </template>

    </h-col>
</template>
<script setup lang="ts">
import {
    Badge as hBadge,
    Progress as hProgress,
    Button as hButton,
    Col as hCol,
} from 'ant-design-vue';
import { ref, onMounted, markRaw, defineAsyncComponent } from "vue";
const components = markRaw(new Map<string, any>);
//组件Accumulative
components.set(
    'Accumulative',
    defineAsyncComponent(() => import('./accumulative.vue')),
);
components.set(
    'CommonData',
    defineAsyncComponent(() => import('./commonData.vue')),
);
components.set(
    'AirTicket',
    defineAsyncComponent(() => import('../../airTicket/components/businessTrend.vue')),
);

components.set(
    'TrainBusinessTrend',
    defineAsyncComponent(() => import('../../trainTicket/components/businessTrend.vue')),
);
components.set(
    'TaxiBusinessTrend',
    defineAsyncComponent(() => import('../../taxi/components/businessTrend.vue')),
);

components.set(
    'HotelBusinessTrend',
    defineAsyncComponent(() => import('../../hotel/components/businessTrend.vue')),
);
components.set(
    'GroundServices',
    defineAsyncComponent(() => import('./groundServices.vue')),
);
components.set(
    'TravelApplicationStatus',
    defineAsyncComponent(() => import('./travelApplicationStatus.vue')),
);
components.set(
    'ExpenseType',
    defineAsyncComponent(() => import('./expenseType.vue')),
);

const tabs = ["年", "月", "日"];
const active = ref(1);

const props = defineProps({
    data: Array<string>,
    layout: {
        type: String,
        default: "colums",
    },
    type: {
        type: String,
        default: "青岛会议",
    },
})

// 获取name
const getName = (status: number | string) => {
    const resultMap: any = {
        'AirTicket1': "国内机票",
        'AirTicket0': "国际机票",
        'TrainBusinessTrend': "火车票",
        'TaxiBusinessTrend': "用车服务",
        'HotelBusinessTrend': "差旅酒店",
        'GroundServices': "地面服务",
        'TravelApplicationStatus': "领域消费分布",
        'ExpenseType': "费用类型占比",
        default: "",
    };
    return resultMap[status] || resultMap.default;
};

</script>
<style scoped lang="less">
@import url(../../../main.less);

.mt-10 {
    margin-top: 10px;
}
</style>