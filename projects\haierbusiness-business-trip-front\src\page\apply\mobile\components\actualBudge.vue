<template>
  <div>
    <div class="title flex justify-content-between align-items-center">
      <div class="flex align-items-center justify-content-center">
        <span class="shu"></span>
        <div class="text">实际行程确认</div>
      </div>
    </div>

    <!-- 行程计划 -->
    <van-cell-group inset class="mb-10">
      <van-field required label-align="top" readonly>
        <template #label>
          <div class="my_field_label flex justify-content-between align-items-center">
            <div class="left">
              <span class="mr-5">实际行程</span>
              <van-icon name="question-o" @click.stop="showMessage('按照出差任务选择始发、目的地以及中转地，务必包含全部途径地及停留时间。事前做好差旅行为计划，提前规划行程（国内机票建议提前5天预订出票），在满足出行需求的前提下，确保成本最优；')" color="#cccccc" />
            </div>
            <div class="color-main font-size-12" v-if="props.creatTripParma.reimburseNum < 2" @click.stop="showAddCity">
              <van-icon name="location-o" />
              添加行程
            </div>
          </div>
        </template>

        <template #input>
          <div class="city-list mt-20 flex flex-column">
            <!-- 已经添加的城市列表 -->
            <div v-for="(item, index) in cityList" :key="index"
              class="city-item mb-30 flex justify-content-between align-items-center">
              <div class="left flex align-items-center">
                <van-icon class="mr-10" name="location" color="#0073e5" />
                <div class="item-city" @click.stop="showCityPop('list', index)">
                  <span>{{ item.city }}</span>
                  <van-icon class="ml-5" name="arrow" />
                </div>
              </div>
              <div class="right">
                <div class="item-time" @click.stop="openTimePicker('list', index)" v-if="index != 0">
                  <span>{{ formatDate(cityList[index - 1].date) }}</span>
                  <van-icon class="ml-5 mr-5" name="minus" />
                  <span>{{ formatDate(item.date) }}</span>
                </div>
                <div class="item-time" @click.stop="openTimePicker('list', index)" v-else>
                  <span>{{ formatDate(item.date) }}</span>
                </div>
              </div>
              <div class="shu" v-if="index + 1 != cityList.length"></div>
            </div>

            <!-- 新增第一个起始点城市 -->
            <div v-if="showFirstAddCity" class="city-item mb-30 flex justify-content-between align-items-center">
              <div class="left flex align-items-center">
                <van-icon class="mr-10" name="location" color="#919191" />
                <div class="item-city add-color" @click.stop="showCityPop('begin', 0)">
                  <span class="add-color">{{ newBeginCity.name || '选择城市' }}</span>
                  <van-icon class="add-color ml-5" name="arrow" />
                </div>
              </div>
              <div class="right add-color">
                <div class="item-time add-color" @click.stop="openTimePicker('begin', 0)">
                  <span>{{ formatDate(beginTime) || '离开日期' }}</span>
                </div>
              </div>
            </div>

            <!-- 新增后续城市 -->
            <div v-if="showNextAddCity" class="city-item mb-30 flex justify-content-between align-items-center">
              <div class="left flex align-items-center">
                <van-icon class="mr-10" name="location" color="#919191" />
                <div class="item-city add-color" @click.stop="showCityPop('next', 0)">
                  <span class="add-color">{{ newEndCity.name || '选择城市' }}</span>
                  <van-icon class="add-color ml-5" name="arrow" />
                </div>
              </div>
              <div class="right add-color">
                <div class="item-time add-color" @click.stop="openTimePicker('end', 0)">
                  <span>{{ formatDate(beginTime) || formatDate(props?.creatTripParma.endDate) || '出发日期' }}</span>
                  <van-icon class="ml-5 mr-5" name="minus" />
                  <span>{{ formatDate(endTime) || '离开日期' }}</span>
                </div>
              </div>
              <div class="dashed"></div>
            </div>
          </div>
        </template>
      </van-field>

      <!-- 时间选择 -->
      <van-popup v-model:show="showTimePicker" position="bottom" >
        <van-date-picker title="请选择日期" v-model="currentDate" :min-date="minDate" :max-date="maxDate" @confirm="confirmTime"
          @cancel="showTimePicker = false" />
      </van-popup>

      <!-- 城市选择 -->
      <van-popup v-model:show="showCityPicker" round position="bottom">
        <van-cascader v-model="chosedCity" title="请选择城市" :options="cityDict"
          :field-names="{ text: 'name', value: 'id', children: 'children' }" @close="showCityPicker = false"
          @finish="finishCityChose" />
        <!-- <van-picker title="请选择城市" :columns="cityDict" :columns-field-names = "{ text: 'name', value: 'citycode', children: 'districts' }"/> -->
      </van-popup>
    </van-cell-group>

    <!-- 具体行程 -->
    <div class="title-mini flex justify-content-between align-items-center">
      <div class="flex align-items-center justify-content-center">
        <div class="text">具体行程</div>
      </div>
    </div>
    <div v-for="(trip, index) in props.creatTripParma?.tripList" :key="index">

      <van-cell-group inset class="mb-10" style="position: relative;  overflow: visible;">
        <div class="padding-16" style="position: relative;">
          <div class="transparent-bg" style="width: 100%;height: 100%; position: absolute; top: 0; left: 0"></div>
          <div class="my_field_label flex justify-content-between align-items-center mb-20 ">
            <div class="left">
              <span class="mr-5" style="color: #898c8c">行程{{ index + 1 }}</span>
            </div>
            <div class="flex align-items-center color-main font-size-12">
              <!-- 报销次数小于2才能删除修改! -->
              <van-icon v-if="props.creatTripParma.reimburseNum < 2" name="close" @click.stop="delTrip(index)" color="red" class="font-size-14" />
            </div>
          </div>

          <div class="flex flex-column" style="width: 100%">
            <div class="flex align-items-center mb-20 ">
              <div class="item-city">
                <span class="strong">{{ trip.beginCityName }}</span>
                <van-icon class="ml-5" name="arrow" />
              </div>
              <van-icon class="ml-10 mr-10" name="minus" />
              <div class="item-city">
                <span class="strong">{{ trip.endCityName }}</span>
                <van-icon class="ml-5" name="arrow" />
              </div>
            </div>

            <div class="flex justify-content-between mb-10">
              <div class="flex align-items-center range-font">
                <div class="item-city">
                  <span class="">{{ formatDate(trip.beginDate) }}</span>
                </div>
                <van-icon class="ml-5 mr-5" name="minus" />
                <div class="item-city">
                  <span class="">{{ formatDate(trip.endDate) }}</span>
                  <van-icon class="ml-5" name="arrow" />
                </div>
              </div>
            </div>
          </div>
        </div>
        
      </van-cell-group>
     
    </div>


  </div>
</template>

<script lang="ts" setup>
import { createVNode, onMounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref } from 'vue';
import hbc2Budget from './hbc2Budget.vue';
import icon12306 from '../../components/icon12306.vue';
import { showLoadingToast } from 'vant';
import dayjs from 'dayjs';

import {
  IUserListRequest,
  IUserInfo,
  ICity,
  CityItem,
  ITripInfo,
  ITraveler,
  ICreatTrip,
  ITripList,
  ITripDetailMap,
  MemberBudgetParams,
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { tripApi, SupplierApi, reasonApi, cityApi } from '@haierbusiness-front/apis';
import { userApi } from '@haierbusiness-front/apis';
import { cloneDeep, debounce, values } from 'lodash';
import { showSuccessToast, showFailToast, showToast } from 'vant';

import { showConfirmDialog } from 'vant';
import userSelectM from './userSelectM.vue'

interface Props {
  creatTripParma?: any; // 人员
  isDetail?: boolean;
  isChange?: boolean;
  chosedNow?: string;
  cityList?: any;
}
const props = defineProps<Props>();

// 获取主出差人信息
const mainPerson = () => {
  return props.creatTripParma?.travelerList?.filter((item:any) => item.travelUserType=='0' && item.mainFlag == '1')[0];
};

/*
  新增城市相关
*/
const showNextAddCity = ref<boolean>(false);
const showFirstAddCity = ref<boolean>(false);

const newBeginCity = ref<CityItem>({
  name: '',
  citycode: '',
  syId: '',
});

const newEndCity = ref<CityItem>({
  name: '',
  citycode: '',
  syId: '',
});

const activeNames = ref(['1']);

const showMessage = (message: string) => {
  showToast(message);
}

// 打开弹窗的类型 begin next list
const cityChoseType = ref<string>('');
// 点击选择的城市下标
const cityChoseIndex = ref<number>(0);

const chosedCity = ref();

// 城市选择相关
const showCityPicker = ref<boolean>(false);
const showCityPop = (type: string, index: number) => {
  // 如果已经报销过,不能修改
  if (props.creatTripParma.reimburseNum > 1) {
    showToast('已报销过的行程不能修改');
    return;
  }
  chosedCity.value = undefined;
  if (type == 'begin') {
    chosedCity.value = newBeginCity.value.citycode;
  } else if (type == 'next') {
    chosedCity.value = newEndCity.value.citycode;
  } else {
    chosedCity.value = cityList.value[index].cityCode;
    cityChoseIndex.value = index;
  }
  cityChoseType.value = type;
  showCityPicker.value = true;
};
const finishCityChose = ({ selectedOptions }) => {
  console.log(999, selectedOptions, chosedCity.value);

  if (cityChoseType.value == 'begin') {
    newBeginCity.value.name = selectedOptions[selectedOptions.length - 1].name;
    newBeginCity.value.citycode = selectedOptions[selectedOptions.length - 1].id;
    newBeginCity.value.syId = selectedOptions[selectedOptions.length - 1].syId;
  } else if (cityChoseType.value == 'next') {
    newEndCity.value.name = selectedOptions[selectedOptions.length - 1].name;
    newEndCity.value.citycode = selectedOptions[selectedOptions.length - 1].id;
    newEndCity.value.syId = selectedOptions[selectedOptions.length - 1].syId;
  } else {
    cityList.value[cityChoseIndex.value].city = selectedOptions[selectedOptions.length - 1].name;
    cityList.value[cityChoseIndex.value].cityCode = selectedOptions[selectedOptions.length - 1].id;
    cityList.value[cityChoseIndex.value].syId = selectedOptions[selectedOptions.length - 1].syId;
    // 根据下标判断 修改完城市后需要修改的值
    if (cityChoseIndex.value == 0) {
      props.creatTripParma['beginCityName'] = selectedOptions[selectedOptions.length - 1].name;
      props.creatTripParma['beginCityCode'] = selectedOptions[selectedOptions.length - 1].id;
      props.creatTripParma['beginCityCodeSy'] = selectedOptions[selectedOptions.length - 1].syId;

      if (props.creatTripParma.tripList?.length > 0) {
        props.creatTripParma.tripList[0].beginCityName = selectedOptions[selectedOptions.length - 1].name;
        props.creatTripParma.tripList[0].beginCityCode = selectedOptions[selectedOptions.length - 1].id;
        props.creatTripParma.tripList[0].beginCityCodeSy = selectedOptions[selectedOptions.length - 1].syId;
      }
    } else if (cityChoseIndex.value + 1 == cityList.value.length) {
      props.creatTripParma['endCityName'] = selectedOptions[selectedOptions.length - 1].name;
      props.creatTripParma['endCityCode'] = selectedOptions[selectedOptions.length - 1].id;
      props.creatTripParma['endCityCodeSy'] = selectedOptions[selectedOptions.length - 1].syId;

      props.creatTripParma.tripList[cityChoseIndex.value - 1]['endCityName'] =
        selectedOptions[selectedOptions.length - 1].name;
      props.creatTripParma.tripList[cityChoseIndex.value - 1]['endCityCodeSy'] =
        selectedOptions[selectedOptions.length - 1].syId;
      props.creatTripParma.tripList[cityChoseIndex.value - 1]['endCityCode'] =
        selectedOptions[selectedOptions.length - 1].id;
    } else {
      props.creatTripParma.tripList[cityChoseIndex.value]['beginCityName'] =
        selectedOptions[selectedOptions.length - 1].name;
      props.creatTripParma.tripList[cityChoseIndex.value]['beginCityCode'] =
        selectedOptions[selectedOptions.length - 1].id;
      props.creatTripParma.tripList[cityChoseIndex.value]['beginCityCodeSy'] =
        selectedOptions[selectedOptions.length - 1].syId;

      props.creatTripParma.tripList[cityChoseIndex.value - 1]['endCityName'] =
        selectedOptions[selectedOptions.length - 1].name;
      props.creatTripParma.tripList[cityChoseIndex.value - 1]['endCityCode'] =
        selectedOptions[selectedOptions.length - 1].id;
      props.creatTripParma.tripList[cityChoseIndex.value - 1]['endCityCodeSy'] =
        selectedOptions[selectedOptions.length - 1].syId;
    }
  }

  // 判断是否选择完毕并添加到数组
  if (cityChoseType.value != 'end') {
    addCity();
  }

  showCityPicker.value = false;
};

const cityList = ref<Array<ICity>>([]);
const cityDict = ref([]);

defineExpose({
  cityList,
});
const showAddCity = () => {

  if (cityList.value?.length > 0) {
    if (showNextAddCity.value) {
      showFailToast('请先完善当前城市信息!');
      return;
    }
    showNextAddCity.value = true;
  } else {
    if (showFirstAddCity.value) {
      showFailToast('请先完善当前城市信息!');
      return;
    }
    showFirstAddCity.value = true;
    showNextAddCity.value = true;
  }
};

watch(
  props,
  (newVal, oldVal) => {
    cityList.value = newVal.cityList;
  },
  {
    immediate: true,
    deep: true,

  },
)

/*
  时间选择相关
*/
const showTimePicker = ref<boolean>(false);
const beginTime = ref('');
const endTime = ref('');

const minDate = ref(new Date(2024, 0, 1));
const maxDate = ref(new Date(2026, 0, 1));

const currentDate = ref<Array<string>>([]);

const currentDateIndex = ref<number>(0);
const choseTimeType = ref('');
const activeTimeItem = ref({});

const openTimePicker = (type: string, index: number) => {
  // 如果已经报销过,不能修改
  if (props.creatTripParma.reimburseNum > 1) {
    showToast('已报销过的行程不能修改');
    return;
  }
  currentDateIndex.value = index;
  currentDate.value = [];
  choseTimeType.value = type;
  if (type == 'begin') {
    minDate.value = new Date(2024, 0, 1);

    maxDate.value = new Date();
    currentDate.value = beginTime.value?.split('-');
  } else if (type == 'end') {

    // 如果选择的时间大于当前时间不允许操作
    if(new Date(cityList.value[cityList.value.length-1].date).getTime() > new Date().getTime()) {
        showFailToast('上个行程时间有误请先重新调整!')
        return false
    }

    if (cityList.value.length > 0) {
      const dateArr = cityList.value[cityList.value.length - 1].date.split('-');
      minDate.value = new Date(parseInt(dateArr[0]), parseInt(dateArr[1]) - 1, parseInt(dateArr[2]));
    } else if (beginTime.value) {
      const dateArr = beginTime.value.split('-');
      minDate.value = new Date(parseInt(dateArr[0]), parseInt(dateArr[1]) - 1, parseInt(dateArr[2]));
    } else {
      minDate.value = new Date(2024, 0, 1);
    }

    // maxDate.value = new Date(2026, 0, 1);
    maxDate.value = new Date();

    currentDate.value = endTime.value?.split('-');
  } else {
    
    if (index == 0) {
      minDate.value = new Date(2024, 0, 1)

      // 判断选择的时间是否大于当前时间
      if(new Date(cityList.value[index].date).getTime() > new Date().getTime()) {
        maxDate.value = new Date(new Date().getFullYear(), new Date().getMonth() , new Date().getDate())
      } else {
        if (cityList.value[1]?.date) {
          const dateArr = cityList.value[1].date.split('-');
          maxDate.value = new Date(parseInt(dateArr[0]), parseInt(dateArr[1]) - 1, parseInt(dateArr[2]));
        } else {
          // maxDate.value = new Date(2026, 0, 1);
          maxDate.value = new Date();
        }
      }
    } else if (index + 1 == cityList.value.length) {
      // 如果选择的时间大于当前时间不允许操作
      if(new Date(cityList.value[index-1].date).getTime() > new Date().getTime()) {
        showFailToast('上个行程时间有误请先重新调整!')
        return false
      }
      const dateArr = cityList.value[cityList.value.length - 2].date.split('-');
      minDate.value = new Date(parseInt(dateArr[0]), parseInt(dateArr[1]) - 1, parseInt(dateArr[2]));
      // maxDate.value = new Date(2026, 0, 1);
      maxDate.value = new Date();
    } else {
      if(new Date(cityList.value[index-1].date).getTime() > new Date().getTime()) {
        showFailToast('上个行程时间有误请先重新调整!')
        return false
      }
      const dateArr = cityList.value[index - 1].date.split('-');
      minDate.value = new Date(parseInt(dateArr[0]), parseInt(dateArr[1]) - 1, parseInt(dateArr[2]));

      const dateArr2:any = cityList.value[index + 1].date.split('-');
      maxDate.value = new Date();
    }
    
    currentDate.value = cityList.value[index].date?.split('-');
  }

  showTimePicker.value = true;
};

let cityItem: ITripList = {
  beginCityName: '',
  beginCityCodeSy: '',
  endCityCodeSy: '',
  endCityName: '',
  beginDate: '',
  endDate: '',
  tripDetailMapList: [],
  showMore: true,
};

const confirmTime = ({ selectedValues }) => {
  if (choseTimeType.value == 'begin') {
    beginTime.value = selectedValues.join('-');
  } else if (choseTimeType.value == 'end') {
    endTime.value = selectedValues.join('-');
  } else {
    cityList.value[currentDateIndex.value].date = selectedValues.join('-');

    // 根据下标判断 修改完时间后需要修改的值
    if (currentDateIndex.value == 0) {
      props.creatTripParma['beginDate'] = selectedValues.join('-');
      if (props.creatTripParma.tripList?.length > 0) {
        props.creatTripParma.tripList[0]['beginDate'] = selectedValues.join('-');
      }
    } else if (currentDateIndex.value + 1 == cityList.value.length) {
      props.creatTripParma['endDate'] = selectedValues.join('-');
      props.creatTripParma.tripList[currentDateIndex.value - 1]['endDate'] = selectedValues.join('-');
    } else {
      props.creatTripParma.tripList[currentDateIndex.value]['beginDate'] = selectedValues.join('-');
      props.creatTripParma.tripList[currentDateIndex.value - 1]['endDate'] = selectedValues.join('-');
    }
  }
  // 判断是否选择完毕并添加到数组
  if (choseTimeType.value != 'list') {
    addCity();
  }
  showTimePicker.value = false;
};


const addCity = () => {
  let leg: number = cityList.value.length;
  if (leg > 0) {
    if (!newEndCity.value.citycode || !endTime.value) {
      return;
    }

    if (newEndCity.value.citycode == cityList.value[leg - 1].cityCode) {
      showToast('出发城市与目的城市一致!');
    }
    cityItem = {
      endCityCodeSy: newEndCity.value.syId,
      beginCityCodeSy: cityList.value[leg - 1].syId,
      beginCityCode: cityList.value[leg - 1].cityCode,
      endCityCode: newEndCity.value.citycode,
      beginCityName: cityList.value[leg - 1].city,
      endCityName: newEndCity.value.name,
      beginDate: cityList.value[leg - 1].date,
      endDate: endTime.value,
      tripDetailMapList: [],
      showMore: true,
      detailMap: true,
    };
    props?.creatTripParma?.tripList.push(cityItem);
    cityList.value.push({
      cityCode: newEndCity.value.citycode,
      city: newEndCity.value.name,
      syId: newEndCity.value.syId,
      date: endTime.value,
      active: false,
    });
    newEndCity.value = {
      name: '',
      citycode: '',
    };
    endTime.value = '';
    showNextAddCity.value = false;
  } else {
    // 一次添加两个
    if (!newBeginCity.value.citycode || !beginTime.value) {
      return;
    }
    if (!newEndCity.value.citycode || !endTime.value) {
      return;
    }
    if (newEndCity.value.citycode == newBeginCity.value.citycode) {
      showToast('出发城市与目的城市一致!');
    }
    cityList.value.push({
      cityCode: newBeginCity.value.citycode,
      city: newBeginCity.value.name,
      date: beginTime.value,
      syId: newBeginCity.value.syId,
      active: false,
    });

    cityItem = {
      endCityCodeSy: newEndCity.value.syId,
      beginCityCodeSy: newBeginCity.value.syId,
      beginCityCode: newBeginCity.value.citycode,
      endCityCode: newEndCity.value.citycode,
      beginCityName: newBeginCity.value.name,
      endCityName: newEndCity.value.name,
      beginDate: beginTime.value,
      endDate: endTime.value,
      tripDetailMapList: [],
      showMore: true,
      detailMap: true,
    };
    if (!props.creatTripParma.tripList) {
      props.creatTripParma.tripList = [];
      props.creatTripParma.tripList.push(cityItem);
    } else {
      props.creatTripParma.tripList.push(cityItem);
    }
    cityList.value.push({
      cityCode: newEndCity.value.citycode,
      city: newEndCity.value.name,
      date: endTime.value,
      syId: newEndCity.value.syId,
      active: false,
    });

    // startCityCodeStrs.value = newBeginCity.value;
    newBeginCity.value = {
      name: '',
      citycode: '',
      syId: '',
    };
    beginTime.value = '';
    newEndCity.value = {
      name: '',
      citycode: '',
      syId: '',
    };
    endTime.value = '';
  }

  showNextAddCity.value = false;
  showFirstAddCity.value = false;

  // 总行程的出发地、目的地、出发时间、到达时间、syid
  props.creatTripParma.beginDate = cityList.value[0].date;
  props.creatTripParma.beginCityCodeSy = cityList.value[0].syId;
  props.creatTripParma.beginCityCode = cityList.value[0].cityCode;
  props.creatTripParma.beginCityName = cityList.value[0].city;

  props.creatTripParma.endCityCode = cityList.value[cityList.value.length - 1].cityCode;
  props.creatTripParma.endDate = cityList.value[cityList.value.length - 1].date;
  props.creatTripParma.endCityCodeSy = cityList.value[cityList.value.length - 1].syId;
  props.creatTripParma.endCityName = cityList.value[cityList.value.length - 1].city;
};

// 时间显示格式转换  2024-04-23 -> 04/23
const formatDate = (date: string)=> {
  if (!date) {
    return ''
  }
  const arr = date?.split('-');
  return arr?.length > 2 ? `${arr[1]}/${arr[2]}` : '';
};


// 删除行程
const delTrip = (index: number) => {
  showConfirmDialog({
    title: '确认要删除此行程吗',
    message: '删除行程无法恢复,您确定吗?',
  })
    .then(() => {
      // on confirm
      // 删除中间城市 清空后边城市的行程
      if (index + 1 !== props.creatTripParma.tripList?.length) {
        props.creatTripParma.tripList[index + 1].tripDetailMapList = [];
      }

      cityList.value.splice(index + 1, 1);
      props?.creatTripParma?.tripList.splice(index, 1);

      // 删除中间城市导致 起始地-终点错误
      cityList.value.forEach((i: ICity, ii: number) => {
        if (ii > 0) {
          props.creatTripParma.tripList[ii - 1].beginCityName = cityList.value[ii - 1].city;
          props.creatTripParma.tripList[ii - 1].endCityName = cityList.value[ii].city;
          props.creatTripParma.tripList[ii - 1].beginCityCode = cityList.value[ii - 1].cityCode;
          props.creatTripParma.tripList[ii - 1].endCityCode = cityList.value[ii].cityCode;
          props.creatTripParma.tripList[ii - 1].beginCityCodeSy = cityList.value[ii - 1].syId;
          props.creatTripParma.tripList[ii - 1].endCityCodeSy = cityList.value[ii].syId;
          if (ii > 1) {
            props.creatTripParma.tripList[ii - 1].beginDate = cityList.value[ii - 1].date;
          } else {
            props.creatTripParma.tripList[ii - 1].beginDate = cityList.value[ii - 1].date;
          }
        }
      });

      if (cityList.value.length > 1) {
        props.creatTripParma['beginDate'] = cityList.value[0].date;
        props.creatTripParma['endDate'] = cityList.value[cityList.value.length - 1]?.date;
      } else {
        props.creatTripParma['beginDate'] = cityList.value[0].date;
        props.creatTripParma['endDate'] = cityList.value[cityList.value.length - 1].date;
      }
    })
    .catch(() => {
      // on cancel
    });
};






onMounted(async () => {
  // 获取省市
  const res:any = await tripApi.district({});
  cityDict.value = res.children;
});




</script>

<style lang="less" scoped>
@import url(./mobile.less);
.show-more-box {
  width: 22px;
  height: 22px;
  background: #fff;
  position: absolute;
  bottom: -10px;
  left: calc(50% - 11px);
  border-radius: 10px;
  padding: 4px;
}
.show-more-box-open {
  background-image: url('@/assets/image/trip/open.png');
  background-size: 100% 100%;
}
.show-more-box-close {
  background-image: url('@/assets/image/trip/retract.png');
  background-size: 100% 100%;
}
</style>