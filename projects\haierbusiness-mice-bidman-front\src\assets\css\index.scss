/* 容器设置为Flex布局 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.vc {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.vl {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.hc {
  display: flex;
  align-items: center;
}
/* 子项目横向排列 */
.row {
  flex-direction: row;
}

/* 子项目纵向排列 */
.column {
  flex-direction: column;
}

/* 子项目水平居中 */
.jcenter {
  justify-content: center;
}

/* 子项目垂直居中 */
.acenter {
  align-items: center;
}

/* 子项目两端对齐 */
.space-between {
  justify-content: space-between;
}

/* 子项目起始对齐 */
.start {
  justify-content: flex-start;
}

/* 子项目末尾对齐 */
.end {
  justify-content: flex-end;
}

/* 子项目均匀分布 */
.space-around {
  justify-content: space-around;
}

/* 子项目填充容器 */
.fill {
  flex: 1;
}

/* 子项目不换行 */
.nowrap {
  flex-wrap: nowrap;
}

/* 子项目换行 */
.wrap {
  flex-wrap: wrap;
}

/* 子项目对齐方式：顶部对齐 */
.align-start {
  align-items: flex-start;
}

/* 子项目对齐方式：底部对齐 */
.align-end {
  align-items: flex-end;
}

/* 子项目对齐方式：基线对齐 */
.align-baseline {
  align-items: baseline;
}

/* 子项目对齐方式：拉伸对齐 */
.align-stretch {
  align-items: stretch;
}

/* 子项目对齐方式：内容对齐 */
.align-content-start {
  align-content: flex-start;
}

/* 子项目对齐方式：内容末尾对齐 */
.align-content-end {
  align-content: flex-end;
}

/* 子项目对齐方式：内容居中对齐 */
.align-content-center {
  align-content: center;
}

/* 子项目对齐方式：内容空间分布 */
.align-content-space-between {
  align-content: space-between;
}

/* 子项目对齐方式：内容空间环绕 */
.align-content-space-around {
  align-content: space-around;
}

/* 子项目对齐自身方式：自动对齐 */
.self-auto {
  align-self: auto;
}

/* 子项目对齐自身方式：起始对齐 */
.self-start {
  align-self: flex-start;
}

/* 子项目对齐自身方式：末尾对齐 */
.self-end {
  align-self: flex-end;
}

/* 子项目对齐自身方式：居中对齐 */
.self-center {
  align-self: center;
}

/* 子项目对齐自身方式：基线对齐 */
.self-baseline {
  align-self: baseline;
}

/* 子项目对齐自身方式：拉伸对齐 */
.self-stretch {
  align-self: stretch;
}
@for $i from 1 through 100 {
  // Margin classes
  .m#{$i} {
    margin: #{$i}px;
  }
  .mt#{$i} {
    margin-top: #{$i}px;
  }
  .mr#{$i} {
    margin-right: #{$i}px;
  }
  .mb#{$i} {
    margin-bottom: #{$i}px;
  }
  .ml#{$i} {
    margin-left: #{$i}px;
  }

  // Padding classes
  .p#{$i} {
    padding: #{$i}px;
  }
  .pt#{$i} {
    padding-top: #{$i}px;
  }
  .pr#{$i} {
    padding-right: #{$i}px;
  }
  .pb#{$i} {
    padding-bottom: #{$i}px;
  }
  .pl#{$i} {
    padding-left: #{$i}px;
  }
}
