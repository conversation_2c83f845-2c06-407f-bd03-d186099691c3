import { loadEnv } from 'vite'

import vue from '@vitejs/plugin-vue'
import path from 'path';

const CWD = process.cwd();

export default ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);

  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
    },
    plugins: [vue()],
    build:{
      target:['es2015']
    },
    server: {
      port: 5177,
      proxy: {
        // "/hb/common/api": {
        //   // target: "http://localhost:8080/hb",
        //   //  target: "https://businessmanagement-test.haier.net/hbweb/payman/hb",
        //   target: "http://localhost:9206",
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(/^\/hb\/common\/api/, ""),
        // },
        "/hb": {
          // target: "http://localhost:8080/hb",
          target: "https://businessmanagement-test.haier.net/hbweb/payman/hb",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb/, ""),
        },
      },
    }
  }
}
