<script setup lang="ts">
import { SearchOutlined } from '@ant-design/icons-vue';
import {
  Button as hButton,
  Checkbox as hCheckbox,
  CheckboxGroup as hCheckboxGroup,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  Input as hInput,
  InputNumber as hInputNumber,
  Modal as hModal,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Textarea as hTextarea,
  message,
} from 'ant-design-vue';
import { computed, defineEmits, defineProps, ref, watch } from 'vue';
// import { WaterDeliveryModalForm } from '@haierbusiness-front/common-libs';
const props = defineProps<{
  visible: boolean;
  record?: any;
}>();
const emit = defineEmits(['onCancel', 'onOk']);

/**
 * @表单相关
 * */

interface WaterDeliveryModalForm {
  applicationDate: string;
  type: string;
  deliveryArea: string;
  receiver: string;
  remark: string;
  customerName: string;
  contactPhone: string;
  deliveryAddress: string;
  payType: string[];
}

const formData = ref<WaterDeliveryModalForm>({
  applicationDate: '', // 申请日期
  type: '', // 类型
  deliveryArea: '', // 送水区域
  receiver: '', // 签收人
  remark: '', // 备注
  customerName: '张三,李四', // 客户名称
  contactPhone: '', // 联系电话
  deliveryAddress: '', // 送水地址
  payType: [], // 支付方式
});
const confirmLoading = ref(false);

/**
 * @表格相关
 * */
const tableData = ref<any>([
  {
    key: '1',
    index: 1,
    category: '2加仑',
    unitPrice: 25,
    quantity: 0,
    amount: 0,
    remark: '2加仑',
  },
  {
    key: '2',
    index: 2,
    category: '5加仑',
    unitPrice: 15,
    quantity: 0,
    amount: 0,
    remark: '5加仑',
  },
  {
    key: '3',
    index: 3,
    category: '330ml瓶装水',
    unitPrice: 36,
    quantity: 0,
    amount: 0,
    remark: '330ml瓶装水',
  },
]);

const tableProps = ref<any>({
  dataSource: tableData.value,

  pagination: computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: 100,
    current: 1,
    pageSize: 10,
    style: { justifyContent: 'center' },
  })),
  columns: [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      sorter: (a: any, b: any) => a.index - b.index,
      width: '10%',
    },
    {
      title: '申请类别',
      dataIndex: 'category',
      key: 'category',
      sorter: (a: any, b: any) => a.category.localeCompare(b.category),
      width: '15%',
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      sorter: (a: any, b: any) => a.unitPrice - b.unitPrice,
      width: '15%',
    },
    {
      title: '申请数量',
      dataIndex: 'quantity',
      key: 'quantity',
      sorter: (a: any, b: any) => a.quantity - b.quantity,
      width: '15%',
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      sorter: (a: any, b: any) => a.amount - b.amount,
      width: '15%',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      sorter: (a: any, b: any) => a.remark.localeCompare(b.remark),
      width: '15%',
    },
  ],
});

/**
 * @弹窗相关
 * */
// 用于绑定 v-model:visible
const localVisible = ref(false);

const handleOk = () => {
  confirmLoading.value = true;
  console.log(formData.value, '表单值');
  emit('onOk');
  confirmLoading.value = false;
};

watch(
  () => props.visible,
  (newVal) => {
    localVisible.value = newVal;
    if (!newVal) {
      // 监听弹窗关闭事件，重置表单数据
      formData.value = {
        applicationDate: '', // 申请日期
        type: '', // 类型
        deliveryArea: '', // 送水区域
        receiver: '', // 签收人
        remark: '', // 备注
        customerName: '', // 客户名称
        contactPhone: '', // 联系电话
        deliveryAddress: '', // 送水地址
        payType: [], // 支付方式
      };
    }
  },
  { immediate: true },
);

/**
 * @客户名称弹窗相关
 * */
const customerModalVisible = ref(false);

// 选择项目
const selectedCustomers = ref<any>(['3']);

const customerTableProps = computed(() => ({
  rowKey: 'customerName',
  dataSource: [
    {
      key: '1',
      customerName: '张三',
      contactPhone: '13800138000',
      deliveryArea: '黄岛区',
    },
    {
      key: '2',
      customerName: '李四',
      contactPhone: '13800138001',
      deliveryArea: '青岛工业园',
    },
    {
      key: '3',
      customerName: '赵六',
      contactPhone: '13800138000',
      deliveryArea: '黄岛区',
    },
    {
      key: '4',
      customerName: '王五',
      contactPhone: '13800138000',
      deliveryArea: '黄岛区',
    },
  ],
  columns: [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: '10%',
      render: (text: any, record: any, index: any) => {
        return index + 1;
      },
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      key: 'customerName',
      width: '25%',
      sorter: (a: any, b: any) => a.customerName.localeCompare(b.customerName),
    },
    {
      title: '联系电话',
      dataIndex: 'contactPhone',
      key: 'contactPhone',
      width: '25%',
      sorter: (a: any, b: any) => a.contactPhone.localeCompare(b.contactPhone),
    },
    {
      title: '送水地址',
      dataIndex: 'deliveryArea',
      key: 'deliveryArea',
      width: '25%',
      sorter: (a: any, b: any) => a.deliveryArea.localeCompare(b.deliveryArea),
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: '15%',
    },
  ],
  // 分页
  pagination: {
    showSizeChanger: true,
    showQuickJumper: true,
    total: 100,
    current: 1,
    pageSize: 10,
    style: { justifyContent: 'center' },
  },
  // 支持多选
  rowSelection: {
    selectedRowKeys: selectedCustomers.value.map((item: any) => item.customerName),
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      console.log(selectedRowKeys, selectedRows);
      selectedCustomers.value = selectedRows;
      console.log(selectedCustomers.value);
    },
  },
}));
const openCustomerModal = () => {
  customerModalVisible.value = true;
};
const closeCustomerModal = () => {
  customerModalVisible.value = false;
};

const handleCustomerOk = () => {
  formData.value.customerName = selectedCustomers.value.map((item: any) => item.customerName).join(',');
  console.log(formData.value.customerName);

  message.success('确认成功');
  customerModalVisible.value = false;
};

const handleCancel = () => {
  emit('onCancel');
};

const lableConfig = {
  xs: 3,
  sm: 3,
  md: 3,
  lg: 3,
  xl: 2,
};
const colConfig = {
  xs: 21,
  sm: 21,
  md: 9,
  lg: 9,
  xl: 6,
};
</script>

<template>
  <div inert class="water-info-modal">
    <h-modal
      :title="'新增'"
      v-model:open="localVisible"
      @cancel="handleCancel"
      @ok="handleOk"
      width="80%"
      :confirmLoading="confirmLoading"
    >
      <template #footer>
        <div style="display: flex; justify-content: center; gap: 10px">
          <h-button class="submit-btn" size="large" type="primary" @click="handleOk">提交</h-button>
          <h-button class="cancel-btn" size="large" @click="handleCancel">取消</h-button>
        </div>
      </template>
      <h-form :model="formData" layout="horizontal">
        <h-row :gutter="[0, 10]">
          <h-col v-bind="lableConfig" style="text-align: right">
            <label for="applicationDate">申请日期：</label>
          </h-col>
          <h-col v-bind="colConfig">
            <h-date-picker v-model:value="formData.applicationDate" value-format="YYYY-MM-DD" style="width: 100%" />
          </h-col>
          <h-col v-bind="lableConfig" style="text-align: right">
            <label for="type">类型：</label>
          </h-col>
          <h-col v-bind="colConfig">
            <h-select
              id="type"
              v-model:value="formData.type"
              placeholder="请选择"
              autocomplete="off"
              style="width: 100%"
            >
              <h-select-option value="10">VIP</h-select-option>
              <h-select-option value="20">月结</h-select-option>
              <h-select-option value="30">现场</h-select-option>
            </h-select>
          </h-col>
          <h-col v-bind="lableConfig" style="text-align: right">
            <label for="contactPhone">客户名称：</label>
          </h-col>
          <h-col v-bind="colConfig" style="display: flex; align-items: center">
            <h-input
              disabled
              style="width: 90%"
              id="contactPhone"
              v-model:value="formData.customerName"
              placeholder="请选择"
              autocomplete="off"
              :title="formData.customerName"
            />
            <span style="flex: 1; text-align: center; cursor: pointer" @click="openCustomerModal">
              <SearchOutlined style="color: #4593ff" />
            </span>
          </h-col>
          <h-col v-bind="lableConfig" style="text-align: right">
            <label for="contactPhone">联系电话：</label>
          </h-col>
          <h-col v-bind="colConfig">
            <h-input id="contactPhone" v-model="formData.contactPhone" placeholder="请输入" autocomplete="off" />
          </h-col>
          <h-col v-bind="lableConfig" style="text-align: right">
            <label for="deliveryArea">送水区域：</label>
          </h-col>
          <h-col v-bind="colConfig">
            <h-select
              id="deliveryArea"
              v-model:value="formData.deliveryArea"
              placeholder="请选择"
              autocomplete="off"
              style="width: 100%"
            >
              <h-select-option value="10">黄岛区</h-select-option>
              <h-select-option value="20">青岛工业园</h-select-option>
              <h-select-option value="30">外围</h-select-option>
            </h-select>
          </h-col>
          <h-col v-bind="lableConfig" style="text-align: right">
            <label for="deliveryAddress">送水地址：</label>
          </h-col>
          <h-col v-bind="colConfig">
            <h-input
              id="deliveryAddress"
              v-model:value="formData.deliveryAddress"
              placeholder="请输入"
              autocomplete="off"
            />
          </h-col>
          <h-col v-bind="lableConfig" style="text-align: right">
            <label for="receiver">签收人：</label>
          </h-col>
          <h-col v-bind="colConfig">
            <h-input id="receiver" v-model:value="formData.receiver" placeholder="请输入" autocomplete="off" />
          </h-col>
          <h-col v-bind="lableConfig" style="text-align: right">
            <label for="payType">支付方式：</label>
          </h-col>
          <h-col v-bind="colConfig">
            <h-checkbox-group v-model:value="formData.payType" style="width: 100%">
              <h-checkbox value="10">微信</h-checkbox>
              <h-checkbox value="20">支付宝</h-checkbox>
              <h-checkbox value="30">水票</h-checkbox>
            </h-checkbox-group>
          </h-col>
          <h-col :span="24">
            <h-row>
              <h-col :xl="2" :lg="3" :md="3" :sm="3" :xs="3" style="text-align: right">
                <label for="remark">备注：</label>
              </h-col>
              <h-col :xl="22" :lg="21" :md="21" :sm="21" :xs="21">
                <h-textarea
                  id="remark"
                  v-model:value="formData.remark"
                  placeholder="请输入"
                  autocomplete="off"
                  :maxlength="100"
                  show-count
                />
              </h-col>
            </h-row>
          </h-col>
        </h-row>
      </h-form>
      <div class="title-text">数据列表</div>
      <h-table v-bind="tableProps" size="middle">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'quantity'">
            <h-input-number v-model:value="record.quantity" />
          </template>
          <template v-else-if="column.dataIndex === 'amount'">
            {{ record.unitPrice * record.quantity }}
          </template>
        </template>
      </h-table>
    </h-modal>
    <h-modal width="50%" v-model:open="customerModalVisible" title="请选择" @cancel="closeCustomerModal" :footer="null">
      <div style="margin: 20px 0">
        <h-button type="primary" @click="handleCustomerOk">确认</h-button>
      </div>
      <h-table v-bind="customerTableProps" size="middle" />
    </h-modal>
  </div>
</template>

<style lang="less" scoped>
:deep(.ant-form-item) {
  margin-bottom: 0;
}
.title-text {
  margin: 10px 0;
  font-weight: bold;
}
</style>
