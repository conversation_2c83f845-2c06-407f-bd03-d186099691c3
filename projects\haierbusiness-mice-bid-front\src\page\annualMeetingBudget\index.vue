<!-- 年度预算 -->

<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker, Timeline as aTimeline, TimelineItem as aTimelineItem, Tooltip as aTooltip, message
} from 'ant-design-vue';
import type { SelectValue, DefaultOptionType } from 'ant-design-vue/es/select';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { QuestionCircleOutlined, UserOutlined, CalendarOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { annualMeetingBudgetApi } from '@haierbusiness-front/apis';
import {
  IAnnualMeetingBudgetFilter,
  IAnnualMeetingBudget,
  MonthData
} from '@haierbusiness-front/common-libs';
import { ref, watch, onMounted, nextTick } from 'vue';
import { useRequest } from 'vue-request';
import { useEditDialog } from '@haierbusiness-front/composables'
import EditDialog from './edit-dialog.vue'
import router from '../../router'

// 定义API响应类型
interface IApiResponse<T> {
  data: T;
  code: string | null;
  message: string | null;
  success: boolean;
}

// 定义月份预算数据类型
interface IMonthBudgetData {
  month: number;
  monthList: IAnnualMeetingBudget[];
}

const currentRouter = ref()

// 年份选择 - 初始设为当前年份
const currentYear = new Date().getFullYear().toString()
const selectedYear = ref(currentYear)

// 处理后的月份数据
const monthlyData = ref<MonthData[]>([])

// 动态生成年份选项
const yearOptions = ref<string[]>([])

// 填充年份选项
const fillYearOptions = () => {
  yearOptions.value = []
  const currentYearNum = new Date().getFullYear()

  for (let i = 0; i >= -5; i--) {
    const year = (currentYearNum + i).toString()
    yearOptions.value.push(year)
  }

  yearOptions.value.sort((a, b) => Number(b) - Number(a))
}

// 获取数据
const { loading, run: fetchData } = useRequest(() => {
  const yearParam = selectedYear.value || currentYear
  console.log('请求参数year:', yearParam)

  const params = { year: yearParam };
  return annualMeetingBudgetApi.list(params);
}, {
  manual: true,
  onSuccess: (res) => {
    console.log('API返回的原始数据:', res)
    processData(res)
  }
})

// 处理API返回的新数据结构
const processData = (response: unknown) => {
  console.log('processData接收到的数据:', response)

  // 检查是否直接返回数组（第一种情况）
  if (Array.isArray(response)) {
    console.log('API直接返回数组，处理中...')
    const mappedData: MonthData[] = response.map((item: IMonthBudgetData) => ({
      month: item.month.toString(), // 转换为字符串以保持一致性
      meetings: item.monthList || []
    }))
    monthlyData.value = mappedData.sort((a, b) => Number(b.month) - Number(a.month))
    console.log('处理后的数据:', monthlyData.value)
    return
  }

  // 检查是否是包装对象格式（第二种情况）
  if (response && typeof response === 'object' && 'data' in response) {
    const apiResponse = response as IApiResponse<IMonthBudgetData[]>

    if (apiResponse.data && Array.isArray(apiResponse.data)) {
      console.log('API返回包装对象，处理data字段...')
      const mappedData: MonthData[] = apiResponse.data.map((item: IMonthBudgetData) => ({
        month: item.month.toString(), // 转换为字符串以保持一致性
        meetings: item.monthList || []
      }))
      monthlyData.value = mappedData.sort((a, b) => Number(b.month) - Number(a.month))
      console.log('处理后的数据:', monthlyData.value)
    } else {
      console.warn('API响应中的data字段不是数组:', apiResponse.data)
      monthlyData.value = []
    }
  } else {
    console.warn('API响应格式不正确:', response)
    monthlyData.value = []
  }
}

// 监听年份变化
watch(selectedYear, (newYear, oldYear) => {
  if (newYear !== oldYear) {
    console.log('正在重新获取数据...')
    fetchData()
  }
}, { immediate: false })

onMounted(async () => {
  currentRouter.value = await router
  fillYearOptions()
  nextTick(() => {
    fetchData()
  })
})

// 格式化月份标签显示
const formatMonthLabel = (month: string) => {
  return `${selectedYear.value}年${month}月`
}

// 获取会议的日期区间显示
const formatMeetingDateRange = (miceTime: string | undefined, days: number | undefined) => {
  if (!miceTime) return '';

  try {
    // 截取年月部分并返回
    const dateParts = miceTime.split('-');
    if (dateParts.length >= 2) {
      return `${dateParts[0]}年${dateParts[1]}月`;
    }
    return miceTime;
  } catch (e) {
    return miceTime;
  }
}

// 获取会议的后两个字作为类型标签
const getNameSuffix = (name: string | undefined) => {
  if (!name) return ''
  if (name.length <= 3) return name
  return name.substring(name.length - 2)
}

// 查看或编辑预算
const handleViewBudget = (meeting: IAnnualMeetingBudget, isEdit = false) => {
  console.log('查看或编辑预算:', meeting.id, isEdit ? '编辑' : '查看');
  if (currentRouter.value) {
    currentRouter.value.push({
      path: isEdit ? '/annualMeetingBudget/edit' : '/annualMeetingBudget/detail',
      query: { id: meeting.id?.toString() }
    })
  }
}

// 处理年份变更
const handleYearChange = (value: SelectValue) => {
  console.log('年份切换为:', value)
  if (typeof value === 'string') {
    selectedYear.value = value
  } else if (typeof value === 'number') {
    selectedYear.value = value.toString()
  }
}

// 弹框相关逻辑
const searchParam = ref<IAnnualMeetingBudgetFilter>({})
const {
  visible,
  editData,
  handleCreate,
  handleEdit: originalHandleEdit,
  onDialogClose,
  handleOk
} = useEditDialog<IAnnualMeetingBudget, IAnnualMeetingBudget>(
  annualMeetingBudgetApi,
  "年度预算",
  () => {
    fetchData()
  }
)

// 重写handleEdit函数以设置isReadonly状态
const handleEdit = (meeting: IAnnualMeetingBudget) => {
  isReadonly.value = false;
  originalHandleEdit(meeting);
}

// 查看模式相关逻辑
const isReadonly = ref(false);
const viewData = ref<IAnnualMeetingBudget | null>(null);
const viewVisible = ref(false);

// 查看预算明细
const handleViewDetail = (meeting: IAnnualMeetingBudget) => {
  viewData.value = { ...meeting };
  isReadonly.value = true;
  viewVisible.value = true;
}

// 关闭查看弹窗
const onViewDialogClose = () => {
  viewVisible.value = false;
  viewData.value = null;
  isReadonly.value = false;
}

// 添加预算 - 弹出对话框
const handleAddBudget = () => {
  isReadonly.value = false;
  handleCreate();
}

// 使用编辑对话框实现查看功能
const handleViewBudgetInDialog = (meeting: IAnnualMeetingBudget) => {
  if (meeting.id) {
    isReadonly.value = true;
    originalHandleEdit(meeting);
  } else {
    message.warning('该会议记录缺少ID，无法查看');
  }
}
</script>

<template>
  <div class="container">
    <div class="annual-budget-container">
      <div class="header-container">
        <div class="year-selector">
          <h-select v-model:value="selectedYear" style="width: 80px" :defaultValue="currentYear"
            @change="handleYearChange">
            <h-select-option v-for="year in yearOptions" :key="year" :value="year">{{ year }}</h-select-option>
          </h-select>
          <div class="page-title">会议年度预算维护</div>
        </div>

        <div class="add-button">
          <h-button type="primary" @click="handleAddBudget">
            <PlusOutlined />
            新增预算
          </h-button>
        </div>
      </div>

      <div class="timeline-container">
        <a-timeline v-if="monthlyData.length > 0">
          <a-timeline-item v-for="monthData in monthlyData" :key="monthData.month">
            <template #dot>
              <div class="custom-dot"></div>
            </template>
            <div class="timeline-content">
              <div class="month-label">{{ formatMonthLabel(monthData.month) }}</div>
              <div class="meeting-list">
                <div v-for="(meeting, index) in monthData.meetings" :key="index" class="meeting-card">
                  <div class="meeting-header">
                    <div class="meeting-type-tags">
                      <h-tag color="blue">{{ getNameSuffix(meeting.name) }}</h-tag>
                    </div>
                    <div class="meeting-title">
                      {{ meeting.name }}
                      <!-- <a-tooltip>
                      <template #title>{{ meeting.name }}</template>
                      <QuestionCircleOutlined />
                    </a-tooltip> -->
                    </div>
                  </div>
                  <div class="meeting-info">
                    <div class="meeting-details">
                      <span>{{ meeting.place }}</span>
                      <span class="budget">{{ meeting.budget }}元</span>
                      <UserOutlined />
                      <span>{{ meeting.personTotal }}人</span>
                      <CalendarOutlined />
                      <span>{{ formatMeetingDateRange(meeting.miceTime, meeting.day) }}</span>
                    </div>
                    <div class="meeting-actions">
                      <h-tag class="action-tag" @click="handleViewBudgetInDialog(meeting)">查看 >></h-tag>
                      <h-tag class="action-tag" @click="() => handleEdit(meeting)">编辑 >></h-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
        <div v-else-if="!loading" class="empty-data">暂无数据</div>
        <div v-else class="loading">加载中...</div>
      </div>

      <div v-if="visible">
        <edit-dialog :show="visible" :data="editData" :readonly="isReadonly" @cancel="onDialogClose" @ok="handleOk">
        </edit-dialog>
      </div>
    </div>
  </div>

</template>

<style scoped lang="less">
.container{
  width: 100%;
  height: 100%;
  background-color:#F1F2F6;
}
.annual-budget-container {
  padding: 24px 10%;
  background-color: #fff;
  min-height: 100vh;
  max-width: 1400px;
  margin: 0 auto;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 16px;
}

.year-selector {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.page-title {
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  margin-left: 10px;
  font-weight: bold;
}

.add-button {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.timeline-container {
  margin-left: 16px;
  margin-top: 24px;
  padding: 0 24px;
}

.custom-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #1890ff;
}

.month-label {
  font-weight: 500;
  margin-bottom: 10px;
}

.meeting-list {
  margin-bottom: 20px;
}

.meeting-card {
  margin-bottom: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.meeting-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #e6f7ff;
}

.meeting-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.meeting-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meeting-title {
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.meeting-details {
  color: #606060;
  display: flex;
  align-items: center;
  gap: 8px;
}

.budget {
  color: #fa8c16;
  font-weight: 500;
}

.meeting-actions {
  display: flex;
  gap: 8px;
}

.action-tag {
  cursor: pointer;
}

.empty-data,
.loading {
  text-align: center;
  padding: 40px 0;
  color: #999;
  font-size: 14px;
}
</style>
