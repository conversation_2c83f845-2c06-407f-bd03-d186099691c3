

type keys = "VALID" | "IN_VALID";

/**
 * 年度计划类型
 */
export const AnnualPlanTypeStateConstant = {
    VALID: { "code": 1, "desc": "有效" },
    IN_VALID: { "code": 0, "desc": "无效" },

    ofCode: (code?: number): { "code": number, "desc": string } | null => {
        for (const key in AnnualPlanTypeStateConstant) {
            const item = AnnualPlanTypeStateConstant[key as keys];
            if (code === item.code) {
                return item;
            }
        }
        return null;
    }
}