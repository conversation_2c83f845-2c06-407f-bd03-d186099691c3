<script setup lang="ts">
import {
  Modal,
  Tag as hTag,
  <PERSON><PERSON> as hButton,
  Select as hSelect,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  SelectOption as hSelectOption,
  Col as hCol,
  Row as hRow,
  Card as hCard,
  Table as hTable,
  FormItem as hFormItem,
  InputNumber as hInputNumber,
  Input as hInput,
  Textarea as hTextarea,
  DatePicker as hDatePicker,
  CollapsePanel as hCollapsePanel,
  Collapse as hCollapse,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { PlusOutlined, SearchOutlined, ExpandAltOutlined, MinusCircleOutlined } from '@ant-design/icons-vue';
import {
  IAnnualPlanSaveOrUpdateRequestDTO,
  AnnualPlanTypeStateConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
  IHaierAccountBillInfo,
  MonthPlanItemTypeConstant,
  IUserListRequest,
  IUserInfo,
} from '@haierbusiness-front/common-libs';
import { getCurrentRouter, resolveParam } from '@haierbusiness-front/utils';
import { ref, createVNode, PropType, computed } from 'vue';

import dayjs, { Dayjs } from 'dayjs';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import {
  IAnnualPlanTypeListRequest,
  IAnnualPlanUpdateItemRequestDTO,
  IAnnualPlanUpdateMonthPlanRequestDTO,
  PlanTypeConstant,
} from '@haierbusiness-front/common-libs/src/daily';
import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
import { number } from 'echarts/core';
import { userApi } from '@haierbusiness-front/apis';
import { usePagination } from 'vue-request';
import { debounce } from 'lodash';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';
import { MonthPlanItemStateConstant } from '../../../../../../common-libs/src/daily/constant/monthPlanItemStateConstant';
const prop = defineProps({
  planIndex: Number as PropType<number>,
  month: Number as PropType<number>,
  year: Number as PropType<number>,
  annualPlan: Object as PropType<IAnnualPlanUpdateItemRequestDTO>,
  type: String as PropType<String>,
});

const columns = computed(() => {
  if (prop?.type === 'summarize' || prop?.type === 'planform-evaluate') {
    if (PlanTypeConstant.QUANTIFY.code !== prop?.annualPlan?.planType) {
      return [
        {
          title: '计划（目标及达成路径）',
          dataIndex: 'planDesc',
          width: '320px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '类型',
          dataIndex: 'typeName',
          width: '100px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '完成状态',
          dataIndex: 'state',
          width: '150px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '实际完成时间',
          dataIndex: 'completePlanTime',
          width: '150px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '完成效果',
          dataIndex: 'completePlanDesc',
          width: '320px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '责任人',
          dataIndex: 'principalUsercode',
          width: '150px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '评价金额',
          dataIndex: 'evaluateAmount',
          width: '200px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '评价备注',
          dataIndex: 'evaluateRemark',
          width: '320px',
          align: 'center',
          ellipsis: true,
        },
      ];
    } else {
      return [
        {
          title: '计划量（目标量）',
          dataIndex: 'planResult',
          width: '320px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '类型',
          dataIndex: 'typeName',
          width: '100px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '完成状态',
          dataIndex: 'state',
          width: '150px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '实际完成时间',
          dataIndex: 'completePlanTime',
          width: '150px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '完成量',
          dataIndex: 'completePlanValue',
          width: '320px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '完成率',
          dataIndex: 'completeRate',
          width: '150px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '责任人',
          dataIndex: 'principalUsercode',
          width: '150px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '评价金额',
          dataIndex: 'evaluateAmount',
          width: '200px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '评价备注',
          dataIndex: 'evaluateRemark',
          width: '320px',
          align: 'center',
          ellipsis: true,
        },
      ];
    }
  } else if (prop?.type === 'evaluate') {
    if (PlanTypeConstant.QUANTIFY.code !== prop?.annualPlan?.planType) {
      return [
        {
          title: '计划（目标及达成路径）',
          dataIndex: 'planDesc',
          width: '320px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '类型',
          dataIndex: 'typeName',
          width: '100px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '完成状态',
          dataIndex: 'state',
          width: '150px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '实际完成时间',
          dataIndex: 'completePlanTime',
          width: '150px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '完成效果',
          dataIndex: 'completePlanDesc',
          width: '320px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '责任人',
          dataIndex: 'principalUsercode',
          width: '150px',
          align: 'center',
          ellipsis: true,
        },
      ];
    } else {
      return [
        {
          title: '计划量（目标量）',
          dataIndex: 'planResult',
          width: '320px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '类型',
          dataIndex: 'typeName',
          width: '100px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '完成状态',
          dataIndex: 'state',
          width: '150px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '实际完成时间',
          dataIndex: 'completePlanTime',
          width: '150px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '完成量',
          dataIndex: 'completePlanValue',
          width: '320px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '完成率',
          dataIndex: 'completeRate',
          width: '150px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '责任人',
          dataIndex: 'principalUsercode',
          width: '150px',
          align: 'center',
          ellipsis: true,
        },
      ];
    }
  } else {
    if (PlanTypeConstant.QUANTIFY.code !== prop?.annualPlan?.planType) {
      return [
        {
          title: '计划（目标及达成路径）',
          dataIndex: 'planDesc',
          width: '320px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '类型',
          dataIndex: 'typeName',
          width: '100px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '责任人',
          dataIndex: 'principalUsercode',
          width: '150px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: '_operator',
          width: '40px',
          fixed: 'right',
          align: 'center',
        },
      ];
    } else {
      return [
        {
          title: '计划量（目标量）',
          dataIndex: 'planResult',
          width: '320px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '类型',
          dataIndex: 'typeName',
          width: '100px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '责任人',
          dataIndex: 'principalUsercode',
          width: '150px',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: '_operator',
          width: '40px',
          fixed: 'right',
          align: 'center',
        },
      ];
    }
  }
});

const currentMonthPlan = computed(() => {
  return prop?.annualPlan?.monthPlans?.filter((it) => {
    return it.month?.toString() === prop?.month?.toString() && !it.isDeleted;
  });
});
const addDomain = () => {
  prop.annualPlan?.monthPlans?.push({
    month: prop?.month,
    planDesc: '',
    planValue: 0,
    type:
      prop?.type === 'adjust'
        ? MonthPlanItemTypeConstant.OUTSIDE_PLAN.code
        : MonthPlanItemTypeConstant.INSIDE_PLAN.code,
    typeName:
      prop?.type === 'adjust'
        ? MonthPlanItemTypeConstant.OUTSIDE_PLAN.desc
        : MonthPlanItemTypeConstant.INSIDE_PLAN.desc,
    principalUsercode: '',
    principalUsername: '',
  });
};
const removeDomain = (item: any) => {
  if (item.id) {
    item.isDeleted = true;
  } else {
    let index = prop.annualPlan?.monthPlans?.indexOf(item);
    if (index != undefined && index !== -1) {
      prop.annualPlan?.monthPlans?.splice(index, 1);
    }
  }
};
const getIndex = (record: any) => {
  return prop.annualPlan?.monthPlans?.indexOf(record) || 0;
};

const userNameChange = (userInfo: IUserInfo, record: IAnnualPlanUpdateMonthPlanRequestDTO) => {
  record.principalUsercode = userInfo.username;
  record.principalUsername = userInfo.nickName;
};
const checkDisabled = (annualPlan: any, record: any) => {
  return (
    (prop.type === 'adjust' &&
      (annualPlan!!.monthPlans!![getIndex(record)] as any).type === MonthPlanItemTypeConstant.INSIDE_PLAN.code) ||
    prop.type === 'summarize' ||
    prop.type === 'evaluate' ||
    prop.type === 'planform-evaluate'
  );
};

const checkDisabledRemove = (annualPlan: any, record: any) => {
  return (
    (checkDisabled(annualPlan, record) || (annualPlan!!.monthPlans!![getIndex(record)] as any).state) &&
    prop.type === 'resetting'
  );
};
const currentData = computed(() => {
  return dayjs()
    .set('year', prop.year as any)
    .set('month', (prop.month || 1) - 1)
    .set('day', 1);
});
</script>

<template>
  <h-row :gutter="24">
    <h-col :span="24" style="text-align: left">
      <h-table
        :columns="(columns as any)"
        :row-key="(record) => record.index"
        :size="'small'"
        :pagination="false"
        :data-source="currentMonthPlan"
        :scroll="{ x: 550 }"
        :bordered="true"
      >
        <template v-if="type == 'summarize'" #emptyText> 当前月份无计划 </template>
        <template v-else #emptyText> 请点击下方按钮新增月度分解 </template>
        <template #bodyCell="{ column, text, record, index }">
          <template v-if="['planDesc'].includes(column.dataIndex as any)">
            <div>
              <h-input
                :disabled="checkDisabled(annualPlan, record)"
                v-model:value="annualPlan!!.monthPlans!![getIndex(record)].planDesc"
                style="margin: -5px 0"
                allowClear
              >
                <template #addonAfter>
                  <h-popover trigger="click">
                    <template #content>
                      <h-textarea
                        :disabled="checkDisabled(annualPlan, record)"
                        v-model:value="annualPlan!!.monthPlans!![getIndex(record)].planDesc"
                        style="width: 500px"
                        :rows="4"
                        allowClear
                      />
                    </template>
                    <ExpandAltOutlined />
                  </h-popover>
                </template>
              </h-input>
            </div>
          </template>
          <template v-if="['planResult'].includes(column.dataIndex as any)">
            <div>
              <h-row>
                <h-col :span="20"
                  ><h-input-number
                    :precision="2"
                    :decimalPlaces="2"
                    :disabled="checkDisabled(annualPlan, record)"
                    v-model:value="annualPlan!!.monthPlans!![getIndex(record)].planValue"
                    style="margin: -5px 0; width: 100%"
                    type="number"
                    allowClear
                    placeholder="计划量（目标量）"
                /></h-col>
                <h-col :span="1">/</h-col>
                <h-col :span="3" style="text-align: left">{{ prop?.annualPlan?.planUnit }}</h-col>
              </h-row>
            </div>
          </template>
          <template v-if="['typeName'].includes(column.dataIndex as any)">
            <h-tag v-if="(annualPlan!!.monthPlans!![getIndex(record)] as any).type === 1" color="blue">{{
              (annualPlan!!.monthPlans!![getIndex(record)] as any).typeName
            }}</h-tag>
            <h-tag v-else-if="(annualPlan!!.monthPlans!![getIndex(record)] as any).type === 2" color="green">{{
              (annualPlan!!.monthPlans!![getIndex(record)] as any).typeName
            }}</h-tag>
          </template>
          <template v-if="['state'].includes(column.dataIndex as any)">
            <h-select
              :disabled="type !== 'summarize'"
              v-if="annualPlan!!.monthPlans!![getIndex(record)].state === MonthPlanItemStateConstant.WAIT.code||
              annualPlan!!.monthPlans!![getIndex(record)].state === MonthPlanItemStateConstant.RUNNING.code||
              annualPlan!!.monthPlans!![getIndex(record)].state === MonthPlanItemStateConstant.COMPLETED.code||
              annualPlan!!.monthPlans!![getIndex(record)].state === MonthPlanItemStateConstant.TERMINATED.code||
              annualPlan!!.monthPlans!![getIndex(record)].state === MonthPlanItemStateConstant.INCOMPLETE.code"
              v-model:value="annualPlan!!.monthPlans!![getIndex(record)].state"
              placeholder="选择状态"
            >
              <h-select-option :value="MonthPlanItemStateConstant.RUNNING.code" :disabled="true">
                请选择</h-select-option
              >
              <h-select-option :value="MonthPlanItemStateConstant.COMPLETED.code">{{
                MonthPlanItemStateConstant.COMPLETED.desc
              }}</h-select-option>
              <h-select-option :value="MonthPlanItemStateConstant.TERMINATED.code">{{
                MonthPlanItemStateConstant.TERMINATED.desc
              }}</h-select-option>
              <h-select-option :value="MonthPlanItemStateConstant.INCOMPLETE.code">{{
                MonthPlanItemStateConstant.INCOMPLETE.desc
              }}</h-select-option>
            </h-select>
            <div v-else>待闭环</div>
          </template>
          <template v-if="['completePlanTime'].includes(column.dataIndex as any)">
            <h-date-picker
              :disabled="annualPlan!!.monthPlans!![getIndex(record)].state !== MonthPlanItemStateConstant.COMPLETED.code || type !== 'summarize'"
              :defaultPickerValue="currentData"
              v-model:value="annualPlan!!.monthPlans!![getIndex(record)].completePlanTime"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </template>
          <template v-if="['completePlanValue'].includes(column.dataIndex as any)">
            <h-row>
              <h-col :span="20"
                ><h-input-number
                  :disabled="type !== 'summarize'"
                  :precision="2"
                  :decimalPlaces="2"
                  v-model:value="annualPlan!!.monthPlans!![getIndex(record)].completePlanValue"
                  style="margin: -5px 0; width: 100%"
                  type="number"
                  allowClear
                  placeholder="完成量"
              /></h-col>
              <h-col :span="1">/</h-col>
              <h-col :span="3" style="text-align: left">{{ prop?.annualPlan?.planUnit }}</h-col>
            </h-row>
          </template>
          <template v-if="['completePlanDesc'].includes(column.dataIndex as any)">
            <div>
              <h-input
                :disabled="type !== 'summarize'"
                v-model:value="annualPlan!!.monthPlans!![getIndex(record)].completePlanDesc"
                style="margin: -5px 0"
                allowClear
              >
                <template #addonAfter>
                  <h-popover trigger="click">
                    <template #content>
                      <h-textarea
                        v-model:value="annualPlan!!.monthPlans!![getIndex(record)].completePlanDesc"
                        style="width: 500px"
                        :rows="4"
                        allowClear
                      />
                    </template>
                    <ExpandAltOutlined />
                  </h-popover>
                </template>
              </h-input>
            </div>
          </template>
          <template v-if="['completeRate'].includes(column.dataIndex as any)">
            {{
              (
                (annualPlan!!.monthPlans!![getIndex(record)].completePlanValue || 0) /
                (annualPlan!!.monthPlans!![getIndex(record)].planValue || 0) /
                0.01
              ).toFixed(2)
            }}
            %
          </template>
          <template v-if="['principalUsercode'].includes(column.dataIndex as any)">
            <user-select
              :value="annualPlan!!.monthPlans!![getIndex(record)].principalUsername"
              :disabled="checkDisabled(annualPlan, record)"
              placeholder="执行人"
              :params="{
                pageNum: 1,
                pageSize: 20,
              }"
              @change="(userInfo: IUserInfo) =>  userNameChange(userInfo,  annualPlan!!.monthPlans!![getIndex(record)])"
            ></user-select>
          </template>
          <template v-if="['evaluateAmount'].includes(column.dataIndex as any)">
            <h-row>
              <h-col :span="20"
                ><h-input-number
                  :disabled="type !== 'summarize'"
                  :precision="2"
                  :decimalPlaces="2"
                  v-model:value="annualPlan!!.monthPlans!![getIndex(record)].evaluateAmount"
                  style="margin: -5px 0; width: 100%"
                  type="number"
                  allowClear
                  placeholder="评价金额"
              /></h-col>
              <h-col :span="1">/</h-col>
              <h-col :span="3" style="text-align: left">元</h-col>
            </h-row>
          </template>
          <template v-if="['evaluateRemark'].includes(column.dataIndex as any)">
            <div>
              <h-input
                :disabled="type !== 'summarize'"
                v-model:value="annualPlan!!.monthPlans!![getIndex(record)].evaluateRemark"
                style="margin: -5px 0"
                allowClear
              >
                <template #addonAfter>
                  <h-popover trigger="click">
                    <template #content>
                      <h-textarea
                        v-model:value="annualPlan!!.monthPlans!![getIndex(record)].evaluateRemark"
                        style="width: 500px"
                        :rows="4"
                        allowClear
                      />
                    </template>
                    <ExpandAltOutlined />
                  </h-popover>
                </template>
              </h-input>
            </div>
          </template>
          <template v-if="['_operator'].includes(column.dataIndex as any)">
            <h-popconfirm
              v-if="!checkDisabledRemove(annualPlan, record)"
              title="确认删除当前计划?"
              @confirm="removeDomain(record)"
            >
              <h-button type="link" danger>
                <template #icon>
                  <MinusCircleOutlined :style="{ fontSize: '20px' }" />
                </template>
              </h-button>
            </h-popconfirm>
          </template>
        </template>
      </h-table>
    </h-col>
    <h-col :span="24" style="margin-top: 14px">
      <h-form-item>
        <h-button
          v-if="type != 'summarize' && type != 'evaluate' && type != 'planform-evaluate'"
          type="dashed"
          style="width: 100%"
          @click="addDomain"
        >
          <PlusOutlined />
          添加月度分解
        </h-button>
      </h-form-item>
    </h-col>
  </h-row>
</template>

<style scoped lang="less">
@import '../../../assets/css/main.less';
</style>
