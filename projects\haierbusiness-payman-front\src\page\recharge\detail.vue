<script lang="ts" setup>
import { computed, createVNode, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';
import { RechargeStatusEnumNng } from '@haierbusiness-front/common-libs';
import {
  Anchor as hAnchor,
  <PERSON><PERSON> as hButton,
  Spin as hSpin,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Checkbox as hCheckbox,
  Form as hForm,
  InputNumber as hInputNumber,
  FormItem as hFormItem,
  Row as hRow,
  Col as hCol,
  Popconfirm as hPopconfirm,
  Upload as hUpload,
  Input as hInput,
  Modal as hModal,
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  DownloadOutlined,
  VerticalAlignBottomOutlined,
  ExclamationCircleFilled,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  SendOutlined,
  PhoneOutlined,
  VerticalAlignTopOutlined,
  DeleteOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { download, tripApi, teamListApi, rechargeApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import relativeTime from 'dayjs/plugin/relativeTime';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';

import { CityResponse, CityItem, TCteateTeam, RRechargeSaveParams } from '@haierbusiness-front/common-libs';
import { fileApi } from '@haierbusiness-front/apis';
import type { TableColumnType } from 'ant-design-vue';
import { useRouter } from 'vue-router';

const route = ref(getCurrentRoute());
const router = useRouter();
const detail = ref(null);

const id = route.value?.query?.id;
const budgetCode = route.value?.query?.budgetCode;


const goRecharge = () => {
  router.push('/payman/recharge');
};

watch(
  () => id,
  () => {},
);
watch(
  () => budgetCode,
  () => {},
);
onMounted(() => {
  getDetail();
});
const formatDateTime = (time: string | undefined) => {
  if (!time) {
    return '';
  }
  return dayjs(time).format('YYYY年MM月DD日 HH:mm:ss');
};
// 根据id获取详情
const getDetail = () => {
  rechargeApi.getAdminRechargeDetail(id).then((res) => {
    console.log('🚀 ~ rechargeApi.getRechargeDetail ~ res:', res);
    detail.value = res;
  });
};

dayjs.extend(relativeTime);

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const labelCol = { span: 3 };
const wrapperCol = { span: 12 };
const spinning = ref<boolean>(false);

const rechargeForm = ref<RRechargeSaveParams>({
  checked: false,
  budgetAmount: '',
  reason: '',
  fileList: [],
  fileName: '',
  fileUrl: '',
});
const getrechangeBusinessFlag = (status:number | string)=>{
  if(status==1){
    return '是'
  }else if(status==0){
    return '否'
  }else {
    return ''
  }
}
</script>

<template>
  <div class="container">
    <div class="row flex">
      <div class="apply-con flex">

        <a-descriptions title="订单详情" class="banner-detail" :column="1">
          <a-descriptions-item label="订单编号">{{ detail?.orderCode }}</a-descriptions-item>
          <a-descriptions-item label="订单状态">{{
            RechargeStatusEnumNng[detail?.status] || ''
          }}</a-descriptions-item>
          <a-descriptions-item label="申请人">{{ detail?.applyName }}</a-descriptions-item>
          <a-descriptions-item label="申请时间">{{ formatDateTime(detail?.applyTime) }}</a-descriptions-item>
          <a-descriptions-item label="是否商旅">{{ getrechangeBusinessFlag(detail?.businessFlag) }}</a-descriptions-item>

          <a-descriptions-item label="充值金额">{{ detail?.budgetAmount }}元</a-descriptions-item>
          <a-descriptions-item label="预算系统单号">{{ budgetCode }}</a-descriptions-item>
          <a-descriptions-item label="平台支付单号">{{ detail?.payCode }}</a-descriptions-item>
          <a-descriptions-item label="方案报告">
            <a :href="detail?.fileUrl" download target="_blank">{{ detail?.fileName }}</a>
          </a-descriptions-item>
          <a-descriptions-item label="充值事由">{{ detail?.reason }}</a-descriptions-item>
          <a-descriptions-item v-if='detail?.processFailInfo' label="拒绝原因">{{ detail?.processFailInfo }}</a-descriptions-item>
        </a-descriptions>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url(./recharge.less);
.container{
    width: 100%;
    .row{
      width: 100%;
      margin-top: 0;
      .apply-con{
        width: 100%;
        padding-top:20px;
      }
    }
}

.change-title {
  position: absolute;
  right: 0;
  top: -80px;
  width: 300px;

  .ant-col-6,
  .ant-col-18 {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #000000d9;
  }
}

.mt-5 {
  margin-top: 5px;
}

.ml-5 {
  margin-left: 5px;
}

.color-eee {
  color: #a8a7a7;
}

.com-box {
  background: #f5f5f5;
  padding: 20px;
  margin-top: 20px;

  .whole-line {
    height: 40px;
    font-weight: 600;
  }

  .info-box {
    display: flex;
    align-items: flex-start;
    padding: 10px 20px;
    border: 1px solid #ffdb5c;
    background: #fffbd8;
  }

  :deep(.ant-form-item-control-input-content) {
    display: flex;
    align-items: center;
  }
}

.title {
  height: 60px;
}

.download-btn {
  color: #408cff;
  cursor: pointer;
}

:deep(.city-chose-box) {
  width: 200px !important;
}

:deep(.mr-10) {
  margin-right: 10px;
}

.background-eee {
  padding: 10px;
  background: #f4f4f4;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
