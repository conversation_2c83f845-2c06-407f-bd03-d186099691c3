<template>
  <div>
    <div class="title flex justify-content-between align-items-center">
      <div class="flex align-items-center justify-content-center">
        <span class="shu"></span>
        <div class="text">基本信息</div>
      </div>
      <div
        class="title-right font-size-14"
        @click.stop="showOut = true"
        :class="showOut ? 'color-disabled' : 'color-main'"
      >
        <van-icon name="add-o" class="mr-5" />添加外部出行人
      </div>
    </div>
    <!-- 出行人 -->
    <van-cell-group inset class="mb-10">
      <van-field is-link  @click="showMainpersonFuc" required label-align="top" readonly>
        <template #label>
          <div class="my_field_label flex justify-content-between align-items-center">
            <div class="left">
              <span class="mr-5">出差人</span>
            </div>
            <div class="right" @click.stop="getClbz">差旅标准</div>
          </div>
        </template>

        <template #input>
          <div class="color-eee" v-if="!chosedPerson.travelUserName">选择出差人</div>
          <div class="flex" v-else>
            <div class="color-main mr-5">{{ `${chosedPerson.travelUserName} / ${chosedPerson.travelUserNo}` }}</div>
            <span class="color-eee">{{ chosedPerson.travelUserDeptName }}</span>
          </div>
        </template>
      </van-field>

      

      <!-- 单选主出差人 -->
      <van-popup v-model:show="showMainPersonList" position="bottom">
        <div class="out-pop" style="height: 100vh">
          <van-sticky>
            <div style="background: #fff; padding-bottom: 10px">
              <van-nav-bar title="选择出行人" left-arrow @click-left="closeMainPersonPop">
                <template #left>
                  <van-icon name="arrow-left" color="#000" size="24" />
                </template>
                <template #right>
                  <div class="color-main"></div>
                </template>
              </van-nav-bar>

              <van-cell-group inset style="margin: 16px; background-color: #fff">
                <van-field
                  @update:model-value="searchMainPerson"
                  v-model="searchValue"
                  style="background: #f6f7f9"
                  placeholder="输入工号、姓名"
                />
              </van-cell-group>
            </div>
          </van-sticky>
          <van-list
            v-model:loading="mainPersonLoading"
            :finished="mainPersonFinished"
            :finished-text=" mainPersonList?.length ? '没有更多了':'' "
            @load="onLoadMainPerson"
          >
            <div v-if="mainPersonList?.length == 0 && mainPersonFinished" style="height: 66vh;" class="flex align-items-center justify-content-center">
              <img class="img_empty" src="../../../../assets/image/trip/empty.jpg" alt="">
            </div>
            <template v-else>
              <van-cell v-for="(item, index) in mainPersonList" :key="index" @click="choseMainPerson(item)">
                <div class="flex align-items-center out-person-checkbox">
                  <div
                    :class="index % 3 == 0 ? 'blue' : 'yellow'"
                    class="mr-20 ml-10 flex align-items-center justify-content-center img-name"
                  >
                    {{ item?.nickName ? item?.nickName.slice(-2) : '未知' }}
                  </div>
                  <div class="main">
                    <div class="user-name color-main">{{ item.nickName }}({{ item?.username }})</div>
                    <div class="phone">{{ item.enterpriseName || '未知' }}</div>
                  </div>
                </div>
              </van-cell>
            </template>
          </van-list>
          
        </div>
      </van-popup>
    </van-cell-group>

    <van-cell-group inset class="mb-10">
      <!-- 内部出行人 -->
      <van-field @click="showInnerList"  label-align="top" readonly>
        <template #label>
          <div class="my_field_label flex justify-content-between align-items-center">
            <div class="left">
              <span class="mr-5">内部出行人</span>
            </div>
          </div>
        </template>

        <template #input>
          <div class="color-eee" v-if="chosedInPersenList?.length < 1">选择内部出行人</div>
          <div class="flex inner-tag-box" v-else>
            <van-tag class="color-main inner-tag mr-5 mb-5" v-for="item, index in chosedInPersenList" :key="index" closeable plain size="medium" type="primary" @close="deleteInPersion(index)">
              {{ `${item.travelUserName}(${item.travelUserNo})` }}
            </van-tag>
          </div>
        </template>
      </van-field>

      <!-- 多选内部出行人 -->
      <van-popup v-model:show="showInPersonsList" position="bottom">
        <div class="out-pop" style="height: 100vh">
          <van-sticky>
            <div style="background: #fff; padding-bottom: 10px">
              <van-nav-bar title="选择出行人" left-arrow @click-left="closeInPersonPop" @click-right="closeInPersonPop">
                <template #left>
                  <van-icon name="arrow-left" color="#000" size="24" />
                </template>
                <template #right>
                  <div class="color-main">确定</div>
                </template>
              </van-nav-bar>

              <van-cell-group inset style="margin: 16px; background-color: #fff">
                <van-field
                  @update:model-value="searchMainPerson"
                  v-model="searchValue"
                  style="background: #f6f7f9"
                  placeholder="输入工号、姓名"
                />
              </van-cell-group>
            </div>
          </van-sticky>

          <van-list
            v-model:loading="mainPersonLoading"
            :finished="mainPersonFinished"
            :finished-text="mainPersonList?.length ? '没有更多了':'' "
            @load="onLoadMainPerson"
          >
            <div v-if="mainPersonList?.length == 0 && mainPersonFinished" style="height: 66vh;" class="flex align-items-center justify-content-center">
              <img class="img_empty" src="../../../../assets/image/trip/empty.jpg" alt="">
            </div>
            <template v-else>
              <van-checkbox-group v-model="checked">

                <van-cell v-for="(item, index) in mainPersonList" :key="index"  @click="toggle(item,index)">
                  <div class="flex align-items-center out-person-checkbox">
                    <div
                      :class="index % 3 == 0 ? 'blue' : 'yellow'"
                      class="mr-20 ml-10 flex align-items-center justify-content-center img-name"
                    >
                      {{ item?.nickName ? item?.nickName.slice(-2) : '未知' }}
                    </div>
                    <div class="main">
                      <!-- <van-text-ellipsis class="user-name color-main" :content="nameText(item)" /> -->

                      <div class="user-name color-main">{{ item.nickName }}({{ item?.username }})</div>
                      <div class="phone">{{ item.departmentName || '-' }}</div>
                    </div>
                  </div>

                  <template #right-icon>
                    <van-checkbox
                      :name="item"
                      :ref="el => checkboxRefs[index] = el"
                      @click.stop="toggle(item,index)"
                    />
                  </template>
                </van-cell>

              </van-checkbox-group>
            </template>
          </van-list>


        </div>
      </van-popup>
    </van-cell-group>
    <van-cell-group v-if="showPersonWarning()" inset class="mb-10">
      <van-field label-align="top" readonly>
        <template #input>
          <div style="color: #ffaa00;"><van-icon name="warning" class="mr-10" />多同行人必须所有人确认后才能报销</div>
        </template>
      </van-field>
    </van-cell-group>

    <!-- 外部出行人 -->
    <van-cell-group inset v-if="showOut" class="mb-10">
      <van-field is-link  @click="showOutPop" label-align="top" readonly>
        <template #label>
          <div class="my_field_label flex justify-content-between align-items-center">
            <div class="left">
              <span class="mr-5">外部出行人</span>
            </div>
            <div class="right" @click.stop="showOut = false">
              <van-icon name="close" color="#e65857" class="mr-5" />取消外部出行人
            </div>
          </div>
        </template>

        <template #input>
          <div class="color-eee" v-if="!chosedOutPersenList?.length">选择外部出差人</div>
          <div class="flex flex-warp" v-else>
            <van-tag
              class="mb-5 mr-5"
              v-for="(item, index) in chosedOutPersenList"
              :key="index"
              closeable
              size="medium"
              type="primary"
              @close="delOutPerson(item, index)"
            >
              {{ item.travelUserName }}
            </van-tag>
          </div>
        </template>
      </van-field>

      <van-popup v-model:show="showOutPersonList" position="bottom">
        <div class="out-pop" style="min-height: 100vh; overflow: hidden;">
          <van-sticky>
            <div style="background: #fff; padding-bottom: 10px">
              <van-nav-bar title="添加外部出行人" left-arrow @click-left="closeOutPersonPop">
                <template #left>
                  <van-icon name="arrow-left" color="#000" size="24" />
                </template>
                <template #right>
                  <div class="color-main" @click.stop="closeOutPersonPop">确定</div>
                </template>
              </van-nav-bar>

              <!-- <van-cell-group inset style="margin: 16px">
                <van-field v-model="searchOutValue" style="background: #f6f7f9" placeholder="请输入用户名" />
              </van-cell-group> -->
              <div class="add-person color-main"  @click="addOuterShow=true"><van-icon name="add-o" class="mr-5" />新增人员</div>
            </div>
          </van-sticky>
          <van-list
            v-model:loading="outPersonLoading"
            :finished="outPersonFinished"
            finished-text="没有更多了"
            @load="onLoadOutPerson"
          >
            <van-cell v-for="(item, index) in outPersonList" :key="index">
              <van-checkbox v-model="item.checked" class="flex align-items-center out-person-checkbox">
                <div
                  :class="index % 3 == 0 ? 'blue' : 'yellow'"
                  class="mr-20 ml-10 flex align-items-center justify-content-center img-name"
                >
                  {{ item?.travelUserName?.slice(-2) }}
                </div>
                <div class="main">
                  <div class="user-name color-main">{{ item.travelUserName }}</div>
                  <div class="phone">{{ item.sjhm || '' }}</div>
                </div>
              </van-checkbox>
            </van-cell>
          </van-list>
        </div>
      </van-popup>

      <!-- 添加外部出行人 -->
      <van-action-sheet v-model:show="addOuterShow" title="添加外部出行人">
        <van-form @submit="onSubmit">

          <van-cell-group inset title="基本信息">
            <van-field
              autocomplete="off"
              input-align="right" error-message-align="right"
              v-model="outerPersonParams.name"
              name="姓名"
              label="姓名"
              placeholder="姓名"
              :required="requiredCn"
              :rules="requiredCn ? [{ required: true, message: '请填写姓名' }] : []"
            />
            <van-field
              autocomplete="off"
              input-align="right" 
              error-message-align="right"
              :required="requiredEng"
              :rules="requiredEng ? [{ required: true, message: '输入英文姓' }] : []"

              v-model="outerPersonParams.familyName"
              :formatter='nameFormat'
              name="输入英文姓"
              label="英文姓名"
              placeholder="输入英文姓"
            />
            <van-field
              autocomplete="off"
              input-align="right" 
              error-message-align="right"
              :required="requiredEng"
              :formatter='nameFormat'

              :rules="requiredEng ? [{ required: true, message: '输入英文名' }] : []"
              v-model="outerPersonParams.realName"
              name="输入英文名"
              placeholder="输入英文名"
            />

            <van-field
              v-model="outerPersonParams.natureName"
              input-align="right" 
              is-link
              error-message-align="right"
              name="国籍/地区"
              label="国籍/地区"
              placeholder="国籍/地区"
              required
              readonly
              @click="opCityPicker('nature')"
              :rules="[{ required: true, message: '请选择国籍/地区' }]"
            />

            <van-field
              name="性别"
              label="性别"
              placeholder="性别"
              input-align="right" error-message-align="right"
              required
              :rules="[{ required: true, message: '请填写姓名' }]"
            >
              <template #input>
                <van-radio-group v-model="outerPersonParams.sex" direction="horizontal">
                  <van-radio name="M">男</van-radio>
                  <van-radio name="F">女</van-radio>
                </van-radio-group>
              </template>
            </van-field>

            <van-field
              v-model="outerPersonParams.birthday"
              input-align="right"
              error-message-align="right"
              name="出生日期"
              label="出生日期"
              placeholder="出生日期"
              readonly
              is-link
              @click="showBirthdayPicker"
            />


            <van-field
              v-model="outerPersonParams.travelType"
              input-align="right" error-message-align="right"
              name="乘机人类型"
              label="乘机人类型"
              placeholder="选择乘机人类型"
              readonly
              is-link
              @click="showCjrPicker=true"
            >
              <template #input>
                <div>{{ columnsCjr.find(item => item.id == outerPersonParams.travelType)?.name }}</div>
              </template>
            </van-field>


            <van-field
              v-model="outerPersonParams.phone"
              input-align="right" 
              error-message-align="right"
              autocomplete="off"
              type="tel"
              name="联系电话"
              label="联系电话"
              placeholder="联系电话"
              required
              :rules="[{ required: true, message: '请输入联系电话' }]"
            />


            <van-field
              v-model="outerPersonParams.email"
              input-align="right" error-message-align="right"
              autocomplete="off"
              name="邮箱"
              label="邮箱"
              placeholder="邮箱"
            />


            <van-field
              v-model="outerPersonParams.telephone"
              input-align="right" error-message-align="right"
              autocomplete="off"
              type="tel"
              name="电话号码"
              label="电话号码"
              placeholder="电话号码"
            />

            <van-field
              v-model="outerPersonParams.promiseLevel"
              name="服务保障级别"
              label="服务保障级别"
              placeholder="选择服务保障级别"
              label-width="100px"
              readonly
              is-link
              input-align="right" error-message-align="right"
              @click="showBzjbPicker = true"
            >
              <!-- <template #input>
                <div>{{ serviceGuarantee.find(item => item.serviceGuaranteeCode == outerPersonParams.promiseLevel)?.serviceGuarantee }}</div>
              </template> -->
            </van-field>



          </van-cell-group>

          <van-cell-group inset title="证件信息">
            <van-field
              v-model="outerPersonParams.cardType"
              input-align="right" 
              error-message-align="right"
              name="证件类型"
              label="证件类型"
              placeholder="证件类型"
              required
              :rules="[{ required: true, message: '请填写证件类型' }]"
              is-link
              @click="showZjlxPicker = true"
            >
              <template #input>
                <div>{{ certificates.find(item => item.certificatesCode == outerPersonParams.cardType)?.certificates }}</div>
              </template>
            </van-field>

            <van-field
              v-model="outerPersonParams.cardNo"
              autocomplete="off"
              input-align="right" error-message-align="right"

              name="证件号码"
              label="证件号码"
              placeholder="证件号码"
              required
              :rules="[{ required: true, message: '请填写证件号码' }]"
            />


            <van-field
              v-model="outerPersonParams.effectBeginDate"
              input-align="right" 
              error-message-align="right"

              name="证件有效期"
              label="证件有效期"
              placeholder="请选择证件起始日期"
              required
              readonly 
              is-link
              @click="openTimePicker('begin')"
              :rules="[{ required: true, message: '请选择证件起始日期' }]"
            />

            <van-field autocomplete="off" required readonly is-link input-align="right" error-message-align="right"
            label="" :rules="[{ required: true, message: '请选择证件有效期' }]" placeholder="请选择证件截止日期"
            @click="openTimePicker('end')" v-model="outerPersonParams.effectEndDate" />

            <van-field
              v-if="outerPersonParams.cardType=='1003402'"
              v-model="outerPersonParams.passportName"
              input-align="right" 
              error-message-align="right"

              name="护照签发地"
              label="护照签发地"
              placeholder="护照签发地"
              is-link
              required
              readonly
              @click="opCityPicker('passport')"
              
              :rules="[{ required: true, message: '请填写护照签发地' }]"
            />


            <van-field
              v-if="outerPersonParams.cardType=='1003402'"
              v-model="outerPersonParams.qfrqStr"
              input-align="right" error-message-align="right"
              name="签发时间"
              label="签发时间"
              placeholder="签发时间"
              required
              is-link
              readonly
              :rules="[{ required: true, message: '请填写签发时间' }]"
              @click="showQfsjPicker = true"

            />

          </van-cell-group>
          <div style="margin: 16px; background-color: #fff">
            <van-button style="width: 300px; margin:0 auto;" round block type="primary" native-type="submit">
                提交
              </van-button>
          </div>
             

        </van-form>
      </van-action-sheet>
    </van-cell-group>

    <!-- 预订方式 -->
    <van-cell-group inset class="mb-10">
      <van-field required :rules="[{ required: true, message: '请选择预订方式' }]" name="radio" label-align="left">
        <template #label>
          <div class="flex justify-content-between align-items-center">
            <div class="left">
              <span class="mr-5">预订方式</span>
              <van-icon name="question-o" @click.stop="showMessage('差旅资源集团内统一集采，大资源换大资源，实现差旅价值最大化，无必要原因，差旅费应100%商旅平台预订。非商旅预订：本次出差交通工具及酒店全部自费预订，事后报销。')" color="#cccccc" />
            </div>
            <div class="right"></div>
          </div>
        </template>
        <template #input>
          <van-radio-group :disabled="props.isChange" v-model="props.creatTripParma.travelReserveFlag" direction="horizontal">
            <van-radio :name="1">经商旅预订</van-radio>
            <van-radio :name="0">自费垫付</van-radio>
          </van-radio-group>
        </template>
      </van-field>

      <!-- <van-field
        v-if="!props?.creatTripParma?.travelReserveFlag"
        is-link
        v-model="props.creatTripParma.unbookedReasonId"
        @click="showResonList = true"
        required
        label-align="top"
        readonly
        :rules="[{ required: true, message: '请选择不经商旅预订原因' }]"
      >
        <template #label>
          <div class="my_field_label flex justify-content-between align-items-center">
            <div class="left">
              <span class="mr-5">不经商旅预订原因</span>
              <van-icon name="question-o" color="#cccccc" />
            </div>
            <div class="right"></div>
          </div>
        </template>

        <template #input>
          <div class="color-eee" v-if="!props.creatTripParma.unbookedReasonId">请选择</div>
          <div class="flex" v-else>
            {{ resonList.filter((item) => item.id == props.creatTripParma.unbookedReasonId)[0]?.reasonInfo }}
          </div>
        </template>
      </van-field> -->

      <van-popup v-model:show="showResonList" position="bottom">
        <van-picker
          title="不经商旅预订原因"
          :columns-field-names="{ text: 'reasonInfo', value: 'id' }"
          :columns="resonList"
          @confirm="onConfirm"
          @cancel="showResonList = false"
        />
      </van-popup>
    </van-cell-group>

    <!-- 出差事由 -->
    <van-cell-group inset class="mb-10">
      <van-field placeholder="请输入出差事由" :rules="[{ required: true, message: '请输入出差事由' }]" autocomplete="off" v-model="props.creatTripParma.travelReason" required label-align="top">
        <template #label>
          <div class="my_field_label flex justify-content-between align-items-center">
            <div class="left">
              <span class="mr-5">出差事由</span>
              <van-icon name="question-o" @click.stop="showMessage('事前明确投入产出，明确项目目标、路径，用于说明出差的必要性和合理性')" color="#cccccc" />
            </div>
            <div class="right"></div>
          </div>
        </template>
      </van-field>
    </van-cell-group>

    <!-- 变更原因 -->
    <van-cell-group inset v-if="props.isChange">
      <van-field placeholder="请输入变更原因" :rules="[{ required: true, message: '请输入变更原因' }]" autocomplete="off" v-model="props.creatTripParma.changeReason" required label-align="top">
        <template #label>
          <div class="my_field_label flex justify-content-between align-items-center">
            <div class="left">
              <span class="mr-5">变更原因</span>
              <van-icon name="question-o" @click.stop="showMessage('变更订单原因')" color="#cccccc" />
            </div>
            <div class="right"></div>
          </div>
        </template>
      </van-field>
    </van-cell-group>


    <!-- 差旅标准 -->
    <van-dialog v-model:show="showClbz" title="差旅标准" >
      <div style="height: 400px; overflow:auto;" v-if="standardList?.length > 0">
        <van-cell-group v-for="item, index in standardList" :key="index">
          <div style="text-align:center; padding:10px; font-size: 14px; font-weight: 500;">{{nameList[index]}}的差旅标准</div>

          <div v-if="item && item.length > 0">
            <van-cell v-for="item2,index2 in item" :key="index2" :title="item2.productName">
              <template #label>
                <div v-html="item2?.describes"></div>
              </template>
            </van-cell>
          </div>
          <van-empty v-else description="暂无数据" />

        </van-cell-group>
      </div>

      <van-empty v-else description="暂无数据" />
    </van-dialog>

    <!-- 外部出行人出生日期 -->
    <van-popup v-model:show="showCsrqPicker" round position="bottom">
      <van-date-picker v-model="currentCsrq" title="选择日期" value-format="YYYY-MM-DD" :min-date="minDateCsrq"
        :max-date="maxDateCsrq" @confirm="confirmCsrq" @cancel="showCsrqPicker = false" />
    </van-popup>

    <!-- 乘机人类型 -->
    <van-popup v-model:show="showCjrPicker" round position="bottom">
      <van-picker title="选择乘机人" :columns="columnsCjr" :columns-field-names="{ text: 'name', value: 'id' }"
        @cancel="showCjrPicker = false" @confirm="onConfirmCjr" />
    </van-popup>

    <!-- 服务保障级别 -->

    <van-popup v-model:show="showBzjbPicker" round position="bottom">
      <van-picker title="选择保障级别" :columns="serviceGuarantee" :columns-field-names="{ text: 'serviceGuarantee', value: 'serviceGuaranteeCode' }"
        @cancel="showBzjbPicker = false" @confirm="onConfirmBzjb" />
    </van-popup>

    <!-- 证件类型 -->
    <van-popup v-model:show="showZjlxPicker" round position="bottom">
      <van-picker title="选择证件类型" :columns="certificates" :columns-field-names="{ text: 'certificates', value: 'certificatesCode' }"
        @cancel="showZjlxPicker = false" @confirm="onConfirmZjlx" />
    </van-popup>

     <!-- 证件有效期选择 -->
     <van-popup v-model:show="showRqPicker" round position="bottom">
      <van-date-picker v-model="currentRq" title="选择日期" :min-date="minDate" :max-date="maxDate" @confirm="confirmYxq"
        @cancel="showRqPicker = false" />
    </van-popup>

    <!-- 外部出行人签发时间 -->
    <van-popup v-model:show="showQfsjPicker" round position="bottom">
      <van-date-picker v-model="currentQfsj" title="选择日期" value-format="YYYY-MM-DD" :min-date="minDateQfsj"
        :max-date="maxDateQfsj" @confirm="confirmQfsj" @cancel="showQfsjPicker = false" />
    </van-popup>

    <!-- 国籍地区选择 -->
    <van-popup v-model:show="showCityPicker" round position="bottom">
      <van-picker @cancel="showCityPicker = false" @confirm="onConfirmCity" title="选择国籍/地区" :columns="cityOptions" :columns-field-names="{ text: 'mc', value: 'mc', children: 'children' }" />
    </van-popup>

  </div>
</template>

<script lang="ts" setup>
import { createVNode, onMounted,onBeforeUpdate, reactive, ref, watch, watchEffect, toRefs,computed } from 'vue';
import type { Ref } from 'vue';

import baseInfo from './components/baseInfo.vue';
import { IUserListRequest,TrainStandardRes,QueryServiceGuaranteeRes, IUserInfo, ICity, ITripInfo, IOutPerson, ITraveler, ICreatTrip, CityOption } from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { reasonApi } from '@haierbusiness-front/apis';
import { userApi,tripApi } from '@haierbusiness-front/apis';
import { debounce, values } from 'lodash';

import { showConfirmDialog, showSuccessToast,showToast } from 'vant';
import { Item } from 'ant-design-vue/es/menu';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'

dayjs.extend(isSameOrAfter)
const store = applicationStore();
const { loginUser } = storeToRefs(store);

interface Props {
  creatTripParma?: ICreatTrip; // 人员
  isDetail?: boolean;
  isChange?: boolean;
  chosedNow?: string;
}
const props = defineProps<Props>();

// 差旅标准
const showClbz = ref<boolean>(false)
const standardList = ref<Array<TrainStandardRes>>([])
const queryTrainStandard = (params:any) => {
  tripApi.queryTrainStandard(params).then((res:any) => {
    standardList.value = res || []
  })
}
const codeList = ref<any>([]) 
const nameList = ref<any>([]) 
const getClbz = () => {
  codeList.value =  props.creatTripParma?.travelerList?.filter((item:any) => item.travelUserType == 0).map(item2 => item2.travelUserNo)
  nameList.value =  props.creatTripParma?.travelerList?.filter((item:any) => item.travelUserType == 0).map(item2 => item2.travelUserName)

  // 判断经办人的工号是否在出差人工号列表里面
  if(codeList.value.indexOf(loginUser.value?.username)== -1 ) {
    codeList.value = [loginUser.value?.username, ...codeList.value]
    nameList.value = [loginUser.value?.nickName, ...nameList.value]
  }
  queryTrainStandard({userCodes:codeList.value});
  showClbz.value = true
}
const requiredEng = computed(() => outerPersonParams.value.natureName != '中国');
const requiredCn = computed(() => outerPersonParams.value.natureName == '中国');

// 新增外部出行人
const addOuterShow = ref<boolean>(false)
const outerPersonParams = ref<IOutPerson>({
  /*姓名 */
  name: '',
  /*英文名 */
  englishName: '',
  realName: '',
  familyName: '',
  /*国籍/地区 */
  natureName: '中国',
  natureCode: 'CN',
  /*性别1男2女 */
  sex: '1',
  /*出生日期 */
  birthday: '',

  /*乘机人类型 */
  travelType: '1',

  /*联系电话 */
  telephone: '',

  /*邮箱 */
  email: '',

  /*电话号码 */
  phone: '',

  /*服务保障等级 */
  promiseLevel: '普通保障级别',

  /*证件类型 */
  cardType: '',

  /*证件号 */
  cardNo: '',


  /*有效期始 */
  effectBeginDate: '',

  /*有效期止 */
  effectEndDate: '',
});

const showMessage = (message: string) => {
  showToast(message);
}

const onSubmit = () => {
  outerPersonParams.value.englishName = outerPersonParams.value.realName && outerPersonParams.value.familyName ? outerPersonParams.value.familyName + '/' + outerPersonParams.value.realName : '';

  tripApi.addOutPerson(outerPersonParams.value).then(res => {
    
    showSuccessToast('新增成功')
    addOuterShow.value = false
    outPersonParams.value.current = 0
    outPersonList.value =[]
    outPersonLoading.value = false;
    onLoadOutPerson()
    outerPersonParams.value = {
      /*姓名 */
      name: '',
      /*英文名 */
      englishName: '',
      realName: '',
      familyName: '',
      /*国籍/地区 */
      natureName: '中国',
      natureCode: 'CN',
      /*性别1男2女 */
      sex: '1',
      /*出生日期 */
      birthday: '',

      /*乘机人类型 */
      travelType: '1',

      /*联系电话 */
      telephone: '',

      /*邮箱 */
      email: '',

      /*电话号码 */
      phone: '',

      /*服务保障等级 */
      promiseLevel: '普通保障级别',

      /*证件类型 */
      cardType: '',

      /*证件号 */
      cardNo: '',


      /*有效期始 */
      effectBeginDate: '',

      /*有效期止 */
      effectEndDate: '',
    }
  })
}
const nameFormat = (res) => { 
  if(res) {
    return res.replace(/[^a-zA-Z]/g, '').toUpperCase()
  }else {
    return ''
  }
}
const showMainpersonFuc = () => {
  if(props.isChange) {
    return
  }
  showMainPersonList.value = true
}
const showPersonWarning = () => {
  const innerPersonList = props.creatTripParma.travelerList?.filter(item => item.travelUserType == '0')
  return innerPersonList?.length > 1
}
// 国籍列表
const cityOptions = ref<Array<CityOption>>([])
const showCityPicker = ref(false)
const openCityType = ref()
const opCityPicker = (type:string) => {
  openCityType.value = type
  showCityPicker.value = true
}

const queryTripNationality = () => {

  tripApi.queryTripNationality().then(res => {
    res?.forEach(item => {
      item.mc = item?.bClassFky?.mc
      item.children = []
      Object.keys(item?.bclassMap).forEach(key => {
        item.children = [...item.children, ...item?.bclassMap[key]]
      });
      
    });
    cityOptions.value = res
  })
}

const showBirthdayPicker =() => {
  currentCsrq.value = [new Date().getFullYear(), new Date().getMonth()+1, new Date().getDate()]
  showCsrqPicker.value = true
}

const onConfirmCity = ({ selectedValues, selectedOptions }: any) => {
  if(openCityType.value == 'nature') {
    outerPersonParams.value.natureName = selectedOptions[1].mc
    outerPersonParams.value.natureCode = selectedOptions[1].by3
  }else {
    outerPersonParams.value.passportName = selectedOptions[1].mc
    outerPersonParams.value.passportCode = selectedOptions[1].by3
  }

  showCityPicker.value = false;
};

// 出生日期选择
const currentCsrq = ref()
const minDateCsrq = new Date(1920,0,1);
const maxDateCsrq = new Date();
const showCsrqPicker = ref<boolean>(false);
const confirmCsrq = ({ selectedValues }: any) => {
  outerPersonParams.value.birthday = selectedValues.join('-');
  showCsrqPicker.value = false;
};

// 签发时间选择
const currentQfsj = ref([new Date().getFullYear(), new Date().getMonth()+1, new Date().getDate()])
const minDateQfsj = new Date(1980,0,1);
const maxDateQfsj = new Date();
const showQfsjPicker = ref<boolean>(false);
const confirmQfsj = ({ selectedValues }: any) => {
  outerPersonParams.value.qfrqStr = selectedValues.join('-');
  showQfsjPicker.value = false;
};

// 乘机人类型
const showCjrPicker = ref<boolean>(false)
const columnsCjr = ref([
  {
    id: '1',
    name: '成人',
  },
  {
    id: '2',
    name: '儿童',
  },
  {
    id: '3',
    name: '婴儿',
  },
])

const onConfirmCjr = ({ selectedOptions }: any) => {
  showCjrPicker.value = false;
  outerPersonParams.value.travelType = selectedOptions[0].id;
}

// 保障级别
const showBzjbPicker = ref<boolean>(false)
const serviceGuarantee = ref<Array<QueryServiceGuaranteeRes>>([])
const queryServiceGuarantee = () => {
  tripApi.queryServiceGuarantee().then(res => {
    serviceGuarantee.value = res
  })
}
const onConfirmBzjb = ({ selectedOptions }: any) => {
  showBzjbPicker.value = false;
  // outerPersonParams.value.promiseLevel = selectedOptions[0].serviceGuaranteeCode;
  outerPersonParams.value.promiseLevel = selectedOptions[0].serviceGuarantee;

}

// 证件类型
const showZjlxPicker = ref<boolean>(false)
const certificates = ref<Array<QueryCertificatesRes>>([])
const queryCertificates = () => {
  tripApi.queryCertificates().then(res => {
    certificates.value = res
  })
}
const onConfirmZjlx = ({ selectedOptions }: any) => {
  showZjlxPicker.value = false;
  outerPersonParams.value.cardType = selectedOptions[0].certificatesCode;
}

// 证件有效期
const showRqPicker = ref<boolean>(false)
const choseTimeType = ref('')
const currentRq = ref<Array<string|number>>([]);
const minDate = ref(new Date(1990, 0, 1));
const maxDate = ref(new Date(2040, 0, 1)); 
const openTimePicker = (type: string) => {
  choseTimeType.value = type;
  currentRq.value = [new Date().getFullYear(), new Date().getMonth()+1, new Date().getDate()]
  if (outerPersonParams.value.effectBeginDate) {
    const minDateArr = outerPersonParams.value.effectBeginDate.split('-');
    minDate.value = new Date(parseInt(minDateArr[0]), parseInt(minDateArr[1]) - 1, parseInt(minDateArr[2]));
  }
  if (outerPersonParams.value.effectEndDate) {
    const maxDateArr = outerPersonParams.value.effectEndDate.split('-');
    maxDate.value = new Date(parseInt(maxDateArr[0]), parseInt(maxDateArr[1]) - 1, parseInt(maxDateArr[2]));
  }

  if (type == 'begin') {
    minDate.value = new Date(1990, 0, 1);
    // minDate.value = new Date();
    if(outerPersonParams.value.effectBeginDate) {
      currentRq.value = outerPersonParams.value.effectBeginDate?.split('-');
    }
  } else {
    maxDate.value = new Date(2040, 0, 1);
    if(outerPersonParams.value.effectEndDate) {
      currentRq.value = outerPersonParams.value.effectEndDate?.split('-');
    }
  }

  showRqPicker.value = true;
};

const confirmYxq = ({ selectedValues }: any) => {
  if (choseTimeType.value == 'begin') {
    outerPersonParams.value.effectBeginDate = selectedValues.join('-');
    outerPersonParams.value.effectBeginDate = `${outerPersonParams.value.effectBeginDate}`
  } else {
    outerPersonParams.value.effectEndDate = selectedValues.join('-');
    outerPersonParams.value.effectEndDate = `${outerPersonParams.value.effectEndDate}`
  }
  showRqPicker.value = false;
};

// 多选出行人
const showInPersonsList = ref(false)
// 已经选择的内部同行人
const chosedInPersenList = ref<Array<ITraveler>>([]);

const checkboxRefs = ref([]);
const checked = ref([]);

const nameText = (item) => {
  return `${item.nickName}(${item.username})`
}

const toggle = (item:any, index:number) => {
  var currentIndex = chosedInPersenList.value?.findIndex(inner => inner.travelUserNo == item.username)
  if(currentIndex < 0) {
    chosedInPersenList.value?.push(
        {
          travelUserNo: item.username,
          travelUserDeptName: item.enterpriseName,
          travelUserSyId: item.username,
          travelUserName: item.nickName,
          travelUserType: '0',
          id: ''
        }
      )
  }else {
    chosedInPersenList.value?.splice(currentIndex, 1)
  }
  props.creatTripParma.travelerList = [chosedPerson.value, ...chosedOutPersenList.value, ...chosedInPersenList.value];

  // 勾选
  checkboxRefs.value[index]?.toggle();
};

const deleteInPersion = (currentIndex:number) => {
  if(props.isChange) {
    return
  }
  let res = mainPersonList.value.findIndex((chosed:any) => chosed.username == chosedInPersenList.value[currentIndex].travelUserNo)
  if(res > -1) {
    checkboxRefs.value[res]?.toggle(false);
  }
  chosedInPersenList.value?.splice(currentIndex, 1)
  props.creatTripParma.travelerList = [chosedPerson.value, ...chosedOutPersenList.value, ...chosedInPersenList.value];
}

const showOutPop = () => {
  if(props.isChange) {
    return
  }
  showOutPersonList.value = true

}

onBeforeUpdate(() => {
  checkboxRefs.value = [];
});
const closeInPersonPop = () => {
  showInPersonsList.value = false
}

const showInnerList = () => {
  if(props.isChange) {
    return
  }
  showInPersonsList.value = true
  toggleByList()
}

// 根据已选人员勾选列表
const toggleByList = () => {
  mainPersonList.value.forEach((item :any,index:number) => {
        let res = chosedInPersenList.value.findIndex((chosed:any) => chosed.travelUserNo == item.username)
        if(res > -1) {
          checkboxRefs.value[index]?.toggle(true);
        }
      })
}


// 出行人相关
const mainPersonList = ref<Array<ITraveler>>([]);
const showMainPersonList = ref(false);
const mainPersonLoading = ref(false);
const mainPersonFinished = ref(false);
const chosedPerson = ref<ITraveler>({});
const mainPersonTotal = ref(0);

watch(
  mainPersonList,
  (newValue, oldValue) => {
    // checked.value = []
    if(newValue?.length > 0 && chosedInPersenList.value.length > 0) {

      newValue.forEach((item :any,index:number) => {
        let res = chosedInPersenList.value.findIndex((chosed:any) => chosed.travelUserNo == item.username)
        if(res > -1) {
          checkboxRefs.value[index]?.toggle(true);
        }
      })
    }
  },
  {
    deep: true
  }
);




// 根据登陆人初始化数据
chosedPerson.value = props.creatTripParma?.travelerList?.filter((item) => {
  return item.travelUserType == '0' && item.mainFlag == '1';
})[0];

const defaultParams: IUserListRequest = {
  pageNum: 0,
  pageSize: 20,
};

const params: Ref<IUserListRequest> = ref(defaultParams);

const onLoadMainPerson = () => {
  params.value.pageNum++;
  userApi.list(params.value).then((res) => {
    // 加载状态结束
    mainPersonLoading.value = false;
    mainPersonList.value = [...mainPersonList.value, ...res.records];
    mainPersonTotal.value = res.total ?? 0;
    // 数据全部加载完成
    if (mainPersonList.value.length >= mainPersonTotal.value) {
      mainPersonFinished.value = true;
    }
  });
};

const searchMainPerson = debounce((val: string) => {
  params.value.keyWord = val;
  params.value.pageNum = 0;
  params.value.pageSize = 20;
  mainPersonFinished.value = false;
  mainPersonList.value = [];
  onLoadMainPerson();
});

const closeMainPersonPop = () => {
  showMainPersonList.value = false;
};

const choseMainPerson = (item: ITraveler) => {
  chosedPerson.value = item;
  chosedPerson.value.travelUserNo = item.username;
  chosedPerson.value.travelUserDeptName = item.enterpriseName;
  chosedPerson.value.travelUserSyId = item.username;
  chosedPerson.value.travelUserName = item.nickName;
  chosedPerson.value.travelUserType = '0';
  chosedPerson.value.mainFlag = '1';
  chosedPerson.value.id = '';

  props.creatTripParma.travelUserName = item?.nickName ?? '未知';

  props.creatTripParma.travelerList = [chosedPerson.value, ...chosedOutPersenList.value, ...chosedInPersenList.value];

  showMainPersonList.value = false;
};

// 外部出行人
const showOutPersonList = ref(false);
const outPersonLoading = ref(false);
const outPersonTotal = ref(0);
const searchOutValue = ref('')

const outPersonFinished = ref(false);
const searchValue = ref('');
const outPersonList = ref<Array<ITraveler>>([]);
const chosedOutPersenList = ref<Array<ITraveler>>([]);

watch(
  outPersonList,
  (newValue) => {
    chosedOutPersenList.value = [];
    chosedOutPersenList.value = newValue.filter((item) => item.checked);

    props.creatTripParma.travelerList = [chosedPerson.value, ...chosedOutPersenList.value, ...chosedInPersenList.value];
  },
  { deep: true },
);

defineExpose({
  chosedPerson,
  chosedInPersenList,
  chosedOutPersenList
});

watch(
  chosedOutPersenList.value,
  (newVal,oldVal) => {
    if (newVal.length > 0) {
      showOut.value = true
    }
  },
  {
    deep: true,
    immediate: true
  }
)


const outPersonParams = ref({
  current: 0,
  size: 10,
  sfmrss: 0
})
const onLoadOutPerson = () => {
  outPersonParams.value.current++;
    tripApi.getOutPersonList(outPersonParams.value).then(res => {
      res?.trainExternalPersonnelVos?.forEach(item => {
        item.travelUserSyId = item.ygid
        item.travelUserName = item.lkxm || item.ywxm
        item.travelUserDeptName = item.qybh
      })
      outPersonList.value = [...outPersonList.value, ...res?.trainExternalPersonnelVos]
      // 加载状态结束
      outPersonLoading.value = false;
      outPersonTotal.value = Number(res?.total) ?? 0;
      // 数据全部加载完成
      if (outPersonList.value.length >= outPersonTotal.value) {
        outPersonFinished.value = true;
      }
    })
    
};

const closeOutPersonPop = () => {
  showOutPersonList.value = false;

  // showConfirmDialog({
  // title: '提示',
  //   message:'确定要返回吗,不会保存已选的外部联系人!',
  // })
  //   .then(() => {
  //     showOutPersonList.value = false
  //   })
  //   .catch(() => {
  //     // on cancel
  //   });
};

const delOutPerson = (item: ITraveler, index: number) => {
  outPersonList.value.filter((out) => out.travelUserSyId == item.travelUserSyId)[0].checked = false;
};

// 基础信息相关
const showResonList = ref(false);
const showOut = ref(false);
const columns = [
  { text: '杭州', value: 'Hangzhou' },
  { text: '宁波', value: 'Ningbo' },
  { text: '温州', value: 'Wenzhou' },
  { text: '绍兴', value: 'Shaoxing' },
  { text: '湖州', value: 'Huzhou' },
];

const onConfirm = ({ selectedOptions }) => {
  props.creatTripParma.unbookedReasonId = selectedOptions[0]?.id;

  showResonList.value = false;
};
// 未预定原因列表
const resonList = ref<Array<object>>([]);
  // 获取未商旅预定原因
const getResonList = () => {
  reasonApi.list({pageNum: 1,
    reasonType:10,
    pageSize: 50,}).then(res => {
      resonList.value=res.records
    })
}

onMounted(() => {
  // queryTrainStandard()
  getResonList()

  queryServiceGuarantee()
  queryCertificates()
  queryTripNationality()

});
</script>

<style lang="less" scoped>
@import url(./mobile.less);
</style>