<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import { banquetReservationApi } from '@haierbusiness-front/apis';
import {
  BApplyListRecord,
  BApplyPerson,
  BanquetPayTypeNum,
  TakeoutOorderStatusNum
} from '@haierbusiness-front/common-libs';
import { getEnumOptions } from '@haierbusiness-front/utils';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';

import router from '../../router'
// const router = useRouter()
const store = applicationStore();

const currentRouter = ref()
const route = ref(getCurrentRoute());
const id = route.value?.query?.id;
const detail = ref<any>();

const { loginUser } = storeToRefs(store);

const getDetail = (id: number) => {

  banquetReservationApi.get(id).then((res) => {
    if(res.waterTicketInformation){
      res.waterTicketInformation = res.waterTicketInformation.split(",")
    }
    detail.value = res;
  });
};

const columns: ColumnType[] = [
          {
            title: '交易时间',
            dataIndex: 'handTime',
            key: 'handTime',
          },
          {
            title: '交易方式',
            dataIndex: 'tradeTypeStr',
            key: 'tradeTypeStr',
          },
          {
            title: '支付方式1',
            dataIndex: 'payType',
            key: 'payType',
          },
          {
            title: '交易金额',
            dataIndex: 'tradeAmount',
            key: 'tradeAmount',
          },
          {
            title: '流水号',
            dataIndex: 'flowCode',
            key: 'flowCode',
          },
        ]

const columnsForCpmx: ColumnType[] = [
          {
            title: '菜品名称',
            dataIndex: 'foodName',
            key: 'foodName',
          },
          {
            title: '菜品原价',
            dataIndex: 'originPrice',
            key: 'originPrice',
          },
          {
            title: '菜品折后价',
            dataIndex: 'foodPrice',
            key: 'foodPrice',
          },
          {
            title: '菜品数量',
            dataIndex: 'foodCount',
            key: 'foodCount',
          },
          {
            title: '小计',
            dataIndex: 'totalPrice',
            key: 'totalPrice',
          },
        ]

const columnsForPsxx: ColumnType[] = [
          {
            title: '订单状态',
            dataIndex: 'deliveryStatusStr',
            key: 'deliveryStatusStr',
          },
          {
            title: '更新时间',
            dataIndex: 'deliveryTime',
            key: 'deliveryTime',
          },
        ]
const showMore = ref(false)


onMounted(async () => {
  currentRouter.value = await router
})

const toRefundForm = (orderBookingCode:string) =>{
  currentRouter.value.push({
    path: '/refundForm',
    query: {
      orderBookingCode
    }
  })
}

const getInnerPerson = (list?:Array<BApplyPerson>) => {
  if (list && list.length > 0) {
   let resultList = list.filter(item => item.haierUser == true)
   return resultList.map(item => `${item.userName}(${item.userCode})`).join(',')
  }else {
     return ''
  }

}

const getOuterPerson = (list?:Array<BApplyPerson>) => {
  if (list && list.length > 0) {
    let resultList = list.filter(item => !item.haierUser)
    return resultList.map(item => item.userName).join(',')
  }else {
    return ''
  }
}

const downloadFile2 = (url:string, name:string) => {
      var xhr = new XMLHttpRequest();
      xhr.open('GET', url, true);
      xhr.responseType = 'blob';

      xhr.onload = function() {
        if (xhr.status === 200) {
          var blob = xhr.response;
          var a = document.createElement('a');
          var url = URL.createObjectURL(blob);
          a.href = url;
          a.download = name;
          a.click();
          URL.revokeObjectURL(url);
        }
      };

      xhr.send();
    }

const downLoadFile = (url:string, name:string) => {
  window.open(url)
  // downloadFile2(url,name)
}

const statusToText = (payStatus?:string|number) => {
  if(payStatus==10){
    return "未支付"
  }else if (payStatus==20){
    return "已支付"
  }else if (payStatus==31){
    return "部分退款"
  }else if (payStatus==32){
    return "全额退款"
  }
}

// 下单场景
const getmtSceneType = (mtSceneType:string) =>{
  if(mtSceneType==1){
    return "商务宴请"
  }else if (mtSceneType==4){
    return "工作餐"
  }else if (mtSceneType==5){
    return "团建用餐"
  }else{
    return "无场景"
  }
}

watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);




</script>

<template>
  <div>
    <h-row justify="space-between" style="padding: 20px;">
      <h-col style="font-size: 18px;">预订单详情</h-col>
      <h-col><h-button type="link" @click="currentRouter.back(-1)">返回</h-button></h-col>
    </h-row>
    <div v-if="detail?.sceneType == 1" style="background-color: #ffff;height: 100%;width: 100%;padding: 30px 60px;overflow: auto;">
      <h-descriptions title="基本信息" style="margin-bottom: 20px;" bordered>
        <h-descriptions-item label="预订单号">{{ detail?.orderBookingCode }}</h-descriptions-item>
        <h-descriptions-item label="美团预订单号">{{ detail?.mtBookingCode }}</h-descriptions-item>
        <h-descriptions-item label="申请单号">{{ detail?.orderCode }}</h-descriptions-item>
        <h-descriptions-item label="经办人">{{detail?.creatorName? `${detail?.creatorName}(${detail?.creator})`:'' }}</h-descriptions-item>
        <h-descriptions-item label="签单人">{{detail?.signerName? `${detail?.signerName}(${detail?.signerCode})`:'' }}</h-descriptions-item>
        <h-descriptions-item label="订单类型">{{ detail?.sceneType == 1 ? '宴请' :  detail?.sceneType == 2 ?'外卖':'' }}</h-descriptions-item>
        <h-descriptions-item label="下单城市">{{ detail?.mealLocationCity }}</h-descriptions-item>
        <h-descriptions-item label="商户名">{{ detail?.restaurantName }}</h-descriptions-item>
        <h-descriptions-item label="餐厅电话">{{ detail?.restaurantPhone }}</h-descriptions-item>

        <h-descriptions-item label="下单场景">{{ getmtSceneType(detail?.mtSceneType) }}</h-descriptions-item>
        <h-descriptions-item label="下单时间">{{ detail?.createTime }}</h-descriptions-item>
        <h-descriptions-item label="支付状态">{{statusToText(detail?.payStatus) }}</h-descriptions-item>


        <h-descriptions-item label="餐厅地址" :span="3"> <span v-if=" detail?.restaurantLocation">{{detail?.mealLocationProvince}} <span v-if="detail?.mealLocationProvince!=detail?.mealLocationCity">{{detail?.mealLocationCity}}</span>{{ detail?.restaurantLocation }}</span></h-descriptions-item>
      </h-descriptions>
      <h-descriptions title="详细信息" style="margin-bottom: 20px;" bordered>
        <h-descriptions-item label="支付类型">{{  BanquetPayTypeNum[detail?.payType] }} </h-descriptions-item>
        <h-descriptions-item label="实际支付金额">
          {{  detail?.actualPaymentAmount }}元
          <div> <span style="margin-right:12px;">企业: {{ detail?.entPayAmount }}元 </span>  <span>个人：{{ detail?.staffPayAmount }}元</span></div>
        </h-descriptions-item>
        <h-descriptions-item label="优惠金额"> <div v-if="detail?.totalReduceAmount||detail?.totalReduceAmount==0">{{  detail?.totalReduceAmount }}元</div> </h-descriptions-item>
        <h-descriptions-item label="退款金额">{{  detail?.totalRefundAmount  }}</h-descriptions-item>
        <h-descriptions-item label="团购类型" :span="2">{{  detail?.groupUseType }}</h-descriptions-item>
        <h-descriptions-item label="用餐小票" :span="3">
          <div class="flex">
              <div v-for="item in detail?.waterTicketInformation" style="margin-right:10px;">
                <a-image
                :width="150"
                :height="150"
                :src="item"
              />
            </div>
          </div>
        </h-descriptions-item>
      </h-descriptions>
      <h-descriptions title="签到信息" style="margin-bottom: 20px;" bordered>
        <h-descriptions-item label="签到人">{{ detail?.checkinPersonName }}({{ detail?.checkinPersonCode }})</h-descriptions-item>
        <h-descriptions-item label="签到时间">{{detail?.checkinTime }}</h-descriptions-item>
        <h-descriptions-item label="签到地址" :span="3">{{ detail?.checkinLocation }} </h-descriptions-item>
      </h-descriptions>
      <h-descriptions title="支付信息" style="margin-bottom: 20px;">
      </h-descriptions>
      <a-table :dataSource="detail?.payInfoList " :columns="columns" bordered  :pagination="false">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'payType'">
              {{ record.payType == 1 ? '公司支付' :record.payType == 2?'个人支付':'' }}
            </template>
          </template>
      </a-table>
      
    </div>

    <!-- 外卖详情 -->
    <div v-else style="background-color: #ffff;height: 100%;width: 100%;padding: 30px 60px;overflow: auto;">
      <h-descriptions title="基本信息" style="margin-bottom: 20px;" bordered>
        <h-descriptions-item label="预订单号">{{ detail?.orderBookingCode }}</h-descriptions-item>
        <h-descriptions-item label="美团预订单号">{{ detail?.mtBookingCode }}</h-descriptions-item>
        <h-descriptions-item label="申请单号">{{ detail?.orderCode }}</h-descriptions-item>
        <h-descriptions-item label="经办人">{{detail?.creatorName? `${detail?.creatorName}(${detail?.creator})`:'' }}</h-descriptions-item>
        <h-descriptions-item label="订单类型">{{ detail?.sceneType == 1 ? '宴请' :  detail?.sceneType == 2 ?'外卖':'' }}</h-descriptions-item>
        <h-descriptions-item label="订单状态">{{TakeoutOorderStatusNum[detail?.takeoutOrder?.orderStatus]}}</h-descriptions-item>
        <h-descriptions-item label="下单城市">{{ detail?.mealLocationCity }}</h-descriptions-item>
        <h-descriptions-item label="商户名">{{ detail?.restaurantName }}</h-descriptions-item>
        <h-descriptions-item label="餐厅电话">{{ detail?.restaurantPhone }}</h-descriptions-item>

        <h-descriptions-item label="下单场景">{{ getmtSceneType(detail?.mtSceneType) }}</h-descriptions-item>
        <!-- <h-descriptions-item label="下单时间">{{ detail?.createTime }}</h-descriptions-item> -->
        <h-descriptions-item label="支付状态">{{statusToText(detail?.payStatus) }}</h-descriptions-item>
        <h-descriptions-item label="商户地址" :span="3"> <span v-if=" detail?.restaurantLocation">{{detail?.mealLocationProvince}} <span v-if="detail?.mealLocationProvince!=detail?.mealLocationCity">{{detail?.mealLocationCity}}</span>{{ detail?.restaurantLocation }}</span></h-descriptions-item>
      </h-descriptions>
      <h-descriptions title="详细信息" style="margin-bottom: 20px;" bordered>
        <h-descriptions-item label="商品原价总金额"> <div v-if="detail?.takeoutOrder?.originalPrice||detail?.takeoutOrder?.originalPrice==0">{{  detail?.takeoutOrder?.originalPrice }}元</div></h-descriptions-item>

        <h-descriptions-item label="优惠金额"> <div v-if="detail?.totalReduceAmount||detail?.totalReduceAmount==0">{{  detail?.totalReduceAmount }}元</div></h-descriptions-item>
        <h-descriptions-item label="随单收服务费"> <div v-if="detail?.realtimeServiceFee||detail?.realtimeServiceFee==0">{{  detail?.realtimeServiceFee }}元</div></h-descriptions-item>
        <h-descriptions-item label="配送金额"> <div v-if="detail?.takeoutOrder?.shippingFee||detail?.takeoutOrder?.shippingFee==0">{{  detail?.takeoutOrder?.shippingFee }}元</div> </h-descriptions-item>
        <h-descriptions-item label="打包费用(餐盒)"> <div v-if="detail?.takeoutOrder?.boxTotalPrice||detail?.takeoutOrder?.boxTotalPrice==0">{{  detail?.takeoutOrder?.boxTotalPrice }}元</div></h-descriptions-item>
        <h-descriptions-item label="订单支付总金额">
          {{  detail?.actualPaymentAmount }}元
          <div> <span style="margin-right:12px;">企业: {{ detail?.entPayAmount }}元 </span>  <span>个人：{{ detail?.staffPayAmount }}元</span></div>
        </h-descriptions-item>
      </h-descriptions>
      <h-descriptions title="菜品明细">
      </h-descriptions>
      <a-table :dataSource="detail?.takeoutOrder?.foodList " :columns="columnsForCpmx" bordered :pagination="false">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'boxPrice'">
              {{  }}
            </template>
          </template>
          <template #summary>
          <a-table-summary-row>
            <a-table-summary-cell>菜品总金额</a-table-summary-cell>
            <a-table-summary-cell :col-span="4">
              <a-typography-text type="danger">
                {{ detail?.takeoutOrder?.foodListTotal }}
              </a-typography-text>
            </a-table-summary-cell>
          </a-table-summary-row>
    </template>
      </a-table>
      <h-descriptions title="收货信息" style="margin-bottom: 20px;margin-top: 20px;" bordered>
        <h-descriptions-item label="收货人">{{ detail?.takeoutOrder?.recipientName }}</h-descriptions-item>
        <h-descriptions-item label="收货时间">{{detail?.takeoutOrder?.deliverTime }}</h-descriptions-item>
        <h-descriptions-item label="收货地址" :span="3">{{ detail?.takeoutOrder?.recipientAddress }} </h-descriptions-item>
      </h-descriptions>
      <h-descriptions title="配送信息">
      </h-descriptions>
      <a-table :dataSource="detail?.deliveryList " :columns="columnsForPsxx" bordered :pagination="false">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'payType'">
              {{ record.payType == 1 ? '公司支付' :record.payType == 2?'个人支付':'' }}
            </template>
          </template>
      </a-table>
      
    </div>

  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}
:deep(.ant-descriptions-item-label) {
  width: 200px;
}
:deep(.ant-descriptions-item-content) {
  width: 300px;
}
.flex{
  display: flex;
}
</style>
