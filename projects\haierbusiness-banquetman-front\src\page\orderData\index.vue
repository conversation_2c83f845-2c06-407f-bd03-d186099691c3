<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Cascader as hCascader
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons-vue';
import { banquetOrderApi, download, banquetApi } from '@haierbusiness-front/apis';
import {
  IApplyFilter,
  IApply,
  BanquetStatusEnum,
  BanquetApproveStatusEnum
} from '@haierbusiness-front/common-libs';
import { getEnumOptions } from '@haierbusiness-front/utils';

import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import router from '../../router'
// const router = useRouter()
import type { ShowSearchType } from 'ant-design-vue/es/cascader';

const currentRouter = ref()

onMounted(async () => {
    currentRouter.value = await router
    handleTableChange({ current: 1, pageSize: 10 })

})

// 订单状态
const orderState = computed(() => {
  return getEnumOptions(BanquetStatusEnum, true);
});

const approveState = computed(() => {
  return getEnumOptions(BanquetApproveStatusEnum, true);
});


const columns: ColumnType[] = [
  {
    title: '序号',
    dataIndex: 'index',
    width: '60px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '餐厅名称',
    dataIndex: 'restaurantName',
    width: '240px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '预订单数量',
    dataIndex: 'totalBookingCount',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '已核对数量',
    dataIndex: 'checkedCount',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '未核对数量',
    dataIndex: 'uncheckedCount',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  


  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<any>({})

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(banquetOrderApi.list);

const {
  data:exportListData,
  run:exportListApiRun,
  loading:exportListLoading,
} = useRequest(banquetOrderApi.exportList);

const reset = () => {
  cityCodeList.value = []
  searchParam.value = {}
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const goToDetail = (name: string) => {
  const orderQuery = {
    ...searchParam.value
  }
  orderQuery.restaurantName = name
  // 保存查询条件
  localStorage.setItem('order_query', JSON.stringify(orderQuery))
  currentRouter.value.push({
    path: '/orderData/detail',
   
  })
}



const filter: ShowSearchType['filter'] = (inputValue, path) => {
  return path.some(option => option.chineseName.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
};


const cityCodeList = ref([])
watch(() => cityCodeList.value, (n: any, o: any) => {
  if (n && n.length > 0) {
    searchParam.value.cityCode = [n[1] || n[0]]
  } else {
    searchParam.value.cityCode = undefined
  }
});

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="restaurantName">餐厅名称:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="餐厅名称" v-model:value="searchParam.restaurantName"  style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="settleCode">对账单号:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="对账单号" v-model:value="searchParam.settleCode"  style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="applicationTimes">申请时间:</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="searchParam.applicationTimes" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="payTime">支付时间:</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="searchParam.payTime" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>

        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="settleTimes">对账时间:</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="searchParam.settleTimes" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>


          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="settleTimes">预订单时间:</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="searchParam.createTimes" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>


        </h-row>





        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button  style="margin-right: 10px" type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>

            <h-button type="primary" :loading="exportListLoading" @click="exportListApiRun(searchParam);">
              导出
            </h-button>
          </h-col>
        </h-row>


        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record,index }">
            <template v-if="column.dataIndex === 'index'">
              {{ index + 1 }}
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="goToDetail(record.restaurantName)">订单核对</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    

  </div>
</template>

<style scoped lang="less">

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

</style>
