import type { Editable, Result } from "@/api/types"
import { ref } from "vue"
import { message } from 'ant-design-vue'
import type { Ref } from 'vue'

export const useEditDialog = <T extends { id: number | null }, R>(
    api: Editable<R>,
    modelLabel = "",
    fetchData: () => void
) => {
    const visible = ref(false)
    const editData = <Ref<null | T>>ref(null)

    const handleCreate = () => {
        visible.value = true
    }

    const handleEdit = (item: any) => {
        editData.value = item;
        visible.value = true;
    };

    const handleOk = (data: R, callback: () => {}) => {
        if (editData.value && editData.value.id) {
            api.edit(data).then((res: Result) => {
                if (res) {
                    message.success(`${modelLabel}编辑成功`);
                    onDialogClose();
                    fetchData()
                } else {
                    message.error(res.msg);
                }
                callback()
            })
        } else {
            api.create(data).then((res: Result) => {
                if (res) {
                    message.success(`${modelLabel}创建成功`);
                    onDialogClose();
                    fetchData()
                } else {
                    message.error(res.msg);
                }
                callback()
            });
        }

    }

    const onDialogClose = () => {
        visible.value = false
        editData.value = null
    }

    return {
        visible,
        editData,
        handleCreate,
        handleEdit,
        onDialogClose,
        handleOk,
    }

}