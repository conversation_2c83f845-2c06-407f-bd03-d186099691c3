<script setup lang="ts">
import {
  theme,
  ConfigProvider as hConfigProvider,
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  Switch as hSwitch,
  CheckboxGroup as hCheckboxGroup,
  Checkbox as hCheckbox,
  Image as hImage,
  Radio as hRadio,
  RadioGroup as hRadiGroup,
  Tabs as hTabs,
  TabPane as hTabPane,
  message,
  notification,
  Button,
  Table as hTable,
  Modal,
  Popover as hPopover,
} from 'ant-design-vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
dayjs.locale('zh-cn');
import { computed, ref, onMounted, watch, reactive, h, Directive, DirectiveBinding,createVNode } from 'vue';
import { useSocketStore } from '@haierbusiness-front/utils/src/store/socket';
import {
  DisconnectOutlined,
  PhoneOutlined,
  DownOutlined,
  RightOutlined,
  LeftOutlined,
  LoginOutlined,
  SwapOutlined,
  ExclamationCircleOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons-vue';
import { callHistoryApi } from '@haierbusiness-front/apis';
import { callCenterApi } from '@haierbusiness-front/apis';
import holdImg from '../../../assets/image/call/hold.png';
import free from '../../../assets/image/call/free.png';
import nofree from '../../../assets/image/call/nofree.png';
import out from '../../../assets/image/call/out.png';
import waihu from '../../../assets/image/call/waihu.png';
import incomingCallSrc from '../../../assets/mp3/callReminder.mp3';
import { DataType, usePagination, useRequest } from 'vue-request';
import EditDialog from '../dialog/edit-dialog.vue';
import EditDetailDialog from '../dialog/workOrderDetail.vue';
import CallHistory from '../dialog/callHistory.vue';
import AddDetail from '../dialog/addDetail.vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
const { loginUser } = storeToRefs(applicationStore(globalPinia));
import type { Rule } from 'ant-design-vue/es/form';
const socket = useSocketStore();
const healthUrl = import.meta.env.VITE_HEALTH_PORT;
const websocketUrl = import.meta.env.VITE_WEBSOCKET_URL;
const password = import.meta.env.VITE_PASSWORD
const vite_call = import.meta.env.VITE_CALL
const outcall = import.meta.env.VITE_OUTCALL_PREFIX
import type { DrawerProps } from 'ant-design-vue';
const placement = ref<DrawerProps['placement']>('left');
const vMove: Directive<any, void> = (el: HTMLElement, binding: DirectiveBinding) => {
  let moveElement: HTMLDivElement = el as HTMLDivElement;
  console.log('moveElement:', moveElement.className);
  const mouseDown = (e: MouseEvent) => {
    let x = e.clientX - el.offsetLeft;
    let y = e.clientY - el.offsetTop;
    const move = (e: MouseEvent) => {
      // 设置边界的最小值
      let curX = e.clientX - x < 0 ? 0 : e.clientX - x;
      let curY = e.clientY - y < 0 ? 0 : e.clientY - y;
      // 设置边界的最大值 300 为div盒子的宽度 高度
      // curX curY 为 0 时 证明已经到边界最小值，就不需要在判断
      if (curX > 0) {
        curX = e.clientX - x > window.innerWidth - el.offsetWidth ? window.innerWidth - el.offsetWidth : e.clientX - x;
      }
      if (curY > 0) {
        curY = e.clientY - y > window.innerHeight - 120 ? window.innerHeight - 120 : e.clientY - y;
      }
      el.style.left = curX + 'px';
      el.style.top = curY + 'px';
    };
    // 监听 鼠标移动事件
    document.addEventListener('mousemove', move);
    document.addEventListener('mouseup', () => {
      document.removeEventListener('mousemove', move);
    });
  };
  // 监听 鼠标按下事件
  moveElement.addEventListener('mousedown', mouseDown);
};
// socket.healthCheck(healthUrl)
// 坐席号
const seatNumber = ref<number>();
const normalSeating = ref<string>("26909");
const outboundCallSeat = ref<string>("26959");
// 客服人员姓名工号
const personName = ref<string>('');
const personNum = ref<string>('');
// 签入
const from = ref();
const form = ref();
const fixedBox = ref();
// 输入的电话号码
const phoneNumber = ref<string>('');
// 是否展示来电显示
const showCallBox = ref<boolean>(false);
// 当前呼入的电话
const caller = ref<string>('');
// 当前呼入的callid
const callid = ref<string>('');
// 是否是通话中
const isCalling = ref<boolean>(false);
// 接听loading
const loading = ref<boolean>(false);
// 保持
const isHolding = ref<boolean>(false);
// 保持的数据
const holdObj = reactive({
  isHolding: false,
  caller: '',
  callid: '',
  callName:'',
  callPersonNum:''
});
// 当前是否是外呼
const isCallOut = ref<boolean>(false);
// 当前外呼的电话号码
const callOuter = ref<string>('');
// 是否是示忙状态
const isBusyStatus = ref<boolean>(false);
// 收起展开
const phoneBoxOpen = ref<boolean>(true);
// 创建工单框
const worderOpen = ref<boolean>(false);
// 创建工单表单
const workOrderForm = ref<any>({
  callId: '',
  personNum: '',
  personName: '',
  businessNum: '',
  mobile: '',
});
// 最小化来电
const miniShowBox = ref<boolean>(false);
const historyLoading = ref<boolean>(false);
// 呼叫号码
const callNumber = ref<string>('');
const toCallBox = ref<boolean>(false);
// 是否显示创建工单弹窗
const visible = ref<boolean>(false);
const detailVisible = ref<boolean>(false);
const editDetail = ref<any>({});
// 通讯录列表
const addressBook = ref<any>({});
const selectAddress = ref<string>('');
// 内部外部
const callType = ref<number>(1);
// 来电信息
const fromCallInfo = ref<any>({});
// 通话记录弹窗
const callHistoryVisible = ref<boolean>(false);
// 新增明细
const showAdd = ref<boolean>(false);
// 当前需要关联的工单
const isRelatedRworkOrderForm = ref<any>({});
// 当前需要关联跟工单的通话
const isRelatedCallHistory = ref<any>({
  userVo: {},
});
// 铃声播放
const incomingCallRef = ref()
const audioShow = ref<boolean>(false)
const busyRef = ref()
// 示忙原因弹窗
const toBusyBox = ref<boolean>(false)
// 示忙原因
const busyForm = reactive<object>(
  {busyReason:''}
)
// 外呼弹窗
const callKey = ref('1');
// 默认展开的列表
const activeKey = ref<any>([])
// 判断有没有接听
const isAnswer = ref<boolean>(false)
// 按键弹窗
const keyboardBoxShow = ref<boolean>(false)


const active = ref<any>(null);
const columns = [
  {
    title: '姓名',
    dataIndex: 'bookName',
  },
  {
    title: '手机/座机号',
    dataIndex: 'bookMobile',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '50px',
  },
];
const columnsForHistory = [
  {
    title: '手机/座机号',
    dataIndex: 'phone',
  },
  {
    title: '姓名',
    dataIndex: 'user',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '50px',
  },
];
const columnsforWork = [
  {
    title: '处理人',
    dataIndex: 'createName',
    width: '80px',
  },
  {
    title: '处理人工号',
    dataIndex: 'createBy',
    width: '120px',
  },
  {
    title: '处理明细',
    dataIndex: 'detailsDesc',
    width: '250px',
  },
  {
    title: '处理时间',
    dataIndex: 'gmtCreate',
    width: '100',
  }
];
const rules: Record<string, Rule[]> = {
  mobile: [{ required: true, message: '请输入来电号码' }],
  personNum: [{ required: true, message: '请输入处理人工号' }],
  personName: [{ required: true, message: '请输入处理人姓名' }],
  businessNum: [{ required: true, message: '请输入业务单号' }],
  workName: [{ required: true, message: '请输入工单名称' }],
  questionDesc: [{ required: true, message: '请输入问题描述' }],
};
const busyFormRules : Record<string, Rule[]> = {
  busyReason: [{ required: true, message: '请选择示忙原因' }],
};
watch(
  () => socket.msg,
  (newValue: any) => {
    if (newValue) {
      if (newValue.msgType == 'event') {
        // 如果返回的是时间
        console.log(newValue, '监听消息');
        // 签入成功示闲状态
        if (newValue.subType == 'AgentState_Ready') {
        }
        switch (newValue.subType) {
          case 'AgentState_Ready':
            showCallBox.value = false;
            audioShow.value = false
            isCalling.value = false;
            keyboardBoxShow.value = false
            break;
          case 'AgentEvent_Call_Release':
            showCallBox.value = false;
            audioShow.value = false
            isCalling.value = false;
            miniShowBox.value = false;
            keyboardBoxShow.value = false
            if(!isAnswer.value){
              if(newValue.msgContent.content == callid.value){
                openMissedCallNotification(`${caller.value} ${fromCallInfo.value.callName?fromCallInfo.value.callName:''} ${fromCallInfo.value.callPersonNum?fromCallInfo.value.callPersonNum:''}`)
                isAnswer.value = false
              }
            }else{
              if(newValue.msgContent.content == callid.value){
                isAnswer.value = false
              }
            }
            break;
          case 'AgentEvent_Incoming_CallInfo':
          // 接听录音
          break;
          case 'AgentEvent_Ringing':
            showCallBox.value = true;
            audioShow.value = true
            isCallOut.value = false;
            loading.value = false;
            setTimeout(()=>{
              incomingCallRef.value.src = incomingCallSrc
              incomingCallRef.value.play()
            },100)
            break;
          case 'AgentEvent_Call_Out_Fail0':
          case 'AgentEvent_Connect_Fail':
            isCallOut.value = false;
            break;
          // 内部呼叫失败
          case 'AgentEvent_Inside_Call_Fail':
            isCallOut.value = false;
            break;
          // 无人接听 AgentEvent_No_Answer
          case 'AgentEvent_No_Answer':
            showCallBox.value = false;
            isCalling.value = false;
            audioShow.value = false
            if(!isAnswer.value){
              openMissedCallNotification(`${caller.value} ${fromCallInfo.value.callName?fromCallInfo.value.callName:''} ${fromCallInfo.value.callPersonNum?fromCallInfo.value.callPersonNum:''}`)
            }
            isAnswer.value = false
            break;
          // 客户振铃事件
          case 'AgentEvent_Customer_Alerting':
            // 去掉前缀9 769 7690
            if (newValue.msgContent.content.otherPhone.charAt(0) == '9' && newValue.msgContent.content.otherPhone.length>8) {
              caller.value = newValue.msgContent.content.otherPhone.slice(1);
            }else if(newValue.msgContent.content.otherPhone.substring(0, 5)=='76900'){
              caller.value = newValue.msgContent.content.otherPhone.slice(5);
            }else if(newValue.msgContent.content.otherPhone.substring(0, 4)=='7690'){
              caller.value = newValue.msgContent.content.otherPhone.slice(4);
            }else if(newValue.msgContent.content.otherPhone.substring(0, 3)=='769'){
              caller.value = newValue.msgContent.content.otherPhone.slice(3);
            } else {
              caller.value = newValue.msgContent.content.otherPhone;
              showAutoNotification(caller.value)
            }
            callid.value = newValue.msgContent.content.callid;
            toCallBox.value = false;
            getCallHistoryGetVoInfo(caller.value);
            break;
          case 'AgentEvent_Talking':
          case 'AgentMediaEvent_Record':
            showCallBox.value = true;
            isCallOut.value = false;
            isCalling.value = true;
            loading.value = false;
            if(newValue.msgContent.content.feature==0){
              caller.value =  newValue.msgContent.content.caller
            }else if (newValue.msgContent.content.feature==7){
              if (newValue.msgContent.content.called.charAt(0) == '9' && newValue.msgContent.content.called.length>8) {
                caller.value = newValue.msgContent.content.called.slice(1);
              }else if(newValue.msgContent.content.called.substring(0, 5)=='76900'){
                caller.value = newValue.msgContent.content.called.slice(5);
              }else if(newValue.msgContent.content.called.substring(0, 4)=='7690'){
                caller.value = newValue.msgContent.content.called.slice(4);
              }else if(newValue.msgContent.content.called.substring(0, 3)=='769'){
              caller.value = newValue.msgContent.content.called.slice(3);
            } else {
                caller.value = newValue.msgContent.content.called;
              }
            }
            getCallHistoryGetVoInfo(caller.value);
            break;
          // 挂断通话
          case 'AgentEvent_Call_Release':
          case 'AgentEvent_Customer_Release':
            // 保持中的挂断
            if (newValue.msgContent.content.callid == holdObj.callid) {
              holdObj.isHolding = false;
              holdObj.caller = '';
              holdObj.callid = '';
              holdObj.callName=''
              holdObj.callPersonNum=''
            }
            // 是否有历史未闭环的工单
            if (fromCallInfo.value?.workHistoryOrderVo&&fromCallInfo.value?.workHistoryOrderVo.length) {
              isRelatedRworkOrderForm.value = fromCallInfo.value?.workHistoryOrderVo[0];
              isRelatedCallHistory.value.callid = newValue.msgContent.content.callid;
              isRelatedCallHistory.value.callerno = newValue.msgContent.content.number;
              isRelatedCallHistory.value.userVo.username = fromCallInfo.value.callName;
              isRelatedCallHistory.value.userVo.userNum = fromCallInfo.value.callPersonNum;
              openNotification();
            }
            fromCallInfo.value = {}
            break;
          case 'AgentEvent_Hold':
            // 保持
            holdObj.isHolding = true;
            holdObj.caller = caller.value;
            holdObj.callid = newValue.msgContent.content;
            holdObj.callName = fromCallInfo.value.callName
            holdObj.callPersonNum = fromCallInfo.value.callPersonNum
            isCalling.value = false;
            showCallBox.value = false;
            audioShow.value = false
            loading.value = false;
            break;
          // 三方通话成功
          case 'AgentEvent_Conference':
            holdObj.isHolding = false;
            holdObj.caller = '';
            holdObj.callid = '';
            holdObj.callName=''
            holdObj.callPersonNum=''
            let strArr = newValue.msgContent.content.partner.split(",")
            let phone:any = []
            strArr.forEach((item:any)=>{
              if (item.charAt(0) == '9'&&item.length>8) {
                phone.push(item.slice(1))
              }else if(item.substring(0, 5)=='76900'){
                phone.push(item.slice(5))
              }else if(item.substring(0, 4)=='7690'){
                phone.push(item.slice(4))
              }else if(item.substring(0, 3)=='769'){
                phone.push(item.slice(3))
              } else{
                phone.push(item)
              }
            })
            caller.value = phone.toString()
            getCallHistoryGetVoInfo(phone[1]);
            break;
          // 示忙
          case 'AgentState_SetNotReady_Success':
            message.info('示忙成功！');
            isBusyStatus.value = true;
            // 如果原因为2 代表未接示忙自动给他示闲
            if(newValue.msgContent.content.reason == 2){
              // openMissedCallNotification(`${caller.value} ${fromCallInfo.value.callName?fromCallInfo.value.callName:''} ${fromCallInfo.value.callPersonNum?fromCallInfo.value.callPersonNum:''}`)
              // showLeisure(newValue.msgContent.content.reason)
            }
            const data:any= {
              seatId:seatNumber.value,
              state:'busy',
              busyReason:newValue.msgContent.content.reason,
            }
            leisureSave(data)
            break;
          // 示闲
          case 'AgentState_CancelNotReady_Success':
              message.info('示闲成功！');
            isBusyStatus.value = false;
            break;
          // 转接成功 
          case 'AgentEvent_SuccTransResult':
            // 返回结果为0转接成功
            if (newValue.msgContent.content.result == 0) {
              message.success('转接成功！');
            }
            break;
          // 外呼失败
          case 'AgentEvent_Call_Out_Fail':
            isCallOut.value = false;
            break;
          // 外呼失败挂机原因
          case 'OUTBOUND_DETECT_EVENT':
            // isCallOut.value = false;
            showMessage(newValue.msgContent.content.detect,newValue.msgContent.content.desc)
            break;
          // 坐席挂机
          case 'AgentOther_PhoneRelease':
            isCallOut.value = false;
            break;
          // 外呼成功
          case 'voicecall/callout':
            if (newValue.msgContent.called.charAt(0) == '9'&& newValue.msgContent.called.length>8) {
              caller.value = newValue.msgContent.called.slice(1);
            }else if(newValue.msgContent.called.substring(0, 5)=='76900') {
              caller.value = newValue.msgContent.called.slice(5);
            }else if(newValue.msgContent.called.substring(0, 4)=='7690') {
              caller.value = newValue.msgContent.called.slice(4);
            }else if(newValue.msgContent.called.substring(0, 3)=='769') {
              caller.value = newValue.msgContent.called.slice(3);
            }else {
              caller.value = newValue.msgContent.called;
            }
            toCallBox.value = false;
            getCallHistoryGetVoInfo(caller.value);
            isCallOut.value = true;
            break;
          // 坐席签出
          case "AgentOther_ShutdownService":
            socket.lockReconnect = false;
            socket.phoneNumber = '';
            socket.seatNumber = ''
            location.reload();
            break
        }
      } else if (newValue.msgType == 'response') {
        if (newValue.msgContent.retcode != 0) {
          if(newValue.msgContent.retcode == '000-003'){
            message.error('坐席签入失效请重新签入');
            location.reload();
          }else if(newValue.msgContent.retcode == '100-006'){
            message.error('坐席签入失效请重新签入');
            location.reload();
          }else{
            message.error('系统异常：' + newValue.msgContent.message);
          }
        }
        switch (newValue.subType) {
          case 'heartbeat':
          socket.heartCheck()
          break;
          case 'onlineagent/syncagentinfo':
          socket.heartCheck()
          break;
          // 取消保持
          case 'voicecall/gethold':
            if (newValue.msgContent.retcode == 0) {
              message.success('取消保持成功');
              holdObj.isHolding = false;
              holdObj.caller = '';
              holdObj.callid = '';
              holdObj.callName=''
              holdObj.callPersonNum=''
              toCallBox.value = false
            }
            break;
          // 外呼成功
          case 'voicecall/callout':
            if (newValue.msgContent.retcode == 0) {
              isCallOut.value = true;
              isAnswer.value = true
            }
            break;
          // 外呼成功
          case 'voicecall/callinner':
            if (newValue.msgContent.retcode == 0) {
              isCallOut.value = true;
              toCallBox.value = false;
              getCallHistoryGetVoInfo(callNumber.value);
            }
            break;
          // 获取录音下载token
          case 'record/token':
            token.value = newValue.msgContent.result.token;
            break;
          // 退出登录
          case 'onlineagent/forcelogout':
            socket.lockReconnect = false;
            socket.phoneNumber = '';
            socket.seatNumber = ''
            location.reload();
            break;
          // 登录
          case 'onlineagent/forcelogin':
            socket.phoneNumber = phoneNumber.value;
            socket.seatNumber = seatNumber.value
            // 开启心跳检测
            socket.heartCheck();
            break;
          // 登录
          case 'onlineagent/login':
            if (newValue.msgContent.retcode == '100-002') {
              message.error('坐席已签入');
            } else {
              socket.phoneNumber = phoneNumber.value;
              socket.seatNumber = seatNumber.value
              // 开启心跳检测
              socket.heartCheck();
            }
            break;
          // 同步信息
          case 'onlineagent/syncagentinfo':
            // 代表坐席被踢出（断网）
            if (newValue.msgContent.message == 'Agent not exist in cache, maybe not login') {
              location.reload();
            }
            break;
        }
      }else if (newValue.msgType == 'error'){
        if(newValue.msgContent.retcode == '000-003'){
          message.error('坐席签入失效请重新签入');
          location.reload();
        }
      }
    }
  },
);
watch(
  () => showCallBox.value,
  (newValue: any) => {
    // fromCallInfo.value = {}
    if (newValue) {
      miniShowBox.value = false;
      phoneBoxOpen.value = false;
      // minimizeBox()
    } else {
      // fromCallInfo.value = {};
      // minimizeBox()
    }
  },
);

watch(
  () => keyboardBoxShow.value,
  (newValue: any) => {
    // fromCallInfo.value = {}
    if (newValue) {
      dialButtonNumberString.value = ''
    } else {
      dialButtonNumberString.value = ''
    }
  },
);

watch(
  () => isBusyStatus.value,
  (newValue: any) => {
    socket.isBusyStatus = newValue
  },
);

watch(
  () => audioShow.value,
  (newValue: any) => {
    if (newValue) {
      // 
    } else {
      incomingCallRef.value.pause();
      incomingCallRef.value.currentTime = 0;
    }
  },
);

watch(
  () => callKey.value,
  (newValue: any) => {
    if (newValue==2) {
        getaddressBookList({ current: 1, pageSize: 10 })
    }
    if(newValue==3){
        listApiRun({
          pageNum: 1,
          pageSize: 10,
        });
    }
  },
);

const showMessage = (val:any,desc:string) =>{
  if(val==1138){
    message.error('外呼失败：被叫挂机')
  }else if(val==715){
    message.error('外呼失败：呼叫超时')
  }else if(val==1163){
    message.error('外呼失败：空号或错号')
  }else if(val==1164){
    message.error('外呼失败：被叫忙')
  }else if(val==1165){
    message.error('外呼失败：手机关机')
  }else if(val==1168){
    message.error('外呼失败：用户无响应')
  }else if(val==1169){
    message.error('外呼失败：用户拒绝')
  }else{
    message.error('外呼失败：'+desc)
  }
}

const tominiShowBox = () =>{
  miniShowBox.value = true
  audioShow.value = false
  setTimeout(() => {
    fixedBox.value.style.left = window.innerWidth - fixedBox.value.offsetWidth + 'px';
  }, 1);
}
// 挂断通话
const hangUpCall = () => {};

// 根据登录人工号获取 坐席
const getSeatInfo = () => {
  callCenterApi
    .AgentCustomerList({ personNum: loginUser.value?.username, pageNum: 1, pageSize: 1 })
    .then((res: any) => {
      if (res.list.length) {
        seatNumber.value = res.list[0].agentId;
        // 普通坐席
        normalSeating.value = res.list[0].agentId;
        // 外呼坐席
        outboundCallSeat.value = res.list[0].outboundNumber

        phoneNumber.value = res.list[0].agentNum;
        personName.value = res.list[0].personName;
        personNum.value = res.list[0].personNum;
        var url = websocketUrl + seatNumber.value;
        if(socket.seatNumber){
          url = websocketUrl + socket.seatNumber;
          seatNumber.value = socket.seatNumber
        }
        if (socket.lockReconnect && socket.phoneNumber) {
          phoneNumber.value = socket.phoneNumber;
          socket.connection(url, toSignInpro, socket.phoneNumber);
          Modal.info({
            title: '由于您的浏览器设置，声音无法自动播放，请点击确认按钮授权播放',
            okText:'确定'
          });
        }
        // else {
        //   socket.connection(url);
        // }
      } else {
        // message.error('当前登录账号未设置坐席');
      }
    });
};

// 切换坐席号
const switchSeatNumber = ()=>{
  Modal.confirm({
    title:normalSeating.value== seatNumber.value?'确定切换为外呼坐席吗？':'确定切换为正常坐席吗？',
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {
      // console.log('OK');
      if(normalSeating.value== seatNumber.value){
        seatNumber.value = outboundCallSeat.value
      }else{
        seatNumber.value = normalSeating.value
      }
      message.success("切换成功")
    },
    onCancel() {
      console.log('Cancel');
    },
    class: 'test',
  });
}

// 来电页面查询信息
const getCallHistoryGetVoInfo = (mobile: string) => {
  if(!fromCallInfo.value?.callName){
    historyLoading.value = true;
  }
  callCenterApi
    .CallHistoryGetVoInfo({ mobile })
    .then((res: any) => {
      fromCallInfo.value = res;
      activeKey.value = []
      if(fromCallInfo.value.workHistoryOrderVo){
      fromCallInfo.value.workHistoryOrderVo.forEach((item:any,index:number)=>{
        activeKey.value.push(index)
      })
      }
      historyLoading.value = false;
    })
    .catch(() => {
      historyLoading.value = false;
    });
};

// 签入
const toSignIn = (seat:any) => {
  seatNumber.value = seat
  if (phoneNumber.value) {
    if (socket.lockReconnect) {
      toSignInpro(phoneNumber.value);
    } else {
      // var url = websocketUrl + seatNumber.value;
      var url = websocketUrl + seat
      socket.connection(url, toSignInpro, phoneNumber.value);
    }
    // 第一次加载音频资源 解决第一次打开浏览器签入之后不响铃的问题
    audioShow.value = true
    setTimeout(()=>{
        incomingCallRef.value.pause();
        incomingCallRef.value.currentTime = 0;
        audioShow.value = false
    },50)
  } else {
    message.info('当前登录账号未设置坐席！');
  }
  //   socket.sendMsg({
  //     msgType: 'operation',
  //     subType: 'onlineagent/login',
  //     version: 'V1',
  //     sequenceNo: getRandomCode(32),
  //     msgContent: { password: '2wsx@WSX', phonenum: '7102', autoanswer: false },
  //   });
};

// 获取呼叫信息
const getCallinfobycallid = () => {
  socket.sendMsg({
    msgType: 'operation',
    subType: 'calldata/callinfobycallid',
    version: 'V1',
    sequenceNo: getRandomCode(32),
    msgContent: { callid: '1712733746-2774' },
  });
};

// 强制签入
const toSignInpro = (phoneNumber: string) => {
  socket.sendMsg({
    msgType: 'operation',
    subType: 'onlineagent/forcelogin',
    version: 'V1',
    sequenceNo: getRandomCode(32),
    msgContent: { password:password, phonenum: phoneNumber, autoanswer: false },
  });
  ResetSkillProcessor();
  if(socket.isBusyStatus){
      socket.sendMsg({
      msgType: 'operation',
      subType: 'onlineagent/saybusy',
      version: 'V1',
      sequenceNo: getRandomCode(32),
      msgContent: { reason: '200', prolong: 'false' },
    });
  }
};

// 签出
const toSignOut = () => {
  Modal.confirm({
    title: '签出之后您将无法接听电话，确定要签出吗?',
    zIndex:3000,
    onOk() {
      socket.sendMsg({
        msgType: 'operation',
        subType: 'onlineagent/forcelogout',
        version: 'V1',
        sequenceNo: getRandomCode(32),
        msgContent: { agentid: seatNumber.value },
      });
      socket.lockReconnect = false;
    },
    onCancel() {
      console.log('Cancel');
    },
    class: 'test',
  });
};

// 重置技能列队
const ResetSkillProcessor = () => {
  socket.sendMsg({
    msgType: 'operation',
    subType: 'onlineagent/resetskill',
    version: 'V1',
    sequenceNo: getRandomCode(32),
    msgContent: { autoflag: true, phonelinkage: 0 },
  });
};

// 同步作息状态 和  通话信息
const SyncAgentInfoProcessor = () => {
  socket.sendMsg({
    msgType: 'operation',
    subType: 'onlineagent/syncagentinfo',
    version: 'V1',
    sequenceNo: getRandomCode(32),
    msgContent: {
      password: password,
    },
  });
};

// 呼叫应答
const CallAnswerProcessor = () => {
  audioShow.value = false
  // 接听过
  isAnswer.value = true
  socket.sendMsg({
    msgType: 'operation',
    subType: 'voicecall/answer',
    version: 'V1',
    sequenceNo: getRandomCode(32),
    msgContent: { callid: '' },
  });
  loading.value = true;
};

// 序列码
const getRandomCode = (length: number) => {
  return Number(Math.random().toString().substr(3, length) + Date.now()).toString(36);
};

// 获取当前坐席状态
const getAgentstatus = () => {
  socket.sendMsg({
    msgType: 'operation',
    subType: 'onlineagent/agentstatus',
    version: 'V1',
    sequenceNo: getRandomCode(32),
    msgContent: {},
  });
};

// 拒接来话
const RefuseIncomingCalls = () => {
  socket.sendMsg({
    msgType: 'operation',
    subType: 'voicecall/reject',
    version: 'V1',
    sequenceNo: getRandomCode(32),
    msgContent: { callid: callid.value, mode: '2' },
  });
};

// 挂断呼叫
const unCall = () => {
  socket.sendMsg({
    msgType: 'operation',
    subType: 'voicecall/release',
    version: 'V1',
    sequenceNo: getRandomCode(32),
    msgContent: {},
  });
};

const unReadyCall = () => {
  socket.sendMsg({
    msgType: 'operation',
    subType: 'voicecall/dropcall',
    version: 'V1',
    sequenceNo: getRandomCode(32),
    msgContent: { callid: callid.value },
  });
};


// 取消保持
const gethold = () => {
  socket.sendMsg({
    msgType: 'operation',
    subType: 'voicecall/gethold',
    version: 'V1',
    sequenceNo: getRandomCode(32),
    msgContent: { callid: holdObj.callid },
  });
};

// 示忙
const ShowBusyness = () => {
  busyRef.value
    .validate()
    .then(() => {
      socket.sendMsg({
        msgType: 'operation',
        subType: 'onlineagent/saybusy',
        version: 'V1',
        sequenceNo: getRandomCode(32),
        msgContent: { reason: busyForm.busyReason, prolong: 'false' },
      });
      const data:any= {
        seatId:seatNumber.value,
        state:'busy',
        busyReason:busyForm.busyReason,
      }
      leisureSave(data)
      toBusyBox.value = false
    })
    .catch(() => {});
};

// 示闲
const showLeisure = () => {
  socket.sendMsg({
    msgType: 'operation',
    subType: 'onlineagent/sayfree',
    version: 'V1',
    sequenceNo: getRandomCode(32),
    msgContent: {},
  });
  const data:any= {
    seatId:seatNumber.value,
    state:'leisure',
    busyReason:"",
  }
  leisureSave(data)
};

// 示忙示闲后端接口保存
const leisureSave = (data:object) =>{
  callCenterApi.leisureSave(data).then((res: any) => {
    addressBook.value = res;
  });
} 

// 呼叫保持
const callHold = () => {
  socket.sendMsg({
    msgType: 'operation',
    subType: 'voicecall/hold',
    version: 'V1',
    sequenceNo: getRandomCode(32),
    msgContent: {},
  });
};

// 评价
const evaluate = () => {
  socket.sendMsg({
    msgType: 'operation',
    subType: 'voicecall/transfer',
    version: 'V1',
    sequenceNo: getRandomCode(32),
    msgContent: {
      devicetype: 3,
      mode: 0,
      address: '80030',
      mediaability: 0,
      caller: '',
      holdflag: 'true',
      callappdata: '{\\"hicaccount\\":\\"' + seatNumber.value + '\\"}',
    },
  });
};

// 选择转接人员弹窗
const switchSeatShow = ref<boolean>(false)
const seatList = ref<any>(false)
const seatListcolumns = [
  {
    title: '坐席ID',
    dataIndex: 'agentId',
  },
  {
    title: '工号',
    dataIndex: 'personNum',
  },
  {
    title: '姓名',
    dataIndex: 'personName',
  },
  {
    title: '产品线',
    dataIndex: 'productName',
  },
  {
    title: '操作',
    dataIndex: 'operation',
  },
];

// 点击转接
const switchSeatOpen = () =>{
  switchSeatShow.value = true
  const obj = Object.assign({ pageNum: 1, pageSize: 9999 });
  // 排除自己的坐席
  callCenterApi.AgentCustomerList(obj).then((res: any) => {
    seatList.value = res.list.filter((item:any)=>{
      return item.personNum !=  loginUser.value?.username
    })
  });
}

// 转接
const switchSeat = (seat:string) => {
  switchSeatShow.value = false
  socket.sendMsg({
    msgType: 'operation',
    subType: 'voicecall/transfer',
    version: 'V1',
    sequenceNo: getRandomCode(2),
    msgContent: {
      devicetype: 2,
      mode: 2,
      address: seat,
      mediaability: 0,
      caller: '',
      holdflag: 'true',
      callappdata: '{\\"hicaccount\\":\\"' + seatNumber.value + '\\"}',
    },
  });
}

// 普通外呼
const callOut = () => {
  fromCallInfo.value = {};

    if(Number(callNumber.value)>=26900&&Number(callNumber.value)<=26920){
      if(callNumber.value==seatNumber.value){
        message.error('请勿呼叫当前坐席')
        return
      }
      // 内部呼叫
      socket.sendMsg({
        msgType: 'operation',
        subType: 'voicecall/callinner',
        version: 'V1',
        sequenceNo: getRandomCode(32),
        msgContent: { called: callNumber.value },
      });
      caller.value = callNumber.value
    }else{
      // message.error('请输入正确的手机电话号码或者坐席号')
      // 外部呼叫
      socket.sendMsg({
        msgType: 'operation',
        subType: 'voicecall/callout',
        version: 'V1',
        sequenceNo: getRandomCode(32),
        msgContent: { called: Number(callNumber.value)>10000? outcall + callNumber.value :'769' + callNumber.value, mediaability: '0', caller: vite_call },
      });
      caller.value = callNumber.value
    }

};

// 三方通話
const confJoin = () => {
  socket.sendMsg({
    msgType: 'operation',
    subType: 'voicecall/confjoin',
    version: 'V1',
    sequenceNo: getRandomCode(32),
    msgContent: { callid: holdObj.callid },
  });
};
const dialButtonNumber = ref('')
const dialButtonNumberString = ref<string>('')

// 二次拨号按键
const dialButton = () => {
  if(dialButtonNumber.value){
    socket.sendMsg({
    msgType: 'operation',
    subType: 'voicecall/seconddialex',
    version: 'V1',
    sequenceNo: getRandomCode(32),
    msgContent: { number: dialButtonNumber.value },
    });
  }else{
    message.error('按键号码不能为空')
  }
  dialButtonNumberString.value = dialButtonNumberString.value + dialButtonNumber.value
  dialButtonNumber.value = ""
};

// 点击创建工单
const createWorkOrder = () => {
  // workOrderForm.value.callid = callid.value;
  // workOrderForm.value.mobile = caller.value;
  detailVisible.value = true;
};

// 关闭创建工单弹窗
const onClose = () => {
  visible.value = false;
};

const saveWorkOrder = (res: any) => {
  visible.value = false;
  editDetail.value = res;
  detailVisible.value = true;
};
const onDialogClose = () => [(visible.value = false)];

const EditDetailDialogCancel = () => {
  detailVisible.value = false;
};

// 点击外呼
const toCall = () => {
  callNumber.value = '';
  selectAddress.value = '';
  callType.value = 1;
  toCallBox.value = true;
  callKey.value ="1"
  addressBook.value=[]
};

// 获取通讯录列表
const getaddressBookList = (pag: { current: number; pageSize: number }) => {
  callCenterApi.list({ pageNum:pag.current,pageSize:pag.pageSize }).then((res: any) => {
    addressBook.value = res.records;
    pageInfo.value.total= res.total;
    pageInfo.value.pageNum= res.pageNum
    pageInfo.value.pageSize= res.pageSize
  });
};

// 选择通讯录
const selectAddressBook = (value: string) => {
  callNumber.value = value;
};

// 查看通话记录
const showCallHistory = () => {
  callHistoryVisible.value = true;
};

// 取消通话记录弹窗
const callHistoryDialogCancel = () => {
  callHistoryVisible.value = false;
};

// 创建工单
const onCreateWorkOrder = () => {
  from.value
    .validate()
    .then(() => {
      callCenterApi.saveOrUpdateWorkOrder(workOrderForm.value).then((res) => {
        message.success('创建工单成功');
        onClose();
      });
    })
    .catch(() => {});
};

// 最小化浮窗
const minimizeBox = () => {
  phoneBoxOpen.value = !phoneBoxOpen.value;
  setTimeout(() => {
    fixedBox.value.style.left = window.innerWidth - fixedBox.value.offsetWidth + 'px';
  }, 1);
};

const toBusyBoxShow = () =>{
  busyForm.busyReason = ''
  toBusyBox.value = true
}

const token = ref();
const getRecord = () => {
  socket.sendMsg({
    msgType: 'operation',
    subType: 'record/downloadrecord',
    version: 'V1',
    sequenceNo: '0000',
    msgContent: {
      recordId: 'Y:/198/0/20210304/2100/1633082.V3',
      token: token.value,
    },
  });
};

const getRecordToken = () => {
  socket.sendMsg({
    msgType: 'operation',
    subType: 'record/token',
    version: 'V1',
    sequenceNo: getRandomCode(32),
    msgContent: {},
  });
};

const openNotification = () => {
  // 先关闭之前的 只保留一个
  const key = `glbox`;
  notification.close(key)
  notification.open({
    message: '未闭环工单',
    description: `当前用户有未闭环工单《${fromCallInfo.value?.workHistoryOrderVo[0].workName}》，点击关联工单并创建新的工单明细`,
    btn: () =>
      h(
        Button,
        {
          type: 'primary',
          size: 'small',
          onClick: () => {
            editDetail.value.id = isRelatedRworkOrderForm.value.id;
            detailVisible.value = true;
            notification.close(key);
          },
        },
        { default: () => '关联' },
      ),
    key,
    duration: 0,
  });
};

const openMissedCallNotification = (description:string) => {
  const key = `open${Date.now()}`;
  notification.open({
    message: '您有一个未接来电',
    description: description,
    key,
    duration: 0,
  });

};

const cancel = () => {
  showAdd.value = false;
};

const callUp = (value: any) => {
  if((value>=26900&&value<=26920)||(value>=36900&&value<=36920)){
    callNumber.value = value;
  }else{
    callNumber.value = value;
  }
  callKey.value = '1';
};

const {
  data,
  run: listApiRun,
  loading:callHistoryLoading,
  current,
  pageSize,
} = usePagination(callHistoryApi.list, {
  defaultParams: [{}],
  manual: true,
});

const pagination = computed(() => ({
  showSizeChanger: false,
  showQuickJumper: false,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));
const pageInfo = ref<any>({})
const pagination1 = computed(() => ({
  showSizeChanger: false,
  showQuickJumper: false,
  total: pageInfo.value?.total,
  current: pageInfo.value?.pageNum,
  pageSize: pageInfo.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  type: number | null,
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    type: type,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const dataSource = computed(
  () =>
    data.value?.records?.filter((item: any) => {
      return item;
    }) || [],
);

// 网页提醒
const showAutoNotification = (content: any) => {
  // const interval = 500; // 闪烁的间隔时间（毫秒）
  // let counter = 0; // 用于控制闪烁次数
  // let originalTitle = document.title; // 保存原始标题

  // // 设置一个定时器来实现标题的闪烁
  // const blinkIntervalId = setInterval(function() {
  //   if (counter++ % 2 === 0) {
  //     // 偶数次则设置为原始标题
  //     document.title = originalTitle;
  //   } else {
  //     // 奇数次则设置为带有提醒符号的标题
  //     document.title = '【未接来电】' + originalTitle;
  //   }
  // }, interval);
  // // 设置一个定时器在指定时间后停止闪烁
  // setTimeout(function() {
  //   clearInterval(blinkIntervalId);
  //   document.title = originalTitle; // 停止闪烁后恢复原始标题
  // }, 10000); // 闪烁持续时间，这里设置为10秒
  if ('Notification' in window) {
    Notification.requestPermission().then(function (permission) {
      if (permission === 'granted') {
        var Phonenotification = new Notification('话务中心提醒-您有来电需要接听', {
          body: content,
          requireInteraction:'true'
        });
        Phonenotification.onclick = function() {
            //可直接打开通知notification相关联的tab窗口
            window.focus();
        }
        setTimeout(function () {
          Phonenotification.close();
        }, 15000);
      } else {
        console.log('请打开网站通知，以便接收本站最新的消息。');
        console.log('当前浏览器禁止了网站推送');
      }
    });
  } else {
    console.log('当前浏览器不支持tuisong.js');
  }
};

onMounted(async () => {
  await getSeatInfo();
  // getAgentstatus();
  getaddressBookList({ current: 1, pageSize: 10 });
  // handleTableChange({ current: 1, pageSize: 10 }, 1);
});

</script>

<template>
  <div>
    <!-- 悬浮窗 -->
    <div
      v-move
      v-show="!showCallBox || miniShowBox"
      v-if="!isCallOut&&seatNumber"
      class="navigation"
      ref="fixedBox"
      :class="{ open: phoneBoxOpen, miniShowBox: miniShowBox, hold: holdObj.isHolding }"
    >
      <!-- 通话中最小化悬浮窗 -->
      <div class="callingBox" v-if="miniShowBox">
        <div style="width:55px;">
          <span class="icon" @click="miniShowBox = false" :style="{ color: '#000', 'font-weight': 900 }"
            ><i style="font-size: 13px" class="iconfont icon-waihu"></i></span
          ><span class="loadertonghuazhong"></span>
        </div>
        <div class="phoneCallerBox">
          <div class="phone">{{ caller }}</div>
          <div class="phone">{{ fromCallInfo.callName != '未知' ? fromCallInfo.callName : '' }}</div>
        </div>
        <span class="icon zuixiaohua" @click="miniShowBox = false" :style="{ color: '#000', 'font-weight': 900 }"
          ><i style="font-size: 23px" class="iconfont icon-zuidahuachuangkou"></i
        ></span>
      </div>
      <!-- 保持中的浮窗 -->
      <div class="holdBox" v-if="holdObj.isHolding">
        <div>
          <span class="icon" style="color: #000"
            ><i style="font-size: 16px" class="iconfont icon-tonghuabaochibeifen"></i
          ></span>
          保持中
        </div>
        <div class="phone">{{ holdObj.caller }}</div>
        <div class="phone">{{ holdObj.callName }}</div>
      </div>
      <div class="operaBox" v-if="holdObj.isHolding">
        <div
          class="operaBoxItem baochi"
          @click="gethold"
          style="--list-active-color: #8ae396"
        >
          <span class="icon"><i style="font-size:14px;color:#000;" class="iconfont icon-quxiaobaochi"></i></span>
          <span class="text">取消保持</span>
        </div>
        <div
          class="operaBoxItem waihu"
          @click="toCall"
          style="--list-active-color: #ffa117"
        >
          <span class="icon"><i style="font-size:14px;color:#000;" class="iconfont icon-waihu"></i></span>
          <span class="text">外呼</span>
        </div>
      </div>

      <div v-if="!miniShowBox && !holdObj.isHolding" class="peopleInfo">
        <p>
          <span class="icon" style="color: #000"><i style="font-size: 13px" class="iconfont icon-kefu"></i></span>
          {{ personName }} <span v-if="phoneBoxOpen">
            <span v-if="socket.lockReconnect && socket.phoneNumber">{{ seatNumber }}{{ seatNumber==normalSeating?'（工作）':'（外呼）' }}</span> 
            <span v-else>{{normalSeating}},{{ outboundCallSeat }} 
              <a-tooltip :overlayClassName="'tooltip'" autoAdjustOverflow placement="top">
                <template #title>
                  <span>{{ outboundCallSeat }}为外呼号码，签入之后不会收到来电</span>
                </template>
                <QuestionCircleOutlined />
              </a-tooltip></span>
          </span> 
          <!-- <a-tooltip :overlayClassName="'tooltip'" autoAdjustOverflow placement="top">
            <template #title>
              <span>切换坐席号</span>
            </template>
            <SwapOutlined @click="switchSeatNumber" class="twichicon" v-if="!socket.lockReconnect || !socket.phoneNumber"/>
          </a-tooltip> -->
        </p>
        <p
          v-if="socket.lockReconnect && socket.phoneNumber && !isBusyStatus"
          style="font-size: 12px; color: #1afa29; padding-left: 1px"
        >
          <span class="icon" style="color: #1afa29"
            ><i style="font-size: 12px" class="iconfont icon-zaixiankefu"></i
          ></span>
          在线
        </p>
        <p
          v-if="socket.lockReconnect && socket.phoneNumber && isBusyStatus"
          style="font-size: 12px; color: #f48888; padding-left: 1px"
        >
          <span class="icon" style="color: #f48888"><i style="font-size: 12px" class="iconfont icon-manglu"></i></span>
          忙碌中
        </p>
        <p v-if="!socket.lockReconnect || !socket.phoneNumber" style="font-size: 12px; color: #000; padding-left: 1px">
          <span class="icon" style="color: #000"><i style="font-size: 12px" class="iconfont icon-lixian"></i></span>
          未签入
        </p>
      </div>
      <ul v-if="socket.lockReconnect && socket.phoneNumber && !miniShowBox && !holdObj.isHolding">
        <li
          v-if="!isBusyStatus && !showCallBox && !holdObj.isHolding"
          :class="{ active: active == 1, activeMin: active == 1 && !phoneBoxOpen }"
          @mouseenter="active = 1"
          @mouseleave="active = null"
          @click="toBusyBoxShow"
          style="--list-active-color: #f44336"
        >
          <a href="#" @click="(e) => e.preventDefault()">
            <span class="icon"><i class="iconfont iconfontFixed icon-shimang"></i></span>
            <span class="text">示忙</span>
          </a>
        </li>
        <li
          v-if="isBusyStatus && !showCallBox && !holdObj.isHolding"
          :class="{ active: active == 1, activeMin: active == 1 && !phoneBoxOpen }"
          @mouseenter="active = 1"
          @mouseleave="active = null"
          @click="showLeisure"
          style="--list-active-color: #f44336"
        >
          <a href="#" @click="(e) => e.preventDefault()">
            <span class="icon"><i class="iconfont iconfontFixed  icon-shixian1"></i></span>
            <span class="text">示闲</span>
          </a>
        </li>
        <li
          v-if="!showCallBox && !holdObj.isHolding"
          :class="{ active: active == 2, activeMin: active == 2 && !phoneBoxOpen }"
          @mouseenter="active = 2"
          @mouseleave="active = null"
          @click="toCall"
          style="--list-active-color: #ffa117"
        >
          <a href="#" @click="(e) => e.preventDefault()">
            <span class="icon"><i class="iconfont iconfontFixed icon-waihu"></i></span>
            <span class="text">外呼</span>
          </a>
        </li>
        <li
          v-if="!showCallBox && !holdObj.isHolding"
          :class="{ active: active == 3, activeMin: active == 3 && !phoneBoxOpen }"
          @mouseenter="active = 3"
          @mouseleave="active = null"
          @click="createWorkOrder"
          style="--list-active-color: #0fc70f"
        >
          <a href="#" @click="(e) => e.preventDefault()">
            <span class="icon"><i class="iconfont iconfontFixed  icon-gongdan"></i></span>
            <span class="text">创建工单</span>
          </a>
        </li>
        <li
          v-if="!showCallBox && !holdObj.isHolding"
          :class="{ active: active == 4, activeMin: active == 4 && !phoneBoxOpen }"
          @mouseenter="active = 4"
          @mouseleave="active = null"
          @click="showCallHistory"
          style="--list-active-color: #2196f3"
        >
          <a href="#" @click="(e) => e.preventDefault()">
            <span class="icon"><i class="iconfont iconfontFixed icon-jilu"></i></span>
            <span class="text">通话记录</span>
          </a>
        </li>
        <li
          v-if="!showCallBox && !holdObj.isHolding"
          :class="{ active: active == 5, activeMin: active == 5 && !phoneBoxOpen }"
          @mouseenter="active = 5"
          @mouseleave="active = null"
          @click="toSignOut"
          style="--list-active-color: #b145e9"
        >
          <a href="#" @click="(e) => e.preventDefault()">
            <span class="icon"><i class="iconfont iconfontFixed icon-qianchu"></i></span>
            <span class="text">签出</span>
          </a>
        </li>
      </ul>
      <div class="toSignBox" v-if="!socket.lockReconnect || !socket.phoneNumber">
        <a-input disabled v-model:value="phoneNumber" placeholder="请输入分机号"> </a-input>
        <div class="loginBtn">
          <div  @click="toSignIn(normalSeating)" class="signBox">
            <LoginOutlined :style="{ fontSize: '28px' }" />
            <span>{{normalSeating}}(工作)</span>
          </div>
          <div  @click="toSignIn(outboundCallSeat)" class="signBox">
            <LoginOutlined :style="{ fontSize: '28px' }"  />
            <span>{{outboundCallSeat}}(外呼) </span>
          </div>
        </div>
      </div>
      <div @click="minimizeBox" v-if="!miniShowBox&&!holdObj.isHolding" class="openBox">
        <LeftOutlined v-if="!phoneBoxOpen" :style="{ fontSize: '16px', color: '#3b3b3b' }" />
        <RightOutlined v-else :style="{ fontSize: '16px', color: '#3b3b3b' }" />
      </div>
    </div>
    <!-- 来电弹窗 -->
    <a-modal
      v-if="!miniShowBox"
      :mask="false"
      :width="800"
      v-model:open="showCallBox"
      :closable="false"
      :maskClosable="false"
      zIndex="9000"
      title=""
      forceRender
    >
    <!-- 录音文件 -->
    <audio
      ref="incomingCallRef" 
      :src="incomingCallSrc" 
      autoplay
      v-if="audioShow"
      style="width:100%;height:0px;margin:0px 0;"
      controls="controls"
      />
      <span
        class="icon zuixiaohua"
        style="position: absolute;right: 0;"
        @click="tominiShowBox"
        v-if="isCalling"
        :style="{ color: '#000', 'font-weight': 900, float: 'right' }"
        ><i style="font-size: 26px; margin: 8px 16px 0 0" class="iconfont icon-chuangkouzuixiaohua"></i
      ></span>
      <div class="callBox">
        <span
          class="icon laiidan-icon"
          style="margin-bottom: 0"
          v-if="!isCalling"
          :style="{ color: 'blue', 'font-weight': 900 }"
          ><i style="font-size: 22px; margin: 8px 16px 0 0" class="iconfont icon-huru"></i
        ></span>
        {{ caller }} <span> &nbsp; {{ fromCallInfo.callName }} {{ fromCallInfo.callPersonNum }}</span>
        <div class="callState">
          <div v-if="isCalling">
            <div class="round green"></div>
            通话中
          </div>
          <div v-else>
            <div class="round red"></div>
            <div class="loaderdaijieting"></div>
          </div>
        </div>
      </div>
      <div class="callerInfo">
        <p style="font-weight:bold" v-if="holdObj.isHolding">
          已有保持中的通话：
          <span> {{ holdObj.caller }} {{ holdObj.callName }} {{ holdObj.callPersonNum }}</span>
        </p>
        <a-spin :spinning="historyLoading">
          <p>
            上次来电时间：<span v-if="fromCallInfo?.lastHistoryCallVo?.lastCallTime">
              {{ fromCallInfo?.lastHistoryCallVo?.lastCallTime }}
              <span v-if="fromCallInfo?.lastHistoryCallVo?.answerUserName">
                接听人({{
                  fromCallInfo?.lastHistoryCallVo?.answerUserName
                }})
              </span>
              <span v-else>
                未接听
              </span>
            </span>
          </p>
          <p v-if="fromCallInfo?.workHistoryOrderVo && fromCallInfo?.workHistoryOrderVo.length">
            历史未闭环的工单：{{
              fromCallInfo?.workHistoryOrderVo && fromCallInfo?.workHistoryOrderVo.length
                ? fromCallInfo?.workHistoryOrderVo[0].workTableNum
                : ''
            }}
          </p>
          <div v-if="fromCallInfo?.workHistoryOrderVo && fromCallInfo?.workHistoryOrderVo.length" style="max-height:290px;overflow:auto;">
            <a-collapse v-model:activeKey="activeKey">
              <a-collapse-panel
                v-for="(item,index) in fromCallInfo?.workHistoryOrderVo"
                :header="item.workTableNum + ' ' + item.workName"
                :key="index"
              >
              <h-table :pagination="false" size="small" :columns="columnsforWork" :data-source="item.detailsList" bordered>
                <template #emptyText>  暂无数据  </template>
                <template #bodyCell="{ column, text, record, index }">
                  <template v-if="column.dataIndex === 'detailsDesc'">
                    <div style="word-wrap: break-word;width:300px">
                      {{ record.detailsDesc }}
                    </div>
                  </template>
                </template>
              </h-table>
              </a-collapse-panel>
            </a-collapse>
          </div>
        </a-spin>
      </div>
      <template #footer>
        <a-spin v-if="!isCalling" tip="接听中" :spinning="loading">
          <div :class="{ footer: loading }" class="footerBox">
          <a-tooltip zIndex="9999" placement="top">
              <template #title>
                <span>接听</span>
              </template>
            <span @click="CallAnswerProcessor" class="icon call-icon" style="color: green"
              ><i style="font-size: 45px" class="iconfont icon-guaduan"></i
            ></span>
           </a-tooltip>
          <!-- 隐藏该功能 -->
          <!-- <a-tooltip zIndex="9999" placement="top">
              <template #title>
                <span>拒接</span>
              </template>
            <span @click="RefuseIncomingCalls" class="icon call-icon" style="color: red; margin-left: 36px"
              ><i style="font-size: 45px" class="iconfont icon-jujie"></i
            ></span>
          </a-tooltip> -->
          </div>
        </a-spin>
        <div v-else class="footerBox" style="text-align: center">
            <a-tooltip zIndex="9999" mouseEnterDelay="1" placement="top">
              <template #title>
                <span>挂断</span>
              </template>
              <span @click="unCall" class="icon call-icon" style="color: red"
                ><i style="font-size: 45px" class="iconfont icon-jujie"></i
              ></span>
             </a-tooltip>
            <a-tooltip zIndex="9999" mouseEnterDelay="1" placement="top">
              <template #title>
                <span>保持</span>
              </template>
            <span @click="callHold" v-if="!holdObj.isHolding&&caller.indexOf(',')==-1"  class="icon call-icon" style="color: #1296db;margin-left: 36px"
              ><i style="font-size: 45px" class="iconfont icon-baochi"></i
            ></span>
            </a-tooltip>
            <a-tooltip zIndex="9999" mouseEnterDelay="1" placement="top">
              <template #title>
                <span>转接</span>
              </template>
            <span @click="switchSeatOpen" v-if="caller.indexOf(',')==-1" class="icon call-icon" style="color:#6bcafb;margin-left: 36px"
              ><i style="font-size: 48px;color:#6bcafb;line-height: 68px;" class="iconfont icon-zhuanjie2"></i
            ></span>
             </a-tooltip>
            <a-tooltip zIndex="9999" mouseEnterDelay="1" placement="top">
              <template #title>
                <span>三方</span>
              </template>
            <span @click="confJoin"  type="primary"  v-if="holdObj.isHolding"  class="icon call-icon sanfangIcon" style="color: #fff;margin-left: 36px"
              ><i style="font-size: 28px" class="iconfont icon-sanfangtonghua11"></i
            ></span>
            </a-tooltip>
            <a-tooltip zIndex="9999" mouseEnterDelay="1" placement="top">
              <template #title>
                <span>评价</span>
              </template>
            <span @click="evaluate" v-if="caller.indexOf(',')==-1" class="icon call-icon" style="color: rgb(239 217 98);margin-left: 36px"
              ><i style="font-size: 45px" class="iconfont icon-shoudaopingjia"></i
            ></span>
            </a-tooltip>
            <a-tooltip zIndex="9999" mouseEnterDelay="1" placement="top">
              <template #title>
                <span>按键</span>
              </template>
            <span @click="keyboardBoxShow=true" v-if="caller.indexOf(',')==-1" class="icon call-icon" style="color: #09a649;margin-left: 36px"
              ><i style="font-size: 45px" class="iconfont icon-anjian"></i
            ></span>
            </a-tooltip>
        </div>
      </template>
    </a-modal>
    <!-- 按键弹窗 -->
    <a-modal :mask="false" :width="500" v-model:open="keyboardBoxShow" :footer="null" :maskClosable="false" title="拨号按键" zIndex="999999" @ok="ShowBusyness" @cancel="toBusyBox=false">
          <h-form ref="busyRef"  :model="busyForm"  :label-col="{ span: 2 }" :wrapper-col="{ span: 24 }" :rules="busyFormRules">
            <h-form-item label="" name="dialButtonNumber">
              <a-input-search
                v-model:value="dialButtonNumber"
                placeholder="请输入"
                onkeyup="value=value.replace(/[^0-9#*]/g,'')"
                size="large"
                @search="dialButton"
              >
                <template #enterButton>
                  <a-button  type="primary">发送</a-button>
                </template>
              </a-input-search>
            </h-form-item>
            <h-form-item v-if="dialButtonNumberString">
              <p style="padding:0;margin:0;font-size:18px;">{{ dialButtonNumberString }}</p>
            </h-form-item>
          </h-form>
    </a-modal>
    <!-- 呼叫弹窗 -->
    <a-modal :mask="false" :width="800" v-model:open="isCallOut" :closable="false" :maskClosable="false" zIndex="9000" title="">
      <div class="callBox">
        <span
          class="icon laiidan-icon"
          style="margin-bottom: 0"
          v-if="!isCalling"
          :style="{ color: 'blue', 'font-weight': 900 }"
          ><i style="font-size: 22px; margin: 8px 16px 0 0" class="iconfont icon-huchu"></i
        ></span>
        {{ caller }} <span> &nbsp;{{ fromCallInfo.callName }} {{ fromCallInfo.callPersonNum }}</span>
        <div class="callState">
          <div>
            <div class="round green"></div>
            <div class="loader"></div>
          </div>
        </div>
      </div>
      <div class="callerInfo">
        <a-spin :spinning="historyLoading">
          <p>
            上次来电时间：<span v-if="fromCallInfo?.lastHistoryCallVo?.lastCallTime">
              {{ fromCallInfo?.lastHistoryCallVo?.lastCallTime }} 接听人({{
                fromCallInfo?.lastHistoryCallVo?.answerUserName
              }})
            </span>
          </p>
          <!-- <p>
            历史未闭环的工单：{{
              fromCallInfo?.workHistoryOrderVo && fromCallInfo?.workHistoryOrderVo.length
                ? fromCallInfo?.workHistoryOrderVo[0].workTableNum
                : ''
            }}
          </p>
          <a-collapse accordion>
            <a-collapse-panel
              v-for="item in fromCallInfo?.workHistoryOrderVo"
              :header="item.workTableNum + ' ' + item.workName"
            >
              <p v-for="v in item.detailsList">
                <span>{{ v.detailCreateTime }}</span> <span>{{ v.answerName }}({{ v.answerNum }})</span>
                {{ v.detailsDesc }}
              </p>
            </a-collapse-panel>
          </a-collapse> -->
        </a-spin>
      </div>
      <template #footer>
        <div style="text-align: center" class="footerBox">
          <span @click="unCall" class="icon call-icon" style="color: red; margin-left: 36px"
            ><i style="font-size: 45px" class="iconfont icon-jujie"></i
          ></span>
        </div>
      </template>
    </a-modal>
    <!-- 示忙原因弹窗 -->
    <a-modal :mask="false" :width="600" v-model:open="toBusyBox" :maskClosable="false" title="示忙" zIndex="1050" @ok="ShowBusyness" @cancel="toBusyBox=false">
          <h-form ref="busyRef"  :model="busyForm"  :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="busyFormRules">
            <h-form-item label="示忙原因" name="busyReason">
              <h-select allowClear v-model:value="busyForm.busyReason" style="width: 100%">
                <h-select-option :value="201">休息(下班)</h-select-option>
                <h-select-option :value="202">吃饭</h-select-option>
                <h-select-option :value="203">会议</h-select-option>
                <h-select-option :value="204">培训</h-select-option>
                <h-select-option :value="205">外拨</h-select-option>
              </h-select>
            </h-form-item>
          </h-form>
    </a-modal>
    <!-- 外呼弹窗 -->
    <a-modal :mask="false" :width="600" v-model:open="toCallBox" :maskClosable="false" zIndex="1100" title="外呼电话">
      <h-tabs v-model:activeKey="callKey">
        <h-tab-pane key="1">
          <template #tab>
            <span>
              <i style="font-size: 14px" class="iconfont icon-bohao"></i>
              拨号
            </span>
          </template>
          <h-form ref="form" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules">
            <h-form-item :label="callType==1?'电话号码':'坐席号'" name="agentId">
              <!-- <h-input v-model:value="callNumber" style="width: 100%" /> -->
              <a-input-group compact>
                <a-input v-if="callType==1" v-model:value="outcall" disabled style="width: 60px" />
                <a-input v-model:value.trim="callNumber" style="width: 300px" />
                <a-tooltip :overlayClassName="'tooltip'" autoAdjustOverflow placement="top">
                <template #title>
                  <span>一般外部号码不需要手动补0，个别需要补两个0的外呼号码需要手动补一个0。内线号码直接输入四位短号即可。</span>
                </template>
                <QuestionCircleOutlined style="margin-top:10px;margin-left:5px;" />
              </a-tooltip>
              </a-input-group>
            </h-form-item>
            <h-form-item label="外呼方式">
              <a-radio-group v-model:value="callType">
                <a-radio :value="1">外部</a-radio>
                <a-radio :value="2">坐席号</a-radio>
              </a-radio-group>
            </h-form-item>
          </h-form>
          <div  style="width: 100%; display:flex;justify-content: center;">
            <span @click="callOut" class="icon callout-icon" style="color: #26ae26;"
              ><i style="font-size: 45px" class="iconfont icon-guaduan"></i
            ></span>
          </div>
        </h-tab-pane>
        <h-tab-pane key="2">
          <template #tab>
            <span>
              <i class="iconfont icon-tongxunlu"></i>
              通讯录
            </span>
          </template>
          <h-table
            :loading="loading"
            :ellipsis="true"
            :row-key="(record) => record.id"
            :size="'small'"
            :pagination="pagination1"
            :columns="columns"
            :data-source="addressBook"
            bordered
            @change="getaddressBookList($event as any)">
              <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex === 'operation'">
                  <div class="editable-row-operations">
                    <span
                      @click="callUp(record.bookMobile)"
                      class="icon"
                      style="margin-bottom: 0"
                      :style="{ color: 'blue', 'font-weight': 900 }"
                      ><i style="font-size: 22px; margin: 8px 16px 0 0" class="iconfont icon-huchu"></i
                    ></span>
                  </div>
                </template>
              </template>
            </h-table>
        </h-tab-pane>
        <h-tab-pane key="3">
          <template #tab>
            <span>
              <i class="iconfont icon-jilu"></i>
              通话记录
            </span>
          </template>
          <h-table
            :columns="columnsForHistory"
            bordered
            :ellipsis="true"
            :row-key="(record) => record.id"
            :size="'small'"
            :data-source="dataSource"
            :pagination="pagination"
            :loading="callHistoryLoading"
            @change="handleTableChange($event as any)"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'phone'">
                <span
                  v-if="record.calltype != 7"
                  class="icon"
                  :style="{ color: record.callbegin == record.callend ? 'red' : 'blue', 'font-weight': 900 }"
                  ><i style="font-size: 18px" class="iconfont icon-huru"></i
                ></span>
                <span
                  v-else
                  class="icon"
                  :style="{ color: record.callbegin == record.callend ? 'red' : 'blue', 'font-weight': 900 }"
                  ><i style="font-size: 18px" class="iconfont icon-huchu"></i
                ></span>
                {{ record.calltype == 7 ? record.calleeno : record.callerno }}
              </template>
              <template v-if="column.dataIndex === 'user'">
                {{ record.userVo.username }}
              </template>
              <template v-if="column.dataIndex === 'operation'">
                <div class="editable-row-operations">
                  <span
                    @click="callUp(record.calltype == 7 ? record.calleeno : record.callerno)"
                    class="icon"
                    style="margin-bottom: 0"
                    :style="{ color: 'blue', 'font-weight': 900 }"
                    ><i style="font-size: 22px; margin: 8px 16px 0 0" class="iconfont icon-huchu"></i
                  ></span>
                </div>
              </template>
            </template>
          </h-table>
        </h-tab-pane>
      </h-tabs>
      <template #footer>
        <div style="text-align: center">
          <!-- <a-button :loading="loading" type="primary" key="back" @click="callOut">呼叫</a-button> -->
          <!-- <span @click="callOut" class="icon call-icon" style="color:green;"><i style="font-size: 45px;" class="iconfont icon-guaduan"></i></span> -->
        </div>
      </template>
    </a-modal>
    <!-- 选择转接人员弹窗 -->
    <a-modal :mask="false" :width="600" v-model:open="switchSeatShow" :footer="null" :maskClosable="false" zIndex="99999999999" title="选择转接人员">
      <h-table :size="'small'" :pagination="false" :columns="seatListcolumns" :data-source="seatList" bordered>
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'operation'">
            <div class="editable-row-operations">
              <span>
                <a @click="switchSeat(record.agentId)">转接</a>
              </span>
            </div>
          </template>
          <template v-else>
            {{ text }}
          </template>
        </template>
      </h-table>
    </a-modal>
    <!-- 创建详情弹窗 -->
    <EditDetailDialog
      :callHistoryInfo="isRelatedCallHistory"
      @cancel="EditDetailDialogCancel"
      v-if="detailVisible"
      :show="detailVisible"
      :id="editDetail.id"
      :isLook="false"
    >
    </EditDetailDialog>
    <!-- 通话记录弹窗 -->
    <CallHistory
      @cancel="callHistoryDialogCancel"
      v-if="callHistoryVisible"
      :isRelated="true"
      :show="callHistoryVisible"
    ></CallHistory>
    <!-- 新增明细弹窗 -->
    <AddDetail
      v-if="showAdd"
      @cancel="cancel"
      :id="isRelatedRworkOrderForm.id"
      :show="showAdd"
      :callHistoryInfo="isRelatedCallHistory"
    ></AddDetail>
  </div>
</template>

<style lang="less" scoped>
.left {
  margin-right: 10px;
}

.phoneBox {
  cursor: move;
  width: 320px;
  height: 120px;
  // background: rgba(74, 76, 77, 0.96);
  // border-radius: 4px;
  position: fixed;
  bottom: 0;
  right: 0;
  // right: 0;
  display: flex;
  padding: 8px;
  z-index: 99999999999;
  .leftbox {
    height: 100%;
    .leftContent {
      width: 66px;
      height: 100%;
      display: flex;
      flex-flow: column;
      justify-content: center;
      align-items: center;
      .icon {
        color: rgba(255, 255, 255, 0.65);
        font-size: 18px;
        margin-bottom: 5px;
      }
      span {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.85);
        line-height: 14px;
        text-align: left;
        font-style: normal;
      }
    }
  }
  .rightBox {
    height: 100%;
    display: flex;
    flex-flow: column;
    // align-items: center;
    justify-content: center;
    .btn {
      margin-right: 10px;
      margin-top: 6px;
      img {
        width: 16px;
        height: 16px;
        margin-right: 3px;
        margin-bottom: 3px;
      }
    }
    .rightbtnBox {
      display: flex;
      // justify-content: flex-end;
      flex-wrap: wrap;
    }
    .peopleInfo {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.85);
      line-height: 14px;
      margin-top: 10px;
    }
    .holdBox {
      word-break: break-all;
      font-size: 12px;
    }
  }
}
.phoneBoxClose {
  width: 100px;
}
.icon {
  color: rgba(255, 255, 255, 0.65);
  font-size: 18px;
  margin-bottom: 5px;
}
// .holdBox {
//   height: 66px;
//   background: rgba(74, 76, 77, 0.96);
//   border-radius: 4px;
//   position: fixed;
//   bottom: 70vh;
//   right: 20px;
//   display: flex;
//   padding: 8px;
//   z-index: 99999999999;
//   .leftContent {
//     height: 100%;
//     display: flex;
//     flex-flow: column;
//     align-items: center;
//     justify-content: center;
//     margin: 0 10px;
//     span {
//       font-family: PingFangSC, PingFang SC;
//       font-weight: 400;
//       font-size: 14px;
//       color: #fff;
//       line-height: 14px;
//       text-align: left;
//       font-style: normal;
//     }
//     .btn {
//       margin-right: 10px;
//     }
//   }
//   .rightBox {
//     height: 100%;
//     display: flex;
//     align-items: center;
//     .btn {
//       margin-right: 10px;
//     }
//     span {
//       color: #fff;
//       margin-right: 8px;
//     }
//   }
// }
.callBox {
  width: 100%;
  text-align: center;
  font-size: 22px;
  font-weight: 600;
  padding-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  .callState {
    font-size: 16px;
    font-weight: 900;
    margin-left: 20px;
    display: flex;
    align-items: center;
    div {
      display: flex;
      align-items: center;
    }
    .round {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 10px;
      position: relative;
      animation: blink-animation 1s infinite alternate; //添加动画
    }
    .red {
      background-color: #ef2828;
    }
    .green {
      background-color: #0bd03c;
    }
  }
}
.callerInfo {
  margin-bottom: 20px;
  max-height: 400px;
  overflow: auto;
}
.listItem {
  width: 200px;
}
:deep(.ant-input-disabled) {
  background: #f1f1f1 !important;
  // color: #000;
}
// 浮窗样式
.navigation {
  background-color: #fff;
  z-index: 99999;
  position: fixed;
  right: 20px;
  top: 40vh;
  padding-top: 6px;
  width: 75px;
  // min-height: 500px;
  display: flex;
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  cursor: move;
  box-shadow: 0 9px 28px 8px rgba(0, 0, 0, 0.05), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12);
  border-radius: 5px;
  .peopleInfo {
    font-size: 13px;
    margin-bottom: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    width: 100%;
    padding: 6px 6px;
    p {
      padding: 0;
      margin: 0;
    }
  }
  // ul{
  //   height:360px;
  // }
  .toSignBox {
    height: 120px;
    padding: 10px 6px;
  }
  .loginBtn {
    height: 60px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    .signBox{
      display:flex;
      flex-flow: column;
      align-items: center;
      padding-top:6px;
      cursor: pointer;
      span{
        font-size: 12px;
        margin-top:5px;
      }
    }
  }
  .openBox {
    position: absolute;
    width: 25px;
    height: 50px;
    background: #e3e3e3;
    border-radius: 3px 0 0 3px;
    left: -25px;
    top: 38%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transition: all 0.6s;
  }
  .openBox:hover {
    opacity: 1;
  }
  &:hover {
    .openBox {
      opacity: 1;
    }
  }
}
.miniShowBox {
  width: 230px;
  height: auto;
  padding: 6px;
  .callingBox {
    // width:300px;
    width: 100%;
    background: #fff;
    font-size: 12px;
    position: relative;
    word-break: break-all;
    padding: 3px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-shrink: 0;
    .icon {
      margin-right: 2px;
    }
    .phoneCallerBox{
      display: flex;
      align-items: center;
      width:130px;
      padding-top: 5px;
    }
    .phone {
      color: #676767;
      // margin-top: 5px;
      // margin-left: 3px;
    }
    .zuixiaohua {
      position: absolute;
      right: 5px;
      top: 2px;
      // border-left:#c8c8c8 1px solid;
      cursor: pointer;
    }
  }
}

.hold {
  width: 210px;
  height: auto;
  // padding: 6px;
  .holdBox {
    width: 100%;
    background: #fff;
    font-size: 12px;
    position: relative;
    word-break: break-all;
    padding:0 0 6px 10px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border-bottom:solid #c8c8c8 1px;
    .icon {
      margin-right: 2px;
    }
    .phone {
      color: #676767;
      margin-top: 5px;
      margin-left: 3px;
    }
    .zuixiaohua {
      position: absolute;
      right: 5px;
      top: 5px;
      // border-left:#c8c8c8 1px solid;
      cursor: pointer;
    }
  }
}

.operaBox{
  width:100%;
  display: flex;
  justify-content: space-around;
  .icon{
    color:#000;
  }
  .operaBoxItem{
    width:50%;
    text-align: center;
    padding:5px;
    font-size:13px;
    cursor: pointer;
    .icon{
      margin-right:3px;
    }
  }
  .waihu{
        &:hover{
      // background: #ffa117;
      color:#ffa117;
      i{
        color:#ffa117!important;
      }
    }
  }
    .baochi{
        &:hover{
      // background: #ffa117;
      color:#8ae396;
      i{
        color:#8ae396!important;
      }
    }
  }
}

.navigation.open {
  width: 180px;
}

.navigation .menu-toggle {
  position: absolute;
  bottom: -40px;
  left: 0;
  width: 100%;
  height: 40px;
  // border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0 20px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.5s;
}

.navigation .menu-toggle::before {
  width: 30px;
  height: 2px;
  content: '';
  background-color: #333;
  transform: translateY(-8px);
  position: absolute;
  transition: all 0.5s;
}

.navigation.open .menu-toggle::before {
  transform: translateY(0px) rotate(-45deg);
}

.navigation .menu-toggle::after {
  width: 30px;
  height: 2px;
  content: '';
  background-color: #333;
  transform: translateY(8px);
  position: absolute;
  transform: translateY(8px);
  box-shadow: 0 -8px 0 #333;
  transition: all 0.5s;
}

.navigation.open .menu-toggle::after {
  transform: translateY(0px) rotate(45deg);
  box-shadow: 0 0 0 #333;
}

.navigation ul {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
}

.navigation ul li {
  list-style: none;
  width: 100%;
  height: 60px;
  padding: 0 10px;
  transition: all 0.5s;
  &:hover {
    transform: translateX(-30px);
  }
}

.navigation ul li.active {
  transform: translateX(-30px);
}

.navigation.open ul li.active {
  transform: translateX(-10px);
}

.navigation ul li.activeMin {
  transform: translateX(0);
}

.navigation.open ul li.activeMin {
  transform: translateX(0);
}

.navigation ul li a {
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  text-align: center;
}

.navigation ul li a .icon {
  min-width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  color: #333;
  border-radius: 10px;
  position: relative;
  transition: all 0.5s;
}

.navigation ul li a .icon .iconfont {
  font-size: 24px;
}

.navigation ul li.active a .icon {
  background-color: var(--list-active-color);
  color: #fff;
}

.navigation ul li a .icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--list-active-color);
  z-index: -1;
  filter: blur(5px);
  opacity: 0;
  transition: all 0.5s;
}

.navigation ul li.active a .icon::before {
  opacity: 1;
}

.navigation ul li a .text {
  padding: 0 15px;
  visibility: hidden;
  height: 60px;
  display: flex;
  align-items: center;
  color: #333;
  opacity: 0;
}

.navigation.open ul li a .text {
  opacity: 1;
  visibility: visible;
}

.navigation ul li.active a .text {
  color: var(--list-active-color);
}

.footerBox {
  width: 100%;
  display: flex;
  justify-content: center;
}
.footer {
  visibility: hidden;
}

.call-icon {
  display: block;
  width: 50px;
  height: 50px;
  background: url('path_to_your_icon.png') no-repeat center;
  border-radius: 50%;
  // animation: rotate 0.3s linear infinite;
}
.laiidan-icon {
  animation: rotate 0.3s linear infinite;
}

.callout-icon {
  display: block;
  width: 50px;
  height: 50px;
  background: url('path_to_your_icon.png') no-repeat center;
  border-radius: 50%;
}

.loader {
  width: fit-content;
  font-weight: bold;
  font-size: 16px;
  clip-path: inset(0 3ch 0 0);
  animation: l4 1.5s steps(4) infinite;
  letter-spacing: 3.5px;
}
.loader:before {
  content: '呼叫中...';
}

.loadertonghuazhong {
  width: fit-content;
  font-size: 12px;
}
.loadertonghuazhong:before {
  content: '通话中';
}

.loaderdaijieting {
  width: fit-content;
  font-weight: bold;
  font-size: 16px;
  clip-path: inset(0 3ch 0 0);
  animation: l4 1.5s steps(4) infinite;
  letter-spacing: 3.5px;
}
.loaderdaijieting:before {
  content: '待接听...';
}
.icon{
  cursor: pointer;
}
.iconfont{
  border-radius: 50%;
    &:hover{
      box-shadow: 0 9px 28px 8px rgba(0, 0, 0, 0.05), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12);
    }
}
.iconfontFixed{
  &:hover{
      box-shadow:none;
    }
}
.twichicon{
  cursor: pointer;
  margin-left:10px;
}
.sanfangIcon{
  background:#1296db;
  display:block;
  width:45px;
  height:45px;
  margin-top: 11px;
}
</style>
<style>
.ant-dropdown {
  z-index: 999999999999999;
}
@keyframes blink-animation {
  from {
    opacity: 0.3; /* 开始时透明 */
  }
  to {
    opacity: 1; /* 结束时不透明 */
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(10deg);
  }
  50% {
    transform: rotate(0deg);
  }
  75% {
    transform: rotate(-10deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

@keyframes l4 {
  to {
    clip-path: inset(0 -1ch 0 0);
  }
}
</style>
<style>
.tooltip{
  z-index: 999999;
}
</style>