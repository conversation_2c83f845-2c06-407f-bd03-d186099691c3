import {
    InvoiceConfirmParams, InvoiceConfirmResult
} from '@haierbusiness-front/common-libs'
import { originalPost } from '../../request'

export const invoiceConfirmApi = {
    submit: (params: InvoiceConfirmParams): Promise<InvoiceConfirmResult> => {
        return originalPost('/mice-bid/api/mice/balance/invoice/confirm', params)
    },
    reject: (params: InvoiceConfirmParams): Promise<InvoiceConfirmResult> => {
        return originalPost('/mice-bid/api/mice/balance/invoice/reject', params)
    }
}
