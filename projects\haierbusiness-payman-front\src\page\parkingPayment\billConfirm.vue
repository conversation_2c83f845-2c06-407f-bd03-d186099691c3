<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
} from "ant-design-vue";
import { ColumnType } from "ant-design-vue/lib/table/interface";
import { Key } from "ant-design-vue/lib/vc-table/interface";
import {
  DownOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
} from "@ant-design/icons-vue";
import { payApi, virtualPayApi } from "@haierbusiness-front/apis";
import {
  VirtualAccountTypeConstant,
  HaierBudgetSourceConstant,
  VirtualScopeConstanty,
  IVirtualChangeListRequest,
  PayStatusConstant,
  PayTypeConstant,
  VirtualAccountChangeTypeConstant,
} from "@haierbusiness-front/common-libs";
import { errorModal, routerParam } from "@haierbusiness-front/utils";
import Eloading from "@haierbusiness-front/components/loading/Eloading.vue";
import dayjs, { Dayjs } from "dayjs";
import { computed, ref, watch } from "vue";
import { DataType, usePagination, useRequest } from "vue-request";
import { useRouter } from "vue-router";
import { useEditDialog, useDelete } from "@haierbusiness-front/composables";
import EditDialog from "./edit-dialog.vue";
import UserDialog from "./user-dialog.vue";

const router = useRouter();
const columns: ColumnType[] = [
  {
    title: "充值订单编码",
    dataIndex: "code",
    fixed: "left",
    width: "240px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "账户号",
    dataIndex: "accountNo",
    width: "240px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "账户名称",
    dataIndex: "accountName",
    width: "240px",
    align: "center",
    ellipsis: true,
  },

  {
    title: "企业名称",
    dataIndex: "enterpriseName",
    width: "240px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "企业编码",
    dataIndex: "enterpriseCode",
    width: "120px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "充值金额",
    dataIndex: "amount",
    width: "120px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "充值时间",
    dataIndex: "gmtCreate",
    width: "170px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "核销状态",
    dataIndex: "verificationState",
    width: "150px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "审批状态",
    dataIndex: "approveStatus",
    width: "150px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "充值状态",
    dataIndex: "rechargeState",
    width: "150px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "充值来源应用",
    dataIndex: "applicationCode",
    width: "150px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "流水单号",
    dataIndex: "changeCode",
    width: "200px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "支付单号",
    dataIndex: "paymentRecordCode",
    width: "220px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "充值说明",
    dataIndex: "remark",
    width: "200px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "充值凭证",
    dataIndex: "changeBusinessCode",
    width: "200px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "充值参数",
    dataIndex: "changeParams",
    width: "300px",
    align: "center",
    ellipsis: true,
  },

  {
    title: "操作",
    dataIndex: "_operator",
    width: "180px",
    fixed: "right",
    align: "center",
  },
];
const searchParam = ref<IVirtualChangeListRequest>({ needPage: true });
const { data, run: listApiRun, loading, current, pageSize } = usePagination(
  virtualPayApi.rechargeOrderList
);

const reset = () => {
  startBeginAndEnd.value = undefined;
  searchParam.value = { needPage: true };
};

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: "center" },
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const {
  data: exportListData,
  run: exportListApiRun,
  loading: exportListLoading,
} = useRequest(virtualPayApi.exportRechargeOrderList);

const type = computed(() => {
  return VirtualAccountChangeTypeConstant.toArray();
});

const advancedSearchVisible = ref(false);
const gotoAdvancedSearch = () => {
  if (advancedSearchVisible.value) {
    advancedSearchVisible.value = false;
  } else {
    advancedSearchVisible.value = true;
  }
};

const onRetry = async (e) => {
  const res = virtualPayApi.voucherRechargeRetry(e.code);
  message.success("重试成功");
};

const onCancel = (e) => {
  const res = virtualPayApi.voucherRechargeOrderCancel(e.code);
  message.success("取消成功");
};

const startBeginAndEnd = ref<[Dayjs, Dayjs]>();
watch(
  () => startBeginAndEnd.value,
  (n: any, o: any) => {
    if (n) {
      searchParam.value.changeBegin = n[0];
      searchParam.value.changeEnd = n[1];
    } else {
      searchParam.value.changeBegin = undefined;
      searchParam.value.changeEnd = undefined;
    }
  }
);
</script>

<template>
  <div
    style="
      background-color: #ffff;
      height: 100%;
      width: 100%;
      padding: 10px 10px 0px 10px;
      overflow: auto;
    "
  >
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="code">充值订单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="code"
              v-model:value="searchParam.code"
              placeholder=""
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="accountNo">账户号：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="accountNo"
              v-model:value="searchParam.accountNo"
              placeholder=""
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="applicationCode">账户名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="applicationCode"
              v-model:value="searchParam.accountName"
              placeholder=""
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="enterpriseCode">企业编码：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="enterpriseCode"
              v-model:value="searchParam.enterpriseCode"
              placeholder=""
              autocomplete="off"
              allow-clear
            />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="enterpriseName">企业名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="enterpriseName"
              v-model:value="searchParam.enterpriseName"
              placeholder=""
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="changeBusinessCode">充值凭证：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="changeBusinessCode"
              v-model:value="searchParam.changeBusinessCode"
              placeholder=""
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="searchState">充值状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value="searchParam.rechargeState"
              style="width: 100%"
              allow-clear
            >
              <h-select-option value="0">取消</h-select-option>
              <h-select-option value="10">保存</h-select-option>
              <h-select-option value="20">充值成功</h-select-option>
              <h-select-option value="999">失败</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="searchState">核销状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value="searchParam.verificationState"
              style="width: 100%"
              allow-clear
            >
              <h-select-option value="1">未核销</h-select-option>
              <h-select-option value="2">已核销</h-select-option>
              <h-select-option value="3">已二次核销</h-select-option>
              <h-select-option value="4">核销失败</h-select-option>
              <h-select-option value="5">二次核销失败</h-select-option>
              <h-select-option value="6">无需核销</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="source">来源：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value="searchParam.source"
              style="width: 100%"
              allow-clear
            >
              <h-select-option value="1">福利积分</h-select-option>
              <h-select-option value="2">代金券</h-select-option>
              <h-select-option value="3">附件凭证</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="approveStatus">审批状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value="searchParam.approveStatus"
              style="width: 100%"
              allow-clear
            >
              <h-select-option value="0">取消</h-select-option>
              <h-select-option value="10">审批中</h-select-option>
              <h-select-option value="20">审批通过</h-select-option>
              <h-select-option value="30">审批驳回</h-select-option>
              <h-select-option value="40">无需审批</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <!-- <template v-if="advancedSearchVisible">
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
            <h-col :span="2" style="text-align: right">
              <label for="startBeginAndEnd">支付发起日期：</label>
            </h-col>
            <h-col :span="4">
              <h-range-picker
                v-model:value="startBeginAndEnd"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </h-col>
          </h-row>
        </template> -->
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <!-- <h-button type="link" @click="gotoAdvancedSearch()">
              <UpOutlined v-if="advancedSearchVisible" />
              <DownOutlined v-else />
              高级搜索
            </h-button> -->
            <h-button type="primary" style="margin-right: 10px" :loading="exportListLoading"
              @click="exportListApiRun(searchParam);">
              <UploadOutlined />
              导出
            </h-button>
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button
              type="primary"
              @click="handleTableChange({ current: 1, pageSize: 10 })"
            >
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="dataSource"
          :pagination="pagination"
          :scroll="{ y: 550 }"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ text, column, record }">
            <template v-if="column.dataIndex === 'source'">
              {{ text == 1 ? "福利积分" : text == 2 ? "代金券" : "附件凭证" }}
            </template>
            <template v-if="column.dataIndex === 'rechargeState'">
              <a-tag v-if="text == '20'" color="#87d068">充值成功</a-tag>
              <a-tag v-if="text == '10'" color="#2db7f5">保存</a-tag>
              <a-tag v-if="text == '999'" color="#f50">失败</a-tag>
              <a-tag v-if="text == '0'" color="default">取消</a-tag>
            </template>
            <template v-if="column.dataIndex === 'verificationState'">
              {{ text === 1 ? '未核销' 
                  : text === 2
                  ? '已核销'
                  : text === 3
                  ? '已二次核销'
                  : text === 4
                  ? '核销失败'
                  : text === 5
                  ? '二次核销失败'
                  : '无需核销'
              }}
            </template>
            <template v-if="column.dataIndex === 'approveStatus'">
              {{ text === 10 ? '审批中' 
                  : text === 20
                  ? '审批通过'
                  : text === 30
                  ? '审批驳回'
                  : text === 40
                  ? '无需审批'
                  : '取消'
              }}
            </template>
            <template v-if="column.dataIndex === 'type'">
              {{ VirtualAccountChangeTypeConstant.ofType(text)?.desc }}
            </template>

            <template v-if="column.dataIndex === '_operator'">
              <template v-if="record.source == 2 && record.rechargeState == 999">
                <h-popconfirm title="确认重试?" @confirm="onRetry(record)">
                  <a style="margin-right: 10px">重试</a>
                </h-popconfirm>
                <h-popconfirm title="确认取消?" @confirm="onCancel(record)">
                  <a>取消</a>
                </h-popconfirm>
              </template>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
