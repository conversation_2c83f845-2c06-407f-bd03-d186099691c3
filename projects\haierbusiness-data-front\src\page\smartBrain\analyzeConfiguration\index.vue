<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  Switch as hSwitch,
  CheckboxGroup as hCheckboxGroup,
  Checkbox as hCheckbox,
  Image as hImage,
  rangePicker as hrangePicker,
  Modal,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, EditOutlined, ExpandAltOutlined } from '@ant-design/icons-vue';
import { smartBrainApi, reportApi } from '@haierbusiness-front/apis';
import {
  ISmartBrainFilter,
  ISmartBrain,
  Datum,
  IReportManagerInfoListList,
  deptLabelVo,
  IUserInfo,
  FetchBrainDataParams,
  IndexList,
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import {
  computed,
  ref,
  watch,
  onMounted,
  reactive,
  onBeforeUnmount,
  defineAsyncComponent,
  Suspense,
  onUnmounted,
  markRaw,
  inject
} from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { EventBus } from '../../../page/board/eventBus';
import { checkUserGroups } from '@haierbusiness-front/utils/src/authorityUtil';
import { UserGroupSystemConstant } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

const IndexItem = defineAsyncComponent(() => import('./indexItem.vue'));
import moment from 'moment';
const yesterday = moment().subtract(1, 'days');
const year = moment().subtract(1, 'year');
const pattern = 'YYYY-MM-DD';
import { getCurrentRouter, routerParam } from '@haierbusiness-front/utils';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';
interface IForm {
  UserId?: string;
  nickName?: string;
  typeId?: string;
}
const router = getCurrentRouter();
import { useRoute } from 'vue-router';
const route = useRoute();
const currentRouter = ref();
const echartRef = ref<any>(null);
const analysisType = ref<IReportManagerInfoListList[]>([]);
// 搜索表单
const searchParam = ref<ISmartBrainFilter>({});
const form = reactive<IForm>({ typeId: '' });
// 拖拽布局
const layout = ref<any>([]);
// 求单个单元格的高度
const gridItemHeight = ref<number>(240);
const gridItemWeight = ref<number>(500);
// 当前选中的分析类型的id
const typeId = ref<string>('');
 

const echartsSearchForm = ref<FetchBrainDataParams>({
  bookingDate: [moment(year).format(pattern), moment(yesterday).format(pattern)],
  accountCompanyCodeList: [],
  budgetDepartmentCodeList: [],
  budgetSourceList:[],
});
const modal2Visible = ref<boolean>(false);
const searchForm = ref<FetchBrainDataParams>({});
const openHelpModal = ref<boolean>(false);
// 指标申请列表
const indexApplicationList = ref<IndexList[]>([]);
const { data, run: listApiRun, loading, current, pageSize } = usePagination(smartBrainApi.list);

const reset = () => {
  echartsSearchForm.value = {};
  handleTableChange();
};

// 跳转新增
const toAdd = () => {
  router.push({
    path: '/data/smartBrain/analyzeConfiguration/add',
    query: {
      userId: form.UserId,
    },
  });
};

// 编辑跳转
const edit = () => {
  router.push({
    path: '/data/smartBrain/analyzeConfiguration/add',
    query: {
      id: typeId.value,
      userId: form.UserId,
    },
  });
};

// 是否展示头部视图
const showTopCard = ref<boolean>(false)

// 获取分析视图详情详情
const getBrainReportManagerVoById = (viewId: string) => {
  smartBrainApi.getBrainReportManagerVoById({ id: viewId }).then((res: any) => {
    const baseManageInfo = JSON.parse(res.baseManageInfo);
    baseManageInfo.forEach((v: any) => {
      res.reportVo.forEach((u: any) => {
        if (v.tid == u.tid) {
          Object.assign(v, u);
        }
      });
    });
    layout.value = baseManageInfo
  });
};

// 查询
const handleTableChange = () => {
  searchForm.value = JSON.parse(JSON.stringify(echartsSearchForm.value));
  setTimeout(() => {
    EventBus.emit('refresh');
  }, 300);
};

// 获取分析类型下拉
const getOwnerBrainReportManagerInfoList = (userId?: string | number | undefined) => {
  smartBrainApi.getOwnerBrainReportManagerInfoList({ userId }).then((res: Array<IReportManagerInfoListList>) => {
    // res把图标配置更新成最新的
    res.forEach((item) => {
      if (!item.mid) {
        item.mid = '';
      }
      // 更新保存的值
      if (item.baseManageInfo) {
        item.baseManageInfo = JSON.parse(item.baseManageInfo);
        item.baseManageInfo.forEach((v: any) => {
          item.reportVo.forEach((u: any) => {
            if (v.tid == u.tid) {
              Object.assign(v, u);
            }
          });
        });
      }
    });
    analysisType.value = [];
    analysisType.value.push(...res);
    // 从新增编辑来 自动选择到新增编辑的第一个
    if (route.query.form == 'add') {
      form.typeId = analysisType.value[1].mid;
      typeId.value = analysisType.value[1].mid;
      //处理数据
      analysisType.value.forEach((item: any) => {
        item.id = Date.now();
        if (item.mid == analysisType.value[1].mid) {
          layout.value = [];
          // 为页面布局赋值
          setTimeout(() => {
            layout.value = item.baseManageInfo;
          }, 300);
        }
      });
    }
  });
};

// 请求已申请指标list列表
const BrainReportLis = (userId?: string | number | undefined) => {
  smartBrainApi.getOwnerBrainReportList({ userId }).then((res: Array<IndexList>) => {
    if (res && res.length) {
      indexApplicationList.value = res;
      indexApplicationList.value.forEach((item: any, index: number) => {
        if (index < 3) {
          item.x = index;
          item.y = 0;
        } else {
          item.x = index % 3;
          item.y = Math.ceil(index / 3);
        }
        item.w = 1;
        item.h = 4;
        item.i = index;
        // layout.value.push(item)
      });
      layout.value = indexApplicationList.value.slice(0, 25);
    } else {
      if (!currentRouter.value.query?.viewId) {
        modal2Visible.value = true;
      }
    }
  });
};

// 跳转申请指标页面
const goApply = () => {
  router.push({
    path: '/data/smartBrain/IndicatorApplication'
  });
  // window.location.reload()
}

const rangePresets = ref([
  { label: '最近一年', value: [dayjs().add(-365, 'd'), dayjs()] },
  { label: '最近半年', value: [dayjs().add(-180, 'd'), dayjs()] },
  // { label: 'Last 30 Days', value: [dayjs().add(-30, 'd'), dayjs()] },
  // { label: 'Last 90 Days', value: [dayjs().add(-90, 'd'), dayjs()] },
]);


// 更改分析类型
const changeType = (value: any) => {
  if (!value) {
    layout.value = [];
    typeId.value = '';
    BrainReportLis(form.UserId);
    return;
  }
  typeId.value = value;
  //处理数据
  analysisType.value.forEach((item: any) => {
    item.id = Date.now();
    if (item.mid == value) {
      layout.value = [];
      // 为页面布局赋值
      setTimeout(() => {
        layout.value = item.baseManageInfo;
      }, 300);
    }
  });
};

const changeUser = (userInfo: IUserInfo) => {
  if (userInfo) {
    form.UserId = userInfo.username;
    form.nickName = userInfo.nickName;
    BrainReportLis(userInfo.username);
    getOwnerBrainReportManagerInfoList(userInfo.username);
  } else {
    form.UserId = '';
    form.nickName = '';
    BrainReportLis();
    getOwnerBrainReportManagerInfoList();
  }
  // 讲选择的人存入sessionstorla
  sessionStorage.setItem('selectUser', JSON.stringify({ UserId: form.UserId, nickName: form.nickName }));
};

const getunit = (json: any) => {
  return JSON.parse(json)?.unit;
};

const doScroll = (event: any) => {
  const scrollHeight = event.target.scrollHeight;
  const scrollTop = event.target.scrollTop;
  const clientHeight = event.target.clientHeight;
  if (scrollHeight - (scrollTop + clientHeight) <= 20) {
    if (layout.value.length < indexApplicationList.value.length && !form.typeId) {
      layout.value = layout.value.concat(
        indexApplicationList.value.slice(layout.value.length, layout.value.length + 25),
      );
      // layout.value = indexApplicationList.value
    }
  }
};
const fullScreenLayout = (id: string) => {
  var isFull = !!(
    document.webkitIsFullScreen ||
    document.mozFullScreen ||
    document.msFullscreenElement ||
    document.fullscreenElement
  );
  if (isFull == false) {
    document.querySelector('#content')?.requestFullscreen();
  } else {
    document.exitFullscreen();
  }
};

const content = ref();

const openItemBox = ref<boolean>(false);
const rowItem = ref<any>({});

const showIndexModal = (row: any) => {
  console.log(row);
  rowItem.value = row;
  openItemBox.value = true;
};

const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const filterAreaOption = (input: string, option: any) => {
  return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
const frameModel = inject<any>('frameModel');


// 查询领域、平台、产业线
// 结算单位
const settlementDept =  ref([]);
// 预算部门
const budgetDepartment = ref([]);
const areaList = ref([]);
const platformList = ref([]);
const industryList = ref([]);
// 预算来源
const budgetSourcePreList = ref([{
  code: 'BCC',
  name: 'BCC',
},{
  code: 'HBC',
  name: 'HBC',
},{
  code: 'GEMS',
  name: 'GEMS',
},{
  code: 'KEMS',
  name: 'KEMS',
},{
  code: 'RRSGEMS',
  name: 'RRSGEMS',
},{
  code: 'MICRO',
  name: '小微',
}]);

// 根据类型查询不同权限类型 permissionType 3:领域 4:平台 5:产业线
const getPowerByApprove = async (name: string, permissionType: number) => {
  const data = await reportApi.getPowerByApprove(name, permissionType, 4, 'brain');
  switch (permissionType) {
    case 1:
      budgetDepartment.value = data;
      break;
    case 2:
      settlementDept.value = data;
    case 3:
      areaList.value = data;
      break;
    case 4:
      platformList.value = data;
      break;
    case 5:
      industryList.value = data;
      break;
    default:
      break;
  }
};

const handleBudgetDepartmentSearch = (val: string) => {
  getPowerByApprove(val, 1);
};
const handleAccountCompanySearch = (val: string) => {
  getPowerByApprove(val, 2);
};

const handleAreaSearch = (val: string) => {
  getPowerByApprove(val, 3);
};

const handlePtSearch = (val: string) => {
  getPowerByApprove(val, 4);
};
const handlePlSearch = (val: string) => {
  getPowerByApprove(val, 5);
};


// 初始化
onMounted(async () => {
  content.value.addEventListener('scroll', doScroll);
  if (sessionStorage.getItem('selectUser')) {
    form.UserId = JSON.parse(sessionStorage.getItem('selectUser')).UserId;
    form.nickName = JSON.parse(sessionStorage.getItem('selectUser')).nickName;
  }
  currentRouter.value = await router;

  getPowerByApprove("", 1);
  getPowerByApprove("", 2);
  getPowerByApprove("", 3);
  getPowerByApprove("", 4);
  getPowerByApprove("", 5);

  if (!route.query.form && !route.query.viewId) {
    BrainReportLis(form.UserId);
  }
  if (!route.query.viewId) {
    showTopCard.value = true
  }
  if (route.query.viewId) {
    getBrainReportManagerVoById(route.query.viewId)
  } else {
    getOwnerBrainReportManagerInfoList(form.UserId);
  }
  if (route?.query?.frameModel) {
    frameModel.value = route?.query?.frameModel;
  }
  searchForm.value = JSON.parse(JSON.stringify(echartsSearchForm.value));
  // BrainReportLis(form.UserId);
  // handleTableChange({ current: 1, pageSize: 10 });
  gridItemHeight.value = (document.getElementById('content')?.offsetWidth / 12) * 0.5;
  gridItemWeight.value = document.getElementById('content')?.offsetWidth / 3 - 12;
  window.onresize = () => {
    return (() => {
      gridItemHeight.value = (document.getElementById('content')?.offsetWidth / 12) * 0.5;
      gridItemWeight.value = document.getElementById('content')?.offsetWidth / 3 - 12;
    })();
  };
});
onUnmounted(() => { });
</script>

<template>
  <div v-show="$route.matched.length < 3" ref="content" style="height: 100%; width: 100%; overflow: auto">
    <!-- 分析类型筛选 -->
    <a-card v-if="showTopCard" style="margin: 0 10px" :bodyStyle="{ padding: '16px' }">
      <h-form ref="from" :model="form" labelWrap :label-col="{ span: 7 }" :wrapper-col="{ span: 16 }">
        <h-row class="topRow">
          <h-col v-if="checkUserGroups([UserGroupSystemConstant.SUPER_MANAGE.groupId], 'OR')" :span="5">
            <h-form-item label="用户权限" name="id">
              <user-select :value="form.nickName" placeholder="请选择用户" :params="{
                pageNum: 1,
                pageSize: 20,
              }" @change="(userInfo: IUserInfo) => changeUser(userInfo)"></user-select>
            </h-form-item>
          </h-col>
          <h-col :span="5">
            <h-form-item label="分析视图" name="id">
              <h-select @change="changeType" v-model:value="form.typeId">
                <h-select-option v-for="item in analysisType" :value="item.mid">{{ item.managerName }}</h-select-option>
              </h-select>
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-button :disabled="typeId ? false : true" style="margin-right: 10px" @click="edit()">
              <EditOutlined />编辑视图
            </h-button>
            <h-button type="primary" @click="toAdd()">
              <PlusOutlined />新增视图
            </h-button>
          </h-col>
          <div class="fullScreen">
            <h-button type="primary" @click="openHelpModal = true">
              <QuestionCircleOutlined />帮助
            </h-button>
            <!-- <a-tooltip>
              <template #title>全屏</template>
              <ExpandAltOutlined @click="fullScreenLayout()" />
            </a-tooltip> -->
          </div>
        </h-row>
      </h-form>
    </a-card>
    <!-- 数据筛选 -->
    <a-card style="margin:10px 10px 0 10px" :bodyStyle="{ padding: '16px' }">
      <h-form ref="from" labelWrap :model="echartsSearchForm" :label-col="{ span: 7 }" :wrapper-col="{ span: 16 }">
        <h-row>
          <h-col :span="6">
            <h-form-item label="起始日期" name="bookingDate">
              <h-range-picker valueFormat="YYYY-MM-DD" :presets="rangePresets"
                v-model:value="echartsSearchForm.bookingDate" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="领域" name="fieldCodeList">
              <!-- <h-select  show-search @search="handleSearchArea" show-arrow allowClear v-model:value="echartsSearchForm.areaCodeList" style="width: 100%">
                <h-select-option v-for="item in areaList" :key="item.code" :value="item.code">{{ item.name }}</h-select-option>
              </h-select> -->
              <h-select placeholder="请搜索领域（支持多选）" v-model:value="echartsSearchForm.fieldCodeList" mode="multiple"
                show-search :filter-option="filterAreaOption" @search="handleAreaSearch" :maxTagCount="1" show-arrow
                allowClear :options="areaList" :field-names="{ label: 'name', value: 'code' }">
              </h-select>
            </h-form-item>
          </h-col>

          <h-col :span="6">
            <h-form-item label="平台" name="ptCodeList">
              <h-select placeholder="请搜索平台（支持多选）" v-model:value="echartsSearchForm.ptCodeList" mode="multiple" show-search
                :filter-option="filterAreaOption" @search="handlePtSearch" :maxTagCount="1" show-arrow allowClear
                :options="platformList" :field-names="{ label: 'name', value: 'code' }">
              </h-select>
            </h-form-item>
          </h-col>

          <h-col :span="6">
            <h-form-item label="产业线" name="plCodeList">
              <h-select placeholder="请搜索产业线（支持多选）" v-model:value="echartsSearchForm.plCodeList" mode="multiple"
                show-search :filter-option="filterAreaOption" @search="handlePlSearch" :maxTagCount="1" show-arrow
                allowClear :options="industryList" :field-names="{ label: 'name', value: 'code' }">
              </h-select>
            </h-form-item>
          </h-col>

        </h-row>
        <h-row style="margin-top: 10px">
          <h-col :span="6">
            <h-form-item label="结算单位" name="accountCompanyCodeList">
              <h-select placeholder="请搜索结算单位（支持多选）" mode="multiple" :filter-option="filterOption" @search="handleAccountCompanySearch"
                :options="settlementDept" :maxTagCount="1" allowClear
                v-model:value="echartsSearchForm.accountCompanyCodeList" :field-names="{ label: 'name', value: 'code' }">
                <!-- <h-select-option v-for="item in settlementDept" :value="item.code">{{ item.name }}</h-select-option> -->
              </h-select>
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="预算部门" name="budgetDepartmentCodeList">
              <h-select allowClear placeholder="请搜索预算部门（支持多选）"  @search="handleBudgetDepartmentSearch"  :filter-option="filterOption" mode="multiple"
                        :options="budgetDepartment" :maxTagCount="1" v-model:value="echartsSearchForm.budgetDepartmentCodeList" :field-names="{ label: 'name', value: 'code' }">
              </h-select>
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="预算来源" name="budgetSourceList">
              <h-select allowClear placeholder="请搜索预算部门（支持多选）" mode="multiple"
                        :options="budgetSourcePreList" :maxTagCount="1" v-model:value="echartsSearchForm.budgetSourceList" :field-names="{ label: 'name', value: 'code' }">
              </h-select>
            </h-form-item>
          </h-col>
          <h-col :span="12" style="text-align: right">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange()">
              <SearchOutlined />查询
            </h-button>
            <span class="full">
              <a-tooltip>
                <template #title>全屏</template>
                <ExpandAltOutlined @click="fullScreenLayout()" />
              </a-tooltip>
            </span>
          </h-col>
        </h-row>
      </h-form>
    </a-card>
    <!-- 指标列表 -->
    <div v-if="$route.matched.length < 3" class="indexListBox" ref="indexListBox" id="content">
      <!-- 指标item -->
      <grid-layout style="background: #f5f5f5" ref="gridlayout" :layout.sync="layout" :col-num="3"
        :row-height="gridItemHeight" :is-draggable="false" :is-resizable="false" :is-mirrored="false"
        :vertical-compact="true" :margin="[12, 12]" :use-css-transforms="true" :preventCollision="false"
        :responsive="false" :useStyleCursor="false">
        <grid-item v-for="(item, index) in layout" ref="gridItemRefs" :x="item.x" :y="item.y" :w="item.w" :h="item.h"
          :i="item.i" :key="item.id">
          <div class="titleBox" v-if="item.name == 'title'" v-html="item.content"></div>
          <IndexItem v-else :row="item" :searchForm="searchForm" @showIndexModal="showIndexModal" />
        </grid-item>
      </grid-layout>
    </div>
  </div>
  <router-view v-if="$route.matched.length >= 3"></router-view>
  <!-- 指标查看 -->
  <a-modal :width="1000" v-model:open="openItemBox" getContainer="#content" :footer="null">
    <div class="IndexItembox" :style="{ height: rowItem.echartsJson.indexOf('地图') != -1 ? '600px' : '350px' }">
      <IndexItem v-if="openItemBox" :identification="'box'" :showFull="false" :searchForm="searchForm" :row="rowItem" />
    </div>
  </a-modal>
  <!-- 帮助弹窗 -->
  <a-modal :width="1200" v-model:open="openHelpModal" title="操作指引" :footer="null">
    <video controls v-if="openHelpModal" autoplay width="1152" height="550">
      <source src="https://businessmanagement-test.haier.net/hbweb/file/download/obs-swszh1/1714372452-申请帮助.mp4"
        type="video/mp4" />
    </video>
  </a-modal>
  <!-- 没有指标时的提示弹窗 -->
  <a-modal v-model:open="modal2Visible" title="暂无指标" :maskClosable="false" :closable="true" centered
    @ok="modal2Visible = false" :footer="null">
    <p>您还没有任何可分析指标，请点击去申请</p>
    <div style="text-align: right">
      <h-button @click="goApply" type="primary">去申请</h-button>
    </div>
  </a-modal>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.indexListBox {
  // min-height:calc(100vh - 300px);
  width: 100%;
  overflow: auto;
  scrollbar-width: none;
  background: rgb(245, 245, 245);
}

.indexListBox::-webkit-scrollbar {
  width: 0;
}

.ant-form-item {
  margin-bottom: 0;
}

:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.5);
}

.ant-popover-inner-content {
  span {
    font-weight: 600;
    margin-top: 10px;
    display: block;
    font-size: 12px;
  }

  div {
    font-size: 12px;
  }
}

.IndexItembox {
  height: 300px;
}

.topRow {
  position: relative;
}

.fullScreen {
  position: absolute;
  right: 10px;
  font-size: 22px;
  color: #1677ff;
  bottom: 1px;
}

.full {
  margin-left: 10px;
  font-size: 22px;
  color: #1677ff;
}

.titleBox {
  display: grid;
  align-items: center;
  background: #fff;
  height: 100%;
  overflow: auto;
  // display: flex;
  // align-items: center;
  // flex-wrap: wrap;
  // justify-content: center;
  color: #000;
  padding: 6px;
  -ms-overflow-style: none;
  /* IE 和 Edge */
  scrollbar-width: none;

  /* Firefox */
  &::-webkit-scrollbar {
    display: none;
  }

  :deep(p) {
    // width: 100%;
    padding: 0px !important;
    margin: 0px !important;
  }
}

ol,
ul {
  /* margin: 0; */
  padding: revert-layer;
  list-style: auto;
}
</style>
