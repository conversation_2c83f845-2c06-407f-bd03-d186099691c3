<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Popover as hPopover,
  Card as hCard,
  Input as hInput,
  Form as hForm,
  FormItem as hFormItem,
  message,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  QuestionCircleOutlined,
  DeleteOutlined,
  MenuOutlined,
  UploadOutlined
} from '@ant-design/icons-vue';
import { hotelApi } from '@haierbusiness-front/apis';
import { angtListRes, AddressBookListParam,moduleType,operateType } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables';
import { getCurrentRouter, routerParam,getEnumOptions } from '@haierbusiness-front/utils';
// import router from '../../../router';
const router = getCurrentRouter();

const moduleTypeOptions = computed(()=>{
  return getEnumOptions(moduleType,true)
})

const operateTypeOptions = computed(()=>{
  return getEnumOptions(operateType,true)
})

const {
  data: exportListData,
  run: exportListApiRun,
  loading: exportListLoading
} = useRequest(hotelApi.export);

const currentRouter = ref();

const addressBook = ref<angtListRes[]>([]);
const keyWord = ref<string>('');
const visible = ref<boolean>(false);
const editData = ref<angtListRes>({});
const productList = ref<any>([]);
const searchFrom = ref<any>({
  personNum: '',
  productId: null,
  agentId: '',
});
const columns:any = [
  {
    title: '当前业务名称',
    dataIndex: 'currentBusinessName',
  },
  {
    title: '目标业务名称',
    dataIndex: 'targetBusinessName',
  },
  {
    title: '供应商',
    dataIndex: 'providerCodeName',
  },
  {
    title: '模块类型',
    dataIndex: 'moduleType',
  },
  {
    title: '操作类型',
    dataIndex: 'operateType',
  },

  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
  },
  {
    title: '操作人',
    dataIndex: 'createName',
  }
];
const searchParam = ref<any>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(hotelApi.manualMappingRecordList, {
  manual: false
});


const reset = () => {
  searchParam.value = { }
  listApiRun({
    ...searchParam.value,
    pageNum: current.value,
    pageSize: pageSize.value,
  });
}

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const handleCreate = () => {
  visible.value = true;
};


const onDialogClose = () => {
  editData.value = {};
  visible.value = false;
};

// 初始化
onMounted(async () => {
  currentRouter.value = await router;
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize: 10,
  });
});
</script>

<template>
  <div
    v-if="$route.matched.length < 3"
    style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto"
  >
    <h-card style="margin-bottom: 10px">
      <h-form :labelCol="{span:8, offset: 1}">
        <h-row>
          <h-col :span="6">
            <h-form-item label="当前业务名称">
              <h-input allow-clear v-model:value="searchParam.currentBusinessName" placeholder="当前业务名称" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
              <h-form-item label="模块类型">
              <h-select
                ref="city"
                show-search
                :options="moduleTypeOptions"
                v-model:value="searchParam.moduleType"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
          <h-col :span="6">
              <h-form-item label="操作类型">
              <h-select
                ref="city"
                show-search
                :options="operateTypeOptions"
                v-model:value="searchParam.operateType"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>

        </h-row>
      <h-row :align="'middle'">
        <h-col :span="24">
          <h-row :align="'middle'">
            <h-col :span="24" style="text-align: right">
               <h-button
                     type="primary"
                     style="margin-right: 10px"
                     :loading="exportListLoading"
                     @click="exportListApiRun(searchParam)"
                   >
                     <UploadOutlined />导出
                   </h-button>
                <h-button style="margin-right: 10px" @click="reset">重置</h-button>
              <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })"> <SearchOutlined />查询 </h-button>
            </h-col>
          </h-row>
        </h-col>
      </h-row>
      </h-form>
    </h-card>
    <h-table :pagination="pagination" :columns="columns" size="small" :loading="loading"  @change="handleTableChange($event as any)" :data-source="dataSource" bordered>
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'moduleType'">
          {{ moduleType[record.moduleType] }}
        </template>
        <template v-if="column.dataIndex === 'operateType'">
          {{ operateType[record.operateType] }}
        </template>
      </template>
    </h-table>
    <edit-dialog
      :labelList="productList"
      :knowCenterOptions="knowCenterOptions"
      :show="visible"
      :data="editData"
      @cancel="onDialogClose"
      @ok="handleOk"
    >
    </edit-dialog>
  </div>
  <router-view></router-view>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.ml10 {
  margin-left: 10px;
}

// .pagination {
//   width: 100%;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   margin-top: 20px;
// }
</style>
