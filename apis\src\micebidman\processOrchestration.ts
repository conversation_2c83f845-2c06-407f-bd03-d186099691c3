import { download, get, post } from '../request'
import {
    IProcessOrchestrationFilter,
    IProcessOrchestration,
    IPageResponse,
    Result,
    IRuleMetadataResponse,
    IProcess
} from '@haierbusiness-front/common-libs'


export const processOrchestrationApi = {
    list: (params: IProcessOrchestrationFilter): Promise<IPageResponse<IProcessOrchestration>> => {
        return get('/mice-bid/api/framework/process/page', params)
    },
    // 查询支持的权限配置下拉项
    listRoleRule: (params: IProcessOrchestrationFilter): Promise<IPageResponse<IProcessOrchestration>> => {
        return get('/mice-bid/api/framework/process/role/rule/list', params)
    },
    // 集团外企业列表
    listEnterprise: (params: IProcessOrchestrationFilter): Promise<IPageResponse<IProcessOrchestration>> => {
        return get('/system/api/enterprise/getNoBidMerPage', params)
    },

    get: (id: number): Promise<IProcessOrchestration> => {
        return get('/mice-bid/api/framework/process/details', {
            id
        })
    },
    // 提交流程
    save: (params: IProcessOrchestration): Promise<Result> => {
        return post('/mice-bid/api/framework/process/submit', params)
    },

    edit: (params: IProcessOrchestration): Promise<Result> => {
        return post('merchant/api/processOrchestration/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('merchant/api/processOrchestration/delete', { id })
    },

    // 流程编排-资源池-查询所有支持的匹配规则
    listMatchRule: (params: IProcessOrchestrationFilter): Promise<IRuleMetadataResponse> => {
        return get('/mice-bid/api/framework/merchant/group/rule/list', params)
    },

    // 流程编排-资源池-根据选择的条件查询符合条件的服务商
    listConformMerchant: (params: IProcessOrchestration): Promise<Result> => {
        return post('/mice-bid/api/framework/merchant/group/conform/page', params)
    },

    // 查询所有节点列表
    listDefineMeta: (params: IProcessOrchestration): Promise<Result> => {
        return post('/mice-bid/api/framework/define/meta/list', params)
    },

    // 启用/停用流程
    updateStatus: (params: { id: number, status: number }): Promise<Result> => {
        return post('/mice-bid/api/framework/process/enable', params)
    },

    // 查询当前登录用户能够访问的所有流程信息
    listForUser: (): Promise<IProcess[]> => {
        return get('/mice-bid/api/framework/process/withPermission/list')
    },

    // 查询当前登录用户是否有指定流程的权限
    checkRole: (processId: number): Promise<Result> => {
        return get('/mice-bid/api/framework/process/check/role', { processId })
    }
}
