<script lang="ts" setup>
import { computed,createVNode, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';


import {
  Anchor as hAnchor,
  <PERSON><PERSON> as hButton,
  Spin as hSpin,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Checkbox as hCheckbox,
  Form as hForm,
  FormItem as hFormItem,
  Row as hRow,
  Col as hCol,
  Popconfirm as hPopconfirm,
  Upload as hUpload,
  Input as hInput,
  Modal as hModal
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  DownloadOutlined,
  VerticalAlignBottomOutlined,
  ExclamationCircleFilled,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  SendOutlined,
  PhoneOutlined,
  VerticalAlignTopOutlined,
  DeleteOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { download, tripApi, teamListApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import {  ExclamationCircleOutlined } from '@ant-design/icons-vue';

import relativeTime from 'dayjs/plugin/relativeTime';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';

import { CityResponse, CityItem, TCteateTeam ,RRechargeParmas } from '@haierbusiness-front/common-libs';
import { fileApi } from '@haierbusiness-front/apis';
import type { TableColumnType } from 'ant-design-vue';
import { useRouter } from 'vue-router';

const route = ref(getCurrentRoute());
const router = useRouter();

const id = route.value?.query?.id;

const goRecharge = () => {
  router.push('/payman/recharge')
}

dayjs.extend(relativeTime);

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const labelCol = { span: 3 };
const wrapperCol = { span: 12 };
const spinning = ref<boolean>(false)

const rechargeForm = ref<RRechargeParmas>({
  checked: false,
  money: '',
  result: '',
  fileList: []
})

const validateTrue = (_: any, value: boolean) =>
    value === true
      ? Promise.resolve()
      : Promise.reject(new Error('请确认订单'))

const rules: Record<string, Rule[]> = {
  money: [{ required: true, message: '请输入充值金额', trigger: 'change' }],
  result: [{ required: true, message: '请输入充值事由', trigger: 'change' }],
  checked: [{ required: true, validator: validateTrue,  trigger: 'change' }],
  fileList: [{ required: true, message: '请上传人员明细数据', trigger: 'change' }],
};

const formRef = ref()



// 提交
const onSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
    })
    .catch(error => {
    });
}



// 上传相关
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

const uploadLoading = ref(false);
const upload = (options: any) => {
  uploadLoading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);
  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;
      options.file.fileName = options.file.name;
     
      // options.
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .catch((e) => {
      rechargeForm.value.fileList = [];
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

</script>


<template>
  <div class="container">
    <div class="row flex">
      
      <div class="apply-con flex">
        <h-row justify="center" align="middle" style="padding: 40px; font-size: 20px ; font-weight: 600;">
            工会预算支付在线充值
        </h-row>
        <h-form
          class="mt-30"
          ref="formRef"
          :model="rechargeForm"
          :rules="rules"
          :wrapper-col="wrapperCol"
          labelAlign="left"
        >
          
          
          <!-- 人员明细 -->
          <h-form-item name="personList">
          
             <h-row>
              <h3>上传报告:</h3>
             </h-row>
             <h-upload
                v-model:file-list="rechargeForm.fileList"
                name="file"
                accept=".pdf"
                list-type="picture"
                :max-count="1"
                :custom-request="upload"
              >
                <h-button size="small">
                  <upload-outlined class="font-size-14"></upload-outlined>
                  <span class="font-size-14">上传附件</span>
                </h-button>
                <div class="text mt-5">
                  <span class="color-red">仅支持上传pdf格式文件</span>
                </div>
              </h-upload>
          </h-form-item>
          <!-- 充值金额 -->
          <h-form-item name="money">
            <h-row>
              <h3>充值金额:</h3>
            </h-row>
            <h-input v-model:value="rechargeForm.money" suffix="元" />
          </h-form-item>

          <!-- 充值事由 -->
          <h-form-item name="result">
            <h-row>
              <h3>充值事由:</h3>
            </h-row>
            <h-textarea v-model:value="rechargeForm.result" placeholder="请输入留言信息" :auto-size="{ minRows: 3, maxRows: 5 }" allow-clear />

          </h-form-item>

           <!-- 注意事项 -->
          <h-form-item name="checked">
            <h-checkbox v-model:checked="rechargeForm.checked">
              <span style="color: red;">注意事项:订单提交后不可进行修改,请确认上传的订单信息.</span>
            </h-checkbox>
            
           </h-form-item>
           <!--  -->
           <h-form-item :wrapper-col="{  offset: 10 }">
              <h-button type="primary" @click="onSubmit">提交</h-button>
              <h-button style="margin-left: 50px" @click="goRecharge">取消</h-button>
          </h-form-item>

        </h-form>

      </div>
    </div>

  </div>
</template>

<style lang="less" scoped>
@import url(./recharge.less);

.change-title {
  position: absolute;
  right: 0;
  top: -80px;
  width: 300px;

  .ant-col-6,
  .ant-col-18 {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #000000d9;
  }
}
.mt-5 {
  margin-top: 5px;
}

.ml-5 {
  margin-left: 5px;
}
.color-eee {
  color: #a8a7a7;
}

.com-box {
  background: #f5f5f5;
  padding: 20px;
  margin-top: 20px;

  .whole-line {
    height: 40px;
    font-weight: 600;
  }
  .info-box {
    display: flex;
    align-items: flex-start;
    padding: 10px 20px;
    border: 1px solid #ffdb5c;
    background: #fffbd8;
  }
  :deep(.ant-form-item-control-input-content) {
    display: flex;
    align-items: center;
  }
}
.title {
  height: 60px;
}
.download-btn {
  color: #408cff;
  cursor: pointer;
}
:deep(.city-chose-box) {
  width: 200px !important;
}
:deep(.mr-10) {
  margin-right: 10px;
}

.background-eee {
  padding: 10px;
    background: #f4f4f4;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}
</style>