import {
    IOwnApplicationCodesResponse,
    IResourceDeleteRequest,
    IResourceInfo,
    IResourceInfoTreeRequest,
    IResourceInfoTreeResponse,
    IResourceSaveRequest
} from '@haierbusiness-front/common-libs'
import {errorHttpMessageHandle, get, post} from '../request'

export const resourceApi = {

    /**
     * 查询资源树
     */
    searchTrees: (params: IResourceInfoTreeRequest): Promise<IResourceInfoTreeResponse[]> => {
        return get('system/api/resource/trees', params)
    },

    /**
     * 查询自己拥有的资源树
     */
    searchOwnTrees: (params: IResourceInfoTreeRequest): Promise<IResourceInfoTreeResponse[]> => {
        return get('system/api/resource/own/trees', params, undefined, (error) => {
            return errorHttpMessageHandle(error)
        })
    },

    /**
     * 查询自己可访问的应用模块,根据可访问的资源筛选出
     */
    searchApplicationCodes: (): Promise<IResourceInfoTreeResponse[]> => {
        return get('system/api/resource/own/application/codes')
    },

    /**
     * 新增资源
     */
    save: (params: IResourceSaveRequest): Promise<IResourceInfo> => {
        return post('system/api/resource/save', params)
    },

    /**
     * 删除资源
     */
    delete: (params: IResourceDeleteRequest): Promise<void> => {
        return post('system/api/resource/delete', params)
    },

    /**
     * 批量修改资源
     */
    batchUpdate: (params: IResourceSaveRequest[]): Promise<void> => {
        return post('system/api/resource/batch/update', params)
    },
}