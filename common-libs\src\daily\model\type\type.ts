import {IBaseDO} from "../../../basic/model/baseModel";

export class IAnnualPlanTypeUpdateRequest {
    data?: [
        {
            id?: number

            /**
             * 所属年份
             */
            year?: number

            /**
             * 删除标记
             */
            isDeleted?: boolean

            /**
             * 类型名称
             */
            name?: string

            /**
             * 状态
             */
            state?: number

            /**
             * 类型描述
             */
            description?: string
        }?
    ]
}

export class IAnnualPlanTypeListRequest {
    id?: number

    /**
     * 类型名称
     */
    name?: string

    /**
     * 年度类型
     */
    year?: number

    /**
     * 状态
     */
    state?: number

    /**
     * 类型描述
     */
    description?: string
}

export class IAnnualPlanTypeListResponse extends IBaseDO {
    id?: number

    /**
     * 类型名称
     */
    name?: string

    /**
     * 类型名称
     */
    year?: number

    /**
     * 状态
     */
    state?: number

    /**
     * 类型描述
     */
    description?: string
}

