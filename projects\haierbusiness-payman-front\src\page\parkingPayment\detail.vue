<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem,
  Modal
} from "ant-design-vue";
import { ColumnType } from "ant-design-vue/lib/table/interface";
import { Key } from "ant-design-vue/lib/vc-table/interface";
import {
  PlusOutlined,
  SearchOutlined,
  DownOutlined,
  UpOutlined
} from "@ant-design/icons-vue";
import { statementAccountApi } from "@haierbusiness-front/apis";
import {
  BApplyListRecord,
  BApplyPerson,
  BanquetStatusEnum
} from "@haierbusiness-front/common-libs";
import dayjs, { Dayjs } from "dayjs";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { computed, ref, watch, onMounted, createVNode } from "vue";
import { DataType, usePagination, useRequest } from "vue-request";
import { useEditDialog, useDelete } from "@haierbusiness-front/composables";
import {
  getCurrentRouter,
  errorModal,
  routerParam,
  getCurrentRoute
} from "@haierbusiness-front/utils";
import { storeToRefs } from "pinia";
import { applicationStore } from "@haierbusiness-front/utils/src/store/applicaiton";

import router from "../../router";
// const router = useRouter()
const store = applicationStore();

const currentRouter = ref();
const route = ref(getCurrentRoute());
const id = route.value?.query?.id;
const detail = ref<any>();

const { loginUser } = storeToRefs(store);

const getDetail = (id: number) => {
  statementAccountApi.getInfo(id).then(res => {
    detail.value = res;
  });
};
const showMore = ref(false);
const columns: ColumnType[] = [
  {
    title: "预订单号",
    dataIndex: "orderBookingCode",
    align: "center",
    width: "210px",
    ellipsis: true
  },
  {
    title: "美团预订单号",
    dataIndex: "mtBookingCode",
    align: "center",
    width: "200px",
    ellipsis: true
  },
  {
    title: "申请单号",
    dataIndex: "orderCode",
    align: "center",
    width: "200px",
    ellipsis: true
  },
  {
    title: "付款单位",
    dataIndex: "accountCompanyName",
    align: "center",
    width: "150px",
    ellipsis: true
  },
  {
    title: "订单方式",
    dataIndex: "orderType",
    align: "center",
    width: "120px",
    ellipsis: true
  },
  {
    title: "交易时间",
    width: "150px",
    dataIndex: "tradeTime",
    align: "center",
    ellipsis: true
  },
  {
    title: "账单类型",
    dataIndex: "sceneType",
    width: "150px",
    align: "center",
    ellipsis: true
  },
  {
    title: "订单总金额",
    dataIndex: "effectiveSettlementAmount",
    width: "150px",
    align: "center",
    ellipsis: true
  },
  {
    title: "餐费金额",
    dataIndex: "mealAmount",
    align: "center",
    width: "120px",
    ellipsis: true
  },
  {
    title: "服务费金额",
    dataIndex: "realtimeServiceFee",
    align: "center",
    width: "120px",
    ellipsis: true
  },
  {
    title: "个人支付(组合)",
    dataIndex: "staffPayAmount",
    align: "center",
    width: "120px",
    ellipsis: true
  },
  {
    title: "企业支付(有效结算金额)",
    dataIndex: "entPayAmount",
    align: "center",
    width: "180px",
    ellipsis: true
  },
  {
    title: "美团原始结算金额",
    dataIndex: "mtOriginAmount",
    align: "center",
    width: "150px",
    ellipsis: true
  },
  {
    title: "美团结算金额",
    dataIndex: "mtSettlementAmount",
    align: "center",
    width: "120px",
    ellipsis: true
  },
  {
    title: "差异金额",
    dataIndex: "diffAmount",
    align: "center",
    width: "120px",
    ellipsis: true
  },
  {
    title: "订单账结果",
    dataIndex: "settleResult",
    width: "150px",
    align: "center",
    ellipsis: true
  },
  {
    title: "差异备注",
    dataIndex: "diffRemark",
    width: "150px",
    align: "center",
    ellipsis: true
  }
];
// 导出明细
const {
  data:exportBanquetSettleStatementData,
  run:exportBanquetSettleStatement,
  loading:exportBanquetSettleStatementLoading,
} = useRequest(statementAccountApi.exportBanquetSettleStatement);

const { data, run: listApiRun, loading, current, pageSize } = usePagination(
  statementAccountApi.detailList
);
const dataSource = computed(() => data.value?.records || []);
const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: "center" }
}));
const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any
) => {
  listApiRun({
    statementId: id,
    pageNum: pag.current,
    pageSize: pag.pageSize
  });
};

onMounted(async () => {
  currentRouter.value = await router;
  listApiRun({
    statementId: id,
    pageNum: 1,
    pageSize: 10
  });
});

const getInnerPerson = (list?: Array<BApplyPerson>) => {
  if (list && list.length > 0) {
    let resultList = list.filter(item => item.haierUser == true);
    return resultList
      .map(item => `${item.userName}(${item.userCode})`)
      .join(",");
  } else {
    return "";
  }
};

const getOuterPerson = (list?: Array<BApplyPerson>) => {
  if (list && list.length > 0) {
    let resultList = list.filter(item => !item.haierUser);
    return resultList.map(item => item.userName).join(",");
  } else {
    return "";
  }
};

const downloadFile2 = (url: string, name: string) => {
  var xhr = new XMLHttpRequest();
  xhr.open("GET", url, true);
  xhr.responseType = "blob";

  xhr.onload = function() {
    if (xhr.status === 200) {
      var blob = xhr.response;
      var a = document.createElement("a");
      var url = URL.createObjectURL(blob);
      a.href = url;
      a.download = name;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  xhr.send();
};

const downLoadFile = (url: string, name: string) => {
  window.open(url);
  // downloadFile2(url,name)
};

// 订单确认
const orderSure = () => {
  Modal.confirm({
    title: "确定要确认此订单吗?",
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {
      statementAccountApi.orderSure({ id }).then(res => {
        getDetail(id);
      });
    },
    onCancel() {
      console.log("Cancel");
    },
    class: "test"
  });
};

const toList = (code, type) => {
  if (type == 1) {
    currentRouter.value.push({
      path: "/reservation",
      query: {
        orderBookingCode: code
      }
    });
  }else if(type==2){
    currentRouter.value.push({
      path: "/apply",
      query: {
        orderCode: code
      }
    });
  }
};

watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true
  }
);
</script>

<template>
  <div>
    <h-row justify="space-between" style="padding: 20px;">
      <h-col style="font-size: 18px;">对账单详情</h-col>
      <h-col>
        <h-button type="link" @click="currentRouter.back(-1)">返回</h-button>
      </h-col>
    </h-row>
    <div
      style="background-color: #ffff;height: 100%;width: 100%;padding: 30px 60px;overflow: auto;"
    >
      <h-descriptions title="订单详情" style="margin-bottom: 20px;" bordered>
        <h-descriptions-item label="汇总单号">{{ detail?.statementCode }}</h-descriptions-item>
        <h-descriptions-item label="账单开始日期">{{detail?.settleStartDate}}</h-descriptions-item>
        <h-descriptions-item label="账单结束日期">{{ detail?.settleEndDate }}</h-descriptions-item>
        <h-descriptions-item><div v-if="detail?.effectiveSettlementAmount||detail?.effectiveSettlementAmount==0">{{ detail?.effectiveSettlementAmount }}元</div>
          <template  v-slot:label>
            订单总金额 
            <a-tooltip placement="top">
              <template #title>
                <span>（企业+组合）=（餐费金额+服务费）</span>
              </template>
              <ExclamationCircleOutlined />
            </a-tooltip>
          </template>
        </h-descriptions-item>
        <h-descriptions-item><div v-if="detail?.entPayAmount|| detail?.entPayAmount==0">{{ detail?.entPayAmount }}元</div>
          <template  v-slot:label>
            企业支付（有效结算金额）
            <a-tooltip placement="top">
              <template #title>
                <span>订单总金额-组合支付里的个人支付</span>
              </template>
              <ExclamationCircleOutlined />
            </a-tooltip>
          </template>
        </h-descriptions-item>
        <h-descriptions-item label="个人支付（有效结算金额）"><div v-if="detail?.staffPayAmount|| detail?.staffPayAmount==0">{{ detail?.staffPayAmount }}元</div></h-descriptions-item>
        <h-descriptions-item>
          <div v-if="detail?.mtOriginAmount||detail?.mtOriginAmount==0">{{detail?.mtOriginAmount}}元</div>
          <template  v-slot:label>
            美团原始结算金额
            <a-tooltip placement="top">
              <template #title>
                <span>（企业+组合）=（餐费金额+服务费）</span>
              </template>
              <ExclamationCircleOutlined />
            </a-tooltip>
          </template>
        </h-descriptions-item>
        <h-descriptions-item>
          <div v-if="detail?.mtSettlementAmount||detail?.mtSettlementAmount==0">{{detail?.mtSettlementAmount}}元</div>
          <template  v-slot:label>
            美团结算金额
            <a-tooltip placement="top">
              <template #title>
                <span>订单总金额-组合支付里的个人支付</span>
              </template>
              <ExclamationCircleOutlined />
            </a-tooltip>
          </template>
        </h-descriptions-item>
        <h-descriptions-item> <div v-if="detail?.diffAmount||detail?.diffAmount==0">{{ detail?.diffAmount }}元</div> 
          <template  v-slot:label>
            差异金额
            <a-tooltip placement="top">
              <template #title>
                <span>企业支付-美团结算金额</span>
              </template>
              <ExclamationCircleOutlined />
            </a-tooltip>
          </template>
        </h-descriptions-item>
        <h-descriptions-item> 
          <div v-if="detail?.mealAmount||detail?.mealAmount==0">{{ detail?.mealAmount }}元</div>
          <template  v-slot:label>
            餐费金额
            <a-tooltip placement="top">
              <template #title>
                <span>订单总金额-服务费</span>
              </template>
              <ExclamationCircleOutlined />
            </a-tooltip>
          </template>
          </h-descriptions-item>
        <h-descriptions-item>
          <div v-if="detail?.realtimeServiceFee||detail?.realtimeServiceFee==0">
            {{
            detail?.realtimeServiceFee
            }}元
          </div>
          <template  v-slot:label>
            服务费金额
            <a-tooltip placement="top">
              <template #title>
                <span>订单总金额-餐费金额</span>
              </template>
              <ExclamationCircleOutlined />
            </a-tooltip>
          </template>
        </h-descriptions-item>
        <h-descriptions-item label="账单所属年月">{{ detail?.settleMonth }}</h-descriptions-item>
        <h-descriptions-item label="账单类型">{{ detail?.sceneType == 1 ? '宴请' : '外卖' }}</h-descriptions-item>
        <h-descriptions-item
          label="订单状态"
        >{{ detail?.orderStatus == 1 ? '待确认' :detail?.orderStatus == 2?'已确认':'已取消' }}</h-descriptions-item>
        <h-descriptions-item label="确认人">
          <div
            v-if="detail?.confirmPersonCode"
          >{{detail?.confirmPersonName }}({{ detail?.confirmPersonCode }})</div>
        </h-descriptions-item>
        <h-descriptions-item  label="确认时间" :span="3">{{ detail?.confirmTime }}</h-descriptions-item>

      </h-descriptions>
      <div class="headerBox">
        <div class="title">子订单信息</div>
        <h-button  type="primary" :loading="exportBanquetSettleStatementLoading" @click="exportBanquetSettleStatement({statementId:id})">导出明细</h-button>
        <h-button v-if="detail?.orderStatus == 1"  style="margin-left:10px;" type="primary" @click="orderSure">订单确认</h-button>
      </div>
      <h-table
        style="width:100%;"
        :columns="columns"
        :row-key="record => record.id"
        :size="'small'"
        :data-source="dataSource"
        :pagination="pagination"
        :scroll="{ x:1000 }"
        :expand-column-width="100"
        :loading="loading"
        @change="handleTableChange($event as any)"
      >
        <template #bodyCell="{ column, record }">
          <!-- 点击预订单号跳转预订单列表 -->
          <template v-if="column.dataIndex === 'orderBookingCode'">
            <a @click="toList(record.orderBookingCode,1)">{{record.orderBookingCode}}</a>
          </template>
          <!-- 点击申请单号跳转预订单列表 -->
          <template v-if="column.dataIndex === 'orderCode'">
            <a @click="toList(record.orderCode,2)">{{record.orderCode}}</a>
          </template>
          <template v-if="column.dataIndex === 'sceneType'">{{ record.sceneType == 1 ? '宴请' :'外卖' }}</template>
          <template
            v-if="column.dataIndex === 'settleResult'"
          >{{ record.settleResult == 1 ? '对账成功':'对账失败' }}</template>
        </template>
      </h-table>
    </div>
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}
.headerBox {
  width: 100%;
  display: flex;
  justify-content: space-between;
  height: 80px;
  align-items: center;
  .title {
    white-space: nowrap;
    text-overflow: ellipsis;
    flex: auto;
    color: rgba(0, 0, 0, 0.88);
    font-weight: 600;
    font-size: 16px;
    line-height: 1.5;
  }
}
:deep(.ant-descriptions-item-label) {
  width: 200px;
}
:deep(.ant-descriptions-item-content) {
  width: 300px;
}
</style>
