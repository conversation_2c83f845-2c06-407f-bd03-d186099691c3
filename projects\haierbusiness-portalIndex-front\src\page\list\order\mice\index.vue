<template>
    <div class="container">
        
        <h-form ref="from" :model="searchKey" @finish="onReFilterChange" style="width: 100%;" :label-col="labelCol" :wrapper-col="wrapperCol">
            <h-row :gutter="24">
                <h-col :span="8">
                    <h-form-item has-feedback label="会议单号" name="coCode">
                        <h-input v-model:value="searchKey.coCode" placeholder="会议单号" allow-clear/>
                    </h-form-item>
                </h-col>
                <h-col :span="8">
                    <h-form-item has-feedback label="会议名称" name="coName">
                        <h-input v-model:value="searchKey.coName" placeholder="会议名称" allow-clear/>
                    </h-form-item>
                </h-col>
                <h-col :span="8">
                    <h-form-item has-feedback label="会议城市" name="coCity">
                        <h-input v-model:value="searchKey.coCity" placeholder="会议城市" allow-clear/>
                    </h-form-item>
                </h-col>
                <h-col :span="8">
                    <h-form-item has-feedback label="会议负责人" name="coIncharge">
                        <h-input v-model:value="searchKey.coIncharge" placeholder="会议负责人" allow-clear/>
                    </h-form-item>
                </h-col>
            </h-row>
            <h-row justify="center">
                <div class="flexCon">
                <h-space>
                    <h-button @click="handleReset">重置</h-button>
                    <h-button type="primary" v-permission="'banner:top-img:query'" html-type="submit">查询</h-button>
                </h-space>
                </div>
            </h-row>
        </h-form>

        <h-spin :spinning="loading">
            <div class="list" v-if="data && data.length > 0" >
                <div class="order-container" v-for="(order, index) in data" :key="index">
                    <div class="order-header">
                        <div class="order-header-left">
                            <span>会议单号：</span>
                            <span>{{ order.code }} <img :src="shiwanhuoji" class="img" v-if="order.isUrgent === 1" /> </span>
                            <template v-if="order.billCode">
                                <span style="margin-left: 20px;">账单号： </span>
                                <span>{{ order.billCode }} </span>
                            </template>
                            
                            <span style="margin-left: 20px;">会议城市： </span>
                            <span>{{ order.city }} </span>
                            <span style="margin-left: 20px;">会议时间： </span>
                            <span>{{ dayjs(order.startDate).format('YYYY-MM-DD') }} 至 {{ dayjs(order.finishDate).format('YYYY-MM-DD') }} </span>
                            <span style="margin-left: 20px;">经办人： </span>
                            <span>{{ order.handler }} </span>
                        </div>
                        <div class="order-header-right">
                            <span v-if="order.azCodes && order.azCodes.length > 0" style="margin-right: 10px;">{{ order.aoCodes[0] }}/{{ order.azCodes[0] }} </span>
                            <span v-else-if="order.aoCodes && order.aoCodes.length > 0" style="margin-right: 10px;"> {{ order.aoCodes[0] }} </span>
                        </div>
                    </div>
                    <div class="order-body">
                        <div class="first">
                            <h-descriptions :column="3">
                                <h-descriptions-item label="会议名称" :span="3">{{ order.subject }}</h-descriptions-item>
                                <h-descriptions-item label="会议负责人">{{ order.incharge }}</h-descriptions-item>
                                <h-descriptions-item label="负责人电话">{{ order.inchargePhone }}</h-descriptions-item>
                                <h-descriptions-item label="负责人邮箱">{{ order.inchargeEmail }}</h-descriptions-item>
                                <h-descriptions-item label="方案截止">{{ order.interactionTimeLimit && dayjs(order.interactionTimeLimit).format('YYYY-MM-DD HH:mm') }}</h-descriptions-item>
                                <h-descriptions-item label="竞价截止" :span="2">{{ order.biddingTimeLimit && dayjs(order.biddingTimeLimit).format('YYYY-MM-DD HH:mm') }}</h-descriptions-item>
                                <h-descriptions-item :label="order.status >= 130 ? '中标供应商' : ''">{{ order.merName }}</h-descriptions-item>
                                <h-descriptions-item :label="order.havePresent > 0 ? '中标金额(含礼品)' : '中标金额'">{{ order.saAmount }} 元</h-descriptions-item>
                                <h-descriptions-item :label="order.status >= 130 ? '账单金额' : ''">{{ order.status >= 130 ? (order.actualAm || '') + '元' : '' }} </h-descriptions-item>
                            </h-descriptions>
                        </div>
                        <div class="last">
                            <h-row>
                                <h-col class="title" v-if="!order.billCode || order.status < 160">{{ getOrderStatusName(order.status) }}</h-col>
                                <h-col class="title" v-else>{{ getBillStatusName(order.cbStatus) }}</h-col>
                            </h-row>
                        </div>
                    </div>
                    <div class="order-footer">
                        <h-button type="primary" size="small" @click="handle(order.code)">去处理</h-button>
                    </div>
                </div>
            </div>

            <div class="page" v-show="pagination.total && pagination.total > 0">
                <h-pagination v-model:current="pagination.current" show-size-changer show-quick-jumper :total="pagination.total" @change="onPageChange" />
            </div>

            <div class="empty" v-if="!data || data.length === 0">
                <h-empty />
            </div>

        </h-spin>
        
    </div>
    
</template>
<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Tag as hTag,
  Spin as hSpin,
  Empty as hEmpty,
  Space as hSpace,
  Pagination as hPagination,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem,
  message,
} from 'ant-design-vue';
import { useOrderSearch } from "@haierbusiness-front/composables"
import {computed, onMounted, reactive, ref, watch} from 'vue'
import type { MiceType, MiceFilter, MergeStatusType } from '@haierbusiness-front/common-libs'
import { miceListApi } from '@haierbusiness-front/apis'
import { PaymentTypeEnum, RestaurantOrderStateEnum } from '@haierbusiness-front/common-libs'
import { getEnumOptions } from '@haierbusiness-front/utils'
import shiwanhuoji from '@/assets/image/shiwanhuoji.png'
import dayjs from 'dayjs'
import { useRoute } from 'vue-router'

const route = useRoute()

const labelCol = {
    span:8
}
const wrapperCol = { 
    span: 16
}

const businesstravel = import.meta.env.VITE_BUSINESSTRAVEL_URL

const handle = (code: string) => {
  const url = businesstravel + '/mice/?#/personal/mice_conference?model.coCode=' + code
  window.open(url)
}

// 支付类型
const payTypes = computed(() => {
    console.log(getEnumOptions(PaymentTypeEnum, true))
    return getEnumOptions(PaymentTypeEnum, true)
})

// 订单状态
const orderState = computed(() => {
    return getEnumOptions(RestaurantOrderStateEnum, true)
})

const searchKey = reactive<MiceFilter>({
    coCode: '',
    coName: '',
    coCity: '',
    coIncharge: '',
    pageNum: 0,
    pageSize: 0,
})

const { data, fetchData, pagination, loading, onTimeChange, from, onFilterChange, mergeStatus } = useOrderSearch<MiceType, MiceFilter>(miceListApi, searchKey)

const conferenceStatus = computed(() => mergeStatus)
const conferenceOrderStatus = ref<Array<MergeStatusType>>()
const conferenceBillStatus = ref<Array<MergeStatusType>>()

watch(conferenceStatus, (newValue) => {
    if(newValue.value) {
        const orderStatus: Array<MergeStatusType> = []
        const billStatus: Array<MergeStatusType> = []
        newValue.value!.map(item => {
            if(item.statusStr.indexOf('ConferenceOrderStatusEnum') > 0) {
                const status = item.statusStr.replace("&ConferenceOrderStatusEnum", '')
                const data = {
                    statusStr: status,
                    name: item.name
                }
                orderStatus.push(data)
            }
            else {
                const status = item.statusStr.replace("&ConferenceBillStatusEnum", '')
                const data = {
                    statusStr: status,
                    name: item.name
                }
                billStatus.push(data)
            }
        })
        conferenceOrderStatus.value = orderStatus
        conferenceBillStatus.value = billStatus
    }
}, { immediate: true, deep: true })

const getOrderStatusName = (status: number) => {
    const orderStatus = conferenceOrderStatus.value?.find(o => o.statusStr === status.toString())
    if(orderStatus) 
        return orderStatus.name
    else
        return ''
}

const getBillStatusName = (status: number) => {
    if(!status) {
        return ''
    }
    const billStatus = conferenceBillStatus.value?.find(o => o.statusStr === status.toString())
    if(billStatus) 
        return billStatus.name
    else
        return ''
}

const onReFilterChange = () => {
    onFilterChange()
}

const onPageChange = (page: number, pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.current = page
    fetchData()
}

const handleReset = () => {
    from.value && from.value.resetFields()
    onReFilterChange()
}

// watch(token, (newValue) => {
//     if(newValue) {
//         const code = route.query.coCode
//         if(code) {
//             searchKey.coCode = code.toString()
//         }
//         onReFilterChange()
//     }
// }, { immediate: true, deep: true })

</script>

<style scoped lang="less">
.empty {
    margin-top: 50px;
    border: 1px solid #f0f0f0;
    padding: 42px 24px 50px;
  }
.container {
    display: flex;
    width: 100%;
    flex-direction: column;

    .search {
        display: flex;
        width: 100%;
    }
}

.list {
    display: flex;
    margin-top: 20px;
    width: 100%;
    flex-direction: column;

    .order-container {
        display: flex;
        flex-direction: column;
        width: 100%;
        margin-bottom: 20px;

        .order-header {
            width: 100%;
            background-color: #f5f5f5;
            height: 43px;
            line-height: 43px;
            padding-left: 20px;
            color: #333;
            font-size: 12px;
            border: 1px solid #eaeaea;
            display: flex;
            justify-content: space-between;
            font-weight: bold;

            .order-header-left {
                display: flex;
                
                .img {
                    width: 41px;
                    margin-top: -5px;
                }
            }
            .order-header-right {
                display: flex;
                align-items: center;
            }
        }
        .order-body {
            display: flex;
            width: 100%;
            border: 1px solid #eaeaea;
            border-top: 0;
            flex-direction: row;

            .first {
                display: flex;
                flex: 4;
                padding: 24px;

                
            }

            .last {
                display: flex;
                flex: 1;
                border-left: 1px solid #eaeaea;
                padding: 24px;
                justify-content: center;
                align-items: center;
            }
            
        }
        .order-footer {
            width: 100%;
            background-color: #f5f5f5;
            height: 43px;
            line-height: 43px;
            padding-right: 8px;
            color: #333;
            font-size: 12px;
            border-left: 1px solid #eaeaea;
            border-right: 1px solid #eaeaea;
            border-bottom: 1px solid #eaeaea;
            display: flex;
            flex-direction: row-reverse;
            align-items: center;
        }
    }

}

.page {
    display: flex;
    width: 100%;
    flex-direction: row-reverse;
    margin-bottom: 20px;
}
</style>