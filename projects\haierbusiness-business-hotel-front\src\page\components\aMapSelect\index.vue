<script setup>
import { onMounted, onUnmounted,defineEmits   } from "vue";
import AMapLoader from "@amap/amap-jsapi-loader";

let map = null;

onMounted(() => {
  window._AMapSecurityConfig = {
    securityJsCode: "610db7cc7881574494e34fd00b13ab97",
  };
});

// 定义触发的事件及其数据类型
const emit = defineEmits(['selectMark']);

const createByMap = (lon,lat,zoom) =>{
  AMapLoader.load({
    key: "25569e43d6c6bcfa4d39a1b920d8d2d1", // 申请好的Web端开发者Key，首次调用 load 时必填
    version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    plugins: ["AMap.Scale","AMap.PlaceSearch", "AMap.AutoComplete"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
  })
    .then((AMap) => {
      map = new AMap.Map("mapSelect", {
        // 设置地图容器id
        viewMode: "3D", // 是否为3D地图模式
        zoom: zoom?zoom:10, // 初始化地图级别
        center: [lon, lat], // 初始化地图中心点位置
      });
      if(zoom){
        addMarker(lon,lat)
      }
      AMap.plugin(['AMap.Autocomplete','AMap.PlaceSearch'],function(){
          var autoOptions = {
            // 城市，默认全国 
            city: "全国",
            // 使用联想输入的input的id
            input: "tipinput",
            datatype:"all"
          }
          var autocomplete= new AMap.Autocomplete(autoOptions)

          var placeSearch = new AMap.PlaceSearch({
            city:'',
            map:map
          })
          AMap.Event.addListener(autocomplete, 'select', function(e){
            console.log(e,"-----------------")
            addMarker(e.poi.location.lng,e.poi.location.lat)
            setZoomAndCenter(e.poi.location.lng,e.poi.location.lat)
          })
        })
        map.on("click", function(ev) {
        // 触发事件的对象
        const target = ev.target;
        // 触发事件的地理坐标，AMap.LngLat 类型
        const lnglat = ev.lnglat;
        addMarker(ev.lnglat.lng,ev.lnglat.lat)
        setZoomAndCenter(ev.lnglat.lng,ev.lnglat.lat)
      });
    })
    .catch((e) => {
      console.log(e);
    });
}

const addMarker = (lon,lat) =>{
  //创建一个 Marker 实例：
  const marker = new AMap.Marker({
    position: new AMap.LngLat(lon, lat), //经纬度对象，也可以是经纬度构成的一维数组[116.39, 39.9]
  });
  //将创建的点标记添加到已有的地图实例：
  map.clearMap()
  map.add(marker);
  emit('selectMark', {lon,lat});
  // https://restapi.amap.com/v5/place/text?key=25569e43d6c6bcfa4d39a1b920d8d2d1&keywords=北京大学&types=141201&region=北京市
}


const setZoomAndCenter = (lon,lat) =>{
  map.setZoomAndCenter(14, [lon, lat]);
}


defineExpose({
  addMarker,
  createByMap,
  setZoomAndCenter
});
onUnmounted(() => {
  map?.destroy();
});
</script>

<template>
  <div  id="mapSelect">
  </div>
  <div class="info">
    <div class="input-item">
      <div class="input-item-prepend">
        <span class="input-item-text" style="width:8rem;">请输入关键字</span>
      </div>
      <input id="tipinput" type="text">
    </div>
  </div>
</template>

<style scoped>
#mapSelect {
  width: 100%;
  height: 100%;
  position: relative;
}
.input-textarea{
   color:grey;
   height:8em;
   overflow:auto;
   border-radius:0.4rem;
   border:1px solid #ced4da;
   margin-bottom:1rem;
}
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  line-height: 1.5;
  font-weight: 300;
  color: #111213;
}

hr {
  margin: 0.5rem 0;
  box-sizing: content-box;
  height: 0;
  overflow: visible;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, .1)
}

p {
  margin-top: 0;
  margin-bottom: 0;
}

label {
  display: inline-block;
  margin-bottom: 0.4rem;
}

label, .btn {
  margin-left: 0;
  font-size: 1rem;
}

button, input, select {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  overflow: visible;
  text-transform: none
}

[type=button]::-moz-focus-inner, [type=reset]::-moz-focus-inner, [type=submit]::-moz-focus-inner, button::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

input[type=checkbox], input[type=radio] {
  box-sizing: border-box;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0;
  margin: 0 0.5rem 0 0;
}

h4 {
  font-family: inherit;
  line-height: 1.8;
  font-weight: 300;
  color: inherit;
  font-size: 1.1rem;
  margin-top: 0;
  margin-bottom: .5rem
}

.btn {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border: 1px solid transparent;
  transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
  background-color: transparent;
  background-image: none;
  color: #25A5F7;
  border-color: #25A5F7;
  padding: .25rem .5rem;
  line-height: 1.5;
  border-radius: 1rem;
  -webkit-appearance: button;
  cursor:pointer;
}

.btn:hover {
  color: #fff;
  background-color: #25A5F7;
  border-color: #25A5F7
}

.btn:hover {
  text-decoration: none
}

.input-item {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  height: 3rem;
}

.input-item:last-child {
  margin-bottom: 0;
}

.input-item>select, .input-item>input[type=text], .input-item>input[type=date] {
  position: relative;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  width: 1%;
  margin-bottom: 0;
}

.input-item>select:not(:last-child), .input-item>input[type=text]:not(:last-child), .input-item>input[type=date]:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0
}

.input-item>select:not(:first-child), .input-item>input[type=text]:not(:first-child), .input-item>input[type=date]:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0
}

.input-item-prepend {
  margin-right: -1px;
}

.input-item-text, input[type=text],input[type=date], select {
  height: calc(2.2rem + 2px);
}

.input-item-text {
  width: 6rem;
  text-align: justify;
  padding: 0.4rem 0.7rem;
  display: inline-block;
  text-justify: distribute-all-lines;
  /*ie6-8*/
  text-align-last: justify;
  /* ie9*/
  -moz-text-align-last: justify;
  /*ff*/
  -webkit-text-align-last: justify;
  /*chrome 20+*/
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 0;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: .25rem;
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}

.input-item-text input[type=checkbox], .input-item-text input[type=radio] {
  margin-top: 0
}

.input-card {
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border-radius: .25rem;
  width: 22rem;
  border-width: 0;
  border-radius: 0.4rem;
  box-shadow: 0 2px 6px 0 rgba(114, 124, 245, .5);
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 0.75rem 1.25rem;
}

.input-text {
  line-height: 2rem;
  margin-right: 2rem;
}

.info hr {
  margin-right: 0;
  margin-left: 0;
  border-top-color: grey;
}

.info {
  padding: .75rem 1.25rem;
  margin-bottom: 1rem;
  border-radius: .25rem;
  position: absolute;
  top: 100px;
  background-color: white;
  width: auto;
  min-width: 22rem;
  border-width: 0;
  right: 40px;
  box-shadow: 0 2px 6px 0 rgba(114, 124, 245, .5);
}

.code {
  left: 1.5rem;
  right: 1.5rem;
  top: 1.5rem;
  bottom: 1.5rem;
  overflow: auto;
  margin-bottom: 0rem;
}

.code .btn {
  top: 1rem;
  position: absolute;
  right: 1rem;
}

.code .result {
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
  padding: 1rem;
  bottom: 1rem;
  position: absolute;
  top: 5.5rem;
  right: 1rem;
  left: 1rem;
  overflow: auto;
}

.code .status {
  color: #80adff;
  display: inline-block;
  font-size: 14px;
}

.code h4 {
  display: inline-block;
  max-width: 20rem;
  margin-right: 1rem;
  margin-bottom: 1rem;
}

select, input[type=text], input[type=date] {
  display: inline-block;
  width: 100%;
  padding: .375rem 1.75rem .375rem .75rem;
  line-height: 1.5;
  color: #495057;
  vertical-align: middle;
  background: #fff url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat right .75rem center;
  background-size: 8px 10px;
  border: 1px solid #ced4da;
  border-radius: .25rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none
}

input[type=text],input[type=date] {
  background: #fff;
  padding: .375rem .75rem;
}

select:focus, input[type=text]:focus, input[type=date]:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 .1rem rgba(128, 189, 255, .1)
}

.btn:focus {
  outline: 0;
  box-shadow: none;
}

select:focus::-ms-value, input[type=text]:focus::-ms-value,input[type=date]:focus::-ms-value {
  color: #495057;
  background-color: #fff
}

</style>
