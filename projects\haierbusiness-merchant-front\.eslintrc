{"extends": ["airbnb-base", "prettier", "plugin:@typescript-eslint/recommended", "plugin:vue/essential"], "env": {"browser": true, "node": true, "jest": true, "es6": true}, "globals": {"cy": "readonly"}, "plugins": ["vue", "@typescript-eslint"], "parserOptions": {"parser": "@typescript-eslint/parser", "sourceType": "module", "allowImportExportEverywhere": true, "ecmaFeatures": {"jsx": true}}, "rules": {"@typescript-eslint/ban-ts-ignore": 0, "@typescript-eslint/no-explicit-any": 0, "@typescript-eslint/no-require-imports": 0, "@typescript-eslint/no-var-requires": 0, "@typescript-eslint/prefer-for-of": 0, "@typescript-eslint/explicit-function-return-type": 0, "@typescript-eslint/explicit-module-boundary-types": 0, "import/no-extraneous-dependencies": 0, "import/extensions": 0, "import/no-unresolved": 0, "indent": [2, 2], "camelcase": 0, "class-methods-use-this": 0, "new-cap": 0, "no-new": 1, "no-shadow": 0, "no-console": 0, "no-underscore-dangle": 0, "no-confusing-arrow": 0, "no-plusplus": ["error", {"allowForLoopAfterthoughts": true}], "no-param-reassign": 0, "func-style": 0, "prefer-default-export": 0, "max-len": 0, "consistent-return": 0}, "overrides": [{"files": ["*.vue"], "rules": {"vue/return-in-computed-property": 1, "vue/order-in-components": 2, "vue/component-name-in-template-casing": [2, "kebab-case"], "vue/require-default-prop": 0, "@typescript-eslint/explicit-module-boundary-types": "off", "import/order": "off"}}, {"files": ["src/*", "*.js"], "rules": {"no-var-requires": 0, "no-console": 0, "no-unused-expressions": 0, "@typescript-eslint/explicit-module-boundary-types": "off", "import/order": "off"}}]}