<script setup lang="ts">
import { onMounted, ref, watch,onUnmounted } from 'vue';
import type { CascaderProps } from 'ant-design-vue';
import { Cascader } from 'ant-design-vue';

interface Props {
    travelType?: number
    leftNum?: string
}

const props = withDefaults(defineProps<Props>(), {
    travelType: 1
});

// 单程，返程
const travelType = ref(props.travelType)
const leftNum = ref(props.leftNum)

watch(props, (newValue) => {
    travelType.value = newValue.travelType
    leftNum.value = newValue.leftNum ?? '1'
})

const options: CascaderProps['options'] = [
    {
        value: 'sd',
        label: '山东',
        children: [
            {
                value: 'qd',
                label: '青岛',
            },
            {
                value: 'jn',
                label: '济南',
            },
            {
                value: 'jining',
                label: '济宁',
            },
            {
                value: 'yt',
                label: '烟台',
            },
            {
                value: 'wh',
                label: '威海',
            },
            {
                value: 'dz',
                label: '德州',
            },
        ],
    },
    {
        value: 'bj',
        label: '北京'
    },
    {
        value: 'js',
        label: '江苏',
        children: [
        {
            value: 'dz',
            label: '南京',
        },
        ]
    },
]

const value = ref<string[]>([]);
const value1 = ref<string[]>([]);



</script>


<template>
    <div class="apply-city-component" :class="{ 'international-left-width': travelType === 3 }">
        <div v-if="travelType === 3" class="left">{{ leftNum }}</div>
        <div class="ticket-item-city" >
            <div class="ticket-item-ai" :class="{ 'left-radius': travelType === 3 }">
                <div class="item-labels">出发城市</div>
                <a-cascader v-model:value="value"  :options="options" placeholder="城市">
                    <template #displayRender="{ labels, selectedOptions }">
                      {{ labels[labels.length - 1] }}
                    </template>
                </a-cascader>
            </div>
            <img src="../../../../assets/image/banner/turn.png" alt="" >
            <div class="ticket-item-ai">
                <div class="item-labels">到达城市</div>
                <a-cascader v-model:value="value1" :options="options" placeholder="城市">
                    <template #displayRender="{ labels, selectedOptions }">
                      {{ labels[labels.length - 1] }}
                    </template>
                </a-cascader>
            </div>
        </div>
    </div>
    
</template>

<style lang="less" scoped>
@import url('./common.less');

.apply-city-component {
    display: flex;
    flex-direction: row;
    .left {
    width: 18px;
    height: 54px;
    border-radius: 8px 0px 0px 8px;
    background: #3983E5;
    display: flex;
    font-size: 14px;
    color: #FFFFFF;
    line-height: 20px;
    justify-content: center;
    align-items: center;
  }
}

</style>

<style>
.apply-city-component .ant-select-selector {
  border:none !important;
  box-shadow: none !important;
  height: 22px !important;
  padding: 0px !important;
  
}

.apply-city-component .ant-select-selection-placeholder {
    font-size: 16px !important;
    color: rgba(0,0,0,0.35) !important;
    
    padding-inline-end: 25px !important;
    display: flex;
    align-items: center;
}

.apply-city-component .ant-select-selection-item {
    display: flex;
    align-items: center;
    font-size: 16px !important;
    
    color: rgba(0,0,0,0.85);
}

.apply-city-component .ant-select-item-option-content {
  
}

.apply-city-component .ant-cascader {
    width: 100%;
}

.ant-cascader-menus {
    
}
</style>
