<script lang="ts" setup>
import { Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
     Input as hInput, Textarea as hTextarea, Upload as hUpload, Button as hButton } from 'ant-design-vue';
import { computed, ref, watch } from "vue";
import type { Ref } from "vue";
import { UploadOutlined, UserOutlined, NotificationOutlined, AppstoreOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons-vue';
import { fileApi } from '@haierbusiness-front/apis';
import { message,Upload } from 'ant-design-vue';
import type { UploadProps } from 'ant-design-vue';

import {
  IEnterprise
} from '@haierbusiness-front/common-libs';

interface Props {
    show: boolean;
    list: any
    data: IEnterprise | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  list: []
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: IEnterprise = {
    name: '',
    code: '',
    state: 0,
    id: null,
    description: ''
};

const rules = {
  areaName: [{ required: true, message: "请输入领域名称！" }],
  accountCompanyName: [{ required: true, message: "请输入法人公司名称！" }],

  accountCompanyCode: [{ required: true, message: "请输入法人公司编码！" }],

  
};

const enterprise: Ref<IEnterprise> = ref(
({ ...props.data } as IEnterprise) || defaultData
);

watch(props, (newValue) => {
    enterprise.value = ({ ...newValue.data } as IEnterprise) || defaultData;
});

const emit = defineEmits(["cancel", "ok"]);

const visible = computed(() => props.show);
const list = computed(() => props.list);


const handleOk = () => {
confirmLoading.value = true;
from.value
    .validate()
    .then(() => {
    emit("ok", enterprise.value, () => {
        confirmLoading.value = false;
    });
    // 不管是否成功 延时一秒关闭loading
    setTimeout(() => {
      confirmLoading.value ? confirmLoading.value = false: '';
    }, 1000)
    })
    .catch(() => {
        confirmLoading.value = false;
    });
};
const uploadLoading = ref(false);

const baseUrl = import.meta.env.VITE_BUSINESS_URL;



</script>

<template>
    <h-modal
      v-model:open="visible"
      :title="enterprise.id ? '编辑领域' : '新增领域'"
      :width="600"
      @cancel="$emit('cancel')"
      :confirmLoading="confirmLoading"
      @ok="handleOk"
    >
      <h-form
        ref="from"
        :model="enterprise"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 17 }"
        :rules="rules"
      >
        <h-form-item label="领域名称" name="areaName" >
          <h-input v-model:value.trim="enterprise.areaName" />
        </h-form-item>

        <h-form-item label="法人公司名称" name="accountCompanyName" >
          <h-input v-model:value.trim="enterprise.accountCompanyName" />
        </h-form-item>

        <h-form-item label="法人公司编码" name="accountCompanyCode" >
          <h-input v-model:value.trim="enterprise.accountCompanyCode" />
        </h-form-item>
        
       
      </h-form>
    </h-modal>
</template>


<style lang="less" scoped>
.important {
color: red;
}
</style>
  