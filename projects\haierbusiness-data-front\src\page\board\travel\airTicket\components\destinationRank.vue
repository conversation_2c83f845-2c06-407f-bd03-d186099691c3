<template>
    <Rank background="rgba(0,0,0,0)" :data="rankData" unit="人次" :base="5" />
</template>
<script setup lang="ts">
import Rank from "../../../components/rank.vue";
import { queryAirlineDestinationRank } from "@haierbusiness-front/apis/src/data/board/travel";
import { onMounted, ref } from "vue";
import { EventBus } from "../../../eventBus";
const props = defineProps({
    gngj: {
        type: [String, Number],
        default: "1",
    },
});
const rankData = ref(
    [] as Array<{
        name: string;
        value: string | number;
    }>
);
const loading = ref(false);
EventBus.on((event) => {
    if (event == "refresh") queryData();
});


const queryData = async () => {
    loading.value = true;

    console.log( { gngj: props.gngj }  )
    const data = await queryAirlineDestinationRank({ gngj: props.gngj });
    loading.value = false;
    const rows = [] as Array<{
        name: string;
        value: string | number;
    }>;
    data.rows.forEach((item) => {
        rows.push({
            name: item[0],
            value: item[1],
        });
    });
    // rows.sort((a,b)=>b.value-a.value);
    rankData.value = rows;
};
onMounted(() => {

    console.log('start=======>>>>')
    queryData()
})
</script>
