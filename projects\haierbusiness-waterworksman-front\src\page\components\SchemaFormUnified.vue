<!-- Ant Design Vue -->
<!-- 统一风格的表单查询组件 -->
<template>
  <h-row :align="'middle'" :gutter="gutter" style="padding: 10px 10px 0px 10px">
    <template v-for="item in props.fieldList" :key="item.key">
      <h-col :span="2" style="text-align: right; padding-right: 10px">
        <label :for="item.key">{{ item.label }}：</label>
      </h-col>
      <h-col :span="4">
        <component :is="item.type" v-model:value="formData[item.name]" v-bind="{ ...item.fieldProps }" :id="item.key">
          {{ item.valueText }}
        </component>
      </h-col>
    </template>
    <h-col :span="24" style="text-align: right; padding-right: 10px">
      <slot name="search"></slot>
    </h-col>
  </h-row>
</template>

<script setup lang="ts">
import { Col as hCol, Row as hRow } from 'ant-design-vue';
import type { ComputedRef } from 'vue';

/**
 * 表单字段类型定义
 */
export type FieldType = {
  label: string; // 表单标签名
  name: string; // 表单字段名
  key: string; // 表单字段key，用于v-for的唯一标识
  type?: string; // 组件类型，如a-input, a-select等
  span?: number; // 组件宽度，栅格列数
  rules?: any[]; // 表单验证规则
  valueText?: string; // 文本值 -- 只读场景下使用
  isShow?: boolean | ComputedRef<boolean>; // 是否显示该字段
  readonly?: boolean; // 是否只读
  fieldProps?: Record<string, any>; // 组件属性配置
  formItemProps?: Record<string, any>; // FormItem组件属性
};

// 组件默认属性
const props = withDefaults(
  defineProps<{
    formData: Record<string, any>; // 表单数据对象
    column?: Partial<Record<'xs' | 'sm' | 'md' | 'lg' | 'xl', number>>; // 响应式栅格配置
    gutter?: number | [number, number]; // 栅格间隔
    fieldList: FieldType[]; // 字段配置数组
    readonly?: boolean; // 整个表单是否只读
    formItemProps?: Record<string, any>; // 所有FormItem的默认属性
  }>(),
  {
    fieldList: () => [],
    gutter: () => [0, 10] as [number, number], // 修复类型错误
    column: () => ({ xl: 6, lg: 6, md: 8, sm: 12, xs: 24 }),
    readonly: false,
  },
);
</script>
