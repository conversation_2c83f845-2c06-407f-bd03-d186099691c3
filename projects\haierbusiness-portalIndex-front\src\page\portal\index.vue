<script setup lang="ts">
import { Carousel as hCarousel, message } from 'ant-design-vue';
import { onMounted, ref, computed } from 'vue';
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Pagination, Scrollbar, A11y } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/scrollbar';
import { advertisementListApi, informationApi, lifeListApi } from '@haierbusiness-front/apis';
import { usePagination, useRequest } from 'vue-request';
import hotel from '@/assets/image/banner/hotel.png'
import hotel1 from '@/assets/image/banner/hotel1.png'
import hotel2 from '@/assets/image/banner/hotel2.png'
import hotel3 from '@/assets/image/banner/hotel3.png'
import router from '../../router'
import activity from '../card/activity.vue'
import youXuan from '../card/youxuan.vue'

const currentRouter = ref()

onMounted(async () => {
    currentRouter.value = await router
})

// 推广位
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(advertisementListApi.queryAdvertisement, {
  defaultParams: [
    {
      showStatus: 1
    }
  ],
  manual: false
});

const dataSource = computed(() => data.value?.records || []);

// 资讯
const {
  data: infoData,
  run: infoListApiRun,
  loading: infoLoading,
} = useRequest(informationApi.top, {
  defaultParams: [
    {
      showStatus: 1
    }
  ],
  manual: false
});

const infoDataSource = computed(() => infoData.value || []);

// 园区生活
const {
  data: lifeData,
  run: lifeListApiRun,
  loading: lifeLoading,
} = useRequest(lifeListApi.life, {
  defaultParams: [
    {
      showStatus: 1
    }
  ],
  manual: false
});

const lifeDataSource = computed(() => lifeData.value || []);

const gotoUrlOrDetail = (data: {jumpLinkPc?: string, id?: number}, url: string) => {
  if (data.jumpLinkPc) {
    window.open(data.jumpLinkPc)
  } else {
    if(!data.id) {
      message.error('未对应信息！')
      return
    }
    const thisUrl = currentRouter.value.resolve({
      path: url,
    })
    window.open(thisUrl.href + '?id=' + data.id)
  }
}

const gotoUrl = (url: string | undefined) => {
  if(!url){
    message.error('链接错误！')
    return
  }
  const thisUrl = currentRouter.value.resolve({
    path: url,
  })
  window.open(thisUrl.href, '_blank')
  // router.push({ path: url })
}

const openNewWindow = (url: string) => {
  window.open(url, '_blank')
}

const modules =ref([Navigation, A11y])


// hover效果
const isBannerHover = ref(false)

const setIsBannerHover = (value: boolean) => {
  isBannerHover.value = value
}

const isLifeHover = ref(false)

const setIsLifeHover = (value: boolean) => {
  isLifeHover.value = value
}

const isSwiperHover = ref(false)

const setIsSwiperHover = (value: boolean) => {
  isSwiperHover.value = value
}

</script>

<template>
    <div class="container">
        <div class="row">
          <div class="banner">
            <h-carousel :autoplay="true" arrows>
              <template #prevArrow>
                <div :class="isBannerHover ? 'custom-slick-arrow' : ''" style="left: 10px; z-index: 1" @mousemove="setIsBannerHover(true)">
                  <LeftOutlined />
                </div>
              </template>
              <template #nextArrow>
                <div :class="isBannerHover ? 'custom-slick-arrow' : ''" style="right: 10px" @mousemove="setIsBannerHover(true)">
                  <RightOutlined />
                </div>
              </template>
              <div class="advertisement pointer" v-for="(item , index) in dataSource" :key="index"  @mousemove="setIsBannerHover(true)" @mouseleave="setIsBannerHover(false)" @click="gotoUrlOrDetail(item, '/travel/adDetail')">
                <img :src="item.imgUrl" class="img">
              </div>
            </h-carousel>
          </div>
          <div class="info">
            <div class="card-header">
              <div class="card-title">
                商务资讯
              </div>
              <div class="card-more" @click="gotoUrl('/travel/index')">
                查看更多>
              </div>
            </div>
            <div class="card-con">
              <div class="info-con pointer" v-for="(item, index) in infoDataSource" :key="index" @click="gotoUrlOrDetail(item, '/travel/infoDetail')">
                <div class="info-left" >
                  <div class="info-title">
                    {{ item.infoTitle }}
                  </div>
                  <div class="info-time">
                    {{ item.infoAuthor }} {{ item.infoDate }}
                  </div>
                </div>
                <div class="info-right">
                  <img :src="item.imgUrl" class="info-img" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="swiper-con">
            <div class="card-header">
              <div class="card-title">
                热门推荐
              </div>
              <div class="card-more">
                
              </div>
            </div>
            <div class="card-con" @mousemove="setIsSwiperHover(true)" @mouseleave="setIsSwiperHover(false)">
              <swiper
                class="swiper-width"
                :modules="modules"
                :slides-per-view="3"
                :space-between="50"
                :navigation="isSwiperHover"
              >
                <swiper-slide class="swiper-hotel">
                  <div class="hotel pointer" @click="openNewWindow('https://businesstravel.haier.net/localhotel/#/hoteldetail?id=78')">
                    <div class="hotel-img">
                      <img :src="hotel" class="img" />
                    </div>
                    <div class="hotel-title">
                      <div class="hotel-name">
                        海尔山庄
                      </div>
                      <div class="hotel-address">
                        青岛崂山仰口风景区
                      </div>
                    </div>
                  </div>
                </swiper-slide>
                <swiper-slide class="swiper-hotel">
                  <div class="hotel pointer" @click="openNewWindow('https://businesstravel.haier.net/localhotel/#/hoteldetail?id=146')">
                    <div class="hotel-img">
                      <img :src="hotel1" class="img" />
                    </div>
                    <div class="hotel-title">
                      <div class="hotel-name">
                        沧海之粟
                      </div>
                      <div class="hotel-address">
                        青岛市崂山区东海东路52号
                      </div>
                    </div>
                  </div>
                </swiper-slide>
                <swiper-slide class="swiper-hotel">
                  <div class="hotel pointer" @click="openNewWindow('https://businesstravel.haier.net/localhotel/#/hoteldetail?id=145')">
                    <div class="hotel-img">
                      <img :src="hotel2" class="img" />
                    </div>
                    <div class="hotel-title">
                      <div class="hotel-name">
                        南海德音（南海路）
                      </div>
                      <div class="hotel-address">
                        青岛市市南区南海路6号
                      </div>
                    </div>
                  </div>
                </swiper-slide>
                <swiper-slide class="swiper-hotel">
                  <div class="hotel pointer" @click="openNewWindow('https://businesstravel.haier.net/localhotel/#/hoteldetail?id=83')">
                    <div class="hotel-img">
                      <img :src="hotel3" class="img" />
                    </div>
                    <div class="hotel-title">
                      <div class="hotel-name">
                        海尔洲际酒店
                      </div>
                      <div class="hotel-address">
                        青岛市市南区澳门路98号
                      </div>
                    </div>
                  </div>
                </swiper-slide>
              </swiper>
            </div>
          </div>
          <div class="life">
            <div class="card-header">
              <div class="card-title">
                服务公告
              </div>
              <div class="card-more">

              </div>
            </div>
            <div class="life-con">
              <h-carousel :autoplay="true" arrows>
                <template #prevArrow>
                  <div :class="isLifeHover ? 'custom-slick-arrow' : ''" style="left: 10px; z-index: 1" @mousemove="setIsLifeHover(true)">
                    <LeftOutlined />
                  </div>
                </template>
                <template #nextArrow>
                  <div :class="isLifeHover ? 'custom-slick-arrow' : ''" style="right: 10px" @mousemove="setIsLifeHover(true)">
                    <RightOutlined />
                  </div>
                </template>
                <div class="life-img pointer" v-for="(item, index) in lifeDataSource" :key="index" @mousemove="setIsLifeHover(true)" @mouseleave="setIsLifeHover(false)" @click="gotoUrlOrDetail(item, '/travel/lifeDetail')">
                  <img :src="item.imgUrl" class="img" >
                </div>
              </h-carousel>
            </div>
          </div>
        </div>
        <div class="row">
          <activity />
          <you-xuan />
        </div>
    </div>
</template>

<style scoped lang="less">

.pointer {
  cursor: pointer;
}

.sub-title {
  color: #3983E5;
  font-size: 14px;
  font-weight: 400;
}

.container {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  background-color: #F5F5F5;
  padding-bottom: 85px;

  .row {
    width: 1280px;
    margin-top: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .banner {
      width: 888px;
      height: 427px;
      border-radius: 8px;

      .advertisement {
        position: relative;
        width: 100%;
        height: 100%;
        border-radius: 8px;

        .img {
          width: 889px;
          height: 427px;
          border-radius: 8px;
          padding-left: 1px;
          padding-right: 1px;
        }

        .desc {
          position: absolute;
          width: 600px;
          height: 120px;
          left: 20px;
          bottom: 20px;
          background: rgba(0, 0, 0, 0.35);
          padding: 21px 17px 26px 26px;
          color: #FFF;

          .banner-title {
            
            font-size: 20px;
            font-weight: 500;
            line-height: 20px;
          }

          .banner-desc {
            font-size: 14px;
            font-weight: 400;
            line-height: 21px;
            margin-top: 17px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
      }
    }

    .card-header {
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: space-between;

      .card-title {
        color: #000;
        font-size: 24px;
        font-weight: 600;
        line-height: 20px;
        display: flex;
      }

      .card-more {
        display: flex;
        color: #3983E5;
        font-size: 14px;
        font-weight: 400;
        line-height: 14px;
        cursor: pointer;
        
      }
    }

    .info {
      width: 349px;
      height: 427px;

      .card-con {
        margin-top: 16px;
        display: flex;
        width: 100%;
        height: 388px;
        border-radius: 8px;
        background: #FFF;
        padding: 0px 30px;
        flex-direction: column;
        font-family: "Microsoft YaHei";

        .info-con:last-child {
          border-bottom: 0px solid rgba(0,0,0,.06);
        }

        .info-con {
          display: flex;
          flex-direction: row;
          height: 84px;
          margin-top: 28px;
          width: 100%;
          align-items: center;
          justify-content: space-between;
          border-bottom: 1px solid rgba(0,0,0,.06);
          padding-bottom: 10px;

          .info-left {
            width: 203px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .info-title {
              display: flex;
              width: 100%;
              color: #000;
              font-size: 14px;
              font-weight: 600;
              line-height: 21px;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }
            .info-time {
              display: flex;
              width: 100%;
              font-size: 14px;
              font-weight: 400;
              line-height: 14px;
              color: #BFBFBF;
            }
          }

          .info-right {
            display: flex;
            width: 77px;
            height: 100%;
            align-items: center;

            .info-img {
              display: flex;
              width: 77px;
              height: 60px;
            }
          }
        }
      }
    }
    
    .swiper-hotel {
      width: 282px !important;
      height: 272px !important;
      margin-right: 20px !important;
    }

    .swiper-con {
      width:889px;
      height: 272px;

      .card-con {
        display: flex;
        height: 272px;
        width: 100%;
        margin-top: 16px;

        .swiper-width {
          width: 100%;
        }

        .hotel {
          width: 282px;
          height: 272px;
          background-color: #FFF;
          border-radius: 8px;
          display: flex;
          flex-direction: column;

          .hotel-img {
            width: 282px;
            height: 176px;
            border-radius: 8px;

            .img {
              width: 100%;
              height: 100%;
              border-radius: 8px 8px 0 0;
            }
          }

          .hotel-title {
            padding: 24px 20px;

            .hotel-name {
              color: #000;
              font-size: 18px;
              font-weight: 600;
              line-height: 18px;
            }

            .hotel-address {
              font-family: 'HarmonyLight';
              color: #000;
              font-size: 14px;
              font-weight: 400;
              line-height: 14px;
              margin-top: 10px;
            }
          }
        }
      }
    }

    .life {
      width: 349px;
      height: 310px;

      .life-con {
        width: 349px;
        height: 272px;
        margin-top: 16px;
        border-radius: 8px;

        .life-img {
          width: 349px;
          height: 272px;
          border-radius: 8px;

          .img {
            width: 349px;
            height: 272px;
            border-radius: 8px;
            padding-left: 1px;
            padding-right: 1px;
          }
        }
      }
    }

  }
}

:root {
  // font-family: HarmonyBold, Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}
</style>

<style>

.life .slick-arrow.custom-slick-arrow {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: #fff;
  background-color: rgba(31, 45, 61, 0.11);
  opacity: 0.8;
  z-index: 1;
}

.banner .slick-arrow.custom-slick-arrow {
  width: 36px;
  height: 36px;
  font-size: 36px;
  color: #fff;
  opacity: 0.8;
  z-index: 1;
}

.banner .custom-slick-arrow:before,
.life .custom-slick-arrow:before
{
  display: none;
}

.swiper-button-prev, .swiper-button-next {
  color: #FFF;
}

.swiper-button-prev:after, .swiper-button-next:after {
  font-size: 36px;
}

</style>