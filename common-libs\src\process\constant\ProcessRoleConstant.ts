
type keys = 'FIXED' | 'HAIER_MANAGER' | 'HAIER_SECOND_LINE' | 'Mice_Approve' | 'MICE_MANAGER' | 'Hbc_Budget_Manager2' | 'Budget_Manager2' | 'CUSTOMIZE';

export const ProcessRoleConstant = {
    FIXED: { "code": 'Fixed', "desc": "固定人" },
    HAIER_MANAGER: { "code": 'Haier_Manager', "desc": "海尔直线" },
    HAIER_SECOND_LINE: { "code": 'Haier_Second_Line', "desc": "海尔二线" },
    Mice_Approve: { "code": 'Mice_Approve', "desc": "会务审批人" },
    MICE_MANAGER: { "code": 'Mice_Manager', "desc": "会务直线经理" },
    HBC_BUDGET_MANAGER: { "code": 'Hbc_Budget_Manager', "desc": "HBC预算经理" },
    Hbc_Budget_Manager2: { "code": 'Hbc_Budget_Manager2', "desc": "HBC2预算经理" },
    Budget_Manager2: { "code": 'Budget_Manager', "desc": "日日顺预算经理" },
    TRADE_UNION_HANDOVER_UNDERTAKER: { "code": 'Trade_Union_Handover_Undertaker', "desc": "工会达产激励承接人" },
    TRADE_UNION_HANDOVER_UNDERTAKER_MANAGER: { "code": 'Trade_Union_Handover_Undertaker_Manager', "desc": "工会达产激励承接人直线" },
    Hbc_Budget_User: { "code": 'Hbc_Budget_User', "desc": "预算人自审" },
    CUSTOMIZE: { "code": 'Customize', "desc": "自定义审批人" },
  
    ofType: (type?: string): { "code": string, "desc": string } | null => {
      for (const key in ProcessRoleConstant) {
        const item = ProcessRoleConstant[key as keys];
        if (type === item.code) {
          return item;
        }
      }
      return null;
    },

    toArray:() :({ code: string, desc: string } | undefined)[] => {
      const types = Object.keys(ProcessRoleConstant).map((i: string) => {
        if(i !== 'ofType' && i !== 'toArray' ) {
          return ProcessRoleConstant[i as keys]
        }
        return
      })
      const newTypes = types.filter(function (s) {
        return s && s; 
      })
      return newTypes
    }
  }