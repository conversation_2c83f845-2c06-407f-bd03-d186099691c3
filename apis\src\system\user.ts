import { IPageResponse, IUserInfo, IUserListRequest, IUserSaveUpdateRequest, UserStateConstant } from '@haierbusiness-front/common-libs'
import { get, post, originalPost, DELETE } from '../request'

export const userApi = {

    saveGroup: (params: any): Promise<void> => {
        return originalPost('/system/api/common/user/picker/save/group', params)
    },
    deleteGroup: (params: any): Promise<void> => {
        return originalPost('/system/api/common/user/picker/delete/group', params)
    },
    groupList: (params: any): Promise<void> => {
        return get('/system/api/common/user/picker/group/list', params)
    },
    queryGroupUser: (params: any): Promise<void> => {
        return get('/system/api/common/user/picker/queryGroupUser', params)
    },
    deleteUserLink: (params: any): Promise<void> => {
        return originalPost('/system/api/common/user/picker/delete/userLink', params)
    },

    /**
     * 获取用户列表
     */
    list: (params: IUserListRequest): Promise<IPageResponse<IUserInfo>> => {
        return get<IPageResponse<IUserInfo>>('system/api/user/list', params).then(
            data => {
                data.records?.forEach(
                    it => {
                        it._genderName = it.gender ? (it.gender === 1 ? '男' : '女') : ""
                        it._stateName = UserStateConstant.of(it.state)?.name
                    }
                )
                return data
            }
        )
    },


    /**
     * 新增用户
     */
    save: (params: IUserSaveUpdateRequest): Promise<IUserInfo> => {
        return post('system/api/user/save', params)
    },


}