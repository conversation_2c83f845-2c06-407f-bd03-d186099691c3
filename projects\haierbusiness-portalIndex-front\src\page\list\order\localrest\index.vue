<template>
    <div class="container">
        
        <h-form ref="from" :model="searchKey" @finish="onReFilterChange" style="width: 100%;" :label-col="labelCol" :wrapper-col="wrapperCol">
            <h-row :gutter="24">
                <h-col :span="8">
                    <h-form-item has-feedback label="订单号" name="orderCode">
                        <h-input v-model:value="searchKey.orderCode" placeholder="订单号" allow-clear/>
                    </h-form-item>
                </h-col>
                <h-col :span="16">
                    <h-form-item has-feedback label="申请日期" name="applyTime" :label-col="{ span: 4}" :wrapper-col="{ span: 20}">
                        <h-range-picker v-model:value="searchKey.applyTime" value-format="YYYY-MM-DD" @change="onCreateTimeChange" style="width: 100%" />
                    </h-form-item>
                </h-col>
                <h-col :span="8">
                    <h-form-item has-feedback label="来宾单位" name="guestCompany">
                        <h-input v-model:value="searchKey.guestCompany" placeholder="来宾单位" allow-clear/>
                    </h-form-item>
                </h-col>
                <h-col :span="8">
                    <h-form-item has-feedback label="来宾姓名" name="mainGuestNames">
                        <h-input v-model:value="searchKey.mainGuestNames" placeholder="来宾姓名" allow-clear/>
                    </h-form-item>
                </h-col>
                <h-col :span="8">
                    <h-form-item has-feedback label="业务申请人" name="applicantName">
                        <h-input v-model:value="searchKey.applicantName" placeholder="业务申请人" allow-clear/>
                    </h-form-item>
                </h-col>
                <h-col :span="8">
                    <h-form-item has-feedback label="经办人" name="ownerName">
                        <h-input v-model:value="searchKey.ownerName" placeholder="经办人" allow-clear/>
                    </h-form-item>
                </h-col>
                <h-col :span="8">
                    <h-form-item has-feedback label="预算人" name="budgeterName">
                        <h-input v-model:value="searchKey.budgeterName" placeholder="预算人" allow-clear/>
                    </h-form-item>
                </h-col>
                <h-col :span="8">
                    <h-form-item has-feedback label="有效签单人" name="signerName">
                        <h-input v-model:value="searchKey.signerName" placeholder="有效签单人" allow-clear/>
                    </h-form-item>
                </h-col>
                <h-col :span="8">
                    <h-form-item has-feedback label="酒店储值卡号" name="paymentCard">
                        <h-input v-model:value="searchKey.paymentCard" placeholder="酒店储值卡号" allow-clear/>
                    </h-form-item>
                </h-col>
                <h-col :span="8">
                    <h-form-item has-feedback label="餐厅名称" name="hotelName">
                        <h-input v-model:value="searchKey.hotelName" placeholder="餐厅名称" allow-clear/>
                    </h-form-item>
                </h-col>
                <h-col :span="8">
                    <h-form-item has-feedback label="支付方式" name="payType">
                        <h-select v-model:value="searchKey.payType" placeholder="支付方式" allow-clear>
                            <h-select-option value="">不限</h-select-option>
                            <h-select-option v-for="(item, index) in payTypes" :key="index" :value="item.value">{{ item.label }}</h-select-option>
                        </h-select>
                    </h-form-item>
                </h-col>
                <h-col :span="8">
                    <h-form-item has-feedback label="订单状态" name="orderState">
                        <h-select v-model:value="searchKey.orderState" placeholder="支付方式">
                            <h-select-option value="">全部</h-select-option>
                            <h-select-option v-for="(item, index) in orderState" :key="index" :value="item.value">{{ item.label }}</h-select-option>
                        </h-select>
                    </h-form-item>
                </h-col>
                <h-col :span="16">
                    <h-row>
                        <h-form-item has-feedback name="ownerIsOwn" >
                            <h-checkbox v-model:checked="searchKey.ownerIsOwn" style="min-width: 130px">经办人是自己</h-checkbox>
                        </h-form-item>
                        <h-form-item has-feedback name="applicantIsOwn" >
                            <h-checkbox v-model:checked="searchKey.applicantIsOwn" style="min-width: 160px">业务申请人是自己</h-checkbox>
                        </h-form-item>
                        <h-form-item has-feedback name="applicantManageIsOwn">
                            <h-checkbox v-model:checked="searchKey.applicantManageIsOwn" style="min-width: 240px">业务申请人直线经理是自己</h-checkbox>
                        </h-form-item>
                    </h-row>
                </h-col>
            </h-row>
            <h-row justify="center">
                <div class="flexCon">
                <h-space>
                    <h-button @click="handleReset">重置</h-button>
                    <h-button type="primary" v-permission="'banner:top-img:query'" html-type="submit">查询</h-button>
                </h-space>
                </div>
            </h-row>
        </h-form>

        <h-spin :spinning="loading">
            <div class="list" v-if="data && data.length > 0" >
                <div class="order-container" v-for="(order, index) in data" :key="index">
                    <div class="order-header">
                        <div class="order-header-left">
                            <span>订单号：</span>
                            <span>{{ order.code}} </span>
                            <span style="margin-left: 10px;">支付方式： </span>
                            <span>{{ LocalHotelPaymentTypeEnum[order.payType + ''] }} </span>
                            <span style="margin-left: 50px;">经办人： </span>
                            <span>{{ order.owner?.name}} </span>
                        </div>
                        <div class="order-header-right">
                            <h-tag v-if="order.payType != '2'" :color="approvalStateTagColorMap[order.approvalState + '']">{{ RestaurantOrderApprovalStateEnum[order.approvalState + ''] }}</h-tag>
                            <h-tag v-if="order.payType != '2'" :color="paymentStateTagColorMap[order.paymentState + '']">{{ RestaurantOrderPayMentStateEnum[order.paymentState + ''] }}</h-tag>
                            <h-tag :color="orderStateTagColorMap[order.state + '']">{{ RestaurantOrderStateEnum[order.state + ''] }}</h-tag>
                        </div>
                    </div>
                    <div class="order-body">
                        <div class="first">
                            <h-row>
                                <h-col class="title">餐厅名称:</h-col>
                                <h-col class="value">{{ order.fullname }}</h-col>
                                <h-col class="title">餐类:</h-col>
                                <h-col class="value">{{ order.cateTypeInfo?.name || '不限' }}</h-col>
                                <h-col class="title">就餐时间:</h-col>
                                <h-col class="value">{{ order.eatingTime }}</h-col>
                                <h-col class="title">酒店储值卡号:</h-col>
                                <h-col class="value">{{ order.paymentCard }}</h-col>
                            </h-row>
                        </div>
                        <div class="second">
                            <h-row>
                                <h-col class="title">来宾单位:</h-col>
                                <h-col class="value">{{ order.treatInfo?.guestCompany }}</h-col>
                                <h-col class="title">主宾姓名:</h-col>
                                <h-col class="value">{{ order.treatInfo?.mainGuestNames }}</h-col>
                                <h-col class="title">就餐人数:</h-col>
                                <h-col class="value">{{ order.treatInfo?.guestCount || 0 + order.treatInfo?.accompanyCount || 0 }}人</h-col>
                                <h-col class="title">就餐标准:</h-col>
                                <h-col class="value">¥{{ order.consumptionStandard && (order.consumptionStandard / 100).toFixed(2) }}</h-col>
                                <h-col class="title">餐位费:</h-col>
                                <h-col class="value">¥0</h-col>
                            </h-row>
                        </div>
                        <div class="three">
                            <h-row>
                                <h-col class="title">工作餐金额:</h-col>
                                <h-col class="value">¥{{ order.workingLunchFee }}</h-col>
                                <h-col class="title">服务费率:</h-col>
                                <h-col class="value">{{order.serviceFee}}%</h-col>
                                <h-col class="title">预算金额:</h-col>
                                <h-col class="value">¥{{ order.budgetAmount }}</h-col>
                                <h-col class="title">实际金额:</h-col>
                                <h-col class="value">¥{{ order.actualAmount }}</h-col>
                            </h-row>
                        </div>
                        <div class="last">
                            <h-row>
                                <h-col class="title">申请日期:</h-col>
                                <h-col class="value">{{ order.gmtCreate }}</h-col>
                                <h-col class="title">业务申请人:</h-col>
                                <h-col class="value">{{ order.applicant?.name }}</h-col>
                                <h-col class="title">有效签单人:</h-col>
                                <h-col class="value">{{ order.treatInfo?.signerName }}</h-col>
                                <h-col class="title">锁定人:</h-col>
                                <h-col class="value">{{ order.lockerId }}</h-col>
                                <h-col class="title">锁定日期:</h-col>
                                <h-col class="value">{{ order.lockedTime }}</h-col>
                            </h-row>
                        </div>
                    </div>
                    <div class="order-footer">
                        <h-button type="primary" size="small" @click="handle(order.code)">去处理</h-button>
                    </div>
                </div>
            </div>

            <div class="page" v-show="pagination.total && pagination.total > 0">
                <h-pagination v-model:current="pagination.current" show-size-changer show-quick-jumper :total="pagination.total" @change="onPageChange" />
            </div>

            <div class="empty" v-if="!data || data.length === 0">
                <h-empty />
            </div>
        </h-spin>
        
    </div>
    
</template>
<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Tag as hTag,
 Spin as hSpin,
  Empty as hEmpty,
  Space as hSpace,
  Pagination as hPagination,
  Checkbox as hCheckbox,
  message,
} from 'ant-design-vue';
import { useOrderSearch } from "@haierbusiness-front/composables"
import {computed, onMounted, reactive, ref, watch} from 'vue'
import type { LocalrestType, LocalrestFilter } from '@haierbusiness-front/common-libs'
import { localrestApi } from '@haierbusiness-front/apis'
import { PaymentTypeEnum, RestaurantOrderStateEnum, orderStateTagColorMap, LocalHotelPaymentTypeEnum, 
    RestaurantOrderApprovalStateEnum, approvalStateTagColorMap, RestaurantOrderPayMentStateEnum, paymentStateTagColorMap } from '@haierbusiness-front/common-libs'
import { getEnumOptions } from '@haierbusiness-front/utils'
// import { useOrderStore } from "@/store"
import { useRoute } from 'vue-router'
import { storeToRefs } from "pinia";
import globalPinia from "@haierbusiness-front/utils/src/store/store"
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';

const { loginUser } = storeToRefs(applicationStore(globalPinia))

const route = useRoute()
// const orderStore = useOrderStore()

const labelCol = {
    span:8
}
const wrapperCol = { 
    span: 16
}

const businesstravel = import.meta.env.VITE_BUSINESSTRAVEL_URL
const handle = (code: string) => {
    const url = businesstravel + '/localrest/#/personal/orderlist?cocode=' + code
    window.open(url)
}

// 支付类型
const payTypes = computed(() => {
    console.log(getEnumOptions(PaymentTypeEnum, true))
    return getEnumOptions(PaymentTypeEnum, true)
})

// 订单状态
const orderState = computed(() => {
    return getEnumOptions(RestaurantOrderStateEnum, true)
})

const searchKey = reactive<LocalrestFilter>({
    applicantIsOwn: false, //业务申请人是自己
    applicantManageIsOwn: false, //业务申请人直线经理是自己
    applicantName: "", //业务申请人
    budgeterName: "", //预算人
    applyTime: "",
    startTime: "", //yyyy-MM-dd 开始时间
    endTime: "", //yyyy-MM-dd 结束时间
    guestCompany: "",
    hotelName: "", //酒店名称
    mainGuestNames: "",
    orderCode: "", //订单号
    orderPayState: "", //支付状态
    orderState: "", //订单状态
    ownerIsOwn: true, //经办人是自己
    ownerName: "", //经办人
    payType: "", //支付类型
    paymentCard: "", //酒店储值卡
    signerName: "", //签单人
    approvalState: "", //审批状态
    billState: "", //账单状态
    pageNum: 0,
    pageSize: 0,
})

// const userInfo = computed(() => orderStore.userInfo)

const { data, fetchData, pagination, loading, onTimeChange, from, onFilterChange } = useOrderSearch<LocalrestType, LocalrestFilter>(localrestApi, searchKey)

const onReFilterChange = () => {
    if (!(searchKey.ownerIsOwn || searchKey.applicantIsOwn || searchKey.applicantManageIsOwn)) {
        message.error("经办人，业务申请人，业务申请人的直线经理必须至少有一个是自己");
        return;
    }
    resetUserSearch()
    onFilterChange()
}

const resetUserSearch = () => {
    searchKey.ownerIsOwn = searchKey.ownerIsOwn && loginUser.value?.username || ''
    searchKey.applicantIsOwn = searchKey.applicantIsOwn && loginUser.value?.username || ''
    searchKey.applicantManageIsOwn = searchKey.applicantManageIsOwn && loginUser.value?.username || ''
}

const onPageChange = (page: number, pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.current = page
    resetUserSearch()
    fetchData()
}

const handleReset = () => {
    from.value && from.value.resetFields()
    if(!searchKey.applyTime || searchKey.applyTime.length != 2) {
        searchKey.startTime = ''
        searchKey.endTime = ''
    }
    onReFilterChange()
}

const onCreateTimeChange = (dateRange: string[]) => {
    if(dateRange && dateRange.length === 2) {
        searchKey.startTime = dateRange[0]
        searchKey.endTime = dateRange[1]
    } else {
        searchKey.startTime = ''
        searchKey.endTime = ''
    }
}

// watch(userInfo, (newValue) => {
//     if(newValue.id) {
//         const code = route.query.coCode
//         if(code) {
//             searchKey.orderCode = code.toString()
//         }
//         onReFilterChange()
//     }
// }, { immediate: true, deep: true })

</script>

<style scoped lang="less">

.empty {
    margin-top: 50px;
    border: 1px solid #f0f0f0;
    padding: 42px 24px 50px;
  }
.container {
    display: flex;
    width: 100%;
    flex-direction: column;

    .search {
        display: flex;
        width: 100%;
    }
}

.list {
    display: flex;
    margin-top: 20px;
    width: 100%;
    flex-direction: column;

    .order-container {
        display: flex;
        flex-direction: column;
        width: 100%;
        margin-bottom: 20px;

        .order-header {
            width: 100%;
            background-color: #f5f5f5;
            height: 43px;
            line-height: 43px;
            padding-left: 20px;
            color: #333;
            font-size: 12px;
            border: 1px solid #eaeaea;
            display: flex;
            justify-content: space-between;

            .order-header-left {
                display: flex;
            }
            .order-header-right {
                display: flex;
                align-items: center;
            }
        }
        .order-body {
            display: flex;
            width: 100%;
            border: 1px solid #eaeaea;
            border-top: 0;
            flex-direction: row;

            .first {
                display: flex;
                flex: 3;
                padding: 24px 10px;

                
            }
            .second {
                display: flex;
                flex: 2;
                border-left: 1px solid #eaeaea;
                padding: 24px 10px;
            }
            .three {
                display: flex;
                flex: 2;
                border-left: 1px solid #eaeaea;
                padding: 24px 10px;
            }
            .last {
                display: flex;
                flex: 3;
                border-left: 1px solid #eaeaea;
                padding: 24px 10px;
            }

            .second,.three {
                .title {
                    width: 80px;
                    text-align: right;
                    line-height: 20px;
                }

                .value {
                    padding-left: 5px;
                    width: calc(100% - 80px);
                    line-height: 20px;
                }
            }

            .first,.last {
                .title {
                    width: 100px;
                    text-align: right;
                    line-height: 20px;
                }

                .value {
                    padding-left: 5px;
                    width: calc(100% - 100px);
                    line-height: 20px;
                }
            }

            
            
        }
        .order-footer {
            width: 100%;
            background-color: #f5f5f5;
            height: 43px;
            line-height: 43px;
            padding-right: 8px;
            color: #333;
            font-size: 12px;
            border-left: 1px solid #eaeaea;
            border-right: 1px solid #eaeaea;
            border-bottom: 1px solid #eaeaea;
            display: flex;
            flex-direction: row-reverse;
            align-items: center;
        }
    }

}

.page {
    display: flex;
    width: 100%;
    flex-direction: row-reverse;
    margin-bottom: 20px;
}
</style>