export const demandJsonAdd = {
  // mainCode: '',
  demandRejectReason: '',
  //
  miceName: '智能填报会议（请修改名称）',
  miceType: 5,
  personTotal: 100,
  contactUserName: '王忠明',
  contactUserCode: '24060822',
  contactUserPhone: '15615738641',
  contactUserEmail: '<EMAIL>',
  districtType: 0,
  //
  remarks: '智能填报测试需求',
  hotels: [
    {
      id: 600,
      sourceId: null,
      provinceId: 23,
      provinceName: '山东',
      cityId: 59,
      cityName: '青岛',
      districtIds: '428,1370,1991,2236,2257,2667,2738,2851,2915,2966',
      districtNames: '崂山区,胶州,城阳区,李沧区,市北区,平度,莱西,即墨区,市南区,黄岛区',
      wishId: null,
      wishAreaId: null,
      wishCode: null,
      level: 48,
      latitude: '36.062687',
      longitude: '120.384599',
      distanceRange: null,
      centerMarker: '五四广场',
    },
  ],
  startDate: '2025-08-01',
  endDate: '2025-08-10',
  stays: [
    {
      id: 2246,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-01',
      roomType: 2,
      breakfastType: 1,
      personNum: 90,
      roomNum: 45,
      discrepancyReason: '',
      calcUnitPrice: 507.0,
    },
    {
      id: 2247,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-01',
      roomType: 1,
      breakfastType: 1,
      personNum: 10,
      roomNum: 10,
      discrepancyReason: '',
      calcUnitPrice: 331.0,
    },
    {
      id: 2248,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-02',
      roomType: 2,
      breakfastType: 1,
      personNum: 95,
      roomNum: 48,
      discrepancyReason: '存在需单人住宿的情况',
      calcUnitPrice: 38.0,
    },
    {
      id: 2249,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-02',
      roomType: 3,
      breakfastType: 0,
      personNum: 5,
      roomNum: 5,
      discrepancyReason: '',
      calcUnitPrice: 579.0,
    },
    {
      id: 2250,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-03',
      roomType: 2,
      breakfastType: 1,
      personNum: 90,
      roomNum: 45,
      discrepancyReason: '',
      calcUnitPrice: 507.0,
    },
    {
      id: 2251,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-03',
      roomType: 1,
      breakfastType: 1,
      personNum: 10,
      roomNum: 10,
      discrepancyReason: '',
      calcUnitPrice: 331.0,
    },
    {
      id: 2252,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-04',
      roomType: 2,
      breakfastType: 1,
      personNum: 90,
      roomNum: 45,
      discrepancyReason: '',
      calcUnitPrice: 507.0,
    },
    {
      id: 2253,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-04',
      roomType: 1,
      breakfastType: 1,
      personNum: 10,
      roomNum: 10,
      discrepancyReason: '',
      calcUnitPrice: 331.0,
    },
    {
      id: 2254,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-05',
      roomType: 2,
      breakfastType: 1,
      personNum: 95,
      roomNum: 48,
      discrepancyReason: '存在需单人住宿的情况',
      calcUnitPrice: 38.0,
    },
    {
      id: 2255,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-05',
      roomType: 3,
      breakfastType: 0,
      personNum: 5,
      roomNum: 5,
      discrepancyReason: '',
      calcUnitPrice: 579.0,
    },
    {
      id: 2256,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-06',
      roomType: 2,
      breakfastType: 1,
      personNum: 95,
      roomNum: 48,
      discrepancyReason: '存在需单人住宿的情况',
      calcUnitPrice: 38.0,
    },
    {
      id: 2257,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-06',
      roomType: 3,
      breakfastType: 0,
      personNum: 5,
      roomNum: 5,
      discrepancyReason: '',
      calcUnitPrice: 579.0,
    },
    {
      id: 2258,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-07',
      roomType: 2,
      breakfastType: 1,
      personNum: 90,
      roomNum: 45,
      discrepancyReason: '',
      calcUnitPrice: 507.0,
    },
    {
      id: 2259,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-07',
      roomType: 1,
      breakfastType: 1,
      personNum: 10,
      roomNum: 10,
      discrepancyReason: '',
      calcUnitPrice: 331.0,
    },
    {
      id: 2260,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-08',
      roomType: 2,
      breakfastType: 1,
      personNum: 95,
      roomNum: 48,
      discrepancyReason: '存在需单人住宿的情况',
      calcUnitPrice: 38.0,
    },
    {
      id: 2261,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-08',
      roomType: 3,
      breakfastType: 0,
      personNum: 5,
      roomNum: 5,
      discrepancyReason: '',
      calcUnitPrice: 579.0,
    },
    {
      id: 2262,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-09',
      roomType: 2,
      breakfastType: 1,
      personNum: 90,
      roomNum: 45,
      discrepancyReason: '',
      calcUnitPrice: 507.0,
    },
    {
      id: 2263,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-09',
      roomType: 1,
      breakfastType: 1,
      personNum: 10,
      roomNum: 10,
      discrepancyReason: '',
      calcUnitPrice: 331.0,
    },
    {
      id: 2264,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-10',
      roomType: 2,
      breakfastType: 1,
      personNum: 90,
      roomNum: 45,
      discrepancyReason: '',
      calcUnitPrice: 507.0,
    },
    {
      id: 2265,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-10',
      roomType: 1,
      breakfastType: 1,
      personNum: 10,
      roomNum: 10,
      discrepancyReason: '',
      calcUnitPrice: 331.0,
    },
  ],
  places: [
    {
      id: 1141,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-01',
      usageTime: 6,
      usagePurpose: 2,
      personNum: 100,
      area: null,
      underLightFloor: 4,
      tableType: 5,
      hasLed: true,
      ledNum: 10,
      ledSpecs: '以服务商提报为准',
      hasTea: false,
      teaEachTotalPrice: null,
      teaDesc: null,
      calcUnitPlacePrice: 53836.0,
      calcUnitLedPrice: 472.0,
      calcUnitTeaPrice: null,
    },
    {
      id: 1142,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-02',
      usageTime: 3,
      usagePurpose: 5,
      personNum: 100,
      area: null,
      underLightFloor: null,
      tableType: 5,
      hasLed: true,
      ledNum: 10,
      ledSpecs: 'LED',
      hasTea: true,
      teaEachTotalPrice: 50.0,
      teaDesc: '茶歇',
      calcUnitPlacePrice: 19868.0,
      calcUnitLedPrice: 2790.0,
      calcUnitTeaPrice: 50.0,
    },
    {
      id: 1143,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-03',
      usageTime: 6,
      usagePurpose: 2,
      personNum: 100,
      area: null,
      underLightFloor: 4,
      tableType: 5,
      hasLed: true,
      ledNum: 10,
      ledSpecs: '以服务商提报为准',
      hasTea: false,
      teaEachTotalPrice: null,
      teaDesc: null,
      calcUnitPlacePrice: 53836.0,
      calcUnitLedPrice: 472.0,
      calcUnitTeaPrice: null,
    },
    {
      id: 1144,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-04',
      usageTime: 6,
      usagePurpose: 2,
      personNum: 100,
      area: null,
      underLightFloor: 4,
      tableType: 5,
      hasLed: true,
      ledNum: 10,
      ledSpecs: '以服务商提报为准',
      hasTea: false,
      teaEachTotalPrice: null,
      teaDesc: null,
      calcUnitPlacePrice: 53836.0,
      calcUnitLedPrice: 472.0,
      calcUnitTeaPrice: null,
    },
    {
      id: 1145,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-05',
      usageTime: 3,
      usagePurpose: 5,
      personNum: 100,
      area: null,
      underLightFloor: null,
      tableType: 5,
      hasLed: true,
      ledNum: 10,
      ledSpecs: 'LED',
      hasTea: true,
      teaEachTotalPrice: 50.0,
      teaDesc: '茶歇',
      calcUnitPlacePrice: 19868.0,
      calcUnitLedPrice: 2790.0,
      calcUnitTeaPrice: 50.0,
    },
    {
      id: 1146,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-06',
      usageTime: 3,
      usagePurpose: 5,
      personNum: 100,
      area: null,
      underLightFloor: null,
      tableType: 5,
      hasLed: true,
      ledNum: 10,
      ledSpecs: 'LED',
      hasTea: true,
      teaEachTotalPrice: 50.0,
      teaDesc: '茶歇',
      calcUnitPlacePrice: 19868.0,
      calcUnitLedPrice: 2790.0,
      calcUnitTeaPrice: 50.0,
    },
    {
      id: 1147,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-07',
      usageTime: 6,
      usagePurpose: 2,
      personNum: 100,
      area: null,
      underLightFloor: 4,
      tableType: 5,
      hasLed: true,
      ledNum: 10,
      ledSpecs: '以服务商提报为准',
      hasTea: false,
      teaEachTotalPrice: null,
      teaDesc: null,
      calcUnitPlacePrice: 53836.0,
      calcUnitLedPrice: 472.0,
      calcUnitTeaPrice: null,
    },
    {
      id: 1148,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-08',
      usageTime: 3,
      usagePurpose: 5,
      personNum: 100,
      area: null,
      underLightFloor: null,
      tableType: 5,
      hasLed: true,
      ledNum: 10,
      ledSpecs: 'LED',
      hasTea: true,
      teaEachTotalPrice: 50.0,
      teaDesc: '茶歇',
      calcUnitPlacePrice: 19868.0,
      calcUnitLedPrice: 2790.0,
      calcUnitTeaPrice: 50.0,
    },
    {
      id: 1149,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-09',
      usageTime: 6,
      usagePurpose: 2,
      personNum: 100,
      area: null,
      underLightFloor: 4,
      tableType: 5,
      hasLed: true,
      ledNum: 10,
      ledSpecs: '以服务商提报为准',
      hasTea: false,
      teaEachTotalPrice: null,
      teaDesc: null,
      calcUnitPlacePrice: 53836.0,
      calcUnitLedPrice: 472.0,
      calcUnitTeaPrice: null,
    },
    {
      id: 1150,
      sourceId: null,
      miceDemandHotelId: 600,
      demandDate: '2025-08-10',
      usageTime: 6,
      usagePurpose: 2,
      personNum: 100,
      area: null,
      underLightFloor: 4,
      tableType: 5,
      hasLed: true,
      ledNum: 10,
      ledSpecs: '以服务商提报为准',
      hasTea: false,
      teaEachTotalPrice: null,
      teaDesc: null,
      calcUnitPlacePrice: 53836.0,
      calcUnitLedPrice: 472.0,
      calcUnitTeaPrice: null,
    },
  ],
  caterings: [
    {
      miceDemandHotelId: 600,
      id: 1142,
      sourceId: null,
      isInsideHotel: true,
      demandDate: '2025-08-01',
      cateringType: 1,
      cateringTime: 1,
      personNum: 100,
      demandUnitPrice: 100.0,
      isIncludeDrinks: true,
      calcUnitPrice: 100.0,
    },
    {
      miceDemandHotelId: 600,
      id: 1143,
      sourceId: null,
      isInsideHotel: false,
      demandDate: '2025-08-02',
      cateringType: 3,
      cateringTime: 0,
      personNum: 100,
      demandUnitPrice: 80.0,
      isIncludeDrinks: false,
      calcUnitPrice: 80.0,
    },
    {
      miceDemandHotelId: 600,
      id: 1144,
      sourceId: null,
      isInsideHotel: true,
      demandDate: '2025-08-03',
      cateringType: 1,
      cateringTime: 1,
      personNum: 100,
      demandUnitPrice: 100.0,
      isIncludeDrinks: true,
      calcUnitPrice: 100.0,
    },
    {
      miceDemandHotelId: 600,
      id: 1145,
      sourceId: null,
      isInsideHotel: true,
      demandDate: '2025-08-04',
      cateringType: 1,
      cateringTime: 1,
      personNum: 100,
      demandUnitPrice: 100.0,
      isIncludeDrinks: true,
      calcUnitPrice: 100.0,
    },
    {
      miceDemandHotelId: 600,
      id: 1146,
      sourceId: null,
      isInsideHotel: false,
      demandDate: '2025-08-05',
      cateringType: 3,
      cateringTime: 0,
      personNum: 100,
      demandUnitPrice: 80.0,
      isIncludeDrinks: false,
      calcUnitPrice: 80.0,
    },
    {
      miceDemandHotelId: 600,
      id: 1147,
      sourceId: null,
      isInsideHotel: false,
      demandDate: '2025-08-06',
      cateringType: 3,
      cateringTime: 0,
      personNum: 100,
      demandUnitPrice: 80.0,
      isIncludeDrinks: false,
      calcUnitPrice: 80.0,
    },
    {
      miceDemandHotelId: 600,
      id: 1148,
      sourceId: null,
      isInsideHotel: true,
      demandDate: '2025-08-07',
      cateringType: 1,
      cateringTime: 1,
      personNum: 100,
      demandUnitPrice: 100.0,
      isIncludeDrinks: true,
      calcUnitPrice: 100.0,
    },
    {
      miceDemandHotelId: 600,
      id: 1149,
      sourceId: null,
      isInsideHotel: false,
      demandDate: '2025-08-08',
      cateringType: 3,
      cateringTime: 0,
      personNum: 100,
      demandUnitPrice: 80.0,
      isIncludeDrinks: false,
      calcUnitPrice: 80.0,
    },
    {
      miceDemandHotelId: 600,
      id: 1150,
      sourceId: null,
      isInsideHotel: true,
      demandDate: '2025-08-09',
      cateringType: 1,
      cateringTime: 1,
      personNum: 100,
      demandUnitPrice: 100.0,
      isIncludeDrinks: true,
      calcUnitPrice: 100.0,
    },
    {
      miceDemandHotelId: 600,
      id: 1151,
      sourceId: null,
      isInsideHotel: true,
      demandDate: '2025-08-10',
      cateringType: 1,
      cateringTime: 1,
      personNum: 100,
      demandUnitPrice: 100.0,
      isIncludeDrinks: true,
      calcUnitPrice: 100.0,
    },
  ],
  vehicles: [
    {
      id: 1137,
      sourceId: null,
      demandDate: '2025-08-01',
      usageType: 1,
      usageTime: 1,
      seats: 19,
      vehicleNum: 5,
      brand: '丰田',
      route: '从青岛胶东国际机场到八大关风景区，再从八大关风景区到五四广场',
      calcUnitPrice: 747.0,
    },
    {
      id: 1138,
      sourceId: null,
      demandDate: '2025-08-02',
      usageType: 0,
      usageTime: 1,
      seats: 31,
      vehicleNum: 3,
      brand: '考斯特',
      route: '五四广场,青岛胶东国际机场',
      calcUnitPrice: 192.0,
    },
    {
      id: 1139,
      sourceId: null,
      demandDate: '2025-08-03',
      usageType: 1,
      usageTime: 1,
      seats: 19,
      vehicleNum: 5,
      brand: '丰田',
      route: '从青岛胶东国际机场到八大关风景区，再从八大关风景区到五四广场',
      calcUnitPrice: 747.0,
    },
    {
      id: 1140,
      sourceId: null,
      demandDate: '2025-08-04',
      usageType: 1,
      usageTime: 1,
      seats: 19,
      vehicleNum: 5,
      brand: '丰田',
      route: '从青岛胶东国际机场到八大关风景区，再从八大关风景区到五四广场',
      calcUnitPrice: 747.0,
    },
    {
      id: 1141,
      sourceId: null,
      demandDate: '2025-08-05',
      usageType: 0,
      usageTime: 1,
      seats: 31,
      vehicleNum: 3,
      brand: '考斯特',
      route: '五四广场,青岛胶东国际机场',
      calcUnitPrice: 192.0,
    },
    {
      id: 1142,
      sourceId: null,
      demandDate: '2025-08-06',
      usageType: 0,
      usageTime: 1,
      seats: 31,
      vehicleNum: 3,
      brand: '考斯特',
      route: '五四广场,青岛胶东国际机场',
      calcUnitPrice: 192.0,
    },
    {
      id: 1143,
      sourceId: null,
      demandDate: '2025-08-07',
      usageType: 1,
      usageTime: 1,
      seats: 19,
      vehicleNum: 5,
      brand: '丰田',
      route: '从青岛胶东国际机场到八大关风景区，再从八大关风景区到五四广场',
      calcUnitPrice: 747.0,
    },
    {
      id: 1144,
      sourceId: null,
      demandDate: '2025-08-08',
      usageType: 0,
      usageTime: 1,
      seats: 31,
      vehicleNum: 3,
      brand: '考斯特',
      route: '五四广场,青岛胶东国际机场',
      calcUnitPrice: 192.0,
    },
    {
      id: 1145,
      sourceId: null,
      demandDate: '2025-08-09',
      usageType: 1,
      usageTime: 1,
      seats: 19,
      vehicleNum: 5,
      brand: '丰田',
      route: '从青岛胶东国际机场到八大关风景区，再从八大关风景区到五四广场',
      calcUnitPrice: 747.0,
    },
    {
      id: 1146,
      sourceId: null,
      demandDate: '2025-08-10',
      usageType: 1,
      usageTime: 1,
      seats: 19,
      vehicleNum: 5,
      brand: '丰田',
      route: '从青岛胶东国际机场到八大关风景区，再从八大关风景区到五四广场',
      calcUnitPrice: 747.0,
    },
  ],
  attendants: [
    {
      id: 1125,
      sourceId: null,
      demandDate: '2025-08-01',
      type: 0,
      personNum: 1,
      duty: '摄影',
      calcUnitPrice: 1984.0,
    },
    {
      id: 1126,
      sourceId: null,
      demandDate: '2025-08-02',
      type: 3,
      personNum: 1,
      duty: '会议主持',
      calcUnitPrice: 1137.0,
    },
    {
      id: 1127,
      sourceId: null,
      demandDate: '2025-08-03',
      type: 0,
      personNum: 1,
      duty: '摄影',
      calcUnitPrice: 1984.0,
    },
    {
      id: 1128,
      sourceId: null,
      demandDate: '2025-08-04',
      type: 0,
      personNum: 1,
      duty: '摄影',
      calcUnitPrice: 1984.0,
    },
    {
      id: 1129,
      sourceId: null,
      demandDate: '2025-08-05',
      type: 3,
      personNum: 1,
      duty: '会议主持',
      calcUnitPrice: 1137.0,
    },
    {
      id: 1130,
      sourceId: null,
      demandDate: '2025-08-06',
      type: 3,
      personNum: 1,
      duty: '会议主持',
      calcUnitPrice: 1137.0,
    },
    {
      id: 1131,
      sourceId: null,
      demandDate: '2025-08-07',
      type: 0,
      personNum: 1,
      duty: '摄影',
      calcUnitPrice: 1984.0,
    },
    {
      id: 1132,
      sourceId: null,
      demandDate: '2025-08-08',
      type: 3,
      personNum: 1,
      duty: '会议主持',
      calcUnitPrice: 1137.0,
    },
    {
      id: 1133,
      sourceId: null,
      demandDate: '2025-08-09',
      type: 0,
      personNum: 1,
      duty: '摄影',
      calcUnitPrice: 1984.0,
    },
    {
      id: 1134,
      sourceId: null,
      demandDate: '2025-08-10',
      type: 0,
      personNum: 1,
      duty: '摄影',
      calcUnitPrice: 1984.0,
    },
  ],
  activities: [
    {
      id: 547,
      sourceId: null,
      demandDate: '2025-08-01',
      demandUnitPrice: 60.0,
      personNum: 100,
      description: '拓展活动要求',
      calcUnitPrice: 60.0,
      paths: [
        '{"name":"拓展活动文件说明.pdf","url":"https://businessmanagement-test.haier.net/hbweb/file/download/obs-swszh1/1749448106-拓展活动文件说明.pdf"}',
      ],
    },
    {
      id: 548,
      sourceId: null,
      demandDate: '2025-08-03',
      demandUnitPrice: 60.0,
      personNum: 100,
      description: '拓展活动要求',
      calcUnitPrice: 60.0,
      paths: [
        '{"name":"拓展活动文件说明.pdf","url":"https://businessmanagement-test.haier.net/hbweb/file/download/obs-swszh1/1749448106-拓展活动文件说明.pdf"}',
      ],
    },
    {
      id: 549,
      sourceId: null,
      demandDate: '2025-08-04',
      demandUnitPrice: 60.0,
      personNum: 100,
      description: '拓展活动要求',
      calcUnitPrice: 60.0,
      paths: [
        '{"name":"拓展活动文件说明.pdf","url":"https://businessmanagement-test.haier.net/hbweb/file/download/obs-swszh1/1749448106-拓展活动文件说明.pdf"}',
      ],
    },
    {
      id: 550,
      sourceId: null,
      demandDate: '2025-08-07',
      demandUnitPrice: 60.0,
      personNum: 100,
      description: '拓展活动要求',
      calcUnitPrice: 60.0,
      paths: [
        '{"name":"拓展活动文件说明.pdf","url":"https://businessmanagement-test.haier.net/hbweb/file/download/obs-swszh1/1749448106-拓展活动文件说明.pdf"}',
      ],
    },
    {
      id: 551,
      sourceId: null,
      demandDate: '2025-08-09',
      demandUnitPrice: 60.0,
      personNum: 100,
      description: '拓展活动要求',
      calcUnitPrice: 60.0,
      paths: [
        '{"name":"拓展活动文件说明.pdf","url":"https://businessmanagement-test.haier.net/hbweb/file/download/obs-swszh1/1749448106-拓展活动文件说明.pdf"}',
      ],
    },
    {
      id: 552,
      sourceId: null,
      demandDate: '2025-08-10',
      demandUnitPrice: 60.0,
      personNum: 100,
      description: '拓展活动要求',
      calcUnitPrice: 60.0,
      paths: [
        '{"name":"拓展活动文件说明.pdf","url":"https://businessmanagement-test.haier.net/hbweb/file/download/obs-swszh1/1749448106-拓展活动文件说明.pdf"}',
      ],
    },
  ],
  insurances: [
    {
      id: 1123,
      sourceId: null,
      demandDate: '2025-08-01',
      demandUnitPrice: 22.0,
      personNum: 100,
      productId: 2,
      productMerchantId: 222,
      insuranceName: '大地保险22',
      insuranceContent: '大地保险保险内容1',
      calcUnitPrice: 22.0,
    },
    {
      id: 1124,
      sourceId: null,
      demandDate: '2025-08-02',
      demandUnitPrice: 11.0,
      personNum: 100,
      productId: 1,
      productMerchantId: 111,
      insuranceName: '平安保险11',
      insuranceContent: '平安保险内容1',
      calcUnitPrice: 11.0,
    },
    {
      id: 1125,
      sourceId: null,
      demandDate: '2025-08-03',
      demandUnitPrice: 22.0,
      personNum: 100,
      productId: 2,
      productMerchantId: 222,
      insuranceName: '大地保险22',
      insuranceContent: '大地保险保险内容1',
      calcUnitPrice: 22.0,
    },
    {
      id: 1126,
      sourceId: null,
      demandDate: '2025-08-04',
      demandUnitPrice: 22.0,
      personNum: 100,
      productId: 2,
      productMerchantId: 222,
      insuranceName: '大地保险22',
      insuranceContent: '大地保险保险内容1',
      calcUnitPrice: 22.0,
    },
    {
      id: 1127,
      sourceId: null,
      demandDate: '2025-08-05',
      demandUnitPrice: 11.0,
      personNum: 100,
      productId: 1,
      productMerchantId: 111,
      insuranceName: '平安保险11',
      insuranceContent: '平安保险内容1',
      calcUnitPrice: 11.0,
    },
    {
      id: 1128,
      sourceId: null,
      demandDate: '2025-08-06',
      demandUnitPrice: 11.0,
      personNum: 100,
      productId: 1,
      productMerchantId: 111,
      insuranceName: '平安保险11',
      insuranceContent: '平安保险内容1',
      calcUnitPrice: 11.0,
    },
    {
      id: 1129,
      sourceId: null,
      demandDate: '2025-08-07',
      demandUnitPrice: 22.0,
      personNum: 100,
      productId: 2,
      productMerchantId: 222,
      insuranceName: '大地保险22',
      insuranceContent: '大地保险保险内容1',
      calcUnitPrice: 22.0,
    },
    {
      id: 1130,
      sourceId: null,
      demandDate: '2025-08-08',
      demandUnitPrice: 11.0,
      personNum: 100,
      productId: 1,
      productMerchantId: 111,
      insuranceName: '平安保险11',
      insuranceContent: '平安保险内容1',
      calcUnitPrice: 11.0,
    },
    {
      id: 1131,
      sourceId: null,
      demandDate: '2025-08-09',
      demandUnitPrice: 22.0,
      personNum: 100,
      productId: 2,
      productMerchantId: 222,
      insuranceName: '大地保险22',
      insuranceContent: '大地保险保险内容1',
      calcUnitPrice: 22.0,
    },
    {
      id: 1132,
      sourceId: null,
      demandDate: '2025-08-10',
      demandUnitPrice: 22.0,
      personNum: 100,
      productId: 2,
      productMerchantId: 222,
      insuranceName: '大地保险22',
      insuranceContent: '大地保险保险内容1',
      calcUnitPrice: 22.0,
    },
  ],
  calcTotalPrice: 227680,
  demandTotalPrice: 2925657,
  material: {
    id: 529,
    sourceId: null,
    demandTotalPrice: 780.0,
    calcTotalPrice: 780.0,
    materialDetails: [
      {
        id: 2092,
        miceDemandMaterialId: 529,
        type: 0,
        specs: '条幅',
        num: 1,
        unitPrice: 30.0,
      },
      {
        id: 2093,
        miceDemandMaterialId: 529,
        type: 1,
        specs: '投影仪',
        num: 2,
        unitPrice: 100.0,
      },
      {
        id: 2094,
        miceDemandMaterialId: 529,
        type: 3,
        specs: '麦克',
        num: 1,
        unitPrice: 150.0,
      },
      {
        id: 2095,
        miceDemandMaterialId: 529,
        type: 2,
        specs: '音响',
        num: 4,
        unitPrice: 100.0,
      },
    ],
  },
  traffic: null,
  presents: [
    {
      id: 937,
      sourceId: null,
      deliveryDate: '2025-07-31',
      demandTotalPrice: 10000.0,
      personNum: 100,
      unit: '盒',
      personSpecs: '以服务商提报为准',
      productId: null,
      productMerchantId: null,
      productName: '坚果礼盒',
      optionType: null,
      unitPrice: null,
      calcTotalPrice: 10000.0,
    },
    {
      id: 938,
      sourceId: null,
      deliveryDate: '2025-07-31',
      demandTotalPrice: 53000.0,
      personNum: 100,
      unit: '件',
      personSpecs: '出厂年份：2020年，品类：特曲80版',
      productId: 1,
      productMerchantId: 4787,
      productName: '泸州老窖',
      optionType: 1,
      unitPrice: 530.0,
      calcTotalPrice: 53000.0,
    },
  ],
  others: [
    {
      id: 533,
      sourceId: null,
      demandDate: null,
      itemName: '门票',
      num: 100,
      unit: '张',
      specs: '以服务商提报为准',
      demandTotalPrice: 20000.0,
      calcTotalPrice: 20000.0,
    },
  ],
};
