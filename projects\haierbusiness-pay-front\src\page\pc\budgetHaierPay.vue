<script setup lang="ts">
import { onMounted, PropType, ref } from 'vue'
import { Divider as hDivider ,RadioGroup as hRadioGroup, Radio as hRadio, Modal as hModal, Input as hInput, Spin as hSpin, Space as hSpace, Button as hButton, Row as hRow, Col as hCol, Tabs as hTabs, TabPane as hTabPane, Image as hImage, message } from 'ant-design-vue';
import { IPayData } from '@haierbusiness-front/common-libs/src/pay/model/basicModel';
import { budgetHaierPayApi, coinHaierPayApi } from '@haierbusiness-front/apis';
import { IBudgetHaierTypesResponse,HaierBudgetTypeConstant } from '@haierbusiness-front/common-libs';
import DeptGems1Budget from './budget/haier/dept/deptGems1Budget.vue';
import DeptKems1Budget from './budget/haier/dept/deptKems1Budget.vue';
import DeptRrs1Budget from './budget/haier/dept/deptRrs1Budget.vue';
import DeptRrs2Budget from './budget/haier/dept/deptRrs2Budget.vue';

import DeptBcc1Budget from './budget/haier/dept/deptBcc1Budget.vue';
import DeptHbc1Budget from './budget/haier/dept/deptHbc1Budget.vue';
import DeptBcc2Budget from './budget/haier/dept/deptBcc2Budget.vue';
import DeptHsh1Budget from './budget/haier/dept/deptHsh1Budget.vue';
import DeptKems2Budget from './budget/haier/dept/deptKems2Budget.vue';
import DeptHbc2Budget from './budget/haier/dept/deptHbc2Budget.vue';
import DeptXwBudget from './budget/haier/dept/deptXwBudget.vue';

const props = defineProps({
    param: Object as PropType<IPayData>,
    username:String
});

const emit = defineEmits<{
    (e: 'payComplete', isPayComplete: boolean): void
}>()


// 查询支持的预算类型
const supportBudgetTyps = ref<IBudgetHaierTypesResponse[]>();
const currentBudgetType = ref();

(() => {
    budgetHaierPayApi.searchBudgetTypes(
        {
            applicationCode: props.param?.applicationCode,
            businessType: props.param?.businessType
        }
    ).then(it => {
        supportBudgetTyps.value = it
        currentBudgetType.value = supportBudgetTyps.value ? supportBudgetTyps.value[0].budgetType : ""
    });
})();

const payComplete = () => {
    emit('payComplete', true)
}


</script>
<template>
    <h-row style="margin-top: 2vh;text-align: right;">
        <h-col span="2" class="label">
            预算类型：
        </h-col>
        <!-- v-if="item.budgetTypeName!='HBC'||props.username=='01506579'" -->
        <h-col span="22" style="text-align: left;">
            <h-radio-group v-model:value="currentBudgetType" v-for="item of supportBudgetTyps" name="budgetTypesGroup">
                <h-radio :value="item.budgetType" :title="item.budgetTypeDesc">{{ item.budgetTypeName }}</h-radio>
            </h-radio-group>
        </h-col>
        <h-col span="24">
            <dept-gems-1-budget :param="props.param" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_GEMS_1.code" @payComplete="payComplete" ></dept-gems-1-budget>
            <dept-kems-1-budget :param="props.param" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_KEMS_1.code" @payComplete="payComplete" ></dept-kems-1-budget>
            <dept-kems-2-budget :param="props.param" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_KEMS_2.code" @payComplete="payComplete" ></dept-kems-2-budget>
            <dept-hsh-1-budget :param="props.param" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_HSH_1.code" @payComplete="payComplete" ></dept-hsh-1-budget>
            <dept-bcc-1-budget :param="props.param" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_BCC_1.code"  @payComplete="payComplete" ></dept-bcc-1-budget>
            <dept-bcc-2-budget :param="props.param" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_BCC_2.code" @payComplete="payComplete" ></dept-bcc-2-budget>
            <dept-rrs-1-budget :param="props.param" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_RRS_1.code" @payComplete="payComplete" ></dept-rrs-1-budget>
            <dept-hbc-1-budget  :param="props.param" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_HBC_1.code" @payComplete="payComplete" ></dept-hbc-1-budget>
            <dept-rrs-2-budget :param="props.param" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_RRS_2.code" @payComplete="payComplete" ></dept-rrs-2-budget>
            <dept-hbc-2-budget :param="props.param" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_HBC_2.code" @payComplete="payComplete" ></dept-hbc-2-budget>
            <!-- <dept-xw-budget :param="props.param" :budgetType="currentBudgetType" v-if="currentBudgetType === HaierBudgetTypeConstant.DEPT_XW.code" @payComplete="payComplete" ></dept-xw-budget> -->
        </h-col>
    </h-row>
</template>

<style scoped lang="less">
.label{
    @media screen and (max-width: 1366px) {
    /* 在此处写入需要应用的CSS样式 */
    font-size: 12px;
    }
}
</style>
