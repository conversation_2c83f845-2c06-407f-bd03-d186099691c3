<script setup lang="ts">
import {
  Modal,
  Calendar as hCalendar,
  Tag as hTag,
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Table as hTable,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { AnnualPlanTypeStateConstant, IAnnualPlanTypeListResponse } from '@haierbusiness-front/common-libs';
import { getCurrentRouter } from '@haierbusiness-front/utils';
import { ref, createVNode, watch, computed } from 'vue';
import { dailyReportApi, dailyMonthPlanApi, dailyPersonApi } from '@haierbusiness-front/apis/src/daily';
import {
  IAnnualPlanTypeListRequest,
  ICalendarAPIFestivalInfoResponse,
  ICalendarAPIResponse,
  IDailyPersonalListRequestDTO,
  IDailyPersonalResponse,
  IDailyReportListRequestDTO,
  IDailyReportListResponseDTO,
  IMonthHolidayRequestDTO,
  IMonthPlanDetailRequestDTO,
  IMonthPlanDetailResponseDTO,
} from '@haierbusiness-front/common-libs/src/daily';
import dayjs, { Dayjs } from 'dayjs';
const router = getCurrentRouter();
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
const { loginUser } = storeToRefs(applicationStore(globalPinia));
import dailyChart from './dailyChart.vue';
import { getDailyPersonUser } from '../../utils/dailyPersonUtil';
import 'animate.css';
const prop = defineProps({
  query: Object,
});

const dailyPersonUser = ref<IDailyPersonalResponse>();
const refreshDailyPersonUser = () => {
  return getDailyPersonUser(loginUser?.value?.username!!).then(
    (it) => (
      (dailyPersonUser.value = it),
      (searchListParam.value.deptCode = dailyPersonUser?.value?.deptCode),
      (searchListParam.value.deptName = dailyPersonUser?.value?.deptName)
    ),
  );
};

refreshDailyPersonUser();

watch(
  () => prop.query,
  (val: any) => {
    searchholidayListParam.value = {
      year: currentDay.value?.year(),
      month: currentDay.value?.month() + 1,
    };
    holidayListData.value = [];
    holidayListRun();

    searchListParam.value = {
      year: currentDay.value?.year(),
      month: currentDay.value?.month() + 1,
      deptCode: dailyPersonUser?.value?.deptCode,
      deptName: dailyPersonUser?.value?.deptName,
    };
    listData.value = [];
    listApiRun();
    refreshDailyPersonUser;
  },

  {
    deep: true,
  },
);

const columns: ColumnType[] = [
  {
    title: '年度',
    dataIndex: 'year',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '类型',
    dataIndex: 'name',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '220px',
    fixed: 'right',
    align: 'center',
  },
];

const currentDay = ref<Dayjs>(dayjs());

const searchholidayListParam = ref<IMonthHolidayRequestDTO>({
  year: currentDay.value?.year(),
  month: currentDay.value?.month() + 1,
});
const holidayListData = ref<ICalendarAPIResponse[]>([]);
const searchholidayLoading = ref(false);
const holidayListRun = () => {
  searchholidayLoading.value = true;
  dailyMonthPlanApi
    .holiday(searchholidayListParam.value)
    .then((it) => {
      holidayListData.value = it;
    })
    .finally(() => {
      searchholidayLoading.value = false;
    });
};
{
  holidayListRun();
}

const searchListParam = ref<IDailyReportListRequestDTO>({
  year: currentDay.value?.year(),
  month: currentDay.value?.month() + 1,
  deptCode: dailyPersonUser?.value?.deptCode,
  deptName: dailyPersonUser?.value?.deptName,
});
const listLoading = ref(false);
const listData = ref<IDailyReportListResponseDTO[]>([]);

const listApiRun = async () => {
  await refreshDailyPersonUser()
  listLoading.value = true;
  dailyReportApi
    .list(searchListParam.value)
    .then((it) => {
      listData.value = it;
    })
    .finally(() => {
      listLoading.value = false;
    });
};
listApiRun();
const getCurrentData = computed(() => {
  return (current: Dayjs) => {
    const year = current.format('YYYY');
    const month = current.format('MM');
    const day = current.format('DD');

    const nows = listData.value.filter((it) => {
      return (
        parseInt(day) === parseInt(it.day as any) &&
        parseInt(month) === parseInt(it.month as any) &&
        parseInt(year) === parseInt(it.year as any)
      );
    });
    if (nows && nows.length > 0) {
      return nows[0];
    } else {
      return undefined;
    }
  };
});
const dateChange = (date: any) => {
  const year = date.format('YYYY');
  const month = date.format('MM');
  let searchFlag = false;
  if (searchholidayListParam.value.year !== year) {
    searchFlag = true;
    searchholidayListParam.value.year = date.format('YYYY');
    searchListParam.value.year = date.format('YYYY');
  }
  if (searchholidayListParam.value.month !== month) {
    searchFlag = true;
    searchholidayListParam.value.month = date.format('MM');
    searchListParam.value.month = date.format('MM');
  }
  if (searchFlag) {
    listApiRun();
    holidayListRun();
  }
};

const isNow = (current: Dayjs) => {
  const now = dayjs();
  const year = current.format('YYYY');
  const month = current.format('MM');
  const day = current.format('DD');
  return year === now.format('YYYY') && month === now.format('MM') && day === now.format('DD');
};

const getCurrentCalendar = (current: Dayjs) => {
  const year = current.format('YYYY');
  const month = current.format('MM');
  const day = current.format('DD');
  const dDate = holidayListData.value.filter((it) => {
    return (
      parseInt(day) === parseInt(it.day as any) &&
      parseInt(month) === parseInt(it.month as any) &&
      parseInt(year) === parseInt(it.year as any)
    );
  })[0];
  return dDate;
};
const getWorkDesc = (current: Dayjs) => {
  const calendar = getCurrentCalendar(current);
  if (calendar && calendar.status) {
    if (String(calendar.status) === '1') {
      return '休';
    } else if (String(calendar.status) === '2') {
      return '班';
    } else {
      return undefined;
    }
  }
};
const checkShowChart = (current: Dayjs) => {
  const dDate = getCurrentCalendar(current);
  return dDate && dDate.workState === 2;
};
</script>

<template>
  <div
    style="
      background-color: #ffff;
      height: 100%;
      width: 100%;
      padding: 0px 10px 0px 10px;
      overflow: auto;
      overflow-x: hidden;
    "
  >
    <div style="height: 0px; display: flex; position: relative; top: 90px; left: 40px">
      <div style="display: flex; margin-right: 10px">
        <div style="height: 20px; width: 45px; border-radius: 4px; background-color: rgb(230, 232, 234)"></div>
        <div style="margin-left: 10px; font-size: 12px; line-height: 20px">无需录入</div>
      </div>
      <div style="display: flex; margin-right: 10px">
        <div style="height: 20px; width: 45px; border-radius: 4px; background-color: rgb(145, 205, 118)"></div>
        <div style="margin-left: 10px; font-size: 12px; line-height: 20px">已录入</div>
      </div>
      <div style="display: flex; margin-right: 10px">
        <div style="height: 20px; width: 45px; border-radius: 4px; background-color: rgb(238, 102, 102)"></div>
        <div style="margin-left: 10px; font-size: 12px; line-height: 20px">未录入</div>
      </div>
    </div>
    <div style="display: flex; position: relative; top: 85px; left: 46%">
      <div style="font-weight: 700">{{ searchListParam.deptName }}日清日历</div>
    </div>
    <h-row :align="'middle'" style="height: 30px; margin-bottom: 20px">
      <h-col :span="24">
        <div class="titel-font-layout">
          <div class="animate__animated animate__backInLeft titel-font-style">日事日毕</div>
          &nbsp;
          <div class="animate__animated animate__backInRight titel-font-style">日清日高</div>
        </div>
      </h-col>
    </h-row>
    <h-row :align="'middle'">
      <h-calendar v-model:value="currentDay" @change="dateChange">
        <template #dateCellRender="{ current }">
          <div
            v-if="getWorkDesc(current) === '休'"
            style="
              background-color: rgb(0, 115, 229);
              border-radius: 100%;
              height: 22px;
              width: 22px;
              line-height: 20px;
              position: absolute;
              text-align: center;
              color: #ffff;
            "
          >
            休
          </div>
          <div
            v-if="getWorkDesc(current) === '班'"
            style="
              background-color: rgb(238, 102, 102);
              border-radius: 100%;
              height: 22px;
              width: 22px;
              line-height: 20px;
              position: absolute;
              text-align: center;
              color: #ffff;
            "
          >
            班
          </div>
          <div
            v-if="isNow(current)"
            style="
              background-color: rgb(250, 200, 88);
              border-radius: 100%;
              height: 12px;
              width: 12px;
              line-height: 20px;
              position: absolute;
              text-align: center;
              color: #ffff;
              top: 42px;
              left: 24px;
            "
          ></div>
          <div style="position: absolute; right: 30px; top: 7px; font-size: 12px">
            {{ getCurrentCalendar(current)?.term }}
          </div>
          <ul class="events" style="overflow: hidden" v-if="checkShowChart(current)">
            <daily-chart v-if="getCurrentData(current)" :data="getCurrentData(current)" />
          </ul>
        </template>
      </h-calendar>
    </h-row>
  </div>
</template>

<style lang="less">
.titel-font-layout {
  text-align: center;
  padding-bottom: 12px;
  font-size: 32px;
  border-bottom: 1px solid rgb(210, 210, 210);
}
.titel-font-style {
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
  display: inline-block;
  font-family: 'Slideqiuhong-Regular';
}
.ant-picker-calendar-mode-switch {
  display: none !important;
}
.ant-picker-calendar-date-content {
  overflow: hidden !important;
}
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
