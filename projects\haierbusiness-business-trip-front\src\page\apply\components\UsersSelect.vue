<script lang="ts" setup>
import { Select as hSelect, SelectOption as hSelectOption, Row as hRow, Col as hCol, Form as hForm, FormItem as hFormItem,
     Input as hInput, Button as hButton, Space as hSpace, Card as hCard, Divider as hDivider, Modal, message } from 'ant-design-vue';
import { computed, ref, watch, onMounted } from "vue";
import type { Ref } from "vue";
import { userApi } from '@haierbusiness-front/apis';
import { debounce, values } from 'lodash';
import { usePagination } from 'vue-request';
import { IUserInfo,ITraveler, IUserListRequest } from '@haierbusiness-front/common-libs'
import {
    guid
} from '@haierbusiness-front/utils';
import { storeToRefs } from 'pinia';
import { userStore } from '@haierbusiness-front/utils/src/store/user';
import globalPinia from "@haierbusiness-front/utils/src/store/store"

const { selectUsers } = storeToRefs(userStore(globalPinia))
const store = userStore()

interface Props {
    value: Array<IUserInfo>
    params: IUserListRequest
    placeholder?: string
    cacheKey?: string
}

const props = defineProps<Props>();

const placeholder = ref(props.placeholder)

const defaultParams: IUserListRequest = {}

const params: Ref<IUserListRequest> = ref(
    (props.params as IUserListRequest) || defaultParams
)

// 查询用户
const getUserList = async (isScroll: boolean = false) => {
    loading.value =  true
    // 判断是否需要缓存   如果需要取缓存的用户放到最前边
    let cacheUsers: Array<IUserInfo> = []
    if(props.cacheKey && !params.value.keyWord) {
        cacheUsers = store.getSelectUsersByCacheKey(props.cacheKey)
    }
    const data = await userApi.list(params.value)
    if(isScroll) {
        // 下拉刷新
        userSelect.value = data.records ? [...userSelect.value, ...data.records] : userSelect.value
    } else{
        const users = data.records ?? []
        userSelect.value = [...cacheUsers, ...users]
    }
    
    total.value = data.total ?? 0
    loading.value =  false
}

// 选中的用户
const userSelect = ref<Array<IUserInfo>>([])
const id = guid()

onMounted(async () => {
    const element = document.getElementById(id);
    const x = element!.getBoundingClientRect().left;
    const screenWidth = window.screen.width
    if(x + 600 > screenWidth) {
        placement.value = 'bottomRight'
    } else {
        placement.value = 'bottomLeft'
    }
})

const placement = ref<"bottomLeft" | "bottomRight" | "topLeft" | "topRight">('bottomLeft');

const emit = defineEmits(["change"]);
let tempList = ref<Array<ITraveler>>([])
const onChange = (value: Array<string>) => {
    
    tempList.value = []
    if (!value) {
        emit("change", value)
        return
    }
    userSelect.value.forEach(item => {
        value.forEach(e => {
            if (e == item.username) {
                tempList.value.push(item)
            }
        });
    })
    emit("change", value, tempList)
    params.value.keyWord = ''
}

const handleSearch = debounce(async (val: string) => {
    params.value.keyWord = val
    params.value.pageNum = 1
    params.value.pageSize = 20
    await getUserList()
}, 500)

const loading = ref<boolean>(false)
const total = ref<number>(0)

const popupScroll = (e) => {
    const { clientHeight, scrollHeight, scrollTop } = e.target;
    //判断一下滚动条
    if (scrollHeight - scrollTop <= clientHeight + 100 && !loading.value) {
        //计算一下当前所查分页是否还有数据，如果小于总数则查，大于则不查，防抖节流，减少服务器损耗
        if(params.value.pageNum! * 10 < total.value){
            params.value.pageNum! += 1
            getUserList(true)
        }
    }
}

const select = ref()

</script>

<template>
    <h-select :id="id" :max-tag-count="4" :value="props.value ?? []"  @change="onChange" :placement="placement" mode="multiple"
        :filter-option="false" show-search allowClear :default-active-first-option="false" option-label-prop="children"
        @search="handleSearch" @focus="getUserList()" :dropdown-style="{ minWidth: '600px', maxWidth: '600px'}" :placeholder="placeholder" @popupScroll="popupScroll">
        <h-select-option value="0" disabled class="userHeaderForSelect">
            <div ref="select" class="userHeader">
                <div class="no">
                    账号
                </div>
                <div class="name">
                    姓名
                </div>
                <div class="email">
                    部门
                </div>
            </div>
        </h-select-option>
        <h-select-option v-for="(item, index) in userSelect" :value="item.username" :key="index" :title="item.nickName" :label="item.nickName">
            <div class="userHeader" >
                <div class="no">
                    {{ item.username }}
                </div>
                <div class="name">
                    {{ item.nickName }}
                </div>
                <div class="email">
                    {{ item.departmentName }}
                </div>
            </div>
        </h-select-option>
    </h-select>
</template>

<style lang="less" scoped>

.userHeader {
    display: flex;
    width: 100%;
    flex-direction: row;
    color: #262626;

    .no {
        display: flex;
        flex: 1;
    }

    .phone {
        display: flex;
        flex: 1;
    }

    .name {
        display: flex;
        flex: 1;
    }

    .email {
        display: flex;
        flex: 1;
    }
}

</style>