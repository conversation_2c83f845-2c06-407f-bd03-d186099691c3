<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import {
  Steps as hSteps,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem,
  But<PERSON> as hButton,
  DatePicker as hDatePicker,
  InputNumber as hInputNumber,
  Input as hInput,
  message,
} from 'ant-design-vue';
import { EllipsisOutlined } from '@ant-design/icons-vue';
import AddressModal from './modal.vue';
import { useCartStore } from '../../../store/goods-cart';
import { storeToRefs } from 'pinia';

const cartStore = useCartStore();
const { orderList } = storeToRefs(cartStore);
const goodsList = ref(orderList.value);
const defaultAddress = ref<Record<string, any>>(); // 默认地址
const checkedAddressId = ref(1); // 选中地址

// 计算订单金额
const orderAmountComputed = computed(() => {
  return goodsList.value.reduce((total, item) => total + item.price * item.waterCount, 0);
});

/**
 * 地址相关
 * */
const checkedAddress = (id: number) => {
  checkedAddressId.value = id;
};
// 展示的商品列表 长度固定为4
// 默认地址会被添加到第一个，其他地址会添加到后面，
// 弹窗选择后，会更新地址列表 选中的是第一个，其余后移一位
const addressList = ref();

// 设置默认地址
const visibleModal = ref(false);

// 处理默认值设置
const handleSetDefaultAddress = (item: any) => {
  defaultAddress.value = item;
  message.success('设置成功');
};

// 处理弹窗选择地址
const handleModalConfirm = (params: any) => {
  // 如果地址已存在，则直接选中
  if (addressList.value.find((item: any) => item.id === params.id)) {
    checkedAddressId.value = params.id;
    return;
  }
  // 如果地址列表已满，则替换最后一个地址
  if (addressList.value.length >= 4) {
    addressList.value.unshift(params);
    addressList.value.pop();
  } else {
    addressList.value.push(params);
  }

  visibleModal.value = false;
  checkedAddressId.value = params.id;
};

/**
 * @提交订单
 * */
// 订单信息
const orderInfoData = ref({
  date: '', //送水日期
  surplusAmount: '', // 剩余额度
  orderRemark: '', // 订单备注
});

const handleSubmitOrder = () => {
  console.log(orderInfoData.value);
  if (!orderInfoData.value.date) {
    message.error('请选择送水日期');
    return;
  }
  // 订单地址
  if (!checkedAddressId.value) {
    message.error('请选择订单地址');
    return;
  }
  console.log(orderInfoData.value, '订单信息');
  console.log(checkedAddressId.value, '选中的地址ID');
  console.log(goodsList.value, '商品列表');
};

/**
 * 初始化逻辑
 * */
const tempData = ref([
  {
    id: 1,
    name: '张三',
    phone: '13800138000',
    address: '山东省青岛市崂山区海尔路xx号中心大楼10楼崂山区海尔路xx号中心大楼10楼',
    isDefault: false,
  },
  {
    id: 2,
    name: '李四',
    phone: '13800138000',
    address: '山东省青岛市崂山区海尔路xx号中心大楼10楼崂山区海尔路xx号中心大楼10楼',
    isDefault: false,
  },
  {
    id: 3,
    name: '王五',
    phone: '13800138000',
    address: '山东省青岛市崂山区海尔路xx号中心大楼10楼崂山区海尔路xx号中心大楼10楼',
    isDefault: true,
  },
  {
    id: 4,
    name: '赵六',
    phone: '13800138000',
    address: '山东省青岛市崂山区海尔路xx号中心大楼10楼崂山区海尔路xx号中心大楼10楼',
    isDefault: false,
  },
]);
onMounted(() => {
  // 获取默认地址
  const _defaultAddres = tempData.value.find((item: any) => item.isDefault);
  addressList.value = [_defaultAddres, ...tempData.value.filter((item: any) => item.id !== _defaultAddres?.id)];
  defaultAddress.value = _defaultAddres;
  checkedAddressId.value = _defaultAddres?.id || -1;
});
</script>
<template>
  <div class="order-check">
    <div class="header-steps">
      <div class="header-title">填写并核对订单</div>
      <h-steps
        class="steps"
        :current="1"
        label-placement="vertical"
        :items="[
          {
            status: 'finish',
            title: '我的购物车',
          },
          {
            status: 'process',
            title: '填写核对订单信息',
          },
          {
            status: 'wait',
            title: '付款',
          },
          {
            status: 'wait',
            title: '完成',
          },
        ]"
      ></h-steps>
    </div>

    <div class="title">收货人信息</div>
    <div class="address-list">
      <div
        v-for="item in addressList"
        :class="[checkedAddressId === item.id ? 'address-item-checked' : 'address-item']"
        :key="item.id"
        @click="checkedAddress(item.id)"
      >
        <div
          class="checked-tag"
          v-if="checkedAddressId === item.id && defaultAddress?.id !== item.id"
          @click="handleSetDefaultAddress(item)"
        >
          设置默认
        </div>
        <div class="checked-tag" v-if="defaultAddress?.id === item.id">默认地址</div>
        <div class="checked-right" v-if="checkedAddressId === item.id">√</div>
        <div class="address-item-title">
          <span>{{ item.name }}</span>
          <span style="margin-left: 10px">{{ item.phone }}</span>
        </div>
        <p class="address-item-content">
          {{ item.address }}
        </p>
      </div>
    </div>
    <div>
      <h-button size="large" @click="visibleModal = true"> <EllipsisOutlined />更多选择</h-button>
    </div>

    <div class="title">基础信息</div>
    <div class="order-info">
      <h-descriptions bordered :labelStyle="{ width: '150px' }">
        <h-descriptions-item label="送水日期">
          <h-date-picker
            v-model:value="orderInfoData.date"
            :valueFormat="'YYYY-MM-DD'"
            format="YYYY-MM-DD"
            placeholder="请输入送水日期"
            style="width: 200px"
          />
        </h-descriptions-item>
        <h-descriptions-item label="剩余额度">
          <div style="width: 200px">{{ orderInfoData.surplusAmount || 0 }}元</div>
        </h-descriptions-item>
      </h-descriptions>
    </div>

    <div class="title">商品清单</div>
    <div class="order-info">
      <div class="tr tr-border">
        <div class="th flex-3" style="justify-content: center">商品信息</div>
        <div class="th flex-1">单价（元）</div>
        <div class="th flex-1">申请数量</div>
        <div class="th flex-1">送水数量</div>
        <div class="th flex-1" style="justify-content: center">小计（元）</div>
      </div>

      <div class="tr" v-for="(item, index) in goodsList" :key="index">
        <div class="td flex-3">
          <img src="../../../assets/image/demo/yszy.png" alt="" />
          <span style="margin-left: 16px">{{ item.name }}</span>
        </div>
        <div class="td flex-1 text-blue">￥ {{ item.price }}</div>
        <div class="td flex-1 text-blue">{{ item.count }}</div>
        <div class="td flex-1 number-controls">
          <h-button
            class="decrease-btn"
            :disabled="item.waterCount <= 1"
            @click="item.waterCount > 1 && item.waterCount--"
          >
            -
          </h-button>
          <h-input-number
            :controls="false"
            :step="1"
            :min="1"
            :precision="0"
            :default-value="1"
            :max="item.count"
            v-model:value="item.waterCount"
            style="width: 80px; text-align: center"
            @blur="
              () => {
                if (item.waterCount === null || isNaN(item.waterCount)) {
                  item.waterCount = 1; // 重置为最小值
                }
              }
            "
          />
          <h-button :disabled="item.waterCount >= item.count" class="increase-btn" @click="item.waterCount++"
            >+</h-button
          >
        </div>
        <div class="td flex-1">
          <div style="width: 100%; text-align: center">￥ {{ item.price * item.waterCount }}</div>
        </div>
      </div>
    </div>

    <div class="title">订单备注</div>
    <div class="order-info" style="width: 50%">
      <h-input
        placeholder="请将购买需求在备注中说明"
        :maxlength="50"
        show-count
        v-model:value="orderInfoData.orderRemark"
      ></h-input>
    </div>

    <div class="order-amount">
      <span>总金额</span>
      <span>￥{{ orderAmountComputed }}</span>
    </div>
    <div style="margin: 24px 0; display: flex; justify-content: flex-end">
      <h-button type="primary" style="width: 160px; height: 40px" @click="handleSubmitOrder"> 提交订单</h-button>
    </div>
  </div>

  <AddressModal v-model:visible="visibleModal" @confirm="handleModalConfirm" />
</template>

<style lang="less" scoped>
.order-check {
  width: 100%;
  .header-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      font-size: 16px;
      color: #666;
      font-weight: 700;
    }

    .steps {
      width: 800px;
    }

    :deep(.ant-steps-item-title) {
      width: 128px;
    }
  }
  .title {
    color: #333;
    margin: 20px 0;
    font-size: 14px;
    font-weight: 700;
  }
  .address-list {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    .address-item-checked {
      width: 25%;
      height: 142px;
      border: 2px solid #2a82db;
      position: relative;
      font-size: 14px;
      line-height: 12px;
      margin-right: 10px;
      padding: 10px;
      box-sizing: border-box;
      cursor: pointer;
      .checked-tag {
        position: absolute;
        right: 0;
        top: 0;
        background: #f2f9ff;
        font-size: 12px;
        padding: 7px;
        color: #2a91e2;
        cursor: pointer;
      }
      .checked-right {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 0;
        height: 0;
        border-right: 12px solid #2a82db;
        border-top: 12px solid transparent;
        border-bottom: 12px solid #2a82db;
        border-left: 12px solid transparent;
        color: #fff;
        font-size: 12px;
        cursor: pointer;
      }
    }
    .address-item {
      width: 25%;
      height: 142px;
      border: 2px solid #dadada;
      position: relative;
      font-size: 14px;
      line-height: 12px;
      margin-right: 10px;
      padding: 10px;
      box-sizing: border-box;
      cursor: pointer;
      .checked-tag {
        position: absolute;
        right: 0;
        top: 0;
        background: #dadada;
        font-size: 12px;
        padding: 7px;
        color: #fff;
        cursor: pointer;
      }
    }

    .address-item-title {
      font-size: 12px;
      color: #333;
      height: 32px;
      line-height: 32px;
      border-bottom: 1px solid #f3f3f3;
    }
    .address-item-content {
      font-size: 12px;
      position: absolute;
      left: 0;
      top: 38px;
      width: 100%;
      padding: 10px;
      line-height: 24px;
      box-sizing: border-box;
    }
  }
  .order-info {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;
    .tr-border {
      background-color: #f2f9ff;
    }

    .tr {
      width: 100%;
      display: flex;
      border: 1px solid #8cbbec;
    }

    .th {
      display: flex;
      align-items: center;
      font-size: 14px;
      padding: 12px;
    }
    .td {
      display: flex;
      align-items: center;
      font-size: 13px;
      padding: 12px;
      img {
        width: 100px;
        height: 100px;
        object-fit: cover;
      }
    }
    .border-left {
      border-left: 1px solid #eef2f7;
    }
    .number-controls {
      gap: 8px;
      .decrease-btn,
      .increase-btn {
        height: 30px;
        width: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      :deep(.ant-input-number-input) {
        text-align: center;
        width: 80px;
      }
    }
    .bottom-info {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 48px;
    }

    .submit-btn {
      width: 100px;
      height: 100%;
      align-items: center;
      text-align: center;
    }
  }
  .order-amount {
    background: #f2f9ff;
    padding: 16px;
    display: flex;
    color: #333333;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    font-size: 14px;
    font-weight: 700;
  }
  .text-blue {
    color: #2a82db;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-2 {
    flex: 2;
  }
  .flex-3 {
    flex: 3;
  }
  .center {
    display: flex;
    align-items: center;
  }
}
</style>
