<script lang="ts" setup>
import { computed, createVNode, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';


import {
  Anchor as hAnchor,
  <PERSON><PERSON> as hButton,
  Spin as hSpin,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Checkbox as hCheckbox,
  Form as hForm,
  FormItem as hFormItem,
  Row as hRow,
  Col as hCol,
  Popconfirm as hPopconfirm,
  Upload as hUpload,
  Input as hInput,
  Modal as hModal
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  DownloadOutlined,
  VerticalAlignBottomOutlined,
  ExclamationCircleFilled,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  SendOutlined,
  PhoneOutlined,
  VerticalAlignTopOutlined,
  DeleteOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { download, tripApi, teamListApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import relativeTime from 'dayjs/plugin/relativeTime';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';

import { CityResponse, CityItem, TCteateTeam, RRechargeParmas } from '@haierbusiness-front/common-libs';
import { fileApi } from '@haierbusiness-front/apis';
import type { TableColumnType } from 'ant-design-vue';
import { useRouter } from 'vue-router';


const route = ref(getCurrentRoute());
const router = useRouter();

const id = route.value?.query?.id;

dayjs.extend(relativeTime);

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const labelCol = { span: 6 };
const wrapperCol = { span: 16 };
// 用户选择
const params = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});

const rechargeForm = ref<RRechargeParmas>({
  // 当前登陆人信息
  contactDeptName: loginUser.value?.departmentName || loginUser.value?.enterpriseName, // 部门名称
  username: loginUser.value?.username, //联系人工号
  nickName: loginUser.value?.nickName, //联系人名称
  phone: loginUser.value?.phone, //联系人电话
  email: loginUser.value?.email, //联系人邮箱

  // 交接人直线是否审批
  isSp: true,
  // 是否主动交接
  isActive: 1,

  // 承接人信息
  cjcontactDeptName: '',
  cjusername: '',
  cjnickName: '',
  cjphone: '',
  cjemail: '',

  // 交接人信息
  jjcontactDeptName: '',
  jjusername: '',
  jjnickName: '',
  jjphone: '',
  jjemail: '',
  cjzxName: '', // 承接人直线
})

const validateTrue = (_: any, value: boolean) =>
  value === true
    ? Promise.resolve()
    : Promise.reject(new Error('请确认明细数据'))
 // 验证电话号码和手机号
 const validatePassTel = async (_rule: Rule, value: string) => {
      let isPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
      let isMob = /^(?:(?:\d{3}-)?\d{8}|^(?:\d{4}-)?\d{7,8})(?:-\d+)?$/;
      if (isMob.test(value) || isPhone.test(value) || !value) {
        return Promise.resolve();
      } else {
        return Promise.reject('请输入正确的电话号码');
      }
    };
const rules: Record<string, Rule[]> = {
  jjnickName: [{ required: true, message: '请选择交接人', trigger: 'change' }],
  jjusername: [{ required: true, message: '请选择交接人', trigger: 'change' }],
  jjphone: [{ required: true, message: '请输入电话', validator:validatePassTel, trigger: 'change'  }],
  cjnickName: [{ required: true, message: '请选择承接人', trigger: 'change' }],
  cjusername: [{ required: true, message: '请选择承接人', trigger: 'change' }],
  cjphone: [{ required: true, message: '请输入电话', validator:validatePassTel, trigger: 'change'  }],

};

const formRef = ref()


// 提交
const onSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
    })
    .catch(error => {
    });
}



// 下载人员明细模板
const ticketTempFile = new URL('@/assets/template/team_recharge_template.xlsx', import.meta.url).href;
const downloadRechargeTemp = (filePath: string, name: string) => {
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = filePath;
  link.setAttribute('download', name);
  document.body.appendChild(link);
  link.click();
};

// 选择人
const userNameChange = (userInfo: IUserInfo) => {
  if (rechargeForm.value.isActive == 1) {
    rechargeForm.value.cjcontactDeptName = userInfo?.departmentName || userInfo?.enterpriseName;
    rechargeForm.value.cjusername = userInfo?.username;
    rechargeForm.value.cjnickName = userInfo?.nickName;
    rechargeForm.value.cjphone = userInfo?.phone;
    rechargeForm.value.cjemail = userInfo?.email;
  } else {
    rechargeForm.value.jjnickName = userInfo?.nickName;
    rechargeForm.value.jjusername = userInfo?.username;
    rechargeForm.value.jjcontactDeptName = userInfo?.departmentName || userInfo?.enterpriseName;
    rechargeForm.value.jjphone = userInfo?.phone;
    rechargeForm.value.jjemail = userInfo?.email;
  }

};


</script>


<template>
  <div class="container">
    <div class="row flex">
      <div class="apply-con flex">
        <h-row justify="center" align="middle" style="padding: 40px; font-size: 20px ; font-weight: 600;">
          账户交接
        </h-row>
        <h-form class="mt-30" ref="formRef" :model="rechargeForm" :label-col="labelCol" :rules="rules"
          :wrapper-col="wrapperCol" labelAlign="left">
          <!-- 当前登陆人 -->
          <a-card style="width: 100%">
            <h3>基础信息</h3>
            <h-row>
              <h-col :span="8">
                <h-form-item name="nickName" label="负责人">
                  <h-input redaonly disabled v-model:value="rechargeForm.nickName" placeholder="请输入负责人" />
                </h-form-item>
              </h-col>

              <h-col :span="8">
                <h-form-item name="username" label="工号">
                  <h-input redaonly disabled v-model:value="rechargeForm.username" placeholder="请输入工号" />
                </h-form-item>
              </h-col>

              <h-col :span="8">
                <h-form-item name="contactDeptName" label="部门">
                  <h-input redaonly disabled v-model:value="rechargeForm.contactDeptName" placeholder="请输入部门" />
                </h-form-item>
              </h-col>

              <h-col :span="8">
                <h-form-item name="phone" label="联系电话">
                  <h-input v-model:value="rechargeForm.phone" placeholder="请输入联系电话" />
                </h-form-item>
              </h-col>

              <h-col :span="8">
                <h-form-item name="email" label="邮箱">
                  <h-input v-model:value="rechargeForm.email" placeholder="请输入邮箱" />
                </h-form-item>
              </h-col>


            </h-row>
          </a-card>

          <!-- 交接类型 -->
          <h-form-item style="margin-top: 20px;">
            <h-radio-group v-model:value="rechargeForm.isActive">
              <h-radio :value="1">主动交接</h-radio>
              <h-radio :value="2">强制交接</h-radio>
            </h-radio-group>
          </h-form-item>

          <a-card style="width: 100%">
            <!-- 主动交接承接人 -->
            <div v-if="rechargeForm.isActive == 1">
              <h3>承接人信息</h3>
              <h-row>
                <h-col :span="8">
                  <h-form-item name="cjnickName" label="承接人">
                    <user-select :value="rechargeForm.cjnickName" placeholder="选择承接人" :params="params"
                      @change="(userInfo: IUserInfo) => userNameChange(userInfo)" class="whole-line font-size-14" />
                  </h-form-item>
                </h-col>

                <h-col :span="8">
                  <h-form-item name="cjusername" label="工号">
                    <h-input redaonly disabled v-model:value="rechargeForm.cjusername" placeholder="请输入工号" />
                  </h-form-item>
                </h-col>

                <h-col :span="8">
                  <h-form-item name="cjcontactDeptName" label="部门">
                    <h-input redaonly disabled v-model:value="rechargeForm.cjcontactDeptName" placeholder="请输入部门" />
                  </h-form-item>
                </h-col>

                <h-col :span="8">
                  <h-form-item name="cjphone" label="联系电话">
                    <h-input v-model:value="rechargeForm.cjphone" placeholder="请输入联系电话" />
                  </h-form-item>
                </h-col>

                <h-col :span="8">
                  <h-form-item name="cjemail" label="邮箱">
                    <h-input v-model:value="rechargeForm.cjemail" placeholder="请输入邮箱" />
                  </h-form-item>
                </h-col>

              </h-row>
              <h-row>
                <h-col :span="8">
                  <h-form-item name="cjzxName" label="承接人直线">
                    <h-input redaonly disabled v-model:value="rechargeForm.cjzxName" placeholder="请输入承接人直线" />
                  </h-form-item>
                </h-col>
              </h-row>

              <h-row>
                <h-col :span="16">
                  <h-form-item :label-col="{ span: 3 }" name="cjzxName" label="交接原因">
                    <a-textarea v-model:value="rechargeForm.result" placeholder="请输入交接原因" allow-clear />
                  </h-form-item>
                </h-col>
              </h-row>
            </div>
            <!-- 强制交接交接人 -->
            <div v-else>
              <h-row justify="space-between">
                <h-col>
                  <h3>
                    交接人信息
                  </h3>
                </h-col>
                <h-col style="text-align: right;">
                  <h3 style="display: flex; align-items: center; color: blue;">
                    <span style="margin-right: 5px;">交接人直线是否审批</span>
                    <a-switch v-model:checked="rechargeForm.isSp" checked-children="开" un-checked-children="关" />
                  </h3>
                </h-col>

              </h-row>

              <h-row>
                <h-col :span="8">
                  <h-form-item name="jjnickName" label="交接人">
                    <user-select :value="rechargeForm.jjnickName" placeholder="选择承接人" :params="params"
                      @change="(userInfo: IUserInfo) => userNameChange(userInfo)" class="whole-line font-size-14" />
                  </h-form-item>
                </h-col>

                <h-col :span="8">
                  <h-form-item name="jjusername" label="工号">
                    <h-input redaonly disabled v-model:value="rechargeForm.jjusername" placeholder="请输入工号" />
                  </h-form-item>
                </h-col>

                <h-col :span="8">
                  <h-form-item name="jjcontactDeptName" label="部门">
                    <h-input redaonly disabled v-model:value="rechargeForm.jjcontactDeptName" placeholder="请输入部门" />
                  </h-form-item>
                </h-col>

                <h-col :span="8">
                  <h-form-item name="jjphone" label="联系电话">
                    <h-input v-model:value="rechargeForm.jjphone" placeholder="请输入联系电话" />
                  </h-form-item>
                </h-col>

                <h-col :span="8">
                  <h-form-item name="jjemail" label="邮箱">
                    <h-input v-model:value="rechargeForm.jjemail" placeholder="请输入邮箱" />
                  </h-form-item>
                </h-col>

              </h-row>

              <h-row>
                <h-col :span="8">
                  <h-form-item name="cjzxName" label="承接人直线">
                    <h-input redaonly disabled v-model:value="rechargeForm.cjzxName" placeholder="请输入承接人直线" />
                  </h-form-item>
                </h-col>
              </h-row>

              <h-row>
                <h-col :span="16">
                  <h-form-item :label-col="{ span: 3 }" name="cjzxName" label="交接原因">
                    <a-textarea v-model:value="rechargeForm.result" placeholder="请输入交接原因" allow-clear />
                  </h-form-item>
                </h-col>
              </h-row>
            </div>
          </a-card>
          <h-form-item :wrapper-col="{ offset: 10 }" style="margin-top: 30px;">
            <h-button type="primary" @click="onSubmit">提交</h-button>
            <h-button style="margin-left: 50px">取消</h-button>
          </h-form-item>

        </h-form>

      </div>
    </div>

  </div>
</template>

<style lang="less" scoped>
@import url(./recharge.less);

.change-title {
  position: absolute;
  right: 0;
  top: -80px;
  width: 300px;

  .ant-col-6,
  .ant-col-18 {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #000000d9;
  }
}

.mt-5 {
  margin-top: 5px;
}

.ml-5 {
  margin-left: 5px;
}

.color-eee {
  color: #a8a7a7;
}

.com-box {
  background: #f5f5f5;
  padding: 20px;
  margin-top: 20px;

  .whole-line {
    height: 40px;
    font-weight: 600;
  }

  .info-box {
    display: flex;
    align-items: flex-start;
    padding: 10px 20px;
    border: 1px solid #ffdb5c;
    background: #fffbd8;
  }

  :deep(.ant-form-item-control-input-content) {
    display: flex;
    align-items: center;
  }
}

.title {
  height: 60px;
}

.download-btn {
  color: #408cff;
  cursor: pointer;
}

:deep(.city-chose-box) {
  width: 200px !important;
}

:deep(.mr-10) {
  margin-right: 10px;
}

.background-eee {
  padding: 10px;
  background: #f4f4f4;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>