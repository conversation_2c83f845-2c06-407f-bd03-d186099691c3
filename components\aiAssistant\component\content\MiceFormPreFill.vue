<template>
  <div v-if="miceRequest" class="form-prefill-container">
    <a-card
        title="会务预填单生成完成"
        class="prefill-card"
    >
      <div class="card-content">
        <a-alert
            message="会务预填单已生成完毕"
            description="点击接收预览按钮查看效果，或复制JSON数据到剪贴板"
            type="success"
            show-icon
            class="alert-message"
        />

        <div class="action-buttons">
          <a-button
              type="primary"
              @click="handlePreview"
              class="preview-btn"
          >
            <template #icon>
              <EyeOutlined />
            </template>
            接收预览
          </a-button>
          <a-button
              @click="copyJson"
              class="copy-btn"
          >
            <template #icon>
              <CopyOutlined />
            </template>
            复制JSON
          </a-button>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { EyeOutlined, CopyOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

const props = defineProps({
  miceRequest: Object
});

const emit = defineEmits(['preview']);

const handlePreview = () => {
  emit('preview', props.miceRequest);
};

const copyJson = () => {
  if (!props.miceRequest) return;

  const jsonString = JSON.stringify(props.miceRequest, null, 2);
  navigator.clipboard.writeText(jsonString);
  message.success('JSON已复制到剪贴板');
};
</script>
<style scoped lang="less">
.form-prefill-container {
  display: flex;
  justify-content: left;
  margin-bottom: 16px;
  width: 100%;

  .prefill-card {
    width: 100%;
    max-width: 600px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;

    /* 科技感边框和动画 - 只执行一次 */
    border: 1px solid transparent;
    background: linear-gradient(#fff, #fff) padding-box,
    linear-gradient(45deg, #40a9ff, #1890ff, #722ed1) border-box;
    animation: borderPulse 4.2s ease-in-out forwards; /* 只执行一次 */

    /* 内部微光效果 - 执行三次 */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 50%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(24, 144, 255, 0.2), transparent);
      animation: lightStreak 4.2s 3 forwards; /* 执行三次 */
    }

    .card-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px;
      position: relative;
      z-index: 1;

      .alert-message {
        margin-bottom: 16px;
        width: 100%;
      }

      .action-buttons {
        display: flex;
        gap: 12px;
        margin-top: 8px;
        width: 100%;
        justify-content: center;

        .preview-btn, .copy-btn {
          border-radius: 4px;
          min-width: 120px;
          position: relative;
          overflow: hidden;

          /* 按钮悬停效果 */
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
          }

          /* 按钮内部微光效果 */
          &::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -60%;
            width: 20%;
            height: 200%;
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(25deg);
            transition: all 0.6s;
          }

          &:hover::after {
            left: 140%;
          }
        }
      }
    }
  }
}

/* 边框脉动动画 - 只执行一次，4.2秒内完成从明显到微弱的过渡 */
@keyframes borderPulse {
  0% {
    box-shadow: 0 0 15px rgba(24, 144, 255, 0.6),
    0 0 25px rgba(114, 46, 209, 0.4);
  }
  100% {
    box-shadow: 0 0 4px rgba(24, 144, 255, 0.2),
    0 0 5px rgba(114, 46, 209, 0.15);
    border-color: rgba(24, 144, 255, 0.2); /* 最终边框颜色 */
  }
}

/* 内部流光动画 - 执行三次，每次4.2秒 */
@keyframes lightStreak {
  0% {
    opacity: 0.8;
    left: -100%;
  }
  100% {
    opacity: 0.1;
    left: 150%;
  }
}
</style>
