<script setup lang="ts">
import { onMounted, PropType, ref } from "vue";
import {
  Modal as hModal,
  Input as hInput,
  Spin as hSpin,
  Space as hSpace,
  Button as hButton,
  Row as hRow,
  Col as hCol,
  Tabs as hTabs,
  TabPane as hTabPane,
  Image as hImage,
  message,
} from "ant-design-vue";
import { IPayData } from "@haierbusiness-front/common-libs/src/pay/model/basicModel";
import { travelCoinHaierPayApi } from "@haierbusiness-front/apis";
import { useRequest } from "vue-request";
import { isMobile } from "@haierbusiness-front/utils/src/commonUtil";
import { PaySourceConstant } from "@haierbusiness-front/common-libs";

const props = defineProps({
  param: Object as PropType<IPayData>,
});

const emit = defineEmits<{
  (e: "payComplete", isPayComplete: boolean): void;
}>();

const payComplete = () => {
  emit("payComplete", true);
};

const countdownTime = 60;

const noCoin = ref(false);

/**
 * 验证码倒计时
 */
const countdown = ref(countdownTime);

/**
 * 开始验证码倒计时
 */
const startCountdown = () => {
  const sh = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      countdown.value = countdownTime;
      clearInterval(sh);
    }
  }, 1000);
};

const reSendLoading = ref(false);
const goPayLoading = ref(false);

const visible = ref(false);
/**
 * 账户
 */
const accountCode = ref();

/**
 * 余额
 */
const balance = ref();

/**
 * 手机号
 */
const phone = ref();

/**
 * 验证码
 */
const captcha = ref();

const {
  data: searchAccountData,
  run: searchAccountApiRun,
  loading: searchAccountLoading,
} = useRequest(travelCoinHaierPayApi.searchAccount, {
  manual: false,
  onSuccess: (data, param) => {
    if (data) {
      accountCode.value = data.id;
      phone.value = data.phone;
      balance.value = data.balance;
      noCoin.value = true;
    }
  },
});

const reSend = () => {
  reSendLoading.value = true;
  countdown.value = countdownTime - 1;
  sendSms(() => {
    reSendLoading.value = false;
    startCountdown();
  });
};

const goPay = () => {
  goPayLoading.value = true;
  if (countdown.value === countdownTime) {
    sendSms(() => {
      goPayLoading.value = false;
      countdown.value = countdownTime - 1;
      visible.value = true;
      startCountdown();
    });
  } else {
    goPayLoading.value = false;
    visible.value = true;
  }
};

const sendSms = (success: () => void) => {
  travelCoinHaierPayApi
    .sendCaptcha(
      {
        orderCode: props.param?.orderCode,
        applicationCode: props.param?.applicationCode,
        payTypes: props.param?.payTypes,
        username: props.param?.username,
        providerCode: props.param?.providerCode,
        amount: Number(props.param?.amount),
        orderDetailsUrl: props.param?.orderDetailsUrl,
        notifyUrl: props.param?.notifyUrl,
        callbackUrl: props.param?.callbackUrl,
        description: props.param?.description,
        payload: props.param?.payload,
        paySource: isMobile() ? PaySourceConstant.MOBILE.type : PaySourceConstant.PC.type,
      },
      {
        applicationCode: props.param?.applicationCode,
        excludes: "paySource",
        nonce: props.param?.hbNonce,
        timestamp: props.param?.hbTimestamp,
        sign: props.param?.sign,
      }
    )
    .then((it) => {
      success();
    })
    .finally(() => {
      reSendLoading.value = false;
      goPayLoading.value = false;
    });
};

const confirmPay = () => {
  let regexp = new RegExp(/^\d{6}$/);
  if (!regexp.test(captcha.value)) {
    message.error("验证码格式错误!");
    return;
  }
  travelCoinHaierPayApi
    .pay({
      orderCode: props.param?.orderCode,
      captcha: captcha.value,
      paymentMethod: 2
    })
    .then((it) => {
      visible.value = false;
      payComplete();
    });
};
</script>
<template>
  <h-modal
    v-model:visible="visible"
    title="请输入验证码"
    @ok="confirmPay"
    okText="确认支付"
  >
    <p>验证码已发送至{{ phone }}</p>
    <p>
      <h-row>
        <h-col span="16"> <h-input v-model:value="captcha" placeholder="验证码" /></h-col>
        <h-col span="7" offset="1">
          <div v-if="countdown != countdownTime" style="margin-top: 4px">
            {{ countdown }}S 后重新发送
          </div>
          <div v-else>
            &nbsp;&nbsp;&nbsp;<hButton
              type="primary"
              :loading="reSendLoading"
              @click="reSend"
            >
              重新发送</hButton
            >
          </div>
        </h-col>
      </h-row>
    </p>
  </h-modal>

  <h-row class="coin-account" style="margin-top: 2vh">
    <h-col :span="16" offset="4">
      <div class="account-card">
        <div class="coin-background"></div>
        <div class="card-no">No. {{ accountCode || "--------" }}</div>
        <div class="phone-no">Tel. {{ phone || "-------------" }}</div>
        <div class="coin-balance">
          <div class="title">余额（元）</div>
          <div class="balance">
            {{ balance || "0.00" }}
          </div>
        </div>
        <div class="coin-balance-desc">
          <span
            >当前余额为“iHaier-福利积分消费”所充值“海尔机票预订”，非福利积分账户余额。</span
          >
        </div>
      </div>
    </h-col>
    <h-col :span="24" style="margin-top: 4vh">
      <div style="color: red; padding-bottom: 12px;">福利积分预定机票,结算方式为企业对公,海尔商旅不对个人开具发票或行程单。</div>
      <hButton
        v-if="noCoin"
        type="primary"
        size="large"
        :loading="goPayLoading"
        @click="goPay"
      >
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;支&nbsp;&nbsp;&nbsp;&nbsp;付&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
      </hButton>
      <span v-else style="color: red"
        >您的账户不存在，请前往“iHaier-福利积分开户”开户，并在“福利积分消费”充值“海尔机票预订”。</span
      >
    </h-col>
  </h-row>
</template>

<style scoped lang="less">
.account-card {
  font-size: 2.8vh;
  font-weight: 700;
  color: rgb(66, 66, 63);

  height: 30vh;
  width: 100%;
  background: linear-gradient(
    to right,
    rgba(245, 218, 139, 0.808),
    rgba(240, 206, 117, 0.908)
  );
  border-radius: 0.8vh;
  box-shadow: 0px 10px 10px 0px rgb(0 0 0 / 20%);

  .coin-background {
    top: 26%;
    left: 66%;
    float: left;
    position: absolute;
    z-index: -1;
    width: 50%;
    height: 20vh;
    background-image: url(../assets/image/coin/background.svg);
    background-repeat: no-repeat;
    background-size: contain;
    transform: skewX(-3deg);
  }

  .card-no {
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
    width: 25%;
    top: 8%;
    left: 5%;
    float: left;
    position: absolute;
  }

  .phone-no {
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
    top: 8%;
    left: 60%;
    float: left;
    position: absolute;
  }

  .coin-balance {
    top: 58%;
    left: 8%;
    float: left;
    position: absolute;
    width: 20%;
    text-align: left;

    .title {
      width: 100%;
      font-size: 2vh;
    }

    .balance {
      width: 100%;
      font-size: 4vh;
    }
  }

  .coin-balance-desc {
    color: #4b474796;
    text-align: right;
    width: 98%;
    top: 90%;
    float: right;
    position: absolute;
    font-size: 0.8vh;
  }
}
</style>
