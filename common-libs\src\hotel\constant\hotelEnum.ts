//酒店信息类型
export const LocalHotelInfoTypeEnum = {
  HOTEL: 1,
  REST: 2,
  MIX: 3,

  "1": "住宿",
  "2": "餐饮",
  "3": "综合"
}



//酒店菜品分类
export const DishTypeEnum = {
  "1": "特色菜",
  "2": "其他菜",
  "3": "凉菜",
  "4": "热菜",
  "5": "汤类",
  "6": "甜品",
  "7": "套餐",
  "8": "主食",
}


//酒店房型枚举
export const LocalHotelRoomTypeEnum = {
  "1": "单人间",
  "2": "标准间",
  "3": "三人间",
  "4": "大床间",
  "5": "套房",
  "6": "别墅"
}

//酒店床型枚举
export const LocalHotelBedTypeEnum = {
  "1": "单床",
  "2": "双床",
  "3": "大床",
  "4": "三人床"
}

//酒店早餐枚举
export const LocalHotelBreakfastTypeEnum = {
  "-1": "有早",
  "0": "无早",
  "1": "单早",
  "2": "双早",
  "3": "三早",
  "4": "四早",
}

//全单折扣枚举
export const fullSingleDiscountEnum = {
  "0": "无折扣",
  "80": "80折",
  "85": "85折",
  "88": "88折",
  "90": "90折",
  "92": "92折",
  "95": "95折",
}


//酒店有效状态枚举
export const LocalHotelStateEnum = {
  "0": "待审核",
  "10": "正常",
  "20": "修改中",
  "30": "审核驳回",
}


//酒店有效状态枚举
export const LocalHotelDishEnum = {
  "1": "特色菜",
  "2": "其他菜",
  "3": "凉菜",
  "4": "热菜",
  "5": "汤类",
  "6": "甜品",
  "7": "套餐",
  "8": "主食",
}

export const LocalHostOuterHotelReasonEnum = {
  "1": "落实酒店已满房间",
  "2": "其他原因"
}

//内部酒店枚举
export const LocalHotelInnerHotelEnum = {
  "78": "海尔山庄",
  "83": "青岛海尔洲际酒店",
  "85": "创牌商务餐厅",
  "145": "国际交流中心（南海路店）",
  "146": "全球创新中心（麦岛店）",
}

// 酒店(房)订单退票原因
export const LocalhotelBackHotelCaseEnum = {
  "1": "酒店选择为海尔酒店时，不能修改其它协议酒店",
  "2": "酒店或房型选择错误需修改",
  "3": "没房",
  "4": "取消预订",
  "5": "预算不足",
  "6": "其它",
  "7": "预算太高"
}

// 餐饮订单退回原因
export const LocalhotelBackRestaurantCaseEnum = {
  "1": "酒店选择为海尔酒店时，不能修改其它协议酒店",
  "2": "酒店选择错误需修改",
  "3": "未按提前预订，就餐没位置",
  "4": "取消预订",
  "5": "预算不足",
  "6": "其它",
  "7": "预算太高"
}

//相册类型枚举
export const LocalHotelAlbumEnum = {
  "0": "普通",
  "1": "大堂",
  "2": "外景",
  "3": "餐厅",
  "4": "客房"
}

//是否通知客人
export const LocalhotelNotifiedGuestEnum = {
  "1": "已通知客人",
  "0": "未通知客人"
}

//是否通知餐厅
export const LocalhotelNotifiedHotelEnum = {
  "1": "已通知餐厅",
  "0": "未通知餐厅"
}

/* function genStoreToEnum() {
  let arr = [{
    key: "1",
    value: "1、酒店选择为海尔酒店时，不能修改其它协议酒店"
  }, {
    key: "2",
    value: "2、酒店选择错误需修改"
  }, {
    key: "3",
    value: "3、未按提前预订，就餐没位置"
  }, {
    key: "4",
    value: "4、取消预订"
  }, {
    key: "5",
    value: "5、预算不足"
  }, {
    key: "6",
    value: "6、其它"
  }, {
    key: "7",
    value: "7、预算太高"
  }];
  let obj = {};
  for (let item of arr) {
    obj[item.key] = item.value;
  }
  console.log(JSON.stringify(obj));
} */

//审核状态
export const LocalHotelBillStateEnum = {
  "REJECT": 0,
  "SUBMIT": 10,
  "HEDUIZHONG": 15,
  "CHECKED": 20,

  "0": "已驳回",
  "10": "已提交",
  "15": "核对中",
  "20": "已核对",
}

//餐房汇总单状态
export const localBalanceStateEnum = {
  "10": "已汇总",
  "20": "已确认",
  "30": "已结算",
  "0": "已取消",
}

//订单状态, 审批状态, 账单状态 联合组成的一个状态
// 保留显示：						订单状态	审批状态	账单状态        自己定义
// 00：已取消（订单）			00				-1				null   △    10
// 10：已保存 （订单）； 	10				-1				null         20
// 10：审批中（审批）； 	20				10				null   ○     30
// 20：审批通过（审批）；	20				20				null   ○     40
// 30：审批驳回（审批）；	00				30				null   △    50
// 30：预定完成（订单）；	30				不限			null         60
// 40：已退回（订单）；		40				不限			null         70
// 00：已驳回（账单）；		不限			不限			00           80
// 10：已提交（账单）；		不限			不限			10           90
// 20：已核对（账单）；		不限			不限			20           100
export const LocalhotelUnionStateEnum = {
  "10": "已取消", //订单
  "20": "已保存", //订单
  "30": "审批中", //审批
  "40": "审批通过", //审批
  "50": "审批驳回", //审批
  "60": "预定完成", //订单
  "70": "已退回", //订单
  "80": "账单已驳回", //账单 0
  "90": "账单已提交", //账单 10
  "100": "账单已核对", //账单 20
}

//由分立状态 转 联合状态
export function getLocalhotelUnionState(orderState, approvalState, billState) {
  orderState = orderState * 1;
  approvalState = approvalState * 1;
  billState = billState * 1;

  if (billState >= 0) {
    if (billState == 0) {
      return 80;
    }
    if (billState == 10) {
      return 90;
    }
    if (billState == 20) {
      return 100;
    }
  }

  if (orderState == 30) {
    return 60;
  }
  if (orderState == 40) {
    return 70;
  }

  if (orderState == 0 && approvalState == -1) {
    return 10;
  }
  if (orderState == 10 && approvalState == -1) {
    return 20;
  }
  if (orderState == 20 && approvalState == 10) {
    return 30;
  }
  if (orderState == 20 && approvalState == 20) {
    return 40;
  }
  if (orderState == 0 && approvalState == 30) {
    return 50;
  }
  return -1;
}

//由联合状态 转 分立状态
export function splitLocalhotelUnionState(unionState) {
  let orderState = "";
  let approvalState = "";
  let billState = "";

  switch (unionState * 1) {
    case 10:
      orderState = 0;
      approvalState = -1;
      billState = null;
      break;
    case 20:
      orderState = 10;
      approvalState = -1;
      billState = null;
      break;
    case 30:
      orderState = 20;
      approvalState = 10;
      billState = null;
      break;
    case 40:
      orderState = 20;
      approvalState = 20;
      billState = null;
      break;
    case 50:
      orderState = 0;
      approvalState = 30;
      billState = null;
      break;
    case 60:
      orderState = 30;
      approvalState = "";
      billState = null;
      break;
    case 70:
      orderState = 40;
      approvalState = "";
      billState = null;
      break;
    case 80:
      orderState = "";
      approvalState = "";
      billState = 0;
      break;
    case 90:
      orderState = "";
      approvalState = "";
      billState = 10;
      break;
    case 100:
      orderState = "";
      approvalState = "";
      billState = 20;
      break;
  }

  return {
    orderState,
    approvalState,
    billState
  }
}
