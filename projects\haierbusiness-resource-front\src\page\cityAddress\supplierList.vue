<script setup lang="ts">
import {
  BreadcrumbItem as hBreadcrumbItem,
  Breadcrumb as hBreadcrumb,
  LayoutSider as hLayoutSider,
  LayoutFooter as hLayoutFooter,
  Layout<PERSON>ontent as hLayoutContent,
  Layout<PERSON>eader as hLayoutHeader,
  Layout as hLayout,
  MenuItem as hMenuItem,
  MenuItemGroup as hMenuItemGroup,
  SubMenu as hSubMenu,
  Menu as hMenu,
  Divider as hDivider,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Result as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Tabs as hTabs,
  message,
  RadioGroup as hRadioGroup,
  RadioButton as hRadioButton,
  Table as hTable,
  Modal as hModal,
  Tree as hTree,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Switch as hSwitch,
  Select  as hSelect,
  SelectOption as hSelectOption 
} from 'ant-design-vue';
import { onMounted, ref, computed, watch,createVNode } from 'vue';
import {
  UploadOutlined,
  UserOutlined,
  NotificationOutlined,
  AppstoreOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';
import EManage from '@haierbusiness-front/components/manange/EManange.vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { addressRes,DistrictProviderMapPageReq,addressSupplierType,addressLevel,addressType } from '@haierbusiness-front/common-libs';
import { addressApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import type { TreeProps } from 'ant-design-vue';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute, getEnumOptions } from "@haierbusiness-front/utils";
const addressSupplierTypeOptions = computed(()=>{
  return getEnumOptions(addressSupplierType)
})
const addressLevelOptions = computed(()=>{
  return getEnumOptions(addressLevel)
})
const store = applicationStore();
const { resource } = storeToRefs(store);
const tabValue = ref<string>("1")
const modalTabValue = ref<string>("1")
const mappingBoxShow = ref<boolean>(false)
const addBoxShow = ref<boolean>(false)
const treeData = ref<addressRes[]>([])
const providerTreeData = ref<DistrictProviderMapPageReq[]>([])
const openBoxTitle = ref<string>("")
const districtId = ref<string>("") //国旅地址地址id
const columnsFormng:any = [
  {
    title: '供应商',
    dataIndex: 'providerCodeName',
    key: 'providerCodeName',
    align:'center',
    ellipsis: true,
  },
  {
    title: '名称',
    dataIndex: 'districtName',
    key: 'districtName',
    align:'center',
  },
  {
    title: '级别',
    dataIndex: 'districtLevel',
    key: 'districtLevel',
    align:'center',
  },
  {
    title: '目前关联',
    dataIndex: 'mainName',
    key: 'mainName',
    align:'center',
  },
  {
    title: '路径',
    dataIndex: 'pathKey',
    align:'center',
    key: 'pathKey',
  },
  {
    title: '操作',
    dataIndex: '_operator',
    align: 'center',
    width: '100px',
  },
]

const columnsp:any = [
  {
    title: '供应商',
    dataIndex: 'providerCodeName',
    key: 'providerCodeName',
    ellipsis: true,
  },
  {
    title: '名称',
    dataIndex: 'districtName',
    key: 'districtName',
  },
  {
    title: 'key',
    dataIndex: 'pathKey',
    key: 'pathKey',
  },
  {
    title: '操作',
    dataIndex: '_operator',
    align: 'center',
    width: '60px',
  },
]

  const columns:any = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true,
    align:"center",
  },
  {
    title: '级别',
    dataIndex: 'level',
    key: 'level',
    align:"center",
  },
  {
    title: '父级',
    dataIndex: 'parentName',
    key: 'parentName',
    align:"center",
  },
  {
    title: '区号',
    dataIndex: 'areaCode',
    key: 'areaCode',
    align:"center",
  },
  {
    title: '国家代码',
    dataIndex: 'code',
    key: 'code',
    align:"center",
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    align:"center",
  },
  // {
  //   title: '携程映射',
  //   dataIndex: 'providerMapList',
  //   key: 'providerMapList',
  //   align:"center",
  // },
  {
    title: '操作',
    dataIndex: '_operator',
    fixed:"right",
    align: 'center',
    width: '100px',
  },
];

// 获取行政区划列表
const searchParam = ref<addressRes>({})
const {
  data:dataList,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(addressApi.getDistrictList);

const dataSource = computed(() => dataList.value?.records || []);

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const pagination = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: dataList.value?.total,
    current: dataList.value?.pageNum,
    pageSize: dataList.value?.pageSize,
    style: { justifyContent: 'center' },
}));


// 获取供应商行政区划
const searchProviderParam = ref<DistrictProviderMapPageReq>({})
const {
  data:dataListProvider,
  run: listApiRunProvider,
  loading:loadingProvider,
  current:currentProvider,
  pageSize:pageSizeProvider,
} = usePagination(addressApi.getProviderDistrictList);

const dataSourceProvider = computed(() => dataListProvider.value?.records || []);

const handleProviderTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRunProvider({
    ...searchProviderParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const providerTableReset = () =>{
  searchProviderParam.value = {}
  listApiRunProvider({
    ...searchProviderParam.value,
    pageNum: currentProvider.value,
    pageSize: pageSizeProvider.value,
  });
}

const paginationProvider = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: dataListProvider.value?.total,
    current: dataListProvider.value?.pageNum,
    pageSize: dataListProvider.value?.pageSize,
    style: { justifyContent: 'center' },
}));

const mappingData = ref<any>([])

const toMappingMng = (row:addressRes) =>{
  openBoxTitle.value = row.name
  districtId.value = row.id
  mappingBoxShow.value = true
  // 获取已关联映射列表
  getMappingList(row.id)
}

const mappingListLoading = ref<boolean>(false)
// 获取已关联映射列表
const getMappingList= (mainId:string|number)=>{
  mappingListLoading.value = true
  addressApi.getProviderDistrictList({mainId}).then((res:any)=>{
    mappingData.value = res.records
    mappingListLoading.value = false
  })
}

const addMapping= (row:addressRes) =>{
  openBoxTitle.value = row.districtName
  districtId.value = row.id
  addBoxShow.value = true
  // 请求供应商数据
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize: 10,
  });
  getDistrictList(1,10)
}

// 请求国旅地址映射
const mappingLoading = ref<boolean>(false)
const getMappingHotelList =(code:string) =>{
  mappingLoading.value = true
  addressApi.getMappingHotelList({code:code}).then((res:any)=>{
    mappingHotelList.value = res
    mappingLoading.value = false
  })
  .catch(()=>{
    mappingLoading.value = false
  })
}

// 解除供应商映射
const delMappingAdress =  (row:any) =>{
  hModal.confirm({
    title: '确定要删除此映射吗？',
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {
      return new Promise((resolve, reject) => {
        addressApi.districtProviderRelieveMap({providerDistrictId:row.id}).then((res:any)=>{
          message.success('刪除成功')
          getMappingList(districtId.value)
          resolve()
        })
      }).catch(()=>{
        resolve()
      })
    },
    onCancel() {
      console.log('Cancel');
    },
  });
}

// 关联地址
const association = (row:any) =>{
  hModal.confirm({
    title: '确定要关联此地址吗？',
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {
      return new Promise((resolve, reject) => {
        console.log(row,"-----------row")
        addressApi.districtProviderMapDistrict({providerDistrictId:districtId.value,districtId:row.id}).then((res:any)=>{
          message.success('关联成功')
          listApiRunProvider({
            ...searchProviderParam.value,
            pageNum: 1,
            pageSize:10,
          });
          getProviderDistrictList(1,10)
          addBoxShow.value = false
          resolve()
        })
      })
    },
    onCancel() {
      console.log('Cancel');
    },
  });
}


// 国旅tree相关
const childrenLoading = ref<boolean>(false)
const onLoadData: TreeProps['loadData'] = treeNode => {
  console.log(treeNode.id)
  return new Promise<void>(resolve => {
    childrenLoading.value = true
    addressApi.getDistrictList({parentId:treeNode.dataRef.id}).then(res=>{
      treeNode.dataRef.children = []
      res.records.forEach(item=>{
        item.children = []
      })
      treeNode.dataRef.children = res.records
      treeData.value = [...treeData.value];
      resolve()
      childrenLoading.value = false
    })
  });
}
// 展开tree
const onExpand = (expanded:boolean, record:addressRes) =>{
    // 当节点展开时，如果没有加载子节点，则进行加载
    if (expanded) {
      onLoadData({ dataRef: record });
    }
}
const treeListData = ref<any>({})
//获取tree一级列表
const treeLoading = ref<boolean>(false)
const getDistrictList = (pageNum:number|string,pageSize:number|string)=>{
  treeLoading.value = true
  addressApi.getDistrictList({level:'country',pageNum: pageNum?pageNum:1,pageSize:pageSize?pageSize:10}).then((res:any)=>{
    treeListData.value = res
    res.records.forEach((item:any)=>{
      item.children = []
    })
    treeData.value = res.records
    treeLoading.value = false
  })
  .catch(()=>{
    treeLoading.value = false
  })
}
const treePagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: treeListData.value?.total,
  current: treeListData.value?.pageNum,
  pageSize: treeListData.value?.pageSize,
  style: { justifyContent: 'center' },
}));
const handleTreeTableChange = (
pag: { current: number; pageSize: number }
) => {
  getDistrictList(pag.current,pag.pageSize)
};

// 供应商tree相关
const providerChildrenLoading = ref<boolean>(false)
const onProviderLoadData: TreeProps['loadData'] = treeNode => {
  return new Promise<void>(resolve => {
    console.log(treeNode,"-------")
    providerChildrenLoading.value = true
    addressApi.getProviderDistrictList({parentPathKey:treeNode.dataRef.pathKey}).then(res=>{
      treeNode.dataRef.children = []
      res.records.forEach((item:any)=>{
        item.children = []
      })
      treeNode.dataRef.children = res.records
      providerTreeData.value = [...providerTreeData.value];
      resolve()
      providerChildrenLoading.value = false
    })
  });
}
// 展开tree
const onExpandProvider = (expanded:boolean, record:addressRes) =>{
    // 当节点展开时，如果没有加载子节点，则进行加载
    if (expanded) {
      onProviderLoadData({ dataRef: record });
    }
}
const treeProviderListData = ref<any>({})
//获取tree一级列表
const getProviderDistrictList = (pageNum:number|string,pageSize:number|string)=>{
  addressApi.getProviderDistrictList({level:'country',pageNum: pageNum?pageNum:1,pageSize:pageSize?pageSize:10}).then((res:any)=>{
    treeProviderListData.value = res
    res.records.forEach((item:any)=>{
      item.children = []
    })
    providerTreeData.value = res.records
    })
}
const treeProviderPagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: treeProviderListData.value?.total,
  current: treeProviderListData.value?.pageNum,
  pageSize: treeProviderListData.value?.pageSize,
  style: { justifyContent: 'center' },
}));
const handleProviderTreeTableChange = (
pag: { current: number; pageSize: number }
) => {
  getProviderDistrictList(pag.current,pag.pageSize)
};

const glReset = () =>{
  searchParam.value = {}
  searchGlList()
}

const searchGlList= () =>{
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize: 10,
  });
  getDistrictList(1,10)
}

  
onMounted(()=>{
    // 请求供应商数据
    listApiRunProvider({
    ...searchProviderParam.value,
    pageNum: 1,
    pageSize:10,
  });
  getProviderDistrictList(1,10)

})
</script>

<template>
  <div class="pageBox">

    <h-row>
      <div style="margin-bottom:10px;" class="headerBox">
        <h-row>
          <h-col :span="3">
            <h-radio-group v-model:value="modalTabValue" button-style="solid">
              <h-radio-button value="1">平铺</h-radio-button>
              <h-radio-button value="2">树形</h-radio-button>
            </h-radio-group>
          </h-col>
          <h-col v-if="modalTabValue==1" :span="21">
          <h-form>
            <h-row>
              <h-col :span="6">
                <h-form-item label="供应商">
                  <h-select
                    ref="select"
                    v-model:value="searchProviderParam.providerCode"
                    style="width: 180px"
                    allow-clear
                  >
                    <h-select-option v-for="item in addressSupplierTypeOptions" :value="item.value">{{item.label}}</h-select-option> 
                  </h-select>
                </h-form-item>
              </h-col>
              <h-col :span="6">
                <h-form-item label="名称">
                  <h-input allow-clear style="width: 180px" v-model:value="searchProviderParam.name" placeholder="请输入名称" />
                </h-form-item>
              </h-col>
              <h-col :span="6">
                <h-form-item label="级别">
                  <h-select
                    ref="select"
                    v-model:value="searchProviderParam.level"
                    style="width: 180px"
                    allow-clear
                  >
                  <h-select-option v-for="item in addressLevelOptions" :value="item.value">{{item.label}}</h-select-option> 
                  </h-select>
                </h-form-item>
              </h-col>
            </h-row>
          </h-form>
        </h-col>
        </h-row>
        <h-row>
          <h-col :span="24" style="text-align: right;">
              <h-button style="margin-right: 10px" @click="providerTableReset">重置</h-button>
              <h-button
                type="primary"
                @click="handleProviderTableChange({ current: 1, pageSize: 10 })"
              >
                <template #icon>
                  <SearchOutlined />  
                </template>
                查询
              </h-button>
            </h-col>
        </h-row>
        </div>
      </h-row>
      <div v-if="modalTabValue=='1'" class="modalBox">
        <h-table :columns="columnsFormng" :size="'small'" :loading="loadingProvider" :data-source="dataSourceProvider" @change="handleProviderTableChange($event as any)" :pagination="paginationProvider">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'districtLevel'">{{ addressLevel[record.districtLevel] }}</template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button @click="addMapping(record)" type="link">映射管理</h-button>
            </template>
          </template>
        </h-table>
      </div>
      <div class="modalBox" v-else>
        <h-table
        :columns="columnsFormng"
        :dataSource="providerTreeData"
        :size="'small'"
        :loadData="onProviderLoadData"
        @expand="onExpandProvider"
        :loading="providerChildrenLoading"
        :pagination="treeProviderPagination"
        @change="handleProviderTreeTableChange($event as any)"
        rowKey="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'districtLevel'">{{ addressLevel[record.districtLevel] }}</template>
          <template v-if="column.dataIndex === '_operator'">
            <h-button @click="addMapping(record)" type="link">映射管理</h-button>
          </template>
        </template>
        <!-- <template #expandedRowRender="{record, index}">
          <h-table
            :columns="columnsFormng"
            :showHeader="false"
            :dataSource="record.children"
            :loadData="onProviderLoadData"
            @expand="onExpandProvider"
            :pagination="false"
            rowKey="id"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'districtLevel'">{{ addressLevel[record.districtLevel] }}</template>
              <template v-if="column.dataIndex === '_operator'">
                <h-button @click="addMapping(record)" type="link">映射管理</h-button>
              </template>
            </template>
          </h-table>
        </template> -->
      </h-table>
      </div>
    <!-- 维护映射关系 -->
    <h-modal
      width="600px"
      v-model:open="mappingBoxShow"
      :title="`维护【${openBoxTitle}】映射关系`"
      :footer="null"
    >
      <h-table :columns="columnsp" :size="'small'" :loading="mappingListLoading" :data-source="mappingData" :pagination="false">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === '_operator'">
            <h-button @click="delMappingAdress(record)" type="link">删除</h-button>
          </template>
        </template>
      </h-table>
      <h-button @click="addMapping" block style="margin-top:16px;">+ 新增映射</h-button>
    </h-modal>
    <!-- 新增映射 -->
    <h-modal width="1400px" v-model:open="addBoxShow" :title="`新增【${openBoxTitle}】映射关系`" :footer="null">
    <div class="headerBox" style="margin-bottom:10px;">
    <h-row>
      <h-col :span="3">
        <h-radio-group v-model:value="tabValue" button-style="solid">
          <h-radio-button value="1">平铺</h-radio-button>
          <h-radio-button value="2">树形</h-radio-button>
        </h-radio-group>
      </h-col>
      <!-- <h-col :span="3">
          <h-form-item label="是否未映射">
            <h-switch
              v-model:checked="searchParam.districtMappingFlag"
              checked-children="开"
              un-checked-children="关"
            />
          </h-form-item>
      </h-col> -->
      <h-col :span="6">
          <h-form-item v-if="tabValue==1" label="名称">
                <h-input allow-clear style="width: 240px" v-model:value="searchParam.name" placeholder="请输入名称" />
          </h-form-item>
      </h-col>
      <h-col :span="6">
        <h-form-item v-if="tabValue==1"  label="级别">
          <h-select
            ref="select"
            v-model:value="searchParam.level"
            style="width: 240px"
            allow-clear
          >
          <h-select-option v-for="item in addressLevelOptions" :value="item.value">{{item.label}}</h-select-option> 
          </h-select>
        </h-form-item>
      </h-col>
      <h-col :span="9" style="text-align: right;">
          <h-button  v-if="tabValue==1" style="margin-right: 10px" @click="glReset">重置</h-button>
          <h-button
            type="primary"
            @click="searchGlList()"
          >
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </h-button>
        </h-col>
    </h-row>
    </div>
    <div v-if="tabValue=='1'" class="contentBox">
      <!-- :row-selection="rowSelection" -->
      <h-table
        :columns="columns"
        :size="'small'"
        :scroll="{ x: 1550 }"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange($event as any)"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'level'">{{ addressLevel[record.level] }}</template>
          <template v-if="column.dataIndex === 'type'">{{ addressType[record.type] }}</template>
          <template v-if="column.dataIndex === '_operator'">

            <h-button @click="association(record)" type="link">关联</h-button>
          </template>
        </template>
      </h-table>
    </div>
    <!-- checkable -->
    <div v-else class="contentBox">
      <h-table
        :columns="columns"
        :dataSource="treeData"
        :size="'small'"
        :loadData="onLoadData"
        @expand="onExpand"
        :loading="treeLoading"
        :pagination="treePagination"
        @change="handleTreeTableChange($event as any)"
        rowKey="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'level'">{{ addressLevel[record.level] }}</template>
          <template v-if="column.dataIndex === 'type'">{{ addressType[record.type] }}</template>
          <template v-if="column.dataIndex === '_operator'">

            <h-button @click="association(record)" type="link">关联</h-button>
          </template>
        </template>
        <!-- <template #expandedRowRender="{record, index}">
          <h-table
            :columns="columns"
            :showHeader="false"
            :dataSource="record.children"
            :loadData="onLoadData"
            @expand="onExpand"
            :pagination="false"
            :loading="childrenLoading"
            rowKey="id"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'level'">{{ addressLevel[record.level] }}</template>
              <template v-if="column.dataIndex === 'type'">{{ addressType[record.type] }}</template>
              <template v-if="column.dataIndex === '_operator'">
    
                <h-button @click="association(record)" type="link">关联</h-button>
              </template>
            </template>
          </h-table>
        </template> -->
      </h-table>
    </div>

    </h-modal>
  </div>
</template>

<style scoped lang="less">
.pageBox {
  padding: 0 8px;
  .headerBox {
    width: 100%;
    // height: 60px;
    background: #fff;
    // display: flex;
    // align-items: center;
    padding:12px;
  }
  .inputBox {
    display: flex;
    align-items: center;
    margin-left: 60px;
  }
  .searchBtn{
    float:right;
  }
  .contentBox {
    margin-top: 16px;
    background: #fff;
    height: calc(100vh - 200px);
  }
}
.modalHeaderBox {
  width: 100%;
  // display: flex;
}
:deep(.ant-tree-node-content-wrapper) {
  .btnBox {
    display: none;
  }
  &:hover {
    .btnBox {
      display: inline-block;
    }
  }
}
</style>
