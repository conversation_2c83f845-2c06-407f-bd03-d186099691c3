import { download, get, post } from '../request'
import { 
    ITagFilter, 
    ITag,
    IPageResponse, 
    Result, 
    BusinessTypeEnums,
    SetTag} from '@haierbusiness-front/common-libs'


export const tagApi = {
    // list: (params: ITagFilter): Promise<IPageResponse<ITag>> => {
    //     return get('merchant/api/tag/list', params)
    // },

    page: (params: ITagFilter): Promise<IPageResponse<ITag>> => {
        return get('merchant/api/merchant/tag/getPage', params)
    },

    get: (id: number): Promise<ITag> => {
        return get('merchant/api/merchant/tag/get', {
            id
        })
    },

    save: (params: ITag): Promise<Result> => {
        return post('merchant/api/merchant/tag/insert', params)
    },

    edit: (params: ITag): Promise<Result> => {
        return post('merchant/api/merchant/tag/updateById', params)
    },

    remove: (id: number): Promise<Result> => {
        return get('merchant/api/merchant/tag/deleteById?id=' + id)
    },

    getBusinessTypeList: (params = {}): Promise<BusinessTypeEnums[]> => {
        return post('merchant/api/merchant/tag/businessTypeEnums', params)
    },

    getSetTagList: (params = {}): Promise<SetTag[]> => {
        return get('merchant/api/merchant/tag/getSetTagList', params)
    },
}
