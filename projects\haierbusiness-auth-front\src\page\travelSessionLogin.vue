<script setup lang="ts">
import { loginApi } from '@haierbusiness-front/apis';
import { PropType, ref } from 'vue';

import { message } from 'ant-design-vue';
import { ILoginResult, ILoginSearchParams } from '@haierbusiness-front/common-libs';


const props = defineProps({
    param: Object as PropType<ILoginSearchParams>
});

const emit = defineEmits<{
    (e: 'loginSuccess', result: ILoginResult): void
}>()

const loginSuccess = (result: ILoginResult) => {
    emit('loginSuccess', result)
};

const loading = ref(false);

(() => {
    const travelSession = props.param?.urlSearch.get("travel_session");
    if (!travelSession) {
        message.error("travelSession不能为空!")
        return;
    }

    loading.value = true;
    loginApi.travelSessionLogin({
      session: travelSession
    }).then(it => {
        loginSuccess({ data: it })
    }).finally(() => {
        loading.value = false;
    })
})()
</script>

<template></template>
