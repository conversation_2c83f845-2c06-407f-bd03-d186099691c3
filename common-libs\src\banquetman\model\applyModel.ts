import { IPageRequest } from "../../basic";

export class IApplyFilter extends IPageRequest  {
  
    /*订单code */
    orderCode?: string;
  
    /*餐厅名字 */
    restaurantName?: string;
  
    /*审批状态 0已创建1通过2撤销3驳回/拒绝 */
    approveStatus?: number;
  
    /*生效状态 0未生效1已生效2已失效 */
    orderEffective?: number;
  
    /*订单状态 */
    orderStatus?: number;
  
    /*申请时间0开始1结束 */
    applicationTimes?: Record<string, unknown>[];
  
    /*场景 */
    sceneType?: number;
  
    /*有效时间 */
    estimatedMealTimeEnd?: Record<string, unknown>[];
  
    /*城市code */
    cityCode?: Record<string, unknown>[];
  
    /*支付单号 */
    payCode?: string;
  
    /*管理端 签单人工号 */
    signerCode?: string;
  
    /*管理端 签单人姓名 */
    signerName?: string;
  
    /*管理端 经办人工号 */
    creator?: string;
  
    /*管理端 经办人姓名 */
    creatorName?: string;
  
    /*预算类型 */
    feeItemName?: string;
  
    /*预算部门 */
    budgetDepartmentName?: string;
      
    /*支付类型 */
    payType?: string;

    statementStatus?: string;

    settleCode?:string;

    balanceCode?:string;
    /* */
    needPage?: boolean;
  }


  // 参数接口
export interface List_4Params {
  /* */
  pageNum?: number;

  /* */
  pageSize?: number;

  /* */
  id?: number;

  /* */
  refundOrderCode?: string;

  /* */
  applicationOrderCode?: string;

  /* */
  orderBookingCode?: string;

  /* */
  refundAmount?: number;

  /* */
  restaurantName?: string;

  /* */
  signerCode?: string;

  /* */
  signerName?: string;

  statementStatus?: string;

  /* */
  dealTime?: Record<string, unknown>;

  /*处理时间0开始1结束 */
  dealTimes?: Record<string, unknown>[];

  /* */
  creator?: string;

  /* */
  createTime?: Record<string, unknown>;

  /* */
  needPage?: boolean;
}


export class IApply {
    id?: number | null
    creator?:string
    createTime?: string
    updater?: string
    updateTime?: string
}


// 参数接口
export interface SyncSettleParams {
  /* */
  pageNum?: number;
    /* */
  sameAmount?:number;
  /* */
  pageSize?: number;

  /*主键id */
  id?: number;

  /*汇总单号 */
  statementCode?: string;

  /*账单所属年月 */
  settleMonth?: Record<string, unknown>;

  /*账单日期0开始1结束 */
  settleDates?: Record<string, unknown>[];

  /*账单类型1宴请2外卖 */
  sceneType?: number;

  /*有效结算金额 */
  effectiveSettlementAmount?: number;

  /*美团结算金额 */
  mtSettlementAmount?: number;

  /*差异金额 */
  diffAmount?: number;

  /*订单状态1待确认2已确认3已取消 */
  orderStatus?: number;

  /*确认人工号 */
  confirmPersonCode?: string;

  /*确认人名称 */
  confirmPersonName?: string;

  /*创建人 */
  creator?: string;

  /*创建人名称 */
  creatorName?: string;

  /*创建时间 */
  createTime?: Record<string, unknown>;

  /*创建人电话 */
  creatorPhone?: string;

  /*更新时间 */
  updateTime?: Record<string, unknown>;

  /*更新人 */
  updater?: string;

  /*备注 */
  remark?: string;

  /* */
  needPage?: boolean;
}