<template>
  <div class="mobile-box pt-20" v-if="!showLoading">

    <van-cell-group inset class="mb-10" v-if="applyDetail?.applyNo">
      <van-cell value-class="large-value" title="申请单号" :value="applyDetail?.applyNo"></van-cell>
      <van-cell title="申请时间" :value="applyDetail?.gmtCreate"></van-cell>
      <van-cell>
        <template #value>
          <van-button v-if="confirmList.length < travelerList.length" class="btn mr-10" size="mini" @click="addConfirm"
            type="primary">添加行程单</van-button>
          <van-button class="btn" size="mini" v-if="applyDetail?.type != 1 && applyDetail?.travelReserveFlag != 0" @click="goToApproval">查看审批流</van-button>
        </template>
      </van-cell>
    </van-cell-group>
    <div v-for="(item, index) in confirmList" :key="index">
      <div v-if="travelerList.length > 1" class="title flex justify-content-center align-items-center">出差行程单{{ index + 1 }}</div>
      <div class="title flex justify-content-between align-items-center">
        <div class="flex align-items-center justify-content-center">
          <span class="shu"></span>
          <div class="text">基本信息</div>
        </div>
        <div>
          <van-button class="btn" v-if="travelerList.length > 1" size="mini" @click="deleteConfirm(index)" type="danger">删除行程单</van-button>
        </div>
      </div>

      <!-- 出行人 -->
      <van-cell-group inset class="mb-10">
        <van-field is-link @click="showMainPersonList = true; activeIndex = index;" required label-align="top" readonly>
          <template #label>
            <div class="my_field_label flex justify-content-between align-items-center">
              <div class="left">
                <span class="mr-5">出差人</span>
                <van-icon name="question-o" color="#cccccc" />
              </div>
            </div>
          </template>

          <template #input>
            <div class="color-eee" v-if="!item.travelUserName">选择出差人</div>
            <div class="flex" v-else>
              <div class="color-main mr-5">{{ `${item.travelUserName} / ${item.travelUserNo}` }}</div>
            </div>
          </template>
        </van-field>


      </van-cell-group>

      <!-- 行程计划与费用 -->
      <actual-budge ref="planBudgeRef" :cityList="item.cityList" :creatTripParma="item"></actual-budge>

    </div>

    <div class="row" style="min-height: 70vh;" v-if="confirmList.length<1">
      <van-empty>请先添加行程单</van-empty>
    </div>

    <van-popup v-model:show="showMainPersonList" position="bottom">
      <div class="out-pop" style="height: 100vh">
        <van-sticky>
          <div style="background: #fff; padding-bottom: 10px">
            <van-nav-bar title="选择出差人" left-arrow @click-left="closeMainPersonPop">
              <template #left>
                <van-icon name="arrow-left" color="#000" size="24" />
              </template>
              <template #right>
                <div class="color-main"></div>
              </template>
            </van-nav-bar>

          </div>
        </van-sticky>
        <van-list :finished="true" finished-text="没有更多了">
          <div v-for="(item, index) in travelerList" :key="index" >
            <van-cell v-if=" item.travelUserNo == loginUser?.username || !item.disabled" @click="chosePerson(item)">
              <div class="flex align-items-center out-person-checkbox">
                <div :class="index % 3 == 0 ? 'blue' : 'yellow'"
                  class="mr-20 ml-10 flex align-items-center justify-content-center img-name">
                  {{ item?.travelUserName ? item?.travelUserName.slice(-2) : '未知' }}
                </div>
                <div class="main">
                  <div class="user-name color-main">{{ item.travelUserName }}</div>
                  <div class="phone">{{ item.travelUserNo || '未知' }}</div>
                </div>
              </div>
            </van-cell>
          </div>
        </van-list>
      </div>
    </van-popup>

    <!-- 提交 -->
    <van-sticky :offset-bottom="0" position="bottom">
      <div class="bottom-order-btn">
        <van-button class="btn btn-background mr-20">取消</van-button>
        <van-button class="btn" :loading="submitLoading" @click="submitConfirmList" type="primary">提交</van-button>
      </div>
    </van-sticky>

  </div>
  <div class="mobile-box flex align-items-center justify-content-center" v-else>
    <van-loading type="spinner" />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import actualBudge from './components/actualBudge.vue';
import { tripApi } from '@haierbusiness-front/apis';
import { showConfirmDialog, showSuccessToast,showFailToast,showDialog } from 'vant';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from "@haierbusiness-front/utils";

const route = ref(getCurrentRoute());
const router = getCurrentRouter();


const store = applicationStore();

const { loginUser } = storeToRefs(store);
const confirmList = ref<Array<any>>([])
// 查询城市列表
const cityOptions = ref({});
const applyNo = route.value?.query?.applyNo;
const travelerList = ref<Array<any>>([]);

// 根据id获取详情
const queryChangeDetailByApplyNo = async (code: string) => {
  const res: any = await tripApi.queryChangeDetailByApplyNo(code);
  return res
}
const activeIndex = ref(-1)
// 选择某个出差人
const chosePerson = (item: any) => {
  confirmList.value[activeIndex.value].travelUserName = item.travelUserName
  confirmList.value[activeIndex.value].travelUserNo = item.travelUserNo
  confirmList.value[activeIndex.value].reimburFinish = item.reimburFinish
  confirmList.value[activeIndex.value].reimburseNum = item.reimburseNum
  changeTravelerList()
  showMainPersonList.value = false
}

// 删除某个行程单
const deleteConfirm = (index: number) => {
  showDialog({
    title: '警告',
    message: '确定要删除这条行程单吗?',
  }).then(() => {
    confirmList.value.splice(index, 1);
    changeTravelerList()
  });

}


// 选择出差人
const showMainPersonList = ref(false);
const closeMainPersonPop = () => {
  showMainPersonList.value = false;
};

// 根据出差申请单号与出差人工号获取 行程确认信息 
const getConfirmDetail = async (applyId: number | string, userCode: string | number) => {
  const params = {
    applyId: applyId,
    userCode: userCode
  }

  const res: any = await tripApi.getRealTrip(params);
  res?.tripList?.forEach((element: any) => {
    element.beginCityCode = element.realBeginCityCode
    element.beginCityName = element.realBeginCityName
    element.beginDate = element.realBeginDate
    element.endCityCode = element.realEndCityCode
    element.endCityName = element.realEndCityName
    element.endDate = element.realEndDate
    element.tripDetailMapList = []
  });
  return res
}
// 根据当前选择的出差人 展示出可选人员
const changeTravelerList = () => {
  travelerList.value.forEach((item: any) => {
    item.disabled = false;

    confirmList.value.forEach((confirm: any) => {
      if (item.travelUserNo == confirm.travelUserNo  || item.reimburseNum > 0) {
        item.disabled = true;
      }
    })
  });
}
// 添加行程单
const addConfirm = async () => {
  changeTravelerList()

  if (confirmList.value.length < travelerList.value?.filter(item => !item.disabled)?.length + 1) {
    let newConfirm = {}
    if(confirmList.value.length > 0) {
      newConfirm = JSON.parse(JSON.stringify(confirmList.value[0]))
    }else {
      newConfirm = JSON.parse(JSON.stringify(applyDetail.value))
    }
    newConfirm.travelUserNo = travelerList.value.find((item: any) => !item.disabled).travelUserNo
    newConfirm.travelUserName = travelerList.value.find((item: any) => !item.disabled).travelUserName
    newConfirm.reimburFinish = travelerList.value.find((item: any) => !item.disabled).reimburFinish
    newConfirm.reimburseNum = travelerList.value.find((item: any) => !item.disabled).reimburseNum

    // 如果有确认过的行程进行回显
    if (newConfirm.reimburFinish == 20) {
      const confirmRes: any = await getConfirmDetail(newConfirm.id, newConfirm.travelUserNo)
      newConfirm.tripList = confirmRes.tripList

    } else {
      newConfirm.tripList = defaultTripList.value
    }

    newConfirm.cityList = []
    
    newConfirm?.tripList?.forEach((item: any, index: number) => {
      if (index == 0) {
        newConfirm.cityList.push({
          cityCode: item.beginCityCode,
          city: item.beginCityName,
          syId: item.beginCityCodeSy,
          date: item.beginDate,
        });
      }
      newConfirm.cityList.push({
        cityCode: item.endCityCode,
        city: item.endCityName,
        syId: item.endCityCodeSy,
        date: item.endDate,
      });

      item?.tripDetailMapList?.forEach((tripMap: any) => {
        tripMap.personIdList = tripMap.travelApplyTripDetailList.map((tt: any) => tt.travelUserSyId);
      });

    });


    confirmList.value = [...confirmList.value, newConfirm]
  }else {
    showFailToast('没有可确认的用户!')
  }
}

const applyDetail = ref<any>({})


const defaultTripList = ref<Array<any>>([]);

const showLoading = ref(false);
onMounted(async () => {

  if (applyNo) {
    showLoading.value = true
    var res: any = await queryChangeDetailByApplyNo(applyNo);
    // cityOptions.value = await tripApi.district({});

    setTimeout(() => {
      showLoading.value = false
    }, 300);

    defaultTripList.value = JSON.parse(JSON.stringify(res?.tripList))


    // 判断一下行程中是否包含当前登录人,如果包含的话默认首要确认当前登录人,否则首先确认主出差人行程
    if (res.travelerList.filter((item: any) => item.travelUserNo == loginUser?.value?.username).length > 0) {
      res.travelUserName = loginUser?.value?.nickName
      res.travelUserNo = loginUser?.value?.username
    } else {
      res.travelUserName = res.travelerList.find((item: any) => item.mainFlag == '1').travelUserName;
      res.travelUserNo = res.travelerList.find((item: any) => item.mainFlag == '1').travelUserNo;
    }

    applyDetail.value = JSON.parse(JSON.stringify(res))


    // 如果是经办人进来确认,可以批量确认 否则只能确认自己的行程
    if (res.operUserNo == loginUser?.value?.username) {
      travelerList.value = res.travelerList.filter((item: any) => item.travelUserType == '0');
      res.reimburseNum = res.travelerList.filter((item: any) => item.travelUserType == '0' && item.mainFlag == '1')[0].reimburseNum
      res.reimburFinish = res.travelerList.filter((item: any) => item.travelUserType == '0' && item.mainFlag =='1')[0].reimburFinish
      if (res.reimburFinish == 20) {
        // 如果确认过行程单,重新回填确认内容
        const confirmRes: any = await getConfirmDetail(res.id, travelerList.value.find(item => item.mainFlag == '1').travelUserNo)
        res.tripList = confirmRes.tripList
        res.endDate = confirmRes.tripList[confirmRes.tripList.length - 1].endDate
        res.beginDate = confirmRes.tripList[0].beginDate
      }
    } else {
      travelerList.value = res.travelerList.filter((item: any) => item.travelUserNo == loginUser?.value?.username);
      res.reimburseNum = travelerList.value[0].reimburseNum
      res.reimburFinish = travelerList.value[0].reimburFinish

      if (res.reimburFinish == 20) {
        // 如果确认过行程单,重新回填确认内容
        const confirmRes: any = await getConfirmDetail(res.id, travelerList.value[0].travelUserNo)
        res.tripList = confirmRes.tripList
        res.endDate = confirmRes.tripList[confirmRes.tripList.length - 1].endDate
        res.beginDate = confirmRes.tripList[0].beginDate

      }
    }


    res.subsidy = res.travelReserveFlag == 1 ? true : false

    res.cityList = []
    res?.tripList?.forEach((item: any, index: number) => {
      if (index == 0) {
        res.cityList.push({
          cityCode: item.beginCityCode,
          city: item.beginCityName,
          syId: item.beginCityCodeSy,
          date: item.beginDate,
        });
      }
      res.cityList.push({
        cityCode: item.endCityCode,
        city: item.endCityName,
        syId: item.endCityCodeSy,
        date: item.endDate,
      });

      item?.tripDetailMapList?.forEach((tripMap: any) => {
        tripMap.personIdList = tripMap.travelApplyTripDetailList.map((tt: any) => tt.travelUserSyId);
      });

    });


    confirmList.value = [res]
    changeTravelerList()

  }

});
const processUrl = import.meta.env.VITE_BUSINESS_PROCESS_URL
// 审批流页面
const goToApproval = () => {
  if (!confirmList.value[0].workFlowId) {
    showFailToast('暂无审批流信息!');
    return
  }
  const url = processUrl + `?code=${confirmList.value[0].workFlowId}#/details`
  location.href = url
}

const submitLoading = ref(false)

const isBgDate = (beginDate:any, endDate:any) => {
  return new Date(applyDetail.value.beginDate).getTime() == new Date(beginDate).getTime() && new Date(applyDetail.value.endDate).getTime() == new Date(endDate).getTime()
}

// 提交确认行程
const submitConfirmList = () => {
  if(confirmList?.value?.length < 1) {
    showFailToast('请先选择行程!')
    return
  }
  showConfirmDialog({
    title: '提示',
    message:
      '请确认提交的行程信息与实际行程一致，如不一致请修改，否则影响补助发放',
  })
  .then(() => {
    // 判断最后选择的时间是否超过当前时间
    let temp = false
    showLoading.value = true
    const params:any = []
    confirmList.value.forEach(item => {
      if(new Date(item.endDate).getTime() > new Date().getTime()){
        temp = true  
      }

      params.push({
        applyNo: item.applyNo,
        travelUserNo: item.travelUserNo,
        travelUserName: item.travelUserName,
        subsidy: item.subsidy,
        realBeginDate: item.beginDate,
        realEndDate: item.endDate,
        // 判断是否变更果订单开始时间,结束时间
        consistency: isBgDate(item.beginDate, item.endDate) ? 1 : 0,
        tripList: item.tripList.map((trip: any) => {
          return {
            realBeginDate: trip.beginDate,
            realEndDate: trip.endDate,
            realBeginCityCode: trip.beginCityCode,
            realBeginCityName: trip.beginCityName,
            realEndCityCode: trip.endCityCode,
            realEndCityName: trip.endCityName,
            travelUserNo: item.travelUserNo,
            travelUserName: item.travelUserName

          }
        })
      })

    })

    if(temp) {
      showFailToast('实际行程的结束日期大于当前日期,无法进行行程确认!')
      showLoading.value = false
      return
    }

    tripApi.tripFinish(params).then((res: any) => {
      // 在用户点击按钮保存的事件内增加消息通知
      window.parent && window.parent.postMessage({
          type: 'Travel:DetailSubmitInEES'
      }, '*')

      showSuccessToast('行程确认成功,2秒后自动跳转至申请单列表页!');
      setTimeout(() => {
        showLoading.value = false
        goToOrderList()
      }, 2000);

    }).catch(err => {
      showLoading.value = false
    })
  })
  .catch(() => {
    // on cancel
  });
  
}

const goToOrderList = () => {
  router.push(`/mobile/applyList`)

}
</script>

<style lang="less" scoped>
@import url(./components/mobile.less);
</style>