<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Upload as hUpload,
  Input as hInput,
  Textarea as hTextarea,
  InputNumber as hInputNumber,
  RangePicker as hRangePicker,
  message,
  Row as hRow,
  Col as hCol,
} from 'ant-design-vue';
import { computed, ref, watch, onMounted, createVNode } from 'vue';
import type { Ref } from 'vue';
import dayjs, { Dayjs } from 'dayjs';
import { PlusOutlined, LoadingOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import type { UploadChangeParam } from 'ant-design-vue';
import {
  IDiscount,
  IMerchantType,
  HeaderConstant,
  IUserInfo,
  IUserListRequest,
  IChargeUser,
} from '@haierbusiness-front/common-libs';
import { fileApi } from '@haierbusiness-front/apis';
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false);

interface Props {
  show: boolean;
  typeList: Array<IMerchantType> | undefined;
  data: IDiscount | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: IDiscount = {
  id: null,
};

const typeList = computed(() => props.typeList);

const rules = {
  merName: [{ required: true, message: '请输入供应商名称！' }],
  name: [{ required: true, message: '请输入流程名称！' }],
  state: [{ required: true, message: '请选择状态！' }],
};

const discount: Ref<IDiscount> = ref(({ ...props.data } as IDiscount) || defaultData);

onMounted(() => {
  imageUrl.value = props.data?.icon ? props.data.icon : '';
  beginAndEnd.value = [dayjs(props.data?.startTime), dayjs(props.data?.endTime)];
  const currentUsers: string[] = [];
  const ids: string[] = [];
  if (discount.value.userList) {
    discount.value.userList.map((item, index) => {
      currentUsers.push(item.name + '/' + item.code);
      ids.push(item.code!);
    });
    userList.value = discount.value.userList;
  }
  users.value = currentUsers;
  userRef.value.setFirstData(ids);
});
watch(props, (newValue) => {
  discount.value = ({ ...newValue.data } as IDiscount) || defaultData;
});

const emit = defineEmits(['cancel', 'ok']);

const visible = computed(() => props.show);

const handleOk = () => {
  confirmLoading.value = true;
  discount.value.startTime = dayjs(discount.value.startTime).format('YYYY-MM-DD HH:mm:ss');
  discount.value.endTime = dayjs(discount.value.endTime).format('YYYY-MM-DD HH:mm:ss');
  discount.value.userList = userList.value;
  from.value
    .validate()
    .then(() => {
      emit('ok', discount.value, () => {
        confirmLoading.value = false;
      });
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};

const beginAndEnd = ref<[Dayjs, Dayjs]>();
watch(
  () => beginAndEnd.value,
  (n: any, o: any) => {
    if (n) {
      discount.value.startTime = n[0];
      discount.value.endTime = n[1];
    } else {
      discount.value.startTime = undefined;
      discount.value.endTime = undefined;
    }
  },
);

const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const fileList = ref<Array<any>>([]);
const loading = ref<boolean>(false);
const imageUrl = ref<string>('');

watch(imageUrl, (newValue) => {
  if (newValue) {
    // 有图片
    setTimeout(() => {
      const img = document.getElementById('img');
      const currentHeight = img?.height;
      const currentWidth = img?.width;
    }, 1000);
  }
});

const upload = async (options: any) => {
  loading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);
  const res = await fileApi.upload(formData);
  const file = {
    ...options.file,
    name: options.file.name,
    url: baseUrl + res.path,
  };
  loading.value = false;
  fileList.value = [...fileList.value, file];
  imageUrl.value = baseUrl + res.path;
  discount.value.icon = baseUrl + res.path;

  options.onProgress(100);
  options.onSuccess(res, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
};

const beforeUpload = (file: any) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('只允许上传图片类型');
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('Image must smaller than 2MB!');
  }
  return isJpgOrPng && isLt2M;
};

// 用户选择
const params = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});

const userList = ref<Array<IChargeUser>>([]);
const users = ref<string[]>([]);
const userRef = ref<any>(null);

const userNameChange = (userInfo: Array<any>) => {
  if (!userInfo.length) {
    userList.value = [];
  }
  const array: Array<string> = [];
  const newuserList: Array<IChargeUser> = [];
  userInfo.forEach((item: any, index: number) => {
    if (item.nickName && item.username) {
      // 从选人组件中选取
      if (!item.email) {
        hModal.confirm({
          title: `用户${item.nickName}/${item.username}未在账号中心中维护邮箱，折扣到期后无法收到提醒邮件！`,
          icon: createVNode(ExclamationCircleOutlined),
          onOk() {},
          onCancel() {},
        });
      }
      array.push(item.nickName + '/' + item.username);
      if (!userList.value.find((o) => o.code === item.username)) {
        userList.value.push({
          code: item.username,
          name: item.nickName,
          email: item.email,
          phone: item.phone,
        });
      }
    } else {
      // 操作回显中的数据
      const current = userList.value?.find((o) => o.code === item);
      if (current) {
        array.push(current.name + '/' + current.code);
      }
    }
  });
  users.value = array;
};

const autoSize = (type: number) => {
  // 1直连 2直签 3订餐 4会务 5机票 6火车票
  let size = '168 * 168';
  switch (type) {
    case 3:
      size = '168 * 168';
      break;
    case 2:
    case 5:
      size = '154 * 110';
      break;
    // 暂时不知道上传的尺寸
    default:
      break;
  }
  return size;
};

const rangePresets = ref([{ label: '未来一年', value: [dayjs(), dayjs().add(1, 'year')] }]);
</script>

<template>
  <h-modal
    v-model:open="visible"
    :title="discount.id ? '编辑折扣' : '新增折扣'"
    :width="600"
    @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
  >
    <h-form ref="from" :model="discount" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules">
      <h-form-item label="供应商名称" name="merName">
        <h-input v-model:value="discount.merName" />
      </h-form-item>
      <h-form-item label="供应商Code" name="merCode">
        <h-input v-model:value="discount.merCode" />
      </h-form-item>
      <h-form-item label="类别" name="merType">
        <h-select v-model:value="discount.merType">
          <h-select-option v-for="(item, index) in typeList" :key="index" :value="item.id">{{
            item.type
          }}</h-select-option>
        </h-select>
      </h-form-item>
      <h-form-item label="图标" name="icon" class="upload">
        <h-row>
          <h-col :span="24">
            <h-upload
              list-type="picture-card"
              class="avatar-uploader"
              :file-list="fileList"
              :max-count="1"
              :show-upload-list="false"
              name="avatar"
              :custom-request="upload"
              :before-upload="beforeUpload"
              :headers="{
                'Hb-Token': token,
              }"
            >
              <img v-show="imageUrl" :src="imageUrl" class="imgShow" id="img" />
              <div v-show="!imageUrl">
                <loading-outlined v-if="loading"></loading-outlined>
                <plus-outlined v-else></plus-outlined>
                <div>上传图片</div>
              </div>
            </h-upload>
          </h-col>
          <h-col :span="24" v-if="discount.merType && [2, 3, 5].includes(discount.merType)">
            建议上传图片的尺寸为<span class="important">{{ autoSize(discount.merType) }}</span> 大小不超过<span
              class="important"
              >2MB</span
            >
            格式为<span class="important">png/jpg/jpeg</span>的文件
          </h-col>
        </h-row>
      </h-form-item>
      <h-form-item label="负责人" name="userList">
        <user-select
          ref="userRef"
          :value="users"
          :multiple="true"
          placeholder="请选择负责人"
          :params="params"
          @change="(userInfo:  Array<IUserInfo>) =>  userNameChange(userInfo)"
        />
      </h-form-item>
      <h-form-item label="折扣值" name="discountValue">
        <h-input-number v-model:value="discount.discountValue" :min="0" :max="100" style="width: 100%" />
      </h-form-item>
      <h-form-item label="折扣简述" name="discountDesc">
        <h-input v-model:value="discount.discountDesc" />
      </h-form-item>
      <h-form-item label="折扣详情" name="details">
        <h-textarea v-model:value="discount.details" />
      </h-form-item>
      <h-form-item label="生效时间" name="beginAndEnd">
        <h-range-picker
          v-model:value="beginAndEnd"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
          allow-clear
          :presets="rangePresets"
        />
      </h-form-item>
    </h-form>
  </h-modal>
</template>

<style lang="less" scoped>
.important {
  color: red;
}

.imgShow {
  width: 100%;
}
</style>
<style>
.upload .ant-form-item-label > label::before {
  display: inline-block;
  margin-inline-end: 4px;
  color: #ff4d4f;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}
</style>
