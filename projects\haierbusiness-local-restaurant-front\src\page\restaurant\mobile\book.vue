<template>
  <div>
    <van-nav-bar :fixed="true" title="餐厅预约" left-arrow>
      <template #left>
        <van-icon name="arrow-left" color="#000" @click="goBack" />
      </template>
    </van-nav-bar>
    <van-form @submit="onSubmit" @failed="onFailed">
      <div class="reservation-content">
        <!-- 基本信息 -->

        <div class="title flex justify-content-between align-items-center">
          <div class="flex align-items-center justify-content-center">
            <span class="shu"></span>
            <div class="text">基本信息</div>
          </div>
        </div>

        <van-cell-group class="">
          <van-field autocomplete="off" readonly required input-align="right" error-message-align="right" label="订餐类型">
            <template #input>
              {{ paymentType == 'public' ? '因公' : '因私' }}
            </template>
          </van-field>

          <van-field autocomplete="off" readonly required input-align="right" error-message-align="right" label="支付方式">
            <template #input>
              {{ payTypeList.filter((item) => item.value == form.budget.paymentType)[0].label }}
            </template>
          </van-field>

          <van-field autocomplete="off" required readonly input-align="right" error-message-align="right"
            v-model.trim="form.owner.name" label="经办人" />

          <van-field autocomplete="off" input-align="right" error-message-align="right" v-model.trim="form.owner.mobile"
            :rules="[{ required: true, message: '请输入手机号' }, { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式错误！' }]"
            required type="tel" label="手机号" placeholder="请填写手机号" />

          <van-field autocomplete="off" input-align="right" error-message-align="right" v-model.trim="form.owner.email"
            label="邮箱" placeholder="请填写邮箱" />

          <van-field autocomplete="off" input-align="right" error-message-align="right" v-model.trim="form.owner.phone"
            type="tel" label="联系电话" placeholder="请填写联系电话" />

          <user-select-m palceholder="请选择业务申请人" label="业务申请人" :value="form.applicant.name"
            @chose="chosedPerson"></user-select-m>

          <van-field autocomplete="off" required readonly input-align="right" error-message-align="right" label="所属部门"
            v-model="form.applicant.departmentName" placeholder="请填写所属部门" />

          <van-field autocomplete="off" input-align="right" error-message-align="right"
            v-model.trim="form.applicant.mobile"
            :rules="[{ required: true, message: '请输入手机号' }, { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式错误！' }]"
            required type="tel" label="手机号" placeholder="请填写手机号" />

          <van-field autocomplete="off" input-align="right" error-message-align="right"
            v-model.trim="form.applicant.email" label="邮箱" placeholder="请填写邮箱" />

          <van-field autocomplete="off" input-align="right" error-message-align="right"
            v-model.trim="form.applicant.phone" type="tel" label="联系电话" placeholder="请填写联系电话" />

          <!-- <van-field autocomplete="off" :required="paymentType == 'public'"
            :rules="paymentType == 'public' ? [{ required: true, message: '请填写申请事由' }] : []"
            v-model.trim="form.applyCause" label-align="top" label="申请事由" placeholder="请填写申请事由" /> -->
        </van-cell-group>

        <!-- 餐厅预订信息 -->
        <div class="title flex justify-content-between align-items-center">
          <div class="flex align-items-center justify-content-center">
            <span class="shu"></span>
            <div class="text">餐厅预订信息</div>
          </div>
        </div>

        <van-cell-group>
          <van-field autocomplete="off" readonly input-align="right" error-message-align="right" label="餐厅名称">
            <template #input>
              <div>{{ detail?.fullname }}</div>
            </template>
          </van-field>

          <van-field autocomplete="off" readonly input-align="right" error-message-align="right" label="服务费率">
            <template #input>
              <div>{{ detail?.serviceFee }}</div>
            </template>
            <template #button>
              <div>%</div>
            </template>
          </van-field>

          <van-field autocomplete="off" required readonly is-link input-align="right" error-message-align="right"
            label="就餐日期" :rules="[{ required: true, message: '请选择就餐日期' }]" placeholder="请选择就餐日期"
            @click="showRqPicker = true" v-model="form.eatingDay" />
          <van-field autocomplete="off" required readonly is-link input-align="right" error-message-align="right"
            label="就餐时间" :rules="[{ required: true, message: '请选择就餐时间' }]" placeholder="请选择就餐时间"
            @click="showSjPicker = true" v-model="form.eatingTime1" />

          <van-field autocomplete="off" required readonly is-link input-align="right" error-message-align="right"
            @click="showClPicker = true" label="餐类" placeholder="请选择餐类" :rules="[{ required: true, message: '请选择餐类' }]"
            v-model="form.cateTypeText" />

          <van-field autocomplete="off" required v-model.number="form.consumptionStandard" type="number"
            label-width="40%" input-align="right" error-message-align="right" placeholder="输入就餐标准"
            :rules="[{ required: true, message: '请输入就餐标准' }]">
            <template #label>
              <span>
                <span>就餐标准 (¥)</span><br />
                <span class="font-size-8 color-eee">(总金额,含酒水)</span>
              </span>
            </template>
          </van-field>
        </van-cell-group>

        <!-- 人员信息 因私隐藏 -->
        <div class="title flex justify-content-between align-items-center">
          <div class="flex align-items-center justify-content-center">
            <span class="shu"></span>
            <div class="text">人员信息</div>
          </div>
        </div>

        <!-- 因公表单 -->

        <van-cell-group v-if="paymentType == 'public'">
          <!-- <van-field autocomplete="off" v-model="form.treatInfo.guestCompany" required input-align="right"
            error-message-align="right" label="来宾单位" placeholder="请输入来宾单位"
            :rules="[{ required: true, message: '请输入来宾单位' }]" />

          <van-field autocomplete="off" v-model="form.treatInfo.mainGuestNames" required input-align="right"
            error-message-align="right" label="来宾姓名" placeholder="请输入来宾姓名"
            :rules="[{ required: true, message: '请输入来宾姓名' }]" />

          <van-field autocomplete="off" v-model="form.treatInfo.mainGuestPosition" required input-align="right"
            error-message-align="right" label="来宾职务" placeholder="请输入来宾职务"
            :rules="[{ required: true, message: '请输入来宾职务' }]" /> -->

          <van-field autocomplete="off" v-model.number="form.treatInfo.guestCount" required input-align="right"
            error-message-align="right" type="digit" label="来宾人数" placeholder="请输入来宾人数"
            :rules="[{ required: true, message: '请输入来宾人数' }]" />

          <van-field autocomplete="off" v-model="form.treatInfo.accompanyLeader" required label-align="top"
            label="我方主要陪同领导" placeholder="请输入领导姓名" :rules="[{ required: true, message: '请输入领导姓名' }]" />
          <van-field autocomplete="off" v-model.number="form.treatInfo.accompanyCount" required input-align="right"
            error-message-align="right" type="digit" label="陪同人数" placeholder="请输入陪同人数"
            :rules="[{ required: true, message: '请输入陪同人数' }]" />

          <van-field autocomplete="off" redaonly input-align="right" error-message-align="right" type="digit"
            label="合计就餐人数" placeholder="请输入合计就餐人数">
            <template #input>
              <div>{{ (form.treatInfo.accompanyCount || 0) * 1 + (form.treatInfo.guestCount || 0) * 1 }}</div>
            </template>
          </van-field>

          <van-field autocomplete="off" v-model="form.remark" required label-align="top"
            :rules="[{ required: true, message: '请输入禁忌和补充说明' }]" label="禁忌和补充说明" placeholder="请输入" />

          <van-field autocomplete="off" label-width="120px" input-align="right" error-message-align="right" type="digit"
            label="工作餐提取人数" placeholder="请输入工作餐提取人数" :rules="[{ required: true, message: '请输入工作餐提取人数' }]" required
            v-model.number="form.workingLunchCount" />

          <van-field autocomplete="off" label-width="160px" required redaonly input-align="right"
            error-message-align="right" placeholder="工作餐金额 (¥)">
            <template #label>
              <span>
                <span>工作餐金额 (¥)</span>
                <br />
                <span class="font-size-8 color-red">工作餐金额=工作餐提取人数*50</span>
              </span>
            </template>
            <template #input>
              <span>
                {{ (form.workingLunchCount || 0) * 50 }}
              </span>
            </template>
          </van-field>

          <van-field autocomplete="off" v-model="form.treatInfo.signerName" required input-align="right"
            error-message-align="right" label="签单人" placeholder="请输入签单人"
            :rules="[{ required: true, message: '请输入签单人' }]" />

          <van-field autocomplete="off" v-model="form.treatInfo.signerMobile" required type="tel" input-align="right"
            :rules="[{ required: true, message: '请输入手机号' }, { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式错误！' }]"
            error-message-align="right" label="手机号" placeholder="请输入签单人手机号" />
        </van-cell-group>

        <!-- 因私表单 -->
        <van-cell-group v-else>
          <!-- <van-field autocomplete="off" v-model="form.treatInfo.mainGuestNames" required input-align="right"
            error-message-align="right" label="人员信息" :rules="[{ required: true, message: '请输入人员信息' }]"
            placeholder="请输入人员信息" /> -->

          <van-field autocomplete="off" input-align="right" required error-message-align="right" type="digit"
            label="合计就餐人数" placeholder="请输入合计就餐人数" :rules="[{ required: true, message: '请输入合计就餐人数' }]"
            v-model="form.treatInfo.guestCount">
          </van-field>

          <van-field autocomplete="off" v-model="form.remark" :rules="[{ required: true, message: '请输入禁忌和补充说明' }]"
            required label-align="top" label="禁忌和补充说明" placeholder="请输入" />

          <van-field autocomplete="off" v-model="form.treatInfo.signerName" required input-align="right"
            error-message-align="right" label="联系人" placeholder="请输入联系人"
            :rules="[{ required: true, message: '请输入联系人' }]" />

          <van-field autocomplete="off" v-model="form.treatInfo.signerMobile" type="tel" input-align="right"
            error-message-align="right" required label="手机号" placeholder="请输入联系人手机号"
            :rules="[{ required: true, message: '请输入手机号' }, { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式错误！' }]" />
        </van-cell-group>

        <!-- 服务预约 -->
        <div class="title flex justify-content-between align-items-center">
          <div class="flex align-items-center justify-content-center">
            <span class="shu"></span>
            <div class="text">服务预约</div>
          </div>
        </div>

        <van-cell-group>
          <van-field autocomplete="off" required redaonly is-link input-align="right" error-message-align="right"
            label="预留车位" placeholder="请选择是否需要预留" @click="showCwPicker = true">
            <template #input>
              {{ columnsCw.filter((item) => item.value == form.treatInfo.needParking)[0]?.text }}
            </template>
          </van-field>

          <van-field autocomplete="off" :readonly="!form.treatInfo.needParking" :required="form.treatInfo.needParking"
            :rules="[{ required: form.treatInfo.needParking, message: '请输入车牌号（多个车牌号“，”隔开）' }]"
            v-model="form.treatInfo.vehicleNo" label-align="top" label="车牌号" placeholder="请输入车牌号（多个车牌号“，”隔开）" />

          <!-- <van-field autocomplete="off" required redaonly is-link input-align="right" error-message-align="right"
            label="需要座牌" placeholder="请选择是否需要座牌" @click="showZpPicker = true">
            <template #input>
              {{ columnsZp.filter((item) => item.value == form.treatInfo.needSeatCard)[0]?.text }}
            </template>
          </van-field>

          <van-field autocomplete="off" :readonly="!form.treatInfo.needSeatCard"
            :disabled="!form.treatInfo.needSeatCard" :required="form.treatInfo.needSeatCard"
            :rules="[{ required: form.treatInfo.needSeatCard, message: '请上传座次图' }]" label="座次图" label-align="left"
            input-align="right" error-message-align="right">
            <template #label>
              <span>
                <span>座次图</span>
                <br />
                <span class="font-size-8 color-red">(仅支持word上传)</span>
              </span>
            </template>
            <template #input>
              <van-uploader :after-read="afterRead" :preview-image="false" v-model="form.treatInfo.seatOrderFileList"
                :max-count="1" :disabled="!form.treatInfo.needSeatCard" accept=".doc,.docx">
                <van-button size="small" icon="plus" type="primary">上传文件</van-button>
                <div class="color-eee font-size-10">请上传word格式文件</div>
              </van-uploader>
              <div class="width100" v-if="form.treatInfo.seatOrderId">
                <span class="mr-10" style="color: #1482ff;">{{ form.treatInfo.seatOrderFileList[0].name }}</span>
                <van-icon @click.stop="delFile" class="ml-20" name="cross" style="color: red" />
              </div>
            </template>
          </van-field>
 -->

        </van-cell-group>

        <van-cell-group>

          <van-field autocomplete="off" readonly :rules="[{ required: true, message: '请阅读完成订餐须知和效果承诺后再勾选' }]" required
            input-align="left" error-message-align="left">
            <template #input>
              <van-checkbox v-model="form.checkedInfo">
                <div style="font-size: 12px;">
                  我已经阅读并同意 <span style="color: #1482ff;" @click.stop="dcxzDialog = true">《订餐须知》</span>
                  <span> 、 </span>
                  <span style="color: #1482ff;" @click.stop="LoadYszcPdf">《隐私政策》</span>
                  <span v-if="paymentType == 'public'"> 和 </span>
                  <span style="color: #1482ff;" @click.stop="xgDialog = true" v-if="paymentType == 'public'">《效果承诺》</span>
                </div>
              </van-checkbox>
            </template>
          </van-field>
        </van-cell-group>
      </div>

      <van-sticky position="bottom">

        <van-row class="bottom-btn-box" justify="space-between">
          <van-col :span="14" class="flex align-items-center">
            <div class="money">
              ¥ {{ budgetAmount() / 100 }}
            </div>

            <van-dropdown-menu ref="menuRef" direction="up">
              <van-dropdown-item title="查看明细" ref="itemRef">
                <van-cell-group>
                  <van-cell>
                    <template #title>
                      <div class="flex justify-content-center">
                        <span class="weight600 font-size-14 mr-5">费用明细</span>
                        <span class="color-000 font-size-10">(总金额,含酒水)</span>
                      </div>
                    </template>
                  </van-cell>
                  <van-cell>
                    <template #title>
                      <div>
                        <span class="weight600 mr-5">就餐标准</span>
                        <span class="color-main font-size-8">(总金额,含酒水)</span>
                      </div>
                    </template>

                    <template #value>
                      <span class="weight600 color-000">¥{{ priceToYuan(budgetAmount() * (1 - detail?.serviceFee / 100))
                        || 0 }}</span>
                    </template>
                  </van-cell>

                  <van-cell>
                    <template #title>
                      <div>
                        <span class="weight600 mr-5">服务费</span>
                        <span class="color-main font-size-8">(服务费率{{ detail?.serviceFee || 0 }}%)</span>
                      </div>
                    </template>

                    <template #value>
                      <span class="weight600 color-000">¥{{ priceToYuan((budgetAmount() * detail?.serviceFee) / 100)
                        }}</span>
                    </template>
                  </van-cell>

                  <van-cell>
                    <template #title>
                      <div>
                        <span class="weight600 mr-5">工作餐金额</span>
                        <span class="color-main font-size-8">(¥50/人)</span>
                      </div>
                    </template>

                    <template #value>
                      <span class="weight600 color-000">¥{{ (form.workingLunchCount || 0) * 50 }}</span>
                    </template>
                  </van-cell>
                </van-cell-group>
              </van-dropdown-item>
            </van-dropdown-menu>
          </van-col>
          <van-col :span="10">
            <van-button class="btn left width100" :loading="btnLoading" type="primary" native-type="submit">提交</van-button>
          </van-col>
        </van-row>
      </van-sticky>
    </van-form>

    <!-- 酒店餐类选择 -->
    <van-popup v-model:show="showClPicker" round position="bottom">
      <van-picker title="餐类选择" :columns="columnsCl" :columns-field-names="{ text: 'name', value: 'id' }"
        @cancel="showClPicker = false" @confirm="onConfirmCl" />
    </van-popup>

    <!-- 就餐日期选择 -->
    <van-popup v-model:show="showRqPicker" round position="bottom">
      <van-date-picker v-model="currentRq" title="选择日期" value-format="YYYY-MM-DD" :min-date="minDate"
        :max-date="maxDate" @confirm="confirmRq" @cancel="showRqPicker = false" />
    </van-popup>

    <!-- 就餐时间选择 -->
    <van-popup v-model:show="showSjPicker" round position="bottom">
      <van-time-picker v-model="currentSj" title="选择时间" @confirm="confirmSj" @cancel="showSjPicker = false" />
    </van-popup>

    <!-- 是否预留选择 -->
    <van-popup v-model:show="showCwPicker" round position="bottom">
      <van-picker title="选择预留车位" :columns="columnsCw" @confirm="onConfirmCw" @cancel="showCwPicker = false" />
    </van-popup>

    <!-- 是否需要座牌 -->
    <van-popup v-model:show="showZpPicker" round position="bottom">
      <van-picker title="是否需要座牌" :columns="columnsZp" @confirm="onConfirmZp" @cancel="showZpPicker = false" />
    </van-popup>

    <!-- 确认弹窗 -->
    <van-dialog v-model:show="showSubmitDialog" title="确定要提交订餐预订单?" show-cancel-button @cancel="btnLoading = false" @confirm="save">
      <div class="submit-dialog-content" style="flex-direction: column;">
        <div class="mb-10">
          订单审批通过之后,请及时与海尔国旅88931999-2-1进行确认和预订!
        </div>
        <div>
          电话确认后,请及时关注预订成功短信
        </div>
      </div>

    </van-dialog>

    <!-- 隐私政策 -->
    <van-popup :lazy-render="false" v-model:show="showYszcPopup" position="bottom" :style="{ height: '100%' }">
      <van-nav-bar
        title="隐私政策"
        left-arrow
        fixed
        :z-index="1000"
      >
        <template #left>
          <van-icon name="arrow-left" color="#000" @click="showYszcPopup=false" size="20" />
        </template>
      </van-nav-bar>
      <div ref="yszcpdfRef" style="height:100%;"></div> 
    </van-popup>

    <!-- 订餐须知弹窗 -->
    <van-dialog v-model:show="dcxzDialog" title="订餐须知">
      <div style="padding: 20px; height: 400px; overflow-y: auto;">
        <p style="font-size: 12px; ">结合我集团纪委颁布的《关于狠刹公款吃喝严禁铺张浪费的通知》，为推进集团商务用餐市场效果落地，实现自挣自花，现对商务订餐系统的审批流程升级：</p>
        <div style="font-size: 12px;  margin-bottom: 10px; font-weight: bold;">
          <br />一、订餐预订条件：
          <br />
        </div>
        <div style="font-size: 10px;">
          1.必须有月度可用预算；
          <br />2.必须明确申请事由、业务目标、市场效果预算，确认招待必要性；
          <br />3.必须符合集团宴请标准，不超标，不随意提高标准，不随意增加人数，超预算部分费用自付；
          <br />4.必须系统中审批通过；
          <br />5.符合自挣自花原则；
          <br />6.首选集团内酒店资源
        </div>

        <div style="font-size: 12px;  margin-bottom: 10px; font-weight: bold;">
          <br />二、 直线经理、二线经理项目及责任：
          <br />
        </div>
        <div style="font-size: 10px;">
          1.审核业务的市场效果及目标；
          <br />2.审核业务的标准是否超标；
          <br />3.审核业务的必要性、真实性、合规性；
          <br />4.如有违规行为一经查出或经举报查实的,全部损失由责任人承担，同时参照《员工行为规范》一级违规处理。
        </div>

        <div style="font-size: 12px;  margin-bottom: 10px; font-weight: bold;">
          <br />三、宴请招待预算控制标准：
          <br />
        </div>
        <table border="1" cellpadding="0" cellspacing="0" class="tb"
          style="table-layout: fixed; font-size: 8px; text-align: center; line-height: 22px;">
          <tbody>
            <tr>
              <td width="20">序号</td>
              <td width="60">来宾级别</td>
              <td width="50">参加领导</td>
              <td width="50">菜金费用标准</td>
              <td width="100">地点选择</td>
              <td width="300">备注</td>
            </tr>
            <tr>
              <td>1</td>
              <td>集团级商务宴请</td>
              <td>D-A级（副总裁以上）</td>
              <td>根据来宾按需求确定</td>
              <td>可按来宾要求自行确定或选择协议酒店</td>
              <td rowspan="4" style="text-align: left">
                1、业务宴请订单必须根据菜金标准、酒水标准等确定费用总额上限；
                <br />2、用餐总费用不得超过预算总额，超标部分由宴请人自行现付买单；
                <br />3、酒水要求：建议业务宴请使用的酒水（除啤酒外），统一从创牌商务餐厅采购。电话：88936033；
                <br />4、如需为司机提取工作餐，必须在预定时备注预提工作餐客人名单，标准为50元/位；
                <br />5、对接待对象在5人以内的,陪餐人数不得超过2人;接待对象在10人以内的,陪餐人数不得超过3人;超过10人的,不得超过接待对象人数的三分之一负责;
                <br />6、行为规范要求：个人在用餐过程中必须以海尔人的标准规范自己行为，维护集团形象，因个人行为不当造成不良影响按照一级违规处理；
                <br />7、投入产出要求：商务宴请需要有投入产出分析，必须以实际就餐人数提交预算，不得私自变更人数或提高预算；
                <br />8、业务合规性要求：宴请业务需求必须与业务目标相同，并保证宴请效果。
                <br />本次餐标调整从2015年12月01日开始执行。
              </td>
            </tr>
            <tr>
              <td>2</td>
              <td>领域级商务宴请(大单及以上)</td>
              <td>10-11级</td>
              <td>不得超过260元/位上限</td>
              <td>选择协议内酒店资源</td>
            </tr>
            <tr>
              <td>3</td>
              <td>平台级商务宴请(中大单及大单以上)</td>
              <td>8-10级</td>
              <td>不得超过200元/位上限</td>
              <td>选择协议内酒店资源</td>
            </tr>
            <tr>
              <td>4</td>
              <td>普通商务宴请(中单或中小单以上)</td>
              <td>7级~10级</td>
              <td>不得超过150元/位上限</td>
              <td>选择协议内酒店资源</td>
            </tr>
          </tbody>
        </table>

        <div style="font-size: 12px; margin-bottom: 10px; font-weight: bold;">
          <br />四、关于相关接口人责任：
          <br />
        </div>
        <table border="1" cellspacing="0" cellpadding="5" style="text-align: center; font-size: 8px;">
          <tbody>
            <tr>
              <td width="52">序号</td>
              <td width="170">角色</td>
              <td width="570">责任承诺</td>
            </tr>
            <tr>
              <td>1</td>
              <td>业务申请人</td>
              <td align="left">
                1.对业务必要性、真实性、合规性负责；
                <br />2.对符合集团政策, 不超标，不随意提高标准，不随意增加人数负责；
                <br />3.对接待对象在5人以内的，陪餐人数不得超过2人；接待对象在10人以内的，陪餐人数不得超过3人；超过10人的，不得超过接待对象人数的三分之一负责；
                <br />4.对超预算部分费用自付；
                <br />5.自挣自花负责；
                <br />6.如有违规行为一经查出或经举报查实的,全部损失由责任人承担，同时参照《员工行为规范》一级违规处理。
              </td>
            </tr>
            <tr>
              <td>2</td>
              <td>业务闸口人及审批人</td>
              <td align="left">
                1.对业务必要性、真实性、合规性负责；
                <br />2.对符合集团政策, 不超标，不随意提高标准，不随意增加人数负责；
                <br />3.对接待对象在5人以内的，陪餐人数不得超过2人；接待对象在10人以内的，陪餐人数不得超过3人；超过10人的，不得超过接待对象人数的三分之一负责；
                <br />4.对超预算部分费用自付；
                <br />5.对自挣自花负责，确认招待必要性；
                <br />6.如有违规行为一经查出或经举报查实的,全部损失由责任人承担，同时参照《员工行为规范》一级违规处理。
              </td>
            </tr>
            <tr>
              <td>3</td>
              <td>签单人</td>
              <td align="left">
                1.对账单准确性、真实性负责；
                <br />2.对违规提取工作餐负责；
                <br />3.对超预算部分费用自付；
                <br />4.如有违规行为一经查出或经举报查实的,全部损失由责任人承担，同时参照《员工行为规范》一级违规处理。
              </td>
            </tr>
          </tbody>
        </table>

      </div>


      <template #footer>
        <van-row class="width100 " style="padding: 10px 0;" justify="center">

          <van-button size="small" type="primary" @click="dcxzSubmit">
            <span>已阅读订餐须知</span>

          </van-button>
        </van-row>
      </template>

    </van-dialog>


    <!-- 效果承诺 -->
    <van-dialog v-model:show="xgDialog" title="效果承诺">
      <div style="padding:0 20px; 20px">
        <div class="text-promise text-emphasize" style="font-size: 12px;">
          <p style="font-size: 12px; font-weight: bold;">有效签单人效果承诺：</p>
          <p style="margin-left:20px;">我承诺：</p>
          <div style="margin-left:20px;">
            <p>1.对账单准确性、真实性负责；</p>
            <p>2.对违规提取工作餐负责；</p>
            <p>3.对超预算部分费用自付；</p>
            <p>4.如有违规行为一经查出或经举报查实的,全部损失由责任人承担，同时参照《员工行为规范》一级违规处理。</p>
          </div>
        </div>

        <div class="text-promise text-emphasize" style="font-size: 12px;">
          <p style="font-size: 12px; font-weight: bold;">业务申请人效果承诺：</p>
          <p style="margin-left:20px;">我承诺：</p>
          <div style="margin-left:20px;">
            <p>1.对业务必要性、真实性、合规性负责；</p>
            <p>2.对符合集团政策, 不超标，不随意提高标准，不随意增加人数负责；</p>
            <p>3.对接待对象在5人以内的，陪餐人数不得超过2人；接待对象在10人以内的，陪餐人数不得超过3人；超过10人的，不得超过接待对象人数的三分之一负责；</p>
            <p>4.对超预算部分费用自付；</p>
            <p>5.自挣自花负责；</p>
            <p>6.如有违规行为一经查出或经举报查实的,全部损失由责任人承担，同时参照《员工行为规范》一级违规处理。</p>
          </div>
        </div>
      </div>


      <template #footer>
        <van-row class="width100 " style="padding: 10px 0;" justify="center">

          <van-button size="small" type="primary" @click="xgSubmit">
            <span>已阅读并接受承诺</span>

          </van-button>
        </van-row>
      </template>

    </van-dialog>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch, reactive, computed } from 'vue';
import type { Ref } from 'vue';
import aMap from '@/components/aMap/index.vue';
import { IUserListRequest, IUserInfo, RHotel, RCate, RpayType, HeaderConstant } from '@haierbusiness-front/common-libs';
import { userApi } from '@haierbusiness-front/apis';

import { restaurantApi, fileApi } from '@haierbusiness-front/apis';
import { RHotelParams } from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute, getEnumOptions } from '@haierbusiness-front/utils';
import { Item } from 'ant-design-vue/es/menu';
import UserSelectM from './userSelectM.vue';
import ymdhms from './ymdhms.vue';
import dayjs from 'dayjs';
import { showConfirmDialog, showSuccessToast } from 'vant';
import { cloneDeep, debounce, values } from 'lodash';
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil'
// import { useAppStore } from "@/store"

import Pdfh5 from "pdfh5";
import "pdfh5/css/pdfh5.css";


const store = applicationStore();

const { loginUser } = storeToRefs(store);

const router = getCurrentRouter();

const route = ref(getCurrentRoute());


const hotelId = route.value?.query?.hotelId;
// 支付方式 private 自付
const paymentType = route.value?.query?.type;

// 支付方式
const payTypeList = computed(() => {
  return getEnumOptions(RpayType, true);
});


// 效果承诺弹窗
const xgDialog = ref<boolean>(false)

const xgSubmit = () => {
  xgDialog.value = false
}

// 订餐须知弹窗
const dcxzDialog = ref<boolean>(false)
// 订餐隐私政策
const showYszcPopup = ref<boolean>(false)
const yszcpdfRef = ref(null);
const yszcpdf = new URL('@/assets/yszc.pdf', import.meta.url).href
const LoadYszcPdf = () => {
  const pdfh5Yszc = new Pdfh5(yszcpdfRef.value, {
    pdfurl: yszcpdf,
  });
 
  pdfh5Yszc.on("complete", (status, msg, time) => { 
    showYszcPopup.value=true
  });
};


const dcxzSubmit = () => {
  dcxzDialog.value = false
}

// 餐类选择弹窗
const showClPicker = ref<boolean>(false);
const columnsCl = ref<Array<RCate>>();
const onConfirmCl = ({ selectedOptions }: any) => {
  showClPicker.value = false;
  form.value.cateTypeId = selectedOptions[0].id;
  form.value.cateTypeText = selectedOptions[0].name;
};

// 车位预留选择 是否预留车位，0：否；1：是；
const showCwPicker = ref<boolean>(false);
const columnsCw = [
  {
    text: '预留',
    value: 1,
  },
  {
    text: '不预留',
    value: 0,
  },
];
//预算金额
const budgetAmount = () => {
  if (!detail.value) {
    return 0;
  }
  //预算金额 = （餐位费 × 就餐总人数 + 就餐标准 + 工作餐金额）×（1 + 餐厅服务费率）
  //2020-05-20 逻辑修改 为
  //预算金额 = （餐位费 × 就餐总人数 + 就餐标准 + 工作餐金额）
  return (
    detail.value.consumptionSeat * (form.value.treatInfo.accompanyCount || 0 + form.value.treatInfo.guestCount || 0) +
    (form.value.consumptionStandard || 0) * 100 +
    (form.value.workingLunchCount || 0) * 5000
  );
};

const priceToYuan = (value: number) => {
  if (!value) return 0;
  let yuan: number = value / 100;
  yuan = yuan.toFixed(2) * 1;
  return yuan;
};


// 文件上传
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const fileList = ref([]);

const businessList = import.meta.env.VITE_BUSINESSTRAVEL_URL;

const afterRead = (options: any) => {
  const formData = new FormData();
  formData.append('file', options.file);
  restaurantApi
    .upload(formData)
    .then((it) => {
      form.value.treatInfo.seatOrderId = it.data;

      form.value.treatInfo.seatOrderFileList = [
        {
          uid: '1',
          name: options.file.name,
          status: 'done',
          // url: download(it.data),
          url: `${businessList}/api/common/v1/file/download/${it.data}`,
        },
      ];
    })
    .finally(() => {
      // uploadLoading.value = false;
    });
};

const delFile = () => {
  form.value.treatInfo.seatOrderId = '';
  form.value.treatInfo.seatOrderFileList = [];
};

const onConfirmCw = ({ selectedValues }: any) => {
  form.value.treatInfo.needParking = selectedValues[0];
  showCwPicker.value = false;
};

// 是否需要座牌
const showZpPicker = ref<boolean>(false);
const columnsZp = [
  {
    text: '需要',
    value: 1,
  },
  {
    text: '不需要',
    value: 0,
  },
];

const onConfirmZp = ({ selectedValues }: any) => {
  form.value.treatInfo.needSeatCard = selectedValues[0];
  showZpPicker.value = false;
};

// 日期选择
// 选择器默认范围（当前时间）
const minDate = new Date();
const maxDate = new Date(2025, 10, 1);

// dayjs().add(1, 'day').format('YYYY-MM-DD')
const minTime = `${dayjs().hour()}:${dayjs().minute()}:${dayjs().second()}`

const currentRq = ref<Array<string>>([]);
const currentSj = ref<Array<string>>([]);

const showRqPicker = ref<boolean>(false);
const showSjPicker = ref<boolean>(false);
const confirmRq = ({ selectedValues }: any) => {
  form.value.eatingDay = selectedValues.join('-');
  showRqPicker.value = false;
};

const confirmSj = ({ selectedValues }: any) => {
  form.value.eatingTime1 = selectedValues.join(':');
  showSjPicker.value = false;
};

const goBack = () => {
  router.back(-1);
};
// 获取餐厅信息
const detail = ref<RHotel>();

const getDetail = (hotelId: string) => {
  const params = {
    hotelId,
  };

  restaurantApi.hotelSingle(params).then((res) => {
    detail.value = res.data;

    columnsCl.value = res.data.cateType;
    form.value.providerCode = res.data.providerCode;
    form.value.consumptionSeat = res.data.consumptionSeat * 1;
  });
};

watch(
  () => hotelId,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);

// 选择业务申请人
const chosedPerson = (item: IUserInfo) => {
  form.value.applicant.name = item.nickName;
  form.value.applicant.email = item.email;
  form.value.applicant.mobile = item.phone;
  form.value.applicant.phone = item.phone;
  form.value.applicant.orderCode = item.username;
  form.value.applicant.username = item.username;
  form.value.applicant.departmentName = item.departmentName || item.enterpriseName;
};

const form = ref<RHotel>({
  //业务申请人
  applicant: {
    email: loginUser.value?.email, //联系人邮箱
    mobile: loginUser.value?.phone, //联系人电话
    name: loginUser.value?.nickName, //联系人名称,
    username: loginUser.value?.username, //联系人工号
    departmentName: loginUser.value?.departmentName || loginUser.value?.enterpriseName,
    orderCode: loginUser.value?.username, //联系人工号
    phone: loginUser.value?.phone, //联系人电话
  },
  applicantId: loginUser.value?.username, //业务申请人工号
  applyCause: '/', //申请事由
  businessAim: '', //业务目标
  //预算信息
  budget: {
    paymentType: paymentType == 'public' ? '1' : '2',
    paymentCard: '', //酒店储值卡
  },
  payType: paymentType == 'public' ? 1 : 2,
  cateTypeId: 0, //餐类ID
  cateTypeText: '',
  consumptionSeat: 0, //餐位费；分； 用于计算预算金额
  consumptionStandard: undefined, //就餐标准 含酒水。PS：所有人加起来的
  eatingTime: '', //就餐时间
  eatingDay: '', //就餐日期
  hotelId: hotelId, //酒店ID
  //为选择内部酒店原因
  outerHotel: {
    reason: '1',
    remark: '',
  },
  //通知信息
  notifying: {
    email: '',
    needEmail: 0, // 0, //是否邮件通知 0：否；1：是
    mobile: '',
    needCall: 0, //0 //是否电话通知 0：否；1：是
    needSms: 0, // 0, //是否短信通知 0：否；1：是
    orderCode: '',
    phone: '',
  },
  orderCode: '',
  //经办人
  owner: {
    email: loginUser.value?.email, //联系人邮箱
    mobile: loginUser.value?.phone, //联系人电话
    name: loginUser.value?.nickName, //联系人名称,
    username: loginUser.value?.username, //联系人工号
    departmentName: loginUser.value?.departmentName || loginUser.value?.enterpriseName,
    orderCode: loginUser.value?.username, //联系人工号
    phone: loginUser.value?.phone, //联系人电话
  },
  ownerId: loginUser.value?.username, //经办人工号
  providerCode: detail.value?.providerCode, //供应商编码
  remark: '', //禁忌和补充说明
  serviceFee: 0, //服务费率
  //订单招待信息
  treatInfo: {
    accompanyCount: undefined, //陪同人数
    accompanyLeader: '', //我方主要陪同领导
    guestCompany: '/', //来宾单位
    guestCount: undefined, //来宾人数
    mainGuestNames: '/', //主宾姓名，可多个人
    mainGuestPosition: '/', //主宾职务
    needParking: 0, // 0, //是否预留车位，0：否；1：是；
    needSeatCard: 0, // 0, //需要座牌
    orderCode: '',
    seatOrderId: 0, //upload_file ID，座次文件ID
    seatOrderFileLoading: false, //
    seatOrderFileList: [], //
    signerMobile: '', //签单人手机
    signerName: '', //签单人姓名
    vehicleNo: '', //车牌号，可多个
  },
  type: 1, //订单类型 1:订餐 , 2:特产
  workingLunchCount: undefined, //工作餐提取人数
  workingLunchFee: 0, //工作餐金额 工作餐提取人数 × 50
  platType: '1', // 客户端类型 0：PC 1：H5
  businessFlag: paymentType == 'public' ? '0' : '1', // 因公因私 0：因公 1：因私
});
const showSubmitDialog = ref<boolean>(false);
const onSubmit = (values) => {
  showSubmitDialog.value = true;
  btnLoading.value = true
};

// 页面滚动到表单验证失败的地方
const scrollToErrorField = () => {
  const firstErrorField = document.querySelector('.van-field__error-message') // 使用类名选择第一个错误元素
  if (firstErrorField) {
    firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
}
const onFailed = (values) => {
  console.log('failed--------', values)
  scrollToErrorField()
}
const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false)

// const appStore = useAppStore()

const payUrl = import.meta.env.VITE_BUSINESS_PAY_URL;

const btnLoading = ref<boolean>(false);
const save = () => {
  form.value.eatingTime = form.value.eatingDay + ' ' + form.value.eatingTime1 + ':00';
  const params = cloneDeep(form.value)
  params.consumptionStandard = params.consumptionStandard * 100
  params.workingLunchFee = (params.workingLunchCount || 0) * 50 * 100
  params.serviceFee = detail.value.serviceFee * 1
  params.orderSource = '6'
  restaurantApi.save(params).then((res) => {
    // showSuccessToast('预订成功!')
    // 跳转中台支付
    const payParam = {
      orderNo: res.data,
    }
    if (paymentType != 'public') {
      router.replace({ path: `/restaurant/order/orderDetail`,
        query:{
          'orderCode': res.data
        }
       })
      return
    }
    restaurantApi.prePay(payParam).then(url => {
      const href = url.data
      console.log(99999, href)
      // 如果因公跳转支付中台
      window.location.replace(href)
      // const a = document.createElement("a");
      // a.setAttribute("href", href);
      // a.setAttribute("style", "display:none");
      // document.body.appendChild(a);
      // a.click();
      // a.parentNode.removeChild(a);
    })

    // router.push({ path: '/restaurant/orderList'  })
  });
};
onMounted(() => { });
</script>


<style lang='less' scoped>
@import url(./common.less);

.reservation-content {
  margin-top: 36px;
  background: #f8f8f8;
}

.submit-dialog-content {
  padding: 20px;
  color: red;
  display: flex;
}

:deep(.van-field__label--required:before) {
  position: absolute;
  left: 6px;
}
</style>