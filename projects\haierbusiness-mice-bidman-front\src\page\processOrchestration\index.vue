<!-- 流程编排 -->
<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  RangePicker as hRangePicker,
  Tag as hTag,
  Input as hInput,
  Select as hSelect,
  Pagination as hPagination,
  Spin as hSpin,
  Tooltip as hTooltip,
  Dropdown as hDropdown,
  <PERSON>u as hMenu,
  MenuItem as hMenuItem,
  Modal as hModal,
  message,
} from 'ant-design-vue';
import { SelectOption as hSelectOption } from 'ant-design-vue/lib/select';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { PlusOutlined, SearchOutlined, EllipsisOutlined, UpOutlined, DownOutlined } from '@ant-design/icons-vue';
import { processOrchestrationApi } from '@haierbusiness-front/apis';
import { priceInquiryApi } from '@haierbusiness-front/apis';
import {
  IProcessOrchestrationFilter,
  IProcessOrchestration,
  IPriceInquiryFilter,
  ProcessOrchestrationModelStatusEnum,
  ProcessOrchestrationServiceTypeEnum,
  ExtendedProcessOrchestrationFilter,
  ProcessCardItem
} from '@haierbusiness-front/common-libs';
import { computed, ref, watch, onMounted, nextTick } from 'vue';
import { usePagination } from 'vue-request';
import { useDelete } from '@haierbusiness-front/composables'
import router from '../../router'

const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router
  // 加载酒店列表
  // fetchHotelList()
  // 加载流程列表数据
  listApiRun({
    pageNum: 1,
    pageSize: 10,
  })
})

// 定义扩展的搜索参数
const searchParam = ref<ExtendedProcessOrchestrationFilter>({})

// 业务类型显示值
const selectedBusinessType = ref<any>(undefined)

// 监听业务类型选择变化
watch(() => selectedBusinessType.value, (val) => {
  if (val) {
    searchParam.value.item = val.value
    console.log('业务类型已选择:', val.value)
  } else {
    searchParam.value.item = undefined
  }
})

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(processOrchestrationApi.list);

const reset = () => {
  searchParam.value = {}
  beginAndEnd.value = null
  createTimeRange.value = null
  updateTimeRange.value = null
  selectedBusinessType.value = undefined

  // 重置后立即查询
  nextTick(() => {
    // 重置后立即查询
    const params = {
      pageNum: 1,
      pageSize: data.value?.pageSize || 10
    };
    console.log('重置后调用接口参数:', params);
    listApiRun(params);
  });
}

// 酒店相关
// const hotelLoading = ref(false)
// const hotelList = ref<any[]>([])

// 获取酒店列表
// const fetchHotelList = async (keyword = '') => {
//   hotelLoading.value = true
//   try {
//     const params: any = {
//       pageSize: 999, // 获取所有数据
//       pageNum: 1,
//       hotelName: keyword
//     }
//     const result: any = await priceInquiryApi.listHotel(params)
//     if (result && Array.isArray(result.records)) {
//       hotelList.value = result.records
//     } else {
//       // 如果返回的不是标准分页格式，尝试直接使用结果
//       hotelList.value = Array.isArray(result) ? result : []
//     }
//   } catch (error) {
//     console.error('获取酒店列表失败', error)
//   } finally {
//     hotelLoading.value = false
//   }
// }

const dataSource = computed<ProcessCardItem[]>(() => {
  return (data.value?.records || []) as ProcessCardItem[];
});

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total || 0,
  current: data.value?.pageNum || 1,
  pageSize: data.value?.pageSize || 10,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
) => {
  // 确保使用最新的搜索参数
  const params = {
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  };

  console.log('查询参数:', JSON.stringify(params));
  // 调用API进行查询
  listApiRun(params);
};

// 专门用于查询按钮的处理函数
const handleSearch = () => {
  console.log('点击查询按钮，搜索参数:', JSON.stringify(searchParam.value));
  handleTableChange({
    current: 1,
    pageSize: data.value?.pageSize || 10
  });
};

const edit = (id?: number) => {
  currentRouter.value.push({ path: "/bidman/processOrchestration/processNodes", query: { id: id } })
}

const beginAndEnd = ref()
watch(() => beginAndEnd.value, (n: any) => {
  if (n) {
    searchParam.value.begin = n[0]
    searchParam.value.end = n[1]
  } else {
    searchParam.value.begin = undefined
    searchParam.value.end = undefined
  }
});

// 使用枚举提供的方法获取状态文本
const getStatusText = (status: number) => {
  return ProcessOrchestrationModelStatusEnum.getStatusText(status)
}

// 解析组合业务类型，返回所有匹配的业务类型数组
const parseBusinessTypes = (combinedValue?: number) => {
  if (!combinedValue) return []

  const types = []
  const allTypes = [
    ProcessOrchestrationServiceTypeEnum.STAY,
    ProcessOrchestrationServiceTypeEnum.PLACE,
    ProcessOrchestrationServiceTypeEnum.CATERING,
    ProcessOrchestrationServiceTypeEnum.VEHICLE,
    ProcessOrchestrationServiceTypeEnum.ATTENDANT,
    ProcessOrchestrationServiceTypeEnum.ACTIVITY,
    ProcessOrchestrationServiceTypeEnum.INSURANCE,
    ProcessOrchestrationServiceTypeEnum.MATERIAL,
    ProcessOrchestrationServiceTypeEnum.TRAFFIC,
    ProcessOrchestrationServiceTypeEnum.PRESENT,
    ProcessOrchestrationServiceTypeEnum.OTHER,
    ProcessOrchestrationServiceTypeEnum.FULL_SERVICE_FEE,
    ProcessOrchestrationServiceTypeEnum.PLATFORM_SERVICE_FEE
  ]

  for (const typeValue of allTypes) {
    if ((combinedValue & typeValue) === typeValue) {
      types.push(ProcessOrchestrationServiceTypeEnum.getTypeText(typeValue))
    }
  }

  return types
}

const viewProcess = (record: any) => {
  // 修改为跳转到edit页面，并添加viewMode参数表示查看模式
  currentRouter.value.push({ path: "/bidman/processOrchestration/processNodes", query: { id: record.id, viewMode: 'true' } })
}

// 处理启用/停用状态变更
const handleStatusChange = async (item: any, newState: number) => {
  try {
    const statusText = newState === 1 ? '启用' : '停用'

    // 调用API更新状态
    await processOrchestrationApi.updateStatus({
      id: item.id,
      status: newState
    })

    // 更新成功后刷新列表
    handleTableChange({
      current: data.value?.pageNum || 1,
      pageSize: data.value?.pageSize || 10
    })

    console.log(`流程${statusText}成功`)
    message.success(`流程"${item.name}"${statusText}成功！`)
  } catch (error) {
    console.error('状态更新失败:', error)
    const statusText = newState === 1 ? '启用' : '停用'
    message.error(`流程"${item.name}"${statusText}失败，请重试`)
  }
}

// 导出数据弹窗相关状态
const exportModalVisible = ref(false)
const exportLoading = ref(false)
const exportData = ref<any>({})
const currentExportItem = ref<any>({})

// 处理导出功能
const handleExport = async (item: any) => {
  try {
    console.log('导出流程:', item.name, '(ID:', item.id, ')')
    currentExportItem.value = item
    exportLoading.value = true
    exportModalVisible.value = true

    // 调用详情接口获取完整数据
    const detailData = await processOrchestrationApi.get(item.id)
    
    if (detailData) {
      // 移除id字段，准备用于导出的数据
      const dataWithVerId = detailData as any
      const { id, verId, ...exportableData } = dataWithVerId
      exportData.value = exportableData
      console.log('导出数据准备完成:', exportableData)
    }
    
    exportLoading.value = false
  } catch (error) {
    console.error('获取导出数据失败:', error)
    message.error(`获取流程"${item.name}"导出数据失败，请重试`)
    exportLoading.value = false
    exportModalVisible.value = false
  }
}

// 复制导出数据到剪贴板
const copyExportData = async () => {
  try {
    const jsonString = JSON.stringify(exportData.value, null, 2)
    await navigator.clipboard.writeText(jsonString)
    message.success('数据已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败，请手动复制')
  }
}

// 关闭导出弹窗
const closeExportModal = () => {
  exportModalVisible.value = false
  exportData.value = {}
  currentExportItem.value = {}
}


// 使用枚举提供的方法获取流程状态选项
const statusOptions = ProcessOrchestrationModelStatusEnum.getStatusOptions()

// 业务类型选项
const businessTypeOptions = ProcessOrchestrationServiceTypeEnum.getTypeOptions()

// 创建时间范围
const createTimeRange = ref()
watch(() => createTimeRange.value, (n: any) => {
  if (n) {
    searchParam.value.createTimeBegin = n[0]
    searchParam.value.createTimeEnd = n[1]
  } else {
    searchParam.value.createTimeBegin = undefined
    searchParam.value.createTimeEnd = undefined
  }
});

// 修复updateTimeRange未定义错误
const updateTimeRange = ref()

// 智能截取名称，确保版本号显示
const formatDisplayName = (name: string, maxLength = 15) => {
  if (!name || name.length <= maxLength) {
    return name
  }
  
  // 查找版本号模式 (如 V1, V11, v1.0等)
  const versionMatch = name.match(/(V\d+(\.\d+)*|v\d+(\.\d+)*)$/i)
  
  if (versionMatch) {
    const versionPart = versionMatch[0]
    const nameWithoutVersion = name.substring(0, name.length - versionPart.length)
    const maxNameLength = maxLength - versionPart.length - 3 // 减去版本号长度和省略号长度
    
    if (nameWithoutVersion.length > maxNameLength) {
      return nameWithoutVersion.substring(0, maxNameLength) + '...' + versionPart
    }
  }
  
  // 如果没有版本号或者其他情况，直接截取
  return name.substring(0, maxLength - 3) + '...'
}
</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <div style="display: none">
      <PlusOutlined />
      <SearchOutlined />
      <EllipsisOutlined />
    </div>

    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <!-- 第一行查询条件 -->
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="processName">流程名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.name" placeholder="请输入流程名称" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="processStatus">流程状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchParam.state" placeholder="请选择流程状态" style="width: 100%" allow-clear>
              <h-select-option v-for="option in statusOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="creator">创建人：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.createName" placeholder="请输入创建人" allow-clear />
          </h-col>
          <!-- <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createTime">创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model="createTimeRange" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col> -->
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="item">业务类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="selectedBusinessType" placeholder="请选择业务类型" style="width: 100%" allow-clear
              label-in-value>
              <h-select-option v-for="option in (businessTypeOptions || [])" :key="option.value" :value="option.value">
                {{ option.label }}
              </h-select-option>
            </h-select>
          </h-col>
        </h-row>

        <!-- 第二行查询条件 -->
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="lastModifiedName">更新人：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.lastModifiedName" placeholder="请输入更新人" allow-clear />
          </h-col>

        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin: 0 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleSearch">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
            <h-button type="primary" @click="edit()">
              <PlusOutlined /> 新增
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-spin :spinning="loading">
          <div class="process-cards">
            <div class="process-card" v-for="item in dataSource" :key="item.id || ''">
              <div class="card-header">
                <h-tooltip placement="top">
                  <template #title>{{ item.name }}</template>
                  <div class="title">{{ formatDisplayName(item.name) }}</div>
                </h-tooltip>
                <div class="actions">
                  <h-tag :color="Number(item.state) === 1 ? 'green' : 'red'" class="flow-btn">
                    {{ getStatusText(Number(item.state)) }}
                  </h-tag>
                </div>
              </div>
              <div class="card-info">
                <div class="info-row">创建时间：{{ item.gmtCreate || item.createTime }}</div>
                <div class="info-row">创建人：{{ item.createName || item.creator }}</div>
                <div class="info-row">更新人：{{ item.lastModifiedName || item.updater }}</div>
                <div class="info-row">
                  <div style="margin-bottom: 4px;">业务类型：</div>
                  <div class="business-types">
                    <template v-if="parseBusinessTypes(Number(item.items)).length > 0">
                      <!-- 显示前4个业务类型 -->
                      <h-tag v-for="(businessType, index) in parseBusinessTypes(Number(item.items)).slice(0, 4)"
                        :key="index" color="blue" style="margin: 2px 4px 2px 0;">
                        {{ businessType }}
                      </h-tag>
                      <!-- 如果超过4个，显示省略号和提示 -->
                      <h-tooltip v-if="parseBusinessTypes(Number(item.items)).length > 4" placement="top">
                        <template #title>
                          <div style="max-width: 300px;">
                            <h-tag v-for="(businessType, index) in parseBusinessTypes(Number(item.items))" :key="index"
                              color="blue" style="margin: 2px 4px 2px 0;">
                              {{ businessType }}
                            </h-tag>
                          </div>
                        </template>
                        <span class="business-type-ellipsis">
                          ...等{{ parseBusinessTypes(Number(item.items)).length }}项
                        </span>
                      </h-tooltip>
                    </template>
                    <span v-else style="color: #999;">暂无</span>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <div class="footer-section">
                  <h-button type="link" @click="viewProcess(item)">查看</h-button>
                </div>
                <div class="footer-section">
                  <h-button type="link" @click="edit(item.id || undefined)">编辑</h-button>
                </div>
                <div class="footer-section">
                  <h-dropdown placement="bottomRight">
                    <h-button type="link">
                      <EllipsisOutlined />
                    </h-button>
                    <template #overlay>
                      <h-menu>
                        <h-menu-item v-if="Number(item.state) === 0" key="enable" @click="handleStatusChange(item, 1)">
                          <span style="color: #52c41a;">启用</span>
                        </h-menu-item>
                        <h-menu-item v-if="Number(item.state) === 1" key="disable" @click="handleStatusChange(item, 0)">
                          <span style="color: #ff4d4f;">停用</span>
                        </h-menu-item>
                        <h-menu-item key="export" @click="handleExport(item)">
                          <span>导出</span>
                        </h-menu-item>
                      </h-menu>
                    </template>
                  </h-dropdown>
                </div>
              </div>
            </div>
          </div>
        </h-spin>

        <!-- 添加分页组件 -->
        <div class="pagination-container">
          <h-pagination v-if="data && data.total" :current="pagination.current" :total="pagination.total"
            :pageSize="pagination.pageSize" :pageSizeOptions="['10', '20', '30', '40']" :disabled="loading"
            show-size-changer show-quick-jumper
            @change="(page, size) => handleTableChange({ current: page, pageSize: size || pagination.pageSize })"
            @showSizeChange="(current, size) => handleTableChange({ current: current, pageSize: size })" />
        </div>
      </h-col>
    </h-row>

    <!-- 导出数据弹窗 -->
    <h-modal
      v-model:open="exportModalVisible"
      :title="`导出流程数据 - ${currentExportItem.name || ''}`"
      width="80%"
      :confirmLoading="exportLoading"
      @ok="copyExportData"
      @cancel="closeExportModal"
      okText="复制到剪贴板"
      cancelText="关闭"
    >
      <div class="export-data-container">
        <div v-if="exportLoading" style="text-align: center; padding: 40px;">
          <h-spin size="large" />
          <div style="margin-top: 16px;">正在获取数据...</div>
        </div>
        <div v-else>
          <div style="margin-bottom: 16px; color: #666;">
            请复制以下数据，然后在新增页面点击"导入"按钮进行导入：
          </div>
          <div class="export-data-content">
            <pre>{{ JSON.stringify(exportData, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </h-modal>
  </div>
</template>

<style scoped lang="less">
.process-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  padding: 16px;
  min-height: 200px;
}

.process-card {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  min-width: 0; /* 防止内容溢出 */
}

.card-header {
  display: flex;
  align-items: center;
  padding: 16px 16px 8px;
}


.title {
  font-size: 16px;
  font-weight: 500;
  flex: 1;
  color: #333;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.actions {
  display: flex;
  gap: 8px;

  .flow-btn {
    font-size: 12px;
    height: 24px;
    padding: 0 10px;
  }

  .pack-btn {
    font-size: 12px;
    height: 24px;
    padding: 0 10px;
  }
}

.card-info {
  padding: 0 16px 16px;

  .info-row {
    margin-bottom: 6px;
    font-size: 14px;
    color: #666;
    line-height: 1.6;
  }

  .status-dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 6px;
  }

  .status-active {
    background-color: #52c41a;
  }

  .status-used {
    background-color: #f5222d;
  }
}

.business-types {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.business-type-ellipsis {
  color: #1890ff;
  cursor: pointer;
  font-size: 12px;
  padding: 2px 6px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  background-color: #fafafa;
  margin: 2px 4px 2px 0;

  &:hover {
    background-color: #e6f7ff;
    border-color: #1890ff;
  }
}

.card-footer {
  display: flex;
  border-top: 1px solid #f0f0f0;
  padding: 0;

  .footer-section {
    flex: 1;
    text-align: center;
    padding: 10px 0;
    border-right: 1px solid #f0f0f0;
    min-width: 0; // 防止按钮文字溢出

    &:last-child {
      border-right: none;
    }

    &.text-right {
      text-align: center;
    }
  }

  button {
    margin: 0;
    padding: 0;
    font-size: 14px;
  }
}

/* 添加分页容器样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
  padding: 0 16px 16px;
}

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

/* 导出数据弹窗样式 */
.export-data-container {
  max-height: 600px;
}

.export-data-content {
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}

.export-data-content pre {
  margin: 0;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
