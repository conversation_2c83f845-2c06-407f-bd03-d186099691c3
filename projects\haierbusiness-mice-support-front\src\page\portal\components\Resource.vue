<script setup>
  import { ref } from 'vue';
  import ResourceBackgroundImg from '@/assets/image/home/<USER>';
  import EcharmImg from '@/assets/image/home/<USER>';
  import JiHotelImg from '@/assets/image/home/<USER>';
  import HiltonImg from '@/assets/image/home/<USER>';
  import AtourImg from '@/assets/image/home/<USER>';
  import LavandeImg from '@/assets/image/home/<USER>';
  import JinJiangHotelImg from '@/assets/image/home/<USER>';
  import ViennaHotelImg from '@/assets/image/home/<USER>';
</script>

<template>
  <div class="resource">
    <img :src="ResourceBackgroundImg" />
    <div class="content">
      <div class="title">
        直签酒店<span class="count">800+</span>家，覆盖<span class="count">60+</span>城市
      </div>
      <div class="place">
        青岛、广州、杭州、上海、厦门、深圳、天津、石家庄、呼和浩特、宁波、合肥、福州、南京、苏州...
      </div>
    </div>
    <div class="hotel">
      <img :src="EcharmImg" :style="{ width: '148px' }" />
      <img :src="JiHotelImg" :style="{ width: '168px' }" />
      <img :src="HiltonImg" :style="{ width: '98px' }" />
      <img :src="AtourImg" :style="{ width: '108px' }" />
      <img :src="LavandeImg" :style="{ width: '118px' }" />
      <img :src="JinJiangHotelImg" :style="{ width: '108px' }" />
      <img :src="ViennaHotelImg" :style="{ width: '118px' }" />
    </div>
  </div>
</template>

<style scoped lang="less">
  .resource {
    position: relative;
    >img {
      width: 100%;
    }
    .content {
      width: 100%;
      position: absolute;
      top: 150px;
      left: 0;
      text-align: center;
      .title {
        font-weight: 500;
        font-size: 32px;
        color: #1D2129;
        line-height: 45px;
        .count {
          font-size: 48px;
          color: #1868DB;
        }
      }
      .place {
        margin-top: 23px;
        font-size: 18px;
        color: #1D2129;
        line-height: 25px;
      }
    }
    .hotel {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-left: -56px;
      padding-left: 67px;
      padding-right: 90px;
      width: 1512px;
      height: 128px;
      background: #FFFFFF;
      box-shadow: 0px 4px 20px 0px rgba(1,3,49,0.07);
      border-radius: 20px;
      border: 2px solid #FFFFFF;
    }
  }
</style>
