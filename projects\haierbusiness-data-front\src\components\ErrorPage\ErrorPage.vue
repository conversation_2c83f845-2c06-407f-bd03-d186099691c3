<template>
  <h-result :status="status" :title="title" :sub-title="description">
    <template #extra>
      <h-button type="primary" @click="$router.replace({ path: '/dashboard' })">返回首页</h-button>
    </template>
  </h-result>
</template>

<script lang="ts" setup>

interface Props {
  status: string
  title: string
  description: string
}

withDefaults(defineProps<Props>(), {
  status: '500',
  title: "未知错误",
  description: "暂时无法找到错误原因",
})

</script>