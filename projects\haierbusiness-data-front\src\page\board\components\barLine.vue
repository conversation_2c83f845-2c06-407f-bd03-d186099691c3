<template>
  <div :id="id" :style="{ height: props.height + 'vh' }"></div>
</template>
<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import * as echarts from "echarts";
import { colors } from "../data";
import { EventBus } from "../eventBus";

const props = defineProps({
  from: {
    type: String,
    default: "",
  },
  height: {
    type: Number,
    default: 33,
  },
  legend: Array,
  xAxis: Array,
  yAxis: Array,
  series: Array,
  tooltip: {
    type: Object,
    default() {
      return {
        trigger: "axis",
      };
    },
  },
});
const id = ref("bar-line-" + Date.now());
let chartDom, myChart;
onMounted(() => {
  chartDom = document.getElementById(id.value);
  myChart = echarts.init(chartDom as any, "dark");
  refresh();
  //点击事件
  myChart.on("click", (param) => {
    if (
      props.from != "ChangeAndRefund" &&
      props.from != "ydsj_hour" &&
      props.from != "overview"
    ) {
      if (props.from == "supplier_name") {
        EventBus.emit("refresh", {
          ...param,
          data: {
            name: param.name,
          },
          from: "supplier_name",
        });
      } else if (props.from == "tqydtsmc") {
        EventBus.emit("refresh", {
          ...param,
          data: {
            name: param.name,
          },
          from: "tqydtsmc",
        });
      } else {
        EventBus.emit("refresh", {
          ...param,
          from: "date",
        });
      }
    }
  });
});

const refresh = () => {
  props.yAxis?.forEach((item: any) => {
    item.splitLine = {
      show: false,
      lineStyle: {
        color: "rgba(69, 177, 239, 0.1)",
      },
    };
  });
  myChart?.clear();
  myChart?.setOption({
    backgroundColor: "transparent",
    tooltip: props.tooltip,
    grid: {
      left: "1%",
      right: "1%",
      bottom: "10%",
      containLabel: true,
    },
    // dataZoom: [
    //     {
    //         type: 'inside'
    //     },
    //     {
    //         type: 'slider'
    //     }
    // ],
    legend: {
      data: props.legend,
    },
    xAxis: [
      {
        type: "category",
        data: props.xAxis,
        axisTick: {
          show: false,
        },
        axisLabel: {
          rotate: 30,
        },
        splitLine: {
          lineStyle: {
            color: "#45B1EF",
          },
        },
      },
    ],
    yAxis: props.yAxis,
    series: props.series,
    color: colors,
  });
  console.log({
      dataType:"柱状图",
    backgroundColor: "transparent",
    tooltip: props.tooltip,
    grid: {
      left: "1%",
      right: "1%",
      bottom: "10%",
      containLabel: true,
    },
    // dataZoom: [
    //     {
    //         type: 'inside'
    //     },
    //     {
    //         type: 'slider'
    //     }
    // ],
    legend: {
      data: props.legend,
    },
    xAxis: [
      {
        type: "category",
        data: props.xAxis,
        axisTick: {
          show: false,
        },
        axisLabel: {
          rotate: 30,
        },
        splitLine: {
          lineStyle: {
            color: "#45B1EF",
          },
        },
      },
    ],
    yAxis: props.yAxis,
    series: props.series,
    color: colors,
  },"-----------------")
};
watch(
  () => props.series,
  () => {
    refresh();
  }
);
</script>
<!-- <sciprt lang="ts">
    const barStyle = {
        color: "rgba(0,240,255,0.4)",
        itemStyle: {
            borderColor: "#00F0FF"
        }
    }
    const lineStyle = {
        color: "#FFD700",
        smooth: true,
        symbol: "none"
    }
    export default{
        barStyle,
        lineStyle
    }
</sciprt> -->
<style scoped lang="less"></style>
