<template>
  <h-row justify="center">
    <h-col :xs="20" :sm="20" :lg="16" :xl="16">
      <h-row class="content_box">
        <h-col :span="24" class="content_title">
          {{ currentDetail?.infoTitle }}
        </h-col>
        <h-col :span="24" class="time">
          <span>{{ currentDetail?.infoAuthor }}</span>
          <h-divider type="vertical" style="height: 20px;" />
          <span>{{ currentDetail?.infoDate && dayjs(currentDetail?.infoDate).format('YYYY-MM-DD') }}</span>
          <h-divider type="vertical" style="height: 20px;" />
          <span><EyeOutlined />  {{ currentDetail?.readCount || 0 }}</span>
        </h-col>
        <h-divider />
        <h-col class="detailed_information">
          <div v-html="currentDetail?.content"></div>
        </h-col>
        <h-divider />
      </h-row>
    </h-col>
  </h-row>
</template>

<script lang="ts" setup>

import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  Divider as hDivider,
  message,
  TableProps
} from 'ant-design-vue';
import { EyeOutlined } from '@ant-design/icons-vue';
import { useRoute, useRouter } from 'vue-router'
import { informationApi } from '@haierbusiness-front/apis';
import { ref, watch, onMounted} from "vue";
import type { IPageRequest, InformationType } from "@haierbusiness-front/common-libs";
import { toNumber } from "lodash-es";
import dayjs from "dayjs";
import { useDetail } from "@haierbusiness-front/composables";
import router from '../../router'

const route = useRoute()
// const router = useRouter()
const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router
  id.value = toNumber(currentRouter.value?.currentRoute.query.id)
})

const id = ref()
const defaultData: InformationType = {
  id: null,
  creator: '',
  createTime: '',
  showStatus: 0,
  imgUrl: '',
  infoAuthor: '',
  infoDate: '',
  infoTitle: '',
  content: '',
}

const { currentDetail, fetchDetail } = useDetail<InformationType>(informationApi, id.value, "差旅资讯");

watch(() => currentRouter.value?.currentRoute?.query, (newValue, oldValue) => {
  id.value = toNumber(newValue.id)
  fetchDetail(id.value)
})

</script>

<style lang="less" scoped>
.content_box {
  padding: 60px 0 0 0;
  text-align: left;

  div {
    text-align: left;
    font-size: 16px;
    font-weight: normal;
    font-stretch: normal;
    line-height: 28px;
    letter-spacing: 0px;
    color: #666666;
  }

  .content_title {
    font-size: 32px;
    font-weight: bold;
    font-stretch: normal;
    line-height: 40px;
    letter-spacing: 0px;
    color: #333333;
  }

  .time {
    padding-top: 20px;

    span {
      display: inline-block;
      font-size: 16px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 18px;
      letter-spacing: 0px;
      color: #999999;
      vertical-align: middle;
    }
  }

  .detailed_information {}
}

@media (min-width: 0px) and (max-width: 991px) {
  .content_box {
    padding-top: 45px;

    .content_title {
      font-size: 24px;
      line-height: 30px;
    }
  }

}

@media (max-width: 575px) {
  .content_box {
    padding-top: 30px;

    div {
      font-size: 14px;
      line-height: 25px;
    }

    .content_title {
      font-size: 20px;
      line-height: 25px;
    }

    .time {
      padding-top: 10px;

      span {
        font-size: 12px;
        line-height: 18px;
      }
    }

  }

}
</style>
<style>
img {
  max-width: 100% !important;
}

code {
  word-wrap: normal;
  font-family: HarmonyBold, Consolas, Monaco, Andale Mono, Ubuntu Mono, monospace;
  -webkit-hyphens: none;
  hyphens: none;
  line-height: 1.5;
  margin: 0.5em 0;
  overflow: auto;
  padding: 1em;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  text-align: left;
  text-shadow: 0 1px #fff;
  white-space: pre;
  word-break: normal;
  word-spacing: normal;
  background-color: #f5f2f0;
  border: 1px solid #e8e8e8;
  border-radius: 4px 4px;
  display: block;
  font-size: 14px;
  text-indent: 0;
}
</style>