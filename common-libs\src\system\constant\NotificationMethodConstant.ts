type keys = 'CONTENT' | 'URL' | 'IMAGE';

export const notificationMethodConstant = {
    CONTENT: { "code": 1, "desc": "正文" },
    URL: { "code": 2, "desc": "链接" },
    IMAGE: { "code": 4, "desc": "图片" },
  
    ofType: (type?: number): { "code": number, "desc": string } | null => {
      for (const key in notificationMethodConstant) {
        const item = notificationMethodConstant[key as keys];
        if (type === item.code) {
          return item;
        }
      }
      return null;
    },
    toArray:() :({ code: number, desc: string } | undefined)[] => {
      const types = Object.keys(notificationMethodConstant).map((i: string) => {
        if(i !== 'ofType' && i !== 'toArray' && i !== 'toNumberArray' ) {
          return notificationMethodConstant[i as keys]
        }
        return
      })
      const newTypes = types.filter(function (s) {
        return s && s; 
      })
      return newTypes
    },
    toNumberArray:() :(number | undefined)[] => {
      const types = Object.keys(notificationMethodConstant).map((i: string) => {
        if(i !== 'ofType' && i !== 'toArray' ) {
          return notificationMethodConstant[i as keys].code
        }
        return
      })
      const newTypes = types.filter(function (s) {
        return s && s; 
      })
      return newTypes
    }
  }