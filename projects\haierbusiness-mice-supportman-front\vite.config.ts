import { loadEnv } from 'vite';

import vue from '@vitejs/plugin-vue';
import path from 'path';

const CWD = process.cwd();

export default ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);

  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
    },
    plugins: [vue()],
    build: {
      target: ['es2015'],
    },
    server: {
      port: 5161,
      host: true,
      proxy: {
        '/hb/mice-bid/api': {
          target: 'https://businessmanagement-test.haier.net/hbweb/merchant/hb/mice-bid/api/',
          // target: 'http://***********:37234/', //*************: 9209
          // target: 'http://*************:9209/', //李靖
          // target: 'http://*************:9209/', //贾博文
          // target: 'http://*************:9209/', // 忠明
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`/hb/mice-bid/api`), ''),
        },
        '/hb/common/api': {
          target: 'https://businessmanagement-test.haier.net/hbweb/index/hb',
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`/hb`), ''),
        },
        '/hb/merchant/api': {
          target: 'https://businessmanagement-test.haier.net/hbweb/merchant/hb/merchant/api/',
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`/hb/merchant/api`), ''),
        },
        '/hb': {
          // target: "http://localhost:8080/hb",
          target: 'https://businessmanagement-test.haier.net/hbweb/index/hb',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb/, ''),
        },
        '/upload': {
          target: 'https://businessmanagement-test.haier.net/hbweb/upload',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/upload/, ''),
        },
      },
    },
  };
};
