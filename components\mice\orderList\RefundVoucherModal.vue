<template>
  <a-modal
    :title="modalConfig.title"
    :open="visible"
    @cancel="handleCancel"
    @ok="handleOk"
    :confirm-loading="submitLoading"
    width="600px"
    destroyOnClose
  >
    <div class="refund-voucher-content">
      <div class="upload-section">
        <div class="upload-item">
          <div class="upload-label">{{ modalConfig.label }}：</div>
          <div class="upload-area">
            <!-- 已上传文件标签展示 -->
            <div class="file-tags" v-if="uploadedFiles.length > 0">
              <a-tag
                v-for="file in uploadedFiles"
                :key="file.uid"
                closable
                class="file-tag"
                @click="() => handlePreviewFile(file)"
                @close="() => handleRemoveFile(file)"
              >
                <span class="file-icon">{{ getFileIcon(file.name) }}</span>
                {{ getFileDisplayName(file.name) }}
              </a-tag>
            </div>

            <!-- 上传按钮 -->
            <a-upload
              :file-list="[]"
              :custom-request="uploadRequest"
              :multiple="true"
              :show-upload-list="false"
              accept=".pdf,.doc,.docx,.jpg,.png,.jpeg,.xls,.xlsx"
              :before-upload="beforeUpload"
            >
              <a-button :loading="uploadLoading" type="primary">
                <upload-outlined />
                上传文件
              </a-button>
            </a-upload>
            <div class="upload-tip">支持格式：PDF、图片、Office文档，文件大小不超过10MB，可上传多个文件</div>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { Modal, Upload, Button, message } from 'ant-design-vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import { fileApi, miceBidManOrderListApi } from '@haierbusiness-front/apis';
import type { UploadProps } from 'ant-design-vue';

interface Props {
  visible: boolean;
  orderData?: any; // 订单数据
  type?: 'refund' | 'payment'; // 凭证类型：退款凭证或支付凭证
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'refund',
});
const emit = defineEmits<Emits>();

// 根据类型配置弹框内容
const modalConfig = computed(() => {
  if (props.type === 'payment') {
    return {
      title: '上传支付凭证',
      label: '支付凭证',
    };
  }
  return {
    title: '上传退款凭证',
    label: '退款凭证',
  };
});

// 响应式数据
const fileList = ref<any[]>([]);
const uploadedFiles = ref<any[]>([]);
const uploadLoading = ref<boolean>(false);
const submitLoading = ref<boolean>(false);

// 基础URL
const baseUrl = import.meta.env.VITE_BUSINESS_URL || '';

// 文件上传前验证
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  // 检查文件大小（10MB限制）
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB！');
    return false;
  }

  // 检查文件类型
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/jpeg',
    'image/jpg',
    'image/png',
  ];

  const allowedExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.jpg', '.jpeg', '.png'];
  const fileName = file.name.toLowerCase();
  const isValidType = allowedTypes.includes(file.type) || allowedExtensions.some((ext) => fileName.endsWith(ext));

  if (!isValidType) {
    message.error('只支持上传 PDF、图片、Office文档格式的文件！');
    return false;
  }

  return true;
};

// 自定义上传请求
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  console.log(`开始上传${modalConfig.value.label}文件:`, options.file.name);

  fileApi
    .upload(formData)
    .then((response) => {
      // 创建文件对象
      const fileObj = {
        uid: options.file.uid || Date.now().toString(),
        name: options.file.name,
        status: 'done',
        url: response.path ? baseUrl + response.path : '',
        filePath: response.path ? baseUrl + response.path : '',
        fileName: options.file.name,
      };

      // 添加到已上传文件列表
      uploadedFiles.value.push(fileObj);

      // 通知上传成功
      options.onProgress(100);
      options.onSuccess(response, options.file);

      console.log(`${modalConfig.value.label}文件上传成功:`, fileObj);
      message.success('文件上传成功');
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error('文件上传失败，请重试');
      options.onError(error);
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 获取文件图标
const getFileIcon = (fileName: string) => {
  const ext = fileName.toLowerCase().split('.').pop();
  switch (ext) {
    case 'pdf':
      return '📄';
    case 'doc':
    case 'docx':
      return '📝';
    case 'xls':
    case 'xlsx':
      return '📊';
    case 'jpg':
    case 'jpeg':
    case 'png':
      return '🖼️';
    default:
      return '📎';
  }
};

// 获取文件显示名称（截断过长的文件名）
const getFileDisplayName = (fileName: string) => {
  if (fileName.length > 20) {
    const ext = fileName.split('.').pop();
    const name = fileName.substring(0, fileName.lastIndexOf('.'));
    return `${name.substring(0, 15)}...${ext}`;
  }
  return fileName;
};

// 预览文件
const handlePreviewFile = (file: any) => {
  if (file.filePath || file.url) {
    window.open(file.filePath || file.url, '_blank');
  }
};

// 删除文件
const handleRemoveFile = (file: any) => {
  const index = uploadedFiles.value.findIndex(f => f.uid === file.uid);
  if (index > -1) {
    uploadedFiles.value.splice(index, 1);
    console.log('删除文件:', file.name);
  }
};

// 删除文件（旧方法，保持兼容）
const handleFileRemove = (file: any) => {
  console.log('删除文件:', file.name);
  return true;
};

// 取消弹框
const handleCancel = () => {
  emit('update:visible', false);
  // 清空文件列表
  fileList.value = [];
  uploadedFiles.value = [];
};

// 确认提交
const handleOk = async () => {
  // 校验：必须上传文件
  if (!uploadedFiles.value || uploadedFiles.value.length === 0) {
    message.error(`请上传${modalConfig.value.label}文件！`);
    return;
  }

  // 校验：确保所有文件都上传完成
  const incompleteFiles = uploadedFiles.value.filter(file => !file.filePath);
  if (incompleteFiles.length > 0) {
    message.error('文件还在上传中，请稍候再试！');
    return;
  }

  if (!props.orderData?.miceId) {
    message.error('订单信息错误，无法提交！');
    return;
  }

  submitLoading.value = true;

  try {
    console.log(`🚀 开始提交${modalConfig.value.label}`);
    console.log('📋 订单数据:', props.orderData);
    console.log('🆔 miceId:', props.orderData.miceId);
    console.log('📎 上传文件:', uploadedFiles.value);

    // 构建附件文件数组（只要路径，不要文件名）
    const attachmentFileList = uploadedFiles.value.map(file => file.filePath);

    // 根据类型调用不同的API
    const submitParams = {
      miceId: props.orderData.miceId,
      attachmentFile: attachmentFileList
    };

    console.log('📤 提交参数:', submitParams);

    let result: any;
    if (props.type === 'payment') {
      // 调用支付凭证上传API
      result = await miceBidManOrderListApi.paymentVoucher(submitParams);
    } else {
      // 调用退款凭证上传API
      result = await miceBidManOrderListApi.refundVoucher(submitParams);
    }

    console.log('✅ 提交成功:', result);
    message.success(`${modalConfig.value.label}上传成功！`);
    emit('success');
    emit('update:visible', false);

    // 清空文件列表
    fileList.value = [];
    uploadedFiles.value = [];
  } catch (error) {
    console.error('❌ 提交失败:', error);
    message.error('提交失败，请稍后重试');
  } finally {
    submitLoading.value = false;
  }
};

// 监听visible变化，重置状态
watch(
  () => props.visible,
  (newVal) => {
    if (!newVal) {
      fileList.value = [];
      uploadedFiles.value = [];
      uploadLoading.value = false;
      submitLoading.value = false;
    }
    console.log(props.orderData?.miceId, 'props.orderData?.miceId');
  },
);
</script>

<style scoped>
.refund-voucher-content {
  padding: 20px 0;
}

.upload-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.upload-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.upload-label {
  min-width: 100px;
  font-weight: 500;
  line-height: 32px;
  color: #333;
}

.upload-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

/* 文件标签样式 */
.file-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.file-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #f6f6f6;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  max-width: 200px;
}

.file-tag:hover {
  background: #e6f7ff;
  border-color: #1890ff;
}

.file-icon {
  font-size: 14px;
  flex-shrink: 0;
}

:deep(.ant-upload-list) {
  margin-top: 8px;
}

:deep(.ant-upload-list-item) {
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

:deep(.ant-upload-list-item-name) {
  color: #1890ff;
}
</style>
