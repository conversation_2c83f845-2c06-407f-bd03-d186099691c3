import {
    Result,
    IBannerRequest ,
    IPageResponse,
    IBannerResponse
} from '@haierbusiness-front/common-libs'
import { get, post } from '../request'
export const bannerListApi ={
    list: (params: IBannerRequest ): Promise<IPageResponse<IBannerResponse>> => {
        return get("/portal/api/admin-api/banner/top-img/page", params)
    },
    get: (id: number): Promise<IBannerResponse> => {
        return get("/portal/api/admin-api/banner/top-img/get", {
            id
        })
    },
    detail: (id: number): Promise<Result> => {
        return get('/portal/api/app-api/banner/top-img/get', { id })
    },
    banners: (params: IBannerRequest ): Promise<IBannerResponse[]> => {
        return get("/portal/api/app-api/banner/top-img/list", params)
    },
    save: (params: IBannerRequest ): Promise<Result> => {
        return post('/portal/api/admin-api/banner/top-img/create', params)
    },
    edit: (params: IBannerRequest): Promise<Result> => {
        return post('/portal/api/admin-api/banner/top-img/update', params)
    },
    remove: (id: number): Promise<Result> => {
        return get('/portal/api/admin-api/banner/top-img/delete', { id })
    },
    onTimeChange : (dateRange: string[]) => {
        const times:string[] = []
        times.push(dateRange[0] + ' 00:00:00')
        times.push(dateRange[1] + ' 23:59:59')
        return times
        // searchKey.createTime = times
    }
 
}