import { download, get, post } from '../request';
import { IMeetingSignInFilter, IMeetingSignInRules, IPageResponse, Result, IMeetingSignInDetails } from '@haierbusiness-front/common-libs';

export const meetingSignInApi = {
  //签到明细
  list: (params: IMeetingSignInFilter): Promise<IPageResponse<IMeetingSignInDetails>> => {
    return get('/mice-support/api/mice/check/in/detailList', params);
  },
  //签到规则
  details: (miceInfoId: number): Promise<IMeetingSignInRules> => {
    return get('/mice-support/api/mice/check/in/detail', {
      miceInfoId,
    });
  },
  //新增签到规则
  save: (params: IMeetingSignInRules): Promise<Result> => {
    return post('/mice-support/api/mice/check/in/add', params);
  },

  //导出签到明细
  export: (params: { miceInfoId: number; miceInfoName?: string }): Promise<void> => {
    return download('/mice-support/api/mice/check/in/exportSignDetail', params)
  },

  //编辑签到规则
  edit: (params: IMeetingSignInRules): Promise<Result> => {
    return post('/mice-support/api/mice/check/in/update', params);
  },
  //发送签到提醒
  sendNotice: (params: IMeetingSignInRules): Promise<Result> => {
    return post('/mice-support/api/mice/check/in/sendCheckNotice', params);
  },

  //补签接口
  backCheck: (params: IMeetingSignInRules): Promise<Result> => {
    return post('/mice-support/api/mice/check/in/back-check', params);
  },
};
