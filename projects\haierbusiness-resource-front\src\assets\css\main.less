@import './scroll-style.less';
// 主题色
@import "@haierbusiness-front/components/theme/theme.css";

@baseWidth: 100vw;
@baseHeight: 100vh;

.base-container {
  height: @baseHeight;
  width: @baseWidth;
  overflow: hidden;
}

.float-left {
  float: left;
}

.multi-line-ellipsis {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2; /* 控制显示的行数 */
  line-height: 1.5; /* 行高，需根据实际需求设置 */
  max-height: 4.5em; /* max-height = line-height * -webkit-line-clamp */
  word-break: break-all;
}