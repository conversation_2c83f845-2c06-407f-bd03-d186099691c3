<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import { banquetApi } from '@haierbusiness-front/apis';
import {
  BApplyListRecord,
  BApplyPerson,
  BanquetStatusEnum
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';

import router from '../../../router'
// const router = useRouter()
const store = applicationStore();

const currentRouter = ref()
const route = ref(getCurrentRoute());
const id = route.value?.query?.id;
const detail = ref<BApplyListRecord>();

const { loginUser } = storeToRefs(store);

const getDetail = (id: number) => {

  banquetApi.getNotificationDetail({id}).then((res) => {
    detail.value = res;
  });
};

const showMore = ref(false)


onMounted(async () => {
  currentRouter.value = await router
})



watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);




</script>

<template>
  <div>
    <h-row justify="space-between" style="padding: 20px;" >
      <h-col style="font-size: 18px;display: flex; align-items: center; justify-content: center;" >公告详情</h-col>
      <h-col><h-button type="link" @click="currentRouter.back(-1)">返回</h-button></h-col>
    </h-row>
    <div style="background-color: #ffff;min-height: 600px;width: 100%;padding: 30px 60px;overflow: auto;">
      <h-row style="margin-bottom: 20px; font-size: 18px; font-weight: 600;">{{ detail?.title }}</h-row>
      <h-row style="margin-bottom: 40px">
        <h-col :span="6">
          经办人 : {{ `${detail?.creatorName}(${detail?.creator})` }}
        </h-col>
        <h-col :span="6">
          操作时间 : {{ detail?.createTime }}
        </h-col>
      </h-row>

      <h-row >
        <h-col :span="24">
          <div v-html="detail?.content"></div>
        </h-col>
        
      </h-row>

    </div>


  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}
:deep(.ant-descriptions-item-label) {
  width: 200px;
}
:deep(.ant-descriptions-item-content) {
  width: 300px;
}
</style>
