<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormInstance,
  FormItem as hFormItem,
  Input as hInput,
  message,
  Modal as hModal,
  Row as hRow,
} from 'ant-design-vue';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { getCurrentRouter } from '@haierbusiness-front/utils';
import { PropType, ref, watch } from 'vue';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import {
  AnnualPlanTypeStateConstant,
  IAnnualPlanTypeListResponse,
  IAnnualPlanTypeUpdateRequest,
} from '@haierbusiness-front/common-libs';
import { Dayjs } from 'dayjs';

const router = getCurrentRouter();
const visible = ref(false);

const emit = defineEmits<{
  (event: 'listApiRun'): void;
}>();

const prop = defineProps({
  existYears: Object as PropType<string[]>,
  // 1 查看 2 新增 3 修改
  type: Number,
  // 展示或修改的参数
  typeData: Object as PropType<IAnnualPlanTypeListResponse>,
});

const show = () => {
  visible.value = true;
};

const saveLoading = ref(false);
const submitChangeForm = () => {
  if (prop.type === 1) {
    visible.value = false;
  } else {
    formRef.value.validate().then(() => {
      submitChange();
    });
  }
};
const submitChange = () => {
  saveLoading.value = true;
  saveParam.value.data?.forEach((it) => {
    if (it) {
      it.state = it.state || AnnualPlanTypeStateConstant.IN_VALID.code;
      it.year = parseInt(saveParam.value.year as any);
    }
  });
  saveParam.value.data?.push(...deletedSaveParam.value.data!!);
  dailyTypeApi
    .update(saveParam.value)
    .then(() => {
      emit('listApiRun');
      message.success('更新成功！');
      saveParam.value = { data: [{}] };
      visible.value = false;
    })
    .finally(() => {
      for (let it of deletedSaveParam.value.data!!) {
        let index = saveParam.value.data?.indexOf(it);
        if (index != undefined && index !== -1) {
          saveParam.value.data?.splice(index, 1);
        }
      }
      saveLoading.value = false;
    });
};

const directSubmitChangeForm = () => {
  setTimeout(() => {
    if (watchMutex.value) {
      submitChange();
    } else {
      directSubmitChangeForm();
    }
  }, 10);
};

class IAnnualPlanTypeUpdateWrapperRequest extends IAnnualPlanTypeUpdateRequest {
  year?: string;
}

const saveParam = ref<IAnnualPlanTypeUpdateWrapperRequest>({
  data: [{}],
});

const deletedSaveParam = ref<IAnnualPlanTypeUpdateRequest>({
  data: [],
});
const watchMutex = ref(false);
const watchUpdateData = () => {
  // 查看
  if (prop.type === 1 && prop.typeData) {
    saveParam.value = { data: [] };
    deletedSaveParam.value = { data: [] };
    const names = (prop.typeData.name || '').split(' / ');
    const descriptions = (prop.typeData.description || '').split(' / ');
    saveParam.value.year = String(prop.typeData.year);
    for (let i in names) {
      saveParam.value.data?.push({
        id: prop.typeData.id,
        name: names[i],
        year: prop.typeData.year,
        state: prop.typeData.state,
        description: descriptions[i] || '',
      });
    }
  } else if (prop.type === 2 && prop.typeData) {
    saveParam.value = { data: [{}] };
    deletedSaveParam.value = { data: [] };
  } else if (prop.type === 3 && prop.typeData) {
    saveParam.value = { data: [] };
    deletedSaveParam.value = { data: [] };
    const ids = (prop.typeData.id + '' || '').split(' / ');
    const names = (prop.typeData.name || '').split(' / ');
    const descriptions = (prop.typeData.description || '').split(' / ');
    saveParam.value.year = String(prop.typeData.year) ;
    for (let i in names) {
      saveParam.value.data?.push({
        id: parseInt(ids[i]),
        name: names[i],
        year: prop.typeData.year,
        state: prop.typeData.state,
        description: descriptions[i] || '',
      });
    }
  }
  watchMutex.value = true;
};

watch([() => prop.type, () => prop.typeData], watchUpdateData);
defineExpose({
  show,
  directSubmitChangeForm,
});
const formRef = ref<FormInstance>({} as FormInstance);

const removeDomain = (item: any) => {
  if (item.id) {
    item.isDeleted = true;
    deletedSaveParam.value.data?.push(item);
    let index = saveParam.value.data?.indexOf(item);
    if (index != undefined && index !== -1) {
      saveParam.value.data?.splice(index, 1);
    }
  } else {
    let index = saveParam.value.data?.indexOf(item);
    if (index != undefined && index !== -1) {
      saveParam.value.data?.splice(index, 1);
    }
  }
};

const addDomain = () => {
  saveParam.value.data?.push({});
};

const disabledDate: any = (current: Dayjs) => {
  return current && prop.existYears?.includes(current.format('YYYY'));
};

const title = () => {
  if (prop.type === 1) {
    return '查看年度目标';
  } else if (prop.type === 2) {
    return '新增年度目标';
  } else if (prop.type === 3) {
    return '修改年度目标';
  } else {
    return '';
  }
};
</script>

<template>
  <h-modal
    v-model:open="visible"
    :title="title()"
    @ok="submitChangeForm"
    :mask-closable="false"
    :confirmLoading="saveLoading"
  >
    <h-form ref="formRef" name="dynamic_form_item" :model="saveParam">
      <h-row :align="'middle'">
        <h-col :span="5" class="align-right">
          <div class="required lable">年份:</div>
        </h-col>
        <h-col :span="18">
          <h-form-item name="year" :rules="{ required: true }" class="fresh-from-item">
            <h-date-picker
              :disabled="type === 1"
              style="width: 315px"
              v-model:value="saveParam.year"
              :disabledDate="disabledDate"
              value-format="YYYY"
              picker="year"
            />
          </h-form-item>
        </h-col>
      </h-row>
      <template v-for="(domain, index) in saveParam.data">
        <h-row :align="'middle'">
          <h-col :span="5" class="align-right">
            <div v-if="index === 0" class="required lable">目标类型:</div>
          </h-col>
          <h-col :span="6">
            <h-form-item
              :name="['data', index, 'name']"
              :rules="{
                required: true,
              }"
              class="fresh-from-item"
            >
              <h-input :disabled="type === 1" v-model:value="domain!!.name" placeholder="目标类型" />
            </h-form-item>
          </h-col>
          <h-col :span="9" :offset="1">
            <h-form-item :name="['data', index, 'description']" class="fresh-from-item">
              <h-input :disabled="type === 1" v-model:value="domain!!.description" placeholder=" 类型描述" />
            </h-form-item>
          </h-col>
          <h-col :span="1" :offset="2">
            <MinusCircleOutlined
              v-if="(saveParam.data?.length || 0) > 1 && type !== 1"
              class="lable"
              @click="removeDomain(domain)"
            />
          </h-col>
        </h-row>
      </template>
      <h-row :align="'middle'">
        <h-col :span="5" class="align-right">
          <div v-if="(saveParam.data?.length || 0) < 1" class="required lable">目标类型:</div>
        </h-col>
        <h-col :span="16" v-if="type !== 1">
          <h-form-item>
            <h-button type="dashed" style="width: 315px" @click="addDomain">
              <PlusOutlined />
              添加
            </h-button>
          </h-form-item>
        </h-col>
      </h-row>
    </h-form>
  </h-modal>
</template>

<style scoped lang="less">
@import '../../assets/css/main.less';

.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
