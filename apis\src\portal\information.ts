import {
    Result,
    IInformationRequest,
    IPageResponse,
    IInformationResponse,
    IInformationType
} from '@haierbusiness-front/common-libs'
import { get, post } from '../request'


export const informationApi = {
    cardList: (params: IInformationRequest): Promise<IPageResponse<IInformationResponse>> => {
        return get("/portal/api/admin-api/banner/travel-info/page", params)
    },
    list: (params: IInformationRequest): Promise<IPageResponse<IInformationResponse>> => {
        return get("/portal/api/admin-api/banner/travel-info/page", params)
    },
    get: (id: number): Promise<IInformationResponse> => {
        return get("/portal/api/admin-api/banner/travel-info/get", {
            id
        })
    },
    top: (params: IInformationRequest): Promise<IInformationResponse[]> => {
        return get("/portal/api/app-api/banner/travel-info/list", params)
    },
    save: (params: IInformationType): Promise<Result> => {
        return post('/portal/api/admin-api/banner/travel-info/create', params)
    },
    edit: (params: IInformationType): Promise<Result> => {
        return post('/portal/api/admin-api/banner/travel-info/update', params)
    },
    remove: (id: number): Promise<Result> => {
        return get('/portal/api/admin-api/banner/travel-info/delete', { id })
    },
    detail: (id: number): Promise<Result> => {
        return get('/portal/api/app-api/banner/travel-info/get', { id })
    }


}