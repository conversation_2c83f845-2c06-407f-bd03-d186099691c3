<script lang="ts" setup>
import { computed, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';

import 'animate.css';

import {
  Anchor as hAnchor,
  <PERSON><PERSON> as hButton,
  Spin as hSpin,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Checkbox as hCheckbox,
  Form as hForm,
  FormItem as hFormItem,
  Row as hRow,
  Col as hCol,
  Popconfirm as hPopconfirm,
  Upload as hUpload,
  Input as hInput,
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  VerticalAlignBottomOutlined,
  ExclamationCircleFilled,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { download, teamApi, teamListApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import relativeTime from 'dayjs/plugin/relativeTime';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';

import { CityResponse, CityItem, TCteateTeam, TTicket } from '@haierbusiness-front/common-libs';
import { fileApi } from '@haierbusiness-front/apis';

const File = new URL('@/assets/template/team_person.xlsx', import.meta.url).href;

const route = ref(getCurrentRoute());

const id = route.value?.query?.id;
const getDetail = (id: string) => {
  if (!id) {
    return;
  }

  teamListApi.teamDetail(id).then((res) => {
    teamForm.value = res;
    if (teamForm.value.beginDate && teamForm.value.endDate) {
      teamForm.value.dateRange = [teamForm.value.beginDate, teamForm.value.endDate];
    }
    if (!teamForm.value.teamDestinePlaneTicket) {
      teamForm.value.teamDestinePlaneTicket = {};
    }
    if (!teamForm.value.teamDestineHotel) {
      teamForm.value.teamDestineHotel = {};
    }
    teamForm.value.destineInfoArr = teamForm.value?.destineInfo?.split(',')?.map((item) => Number(item)) || [];
    if (teamForm.value.destineInfoArr.length > 0) {
      changeCheckbox(teamForm.value.destineInfoArr);
    }
    if (teamForm.value?.travelerFileName) {
      teamForm.value.fileList = [
        {
          name: teamForm.value?.travelerFileName,
          url: teamForm.value?.travelerFileUrl,
        },
      ];
    }
  });
};

watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);

dayjs.extend(relativeTime);

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const anchorItems = [
  {
    key: '1',
    href: '#base-info',
    title: '基本信息',
  },
  {
    key: '2',
    href: '#order',
    title: '订票/订房',
  },
  {
    key: '3',
    href: '#person',
    title: '联系人信息',
  },
];

const handleClick: AnchorProps['onClick'] = (e, link) => {
  e.preventDefault();
  console.log(link);
};

/* 表单相关 */
// 下载模板
const downloadFile = (filePath: string, name: string) => {
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = filePath;
  link.setAttribute('download', name);
  document.body.appendChild(link);
  link.click();
};

// 用户选择
const params = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});
const userNameChange = (userInfo: IUserInfo) => {
  teamForm.value.contactUserCode = userInfo?.username;
  teamForm.value.contactDeptName = userInfo?.enterpriseName || '';
  teamForm.value.contactUserPhone = userInfo?.phone;
  teamForm.value.contactUserName = userInfo?.nickName;
  teamForm.value.contactUserMail = userInfo?.email;
};

// 默认以当前时间10天后可选
const disabledDateBegin = (current: Dayjs) => {
  return current && current < dayjs(dayjs().add(10, 'day').add(1, 'minute').format());
};

const teamForm = ref<TCteateTeam>({
  beginCityCode: '',
  beginCityName: '',

  endCityCode: '',
  endCityName: '',
  destineNo: '',
  gmtCreate: '',

  // 出差类型0因公1因私
  evectionType: 0,

  // 附件地址
  travelerFileUrl: '',
  travelerFileName: '',

  // 产品类型
  destineInfoArr: [],
  teamDestinePlaneTicket: {
    voyageType: 0,
  },
  teamDestineHotel: {},

  contactDeptName: loginUser.value?.departmentName || loginUser.value?.enterpriseName,
  contactUserCode: loginUser.value?.username, //联系人工号
  contactUserName: loginUser.value?.nickName, //联系人名称
  contactUserPhone: loginUser.value?.phone, //联系人电话
  contactUserMail: loginUser.value?.email, //联系人邮箱
});

const startDatePickerValue = computed(() => dayjs(new Date()).add(10, 'day'));

const formRef = ref();
const ticketFormRef = ref();
const hotelFormRef = ref();

const labelCol = { span: 3 };
const wrapperCol = { span: 20 };

watch(
  () => teamForm.value.dateRange,
  (val: Array<string>) => {
    if (val && val.length > 1) {
      teamForm.value.beginDate = teamForm.value.dateRange[0];
      teamForm.value.endDate = teamForm.value.dateRange[1];
    } else {
      teamForm.value.beginDate = '';
      teamForm.value.endDate = '';
    }
  },
);

const rules: Record<string, Rule[]> = {
  changeReason: [{ required: true, message: '请输入变更原因', trigger: 'blur' }],
  travelUserName: [{ required: true, message: '请选择出差人', trigger: 'change' }],
  evectionType: [{ required: true, message: '请选择出差类型', trigger: 'change' }],
  beginCityName: [{ required: true, message: '请选择出发城市', trigger: 'change' }],
  endCityName: [{ required: true, message: '请选择目的城市', trigger: 'change' }],
  dateRange: [{ required: true, message: '请选择行程日期', trigger: 'change' }],
  destineInfoArr: [{ required: true, message: '至少选择一种产品', trigger: 'change' }],
  contactUserName: [{ required: true, message: '请选择联系人', trigger: 'change' }],
  contactUserPhone: [{ required: true, message: '请填写联系人电话', trigger: 'change' }],
};

const ticketRules: Record<string, Rule[]> = {
  voyageType: [{ required: true, message: '请选择航程类型', trigger: 'change' }],
  travelerNum: [{ required: true, message: '请输入出行人数', trigger: 'change' }],
  travelPriod: [{ required: true, message: '请选择出行时段', trigger: 'change' }],
  otherInfo: [{ required: true, message: '请输入其他需求', trigger: 'change' }],
};

const hotelRules: Record<string, Rule[]> = {
  doubleRoomNum: [{ required: true, message: '请输入双床房数量', trigger: 'change' }],
  kingRoomNum: [{ required: true, message: '请输入大床房数量', trigger: 'change' }],
  otherInfo: [{ required: true, message: '请输入其他需求', trigger: 'change' }],
};

const chosedBeginCity = (city: CityItem) => {
  teamForm.value.beginCityCode = city.citycode;
  teamForm.value.beginCityName = city.name;
  formRef.value.validateFields('beginCityName');
};
const chosedEndCity = (city: CityItem) => {
  teamForm.value.endCityCode = city.citycode;
  teamForm.value.endCityName = city.name;
  formRef.value.validateFields('endCityName');
};

const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 上传相关

const uploadLoading = ref(false);
const upload = (options: any) => {
  uploadLoading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);
  teamApi
    .travelerImport(formData)
    .then((it) => {
      // options.file.filePath = baseUrl + it.path;
      // options.file.fileName = options.file.name;
      teamForm.value.travelerFileUrl = baseUrl + it.path;
      teamForm.value.travelerFileName = options.file.name;
      // options.
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .catch((e) => {
      teamForm.value.fileList = [];
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 多选框
const checkboxOpitons = [
  { label: '国内机票', value: 0, disabled: false },
  { label: '国际机票', value: 1, disabled: false },
  { label: '酒店', value: 2, disabled: false },
];

const changeCheckbox = (checkedValue: any) => {
  checkboxOpitons[1].disabled = checkedValue.indexOf(0) > -1;
  checkboxOpitons[0].disabled = checkedValue.indexOf(1) > -1;
  const tempObj: TTicket = {
    planeTicketType: checkboxOpitons[0].disabled ? 1 : 0,
  };

  teamForm.value.teamDestinePlaneTicket = { ...teamForm.value.teamDestinePlaneTicket, ...tempObj };
};

/* 表单结束 */

const teamList = import.meta.env.VITE_BUSINESS_INDEX_URL;

const goToList = () => {
  const url = teamList + '#' + '/card-order/team';
  window.open(url, '_self');
};

// 查询城市列表

const timer = ref();
onMounted(async () => {
  // 两分钟自动保存
  timer.value = setInterval(() => {
    // applySubmit();
  }, 1000 * 60 * 2);
});

const submitLoading = ref<boolean>(false);

// 提交
const submit = () => {
  // 提交前表单验证
  formRef.value
    .validate()
    .then(() => {
      // teamForm?.destineInfoArr?.indexOf(2) > -1
      if (teamForm.value?.destineInfoArr?.indexOf(2) > -1) {
        hotelFormRef.value.validate().then(() => {
          submitFuc();
        });
      } else if (teamForm.value?.destineInfoArr?.indexOf(0) > -1 || teamForm.value?.destineInfoArr?.indexOf(1) > -1) {
        ticketFormRef.value.validate().then(() => {
          submitFuc();
        });
      } else {
        submitFuc();
      }
    })
    .catch(() => {});
};

// 保存接口
const submitFuc = () => {
  submitLoading.value = true;
  teamApi
    .submitTeam(teamForm.value)
    .then((res) => {
      console.log(999, res);
      hMessage.success('提交成功,2s后自动跳转列表页!');
      clearInterval(timer.value);

      // 提交后跳转流程页面
      setTimeout(() => {
        submitLoading.value = false;
        goToList();
      }, 2000);
    })
    .catch(() => {
      submitLoading.value = false;
    });
};

const lastSubmitTime = ref<string>();

const save = () => {
  if (teamForm.value.id) {
    teamApi.updateTeam(teamForm.value).then((res) => {
      console.log(999, res);
      lastSubmitTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
    });
  } else {
    teamApi.addTeam(teamForm.value).then((res) => {
      lastSubmitTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
      teamForm.value.id = res.id;
      teamForm.value.destineNo = res.destineNo;
      teamForm.value.gmtCreate = res.gmtCreate;
    });
  }
};

onUnmounted(() => {
  clearInterval(timer.value);
});

const spinning = ref<boolean>(false);
</script>

<template>
  <h-spin size="large" :spinning="spinning">
    <div class="container">
      <div class="row flex">
        <div class="change-title" v-if="teamForm.destineNo">
          <h-row class="mb-10">
            <h-col :span="6" style="color: #00000073">需求单号:</h-col>
            <h-col :span="18">{{ teamForm.destineNo }}</h-col>
          </h-row>
          <h-row class="mb-10">
            <h-col :span="6" style="color: #00000073">申请时间:</h-col>
            <h-col :span="18">{{ teamForm.gmtCreate }}</h-col>
          </h-row>
        </div>

        <div class="main-title">
          <!-- <img src="../../assets/image/trip/title.png" alt="" /> -->
          <span>团队订票订房需求</span>
        </div>
        <div class="apply-con flex">
          <h-form
            class="mt-50"
            ref="formRef"
            :model="teamForm"
            :rules="rules"
            :label-col="labelCol"
            :wrapper-col="wrapperCol"
            labelAlign="left"
          >
            <!-- 基本信息 -->

            <div class="title whole-line" id="base-info">基本信息</div>
            <h-row>
              <h-form-item name="beginCityName" class="mr-40" :label-col="{ span: 10, offset: 0 }">
                <template #label>
                  <span
                    >起止城市<a-tooltip>
                      <template #title>请选择起止城市</template>
                      <question-circleOutlined class="icon" /> </a-tooltip
                  ></span>
                </template>
                <city-chose
                  class="mr-10 city-chose-box"
                  placeholder="出发城市"
                  :value="teamForm.beginCityName"
                  :bordered="true"
                  @chosedCity="chosedBeginCity"
                ></city-chose>
              </h-form-item>
              <h-form-item name="endCityName">
                <city-chose
                  class="city-chose-box"
                  placeholder="目的城市"
                  :value="teamForm.endCityName"
                  :bordered="true"
                  @chosedCity="chosedEndCity"
                ></city-chose>
              </h-form-item>
            </h-row>

            <h-form-item name="dateRange">
              <template #label>
                <span
                  >行程日期<a-tooltip>
                    <template #title>请选择行程日期</template>
                    <question-circleOutlined class="icon" /> </a-tooltip
                ></span>
              </template>

              <a-range-picker
                style="width: 426px"
                :disabled-date="disabledDateBegin"
                :placeholder="['出发日期', '返回日期']"
                :defaultPickerValue="startDatePickerValue"
                valueFormat="YYYY-MM-DD"
                v-model:value="teamForm.dateRange"
              />
            </h-form-item>

            <!-- 
           <h-row>
              <h-form-item name="beginDate" class="mr-40" :label-col="{ span: 10 }">
                <template #label>
                  <span
                    >行程日期<a-tooltip>
                      <template #title>请选择行程日期</template>
                      <question-circleOutlined class="icon" /> </a-tooltip
                  ></span>
                </template>
                <a-date-picker
                  style="width: 200px"
                  class="mr-10"
                  valueFormat="YYYY-MM-DD"
                  placeholder="出发日期"
                  :defaultPickerValue="startDatePickerValue"
                  v-model:value="teamForm.beginDate"
                  :disabled-date="disabledDateBegin"
                  :format="dateFormat"
                />
              </h-form-item>
              <h-form-item name="endDate">
                <a-date-picker
                  style="width: 200px"
                  placeholder="返回日期"
                  :defaultPickerValue="endDatePickerValue"
                  :disabled-date="disabledDateEnd"
                  valueFormat="YYYY-MM-DD"
                  v-model:value="teamForm.endDate"
                  :format="dateFormat"
                />
              </h-form-item> 
            </h-row>

            -->

            <h-form-item name="evectionType">
              <template #label>
                <span
                  >出差类型<a-tooltip>
                    <template #title>可选因公或者因私预定</template>
                    <question-circleOutlined class="icon" /> </a-tooltip
                ></span>
              </template>
              <h-radio-group v-model:value="teamForm.evectionType">
                <h-radio :value="0">因公</h-radio>
                <h-radio :value="1">因私</h-radio>
              </h-radio-group>
            </h-form-item>

            <h-form-item ref="file" name="file">
              <template #label>
                <span
                  >出行人<a-tooltip>
                    <template #title>出行人</template>
                    <question-circleOutlined class="icon" /> </a-tooltip
                ></span>
              </template>
              <h-upload
                v-model:file-list="teamForm.fileList"
                name="file"
                accept=".xls,.xlsx"
                list-type="picture"
                :max-count="1"
                :custom-request="upload"
              >
                <h-button>
                  <upload-outlined class="font-size-14"></upload-outlined>
                  <span class="font-size-14">上传出行人信息</span>
                </h-button>
                <div class="text mt-5">
                  <span class="download-btn" @click.stop="downloadFile(File, '团队票出行人信息模板')">
                    <VerticalAlignBottomOutlined class="icon" />
                    下载模板
                  </span>

                  <span class="color-eee">,上传支持的文件格式xls、xlsx</span>
                </div>
              </h-upload>
            </h-form-item>

            <!-- 订票/订房 -->
            <div class="title whole-line" id="order">订票/订房</div>
            <h-form-item name="destineInfoArr">
              <template #label>
                <span
                  >产品类型<a-tooltip>
                    <template #title>请选择产品类型</template>
                    <question-circleOutlined class="icon" /> </a-tooltip
                ></span>
              </template>
              <div class="box-padding">
                <a-checkbox-group
                  :options="checkboxOpitons"
                  @change="changeCheckbox"
                  v-model:value="teamForm.destineInfoArr"
                >
                </a-checkbox-group>

                <!-- 选择机票 -->
                <div
                  class="com-box"
                  v-if="teamForm?.destineInfoArr?.indexOf(0) > -1 || teamForm?.destineInfoArr?.indexOf(1) > -1"
                >
                  <div class="whole-line">
                    {{ teamForm?.destineInfoArr?.indexOf(0) > -1 ? '国内机票' : '国际机票' }}
                  </div>
                  <div class="info-box flex mb-10">
                    <ExclamationCircleFilled style="color: #ff8026" class="icon mt-5 mr-10" />
                    <!-- 国内 -->
                    <span v-if="teamForm?.destineInfoArr?.indexOf(0) > -1">
                      10位成人(含)以上可申请团队，国内团队机票请至少在航班起飞72小时前确认出票。请注意：团队机票出票后可能无法退改，具体以航司申请确认为主，我们会在工作日9:00-17:30回复您！
                    </span>
                    <!-- 国际 -->
                    <span v-else>
                      10位成人(含)以上可申请团队，国际团队机票请提至少提前15个工作日提交申请，请注意：团队机票出票后可能无法退改，具体以航司申请确认为主，我们会在工作日9:00-17:30回复您！
                    </span>
                  </div>
                  <h-form
                    ref="ticketFormRef"
                    :label-col="labelCol"
                    labelAlign="left"
                    :wrapper-col="{ span: 21 }"
                    :model="teamForm.teamDestinePlaneTicket"
                    :rules="ticketRules"
                  >
                    <h-form-item label="航程类型" name="voyageType">
                      <a-select
                        ref="select"
                        required
                        v-model:value="teamForm.teamDestinePlaneTicket.voyageType"
                        style="width: 240px"
                        placeholder="选择航程类型"
                      >
                        <a-select-option :value="0">单程</a-select-option>
                        <a-select-option :value="1">往返</a-select-option>
                      </a-select>
                    </h-form-item>

                    <h-form-item label="出行人数" name="travelerNum">
                      <a-input-number
                        :precision="0"
                        placeholder="出行人数"
                        v-model:value="teamForm.teamDestinePlaneTicket.travelerNum"
                        :min="1"
                        :max="100"
                      />
                      <span class="color-eee ml-5">人 (成人)</span>
                    </h-form-item>

                    <h-form-item label="出行时段" name="travelPriod">
                      <a-select
                        ref="select"
                        v-model:value="teamForm.teamDestinePlaneTicket.travelPriod"
                        style="width: 240px"
                        placeholder="选择出行时段"
                      >
                        <a-select-option :value="0">上午</a-select-option>
                        <a-select-option :value="1">下午</a-select-option>
                      </a-select>
                    </h-form-item>

                    <h-form-item label="其他需求" name="otherInfo">
                      <a-textarea
                        placeholder="其他需求"
                        v-model:value="teamForm.teamDestinePlaneTicket.otherInfo"
                        show-count
                        style="width: 100%"
                        :maxlength="500"
                      />
                    </h-form-item>
                  </h-form>
                </div>

                <!-- 选择酒店 -->
                <div class="com-box" v-if="teamForm?.destineInfoArr?.indexOf(2) > -1">
                  <div class="whole-line">酒店</div>
                  <div class="info-box flex mb-10">
                    <ExclamationCircleFilled style="color: #ff8026" class="icon mt-5 mr-10" />
                    <span>
                      10间/夜以上(含)可申请团队价或连续长期入驻的可申请长包房价格。团队房/长包房客户提前7个工作日提交申请。我们会在工作日9:00-17:30回复，具体结果以申请为准。
                    </span>
                  </div>
                  <h-form
                    ref="hotelFormRef"
                    :model="teamForm.teamDestineHotel"
                    :rules="hotelRules"
                    :label-col="labelCol"
                    labelAlign="left"
                    :wrapper-col="{ span: 21 }"
                  >
                    <h-form-item label="大床房" name="kingRoomNum">
                      <a-input-number
                        :precision="0"
                        placeholder="大床房"
                        v-model:value="teamForm.teamDestineHotel.kingRoomNum"
                        :min="0"
                        :max="200"
                      />
                      <span class="color-eee ml-5">间</span>
                    </h-form-item>

                    <h-form-item label="双床房" name="doubleRoomNum">
                      <a-input-number
                        :precision="0"
                        placeholder="双床房"
                        v-model:value="teamForm.teamDestineHotel.doubleRoomNum"
                        :min="0"
                        :max="200"
                      />
                      <span class="color-eee ml-5">间</span>
                    </h-form-item>

                    <h-form-item label="其他需求" name="otherInfo">
                      <a-textarea
                        placeholder="其他需求"
                        style="width: 100%"
                        v-model:value="teamForm.teamDestineHotel.otherInfo"
                        show-count
                        :maxlength="500"
                      />
                    </h-form-item>
                  </h-form>
                </div>
              </div>
            </h-form-item>

            <!-- 联系人信息 -->
            <div class="title whole-line" id="person">联系人信息</div>

            <h-form-item ref="name" name="contactUserName">
              <template #label>
                <span
                  >联系人<a-tooltip>
                    <template #title>选择联系人</template>
                    <question-circleOutlined class="icon" /> </a-tooltip
                ></span>
              </template>
              <user-select
                :value="`${teamForm.contactUserName || '未知'}/${teamForm.contactUserCode || '未知工号'}[${
                  teamForm.contactDeptName || '未知部门'
                }]`"
                placeholder="选择出差人"
                :params="params"
                @change="(userInfo: IUserInfo) =>  userNameChange(userInfo)"
                class="whole-line font-size-14"
              />
            </h-form-item>

            <h-form-item name="contactUserPhone">
              <template #label>
                <span
                  >电话号码<a-tooltip>
                    <template #title>请填写真实电话号码</template>
                    <question-circleOutlined class="icon" /> </a-tooltip
                ></span>
              </template>
              <a-input-number
                style="width: 100%"
                :controls="false"
                :precision="0"
                v-model:value="teamForm.contactUserPhone"
                placeholder="请输入联系人电话号码"
              />
            </h-form-item>

            <h-form-item name="contactUserMail">
              <template #label>
                <span
                  >邮箱<a-tooltip>
                    <template #title>请填写真实邮箱</template>
                    <question-circleOutlined class="icon" /> </a-tooltip
                ></span>
              </template>
              <h-input v-model:value="teamForm.contactUserMail" placeholder="请输入联系人邮箱" />
            </h-form-item>
          </h-form>
        </div>
        <div class="anchor-con flex">
          <h-anchor :items="anchorItems" @click="handleClick" />
        </div>
      </div>
      <a-affix :offset-bottom="0" id="affix-bottom" class="affix-bottom">
        <div class="save-box flex">
          <div class="box-center">
            <div class="save-box-left font-size-14"></div>
            <div class="save-box-right flex">
              <div class="auto-text font-size-14 mr-20 font-color" v-if="lastSubmitTime">
                <check-circle-two-tone two-tone-color="#52c41a" />
                {{ dayjs(lastSubmitTime).fromNow() }}自动保存
              </div>
              <div class="save-btns">
                <!-- <h-popconfirm title="确定取消编辑并列表页吗?" ok-text="确定" cancel-text="取消" @confirm="goToList">
                  
                </h-popconfirm> -->
                <h-button size="small" class="my-button mr-10" @click="goToList">取消</h-button>
                <h-button size="small" class="my-button mr-10" @click="save">保存</h-button>

                <h-button size="small" class="my-button" :loading="submitLoading" type="primary" @click="submit"
                  >提交</h-button
                >
              </div>
            </div>
          </div>
        </div>
      </a-affix>
    </div>
  </h-spin>
</template>

<style lang="less" scoped>
@import url(./components/trip.less);

.change-title {
  position: absolute;
  right: 0;
  top: -80px;
  width: 300px;

  .ant-col-6,
  .ant-col-18 {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #000000d9;
  }
}
.mt-5 {
  margin-top: 5px;
}

.ml-5 {
  margin-left: 5px;
}
.color-eee {
  color: #a8a7a7;
}

.com-box {
  background: #f5f5f5;
  padding: 20px;
  margin-top: 20px;

  .whole-line {
    height: 40px;
    font-weight: 600;
  }
  .info-box {
    display: flex;
    align-items: flex-start;
    padding: 10px 20px;
    border: 1px solid #ffdb5c;
    background: #fffbd8;
  }
  :deep(.ant-form-item-control-input-content) {
    display: flex;
    align-items: center;
  }
}
.title {
  height: 60px;
}
.download-btn {
  color: #408cff;
  cursor: pointer;
}

:deep(.mr-10) {
  margin-right: 10px;
}
.box-padding {
  padding: 5px 0;
}
</style>
