<!-- 满意度评价 -->
<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Modal as hModal,
  Rate as hRate,
  Input as hInput,
} from 'ant-design-vue';

import { satisfactionRatingApi } from '@haierbusiness-front/apis';
import { computed, ref, onMounted } from 'vue';
import router from '../../router'
import satisfiedIcon from '@/assets/image/satisfactionRating/satisfied.png'
import acceptableIcon from '@/assets/image/satisfactionRating/acceptable.png'
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
const { loginUser } = storeToRefs(applicationStore(globalPinia))
console.log(loginUser,"const { loginUser } = storeToRefs(applicationStore(globalPinia))");

const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router
})

// 满意度评价功能
const ratingVisible = ref(false);
const detailMode = ref(false);
const accommodation = ref(0);
const feedback = ref('');

// 显示评价弹框
const showRatingModal = () => {
  ratingVisible.value = true;
  detailMode.value = false;
  accommodation.value = 0;
  feedback.value = '';
};

// 处理简单评价点击
const handleRatingClick = (score: number) => {
  if (score === 5) {
    submitRating(score);
  }
};

// 显示详细评分模式
const showDetailMode = () => {
  detailMode.value = true;
};

// 提交评分
const submitRating = (score?: number) => {
  const evaluateScore = score || accommodation.value;

  // 打印传递的参数
  const params = {
    miceId: 26,
    evaluateState: evaluateScore,
    description: feedback.value,
  };
  console.log('提交参数:', params);

  // 使用Promise处理评价提交
  return new Promise((resolve, reject) => {
    satisfactionRatingApi.save(params as any)
      .then(res => {
        console.log('提交成功，响应:', res);
        ratingVisible.value = false;
        detailMode.value = false;
        accommodation.value = 0;
        feedback.value = '';

        resolve(res);
      })
      .catch(error => {
        console.error('提交评价失败:', error);
        reject(error);
      });
  });
};

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
            <h-button type="primary" @click="showRatingModal">
              评价
            </h-button>
          </h-col>
        </h-row>
      </h-col>
    </h-row>

    <!-- 满意度评价弹框 -->
    <h-modal v-model:visible="ratingVisible" :footer="null" :closable="true" :mask-closable="false"
      :width="detailMode ? 450 : 350" centered>
      <!-- 简单评价模式 -->
      <div v-if="!detailMode" class="rating-simple">
        <div class="rating-title">期待您的评价！您的意见对我们非常重要。</div>
        <div class="rating-buttons">
          <div class="rating-button" @click="handleRatingClick(5)">
            <div class="icon-wrapper">
              <img :src="satisfiedIcon" alt="很满意" class="rating-icon" />
            </div>
            <div class="text">很满意</div>
          </div>
          <div class="rating-button" @click="showDetailMode">
            <div class="icon-wrapper">
              <img :src="acceptableIcon" alt="还可以" class="rating-icon" />
            </div>
            <div class="text">还可以</div>
          </div>
        </div>
      </div>

      <!-- 详细评分模式 -->
      <div v-else class="rating-detail">
        <div class="rating-title">期待您的评价！您的意见对我们非常重要。</div>
        <div class="rating-buttons">
          <div class="rating-button" @click="handleRatingClick(5)">
            <div class="emoji-wrapper">
              <img :src="satisfiedIcon" alt="">
            </div>
            <div class="text">很满意</div>
          </div>
          <div class="rating-button active">
            <div class="emoji-wrapper">
              <img :src="satisfiedIcon" alt="">
            </div>
            <div class="text">还可以</div>
          </div>
        </div>
        <div class="divider"></div>
        <div class="rating-form">
          <div class="rating-item">
            <div class="label">整体评价</div>
            <div class="stars">
              <h-rate v-model:value="accommodation" />
              <span class="score">{{ accommodation }}分</span>
            </div>
          </div>
          <div class="feedback-input" v-if="accommodation < 5">
            <hInput.TextArea v-model:value="feedback" placeholder="很抱歉数据未达预期，请详细描述您不满意的地方..." :rows="4" allow-clear
              :maxlength="500" showCount />
          </div>
          <div class="rating-actions">
            <h-button type="primary" @click="submitRating()" :disabled="accommodation < 5 && !feedback">提交</h-button>
          </div>
        </div>
      </div>
    </h-modal>

  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

/* 评价弹框样式 */
.rating-simple,
.rating-detail {
  padding: 20px 10px;
  text-align: center;
}

.rating-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 20px;
}

.rating-buttons {
  display: flex;
  justify-content: space-around;
  margin-top: 30px;
}

.rating-button {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;

  &.active {
    .icon-wrapper {
      box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
    }

    .text {
      font-weight: bold;
    }
  }
}

.emoji-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  font-size: 48px;
  background-color: #f5f5f5;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.rating-button:hover .emoji-wrapper {
  background-color: #e6f7ff;
  border-color: #40a9ff;
}

.rating-button.active .emoji-wrapper {
  background-color: #e6f7ff;
  border-color: #1890ff;
  box-shadow: 0 0 8px rgba(24, 144, 255, 0.3);
}

.text {
  font-size: 14px;
}

.divider {
  border-top: 1px dashed #ddd;
  margin: 20px 0;
  width: 100%;
}

.rating-form {
  margin-top: 10px;
}

.rating-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.label {
  width: 60px;
  text-align: right;
  margin-right: 15px;
}

.stars {
  flex: 1;
  display: flex;
  align-items: center;
}

.score {
  margin-left: 10px;
  color: #ff6b6b;
  font-weight: 500;
}

.feedback-input {
  margin: 20px 0;
}

.rating-actions {
  margin-top: 20px;
  text-align: center;
}
</style>
