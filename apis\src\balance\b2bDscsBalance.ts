import { IPageResponse, IDscsB2bListRequest,IHaierAccountBillInfo, IDscsB2bAccountRequest, IDscsB2bDetailsRequest, IHabDetailInfo, IDscsB2bConfirmRequest, IDscsB2bRevokeConfirmRequest, IDscsB2bConfirmBalanceOrderBudgetRequest, IDscsB2bCancelRequest, IDscsB2bMarkReadRequest } from '@haierbusiness-front/common-libs'
import { download, get, post } from '../request'

export const b2bDscsBalanceApi = {


     /**
     * 进行汇总
     */
     account: (params: IDscsB2bAccountRequest): Promise<void> => {
        return post('balance/api/b2b/dscs/account', params)
    },

    /**
     * 确认汇总
     */
    confirm: (params: IDscsB2bConfirmRequest): Promise<void> => {
        return post('balance/api/b2b/dscs/confirm', params)
    },

     /**
     * 确认并释放全部预算,全部确认成功则通知商户通
     */
     confirmBalanceOrderBudget: (params: IDscsB2bConfirmBalanceOrderBudgetRequest): Promise<IHabDetailInfo[]> => {
        return post('balance/api/b2b/dscs/revoke/confirm/budget', params)
    },

    /**
     * 撤回已确认状态
     */
    revokeConfirm: (params: IDscsB2bRevokeConfirmRequest): Promise<void> => {
        return post('balance/api/b2b/dscs/revoke/confirm', params)
    },

    /**
     * 取消汇总
     */
    cancel: (params: IDscsB2bCancelRequest): Promise<void> => {
        return post('balance/api/b2b/dscs/cancel', params)
    },
    
    /**
     * 获取结算列表
     */
    list: (params: IDscsB2bListRequest): Promise<IPageResponse<IHaierAccountBillInfo>> => {
        return get('balance/api/b2b/dscs/list', params)
    },

    /**
     * 导出结算列表
     */
    exportList: (params: IDscsB2bListRequest): Promise<void> => {
        return download('balance/api/b2b/dscs/list/export', params)
    },

    /**
     * 获取结算状态数量
     */
    stateAccount: (params: IDscsB2bListRequest): Promise<any> => {
        return get('balance/api/b2b/dscs/state/account', params)
    },

    /**
     * 获取预算释放失败,cvp通知失败数量
     */
    cvpErrorAccount: (params: IDscsB2bListRequest): Promise<number> => {
        return get('balance/api/b2b/dscs/cvp/error/account', params)
    },

    /**
     * 获取结算详情
     */
    details: (params: IDscsB2bDetailsRequest): Promise<IPageResponse<IHabDetailInfo>> => {
        return get('balance/api/b2b/dscs/details', params)
    },

    /**
     * 导出结算详情
     */
    detailsExport: (params: IDscsB2bDetailsRequest): Promise<void> => {
        return download('balance/api/b2b/dscs/details/export', params)
    },

    /**
     * 已读CVP错误消息
     */
    markRead: (params: IDscsB2bMarkReadRequest): Promise<void> => {
        return post('balance/api/b2b/dscs/mark/read', params)
    },

    /**
     * 获取推送CVP失败且未读的汇总单
     */
    pushCvpErrorAccount: (params: IDscsB2bListRequest): Promise<number> => {
        return get('balance/api/b2b/dscs/push/cvp/error/account', params)
    },
}