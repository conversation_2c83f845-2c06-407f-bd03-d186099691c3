<template>
  <div class="body">
    <div class="header">
      <div class="title">差旅资讯</div>
    </div>
    <div class="list-con" v-if="informations.length > 0">
      <div class="notice-con" v-for="(item, index) in informations" :key="index" @click="gotoUrl(item)">
        <h-card class="notice" :body-style="{ padding: '0px' }">
          <div class="notice-item">
            <div class="left">
              <div class="notice-title">
                {{ item.infoTitle }}
              </div>
              <div class="notice-tip">{{ item.infoAuthor }}&ensp;&ensp;{{ item.infoDate.replace(/-/gi, "/") }}</div>
            </div>
            <div class="right">
              <h-image :src="item.imgUrl" width="72" height="56" :preview="false" />
            </div>
          </div>
        </h-card>
      </div>
      <h-spin :spinning="isLoading" style="margin-top: 8px" />
    </div>
    <div v-else class="list-con">
      <div class="no-data">
        <img :src="image" class="img" />
        <p class="no">暂无差旅资讯</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  Card as hCard,
  Image as hImage,
  Spin as hSpin,
  message,
} from 'ant-design-vue';
import { onBeforeUnmount, onMounted, ref, watch } from "vue";
import { informationApi } from '@haierbusiness-front/apis';
import type { IPageRequest, InformationType } from "@haierbusiness-front/common-libs";
import image from '@/assets/image/noData.png'
import router from '../../router'

const currentRouter = ref()

onMounted(async () => {
    currentRouter.value = await router
})


const pagination = ref<IPageRequest>({
  pageSize: 20,
  pageNum: 1,
  showStatus: 1
})

const informations = ref<Array<InformationType>>([])
const total = ref<number>(0)
const isLoading = ref<boolean>(false)

const fetchData = () => {
  if (isLoading.value) return
  isLoading.value = true
  informationApi.cardList(pagination.value).then(res => {

    informations.value = informations.value?.concat(...res.records)
    total.value = res.total
    isLoading.value = false
    if (res.records!.length == 0) {
      window.removeEventListener('scroll', handleScroll)
    }

  })
}

const handleScroll = () => {
  // 滚动到底部，再加载的处理事件
  const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
  const clientHeight = document.documentElement.clientHeight;
  const scrollHeight = document.documentElement.scrollHeight;
  if (scrollTop + clientHeight >= scrollHeight) {

    if (informations.value.length % 10 > 0) return
    pagination.value.pageNum++
    fetchData();
  }
}

const isMobile = () => {
  const flag = navigator.userAgent.match(
    /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
  );
  return flag;
}

const checkDisUrlTip = (url) => {
  if (url !== "") {
    const reg = /^((https|http|ftp|rtsp|mms)?:\/\/)[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/;
    if (reg.test(url)) {
      return true;
    } else {
      return false;
    }
  }
}

const gotoUrl = (item: InformationType) => {
  if(item.jumpLinkPc || item.jumpLinkApp) {
    if (isMobile()) {
      if (!checkDisUrlTip(item.jumpLinkApp)) {
        message.error('链接不合法！')
        return
      }
      window.open(item.jumpLinkApp)
    } else {
      if (!checkDisUrlTip(item.jumpLinkPc)) {
        message.error('链接不合法！')
        return
      }
      window.open(item.jumpLinkPc)
    }
  } else {
    if(!item.id) {
      message.error('未对应信息！')
      return
    }
    const thisUrl = currentRouter.value.resolve({
      path: '/travel/infoDetail',
    })
    window.open(thisUrl.href + '?id=' + item.id)
  }
  

}

onMounted(() => {
  fetchData()
  window.addEventListener('scroll', handleScroll)
})

onBeforeUnmount(() => {
  window.removeEventListener("scroll", handleScroll)
})

</script>

<style lang="less" scoped>
.body {
  overflow-y: hidden;

  .header {
    width: 100%;
    height: 44px;
    background: #fff;
    border-bottom: 1px solid #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 12px;

    .title {
      font-size: 18px;
      font-weight: 400;
      color: rgba(0, 0, 0, .85);
      line-height: 22px;
    }
  }

  .list-con {
    display: flex;
    flex-direction: column;
    width: 100%;
    min-height: calc(100vh - 44px);
    background-color: #f6f6f6;
    padding: 0px 12px 8px 12px;
    overflow-y: scroll;

    .notice-con {
      padding-top: 8px;
    }

    .notice {
      border-radius: 4px;
      height: 82px;

      .notice-item {
        display: flex;
        height: 82px;
        background: #ffffff;
        margin-bottom: 0px;
        cursor: pointer;
        padding: 0px 12px;
        flex-direction: row;
      }

      .left {
        flex-grow: 1;
        height: 100%;
        overflow: hidden;
        padding-right: 18px;
      }

      .right {
        width: 72px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 0px;
      }

      .notice-title {
        font-size: 14px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        line-height: 20px;
        letter-spacing: 1px;
        margin: 12px 0 8px !important;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        height: 40px;
      }

      .notice-tip {
        font-size: 12px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.25);
        width: 100%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-bottom: 0px;
      }
    }

    .no-data {
      width: 100%;
      min-height: calc(100vh - 44px);
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      .img {
        width: 176px;
        height: 135px;
      }

      .no {
        margin-top: 16px;
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, .45);
        line-height: 17px;
      }
    }
  }
}
</style>
