<template>
  <div class="wyyContainer">
    <div class="search"></div>
    <h-form
      ref="from"
      :model="searchKey"
      @finish="onFilterChange"
      style="width: 100%"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <h-row :gutter="24">
        <h-col :span="8">
          <h-form-item has-feedback label="订单号" name="orderCode">
            <h-input
              v-model:value="searchKey.keyWord"
              placeholder="商品名称/商品编号/订单号"
              allow-clear
            />
          </h-form-item>
        </h-col>
        <h-col :span="8">
          <h-form-item has-feedback label="收货人" name="guestCompany">
            <h-input
              v-model:value="searchKey.recipients"
              placeholder="收货人"
              allow-clear
            />
          </h-form-item>
        </h-col>
        <h-col :span="8">
          <h-form-item has-feedback label="收货人电话" name="guestCompany">
            <h-input
              v-model:value="searchKey.receivingPhone"
              placeholder="收货人电话"
              allow-clear
            />
          </h-form-item>
        </h-col>
        <h-col :span="8">
          <h-form-item has-feedback label="收货地址" name="guestCompany">
            <h-input
              v-model:value="searchKey.address"
              placeholder="收货地址"
              allow-clear
            />
          </h-form-item>
        </h-col>
        <h-col :span="8">
          <h-form-item has-feedback label="支付类型" name="payType">
            <h-select
              v-model:value="searchKey.paymentType"
              placeholder="支付类型"
              allow-clear
            >
            <h-select-option v-for="(item, index) in payTypes" :key="index" :value="item.value">{{ item.label }}</h-select-option>
            </h-select>
          </h-form-item>
        </h-col>
        <h-col :span="8">
          <h-form-item has-feedback label="支付组合" name="paymentGroupType">
            <h-select
              v-model:value="searchKey.paymentGroupType"
              placeholder="支付组合"
              allow-clear
            >
            <h-select-option value="1">个人（除虚拟卡外，对公外其他所有支付方式）</h-select-option>
            <h-select-option value="2">对公（对公支付）</h-select-option>
            <h-select-option value="3">预付费（虚拟卡）</h-select-option>
            </h-select>
          </h-form-item>
        </h-col>
        <h-col :span="8">
          <h-form-item has-feedback label="申请售后" name="isAfterSale">
            <h-select
              v-model:value="searchKey.isAfterSale"
              placeholder="申请售后"
              allow-clear
            >
            <h-select-option value="1">是</h-select-option>
            <h-select-option value="0">否</h-select-option>
            </h-select>
          </h-form-item>
        </h-col>
        <h-col :span="16">
          <h-form-item
            has-feedback
            label="下单日期"
            name="creationDate"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 20 }"
          >
            <h-range-picker
              v-model:value="searchKey.creationDate"
              value-format="YYYY-MM-DD"
              @change="onCreateTimeChange"
              style="width: 100%"
            />
          </h-form-item>
        </h-col>

        <h-col :span="8">
          <h-form-item has-feedback label="摘要" name="guestCompany">
            <h-input
              v-model:value="searchKey.summaryInfo"
              placeholder="摘要"
              allow-clear
            />
          </h-form-item>
        </h-col>
        
        <h-col :span="8">
          <h-form-item has-feedback label="订单状态" name="state">
            <h-select
              v-model:value="searchKey.state"
              placeholder="订单状态"
              allow-clear
            >
              <h-select-option
                v-for="(item, index) in orderState"
                :key="index"
                :value="item.value"
                >{{ item.label }}</h-select-option
              >
            </h-select>
          </h-form-item>
        </h-col>
        
      </h-row>
      <h-row justify="center">
        <div class="flexCon">
          <h-space>
            <h-button @click="handleReset">重置</h-button>
            <h-button
              type="primary"
              html-type="submit"
              >查询</h-button
            >
          </h-space>
        </div>
      </h-row>
    </h-form>

    <h-spin :spinning="loading">
      <div class="list" v-if="list && list.length > 0">
        <div
          class="order-container"
          v-for="(order, index) in list"
          :key="index"
        >
          <div class="order-header">
            <div class="order-header-left">
              <span>订单号：</span>
              <span>{{ order.code }} </span>
              <template v-if="order.budgetId">
                <span style="margin-left: 10px;">预算单号： </span>
                <span>{{ order.budgetId }} </span>
              </template>
              <span style="margin-left: 10px;">京东单号： </span>
              <span>{{ order.providerOrderId }} </span>
              <span style="margin-left: 10px;">收货人： </span>
              <span>{{ order.deliveryAddressDo.name }} </span>
              <span>{{ order.gmtCreate ?  order.gmtCreate.substring(0,10) : '' }} </span>
            </div>
            <div class="order-header-right">
              <!-- <h-tag :color="orderStateTagColorMap[order.approvalState + '']">{{
                RestaurantOrderApprovalStateEnum[order.approvalState + ""]
              }}</h-tag> -->
              <!-- <h-tag :color="paymentStateTagColorMap[order.state + '']">{{
                RestaurantOrderStateEnum[order.state + ""]
              }}</h-tag> -->
            </div>
          </div>
          <div class="order-body p-20">
            <div class="flex pb-10 border-bottom-gray">
              <div class="flex-3 text-gray">商品信息</div>
              <div class="flex-1 text-gray">单价</div>
              <div class="flex-1 text-gray">数量</div>
              <div class="flex-1 text-gray">小计</div>
              <div class="flex-1 text-gray">状态</div>
            </div>
            <template v-for="(product, orderIndex) in order.subOrders" :key="orderIndex">
              <div class="flex item-center pt-15 pb-15 border-bottom-gray" v-for="(item, thisIndex) in product.products" :key="thisIndex">
              <div class="flex item-center flex-3">
                <div class="img mr-10">
                  <img
                    :src="item.imagePath"
                  />
                </div>
                <div>
                  <div class="font-14">{{ item.name }}</div>
                  <div class="font-12 text-gray mt-5" v-html="item.wareQD"></div>
                  <div class="font-12 text-gray mt-5">
                    规格编号： {{ item.skuId }}
                  </div>
                </div>
              </div>
              <div class="flex-1">¥{{ item.salesPrice*1/100 }}</div>
              <div class="flex-1">{{ item.num }}</div>
              <div class="flex-1">{{ (item.salesPrice*1/100) * item.num }}</div>
              <div class="flex-1"><h-tag :color="orderStatusColor[product.isState]">{{orderStatusEnum[product.isState+'']}}</h-tag></div>
              
            </div>
            </template>
            
            <div class="pt-15 flex flex-direction">
              <div style="width: 150px">实付： ¥{{ floatDiv(order.amount*1, 100).toFixed(2) }}</div>
              <div style="width: 150px">邮费： ¥{{ order.freight*1/100 }}</div>
              <div style="width: 150px">商品合计： ¥{{ floatAdd(floatDiv(order.amount*1, 100), order.freight*1/100) }}</div>
            </div>
          </div>
          <div class="order-footer">
            <h-button type="primary" size="small" @click="handle(order.code)">去处理</h-button>
          </div>
        </div>
        
      </div>

      <div class="page" v-show="pagination.total && pagination.total > 0">
        <h-pagination
          v-model:current="pagination.current"
          show-size-changer
          show-quick-jumper
          :total="pagination.total"
          @change="onPageChange"
        />
      </div>

      <div class="empty" v-if="!list || list.length === 0">
        <h-empty />
      </div>
      
    </h-spin>
  </div>
</template>

<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Tag as hTag,
  Spin as hSpin,
  Empty as hEmpty,
  Space as hSpace,
  Pagination as hPagination,
  message,
} from 'ant-design-vue';
import { useOrderSearch } from "@haierbusiness-front/composables";
import { computed, onMounted, reactive, ref, watch } from "vue";
import type { JdFilter, JdType,  } from "@haierbusiness-front/common-libs";
  import { PaymentTypeEnum,
  orderStatusEnum,
  orderStatusColor, } from "@haierbusiness-front/common-libs";
import { jdApi } from "@haierbusiness-front/apis";
import { getEnumOptions, floatDiv, floatAdd } from "@haierbusiness-front/utils";
// import { useOrderStore } from "@/store";
import { useRoute } from 'vue-router'
import dayjs, { Dayjs } from 'dayjs';

const route = useRoute()
// const orderStore = useOrderStore();

const labelCol = {
  span: 8,
};
const wrapperCol = {
  span: 16,
};

const businesstravel = import.meta.env.VITE_BUSINESSTRAVEL_URL

const handle = (code: string) => {
  const url = businesstravel + '/mall/#/personal/orderJd?cocode=' + code
  window.open(url)
}

// 支付类型
const payTypes = computed(() => {
  console.log(getEnumOptions(PaymentTypeEnum, true));
  return getEnumOptions(PaymentTypeEnum, true);
});

// 订单状态
const orderState = [
    {value:'0',label:'已取消'},
    {value:'10,15',label:'待付款'},
    {value:'20,30,40',label:'待收货'},
    {value:'50',label:'已完成'},
  ];

const searchKey = reactive<JdFilter>({
  state: '',
  pageNum: 1,
  pageSize: 10,
});

// const userInfo = computed(() => orderStore.userInfo)

const {
  data,
  fetchData,
  pagination,
  loading,
  onTimeChange,
  from,
  onFilterChange,
} = useOrderSearch<JdType, JdFilter>(jdApi, searchKey);



const list = computed(() => {
  if(data.value && data.value.length > 0) {
    data.value.forEach((order) => {
      transOrder_pc(order)
    })
    return data.value
  } else
    return []
})

const transOrder_pc = (order: any) => {
  order.productNum = 0;
  order.totalAmount = floatAdd(order.amount, order.freight); //计算总价
  //子订单
  let mallOrders = order.mallOrders;
  //是否拆单
  order.isSplitOrder = mallOrders.length > 1;

  mallOrders.forEach((subOrder: any) => {
    subOrder.mallOrderGoods.forEach((product: any) => {
      let mallGoods = product.mallGoods;
      // tranOrderProduct_pc(mallGoods);
      Object.assign(product, mallGoods);
      product.imagePath = 'http://img13.360buyimg.com/n1/' + product.imagePath;
      order.productNum++;
      delete product.mallGoods;
    })
    subOrder.products = subOrder.mallOrderGoods;
    delete subOrder.mallOrderGoods;
  });

  order.subOrders = mallOrders;
  delete order.mallOrders;
}

const handleReset = () => {
  from.value && from.value.resetFields()
  if(!searchKey.creationDate || searchKey.creationDate.length != 2) {
      searchKey.creationStartDate = ''
      searchKey.creationEndDate = ''
  }
  onFilterChange()
}


const onCreateTimeChange = (dateRange: [string, string] | [Dayjs, Dayjs]) => {
  if(dateRange && dateRange.length === 2) {
      searchKey.creationStartDate = dateRange[0].toString()
      searchKey.creationEndDate = dateRange[1].toString()
  } else {
      searchKey.creationStartDate = ''
      searchKey.creationEndDate = ''
  }
};

const onPageChange = (page: number, pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = page
  fetchData()
}

// watch(userInfo, (newValue) => {
//   if(newValue.id) {
//     const code = route.query.coCode
//     if(code) {
//       searchKey.keyWord = code.toString()
//     }
//     onFilterChange()
//   }
// }, { immediate: true, deep: true })

</script>
  
<style scoped lang="less">

.empty {
  margin-top: 50px;
  border: 1px solid #f0f0f0;
  padding: 42px 24px 50px;
}

.wyyContainer {
  display: flex;
  width: 100%;
  flex-direction: column;
  .search {
    display: flex;
    width: 100%;
  }

  .list {
    display: flex;
    margin-top: 20px;
    width: 100%;
    flex-direction: column;
  
    .order-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      margin-bottom: 20px;
  
      .order-header {
        width: 100%;
        background-color: #f5f5f5;
        height: 43px;
        line-height: 43px;
        padding-left: 20px;
        color: #333;
        font-size: 12px;
        border: 1px solid #eaeaea;
        display: flex;
        justify-content: space-between;
  
        .order-header-left {
          display: flex;
        }
        .order-header-right {
          display: flex;
          align-items: center;
        }
      }
      .order-body {
        //   display: flex;
        //   flex-direction: row;
        width: 100%;
        border: 1px solid #eaeaea;
        border-top: 0;
        .img {
          img {
            width: 100px;
            height: 100px;
          }
        }
      }
    }
  }
}

.order-footer {
    width: 100%;
    background-color: #f5f5f5;
    height: 43px;
    line-height: 43px;
    padding-right: 8px;
    color: #333;
    font-size: 12px;
    border-left: 1px solid #eaeaea;
    border-right: 1px solid #eaeaea;
    border-bottom: 1px solid #eaeaea;
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
}



.page {
  display: flex;
  width: 100%;
  flex-direction: row-reverse;
  margin-bottom: 20px;
}

.flex {
  display: flex !important;
}

.flex-direction {
  flex-direction: row-reverse !important;
}

.item-center {
  align-items: center !important;
}
.p-20 {
  padding: 20px !important;
}
.pt-15 {
  padding-top: 15px !important;
}
.pb-10 {
  padding-bottom: 10px !important;
}
.pb-15 {
  padding-bottom: 10px !important;
}
.border-bottom-gray {
  border-bottom: 1px dashed #ccc;
}
.mr-10 {
  margin-right: 10px;
}
.ml-10 {
  margin-left: 10px;
}
.mt-5 {
  margin-top: 5px;
}
.font-12 {
  font-size: 12px;
}
.font-14 {
  font-size: 14px;
}
.text-gray {
  color: rgba(0, 0, 0, 0.65);
}
.flex-1 {
  flex: 1;
}
.flex-3 {
  flex: 3;
}
</style>
  