/**
 * 公告通知状态枚举
 */
export enum AnnouncementNoticeState {
  /** 正常 */
  NORMAL = '正常',
  /** 隐藏 */
  HIDDEN = '隐藏',
}

/**
 * 公告通知状态选项
 */
export const announcementNoticeStateOptions = [
  { label: '正常', value: AnnouncementNoticeState.NORMAL },
  { label: '隐藏', value: AnnouncementNoticeState.HIDDEN },
];

/**
 * 公告通知内容形式枚举
 */
export enum AnnouncementContentForm {
  /** 文本 */
  TEXT = 1,
  /** 链接 */
  LINK = 2,
}

/**
 * 公告通知内容形式选项
 */
export const announcementContentFormOptions = [
  { label: '文本', value: AnnouncementContentForm.TEXT },
  { label: '链接', value: AnnouncementContentForm.LINK },
];

/**
 * 公告通知作用范围枚举
 */
export enum AnnouncementEffectScope {
  /** 服务商 */
  MERCHANT = 1,
  /** 用户 */
  USER = 2,
  /** 顾问 */
  COUNSELOR = 4,
  /** 管理员 */
  MANAGEMENT = 8,
}

/**
 * 公告通知作用范围选项
 */
export const announcementEffectScopeOptions = [
  { label: '服务商', value: AnnouncementEffectScope.MERCHANT },
  { label: '用户', value: AnnouncementEffectScope.USER },
  { label: '顾问', value: AnnouncementEffectScope.COUNSELOR },
];
