// 会场用途

type keys = 'HOLD' | 'BUILD' | 'WITHDRAWAL' | 'HOLD_BUILD' | 'BUILD_WITHDRAWAL' | 'HOLD_BUILD_WITHDRAWAL';

export const UsagePurposeTypeConstant = {
  HOLD: { code: 1, desc: '会议举行' },
  BUILD: { code: 2, desc: '布展搭建' },
  // WITHDRAWAL: { code: 4, desc: '会议撤场' },

  // HOLD_BUILD: { code: 3, desc: '布展搭建+会议举行' },
  // BUILD_WITHDRAWAL: { code: 5, desc: '会议举行+会议撤场' },
  // HOLD_BUILD_WITHDRAWAL: { code: 7, desc: '布展搭建+会议举行+会议撤场' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in UsagePurposeTypeConstant) {
      const item = UsagePurposeTypeConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(UsagePurposeTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return UsagePurposeTypeConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
