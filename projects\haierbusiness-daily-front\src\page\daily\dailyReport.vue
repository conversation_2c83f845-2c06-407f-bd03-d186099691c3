<script setup lang="ts">
import {
  FormInstance,
  Modal,
  Tag as hTag,
  Spin as hSpin,
  Space as hSpace,
  Popconfirm as hPopconfirm,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  Row as hRow,
  Table as hTable,
  Form as hForm,
  FormItem as hFormItem,
  RadioButton as hRadioButton,
  CollapsePanel as hCollapsePanel,
  Collapse as hCollapse,
  RadioGroup as hRadioGroup,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import {
  EyeOutlined,
  FormOutlined,
  SolutionOutlined,
  PlusOutlined,
  SyncOutlined,
  SearchOutlined,
  SaveOutlined,
  SubnodeOutlined,
  SafetyOutlined,
  RetweetOutlined,
  FundOutlined,
} from '@ant-design/icons-vue';
import {
  AnnualPlanStateConstant,
  AnnualPlanTypeStateConstant,
  DailyReportEvaluateStateConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
  QuantifierStateConstant,
  UserGroupSystemConstant,
  DailyReportStateConstant,
  EvaluateControlConstant,
} from '@haierbusiness-front/common-libs';
import { checkUserGroup, getCurrentRouter, resolveParam, routerParam } from '@haierbusiness-front/utils';
import { ref, createVNode, PropType, watch, computed } from 'vue';
import { dailyMonthPlanApi, dailyPersonApi, dailyReportApi } from '@haierbusiness-front/apis/src/daily';
import {
  IAnnualPlanTypeListRequest,
  IDailyPersonalListRequestDTO,
  IDailyPersonalResponse,
  IDailyReportDetailRequestDTO,
  IDailyReportDetailResponseDTO,
  IDailyReportListResponseDTO,
  IDailyReportSaveRequestDTO,
  IMonthPlanDetailRequestDTO,
  IMonthPlanDetailResponseDTO,
} from '@haierbusiness-front/common-libs/src/daily';
import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import detail from '../annual/plan/detail.vue';
import dailyReportConferences from './dailyReportConferences.vue';
import dailyReportEvaluates from './dailyReportEvaluates.vue';
import dailyReportQuantifierIssueItems from './dailyReportQuantifierIssueItems.vue';
import detailReportProject from './detailReportProject.vue';
import dailyReportPlatformEvaluate from './dailyReportPlatformEvaluate.vue';
import { message } from 'ant-design-vue';
import { use } from 'echarts/core';
import dayjs, { Dayjs } from 'dayjs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';

const { loginUser } = storeToRefs(applicationStore(globalPinia));
const router = getCurrentRouter();
const prop = defineProps({
  query: Object,
});
watch(
  () => prop!!.query!!.data,
  (n: any, o: any) => {
    if (n === o) {
      return;
    }
    formParam.value = {
      dailyReportProjects: [],
      dailyReportConferences: [],
      dailyReportQuantifierIssueItems: [],
      dailyReportEvaluates: [],
    };
    dailyReportDetailParam.value = { id: data.value.id };
    dailyReportDetailData.value = {
      platformEvaluate: { evaluateRemark: '', evaluateAmount: 0 },
    };
    dailyPersonParam.value = {
      deptCode: loginUser?.value?.departmentCode,
    };
    dailyPersonData.value = [];

    monthDetailParam.value = {
      code: data.value.mpCode,
      evaluateControl: EvaluateControlConstant.ALL.code,
    };
    monthDetailData.value = {};

    dailyReportDetailRun();
    dailyPersonApiRun();
    monthDetailRun();
  },
  {
    deep: true,
  },
);
const data = computed(() => <IDailyReportListResponseDTO>resolveParam(prop.query?.data));

const dailyReportDetailParam = ref<IDailyReportDetailRequestDTO>({
  id: data.value.id,
});
const dailyReportDetailData = ref<IDailyReportDetailResponseDTO>({
  platformEvaluate: { evaluateRemark: '', evaluateAmount: 0 },
});
const dailyReportDetailLoading = ref(false);
const dailyReportDetailRun = () => {
  dailyReportDetailLoading.value = true;
  dailyReportApi
    .detail(dailyReportDetailParam.value)
    .then((it) => {
      dailyReportDetailData.value = it;
      if (!dailyReportDetailData.value.platformEvaluate) {
        dailyReportDetailData.value.platformEvaluate = { evaluateRemark: '', evaluateAmount: 0 };
      }
      convertDataToParams(dailyReportDetailData.value);
    })
    .finally(() => {
      dailyReportDetailLoading.value = false;
    });
};
{
  dailyReportDetailRun();
}

const convertDataToParams = (data: IDailyReportDetailResponseDTO) => {
  formParam.value.id = data.id;
  data.dailyReportProjects?.forEach((it) => {
    formParam.value.dailyReportProjects?.push({
      id: it.id,
      apiCode: it.apiCode,
      mpiCode: it.mpiCode,
      content: it.content,
      principalUsercode: it.principalUsercode,
      principalUsername: it.principalUsername,
      principalDeptCode: it.principalDeptCode,
      principalDeptName: it.principalDeptName
    });
  });

  data.dailyReportConferences?.forEach((it) => {
    formParam.value.dailyReportConferences?.push({
      id: it.id,
      executionTime: it.executionTime,
      content: it.content,
      principalUsercode: it.principalUsercode,
      principalUsername: it.principalUsername,
      principalDeptCode: it.principalDeptCode,
      principalDeptName: it.principalDeptName,
    });
  });

  data.dailyReportEvaluates?.forEach((it) => {
    formParam.value.dailyReportEvaluates?.push({
      id: it.id,
      evaluateRemark: it.evaluateRemark,
      evaluateAmount: it.evaluateAmount,
      dispose: it.dispose,
      disposeTime: it.disposeTime,
      state: it.state,
      deptCode: it.deptCode,
      deptName: it.deptName,
      principalUsercode: it.principalUsercode,
      principalUsername: it.principalUsername,
      principalDeptCode: it.principalDeptCode,
      principalDeptName: it.principalDeptName,
    });
  });

  data.dailyReportQuantifierIssues?.forEach((item) => {
    item.dailyReportQuantifierIssueItems?.forEach((it) => {
      formParam.value.dailyReportQuantifierIssueItems?.push({
        id: it.id,
        drqiId: it.drqiId,
        state: item.quantifierState,
        reason: item.noQuantifierReason,
        issue: it.issue,
        dispose: it.dispose,
        disposeDate: it.disposeDate,
        quantifierUsercode: it.quantifierUsercode,
        quantifierUsername: it.quantifierUsername,
        quantifierDeptCode: it.quantifierDeptCode,
        quantifierDeptName: it.quantifierDeptName,
        principalUsercode: it.principalUsercode,
        principalUsername: it.principalUsername,
        principalDeptCode: it.principalDeptCode,
        principalDeptName: it.principalDeptName,
        managerUsercode: it.managerUsercode,
        managerUsername: it.managerUsername,
        managerDeptCode: it.managerDeptCode,
        managerDeptName: it.managerDeptName,
      });
    });
  });
  data.dailyReportQuantifierIssues?.forEach((it) => {
    if (!it.dailyReportQuantifierIssueItems?.length || it.dailyReportQuantifierIssueItems?.length < 1) {
      formParam.value.dailyReportQuantifierIssueItems?.push({
        drqiId: it.id,
        state: QuantifierStateConstant.WAIT.code,
        quantifierUsercode: it.quantifierUsercode,
        quantifierUsername: it.quantifierUsername,
        quantifierDeptCode: it.quantifierDeptCode,
        quantifierDeptName: it.quantifierDeptName,
      });
    }
  });
};
const monthDetailParam = ref<IMonthPlanDetailRequestDTO>({
  code: data.value.mpCode,
  evaluateControl: EvaluateControlConstant.ALL.code,
});
const monthDetailData = ref<IMonthPlanDetailResponseDTO>({});
const monthDetailDataLoading = ref(false);
const monthDetailRun = () => {
  monthDetailDataLoading.value = true;
  dailyMonthPlanApi
    .detail(monthDetailParam.value)
    .then((it) => {
      monthDetailData.value = it;
    })
    .finally(() => {
      monthDetailDataLoading.value = false;
    });
};
{
  monthDetailRun();
}

const dailyPersonLoading = ref(false);
const dailyPersonData = ref<IDailyPersonalResponse[]>([]);
const dailyPersonParam = ref<IDailyPersonalListRequestDTO>({
  deptCode: loginUser?.value?.departmentCode,
});
const dailyPersonApiRun = () => {
  dailyPersonLoading.value = true;
  dailyPersonApi
    .list(dailyPersonParam.value)
    .then((it) => {
      dailyPersonData.value = it.records || [];
    })
    .finally(() => {
      dailyPersonLoading.value = false;
    });
};
{
  dailyPersonApiRun();
}

// 表单
const formRef = ref<FormInstance>({} as FormInstance);
const formParam = ref<IDailyReportSaveRequestDTO>({
  dailyReportProjects: [],
  dailyReportConferences: [],
  dailyReportQuantifierIssueItems: [],
  dailyReportEvaluates: [],
});

const dailyReportSaveLoading = ref(false);

const dailyReportSaveApiRun = () => {
  // 如果不是小微主, 只可提交自己的日量化问题
  if (!checkUserGroup(UserGroupSystemConstant.DAILY_MICRO.groupId)) {
    formParam.value.dailyReportQuantifierIssueItems = formParam.value.dailyReportQuantifierIssueItems?.filter((it) => {
      return it.quantifierUsercode === loginUser.value?.username;
    });
  }
  dailyReportSaveLoading.value = true;
  dailyReportApi
    .save(formParam.value)
    .then((it) => {
      if (formParam.value.action === 1) {
        message.success('日清保存成功!');
      } else {
        message.success('日清提交成功!');
      }
      setTimeout(() => {
        router.push({ path: '/daily/daily/list', query: { timestamp: routerParam(dayjs().toDate().getTime()) } });
      }, 1000);
    })
    .finally(() => {
      dailyReportSaveLoading.value = false;
    });
};
const saveForm = () => {
  formParam.value.action = 1;
  formRef.value.validate().then(() => {
    dailyReportSaveApiRun();
  });
};
const submitForm = () => {
  formParam.value.action = 2;
  formRef.value.validate().then(() => {
    dailyReportSaveApiRun();
  });
};

const platformEvaluateLoading = ref(false);

const platformEvaluateApiRun = () => {
  dailyReportSaveLoading.value = true;
  formParam.value.action = 3;
  formParam.value.dailyReportProjects = [];
  formParam.value.dailyReportConferences = [];
  formParam.value.dailyReportQuantifierIssueItems = [];
  dailyReportApi
    .save(formParam.value)
    .then((it) => {
      platformEvaluateLoading.value = true;
      dailyReportApi
        .platformEvaluate({
          evaluateAmount: dailyReportDetailData?.value?.platformEvaluate?.evaluateAmount,
          evaluateRemark: dailyReportDetailData?.value?.platformEvaluate?.evaluateRemark,
          id: data.value.id,
        })
        .then((it) => {
          message.success('日清评价成功!');
          setTimeout(() => {
            router.push({
              path: '/daily/daily/planform-daily/list',
              query: { timestamp: routerParam(dayjs().toDate().getTime()) },
            });
          }, 1000);
        })
        .finally(() => {
          platformEvaluateLoading.value = false;
        });
    })
    .finally(() => {
      dailyReportSaveLoading.value = false;
    });
};

const submitPlatformEvaluateForm = () => {
  platformEvaluateApiRun();
};
const cancelForm = () => {
  router.go(-1);
};
const collapseActiveKey = ref([1]);

const showSave = () => {
  const quantifierUsers = formParam.value.dailyReportQuantifierIssueItems?.filter((it) => {
    return it.quantifierUsercode === loginUser.value?.username && it.state === QuantifierStateConstant.COMPLETED.code;
  });
  return (
    (!quantifierUsers || quantifierUsers?.length <= 0) &&
    DailyReportStateConstant.WAIT.code === data.value.state &&
    !checkUserGroup(UserGroupSystemConstant.DAILY_MICRO.groupId)
  );
};
const showSubmit = () => {
  return (
    checkUserGroup(UserGroupSystemConstant.DAILY_MICRO.groupId) &&
    DailyReportStateConstant.WAIT.code === data.value.state
  );
};

const showPlatform = () => {
  return (
    checkUserGroup(UserGroupSystemConstant.DAILY_PLATFORM_MANAGER.groupId) &&
    DailyReportStateConstant.RUNNING.code === data.value.state
  );
};
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <div
      v-if="dailyReportSaveLoading || platformEvaluateLoading"
      style="
        text-align: center;
        height: 100vh;
        width: 90vw;
        position: fixed;
        z-index: 100;
        opacity: 0.5;
        background-color: #ffff;
        padding-top: 35vh;
      "
    >
      <h-spin :size="'large'" />
    </div>
    <h-form ref="formRef" name="dynamic_form_item" :model="formParam" :scrollToFirstError="true">
      <h-row :align="'middle'" :gutter="24" style="margin: 12px 0">
        <h-col :span="24" style="text-align: center">
          <div style="font-size: 20px; font-weight: 700">
            {{ data?.year }} 年{{ data?.month }} 月{{ data?.day }} 日 - {{ data.deptName }}小微日清
          </div>
        </h-col>
        <h-col :span="24">
          <detail :query="{ code: data.apCode, month: data.month, hideTitle: true, fold: true }" />
        </h-col>
        <h-col :span="24">
          <div style="padding: 0px 10px 0px 10px">
            <h-collapse
              v-model:activeKey="collapseActiveKey"
              :bordered="false"
              style="background-color: white"
              :collapsible="'icon'"
            >
              <h-collapse-panel key="1">
                <template #header>
                  <div style="display: flex">
                    <div style="height: 25px; width: 10px; background-color: rgb(0, 115, 229)" />
                    <div style="font-size: 16px; font-weight: 600; margin-left: 10px">日清明细</div>
                  </div>
                </template>
                <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%; margin-bottom: 20px"></div>
                <detail-report-project
                  :data="formParam.dailyReportProjects"
                  :month-data="monthDetailData"
                  :daily-person="dailyPersonData"
                  :daily-report="data"
                />
                <daily-report-conferences
                  :data="formParam.dailyReportConferences"
                  :month-data="monthDetailData"
                  :daily-person="dailyPersonData"
                  :daily-report="data"
                />
                <daily-report-quantifier-issue-items
                  :data="formParam.dailyReportQuantifierIssueItems"
                  :report-detail="dailyReportDetailData"
                  :month-data="monthDetailData"
                  :daily-person="dailyPersonData"
                  :daily-report="data"
                />
                <daily-report-evaluates
                  v-if="
                    checkUserGroup(UserGroupSystemConstant.DAILY_MICRO.groupId) ||
                    checkUserGroup(UserGroupSystemConstant.DAILY_PLATFORM_MANAGER.groupId)
                  "
                  :daily-report="data"
                  :data="formParam.dailyReportEvaluates"
                  :month-data="monthDetailData"
                  :daily-person="dailyPersonData"
                />
                <daily-report-platform-evaluate
                  v-if="checkUserGroup(UserGroupSystemConstant.DAILY_PLATFORM_MANAGER.groupId) ||
                 (checkUserGroup(UserGroupSystemConstant.DAILY_MICRO.groupId) && data.state === DailyReportStateConstant.FINISH.code) "
                  :data="dailyReportDetailData.platformEvaluate"
                  :daily-report="data"
                  :month-data="monthDetailData"
                  :daily-person="dailyPersonData"
                />
              </h-collapse-panel>
            </h-collapse>
          </div>
        </h-col>
      </h-row>
      <h-row :align="'middle'">
        <h-col :span="24" style="text-align: center">
          <h-space>
            <h-form-item v-if="showSave()">
              <h-popconfirm :title="'确认保存?'" ok-text="确认" cancel-text="取消" @confirm="saveForm">
                <h-button type="primary" style="width: 100%"> 提交(d)</h-button>
              </h-popconfirm>
            </h-form-item>
            <h-form-item v-if="showSubmit()">
              <h-popconfirm
                :title="'提交后将至平台审核，确认提交?'"
                ok-text="确认"
                cancel-text="取消"
                @confirm="submitForm"
              >
                <h-button type="primary" style="width: 100%"> 提交(m)</h-button>
              </h-popconfirm>
            </h-form-item>
            <h-form-item v-if="showPlatform()">
              <h-popconfirm
                :title="'确认提交?'"
                ok-text="确认"
                cancel-text="取消"
                @confirm="submitPlatformEvaluateForm"
              >
                <h-button type="primary" style="width: 100%"> 提交(p)</h-button>
              </h-popconfirm>
            </h-form-item>
            <h-form-item>
              <h-button style="width: 100%" @click="cancelForm"> 取消</h-button>
            </h-form-item>
          </h-space>
        </h-col>
      </h-row>
    </h-form>
  </div>
</template>

<style scoped lang="less">
.subscript {
  position: absolute;
  width: 23%;
  height: 8%;
  right: 2.5%;
  color: white;
  font-size: 12px;
  text-align: center;
  vertical-align: middle;
  border-bottom-left-radius: 5px;
  border-top-right-radius: 5px;
}

.card {
  overflow-y: hidden;
  overflow-x: hidden;
  text-align: center;
  height: calc(7vh);
  min-height: 80px;

  .content {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    height: 90%;
  }

  .footer {
    padding-top: 2px;
    text-align: center;
    font-weight: 700;
    font-size: 16px;
    border-top: 0.5px solid #eaeaea;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    background-color: rgb(247, 249, 250);
    height: 10%;
  }
}

.card-mask {
  border-radius: 5px;
  cursor: pointer;
  position: absolute;
  height: calc(7vh);
  min-height: 80px;
  width: 95%;
  z-index: 10;
}
</style>
