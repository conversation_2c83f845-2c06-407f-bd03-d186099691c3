<template>
  <div class="wallet">
    <div class="wallet-nav mt-20 mb-10">
      <van-cell icon="scan" title="扫一扫" is-link>
        <template #value>
          <a class="wallet-label" @click="ihaierAuth">扫码充值</a>
        </template>
      </van-cell>
    </div>
    <van-form @submit="onSubmit">
      <van-cell-group>
        <van-field
          v-model="expansion.voucherAccount"
          name="代金券卡号"
          label="代金券卡号"
          @update:model-value="clearMoney()"
          placeholder="代金券卡号"
          :rules="[{ required: true, message: '请填写代金券卡号' }]"
        />
        <van-field
          v-model="expansion.voucherPassword"
          name="代金券密码"
          label="代金券密码"
          :type="showPassword ? 'text' : 'password'"
          @update:model-value="clearMoney()"
          placeholder="代金券密码"
          :rules="[{ required: true, message: '请填写代金券密码' }]"
        >
          <template #right-icon>
            <van-icon v-if="!showPassword" name="closed-eye" @click="changeShow()" />
            <van-icon v-else name="eye-o" @click="changeShow()" /> </template
        ></van-field>
        <van-field
          v-model="expansion.voucherAmount"
          name="代金券金额"
          label="代金券金额"
          placeholder="代金券金额"
          :rules="[{ required: true, message: '请填写代金券金额' }]"
          disabled
        >
          <template #button>
            <van-button size="small" type="primary" @click="getcardMoney()">获取金额</van-button>
          </template></van-field
        >
      </van-cell-group>
      <div class="wallet-nav mt-20">
        <van-button round block type="primary" native-type="submit"> 充值 </van-button>
      </div>
    </van-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { payPhoneApi } from '@haierbusiness-front/apis';
import { showToast } from 'vant';

import { getCurrentRouter } from '@haierbusiness-front/utils';

const appId = import.meta.env.VITE_APP_ID
const sdkUrl = import.meta.env.VITE_SDK_URL

const router = getCurrentRouter();
const route = router.currentRoute.value;

const onClickLeft = () => {
  router.push({
    path: '/wallet',
  });
};

const expansion = ref({
  voucherAccount: '',
  voucherPassword: '',
  voucherAmount: '',
});
const showPassword = ref(false);
const onSubmit = async (values) => {
  let params = {
    accountNo: route.query.accountNo,
    rechargeType: '2',
    remark: '代金券充值',
    expansion: expansion.value,
  };
  try {
    const res = await payPhoneApi.recharge(params);
    showToast(res.message);

    router.push({
      path: '/wallet',
    });
    console.log(11111);
  } catch (error) {
    console.log(error);
  }
};
const changeShow = () => {
  showPassword.value = !showPassword.value;
};
const ihaierAuth = async () => {
  const ihaierSDK = await window.IHaierRuntime.getRuntime({
    appId: appId, //您在准备阶段获取的appId，不填默认临时appId，
    authUrl: sdkUrl, //鉴权服务地址，不填默认http://127.0.0.1:13025/ihaier-runtime-auth-server
  });

  console.log(ihaierSDK, 'ihaierSDK=>>>>>>>>>>>>>>>>>>>');
  ihaierSDK
    .scanCode()
    .then(async (res) => {
      console.log(res, 'res======>>>>>>>>>>>>>>>');
      const response = await payPhoneApi.getVoucherInfo({
        // checkOffCode: res,
        checkOffCode: res.data.data,
      });
      showToast('扫码成功');
      var voucherArray = res.data.data.split(':');
      expansion.value.voucherAccount = response.cardNum;
      expansion.value.voucherAmount = response.faceValue;
      expansion.value.voucherPassword = voucherArray[1];
    })
    .catch((err) => {
      console.log(`getSystemInfo fail: ${JSON.stringify(err)}`);
    });
}
const getMoney = async (code: string) => {
  try {
    const response = await payPhoneApi.getVoucherInfo({
      // checkOffCode: res,
      checkOffCode: code,
    });
    expansion.value.voucherAccount = response.cardNum;
    expansion.value.voucherAmount = response.faceValue;
  } catch (error) {
    console.log(error, 'error');
  }
};
const getcardMoney = () => {
  if (!expansion.value.voucherAccount) {
    showToast('请输入卡号');
    return;
  }
  if (!expansion.value.voucherPassword) {
    showToast('请输入密码');
    return;
  }
  const code = `${expansion.value.voucherAccount}:${expansion.value.voucherPassword}`;
  getMoney(code);
};
const clearMoney = () => {
  expansion.value.voucherAmount = '';
};
</script>

<style lang="scss" scoped>
.wallet {
  background: #f3f3f3;
  padding: 0 10px;
  &-label {
    color: #1989fa;
    font-size: 14px;
  }
  &-qrcode {
    display: flex;
    justify-content: center;
    width: 100%;
  }
  &-nav {
    // margin: 0 15px 10px;
    margin-bottom: 15px;
    border-radius: 10px;
    overflow: hidden;
  }
  &-bg {
    background: url('../../assets/image/blurry-gradient-haikei.png');
    background-size: 100% 100%;
    height: 200px;
    padding-top: 20px;
    // border-bottom-left-radius: 20%;
    // border-bottom-right-radius: 20%;
  }
  &-code {
    background: #fff;
    height: 55vh;
  }

  &-code1 {
    display: flex;
    justify-content: center;
    margin-top: 25px;
    margin-bottom: 20px;
  }
  &-user {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
    color: #fff;
    img {
      width: 70px;
      height: 70px;
      margin-right: 10px;
    }
  }
  .van-cell {
    align-items: center;
  }
}
.user {
  &-poster {
    width: 100%;
    height: 53vw;
    display: block;
  }

  &-group {
    margin-bottom: 15px;
  }

  &-links {
    padding: 15px 0;
    font-size: 12px;
    text-align: center;
    background-color: #fff;

    .van-icon {
      display: block;
      font-size: 24px;
    }
  }
}
.mt-20 {
  margin-top: 20px;
}
.mb-10 {
  margin-bottom: 10px;
}
.pb-10 {
  padding-bottom: 10px;
}
</style>
