<script setup lang="ts">
import {
  Button,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Upload as hUpload,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  message,
} from 'ant-design-vue';
import { reactive, ref, watchEffect } from 'vue';
import { Form, TableProps } from 'ant-design-vue';
import { EditModal, TagModal } from './components';
import { useRouter } from 'vue-router';
import { MerchantBusiness, ServiceProvider } from '@haierbusiness-front/common-libs';
import { serviceProviderApi } from '@haierbusiness-front/apis';
import { useServiceProviderListStore } from './store';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { log } from 'node:console';

// 不变的数据
const columns: ColumnType[] = [
  {
    title: '商户企业名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    ellipsis: true,
  },
  {
    title: '统一社会信用代码',
    dataIndex: 'unifiedSocialCreditCode',
    key: 'unifiedSocialCreditCode',
    width: '200px',
    ellipsis: true,
  },
  {
    title: '国内结算V码',
    dataIndex: 'code',
    key: 'code',
    width: '120px',
  },
  // {
  //   title: '海外结算码',
  //   dataIndex: 'xxxxxxxx',
  //   key: 'xxxxxxxx',
  //   width: 120,
  // },
  {
    title: '数据来源',
    dataIndex: 'resource',
    key: 'resource',
    width: "120px",
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    key: 'createName',
    width: "80px",
  },
  {
    title: '引入时间',
    dataIndex: 'gmtCreate',
    key: 'gmtCreate',
    width: "180px",
  },
  {
    title: '操作',
    dataIndex: 'options',
    key: 'options',
    width: "280px",
    fixed: 'right',
  },
];


const gmtCreate = ref('')
// 变化的数据
const router = useRouter();
const useForm = Form.useForm;
let originSearchValues = reactive({ name: '' });
const searchValues = reactive({ name: '' })
const pagination = reactive({ current: 1, pageSize: 10 });
const editModalOpen = ref(false);
const tagModalOpen = ref(false);
const { resetFields } = useForm(searchValues);
const store = useServiceProviderListStore();

// 业务数据
const businessList = ref<MerchantBusiness[]>([]);
const businessListTotal = ref(0);

const getBusinessList = async () => {
  try {
    const res = await serviceProviderApi.listProcessing({
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      ...originSearchValues
    });
    businessList.value = res.records as MerchantBusiness[];
    businessListTotal.value = res.total || 0;
    
  } catch (e) {
    console.log(e);
  }
}

// 方法
const handleSearch = () => {
  originSearchValues = { ...searchValues };
  pagination.current = 1;
  pagination.pageSize = 10;
  getBusinessList();
};

const handleReset = () => {
  resetFields();
  handleSearch();
};

const findById = (list: any[], id: number | undefined) => list.filter(item => item.id == id);

const handleEdit = (record: MerchantBusiness) => {
  console.log(record,"record");

  store.businessDetail = {
    id: record.id,
    name: record.name,
    code: record.code,
    enterpriseCode: record.enterpriseCode,
    enterpriseId: record.enterpriseId,
    enterpriseName: record.enterpriseName, // 直接使用接口返回的企业名称
    unifiedSocialCreditCode: record.unifiedSocialCreditCode,
  };
  console.log(store.businessDetail,"store.businessDetail");

  editModalOpen.value = true;
}

const handleSetTag = (record: MerchantBusiness) => {
  store.businessDetail = {
    id: record.id,
    name: record.name,
  };
  tagModalOpen.value = true;
}


const handleTableChange: TableProps['onChange'] = ({ pageSize, current }) => {
  current && (pagination.current = current);
  pageSize && (pagination.pageSize = pageSize);
};

// 钩子
watchEffect(() => {
  getBusinessList();
})
</script>

<template>
  <!-- 服务商列表页 -->
  <div class="service-provider-list">
    <h-row class="search-row">
      <h-col :span="2" class="label-right modify">
        <label for="merchantName">商户企业名称：</label>
      </h-col>
      <h-col :span="4" class="top">
        <h-input id="merchantName" v-model:value="searchValues.name" placeholder="请输入" allow-clear :maxlength="200" />
      </h-col>
    </h-row>

    <h-row class="search-row">
      <h-col :span="24" style="text-align: right;">
        <Button type="primary" @click="handleSearch" style="margin-right: 10px;">查询</Button>
        <Button @click="handleReset">重置</Button>
      </h-col>
    </h-row>
    <div class="toolbar">
      <Button type="primary" @click="editModalOpen = true">新增</Button>
    </div>
    <Table :dataSource="businessList" :columns="columns" :pagination="{ ...pagination, total: businessListTotal }" @change="handleTableChange">
      <template #bodyCell="{ column, record }">
        <a-space v-if="column.key === 'options'">
          <a @click="router.push(`/merchant/serviceProvider/detail?id=${record.id}`)">详情</a>
          <a v-if="record.resource === 'LOCALCREATE'" @click="handleEdit(record)">编辑</a>
          <a @click="handleSetTag(record)">设置标签</a>
        </a-space>
        <div v-if="column.key === 'resource'">
          {{ record.resource === 'LOCALCREATE' ? '手动创建' : record.resource }}
        </div>
      </template>
    </Table>
    <edit-modal v-model="editModalOpen" :getDataList="getBusinessList"
      :businessDetail="store.businessDetail"></edit-modal>
    <tag-modal v-model="tagModalOpen" :businessDetail="store.businessDetail"></tag-modal>
  </div>
</template>

<style scoped lang="less">
.service-provider-list {
  background-color: #fff;
  padding: 10px;

  .search {
    margin-bottom: 10px;

    .search-buttons {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }

  .toolbar {
    margin: 10px 0;
  }
}

.search-row {
  align-items: middle;
  padding: 10px 10px 0px 10px;
}

.button-row {
  align-items: middle;
  padding: 10px 10px 0px 10px;
}

.label-right {
  text-align: right;
  padding-right: 10px;
}

.top {
  margin-top: -8px;
}

.margin-right {
  margin-right: 10px;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.full-width {
  width: 100%;
}

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.form-container {
  .form-item {
    margin-bottom: 24px;

    .label {
      margin-bottom: 8px;
    }
  }
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 8px;
}

.input-with-unit {
  display: flex;
  align-items: center;

  .unit {
    margin-left: 8px;
    color: rgba(0, 0, 0, 0.85);
  }
}

.operator-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.operator-buttons :deep(.ant-btn) {
  padding: 0 4px;
  font-size: 14px;
}

.modify {
  display: block;
  flex: 0 0 10.333333%;
  max-width: 10.333333%;
}
:deep(.ant-pagination){
  justify-content: center !important;
}
</style>
