import { IPageResponse, Result, ReportFilter } from '@haierbusiness-front/common-libs'
import { get, post, downloadPost, download } from '../../request'
import { queryDataList, reportToExport } from "../board"
export const reportApi = {
    /**
     * 查询园区生活
     */
    list: async (params: ReportFilter): Promise<IPageResponse<any>> => {
        const res: any = await queryDataList(params);
        return {
            total: res.pageInfo?.total,
            records: res.rows
        }
    },

    save: (params: ReportFilter): Promise<Result> => {
        return post('admin-api/banner/park-life/create', params)
    },

    edit: (params: ReportFilter): Promise<Result> => {
        return post('admin-api/banner/park-life/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('admin-api/banner/park-life/delete', { id })
    },

    download: (filter: any): Promise<any> => {
        const res: any = reportToExport(filter);
        return res
    },
    /**
     * 导出结算列表
     */
    exportList: (params: any): Promise<void> => {
        return downloadPost('data/api/bi/common/downReport', params)
    },
    /**
   * 导出结算列表
   */
    exportPPTList: (params: any): Promise<void> => {
        return downloadPost('data/api/generate/ppt', params)
    },
    /**
      * 导出结算单位模板
      */
    exportFggygList: (params: any): Promise<void> => {
        return download('data/api/datart/permission-approve/getSettlementUnitImportTemplate', params)
    },

    /**
     * 导出预算部门模板
     */
    exportYsList: (params: any): Promise<void> => {
        return download('data/api/datart/permission-approve/getBudgetDepartmentImportTemplate', params)
    },


    // 获取审批通过的权限 权限类型 3:领域 4:平台 5:产业线
    // moduleType	模块类型,当系统类型为中台时 必传 1: 报表  2:看板 
    // type	类型 当moduleType 为模板时必传
    getPowerByApprove: (name: string, permissionType: number, moduleType?:number,type?:string ): Promise<any> => {
        return get("data/api/bi/account/getPowerByApprove", {
            name,
            pageNum: 1,
            pageSize: 20,
            permissionType,
            moduleType,
            type
        });
    },
    // 根据类型获取所有可申请权限信息 权限类型 3:领域 4:平台 5:产业线
    getPowerAllByType: (name: string, permissionType: number): Promise<any> => {
        return get("data/api/bi/account/getPowerAllByType", {
            name,
            pageNum: 1,
            pageSize: 20,
            permissionType
        });
    },

    // 根据id获取某个权限申请(验签)
    postByIdAndVerificatSignature: (
        params: any
    ): Promise<any> => {
        return get("data/api/datart/permission-approve/getMyPermissionList", {
            ...params,
        });
    },
    //结算单位查询
    querySettleCompany: (params: { type: string, moduleType: number, keyword: string }): Promise<any> => {
        return get("data/api/bi/account/getbykey", {
            ...params,
            pageNo: 1,
            pageSize: 20,
        });
    },

    //结算单位查询 (申请页面用)
    queryApplySettleCompany: (keyword: string): Promise<any> => {
        return get("data/api/bi/account/getAccountCompanyAll", {
            keyword,
            pageNo: 1,
            pageSize: 20,
        });
    },


    //预算部门
    querySettleDepartment: (params: { type: string, moduleType: number, keyword: string }): Promise<any> => {
        return get("data/api/bi/account/getBudgetDepartmentList", {
            ...params,
            pageNo: 1,
            pageSize: 20,
        });
    },
    //预算部门
    queryApplySettleDepartment: (keyword: string): Promise<any> => {
        return get("data/api/bi/account/getBudgetDepartmentListAll", {
            keyword,
            pageNo: 1,
            pageSize: 20,
        });
    },
    // 领域看板
    queryAreaList: (params: { type: string, moduleType: number, keyword: string }): Promise<any> => {
        return get("data/api/bi/account/getAreaByPower", {
            ...params,
            pageNo: 1,
            pageSize: 20,
        });
    },
    // 领域 
    queryApplySettleArea: (keyword: string): Promise<any> => {
        return get("data/api/bi/account/getAreaAll", {
            keyword,
            pageNo: 1,
            pageSize: 20,
        });
    },
    // 领域带权限
    queryApplySettleAreaPower: (keyword: string): Promise<any> => {
        return get("data/api/bi/account/getAreaAll", {
            keyword,
            type:2,
            pageNo: 1,
            pageSize: 20,
        });
    },
    //预算类型
    queryBudgetTypeList: (params: { type: string, moduleType: number, keyword: string }): Promise<any> => {
        return get("data/api/bi/account/getBudgetTypeList", {
            ...params,
            pageNo: 1,
            pageSize: 20,
        });
    },

}
