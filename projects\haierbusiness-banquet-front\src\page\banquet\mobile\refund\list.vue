<template>

  <div class="order-list" style=" min-height: 100vh;">
  

    <van-list v-model:loading="orderLoading" :finished="orderFinished" :finished-text="orderList.length ? '没有更多了' : ''"
      @load="loadorderList" class="van-list-box">
      <div class="order-item flex" v-for="(item, index) in orderList" :key="index">
        <div class="order-item-right flex ">
         
          <div class="order-content">
            <van-cell-group round style="background-color: rgba(0,0,0,0);">

              <van-cell > 
                <template #title>
                  <div class="flex align-items-center justify-content-between ">
                    <div class="flex align-items-center mr-10">
                      <img v-if="item?.sceneType" class="icon-size mr-5" :src="item?.sceneType == 1 ? yqIcon : item?.sceneType == 2 ? wmIcon : ''" />
                      <span class="font-size-10">{{ item?.refundOrderCode }}</span>
                    </div>
                    
                  </div>
                </template>
              </van-cell>

              <van-cell > 
                <template #title>
                  <div class="flex  flex-column" style="position:relative">
                    <div>签单人:{{`${item?.signerName}(${item?.signerCode})`}}</div>
                    <div>关联单号:{{item?.orderBookingCode}}</div>
                    <div>退款餐厅:{{item?.restaurantName}}</div>
                    <div>退款金额:{{`${item.refundAmount || 0}元`}}</div>
                    <div>处理时间:{{item?.dealTime}}</div>
                  </div>
                </template>
              </van-cell>
              
              <!-- <van-cell :title="`订单编号：${item.applicationOrderCode}`" class="order-title-cell"/>
              <van-cell title="签单人" :value="`${item.signerName}(${item.signerCode})`"/>
              <van-cell title="关联单号" :value="item.orderBookingCode"/>
              <van-cell title="退款餐厅" :value="item.restaurantName"/>
              <van-cell title="退款金额" :value="`${item.refundAmount || 0}元`"/>
              <van-cell title="处理时间" :value="item.dealTime"/> -->
            </van-cell-group>
          </div>
        </div>
      </div>

    </van-list>

    <van-empty v-if="!orderLoading && orderList.length == 0" description="暂无数据" />

    
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onActivated } from 'vue';

import {
  IUserListRequest,
  ROrderPayMentStateEnum,
  ROrderPayMentStateEnumColor,
  ROrderApprovalStateEnum,
  ROrderApprovalStateEnumColor,
  ROrderStateEnum,
  ROrderStateEnumColor,
  BanquetStatusEnumMobile,
  BanquetStateTagColorMap,
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { banquetRefundApi } from '@haierbusiness-front/apis';
import { IRefundReq,IRefundRes } from '@haierbusiness-front/common-libs';
import type { Ref } from 'vue';

import dayjs from 'dayjs';

import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { showConfirmDialog, showSuccessToast } from 'vant';
const store = applicationStore();

const { loginUser } = storeToRefs(store);

const router = getCurrentRouter();
const route = ref();






const goBack = () => {
  router.back(-1);
};


// 订单列表
const orderLoading = ref<boolean>(false);
const orderFinished = ref<boolean>(false);
const orderList = ref<Array<IRefundRes>>([])
const orderTotal = ref<number>(0);
const defaultParams = ref<IRefundReq>({
  pageNum: 0,
  pageSize: 20,
  signerCode:loginUser.value?.username

});

const wmIcon = new URL('@/assets/image/banquet/order/order-wm.png', import.meta.url).href
const yqIcon = new URL('@/assets/image/banquet/order/order-yq.png', import.meta.url).href


const loadorderList = () => {
  defaultParams.value.pageNum++;
  banquetRefundApi.list(defaultParams.value).then((res) => {
    // 加载状态结束
    orderLoading.value = false;
    orderList.value = [...orderList.value, ...res.records];
    orderTotal.value = res.total ?? 0;
    // 数据全部加载完成
    if (orderList.value.length >= orderTotal.value) {
      orderFinished.value = true;
    }
  });
};

onActivated(() => {
  route.value = getCurrentRoute()
  const type = route.value?.query?.reload
  if (type == '1') {
    console.log('reload', type)
    reSet()
    reSearch();
  }
})

watch(
  () => defaultParams.value.timeType,
  (val: string | undefined) => {
    console.log('9999', val);
    switch (val) {
      case 'week':
        defaultParams.value.endTime = dayjs().format('YYYY-MM-DD');
        defaultParams.value.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD');
        break;

      case 'month':
        defaultParams.value.endTime = dayjs().format('YYYY-MM-DD');
        defaultParams.value.startTime = dayjs().subtract(1, 'month').format('YYYY-MM-DD');
        break;
      case 'year':
        defaultParams.value.endTime = dayjs().format('YYYY-MM-DD');
        defaultParams.value.startTime = dayjs().subtract(1, 'year').format('YYYY-MM-DD');
        break;

      default:
        break;
    }
  },
);

watch(
  () => defaultParams.value.orderStatusNew,
  () => {
    reSearch();
  },
);

// 清空
const reSet = () => {
  defaultParams.value = {
    hotelName: '',
    timeType: '',
    ownerIsOwn: loginUser.value?.username, //联系人工号
    pageNum: 0,
    pageSize: 20,
    keyword: '',
    orderStatusNew: '', // 订单状态
    payType: null, // 支付方式
    startTime: '',
    endTime: '',
  };
};

// 搜索
const menuRef = ref(null);
const reSearch = () => {
  orderLoading.value = true
  orderFinished.value = false
  defaultParams.value.pageNum = 0;
  orderList.value = []
  loadorderList();
  menuRef.value.close();
};

// 时间选择相关
const showTimePicker = ref<boolean>(false);

const minDate = ref(new Date(2021, 0, 1));
const maxDate = ref(new Date(2034, 0, 1));
const choseTimeType = ref('');
const currentDate = ref<Array<string>>([]);



// 详情
const goToDetail = (orderCode: string) => {
  router.push({ path: '/banquet/apply/detail', query: { orderCode: orderCode } });
}; 

</script>

<style lang='less' scoped>
@import url(../common.less);

:deep(.van-dropdown-menu__bar) {
  //  background: var(--van-dropdown-menu-background);
  box-shadow: none;
}

.list-search {
  :deep(.van-search__action) {
    // padding: 0;
  }
}

.list-search-btn {
  width: 110px;
}

.my-radio {
  :deep(.van-radio__icon) {
    height: auto !important;
  }
}

.btn-com {
  width: 70px;
}

.input-border {
  border: 1px solid #dcdee0;
  border-radius: 20px;
  padding: 2px 10px;
}

.van-list-box {
  padding: 8px 10px;
}
.icon-size {
  height: 16px;
}

.order-item {
  margin-bottom: 8px;
  .order-item-left {
    width: 10px;
    height: 100%;
    background: url('@/assets/image/banquet/order/step.png') no-repeat;
    background-size: cover;
  }

  .order-item-right {
    flex: 1;
    flex-direction: column;

    .order-item-title-left {
      font-size: 10px;
      color: rgba(0,0,0,0.5);
    }
    .order-content {
      :deep(.van-cell__title) {
        color: rgba(20,21,3,0.6);      }
      :deep(.van-cell__value) {
        color: rgba(20,21,3,0.8);     
      }
      :deep(.van-cell__label) {
        color: rgba(20,21,3,0.8);  
      }
      
    }

    .order-title-cell {
      :deep(.van-cell__title) {
        color: rgba(0,0,0,0.9) !important;
      }
      border-radius: 10px 10px 0px 0px;
    }
    
  }
}
</style>