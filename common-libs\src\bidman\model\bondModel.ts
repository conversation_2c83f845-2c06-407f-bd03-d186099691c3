// 需求提报
export interface MiceBidManBussinessListResult {
  /* */
  pageNum: number;

  /* */
  pageSize: number;

  /* */
  total: number;

  /* */
  totalPage: number;

  /* */
  records: {
    /* */
    pageNum: number;

    /* */
    pageSize: number;

    /*主键 */
    id: number;

    /*服务商名 称(中台) */
    merchantName: string;

    /*服务商code */
    merchantCode: string;

    /*服务商类型 枚举 */
    merchantType: string;

    /*服务商简介 */
    merchantDesc: string;

    /*是否需要缴纳保证金 */
    isEnsureMoney: number;

    /*有效保证金总额 */
    earnestMoney: number;

    /*评分 */
    score: number;

    /*状态 1有效 0冻结 */
    state: number;

    /*试用期状态 试用期/正式 */
    trialState: number;

    /*平台服务费比例 */
    serviceFeeRate: number;

    /*保证金记录 */
    earnestRecords: {
      /* */
      pageNum: number;

      /* */
      pageSize: number;

      /*服务商id */
      merchantId: number;

      /*服务商名称 */
      merchantName: string;

      /*状态 */
      state: string;

      /*创建开始时间 */
      createStart: Record<string, unknown>;

      /*创建结束时间 */
      createEnd: Record<string, unknown>;

      /*确认开始时间 */
      receiveStart: Record<string, unknown>;

      /*确认结束时间 */
      receiveEnd: Record<string, unknown>;

      /* */
      needPage: boolean;
    }[];

    /*银行账户变更记录 */
    merchantBankRecords: {
      /*主键 */
      id: number;

      /*供应商id */
      merchantId: string;

      /*银行账号 */
      accountNumber: string;

      /*开户行编码 */
      bankBranchCode: string;

      /*开户行地址 */
      bankBranchAddress: string;

      /*银行户主 */
      accountHolderName: string;

      /*银行所属国家 */
      bankCountry: number;

      /*是否默认 0否 1是 */
      isDefault: number;

      /*创建人 */
      createBy: string;

      /*创建时间 */
      gmtCreate: Record<string, unknown>;
    }[];

    /*合同变更记录 */
    merchantContractRecords: {
      /*主键 */
      id: number;

      /*供应商id */
      merchantId: string;

      /*合同号 */
      contractCode: string;

      /*合同开始日期 */
      contractStart: Record<string, unknown>;

      /*合同结束日期 */
      contractEnd: Record<string, unknown>;

      /*状态 0有效 1失效 */
      state: number;

      /*签订日期 */
      signDate: Record<string, unknown>;

      /*是否默认 0否 1是 */
      isDefault: number;

      /*创建人 */
      createBy: string;

      /*创建人姓名 */
      createName: string;

      /*创建时间 */
      gmtCreate: Record<string, unknown>;
    }[];

    /*转正记录 */
    merchantTrialRecords: {
      /*主键 */
      id: number;

      /*操作类型 */
      operateType: string;

      /*理由 */
      reason: string;

      /*试用结束日期 */
      trialEndDate: string;
    }[];

    /*供应商状态变更记录 */
    merchantStateRecords: {
      /*主键 */
      id: number;

      /*供应商id */
      merchantId: string;

      /*银行账号 */
      changeBefore: string;

      /*开户行编码 */
      changeAfter: string;

      /*理由 */
      reason: string;

      /*创建人 */
      createBy: string;

      /*创建时间 */
      gmtCreate: Record<string, unknown>;
    }[];

    /* */
    needPage: boolean;
  }
}