import { loadEnv } from 'vite'

import vue from '@vitejs/plugin-vue'
import path from 'path';

const CWD = process.cwd();

export default ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);

  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
    },
    plugins: [vue()],
    build:{
      target:['es2015']
    },
    server: {
      port: 5160,
      proxy: {
         // 本地
         '/hb/travel': {
          target: 'http://************:9222',
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`/hb/travel`), '/travel'),
        },
        // "/hb/common/api": {
        //   // target: "http://localhost:8080/hb",
        //   //  target: "https://businessmanagement-test.haier.net/hbweb/payman/hb",
        //   target: "http://localhost:9206",
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(/^\/hb\/common\/api/, ""),
        // },
        // "/hb/merchant/api": {
        //   // target: "http://localhost:8080/hb",
        //   target: "http://***********:35372",
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(new RegExp(`/hb/merchant/api`), ''),
        // },
        "/hb": {
          // target: "http://localhost:8080/hb",
          target: "https://businessmanagement-test.haier.net/hbweb/index/hb",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb/, ""),
        },
       
      },
    }
  }
}
