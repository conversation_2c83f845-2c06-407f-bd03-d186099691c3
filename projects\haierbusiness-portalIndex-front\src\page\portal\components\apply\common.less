.ticket-item{
    width: 408px;
    height: 54px;
    background: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #D9D9D9;
    margin-bottom: 8px;
    padding: 8px 12px 0 12px;
    box-sizing: border-box;
    position: relative;
    display: flex;
    flex-direction: column;
    .add-return{
      position: absolute;
      bottom: 8px;
      right: 12px;
      font-weight: 500;
      font-size: 16px;
      color: rgba(0,0,0,0.35);
    }
    .item-labels{
      font-size: 12px;
      color: rgba(0,0,0,0.35);
      line-height: 17px;
    }

    .item-row {
      display: flex;
      width: 100%;
      flex-direction: row;
      align-items:center;

      .item-start {
        display: flex;
        width: 185px;
        flex-direction: column;
        
      }

      .to {
        width: 14px;
        height: 1px;
        background: rgba(0,0,0,0.15);
      }

      .item-end {
        display: flex;
        width: 185px;
        flex-direction: column;
        align-items: end;

        :deep(.ant-picker){
          .ant-picker-input{
            input{
              text-align: right;
            }
            input::placeholder{
              text-align: right;
            }
          }
        }
      }
    }

    :deep(.ant-picker){
      height: 22px;
      line-height: 22px;
      .ant-picker-input{
        font-size: 16px;
        input{
          font-size: 16px;
          
        }
        input::placeholder{
          
          font-size: 16px;
          color: rgba(0,0,0,0.35) !important;
        }
      }
    }

    .item-time {
      // margin-top: 5px;
      margin-left: -11px;
    }

    .item-end-time {
      margin-right: -15px;
    }
    
    

    .item-num{
      font-weight: 500;
      font-size: 16px;
      color: rgba(0,0,0,0.85);
      line-height: 22px;
    }
}

.ticket-item-city{
  display: flex;
  position: relative;
  width: 408px;
  justify-content: space-between;
  position: relative;

  

  .ticket-item-ai{
    width: 200px;
    height: 54px;
    background: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #D9D9D9;
    padding: 8px 0 0 12px;
    .item-labels{
      font-size: 12px;
      color: rgba(0,0,0,0.35);
      line-height: 17px;
    }
  }

  .left-radius {
    border-radius: 0 8px 8px 0;
    border-left: 0px;
  }

  img {
    width: 28px;
    height: 28px;
    position: absolute;
    top: 14px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #fff;
    cursor: pointer;
  }
  
}

.international-left-width {
  width: 426px;
}
.international-right-width {
  width: 354px;
}