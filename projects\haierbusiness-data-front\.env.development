# 打包路径

VITE_API_BASE_URL=http://businessmanagement-test.haier.net
# VITE_API_BASE_URL=http://10.128.15.241:187
# VITE_API_BASE_URL=http://10.128.15.241:48080
# VITE_API_BASE_URL=http://10.250.9.116/datartplatform
VITE_UPLOAD_URL=/upload

# 账号中心cliendtId
VITE_CLIENT_ID=Kcd47f860e913400f
# 账号中心appId
VITE_APP_ID=cli_a320ed2dd3f1500e
# 账号中心统一登录页
VITE_SSO_URL=https://iam-test.haier.net
# 集成了账号中心提供的后端服务的地址
VITE_TOKEN_URL=http://10.250.7.69:32170/admin-api/system/auth/iamLogin
VITE_JUMP_URL=https://businessmanagement-test.haier.net/
# 测试水印
VITE_TEST_WATERMARK = '测试系统'
