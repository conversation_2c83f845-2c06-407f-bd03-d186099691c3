import { loadEnv } from 'vite'

import vue from '@vitejs/plugin-vue'
import path from 'path';
import postcssPx2remExclude from 'postcss-px2rem-exclude'

const CWD = process.cwd();

export default ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);

  
  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
    },
    plugins: [vue()],
    build:{
      target:['es2015'],
      sourcemap: true
    },
    server: {
      port: 5201,
      host: "0.0.0.0",
      proxy: {

        // http://************:9223/team/order/create
        
        // http://localhost:5201/hb/team/order/create
        
        // "/hb/team/api": {
        //   target: "http://************:9223",
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(new RegExp(`/hb/team/api`), ''),
        // },
        
        "/hb": {
          target: "https://businessmanagement-test.haier.net/hbweb/hotel/hb",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb/, ""),
        },
        "/upload": {
          target: "https://businessmanagement-test.haier.net/hbweb/upload",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/upload/, ""),
        },
        
      }
    },
    // css: {
    //   postcss: {
    //     plugins: [
    //       postcssPx2remExclude({
    //         remUnit: 192, 
    //         // exclude: /mobile/i // 忽略node_modules目录下的文件
    //       })
    //     ]
    //   }
    // }
  }
}
