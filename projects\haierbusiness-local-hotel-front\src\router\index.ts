import { RouteRecordRaw } from 'vue-router';

const modules = import.meta.glob('/src/page/**/*.vue');

import { baseRouterConstructor } from '@haierbusiness-front/utils';

const routes = [{
    path: '/',
    redirect: '/hotel/hotelList'
},
{
    path: '/hotel',
    redirect: '/hotel/hotelList',
    component: () => import('../page/hotel/mobile/index.vue'),
    children: [
        {
            path: '/hotel/hotelList',
            name: 'hotelList',
            meta: {
                keepAlive: true //设置页面是否需要使用缓存
            },

            component: () => import('../page/hotel/mobile/hotelList.vue'),
        },
        {
            path: '/hotel/orderList',
            name: 'orderList',
            meta: {
                keepAlive: true //设置页面是否需要使用缓存
            },

            component: () => import('../page/hotel/mobile/orderList.vue'),
        },
        // 搜索页
        {
            path: '/hotel/search',
            name: 'search',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },

            component: () => import('../page/hotel/mobile/search.vue'),
        },
        // 酒店详情
        {
            path: '/hotel/hotelDetail',
            name: 'hotelDetail',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },
            component: () => import('../page/hotel/mobile/hotelDetail.vue'),
        },

        {
            path: '/hotel/hotel/imgList',
            name: 'imgList',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },
        
            component: () => import('../page/hotel/mobile/imgList.vue'),
        },

        {
            path: '/hotel/order/orderDetail',
            name: 'orderDetail',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },
        
            component: () => import('../page/hotel/mobile/orderDetail.vue'),
        },

        {
            path: '/hotel/book',
            name: 'book',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },
        
            component: () => import('../page/hotel/mobile/book.vue'),
        },
        {
            path: '/hotel/order/orderLog',

            name: 'orderLog',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },
            component: () => import('../page/hotel/mobile/orderLog.vue'),
        },


    ]
},


];
// const router = baseRouterConstructor("haierbusiness-portalIndex", modules, flag, undefined, routes)
const router = baseRouterConstructor("haierbusiness-localhotel", modules, true, undefined, routes)

export default router;
