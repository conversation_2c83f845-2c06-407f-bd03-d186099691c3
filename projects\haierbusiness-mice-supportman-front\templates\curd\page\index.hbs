<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { {{ camelCase modelName }}Api } from '@haierbusiness-front/apis';
import {
  I{{ properCase modelName }}Filter,
  I{{ properCase modelName }}
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
{{#if isDialog}}
import EditDialog from './edit-dialog.vue'
{{/if}}
import router from '../../router'
// const router = useRouter()

const currentRouter = ref()

onMounted(async () => {
    currentRouter.value = await router
})

const columns: ColumnType[] = [
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<I{{ properCase modelName }}Filter>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination({{ camelCase modelName }}Api.list);

const reset = () => {
  searchParam.value = {}
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};
{{#if isEdit}}
{{#if isDialog}}
const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } =
  useEditDialog<I{{ properCase modelName }}, I{{ properCase modelName }}>({{ camelCase modelName }}Api, "{{ labelName }}", () => listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum || 1,
    pageSize: data.value?.pageSize || 10,
  }))

const thisHandleEdit = (item: I{{ properCase modelName }}) => {
  const currentData = {
    ...item
  };
  handleEdit({ ...currentData });
}
{{else}}
const edit = (id?: number) => {
        currentRouter.value.push({ path: "/supportman/{{ camelCase modelName }}/edit", query: { id: id } })
}
{{/if}}
{{/if}}


// 删除
const { handleDelete } = useDelete({{ camelCase modelName }}Api, () => listApiRun({
  ...searchParam.value,
  pageNum: data.value?.pageNum,
  pageSize: data.value?.pageSize,
}))

const beginAndEnd = ref<[Dayjs, Dayjs]>()
watch(() => beginAndEnd.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.begin = n[0]
    searchParam.value.end = n[1]
  } else {
    searchParam.value.begin = undefined
    searchParam.value.end = undefined
  }
});

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createTime">创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="beginAndEnd" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
            {{#if isEdit}}
            {{#if isDialog}}
            <h-button type="primary" @click="handleCreate()">
              <PlusOutlined /> 新增
            </h-button>
            {{else}}
            <h-button type="primary" @click="edit()">
              <PlusOutlined /> 新增
            </h-button>
            {{/if}}
            {{/if}}
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === '_operator'">
              {{#if isEdit}}
              {{#if isDialog}}
                <h-button type="link"  @click="thisHandleEdit(record)">编辑</h-button>
              {{else}}
              <h-button type="link" @click="edit(record.id)">编辑</h-button>
              {{/if}}
              {{/if}}
              
              <h-button type="link" @click="handleDelete(record.id)">删除</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    
    {{#if isDialog}}
    <div v-if="visible">
        <edit-dialog
            :show="visible"
            :data="editData"
            @cancel="onDialogClose"
            @ok="handleOk"
        >
        </edit-dialog>
    </div>
    {{/if}}

  </div>
</template>

<style scoped lang="less">

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

</style>
