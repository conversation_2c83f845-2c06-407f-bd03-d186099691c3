import {IPageRequest} from "../../basic";
export class IProcessDetailsRequest {
    todoId?: number
    code?: string
}

export class IControlProcessResponse {
    processCode?: string
    processRecord?: IProcessRecord
}

export class IProcessRecord {
    steps?: IProcessRecordStep[];
    id?: number

    code?: string
    businessCode?: string

    /**
     * 标题
     **/
    title?: string

    /**
     * 描述
     **/
    description?: string

    /**
     * 所属人/申请人
     */
    applicantUser?: string

    applicantName?: string

    /**
     * 流程定义id
     */
    pdId?: number

    pdName?: string

    pdsId?: number

    customBudgetCode?: string

    customBudgetName?: string

    customUsername?: string

    customNickName?: string

    /**
     * 0：取消 10：审批中 20：审批通过 30：审批驳回 40:审批撤回
     */
    state?: number

    /**
     * 企业编码
     */
    enterpriseCode?: string

    /**
     * 企业
     */
    enterpriseName?: string

    /**
     * 发起审批应用
     */
    applicationCode?: string

    /**
     * 驳回人
     */
    rejectedUser?: string

    /**
     * 驳回人
     */
    rejectedUsername?: string

    /**
     * 驳回原因
     */
    rejectedRemark?: string

    gmtCreate?: string

    orderDetailsUrl?: string
    mobileOrderDetailsUrl?: string
    notifyUrl?: string
    notifyState?: number

    /**
     * 审批流完成时间
     */
    completeTime?: string

    gmtModified?: string

    createBy?: string

    lastModifiedBy?: string
}

export interface IProcessBusinessDetailsUrl {
    todoId?: string;
}

export class IProcessRecordStep {
    operators?: IProcessRecordStepOperator[];
    id?: number

    /**
     * 下一步流程id
     */
    nextPrsId?: number
    seq?: number

    recordCode?: string
    businessCode?: string
    /**
     * 所属人/申请人
     */
    applicantUser?: string

    applicantName?: string

    /**
     * 流程定义id
     */
    pdId?: number

    pdName?: string

    pdsId?: number

    customBudgetCode?: string

    customBudgetName?: string

    customUsername?: string

    customNickName?: string
    psId?: number

    psName?: string

    /**
     * 当步骤中存在多人时
     * 1: 全部(∀) , 当前步骤全部通过则通过,一人驳回则驳回
     * 2: 存在(∃) , 当前步骤存在一人通过则通过, 一人驳回则驳回
     */
    psType?: number

    /**
     * 需要的总票数,如果为2: 存在(∃),当前条件无意义
     */
    allVote?: number

    /**
     * 当前多少人投票
     */
    currentVote?: number

    /**
     * 0: 未触发 10: 审批中 20: 审批完成
     */
    state?: number

    /**
     * 1: 通过 2:驳回 3:跳审(重复审批人) 4:免审, 5: 取消
     */
    result?: number

    /**
     * 1: 常规步骤
     * 2: 加审步骤
     */
    type?: number


    /**
     * 加审提出人
     */
    addUser?: string

    addName?: string

    /**
     * 企业编码
     */
    enterpriseCode?: string

    /**
     * 企业
     */
    enterpriseName?: string
    notifyUrl?: string
    /**
     * 通知回调状态 0:成功 1:重试 2:失败
     */
    notifyState?: number

    /**
     * 已重试次数
     */
    notifyRetry?: number

    /**
     * 最后通知失败原因
     */
    notifyErrorMessage?: string


    /**
     * 发起审批应用
     */
    applicationCode?: string

    /**
     * 审批开始时间
     */
    startTime?: string

    /**
     * 审批步骤完成时间
     */
    completeTime?: string

    gmtCreate?: string

    gmtModified?: string

    createBy?: string

    lastModifiedBy?: string
}

export class IProcessRecordStepOperator {
    id?: number

    recordCode?: string
    businessCode?: string
    prsId?: number

    /**
     * 所属人/申请人
     */
    applicantUser?: string

    applicantName?: string

    /**
     * 流程定义id
     */
    pdId?: number

    pdName?: string

    pdsId?: number

    customBudgetCode?: string

    customBudgetName?: string

    customUsername?: string

    customNickName?: string

    psId?: number

    psName?: string

    /**
     * 当步骤中存在多人时
     * 1: 全部(∀) , 当前步骤全部通过则通过,一人驳回则驳回
     * 2: 存在(∃) , 当前步骤存在一人通过则通过, 一人驳回则驳回
     */
    psType?: number

    /**
     * 1: 常规步骤
     * 2: 加审步骤
     */
    prsType?: number

    addUser?: string

    addName?: string

    psoId?: number

    /**
     * 审批角色
     * haier_manage 海尔直线
     * fixed_person   固定的人
     * 等
     */
    role?: string

    /**
     * 角色名称
     */
    roleName?: string

    /**
     * 1: 主审人 2:代审人
     */
    approveUserType?: number

    /**
     * 主审记录id
     */
    masterPrsoId?: number

    /**
     * 审批人
     */
    approverCode?: string

    /**
     * 审批人名字
     */
    approverName?: string

    /**
     * 代审最终执行人
     */
    agentUser?: string
    agentName?: string
    /**
     * 审批意见
     */
    approverRemark?: string


    /**
     * 1: 通过 2:驳回 3:未抉择
     */
    state?: number

    /**
     * 企业编码
     */
    enterpriseCode?: string

    /**
     * 企业
     */
    enterpriseName?: string

    /**
     * 发起审批应用
     */
    applicationCode?: string

    /**
     * 审批人审批完成时间
     */
    completeTime?: string

    gmtCreate?: string

    gmtModified?: string

    createBy?: string

    lastModifiedBy?: string

}

export class IControlProcessExecuteRequest {
    todoId?: number
    processCode?: string
    remark?: string
    result?: number
} 

export class IProcessRecordList {
    id?: number

    /**
     * 审批记录
     */
    recordCode?: string
    businessCode?: string

    /**
     * 所属人
     */
    username?: string

    /**
     * 审批人记录ID
     */
    currentOperatorId?: number

    /**
     * 0:PC 1:手机端 2：ihaier
     */
    dealFrom?: number

    /**
     * 待办状态 1:通过 2：撤销 3：驳回/拒绝  4：其他  7：已关闭
     */
    resultState?: number

    /**
     * 标题
     */
    title?: string

    /**
     * 描述
     **/
     description?: string

    /**
     * 所属人/申请人
     */
    applicantUser?: string

    applicantName?: string

    /**
     * 流程定义id
     */
    pdId?: number

    pdName?: string

    pdsId?: number

    /**
     * 1: 主审人 2:代审人
     */
    approveUserType?: number

    /**
     * 为指定部门创建不同审批流
     */
    customBudgetCode?: string

    customBudgetName?: string

    /**
     * 为指定人创建不同审批流
     */
    customUsername?: string

    customNickName?: string

    /**
     * 1：待审批  2：已审批 3：已撤回
     */
    approveState?: number

    /**
     * 审批人审批完成时间
     */
    completeTime?: string

    /**
     * 企业编码
     */
    enterpriseCode?: string

    /**
     * 企业
     */
    enterpriseName?: string

    /**
     * 发起审批应用
     */
    applicationCode?: string

    orderDetailsUrl?: string

    mobileOrderDetailsUrl?: string

    gmtCreate?: string

    gmtModified?: string

    createBy?: string

    lastModifiedBy?: string

    ownerId?: string

    ownerName?: string

}

export class IProcessRecordListRequest extends IPageRequest {

    code?: string

    approveState?: number

    type?: number

    applicantUser?: string

    ownerId?: string
}

// 参数接口
export interface AddSceneParams {
    /* */
    pageNum?: number;
  
    /* */
    pageSize?: number;
  
    /*场景id */
    id?: number;
  
    /*流程ID */
    pdId?: number;
  
    /*场景名称 */
    name?: string;
  
    /*场景编码 */
    code?: string;
  
    /*0无效 1有效 */
    active?: number;
  
    /* */
    needPage?: boolean;
  }

export class IControlLogList {
    id?: number
    recordCode?: string
    bizType?: number
    bizTypeName?: string
    step?: number
    stepName?: string
    content?: string
    state?: number
    showType?: number
    gmtCreate?: string
    createBy?: string
}

export class IControlLogRequest extends IPageRequest {
    recordCode?: string
}

export class IProcessListRequest extends IPageRequest {
    name?: string
    enterpriseCode?: string
    enterpriseName?: string
    state?: string
    additionable?: string
    revocable?: string
}

export class IRule {
    id?: number | null
    name?: string
    description?: string
    state?: number
}

export class IProcessIno {
    id?: number
    pdsId?: number
    pdId?: number
    name?: string
    scope?: number
    additionable?: number
    revocable?: number
    enterpriseCode?: string
    enterpriseName?: string
    departmentCode?: string
    departmentName?: string
    username?: string
    nickName?: string
    assist?: number
    nodeCall?: number
    autograph?: number
    todoMethod ?: number
    voteCall?: number
    steps?: Array<IStepsInfo>
    sceneCode?: string
}

export class IStepsInfo {
    guid?: string
    name?: string
    seq?: number
    type?: number
    pdId?: number
    pdsId?: number
    operators?: Array<IOperatorsInfo>
    roles?: Array<IOperatorsInfo>
    stepOperators?: Array<IOperatorsInfo>
}

export class IOperatorsInfo {
    guid?: string
    role?: string
    roleName?: string
    approver?: string
    approverName?: string
    substituteRole?: string
    substituteRoleName?: string
    substituteApprover?: string
    substituteApproverName?: string
}

export class IDefinedProcess {
    id?: number
    name?: string
    description?: string
    state?: number
    deleted?: number
    definedSubsets?: Array<IProcessIno>
}

export interface PageSceneParams {
    /* */
    pageNum?: number;
  
    /* */
    pageSize?: number;
  
    /*场景id */
    id?: number;
  
    /*流程ID */
    pdId?: number;
  
    /*场景名称 */
    name?: string;
  
    /*场景编码 */
    code?: string;
  
    /*0无效 1有效 */
    active?: number;
  
    /* */
    needPage?: boolean;
  }