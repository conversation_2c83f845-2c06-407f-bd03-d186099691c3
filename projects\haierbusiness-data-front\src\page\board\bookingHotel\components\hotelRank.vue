<template>
  <div background="rgba(0,0,0,0)" style="height:33vh">
    <Rank :data="rankData" unit="间夜" />
  </div>
</template>
<script setup lang="ts">
import Rank from "../../components/rank.vue";
import { queryHotelRank } from "@haierbusiness-front/apis/src/data/board";
import { onMounted, ref } from "vue";
import { EventBus } from "../../eventBus";
const rankData = ref([]);
const loading = ref(false);
onMounted(() => {
  queryData();
})
EventBus.on((event, params) => {
  if (event == "refresh") queryData(params);
});
const queryData = async (params?: any) => {
  loading.value = true;
  const data = await queryHotelRank(
    params ? params.data.name : null,
    params ? params.from : null
  );
  loading.value = false;
  const rows: any = [];
  data.rows.forEach((item: any) => {
    rows.push({
      name: item[0],
      value: item[1],
    });
  });
  // rows.sort((a,b)=>b.value-a.value);
  rankData.value = rows;
};
</script>
