
export interface brandReq {
  id?: number;

  /* */
  brandName?: string;

  /* */
  brandCategory?: string;

  /* */
  brandCategoryName?: string;

  /* */
  brandGroupId?: number;

  pageNum?: number | string;
  pageSize?: number | string;

  /* */
  brandGroupName?: string;

  /* */
  deleted?: number;

  /* */
  type?: number;

  /* */
  gmtModified?: Record<string, unknown>;

  /* */
  gmtCreate?: Record<string, unknown>;
}



export interface providerBrandReq {
  /*供应商品牌Code */
  providerCode?: string;

  /*供应商品牌id */
  providerBrandId?: string;

  /*供应商品牌名称 */
  providerBrandName?: string;

  /*供应商品牌集团id */
  providerBrandGroupId?: string;

  /*供应商品牌集团名称 */
  providerBrandGroupName?: string;

  /*映射品牌id */
  mappingBrandId?: number | string;

  /*映射品牌名称 */
  mappingBrandName?: string;

  /*映射品牌集团id */
  mappingBrandGroupId?: number;

  /*映射品牌集团名称 */
  mappingBrandGroupName?: string;

  /*供应商酒店类型 1:集团 2:品牌 */
  type?: string;
  pageNum?: number | string;
  pageSize?: number | string;
}
