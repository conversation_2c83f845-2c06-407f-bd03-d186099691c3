import {  IUserInfo } from '../../system/model/userModel';

export interface MMeiTuanReqIo {
  name?: string;
  mobile?: string;
  applicationTypeText?: string;
  applicationType?: string;
  beginDate?: string;
  endDate?: string;
  cityName?:string;
  cityCode?:string;

  contactUserName?:string; // 签单人姓名
  contactUserCode?:string; // 签单人工号
  contactDeptName?: string;
  contactUserPhone?: string;	//联系人电话
  contactUserMail?: string;	//联系人邮箱

  mtRusult?:string;
  innerPerson: Array<IUserInfo>;
  outerPerson: Array<IUserInfo>;
}

export interface MApplicationType {
  id?: string; // id
  name?: string; // name
}