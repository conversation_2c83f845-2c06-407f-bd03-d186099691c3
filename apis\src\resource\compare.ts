import { IPageResponse, IDscsB2bListRequest, IHaierAccountBillInfo, IDscsB2bAccountRequest, IDscsB2bDetailsRequest, IHabDetailInfo, IDscsB2bConfirmRequest, IDscsB2bRevokeConfirmRequest, IDscsB2bConfirmBalanceOrderBudgetRequest, IDscsB2bCancelRequest, IDscsB2bMarkReadRequest } from '@haierbusiness-front/common-libs'
import { download, get, post, filepost, originalGet } from '../request'

export const SupplierApi = {

    getSupplierList: (params: any): Promise<void> => {
        return get('hotel-analysis/api/ledger/spot-check/page', params)
    },
    confirm: (params: any): Promise<void> => {
        return post('hotel-analysis/api/ledger/spot-check/update', params)
    },
    updateVentor: (params: any): Promise<void> => {
        return post('hotel-analysis/api/ledger/spot-check-detail/update', params)
    },
    getTemplate: (): Promise<void> => {
        return download('hotel-analysis/api/ledger/spot-check/get-import-template')
    },
    exportExcel: (params: any): Promise<void> => {
        return download('hotel-analysis/api/ledger/spot-check/export-excel', params)
    },

    exportAnalysisExcel: (params: any): Promise<void> => {
        return download('hotel-analysis/api/ledger/spot-check/export-excel-analysis', params)
    },
    createAll: (params: any): Promise<void> => {
        return post('hotel-analysis/api/ledger/spot-check/batch-create', params)
    },

    createVentor: (params: any): Promise<void> => {
        return post('hotel-analysis/api/ledger/spot-check-detail/create', params)
    },
    /**
     * 撤回已确认状态
     */
    revokeConfirm: (params: IDscsB2bRevokeConfirmRequest): Promise<void> => {
        return post('balance/api/b2b/dscs/revoke/confirm', params)
    },

    /**
     * 取消汇总
     */
    cancel: (params: IDscsB2bCancelRequest): Promise<void> => {
        return post('balance/api/b2b/dscs/cancel', params)
    },

    /**
     * 获取结算列表
     */
    list: (params: IDscsB2bListRequest): Promise<IPageResponse<IHaierAccountBillInfo>> => {
        return get('balance/api/b2b/dscs/list', params)
    },

    /**
     * 获取结算状态数量
     */
    stateAccount: (params: IDscsB2bListRequest): Promise<any> => {
        return get('balance/api/b2b/dscs/state/account', params)
    },

    /**
     * 获取预算释放失败,cvp通知失败数量
     */
    cvpErrorAccount: (params: IDscsB2bListRequest): Promise<number> => {
        return get('balance/api/b2b/dscs/cvp/error/account', params)
    },

    /**
     * 获取结算详情
     */
    details: (params: IDscsB2bDetailsRequest): Promise<IPageResponse<IHabDetailInfo>> => {
        return get('balance/api/b2b/dscs/details', params)
    },

    /**
     * 导出结算详情
     */
    detailsExport: (params: IDscsB2bDetailsRequest): Promise<void> => {
        return download('balance/api/b2b/dscs/details/export', params)
    },

    /**
     * 已读CVP错误消息
     */
    markRead: (params: IDscsB2bMarkReadRequest): Promise<void> => {
        return post('balance/api/b2b/dscs/mark/read', params)
    },

    /**
     * 获取推送CVP失败且未读的汇总单
     */
    pushCvpErrorAccount: (params: IDscsB2bListRequest): Promise<number> => {
        return get('balance/api/b2b/dscs/push/cvp/error/account', params)
    },

    /**
     * 批量确认爬虫台账改为已确认状态
     */
    batchConfirm: (params: { ids: Array<string> }): Promise<void> => {
        return get('hotel-analysis/api/ledger/spot-check/batch-confirm', params)
    },
    /**
     * 批量删除台账抽检
     */
    batchDelete: (params: { ids: Array<string> }): Promise<void> => {
        return get('hotel-analysis/api/ledger/spot-check/batch-delete', params)
    },
    /**
     * 批量取消
     */
    batchCancel: (params: { ids: Array<string> }): Promise<void> => {
        return get('hotel-analysis/api/ledger/spot-check/batch-cancel', params)
    },
    /**
     * 获取城市接口
     */
    getCtiyList: (params?: any): Promise<void> => {
        return get('common/api/district/list', params)
    },
    /**
     * 修改备注
     */
    updateRemark: (params?: any): Promise<void> => {
        return post('hotel-analysis/api/ledger/spot-check/update', params)
    },


}