import { IPageRequest } from '../../basic';
import { UploadFile } from '../../micebid';

export class IMeetingSignInFilter extends IPageRequest {
  miceInfoId?: number;
  begin?: string;
  end?: string;
  miceId?: string;
}

export class IMeetingSignInRules {
  id?:number;
  miceInfoId?:number;
  //是否开启签到
  isOpen?:boolean;
  //签到开始日期	
  checkInStartDate?:string;
  //签到截止日期		
  checkInEndDate?:string;
  //签到方式	
  checkInMethod?:number;
  //是否打开小程序自动签到	
  isOpenCheckIn?:boolean;
  //签到二维码	
  checkInCodeUrl?:string;
  //签到负责人姓称	
  checkInHandleNickName?:string;
  //签到负责人工号	
  checkInHandleUsername?:string;
  //是否开启签到通知 0否1是	
  isCheckInNotice?:boolean;
  //签到范围	
  checkInRange?:number;
  checkList?:{
    id?:number;
    //签到地点名称
    place?:string;
    //签到地点经度	
    longitude?:string;
    //签到地点纬度	
    latitude?:string;
  }[];
  //签到二维码图片id	
  checkInCodeId?:number;
  //二维码背景图片	
  checkInCodeBgUrl?:string;
  //二维码图片类型	
  checkInCodeType?:string;
}
export class IMeetingSignInDetails {
  id?:number;
  //应签到人工号	
  username?:string;
  //应签到人姓名		
  nickName?:string;
  //手机号	
  phoneNo?:string;
  //身份证号	
  idCard?:string;
  //性别	
  sex?:string;
  //签到状态 0否1是	
  isCheckIn?:number;
  //签到时间	
  checkInTime?:string;
  //签到方式 0手动 1被动
  checkInMethod?:number;
  //签到人员工号	
  checkInUsername?:number;
  //签到人员姓名	
  checkInNickName?:string;
  //是否补签到	
  isAdditionalCheckIn?:boolean;
  //补签到审批状态	
  approveState?:number;
  //补签到审批人	
  approvePerson?:string;
  //补签到审批时间	
  approveTime?:string;
  //已签到人数	
  checkInCount?:string;
  //未签到人数	
  noCheckInCount?:string;
  //审批code	
  approveCode?:string;
}