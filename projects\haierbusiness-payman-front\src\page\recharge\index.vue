<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Input as hInput,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  Alert,
  Tooltip,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { DownOutlined, UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { payApi,rechargeApi } from '@haierbusiness-front/apis';
import {
  PayNotifyStateConstant,
  PayTypeChildConstant,
  ICoinFlowingWaterRequest,
  PayStatusConstant,
  PayTypeConstant,
} from '@haierbusiness-front/common-libs';
import { getCurrentRouter, errorModal, routerParam } from '@haierbusiness-front/utils';
import dayjs, { Dayjs } from 'dayjs';
import { Ref, computed, getCurrentInstance, onMounted, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { toNumber } from 'lodash';

const router = getCurrentRouter();

const searchParamBill = ref<any>({
  accountNo:router.currentRoute.value.query?.accountNo
});

onMounted(async () => {
  const accountNo = router.currentRoute.value.query?.accountNo;
  searchParam.value.createBy = accountNo
  getAndInitTradeUnionAccount()
  listApiRun({
      ...searchParam.value,
      pageNum: 1,
      pageSize: 10,
  });
});
const accountInfo = ref<any>({})
// 获取账户信息 
const getAndInitTradeUnionAccount = ()=>{
  rechargeApi.getAndInitTradeUnionAccount({accountNo:router.currentRoute.value.query?.accountNo}).then(res=>{
    accountInfo.value = res
  })
}

const goAccountRecharge = () => {
  router.push('/payman/recharge/accountRecharge');
};
const goHandover = () => {
  router.push('/payman/recharge/handover');
};

const columns: ColumnType[] = [
  {
    title: '订单编号',
    dataIndex: 'orderCode',
    fixed: 'left',
    width: '240px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '平台支付单号',
    dataIndex: 'payCode',
    width: '240px',
    align: 'center',
    ellipsis: true,
  },  
  {
    title: '预算占用单号',
    dataIndex: 'budgetCode',
    width: '240px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '申请时间',
    dataIndex: 'applyTime',
    width: '240px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '充值金额',
    dataIndex: 'budgetAmount',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '订单状态',
    dataIndex: 'status',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '250px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<any>({});

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(rechargeApi.getAdminRechargeList, {
  manual: true,
});

const {
  data:billData,
  run: listApiRunForList,
  loading:laodingForBill,
  current:currentForList,
  pageSize:pageSizeForList,
} = usePagination(rechargeApi.getFlowingWaterByAccountPage, {
  manual: true,
});

const {
  data: exportListData,
  run: exportListApiRun,
  loading: exportListLoading,
} = useRequest(rechargeApi.exportRechargeAdminList);

const {
  data: exportFlowingWaterByAccountData,
  run: exportFlowingWaterByAccount,
  loading: exportFlowingWaterByAccountLoading,
} = useRequest(rechargeApi.exportFlowingWaterByAccount);

const reset = () => {
  startBeginAndEnd.value = undefined;
  searchParam.value = {
    createBy:router.currentRoute.value.query?.accountNo
  };
  listApiRun({
      ...searchParam.value,
      pageNum: 1,
      pageSize: 10,
    });
}

const resetBill = () =>{
  startBeginAndEndBill.value = undefined
  searchParamBill.value = {
    accountNo:router.currentRoute.value.query?.accountNo
  }
  listApiRunForList({
      ...searchParamBill.value,
      accountNo:router.currentRoute.value.query?.accountNo,
      pageNum: 1,
      pageSize: 10,
    });
}

const dataSource = computed(() => data.value?.records || []);
const dataSourceForBill = computed(() => billData.value?.records || []);
const paginationBill = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: billData.value?.total,
  current: billData.value?.pageNum,
  pageSize: billData.value?.pageSize,
  style: { justifyContent: 'center' },
}));
const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChangeBill = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  listApiRunForList({
      ...searchParamBill.value,
      accountNo:router.currentRoute.value.query?.accountNo,
      pageNum: pag.current,
      pageSize: pag.pageSize,
    });
};

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
    listApiRun({
      ...searchParam.value,
      pageNum: pag.current,
      pageSize: pag.pageSize,
    });
};
const gotoDetails = (record: RtoPageParams) => {
  router.push({ path: "/payman/recharge/detail", query: { id: record.id, budgetCode: record.budgetCode||'' } })
}
const advancedSearchVisible = ref(false);
const gotoAdvancedSearch = () => {
  if (advancedSearchVisible.value) {
    advancedSearchVisible.value = false;
  } else {
    advancedSearchVisible.value = true;
  }
};

const startBeginAndEnd = ref<[Dayjs, Dayjs]>();
watch(
  () => startBeginAndEnd.value,
  (n: any, o: any) => {
    if (n) {
      searchParam.value.applyBeginDate = dayjs(n[0]).format('YYYY-MM-DD') + " 00:00:00";
      searchParam.value.applyEndDate = dayjs(n[1]).format('YYYY-MM-DD') + " 23:59:59";
    } else {
      searchParam.value.applyBeginDate = undefined;
      searchParam.value.applyEndDate = undefined;
    }
  },
);

const startBeginAndEndBill = ref<[Dayjs, Dayjs]>();
watch(
  () => startBeginAndEndBill.value,
  (n: any, o: any) => {
    if (n) {
      searchParamBill.value.changeBegin = dayjs(n[0]).format('YYYY-MM-DD') + " 00:00:00";
      searchParamBill.value.changeEnd = dayjs(n[1]).format('YYYY-MM-DD') + " 23:59:59";
    } else {
      searchParamBill.value.changeBegin = undefined;
      searchParamBill.value.changeEnd = undefined;
    }
  },
);



const open = () =>{
  billOpen.value = true
  listApiRunForList({
      ...searchParamBill.value,
      accountNo:router.currentRoute.value.query?.accountNo,
      pageNum: 1,
      pageSize: 10,
    });
}

// 流水记录弹窗数据
const billOpen = ref<boolean>(false);

const handleOk = () => {};
const billColumns = [
  {
    title: '流水单号',
    dataIndex: 'code',
    width:"180px",
    key: 'code',
  },
  {
    title: '业务单号',
    dataIndex: 'dcMainOrder',
    width:"180px",
    key: 'dcMainOrder',
  },
  // {
  //   title: '变动凭证单号',
  //   dataIndex: 'changeCode',
  //   width:"180px",
  //   key: 'changeCode',
  // },
  // {
  //   title: '变动凭证业务单号',
  //   dataIndex: 'changeBusinessCode',
  //   width:"180px",
  //   key: 'changeBusinessCode',
  // },
  {
    title: '操作人',
    dataIndex: 'createName',
    width:"100px",
    key: 'createName',
  },
  {
    title: '操作时间',
    dataIndex: 'gmtCreate',
    width:"150px",
    key: 'gmtCreate',
  },
  {
    title: '变动类型',
    dataIndex: 'type',
    width:"100px",
    key: 'type',
  },
  {
    title: '变动金额',
    dataIndex: 'amount',
    width:"100px",
    key: 'amount',
  },
];

const getStatus=(status:number)=>{
  if(status==10){
    return '待提交'
  }else if(status==20){
    return '已提交'
  }else if(status==30){
    return '支付中'
  }else if(status==40){
    return '已关闭'
  }else if(status==90){
    return '已完成'
  }
}

const getType =(type:number)=>{
  if(type==1){
    return '下发'
  }else if(type==2){
    return '退款'
  }else if(type==3){
    return '充值'
  }
}

const toDetail = (record:any) =>{
  if(record.type==3){ 
    searchParam.value.orderCode = record.dcMainOrder
    billOpen.value = false
    listApiRun({
      ...searchParam.value,
      pageNum: 1,
      pageSize: 10,
    });
  }else if(record.type==1){
    router.push({
      path:'/payman/grant',
      query:{
        orderCode:record.dcMainOrder
      }
    });
  }
}

</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="orderCode">订单编号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="orderCode" v-model:value="searchParam.orderCode" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="payCode">平台支付单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="payCode" v-model:value="searchParam.payCode" placeholder=""
              autocomplete="off" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="statusList">订单状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="statusList" mode="multiple" v-model:value="searchParam.statusList" style="width: 100%" allow-clear>
              <h-select-option :value="10">待提交</h-select-option>
              <h-select-option :value="20">已提交</h-select-option>
              <h-select-option :value="30">支付中</h-select-option>
              <h-select-option :value="40">已关闭</h-select-option>
              <h-select-option :value="90">已完成</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;">
            <label for="startBeginAndEnd">申请时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="startBeginAndEnd" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button
              type="primary"
              style="margin-right: 10px"
              :loading="exportListLoading"
              @click="exportListApiRun(searchParam)"
            >
              <UploadOutlined />
              导出
            </h-button>
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="dataSource"
          :pagination="pagination"
          :scroll="{ x: 1550 }"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #title>
            <a-alert type="info">
              <template #description>
                <h-row>
                  <h-col :span="18" style="font-size: 18px; font-weight: 600"> 账户余额:{{accountInfo.amount}}元 </h-col>
                  <h-col :span="6" style="text-align: right">
                    <h-button type="primary" style="margin-right: 10px" @click="open"> 流水记录 </h-button>
                  </h-col>
                </h-row>
                <h-row style="font-size: 16px">
                  <h-col :span="6"> 负责人:{{accountInfo.employeeName}}({{ accountInfo.employeeId }}) </h-col>
                  <!-- <h-col :span="12"> 联系电话:************ </h-col> -->
                </h-row>
              </template>
            </a-alert>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'status'">
              {{getStatus(record.status)}}
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="gotoDetails(record)">详情</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

    <!-- 流水记录 -->
    <a-modal v-model:open="billOpen" title="账户流水明细" width="1200px" @ok="handleOk">
      <h-row>
        <h-col :span="24" style="margin-bottom: 10px">
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;margin-bottom:10px;">
            <h-col :span="2" style="text-align: right; padding-right: 10px">
              <label for="type">类型：</label>
            </h-col>
            <h-col :span="4">
              <h-select ref="type" v-model:value="searchParamBill.type" style="width: 100%" allow-clear>
                <h-select-option :value="1">下发</h-select-option>
                <h-select-option :value="2">退款</h-select-option>
                <h-select-option :value="3">充值</h-select-option>
              </h-select>
            </h-col>
            <h-col :span="2" style="text-align: right; padding-right: 10px">
              <label for="startBeginAndEndBill">操作时间:</label>
            </h-col>
            <h-col :span="8">
              <h-range-picker v-model:value="startBeginAndEndBill" value-format="YYYY-MM-DD" style="width: 100%" />
            </h-col>

            <!-- <h-col :span="4">
              <h-date-picker v-model:value="value1" placeholder="选择开始时间" value-format="YYYY-MM-DD" style="width: 100%;" />
            </h-col>
            <h-col :offset="1" :span="4">
              <h-date-picker v-model:value="value1" placeholder="选择结束时间" value-format="YYYY-MM-DD" style="width: 100%;"  />
            </h-col> -->
            <h-col :offset="1" :span="6">
              <h-button style="margin-right: 10px" @click="resetBill">重置</h-button>
              <h-button type="primary" @click="handleTableChangeBill({ current: 1, pageSize: 10 })">
                <SearchOutlined />
                查询
              </h-button>
              <h-button
              type="primary"
              style="margin-left: 10px"
              :loading="exportFlowingWaterByAccountLoading"
              @click="exportFlowingWaterByAccount(searchParamBill)"
            >
              <UploadOutlined />
              导出
            </h-button>
            </h-col>
          </h-row>

          <h-row>
            <h-col :span="24">
              <h-table
                :columns="billColumns"
                :row-key="(record) => record.id"
                :size="'small'"
                :data-source="dataSourceForBill"
                :pagination="paginationBill"
                :scroll="{ x: 800 }"
                :loading="laodingForBill"
                @change="handleTableChangeBill($event as any)"
              >
              <template #bodyCell="{ column, record, text }">
                <template v-if="column.dataIndex === 'type'">
                  {{getType(record.type)}}
                </template>
                <template v-if="column.dataIndex === 'dcMainOrder'">
                  <a @click="toDetail(record)">{{ text }}</a>
                </template>
              </template>
            </h-table>
            </h-col>
          </h-row>
        </h-col>
      </h-row>
      <template #footer> </template>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
