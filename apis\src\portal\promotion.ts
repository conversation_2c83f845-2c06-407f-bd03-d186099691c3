import {
    Result,
    IAdvertisementListRequest,
    IPageResponse,
    IAdvertisementResponse,
    IAdvertisementAccount
} from '@haierbusiness-front/common-libs'
import { get, post } from '../request'
export const advertisementListApi = {
        /**
     * 查询广告位列表
     */
    queryAdvertisement: (params: IAdvertisementListRequest): Promise<IPageResponse<IAdvertisementResponse>> => {
        return get('/portal/api/admin-api/ad/promotion/page', params)
    },
    get: (id: number): Promise<IAdvertisementResponse> => {
        return get('/portal/api/admin-api/ad/get', {
            id
        })
    },
    detail: (id: number): Promise<IAdvertisementAccount> => {
        return get('/portal/api/app-api/ad/get', { id })
    },
    save: (params: IAdvertisementListRequest): Promise<Result> => {
        return post('/portal/api/admin-api/ad/promotion/create', params)
    },
    edit: (params: IAdvertisementListRequest): Promise<Result> => {
        return post('/portal/api/admin-api/ad/promotion/update', params)
    },
    remove: (id: number): Promise<Result> => {
        return get('/portal/api/admin-api/ad/promotion/delete', { id })
    },
 
}