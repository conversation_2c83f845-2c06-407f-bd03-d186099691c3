<script lang="ts" setup>
import {
  Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
  Input as hInput, Textarea as hTextarea, <PERSON><PERSON> as hButton, DatePicker as hDatePicker, Col as hCol, Row as hRow, Upload as hUpload, Switch as hSwitch
} from 'ant-design-vue';
import { computed, ref, watch, onMounted } from "vue";
import type { Ref } from "vue";
import {
  IInformationType
} from '@haierbusiness-front/common-libs';
import type { Rule } from "ant-design-vue/es/form";
import type { UploadFile, UploadChangeParam, UploadProps } from 'ant-design-vue'
import { message } from 'ant-design-vue'
import { UploadOutlined } from '@ant-design/icons-vue'
import PlusOutlined from '@ant-design/icons-vue/PlusOutlined';
import LoadingOutlined from '@ant-design/icons-vue/LoadingOutlined';
import { useRoute, useRouter } from 'vue-router'
import { toNumber } from "lodash-es"
import { informationApi } from '@haierbusiness-front/apis';
import Editor from '@haierbusiness-front/components/editor/Editor.vue'
import type { IDomEditor } from "@wangeditor/editor"
import dayjs, { Dayjs } from 'dayjs';
import router from '../../router'

const route = useRoute();
// const router = useRouter()

const currentRouter = ref()

const from = ref();
const confirmLoading = ref(false);

const id = ref<number>()
const isEdit = ref(false)

onMounted(async () => {
    currentRouter.value = await router
    isEdit.value = true
    const currentId = currentRouter.value.currentRoute.query?.id
    id.value = toNumber(currentId)
    if (id.value) {
      await get(id.value)
    }
    else {
        information.value = { showStatus: 1 }
        imageUrl.value = ''
    }
})

watch(() => currentRouter.value?.currentRoute.query, (newValue, oldValue) => {
  id.value = toNumber(newValue.id)
  if (id.value) {
    get(id.value)
  } 
  else {
    information.value = { showStatus: 1 }
    imageUrl.value = ''
  }
})

const get = async (id: number) => {
  const data = await informationApi.get(id)
  if(data && data.id) {
    information.value = data
    information.value.infoDate = dayjs(information.value.infoDate)
    imageUrl.value = data.imgUrl || ''
  } 
}

const checkDisUrlTip = (url: string) => {
  const reg = /^((https|http|ftp|rtsp|mms)?:\/\/)[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/;
  if (reg.test(url)) {
    return true;
  } else {
    return false;
  }
}

const validateUrl = (_rule: Rule, value: string, name: string) => {
  if (value && value !== "") {
    if (checkDisUrlTip(value)) {
      return Promise.resolve();
    } else {
      return Promise.reject('必须以http或https开头的网页链接！');
    }
  } 
  return Promise.resolve();
}

const rules = {
  imgUrl: [
    { required: true, message: '请上传资讯图片！' }
  ],
  infoAuthor: [
    { required: true, message: '请填写资讯作者！' }
  ],
  infoDate: [
    { required: true, message: '请填写资讯发布日期！' }
  ],
  infoTitle: [
    { required: true, message: '请填写资讯标题！' }
  ],
  jumpLinkApp: [
    { validator: (_rule: Rule, value: string) => validateUrl(_rule, value, '移动端链接'), trigger: 'change' }
  ],
  jumpLinkPc: [
    { validator: (_rule: Rule, value: string) => validateUrl(_rule, value, 'PC端链接'), trigger: 'change' }
  ],
  showStatus: [
    { required: true, message: '请选择展示状态！' }
  ],
};

const information= ref<IInformationType>({ showStatus: 1 });

const handleOk = () => {
  confirmLoading.value = true;
  if (!information.value.content) {
    if(!information.value.jumpLinkApp && !information.value.jumpLinkPc) {
      message.error(`链接和详情页不能同时为空!`);
      confirmLoading.value = false;
      return
    }
    else if(information.value.jumpLinkApp && information.value.jumpLinkPc) {

    }
    else {
      message.error(`PC端链接和移动端链接需要同时存在!`);
      confirmLoading.value = false;
      return
    }
  }
  from.value
    .validate()
    .then(() => {
      if (!information.value.isTopping) {
          information.value.isTopping = 0
        }
      if (information.value.id) {
        informationApi.edit(information.value).then(res => {
          message.success(`编辑成功!`);
          currentRouter.value.push({ path: "/portal/information/list"})
        })
      } else {
        informationApi.save(information.value).then(res => {
          message.success(`新增成功!`);
          currentRouter.value.push({ path: "/portal/information/list"})
        })
      }
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};

// 上传相关
const imageUrl = ref<string>(information.value.imgUrl || '')
const loading = ref<boolean>(false)
const oldFileList = computed(() => {
  if (information.value.imgUrl) {
    const data: UploadProps['fileList'] = {
      uid: '1',
      name: '图片.png',
      status: 'done',
      url: information.value.imgUrl,
    }
    return [data]
  } else {
    return null
  }
})

const fileList = ref(oldFileList.value || [])

const beforeUpload = (file: UploadFile) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
  if (!isJpgOrPng) {
    message.error('只能上传格式为png/jpg/jpeg的文件！');
  }
  const isLt5M = file.size && file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    message.error('文件大小不能超过5M!');
  }
  return isJpgOrPng && isLt5M;
}

const getBase64 = (img: any, callback: (base64Url: string) => void) => {
  const reader = new FileReader()
  reader.addEventListener('load', () => callback(reader.result as string))
  reader.readAsDataURL(img)
}

const handleChange = (info: UploadChangeParam) => {
  console.log(info)
  if (info.file.status === 'uploading') {
    loading.value = true;
    return;
  }

  if (info.file.status === 'done') {
    getBase64(info.file.originFileObj, (base64Url: string) => {
      imageUrl.value = base64Url;
      loading.value = false;
    })

    information.value.imgUrl = info.file.response.content.url
  }
  if (info.file.status === 'error') {
    loading.value = false;
    message.error('上传出错！');
  }
}

const action = import.meta.env.VITE_UPLOAD_URL

const onEditorChange = (editor: IDomEditor) => {
    information.value.content = editor.getHtml()
}

const uploadUrl = import.meta.env.VITE_UPLOAD_URL

const key = ref(0)

</script>

<script lang="ts">
  export default {
    name: "informationEdit",
  };
</script>

<template>
    <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
        <h-form ref="from" :model="information" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules" @finish="handleOk">
            <h-form-item label="资讯标题" name="infoTitle">
                <h-input v-model:value="information.infoTitle" />
            </h-form-item>
            <h-form-item label="资讯作者" name="infoAuthor">
                <h-input v-model:value="information.infoAuthor" />
            </h-form-item>
            <h-form-item label="资讯图片" name="imgUrl">
                <h-row>
                <h-col :span="24">
                    <h-upload v-model:file-list="fileList" accept="image/png,image/jpeg,image/jpg" :max-count="1"
                    class="avatar-uploader" list-type="picture-card" :show-upload-list="false" :action="action"
                    @change="handleChange" :before-upload="beforeUpload">
                    <img v-if="imageUrl" :src="imageUrl" alt="information" class="imgShow" />
                    <div v-else>
                        <loading-outlined v-if="loading"></loading-outlined>
                        <plus-outlined v-else></plus-outlined>
                        <div>上传图片</div>
                    </div>
                    </h-upload>
                </h-col>
                <h-col :span="24">
                    请上传图片的尺寸为<span class="important">72*56</span> 大小不超过<span class="important">2MB</span> 格式为<span
                    class="important">png/jpg/jpeg</span>的文件
                </h-col>
                </h-row>
            </h-form-item>
            <h-form-item label="PC端链接" name="jumpLinkPc">
                <h-textarea v-model:value="information.jumpLinkPc" />
            </h-form-item>
            <h-form-item label="移动端链接" name="jumpLinkApp">
                <h-textarea v-model:value="information.jumpLinkApp" />
            </h-form-item>
            <h-form-item v-if="isEdit" label="详情页" name="content">
                <editor :currentKey="key" height="300px" :modelValue="information.content" @change="onEditorChange" style="z-index: 20"
                :uploadUrl="uploadUrl" />
            </h-form-item>
            <h-form-item label="资讯发布日期" name="infoDate" :label-col="{ span: 4 }" :wrapper-col="{ span: 10 }">
                <h-date-picker v-model:value="information.infoDate" style="width: 100%" />
            </h-form-item>
            <h-form-item label="是否置顶" name="isTopping">
              <h-switch v-model:checked="information.isTopping" :checked-value="1" :un-checked-value="0" />
            </h-form-item>
            <h-form-item label="展示状态" name="showStatus">
                <h-select v-model:value="information.showStatus" allow-clear>
                <h-select-option :value="1">展示</h-select-option>
                <h-select-option :value="0">隐藏</h-select-option>
                </h-select>
            </h-form-item>
            <div class="submit-btn">
                <h-button type="primary" shape="round" html-type="submit" class="sub-btn">提交</h-button>
            </div>
        </h-form>
    </div>
</template>


<style lang="less" scoped>
.submit-btn {
  width: 100%;
  display: flex;
  justify-content: center;
  padding-bottom: 20px;
}

.sub-btn {
  width: 200px;
  
}

.important {
  color: red;
}


.imgShow {
  width: 100%;
}


.avatar-uploader {
  :deep(.bwdv-upload.bwdv-upload-select-picture-card) {
    width: 144px !important;
    height: 112px !important;
    overflow: hidden;
    border-radius: 8px;
  }
}
</style>
  