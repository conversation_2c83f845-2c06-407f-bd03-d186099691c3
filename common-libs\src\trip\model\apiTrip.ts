// 创建出差单接口参数
import { ICity } from '@haierbusiness-front/common-libs';

export interface ICreatTrip {
  /*页数 */
  pageNum?: number;

  username?: string;
  /*页码 */
  pageSize?: number;


  /*主键id */
  id?: number;

  /*出差人姓名 */
  travelUserName?: string

  /*申请单号 */
  applyNo?: string;

  /*原申请单号 */
  applyNoOld?: string;

  /*出差事由 */
  travelReason?: string;

  /*是否商旅预定 */
  travelReserveFlag?: number;

  /*费用预算总计 */
  amountSum?: Record<string, unknown>;

  /*数据批次 */
  dataVersion?: string;

  /*删除标识，0正常1删除，默认0 */
  deleted?: number;

  /*发送胜意状态10未发送20已发送 */
  syStatus?: string;

  /*预算状态10待占用20占用中30占用成功40占用失败50释放预算 */
  budgetStatus?: string;

  /*单据状态10待提交20待生效30出行中40行程结束50已完成90已作废 */
  status?: string;

  /*审批状态10未发起20审批中30审批通过40审批驳回50发起失败 */
  auditStatus?: string;

  /*变更状态10正常20变更中30已作废40历史版本 */
  changeStatus?: string;

  /*审批流程id */
  workFlowId?: string;

  /*审批失败原因 */
  workFlowFailInfo?: string;

  /*是否超标0未超标1已超标默认0 */
  excessiveFlag?: number;

  /*出发地编码 */
  beginCityCode?: string;

  /*出发地名称 */
  beginCityName?: string;

  /*出发地编码(胜意) */
  beginCityCodeSy?: string;

  /*目的地编码 */
  endCityCode?: string;

  /*目的地名称 */
  endCityName?: string;

  /*目的地编码(胜意) */
  endCityCodeSy?: string;

  /*出差日期始 */
  beginDate?: string;

  /*出差日期止 */
  endDate?: string;

  /*出差日期始(实际) */
  realBeginDate?: string;

  /*出差日期止(实际) */
  realEndDate?: string;

  /*申请人(经办人)id */
  operUserId?: number;

  /*申请人(经办人)工号 */
  operUserNo?: string;

  /*申请人(经办人)名称 */
  operUserName?: string;

  /*申请人(经办人)部门id */
  operDeptId?: number;

  /*申请人(经办人)部门名称 */
  operDeptName?: string;

  /*创建人 */
  createBy?: string;

  /*创建人名称 */
  createName?: string;

  /*最后一次修改人 */
  lastModifiedBy?: string;

  /*最后一次修改人名称 */
  lastModifiedName?: string;

  /*创建时间 */
  gmtCreate?: Record<string, unknown>;

  /*修改时间 */
  gmtModified?: Record<string, unknown>;

  /*出行人 */
  travelerList?: Array<ITraveler>;

  /*行程 */
  tripList?: Array<ITripList>;

  tripListHw?: Array<ITripList>;

  /*数据来源 AI HWORK */
  originApp?: string;
  

  /*附件 */
  fileList?: Array<IFile>;

   /*占用预算 */
   haierBudgetPayOccupyRequest?: {
    /*应用程序code 每个应用程序对应了多种支付方式 */
    applicationCode: string;

    /*业务自己进行控制需要显示哪些支付类型 如果为空则获取应用程序对应的全部支付方式 多种方式逗号分隔 */
    payTypes?: string;

    /*支付人账号 */
    username: string;

    /*订单号 */
    orderCode: string;

    /*订单供应商编码 PS: wyy */
    providerCode?: string;

    /*订单金额 保留两位小数 20.02 */
    amount: number;

    /*支付回调地址 */
    notifyUrl: string;

    /*支付成功后跳转地址 */
    callbackUrl?: string;

    /*订单详情url */
    orderDetailsUrl?: string;

    /*商务支付中台支付来源 1： pc 2： 移动端 */
    paySource?: Record<string, unknown>;

    /*订单描述 */
    description?: string;

    /*载荷 客户端自定义参数，将在后续回调业务系统 */
    payload?: string;

    /*支付请求时间戳 加签工具自动生成 */
    hbTimestamp?: string;

    /*随机字符串 加签工具自动生成 */
    hbNonce?: string;

    /*签名 加签工具自动生成 不参与验签 */
    sign?: string;

    /*业务类型 */
    businessType?: string;

    /*是否需要审批 */
    startApproveFlag?: boolean;

    /*审批流id */
    processId?: string;

    /* */
    feeItemCode?: string;

    /*费用项目 */
    feeItemName?: string;

    /* */
    extJsonParam?: string;

    /*预算来源，不同企业预算来源不同，海尔：BCC；GMS； */
    haierBudgetType?: string;

    /*预算人工号 */
    budgeterCode?: string;

    /*预算人工号 */
    budgeterName?: string;

    /*预算部门(预算主体) */
    budgetDepartmentCode?: string;

    /*结算单位（出账法人） */
    accountCompanyCode?: string;

    /*研发项目 */
    projectCode?: string;

    /*WBS */
    wbsCode?: string;

    /*立项 */
    itemCode?: string;

    /*费用项目编码 */
    feeItem?: string;

    /*销售类型,1：内销；2：外销； */
    saleType?: Record<string, unknown>;

    /*预算单元-gems */
    unitCode?: string;

    /*预算单元名称 */
    unitName?: string;

    /*地产项目 */
    dcProjectCode?: string;

    /*地产分期(编码为GUID) */
    dcItemCode?: string;

    /*财务组织编码，用于查hbc预算经理 */
    financialCode?: string;

    /*财务组织名称，用于查hbc预算经理 */
    financialName?: string;

    /*hbc预算经理 */
    budgetManager?: string;

    /*是否按部门占用预算 0否 1是 */
    isQueryDept?: Record<string, unknown>;

    /*月度账户id（hbc预算查询返回） */
    accountCode?: string;

    /*执行主体编码 */
    performCode?: string;

    /*执行主体名称 */
    performName?: string;

    /*成本中心编码 */
    costCenter?: string;

    /*成本中心名称 */
    costCenterName?: string;

    /*受益主体编码 */
    beneficialCode?: string;

    /*受益主体名称 */
    beneficialName?: string;

    /*用户选择的预算系统编码（预算查到的） */
    budgetSystemCode?: string;

    /* */
    isForce?: Record<string, unknown>;
  };
  

  /* */
  needPage?: boolean;
}
export interface IFile {
  /*主键id */
  id?: Record<string, unknown>;

  /*申请单号 */
  applyId?: Record<string, unknown>;

  /*附件预览地址 */
  filePath?: string;

  /*创建人 */
  createBy?: string;

  /*创建人名称 */
  createName?: string;

  /*最后一次修改人 */
  lastModifiedBy?: string;

  /*最后一次修改人名称 */
  lastModifiedName?: string;

  /*创建时间 */
  gmtCreate?: string;

  /*修改时间 */
  gmtModified?: string;
}
// 创建出差单响应接口
export interface Create_3Res {
  districts?: Array<ICity>;
}
// 行程与费用
export interface ITripList {
  // 前端逻辑必须字段
  // key
  key?: number,
  detailMap?: boolean,
  showMore?: boolean,

  // 选择的人员id列表
  personIdList?: Array<string>;

  // 
  beginCityName?: string;
  endCityName?: string;
  

  /*主键id */
  id?: Record<string, unknown>;

  /*申请单id */
  applyId?: Record<string, unknown>;

  /*行程出发地编码 */
  beginCityCode?: string;

  /*行程出发地编码-胜意 */
  beginCityCodeSy?: string;
  endProvinceCodeSy?: string;
  
  /*行程出发地 */
  beginCity?: string;

  /*行程目的地编码 */
  endCityCode?: string;

  /*行程目的地编码-胜意 */
  endCityCodeSy?: string;

  /*行程目的地 */
  endCity?: string;

  /*出差日期始 */
  beginDate?: string;

  /*出差日期始 */
  endDate?: string;

  /*出差日期始(实际) */
  realBeginDate?: string;

  /*主键id */
  realEndDate?: string;

  /*创建人 */
  createBy?: string;

  /*创建人名称 */
  createName?: string;

  /*最后一次修改人 */
  lastModifiedBy?: string;

  /*最后一次修改人名称 */
  lastModifiedName?: string;

  /*创建时间 */
  gmtCreate?: string;

  /*修改时间 */
  gmtModified?: string;

  /*行程明细关系集合 */
  tripDetailMapList?: Array<ITripDetailMap>;
};

// 火车三字码
export interface ITrainThree {
  cityCodeThree?: string;
  cityName?: string;
}

// 获取出差申请单车站信息

export interface IGetApplicationFormTrainStationByCityRes {
  /*胜意站点id */
  stationId: string;

  /*站点名称 */
  stationName: string;

  /*站点三字码 */
  stationCode: string;

  /*城市id */
  cityId: number;

  /*城市名称 */
  cityName: string;
}

export interface IGetApplicationFormTrainStationByCityReq {
  cityId?: string|number|undefined;
}

// 行程花费项目
export interface ITripDetailMap {
  budgetAmountSum?: number;
  budgetAmountSumBz?: number;
  budgetAmountDesc?: Array<object>;
  tarinList?: Array<ITrainThree>
  /*主键id */
  id?: Record<string, unknown>;

  /*申请单号 */
  applyId?: Record<string, unknown>;

  /*行程表id */
  tripId?: Record<string, unknown>;

  /*产品编码 */
  productCode?: string;

  /*产品名称 */
  productName?: string;

  /*费用预算总计 */
  budgetAmount?: number;

  /*是否超标0未超标1已超标默认0 */
  excessiveFlag?: Record<string, unknown>;

  /*创建人 */
  createBy?: string;

  /*创建人名称 */
  createName?: string;

  /*最后一次修改人 */
  lastModifiedBy?: string;

  /*最后一次修改人名称 */
  lastModifiedName?: string;

  /*创建时间 */
  gmtCreate?: string;

  /*修改时间 */
  gmtModified?: string;

  /*是否购买保险0否1是 */
  insuranceFlag?: number;

  /*保险金额，购买保险时必传 */
  insuranceAmount?: number;

  personIdList?: string[]

  /*行程明细集合 */
  travelApplyTripDetailList: Array<ITraveler>;
}

// 出行人
export interface ITraveler {
  /*主键id */
  id?: Record<string, unknown>;

  /*申请单号 */
  applyId?: Record<string, unknown>;

  /*出差人id(胜意) */
  travelUserSyId?: string;

  /*出差人名称 */
  travelUserName?: string;

  /*出差人工号 */
  travelUserNo?: string;

  /*出差人部门id */
  travelUserDeptId?: string;

  /*出差人部门名称 */
  travelUserDeptName?: string;

  /*出差人类型0出差人1外部出行人 */
  travelUserType?: string;

  /*是否主出差人0否1是 */
  mainFlag?: string;

  /*创建人 */
  createBy?: string;

  /*创建人名称 */
  createName?: string;

  /*最后一次修改人 */
  lastModifiedBy?: string;

  /*最后一次修改人名称 */
  lastModifiedName?: string;

  /*创建时间 */
  gmtCreate?: string;

  /*修改时间 */
  gmtModified?: string;
}

// 获取差旅费接口

export interface MemberBudgetParams {
  /**
 * 费用预算标准
 */
  budgetAmountBz?: number;
  /**
 * 是否超标0未超标1已超标默认0
 */
  excessiveFlag?: number;
  /*开始城市编码 */
  beginCityCode?: string;

  /*结束城市编码 */
  endCityCode?: string;

  /*开始城市编码（胜意） */
  beginCityCodeSy?: string;

  /*结束城市编码（胜意） */
  endCityCodeSy?: string;

  /*开始日期 */
  startDate?: string;

  /*结束日期 */
  endDate?: string;

  /*产品类型 */
  productNo?: string;

  /*是否购买保险0否1是 */
  insuranceFlag?: number;

  /*人员集合 */
  memberList?: {
    /*主键id */
    id?: Record<string, unknown>;

    /*申请单id */
    applyId?: Record<string, unknown>;

    /*行程表id */
    tripId?: Record<string, unknown>;

    /*行程产品关系表id */
    tripDetailMapId?: Record<string, unknown>;

    /*父级id;主要用于记录保险费和服务费归属 */
    parentId?: Record<string, unknown>;

    /*费用名称 */
    costName?: string;

    /*出差人员 */
    travelUserSyId?: string;

    /*出差人员名称 */
    travelUserName?: string;

    /*出差人员工编号 */
    travelUserNo?: string;

    /*出差人部门id */
    travelUserDeptId?: string;

    /*出差人部门名称 */
    travelUserDeptName?: string;

    /*出差人类型1出差人2外部出行人 */
    travelUserType?: string;

    /*是否主出差人人0否1是 */
    mainFlag?: string;

    /*费用预算 */
    budgetAmount?: number;

    /*是否超标0未超标1已超标默认0 */
    excessiveFlag?: Record<string, unknown>;

    /*超标原因 */
    excessiveReason?: string;

    /*差标 */
    differentialStandard?: string;

    /*产品类型1费用2保险3服务费 */
    productType?: Record<string, unknown>;

    /*保险金额 */
    insuranceAmount?: number;
  }[];
}
export interface MemberItem{ 
  /*员工id */
  travelUserSyId?: string;

  /*出差人名称 */
  travelUserName?: string;

  /*出差人工号 */
  travelUserNo?: string;

  /*产品名称 */
  productName?: string;

  /*费用 */
  amount?: number;
}

// 响应接口
export interface MemberBudgetRes {
  /* */
  data: {
    /*费用预算总计 */
    budgetAmountSum: Record<string, unknown>;

    /*出差人集合 */
    memberList: {
      /*员工id */
      travelUserSyId: string;

      /*出差人名称 */
      travelUserName: string;

      /*出差人工号 */
      travelUserNo: string;

      /*差标 */
      differentialStandard: string;
    }[];
    /*出差人费用集合 */
    memberBudgetList: Array<MemberItem>;
  };

  /* */
  code: string;

  /* */
  message: string;

  /* */
  success: boolean;
}

