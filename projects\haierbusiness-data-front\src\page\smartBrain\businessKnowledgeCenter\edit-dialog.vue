<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  Switch as hSwitch,
  CheckboxGroup as hCheckboxGroup,
  Checkbox as hCheckbox,
  Image as hImage,
} from 'ant-design-vue';
import {
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
} from '@ant-design/icons-vue';
import { computed, ref, watch } from 'vue';
import { fileApi } from '@haierbusiness-front/apis';
import type { Ref } from 'vue';
import { IndicatorData, Datum } from '@haierbusiness-front/common-libs';
import { de } from 'element-plus/es/locale';
import { Codemirror } from "vue-codemirror";
import { javascript } from "@codemirror/lang-javascript";
import { oneDark } from "@codemirror/theme-one-dark";
import type { Rule } from 'ant-design-vue/es/form';
const extensions = [javascript(), oneDark];

interface Props {
  show: boolean;
  data: IndicatorData;
  labelList: Array<Datum>;
}

const props = withDefaults(defineProps<Props>(), {
  show: false
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: IndicatorData = {
  entryName: '',
  entryContent: "",
  entryCategoryName:"",
  id: null,
};


const rules: Record<string, Rule[]> = {
  entryName: [{ required: true, message: '请输入词条名称' }],
  entryContent: [{ required: true, message: '请输入词条详细说明' }],
  entryCategoryName: [{ required: true, message: '请选择词条分类' }],
};

const indexData: Ref<IndicatorData> = ref(props.data?props.data:defaultData);

watch(props, (newValue) => {
  indexData.value = ({ ...newValue.data } as IndicatorData) || defaultData;
});

const emit = defineEmits(['cancel', 'ok']);

const visible = computed(() => props.show);

const handleOk = () => {
  confirmLoading.value = true;
  from.value
    .validate()
    .then(() => {
      emit('ok', indexData.value, () => {
        confirmLoading.value = false;
      });
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};
</script>

<template>
  <h-modal
    v-model:visible="visible"
    :title="indexData.id ? '编辑词条' : '新增词条'"
    :width="600"
    @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
  >
    <h-form ref="from" :model="indexData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" :rules="rules">
      <h-form-item label="词条名称" name="entryName">
        <h-input v-model:value="indexData.entryName" style="width: 100%" />
      </h-form-item>
      <h-form-item label="词条详细说明" name="entryContent">
        <h-textarea :rows="4" v-model:value="indexData.entryContent" style="width: 100%" />
      </h-form-item>
      <h-form-item label="词条分类" name="entryCategoryName">
        <h-select v-model:value="indexData.entryCategoryName" style="width: 100%">
                <h-select-option v-for="item in props.labelList" :value="item.catName">{{ item.catName }}</h-select-option>
        </h-select>
      </h-form-item>
      <h-form-item label="创建人">
        {{indexData.creatorName}}
      </h-form-item>
      <h-form-item label="修改人">
        {{indexData.updaterName}}
      </h-form-item>
    </h-form>
  </h-modal>
</template>

<style lang="less" scoped>
.important {
  color: red;
}
.imgItem {
  position: relative;
  display: inline-block;
  margin-right: 4px;
  .deleteIcon {
    position: absolute;
    right: 0px;
    z-index: 10;
  }
}
</style>
