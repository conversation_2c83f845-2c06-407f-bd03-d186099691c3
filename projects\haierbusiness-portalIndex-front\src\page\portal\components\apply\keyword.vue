<script setup lang="ts">
import { ref } from 'vue';
const emit = defineEmits(['keyWordChange'])

const value = ref('')
const changeKey = () => {
    emit('keyWordChange', value.value)
}
</script>


<template>
    <div class="apply-keyword-component">
        <div class="ticket-item">
            <div class="item-labels">关键词</div>
            <a-input v-model:value="value" @change="changeKey" placeholder="酒店名/地标/关键字" :bordered="false" :allow-clear="true" />
        </div>
    </div>
</template>

<style lang="less" scoped>
@import url('./common.less');

.apply-no {
    width:100%; 
    height: 22px;
}

.ant-input{
    border: 0;
    height: 22px;
    line-height: 22px;
    padding-left: 0;
    font-size: 16px;
    // font-weight: 600;
    
    color: rgba(0,0,0,0.85);
    &:focus{
        box-shadow:0 0 0 0 rgba(0,0,0,0);
    }
}

.ant-input-affix-wrapper {
    padding: 0;
}

</style>

<style>
.ant-input::-webkit-input-placeholder {
    font-size: 16px;
    font-weight: 500;
    font-family: "Microsoft YaHei";
    color: rgba(0,0,0,0.35) !important;
}
</style>
