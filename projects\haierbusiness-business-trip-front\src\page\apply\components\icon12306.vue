<template>
  <icon>
      <template #component>
        <svg t="1713513074501" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1479" width="14" height="14"><path d="M480.192 131.584A32.32 32.32 0 0 1 480 128V96a32 32 0 0 1 64 0v32c0 1.216-0.064 2.4-0.192 3.584C739.744 147.744 893.728 311.904 893.728 512c0 130.24-65.76 249.312-172.64 319.392a29.728 29.728 0 1 1-32.608-49.696A321.92 321.92 0 0 0 834.272 512c0-177.984-144.288-322.272-322.272-322.272-177.984 0-322.272 144.288-322.272 322.272a321.92 321.92 0 0 0 146.784 270.368 29.728 29.728 0 0 1-32.416 49.824A381.344 381.344 0 0 1 130.272 512c0-200.096 153.984-364.256 349.92-380.416zM480 896V544h-128a32 32 0 0 1 0-64h320a32 32 0 0 1 0 64h-128v352h256a32 32 0 0 1 0 64H224a32 32 0 0 1 0-64h256z" fill="#333333" p-id="1480"></path></svg>      </template>
    </icon>
</template>

<script lang="ts" setup>
import Icon from '@ant-design/icons-vue';
</script>