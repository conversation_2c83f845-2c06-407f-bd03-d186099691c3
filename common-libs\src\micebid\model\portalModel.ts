// 参数接口
export interface MiceBidCreateOrderParams {
  /*流程定义主表id */
  pdMainId?: number;

  /*会议名称 */
  miceName?: string;

  /*需求开始时间 */
  startDate?: string;

  /*需求结束时间 */
  endDate?: string;

  /*需求提报类型enum?:[[{"code"?:0,"desc"?:"用户提报"},{"code"?:1,"desc"?:"顾问代提"}]],可用值?:0,1 */
  demandType?: number;

  /*会议地点 国内/国际enum?:[[{"code"?:0,"desc"?:"国内"},{"code"?:1,"desc"?:"国际"}]],可用值?:0,1 */
  districtType?: number;

  /*会议人数 */
  personTotal?: number;

  /*会议类型enum?:[[{"code"?:1,"desc"?:"开盘会"},{"code"?:2,"desc"?:"客户会"},{"code"?:3,"desc"?:"培训会"},{"code"?:4,"desc"?:"发布会"},{"code"?:5,"desc"?:"展览会"},{"code"?:6,"desc"?:"内部沟通会"},{"code"?:7,"desc"?:"招商会"}]],可用值?:1,2,3,4,5,6,7 */
  miceType?: number;

  /*意向会务顾问工号 */
  intentionConsultantUserCode?: string;
}

export interface MiceBidConsultant {
  /*主键id */
  id?: number;

  /*工作年限 */
  seniority?: number;

  /*顾问介绍 */
  description?: string;

  /*图片路径 */
  path?: string;
}

export interface MiceBidCalendarFile {
  /*文件id */
  id?: number;

  /*文件路径 */
  path?: string;
}
export interface MiceBidCalendar {
  /*会议名称 */
  name?: string;

  /*会议时间 */
  time?: string;

  /*会议城市 */
  city?: string;

  /*会议地点 */
  place?: string;

  /*会议面积 */
  floorSpace?: number;

  /*会议来源 */
  meetingType?: number;

  /*文件地址 */
  pathList?: MiceBidCalendarFile[];

  //距开幕
  countdown:string
}

export interface MiceBidCalendarList {
  /*会议名称 */
  name?: string;

  /*会议时间 */
  time?: string;

  /*会议城市 */
  city?: string;

  /*会议地点 */
  place?: string;

  /*会议面积 */
  floorSpace?: number;


}