<template>
  <custom-com>
    <a-popover
      v-model:open="visible"
      class="city-popover"
      trigger="click"
      :destroyTooltipOnHide="true"
      placement="bottomLeft"
      :getPopupContainer="getPopupContainer"
      autoAdjustOverflow
    > 
      <template #title>
        <a-row justify="space-between">
          <a-col v-if="!props.defaultValue.applyNo">
            支持城市检索
          </a-col>
        </a-row>
      </template>
      <template #content>
        <div class="city-main-box">
          <!-- 输入检索 -->
          <a-input v-if="!props.defaultValue.applyNo" v-model:value="searchValue" allowClear autocomplete="off" placeholder="输入城市名称检索" />
          <!-- 输入检索列表 -->
          <div class="search-value-list" v-if="searchList?.length > 0">
            <a-list size="small" :data-source="searchList" :pagination="listPagination">
              <template #renderItem="{ item, index }">
                <a-list-item class="pointer" :class="activeIndex == index ? 'active' :''" @mouseenter="mouseenter(index)" @mouseleave="mouseleave" @click="choseCity(item)">
                  <div>{{ item.name }}</div>
                  <div>{{ item.provinceName }}</div>
                </a-list-item>
              </template>
            </a-list>
          </div>

          <div v-else>
            <!-- 国内城市 -->
            <a-tabs v-model:activeKey="activeKey">
              <a-tab-pane style="max-height: 300px; overflow-y: scroll;" v-for="tab in cityOptions" :key="tab.key" :tab="tab.label">
                <div class="city-list">
                  <div class="city-box" v-show="city?.children?.length > 0"  v-for="(city, index) in tab?.children" :key="index">
                    <div class="box-left">{{ city.label }}</div>
                    <div class="box-right">
                      <a-button class="btn" v-for="(item, index) in city?.children" :key="index" @click="choseCity(item)" type="text">
                        <span :title="item.name">{{item.name}}</span>
                      </a-button>
                    </div>
                  </div>
                </div>
              </a-tab-pane>
            </a-tabs>

          </div>
        
        </div>
      </template>
      <a-input :style="{width: props.width}" class="city-chose-input" allowClear readonly :bordered="props.bordered" v-model:value="props.value" :placeholder="props.placeholder" />
    </a-popover>
  </custom-com>
</template>

<script setup lang="ts">


import { computed, onMounted, reactive, ref, watch, watchEffect, toRefs, createVNode } from 'vue';
import { cityApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { CityResponse, CityItem, CityParams,CityOption } from '@haierbusiness-front/common-libs';
import { useCityStore } from '@haierbusiness-front/utils/src/store/city';
// 自定义组件
import { Form } from 'ant-design-vue';
const formItemContext = Form.useInjectFormItemContext();

const emit = defineEmits(['chosedCity']);
interface Props {
    width: string | number
    value: string
    placeholder?: string
    bordered?: boolean
    defaultValue?: any

    index?: number
    i?: number
    // 不可选择城市id
    eliminateSearchProvinceIds?: Array<any|string>
}

const getPopupContainer = (triggerNode: HTMLElement): HTMLElement  =>{
      // 返回触发元素的父节点作为弹出层的容器
      return triggerNode.parentNode as HTMLElement;
    }
const props = withDefaults(defineProps<Props>(), {
    value: '',
    bordered: true,
    placeholder: '请选择城市',
    index: 0,
    i: 0,
    width: '200px',
    defaultValue: {},
    eliminateSearchProvinceIds: () => []

});

const listPagination = {
  current : 1,
  defaultPageSize: 10,
  pageSize: 10,
  total: 0,
  size: 'small',
  showSizeChanger: false,
  onChange: (page: number) => {
    listPagination.current = page
    listDistrictBySyRun()
  },
}

// 国际城市---
const internationalKey = ref<number>(0)
const internationalTab = ref([
  {
    key: 0,
    label: '国内热门',
    value: '1'

  },

  {
    key: 1,
    label: '亚洲',
    value: '2'
  },
  {
    key: 2,
    label: '美洲',
    value: '3'
  },

  {
    key: 3,
    label: '欧洲',
    value: '4'

  },
  {
    key: 4,
    label: '大洋洲',
    value: '5'

  },
  {
    key: 5,
    label: '非洲',
    value: '6'

  },

  {
    key: 6,
    label: '国际/中国港澳台热门',
    value: '7'

  },

])

const ininternationalList = ref([])

const getIninternationalList = () => {
  const params = {
    level: 'city',
    internationalFlag: 1,
    districtPopularId: internationalTab.value[internationalKey.value].value
  }
  cityApi.getCityList(params).then(res => {
    ininternationalList.value = res.records
  })
}

// 获取热门
// ----

const activeIndex = ref<number>()
const searchValue = ref<string>('');
const visible = ref<boolean>(false);
const activeKey = ref<number>(999);

// const searchList = ref<Array<object>>([]);

const cityParams = ref<CityParams>({
  level: 'city',
  internationalFlag: 0,
  providerCode: 'VETECH',
  eliminateSearchProvinceIds: props.eliminateSearchProvinceIds

})

const cityOptions = ref<Array<CityOption>>([])
const cityObj = ref({})

const getCityList = async() => {
  const store = useCityStore();
  const { city } = store

  if (city?.length > 0) {
    cityOptions.value = city
    return
  }
  
  const params = {
    level: 'city',
    internationalFlag: 1,
    districtPopularId: 1,
    providerCode: 'VETECH',
  }
  const cityData = await cityApi.getCityList(params)

  cityApi.getCityList(cityParams.value).then(res => {
    if(cityParams.value.internationalFlag == 0) {
      // 如果是国内城市 剔除掉 台湾、香港、澳门的城市 provinceName 
      let citys = ['香港', '澳门', '台湾']
      res.records = res.records.filter(item => !citys.includes(item.provinceName));
    }
    
    cityObj.value = groupByForEach(res.records, 'initial')
    const cityInitialArr = Object.keys(cityObj.value)
    

    cityInitialArr.sort(function(a, b) {
      return a.localeCompare(b);
    });
    cityOptions.value =  chunkArray(cityInitialArr, 3,cityData.records)
    store.setDate(cityOptions.value)
  })
}

// 分组拼接国内城市数据-------

// 数组分层
const chunkArray = (array, chunkSize:number,cityData:any) => {
  let result = [
    {
      key: 999,
      label: '热门',
      children:[{
        label:"热门",
        children:cityData
      }]
    }
  ];
  for (let i = 0; i < array.length; i += chunkSize) {
    result.push({
      key: i,
      label: array.slice(i, i + chunkSize).join(' '),
      children: getChildrenByList(array.slice(i, i + chunkSize))
    });
  }
  console.log(result,"-----")
  return result;
}


// 根据分类数组填充子类数据
const getChildrenByList = (arr) => {
  let children = []
  arr.forEach(item => {
    children.push(
      {
        label: item,
        children: cityObj.value[item]
      }
    )
  })
  return children
}

// 城市数据分组
const groupByForEach = (arr: Array<CityItem>, prop:string) => {
    const grouped = {};
      arr.forEach(item => {
        const key = item[prop];
        if (!grouped[key]) {
          grouped[key] = [];
        }
        grouped[key].push(item);
      });
      return grouped;
}



const searchList = ref<Array<CityResponse>>([])

const listDistrictBySyRun = () => {
  const params = {
    name:searchValue.value,
    pageSize: 8,
    pageNum: listPagination.current,
    // level: 'city',
    types: '1,2,3,4',

    internationalFlag: cityParams.value.internationalFlag,
    providerCode: 'VETECH',
    eliminateSearchProvinceIds: props.eliminateSearchProvinceIds

  }
  cityApi.getCityList(params).then(res => {
    listPagination.total = res.total 
    searchList.value = res.records
  })
}

const radioChange = () => {
  searchList.value = []
  searchValue.value = ''
}



const choseCity = (city: CityItem) => {
  /*
     选择城市后 根据id获取胜意code  cityCode 
  */
  // cityApi.displaceProviderDistrictId({
  //   cityIdList: [city.id],
  //   providerCode: 'XC'
  // }).then(res => {
  //   if (res && res.length > 1) {
  //     city.citycode = res[0].cityCode
  //   }
  //   searchList.value = [];
  //   visible.value = false;
  //   searchValue.value = '';
  //   emit('chosedCity', city,props.index,props.i);
  // })


  // 暂时不转换 使用id
  city.citycode = city.id
  searchList.value = [];
  visible.value = false;
  searchValue.value = '';
  emit('chosedCity', city,props.index,props.i);
  
  
};

const mouseenter = (index: number) => {
  activeIndex.value = index
}

const mouseleave = () => {
  activeIndex.value = undefined
}

const hasCityOptions = () => {
  // 判断仓库中是否有城市数据
  if (localStorage.getItem('city')) {
    return true
  }
  return false
}

onMounted(() => {
  getCityList()
})
watch(
  internationalKey,
  () => {
    getIninternationalList()
  },
)

// 如果已经选择申请单
const planCityList = ref<any>([])

const defaultValue = ref(props.defaultValue)

watch(props, (newValue) => {
    defaultValue.value = newValue.defaultValue
})
watch(
  visible,
  (newValue) => {
      console.log('visible----->',newValue, defaultValue.value)
      if(newValue && defaultValue.value?.tripList?.length > 0) {
        let temp:any = []
        defaultValue.value?.tripList.forEach((item:any,index:number) => {
          if (index == 0) {
            searchList.value.push({
              cityCode: item.beginCityCode,
              name: item.beginCityName,
              syId: item.beginCityCodeSy,
              date: item.beginDate,
            });
          }
          searchList.value.push({
            cityCode: item.endCityCode,
            name: item.endCityName,
            syId: item.endCityCodeSy,
            date: item.endDate,
          });
        })
        searchList.value = searchList.value.filter((item:any, index:any, self:any) =>
          index === self.findIndex((t:any) => (
            t.cityCode === item.cityCode
          ))
        );
        console.log(1111, searchList.value)
      }else {
        searchValue.value = '';
        searchList.value = [];
      }
  }
)
const uniqueData = (data:any) => {
  data.filter((item:any, index:any, self:any) =>
    index === self.findIndex((t:any) => (
      t.cityCode === item.cityCode
    ))
  );
  return data
}
watch(
  searchValue,
  (newVal) => {
    if (!newVal) {
      searchList.value = [];
      return;
    }
    listPagination.current = 1
    listDistrictBySyRun();
  },
  {
    deep: true,
  },
);
</script>

<style scoped lang="less">
.city-main-box {
  width: 600px;
  .pointer{
    cursor: pointer;
  }
}
.city-list {
  .city-box {
    display: flex;
    border-bottom: 1px solid #eee;
    .box-left {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 10%;
      color: orange;
      font-size: 20px;
    }
  }
}
.box-right {
      display: flex;
      flex-wrap: wrap;
      flex: 1;
      .btn{
        width:105px;
        text-align: left;
        :deep(span){
          width:80px;
          display: block;
          white-space: nowrap; /* 确保文本在一行内显示 */
          overflow: hidden; /* 隐藏超出容器的文本 */
          text-overflow: ellipsis; /* 使用省略符号表示被截断的文本 */
        }
      }
      .internationalbtn{
        width:150px;
        :deep(span){
          width:120px;
        }
      }
    }
.active {
  color:#1e65df ;
}
</style>