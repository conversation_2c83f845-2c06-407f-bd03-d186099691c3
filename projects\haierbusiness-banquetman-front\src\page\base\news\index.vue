<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Cascader as hCascader,
  Switch as hSwitch
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons-vue';
import { banquetApi, download, banquetNewsApi } from '@haierbusiness-front/apis';
import {
  IApplyFilter,
  IApply,
  BanquetStatusEnum,
  BanquetApproveStatusEnum
} from '@haierbusiness-front/common-libs';
import { getEnumOptions } from '@haierbusiness-front/utils';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
const { loginUser } = storeToRefs(applicationStore(globalPinia));

import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import router from '../../../router'
// const router = useRouter()
import type { ShowSearchType } from 'ant-design-vue/es/cascader';

const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router
  handleTableChange({ current: 1, pageSize: 10 })

})

// 订单状态
const orderState = computed(() => {
  return getEnumOptions(BanquetStatusEnum, true);
});

const approveState = computed(() => {
  return getEnumOptions(BanquetApproveStatusEnum, true);
});


const columns: ColumnType[] = [
  {
    title: '经办人工号',
    dataIndex: 'creator',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '经办人姓名',
    dataIndex: 'creatorName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'createTime',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '通知标题',
    dataIndex: 'title',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },

  {
    title: '是否显示',
    dataIndex: 'reveal',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '排序',
    dataIndex: 'notificationSort',
    width: '60px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '100px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<IApplyFilter>({})

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(banquetNewsApi.list);


const reset = () => {
  cityCodeList.value = []
  searchParam.value = {}
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const goToDetail = (id: string | number) => {
  currentRouter.value.push({
    path: `/base/newsDetail`,
    query: {
      id: id
    }

  })
}

// 跳转编辑页面
const goToEdit = (id: string | number) => {
  currentRouter.value.push({
    path: `/base/addNews`,
    query: {
      id: id
    }

  })
}


const goToAdd = () => {
  currentRouter.value.push({
    path: `/base/addNews`,

  })
}



const switchChange = (val, record) => {
  const params = {
    id: record.id,
    showStatus: val
  }
  banquetNewsApi.updateNotification(params).then(res => {
    handleTableChange({ current: current.value, pageSize: 10 })
  })
}



const filter: ShowSearchType['filter'] = (inputValue, path) => {
  return path.some(option => option.chineseName.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
};


const cityCodeList = ref([])
watch(() => cityCodeList.value, (n: any, o: any) => {
  if (n && n.length > 0) {
    searchParam.value.cityCode = [n[1] || n[0]]
  } else {
    searchParam.value.cityCode = undefined
  }
});

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="creatorName">经办人:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="经办人" v-model:value="searchParam.creatorName" style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="creator">经办人工号:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="经办人工号" v-model:value="searchParam.creator" style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createTime">操作时间:</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="searchParam.createTime" :show-time="{
              hideDisabledOptions: true,
              defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('11:59:59', 'HH:mm:ss')],
            }" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" allow-clear />
          </h-col>

        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button style="margin-right: 10px" type="primary"
              @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" >
            <h-button type="primary" style="margin-right: 10px" @click="goToAdd">新增公告</h-button>
          </h-col>
        </h-row>


        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">

            <template v-if="column.dataIndex === 'reveal'">
              <h-switch :disabled="loginUser?.username != record.creator" v-model:checked="record.reveal" @change="switchChange($event, record)" />
            </template>

            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" v-if="loginUser?.username == record.creator" @click="goToEdit(record.id)">编辑</h-button>
              <h-button type="link" @click="goToDetail(record.id)">查看详情</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>


  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}
</style>
