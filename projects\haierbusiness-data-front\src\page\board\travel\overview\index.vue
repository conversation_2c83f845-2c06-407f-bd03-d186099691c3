<template>
    <div class="bg">
        <div class="main">
            <div class="title">商旅业务总览图</div>
            <picker>
                <template #dateType>
                    <div class="picker-item">
                        <el-select v-model="active" style="width: 100px" class="bigscreen">
                            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </div>
                </template>
            </picker>

            <div class="content">
                <h-row type="flex" :gutter="[20, 20]" justify="center" v-for="(item, i) in data" :key="i">
                    <common v-for="(each, index) in item.list" :key="index" :data="each" :layout="item.layout" gngj="0">
                    </common>

                </h-row>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import "element-plus/theme-chalk/dark/css-vars.css";
import { ElSelect, ElDatePicker, ElOption } from 'element-plus'
import {
    Badge as hBadge,
    Progress as hProgress,
    <PERSON><PERSON> as hButton,
    Col as hCol,
    DatePicker as hDatePicker,
    Form as hForm,
    FormItem as hFormItem,
    Input as hInput,
    Modal as hModal,
    Popconfirm as hPopconfirm,
    Popover as hPopover,
    RangePicker as hRangePicker,
    Row as hRow,
    Select as hSelect,
    SelectOption as hSelectOption,
    Table as hTable,
    Tag as hTag,
    message,
    TableProps
} from 'ant-design-vue';
import "element-plus/theme-chalk/dark/css-vars.css";
import Picker from "../../components/picker.vue";
import Accumulative from "./components/accumulative.vue";
import AirTicket from "../airTicket/components/businessTrend.vue";
import TrainBusinessTrend from "../trainTicket/components/businessTrend.vue";
import TaxiBusinessTrend from "../taxi/components/businessTrend.vue";
import HotelBusinessTrend from "../hotel/components/businessTrend.vue";
import InsurePercentage from "./components/insurePercentage.vue";
import TravelApplicationStatus from "./components/travelApplicationStatus.vue";
import common from "./components/common.vue";
import GroundServices from "./components/groundServices.vue";
import ExpenseType from "./components/expenseType.vue";
import dayjs from "dayjs";
import ServiceCharge from "./components/serviceCharge.vue";
import { ref, onMounted } from "vue";



import { boardApi } from '@haierbusiness-front/apis';

const data = ref<any>([])
onMounted(async () => {
    const res = await boardApi.getBulletinBoardConfig({ url: '/data/board/travel/index' })
    if (res.bulletinBoardJson) {
        data.value = JSON.parse(res.bulletinBoardJson)
    }

})
function refreshData() {
  // 这里是刷新数据的代码
  location.reload();
}

function startRefresh() {
  var now = new Date(); // 获取当前时间
  var nextRefresh = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 6, 0, 0); // 设置下一个刷新时间为今天的6点
  if (now.getHours() >= 6) {
    nextRefresh.setDate(now.getDate() + 1); // 如果当前时间已经过了6点，则下一个刷新时间设定为明天的6点
  }

  var timeToRefresh = nextRefresh.getTime() - now.getTime(); // 计算距离下一个刷新时间还有多长时间（单位：毫秒）

  setTimeout(function () {
    refreshData(); // 刷新数据
    setInterval(refreshData, 12 * 60 * 60 * 1000); // 设置每隔12小时刷新一次数据
  }, timeToRefresh);
}
onMounted(() => {
  startRefresh();
});
const options = [
    {
        label: "年",
        value: 0,
    },
    {
        label: "月",
        value: 1,
    },
    {
        label: "日",
        value: 2,
    },
];
const active = ref(1);
</script>
<style scoped lang="less">
@import url(../../main.less);

.content {
    margin-top: 3vh;
}
</style>
