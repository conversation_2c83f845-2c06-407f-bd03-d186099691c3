import { download, get, post } from '../request'
import { 
    IServiceProviderDetailFilter, 
    IServiceProviderDetail,
    IPageResponse, 
    Result } from '@haierbusiness-front/common-libs'


export const serviceProviderDetailApi = {
    list: (params: IServiceProviderDetailFilter): Promise<IPageResponse<IServiceProviderDetail>> => {
        return get('/merchant/api/serviceProviderDetail/list', params)
    },

    get: (id: number): Promise<IServiceProviderDetail> => {
        return get('/merchant/api/serviceProviderDetail/get', {
            id
        })
    },

    save: (params: IServiceProviderDetail): Promise<Result> => {
        return post('merchant/api/serviceProviderDetail/save', params)
    },

    edit: (params: IServiceProviderDetail): Promise<Result> => {
        return post('merchant/api/serviceProviderDetail/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('merchant/api/serviceProviderDetail/delete', { id })
    },
}
