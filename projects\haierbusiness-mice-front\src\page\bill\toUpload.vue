<script setup lang="ts">
import hBase from '../../components/conference/detail/conference_detail_base.vue'
import hBill from './bill.vue'
import { ref, watch, onMounted } from 'vue';
import router from '../../router'
import {
    isMobile
} from '@haierbusiness-front/utils';
import { useResizeObserver } from '@vueuse/core'

const currentRouter = ref()
const code = ref<string>('')
const target = ref<'PC' | 'MOBILE'>('PC')

onMounted(async() => {
    currentRouter.value = await router
    if(isMobile()) {
        target.value = 'MOBILE'
    } else {
        target.value = 'PC'
    }
    code.value = currentRouter.value.currentRoute.query?.code ?? ''
})

const billUpload = ref()

const communicationUrl = import.meta.env.VITE_BUSINESS_COMMUNICATION_URL

watch(() => billUpload.value, (newValue) => {
    if(newValue) {
        useResizeObserver(billUpload.value, (el) => {
            const entry = el[0]
            const { height } = entry.contentRect
            console.log('高度', height);
            window.parent.postMessage({ height }, communicationUrl);
        })
    }
}, { deep: true })

</script>

<template>
    <div class="content" ref="billUpload">
        <div class="body">
            <div class="top-title">账单上传</div>
            <div class="space">
                <h-base :show="true" :code="code" :flag="1" />
            </div>
            <div class="space">
                <h-bill />
            </div>
        </div>
        
    </div>
    
</template>

<style scoped lang="less">
    .content {
        display: flex;
        width: 100%;
        flex-direction: column;
        align-items: center;
        overflow: hidden;

        .body {
            width: 90%;

            .space {
                margin: 20px 0;
            }
        }
    }
</style>