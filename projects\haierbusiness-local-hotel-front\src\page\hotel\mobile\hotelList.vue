<template>
  <div class="book-list">
    <van-sticky>

      <div class="search-img">
        <div class="search-box">
          <!-- 时间查询 -->
          <div class="search-time flex justify-content-between align-items-center">
            <div class="search-time-left flex align-items-center">
              <div class="search-time-start flex" @click="openTimePicker('begin')">
                <div class="search-time-text">
                  <span>{{dayjs(defaultParams.checkIn).month() +1}}</span>月<span>{{dayjs(defaultParams.checkIn).date()}}</span>日
                </div>
                <div class="search-time-text-small ml-5">{{ timeToStr(defaultParams.checkIn)}}</div>
              </div>
              <div class="ml-32 mr-32" style="font-weight: bold;">—</div>
              <div class="search-time-start flex" @click="openTimePicker('end')">
                <div class="search-time-text">
                  <span>{{dayjs(defaultParams.checkOut).month() +1}}</span>月<span>{{dayjs(defaultParams.checkOut).date()}}</span>日
                </div>
                <div class="search-time-text-small ml-5">{{timeToStr(defaultParams.checkOut)}}</div>
              </div>
            </div>
            <div class="search-time-right">
              共{{dayjs(defaultParams.checkOut).diff(defaultParams.checkIn, 'day')}}晚
            </div>
          </div>
          <van-divider class="mt-32 mb-32 bg-999" />
          <!-- 关键字 -->
          <van-search
            v-model="defaultParams.keyword"
            placeholder="请输入酒店名称/位置"
            @search="onSearch"
            style="padding: 0;"
            class="my-search"
            autocomplete="off"
            >
            <template #left-icon>
              <van-icon name="search" class="search-icon-color"  @click="onSearch" />
            </template>
            
          </van-search>
          <van-divider class="mt-32 mb-32 bg-999" />
          <!-- 查询按钮 -->
           <div class="search-btn-box">
            <van-button class="search-box-btn" round type="primary" size="small" @click="onSearch">酒店查询</van-button>
           </div>
        </div>
      </div>

     

      <!-- 下拉筛选菜单 -->
      <van-dropdown-menu ref="menuRef" class="my-dropdown" :close-on-click-outside="false">
        <van-dropdown-item :title-class="defaultParams.regionIds?.length> 0 ? 'active-dropdown-item' : ''" @opened="isAreaOpen = 1" @closed="isAreaOpen = 0">
          <template #title>
            <div class="mr-10 font-size-vant">区域</div>
            <div class="default-down-icon">
            </div>
          </template>
          <van-tree-select @click-nav="defaultParams.regionIds =[]" v-model:main-active-index="defaultParams.regionType" @click-item="changeRegin" v-model:active-id="defaultParams.regionIds"  :items="areaList" />
        </van-dropdown-item>
        <!-- 价格 -->
        <van-dropdown-item :title-class="defaultParams.consumptionPerStartY || defaultParams.consumptionPerEndY  ? 'active-dropdown-item' : ''"  @opened="isPriceOpen = 1" @closed="isPriceOpen = 0">
          <template #title>
            <div class="mr-10 font-size-vant">价格</div>
            <div class="default-down-icon">
            </div>
          </template>
          <van-cell-group class="mb-10">
            <van-field label-align="top">
              <template #label>
                <div class="flex width100 justify-content-between align-items-center">
                  <div class="left">
                    <span class="color-main">人均价格</span>
                  </div>
                  <div class="right color-main" @click="reSetMoney">重置</div>
                </div>
              </template>

              <template #input>
                <div class="flex justify-content-between align-items-center">
                  <div class="left flex">
                    <van-field autocomplete="off" v-model.number="defaultParams.consumptionPerStartY" type="digit" placeholder="最低价" />
                    <span class="flex align-items-center">—</span>
                    <van-field autocomplete="off" :min="defaultParams.consumptionPerStartY" v-model.number="defaultParams.consumptionPerEndY" type="digit" placeholder="最高价" />
                  </div>
                  <van-button type="primary" size="small" style="width: 80px" @click="onSearch">查询</van-button>
                </div>
              </template>
            </van-field>
          </van-cell-group>
        </van-dropdown-item>
        <!-- 星级 -->
        <van-dropdown-item :title-class="defaultParams.starLevels?.length> 0 ? 'active-dropdown-item' : ''" @opened="isStarOpen = 1" @closed="isStarOpen = 0">
          <template #title>
            <div class="mr-10 font-size-vant">星级</div>
            <div class="default-down-icon">
            </div>
          </template>

          <van-cell-group>
            <van-cell title="全选" clickable >
              <template #right-icon>
                <van-checkbox v-model="isCheckAllXj" :indeterminate="isIndeterminateXj" @change="checkAllChangeXj">
                </van-checkbox>
              </template>
            </van-cell>
          </van-cell-group>

          <van-checkbox-group v-model="defaultParams.starLevels" @change="checkedResultChangeXj">
            <van-cell-group>
              <van-cell
                v-for="(item, index) in hotalStarList"
                clickable
                :key="index"
                :title="item.text"
                @click="toggleXj(index)"
              >
                <template #right-icon>
                  <van-checkbox :name="item.value" :ref="(el) => (checkboxRefsXj[index] = el)" @click.stop />
                </template>
              </van-cell>
              <van-cell>
                <van-row class="width100" justify="space-between">
                  <van-button round style="width: 48%" @click="defaultParams.starLevels = []">重置</van-button>
                  <van-button round style="width: 48%" type="primary" @click="onSearch">确定</van-button>
                </van-row>
              </van-cell>
            </van-cell-group>
          </van-checkbox-group>
        </van-dropdown-item>
      </van-dropdown-menu>
    </van-sticky>

    <van-list
      class="hotel-list-box"
      v-model:loading="hotelLoading"
      :finished="hotelFinished"
      :finished-text="hotelList.length ? '没有更多了' : ''"
      @load="loadHotelList"
      :immediate-check="false"
    >

        <van-cell class="hotel-item" v-for="(item, index) in hotelList" :key="index" >
          <div class="flex align-items-center mb-10" @click="goToDetail(index)">
            <div class="hotel-img mr-10">
              <img style="width: 100%; height:100%;object-fit: cover;" :src="getImgUrl(item.album)" alt="">
            </div>
            <div class="main flex-1">
              <div style="position: relative;">
                <van-text-ellipsis class="color-main hotel-item-title" :content="item.fullname" />
                <div class="switch-box" @click.stop="item.open = !item.open">
                  <img :src="item.open ? closeIcon :  openIcon" alt="">
                </div>
              </div>

              <van-text-ellipsis class="hotel-item-foods" :content="getFoodTypeStr(item.cateType)" />

              <van-text-ellipsis class="hotel-item-address" rows="1" :content="item.address" />

              <div class="flex flex-center font-size-22 hotel-item-juli">
                <div class="felx flex-center"><img class="train-img" :src="trainIcon" />距离火车站{{item.railwayDistance || 0}}km</div>
                <van-divider vertical />
                <div><img class="train-img" :src="airIcon" />距离飞机场{{item.airportDistance || 0}}km</div>
              </div>
              <div class="mt-12 flex">
                <span class="tag" 
                  v-for="(mark, markI) in item.landMark"
                  v-show="markI < 2"
                  :key="markI">
                  {{ mark.name }}
                </span>
              </div>
              
            </div>
          </div>
          <!-- 房型数据 -->
          <div v-if="item.open">
            <div v-if="item.roomList && item.roomList.length > 0">
              <!-- 房型 -->
              <div class="items-rooms-list mb-10">
                <roomList :check-in="defaultParams.checkIn" :check-out="defaultParams.checkOut" :roomList="item.roomList"></roomList>
              </div>
              <!-- 预订 -->
              <div class="item-btns">
                <van-button size="small" round class="mr-10 per"  @click="goToReservation('private', index)">因私预订</van-button>
                <van-button size="small" round class="com" @click="goToReservation('public', index)">因公预订</van-button>
                <!-- <van-button class="com" @click="handleBook">因公预订</van-button> -->


              </div>
            </div>
            <div v-else>
              <van-empty description="暂无合适房型" />
            </div>
          </div>
          
        </van-cell>
    </van-list>
    <van-empty v-if="!hotelLoading && hotelList.length == 0" description="暂无数据" />

    

    <!-- 订餐折扣政策 -->
    <van-dialog v-model:show="discountDialog" :close-on-click-overlay="true" title="折扣政策" >
      <div style="padding:0 20px; height: 400px; overflow-y: auto;" @scroll="handleScroll">
      
        <div style="font-size: 12px; font-weight: bold; text-align: center; margin-bottom: 10px;">2024年青岛市协议酒店折扣政策</div>
        <table border="1" cellspacing="0" cellpadding="5" style="text-align: center; font-size: 8px;">
          <tbody>
            <tr>
              <td rowspan="2">区域</td>
              <td rowspan="2">酒店名称</td>
              <td colspan="3">资源政策</td>
            </tr>
            <tr>
              <td>服务费</td>
              <td>中餐折扣</td>
              <td>自助餐价格</td>
            </tr>
            <tr v-for="(item,index) in hotelsData" :key="index">
              <td style="width:100px;">{{item.regionName}}</td>
          <td style="width:200px;">{{item.fullname}}</td>
          <td style="width:100px;">{{item.serviceFee}}%</td>
          <td style="width:300px;">{{item.discountPolicy ||'-'}}</td>
          <td style="width:100px;">{{item.buffetPrices ? item.buffetPrices + '元/人':'-' }}</td>
            </tr>
          </tbody>
        </table>

      </div>
      <template #footer>
        <van-row class="width100 " style="padding: 10px 0;" justify="center" >
          
          <van-button type="primary" size="small"  @click="discountDialog = false" >
            <span>已阅读</span>
          </van-button>
        </van-row>
      </template>
    </van-dialog>

    <!-- 折扣政策  -->
    <!-- <van-floating-bubble v-model:offset="offset" position="left" @click="discountDialog = true" axis="y" :gap="4" >
      <template #default>
        <div>
          折扣
          <br />
          政策
        </div>
      </template>
    </van-floating-bubble> -->


    <van-tabbar v-model="active" route class="tabbar">
      <van-tabbar-item replace to="/hotel/hotelList">
        <span class="fs-24">酒店预订</span>
        <template #icon="props">
          <img :src="props.active ? bookIcon.active : bookIcon.inactive" class="book-icon" />
        </template>
      </van-tabbar-item>

      <van-tabbar-item replace to="/hotel/orderList">
        <span class="fs-24">订单中心</span>
        <template #icon="props">
          <img :src="props.active ? orderIcon.active : orderIcon.inactive" class="book-icon" />
        </template>
      </van-tabbar-item>
    </van-tabbar>

     <!-- 入住日期选择 -->
    <van-popup v-model:show="showRqPicker" round position="bottom">
      <van-date-picker v-model="currentRq" title="选择日期" :min-date="minDate" :max-date="maxDate" @confirm="confirmRq"
        @cancel="showRqPicker = false" />
    </van-popup>

  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, onBeforeUpdate,watch } from 'vue';
import type { Ref } from 'vue';
import { localHotelApi } from '@haierbusiness-front/apis';

import { RHotelParams } from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { vShow } from 'vue';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

import roomList from './roomList.vue';
import { showToast,showDialog } from 'vant';

dayjs.extend(relativeTime)


const isAreaOpen = ref(0)
const isPriceOpen = ref(0)
const isStarOpen = ref(0)

const router = getCurrentRouter();

const value = ref('');

// 
const active = ref(0);


const bookIcon = {
  active: new URL('@/assets/image/hotel/tab-hotel-active.png', import.meta.url).href,
  inactive: new URL('@/assets/image/hotel/tab-hotel-default.png', import.meta.url).href,
};

const orderIcon = {
  active: new URL('@/assets/image/hotel/tab-list-active.png', import.meta.url).href,
  inactive: new URL('@/assets/image/hotel/tab-list-default.png', import.meta.url).href,
};

const trainIcon = new URL('@/assets/image/hotel/train.png', import.meta.url).href;
const airIcon = new URL('@/assets/image/hotel/air.png', import.meta.url).href;

const openIcon = new URL('@/assets/image/hotel/open.png', import.meta.url).href;
const closeIcon = new URL('@/assets/image/hotel/close.png', import.meta.url).href;


// 跳转预定
const orderType = ref('')
const handleBook = () => {

  showDialog({
    message: '功能建设中',
  }).then(() => {
    // on close
  });
}
const goToReservation = (val: string,index: string) => {

  const selectedRooms = hotelList.value[index].roomList.filter(item => item.select)

  if (selectedRooms.length < 1) {
    showToast('请先选择房型')
    return
  }
  orderType.value = val
  localStorage.setItem('roomList', JSON.stringify(hotelList.value[index].roomList))
  goBooking(hotelList.value[index].id)
  
};
const goBooking = (id:number|string) => {
  router.push({ path: '/hotel/book', query: { type: orderType.value , hotelId: id,  checkIn: defaultParams.value.checkIn, checkOut: defaultParams.value.checkOut } });
}

const onChange = () => {};
// 查询订餐折扣政策
const hotelsData = ref([])
const discountDialog= ref<boolean>(false);

const offset = ref({ x: 5, y: 400 });

const dataList = () => {
  const params = {}
  localHotelApi.dataList(params).then(res => {
    hotelsData.value = res.data
  })
}

const goToSearch = () => {
  router.push({ path: '/hotel/search'});
}

// 选择就餐日期
const minDate = ref((dayjs()).toDate());
const maxDate = ref(dayjs().add(1, 'year').toDate());
const choseTimeType = ref('')
const currentRq = ref<Array<string>>([]);

const showRqPicker = ref<boolean>(false);
const confirmRq = ({ selectedValues }: any) => {
  if (choseTimeType.value == 'begin') {
    defaultParams.value.checkIn = selectedValues.join('-');
  } else {
    defaultParams.value.checkOut = selectedValues.join('-');
  }
  showRqPicker.value = false;
  onSearch()
};


  // 申请单的时间需要加限制，开始和结束日期区间最多是10天
const openTimePicker = (type: string) => {
  choseTimeType.value = type;
  currentRq.value = [];
  minDate.value = dayjs().toDate();
  maxDate.value = dayjs().add(1, 'year').toDate();


  if (type == 'begin') {
    if (defaultParams.value.checkIn) {
      if (dayjs().isBefore(dayjs(defaultParams.value.checkIn))) {
        currentRq.value = defaultParams.value.checkIn?.split('-');
      } else {
        currentRq.value = []
      }
    }
    if (defaultParams.value.checkOut) {
      maxDate.value = (dayjs(defaultParams.value.checkOut).subtract(1, 'day')).toDate();
      // 最早只能选今天
      if (dayjs().isAfter(dayjs(minDate.value))) {
        minDate.value = (dayjs()).toDate();
      }
    }

  } else {
    if (defaultParams.value.checkOut) {
      if (dayjs().isBefore(dayjs(defaultParams.value.checkOut))) {
        currentRq.value = defaultParams.value.checkOut?.split('-');
      } else {
        currentRq.value = []
      }
    }
    if (defaultParams.value.checkIn) {
      minDate.value = (dayjs(defaultParams.value.checkIn)).add(1,'day').toDate();
    }
  }
  showRqPicker.value = true;

};



// 选择的时间跟现在时间对比
const timeToStr = (time:string) => {
  const now = dayjs().format('YYYY-MM-DD');
  const days = dayjs(time).diff(now, 'day') // 7
  let timeStr = ''
  switch (days) {
    case 0:
      timeStr = '今天'
    break;
    case 1:
      timeStr = '明天'
    break;
    case 2:
      timeStr = '后天'
    break;
    default:
      timeStr = `${days}天后`
  }
  return timeStr
}

// 下拉菜单相关
const activeIds = ref([1, 2]);
const menuRef = ref()
const activeIndex = ref(0);
const areaList = ref<any>([]);
const clickTreeItem = (val) => {
console.log(999, val)
}
interface ComObj {
  id?: string;
  name?: string;
}

const changeRegin = (val) => {
  console.log(999, val)
  if (val.id == 999) {
    if (defaultParams.value.regionIds.length == areaList.value[0].children.length -1) {
      defaultParams.value.regionIds = []
    }else {
      const idList = areaList.value[0].children.map(item => item.id)
      defaultParams.value.regionIds = idList
    }
  }else {
    if (defaultParams.value.regionIds.length == areaList.value[0].children.length -2) {
      const idList = areaList.value[0].children.map(item => item.id)
      defaultParams.value.regionIds = idList
    }else {
      defaultParams.value.regionIds = defaultParams.value.regionIds.filter(item => item != 999); // 过滤掉等于999
    }
  }
  
  defaultParams.value.pageNum= 0;
  hotelList.value = []
  loadHotelList()
}
//  --------餐类选择相关
const isCheckAllCl = ref(false);
const foodTypeList = ref<Array<ComObj>>([]);

const isIndeterminateCl = ref(false);
const checkboxRefsCl = ref([]);
const toggle = (index) => {
  checkboxRefsCl.value[index].toggle();
};
onBeforeUpdate(() => {
  checkboxRefsCl.value = [];
});

const checkAllChangeCl = (val: boolean) => {
  let checkedCount = defaultParams.value.cateTypeIds.length;
  if (val) {
    defaultParams.value.cateTypeIds = foodTypeList.value.map((item) => item.id);
  } else if (checkedCount === foodTypeList.value.length) {
    defaultParams.value.cateTypeIds = [];
  }
  isIndeterminateCl.value = false;
};

const checkedResultChangeCl = (value: string[]) => {
  const checkedCount = value.length;
  isCheckAllCl.value = checkedCount === foodTypeList.value.length;
  isIndeterminateCl.value = checkedCount > 0 && checkedCount < foodTypeList.value.length;
};
//  --------餐类选择相关

// --------星级选择相关

const hotalStarList = [
  { text: '白金五星级', value: 55 },
  { text: '五星级', value: 50 },
  { text: '准五星', value: 45 },
  { text: '四星级', value: 40 },
  { text: '准四星', value: 35 },

  { text: '三星级', value: 30 },
  { text: '准三星', value: 25 },
  { text: '二星级', value: 20 },
  { text: '准二星', value: 15 },

  { text: '一星级', value: 10 },
  { text: '准一星', value: 5 },
];
const checkboxRefsXj = ref([]);
const toggleXj = (index) => {
  checkboxRefsXj.value[index].toggle();
};

const isCheckAllXj = ref(false);
const isIndeterminateXj = ref(false);

const checkAllChangeXj = (val: boolean) => {
  let checkedCount = defaultParams.value?.starLevels?.length;
  if (val) {
    defaultParams.value.starLevels = hotalStarList.map((item) => item.value);
  } else if (checkedCount === hotalStarList.length) {
    defaultParams.value.starLevels = [];
  }
  isIndeterminateXj.value = false;
};

const checkedResultChangeXj = (value: string[]) => {
  const checkedCount = value.length;
  isCheckAllXj.value = checkedCount === hotalStarList.length;
  isIndeterminateXj.value = checkedCount > 0 && checkedCount < hotalStarList.length;
};

// --------------星级选择相关


const hotelLoading = ref<boolean>(false);
const hotelFinished = ref<boolean>(false);
const hotelList = ref<any>([]);
const hotelTotal = ref<number>(0);
  
const defaultParams = ref<any>({

  checkIn: dayjs().format('YYYY-MM-DD'),
  checkOut: dayjs().add(1, 'day').format('YYYY-MM-DD'),
  cityId: 88,
  keyword: null, // 关键字查询


  regionType: 0, // 0
  pageNum: 0,
  pageSize: 10,
  regionIds: [], // 区域查询
  starLevels: [], // 星级

  roomPriceStart: null, //人均消费 分
  roomPriceEnd: null, //人均消费 分
  cateTypeIds: [], // 餐类
});



const loadHotelList = () => {
  if(!defaultParams.value.keyword) {
    defaultParams.value.keyword = null
  }
  defaultParams.value.pageNum++;
  // 如果为选择星际,删除掉
  if (defaultParams.value?.starLevels?.length < 1) {
    delete defaultParams.value.starLevels;
  }
  localHotelApi.hotelList(defaultParams.value).then((res) => {
    res.data.forEach((item:any) => {
      item.open = false
      for (let room of item.roomList) {
        room.select = false;
      }
    });
    // 加载状态结束
    hotelLoading.value = false;
    hotelList.value = [...hotelList.value, ...res.data];
    hotelTotal.value = res.total ?? 0;
    // 数据全部加载完成
    if (hotelList.value.length >= hotelTotal.value) {
      hotelFinished.value = true;
    }
  });
};

interface cateType {
  id: string;
  name: string;
}
const getFoodTypeStr = (list: Array<cateType>) => {
  if (list && list.length > 0) {
    return list.map((item) => item.name).join(' / ');
  }
};

const goToDetail = (index: string) => {
  router.push({ path: '/hotel/hotelDetail', query: { hotelId: hotelList.value[index].id, checkIn: defaultParams.value.checkIn, checkOut: defaultParams.value.checkOut }});
};

const businessList = import.meta.env.VITE_BUSINESSTRAVEL_URL;
const getImgUrl = (imgList) => {
  const food = []
    const restaurant = []
    const room = []
  if (imgList && imgList.length > 0) {
    
    // var type: Short? = null  //0：普通图片； 1：大堂；  2： 外景； 3： 餐厅； 4： 客房
    imgList?.forEach((item) => {
      switch (item.type) {
        case 0:
          food.push(item);
          break;
        case 1:
          room.push(item);
          break;
        case 2:
          restaurant.push(item);
          break;
        case 3:
          room.push(item);
          break;
        case 4:
          room.push(item);
          break;

        default:
          break;
      }
    });
  }
  if (restaurant.length > 0) {
    return `${businessList}api/common/v1/file/download/${restaurant[0].id}`
  } else if (room.length > 0) {
    return `${businessList}api/common/v1/file/download/${room[0].id}`

  }else if(food.length >0) {
    return `${businessList}api/common/v1/file/download/${food[0].id}`

  }else {
    return ''
  }
}


const onSearch = () => {
  defaultParams.value.pageNum= 0;
  hotelList.value = []
    loadHotelList()
    menuRef.value.close()
}

const reSetMoney = () => {
  defaultParams.value.consumptionPerStartY = '';
   defaultParams.value.consumptionPerEndY = ''
}

// 菜系
const cateClassList = ref<Array<ComObj>>([])
const cateClassActive = ref<number|string>('');


watch(
  () => defaultParams.value.consumptionPerStartY,
  (val: string|number) => {
    if (!val) {
      defaultParams.value.roomPriceStart = null
    }else {
      defaultParams.value.roomPriceStart = defaultParams.value.consumptionPerStartY * 100
    }
  },
)

watch(
() => defaultParams.value.consumptionPerEndY,
  (val: string|number) => {
    if (!val) {
      
      defaultParams.value.roomPriceEnd = null
    }else {
      defaultParams.value.roomPriceEnd = defaultParams.value.consumptionPerEndY * 100
    }
  },
)

// 查询酒店菜系数据
const getCateClass = () => {
  return new Promise((resolve, reject)=>{

      const params = {}
      localHotelApi.findCateClass(params).then(res => {
        cateClassList.value = res.data
        cateClassList.value.unshift(
          {
            name: '全部',
            id: '0'
          }
        )
        cateClassActive.value = res.data[0].id
        resolve('success')
      })
      
    })

  
}

// 查询酒店餐类数据
const getCateType = () => {
  return new Promise((resolve, reject)=>{

    const params = {}
    localHotelApi.findCateType(params).then(res => {
      foodTypeList.value = res.data
      // isCheckAllCl.value = true
      // defaultParams.value.cateTypeIds = res.data.map(item => item.id)
      resolve('success')
    })

  })
}

// 查询酒店行政区数据
const getRegin = () => {
  return new Promise((resolve, reject) => {
    const params = {}
    localHotelApi.findRegionList(params).then(res => {
      res.data.forEach(item => {
        item.text = item.name
      })
      areaList.value = [...areaList.value, {
        text: '行政区域',
        children: [{ id: 999, text: '全青岛' }, ...res.data],
      }]
      resolve('success')

      // localHotelApi.getBusinessCircle(params).then(circle => {
      //   circle.data.forEach(item => {
      //     item.text = item.circleName
      //   })
      //   areaList.value = [...areaList.value, {
      //     text: '热门商圈',
      //     children: circle.data,
      //   }]

      // })

    })
  })
}

// 查询商圈数据
const getBusinessCircle = () => {
  return new Promise((resolve, reject)=>{
    const params = {}
    
  })
}

// 查询城市数据
const getCityList = () => {
  const params = {}
  localHotelApi.findCityList(params).then(res => {

  })
}

// 查询列表
const getList = () => {
    Promise.all([getRegin()]).then(res => {
      // 拿到两个参数后再进行查询操作
      loadHotelList()
	})
}



onMounted(() => {
  getList()
  dataList()

});
</script>



<script lang="ts">
export default {
  name: 'hotelList'
}
</script>

<style lang='less' scoped>
@import url(./common.less);
</style>