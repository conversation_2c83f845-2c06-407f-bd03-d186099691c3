import { download, get, post, filepost, originalGet } from '../request'

export const waterworkSupplierApi = {
    list: (params: any): Promise<void> => {
        return get('waterworks/api/tsupplierInfo/page', params)
    },
    listAll: (params: any): Promise<void> => {
        return get('waterworks/api/tsupplierInfo/list', params)
    },
    add: (params: any): Promise<void> => {
        return post('waterworks/api/tsupplierInfo/save', params)
    },
    update: (params: object): Promise<void> => {
        return post(`waterworks/api/tsupplierInfo/update`, params);
    },
    export: (params: any): Promise<void> => {
        return download('waterworks/api/tsupplierInfo/export', params)
    },
    delete: (ids: string): Promise<void> => {
        return post(`waterworks/api/tsupplierInfo/delete/${ids}`);
    },
}