<script setup lang="ts">
import dayjs from 'dayjs';
import { useServiceProviderDetailStore } from '../store';
import { MerchantContract } from '@haierbusiness-front/common-libs';
import { Modal } from 'ant-design-vue';

const store = useServiceProviderDetailStore();
const columns = [
  {
    title: '合同流水号',
    dataIndex: 'contractNo',
    key: 'contractNo',
  },
  {
    title: '合同编号',
    dataIndex: 'contractCode',
    key: 'contractCode',
  },
  {
    title: '合同有效期',
    dataIndex: 'contractDate',
    key: 'contractDate',
  },
  {
    title: '状态',
    dataIndex: 'state',
    key: 'state',
  },
  {
    title: '签订日期',
    dataIndex: 'signDate',
    key: 'signDate',
  },
  {
    title: '合同附件',
    dataIndex: 'contractUrl',
    key: 'contractUrl',
  },
  {
    title: '操作',
    dataIndex: 'options',
    key: 'options',
  },
];
const handleEdit = (record: MerchantContract) => {
  const recordKeys = Object.keys(record);
  for (const recordKey of recordKeys) {
    store.contractDetail[recordKey] = record[recordKey]
  }
  store.contractDetail.contractDate = [dayjs(record.contractStart), dayjs(record.contractEnd)];
  store.contractDetail.signDate = dayjs(record.signDate);
  store.contractDetail.state = !!record.state;
  store.editContractModalOpen = true;
}
const handleDelete = (record: MerchantContract) => {
  Modal.confirm({
    title: '是否确定删除?',
    async onOk() {
      await store.deleteContract({ id: record.id })
      store.getContractList({ merchantId: store.businessId })
    },
  })
}
</script>

<template>
  <a-table :dataSource="store.contractList" :columns="columns" bordered :pagination="false">
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'contractDate'">
        <span>{{ record.contractStart }}</span>
        <span>-</span>
        <span>{{ record.contractEnd }}</span>
      </template>
      <template v-if="column.key === 'contractUrl'">
        <a v-if="!!record.contractUrl" :href="record.contractUrl" target="_blank">合同附件</a>
      </template>
      <template v-if="column.key === 'state'">
        {{ ['有效', '失效'][record.state] }}
      </template>
      <template v-if="column.key === 'options'">
        <a-space>
          <a @click="handleEdit(record)">编辑</a>
          <a @click="handleDelete(record)">删除</a>
        </a-space>
      </template>
    </template>
  </a-table>
</template>
