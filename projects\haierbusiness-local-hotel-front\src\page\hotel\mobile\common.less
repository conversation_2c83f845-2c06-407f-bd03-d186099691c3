.flex {
  display: flex;
}
.flex-column {
  flex-direction: column;
}
.align-items-center {
  align-items: center;
}
.justify-content-center {
  justify-content: center;
}
.justify-content-between {
  justify-content: 
  space-between;
}
.flex-center {
  display: flex;
    justify-content: center;
    align-items: center;
}
.width100 {
  width: 100%;
}
.flex-warp {
  flex-wrap: wrap;
}
.mr-5 {
  margin-right: 5px !important;
}
.mt-5 {
  margin-top: 5px !important;
}
.mt-8 {
  margin-top: 8px !important;
}
.mb-8 {
  margin-bottom: 8px !important;
}
.mt-32 {
  margin-top: 28px !important;
}
.mb-32 {
  margin-bottom: 28px !important;
}
.mt-10 {
  margin-top: 10px;
}
.mt-12 {
  margin-top: 12px;
}
.ml-5 {
  margin-left: 5px !important;
}
.mb-5 {
  margin-bottom: 5px !important;
}
.mr-10 {
  margin-right: 10px !important;
}
.mr-32 {
  margin-right: 32px !important;
}
.ml-32 {
  margin-left: 32px !important;
}
.mt-20 {
  margin-top: 20px;
}
.mb-20 {
  margin-bottom: 20px;
}
.ml-10 {
  margin-left: 10px !important;
}
.mb-10 {
  margin-bottom: 10px !important;
}
.mb-12 {
  margin-bottom: 12px !important;
}
.mb-30 {
  margin-bottom: 30px !important;
}
.strong {
  font-weight: 600;
  font-size: 14px;
}
.mr-20 {
  margin-right: 20px;
}
.font-size-14 {
  font-size: 14px;
}
.font-size-13 {
  font-size: 13px;
}
.font-size-11 {
  font-size: 11px;
}
.font-size-12 {
  font-size: 12px;
}
.font-size-vant {
  font-size: 28px;
}
.color-disabled {
  color: #9b9ea1;
}
.font-size-10{
  font-size: 10px;
}
.font-size-22{
  font-size: 22px;
}
.font-size-8{
  font-size: 8px;
}
.color-000 {
  color: #000;
}
.color-eee {
  color: #c0c0c0;
}
.weight600 {
  font-weight: 600;
}
.flex-1 {
  flex: 1;
}
.color-main {
  
  color: #0073e5;
}
.hotel-img {
  width: 160px;
    height: 200px;
    // background-color: #0073e5;
    border-radius: 6px;
  img {
    width: 100%;
    height: 100%;
    border-radius: 6px;  }
}
.book-list {
  background: #F6F7F9;
  min-height: 100vh;
  :deep(.van-cell__value) {
    text-align: left;
  }
}

.order-list {
  // margin-top: 36px;
  background-color: #f3f3f3;
}

.color-red {
  color: red;
}
.bg-eee {
  background: #F7F7F7;
}
.bg-999 {
  background-color: #e3e3e3;
}
.my-swipe {
}

.padding-16 {
  padding: 16px;
}

.detail-title-box {
  border-radius: 20px;
  background: #FFFFFF;
  position: relative;
  top: -40px;
  padding: 40px 24px 27px 24px;
}
.detail-title-time {
  height: 40px;
}
.detail-title {
  font-weight: 500;
  font-size: 40px;
  color: #262626;
  line-height: 56px;
  display: flex;
  justify-content: space-between;
  .title-more {
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
  }
}
.detail-food  {
  display: flex;
  flex-direction: row;
  font-weight: 400;
  font-size: 24px;
  color: #595959;
  height: 30px;
  display: flex;
  align-items: center;
  // line-height: 33px;
}
.food {
  display: flex;
  width: 200px;
}
.detail-area {
  display: flex;
  width: 502px;
  height: 35px;
  overflow-x: scroll;
  overflow-y: hidden;

  /* 隐藏滚动条 */
  &::-webkit-scrollbar {
    display: none;
    /* 隐藏滚动条 */
  }

  /* 兼容其他浏览器 */
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}
.detail-star {
  display: flex;
  align-items: flex-end;
  margin-left: 12px;
  padding-bottom: 10px;
  
  img {
    width: 40px;
    height: 32px;
  }
  div {
    background: rgba(153, 176, 197, 0.2);
    margin-left: 5px;
    font-weight: 600;
    font-size: 16px;
    color: #FF9B4C;
    line-height: 22px;
  }
  .star-level-5 {
    background: rgba(255, 155, 76, 0.2);
    color: #FF9B4C;
  }
}
.detail-money {
  font-weight: 600;
  font-size: 12px;
  color: #c2c2c2;
  line-height: 18px;
}
.detail-airportDistance {
  font-weight: 400;
  font-size: 10px;
  color: #BFBFBF;
  line-height: 20px;
}

.my-swipe .van-swipe-item {
 
  color: #fff;
  font-size: 20px;
  line-height: 150px;
  text-align: center;
  background-color: #39a9ed;
}
.detail-content {
  padding: 10px;
}
.main {
  position: relative;
}
.main-title {
  font-size: 12px;
}
.row-scollx{
  overflow-x: auto;
}
.hotel-item-title {
  font-weight: 500;
  font-size: 32px;
  color: #262626;
  font-style: normal;
  line-height: 48px;
}
.hotel-item-foods {
  color: #8C8C8C;
  font-size: 10px;

}
.item-btns {
  display: flex;
  justify-content: flex-end;
}
.hotel-item-consumptionPer {
  font-weight: 600;
  font-size: 10px;
  color: #c2c2c2;
  text-align: right;
}

.hotel-item-address {
  margin-top: 12px;
  color: #595959;
  font-size: 24px;
  height: 33px;
  width: 486px;
}

.hotel-item-juli {
  margin-top: 12px;
}

.scroll-hidden{
  height:190px;
  overflow:hidden;
  .scroll-body{
      overflow-y: hidden;
      overflow-x: auto;
      &::-webkit-scrollbar {
          display: none;
      }
      padding-bottom: 20px;
      .scroll-secbody{
          white-space: nowrap;
          display: flex;   
          .every_content{
              flex-shrink: 0;
              height:190px;
              width: 284px;
              margin-right: 20px;
              text-align: center;
          }
          .img-title {
            position: absolute;
            bottom: 8px;
            left: 0;
            background: rgba(0,0,0,0.3);
            height: 36px;
            font-weight: 400;
            font-size: 24px;
            color: #FFFFFF;
            line-height: 33px;
            width: 100%;
          }
      }
  }
}

  .navigation-box {
    position: absolute;
    top: 0;
    height: 100%;
    right: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80px;
    img {
      width: 20px;
      height: 20px;
    }
    span {
     color: #595959;
     font-size: 12px;
    }
  }
.bottom-order-btn {
  background: #fff;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  .btn {
    width: 348px;
    height: 88px;
    background: #FFFFFF;
    box-shadow: 0px -2px 0px 0px rgba(0,0,0,0.02);
    border-radius: 16px;
    border: 1px solid #2681FF;
    font-weight: 400;
    font-size: 32px;
    color: #2681FF;
    line-height: 48px;
  }
  .left {
    background-color: #ff6410;
    border-radius: 20px 0 0 20px !important;
  }
  .right {
    border-radius:0 20px  20px 0 !important;
  }
}

.title {
  height: 40px;
  padding-left: 20px;
  padding-bottom: 20px;
  .shu {
    display: inline-block;
    height: 20px;
    width: 4px;
    background-color: #0052d9;
    margin-right: 10px;
    border-radius: 8px;
  }
  .text {
    font-weight: 500;
    font-size: 30px;
    color: #262626;
    line-height: 42px;
    // font-size: 14px;
  }
}
.money {
  font-weight: 400;
  font-size: 20px;
  color: #FF5339;
  line-height: 28px;
}

.pl-20 {
  padding-left: 20px;
}
.pr-20 {
  padding-right: 20px;
}

.bottom-btn-box {

  display: flex;
  align-items: center;
  background-color: #fff;
  padding-bottom:  36px;
  :deep(.van-dropdown-menu__bar) {
    box-shadow: none
  }
  
  :deep(.van-dropdown-menu__title) {
    font-size: 24px;
    color: #868686;

  }
}
.font-size-20 {
  font-size: 20px;
}
.cell-item {
  :deep(.van-cell__value) {
    width: 25%;
    flex: none;
  }
}
.order-detail {
  min-height: 100vh;
  padding-top: 14px;
  background-color: #f3f3f3;
}
.search-btn {
  padding: 0 15px;
  background: linear-gradient( 180deg, #4EB7FF 0%, #2681FF 100%);
  border-radius: 32px;

  font-weight: 400;
  color: #FFFFFF;
  text-align: left;
  font-style: normal;

}

:root:root {
  --van-search-content-background: rgba(242,243,245,0);
  --van-dropdown-menu-content-max-height: 100%;
}

.search-icon-color {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 34px;
  font-size: 36px;
}

.top-search {
  .search-icon-color {
    color: #2681FF;
    width: 48px;
    height: 48px;
  }
  :deep(.van-search__content) {
    background-color: rgba(242,243,245,0);
    border: 1px solid #2681FF;
  }
  :deep(.van-field__right-icon) {
    padding: 0;
  }
}

.my-dropdown {
  height: 88px;
  :deep(.van-dropdown-menu__title) {
    // height: 88px;
    // background: #F6F7F9;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    padding: 2px 10px;

  }
  :deep(.van-dropdown-menu__bar) {
    height: 88px;
    background: #F6F7F9;
  }
  :deep(.van-dropdown-menu__title:after) {
    opacity: 0;
  }
  :deep(.van-ellipsis) {
    display: flex;
    align-items: center;
  }
 
  .sanjiao {
    transform-origin: center;
    transform: rotate(90deg);
    width: 18px;
    height: 13px;
  }
  .daosanjiao {
    transform-origin: center;
    transform: rotate(-90deg);
    width: 18px;
    height: 13px;
    margin-left: -24px;
  }

  // .default-down-icon {
  //   display: flex;
  //   justify-content: center;
  //   align-items: center;
  //   margin-left: 17px;
  //   width: 18px;
  //   height: 13px;
  // }

  .default-down-icon {
    background: url('@/assets/image/restaurant/dropdown_default.png') no-repeat;
    background-size: 100% 100%;
    width: 22px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :deep(.van-ellipsis) {
    display: flex;
    align-items: center;
  }
  :deep(.van-dropdown-menu__title--active) {
    border: 1px solid #2681FF;
    color: #2681FF;
    background: rgba(38,129,255,0.08);
  }
  .van-dropdown-menu__title--active >.van-ellipsis >.default-down-icon {
    color: #fff;
    
    background: url('@/assets/image/restaurant/dropdown_active.png') no-repeat ;
    background-size: 100% 100%;
  }
}
:deep(.van-dropdown-item__content) {
  max-height: 100%;
}
.hidden-3 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;

}

.detail-text-color {
  color: rgba(140, 140, 140, 1);
}

.btn-background {
  background: #2681FF !important;
  box-shadow: 0px -2px 0px 0px rgba(0,0,0,0.02) !important;
  border-radius: 16px !important;
  color: #FFFFFF !important;
}
:deep(.active-dropdown-item) {
  border: 0.013333rem solid #2681FF !important;
  color: #2681FF !important;
  background: rgba(38, 129, 255, 0.08)!important;
  .default-down-icon {
    background: url('@/assets/image/restaurant/dropdown_default2.png') no-repeat;
    background-size: 100% 100%;
  }
}
:deep(.active-dropdown-list-item) { 
  color: #2681FF !important;

}

.search-img {
  width: 750px;
  height: 390px;
  background-image: url('@/assets/image/hotel/list-top-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 40px 24px 24px 24px;

  .search-box {
    width: 702px;
    height: 326px;
    background: linear-gradient( 180deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.98) 100%);
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.08);
    border-radius: 16px;
    // border: 2px solid;
    border-image: linear-gradient(141deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 2 2;
    backdrop-filter: blur(4px);
    padding: 32px 32px 20px 32px;
    
    .my-search {
      font-size: 32px !important;
      height: 45px;
      background: rgba(0, 0, 0, 0) !important;
      :deep(.van-search__content) {
        display: flex;
        align-items: center;
        width: 100%;
        padding-left: 0 !important;
        background: rgba(0, 0, 0, 0) !important;
        font-size: 32px;
        height: 45px;
      }
      :deep(.van-search__content) {
        padding-left: 0 !important;
        background: rgba(0, 0, 0, 0) !important;
        font-size: 32px !important;
        height: 45px;
      }
      :deep(.van-cell) {
        font-size: 32px !important;
        height: 45px;
      }
    }
    .search-btn-box {
      .search-box-btn {
        width: 638px;
        height: 76px;
        background: linear-gradient( 180deg, #4EB7FF 0%, #2681FF 100%);
        box-shadow: 0px 1 4px 0px rgba(0,0,0,0.08);
        border-radius: 38px;
        font-weight: 500;
        font-size: 32px;
        color: #FFFFFF;
        line-height: 45px;
        text-shadow: 0px 2px 8px rgba(0,0,0,0.08);
        text-align: left;
        font-style: normal;
      }
    }
  }
}

.search-time-start {
  align-items: baseline;
}

.search-time-text {
  font-weight: 400;
  font-size: 28px;
  color: rgba(0,0,0,0.85);
  span {
    font-size: 40px;
    color: rgba(0,0,0,0.85);
  }
}
.search-time-text-small {
  font-size: 24px;
  color: #8C8C8C;
}

.search-time-right {
  font-weight: 400;
  font-size: 28px;
  color: #262626;
  line-height: 28px;
  text-shadow: 0px 2px 8px rgba(0,0,0,0.08);
  text-align: right;
  font-style: normal;
}

.hotel-list-box {
  
  padding: 0 24px;
  padding-top: 16px;
  .hotel-item {
    margin-bottom: 20px;
  }
}
.train-img {
  width: 22px;
  height: 22px;
  margin-right: 2px;
  margin-bottom: 3px;
}

.switch-box {
  position: absolute;
  right: 0px;
  top: 4px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 28px;
    height: 28px;
  }
}

.blue-text {
  // 
  color: #2681FF;
  // line-height: 30px;
}
.font-size-22 {
  font-size: 22px;
}

.room-list {
  .room-item {
    height: 164px;
    border-radius: 8px;
    background: #F6F7F9;
    padding: 20px;
    justify-content: space-between;
    // align-items: center;
    border: 1px solid rgba(0, 0, 0, 0);
    &.active {
      border-radius: 16px;
      border: 1px solid #93C0FF;
      background: #F3F8FF;
    }
    .item-check {
      display: flex;
      align-items: center;
    }
    .item-label {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex: 1;
      .item-label-title {
        font-weight: 500;
        color: rgba(38, 38, 38, 1);
        font-size: 28px;
        height: 25px;
        align-items: center;
        line-height: 40px;
      }
      .item-lable-more {
        line-height: 33px;
        font-weight: 400;
        font-size: 24px;
        color: #8C8C8C;
        height: 24px;
        align-items: center;
      }
      .shu {
        border-color: #D9D9D9;
      }
      .item-label-price {
        font-weight: 400;
        font-size: 12px;
        color: #8C8C8C;
        height: 24px;
        align-items: center;

        .shouri {
          font-size: 22px;
          color: #8C8C8C;
          line-height: 30px;
        }
      }
    }
  }
}

.time-range-tag {
  display: flex;
  justify-content: center;
  background-color: rgba(233, 242, 255, 1);
  color: rgba(38, 129, 255, 1);
  width: 80px;
  background: #E9F2FF;
  border-radius: 15px;
  font-weight: 400;
  font-size: 20px;
  color: #2681FF;
  line-height: 28px;
}

.room-add-btn {
  width: 100%;  
  background-color: #fff; 
  border: none;
  font-weight: 500;
  font-size: 28px;
  color: #2681FF;
  line-height: 40px;
  
}

.man-user{
  padding:88px 24px 20px;
  height: 100vh; 
  background-color:#F6F7F9; 
}

.user-item {
  background-color: #fff;
  justify-content: space-between;
  margin-top: 20px;
  padding: 28px 32px;
  align-items: center;
  .user-item-left {
    display: flex;
    flex: 1;
    .user-item-title {
      font-weight: 500;
      font-size: 30px;
      color: #262626;
      line-height: 42px;
      display: flex;
      align-items: center;
      .user-item-name {
        min-width: 100px;
      }
      .uset-item-phone {
        margin-top: 8px;
        font-weight: 400;
        font-size: 28px;
        color: #262626;
        line-height: 40px;
      }

      .user-item-sex {
        width: 32px;
        height: 32px;
        &.icon-man {
          background: url('@/assets/image/hotel/boy.png') no-repeat;
          background-size: cover;
        }
        &.icon-woman {
          background: url('@/assets/image/hotel/girl.png') no-repeat;
          background-size: cover;

        }
      }

    }
    .user-item-remark {
      font-weight: 400;
      font-size: 24px;
      color: #8C8C8C;
      line-height: 33px;
      margin-top: 12px;
    }
  }

  .user-item-right {
    .user-item-edit {
      font-weight: 400;
      font-size: 24px;
      color: #2681FF;
      line-height: 36px;
      .user-item-edit-icon {
        width: 28px;
        height: 28px;
        background: url('@/assets/image/hotel/edit.png') no-repeat;
        background-size: cover;
      }
    }
    .user-item-delete {
      font-weight: 400;
      font-size: 24px;
      color: #FF4D4F;
      line-height: 36px;
      .user-item-delete-icon {
        width: 28px;
        height: 28px;
        background: url('@/assets/image/hotel/delete.png') no-repeat;
        background-size: cover;
      }
    }
  }
}

.tag {
  height: 35px;
  padding: 0px 8px;
  border-radius: 4px;
  border: 1px solid #B4D4FF;
  font-weight: 400;
  font-size: 20px;
  color: #2681FF;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-detail {
  // display: inline;
  white-space: nowrap;
}

.per {
  width: 178px;
  height: 72px;
  background: rgba(38,129,255,0.1);
  border-radius: 36px;
  font-weight: 400;
  font-size: 28px;
  color: #2681FF;
  line-height: 42px;
  border: 0;
}
.com {
  width: 178px;
  height: 72px;
  background: #2681FF;
  border-radius: 36px;
  font-weight: 400;
  font-size: 28px;
  color: #FFFFFF;
  line-height: 42px;
  border: 0;
}

.container{
  background: linear-gradient( 180deg, #FFFFFF 0%, #F6F7F9 100%);
  border-radius: 20px;
  padding: 36px 24px;
}

.room-list{
  .room-item {
    height: 164px;
    background: #F6F7F9;
    border-radius: 16px;
    padding: 20px;
    justify-content: space-between;
  }

  .item-price {
    color: rgba(255, 83, 57, 1);
    font-size: 20px;
    >span {
      font-size: 40px;
    }
  }
}

.detail-huanjing-box, .detail-desc-box, .detail-more-box {
  background: #FFFFFF;
  border-radius: 20px;
  padding: 32px 24px;
}

.detail-title-s {
  font-weight: 600;
  font-size: 36px;
  color: #262626;
  line-height: 50px;
  display: flex;
  justify-content: space-between;

  .title-more {
    font-size: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
  }
}

.item-label-book {
  width: 100%;
  height: 200px;
  background: linear-gradient( 179deg, #EEF5FF 0%, #FFFFFF 100%);
  border-radius: 16px;
  border: 2px solid #FFFFFF;
  padding: 28px 32px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  .delete-text {
    height: 160px;
    position: absolute;
    right: 2px;
    width: 30px;
    font-size: 22px;
    color: #f4baba;
  }
  .item-label-title {
    font-weight: 500;
    font-size: 28px;
    color: #262626;
    line-height: 40px;
    display: flex;
    align-items: baseline;

    .blue-text {
      color: #2681FF;
      font-size: 28px;
    }
  }
  .item-lable-more {
    font-weight: 400;
    font-size: 24px;
    color: #595959;
    line-height: 33px;
  }

  .shu {
    border-color: #D9D9D9;
  }

  .item-price {
    color: rgba(255, 83, 57, 1);
    font-size: 20px;
    >span {
      font-size: 36px;
    }
  }

  .shouri {
    font-weight: 400;
    font-size: 22px;
    color: #8C8C8C;
    line-height: 30px;
  }

  .item-label-price {
    .blue-text {
      font-weight: 400;
      font-size: 22px;
      color: #2681FF;
      line-height: 30px;
      margin-left: 16px;
    }

    .item-room-num {
      display: flex;
      justify-content: space-between;
      width: 190px;
      align-items: center;
      background: #F6F7F9;
      border-radius: 26px;
      .room-num-add {
        width: 40px;
        height: 40px;
        background: url('@/assets/image/hotel/room_add.png') ;
        background-size:cover ;
      }
      .room-num-sub {
        width: 40px;
        height: 40px;
        background: url('@/assets/image/hotel/room_sub.png');
        background-size:cover ;
        &.active {
          background-image: url('@/assets/image/hotel/room_sub_active.png');
        }
      }
    }
  }
}

.mt-20 {
  margin-top: 20px;
}

.font-size-28 {
  font-size: 28px;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-40{
  margin-bottom: 40px;
}

.ml-16 {
  margin-left: 16px;
}

.fs-24{
  font-size: 24px;
}

.fs-40 {
  font-size: 40px;
}

.fs-30 {
  font-size: 30px;
}

.mb-30 {
  margin-bottom: 30px;
}

.order-con {
  padding: 20px 32px;
  min-height: 100px;
  display: flex;
  width: 100%;
  flex-direction: row;
  justify-content: space-between;
}

.order {
  width: 327px;
  height: 88px;
  background: #2681FF;
  border-radius: 12px;
  font-weight: 400;
  font-size: 32px;
  color: #FFFFFF;
  line-height: 48px;
}

.jine {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.bottom-box {
  min-height: 205px !important
}

.tabbar {
  height: 112px;
  width: 100%;
}

.book-icon {
  width: 40px;
  height: 40px;
}