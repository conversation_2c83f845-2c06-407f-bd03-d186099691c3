// 酒店星级

type keys =
  | 'TWO'
  | 'THREE'
  | 'TWO_THREE'
  | 'FOUR'
  | 'TWO_FOUR'
  | 'THREE_FOUR'
  | 'TWO_THREE_FOUR'
  | 'FIVE'
  | 'TWO_FIVE'
  | 'THREE_FIVE'
  | 'TWO_THREE_FIVE'
  | 'FOUR_FIVE'
  | 'TWO_FOUR_FIVE'
  | 'THREE_FOUR_FIVE'
  | 'TWO_THREE_FOUR_FIVE';

export const hotelLevelAllConstant = {
  // 无星级-1
  // 一星级-2
  // 二星级-4
  // 三星级-8
  // 四星级-16
  // 五星级-32
  TWO: { code: 7, desc: '2钻/星及以下' },
  THREE: { code: 8, desc: '3钻/星' },

  TWO_THREE: { code: 15, desc: '3钻/星及以下' },
  FOUR: { code: 16, desc: '4钻/星' },

  TWO_FOUR: { code: 23, desc: '2钻/星及以下或4钻/星' },
  THREE_FOUR: { code: 24, desc: '3钻/星或4钻/星' },

  TWO_THREE_FOUR: { code: 31, desc: '4钻/星及以下' },
  FIVE: { code: 32, desc: '5钻/星' },
  TWO_FIVE: { code: 39, desc: '2钻/星及以下或5钻/星' },

  THREE_FIVE: { code: 40, desc: '3钻/星或5钻/星' },
  TWO_THREE_FIVE: { code: 47, desc: '3钻/星及以下或5钻/星' },
  FOUR_FIVE: { code: 48, desc: '4钻/星或5钻/星' },

  TWO_FOUR_FIVE: { code: 55, desc: '2钻/星及以下或4钻/星或5钻/星' },
  THREE_FOUR_FIVE: { code: 56, desc: '3钻/星及以上' },

  TWO_THREE_FOUR_FIVE: { code: 63, desc: '全部' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in hotelLevelAllConstant) {
      const item = hotelLevelAllConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(hotelLevelAllConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return hotelLevelAllConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
