<template>
  <div>

    <van-form @submit="onSubmit" @failed="onFailed">
      <div class="reservation-content">
        <!-- 基本信息 -->

        <div class="title flex justify-content-between align-items-center ">
          <div class="flex align-items-center justify-content-center">
            <span class="shu"></span>
            <div class="text">基本信息</div>
          </div>
        </div>

        <van-cell-group inset class="mb-20">
          <van-field autocomplete="off" readonly required input-align="right" error-message-align="right" label="订房类型">
            <template #input>
              {{ paymentType == 'public' ? '因公' : '因私' }}
            </template>
          </van-field>

          <van-field autocomplete="off" readonly required input-align="right" error-message-align="right" label="支付方式">
            <template #input>
              {{ payTypeList.filter((item) => item.value == form.budget.paymentType)[0].label }}
            </template>
          </van-field>

          <van-field autocomplete="off" required readonly input-align="right" error-message-align="right"
            v-model.trim="form.owner.name" label="经办人" />

          <van-field autocomplete="off" input-align="right" error-message-align="right" v-model.trim="form.owner.mobile"
            :rules="[{ required: true, message: '请输入手机号' }, { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式错误！' }]"
            required type="tel" label="手机号" placeholder="请填写手机号" />

          <van-field autocomplete="off" input-align="right" error-message-align="right" v-model.trim="form.owner.email"
            label="邮箱" placeholder="请填写邮箱" />

          <van-field autocomplete="off" input-align="right" error-message-align="right" v-model.trim="form.owner.phone"
            type="tel" label="备用联系电话" placeholder="请填写备用联系电话" />


        </van-cell-group>

        <van-cell-group inset class="mb-40">
          <user-select-m palceholder="请选择业务申请人" label="业务申请人" :value="form.applicant.name"
            @chose="chosedPerson"></user-select-m>

          <van-field autocomplete="off" required readonly input-align="right" error-message-align="right" label="所属部门"
            v-model="form.applicant.departmentName" placeholder="请填写所属部门" />

          <van-field autocomplete="off" input-align="right" error-message-align="right"
            v-model.trim="form.applicant.mobile"
            :rules="[{ required: true, message: '请输入手机号' }, { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式错误！' }]"
            required type="tel" label="手机号" placeholder="请填写手机号" />

          <van-field autocomplete="off" input-align="right" error-message-align="right"
            v-model.trim="form.applicant.email" label="邮箱" placeholder="请填写邮箱" />

          <van-field autocomplete="off" input-align="right" error-message-align="right"
            v-model.trim="form.applicant.phone" type="tel" label="联系电话" placeholder="请填写联系电话" />

          <!-- <van-field autocomplete="off" v-if="paymentType == 'public'" required
            :rules="paymentType == 'public' ? [{ required: true, message: '请填写申请事由' }] : []"
            v-model.trim="form.applyCause" label-align="top" label="申请事由" placeholder="请填写申请事由" /> -->
        </van-cell-group>

        <!-- 酒店预订信息 -->
        <div class="title flex justify-content-between align-items-center mb-20">
          <div class="flex align-items-center justify-content-center">
            <span class="shu"></span>
            <div class="text">酒店预订信息</div>
          </div>
        </div>

        <van-cell-group inset class="mb-40">
          <van-field autocomplete="off" readonly input-align="right" error-message-align="right" label="酒店名称">
            <template #input>
              <div>{{ detail?.fullname }}</div>
            </template>
          </van-field>

          <van-field autocomplete="off" readonly input-align="right" error-message-align="right" label="入住日期">
            <template #input>
              <div>{{ checkIn }}</div>
            </template>
          </van-field>

          <van-field autocomplete="off" readonly input-align="right" error-message-align="right" label="离店日期">
            <template #input>
              <div>{{ checkOut }}</div>
            </template>
          </van-field>

        </van-cell-group>


        <!-- 房间信息 -->
        <div class="title flex justify-content-between align-items-center mb-20">
          <div class="flex align-items-center justify-content-center">
            <span class="shu"></span>
            <div class="text">房间信息</div>
          </div>
        </div>

        <van-cell-group inset style="background-color: #f8f8f8;" class="mb-40">
          <van-swipe-cell v-for="(room, index) in roomList" :key="room.id">
            <div class="item-label-book mb-10" v-if="room.select">
              <div class="item-label-title flex">
                <span class="mr-5 blue-text">{{ room.name }}</span>
                <span class="mr-5">{{ LocalHotelRoomTypeEnum[room.type] }}</span>
                <span>[{{ LocalHotelBedTypeEnum[room.bedType] }} {{ room.bedName }}]</span>
              </div>
              <div class="item-lable-more flex">
                <span>{{ LocalHotelBreakfastTypeEnum[room.breakfastType] }} (¥{{ room.breakfastPrice / 100 }})</span>
                <van-divider vertical class="shu" />
                <span>{{ room.hasInternet ? '有宽带' : '无宽带' }}</span>
              </div>
              <div class="item-label-price flex justify-content-between">
                <div class="flex align-items-center">
                  <span class="item-price mr-5">¥<span>{{ room.firstDayPrice / 100 }}</span></span>
                  <span class="mr-5 shouri">首日房价</span>
                  <span class="blue-text" @click.stop="showRoomDayPrices(index)">查看每日房价 ></span>
                </div>
                <div class="item-room-num flex">
                  <span class="room-num-sub mr-5" :class="room.num != 1 ? 'active' : ''"
                    @click.stop="room.num != 1 ? room.num-- : ''"></span>

                  <span class="mr-5">{{ room.num }}间</span>
                  <span class="room-num-add " @click.stop="room.num++"></span>

                </div>
              </div>
              <div class="delete-text">左滑删除</div>
            </div>

            <template #right>
              <van-button style="width: 100%;height: 100%; border-radius: 0 10px 10px 0;" @click="deleteRoom(index)"
                v-if="room.select" square text="删除" type="danger" class="delete-button" />
            </template>
          </van-swipe-cell>

          <van-empty description="暂无数据" v-if="roomList.filter(item => item.select).length == 0" />


          <van-button @click="showRoomSelectPop" icon="add" class="mb-5 room-add-btn" square>
            添加房型
          </van-button>

        </van-cell-group>


        <!-- 人员信息 因私隐藏 -->
        <div class="title flex justify-content-between align-items-center mb-20">
          <div class="flex align-items-center justify-content-center">
            <span class="shu"></span>
            <div class="text">人员信息</div>
          </div>
        </div>

        <!-- 表单 -->
        <van-cell-group inset class="mb-20">
          <!-- <van-field v-if="paymentType == 'public'" autocomplete="off" v-model="form.treatInfo.guestCompany" required
            input-align="right" error-message-align="right" label="来宾单位" placeholder="请输入来宾单位"
            :rules="[{ required: true, message: '请输入来宾单位' }]" />

          <van-field v-if="paymentType == 'public'" autocomplete="off" v-model="form.treatInfo.guestNames" required
            input-align="right" error-message-align="right" label="来宾姓名" placeholder="请输入来宾姓名"
            :rules="[{ required: true, message: '请输入来宾姓名' }]" /> -->

          <van-field autocomplete="off" label-width="120px" v-model.number="form.treatInfo.guestCount"
            input-align="right" error-message-align="right" type="digit" label="入住人数" placeholder="请添加入住人" readonly
            is-link :rules="[{ required: true, message: '请添加入住人' }]" required @click="showUserSelect = true" />

          <van-field autocomplete="off" v-model="form.keepTime" @click="opewSjPicker" required input-align="right"
            error-message-align="right" label="期望保留时间" readonly is-link placeholder="请选择期望保留时间"
            :rules="[{ required: true, message: '请选择期望保留时间' }]">
            <template #input>
              <div v-if="form.keepTime">{{ `${checkIn} ${form.keepTime}` }}</div>
              <div v-else style="color:#c8c9cc">请选择期望保留时间 </div>
            </template>
          </van-field>

        </van-cell-group>

        <van-cell-group inset class="mb-40">
          <van-field autocomplete="off" v-model="form.treatInfo.signerName" required input-align="right"
            error-message-align="right" :label="paymentType == 'public' ? '签单人' : '联系人'"
            :placeholder="paymentType == 'public' ? '请输入签单人' : '请输入联系人'"
            :rules="[{ required: true, message: paymentType == 'public' ? '请输入签单人' : '请输入联系人' }]" />

          <van-field autocomplete="off" v-model="form.treatInfo.signerMobile" required type="tel" input-align="right"
            :rules="[{ required: true, message: '请输入手机号' }, { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式错误！' }]"
            error-message-align="right" label="手机号" placeholder="请输入手机号" />


          <van-field autocomplete="off" show-word-limit :maxlength="300" v-model="form.remark" 
            label-align="top" label="备注" placeholder="请输入备注" />
        </van-cell-group>

        <!-- 服务预约 -->
        <div class="title flex justify-content-between align-items-center mb-20">
          <div class="flex align-items-center justify-content-center">
            <span class="shu"></span>
            <div class="text">服务预约</div>
          </div>
        </div>
        <van-cell-group class="mb-20" inset>
          <van-field autocomplete="off" required redaonly is-link input-align="right" error-message-align="right"
            label="预留车位" placeholder="请选择是否需要预留" @click="showCwPicker = true">
            <template #input>
              {{ columnsCw.filter((item) => item.value == form.treatInfo.needParking)[0]?.text }}
            </template>
          </van-field>

          <van-field autocomplete="off" :readonly="!form.treatInfo.needParking" :required="form.treatInfo.needParking"
            :rules="[{ required: form.treatInfo.needParking, message: '请输入车牌号（多个车牌号“/”隔开）' }]"
            v-model="form.treatInfo.vehicleNo" label-align="top" label="车牌号" placeholder="请输入车牌号（多个车牌号“/”隔开）" />


        </van-cell-group>


      </div>

      <van-sticky :offset-bottom="0" position="bottom" class="bottom-box">
        <van-row class="bottom-btn-box " justify="space-between">
          <van-col :span="24">
            <van-field autocomplete="off" readonly :rules="[{ required: true, message: '请阅读完成《订房须知》后再勾选' }]" required
              input-align="left" error-message-align="left">
              <template #input>
                <van-checkbox v-model="form.checkedInfo">
                  <div class="fs-24">
                    我已经阅读并同意 <span style="color: #1482ff;" @click.stop="dcxzDialog = true">《订房须知》</span>和<span style="color: #1482ff;" @click.stop="LoadYszcPdf">《隐私政策》</span>
                  </div>
                </van-checkbox>
              </template>
            </van-field>
          </van-col>
          <van-col :span="24" class="order-con">
            <div style="flex-direction: row;" class="jine flex  align-items-center justify-content-between">
              <!-- <div class="fs-24" style="color: #8C8C8C;">订单预算金额(超标请现付)</div> -->
              <div class="money">
                ¥ <span class="fs-40">{{ (budgetAmount() / 100).toFixed(2) }}</span>
              </div>

              <van-dropdown-menu  ref="menuRef" direction="up">
                <van-dropdown-item title="查看明细" ref="itemRef">
                  <van-cell-group  round >
                    <van-cell>
                      <template #title>
                          <span class="weight600  mr-5">房费明细</span>
                      </template>
                      <template #value>
                        <div>
                          <span class="mr-10" style="color:#000">{{ countRooms }}间{{ countNights }}晚</span>
                          <span class="mr-10" style="color:#000">共 <span style="color: #FF5339;">¥{{ (budgetAmount() / 100).toFixed(2) }}</span></span>
                        </div>
                      </template>
                    </van-cell>



                    <van-collapse style="height: 300px;" v-model="activeName">
                      <van-collapse-item v-for="item,index in timeList" :key="index" :title="item" :name="index">
                        <template #value>
                          <div style="color: #FF5339"> ¥{{ (countPrice(index) / 100).toFixed(2) }}</div>
                        </template>
                        
                        <div v-for="room,roomI in roomList" :key="roomI">
                            <div v-if="room.select" class="flex" style="justify-content: flex-end;"> 
                              <div class="mr-10" style="color: #000;">{{ room.name }}</div>
                              <div class="mr-10">
                                (
                                  {{ LocalHotelBreakfastTypeEnum[room.breakfastType] }}

                                  <template v-if="room.breakfastType > 0">
                                    {{ room.breakfastType }}
                                    *
                                    {{  (room.breakfastPrice / 100).toFixed(2) }}
                                  </template>

                                元)
                              </div>
                              <div style="color: #FF5339">¥{{ (room.dayPrices[index].individualPrice / 100).toFixed(2) }} * {{ room.num  }}</div>
                            </div>
                          </div>
                      </van-collapse-item>
                      
                    </van-collapse>


                  </van-cell-group>
                </van-dropdown-item>
              </van-dropdown-menu>
            </div>

            <div :span="10">
              <van-button class="order" :loading="btnLoading" type="primary" native-type="submit">立即预定</van-button>
            </div>
          </van-col>

        </van-row>
      </van-sticky>
    </van-form>

    <van-calendar title="每日房价" :max-date="new Date(checkOut)" :default-date="[new Date(checkIn), new Date(checkOut)]"
      readonly :show-confirm="false" v-model:show="showCalendar" type="range" :formatter="formatter" />

    <!-- 房间信息选择 -->
    <van-popup v-model:show="roomSelectPop" position="bottom">
      <div style="padding:50px 10px 20px;height: 100vh;" class="flex flex-column">
        <van-nav-bar :fixed="true" title="添加房型" left-arrow>
          <template #left>
            <van-icon name="arrow-left" color="#000" size="24" @click="roomSelectPop = false" />
          </template>
        </van-nav-bar>
        <div style="flex:1; overflow: scroll">
          <roomListCom :check-in="checkIn" :check-out="checkOut" :roomList="roomList"></roomListCom>
        </div>
        <div style="height: 60px; ">
          <van-button style="width: 100%" @click="roomSelectPop = false" type="primary" square>
            确定
          </van-button>
        </div>
      </div>
    </van-popup>

    <!-- 入住人管理 -->
    <van-popup v-model:show="showUserSelect" position="bottom">
      <div class="flex flex-column man-user header">
        <van-nav-bar :fixed="true" title="入住人管理" left-arrow>
          <template #left>
            <van-icon name="arrow-left" color="#000" @click="showUserSelect = false" size="24" />
          </template>
          <template #right>
            <div @click="residentUserPop = true" class="my">我的常住人</div>
          </template>
        </van-nav-bar>
        <div class="mb-20" style="flex:1; overflow: scroll" v-if="userRoomList.length > 0">
          <div class="user-item flex mb-10" v-for="(item, index) in userRoomList" :key="index">
            <div class="user-item-left flex-column">
              <div class="user-item-title mb-5 flex">
                <!-- <span class="user-item-name mr-5">{{ item.userName }}</span> -->
                <van-text-ellipsis row="1" class="user-item-name mr-5" :content="item.userName" />

                <div class="user-item-sex" :class="item.sex == 1 ? 'icon-man' : 'icon-woman'"></div>
              </div>
              <div class="uset-item-phone mb-5">{{ item.phoneNum }}</div>
              <van-text-ellipsis class="user-item-remark" style="width: 100%;"
                :content="item.comment ? `备注:${item.comment}` : `备注: -`" />

            </div>
            <div class="user-item-right flex ">
              <div class="user-item-edit flex" @click.stop="editUser(index)">
                <div class="user-item-edit-icon mr-5"></div>
                <span>编辑</span>
              </div>
              <van-divider vertical />
              <div class="user-item-delete flex" @click.stop="deleteUser(index)">
                <div class="user-item-delete-icon mr-5"></div>
                <span>删除</span>
              </div>
            </div>
          </div>
          <van-button style="width: 100%" icon="add" class="mb-5 room-add-btn" @click="addInUserFuc" square>
            添加入住人
          </van-button>

        </div>
        <div style="flex:1" v-else>
          <van-empty description="暂无入住人" />
          <van-button style="width: 100%" icon="add" class="mb-5 room-add-btn" @click="addInUserFuc" square>
            添加入住人
          </van-button>

        </div>
        <van-sticky :offset-bottom="20" position="bottom">
          <div class="bottom-submit-btn-box ">
            <van-button style="width: 100%" type="primary" :loading="rzrLoading" @click="putCheckInUser" square>
              提交
            </van-button>
          </div>
        </van-sticky>
      </div>
    </van-popup>
    <!-- 添加入住人 -->
    <van-popup v-model:show="showAddPop" position="bottom">
      <van-form @submit="addInUser" style="padding:50px 10px 20px;height: 100vh; background-color:#F6F7F9; "
        class="flex flex-column">
        <van-nav-bar :fixed="true" :title="openType == 'add' ? '添加入住人' : '编辑入住人'" left-arrow>
          <template #left>
            <van-icon name="arrow-left" size="24" color="#000" @click="showAddPop = false" />
          </template>
        </van-nav-bar>

        <div style="flex:1; overflow: scroll">
          <van-cell-group class="mt-10">

            <van-field autocomplete="off" required :rules="[{ required: true, message: '请输入姓名' }]" input-align="right"
              error-message-align="right" v-model.trim="addForm.userName" placeholder="请输入姓名" label="姓名" />

            <van-field name="radio" input-align="right" required label="性别" error-message-align="right"
              :rules="[{ required: true, message: '请选择性别' }]">
              <template #input>
                <van-radio-group v-model="addForm.sex" direction="horizontal">
                  <van-radio :name="1">男</van-radio>
                  <van-radio :name="2">女</van-radio>
                </van-radio-group>
              </template>
            </van-field>

            <van-field autocomplete="off" input-align="right" error-message-align="right"
              v-model.trim="addForm.phoneNum"
              :rules="[{ required: true, message: '请输入手机号' }, { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式错误！' }]"
              required type="tel" label="手机号" placeholder="请填写手机号" />

            <van-field name="checkbox" input-align="right" label="设为常住人">
              <template #input>
                <van-switch :active-value="1" :inactive-value="0" v-model="addForm.frequent" />
              </template>
            </van-field>

            <van-field autocomplete="off" label-align="top" placeholder="请输入备注信息" v-model.trim="addForm.comment"
              label="备注" />


          </van-cell-group>
        </div>



        <div style="height: 60px; ">
          <van-button v-if="openType == 'add'" style="width: 100%" type="primary" native-type="submit" square>
            提交
          </van-button>

          <van-button v-else style="width: 100%" type="primary" native-type="submit" square>
            保存
          </van-button>
        </div>

      </van-form>
    </van-popup>

    <!-- 我的常住人 -->
    <van-popup v-model:show="residentUserPop" :destroy-on-close="true" position="bottom">
      <div style="padding:50px 10px 2cqmax; height: 100vh; background-color:#F6F7F9; " class="flex flex-column">
        <van-nav-bar :fixed="true" title="常住人" left-arrow>
          <template #left>
            <van-icon name="arrow-left" size="24" color="#000" @click="residentUserPop = false" />
          </template>

        </van-nav-bar>
        <van-pull-refresh style="overflow-y: scroll; flex: 1;" v-model="onRefreshResidentUserLoading"
          @refresh="onRefreshResidentUser">

          <div v-if="residentUserList.length > 0">
            <div class="user-item flex mb-10" v-for="(item, index) in residentUserList" :key="index"
              @click="chosedResidentUser(item)">
              <div class="user-item-left flex-column">
                <div class="user-item-title mb-5 flex">
                  <!-- <span class="user-item-name mr-5">{{ item.userName }}</span> -->
                  <van-text-ellipsis class="user-item-name mr-5" :content="item.userName" />

                  <div class="user-item-sex" :class="item.sex == 1 ? 'icon-man' : 'icon-woman'"></div>
                </div>
                <div class="uset-item-phone mb-5">{{ item.phoneNum }}</div>
              </div>
              <div class="user-item-right flex ">
                <div class="user-item-edit flex" @click.stop="editResidentUserFuc(index)">
                  <div class="user-item-edit-icon mr-5"></div>
                  <span>编辑</span>
                </div>
                <van-divider vertical />
                <div class="user-item-delete flex" @click.stop="deleteResidentUser(index)">
                  <div class="user-item-delete-icon mr-5"></div>
                  <span>删除</span>
                </div>
              </div>
            </div>




          </div>
          <div style="flex:1" v-else>
            <van-empty description="暂无常住人" />
          </div>
        </van-pull-refresh>

        <van-sticky :offset-bottom="20" position="bottom">
          <div class="bottom-submit-btn-box ">
            <van-button style="width: 100%" icon="add" class="mb-5 room-add-btn" @click="addResidentUserFuc" square>
              添加常住人
            </van-button>
          </div>
        </van-sticky>


      </div>
    </van-popup>

    <!-- 添加常住人 -->
    <van-popup v-model:show="showResidentPop" position="bottom">
      <van-form @submit="addResidentUser" style="padding:50px 10px 20px;height: 100vh; background-color:#F6F7F9; "
        class="flex flex-column">
        <van-nav-bar :fixed="true" :title="openTypeResident == 'add' ? '添加常住人' : '编辑常住人'" left-arrow>
          <template #left>
            <van-icon name="arrow-left" color="#000" @click="showResidentPop = false" size="24" />
          </template>
        </van-nav-bar>
        <div style="flex:1; overflow: scroll">
          <van-cell-group class="mt-10">

            <van-field autocomplete="off" required input-align="right" :rules="[{ required: true, message: '请输入姓名' }]"
              error-message-align="right" v-model.trim="addResidentForm.userName" placeholder="请输入姓名" label="姓名" />

            <van-field name="radio" input-align="right" error-message-align="right"
              :rules="[{ required: true, message: '请选择性别' }]" required label="性别">
              <template #input>
                <van-radio-group v-model="addResidentForm.sex" direction="horizontal">
                  <van-radio :name="1">男</van-radio>
                  <van-radio :name="2">女</van-radio>
                </van-radio-group>
              </template>
            </van-field>

            <van-field autocomplete="off" input-align="right" error-message-align="right"
              v-model.trim="addResidentForm.phoneNum"
              :rules="[{ required: true, message: '请输入手机号' }, { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式错误！' }]"
              required type="tel" label="手机号" placeholder="请填写手机号" />


          </van-cell-group>
        </div>

        <div style="height: 60px; ">
          <van-button v-if="openTypeResident == 'add'" style="width: 100%" type="primary" native-type="submit" square>
            提交
          </van-button>

          <van-button v-else style="width: 100%" type="primary" native-type="submit" square>
            保存
          </van-button>
        </div>
      </van-form>
    </van-popup>


    <!-- 酒店餐类选择 -->
    <van-popup v-model:show="showClPicker" round position="bottom">
      <van-picker title="餐类选择" :columns="columnsCl" :columns-field-names="{ text: 'name', value: 'id' }"
        @cancel="showClPicker = false" @confirm="onConfirmCl" />
    </van-popup>

    <!-- 就餐日期选择 -->
    <van-popup v-model:show="showRqPicker" round position="bottom">
      <van-date-picker v-model="currentRq" title="选择日期" value-format="YYYY-MM-DD" :min-date="minDate"
        :max-date="maxDate" @confirm="confirmRq" @cancel="showRqPicker = false" />
    </van-popup>

    <!-- 保留时间选择 -->
    <van-popup v-model:show="showSjPicker" round position="bottom">
      <van-time-picker v-model="currentSj" title="选择时间" @confirm="confirmSj"
        @cancel="showSjPicker = false; form.keepTime = ''" />
    </van-popup>

    <!-- 是否预留选择 -->
    <van-popup v-model:show="showCwPicker" round position="bottom">
      <van-picker title="选择预留车位" :columns="columnsCw" @confirm="onConfirmCw" @cancel="showCwPicker = false" />
    </van-popup>


    <!-- 是否需要座牌 -->
    <van-popup v-model:show="showZpPicker" round position="bottom">
      <van-picker title="是否需要座牌" :columns="columnsZp" @confirm="onConfirmZp" @cancel="showZpPicker = false" />
    </van-popup>

    <!-- 确认弹窗 -->
    <van-dialog v-model:show="showSubmitDialog" title="确定要提交订房预订单?" show-cancel-button @cancel="btnLoading = false"
      @confirm="save">
      <div class="submit-dialog-content" style="flex-direction: column;">
        <div class="mb-10">
          订单审批通过之后,请及时与海尔国旅88931999-2-1进行确认和预订!
        </div>
        <div>
          电话确认后,请及时关注预订成功短信
        </div>
      </div>

    </van-dialog>

    <!-- 隐私政策 -->
    <van-popup :lazy-render="false" v-model:show="showYszcPopup" position="bottom" :style="{ height: '100%' }">
      <van-nav-bar
        title="隐私政策"
        left-arrow
        fixed
        :z-index="1000"
      >
        <template #left>
          <van-icon name="arrow-left" color="#000" @click="showYszcPopup=false" size="20" />
        </template>
      </van-nav-bar>
      <div ref="yszcpdfRef" style="height:100%;"></div> 
    </van-popup>


    <!-- 订房须知弹窗 -->
    <van-dialog v-model:show="dcxzDialog" title="订房须知">
      <div style="padding: 20px; height: 400px; overflow-y: auto;">
        <p style="color:#444499">
          根据集团各利共体对商务接待活动目标升级及集团对餐务/会务/票务等流程规范升级要求，为保证效果，现整合集团内酒店/商务餐厅等资源，满足集团各单位商务接待活动。
          集团各单位在青岛的商务活动（会务、宴请、商务接待等）原则上首选集团内酒店资源，各单位根据市场效果和自挣自花机制选择并预定:
        </p>
        <div style="font-size: 14px; font-weight: bold;">
          <br />一、订房预订条件：
          <br />
        </div>
        <div style="margin-left: 20px;">
          1.必须有月度可用预算；
          <br />2.必须明确申请事由、业务目标、市场效果预算，确认招待必要性；
          <br />3.必须符合集团招待/差旅住宿标准，不超标，不随意提高标准，不随意增加人数，超预算部分费用自付；
          <br />4.必须系统中审批通过；
          <br />5.符合自挣自花原则；
          <br />
          <span style="color:#444499">6.首选集团内酒店资源；</span>
        </div>
        <!-- 
			<div style="font-size: 14px; font-weight: bold;"> 
				<br/>
				二、财务接口人审核项目及责任： 
				<br/>
			</div>
			<div style="margin-left: 20px;">
				1.审核是否有月度可用预算； <br/>
				2.审核是否符合自挣自花原则； <br/>
				3.审核业务是否符合集团招待/差旅住宿标准，<span style="color:#444499">原则上首选集团内酒店资源；</span> <br/>
				4.如有违规行为一经查出或经举报查实的,全部损失由责任人承担，同时参照《员工行为规范》一级违规处理； 
		</div>-->
        <div style="font-size: 14px; font-weight: bold;">
          <br />二、 直线经理、二线经理项目及责任：
          <br />
        </div>
        <div style="margin-left: 20px;">
          1.审核业务的市场效果及目标；
          <br />2.审核业务的标准是否超标；
          <br />3.审核业务的必要性、真实性、合规性；
          <br />4.
          <span style="color:#444499">原则上首选集团内酒店资源；</span>
          <br />5.如有违规行为一经查出或经举报查实的,全部损失由责任人承担，同时参照《员工行为规范》一级违规处理。
        </div>
        <!-- 
			<div style="font-size: 14px; font-weight: bold;"> 
				<br/>
				三、	商务订房流程： 
				<br/>
			</div>
			<div style="margin-left: 20px;">
				<img src="./img/hprocess.jpg" />
		</div>-->
        <div style="font-size: 14px; font-weight: bold;">
          <br />三、 出差青岛住宿标准(与差旅政策一致)：
          <br />
        </div>
        <div style="margin-left: 20px;">
          <table style="table-layout:fixed;" border="1" align="center" cellpadding="5" cellspacing="0">
            <tbody>
              <tr>
                <td width="40">
                  <img src="../../../assets/image/hotel/notice_type_line.png" width="80" height="80" />
                </td>
                <td width="110" align="center">小单及操作类的单</td>
                <td width="110" align="center">中小单</td>
                <td width="110" align="center">中单</td>
                <td width="110" align="center">中大单</td>
                <td width="130" align="center">大单、巨大单、VP单</td>
                <td width="60" align="center">引领单</td>
              </tr>
              <tr>
                <td width="200">青岛 住宿费(元/标间/日)</td>
                <td width="110">200</td>
                <td width="110">270</td>
                <td width="110">300</td>
                <td width="110">400</td>
                <td width="130">650</td>
                <td width="60">1000</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div style="font-size: 14px; font-weight: bold;">
          <br />四、 （异地来访）青岛订房招待预算控制标准
          <br />
        </div>
        <div style="margin-left: 20px;">
          <table style="table-layout:fixed;" border="1" align="center" cellpadding="5" cellspacing="0">
            <tbody>
              <tr>
                <td width="20" align="center">序号</td>
                <td width="110" align="center">异地来访方-级别</td>
                <td width="110" align="center">
                  青岛协议酒店住宿
                  <br />指导标准
                  <br />（元/间*夜）
                </td>
                <td width="110" align="center">推荐海尔酒店</td>
                <td width="110" align="center">备注</td>
              </tr>
              <tr>
                <td width="20">1</td>
                <td width="110">集团级商务住宿</td>
                <td width="110">1000元以上</td>
                <td width="110">首选集团内自有酒店</td>
                <td width="110" rowspan="5">
                  1.遇到类似啤酒节等特殊时间段根据实际按当地、当时情况调整；
                  <br />2.如遇内部酒店满房或特殊情况可选择海尔协议内酒店；
                  <br />3.对未在上述范围内人员及特殊需求按实际执行；
                </td>
              </tr>
              <tr>
                <td width="20">2</td>
                <td width="110">领域级商务住宿(大单及以上)</td>
                <td width="110">500-600</td>
                <td width="110">首选集团内自有酒店</td>
              </tr>
              <tr>
                <td width="20">3</td>
                <td width="110">平台级商务住宿(中大单及大单以上)</td>
                <td width="110">400-500</td>
                <td width="110">首选集团内自有酒店</td>
              </tr>
              <tr>
                <td width="20">4</td>
                <td width="110">普通商务住宿(中单或中小单以上)</td>
                <td width="110">400以下</td>
                <td width="110">首选集团内自有酒店</td>
              </tr>
              
            </tbody>
          </table>
        </div>

        <div style="font-size: 14px; font-weight: bold;">
          <br />五、关于相关接口人责任：
          <br />
        </div>
        <table border="1" cellspacing="0" cellpadding="5" style="text-align: center;">
          <tbody>
            <tr>
              <td width="52">序号</td>
              <td width="170">角色</td>
              <td width="570">责任承诺</td>
            </tr>
            <tr>
              <td>1</td>
              <td>业务申请人</td>
              <td align="left">
                1.对业务必要性、真实性、合规性负责；
                <br />2.对符合集团招待/差旅住宿政策, 不超标，不随意提高标准，不随意增加人数负责；
                <br />3.对超预算部分费用自付；
                <br />4.自挣自花负责；
                <br />
                <span style="color:#444499">5.原则上首选集团内酒店资源；</span>
                <br />6.如有违规行为一经查出或经举报查实的,全部损失由责任人承担，同时参照《员工行为规范》一级违规处理。
              </td>
            </tr>
            <!-- 
				<tr>
					<td>
						2
					</td>
					<td>
						财务接口人
					</td>
					<td align="left">
						1.必须有月度可用预算；
						<br />
						2.符合自挣自花原则；
						<br />
						3.符合集团招待/差旅住宿标准，<span style="color:#444499">原则上首选集团内酒店资源；</span>
						<br />
						4.如有违规行为一经查出或经举报查实的,全部损失由责任人承担，同时参照《员工行为规范》一级违规处理。 
					</td>
				</tr>-->
            <tr>
              <td>2</td>
              <td>业务审批人</td>
              <td align="left">
                1.对业务必要性、真实性、合规性负责；
                <br />2.对符合集团招待/差旅住宿政策, 不超标，不随意提高标准，不随意增加人数负责；
                <br />3.对超预算部分费用自付；
                <br />4.对自挣自花负责，确认申请必要性；
                <br />
                <span style="color:#444499">5.原则上首选集团内酒店资源；</span>
                <br />6.如有违规行为一经查出或经举报查实的,全部损失由责任人承担，同时参照《员工行为规范》一级违规处理。
              </td>
            </tr>
            <tr>
              <td>3</td>
              <td>签单人</td>
              <td align="left">
                1.对账单准确性、真实性负责；
                <br />2.对违规提取工作餐负责；
                <br />3.对超预算部分费用自付；
                <br />4.如有违规行为一经查出或经举报查实的,全部损失由责任人承担，同时参照《员工行为规范》一级违规处理。
              </td>
            </tr>
          </tbody>
        </table>

      </div>


      <template #footer>
        <van-row class="width100 " style="padding: 10px 0;" justify="center">

          <van-button size="small" type="primary" @click="dcxzSubmit">
            <span>已阅读订房须知</span>

          </van-button>
        </van-row>
      </template>

    </van-dialog>

    <!-- 效果承诺 -->
    <van-dialog v-model:show="xgDialog" title="效果承诺">
      <div style="padding:0 20px; 20px">
        <div class="text-promise text-emphasize" style="font-size: 12px;">
          <p style="font-size: 12px; font-weight: bold;">有效签单人效果承诺：</p>
          <p style="margin-left:20px;">我承诺：</p>
          <div style="margin-left:20px;">
            <p>1.对账单准确性、真实性负责；</p>
            <p>2.对违规提取工作餐负责；</p>
            <p>3.对超预算部分费用自付；</p>
            <p>4.如有违规行为一经查出或经举报查实的,全部损失由责任人承担，同时参照《员工行为规范》一级违规处理。</p>
          </div>
        </div>

        <div class="text-promise text-emphasize" style="font-size: 12px;">
          <p style="font-size: 12px; font-weight: bold;">业务申请人效果承诺：</p>
          <p style="margin-left:20px;">我承诺：</p>
          <div style="margin-left:20px;">
            <p>1.对业务必要性、真实性、合规性负责；</p>
            <p>2.对符合集团政策, 不超标，不随意提高标准，不随意增加人数负责；</p>
            <p>3.对陪餐人数在原则内不超过客人人数的三分之一负责人；</p>
            <p>4.对超预算部分费用自付；</p>
            <p>5.自挣自花负责；</p>
            <p>6.如有违规行为一经查出或经举报查实的,全部损失由责任人承担，同时参照《员工行为规范》一级违规处理。</p>
          </div>
        </div>
      </div>


      <template #footer>
        <van-row class="width100 " style="padding: 10px 0;" justify="center">

          <van-button size="small" type="primary" @click="xgSubmit">
            <span>已阅读并接受承诺</span>

          </van-button>
        </van-row>
      </template>

    </van-dialog>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch, reactive, computed } from 'vue';
import type { Ref } from 'vue';
import aMap from '@/components/aMap/index.vue';
import { LocalHotelRoomTypeEnum, LocalHotelBreakfastTypeEnum, LocalHotelBedTypeEnum, IUserListRequest, IUserInfo, RHotel, RCate, RpayType, HeaderConstant } from '@haierbusiness-front/common-libs';
import { userApi } from '@haierbusiness-front/apis';

import { localHotelApi, fileApi } from '@haierbusiness-front/apis';
import { RHotelParams } from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute, getEnumOptions } from '@haierbusiness-front/utils';
import { Item } from 'ant-design-vue/es/menu';
import UserSelectM from './userSelectM.vue';
import ymdhms from './ymdhms.vue';
import dayjs from 'dayjs';
import roomListCom from './roomList.vue';

import { showConfirmDialog, showFailToast, showSuccessToast, toastProps } from 'vant';
import { add, cloneDeep, debounce, values } from 'lodash';
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil'
// import { useAppStore } from "@/store"
import Pdfh5 from "pdfh5";
import "pdfh5/css/pdfh5.css";


// 房间列表
const roomList = ref<Array<any>>([]);
roomList.value = JSON.parse(localStorage.getItem('roomList') || '[]');
roomList.value.forEach(item => {
  item.num = 1
})

const showCalendar = ref(false)
const currentIndex = ref<number>(-1)
const showRoomDayPrices = (index: number) => {
  currentIndex.value = index
  showCalendar.value = true;
}

const deleteRoom = (index: number) => {
  roomList.value[index].select = false
}



const roomSelectPop = ref(false)
const showRoomSelectPop = () => {
  roomSelectPop.value = true
}

const formatter = (day) => {
  if (day.type == 'middle' || day.type === 'start' || day.type === 'end') {
    roomList.value[currentIndex.value].dayPrices.forEach(item => {
      if (new Date(`${item.dateTime} 00:00:00`).getTime() == day.date.getTime()) {
        day.bottomInfo = `¥${item.individualPrice / 100}`
      }

    })
  }

  if (day.type === 'start') {
    day.topInfo = '入住';
  } else if (day.type === 'end') {
    day.topInfo = '离店';
    day.bottomInfo = '退房';
  }
  return day;
};

const store = applicationStore();

const { loginUser } = storeToRefs(store);

const router = getCurrentRouter();

const route = ref(getCurrentRoute());


const hotelId = route.value?.query?.hotelId;
const checkIn = route.value?.query?.checkIn;
const checkOut = route.value?.query?.checkOut;
// 支付方式 private 自付
const paymentType = route.value?.query?.type;

// 支付方式
const payTypeList = computed(() => {
  return getEnumOptions(RpayType, true);
});

// xxx晚
const countNights = computed(() => {
  return dayjs(checkOut).diff(checkIn, 'day')
})

// 根据入住时间返回数组
const timeList = computed(() => {
  return generateDateRange(checkIn,checkOut)
})

// 计算当天的价格
const countPrice =(index: number) => {
  return roomList.value.reduce((acc, cur) => {
    if (cur.select) {
      const dayPrice = cur.dayPrices.find(item => item.dateTime === timeList.value[index]);
      if (dayPrice) {
        return acc + dayPrice.individualPrice * cur.num;
      }
    }
    return acc;
  }, 0);
}

// 获取入住总房间数
const countRooms = computed(() => {
  let amount = 0;
  let selectedRooms = roomList.value.filter(item => item.select);
  for (let room of selectedRooms) {
    amount += room.num;
  }
  return amount;
})

const generateDateRange = (start: string, end: string): string[] => {
  const startDate = dayjs(start);
  const endDate = dayjs(end);
  const dateRange: string[] = [];

  for (let date = startDate; date.isBefore(endDate); date = date.add(1, 'day')) {
    dateRange.push(date.format('YYYY-MM-DD'));
  }

  return dateRange;
}
const activeName = ref([])


// 入住人管理
const showUserSelect = ref<boolean>(false);
const userRoomList = ref<Array<any>>([]);

const deleteUser = (index: number) => {
  showConfirmDialog({
    title: '提示',
    message: '确认要删除此入住人吗?',
  })
    .then(() => {
      userRoomList.value.splice(index, 1)
    })
    .catch(() => {
    });
}

const editUser = (index: number) => {
  openType.value = 'edit'
  addForm.value = userRoomList.value[index]
  showAddPop.value = true
}

// 我的常住人
const residentUserList = ref<Array<any>>([]);
const residentUserPop = ref<boolean>(false);
const addResidentForm = ref<any>({})
// 添加常住人
const showResidentPop = ref<boolean>(false);
const openTypeResident = ref('add')
// 添加常住人
const addResidentUser = () => {
  if (openTypeResident.value == 'add') {
    addResidentForm.value.owner = loginUser.value?.username
    localHotelApi.saveFrequentUser(addResidentForm.value).then(res => {
      showSuccessToast('添加成功!')
      getResidentUserList()
      addResidentForm.value = {}
      showResidentPop.value = false;
    })
  } else {
    editResidentUser()
  }

}
// 编辑常住人
const editResidentUser = () => {
  localHotelApi.updateFrequentUser(addResidentForm.value).then(res => {
    showSuccessToast('编辑成功!')
    getResidentUserList()
    addResidentForm.value = {}
    showResidentPop.value = false;
  })
}


const addResidentUserFuc = () => {
  showResidentPop.value = true;
  openTypeResident.value = 'add';
  addResidentForm.value = {}
}


// 编辑常住人
const editResidentUserFuc = (index: number) => {
  openTypeResident.value = 'edit';
  addResidentForm.value = residentUserList.value[index]
  showResidentPop.value = true;
}
// 选择常住人
const chosedResidentUser = (item: any) => {
  const result = userRoomList.value.findIndex(user => user.userName == item.userName && user.phoneNum == item.phoneNum)
  if (result > -1) {
    userRoomList.value[0].id = item.id
    showFailToast('已经添加此入住人!')
  } else {
    item.frequent = 1
    userRoomList.value.push(item)
    residentUserPop.value = false
  }

}

const deleteResidentUser = (index: number) => {
  showConfirmDialog({
    title: '提示',
    message: '确认要删除此常住人吗?',
  })
    .then(() => {
      localHotelApi.deleteFrequentUser(residentUserList.value[index]).then(res => {
        showSuccessToast('删除成功!')
        getResidentUserList()
      })
    })
    .catch(() => {
    });
}

// 下啦刷新常住人
const onRefreshResidentUserLoading = ref(false)
const onRefreshResidentUser = () => {
  getResidentUserList()
};

const getResidentUserList = () => {
  localHotelApi.searchFrequentUser({
    owner: loginUser.value?.username
  }).then(res => {
    residentUserList.value = res.data
    onRefreshResidentUserLoading.value = false
  }).catch(err => {
    onRefreshResidentUserLoading.value = false
  })
}


// 添加入住人
const showAddPop = ref<boolean>(false);
const addForm = ref<any>({});

const openType = ref('add')

const addInUser = () => {
  if (openType.value == 'add') {
    userRoomList.value.push(addForm.value)
    showSuccessToast('添加成功!')
    addForm.value = {}
    showAddPop.value = false;
  } else {
    editInUser()
  }

}

const addInUserFuc = () => {
  showAddPop.value = true;
  openType.value = 'add';
  addForm.value = {
    owner: loginUser.value?.username
  }
}

const editInUser = () => {
  showSuccessToast('修改成功!')
  addForm.value = {}
  showAddPop.value = false;
}
const rzrLoading = ref(false)
// 入住人提交
const putCheckInUser = () => {
  rzrLoading.value = true
  if (userRoomList.value.length < 1) {
    showFailToast('请先添加入住人再提交!')
    rzrLoading.value = false
    return
  }
  localHotelApi.putCheckInUser(userRoomList.value).then(res => {
    form.value.treatInfo.guestListId = res.data
    form.value.treatInfo.guestCount = userRoomList.value.length
    form.value.treatInfo.guestNames = userRoomList.value.map(item => item.userName).join(',')
    showSuccessToast('提交成功!')
    showUserSelect.value = false;
    rzrLoading.value = false

    // 重新获取一下常住人数据
    getResidentUserList()
  }).catch(err => {
    rzrLoading.value = false
  })
}




// 效果承诺弹窗
const xgDialog = ref<boolean>(false)

const xgSubmit = () => {
  xgDialog.value = false
}

// 订房须知弹窗
const dcxzDialog = ref<boolean>(false)

const dcxzSubmit = () => {
  dcxzDialog.value = false
}

// 订房隐私政策
const showYszcPopup = ref<boolean>(false)
const yszcpdfRef = ref(null);
const yszcpdf = new URL('@/assets/yszc.pdf', import.meta.url).href
const LoadYszcPdf = () => {
  const pdfh5Yszc = new Pdfh5(yszcpdfRef.value, {
    pdfurl: yszcpdf,
  });
 
  pdfh5Yszc.on("complete", (status, msg, time) => { 
    showYszcPopup.value=true
  });
};


// 餐类选择弹窗
const showClPicker = ref<boolean>(false);
const columnsCl = ref<Array<RCate>>();
const onConfirmCl = ({ selectedOptions }: any) => {
  showClPicker.value = false;
  form.value.cateTypeId = selectedOptions[0].id;
  form.value.cateTypeText = selectedOptions[0].name;
};

// 车位预留选择 是否预留车位，0：否；1：是；
const showCwPicker = ref<boolean>(false);
const columnsCw = [
  {
    text: '预留',
    value: 1,
  },
  {
    text: '不预留',
    value: 0,
  },
];

//预算总额
const budgetAmount = () => {
  let amount = 0;
  let selectedRooms = roomList.value.filter(item => item.select);
  for (let room of selectedRooms) {
    let dayPriceAmount = 0;
    for (let day of room.dayPrices) {
      dayPriceAmount += day.individualPrice;
    }
    amount += dayPriceAmount * room.num;
  }
  console.log('amount', amount);
  return amount;
};

const priceToYuan = (value: number) => {
  if (!value) return 0;
  let yuan: number = value / 100;
  yuan = yuan.toFixed(2) * 1;
  return yuan;
};


// 文件上传
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const fileList = ref([]);

const businessList = import.meta.env.VITE_BUSINESSTRAVEL_URL;

const afterRead = (options: any) => {
  const formData = new FormData();
  formData.append('file', options.file);
  localHotelApi
    .upload(formData)
    .then((it) => {
      form.value.treatInfo.seatOrderId = it.data;

      form.value.treatInfo.seatOrderFileList = [
        {
          uid: '1',
          name: options.file.name,
          status: 'done',
          // url: download(it.data),
          url: `${businessList}/api/common/v1/file/download/${it.data}`,
        },
      ];
    })
    .finally(() => {
      // uploadLoading.value = false;
    });
};

const delFile = () => {
  form.value.treatInfo.seatOrderId = '';
  form.value.treatInfo.seatOrderFileList = [];
};

const onConfirmCw = ({ selectedValues }: any) => {
  form.value.treatInfo.needParking = selectedValues[0];
  showCwPicker.value = false;
};

// 是否需要座牌
const showZpPicker = ref<boolean>(false);
const columnsZp = [
  {
    text: '需要',
    value: 1,
  },
  {
    text: '不需要',
    value: 0,
  },
];

const onConfirmZp = ({ selectedValues }: any) => {
  form.value.treatInfo.needSeatCard = selectedValues[0];
  showZpPicker.value = false;
};

// 日期选择
// 选择器默认范围（当前时间）
const minDate = new Date();
const maxDate = new Date(2025, 10, 1);

// dayjs().add(1, 'day').format('YYYY-MM-DD')
const minTime = `${dayjs().hour()}:${dayjs().minute()}:${dayjs().second()}`

const currentRq = ref<Array<string>>([]);

const showRqPicker = ref<boolean>(false);
const confirmRq = ({ selectedValues }: any) => {
  form.value.eatingDay = selectedValues.join('-');
  showRqPicker.value = false;
};

const currentSj = ref<Array<string>>([]);
const showSjPicker = ref<boolean>(false);
const confirmSj = ({ selectedValues }: any) => {
  form.value.keepTime = selectedValues.join(':');
  showSjPicker.value = false;
};

const opewSjPicker = () => {
  currentSj.value = form.value.keepTime ? form.value.keepTime.split(':') : []
  showSjPicker.value = true;

}

const goBack = () => {
  router.back(-1);
};
// 获取餐厅信息
const detail = ref<RHotel>();

const getDetail = (hotelId: string) => {
  const params = {
    hotelId,
  };

  localHotelApi.hotelSingle(params).then((res) => {
    detail.value = res.data;
    form.value.fullname = detail.value.fullname
    columnsCl.value = res.data.cateType;
    form.value.providerCode = res.data.providerCode;
    form.value.consumptionSeat = res.data.consumptionSeat * 1;
  });
};

watch(
  () => hotelId,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);
// 
watch(
  residentUserPop,
  (newVal: boolean,oldVal: boolean) => {

    if (newVal) {
      getResidentUserList()
    }
  },
  
);

// 选择业务申请人
const chosedPerson = (item: IUserInfo) => {
  form.value.applicant.name = item.nickName;
  form.value.applicant.email = item.email;
  form.value.applicant.mobile = item.phone;
  form.value.applicant.phone = item.phone;
  form.value.applicant.orderCode = item.username;
  form.value.applicant.username = item.username;
  form.value.applicant.departmentName = item.departmentName || item.enterpriseName;
};

const form = ref<RHotel>({
  //业务申请人
  applicant: {
    email: loginUser.value?.email, //联系人邮箱
    mobile: loginUser.value?.phone, //联系人电话
    name: loginUser.value?.nickName, //联系人名称,
    username: loginUser.value?.username, //联系人工号
    departmentName: loginUser.value?.departmentName || loginUser.value?.enterpriseName,
    orderCode: loginUser.value?.username, //联系人工号
    phone: loginUser.value?.phone, //联系人电话
  },
  applicantId: loginUser.value?.username, //业务申请人工号
  applyCause: '/', //申请事由
  businessAim: '', //业务目标
  //预算信息
  budget: {
    paymentType: paymentType == 'public' ? '1' : '2',
    paymentCard: '', //酒店储值卡
  },
  payType: paymentType == 'public' ? 1 : 2,
  cateTypeId: 0, //餐类ID
  cateTypeText: '',
  consumptionSeat: 0, //餐位费；分； 用于计算预算金额
  consumptionStandard: undefined, //就餐标准 含酒水。PS：所有人加起来的
  eatingTime: '', //就餐时间
  eatingDay: '', //就餐日期
  hotelId: hotelId, //酒店ID
  //为选择内部酒店原因
  outerHotel: {
    reason: '1',
    remark: '',
  },
  keepTime: '',
  //通知信息
  notifying: {
    email: '',
    needEmail: 0, // 0, //是否邮件通知 0：否；1：是
    mobile: '',
    needCall: 0, //0 //是否电话通知 0：否；1：是
    needSms: 0, // 0, //是否短信通知 0：否；1：是
    orderCode: '',
    phone: '',
  },
  orderCode: '',
  //经办人
  owner: {
    email: loginUser.value?.email, //联系人邮箱
    mobile: loginUser.value?.phone, //联系人电话
    name: loginUser.value?.nickName, //联系人名称,
    username: loginUser.value?.username, //联系人工号
    departmentName: loginUser.value?.departmentName || loginUser.value?.enterpriseName,
    orderCode: loginUser.value?.username, //联系人工号
    phone: loginUser.value?.phone, //联系人电话
  },
  ownerId: loginUser.value?.username, //经办人工号
  providerCode: detail.value?.providerCode, //供应商编码
  remark: '', //禁忌和备注
  serviceFee: 0, //服务费率
  //订单招待信息
  treatInfo: {
    accompanyCount: undefined, //陪同人数
    accompanyLeader: '', //我方主要陪同领导
    guestCompany: '/', //来宾单位
    guestNames: '/', // 来宾姓名
    guestCount: undefined, //来宾人数
    mainGuestNames: '', //主宾姓名，可多个人
    mainGuestPosition: '', //主宾职务
    needParking: 0, // 0, //是否预留车位，0：否；1：是；
    needSeatCard: 0, // 0, //需要座牌
    orderCode: '',
    seatOrderId: 0, //upload_file ID，座次文件ID
    seatOrderFileLoading: false, //
    seatOrderFileList: [], //
    signerMobile: '', //签单人手机
    signerName: '', //签单人姓名
    vehicleNo: '', //车牌号，可多个
  },
  type: 1, //订单类型 1:订房 , 2:特产
  workingLunchCount: undefined, //工作餐提取人数
  workingLunchFee: 0, //工作餐金额 工作餐提取人数 × 50
  platType: '1', // 客户端类型 0：PC 1：H5
  businessFlag: paymentType == 'public' ? '0' : '1', // 因公因私 0：因公 1：因私
});
const showSubmitDialog = ref<boolean>(false);
const onSubmit = (values) => {
  showSubmitDialog.value = true;
  btnLoading.value = true
};

// 页面滚动到表单验证失败的地方
const scrollToErrorField = () => {
  const firstErrorField = document.querySelector('.van-field__error-message') // 使用类名选择第一个错误元素
  if (firstErrorField) {
    firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
}
const onFailed = (values) => {
  console.log('failed--------', values)
  scrollToErrorField()
}
const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false)

// const appStore = useAppStore()

const payUrl = import.meta.env.VITE_BUSINESS_PAY_URL;

const btnLoading = ref<boolean>(false);
const save = () => {
  // 判断是否选择房间
  let selectedRooms = roomList.value.filter(item => item.select);
  if (selectedRooms.length < 1) {
    showFailToast('请选择预定房间!')
    btnLoading.value = false
    return
  }


  const params = cloneDeep(form.value)
  params.orderSource = '6'
  params.room = selectedRooms.map(room => {
    return {
      checkIn: room.checkIn, //yyyy-MM-dd 入住日期
      checkOut: room.checkOut, //yyyy-MM-dd 离店日期
      //id: 0,
      name: room.name, //房型名称
      num: room.num,
      //orderCode: "",
      roomId: room.id,
      //房间价格
      roomPrice: room.dayPrices.map(item => {
        return {
          orderRoomId: room.id, //预定房型ID
          roomPriceId: item.id //房价ID
        };
      })
    };
  })
  localHotelApi.save(params).then((res) => {
    // showSuccessToast('预订成功!')
    // 跳转中台支付
    const payParam = {
      orderNo: res.data
    }
    if (paymentType != 'public') {
      router.push({
        path: '/hotel/orderList',
        query: {
          'reload': 1
        }
      })
      return
    }
    localHotelApi.prePay(payParam).then(url => {
      const href = url.data
      // 如果因公跳转支付中台
      const a = document.createElement("a");
      a.setAttribute("href", href);
      a.setAttribute("style", "display:none");
      document.body.appendChild(a);
      a.click();
      a.parentNode.removeChild(a);
      
      btnLoading.value = false
    }).catch(err => {
      btnLoading.value = false
    })

    // router.push({ path: '/hotel/orderList'  })
  }).catch(err => {
    btnLoading.value = false
  })
};
onMounted(() => {
  getResidentUserList()
});
</script>


<style lang='less' scoped>
@import url(./common.less);

.reservation-content {
  background: #f8f8f8;
  padding: 44px 0px;
}

.submit-dialog-content {
  padding: 0 40px 20px;
  color: red;
  display: flex;
}

:deep(.van-field__label--required:before) {
  position: absolute;
  left: 6px;
}

.item-label {
  padding: 10px;
  background: linear-gradient(179deg, #EEF5FF 0%, #FFFFFF 100%);
  border-radius: 8px;
  border: 1px solid #FFFFFF;

  .item-label-title {
    font-weight: 500;
    color: rgba(38, 38, 38, 1);
    font-size: 14px;
    height: 25px;
    align-items: center;
  }

  .item-lable-more {
    font-weight: 400;
    font-size: 12px;
    color: #8C8C8C;
    height: 24px;
    align-items: center;
  }

  .item-label-price {
    font-weight: 400;
    font-size: 12px;
    color: #8C8C8C;
    height: 24px;
    align-items: center;

    .item-price {
      color: rgba(255, 83, 57, 1);

      >span {
        font-size: 18px;
      }
    }

    .item-room-num {
      align-items: center;

      .room-num-add {
        width: 16px;
        height: 16px;
        background: url('@/assets/image/hotel/room_add.png');
        background-size: cover;
      }

      .room-num-sub {
        width: 16px;
        height: 16px;
        background: url('@/assets/image/hotel/room_sub.png');
        background-size: cover;

        &.active {
          background-image: url('@/assets/image/hotel/room_sub_active.png');
        }
      }
    }
  }
}


.header {

  .my {
    font-weight: 400;
    font-size: 28px;
    color: #2681FF;
    line-height: 40px;
  }

  :deep(.van-nav-bar__content) {
    height: 88px;
  }

  :deep(.van-nav-bar__title) {
    font-weight: 400;
    font-size: 34px;
    color: #000000;
    line-height: 40px;
  }
}
</style>