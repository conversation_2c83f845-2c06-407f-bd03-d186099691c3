<script setup>
import { ref } from 'vue';
import { usePortalStore } from '../store';

const store = usePortalStore();
const activeId = ref('');
</script>

<template>
  <div class="consultant">
    <template v-for="consultantItem of store.consultantList" :key="consultantItem.id">
      <div class="consultant-item" @mouseenter="activeId = consultantItem.id" @mouseleave="activeId = ''">
        <img :src="consultantItem.pic" />
        <div class="mask" />
        <div class="info">
          <div class="name text-ellipsis">{{ consultantItem.nickName }}</div>
          <div class="desc text-ellipsis" :style="{ marginBottom: activeId === consultantItem.id ? '11px' : '18px' }">
            {{ consultantItem.description }}
          </div>
          <div class="option" :style="{ height: activeId === consultantItem.id ? '44px' : 0 }">
            <span class="option-text">立即咨询</span>
            <span class="arrow" />
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped lang="less">
.consultant {
  display: flex;
  align-items: center;
  width: 100%;
  overflow-x: auto;
  padding-bottom: 2px;

  .consultant-item {
    flex: 0 0 240px;
    cursor: pointer;
    position: relative;
    height: 300px;
    margin-right: 50px;
    border-radius: 0px 0px 16px 16px;
    overflow: hidden;
    &:last-child {
      margin-right: 0;
    }
    > img {
      width: 100%;
      height: 100%;
    }
    .mask {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 122px;
      background: linear-gradient(180deg, rgba(0, 14, 75, 0) 0%, rgba(0, 0, 0, 0.76) 100%);
      border-radius: 0px 0px 16px 16px;
      backdrop-filter: blur(0px);
    }
    .info {
      width: 100%;
      position: absolute;
      left: 0;
      bottom: 0;
      .name {
        padding: 0 20px;
        font-weight: 500;
        font-size: 20px;
        color: #ffffff;
        line-height: 28px;
      }
      .desc {
        padding: 0 20px;
        font-size: 16px;
        color: #ffffff;
        line-height: 22px;
        margin-top: 4px;
      }
      .option {
        transition: height 0.5s ease;
        overflow: hidden;
        padding: 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        // height: 44px;
        background: linear-gradient(90deg, #1868db 0%, #3bc6fc 100%);
        border-radius: 0px 0px 16px 16px;
        &:hover {
          .arrow {
            background-position: left 0px top -8.5px;
          }
        }
        .option-text {
          font-weight: 500;
          font-size: 18px;
          color: #ffffff;
          line-height: 25px;
        }
        .arrow {
          display: block;
          width: 17px;
          height: 17px;
          background-image: url(@/assets/image/home/<USER>
          background-size: 34px 34px;
          background-repeat: no-repeat;
          position: relative;
          flex-shrink: 0;
          background-position: right 0px top -8.5px;
          transition: 0.3s ease-out;
        }
      }
    }
  }
}
</style>
