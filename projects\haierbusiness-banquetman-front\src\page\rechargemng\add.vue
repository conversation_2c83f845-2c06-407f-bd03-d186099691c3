<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem,
  Form as hForm,
  FormItem as hFormItem,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  Cascader as hCascader,
  Card as hCard,
  TreeSelect as hTreeSelect,
  Modal as hModal,
  Input as hInput,
  Icon as hIcon,
  Textarea as hTextarea,
  Upload as hUpload,
  message
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, DownOutlined, UpOutlined,CloseOutlined } from '@ant-design/icons-vue';
import { rechargemng<PERSON>pi, departmentApi, banquetApi, fileApi } from '@haierbusiness-front/apis';
import {
  IPserviceRatemng
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute ,smalltoBIG} from '@haierbusiness-front/utils';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';
import type { ShowSearchType } from 'ant-design-vue/es/cascader';
import { Cascader } from 'ant-design-vue';

import router from '../../router'
// const router = useRouter()
const store = applicationStore();
const uploadLoading = ref<boolean>(false)
const currentRouter = ref()
const loading = ref<boolean>(false)
onMounted(async () => {
  currentRouter.value = await router
  // 获取缓存数据
  if (localStorage.getItem('banquet_policy_form')) {
    formState.value = JSON.parse(localStorage.getItem('banquet_policy_form'))
  }
})
const route = ref(getCurrentRoute());
const id = route.value?.query?.id;

const { loginUser } = storeToRefs(store);

const rules = {
  rechargeAmount: [
    {
      required: true,
      message: '请输入充值金额',
      trigger: 'change',
    },
  ],
  // fileList: [
  //   {
  //     required: true,
  //     message: '请上传附件信息',
  //     trigger: 'blur',
  //   },
  // ],
}


const formState = ref<IPserviceRatemng>(
  {
    programmeAnnexes:"",
    settleMonth: "",
    remark: "",
    rechargeAmount:null,
    fileList:[]
  }
);

const handleRemove: UploadProps['onRemove'] = file => {
  const index = formState.value.fileList.indexOf(file);
  const newFileList = formState.value.fileList.slice();
  newFileList.splice(index, 1);
  formState.value.fileList = newFileList;
};

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
}

const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const upload = (options: any) => {
  uploadLoading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);
  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path
      options.file.fileName = options.file.name
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};


const cancel = () => {
  currentRouter.value.back(-1);  
  localStorage.removeItem('banquet_policy_form')
}

const delRestaurant =(index:number) => {
  formState.value?.restaurants?.splice(index, 1)
}

// 下载模版
const downLoadTem = () =>{
   if(!formState.value.rechargeAmount){
    message.info(`请输入充值金额`);
    return
   }
   rechargemngApi.exportBanquetBalance({rechargeAmount:formState.value.rechargeAmount,remark:formState.value.remark})
}

// 修改金額
const changeRechargeAmount = ()=>{
  formState.value.fileList = []
}

// 提交或者保存
const onFinish = () => {
loading.value = true
const array = formState.value.fileList.map((item)=>{
  return {
    fileName:item.fileName,
    filePath:item.filePath,
  }
})
  formState.value.programmeAnnexes = JSON.stringify(array)
  rechargemngApi.add(formState.value).then(res => {
    message.success(`提交成功`);
    currentRouter.value.push('/rechargemng');
    loading.value =false
  })
  .catch(()=>{
    loading.value =false
  })
}

</script>


<template>
  <div>
    <h-row justify="space-between" style="padding: 20px;">
      <h-col style="font-size: 18px;">新增充值单</h-col>
      <h-col><h-button type="link" @click="currentRouter.back(-1)">返回</h-button></h-col>
    </h-row>
    <div style="background-color: #ffff;height: 100%;width: 1200px;padding: 50px;margin: 0 auto; overflow: auto;">
      <h-form :model="formState" name="basic" :label-col="{ span: 3 }" :wrapper-col="{ span: 14 }" autocomplete="off"
        labelAlign="right" @finish="onFinish" :rules="rules">
        <h-form-item name="rechargeAmount" label="充值金额">
          <a-input-number :max="99999999" :precision="2" @change="changeRechargeAmount" v-model:value="formState.rechargeAmount" style="width:50%" />
          <span style="margin-top:5px;margin-left:10px;">
            <a @click="downLoadTem">下载附件</a>
          </span>
          <div v-if="formState.rechargeAmount" style="color:#1677ff;">{{smalltoBIG(formState.rechargeAmount)}}</div>
        </h-form-item>
        <!-- <h-form-item name="fileList" label="附件信息">
          <h-upload v-model:fileList="formState.fileList"  :custom-request="upload" :multiple="true" :max-count="5" :before-upload="beforeUpload" @remove="handleRemove">
            <h-button>
              <upload-outlined></upload-outlined>
              上传附件
            </h-button>
          </h-upload>
        </h-form-item> -->
        <h-form-item name="remark" label="备注信息">
          <h-textarea v-model:value="formState.remark" placeholder="请输入备注信息"  style="width: 50%" :rows="4" />
        </h-form-item>
        <h-form-item :wrapper-col="{ offset: 10, span: 16 }">
          <!-- <h-button style="margin-right: 20px;" :loading="loading" @click="cancel">取消</h-button> -->
          <h-button type="primary" :loading="loading" html-type="submit">提交</h-button>
        </h-form-item>
      </h-form>

    </div>
    
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

:deep(.ant-descriptions-item-label) {
  width: 200px;
}

:deep(.ant-descriptions-item-content) {
  width: 300px;
}
</style>
