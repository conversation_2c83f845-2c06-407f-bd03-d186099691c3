<script lang="ts" setup>
// 省市区三级联动
import { Cascader as hCascader, Spin as hSpin, message } from 'ant-design-vue';
import { onMounted, ref, watch, defineProps, defineEmits } from 'vue';
import { cityApi } from '@haierbusiness-front/apis';

const props = defineProps({
  cityShow: {
    type: Boolean,
    default: false,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  cityList: {
    type: Array,
    default: [],
  },
});

const emits = defineEmits(['cityChooseBack', 'cityLoadingFunc']);

watch(
  () => props.cityShow,
  (newVal) => {
    if (newVal) {
      cityList.value = props.cityList || [];
    }
  },
);

const cityLoading = ref<Boolean>(false);
const cityList = ref<Array>([]);
const cityDict = ref<Array>([]);

// 省市区三级联动
const getCityList = async () => {
  emits('cityLoadingFunc', true);
  // cityLoading.value = true;
  const params = {
    id: 1,
    providerCode: 'MT',
    subdistrict: 3,
  };

  const CN = await cityApi.getCityTree(params);
  cityDict.value = CN.children;
  cityLoading.value = false;

  cityList.value = props.cityList || [];

  emits('cityLoadingFunc', false);
};

const changeCity = (_value, selectedOptions) => {
  emits('cityChooseBack', {
    cityList: _value,
    cityOptions: selectedOptions,
  });
};

onMounted(() => {
  getCityList();
});
</script>

<template>
  <div class="a_map_home">
    <!-- 省市区三级联动 -->
    <h-spin :spinning="cityLoading">
      <h-cascader
        v-model:value="cityList"
        @change="changeCity"
        show-search
        :fieldNames="{ label: 'name', value: 'id', children: 'children' }"
        :options="cityDict"
        placeholder="请选择"
        :multiple="props.multiple"
        style="width: 100%"
        allow-clear
      />
    </h-spin>
  </div>
</template>

<style lang="less" scoped></style>
