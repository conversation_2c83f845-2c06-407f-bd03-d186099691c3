import { IPageRequest } from "../../basic";

export class IAdvertisementProviderRequest extends IPageRequest {
    /**
     * 标题
     */
    title?: string

    /**
     * 描述
     */
    content?: string

    /**
     * 图片
     */
    imgUrl?: string
      /**
     * 状态
     */
    showStatus?:number
    id?: number
}
export interface IAdvertisementPrividerAccount {
    /**
     * 标题
     */
    title?: string

    /**
     * 描述 
     */
    content?: string

    /**
     * 图片
     */
    imgUrl?: string
    /**
     * 状态
     */
    showStatus?: number
    /**
     * 顺序
     */
    showOrder?: number

    id?: number

    jumpLinkPc?: string

    isTopping?: boolean

    author?: string

}
export interface IAdvertisementPriverResponse {

    jumpLinkPc?: string

    /**
     * 标题
     */
    title?: string

    /**
     * 描述 
     */
    content?: string

    /**
     * 图片
     */
    imgUrl?: string
    /**
     * 状态
     */
    showStatus?: number
    /**
     * 顺序
     */
    showOrder?: number
    /**
     * 创建时间
     */
    gmtCreate?: string
    id?: number


}
