import { download, get, post, filepost, originalGet } from '../request'

export const waterworkAreaApi = {
    list: (params: any): Promise<void> => {
        return get('waterworks/api/tregion/page', params)
    },
    listAll: (params: any): Promise<void> => {
        return get('waterworks/api/tregion/list', params)
    },
    add: (params: any): Promise<void> => {
        return post('waterworks/api/tregion/save', params)
    },
    update: (params: object): Promise<void> => {
        return post(`waterworks/api/tregion/update`, params);
    },
    export: (params: any): Promise<void> => {
        return download('waterworks/api/tregion/export', params)
    },
    delete: (ids: string): Promise<void> => {
        return post(`waterworks/api/tregion/delete/${ids}`);
    },
}