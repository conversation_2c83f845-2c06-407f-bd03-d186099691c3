import { IPageRequest } from "../../basic";

export interface TablePagination {
    current: number;
    pageSize: number;
    total: number;
    state?: number
}

export interface AssessmentItem {
    id: string;
    name: string;
    detail: string;
    score: number;
    money: number;
    examType: string;
}

export interface ViolationRecord {
    id: number;
    examineCode: string;
    type: number;
    score: number;
    fine: number;
    gmtCreate: string;
    examineState: number;
    title?: string;
    mainCode?: string;
    violationDisposeEndTime?: string | [string, string] | null;
    entry?: string;
    details?: string;
    violationDesc?: string;
    violationTime?: string | [string, string] | null;
    path?: string[];
    rejectReason?: string;
    state?: number
}

export interface ProcessingRecord {
    id: number;
    operateType: number;
    illustrate: string;
    applyTime: string;
    result: number;
    disposeIdea: string;
    disposeTime: string;
    path: Array<{
        type: number;
        path: string;
    }>;
}

export interface PaymentRecord {
    paymentCode: string;
    payer: string;
    paymentTime: string;
    amount: number;
}

export interface UploadFile {
    url?: string;
    filePath?: string;
    fileName?: string;
    uid: string;
    name: string;
    status: 'uploading' | 'done' | 'error' | 'removed';
}

export interface ProcessForm {
    description: string;
    path: string[];
}

export class ServiceExamFilter extends IPageRequest {
    begin?: string
    end?: string
    examineCode?: string
    examineState?: number
    mainCode?: string
    creator?: string
    type?: string
    merchantName?: string
    merchantId?: number
    againTime?: string
    endTime?: string
    id?: number
    name?: string
    state?: number
    entry?: string
    score?: number
    createName?: string
    violationTime?: string
    violationDisposeEndTime?:string
    gmtCreate?: string
    gmtCreateEnd?: string
    isDispose?: boolean
    details?: string
    violationDisposeStartTime?: string
}

export class ServiceExam {
    id?: number;
    merchantId?: number;
    mdrId?: number | null;
    miceId?: number | null;
    creator?: string;
    merchantName?: string;
    mainCode?: string;
    miceName?: string;
    createTime?: string;
    gmtCreate?: string;
    entry?: string;
    details?: string;
    type?: string;
    score?: number;
    fine?: number;
    status?: number;
    result?: number;
    disposeIdea?: string;
    illustrate?: string;
    path?: string[];
    updater?: string;
    updateTime?: string;
    hotelId?: string;
    hotelName?: string;
    priceValidStart?: string;
    priceValidEnd?: string;
    reason?: string;
    attachment?: string;
    violationTime?: [string, string] | null;
    violationDisposeEndTime?: [string, string] | null;
    violationDesc?: string;
    evidenceMaterial?: string;
    acceptCode?: string;
    merchantExamItemId?: number | null;
    examineCode?: string;
    examineState?: number;
    state?: number
    pathType?: number
}