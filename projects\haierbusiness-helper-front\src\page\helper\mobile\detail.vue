<template>
  <div class="helper-detail">
    <div class="helper-detail-top" v-if="tabValue == 2 && detail?.piggybackStatus == 10">
      正在为您寻找顺路创客，请保持通讯畅通…
    </div>
    <div class="helper-detail-contetnt">
      <!-- 出发地 -->
      <van-cell-group inset class="mb-10 mt-10">
        <div class="detail-city-box flex">
          <div class="transparent-bg"></div>
          <div class="detail-city-text">
            <div class="city-name mb-5">{{ detail?.fromCityName }}</div>
            <div class="city-address">{{ detail?.pickupAddress }}</div>
          </div>
          <div class="detail-city-position flex" @click="copyAddress('begin')" >
            <!-- <van-icon size="18" class="mb-5" name="location-o" /> -->
            复制地址
          </div>
        </div>
      </van-cell-group>

      <!-- 目的地 -->
      <van-cell-group inset class="mb-10 mt-10">
        <div class="detail-city-box flex">
          <div class="transparent-bg"></div>
          <div class="detail-city-text">
            <div class="city-name mb-5">{{ detail?.destCityName }}</div>
            <div class="city-address">{{ detail?.destAddress }}</div>
          </div>
          <div class="detail-city-position flex" @click="copyAddress('end')">
            复制地址
          </div>
        </div>
      </van-cell-group>

      <van-cell-group inset class="mb-10">
        <van-cell title="发布时间" :value="detail?.createTime" />
        <van-cell title="联系电话" :value="detail?.creatorPhone" />
        <van-cell title="期望送达" value-class="large-value">
          <template #value>
            {{ dayjs(detail?.expectTimeFrom).format(' M月D日 HH:mm') }} - {{
              dayjs(detail?.expectTimeTo).format('M月D日 HH:mm') }}
          </template>
        </van-cell>
        <van-cell title="需求发布人">
          <template #value>
            <div>
              <span class="flex-center" style="height: 20px;   justify-content: flex-end;" @click="acceptDemand(detail, 10)">
                <span style="margin-right: 5px; line-height: 20px;">
                  {{ `${detail?.createUserName}(${detail?.createUser})` }}
                </span>
                <span class="icon-chart pointer"></span>
              </span>
            </div>
          </template>
        </van-cell>
        <van-cell title="物品类型" :value="IHelperThingsTypeEnum[detail?.objectType]" />
        <van-cell title="物品重量" :value="IHelperWeightTypeEnum[detail?.objectWeightRange]" />
        <van-cell title="物品描述" :label="detail?.objectDesc">
          <template #label>
            <div class="mb-10">
              <div style="word-break: break-all;">{{ detail?.objectDesc }}</div>
            </div>
              <van-row class="width100">
                <van-col style="display: flex; align-items: center;justify-content: center;" :span="8"  v-for="item, index in detail?.files" :key="index">
                  <van-image class="mb-5" @click="previewImage(index)" width="80"
              height="80" :src="item.fileUrl" />
                </van-col>
              </van-row>
          </template>
        </van-cell>
      </van-cell-group>


      <van-cell-group inset class="mb-10" v-if="loginUser?.username == detail?.createUser">
        <van-cell title="需求接受人">
          <template #value>
            <div v-if="detail?.acceptObj?.acceptUserCode">
              <span class="flex-center" style="height: 20px;   justify-content: flex-end;" @click="contactAcceptor(detail)">
                <span style="margin-right: 5px; line-height: 20px;">
                  {{ `${detail?.acceptObj?.acceptUserName}(${detail?.acceptObj?.acceptUserCode})` }}
                </span>
                <span class="icon-chart pointer"></span>
              </span>
            </div>
            <div v-else>
              <span style="color: #FF8133">匹配中</span>
            </div>
          </template>
        </van-cell>

      </van-cell-group>

      <van-cell-group inset class="mb-10" v-if="loginUser?.username == detail?.createUser">
        <van-cell title="咨询记录">
          <template #label>
            <span v-if="!detail?.acceptRecords?.length" style="color: #FF8133">暂无记录</span>

            <div v-else>
              <span class="flex-center mb-5" style="height: 20px; justify-content: space-between" @click="contactService(item)"  v-for="item, index in detail?.acceptRecords" :key="index">
                <span>{{item.updateTime}}</span>
                <span style="margin-right: 5px; line-height: 20px;" class="flex-center">
                  {{ `${item.acceptUserName}(${item.acceptUserCode})` }}
                  <span class="icon-chart pointer ml-5"></span>
                </span>
              </span>
            </div>
          </template>

         
        </van-cell>
      </van-cell-group>
    </div>

    <!-- 新增按钮 -->
    <van-sticky position="bottom" :offset-bottom="0">
      <div class="addbtn-box" >
        <van-button @click.stop="cancelAccept(detail)"
          v-if="tabValue == 2 && detail?.piggybackStatus != 30 && detail?.piggybackStatus != 40" class="add-btn"
          type="danger">取消需求</van-button>

        <van-button @click.stop="acceptDemand(detail, 10)" style="border: none;"
          v-if="(tabValue == 1 || tabValue == 3) && detail?.createUser != loginUser?.username" class="add-btn">
          <div class="flex" style="align-items: center">
            <span class="icon-chart mr-5"></span>
            <span style="color: rgba(0, 115, 229, 1);">联系发布人</span>
          </div>
        </van-button>

        <van-button @click.stop="acceptDemand(detail,20)" v-if="tabValue == 1 && detail?.createUser != loginUser?.username"
          class="add-btn" type="primary">接受需求</van-button>
      </div>
    </van-sticky>
  </div>
</template>

<script lang="ts" setup>
import { computed, createVNode, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { IHelperThingsTypeEnum, ICity, IHelperThingsTypeTagColorMap, IHelperWeightTypeEnum, UserTicketResponseDTO, UserFlightTicketDTO, IHelperStatusEnum, IHelperStateTagColorMap, IHelperSearchParam, IHelperListRes, IHelperReq } from '@haierbusiness-front/common-libs';
import { helperApi } from '@haierbusiness-front/apis';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getEnumOptions } from '@haierbusiness-front/utils';
import { cityApi, tripApi } from '@haierbusiness-front/apis';
import { showImagePreview, showDialog, showSuccessToast, showConfirmDialog, showFailToast, showToast } from 'vant';

import {
  SwapRightOutlined,
  CaretDownOutlined
} from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
const store = applicationStore();
const { loginUser } = storeToRefs(store);
console.log('🚀 ~ loginUser:', loginUser);

const router = getCurrentRouter();
const route = ref(getCurrentRoute());

const id = route.value?.query?.id;

const tabValue = route.value?.query?.tabValue;

const detail = ref<IHelperReq>()
const detailLoading = ref(false)

const copyAddress = async(type:string) => {
  let copyResult = true

  let text = ''
  if (type == 'begin') {
    text = detail.value?.fromCityName + detail.value?.pickupAddress
  }else {
    text = detail.value?.destCityName + detail.value?.destAddress
  }
  
  if (!!window.navigator.clipboard) {
    // 利用clipboard将文本写入剪贴板（这是一个异步promise）
    await window.navigator.clipboard.writeText(text).then((res) => {
      showSuccessToast('复制成功!')
    }).catch((err) => {
      console.log('复制失败--采取第二种复制方案', err);
      // clipboard方式复制失败 则采用document.execCommand()方式进行尝试
      copyResult = copyContent2(text)
    })
  } else {
    // 不支持clipboard方式 则采用document.execCommand()方式
    copyResult = copyContent2(text)
  }
  // 返回复制操作的最终结果
  return copyResult;

}
const copyContent2 = (text:string) => {
  // 复制结果
  let copyResult = true
  // 创建一个input元素
  let inputDom = document.createElement('textarea');
  // 设置为只读 防止移动端手机上弹出软键盘  
  inputDom.setAttribute('readonly', 'readonly');
  // 给input元素赋值
  inputDom.value = text;
  // 将创建的input添加到body
  document.body.appendChild(inputDom);
  // 选中input元素的内容
  inputDom.select();
  // 执行浏览器复制命令
  // 复制命令会将当前选中的内容复制到剪切板中（这里就是创建的input标签中的内容）
  // Input要在正常的编辑状态下原生复制方法才会生效
  const result = document.execCommand('copy')
  // 判断是否复制成功
  if (result) {
    showSuccessToast('复制成功!')
  } else {
    showFailToast('复制失败')
    copyResult = false
  }
  // 复制操作后再将构造的标签 移除
  document.body.removeChild(inputDom);
  // 返回复制操作的最终结果
  return copyResult;
}

const getDetail = (id: string) => {
  // detailLoading.value = true
  helperApi.getById(id).then((res: any) => {
    res.acceptObj = res?.acceptRecords.find((item: any) => item.acceptStatus == 20)
    detail.value = res
    // detailLoading.value = false
  }).catch((err: any) => {
    // detailLoading.value = false
  })
}
// 图片预览
const previewImage = (index: number) => {
  showImagePreview({
    images: detail.value?.files?.map(item => item.fileUrl),
    startPosition: index,
  });
}
const feishuUrl = import.meta.env.VITE_FEI_SHU_URL;

// 联系接受人
const contactAcceptor = (item: any) => {
  helperApi.contactAcceptor(item.id).then(res => {
    location.href = feishuUrl + '?openId=' + res
  })
}

// 联系咨询人
const contactService = (item: any) => {
  helperApi.getContactsOpenid(item.id).then(res => {
    location.href = feishuUrl + '?openId=' + res
  })
}

// 取消需求
const cancelAccept = (detail: any) => {
  showConfirmDialog({
    title: '请确认',
    message: '确认取消此带物需求吗?',
  }).then(() => {
    helperApi.cancelAccept(detail.id).then(res => {
      showSuccessToast('取消需求成功')
      router.push({
        path: '/mobile/index',
        query: {
          tabValue: 2
        }
      })
    })
  })

}

// 联系发布人 / 接受需求
// acceptStatus 联系10 接受20  acceptUserCode 当前人工号
const acceptDemand = (item:any, type:number) => {
  const params = {
    piggybackId: item.id,
    acceptStatus: type,
    acceptUserCode: loginUser.value?.username,
    acceptUserName: loginUser.value?.nickName

  }
  if (type == 20) {
    showConfirmDialog({
      title: '确认接受此带物需求吗?',
      message: '请确认已与需求发布人沟通达成一致;接受需求后,如果行程发生变化,请主动与需求发布人沟通',
    }).then(() => {
      acceptRequirement(params, type)
    })
  }else {
    acceptRequirement(params, type)
  }
  
}

// 联系发布人 / 接受需求
const acceptRequirement = (params:any, type:number) => {
  helperApi.acceptRequirement(params).then(res => {
    if (type == 20) {
      showSuccessToast('接受需求成功')
      router.push({
        path: '/mobile/index',
      
      })
    }else {
      location.href = feishuUrl + '?openId=' + res
    }
  })
}
onMounted(() => {
  getDetail(id)
})


</script>

<style lang="less" scoped>
@import url(./helper.less);
</style>