<script lang="ts" setup>
import { computed, createVNode, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';

import {
  Anchor as hAnchor,
  <PERSON><PERSON> as hButton,
  Spin as hSpin,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Checkbox as hCheckbox,
  Form as hForm,
  InputNumber as hInputNumber,
  FormItem as hFormItem,
  Row as hRow,
  Col as hCol,
  Popconfirm as hPopconfirm,
  Upload as hUpload,
  Input as hInput,
  Modal as hModal,
  UploadProps,
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  DownloadOutlined,
  VerticalAlignBottomOutlined,
  ExclamationCircleFilled,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  SendOutlined,
  PhoneOutlined,
  VerticalAlignTopOutlined,
  DeleteOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { download, rechargeApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import relativeTime from 'dayjs/plugin/relativeTime';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';

import { CityResponse, CityItem, TCteateTeam, RRechargeSaveParams } from '@haierbusiness-front/common-libs';
import { fileApi } from '@haierbusiness-front/apis';
import type { TableColumnType } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import Big from 'big.js';
const route = ref(getCurrentRoute());
const router = useRouter();

const id = route.value?.query?.id;

const goRecharge = () => {
  router.go(-1)
};

dayjs.extend(relativeTime);

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const labelCol = { span: 4 };
const wrapperCol = { span: 20 };
const spinning = ref<boolean>(false);

const rechargeForm = ref<RRechargeSaveParams>({
  checked: false,
  budgetAmount: '',
  reason: '',
  fileList: [],
  fileName: '',
  fileUrl: '',
  paymentAccount: '',
  paymentAccountName: '',
  businessFlag: '',
});

const validateTrue = (_: any, value: boolean) =>
  value === true ? Promise.resolve() : Promise.reject(new Error('请勾选注意事项！'));

const rules: Record<string, Rule[]> = {
  budgetAmount: [{ required: true, message: '请输入充值金额', trigger: 'change' }],
  businessFlag: [{ required: true, message: '请选择是否商旅', trigger: 'change' }],
  checked: [{ required: true, validator: validateTrue, trigger: 'change' }],
  fileList: [{ required: true, message: '请上传报告附件信息', trigger: 'change' }],
};

const formRef = ref();

// 提交
const submitLoading = ref(false)
const onSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value=true
      // console.log(111111,rechargeForm.value)
      rechargeApi.addRecharge(rechargeForm.value).then((res) => {
        console.log('🚀 ~ rechargeApi.addRecharge ~ res:', res);
        hMessage.success('提交成功,1秒后自动跳转到支付页面!');
        setTimeout(() => {
          submitLoading.value=false
          window.location.href =res;

        }, 1000);
      });
    })
    .catch((error: any) => {
      submitLoading.value=false
    });
};

const handleInputChange = (value)=> {
      if (value) {
        rechargeForm.value.budgetAmount = Math.floor(new Big(value).times(100)) / 100;
      } else {
        rechargeForm.value.budgetAmount = value;
      }
    }

// 上传相关
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

const uploadLoading = ref(false);
const upload = (options: any) => {
  uploadLoading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);
  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;
      options.file.fileName = options.file.name;
      rechargeForm.value.fileName = options.file.name;
      rechargeForm.value.fileUrl = baseUrl + it.path;

      // options.
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .catch((e) => {
      rechargeForm.value.fileList = [];
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};
const beforeUpload = (file: UploadProps['fileList'][number]) => {
  const validTypes = ['application/pdf']; // 允许的文件类型
  if (!validTypes.includes(file.type)) {
    hMessage.error('只能上传pdf格式文件!');
    rechargeForm.value.fileList = []
    return hUpload.LIST_IGNORE;
  }
  const isLt5M = file.size / 1024 / 1024 < 10;
  if (!isLt5M) {
    hMessage.error('文件大小不能超过10M');
    rechargeForm.value.fileList = []
    return hUpload.LIST_IGNORE;
  }
  return true
};
// 获取账户激励
const getAccountNo = () => {
  rechargeApi.getAndInitTradeUnionAccount().then((res) => {
    console.log(res, '-------------------------------');
    rechargeForm.value.paymentAccount = res.accountNo;
    rechargeForm.value.paymentAccountName = res.accountName;

    rechargeForm.value.businessOption = res.businessOption
  });
};

onMounted(() => {
  getAccountNo();
});
</script>

<template>
  <div class="container">
    <div class="row flex">
      <div class="apply-con flex acenter">
        <h-row justify="center" align="middle" style="padding: 40px; font-size: 20px; font-weight: 600">
          商务云对公充值
        </h-row>
        <h-form
          style="width: 450px"
          ref="formRef"
          :model="rechargeForm"
          :rules="rules"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          labelAlign="left"
        >
          <!-- 充值金额 -->
          <h-form-item name="budgetAmount" label="充值金额">
            <h-input-number
              style="width: 100%"
              :controls="false"
              @change="handleInputChange"
              v-model:value="rechargeForm.budgetAmount"
              addon-after="元"
            />
          </h-form-item>

          <!-- 充值事由 -->
          <h-form-item name="reason" label="充值事由">
            <h-textarea
              v-model:value="rechargeForm.reason"
              placeholder="请输入留言信息"
              :auto-size="{ minRows: 3, maxRows: 5 }"
              allow-clear
              :maxlength="300"
              show-count
            />
          </h-form-item>

          <!-- 是否商旅 -->
          <h-form-item name="businessFlag" v-if="rechargeForm.businessOption == 1" label="是否商旅">
            <h-radio-group v-model:value="rechargeForm.businessFlag">
              <h-radio :value="1">是</h-radio>
              <h-radio :value="0">否</h-radio>
            </h-radio-group>
          </h-form-item>
          
          <!-- 人员明细 -->
          <h-form-item name="fileList" label="上传报告">
            <h-upload
              v-model:file-list="rechargeForm.fileList"
              name="file"
              accept=".pdf"
              list-type="text"
              :max-count="1"
              :custom-request="upload"
              :before-upload="beforeUpload"
            >
              <h-button size="small" v-if="!rechargeForm.fileList || rechargeForm.fileList.length == 0">
                <upload-outlined class="font-size-14"></upload-outlined>
                <span class="font-size-14">上传附件</span>
                
              </h-button>
              <div class="text ">
                  <span style="color: #999;" class="font-size-12">仅支持上传pdf格式文件,文件大小不能超过10M</span>
                </div>
              
            </h-upload>
          </h-form-item>

          <!-- 注意事项 -->
          <h-form-item name="checked" :wrapper-col="{span: 24}">
            <h-checkbox v-model:checked="rechargeForm.checked">
              <span style="color: red">注意事项:订单提交后不可进行修改,请确认上传的订单信息</span>
            </h-checkbox>
          </h-form-item>
          <h-form-item :wrapper-col="{ offset: 10 }">
            <h-button type="primary" :loading="submitLoading" @click="onSubmit">提交</h-button>
            <h-button style="margin-left: 50px" @click="goRecharge">取消</h-button>
          </h-form-item>
        </h-form>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url(./recharge.less);

.change-title {
  position: absolute;
  right: 0;
  top: -80px;
  width: 300px;

  .ant-col-6,
  .ant-col-18 {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #000000d9;
  }
}
.mt-5 {
  margin-top: 5px;
}

.ml-5 {
  margin-left: 5px;
}
.color-eee {
  color: #a8a7a7;
}

.com-box {
  background: #f5f5f5;
  padding: 20px;
  margin-top: 20px;

  .whole-line {
    height: 40px;
    font-weight: 600;
  }
  .info-box {
    display: flex;
    align-items: flex-start;
    padding: 10px 20px;
    border: 1px solid #ffdb5c;
    background: #fffbd8;
  }
  :deep(.ant-form-item-control-input-content) {
    display: flex;
    align-items: center;
  }
}
.title {
  height: 60px;
}
.download-btn {
  color: #408cff;
  cursor: pointer;
}
:deep(.city-chose-box) {
  width: 200px !important;
}
:deep(.mr-10) {
  margin-right: 10px;
}

.background-eee {
  padding: 10px;
  background: #f4f4f4;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
