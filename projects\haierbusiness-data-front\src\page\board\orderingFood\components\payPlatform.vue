<template>
  <div :style="{ height: props.height + 'vh' }">
    <div :id="circleId" :style="{ height: Number(props.height) - 12 + 'vh' }"></div>
    <div class="tips">
      <div class="tips-main">
        <div class="tip" v-for="(row, index) in payTypeDetailRows" :key="row.name">
          <span class="tip-dot" :style="{ borderColor: colors[index] }"></span>
          <span class="tip-percent"
            >{{ ((Number(row.value) / payTypeDetailTotal) * 100).toFixed(0) }}%</span
          >
          <span class="tip-title">{{ row.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { circle, colors } from "../../data";
import * as echarts from "echarts";
import { queryOrderingFoodPayPlatform } from "@haierbusiness-front/apis/src/data/board";
import { EventBus } from "../../eventBus";
const props = defineProps({
  height: Number,
});
const loading = ref(false);
const circleId = ref("circle-" + Date.now());
const payTypeDetailRows = ref(
  [] as Array<{
    name: string;
    value: string | number;
  }>
);
const payTypeDetailTotal = ref(0);
let circleChartDom, circleChart;
const payTypeCheck = ref<string>("");

onMounted(() => {
  queryCircle();

  circleChartDom = document.getElementById(circleId.value);
  circleChart = echarts.init(circleChartDom as any, "dark");

  circleChart.on("click", (param) => {
    if (param.from != "budget_source" && param.name != payTypeCheck.value) {
      payTypeCheck.value = param.name;
      EventBus.emit("refresh", {
        ...param,
        from: "budget_source",
      });
    } else {
      payTypeCheck.value = "";
      EventBus.emit("refresh");
    }
  });
});
EventBus.on((event, params) => {
  if (event == "refresh") {
    //获取缓存中筛选的模块
    if (!params) queryCircle();
    //同组件触发
    if (params && params.from != "budget_source") {
      queryCircle(params);
    }
    if (params && params.from == "budget_source") {
      queryCircle().then(() => {
        circleChart.dispatchAction({
          type: "highlight",
          seriesIndex: 0,
          dataIndex: params.dataIndex,
        });
      });
    }
  }
});
const queryCircle = async (params?: { data: { name: string }; from: string }) => {
  loading.value = true;
  const data = await queryOrderingFoodPayPlatform(
    params ? params.data.name : null,
    params ? params.from : null
  );
  loading.value = false;
  const payTypeDetailData = [] as Array<{
    name: string;
    value: string | number;
  }>;
  let total = 0;
  data.rows.forEach((item) => {
    total += item[1];
    payTypeDetailData.push({
      name: item[0],
      value: item[1],
    });
  });
  payTypeDetailTotal.value = total;
  payTypeDetailRows.value = payTypeDetailData;
  const { series } = circle;
  series[0].color = colors;
  series[0].data = payTypeDetailData;
  series[0].radius = ["50%", "80%"];
  circleChart.clear();
  circleChart.setOption(circle);
};
</script>
<style scoped lang="less">
.tips {
  display: flex;
  justify-content: center;
}

.tips-main {
  display: flex;
  flex-wrap: wrap;
  width: 260px;
}

.tip {
  width: 130px;

  &-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    border: 3px solid #ffd700;
    border-radius: 50%;
  }

  &-percent {
    font-size: 20px;
    margin: 0 5px 0 7px;
  }

  &-title {
    font-size: 12px;
  }
}

@media screen and (max-width: 1500px) {
  .tips-main {
    width: 200px;
  }

  .tip {
    width: 100px;

    &-dot {
      display: inline-block;
      width: 6px;
      height: 6px;
      border: 2px solid #ffd700;
      border-radius: 50%;
    }

    &-percent {
      font-size: 14px;
      margin: 0 3px 0 5px;
    }

    &-title {
      font-size: 10px;
    }
  }
}
</style>
