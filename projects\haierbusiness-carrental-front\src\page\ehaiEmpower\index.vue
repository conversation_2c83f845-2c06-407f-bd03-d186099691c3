<template>
    <div v-if="!isPower||isBase!=base" class="box">
        <div class="contentBox">
            <div class="topTitle">
                <p class="content">
                    您同意我要租车获取以下权限
                </p>
                <p class="contentList">
                   <li>获取您的注册信息用于一嗨业务</li> 
                </p>
            </div>
        </div>
        <div class="logoBox">   
            <div class="leftLogo">
              <img :src="woyaozuche" alt="">
            </div>
            <div>
                <van-icon size="2em" color="#1677ff" name="exchange" />
            </div>
            <div class="rightLogo">
              <img :src="ehaiLogo" alt="">
            </div>
        </div>
        <div class="sqBox">   
            确认授权视为同意<a @click="toPlatformService">《平台服务协议》</a><a @click="toPrivacyAgreement">《隐私政策》</a>
        </div>
        <div class="btnBox">
            <van-button @click="getCode" block round color="#1677ff">确认授权</van-button>
            <!-- <p @click="goBack">
                暂不授权
            </p> -->
        </div>
    </div>
</template>
  
<script lang="ts" setup>
  import { onMounted, ref, watch } from 'vue';
  import type { Ref } from 'vue';
  
  import { ehaiApi } from '@haierbusiness-front/apis';
  
  import { storeToRefs } from 'pinia';
  import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
  import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
  import { Item } from 'ant-design-vue/es/menu';
  import { showSuccessToast, showFailToast,showLoadingToast } from 'vant';
  import dayjs from 'dayjs';
  import ehaiLogo from '../../assets/image/ehaiLogo.jpg';
  import woyaozuche from '../../assets/image/woyaozuche.png';

  const store = applicationStore();
  const { loginUser } = storeToRefs(store);
  const appId = import.meta.env.VITE_EHAI_APPID;
  const appSecret = import.meta.env.VITE_EHAI_APPSECRET;
  const ehaiUrl = import.meta.env.VITE_EHAI_URL;
  const base = import.meta.env.VITE_BASE;

  const showError=(error)=>{
    console.log(error)
  }
  const isPower = localStorage.getItem('isPower')?true:false
  const isBase =localStorage.getItem('isBase')?localStorage.getItem('isBase'):'0.0.0'
  const getCode = () => {
    // if(!loginUser._object.loginUser.phone){
    //     showFailToast('未获取到手机号')
    //     return
    // }
    const toast =  showLoadingToast({
        message: '授权中...',
        forbidClick: true,
        duration:0
        });
    const params = {
        // appId:appId,
        // appSecret:appSecret,
        // openId:loginUser._object.loginUser.phone,
        // IdType:"1"
    };
    ehaiApi.getcode(params,null,showError).then((res) => {
        if(res.Errcode==0){
            window.location.replace(`${ehaiUrl}MediaPlatform/Hybrid/FederatedLogin?appId=${appId}&preAuthCode=${res.PreAuthCode}&extraData=extraData`);
            localStorage.setItem('isPower',true)
            localStorage.setItem('isBase',base)
            // toast.close()
        }else{
            showFailToast(res.Errmsg)
            toast.close()
        }
    })
    .catch(error=>{
        console.log(error)
        toast.close()
    })
  };

  const goBack = () =>{
    window.history.back()
  }

  const toPrivacyAgreement= ()=>{
    window.open('https://businessmanagement.haier.net/hbweb/file/download/obs-swszh2/1727426344-%E7%A7%9F%E8%BD%A6%E9%9A%90%E7%A7%81%E5%8D%8F%E8%AE%AE.pdf','_self')
  }
  const toPlatformService= ()=>{
    window.open('https://businessmanagement.haier.net/hbweb/file/download/obs-swszh2/1728455970-%E5%B9%B3%E5%8F%B0%E6%9C%8D%E5%8A%A1%E5%8D%8F%E8%AE%AE.pdf','_self')
  }
  onMounted(()=>{
    console.log(isPower)
    if(isPower&&isBase==base){
        getCode()
    }
    })
  </script>
  
  <style lang="less" scoped>
  @import url(./common.less);
  
  .box {
    padding:10px;
    height: 100vh;
    width:100%;
    background-image: linear-gradient(to bottom,#1677ff 18%,#ffffff 22%);
  }
  .contentBox{
    letter-spacing: 1px;
    .topTitle{
        color:#fff;
        padding:10px;
        .content{
            font-size: 12px;
        }
        .contentList{
            margin-top:5px;
        }
    }
  }
  .logoBox{
    width:100%;
    height: 100px;
    background: #fff;
    border-radius: 8px;
    margin-top:16px;
    box-shadow: 0px 4px 4px 0px rgba(157,178,232,0.1);
    display: flex;
    justify-content:space-around;
    align-items: center;
    padding:0 28px;
    .leftLogo{
        width:40px;
        height: 40px;
        border-radius: 5px;
        img{
          width:100%;
          height: 100%;
          border-radius: 5px;
        }
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        // border:1px solid #c6c6c6;
    }
    .rightLogo{
        width:40px;
        height: 40px;
        border-radius: 5px;
        img{
          width:100%;
          height: 100%;
          border-radius: 5px;
        }
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
  }
  .sqBox{
    padding:6px;
    margin-top:20px;
    font-size: 10px;
    color: #504f4f;
    a{
        color: #1677ff;
    }
  }
  .btnBox{
    padding: 25px 6px;
    p{
        text-align: center;
        color: #1677ff;
        cursor: pointer;
        margin-top:15px;
    }
  }
  :deep(.large-value) {
    min-width: 70%;
  }
  
  :deep(.van-cell-group--inset) {
    box-shadow: 0px 8px 8px 0px rgba(157,178,232,0.1);
  }
  </style>