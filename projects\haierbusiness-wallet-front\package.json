{"name": "haierbusiness-wallet-front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --open --mode development", "build-dev": "vite build --mode development", "build-test": "vite build --mode test", "build": "vite build --mode production", "preview": "vite preview"}, "dependencies": {"@haierbusiness-front/apis": "workspace:^0.0.0", "@haierbusiness-front/common-libs": "workspace:^0.0.0", "@haierbusiness-front/utils": "workspace:^0.0.0", "@types/qs": "^6.9.7", "@vant/auto-import-resolver": "^1.0.2", "ant-design-vue": "4.x", "axios": "^1.3.4", "dayjs": "1.11.9", "pinia": "^2.0.33", "postcss-px-to-viewport": "^1.1.1", "qrcode": "^1.5.3", "qrcodejs2": "^0.0.2", "qrcodejs2-fix": "^0.0.1", "qs": "^6.11.0", "sass": "^1.69.5", "unplugin-vue-components": "^0.26.0", "vant": "^4.2.0", "vue": "^3.3.4", "vue-request": "2.0.0-rc.4", "vue-router": "^4.1.6", "vue3-barcode": "^1.0.1"}, "devDependencies": {"@types/node": "^18.13.0", "@types/nprogress": "^0.2.0", "@typescript-eslint/eslint-plugin": "^4.19.0", "@typescript-eslint/parser": "^4.19.0", "@vitejs/plugin-vue": "^4.0.0", "less": "^4.1.3", "nprogress": "^0.2.0", "typescript": "^4.9.3", "vite": "^4.1.0", "vue-tsc": "^1.0.24"}}