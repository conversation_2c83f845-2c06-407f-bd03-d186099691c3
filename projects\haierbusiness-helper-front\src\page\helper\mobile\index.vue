<template>
  <div>
    <div class="container">
      <div class="banner-img"></div>
      <!-- 筛选 -->
      <van-radio-group v-model="tabValue">
        <van-row justify="space-between" class="mt-10 mb-10">
          <van-radio :name="1" class="my-radio">
            <template #icon="{ checked }">
              <van-button class="btn-com" :class="checked? 'active':''" size="small" :plain="checked" round
                :type="checked ? 'primary' : 'default'">带物需求</van-button>
            </template>
          </van-radio>
          <van-radio :name="2" class="my-radio">
            <template #icon="{ checked }">
              <van-button class="btn-com" :class="checked? 'active':''" size="small" :plain="checked" round
                :type="checked ? 'primary' : 'default'">我发布的</van-button>
            </template>
          </van-radio>
          <van-radio :name="3" class="my-radio">
            <template #icon="{ checked }">
              <van-button class="btn-com" :class="checked? 'active':''" size="small" :plain="checked" round
                :type="checked ? 'primary' : 'default'">我接受的</van-button>
            </template>
          </van-radio>
        </van-row>
      </van-radio-group>

      <!-- 行程 -->
      <div v-if="tabValue == 1" class="plan-list">
        <van-row justify="space-around" style="background-color: #fff; padding: 10px"
          v-for="(trip, index) in userTicketsList" :key="index">
          <!-- 出发城市 -->
          <div class="trip-item">
            <div class="item-address">
              {{ trip?.jpCfcityMc || trip?.cfcsmc }}
            </div>
            <div class="item-time">
              {{ trip?.jpCfsj ? dayjs(trip.jpCfsj).format(' M月D日 HH:mm') : dayjs(trip.cfrq).format(' M月D日 HH:mm') }}
            </div>
          </div>
          <!-- 图标 -->
          <SwapRightOutlined class="city-to" style="font-size: 24px" />
          <!-- 到达城市 -->
          <div class="trip-item">
            <div class="item-address">
              {{ trip?.jpDdcityMc || trip?.ddcsmc }}
            </div>
            <div class="item-time">
              {{ trip?.jpDdsj ? dayjs(trip.jpDdsj).format(' M月D日 HH:mm') : dayjs(trip.ddrq).format(' M月D日 HH:mm') }}
            </div>
          </div>
        </van-row>
      </div>

      <!-- 条件查询 -->
      <div class="mt-10 my-tab mb-10" v-if="tabValue == 1">
        <!-- 匹配 -->
        <div v-if="userTicketsList && userTicketsList.length > 0" class="tab-item"
          :class="searchKey.onlyMatchFlag == 1 ? 'active' : ''" @click="showPpPicker = true">
          {{searchKey.onlyMatchFlag == 1 ?'只看匹配' :'全部需求'}}
          <CaretDownOutlined />
        </div>
        <!-- 出发城市 -->
        <div class=" tab-item" :class="searchKey.fromCityName ? 'active' : ''"
          @click.stop="showCityPop('begin', searchKey.fromCityCode)">
          {{ searchKey.fromCityName || '出发城市' }}
          <CaretDownOutlined />
        </div>
        <div class=" tab-item">
          <SwapRightOutlined style="font-size: 15px" />
        </div>
        <!-- 送达城市 -->
        <div class=" tab-item" :class="searchKey.destCityName ? 'active' : ''"
          @click.stop="showCityPop('end', searchKey.destCityCode)">
          {{ searchKey.destCityName || '送达城市' }}
          <CaretDownOutlined />
        </div>

        <div class=" tab-item" :class="searchKey.expectTimeFrom ? 'active' : ''" @click="openTimerPicker('begin')">
          {{ searchKey.expectTimeFrom ? dayjs(searchKey.expectTimeFrom).format('M月D日') : '开始日期' }}
          <CaretDownOutlined />
        </div>
        <div class=" tab-item">
          <SwapRightOutlined style="font-size: 15px" />
        </div>

        <!-- 送达时间 -->
        <div class=" tab-item" :class="searchKey.expectTimeTo ? 'active' : ''" @click="openTimerPicker('end')">
          {{ searchKey.expectTimeTo ? dayjs(searchKey.expectTimeTo).format('M月D日') : '结束日期' }}
          <CaretDownOutlined />
        </div>

        <!-- 物品类型 -->
        <div class=" tab-item" :class="searchKey.objectType ? 'active' : ''" @click="showWpPicker = true">
          {{searchKey.objectType ? IHelperThingsTypeEnum[searchKey.objectType] : '物品类型'}}
          <CaretDownOutlined />
        </div>

      </div>

      <!-- 排序 -->
      <div class=" order-box" v-else>
        <div @click="setDemandStatus">
          <div class="order-btn-box">
            <span class="order-btn-text" :class="{ 'active': demandStatus != 0 }">需求状态</span>
            <span class="order-btn">
              <span :class="{ 'active': demandStatus == 1 }" class="order-btn-top"></span>
              <span :class="{ 'active': demandStatus == 2 }" class="order-btn-bottom"></span>
            </span>
          </div>
        </div>
        <div @click="setDeliveryTimeStatus">
          <div class="order-btn-box">
            <span class="order-btn-text" :class="{ 'active': deliveryTimeStatus != 0 }">送达时间</span>
            <span class="order-btn">
              <span :class="{ 'active': deliveryTimeStatus == 1 }" class="order-btn-top"></span>
              <span :class="{ 'active': deliveryTimeStatus == 2 }" class="order-btn-bottom"></span>
            </span>
          </div>
        </div>
        <div @click="setCreateTimeStatus">
          <div class="order-btn-box">
            <span class="order-btn-text" :class="{ 'active': createTimeStatus != 0 }">发布时间</span>
            <span class="order-btn">
              <span :class="{ 'active': createTimeStatus == 1 }" class="order-btn-top"></span>
              <span :class="{ 'active': createTimeStatus == 2 }" class="order-btn-bottom"></span>
            </span>
          </div>
        </div>
      </div>

      <!-- 列表 -->
      <van-loading class="flex-center" style="min-height: 500px; justify-content: center"
        v-if="listLoading"></van-loading>
      <template v-else>
        <div class="list-box" v-if="helperList && helperList.length > 0">
          <!-- <div class="list-box" v-if="false"> -->
          <!-- 列表数据 -->
          <div class="list-item" v-for="(item, index) in helperList" :key="index" @click="goToDetail(item)">
            <div class="list-item-text" v-if="tabValue != 1"
              :style="{color: IHelperStateTagColorMap[item.piggybackStatus]}">
              {{IHelperStatusEnum[item.piggybackStatus]}}</div>
            <div class="mb-10">
              <van-tag class="mr-10" :color="IHelperThingsTypeTagColorMap[item.objectType]" :bordered="false">{{
                IHelperThingsTypeEnum[item.objectType] }}</van-tag>
              <span class="list-item-title">期望送达:{{ dayjs(item.expectTimeFrom).format(' M月D日 HH:mm') }} - {{
                dayjs(item.expectTimeTo).format('M月D日 HH:mm')
                }}</span>
            </div>
            <div class="mb-10">
              <span class="city-font">{{ item.fromCityName }}</span>
              <SwapRightOutlined class="city-to" style="font-size: 20px" />
              <span class="city-font">{{ item.destCityName }}</span>
            </div>
            <div class="list-item-btns" :style="tabValue != 1 ? {'min-height': '25px'} : ''">

              <template v-if="tabValue == 1 && item.createUser != loginUser?.username">
                <van-button @click.stop="acceptDemand(item, 10)" type="primary" class="list-btn-big" size="mini">
                  <div class="flex">
                    <span class="icon-chart mr-5"></span>
                    <span>联系发布人</span>
                  </div>
                </van-button>
                <van-button type="primary" size="mini" @click.stop="acceptDemand(item,20)">接受需求</van-button>
              </template>

              <template v-if="tabValue == 2">
                <van-badge dot v-if="item.piggybackStatus == 10 && (item?.piggybackAcceptRecords?.length > 0)">
                  <van-button type="primary" class="list-btn-big" size="mini">
                    <div class="flex">
                      <span class="icon-chart mr-5"></span>
                      <span>已有{{ item?.piggybackAcceptRecords?.length || 0 }}人咨询</span>
                    </div>
                  </van-button>
                </van-badge>
                <van-button @click.stop="contactAcceptor(item)" v-if="item.piggybackStatus == 20" type="primary"
                  class="list-btn-big" size="mini">
                  <div class="flex">
                    <span class="icon-chart mr-5"></span>
                    <span>联系接受人</span>
                  </div>
                </van-button>

                <template v-if="item.piggybackStatus == 30 || item.piggybackStatus == 40">
                  <van-button type="primary" @click.stop="deleteAccept(item)" class="list-btn-big"
                    size="mini">删除</van-button>
                  <van-button @click.stop="copyAccept(item)" type="primary" class="list-btn-big"
                    size="mini">再次发布</van-button>
                </template>
              </template>



              <template v-if="tabValue == 3">
                <van-button v-if="item.piggybackStatus == 30" @click.stop="deleteRecord(item)" type="primary"
                  class="list-btn-big" size="mini">删除</van-button>
                <van-button v-else @click.stop="acceptDemand(item, 10)" type="primary" class="list-btn-big" size="mini">
                  <div class="flex">
                    <span class="icon-chart mr-5"></span>
                    <span>联系发布人</span>
                  </div>
                </van-button>
              </template>

            </div>
          </div>
        </div>
        <!-- 分页 -->
        <van-pagination v-if="helperList && helperList.length > 0" v-model="searchKey.pageNum"
          :items-per-page="searchKey.pageSize" @change="getList" :total-items="searchKey.total" mode="simple" />
        <!-- 空 -->
        <div class="list-empty" style="padding: 20px" v-else>
          <div class="empty">
            <div style="color: #595959;font-weight: bold;  font-size: 16px;margin-bottom:20px;text-align: center;"
              justify="center">暂未匹配到带物需求
            </div>
            <div class="mb-10" style="color: #8C8C8C; font-size: 14px; text-align: center;" justify="center">
              您可直接发布带物需求
            </div>
            <div style="color: #8C8C8C;text-align: center;" justify="center">
              平台会为您快速匹配顺路创客
            </div>
          </div>
        </div>
      </template>




      <!-- 时间选择 -->
      <van-popup v-model:show="showTimePicker" position="bottom">
        <van-date-picker title="选择日期" v-model="currentDate" @confirm="confirmTime" @cancel="cancelTime"
          :min-date="minDate" :max-date="maxDate" cancel-button-text="清空" />
      </van-popup>

      <!-- 城市选择 -->
      <van-popup v-model:show="showCityPicker" style="height:60vh;overflow:hidden;" position="bottom">
        <van-sticky :offset-top="0">
          <van-nav-bar title="城市选择可搜索">
            <template #right>
              <van-icon @click="closeCityChosePop" name="cross" size="18" />
            </template>
          </van-nav-bar>
          <van-search autocomplete="off" :show-action="searchCityList.length > 0" @cancel="onCityClear"
            @blur="onCitySearch" @clear="onCityClear" v-model="cityName" placeholder="请输入城市" />
        </van-sticky>

        <van-list style="height: calc(60vh - 100px); overflow-y: scroll;" :offset="100" :immediate-check="false"
          v-if="searchCityList.length > 0" v-model:loading="cityLoading" :finished="cityFinished" finished-text="没有更多了"
          @load="onLoadCity">
          <van-cell @click="chosedCityItem(item)" v-for="item in searchCityList" :key="item" :title="item.name"
            :value="`${item.continentsName}/${item.countryName}/${item.provinceName}`" />
        </van-list>

        <van-cascader v-else v-model="chosedCity" :show-header="false" :options="cityDict"
          :field-names="{ text: 'name', value: 'id', children: 'children' }" @finish="finishCityChose"
          @change="onChange" />

      </van-popup>

      <!-- 匹配需求 -->
      <van-popup v-model:show="showPpPicker" position="bottom">
        <van-picker title="只看匹配" :columns="ppColumns" @confirm="onPpConfirm" @cancel="onPpCancel"
          @change="onPpChange" />
      </van-popup>

      <!-- 物品类型 -->
      <van-popup v-model:show="showWpPicker" position="bottom">
        <van-picker title="选择物品类型" :columns="thingsList" @confirm="onWpConfirm" @cancel="onWpCancel"
          @change="onWpChange" />
      </van-popup>


    </div>
    <!-- 新增按钮 -->
    <van-sticky position="bottom" :offset-bottom="0">
      <div class="addbtn-box" v-if="tabValue == 1">
        <van-button class="add-btn" @click="goToAdd" type="primary">发布需求</van-button>
      </div>
    </van-sticky>
  </div>
</template>

<script lang="ts" setup>
import { computed, createVNode, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { IHelperThingsTypeEnum, ICity, IHelperThingsTypeTagColorMap, IHelperWeightTypeEnum, UserTicketResponseDTO, UserFlightTicketDTO, IHelperStatusEnum, IHelperStateTagColorMap, IHelperSearchParam, IHelperListRes, IHelperReq } from '@haierbusiness-front/common-libs';
import { helperApi } from '@haierbusiness-front/apis';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getEnumOptions } from '@haierbusiness-front/utils';
import { cityApi, tripApi } from '@haierbusiness-front/apis';
import { showDialog,showSuccessToast,showConfirmDialog, showFailToast, showToast } from 'vant';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';

import {
  SwapRightOutlined,
  CaretDownOutlined
} from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
const router = getCurrentRouter()

const store = applicationStore();
const { loginUser } = storeToRefs(store);
const route = ref(getCurrentRoute());

const searchKey = ref<IHelperSearchParam>({
  pageNum: 1,
  pageSize: 5,
  total: 0,
  piggybackStatus: 10,
})

// 获取用户机票信息
const userTicketsList = ref<UserFlightTicketDTO>([])
const userTickets = () => {
  helperApi.userTickets().then((res: UserTicketResponseDTO) => {
    userTicketsList.value = [...res.userFlightTicketDTOList, ...res.userTrainTicketDTOList]
    // userTicketsList.value = userTicketsList.value.slice(0,2)
  })
}

// 匹配信息
const showPpPicker = ref(false)
const ppColumns = [
  { text: '全部需求', value: 0 },
  { text: '只看匹配', value: 1 },
];

const onPpConfirm = ({ selectedValues }) => {
  searchKey.value.onlyMatchFlag = selectedValues[0]
  showPpPicker.value = false
  getList(1)
}

const onPpCancel = () => {
  searchKey.value.onlyMatchFlag = 0
  showPpPicker.value = false
  getList(1)
};

const onPpChange = () => {

}

// 联系发布人 / 接受需求
// acceptStatus 联系10 接受20  acceptUserCode 当前人工号
const acceptDemand = (item:any, type:number) => {
  const params = {
    piggybackId: item.id,
    acceptStatus: type,
    acceptUserCode: loginUser.value?.username,
    acceptUserName: loginUser.value?.nickName

  }
  if (type == 20) {
    showConfirmDialog({
      title: '确认接受此带物需求吗?',
      message: '请确认已与需求发布人沟通达成一致;接受需求后,如果行程发生变化,请主动与需求发布人沟通',
    }).then(() => {
      acceptRequirement(params, type)
    })
  }else {
    acceptRequirement(params, type)
  }
  
}
const feishuUrl = import.meta.env.VITE_FEI_SHU_URL;

// 联系发布人 / 接受需求
const acceptRequirement = (params:any, type:number) => {
  helperApi.acceptRequirement(params).then(res => {
    if (type == 20) {
      getList()
      showSuccessToast('接受需求成功')
      // detailVisible.value = false
    }else {
      location.href = feishuUrl + '?openId=' + res
    }
  })
}

// 联系接受人
const contactAcceptor = (item:any) => {
  helperApi.contactAcceptor(item.id).then(res => {
    location.href = feishuUrl + '?openId=' + res
  })
}

// 再次发布
const copyAccept = (item: any) => {
  router.push({
    path:'/mobile/add',
    query: {
      id: item.id
    }
  })
}


// 跳转详情页面
const goToDetail = (item:any) => {
  router.push({
    path:'/mobile/detail',
    query: {
      id: item.id,
      tabValue: tabValue.value
    }
  })
}

const goToAdd = () => {
  router.push({
    path:'/mobile/add',
  })
}

// 删除一条已取消的沟通记录
const deleteRecord = (item:any) => {

  showConfirmDialog({
    title: '提示',
    message:
      '确定要删除这条沟通记录吗?',
  })
  .then(() => {
    const params = {
      piggybackId: item.id,
      acceptUserCode: loginUser.value?.username
    }
    helperApi.deleteRecord(params).then(res => {
      showSuccessToast('删除成功')
      getList()
    })
  })
  .catch(() => {
    // on cancel
  });
  
}

const deleteAccept = (detail:any) => {
  showConfirmDialog({
    title: '提示',
    message:
      '确定要删除这条需求吗?',
  })
  .then(() => {
    helperApi.deleteById(detail.id).then(res => {
      showSuccessToast('删除需求成功')
      getList()
    })
  })
  .catch(() => {
    // on cancel
  });
  
}

// 排序
// 0 默认 1 asc 2 desc
const demandStatus = ref(0)
const deliveryTimeStatus = ref(0)
const createTimeStatus = ref(0)
const setDemandStatus = () => {
  if (demandStatus.value != 2) {
    demandStatus.value++
  } else {
    demandStatus.value = 0
  }
  handleStatusChange()
}

const setDeliveryTimeStatus = () => {
  if (deliveryTimeStatus.value != 2) {
    deliveryTimeStatus.value++
  } else {
    deliveryTimeStatus.value = 0
  }
  handleStatusChange()
}

const setCreateTimeStatus = () => {
  if (createTimeStatus.value != 2) {
    createTimeStatus.value++
  } else {
    createTimeStatus.value = 0
  }
  handleStatusChange()
}
const setOrderList = () => {
  const orderByItem = [
    {
      fieldName: 'piggybackStatus',
      order: demandStatus.value == 0 ? '':demandStatus.value == 1?'asc': 'desc'
    },
    {
      fieldName: 'expectTimeTo',
      order: deliveryTimeStatus.value == 0 ? '':deliveryTimeStatus.value == 1?'asc': 'desc'
    },
    {
      fieldName: 'createTime',
      order: createTimeStatus.value == 0 ? '' :createTimeStatus.value == 1?'asc': 'desc'
    }
  ]
  searchKey.value.orderByItemList = orderByItem
}

// 改变排序
const handleStatusChange = () => {
  setOrderList()
  getList(1)
}
// watch(demandStatus, handleStatusChange)
// watch(deliveryTimeStatus, handleStatusChange)
// watch(createTimeStatus, handleStatusChange)


// 物品选择
const showWpPicker = ref(false)
// 物品类型
const thingsList = computed(() => {
  let list = getEnumOptions(IHelperThingsTypeEnum, true)
  list.forEach(item => {
    item.text = item.label
  })
  return list
});

const onWpConfirm = ({ selectedValues }) => {
  searchKey.value.objectType = selectedValues[0]
  showWpPicker.value = false
  getList(1)
}

const onWpCancel = () => {
  searchKey.value.objectType = ''
  showWpPicker.value = false
  getList(1)
};

const onWpChange = () => {

}

// 城市选择
const showCityPicker = ref<boolean>(false);
const cityDict = ref<Array<ICity>>([])

// 城市选择相关
const cityName = ref('')
const cityLoading = ref<boolean>(false)
const cityFinished = ref<boolean>(false)
const internationalFlag = ref<number>(0)

const cityPageNum = ref<number>(1)
const cityPageSize = ref<number>(10)
const searchCityList = ref([])

const onLoadCity = () => {
  const params = {
    name: cityName.value,
    pageSize: cityPageSize.value,
    pageNum: cityPageNum.value,
    level: 'city',
    internationalFlag: internationalFlag.value
  }
  cityApi.getCityList(params).then(res => {
    searchCityList.value = [...searchCityList.value, ...res.records]
    cityLoading.value = false;
    if (searchCityList.value.length >= res.total) {
      cityFinished.value = true
    } else {
      cityFinished.value = false
      cityPageNum.value++
    }
  })
}

const closeCityChosePop = () => {
  if (cityChoseType.value == 'begin') {
    searchKey.value.fromCityCode = ''
    searchKey.value.fromCityName = ''
  } else {
    searchKey.value.destCityCode = ''
    searchKey.value.destCityName = ''
  }
  showCityPicker.value = false;
  cityName.value = ''
  getList(1)
  onCityClear()
}
const onCitySearch = (val: string) => {
  searchCityList.value = []
  cityPageNum.value = 1
  onLoadCity()
}
const onCityClear = () => {
  cityPageNum.value = 1
  searchCityList.value = []
}
const chosedCity = ref();
const cityChoseType = ref<string>('');
const showCityPop = (type: string, code: string | number) => {
  chosedCity.value = Number(code);

  cityChoseType.value = type;
  showCityPicker.value = true;
};

const chosedCityItem = (item) => {
  if (cityChoseType.value == 'begin') {
    searchKey.value.fromCityCode = item.id;
    searchKey.value.fromCityName = item.name;
  } else {
    searchKey.value.destCityCode = item.id;
    searchKey.value.destCityName = item.name;
  }
  onCityClear()
  cityName.value = ''
  showCityPicker.value = false
  getList(1)
}

const finishCityChose = ({ selectedOptions }: any) => {
  console.log('1111', selectedOptions);

  if (cityChoseType.value == 'begin') {
    searchKey.value.fromCityCode = selectedOptions[selectedOptions.length - 1].id;
    searchKey.value.fromCityName = selectedOptions[selectedOptions.length - 1].name;
  } else {
    searchKey.value.destCityCode = selectedOptions[selectedOptions.length - 1].id;
    searchKey.value.destCityName = selectedOptions[selectedOptions.length - 1].name;
  }

  showCityPicker.value = false;
  getList(1)
};


const reduceYear = () => {
  // 创建一个新的Date对象，表示当前日期和时间
  const currentDate = new Date();

  currentDate.setFullYear(currentDate.getFullYear() - 1);

  return currentDate
}

const addYear = () => {
  // 创建一个新的Date对象，表示当前日期和时间
  const currentDate = new Date();

  currentDate.setFullYear(currentDate.getFullYear() + 1);

  return currentDate
}


// 时间选择
const showTimePicker = ref<boolean>(false);
const currentDate = ref<Array<string>>([]);
const minDate = ref();
const maxDate = ref()

minDate.value = reduceYear()
maxDate.value = addYear()

const choseTimeType = ref('begin')

const openTimerPicker = (type: string) => {
  choseTimeType.value = type

  currentDate.value = [];
  if (searchKey.value.expectTimeFrom) {
    const minDateArr = searchKey.value.expectTimeFrom.split('-');
    minDate.value = new Date(parseInt(minDateArr[0]), parseInt(minDateArr[1]) - 1, parseInt(minDateArr[2]));
  }
  if (searchKey.value.expectTimeTo) {
    const maxDateArr = searchKey.value.expectTimeTo.split('-');
    maxDate.value = new Date(parseInt(maxDateArr[0]), parseInt(maxDateArr[1]) - 1, parseInt(maxDateArr[2]));
  }

  if (type == 'begin') {
    minDate.value = reduceYear();
    if (searchKey.value.expectTimeFrom) {
      currentDate.value = searchKey.value.expectTimeFrom?.split('-');
    }
  } else {
    maxDate.value = addYear()
    if (searchKey.value.expectTimeTo) {
      currentDate.value = searchKey.value.expectTimeTo?.split('-');
    }
  }

  if (currentDate.value.length <1) {
    currentDate.value = dayjs(new Date()).format('YYYY-MM-DD').split('-');

  }

  showTimePicker.value = true;
}
const confirmTime = ({ selectedValues }: any) => {
  if (choseTimeType.value == 'begin') {
    searchKey.value.expectTimeFrom = selectedValues.join('-');
  } else {
    searchKey.value.expectTimeTo = selectedValues.join('-');
  }
  showTimePicker.value = false;
  getList(1)
};

const cancelTime = () => {
  if (choseTimeType.value == 'begin') {
    searchKey.value.expectTimeFrom = ''
  } else {
    searchKey.value.expectTimeTo = ''
  }
  showTimePicker.value = false;
  getList(1)
}



// 查询列表
const helperList = ref<Array<IHelperListRes>>([])
const listLoading = ref<boolean>(false)
const getList = (page?: number) => {
  if (page) {
    searchKey.value.pageNum = page
  }
  listLoading.value = true
  helperApi.page(searchKey.value).then((res: any) => {
    helperList.value = res.records
    searchKey.value.total = res.total
    listLoading.value = false
  }).catch(err => {
    listLoading.value = false
  })
}

// 是否只看匹配
const checkedVal = ref<boolean>(false)
watch(
  checkedVal,
  (newVal, oldVal) => {
    searchKey.value.onlyMatchFlag = newVal ? 1 : 0
    getList(1)
  }
)


const tabValue = ref(1)

watch(
  tabValue,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      searchKey.value = {
        pageNum: 1,
        pageSize: 5,
        total: 0,
        piggybackStatus: '',

      }
      switch (newVal) {
        case 1:
          searchKey.value.createUser = ''
          searchKey.value.acceptUser = ''
          searchKey.value.piggybackStatus = 10
          demandStatus.value = 0
          deliveryTimeStatus.value = 0
          createTimeStatus.value = 0
          break;
        case 2:
          searchKey.value.createUser = loginUser.value?.username
          searchKey.value.acceptUser = ''
          searchKey.value.piggybackStatus = ''
          demandStatus.value = 0
          deliveryTimeStatus.value = 0
          createTimeStatus.value = 0
          break;
        case 3:
          searchKey.value.createUser = ''
          searchKey.value.acceptUser = loginUser.value?.username
          searchKey.value.piggybackStatus = ''
          demandStatus.value = 0
          deliveryTimeStatus.value = 0
          createTimeStatus.value = 0
          break;

        default:
          break;
      }
      if (checkedVal.value == true) {
        checkedVal.value = false
      } else {
        getList(1)

      }
    }
  }
)

const getCityList = () => {
  tripApi.district().then(res => {
    cityDict.value = res.children;
  })
}

const tabValueProp = route.value?.query?.tabValue;

onMounted(() => {
  // 获取省市
  getCityList()
  userTickets()
  if (tabValueProp) {
    tabValue.value = Number(tabValueProp)
  }
  getList(1)
});
</script>

<style lang="less" scoped>
@import url(./helper.less);
</style>