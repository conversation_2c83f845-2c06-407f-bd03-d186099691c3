
type keys = 'CANCEL' | 'APPROVAL' | 'COMPLETE'  | 'REJECT' | 'REVOKE';

/**
 * 审批状态枚举 
 * 0：取消 10：审批中 20：审批通过 30：审批驳回 40:审批撤回
 */
export const ProcessStateConstant = {
  CANCEL: { "type": 0, "name": "取消" },
  APPROVAL: { "type": 10, "name": "审批中" },
  COMPLETE: { "type": 20, "name": "审批通过" },
  REJECT: { "type": 30, "name": "审批驳回" },
  REVOKE: { "type": 40, "name": "审批撤回" },

  ofType: (type?: number): { "type": number, "name": string } | null => {
    for (const key in ProcessStateConstant) {
      const item = ProcessStateConstant[key as keys];
      if (type === item.type) {
        return item;
      }
    }
    return null;
  }
}