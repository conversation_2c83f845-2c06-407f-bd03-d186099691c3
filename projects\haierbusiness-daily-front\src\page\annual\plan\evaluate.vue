<script setup lang="ts">
import { UploadOutlined, PlusOutlined, MinusCircleOutlined } from '@ant-design/icons-vue';
import { fileApi } from '@haierbusiness-front/apis';
import { IAnnualPlanSaveOrUpdateRequestDTO, IUserInfo } from '@haierbusiness-front/common-libs';
import { IEvaluateSaveDetailsRequestDTO, IEvaluateSaveRequestDTO } from '@haierbusiness-front/common-libs/src/daily';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';
import type { UploadProps } from 'ant-design-vue';
import {
  Button as hButton,
  Col as hCol,
  Select as hSelect,
  SelectOption as hSelectOption,
  Collapse as hCollapse,
  CollapsePanel as hCollapsePanel,
  FormItem as hFormItem,
  Row as hRow,
  Textarea as hTextarea,
  Input as hInput,
  InputNumber as hInputNumber,
  Upload as hUpload,
} from 'ant-design-vue';
import { PropType, Ref, inject, ref, watch } from 'vue';
const collapseActiveKey = ref([1]);
const prop = defineProps({
  saveParam: Object as PropType<IEvaluateSaveRequestDTO>,
  type: String as PropType<String>,
  evaluateType: String as PropType<String>,
  evaluateGroupFlag: String as PropType<String>,
  // 1: 展示模式
  show: Number as PropType<number>,
});
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const fileList = ref<Array<any>>([]);
const saveParam = ref(prop.saveParam);

const initFlag = () =>{
  if (prop.evaluateGroupFlag) {
    saveParam.value.groupFlag = parseInt(prop.evaluateGroupFlag as any);
  }
  if (prop?.show && prop?.show === 1) {
    const file = {
      name: filePath.value.substring(filePath.value.lastIndexOf('-') + 1, filePath.value.length),
      url: baseUrl + filePath.value,
    };
    fileList.value.push(file);
  }
}

const remark = ref(saveParam?.value?.evaluates?.[0].evaluateRemark || '');
const filePath = ref(saveParam?.value?.evaluates?.[0].attachmentPath || '');

watch(
  () => prop.saveParam,
  (val: any) => {
    saveParam.value = prop.saveParam;
    initFlag();
    remark.value = saveParam?.value?.evaluates?.[0].evaluateRemark || ''
    filePath.value = saveParam?.value?.evaluates?.[0].attachmentPath || ''
  },
  { deep: true },
);

{
  initFlag()
}


watch(
  () => remark.value,
  (val: string) => {
    if (saveParam.value.evaluates && saveParam.value.evaluates.length > 0) {
      saveParam.value.evaluates.forEach((it) => {
        it.evaluateRemark = val;
      });
    }
  },
);
watch(
  () => filePath.value,
  (val: string) => {
    if (saveParam.value.evaluates && saveParam.value.evaluates.length > 0) {
      saveParam.value.evaluates.forEach((it) => {
        console.log('filePath.watch', filePath.value);
        it.attachmentPath = val;
      });
    }
  },
);
const removeDomain = (item: any) => {
  let index = saveParam.value.evaluates?.indexOf(item);
  if (index != undefined && index !== -1) {
    saveParam.value.evaluates?.splice(index, 1);
  }
};

const addDomain = () => {
  if (!saveParam.value.evaluates) {
    saveParam.value.evaluates = [];
  }
  saveParam.value.evaluates?.push({
    evaluateRemark: remark.value,
    attachmentPath: filePath.value,
    type: parseInt(prop?.evaluateType as any),
  });
};
const uploadLoading = ref(false);
const upload = (options: any) => {
  uploadLoading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);
  fileApi
    .upload(formData)
    .then((it) => {
      filePath.value = it.path;
      const file = {
        ...options.file,
        name: options.file.name,
        url: baseUrl + it.path,
      };
      fileList.value = [...fileList.value, file];
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

const userNameChange = (userInfo: IUserInfo, record: IEvaluateSaveDetailsRequestDTO) => {
  record.rateeUsercode = userInfo.username;
  record.rateeUsername = userInfo.nickName;
};

const isDisabled = () => {
  return prop.show === 1;
};
const changeEType = (record: IEvaluateSaveDetailsRequestDTO) => {
  record.evaluateAmount = undefined;
  record.evaluateCoefficient = undefined;
};

const selectEvaluateLevel = (select: string, request: IEvaluateSaveDetailsRequestDTO) => {
  if (select === '2') {
    request.evaluateCoefficient = '0.6';
  } else if (select === '4') {
    request.evaluateCoefficient = '0.7';
  } else if (select === '6') {
    request.evaluateCoefficient = '0.9';
  } else if (select === '6-8') {
    request.evaluateCoefficient = '0.95';
  } else if (select === '8') {
    request.evaluateCoefficient = '1';
  } else if (select === '8-10') {
    request.evaluateCoefficient = '1.15';
  } else if (select === '10') {
    request.evaluateCoefficient = '1.3';
  }
};
</script>

<template>
  <h-row :align="'middle'" style="margin: 5px 24px">
    <h-col :span="24" style="text-align: left">
      <h-collapse
        v-model:activeKey="collapseActiveKey"
        style="background-color: rgb(250, 250, 250)"
        :collapsible="'icon'"
      >
        <h-collapse-panel key="1">
          <template #header>
            <div style="font-size: 15px; font-weight: 500; margin-left: 10px">评价详情</div>
          </template>
          <h-row>
            <h-col :span="21" offset="2" style="text-align: left">
              <h-form-item name="target" label="评价维度">
                <h-textarea
                  :rules="{ required: true }"
                  v-model:value="remark"
                  :rows="3"
                  allow-clear
                  :disabled="isDisabled()"
                />
              </h-form-item>
            </h-col>
          </h-row>
          <h-row v-if="filePath || show !== 1">
            <h-col :span="21" offset="2" style="text-align: left">
              <h-form-item name="orientation" label="上传附件">
                <h-upload
                  :disabled="isDisabled()"
                  v-model:file-list="fileList"
                  list-type="picture"
                  :max-count="1"
                  :custom-request="upload"
                >
                  <h-button v-if="!isDisabled()">
                    <upload-outlined></upload-outlined>
                    上传附件
                  </h-button>
                </h-upload>
              </h-form-item>
            </h-col>
          </h-row>
          <h-row>
            <h-col :span="21" offset="2" style="text-align: left">
              <h-form-item name="target" label="被评价人">
                <template v-for="(domain, index) in saveParam.evaluates">
                  <h-row :align="'middle'">
                    <h-col :span="6">
                      <h-form-item :name="['data', index, 'name']">
                        <user-select
                          :disabled="isDisabled()"
                          :value="domain.rateeUsername"
                          placeholder="姓名"
                          :params="{
                            pageNum: 1,
                            pageSize: 20,
                          }"
                          @change="(userInfo: IUserInfo,) =>  userNameChange(userInfo, domain)"
                        ></user-select>
                      </h-form-item>
                    </h-col>
                    <h-col :span="6" :offset="1">
                      <h-form-item :name="['data', index, 'description']" class="fresh-from-item">
                        <h-input-number
                          :disabled="isDisabled()"
                          :precision="2"
                          :decimalPlaces="2"
                          v-model:value="domain.evaluateAmount"
                          style="margin: -5px 0; width: 160px"
                          type="number"
                          allowClear
                          placeholder="评价金额"
                        />
                        / 元
                      </h-form-item>
                    </h-col>
                    <h-col :span="6" :offset="1" v-if="type === 'planform-evaluate'">
                      <h-form-item :name="['data', index, 'description']" class="fresh-from-item">
                        <h-select
                          :disabled="isDisabled()"
                          placeholder="评价区位"
                          v-model:value="domain.evaluateLevel"
                          style="margin: -5px 0; width: 160px"
                          @change="selectEvaluateLevel($event as any, domain)"
                          allow-clear
                        >
                          <h-select-option :value="'2'">2 </h-select-option>
                          <h-select-option :value="'4'">4 </h-select-option>
                          <h-select-option :value="'6'">6 </h-select-option>
                          <h-select-option :value="'6-8'">6-8 </h-select-option>
                          <h-select-option :value="'8'">8</h-select-option>
                          <h-select-option :value="'8-10'">8-10</h-select-option>
                          <h-select-option :value="'10'">10 </h-select-option>
                        </h-select>
                      </h-form-item>
                    </h-col>
                    <h-col :span="1" :offset="1">
                      <MinusCircleOutlined v-if="saveParam.evaluates" class="lable" @click="removeDomain(domain)" />
                    </h-col>
                  </h-row>
                </template>
                <h-row :align="'middle'" v-if="!isDisabled()">
                  <h-col :span="24">
                    <h-form-item>
                      <h-button type="dashed" style="width: 510px" @click="addDomain">
                        <PlusOutlined />
                        添加
                      </h-button>
                    </h-form-item>
                  </h-col>
                </h-row>
              </h-form-item>
            </h-col>
          </h-row>
        </h-collapse-panel>
      </h-collapse>
    </h-col>
  </h-row>
</template>

<style scoped lang="less"></style>
