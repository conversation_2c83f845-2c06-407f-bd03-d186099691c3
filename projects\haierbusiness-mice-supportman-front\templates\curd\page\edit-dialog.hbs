<script lang="ts" setup>
import { Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
     Input as hInput, Textarea as hTextarea } from 'ant-design-vue';
import { computed, ref, watch } from "vue";
import type { Ref } from "vue";
import {
  I{{ properCase modelName }}
} from '@haierbusiness-front/common-libs';

interface Props {
    show: boolean;
    data: I{{ properCase modelName }} | null;
}

const props = withDefaults(defineProps<Props>(), {
show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: I{{ properCase modelName }} = {
    name: '',
    code: '',
    state: 0,
    id: null,
    description: ''
};

const rules = {

};

const {{ camelCase modelName }}: Ref<I{{ properCase modelName }}> = ref(
({ ...props.data } as I{{ properCase modelName }}) || defaultData
);

watch(props, (newValue) => {
    {{ camelCase modelName }}.value = ({ ...newValue.data } as I{{ properCase modelName }}) || defaultData;
});

const emit = defineEmits(["cancel", "ok"]);

const visible = computed(() => props.show);

const handleOk = () => {
confirmLoading.value = true;
from.value
    .validate()
    .then(() => {
    emit("ok", {{ camelCase modelName }}.value, () => {
        confirmLoading.value = false;
    });
    })
    .catch(() => {
        confirmLoading.value = false;
    });
};
</script>

<template>
    <h-modal
      v-model:visible="visible"
      :title="{{ camelCase modelName }}.id ? '编辑{{labelName}}' : '新增{{labelName}}'"
      :width="600"
      @cancel="$emit('cancel')"
      :confirmLoading="confirmLoading"
      @ok="handleOk"
    >
      <h-form
        ref="from"
        :model="{{ camelCase modelName }}"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
        :rules="rules"
      >
      </h-form>
    </h-modal>
</template>


<style lang="less" scoped>
.important {
color: red;
}
</style>
  