<script setup lang="ts">
import conferenceDetail from '../../components/conference/detail/conference_detail_base.vue'
import { Card as hCard, Descriptions as hDescriptions, DescriptionsItem as hDescriptionsItem, Steps as hSteps, Table as hTable} from 'ant-design-vue';
import { isMobile } from '@haierbusiness-front/utils';
import { DataType, usePagination, useRequest } from 'vue-request';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { miceApi } from '@haierbusiness-front/apis';
import { computed, onMounted, ref } from 'vue';
import router from '../../router'

const target = ref<'PC' | 'MOBILE'>('PC')
const currentRouter = ref()
const code = ref<string>('')
const currentSteps = ref<number>(0)
const stepsItem = ref(
    [
      {
        title: '待提交',
      },
      {
        title: '审核中',
      },
      {
        title: '待生效',
      },
      {
        title: '预算占用成功',
      },
    ]
)
const columns: ColumnType[] = [
  {
    title: '序号',
    dataIndex: 'serialNumber',
    align: 'center',
  },
  {
    title: '流程节点',
    dataIndex: 'statusDesc',
    align: 'center',
  },
  {
    title: '操作人',
    dataIndex: 'createUser',
    align: 'center',
  },
  {
    title: '操作时间',
    dataIndex: 'createTime',
    align: 'center',
  },
  {
    title: '操作内容',
    dataIndex: 'content',
    align: 'center',
  }
];

const dataList = ref<any>([])
const loading = ref<boolean>(false)

// 请求详情
const getTableInfo = ()=>{
  loading.value = true
  miceApi.getStepInfoByMeeting(code.value).then(res=>{
    res.data.details.forEach((item:any)=>{
      item.details.forEach((v:any)=>{
        v.statusDesc = item.statusDesc
        v.status = item.status
      })
      dataList.value.push(...item.details)
      currentSteps.value = res.data.status
    })
    loading.value = false
  })
  .catch(()=>{
    loading.value = false
  })
}

onMounted(async() => {
    currentRouter.value = await router
    if(isMobile()) {
        target.value = 'MOBILE'
    } else {
        target.value = 'PC'
    }
    code.value = currentRouter.value.currentRoute.query?.code ?? ''
    getTableInfo()
})

</script>

<template>
    <div class="contentbox">
        <div class="detail">
            <h-card size="small" title="流程查看" style="width: 100%" :headStyle="{'font-weight': 'bold'}">
                <h-steps
                    :current="currentSteps"
                    :items="stepsItem"
                    >
                </h-steps>
                <h-table
                style="margin-top:20px;"
                :columns="columns"
                :ellipsis="true"
                :row-key="(record) => record.id"
                :size="'small'"
                :data-source="dataList"
                :pagination="false"
                :scroll="{ y: 500 }"
                :loading="loading"
                >
                <!-- 自定义序号列 -->
                <template #bodyCell="{ column, text, record, index }">
                      <!-- 当前列是序号列时，显示序号 -->
                      <span v-if="column.dataIndex === 'serialNumber'">{{ index + 1 }}</span>
                      <!-- 其他列正常显示 -->
                      <span v-else>{{ text }}</span>
                    </template>
                </h-table>
            </h-card>
        </div>
    </div>
</template>

<style lang="less" scoped>
.contentbox {
    display: flex;
    justify-content: center;
    width: 100%;
    padding-top: 60px;
}

.detail {
    width: 1200px;
}

@media screen and (min-width: 0px) and (max-width: 1199px) {
    .content {
        display: flex;
        justify-content: center;
        width: 100%;
        margin-top: 0px;
    }
    .detail {
        width: 100%;
    }
}

@media screen and (max-width: 767px) {
    .content {
        display: flex;
        justify-content: center;
        width: 100%;
        margin-top: 0px;
    }

    .detail {
        width: 100%;
    }
}

</style>