<!-- 会议交接列表 -->

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Table as hTable } from 'ant-design-vue';
import type { ColumnType } from 'ant-design-vue/lib/table/interface';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { meetingHandoversApi } from '@haierbusiness-front/apis';
import type { IMeetingHandoversFilter, IMeetingHandovers } from '@haierbusiness-front/common-libs';

const { loginUser } = storeToRefs(applicationStore(globalPinia));

// 会议交接列表数据
const meetingHandoversList = ref<IMeetingHandovers[]>([]);
const loading = ref(false);

// 分页相关状态
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ['10', '20', '50', '100']
});

// 表格列定义
const columns: ColumnType[] = [
  {
    title: '经办人',
    dataIndex: 'connectBeforeName',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '经办人工号',
    dataIndex: 'connectBeforeCode',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '经办人电话',
    dataIndex: 'connectBeforePhone',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '经办人邮箱',
    dataIndex: 'connectBeforeEmail',
    width: '180px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '承接人',
    dataIndex: 'handoverAfterName',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '承接人工号',
    dataIndex: 'handoverAfterCode',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '承接人电话',
    dataIndex: 'handoverAfterPhone',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '承接人邮箱',
    dataIndex: 'handoverAfterEmail',
    width: '180px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '承接人直线',
    dataIndex: 'handoverLineName',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '交接原因',
    dataIndex: 'handoverReason',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '交接状态',
    dataIndex: 'handoverState',
    width: '100px',
    align: 'center',
    customRender: ({ text }) => {
      const stateText = text === 10 ? '交接中' : text === 20 ? '已完成' : text === 30 ? '已驳回' : '已撤回';
      return stateText;
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: '130px',
    align: 'center',
    ellipsis: true,
  },
];

// 获取会议交接列表
const fetchMeetingHandoversList = async (pageNum?: number, pageSize?: number) => {
  if (!loginUser.value?.username) {
    console.warn('用户信息不完整，无法获取工号');
    return;
  }

  loading.value = true;
  try {
    // 构建请求参数，使用工号作为operator
    const params: IMeetingHandoversFilter & { operator?: string } = {
      pageNum: pageNum || pagination.value.current,
      pageSize: pageSize || pagination.value.pageSize,
      operator: loginUser.value.username, // 传入当前用户工号
    };

    const response = await meetingHandoversApi.list(params);

    if (response && response.records) {
      meetingHandoversList.value = response.records;
      // 更新分页信息
      pagination.value.total = response.total || 0;
      pagination.value.current = pageNum || pagination.value.current;
      console.log('会议交接列表获取成功:', response.records);
    } else {
      meetingHandoversList.value = [];
      pagination.value.total = 0;
      console.log('暂无会议交接数据');
    }
  } catch (error) {
    console.error('获取会议交接列表失败:', error);
    meetingHandoversList.value = [];
    pagination.value.total = 0;
  } finally {
    loading.value = false;
  }
};

// 处理分页变化
const handleTableChange = (paginationInfo: any) => {
  pagination.value.current = paginationInfo.current;
  pagination.value.pageSize = paginationInfo.pageSize;
  fetchMeetingHandoversList(paginationInfo.current, paginationInfo.pageSize);
};

// 页面加载时调用接口
onMounted(() => {
  fetchMeetingHandoversList();
});
</script>

<template>
  <div class="meeting-handover-container">
    <h3>会议交接列表</h3>

    <hTable
      :columns="columns"
      :dataSource="meetingHandoversList"
      :loading="loading"
      :rowKey="(record: IMeetingHandovers, index: number) => (record.id ? record.id.toString() : `empty-${index}`)"
      :pagination="pagination"
      :scroll="{ x: 'max-content' }"
      size="middle"
      @change="handleTableChange"
    >
      <template #emptyText>
        <div style="padding: 40px 0">
          <p>暂无会议交接数据</p>
        </div>
      </template>
    </hTable>
  </div>
</template>

<style scoped>
.meeting-handover-container {
  padding: 20px;
  background: #fff;
}

h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 12px 8px;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}
</style>
