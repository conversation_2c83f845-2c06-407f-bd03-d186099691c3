.flex {
  display: flex;
}
.flex-column {
  flex-direction: column;
}
.align-items-center {
  align-items: center;
}
.justify-content-center {
  justify-content: center;
}
.justify-content-between {
  justify-content: space-between;
}
.width100 {
  width: 100%;
}
.flex-warp {
  flex-wrap: wrap;
}
.font-weight-600 {
  font-weight: 600;
}
.mr-5 {
  margin-right: 5px !important;
}
.mt-5 {
  margin-top: 5px !important;
}
.mt-10 {
  margin-top: 10px;
}
.ml-5 {
  margin-left: 5px !important;
}
.mb-5 {
  margin-bottom: 5px !important;
}
.mr-10 {
  margin-right: 10px !important;
}
.mt-20 {
  margin-top: 20px;
}
.mb-20 {
  margin-bottom: 20px;
}
.ml-10 {
  margin-left: 10px !important;
}
.mb-10 {
  margin-bottom: 10px !important;
}
.mb-30 {
  margin-bottom: 30px !important;
}
.strong {
  font-weight: 600;
  font-size: 14px;
}
.mr-20 {
  margin-right: 20px;
}
.font-size-14 {
  font-size: 14px;
}
.font-size-13 {
  font-size: 13px;
}
.font-size-11 {
  font-size: 11px;
}
.font-size-12 {
  font-size: 12px;
}
.font-size-vant {
  font-size: var(--van-tab-font-size);
}
.color-disabled {
  color: #9b9ea1;
}
.font-size-10{
  font-size: 10px;
}
.font-size-8{
  font-size: 8px;
}
.color-000 {
  color: #000;
}
.color-eee {
  color: #c0c0c0;
}
.weight600 {
  font-weight: 600;
}
.flex-1 {
  flex: 1;
}
.color-main {
  
  color: #0073e5;
}
.hotel-img {
  width: 105px;
    height: 105px;
    // background-color: #0073e5;
    border-radius: 6px;
  img {
    width: 100%;
    height: 100%;
    border-radius: 6px;  }
}
.book-list {

  :deep(.van-cell__value) {
    text-align: left;
  }
}

.order-list {
  // margin-top: 36px;
  background-color: #FAFBFD;
}

.color-red {
  color: red;
}
.bg-eee {
  background: #F7F7F7;
}
.my-swipe {
}

.padding-16 {
  padding: 16px;
}

.detail-title-box {
  border-radius: 8px;
  background: #FFFFFF;
  position: relative;
  top: -10px;
  .detail-title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 14px;
    color: #262626;
    display: flex;
    justify-content: space-between;
    .title-more {
      font-size: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #999;
    }
  }
  .detail-food  {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 10px;
    color: #8C8C8C;
  }
  .detail-area {
    
  }
  .detail-star {
    
    img {
      width: 20px;
      height: 16px;
    }
    div {
      background: rgba(153, 176, 197, 0.2);
      
      line-height: 12px;
      padding: 0 2px;
      height: 12px;
      margin-top: 4px;
      font-size: 8px;
      color: #99B0C5;
    }
    .star-level-5 {
      background: rgba(255, 155, 76, 0.2);
      color: #FF9B4C;
    }
  }
  .detail-money {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 12px;
    color: #c2c2c2;
    line-height: 18px;
  }
  .detail-airportDistance {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 10px;
    color: #BFBFBF;
    line-height: 20px;
  }
}
.my-swipe .van-swipe-item {
 
  color: #fff;
  font-size: 20px;
  line-height: 150px;
  text-align: center;
  background-color: #39a9ed;
}
.detail-content {
  padding: 10px;
}
.main {
  position: relative;
}
.main-title {
  font-size: 12px;
}
.row-scollx{
  overflow-x: auto;
}
.hotel-item-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #262626;
  font-style: normal;
}
.hotel-item-foods {
  color: #8C8C8C;
  font-family: PingFangSC, PingFang SC;
  font-size: 10px;

}
.hotel-item-consumptionPer {
font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 10px;
color: #c2c2c2;
text-align: right;
}

.hotel-item-address {
  color: #595959;
    font-family: PingFangSC, PingFang SC;
  font-size: 12px;

}

.scroll-hidden{
  height:100px;
  overflow:hidden;
  .scroll-body{
      overflow-y: hidden;
      overflow-x: auto;
      &::-webkit-scrollbar {
          display: none;
      }
      padding-bottom: 20px;
      .scroll-secbody{
          white-space: nowrap;
          display: flex;   
          .every_content{
              flex-shrink: 0;
              height:150px;
              margin-right: 25px;
              margin-bottom: 10px;
              text-align: center;
          }
          .img-title {
            position: absolute;
            bottom: 0;
            left: 0;
            background: rgba(0, 0, 0, 0.4);
            // height: 20px;
            color: #fff;
            width: 100%;
          }
      }
  }
}

  .navigation-box {
    position: absolute;
    top: 0;
    height: 100%;
    right: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80px;
    img {
      width: 20px;
      height: 20px;
    }
    span {
     color: #595959;
     font-size: 12px;
    }
  }
.bottom-order-btn {
  background: #fff;
  height: 50px;
  /* padding: 10px; */
  display: flex;
  align-items: center;
  justify-content: center;
  .btn {
    width: 120px !important;
    
  }
  .left {
    background-color: #ff6410;
    border-radius: 20px 0 0 20px !important;
  }
  .right {
    border-radius:0 20px  20px 0 !important;
  }
}

.title {
  height: 26px;
  margin: 0 10px;
  .shu {
    display: inline-block;
    height: 10px;
    width: 2px;
    background-color: #0052d9;
    margin-right: 5px;
    border-radius: 4px;
  }
  .text {
    font-weight: 600;
    // font-size: 14px;
  }
}
.bottom-btn-box {
  background-color: #fff;
  padding: 0 10px;
  :deep(.van-dropdown-menu__bar) {
    box-shadow: none
  }
  .money {
    color: red;
    font-size: 16px;
  }
  :deep(.van-dropdown-menu__title) {
    font-size: 8px;
    color: #868686;

  }
}
.cell-item {
  :deep(.van-cell__value) {
    width: 25%;
    flex: none;
  }
}
.order-detail {
  min-height: 100vh;
  padding-top: 14px;
  background-color: #FAFBFD;
}
.search-btn {
  padding: 0 15px;
  background: linear-gradient( 180deg, #4EB7FF 0%, #2681FF 100%);
  border-radius: 32px;

  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  text-align: left;
  font-style: normal;

}

:root:root {
  --van-search-content-background: rgba(242,243,245,0);
  --van-dropdown-menu-content-max-height: 100%;
}

.top-search {
  .search-icon-color {
    color: #2681FF;
  }
  :deep(.van-search__content) {
    background-color: rgba(242,243,245,0);
    border: 1px solid #2681FF;
  }
  :deep(.van-field__right-icon) {
    padding: 0;
  }
}

.my-dropdown {
  :deep(.van-dropdown-menu__title) {
    height: 60%;
    background: #F6F7F9;
    padding: 0 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;

   
  }
  :deep(.van-dropdown-menu__title:after) {
    opacity: 0;
  }
 

  .default-down-icon {
    background: url('@/assets/image/restaurant/dropdown_default.png') no-repeat 100% 100%;
    background-size: cover;
    width: 12px;
    height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :deep(.van-ellipsis) {
    display: flex;
    align-items: center;
  }
  :deep(.van-dropdown-menu__title--active) {
    border: 1px solid #2681FF;
    color: #2681FF;
    background: rgba(38,129,255,0.08);

   
  }

 
  .van-dropdown-menu__title--active >.van-ellipsis >.default-down-icon {
    color: #fff;
    
    background: url('@/assets/image/restaurant/dropdown_active.png') no-repeat 100% 100%;
    background-size: cover;
  }
}
:deep(.van-dropdown-item__content) {
  max-height: 100%;
}
.hidden-3 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;

}

.detail-text-color {
  color: rgba(140, 140, 140, 1);
}

.btn-background {
  background: rgba(38,129,255,0.1);
  border: 1px solid #2681FF;
  color: rgba(38, 129, 255, 1);
}
:deep(.active-dropdown-item) {
  border: 0.013333rem solid #2681FF !important;
  color: #2681FF !important;
  background: rgba(38, 129, 255, 0.08)!important;
}
.order-title-cell {
  background: -webkit-gradient(360deg, rgba(195, 211, 255, 0.3) 0%, rgba(38 129,255,0.3) 100%);  
  background: linear-gradient(360deg, rgba(195, 211, 255, 0.3) 0%, rgba(38 129,255,0.3) 100%);  
  border-radius: 10px 10px 0px 0px;
}
.cell-group-shadow {
  box-shadow: 0px 8px 8px 0px rgba(157,178,232,0.1);
}
:deep(.van-toast) {
  z-index: 2000;
}

.empty-min-height {
  min-height: 74vh;
}

// 编辑器table样式问题
/* table 样式 */
:deep(table){
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
:deep(table td),
:deep(table th) {
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
  padding: 3px 5px;
  height:32px;
}
:deep(table th) {
  border-bottom: 2px solid #ccc;
  text-align: center;
  background-color: #f1f1f1 ;
}

.my-radio {
  :deep(.van-radio__icon) {
    height: auto !important;
    line-height: 10px;
  }
  .btn-com {
    width: 70px;
    height: 20px;
    font-size: 10px;
  }
}