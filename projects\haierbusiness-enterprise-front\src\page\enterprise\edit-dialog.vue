<script lang="ts" setup>
import { Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
     Input as hInput, Textarea as hTextarea } from 'ant-design-vue';
import { computed, ref, watch } from "vue";
import type { Ref } from "vue";
import {
  IEnterprise
} from '@haierbusiness-front/common-libs';

interface Props {
    show: boolean;
    data: IEnterprise | null;
}

const props = withDefaults(defineProps<Props>(), {
show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: IEnterprise = {
    name: '',
    code: '',
    state: 0,
    id: null,
    description: ''
};

const rules = {
    code: [{ required: true, message: "请输入企业编码！" }],
    name: [{ required: true, message: "请输入企业名称！" }],
    state: [{ required: true, message: "请选择状态！" }],
};

const enterprise: Ref<IEnterprise> = ref(
({ ...props.data } as IEnterprise) || defaultData
);

watch(props, (newValue) => {
    enterprise.value = ({ ...newValue.data } as IEnterprise) || defaultData;
});

const emit = defineEmits(["cancel", "ok"]);

const visible = computed(() => props.show);

const handleOk = () => {
confirmLoading.value = true;
from.value
    .validate()
    .then(() => {
    emit("ok", enterprise.value, () => {
        confirmLoading.value = false;
    });
    })
    .catch(() => {
        confirmLoading.value = false;
    });
};
</script>

<template>
    <h-modal
      v-model:visible="visible"
      :title="enterprise.id ? '编辑企业' : '新增企业'"
      :width="600"
      @cancel="$emit('cancel')"
      :confirmLoading="confirmLoading"
      @ok="handleOk"
    >
      <h-form
        ref="from"
        :model="enterprise"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
        :rules="rules"
      >
        <h-form-item label="企业编码" name="code">
          <h-input v-model:value="enterprise.code" />
        </h-form-item>
        <h-form-item label="企业名称" name="name">
          <h-input v-model:value="enterprise.name" />
        </h-form-item>
        <h-form-item label="状态" name="state">
          <h-select v-model:value="enterprise.state" allow-clear>
            <h-select-option :value="1">正常</h-select-option>
            <h-select-option :value="2">冻结</h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="描述" name="description">
            <h-textarea v-model:value="enterprise.description" placeholder="描述" :rows="2" />
        </h-form-item>
      </h-form>
    </h-modal>
</template>


<style lang="less" scoped>
.important {
color: red;
}
</style>
  