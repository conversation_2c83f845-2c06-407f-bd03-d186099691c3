<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  CheckboxGroup as hCheckboxGroup,
  Checkbox as hCheckbox,
  Row as hRow,
  Col as hCol,
  DatePicker as hDatePicker,
  message,
  Upload as hUpload,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  InputNumber as hInputNumber,
  Button as hButton,
  Switch as hSwitch,
  Table as hTable,
  TableColumn as hTableColumn,
  Textarea as hTextarea,
} from 'ant-design-vue';
import { computed, ref, watch, onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import type { Ref } from 'vue';
import {
  IProductLine,
  IUser,
  IConsultant,
  IConsultantFilter,
  IProductLineFilter,
  IProductLineFlow,
  IPageResponse,
  IUserListRequest,
} from '@haierbusiness-front/common-libs';
import { product<PERSON><PERSON><PERSON><PERSON>, consultant<PERSON>pi } from '@haierbusiness-front/apis';
import router from '../../router';
import type { Rule } from 'ant-design-vue/lib/form';
import UserSelectPerson from '@haierbusiness-front/components/user/UserSelectPerson.vue';

const route = useRoute();
const currentRouter = ref();
const from = ref();
const confirmLoading = ref(false);
const id = ref<number>();

// 定义选项类型
interface UserOption {
  label: string;
  value: string;
  id?: number | null;
}

// 人员选项
const userOptions = ref<UserOption[]>([]);

const childRef = ref()

// 搜索关键词
const searchKeyword = ref('');

// 设置管理员最大展示数量
const maxManagerCount = ref<number>(3);

// 查询会议顾问列表
const fetchConsultants = async (keyword = '') => {
  const params: IConsultantFilter = {
    pageNum: 1,
    pageSize: 99999,
    state:0,
  };

  if (keyword) {
    params.keyword = keyword;
  }

  try {
    const res = await consultantApi.list(params);
    if (res && res.records) {
      // 打印第一条数据，查看结构
      if (res.records.length > 0) {
        for (const key in res.records[0]) {
        }
      }

      userOptions.value = res.records.map((item) => ({
        label: item.nickName || '未命名顾问',
        value: item.username || '',
        id: item.id, // 确保这里使用的是id
      }));

    }
  } catch (error) {
    message.error('获取会议顾问列表失败');
  }
};

// 处理搜索
const handleSearch = (value: string) => {
  searchKeyword.value = value;
  fetchConsultants(value);
};


// 产品线流程数据
const flowColumns = [
  {
    title: '流程名称',
    dataIndex: 'flowName',
    key: 'flowName',
  },
  {
    title: '操作',
    key: 'operation',
    dataIndex: 'operation',
  },
];

// 扩展产品线流程接口，添加disabled属性
interface ExtendedProductLineFlow extends IProductLineFlow {
  disabled?: boolean;
}

// 计算展示用的流程列表，确保始终有一个空行用于添加按钮
const displayProcessList = computed(() => {
  if (!productLine.value.processList || productLine.value.processList.length === 0) {
    return [{ flowName: '', flowId: '', flowType: '', operation: '', isEmptyRow: true }];
  }
  return productLine.value.processList;
});

// 流程列表数据
const processList = ref<IPageResponse<IProductLine> | null>(null);
// 选择流程的弹框状态
const flowModalVisible = ref(false);
// 流程列表筛选关键词
const flowKeyword = ref('');
// 选择的流程列表
const selectedFlows = ref<ExtendedProductLineFlow[]>([]);
// 临时存储所有可选流程
const availableFlows = ref<ExtendedProductLineFlow[]>([]);

// 获取流程列表
const fetchProcessList = async (keyword = '') => {
  try {
    const params: IProductLineFilter = {};

    if (keyword) {
      params.name = keyword;
    }

    const res = await productLineApi.getProcessList(params);

    if (res) {
      processList.value = res || null;

      // 提取所有可用的流程
      if (processList.value && processList.value.records && processList.value.records.length > 0) {
        availableFlows.value = processList.value.records.map((item) => ({
          flowId: item.id?.toString() || '',
          flowName: item.name || '',
          // 使用可选的属性读取
          flowType: typeof item.description === 'string' ? item.description : '',
          operation: ''
        }));
      } else if (Array.isArray(processList.value)) {
        availableFlows.value = processList.value.map((item) => ({
          flowId: item.id?.toString() || '',
          flowName: item.name || '',
          // 使用可选的属性读取
          flowType: typeof item.description === 'string' ? item.description : '',
          operation: '',
          disabled: false
        }));
      } else {
        availableFlows.value = [];
      }

      // 初始化空流程列表，不自动添加流程
      if (!productLine.value.processList) {
        productLine.value.processList = [];
      }
    }
  } catch (error) {
    console.error('获取流程列表失败', error);
    message.error('获取流程列表失败');
    availableFlows.value = [];
  }
};

// 打开流程选择弹框
const openFlowModal = () => {
  selectedFlows.value = [];
  flowModalVisible.value = true;
  // 更新弹框中已选过的流程状态
  updateExistingFlowsStatus();
};

// 更新已选过的流程状态
const updateExistingFlowsStatus = () => {
  // 获取已经选择的流程ID列表
  const existingFlowIds = productLine.value.processList?.map(flow => flow.flowId) || [];

  // 标记每个流程是否已选过
  availableFlows.value.forEach(flow => {
    flow.disabled = existingFlowIds.includes(flow.flowId);
  });
};

// 关闭流程选择弹框
const closeFlowModal = () => {
  flowModalVisible.value = false;
  selectedFlows.value = [];
};

// 处理流程选择
const handleFlowSelect = (flow: ExtendedProductLineFlow) => {
  const index = selectedFlows.value.findIndex(f => f.flowId === flow.flowId);
  if (index > -1) {
    selectedFlows.value.splice(index, 1);
  } else {
    selectedFlows.value.push(flow);
  }
};

// 确认选择的流程
const confirmFlowSelection = () => {
  if (selectedFlows.value.length > 0) {
    const existingFlowIds = (productLine.value.processList || []).map(f => f.flowId);
    const newFlows = selectedFlows.value.filter(flow => !existingFlowIds.includes(flow.flowId));
    // 添加新选择的流程到产品线流程中
    if (newFlows.length > 0) {
      if (!productLine.value.processList) {
        productLine.value.processList = [];
      }
      productLine.value.processList.push(...newFlows);

      // 编辑模式下，记录新增的流程ID
      if (id.value) {
        newFlows.forEach(flow => {
          if (flow.flowId && !originalProcessIds.value.includes(flow.flowId)) {
            processAddIdList.value.push(flow.flowId);
          }
        });
      }

      message.success(`已添加 ${newFlows.length} 个流程`);
    } else {
      message.info('选择的流程已存在');
    }
  }
  closeFlowModal();
};

// 存储选中的管理员和顾问
const selectedManagers = ref<any[]>([]);
const selectedManagerUsernames = ref<string[]>([]);
const selectedCounsellors = ref<string[]>([]);

// 存储原始的管理员和顾问ID
const originalManagerIds = ref<number[]>([]);
const originalCounsellorIds = ref<number[]>([]);

// 存储需要删除的用户ID
const userDelIdList = ref<number[]>([]);

// 存储原始的流程ID和需要删除的流程ID
const originalProcessIds = ref<string[]>([]);
const processDelIdList = ref<string[]>([]);
const processAddIdList = ref<string[]>([]);

// 用于UserSelect组件的参数
const userSelectParams = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20
});

onMounted(async () => {
  currentRouter.value = await router;
  await fetchConsultants();
  // 不立即获取流程列表，而是在点击添加按钮时再获取

  const currentId = route.query?.id as string;
  if (currentId) {
    id.value = Number(currentId);
    await get(id.value);
  } else {
    productLine.value = {
      processList: [],
    };
  }
})
watch(
  () => route.query,
  (newValue) => {
    const newId = newValue?.id as string;
    if (newId) {
      id.value = Number(newId);
      get(id.value);
    } else {
      productLine.value = {
        processList: [],
      };
      selectedManagers.value = [];
      selectedCounsellors.value = [];
    }
  },
);

const get = async (id: number) => {
  const data = await productLineApi.get(id);

  if (data && data.id) {

    // 清空原始记录
    originalManagerIds.value = [];
    originalCounsellorIds.value = [];
    originalProcessIds.value = [];
    userDelIdList.value = []; // 确保完全清空删除列表
    processDelIdList.value = [];
    processAddIdList.value = [];

    // 创建一个映射，记录原始顾问的工号与ID的对应关系
    const originalCounsellorMap = new Map<string, number[]>();

    // 从原始顾问列表中收集所有工号
    const originalWorkNumbers = new Set<string>();

    // 处理流程列表数据格式转换
    let formattedProcessList: ExtendedProductLineFlow[] = [];

    if (data.processList && Array.isArray(data.processList) && data.processList.length > 0) {
      // 将后端返回的流程数据格式转换为前端需要的格式
      formattedProcessList = data.processList.map(process => ({
        flowId: process.id?.toString() || '',
        flowName: process.name || '',
        flowType: process.description || '',
        operation: '',
        disabled: false
      }));

      // 记录原始流程ID
      originalProcessIds.value = data.processList.map(process => process.id?.toString() || '');
    }

    productLine.value = {
      id: data.id,
      productName: data.name,
      description: data.description,
      processList: formattedProcessList
    };


    // 提取已有的管理员和顾问信息用于回显并记录原始ID
    if (data.adminList && data.adminList.length > 0) {
      console.log(data.adminList, "data.adminLis");
      // 使用对象格式保存管理员信息，以便UserSelect组件能够正确回显
      const managers = data.adminList.map(admin => ({
        ...admin, // 保留所有原始属性
        username: (admin as any).username || admin.nickName || '',
        nickName: admin.nickName || '',
        id: admin.id || null
      }));
      console.log(managers, "managers");
      selectedManagers.value = managers
      // 修改：为UserSelect组件提供格式化后的显示值
      selectedManagerUsernames.value = managers.map(manager => manager.nickName || '')

      console.log(selectedManagerUsernames.value, selectedManagers.value, "selectedManagerUsernames.value");
      childRef.value?.setFirstData(selectedManagers.value)

      originalManagerIds.value = data.adminList.map(admin => admin.id || 0).filter(id => id !== 0);
    }

    // 如果有顾问列表，优先使用它
    if (data.counsellorList && data.counsellorList.length > 0) {

      // 不进行工号去重，记录所有顾问工号
      selectedCounsellors.value = data.counsellorList.map(counsellor => (counsellor as any).username || '');

      // 正确记录所有顾问的ID，确保这里是完全正确的
      originalCounsellorIds.value = data.counsellorList
        .map(counsellor => counsellor.id || 0)
        .filter(id => id !== 0);


      // 创建顾问工号到ID的映射
      data.counsellorList.forEach(counsellor => {
        const username = (counsellor as any).username || '';
        const id = counsellor.id || 0;
        if (username && id) {
          if (!originalCounsellorMap.has(username)) {
            originalCounsellorMap.set(username, [id]);
          } else {
            originalCounsellorMap.get(username)?.push(id);
          }
        }

        // 将工号添加到原始工号集合
        if (username) {
          originalWorkNumbers.add(username);
        }
      });

      // 将顾问工号到ID的映射存储到window对象，方便调试
      (window as any).originalCounsellorMap = Object.fromEntries([...originalCounsellorMap.entries()]
        .map(([k, v]) => [k, v]));

    }

    // 如果有userList数据，优先使用它
    if (data.userList && data.userList.length > 0) {
      const managers: any[] = [];
      const managerUsernames: string[] = [];
      const counsellors: string[] = []; // 使用数组存储顾问工号，允许重复
      const managerIds: number[] = [];
      const counsellorIds: number[] = [];

      data.userList.forEach((user) => {
        if (user.role === 8) {
          // 管理员 - 保存完整的用户对象
          const username = (user as any).username || '';
          const nickName = (user as any).nickName || '';
          managers.push({
            ...user, // 保留所有原始属性
            username: username,
            nickName: nickName,
            id: user.id || null
          });
          // 修改：使用昵称(工号)格式进行回显
          managerUsernames.push(nickName ? `${nickName}(${username})` : username);
          if (user.id) managerIds.push(user.id);
        } else if (user.role === 4) {
          // 顾问 - 使用username作为工号
          const username = (user as any).username || '';
          if (username) {
            counsellors.push(username); // 允许工号重复
            originalWorkNumbers.add(username); // 添加到原始工号集合
          }
          if (user.id) counsellorIds.push(user.id);
        }
      });

      if (managers.length > 0) {
        selectedManagers.value = managers;
        selectedManagerUsernames.value = managerUsernames;
        if (managerIds.length > 0) {
          originalManagerIds.value = managerIds;
        }
      }

      if (counsellors.length > 0) {
        selectedCounsellors.value = counsellors; // 直接使用数组，允许工号重复
        if (counsellorIds.length > 0) {
          originalCounsellorIds.value = counsellorIds;
        }
      }

    }
    (window as any).originalData = data;
  }
};

const productLine = ref<IProductLine>({
  processList: [],
});

// 添加流程
const addFlow = () => {
  // 打开弹框前先获取最新的流程列表
  fetchProcessList().then(() => {
    openFlowModal();
    flowKeyword.value = ''; // 清空搜索关键词
  });
};

// 执行搜索
const handleFlowSearch = () => {
  // 将搜索关键词传递给接口
  fetchProcessList(flowKeyword.value);
};

// 删除流程
const removeFlow = (index: number) => {
  if (productLine.value.processList) {
    // 检查是否为空行，如果是则不执行删除操作
    if (displayProcessList.value[index].isEmptyRow) {
      return;
    }

    const flowToRemove = productLine.value.processList[index];

    // 如果是编辑状态，且该流程存在于原始数据中，则添加到删除列表
    if (id.value && flowToRemove?.flowId && originalProcessIds.value.includes(flowToRemove.flowId)) {
      processDelIdList.value.push(flowToRemove.flowId);
    }

    // 如果该流程是新添加的，则从添加列表中移除
    if (id.value && flowToRemove?.flowId && processAddIdList.value.includes(flowToRemove.flowId)) {
      const addIndex = processAddIdList.value.indexOf(flowToRemove.flowId);
      if (addIndex > -1) {
        processAddIdList.value.splice(addIndex, 1);
      }
    }

    // 从界面移除
    productLine.value.processList.splice(index, 1);
  }
};

// 组合用户列表
const combineUserList = () => {
  const userList = [] as any[];

  // 添加管理员 (role = 1) - 只添加不在原始列表中的新管理员
  selectedManagers.value.forEach((manager: any) => {
    // 只有当管理员ID不在原始ID列表中时，才添加到userAddList
    // 或者管理员没有ID（表示是新添加的）
    if (!manager.id || !originalManagerIds.value.includes(manager.id)) {
      userList.push({
        username: manager.username || '',
        nickName: manager.nickName || '',
        role: 8,
      });
    }
  });

  const selectedCounsellorInfo = selectedCounsellors.value.map(username => {
    // 查找对应的顾问信息
    const counsellorOption = userOptions.value.find(opt => opt.value === username);
    return {
      username,
      id: counsellorOption?.id || null
    };
  });

  // 添加新增的顾问 - 只添加ID不在原始ID列表中的顾问
  selectedCounsellorInfo.forEach(info => {
    // 如果顾问没有ID，或者ID不在原始列表中
    if (!info.id || !originalCounsellorIds.value.includes(info.id)) {
      userList.push({
        username: info.username,
        role: 4
      });
    }
  });

  return { userList };
};

const submitForm = () => {
  // 调试：检查userDelIdList中的每个值
  if (userDelIdList.value.length > 0) {
    userDelIdList.value.forEach((id, index) => {
    });
  }

  // 手动验证，一次只提示一个错误
  if (!productLine.value.productName) {
    message.error('请输入产品线名称');
    return;
  }

  if (!selectedManagers.value || selectedManagers.value.length === 0) {
    message.error('请选择产品线管理员');
    return;
  }

  if (!selectedCounsellors.value || selectedCounsellors.value.length === 0) {
    message.error('请选择会议顾问');
    return;
  }
  
  // 添加对产品线流程的验证
  if (!productLine.value.processList || productLine.value.processList.length === 0) {
    message.error('请添加产品线流程');
    return;
  }

  confirmLoading.value = true;  

  if (id.value) {
    const userAddList: any[] = [];

    selectedManagers.value.forEach((manager: any) => {
      if (!manager.id || !originalManagerIds.value.includes(manager.id)) {
        userAddList.push({
          username: manager.username || '',
          nickName: manager.nickName || '',
          role: 8,
        });
      }
    });

    // 重新计算真正需要删除的用户ID
    const finalUserDelIdList: number[] = [];
    
    // 处理管理员删除
    const currentManagerIds = selectedManagers.value.map(m => m.id).filter(Boolean);
    originalManagerIds.value.forEach(originalId => {
      if (!currentManagerIds.includes(originalId)) {
        finalUserDelIdList.push(originalId);
      }
    });

    // 处理顾问删除 - 重新计算
    const originalData = (window as any).originalData;
    const currentSelectedWorkNumbers = new Set(selectedCounsellors.value);
    
    console.log('当前选中的顾问工号:', Array.from(currentSelectedWorkNumbers));
    console.log('原始顾问ID列表:', originalCounsellorIds.value);
    
    // 从原始数据中找出所有原始顾问的工号和ID对应关系
    const originalCounsellorWorkNumbers = new Set<string>();
    const workNumberToIdMap = new Map<string, number[]>();
    
    if (originalData) {
      // 从counsellorList中收集
      if (originalData.counsellorList) {
        originalData.counsellorList.forEach((counsellor: any) => {
          const username = counsellor.username || '';
          const id = counsellor.id;
          if (username && id) {
            originalCounsellorWorkNumbers.add(username);
            if (!workNumberToIdMap.has(username)) {
              workNumberToIdMap.set(username, []);
            }
            workNumberToIdMap.get(username)?.push(id);
          }
        });
      }
      
      // 从userList中收集顾问信息
      if (originalData.userList) {
        originalData.userList.forEach((user: any) => {
          if (user.role === 4) { // 顾问角色
            const username = user.username || '';
            const id = user.id;
            if (username && id) {
              originalCounsellorWorkNumbers.add(username);
              if (!workNumberToIdMap.has(username)) {
                workNumberToIdMap.set(username, []);
              }
              workNumberToIdMap.get(username)?.push(id);
            }
          }
        });
      }
    }
    
    console.log('原始顾问工号列表:', Array.from(originalCounsellorWorkNumbers));
    console.log('工号到ID的映射:', Object.fromEntries(workNumberToIdMap));
    
    // 找出被删除的顾问工号
    const deletedCounsellorWorkNumbers = Array.from(originalCounsellorWorkNumbers).filter(workNumber => {
      return !currentSelectedWorkNumbers.has(workNumber);
    });
    
    console.log('被删除的顾问工号:', deletedCounsellorWorkNumbers);
    
    // 将被删除顾问的ID添加到删除列表
    deletedCounsellorWorkNumbers.forEach(workNumber => {
      const ids = workNumberToIdMap.get(workNumber) || [];
      ids.forEach(id => {
        if (!finalUserDelIdList.includes(id)) {
          finalUserDelIdList.push(id);
        }
      });
    });

    // 获取当前选中的顾问列表，但排除原始列表中已有的顾问工号
    const newSelectedCounsellors = selectedCounsellors.value.filter(workNumber => {
      return !originalCounsellorWorkNumbers.has(workNumber);
    });

    // 将新增的顾问添加到userAddList
    newSelectedCounsellors.forEach(workNumber => {
      userAddList.push({
        username: workNumber,
        role: 4,
      });
    });

    // 创建要提交的数据对象
    const data = {
      id: productLine.value.id, // 确保编辑时id存在
      name: productLine.value.productName,
      description: productLine.value.description,
      userAddList: userAddList, // 使用只包含新增用户的列表
      userDelIdList: finalUserDelIdList, // 使用重新计算的删除列表
      processAddIdList: processAddIdList.value,
      processDelIdList: processDelIdList.value
    } as any;

    console.log('编辑模式 - 提交的数据:', data);
    console.log('最终删除的用户ID列表:', finalUserDelIdList);

    // 恢复API调用
    productLineApi.edit(data).then(() => {
      message.success(`编辑成功!`);
      currentRouter.value.push({ path: "/bidman/productLine/index" });
    }).catch((err) => {
      message.error(`编辑失败: ${err.message || '未知错误'}`);
      confirmLoading.value = false;
    });
  } else {
    // 新增模式 - 保持原有逻辑
    // 获取合并后的用户列表
    const { userList } = combineUserList();

    // 获取选中流程的ID列表
    const processIdList = productLine.value.processList?.map(flow => flow.flowId).filter(Boolean) || [];

    // 创建要提交的数据对象
    const data = {
      name: productLine.value.productName,
      description: productLine.value.description,
      userList,
      processIdList // 添加流程ID列表
    } as IProductLine;

    console.log('新增模式 - 提交的数据:', data);

    productLineApi.save(data).then(() => {
      message.success(`新增成功!`);
      currentRouter.value.push({ path: "/bidman/productLine/index" });
    }).catch((err) => {
      message.error(`新增失败: ${err.message || '未知错误'}`);
      confirmLoading.value = false;
    });
  }
};

// 处理管理员选择变更
const handleManagerChange = (users: any) => {
  console.log('管理员选择变更:', users);

  // 确保users是数组
  const userArray = Array.isArray(users) ? users : [users].filter(Boolean);

  // 创建新的管理员和昵称数组
  const newManagers: any[] = [];
  const newManagerUsernames: string[] = [];

  // 处理每个用户对象
  userArray.forEach(user => {
    // 如果用户是对象格式
    if (typeof user === 'object' && user !== null) {
      // 保留用户对象的所有原始属性
      const manager = { ...user };
      newManagers.push(manager);
      // 使用昵称
      const nickName = manager.nickName || '';
      newManagerUsernames.push(nickName);
    }
  });

  // 找出被删除的管理员
  const deletedManagers = selectedManagers.value.filter(manager => {
    return !newManagers.some(m => {
      if (manager.id && m.id) {
        return manager.id === m.id;
      }
      return manager.username === m.username;
    });
  });

  // 处理被删除的管理员
  deletedManagers.forEach(manager => {
    // 只有当管理员有ID且ID在原始列表中时，才添加到删除列表
    if (manager.id && originalManagerIds.value.includes(manager.id)) {
      const idToAdd = Number(manager.id);
      if (!userDelIdList.value.includes(idToAdd) && !isNaN(idToAdd)) {
        userDelIdList.value.push(idToAdd);
      }
    }
  });
  

  // 更新管理员列表
  selectedManagers.value = newManagers;
  selectedManagerUsernames.value = newManagerUsernames;
  childRef.value?.setFirstData(selectedManagers.value)
};

// 处理顾问选择变更
const handleCounsellorChange = (values: any) => {
  console.log(values,"values");
  console.log(selectedCounsellors.value,"selectedCounsellors");
  
  
  // 特殊处理：清空选择的情况
  if (!values || (Array.isArray(values) && values.length === 0)) {
    // 将所有原始顾问ID添加到删除列表中
    originalCounsellorIds.value.forEach(id => {
      const idToAdd = Number(id);
      if (!userDelIdList.value.includes(idToAdd) && !isNaN(idToAdd)) {
        userDelIdList.value.push(idToAdd);
      }
    });

    // 清空选择
    selectedCounsellors.value = [];
    return;
  }

  // 确保values是数组
  const valuesArray = Array.isArray(values) ? values : [values].filter(Boolean);
  console.log(valuesArray,"valuesArray");
  

  // 提取所有工号
  const newCounsellorUsernames: string[] = [];

  valuesArray.forEach(item => {
    if (typeof item === 'object' && item !== null) {
      // 如果是对象，提取username
      if (item.username) {
        newCounsellorUsernames.push(item.username);
      }
    } else if (typeof item === 'string') {
      // 如果是字符串，直接使用
      newCounsellorUsernames.push(item);
    }
  });
  console.log(newCounsellorUsernames,"newCounsellorUsernames");
  

  // 找出被删除的顾问工号
  const deletedCounsellors = selectedCounsellors.value.filter(username =>
    !newCounsellorUsernames.includes(username)
  );
  
  // 处理被删除的顾问
  deletedCounsellors.forEach(username => {
    console.log('正在处理删除的顾问工号:', username);
    
    // 方法1：从userOptions中查找
    const counsellor = userOptions.value.find(opt => opt.value === username);
    console.log('在userOptions中找到的顾问:', counsellor);

    if (counsellor && counsellor.id) {
      // 如果在原始ID列表中找到该ID，添加到删除列表
      if (originalCounsellorIds.value.includes(counsellor.id)) {
        const idToAdd = Number(counsellor.id);
        if (!userDelIdList.value.includes(idToAdd) && !isNaN(idToAdd)) {
          console.log('从userOptions添加到删除列表的ID:', idToAdd);
          userDelIdList.value.push(idToAdd);
        }
      } else {
        console.log('顾问ID不在原始列表中:', counsellor.id, '原始列表:', originalCounsellorIds.value);
      }
    } else {
      console.log('在userOptions中未找到顾问，尝试从原始数据映射中查找');
      
      // 方法2：从原始数据映射中获取ID
      const originalMap = (window as any).originalCounsellorMap;
      console.log('原始顾问映射:', originalMap);
      
      if (originalMap && originalMap[username]) {
        const ids = originalMap[username];
        console.log('找到对应的ID列表:', ids);
        
        if (Array.isArray(ids) && ids.length > 0) {
          // 将所有与该工号关联的ID添加到删除列表
          ids.forEach(id => {
            const idToAdd = Number(id);
            if (!userDelIdList.value.includes(idToAdd) && !isNaN(idToAdd)) {
              console.log('从原始映射添加到删除列表的ID:', idToAdd);
              userDelIdList.value.push(idToAdd);
            }
          });
        }
      } else {
        console.log('在原始映射中也未找到该工号的ID');
        
        // 方法3：直接从原始数据中查找
        const originalData = (window as any).originalData;
        if (originalData) {
          // 在counsellorList中查找
          if (originalData.counsellorList) {
            const foundCounsellor = originalData.counsellorList.find((c: any) => c.username === username);
            if (foundCounsellor && foundCounsellor.id) {
              const idToAdd = Number(foundCounsellor.id);
              if (!userDelIdList.value.includes(idToAdd) && !isNaN(idToAdd)) {
                console.log('从counsellorList添加到删除列表的ID:', idToAdd);
                userDelIdList.value.push(idToAdd);
              }
            }
          }
          
          // 在userList中查找
          if (originalData.userList) {
            const foundUser = originalData.userList.find((u: any) => u.username === username && u.role === 4);
            if (foundUser && foundUser.id) {
              const idToAdd = Number(foundUser.id);
              if (!userDelIdList.value.includes(idToAdd) && !isNaN(idToAdd)) {
                console.log('从userList添加到删除列表的ID:', idToAdd);
                userDelIdList.value.push(idToAdd);
              }
            }
          }
        }
      }
    }
    
    console.log('当前删除列表:', userDelIdList.value);
  });

  // 更新顾问工号列表
  selectedCounsellors.value = newCounsellorUsernames;

};

const handledeselect = (value: any) =>{
  console.log(value,"删除");
  
}

const goback = async ()=>{
  (await router).go(-1)
}

</script>

<template>
  <div class="edit-container">
    <div class="form-container">
      <h-form ref="from" :model="productLine" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" @finish="submitForm">
        <!-- 产品线名称 -->
        <h-form-item label="产品线名称:" required>
          <h-input v-model:value="productLine.productName" placeholder="请输入" />
        </h-form-item>

        <!-- 产品线管理员 -->
        <h-form-item label="产品线管理员:" required>
          <UserSelectPerson v-model:value="selectedManagerUsernames" :params="userSelectParams" :multiple="true"
            :max-tag-count="maxManagerCount" :max-count="maxManagerCount" placeholder="请选择产品线管理员" 
            
            @change="handleManagerChange" ref="childRef" />
        </h-form-item>

        <!-- 会议顾问 -->
        <h-form-item label="会议顾问:" required>
          <h-select v-model:value="selectedCounsellors" placeholder="请选择" mode="multiple" :filter-option="false"
            :show-search="true" allow-clear @search="handleSearch" @change="handleCounsellorChange" @deselect="handledeselect">
            <h-select-option v-for="option in userOptions" :key="option.value" :value="option.value">
              {{ option.label }}({{ option.value }})
            </h-select-option>
          </h-select>
        </h-form-item>

        <!-- 产品线简介 -->
        <h-form-item label="产品线简介:">
          <h-textarea v-model:value="productLine.description" :rows="4" placeholder="非必填，请输入产品线简介" />
        </h-form-item>

        <!-- 产品线流程 -->
        <h-form-item label="产品线流程:">
          <h-table :columns="flowColumns" :data-source="displayProcessList" rowKey="flowName" bordered>
            <template #bodyCell="{ column, index }">
              <template v-if="column.key === 'operation'">
                <template v-if="displayProcessList[index].isEmptyRow">
                  <a class="operation-link add-link" @click="addFlow()">添加</a>
                </template>
                <template v-else>
                  <a class="operation-link remove-link" @click="removeFlow(index)">移除</a>
                  <a v-if="index === (productLine.processList?.length || 0) - 1" class="operation-link add-link"
                    @click="addFlow()">添加</a>
                </template>
              </template>
            </template>
            <template #emptyText>
              <div class="empty-flow-container">
                <a class="operation-link add-link" @click="addFlow()">添加流程</a>
              </div>
            </template>
          </h-table>
        </h-form-item>

        <div class="submit-btn">
          <h-button type="primary" shape="round" html-type="submit" class="sub-btn">提交</h-button>
          <h-button shape="round" @click="goback" class="sub-btn" style="margin-left: 10px;">返回</h-button>
        </div>
      </h-form>
    </div>

    <!-- 流程选择弹框 -->
    <h-modal title="选择流程" :visible="flowModalVisible" @cancel="closeFlowModal" @ok="confirmFlowSelection" width="700px">
      <div class="flow-search">
        <h-input v-model:value="flowKeyword" placeholder="输入流程名称搜索"
          style="width: calc(100% - 88px); margin-right: 8px" />
        <h-button type="primary" @click="handleFlowSearch">搜索</h-button>
      </div>
      <div class="flow-list">
        <div v-for="flow in availableFlows" :key="flow.flowId" class="flow-item">
          <h-checkbox :value="flow.flowId" :checked="selectedFlows.some(f => f.flowId === flow.flowId)"
            @change="() => handleFlowSelect(flow)" :disabled="flow.disabled">
            {{ flow.flowName }}
            <span v-if="flow.flowType" class="flow-type">({{ flow.flowType }})</span>
          </h-checkbox>
        </div>
      </div>
    </h-modal>
  </div>
</template>

<style lang="less" scoped>
.edit-container {
  background-color: #ffff;
  height: 100%;
  width: 100%;
  padding: 10px 10px 0px 10px;
  overflow: auto;
}

.form-container {
  max-width: 900px;
  margin-left: 20px;
}

.submit-btn {
  width: 100%;
  display: flex;
  justify-content: center;
  padding-bottom: 20px;
}

.sub-btn {
  width: 200px;
}

.tip-text {
  color: #999;
  margin-left: 10px;
}

.operation-link {
  color: #1890ff;
}

.remove-link {
  margin-right: 10px;
}

.flow-list {
  max-height: 400px;
  overflow-y: auto;
}

.flow-item {
  margin-bottom: 8px;
  padding: 6px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.flow-item:hover {
  background-color: #f5f5f5;
}

.flow-type {
  color: #888;
  font-size: 12px;
  margin-left: 8px;
}

.empty-flow-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.selected-managers {
  margin-top: 10px;
  padding: 10px;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.selected-manager-item {
  margin-bottom: 5px;
}

.selected-counsellors {
  margin-top: 10px;
  padding: 10px;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.selected-counsellor-item {
  margin-bottom: 5px;
}

:deep(.ant-form-item-required::before) {
  display: none !important;
}
</style>
