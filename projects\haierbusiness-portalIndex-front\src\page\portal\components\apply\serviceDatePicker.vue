<script setup lang="ts">
import { onMounted, ref, watch,onUnmounted } from 'vue';
import dayjs, { Dayjs } from "dayjs";


interface Props {
    terminalDate?: string;
    planBeginDate?: string;
    planEndDate?: string;

}
const props = withDefaults(defineProps<Props>(), {
    terminalDate: '',
    planBeginDate:'',
    planEndDate:''

});
watch(props, (newValue) => {
    terminalDate.value = newValue.terminalDate
})

const terminalDate = ref(props.terminalDate)
const emit = defineEmits(['setTerminalDate'])

const setTerminalDate = (val) => {
    emit('setTerminalDate', val)
}

const disabledDate = (current: Dayjs) => {
  // Can not select days before today and today
  if(props.planBeginDate && props.planEndDate) {
    return (current && current < dayjs(props.planBeginDate).endOf('day')) || (current && current > dayjs(props.planEndDate).endOf('day'))
  }else {
    return false
  }
};

</script>

<template>
    <div class="apply-service-date-picker-component">
        <div class="ticket-item">
            <div class="item-row">
                <div class="item-start service-time">
                    <div class="item-labels">出发日期</div>
                    <a-date-picker valueFormat="YYYY-MM-DD" :disabled-date="disabledDate" v-model:value="terminalDate"  @change="setTerminalDate"  placeholder="请选择" :bordered="false" class="item-time">
                        <template #suffixIcon>  
                        </template>
                    </a-date-picker>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
@import url('./common.less');

.pointer {
    cursor: pointer;
}

.service-time {
    width: 100% !important;
}


</style>

<style>

</style>
