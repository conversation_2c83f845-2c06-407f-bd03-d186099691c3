// import {

// } from '@haierbusiness-front/common-libs'
import { get, post, errorHttpMessageHandle } from '../request'
import type{ CancelToken } from 'axios'

export const payPhoneApi = {
    /**
     * 生成付款码
     */
    generatePaymentCode: (): Promise<any> => {
        return get('pay/api/virtual/generatePaymentCode')
    },
    /**
     * 查询付款状态
     */
    searchPaymentCodePayStatus: (params: { paymentCode: string }, errorNotify: ((error: any) => any), cancelToken?: CancelToken): Promise<any> => {
        return post('pay/api/virtual/searchPaymentCodePayStatus', params, undefined, errorNotify, undefined, cancelToken)
    },
    /**
    * 付款码付款此接口由用户触发
    */
    paymentCodePay: (params: { paymentCode: string }): Promise<any> => {
        return post('pay/api/virtual/paymentCodePay', params)
    },
   /**
        * 获取电子钱包账户信息
        */
   getCoinAccount: (): Promise<any> => {
    return get('pay/api/coin/haier/account')
},
    /**
        * 获取个人福利账户信息
        */
    getPersonalWelfareAccount: (): Promise<any> => {
        return get('pay/api/virtual/account/getPersonalWelfareAccount', undefined, undefined,  (error) => {
            return errorHttpMessageHandle(error)
        })
    },
    /**
     * 充值
     */
    recharge: (params: { paymentCode: string }): Promise<any> => {
        return post('pay/api/virtual/account/recharge', params)
    },
    /**
     * 积分充值
     */
    coinRechargePrePay: (params: { amount: number, callbackUrl: string }): Promise<any> => {
        return post('pay/api/virtual/account/coinRechargePrePay', params)
    },
    /**
        * 账户流水合计
        */
    personalWelfareAccountFlowingWaterTotal: (params: any): Promise<any> => {
        return get('pay/api/virtual/account/personalWelfareAccountFlowingWaterTotal', params)
    },
    /**
        * 福利积分流水
        */
    getMyCoinFlowingWaterTotal: (params: any): Promise<any> => {
        return get('pay/api/coin/haier/getMyCoinFlowingWaterTotal', params)
    },
    /**
     * 账户流水
     */
    personalWelfareAccountList: (params: any): Promise<any> => {
        return get('pay/api/virtual/account/personalWelfareAccountFlowingWater', params)
    },
    /**
     * 查询代金券信息
     */
    getVoucherInfo: (params: any): Promise<any> => {
        return post('pay/api/virtual/voucher/getVoucherInfo', params)
    },

    /**
     * 查询福利积分流水
     */
    getMyCoinFlowingWater: (params: any): Promise<any> => {
        return get('pay/api/coin/haier/getMyCoinFlowingWater', params)
    },



}