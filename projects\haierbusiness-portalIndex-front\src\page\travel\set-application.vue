<template>
    <div class="body" >
      <div class="header">
        <div class="flex left"></div>
        <div class="flex title">设置应用</div>
        <div class="flex extra">
          <a v-if="isEdit" @click.prevent="handleComplete">完成</a>
          <a v-if="!isEdit" @click.prevent="handleEdit">编辑</a>
        </div>
      </div>
  
      <div class="cardContainer">
        <h-row >
          <h-col class="myAppHeader">我的应用</h-col>
          <draggable :list="myApps" animation="300" touchStartThreshold="1000" :itemKey="(item) => item.id" class="myAppContainer" :disabled="!isEdit" chosen-class="chosenClass">
            <template #item="{ element }">
              <div class="item itemBottom" :key="element.id">
                <div class="poAll" @click="gotoUrl(element.jumpLinkPc, element.jumpLinkApp)"></div>
                <div class="appImageCon">
                  <div class="imgCon">
                    <img class="appImage" :src="element.iconUrl"/>
                    <img v-if="isEdit" class="operation" @click="deleteApp(element.id)" :src="deleteUrl"/>
                  </div>
                </div>
                <p class="overText">{{element.menuName}}</p>
              </div>
            </template>
          </draggable>
        </h-row>
      </div>
  
      <div class="cardBusiness">
        <h-row >
          <h-col class="myAppHeader">商务服务</h-col>
          <h-col class="myAppContainer">
            <div class="item itemBottom" v-for="(item, index) in apps" :key="index">
              <div class="poAll" @click="gotoUrl(item.jumpLinkPc, item.jumpLinkApp)"></div>
              <div class="appImageCon">
                <div class="imgCon">
                  <img class="appImage"  :src="item.iconUrl"/>
                  <img v-if="isEdit && isNotContain(item.id)" class="operation" @click="addApp(item)" :src="addUrl"/>
                </div>
              </div>
              <p class="overText">{{item.menuName}}</p>
            </div>
          </h-col>
        </h-row>
      </div>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { computed, onMounted, ref } from "vue"
  import { applicationListApi } from '@haierbusiness-front/apis';
  import {
    IApplicationAccount, AssignUserMenuType
  } from '@haierbusiness-front/common-libs';
  import { message, Row as hRow, Col as hCol } from 'ant-design-vue'
  import addUrl from '@/assets/application/add.png'
  import deleteUrl from '@/assets/application/delete.png'
  import { remove, findIndex } from "lodash-es"
  import draggable from "vuedraggable"
  import { storeToRefs } from 'pinia';
  import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
  import globalPinia from "@haierbusiness-front/utils/src/store/store"

  
  const apps = ref([] as Array<IApplicationAccount>)
  const myApps = ref([] as Array<IApplicationAccount>)
  const isEdit = ref(false)
  const isSubmit = ref(false)
  const { loginUser } = storeToRefs(applicationStore(globalPinia))
  
  // const user = ref({
  //   userId: '',
  //   userName: ''
  // })
  
  const handleEdit = () => {
    isEdit.value = true
  }
  
  const queryData = () => {
    getUserInfo()
  }
  
  const handleComplete = () => {
    const client = isMobile() ? 'MOBILE' : 'PC'
    if (isSubmit.value === true)
      return
    isSubmit.value = true
    let menuIds: number[] = []
    myApps.value.map(item => {
      if (item.id)
        menuIds = [...menuIds, item.id]
    })
  
    const userMenu: AssignUserMenuType = {
      userId: loginUser.value?.username!,
      userName: loginUser.value?.nickName!,
      menuIds,
      client
    }
    applicationListApi.assignUserMenu(userMenu).then(res => {
      isEdit.value = false
      isSubmit.value = false
    })
  
  }
  
  const isNotContain = computed(() => (id: number) => {
    const index = findIndex(myApps.value, (o) => { return o.id === id })
    return index === -1 ? true : false
  })
  
  const isMobile = () => {
    let flag = navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)
    return flag;
  }
  
  const gotoUrl = (pc: string, app: string) => {
    return;
    // 暂时不让跳转
    // if (isEdit.value)
    //   return
    //
    // if (isMobile()){
    //   // 手机端
    //   window.location.href = app
    // }else{
    //   // 电脑端
    //   window.location.href = pc
    // }
  }
  
  const addApp = (item: IApplicationAccount) => {
    myApps.value = [
        ...myApps.value,
        item
    ]
  }
  
  const deleteApp = (id: number) => {
    remove(myApps.value, function (o: IApplicationAccount) {
      return o.id === id
    })
  }
  
  const fetchApps = (client: string) => {
    applicationListApi.allList(client).then(res => {
      apps.value = res
    })
  }
  
  const fetchUserApps = (client: string) => {
    applicationListApi.userApps(loginUser.value?.username, client).then(res => {
      myApps.value = res
    })
  }
  
  const getUserInfo = () => {
    const client = isMobile() ? 'MOBILE' : 'PC'
    fetchApps(client)
    fetchUserApps(client)
  }
  
  onMounted(getUserInfo)
  
  </script>
  
  <style lang="less" scoped>
    .body {
      background-color: #f5f5f5;
      min-height: 100vh;
    }
  
    .header {
      display: flex;
      flex: 1;
      justify-content: space-between;
      background-color: #ffffff;
      height: 36px;
      align-items: center;
  
      .flex {
        display: flex;
      }
  
      .title {
        font-size: 15px;
        font-weight: 600;
        color: rgba(0,0,0,.85);
      }
  
      .left {
        padding-left: 12px;
      }
  
      .extra {
        padding-right: 12px;
      }
    }
  
    .cardContainer, .cardBusiness  {
      margin: 8px 12px;
      position: relative;
      background-color: #ffffff;
      border-radius: 4px;
    }
  
    .myAppHeader {
      height: 36px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      padding-left: 12px;
      display: flex;
      width: 100%;
      align-items: center;
    }
  
    .myAppContainer{
      display: flex;
      justify-content: flex-start;
      align-content: flex-start;
      flex-wrap: wrap;
      width: 100%;
      overflow: hidden;
  
  
      .item {
        width: 25%;
        height: 73px;
        display: flex;
        //justify-content: space-between;
        align-content: center;
        flex-direction: column;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 12px;
        position: relative;
  
        p {
          margin: 7px 0 0px ;
          font-size: 12px;
          height: 14px;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 14px;
          text-align: center;
          overflow: hidden;
          white-space: nowrap;
          word-wrap: break-word;
          text-overflow: ellipsis;
        }
      }
  
      .itemBottom {
        margin-bottom: 0px;
      }
  
      .poAll{
        position: absolute;
        width: 100%;
        height: 100%;
        opacity: 0;
        z-index: 100000;
      }
  
      .appImageCon {
        display: flex;
        flex-direction: column;
        position: relative;
        width: 100%;
        align-content: center;
        align-items: center;
      }
  
      .appImage {
        width: 36px;
        height: 36px;
        border-radius: 6px;
        display: flex;
      }
  
      .imgCon {
        position: relative;
        width: 36px;
        height: 36px;
      }
  
      .operation {
        position: absolute;
        z-index: 10;
        width: 18px;
        height: 18px;
        top: -9px;
        right: -9px;
      }
  
      .overText {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  
    .chosenClass {
      opacity: 0;
    }
  </style>
  