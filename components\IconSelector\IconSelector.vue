<template>
  <a-popover trigger="click">
    <h-input
      placeholder="请选择图标"
      v-model:value="iconName"
      allow-clear
      @blur="handleBlur"
    >
      <template #prefix>
        <template v-if="iconName">
          <AntIcon v-if="iconName" :size="16" :name="iconName" />
        </template>
        <SearchOutlined v-else />
      </template>
    </h-input>

    <template #content>
      <div class="container" :class="{ 'is-list': !isGridView }">
        <!-- 搜索栏和视图切换按钮 -->
        <a-row>
          <section style="flex: 1; margin-right: 8px">
            <a-form-item-rest>
              <h-input
                v-model:value="searchValue"
                placeholder="搜索图标名称"
                allow-clear
                size="small"
                @change="search"
              >
                <template #prefix>
                  <SearchOutlined />
                </template>
              </h-input>
            </a-form-item-rest>
          </section>

          <a-button size="small" @click="isGridView = !isGridView">
            <template #icon>
              <AppstoreOutlined v-if="isGridView" />
              <UnorderedListOutlined v-else />
            </template>
          </a-button>
        </a-row>

        <!-- 图标列表 -->
        <section class="icon-list">
          <a-row wrap :gutter="4">
            <a-col
              v-for="item of currentPageIconList"
              :key="item"
              :span="isGridView ? 4 : 8"
            >
              <div
                class="icon-item"
                :class="{ active: iconName === item }"
                @click="handleSelectedIcon(item)"
              >
                <AntIcon :name="item" :size="20"></AntIcon>
                <div class="icon-name">{{ item }}</div>
              </div>
            </a-col>
          </a-row>
        </section>

        <!-- 分页 -->
        <a-row justify="center" align="center">
          <a-pagination
            size="small"
            :page-size="pageSize"
            :total="total"
            :show-size-changer="false"
            @change="onPageChange"
          ></a-pagination>
        </a-row>
      </div>
    </template>
  </a-popover>
</template>
  
<script lang="ts" setup>
  import { useClipboard } from '@vueuse/core';
  import { message } from 'ant-design-vue';
  import { ref, watch } from 'vue';
  import { Input as hInput } from 'ant-design-vue';
  import * as Icons from "@ant-design/icons-vue";
  import { SearchOutlined, AppstoreOutlined, UnorderedListOutlined } from '@ant-design/icons-vue';
  import AntIcon from './AntIcon.vue';
  
  defineOptions({ name: 'IconSelector' });

  interface Props {
    iconName?: string | undefined;
    enableCopy?: boolean;
  }
  
  const props = withDefaults(defineProps<Props>(), {
    iconName: '',
    enableCopy: false,
  });

  const emit = defineEmits(['select', 'update:iconName']);

  const iconName = ref(props.iconName || '');

  // 监听 props 变化
  watch(() => props.iconName, (newVal) => {
    iconName.value = newVal || '';
  });

  watch(iconName, (newVal) => {
    emit('update:iconName', newVal);
  });

  const searchValue = ref(''); // 搜索词 

  // 图标列表
  const isGridView = ref(false);
  
  const iconList = Object.keys(Icons).filter(
    name =>
      name.endsWith("Outlined") ||
      name.endsWith("Filled") ||
      name.endsWith("TwoTone")
  );

  const pageSize = 42;
  const current = ref(1);
  const total = ref(iconList.length); // 图标总数

  // 当前页的图标列表
  const currentPageIconList = ref(iconList.slice(0, pageSize));
  // 搜索列表
  const searchList = ref<string[]>([]);

  // 页码改变
  const onPageChange = (page: number) => {
    current.value = page;
    if (!searchList.value.length) {
      currentPageIconList.value = iconList.slice(
        (page - 1) * pageSize,
        page * pageSize
      );
    } else {
      currentPageIconList.value = searchList.value.slice(
        (page - 1) * pageSize,
        page * pageSize
      );
    }
  };

  // 搜索
  const search = () => {
    if (searchValue.value) {
      const temp = searchValue.value.toLowerCase();
      searchList.value = iconList.filter((item) => {
        return item.toLowerCase().includes(temp);
      });
      total.value = searchList.value.length;
      currentPageIconList.value = searchList.value.slice(0, pageSize);
    } else {
      searchList.value = [];
      total.value = iconList.length;
      currentPageIconList.value = iconList.slice(
        (current.value - 1) * pageSize,
        current.value * pageSize
      );
    }
  };

  // 点击选择图标
  const handleSelectedIcon = async (icon: string) => {
    emit('select', icon);
    emit('update:iconName', icon);
    if (props.enableCopy) {
      const { isSupported, copied, copy } = useClipboard();
      if (isSupported) {
        await copy(`<${icon} />`);
        if (copied) {
          message.success(`已选择并且复制成功 ${icon} 图标`);
        }
      }
    }
  };

  // 失去焦点时验证图标名称
  const handleBlur = () => {
    if (iconName.value && !iconList.includes(iconName.value)) {
      iconName.value = '';
    }
  };
</script>
  
<style lang="less" scoped>
  .container {
    width: 350px;
    overflow: hidden;
    .icon-list {
      margin-top: 10px;
      margin-bottom: 10px;
      .icon-item {
        height: 30px;
        margin-bottom: 4px;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        border: 1px dashed var(--color-bg-1);
        .icon-name {
          display: none;
        }
        &.active {
          border: 1px dashed rgb(var(--primary-3));
          background-color: rgba(var(--primary-6), 0.05);
        }

        &:not(.active) {
          &:hover {
            border-color: var(--color-border-3);
          }
        }
      }
    }
  }

  .is-list {
    min-width: 500px;
    .icon-list {
      height: 300px;
      overflow: hidden;
      overflow-y: auto;
      .icon-item {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        padding-left: 4px;
        box-sizing: border-box;
        .icon-name {
          margin-left: 6px;
          font-size: 12px;
          color: var(--color-text-2);
          display: block;
        }
      }
    }
  }
</style>