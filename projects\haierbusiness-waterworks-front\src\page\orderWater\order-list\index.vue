<script setup lang="ts">
import { Empty, Button as hButton, Input as hInput, message } from 'ant-design-vue';
import { computed, onMounted, ref } from 'vue';
import { SearchOutlined, DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
const router = useRouter();
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE; // 空状态

// 订单状态映射
const orderStatusMap = {
  10: '待配送',
  20: '待收货',
  30: '待评价',
};

const orderList = ref<Record<string, any>[]>([
  {
    id: 1,
    name: '5加仑',
    price: 35,
    count: 1,
    status: '10',
    deliveryCount: 91,
    orderTime: '2025-02-24 12:12:54',
    applyNo: 'WT51132489580',
    applyDepartment: '商旅小微',
    settlementCompany: '青岛国际旅行社有限公司',
    budgetAmount: 1000,
    paymentState: '10',
    paymentNo: 'PAY20250417163825580510',
  },
]);

/**
 * 筛选相关 -- 顶部筛选
 * */
const activeKey = ref<string>(''); // 筛选
const searchValue = ref<string>(''); // 搜索框

/**
 * 展开相关逻辑
 * */
const allOpen = ref(true);

// 全部展开/收起
const handleAllOpenControl = (open: boolean) => {
  allOpen.value = open;
  orderList.value.forEach((item) => {
    item.isOpen = open;
  });
};

/**
 * 分页相关逻辑
 * */
const current1 = ref(1);
const total = ref(1);
const handleTableChange = (page: number, pageSize: number) => {
  console.log(page, pageSize);
};

/**
 * 表单操作按钮相关
 * */
const handleCancelOrder = (orderId: number) => {
  message.success('取消订单' + orderId);
  console.log('取消订单', orderId);
};
const handleReceiveOrder = (orderId: number) => {
  message.success('收货' + orderId);
  console.log('收货', orderId);
};
const handleEvaluateOrder = (orderId: number) => {
  message.success('去评价' + orderId);
  console.log('去评价', orderId);
};
const handleViewOrder = (orderId: number) => {
  message.success('查看订单' + orderId);
  console.log('查看订单', orderId);
};

/**
 * 初始化逻辑
 * */
// 临时测试用 -- 待删
const tempData = [
  {
    id: 1,
    orderList: [
      {
        name: '5加仑',
        price: 35,
        count: 1,
        deliveryCount: 91,
        budgetAmount: 1000,
        paymentState: '10',
      },
    ],
    status: '10',
    orderTime: '2025-02-24 12:12:54',
    applyNo: 'WT51132489580',
    applyDepartment: '商旅小微',
    settlementCompany: '青岛国际旅行社有限公司',
    paymentNo: 'PAY20250417163825580510',
  },
  {
    id: 2,
    orderList: [
      {
        name: '330ml瓶装水',
        price: 215,
        count: 14,
        deliveryCount: 91,
        budgetAmount: 1000,
        paymentState: '10',
      },
      {
        name: '330ml瓶装水',
        price: 25,
        count: 13,
        deliveryCount: 91,
        budgetAmount: 1000,
        paymentState: '10',
      },
      {
        name: '330ml瓶装水',
        price: 25,
        count: 12,
        deliveryCount: 91,
        budgetAmount: 1000,
        paymentState: '10',
      },
    ],
    status: '20',
    orderTime: '2025-02-24 12:12:54',
    applyNo: 'WT51132489580',
    applyDepartment: '商旅小微',
    settlementCompany: '青岛国际旅行社有限公司',
    paymentNo: 'PAY20250417163825580510',
  },
  {
    id: 3,
    orderList: [
      {
        name: '2加仑',
        price: 15,
        count: 1,
        deliveryCount: 91,
        budgetAmount: 1000,
        paymentState: '10',
      },
      {
        name: '5加仑',
        price: 55,
        count: 2,
        deliveryCount: 91,
        budgetAmount: 100,
        paymentState: '10',
      },
    ],
    status: '30',
    orderTime: '2025-06-1 8:30:54',
    applyNo: 'WT51132489580',
    applyDepartment: '集团IT集团IT集团IT',
    settlementCompany: '青岛国际国际旅行社有限公司',
    paymentNo: 'PAY20250417163825580510',
  },
];

const handleSearch = (page?: number, pageSize?: number) => {
  const condition = {
    page: page || 1,
    pageSize: pageSize || 10,
    keyWord: searchValue.value,
  };
  console.log('搜索条件', condition);
  total.value = 500;
  // 通过添加isOpen属性来控制展开状态
  orderList.value = tempData.map((item) => ({
    ...item,
    isOpen: true,
  }));
};

onMounted(() => {
  handleSearch();
});
</script>

<template>
  <div class="transfer-order">
    <a-breadcrumb class="breadcrumb">
      <a-breadcrumb-item @click="router.push('/')"><a>首页</a></a-breadcrumb-item>
      <a-breadcrumb-item><a>个人中心</a></a-breadcrumb-item>
      <a-breadcrumb-item>我的订单</a-breadcrumb-item>
    </a-breadcrumb>

    <div class="top-control">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="" tab="全部"></a-tab-pane>
        <a-tab-pane key="10" tab="待送水"></a-tab-pane>
        <a-tab-pane key="20" tab="待付款"></a-tab-pane>
        <a-tab-pane key="30" tab="已付款"></a-tab-pane>
      </a-tabs>
      <div>
        <h-input
          class="search-input"
          v-model:value="searchValue"
          @keydown.enter="() => handleSearch()"
          @blur="() => handleSearch()"
          placeholder="请输入订单号/商品名称搜索"
        >
          <template #suffix>
            <search-outlined style="color: rgba(0, 0, 0, 0.45)" />
          </template>
        </h-input>
      </div>
    </div>

    <div class="table">
      <div class="tr tr-border" style="border: 1px solid #8cbbec">
        <div class="th flex-2" style="justify-content: center">商品信息</div>
        <div class="th flex-1">单价（元）</div>
        <div class="th flex-1">申请数量</div>
        <div class="th flex-1">送水数量</div>
        <div class="th flex-1">已配送数量</div>
        <div class="th flex-1">付款金额（元）</div>
        <div class="th flex-1" style="justify-content: center">订单状态</div>
        <div class="th flex-1" style="justify-content: center">
          <a-popover trigger="hover" placement="bottom">
            <template #content>
              <div class="popover-item" @click="handleAllOpenControl(true)">全部展开</div>
              <div class="popover-item" @click="handleAllOpenControl(false)">全部收起</div>
            </template>
            <span style="cursor: pointer">
              操作
              <up-outlined v-if="allOpen" />
              <down-outlined v-else />
            </span>
          </a-popover>
        </div>
      </div>

      <!-- 商品表格 -->
      <template v-if="orderList.length > 0">
        <div class="row" v-for="(item, index) in orderList" :key="index">
          <!-- 头部详情 -->
          <div class="tr tr-border" style="width: 100%">
            <div class="td flex-auto table-item-header" style="width: 138px" :title="item.orderTime">
              {{ item.orderTime }}
            </div>
            <div class="td flex-auto table-item-header" :title="item.applyNo">
              <span>申请单：</span>
              <div class="ellipsis">{{ item.applyNo }}</div>
            </div>
            <div class="td flex-auto table-item-header" :title="item.applyDepartment">
              <span>申请部门：</span>
              <div class="ellipsis">{{ item.applyDepartment }}</div>
            </div>
            <div class="td flex-auto table-item-header" :title="item.settlementCompany">
              <span>结算公司：</span>
              <div class="ellipsis">{{ item.settlementCompany }}</div>
            </div>
            <div class="td flex-auto table-item-header" :title="item.budgetAmount">
              <span>预算金额：</span>
              <div class="ellipsis">{{ item.budgetAmount }}</div>
            </div>
            <div
              class="td flex-auto table-item-header"
              :title="orderStatusMap[item.paymentState as unknown as keyof typeof orderStatusMap]"
            >
              <span>订单状态：</span>
              <div class="ellipsis">
                {{ orderStatusMap[item.paymentState as unknown as keyof typeof orderStatusMap] }}
              </div>
            </div>
            <div class="td flex-auto table-item-header" :title="item.paymentNo">
              <span>支付单号：</span>
              <div class="ellipsis">{{ item.paymentNo }}</div>
            </div>
            <div class="td flex-auto table-item-header">
              <span style="color: #2a82db; cursor: pointer" @click="item.isOpen = !item.isOpen">
                <h-button type="link" size="small">
                  <component :is="item?.isOpen ? UpOutlined : DownOutlined" style="color: #2a82db" />
                  {{ item?.isOpen ? '收起' : '展开' }}
                </h-button>
              </span>
            </div>
          </div>
          <!-- 商品内容 -->
          <div class="tr" v-if="item.isOpen">
            <!-- 占据7个单位 -->
            <div style="flex: 7; display: flex; flex-direction: column; gap: 12px">
              <div v-for="(_el, index) in item.orderList" :key="index" class="border-bottom" style="display: flex">
                <!-- 商品信息 -->
                <div class="td flex-2">
                  <img src="../../../assets/image/demo/yszy.png" alt="" />
                  <span style="margin-left: 16px">{{ _el.name }}</span>
                </div>
                <!-- 单价 -->
                <div class="td flex-1">{{ _el.price }}</div>
                <!-- 申请数量 -->
                <div class="td flex-1">{{ _el.count }}</div>
                <!-- 送水数量 -->
                <div class="td flex-1 text-blue">￥ {{ _el.price * _el.count }}</div>
                <!-- 已配送数量 -->
                <div class="td flex-1">{{ _el.deliveryCount }}</div>
                <!-- 付款金额 -->
                <div class="td flex-1 text-blue">￥ {{ _el.price * _el.count }}</div>
              </div>
            </div>
            <!-- 订单状态 -->
            <div class="td flex-1 border-left" style="justify-content: center">
              {{ orderStatusMap[item.status as unknown as keyof typeof orderStatusMap] }}
            </div>
            <!-- 操作 -->
            <div class="td flex-1 border-left" style="display: flex; flex-direction: column; justify-content: center">
              <template v-if="item.status === '10'">
                <h-button type="primary" @click="handleCancelOrder(item.id)">付款</h-button>
                <h-button type="link" @click="handleCancelOrder(item.id)">取消订单</h-button>
              </template>
              <template v-else-if="item.status === '20'">
                <h-button type="primary" @click="handleReceiveOrder(item.id)">申请送水</h-button>
                <h-button type="link" @click="handleViewOrder(item.id)">再次购买</h-button>
                <h-button type="link" @click="handleViewOrder(item.id)">查看订单</h-button>
              </template>
              <template v-else-if="item.status === '30'">
                <!-- <h-button @click="handleEvaluateOrder(item.id)">去评价</h-button> -->
              </template>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <a-empty :image="simpleImage" />
      </template>

      <!-- 分页 -->
      <div v-if="orderList.length > 0" style="margin-bottom: 24px">
        <a-pagination v-model:current="current1" show-quick-jumper :total="total" @change="handleTableChange" />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.transfer-order {
  width: 100%;
  height: 100%;

  .breadcrumb {
    margin-bottom: 16px;
    :deep(.a-breadcrumb-item) {
      font-size: 14px;
    }
  }
  .top-control {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    .search-input {
      width: 320px;
    }
    // 去除底部外边距
    :deep(.ant-tabs-nav) {
      margin-bottom: 0;
    }
    // 隐藏tabs底部的边
    :deep(.ant-tabs-nav::before) {
      border-bottom: none !important;
    }
  }

  .table {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
    .tr-border {
      background-color: #f2f9ff;
    }
    .table-item-header {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 12px;
      span {
        color: #909399;
      }
      .ellipsis {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .table-item-header:last-child {
      justify-content: right;
    }

    .row {
      width: 100%;
      display: flex;
      flex-direction: column;
      border: 1px solid #8cbbec;
    }
    .tr {
      width: 100%;
      display: flex;
      padding: 12px;
    }

    .th {
      display: flex;
      align-items: center;
      font-size: 14px;
    }
    .td {
      display: flex;
      align-items: center;

      font-size: 13px;
      img {
        width: 100px;
        height: 100px;
        object-fit: cover;
      }
    }
    .border-left {
      border-left: 1px solid #eef2f7;
    }
    .border-bottom {
      padding-bottom: 12px;
      border-bottom: 1px solid #eef2f7;
      &:last-child {
        padding-bottom: 0;
        border-bottom: none;
      }
    }
  }
}
.popover-item {
  padding: 4px 8px;
  cursor: pointer;
  &:hover {
    border-radius: 4px;
    color: #2a82db;
    background-color: #eaf3fb;
  }
}

.text-blue {
  color: #2a82db;
}
.flex-auto {
  flex: auto;
}
.flex-1 {
  flex: 1;
}
.flex-2 {
  flex: 2;
}
.flex-3 {
  flex: 3;
}
.center {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
