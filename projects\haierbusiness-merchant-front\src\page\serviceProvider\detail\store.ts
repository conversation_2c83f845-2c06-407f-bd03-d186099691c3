import { defineStore } from "pinia"
import { serviceProviderApi } from '@haierbusiness-front/apis/src/merchant/serviceProvider';

import { MerchantBusiness, MerchantContract, MerchantBank } from "@haierbusiness-front/common-libs";
import { message } from "ant-design-vue";

interface State {
  businessId: string,
  editContractModalOpen: boolean,
  editBankModalOpen: boolean,
  contractList: MerchantContract[],
  bankList: MerchantBank[]
  businessDetail: MerchantBusiness,
  contractDetail: MerchantContract,
  bankDetail: MerchantBank,
}
export const useServiceProviderDetailStore = defineStore('serviceProviderDetail', {
  state: (): State => ({
    businessId: '',
    editContractModalOpen: false,
    editBankModalOpen: false,
    contractList: [],
    bankList: [],
    businessDetail: {},
    contractDetail: {
      contractNo: '',
      contractCode: '',
      contractDate: [],
      signDate: void 0,
      state: false,
      contractUrl: '',
    },
    bankDetail: {
      accountNumber: '',
      bankBranchCode: '',
      bankBranchAddress: '',
      accountHolderName: '',
      bankCountry: '',
    },
  }),
  getters: {},
  actions: {
    async getBusinessDetail(params = {}) {
      try {
        const res = await serviceProviderApi.getBusinessDetail(params);
        this.businessDetail = res;
      } catch (e) {
        console.log(e)
      }
    },
    async getContractList(params = {}) {
      try {
        const res = await serviceProviderApi.getContractList(params);
        this.contractList = res;
      } catch (e) {
        console.log(e)
      }
    },
    async getBankList(params = {}) {
      try {
        const res = await serviceProviderApi.getBankList(params);
        this.bankList = res;
      } catch (e) {
        console.log(e)
      }
    },
    async addContract(params = {}) {
      try {
        await serviceProviderApi.addContract(params);
        message.success('新增成功')
      } catch (e) {
        console.log(e)
      }
    },
    async editContract(params = {}) {
      try {
        await serviceProviderApi.editContract(params);
        message.success('编辑成功')
      } catch (e) {
        console.log(e)
      }
    },
    async deleteContract(params = {}) {
      try {
        await serviceProviderApi.deleteContract(params);
        message.success('删除成功')
      } catch (e) {
        console.log(e)
      }
    },
    async addBank(params = {}) {
      try {
        await serviceProviderApi.addBank(params);
        message.success('新增成功')
      } catch (e) {
        console.log(e)
      }
    },
    async editBank(params = {}) {
      try {
        await serviceProviderApi.editBank(params);
        message.success('编辑成功')
      } catch (e) {
        console.log(e)
      }
    },
    async deleteBank(params = {}) {
      try {
        await serviceProviderApi.deleteBank(params);
        message.success('删除成功')
      } catch (e) {
        console.log(e)
      }
    },
  },
})