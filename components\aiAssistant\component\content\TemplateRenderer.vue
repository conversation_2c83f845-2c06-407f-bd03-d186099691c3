<template>
  <div class="template-renderer">
    <div class="template-hint">
      <ExclamationCircleOutlined style="color: #ffaa00" /> &nbsp;&nbsp;由于您的需求不够明确，经过AI模型思考给出如下细化方案，请选择参数后继续发送
    </div>
    <div class="template-message">
      <div v-for="(part, index) in parsedTemplate" :key="index" class="template-part">
        <template v-if="part.type === 'text'">
          {{ part.value }}
        </template>

        <template v-else-if="part.type === 'param'">

          <a-select
              v-if="hasParamOptions(part.index)"
              mode="multiple"
              v-model:value="selectedValues[part.index]"
              style="text-align: center; background-color: #e6f7ff"
              :style="{ width:  getTextWidth(getParamName(part.index)) + 'px' }"
              :options="getParamOptions(part.index)"
              :placeholder="`[${getParamName(part.index)}]∨`"
              :suffix-icon="false"
          >
          </a-select>

          <a-input
              v-else
              v-model:value="customValues[part.index]" style="width: 120px"
              placeholder="请输入"
          />
        </template>
      </div>
    </div>
    <!-- 新增的发送按钮 -->
    <div class="send-button-container">
      <a-button
          type="primary"
          style="border-radius: 6px "
          @click="sendTemplateMessage"
      >
        确认并继续发送
      </a-button>

      <a-button
          style="border-radius: 6px "
          @click="sendTemplateMessage"
      >
        保持原请求
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, ref} from 'vue';
import {IUserMessageTemplateResult} from "@haierbusiness-front/common-libs/src/ai";
import {ExclamationCircleOutlined} from '@ant-design/icons-vue';

const props = defineProps<{
  userMessageTemplateResult: IUserMessageTemplateResult;
}>();

// 在 script 部分添加 emit
const emit = defineEmits(['send-template-message']);

function getTextWidth(text: string): number {
  return text.length * 16 + 40;
}


// 获取参数名称
const getParamName = (index: string) => {
  const idx = parseInt(index) - 1;
  return props.userMessageTemplateResult.params?.[idx]?.name || '';
};

// 在 finalResult 计算属性后添加方法
const sendTemplateMessage = () => {
  const result = finalResult.value;
  if (result.trim()) {
    emit('send-template-message', result);
  }
};

const parsedTemplate = computed(() => {
  const regex = /(#param_(\d+))/g;
  const parts = [];
  let lastIndex = 0;

  let match;
  while ((match = regex.exec(props.userMessageTemplateResult.messageTemplate)) !== null) {
    // 添加普通文本部分
    if (match.index > lastIndex) {
      parts.push({
        type: 'text',
        value: props.userMessageTemplateResult.messageTemplate.slice(lastIndex, match.index)
      });
    }

    // 添加参数部分
    const paramIndex = match[2]; // 获取参数编号
    parts.push({
      type: 'param',
      index: paramIndex,
      key: props.userMessageTemplateResult.params?.[parseInt(paramIndex) - 1]?.key,
      values: props.userMessageTemplateResult.params?.[parseInt(paramIndex) - 1]?.values
    });

    lastIndex = match.index + match[0].length;
  }

  // 添加最后剩余的文本
  if (lastIndex < props.userMessageTemplateResult.messageTemplate.length) {
    parts.push({
      type: 'text',
      value: props.userMessageTemplateResult.messageTemplate.slice(lastIndex)
    });
  }

  return parts;
});

// 检查是否存在参数选项
const hasParamOptions = (index: string) => {
  const paramIndex = parseInt(index) - 1;
  return props.userMessageTemplateResult.params?.[paramIndex]?.values?.length > 0;
};

// 获取参数选项
const getParamOptions = (index: string) => {
  const paramIndex = parseInt(index) - 1;
  const params = props.userMessageTemplateResult.params?.[paramIndex];

  if (!params?.values) {
    return [];
  }

  return params.values.map(value => ({
    value: value.valueDesc || '',
    label: value.valueDesc || value.value
  }));
};

// 存储用户选择的值
const selectedValues = ref<Record<number, string[]>>({});
const customValues = ref<Record<number, string>>({});

// 生成最终结果
const finalResult = computed(() => {
  let result = props.userMessageTemplateResult.messageTemplate;

  // 替换选择的值
  Object.entries(selectedValues.value).forEach(([index, values]) => {
    const placeholder = `#param_${parseInt(index)}`;
    result = result.replace(new RegExp(placeholder, 'g'), values.join(','));
  });

  // 替换自定义值
  Object.entries(customValues.value).forEach(([index, value]) => {
    const placeholder = `#param_${parseInt(index)}`;
    result = result.replace(new RegExp(placeholder, 'g'), value);
  });

  // 替换剩余的 #param_n 占位符
  const remainingParamRegex = /#param_\d+/g;
  result = result.replace(remainingParamRegex, '不限制');
  return result;
});

</script>

<style scoped>
.send-button-container {
  display: flex;
  justify-content: flex-start;
  gap: 12px;
  width: 100%;
  padding-left: 16px;
}

.template-renderer {
  margin-left: 2px;
  border-left: 3px solid #1890ff;
  width: 100%;
  display: inline-block;
  line-height: 1.5;
}

.template-part {
  white-space: pre-wrap;
  word-break: break-word;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

:deep(.ant-select-selection-item) {
  border-radius: 2px;
  background-color: white;
}

:deep(.ant-select-selector) {
  border: none !important;
  background-color: transparent !important;
  padding-left: 6px !important;
  padding-right: 6px !important;
}

.select-prefix,
.select-suffix {
  color: #1890FF;
  font-weight: bold;
}

:deep(.ant-select-selection-placeholder) {
  color: #1890FF !important;
}

:deep(.ant-select-selection-item) {
  padding-left: 4px !important;
  padding-right: 4px !important;
}

.template-hint {
  background-color: #f5f5f5;
  padding: 12px 16px;
  border-radius: 4px;
  font-size: 14px;
  color: #595959;
  line-height: 1.2;
}

.template-message {
  margin-top: 10px;
  margin-bottom: 10px;
  padding: 12px 16px;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.6;
}

</style>
