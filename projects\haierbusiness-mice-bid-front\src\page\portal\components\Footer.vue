<script setup>
import { ref } from 'vue';
</script>

<template>
  <div class="footer-container">
    <div class="footer-content">
      <div class="footer-top">
        <ul class="footer">
          <li class="title">关于我们</li>
          <li><a href="https://businesstravel-test.haier.net/#/about">成长历程</a></li>
          <li><a href="javascript:;">企业赋能</a></li>
          <li><a href="javascript:;">服务优势</a></li>
          <li><a href="https://businesstravel-test.haier.net/static-index/img/business_license.a9882bbb.jpg">营业执照</a></li>
        </ul>
        <ul class="footer">
          <li class="title">服务声明</li>
          <li><a href="javascript:;">服务协议</a></li>
          <li><a href="javascript:;">隐私政策</a></li>
          <li><a href="javascript:;">帮助中心</a></li>
          <li><a href="javascript:;">投诉建议</a></li>
        </ul>
        <ul class="footer">
          <li class="title">合作代理</li>
          <li><a href="javascript:;">企业合作</a></li>
          <li><a href="javascript:;">加盟合作</a></li>
          <li><a href="javascript:;">商务会展</a></li>
          <li><a href="javascript:;">加入我们</a></li>
        </ul>
        <ul class="footer">
          <li class="title">环境帮助</li>
          <li><a href="javascript:;">谷歌下载</a></li>
          <li><a href="javascript:;">搜狗下载</a></li>
          <li><a href="javascript:;">ESP问题反馈</a></li>
          <li><a href="javascript:;">ESP问题查询</a></li>
        </ul>
        <ul class="footer">
          <li class="title">联系我们</li>
          <li>地址：山东省青岛市海尔信息产业园</li>
          <li>邮箱：<EMAIL></li>
          <li>合规热线：www.haierchina.ethicspoint.com</li>
          <li>合规举报：<EMAIL></li>
        </ul>
      </div>
      <hr style="margin-top: 100px;color: #86909C;">
      <div class="footer-bottom">
        <p>Coptyright @2016 海尔商旅 版权所有</p>
        <p>鲁公网安备 37021202022259号</p>
      </div>

    </div>

  </div>
</template>

<style scoped lang="scss">
ul {
  margin-bottom: 0px;
}

.footer-container {
  width: 100%;
  background-color: #f4f4f4;
  color: #86909C;
  padding: 40px 0;
  padding-top: 50px;

  .footer-content {
    font-size: 14px;
  }

  .footer-top {
    height: 120px;
    display: flex;
    justify-content: center;
    gap: 10%;

    .footer {
      li {
        margin-bottom: 16px;

        a {
          font-size: 14px;
          color: #86909C;
        }

        a:hover {
          text-decoration: underline;
        }
      }

      .title {
        font-size: 16px;
        color: #1D2129;
        font-weight: bold;
      }
    }
  }

  .footer-bottom {
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;

    p:nth-child(2) {
      margin-left: 20px;
    }
  }

  .line {
    width: 1px;
    height: 100%;
    background-color: #86909C;
  }

}
</style>