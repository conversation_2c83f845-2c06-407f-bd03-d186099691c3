// 摆台形式

type keys = 'U' | 'DIRECTOR' | 'THEATER' | 'ISLAND' | 'DRINK' | 'FISH' | 'CIRCLE';

export const TableTypeConstant = {
  U: { code: 1, desc: 'U型式' },
  DIRECTOR: { code: 2, desc: '董事会式' },
  THEATER: { code: 3, desc: '剧院式' },
  ISLAND: { code: 4, desc: '海岛式' },
  DRINK: { code: 5, desc: '酒会式' },
  DESK: { code: 6, desc: '课桌式' },
  FISH: { code: 7, desc: '鱼骨式' },
  CIRCLE: { code: 8, desc: '回形式' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in TableTypeConstant) {
      const item = TableTypeConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(TableTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return TableTypeConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
