<template>
  <div class="w-1000">
    <h-card size="large" title="申请审批详情">
      <h-row :gutter="24">
        <h-col class="mt-10 phone-w" :span="12">申请人工号： {{ detail.applicantCode }}</h-col>
        <h-col class="mt-10 phone-w" :span="12">申请人名称： {{ detail.applicantName }}</h-col>
        <h-col class="mt-10 phone-w" :span="12">系统来源：{{detail.applicationName}} </h-col>
        <h-col class="mt-10 phone-w" :span="12">预算系统：{{detail.budgetSysCode}} </h-col>
        <h-col class="mt-10 phone-w" :span="12">业务订单号：{{detail.businessOrderNo}} </h-col>
        <h-col class="mt-10 phone-w" :span="12">支付订单号：{{detail.payCode}} </h-col>
        <h-col class="mt-10 phone-w" :span="12">申请金额：{{detail.applyAmount}} </h-col>
        <h-col class="mt-10 phone-w" :span="12">预算类型：{{detail.feeItemName}} </h-col>
        <h-col class="mt-10 phone-w" :span="12">执行主体：{{detail.performName}} </h-col>
        <h-col class="mt-10 phone-w" :span="12">预算主体：{{detail.budgetDepartmentName}} </h-col>
        <h-col class="mt-10 phone-w" :span="12">出账法人：{{detail.legalPersonName}} </h-col>
        <h-col class="mt-10 phone-w" :span="12">受益主体：{{detail.beneficialName}} </h-col>
      </h-row>
    </h-card>
    <br />
  </div>
</template>
<script setup lang="ts">
import {
  Badge as hBadge,
  Card as hCard,
  Progress as hProgress,
  Button as hButton,  
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
} from 'ant-design-vue';
import { businessType } from '../columns';
import { computed, ref, watch, onMounted } from 'vue';
import { budgetHaierPayApi } from '@haierbusiness-front/apis/src/pay/budgetHaierPay';

import { ApplyCompanyType, ApplyType } from '@haierbusiness-front/common-libs';

import { useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';

const getQuery = () => {
  let hashUrl = window.location.hash;
  let url = hashUrl.substring(hashUrl.indexOf('?') + 1, hashUrl.length);
  let arr = url.replace(/^\#/, '').split('&');
  let params: any = {};
  for (let i = 0; i < arr.length; i++) {
    let data = arr[i].split('=');
    if (data.length === 2) {
      params[data[0]] = data[1];
    }
  }
  return params;
};
const route = useRoute();
const detail = ref({});
const accountCompanyName = ref([]);
const budgetDepartmentName = ref([]);
const permissionType = ref([] as Array<{ alias: string }>);
const getDetail = async () => {
  const data = await budgetHaierPayApi.queryApproveDetail(getQuery());
  detail.value = data;
//   permissionType.value = JSON.parse(data.permissionString).map((item: any) => item.alias);
//   accountCompanyName.value = JSON.parse(data.accountCompanyName);
//   budgetDepartmentName.value = JSON.parse(data.budgetDepartmentName);
};

// 获取退款状态
// const getApproveStatus = (status: number | string) => {
//   const resultMap: any = {
//     0: '取消',
//     10: '审批中',
//     20: '审批通过',
//     30: '审批驳回',
//     40: '审批撤回',
//     default: '',
//   };
//   return resultMap[status] || resultMap.default;
// };
onMounted(() => {
    getDetail();
});
</script>
<style lang="less" scoped>
.w-1000 {
  width: 1000px;
  margin: 20px auto;
}

.mt-10 {
  margin-top: 10px;
}

@media screen and (max-width: 599px) {
  .w-1000 {
    width: 98%;
    margin: 20px auto;
  }

  .phone-w {
    flex: 0 0 100%;
    max-width: 100%;
  }
}
</style>
