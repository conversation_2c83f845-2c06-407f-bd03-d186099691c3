import { IPageRequest } from '../../basic';
import { Dayjs } from 'dayjs';

// 需求提报
export interface miceSchemeSubmitRequest {
  /*商户id, 服务商端不需要传此id,自动获取当前登录商户信息 */
  merchantId?: number;

  /*主表id */
  miceId?: number;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId?: number;

  /*方案总金额(后端计算与前端传入比对) */
  schemeTotalPrice?: number;

  /*协议总金额(后端计算与前端传入比对) */
  agreementTotalPrice?: number;

  /*市场价总金额(后端计算与前端传入比对) */
  marketTotalPrice?: number;

  /*方案备注 */
  remarks?: string;

  /*方案酒店信息 */
  hotels?: Array<schemeHotelsArr>;

  /*方案住宿信息 */
  stays?: Array<schemeStaysArr>;

  /*方案用餐信息 */
  caterings?: Array<schemeCateringsArr>;

  /*方案会场信息 */
  places?: Array<schemePlacesArr>;

  /*方案用车信息 */
  vehicles?: Array<schemeVehiclesArr>;

  /*方案服务人员信息 */
  attendants?: Array<schemeAttendantsArr>;

  /*方案拓展活动信息 */
  activities?: Array<schemeActivitiesArr>;

  /*方案保险产品信息 */
  insurances?: Array<schemeInsurancesArr>;

  /* */
  material?: {
    /*需求布展物料id */
    miceDemandMaterialId?: number;

    /*需求总价 */
    demandTotalPrice?: number;

    /*方案布展物料总价 */
    schemeTotalPrice?: number;

    /*方案说明 */
    description?: string;

    /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
    sourceId?: number;

    /*方案布展物料明细 */
    materialDetails: Array<schemeMaterialDetailsArr>;
  };

  /*方案礼品信息 */
  presents?: Array<schemePresentsArr>;

  /*方案其他信息 */
  others?: Array<schemeOthersArr>;

  /* */
  traffic?: {
    /*需求交通id */
    miceDemandTrafficId?: number;

    /*总预算金额 */
    demandTotalPrice?: number;

    /*方案总金额 */
    schemeTotalPrice?: number;
  };

  /* */
  serviceFee?: {
    /*服务商比例上限 */
    serviceFeeLimitRate?: number;

    /*服务商对应上限金额 */
    serviceFeeLimit?: number;

    /*实际服务费 */
    schemeServiceFeeReal?: number;

    /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
    sourceId?: number;
  };

  /*需求住宿方案差异明细 */
  differenceStays?: Array<schemeDifferenceStaysArr>;
}

export interface schemeHotelsArr {
  /*方案酒店id */
  tempId?: number;

  /*需求酒店id */
  miceDemandHotelId?: number;

  /*需求发布酒店id */
  miceDemandPushHotelId?: number;

  /*需求发布酒店名称 */
  hotelName?: string;

  /*装修年份 */
  decorationYear?: string;

  /*酒店所在城市id */
  cityId?: number;

  /*酒店所在城市名称 */
  cityName?: string;

  /*酒店所在区域id */
  districtId?: number;

  /*酒店所在区域名称 */
  districtName?: string;

  /*酒店地址 */
  hotelAddress?: string;

  /*酒店等级 */
  level?: number;
}

export interface schemeStaysArr {
  /*临时方案酒店id,用来构建关联关系 */
  tempSchemeHotelId?: number;

  /*临时id, 用于关联差异表 schemeTempStayIds */
  tempId?: number;

  /*需求酒店id */
  miceDemandHotelId?: number;

  /*需求住宿id */
  miceDemandStayId?: number;

  /*需求日期 */
  demandDate?: Record<string, unknown>;

  /*房型 大床/双床/套房 */
  roomType?: number;

  /*早餐类型 */
  breakfastType?: number;

  /*人数 */
  personNum?: number;

  /*方案 入住房间数 */
  schemeRoomNum?: number;

  /*人数与房间数不一致原因 */
  discrepancyReason?: string;

  /*方案单价 */
  schemeUnitPrice?: number;

  /*协议产品id */
  agreementProductId?: number;

  /*协议单价, 来源于协议产品关联协议价格 */
  agreementUnitPrice?: number;

  /*门市单价, 来源于协议产品关联门市价格 */
  retailUnitPrice?: number;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId?: number;

  /*方案说明 */
  description?: string;
}

export interface schemeCateringsArr {
  /*临时方案酒店id,用来构建关联关系 */
  tempSchemeHotelId?: number;

  /*是否酒店提供用餐 */
  isInsideHotel?: boolean;

  /*需求酒店id */
  miceDemandHotelId?: number;

  /*需求用餐id */
  miceDemandCateringId?: number;

  /*需求日期 */
  demandDate?: Record<string, unknown>;

  /*用餐类型 */
  cateringType?: number;

  /*用餐时间 午餐/晚餐 */
  cateringTime?: number;

  /*方案人数 */
  schemePersonNum?: number;

  /*用餐标准 */
  demandUnitPrice?: number;

  /*是否包含酒水 */
  isIncludeDrinks?: boolean;

  /*方案单价 */
  schemeUnitPrice?: number;

  /*方案说明 */
  description?: string;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId?: number;
}

export interface schemePlacesArr {
  /*临时方案酒店id,用来构建关联关系 */
  tempSchemeHotelId?: number;

  /*需求酒店id */
  miceDemandHotelId?: number;

  /*需求会场id */
  miceDemandPlaceId?: number;

  /*需求日期 */
  demandDate?: Record<string, unknown>;

  /*使用时间 上午/下午/晚间 bitmap */
  usageTime?: number;

  /*使用用途 会议举行/布展搭建/会议撤场 bitmap */
  usagePurpose?: number;

  /*方案人数 */
  schemePersonNum?: number;

  /*面积 */
  area?: number;

  /*灯下层高 */
  underLightFloor?: number;

  /*摆台形式 */
  tableType?: number;

  /*是否需要led */
  hasLed?: boolean;

  /*方案led数量 */
  schemeLedNum?: number;

  /*方案LED来源 */
  schemeLedSource?: string;

  /*会议厅 */
  guildhall?: string;

  /*led规格说明 */
  ledSpecs?: string;

  /*是否需要茶歇 */
  hasTea?: boolean;

  /*茶歇标准/每人 */
  teaEachTotalPrice?: number;

  /*茶歇说明 */
  teaDesc?: string;

  /*方案报价会场单价 */
  schemeUnitPlacePrice?: number;

  /*方案报价led单价 */
  schemeUnitLedPrice?: number;

  /*方案报价茶歇单价 */
  schemeUnitTeaPrice?: number;

  /*协议产品id */
  agreementProductId?: number;

  /*协议产品单价 */
  agreementUnitPrice?: number;

  /*门市单价, 来源于协议产品关联门市价格 */
  retailUnitPrice?: number;

  /*方案说明 */
  description?: string;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId?: number;
}

export interface schemeVehiclesArr {
  /*需求用车id */
  miceDemandVehicleId?: number;

  /*需求日期 */
  demandDate?: Record<string, unknown>;

  /*使用方式 单趟/包车 */
  usageType?: number;

  /*使用时长 半天/全天 */
  usageTime?: number;

  /*座位数 */
  seats?: number;

  /*方案车辆数量 */
  schemeVehicleNum?: number;

  /*品牌 */
  brand?: string;

  /*路线,多程逗号分隔 */
  route?: string;

  /*方案单价 */
  schemeUnitPrice?: number;

  /*方案说明 */
  description?: string;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId?: number;
}

export interface schemeAttendantsArr {
  /*需求服务人员id */
  miceDemandAttendantId?: number;

  /*需求日期 */
  demandDate?: Record<string, unknown>;

  /*人员类型 */
  type?: number;

  /*方案人数 */
  schemePersonNum?: number;

  /*工作范围 */
  duty?: string;

  /*方案单价 */
  schemeUnitPrice?: number;

  /*方案说明 */
  description?: string;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId?: number;
}

export interface schemeActivitiesArr {
  /*需求拓展活动id */
  miceDemandActivityId?: number;

  /*需求日期 */
  demandDate?: Record<string, unknown>;

  /*费用标准 */
  demandUnitPrice?: number;

  /*方案人数 */
  schemePersonNum?: number;

  /*方案单价 */
  schemeUnitPrice?: number;

  /*方案说明 */
  description?: string;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId?: number;
}

export interface schemeInsurancesArr {
  /*需求保险id */
  miceDemandInsuranceId?: number;

  /*需求日期 */
  demandDate?: Record<string, unknown>;

  /*需求单价 */
  demandUnitPrice?: number;

  /*方案参保人数 */
  schemePersonNum?: number;

  /*保险产品id(以互动时为准,需求时只为意向) */
  productId?: number;

  /*产品所属商户id */
  productMerchantId?: number;

  /*险种名称 */
  insuranceName?: string;

  /*险种条目 */
  insuranceContent?: string;

  /*方案单价 */
  schemeUnitPrice?: number;

  /*方案说明 */
  description?: string;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId?: number;
}

export interface schemeMaterialDetailsArr {
  /* */
  miceSchemeMaterialId?: number;

  /* */
  miceDemandMaterialDetailsId?: number;

  /* */
  type?: number;

  /* */
  specs?: string;

  /* */
  schemeMaterialNum?: number;

  /* */
  demandUnitPrice?: number;

  /* */
  schemeUnitPrice?: number;

  /* */
  sourceId?: number;
}

export interface schemePresentsArr {
  /*需求礼品id */
  miceDemandPresentId?: number;

  /*方案总金额 */
  schemeTotalPrice?: number;

  /*方案说明 */
  description?: string;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId?: number;

  /*方案礼品明细 */
  presentDetails?: Array<schemePresentDetailsArr>;
}

export interface schemePresentDetailsArr {
  /*方案礼品id */
  miceSchemePresentDetailsId?: number;

  /*需求礼品id */
  miceDemandPresentId?: number;

  /*送达日期 */
  deliveryDate?: Record<string, unknown>;

  /*方案礼品数量 */
  schemePersonNum?: number;

  /*礼品产品id(以互动时为准,需求时只为意向) */
  productId?: number;

  /*产品所属商户id */
  productMerchantId?: number;

  /*产品名称,当未选择产品时可自由修改 */
  productName?: string;

  /*单位 */
  unit?: string;

  /*礼品说明 */
  personSpecs?: string;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId?: number;
}

export interface schemeOthersArr {
  /*需求其它id */ miceDemandOtherId?: number;

  /*需求日期 */
  demandDate?: Record<string, unknown>;

  /*费用标准 */
  demandTotalPrice?: number;

  /*数量(不参与总价计算) */
  num?: number;

  /*单位 */
  unit?: string;

  /*规格描述 */
  specs?: string;

  /*方案总金额 */
  schemeTotalPrice?: number;

  /*方案说明 */
  description?: string;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId?: number;
}

export interface schemeDifferenceStaysArr {
  /*需求表id */
  demandId?: number;

  /*需求住宿表ids,逗号分隔 */
  demandStayIds?: string;

  /*方案住宿表ids(虚拟id,需要和方案表住宿虚拟id一致) */
  schemeTempStayIds?: string;

  /*差异日期 */
  differenceDate?: Record<string, unknown>;

  /*需求当日总人数 */
  demandTotalPerson?: number;

  /*方案当日总人数 */
  schemeTotalPerson?: number;

  /*需求房型统计 */
  demandRoomType?: string;

  /*方案房型统计 */
  schemeRoomType?: string;

  /*需求大床数量 */
  demandOneRooms?: number;

  /*方案大床数量 */
  schemeOneRooms?: number;

  /*需求双床数量 */
  demandTwoRooms?: number;

  /*方案双床数量 */
  schemeTwoRooms?: number;

  /*需求套床数量 */
  demandSuiteRooms?: number;

  /*方案套床数量 */
  schemeSuiteRooms?: number;

  /*差异原因 */
  reason?: string;
}
