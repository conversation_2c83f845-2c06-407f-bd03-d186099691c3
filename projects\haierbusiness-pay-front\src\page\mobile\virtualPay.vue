<script setup lang="ts">
import { computed, onMounted, PropType, ref, watch } from 'vue'
import { Cell, CellGroup, RadioGroup, Radio, Popup, Icon, NumberKeyboard, PasswordInput, showFailToast, showSuccessToast } from 'vant';
import { IPayData } from '@haierbusiness-front/common-libs/src/pay/model/basicModel';
import { coinHaierPayApi, virtualPayApi } from '@haierbusiness-front/apis';
import { IQueryVirtualAccountsResponse, VirtualAccountTypeConstant, PaySourceConstant } from '@haierbusiness-front/common-libs';
import { resolveToken } from '@haierbusiness-front/utils';
import logo from '@/assets/image/small-logo.png';
import {isMobile} from "@haierbusiness-front/utils";

const loginUser = ref(resolveToken() || {})

interface Props {
    accountChecked: string
    accounts: IQueryVirtualAccountsResponse[]
    param: IPayData
    show: boolean
}

const props = withDefaults(defineProps<Props>(), {
    accountChecked: '',
    show: false
})

const accountChecked = ref(props.accountChecked)

watch(props, (newValue) => {
    accountChecked.value = props.accountChecked || ''
    show.value = props.show || false

    // 显示的时候判断状态，是否发送短信
    if (show.value && countDown.value === 60) {
        // console.log('发送短信并倒计时！')
        send()
    }
})

// 发送验证码

const emit = defineEmits(["payComplete", "setShow", "setAccountChecked", "pay"]);

//验证码
const code = ref()
// 显示键盘
const showKeyboard = ref()
// 倒计时
const countDown = ref(60)
// 正在发送
const sending = ref(false)
// 是否显示
const show = ref(false)
/**
 * 手机号
*/
const phone = ref(loginUser.value.phone);


const send = () => {
  sending.value = true;
  countDown.value = countDown.value - 1;
  sendSms(
      () => {
        startCountdown();
      }
  )
}

const sendSms = (success: () => void) => {
    virtualPayApi.sendCodeCaptcha(
      {
        accountNo: accountChecked.value,
        orderCode: props.param?.orderCode ?? ''
      }
  ).then(it => {
    success()
  })
}

const startCountdown = () => {
  let interval = window.setInterval(function () {
    if (countDown.value-- <= 0) {
      countDown.value = 60;
      sending.value = false;
      window.clearInterval(interval);
    }
  }, 1000);
}

watch(code, (newValue) => {
  if (code.value && code.value.length === 6) {
    confirmPay()
  }
})

const confirmPay = () => {
  let regexp = new RegExp(/^\d{6}$/);
  if (!regexp.test(code.value)) {
    showFailToast("验证码格式错误!");
    code.value = ''
    return;
  }

  emit("pay", code.value)

  setTimeout(() => {
      code.value = ''
    }, 500);
}


</script>
<template>
<div class="composition-con">
  <radio-group v-model="accountChecked">
      <cell-group inset>
          <cell :border="false">
              <template #title>
              <div class="type-con">
                  <img :width="25" :height="25" :src="logo" />
                  <span class="pay-title title">企业支付</span>
              </div>
              </template>
          </cell>
          <cell :border="true"  clickable @click="$emit('setAccountChecked', item.accountNo || '')"  v-for="(item, index) in props.accounts" :key="index">
              <template #title>
                  <div class="type-con">
                      <span class="pay-title no-img text-ellipsis">{{ item.accountNo }}/{{ item.accountName }}</span>
                  </div>
                  <div class="type-con">
                      <span class="pay-title no-img desc">类型：{{ item._typeName }}</span>
                      <span class="pay-title no-img desc">企业：{{ item.enterpriseName }}({{ item.enterpriseCode }})</span>
                  </div>
                  <div class="type-con">
                      <span class="pay-title no-img desc">余额：{{ item.amount }}</span>
                  </div>
              </template>
              <template #right-icon>
                  <radio :name="item.accountNo" />
              </template>
          </cell>
          <popup
                v-model:show="show"
                position="bottom"
                round
                @closed="$emit('setShow', false)"
                :style="{ height: 550 + 'px' }"
            >
            <div class="send-code-title">
                <div class="close">
                <icon name="cross" size="22px" @click="show = false" />
                </div>
                <div class="title">请输入验证码</div>
                <div class="space"></div>
            </div>
            <div class="send-code-money">
                <div class="">企业支付账号</div>
                <div class="money">
                <span class="rmb">No.</span>
                <span class="balance">{{ accountChecked }}</span>
                </div>
            </div>
            <div class="line"></div>
            <div class="phone">
                <div class="phone-desc">
                短信已发送至{{ phone }}
                </div>
            </div>
            <div class="code-container">
                <password-input
                    class="code"
                    :value="code"
                    :gutter="'10px'"
                    :mask="false"
                    :focused="showKeyboard"
                    @focus="showKeyboard = true"
                />
                <number-keyboard
                    :style="{ height: 250 + 'px' }"
                    v-model="code"
                    :show="showKeyboard"
                    @blur="showKeyboard = false"
                />
            </div>
            <div class="count-down-container">
                <div class="count-down">
                <div class="send-code-btn" v-if="!sending" @click="send">
                    重新发送
                </div>
                <div class="send-code-btn" v-else>
                    {{countDown}}秒后可重新发送
                </div>
                </div>
            </div>
            </popup>
      </cell-group>
  </radio-group>
</div>
</template>

<style scoped lang="less">
.composition-con {
    padding-top: 10px;
}

.type-con {
    display: flex;
    flex-direction: row;
    align-items: center;

    .pay-title {
        display: flex;
        padding-left: 5px;
    }

    .title {
        font-weight: bold;
        font-size: 16px;
    }

    .no-img {
        display: flex;
        padding-left: 25px;
    }

    .desc {
        font-size: 12px;
        color: #969799;
    }
}

.text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.send-code-title {
  display: flex;
  width: 100%;
  padding: 16px 16px;
  flex-direction: row;
  justify-content: space-between;

  .close {
    display: flex;
  }

  .title {
    display: flex;
    font-size: 14px;
    font-weight: bold;
  }

  .space {
    width: 22px;
    height: 22px;
  }
}

.send-code-money {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .money {
    display: flex;
    font-size: 28px;
    padding-top: 5px;
    font-weight: bold;
    flex-direction: row;
    align-items: baseline;
    line-height: 28px;

    .rmb {
      font-size: 18px;
    }

    .balance {
      padding-left: 5px;
    }
  }

}

.line {
  padding-top: 10px;
  margin: 0 16px;
  border-bottom: 1px solid #ebedf0;
}

.phone {
  display: flex;
  width: 100%;
  padding-top: 15px;
  color: #6479ff;
  justify-content: center;

  .phone-desc {
    display: flex;
    width: calc(80%);
    flex-direction: row;
  }
}

.code-container {
  display: flex;
  width: 100%;
  padding-top: 15px;
  justify-content: center;

  .code {
    width: 80%;
  }

}

.count-down-container {
  display: flex;
  width: 100%;
  padding-top: 15px;
  color: #6479ff;
  justify-content: center;

  .count-down {
    display: flex;
    width: calc(80%);
    flex-direction: row-reverse;
  }
}

</style>
