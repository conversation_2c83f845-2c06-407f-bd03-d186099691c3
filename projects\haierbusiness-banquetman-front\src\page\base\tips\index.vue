<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  message
} from "ant-design-vue";
import { ColumnType } from "ant-design-vue/lib/table/interface";
import { Key } from "ant-design-vue/lib/vc-table/interface";
import {
  PlusOutlined,
  SearchOutlined,
  DownOutlined,
  UpOutlined
} from "@ant-design/icons-vue";
import { banquetApplyApi } from "@haierbusiness-front/apis";
import {
  BApplyListRecord,
  BApplyPerson,
  BanquetStatusEnum
} from "@haierbusiness-front/common-libs";
import dayjs, { Dayjs } from "dayjs";
import { computed, ref, watch, onMounted } from "vue";
import { DataType, usePagination, useRequest } from "vue-request";
import { useEditDialog, useDelete } from "@haierbusiness-front/composables";
import {
  getCurrentRouter,
  errorModal,
  routerParam,
  getCurrentRoute
} from "@haierbusiness-front/utils";
import { storeToRefs } from "pinia";
import { applicationStore } from "@haierbusiness-front/utils/src/store/applicaiton";
import Editor from "@haierbusiness-front/components/editor/Editor.vue";
import { tipsApi } from "@haierbusiness-front/apis";

const formState = ref({
  id: "",
  content: "",
  type: "TIPS"
});

const isEdit = ref<boolean>(false);

const uploadUrl = import.meta.env.VITE_UPLOAD_URL;

const detail = ref<any>({});

const onEditorChange = (editor: IDomEditor) => {
  formState.value.content = editor.getHtml();
};

const onFinish = () => {
  tipsApi.saveOrUpdateOther(formState.value).then(res => {
    message.success("保存成功!");
    isEdit.value = false
    getInfo();
  });
};

// 获取承诺需知详情
const getInfo = () => {
  tipsApi.getTips().then(res => {
    formState.value.id = res.id;
    formState.value.content = res.content;
    detail.value = res;
  });
};

onMounted(() => {
  getInfo();
});
</script>

<template>
  <div>
    <h-row justify="space-between" style="padding: 20px;">
      <h-col style="font-size: 18px;">温馨提示</h-col>
    </h-row>
    <div
      v-if="isEdit"
      style="background-color: #ffff;height: 100%;width: 100%;padding: 30px 60px;overflow: auto;min-height:500px;"
    >
      <h-form
        :model="formState"
        name="basic"
        :label-col="{ span: 2 }"
        :wrapper-col="{ span: 22 }"
        autocomplete="off"
        @finish="onFinish"
      >
        <h-form-item label="提示内容" name="content" :rules="[{ required: true, message: '请输入内容' }]">
          <editor
            height="500px"
            :modelValue="formState.content"
            @change="onEditorChange"
            style="z-index: 20"
            :uploadUrl="uploadUrl"
          />
        </h-form-item>
        <h-form-item :wrapper-col="{ offset: 2, span: 16 }">
          <h-button style="margin-right: 40px" @click="isEdit=false">取消</h-button>
          <h-button type="primary" html-type="submit">提交</h-button>
        </h-form-item>
      </h-form>
    </div>
    <div
      v-else
      style="background-color: #ffff;height: 100%;width: 100%;padding: 60px 60px;overflow: auto;min-height:500px;position: relative;"
    >
      <h-row>
        <h-col :span="6">
          <h-form-item label="经办人" name="content">
            {{detail.creatorName}}({{detail.creator}})
          </h-form-item>
        </h-col>
        <h-col :span="6">
          <h-form-item label="操作时间" name="content">
            {{detail.updateTime}}
          </h-form-item>
        </h-col>
      </h-row>
      <div class="content" v-html="detail.content"></div>
      <h-button type="primary" class="btn" @click="isEdit=true">修改内容</h-button>
    </div>
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}
.btn {
  position: absolute;
  bottom: 20px;
  left:45%;
}
.img {
  width: 104.5px;
  height: 50px;
}
:deep(.ant-descriptions-item-label) {
  width: 200px;
}
:deep(.ant-descriptions-item-content) {
  width: 300px;
}
</style>
