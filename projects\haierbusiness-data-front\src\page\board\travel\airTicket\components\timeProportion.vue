<template>
  <div :style="{ height: height + 'vh' }" background="rgba(0,0,0,0)">
    <bar-line
      from="tqydtsmc"
      :height="height"
      v-if="loaded"
      :legend="legend"
      :x-axis="xAxis"
      :y-axis="yAxis"
      :series="series"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import BarLine from "../../../components/barLine.vue";
import { queryTimeProportion } from "@haierbusiness-front/apis/src/data/board/travel";
import { EventBus } from "../../../eventBus";
const props = defineProps({
  dateType: Number,
  gngj: {
    type: [String, Number],
    default: "1",
  },
  height: {
    type: Number,
    default: 35,
  },
  from: {
    type: String,
    default: "",
  },
});

const loaded = ref(false);
const loading = ref(false);
const legend = ["数量", "平均折扣"];
const xAxis = ref([]);
const yAxis = [
  {
    type: "value",
    name: "数量(万)",
    splitNumber: 3,
    axisLabel: {
      formatter(value) {
        return value / 10000;
      },
    },
  },
  {
    type: "value",
    name: "平均折扣",
    axisLabel: {
      formatter(value) {
        return value;
      },
    },
  },
];
const series = ref([]);
onMounted(() => {
  queryData();
});
const payTypeCheck = ref<string>("");
const paramsData: any = ref({});
EventBus.on((event, params) => {
  if (event == "refresh") {
    if (!params) queryData();
    if (params && params.from != "tqydtsmc") {
      payTypeCheck.value = params.name;
      paramsData.value = { ...params, from: "tqydtsmc" };
    } else {
      paramsData.value = {};
      payTypeCheck.value = "";
    }
  }
});
const getFunctionColumns = () => {
  if (props.dateType == 0) {
    return [
      {
        alias: "dd_ydsj_query",
        snippet: "AGG_DATE_YEAR([dd_ydsj])",
      },
    ];
  }
  if (props.dateType == 1) {
    return [
      {
        alias: "dd_ydsj_query",
        snippet: "AGG_DATE_MONTH([dd_ydsj])",
      },
    ];
  }
  return [
    {
      alias: "dd_ydsj_query",
      snippet: "AGG_DATE_DAY([dd_ydsj])",
    },
  ];
};
const queryData = async () => {
  loading.value = true;
  const functionColumns = getFunctionColumns();
  const data = await queryTimeProportion(
    paramsData.value && paramsData.value.data ? paramsData.value.data.name : null,
    paramsData.value && paramsData.value.from ? paramsData.value.from : null
  );
  loading.value = false;
  const barData: any = [];
  const lineData: any = [];
  const xData: any = [];
  data.rows.forEach((item, index) => {
    xData.push(item[0]);
    barData.push(item[2] || 0);
    lineData.push(item[3] || 0);
  });

  xAxis.value = xData.map((item) => item);
  series.value = [
    {
      name: "数量",
      type: "bar",
      color: "rgba(0,240,255,0.4)",
      itemStyle: {
        borderColor: "#00F0FF",
      },
      selectedMode: props.from ? "" : "single", //鼠标点击是否突出该区域
      data: barData,
    },
    {
      name: "平均折扣",
      type: "line",
      yAxisIndex: 1,
      color: "rgba(255,215,0,1)",
      smooth: true,
      symbol: "none",
      data: lineData.map((item) => item.toFixed(2)),
    },
  ] as any;
  loaded.value = true;
};
watch(() => props.dateType, queryData);
</script>
