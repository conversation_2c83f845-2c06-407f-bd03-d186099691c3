<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { DownOutlined, ExclamationCircleOutlined, PlusOutlined,UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { payApi,virtualPayApi } from '@haierbusiness-front/apis';
import {
  VirtualAccountTypeConstant,
  HaierBudgetSourceConstant,
  VirtualScopeConstanty,
  IPaymentRecordListRequest,
  IPaymentRecordListResponse,
  IPaymentVirtualAccount,
  IWyyB2bAccountRequest,
  IWyyB2bCancelRequest,
  IWyyB2bConfirmBalanceOrderBudgetRequest,
  IWyyB2bConfirmRequest,
  IWyyB2bListRequest,
  IWyyB2bMarkReadRequest,
  IWyyB2bRevokeConfirmRequest,
PayStatusConstant,
PayTypeConstant
} from '@haierbusiness-front/common-libs';
import {errorModal, getCurrentRouter, routerParam} from '@haierbusiness-front/utils';
import Eloading from '@haierbusiness-front/components/loading/ELoading.vue';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import EditDialog from './edit-dialog.vue'
const router = getCurrentRouter()

const columns: ColumnType[] = [
  {
    title: '账户',
    dataIndex: 'accountNo',
    fixed: 'left',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '名称',
    dataIndex: 'accountName',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '企业',
    dataIndex: 'enterpriseCode',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '余额',
    dataIndex: 'amount',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '作用域',
    dataIndex: 'scope',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '过期时间',
    dataIndex: 'expireDate',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改人',
    dataIndex: 'lastModifiedBy',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改时间',
    dataIndex: 'gmtModified',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<IPaymentRecordListRequest>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(virtualPayApi.list);

const {
  data:exportListData,
  run:exportListApiRun,
  loading:exportListLoading,
} = useRequest(payApi.exportVirtualAccountList);

const reset = () => {
  // window.location.reload(true)
  location.reload(true)
  // expireBeginAndEnd.value = undefined
  // searchParam.value = {}
}

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
  /* stateAccountApiRun(
    searchParam.value
  )
  cvpErrorAccountApiRun(
    searchParam.value
  )
  pushCvpErrorAccountApiRun(
    searchParam.value
  ) */
};

// 汇总相关
const gotoAccount = () => {
  visibleNew.value = true
}

// 新增表单相关
const accountNewForm = ref()
const confirmLoading = ref(false)
const visibleNew = ref(false)
const newAccountForm = ref<IWyyB2bAccountRequest>({})
const labelCol = { span: 10 }
const wrapperCol = { span: 10 }

const selectPeriodYearMonth = (item: any) => {
  const selectDay = dayjs(item, "YYYY-MM")
  newAccountForm.value.begin = selectDay.date(1) as unknown as string
  newAccountForm.value.end = selectDay.date(selectDay.daysInMonth()) as unknown as string
}

const gotoDetails = (record: IPaymentVirtualAccount) => {
  router.push({ path: "/payman/virtual/details", query: { record: routerParam(record) } })
}
const confirmParam = ref<IWyyB2bConfirmRequest>({})
const confirmAccountLoading = ref(false)

const revokeConfirmParam = ref<IWyyB2bRevokeConfirmRequest>({})
const revokeConfirmLoading = ref(false)


const cancelParam = ref<IWyyB2bCancelRequest>({})
const cancelLoading = ref(false)


const confirmBudgetParam = ref<IWyyB2bConfirmBalanceOrderBudgetRequest>({})


const advancedSearchVisible = ref(false)
const gotoAdvancedSearch = () => {
  if (advancedSearchVisible.value) {
    advancedSearchVisible.value = false
  } else {
    advancedSearchVisible.value = true
  }
}

const expireBeginAndEnd = ref<[Dayjs, Dayjs]>()
watch(() => expireBeginAndEnd.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.expireBeginDate = dayjs(n[0]).format('YYYY-MM-DD')
    searchParam.value.expireEndDate = dayjs(n[1]).format('YYYY-MM-DD')
  } else {
    searchParam.value.expireBeginDate = undefined
    searchParam.value.expireEndDate = undefined
  }
});


const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } =
useEditDialog<IPaymentVirtualAccount, IPaymentVirtualAccount>(virtualPayApi, "账号", () => listApiRun({
  ...searchParam.value,
  pageNum: data.value?.pageNum,
  pageSize: data.value?.pageSize,
}))

const thisHandleEdit = (item: IPaymentVirtualAccount) => {
  const currentData = {
    ...item,
    expireDate: dayjs(item.expireDate)
  };

  handleEdit({ ...currentData });
}

const goAuth = (accountNo: string, enterpriseCode: string) => {
  router.push({ path: "/payman/virtual/auth", query: { accountNo, enterpriseCode } })
}

const virtualAccountTypes = computed(() => VirtualAccountTypeConstant.toArray())

</script>

<template>
  <Eloading :loading="confirmLoading"></Eloading>
  <Eloading :loading="confirmAccountLoading"></Eloading>
  <Eloading :loading="revokeConfirmLoading"></Eloading>
  <Eloading :loading="cancelLoading"></Eloading>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">

    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="keyword">账户：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="keyword" v-model:value="searchParam.keyword" placeholder="账户/账户名称" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchState">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.state" style="width: 100%" allow-clear>
              <h-select-option :value="0">无效</h-select-option>
              <h-select-option :value="1">有效</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchState">类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.type" style="width: 100%" allow-clear>
              <h-select-option v-for="(item, index) in virtualAccountTypes" :value="item?.code">{{ item?.desc }}</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;">
              <label for="expireBeginAndEnd">过期时间：</label>
            </h-col>
            <h-col :span="4">
              <h-range-picker v-model:value="expireBeginAndEnd" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button type="primary" style="margin-right: 10px" :loading="exportListLoading"  @click="exportListApiRun(searchParam);">
              <UploadOutlined />
              导出
            </h-button>
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: left;">
            <h-button type="primary" @click="handleCreate">
              <PlusOutlined /> 新增账号
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table  :columns="columns" :row-key="record => record.id" :size="'small'"
          :data-source="dataSource" :scroll="{ x: 1550 }" :pagination="pagination" :loading="loading"
          @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'accountNo'">
              <h-button type="link" @click="gotoDetails(record)">{{ record.accountNo }}</h-button>
            </template>
            <template v-if="column.dataIndex === 'payType'">
              {{ PayTypeConstant.ofType(record.payType)?.name }}
            </template>
            <template v-if="column.dataIndex === 'state'">
              <h-tag v-if="record.state === 0" color="default">无效</h-tag>
              <h-tag v-if="record.state === 1" color="success">有效</h-tag>
            </template>
            <template v-if="column.dataIndex === 'type'">
              {{ VirtualAccountTypeConstant.ofType(record.type)?.desc }}
            </template>
            <template v-if="column.dataIndex === 'scope'">
              {{ VirtualScopeConstanty.ofType(record.scope)?.desc }}
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link"  @click="thisHandleEdit(record)">编辑</h-button>
              <h-button type="link"  @click="goAuth(record.accountNo, record.enterpriseCode)">查看授权用户</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

    <div v-if="visible">
      <edit-dialog
          :show="visible"
          :data="editData"
          @cancel="onDialogClose"
          @ok="handleOk"
      >
      </edit-dialog>
    </div>

  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #********;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
