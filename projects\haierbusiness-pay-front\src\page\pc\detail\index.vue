<script setup lang="ts">
import { Segmented, Row, Col, Descriptions, message } from 'ant-design-vue'
import { reactive, ref, computed, onMounted,watch } from 'vue'
import { useRequest } from 'vue-request'
import { b2bWyyBalanceApi, budgetHaierPayRecordApi, payApi } from '@haierbusiness-front/apis'
import {isMobile} from "@haierbusiness-front/utils";
import {
  HeaderConstant,
  IPageResponse,
  IPayData,
  IPaymentRecord,
  PayStatusConstant,
  PayTypeConstant
} from '@haierbusiness-front/common-libs';
import { useRouter } from "vue-router";
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil'
import { set } from 'nprogress';

const router = useRouter()

const data = ref(['支付信息','订单详情'])
const select = ref(data.value[0])

const orderCode = ref('')
const finishedPayRecord = ref<IPaymentRecord>({})

const addParams = (url: string) => {
    if (!url) {
        return ''
    }
    const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false)
    if(url.indexOf('?') > -1) {
        return '&hb-token=' + token
    } else {
        return '?hb-token=' + token
    }
}


// watch(select, (newVal, oldVal) => {
//     console.log(newVal,"----")
//     if(newVal=='订单详情'){
//             var iframe = document.getElementById('details_iframe');
//             console.log(iframe,"iframe")
//             var iframeDoc = iframe?.contentDocument || iframe?.contentWindow?.document;
//             var innerElement = iframeDoc.getElementById('drag');
//             innerElement.style.display = 'none';/* 隐藏 */
//     }
// })

onMounted(() => {
    orderCode.value = router.currentRoute.value.query.orderCode?.toString() ?? ''
    if(!orderCode.value) {
        message.error("缺少订单号!")
        return;
    }
    // 获取支付成功记录
    payApi.searchRecord({ businessCode: orderCode.value }).then(it => {
        finishedPayRecord.value = it;
        // it.orderDetailsUrl
        if(it.orderDetailsUrl&&it.orderDetailsUrl?.indexOf('businesstravel.haier.net')!=-1){
            // console.log(encodeURIComponent(it.orderDetailsUrl),"------------------")
            finishedPayRecord.value.orderDetailsUrl ="https://businesstravel.haier.net/#/login?url="+ encodeURIComponent(it.orderDetailsUrl)
        }else{
            finishedPayRecord.value.orderDetailsUrl = it.orderDetailsUrl
        }
        if(finishedPayRecord.value?.code) {
            budgetHaierPayRecordApiRun({paymentCode: finishedPayRecord.value?.code})
        }
        // 如果没有详情页
        if(!finishedPayRecord.value.orderDetailsUrl){
            data.value = ['支付信息']
            select.value = data.value[0]
        }
    })
})

// 查询预算相关信息
const {
  data: budgetHaierPayRecordApiData,
  run: budgetHaierPayRecordApiRun,
  loading: budgetHaierPayRecordApiLoading
} = useRequest(budgetHaierPayRecordApi.list, {
  defaultParams: [
    {
      paymentCode: finishedPayRecord.value?.code
    }
  ],
  manual: true
});

const budgetDataSource = computed(() => budgetHaierPayRecordApiData.value?.records || []);


</script>

<template>
  <div class="container">
    <div class="payInfo">
        <a-segmented v-show="data.length==2" v-model:value="select" block :options="data" />
        <div :class="{'show': select=== '支付信息', 'hide': select != '支付信息'}" class="payContent">
            <a-descriptions title="支付信息">
                <a-descriptions-item v-if="finishedPayRecord.businessCode" label="订单号">{{ finishedPayRecord.businessCode }}</a-descriptions-item>
                <a-descriptions-item v-if="finishedPayRecord.payType" label="支付方式">{{ PayTypeConstant.ofType(finishedPayRecord.payType)?.name }}</a-descriptions-item>
                <a-descriptions-item v-if="finishedPayRecord.state" label="支付状态">{{ PayStatusConstant.ofType(finishedPayRecord.state)?.name }}</a-descriptions-item>
                <a-descriptions-item v-if="finishedPayRecord.code" label="中台交易流水号">{{ finishedPayRecord.code }}</a-descriptions-item>
                <a-descriptions-item v-if="finishedPayRecord.providerOrderCode" label="第三方流水号">{{ finishedPayRecord.providerOrderCode }}</a-descriptions-item>
                <a-descriptions-item v-if="finishedPayRecord.secondProviderOrderCode" label="第三方单号2">{{ finishedPayRecord.secondProviderOrderCode }}</a-descriptions-item>
                <a-descriptions-item v-if="finishedPayRecord.createBy" label="创建人">{{ finishedPayRecord.createBy }}</a-descriptions-item>
                <a-descriptions-item v-if="finishedPayRecord.owner" label="支付人">{{ finishedPayRecord.owner }}</a-descriptions-item>
                <a-descriptions-item v-if="finishedPayRecord.gmtCreate" label="创建时间">{{ finishedPayRecord.gmtCreate }}</a-descriptions-item>
                <a-descriptions-item v-if="finishedPayRecord.payTime" label="发起支付时间">{{ finishedPayRecord.payTime }}</a-descriptions-item>
                <a-descriptions-item v-if="finishedPayRecord.successPayTime" label="交易成功时间">{{ finishedPayRecord.successPayTime }}</a-descriptions-item>
                <a-descriptions-item label="交易金额">{{ finishedPayRecord.amount }} 元</a-descriptions-item>
            </a-descriptions>
            <a-descriptions v-if="budgetDataSource[0]?.budgetCode" title="预算信息">
                <a-descriptions-item v-if="budgetDataSource[0]?.budgetCode" label="预算单号">{{ budgetDataSource[0]?.budgetCode }}</a-descriptions-item>
                <a-descriptions-item v-if="budgetDataSource[0]?.budgetSysCode" label="预算系统">{{ budgetDataSource[0]?.budgetSysCode }}</a-descriptions-item>
                <a-descriptions-item v-if="budgetDataSource[0]?.source" label="预算来源">{{ budgetDataSource[0]?.source }}</a-descriptions-item>
                <a-descriptions-item v-if="budgetDataSource[0]?.budgetDepartmentCode" label="预算部门">{{ budgetDataSource[0]?.budgetDepartmentCode }}/{{ budgetDataSource[0]?.budgetDepartmentName}}</a-descriptions-item>
                <a-descriptions-item v-if="budgetDataSource[0]?.feeItem" label="费用项目">{{ budgetDataSource[0]?.feeItem }}/{{ budgetDataSource[0]?.feeItemName }}</a-descriptions-item>
                <a-descriptions-item v-if="budgetDataSource[0]?.itemCode" label="立项">{{ budgetDataSource[0]?.itemCode }}/{{ budgetDataSource[0]?.itemName }}</a-descriptions-item>
                <a-descriptions-item v-if="budgetDataSource[0]?.accountCompanyCode" label="结算单位">{{ budgetDataSource[0]?.accountCompanyCode }}/{{ budgetDataSource[0]?.accountCompanyName}}</a-descriptions-item>
                <a-descriptions-item v-if="budgetDataSource[0]?.customCode" label="客户">{{ budgetDataSource[0]?.customCode }}/{{ budgetDataSource[0]?.customName }}</a-descriptions-item>
                <a-descriptions-item v-if="budgetDataSource[0]?.providerName" label="供应商">{{ budgetDataSource[0]?.providerName }}（{{ budgetDataSource[0]?.providerCode }}/{{ budgetDataSource[0]?.providerOverseasCode }}）</a-descriptions-item>
                <a-descriptions-item v-if="budgetDataSource[0]?.applicantCode" label="申请人">{{ budgetDataSource[0]?.applicantCode }}/{{ budgetDataSource[0]?.applicantName }}</a-descriptions-item>
                <a-descriptions-item v-if="budgetDataSource[0]?.projectCode" label="研发项目">{{ budgetDataSource[0]?.projectCode }}/{{ budgetDataSource[0]?.projectName }}</a-descriptions-item>
                <a-descriptions-item v-if="budgetDataSource[0]?.wbsCode" label="WBS">{{ budgetDataSource[0]?.wbsCode }}/{{ budgetDataSource[0]?.wbsName }}</a-descriptions-item>
                <a-descriptions-item v-if="budgetDataSource[0]?.saleType" label="销售类型">
                    <span v-if="budgetDataSource[0]?.saleType === 1">内销</span>
                    <span v-if="budgetDataSource[0]?.saleType === 2">外销</span>
                </a-descriptions-item>
                <a-descriptions-item v-if="budgetDataSource[0]?.dcProjectCode" label="地产项目">{{ budgetDataSource[0]?.dcProjectCode }}/{{ budgetDataSource[0]?.dcProjectName }}</a-descriptions-item>
                <a-descriptions-item v-if="budgetDataSource[0]?.dcItemCode" label="地产分期">{{ budgetDataSource[0]?.dcItemCode }}/{{ budgetDataSource[0]?.dcItemName }}</a-descriptions-item>
            </a-descriptions>
        </div>
        <div :class="{'show': select=== '订单详情', 'hide': select != '订单详情'}" class="businessDetail">
            <iframe  :width="isMobile()?365:1400" height="600" v-if="finishedPayRecord.orderDetailsUrl" :src="finishedPayRecord.orderDetailsUrl.indexOf('businesstravel.haier.net')==-1?finishedPayRecord.orderDetailsUrl + addParams(finishedPayRecord.orderDetailsUrl ?? ''):finishedPayRecord.orderDetailsUrl" frameborder="0" class="details_iframe" id="details_iframe"></iframe>
        </div>
    </div>
  </div>
</template>

<style scoped lang="less">

.container {
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    height: 100%;
    overflow-y: auto;
}

.payContent {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding-top: 20px;
    transition: all 0.5s;
}

.payInfo {
    display: flex;
    width: 1400px;
    flex-direction: column;
    align-items: center;
    @media screen and (max-width: 600px) {
    // 当屏幕宽度小于等于 600px 时应用的样式
    width: 355px;
    }
}

.businessDetail {
    display: flex;
    width: 100%;
    min-height: 600px;
    transition: all 0.5s;
    margin-top:20px;
}

.show {
    // opacity: 1;
    display: block;
}

.hide {
    //opacity: 0;
    display: none;
}


</style>