<script setup lang="ts">
import { Tag as hTag, <PERSON>lapse as hColl<PERSON>se, CollapsePanel as hCollapsePanel, DatePicker as hDatePicker, Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem, Input as hInput, Table as hTable, BreadcrumbItem as hBreadcrumbItem, Breadcrumb as hBreadcrumb, LayoutSider as hLayoutSider, LayoutFooter as hLayoutFooter, LayoutContent as hLayoutContent, LayoutHeader as hLayoutHeader, Layout as hLayout, MenuItem as hMenuItem, MenuItemGroup as hMenuItemGroup, SubMenu as hSubMenu, Menu as hMenu, Divider as hDivider, Space as hSpace, Button as hButton, Col as hCol, Result as hResult, Row as hRow, TabPane as hTabPane, Tabs as hTabs, message, TableProps } from 'ant-design-vue';
import { computed, onMounted, ref, watch } from 'vue';
import { UploadOutlined, UserOutlined, NotificationOutlined, AppstoreOutlined, MenuFoldOutlined, MenuUnfoldOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { b2bDcBalanceApi } from '@haierbusiness-front/apis';
import { usePagination, useRequest } from 'vue-request';
import { BudgetNotifiedReleaseCvpStateConstant, BalanceStatusConstant, IHaierAccountBillInfo, IUserSaveUpdateRequest, IDcB2bAccountRequest, IDcB2bDetailsRequest } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { resolveParam } from '@haierbusiness-front/utils';
import { ColumnType } from 'ant-design-vue/lib/table';

const columns: ColumnType[] = [
  {
    title: '业务单号',
    dataIndex: 'businessCode',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '支付单号',
    dataIndex: 'paymentCode',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '退款业务单号',
    dataIndex: 'refundBusinessCode',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '退款支付单号',
    dataIndex: 'refundPaymentCode',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '预算系统单号',
    dataIndex: 'budgetCode',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '订单实付金额',
    dataIndex: 'orderAmount',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '结算金额(含税)',
    dataIndex: 'amount',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '不含税结算金额',
    dataIndex: 'exTaxAmount',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '税额',
    dataIndex: 'taxAmount',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '成交时间',
    dataIndex: 'dealTime',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '产品类型',
    dataIndex: 'productType',
    width: '100px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '产品ID',
    dataIndex: 'productId',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
    width: '700px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '产品数量',
    dataIndex: 'num',
    width: '100px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '商品单价',
    dataIndex: 'unitPrice',
    width: '100px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '税收分类编码',
    dataIndex: 'taxCode',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '税率',
    dataIndex: 'taxRate',
    width: '100px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '影像地址',
    dataIndex: 'attachmentUrl',
    width: '600px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '规格型号',
    dataIndex: 'specModel',
    width: '400px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '单位',
    dataIndex: 'unitCode',
    width: '100px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '预算是否确认',
    dataIndex: 'confirmed',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '预算确认失败原因',
    dataIndex: 'confirmErrorMessage',
    width: '500px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改人',
    dataIndex: 'lastModifiedBy',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改时间',
    dataIndex: 'gmtModified',
    width: '150px',
    align: 'center',
    ellipsis: true,
  }
];

const props = defineProps({
  query: Object
});
const recordData = computed(() => <IHaierAccountBillInfo>resolveParam(props.query?.record));
const searchParam = ref<IDcB2bDetailsRequest>({})
const {
  data,
  run: detailsApiRun,
  loading,
  current,
  pageSize,
} = usePagination(b2bDcBalanceApi.details, {
  defaultParams: [
    {
      accountCode: recordData.value.code
    }
  ],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
  },
  manual: false
});

const {
  data: detailsExportData,
  run: detailsExportApiRun,
  loading: detailsExportLoading,
} = useRequest(b2bDcBalanceApi.detailsExport);

const dataSource = computed(() => data.value?.records || []);
const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));
const reset = () => {
  searchParam.value = {}
}
const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  detailsApiRun({
    ...searchParam.value,
    accountCode: recordData.value.code,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

watch(() => props.query, (n: any, o: any) => {
  handleTableChange({ current: 1, pageSize: pageSize.value })
});

const gotoAttachment = (url: string) => {
  window.open(url)
}
const collapseAactiveKey = ref([])
</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-collapse v-model:activeKey="collapseAactiveKey" style="font-size: 14px;font-weight: 600;" ghost>
      <h-collapse-panel key="1" :header="recordData.code">
        <div style="padding: 0 20px 20px 20px;line-height: 28px;">
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">所属月份：</h-col>
            <h-col span="5">{{ recordData.periodYearMonth }}</h-col>
            <h-col span="3" style="text-align: right;">开始日期：</h-col>
            <h-col span="5">{{ recordData.periodBegin }}</h-col>
            <h-col span="3" style="text-align: right;">结束日期：</h-col>
            <h-col span="5">{{ recordData.periodEnd }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">结算状态：</h-col>
            <h-col span="5">
              <h-tag v-if="recordData.state === BalanceStatusConstant.CANCEL.code" color="default">
                {{ BalanceStatusConstant.CANCEL.name }}
              </h-tag>
              <h-tag v-if="recordData.state === BalanceStatusConstant.ACCOUNTED.code" color="warning">
                {{ BalanceStatusConstant.ACCOUNTED.name }}
              </h-tag>
              <h-tag v-if="recordData.state === BalanceStatusConstant.CONFIRMED.code" color="processing">
                {{ BalanceStatusConstant.CONFIRMED.name }}
              </h-tag>
              <h-tag v-if="recordData.state === BalanceStatusConstant.SETTLED.code" color="success">
                {{ BalanceStatusConstant.SETTLED.name }}
              </h-tag>
            </h-col>
            <h-col span="3" style="text-align: right;">CVP单号：</h-col>
            <h-col span="5">{{ recordData.cvpCode }}</h-col>
            <h-col span="3" style="text-align: right;">CVP错误：</h-col>
            <h-col span="5">{{ recordData.cvpErrorMessage }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">记账单号：</h-col>
            <h-col span="5">{{ recordData.bookVoucherCode }}</h-col>
            <h-col span="3" style="text-align: right;">付款号：</h-col>
            <h-col span="5">{{ recordData.balancePaymentCode }}</h-col>
            <h-col span="3" style="text-align: right;">付款日期：</h-col>
            <h-col span="5">{{ recordData.balancePaymentTime }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">总结算金额(含税)：</h-col>
            <h-col span="5">{{ recordData.totalAmount }}</h-col>
            <h-col span="3" style="text-align: right;">总税额：</h-col>
            <h-col span="5">{{ recordData.totalTaxAmount }}</h-col>
            <h-col span="3" style="text-align: right;">总结算金额(不含税)：</h-col>
            <h-col span="5">{{ recordData.exTaxTotalAmount }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">预算系统：</h-col>
            <h-col span="5">{{ recordData.budgetSysCode }}</h-col>
            <h-col span="3" style="text-align: right;">预算部门：</h-col>
            <h-col span="5">{{ recordData.departmentCode }}/{{ recordData.departmentName }}</h-col>
            <h-col span="3" style="text-align: right;">供应商：</h-col>
            <h-col span="5">{{ recordData.providerCode }}/{{ recordData.providerName }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">费用项目：</h-col>
            <h-col span="5">{{ recordData.feeItem }}/{{ recordData.feeItemName }}</h-col>
            <h-col span="3" style="text-align: right;">结算单位：</h-col>
            <h-col span="5">{{ recordData.accountCompanyCode }}/{{ recordData.accountCompanyName }}</h-col>
            <h-col span="3" style="text-align: right;">客户：</h-col>
            <h-col span="5">{{ recordData.customCode }}/{{ recordData.customName }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">研发项目：</h-col>
            <h-col span="5">{{ recordData.projectCode }}/{{ recordData.projectName }}</h-col>
            <h-col span="3" style="text-align: right;">地产项目：</h-col>
            <h-col span="5">{{ recordData.dcProjectCode }}/{{ recordData.dcProjectName }}</h-col>
            <h-col span="3" style="text-align: right;">地产分期：</h-col>
            <h-col span="5">{{ recordData.dcItemCode }}/{{ recordData.dcItemName }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">WBS：</h-col>
            <h-col span="5">{{ recordData.wbsCode }}/{{ recordData.wbsName }}</h-col>
            <h-col span="3" style="text-align: right;">创建人：</h-col>
            <h-col span="5">{{ recordData.createBy }}</h-col>
            <h-col span="3" style="text-align: right;">创建时间：</h-col>
            <h-col span="5">{{ recordData.gmtCreate }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">最后修改人：</h-col>
            <h-col span="5">{{ recordData.lastModifiedBy }}</h-col>
            <h-col span="3" style="text-align: right;">最后修改时间：</h-col>
            <h-col span="5">{{ recordData.gmtModified }}</h-col>
            <h-col span="3" style="text-align: right;">预算释放状态：</h-col>
            <h-col span="5">
              <h-tag v-if="recordData.notifiedReleaseCvp === BudgetNotifiedReleaseCvpStateConstant.NOT.code"
                color="default">
                {{ BudgetNotifiedReleaseCvpStateConstant.NOT.name }}
              </h-tag>
              <h-tag v-if="recordData.notifiedReleaseCvp === BudgetNotifiedReleaseCvpStateConstant.SUCCESS.code"
                color="processing">
                {{ BudgetNotifiedReleaseCvpStateConstant.SUCCESS.name }}
              </h-tag>
              <h-tag v-if="recordData.notifiedReleaseCvp === BudgetNotifiedReleaseCvpStateConstant.ERROR.code"
                color="warring">
                {{ BudgetNotifiedReleaseCvpStateConstant.ERROR.name }}
              </h-tag>
            </h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="24"><h-divider /></h-col>
          </h-row>
        </div>
      </h-collapse-panel>
    </h-collapse>

    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="businessCode">业务单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="businessCode" v-model:value="searchParam.businessCode" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="paymentCode">支付单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="paymentCode" v-model:value="searchParam.paymentCode" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="refundBusinessCode">退款业务单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="refundBusinessCode" v-model:value="searchParam.refundBusinessCode" placeholder=""
              autocomplete="off" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="refundPaymentCode">退款支付单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="refundPaymentCode" v-model:value="searchParam.refundPaymentCode" placeholder=""
              autocomplete="off" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="budgetCode">预算系统单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="budgetCode" v-model:value="searchParam.budgetCode" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="productId">产品ID：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="productId" v-model:value="searchParam.productId" placeholder="" autocomplete="off" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="productName">产品名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="productName" v-model:value="searchParam.productName" placeholder="模糊匹配" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="confirmed">预算确认状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.confirmed" style="width: 100%" allow-clear>
              <h-select-option :value="true">
                已确认
              </h-select-option>
              <h-select-option :value="false">
                未确认
              </h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :offset="18" :span="6" style="text-align: right;">
            <h-button type="primary" style="margin-right: 10px" :loading="detailsExportLoading"
              @click="detailsExportApiRun({ accountCode: recordData.code  });">
              <UploadOutlined />
              导出
            </h-button>
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined /> 查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'attachmentUrl'">
              <span style="color: #0073e5;" @click="gotoAttachment(record.attachmentUrl)">{{ record.attachmentUrl
              }}</span>
            </template>
            <template v-if="column.dataIndex === 'confirmed'">
              <span v-if="record.confirmed">是</span>
              <span v-else>否</span>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}</style>
