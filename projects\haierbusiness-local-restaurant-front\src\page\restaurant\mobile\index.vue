<template>
  <div>


    <router-view v-slot="{ Component }">
      <keep-alive>
        <component :is="Component" :key="$route.name" v-if="$route.meta.keepAlive" />
      </keep-alive>
      <component :is="Component" :key="$route.name" v-if="!$route.meta.keepAlive" />
    </router-view>

    

    <van-dialog v-model:show="show" title="特别提示">
      <div class="info-dialog" v-html="instruction">

      </div>

      <template #footer>
        <van-row class="info-row" justify="center">
          <van-col :span="18">
            <van-button class="info-btn" @click="show = false" type="primary">我知道了</van-button>
          </van-col>
        </van-row>
      </template>
    </van-dialog>
  </div>
</template>

<script lang="ts" setup>

import { IUserListRequest } from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { onMounted, ref, onBeforeUpdate, watch } from 'vue';
import { restaurantApi } from '@haierbusiness-front/apis';

const show = ref(false);

// 查询阅读须知
const instruction = ref<string>('')
const getReadInstruction = () => {
  const params = {
    id: 1
  }
  restaurantApi.getReadInstruction(params).then(res => {
    instruction.value = res.data.instructionInfo || ''
    if (instruction.value) {
      show.value = true
      sessionStorage.setItem('isShowInstruction', '1')

    }
  })
}
onMounted(() => {
  const isShowInstruction = sessionStorage.getItem('isShowInstruction')
  if (!isShowInstruction) {
    getReadInstruction()
  }

});

const onChange = (index: number) => { };
</script>

<style scoped>
.info-dialog {
  width: 100%;
  padding: 0 20px;
  max-height: 200px;
  overflow-y: scroll;
  color: #7f7e82;
}

.info-row {
  padding: 10px 0;
}

.info-btn {
  width: 100%;
}

:deep(.van-dialog__header) {
  padding-bottom: 20px;
  font-size: 16px;
}
</style>