import { IPageRequest } from '../../basic';

export class IPriceInquiryFilter extends IPageRequest {
  begin?: string;
  end?: string;
  platformHotelName?: string;
  inquiryState?: number;
  createName?: string;
  billType?: number;
  inquiryCode?: string;
}

export class IPriceInquiry {
  inquiryState?: number;
  id?: number | null;
  createName?: string;
  createTime?: string;
  updater?: string;
  updateTime?: string;
  hotelId?: string;
  platformHotelName?: string;
  validStartTime?: string;
  validEndTime?: string;
  inquiryStartTime?: string;
  inquiryEndTime?: string;
  reason?: string;
  attachment?: string;
  enablePlaceChange?: boolean;
  code?: string;
  enableQuarter?: boolean;
  isLocal?: boolean;
  resourceHotelPlaces?: [];
  resourceHotelQuarters?: {
    id?: number;
    startDate?: string;
    endDate?: string;
    season?: number;
  }[];
  resourceHotelRooms?: {
    id?: number;
    roomType?: number; // 1:大床房, 2:双床房, 3:套房
    roomName?: string;
    roomDetails?: string;
    resourceHotelLeadIntoId?: string;
    resourceHotelInquiryId?: number;
    priceResults?: [];
    fileInfo?: {};
  }[];
  priceList?: {
    type?: string;
    marketPrice?: number;
    lightSeasonBasePrice?: number;
    lightSeasonTier1?: number;
    lightSeasonTier2?: number;
    peakSeasonBasePrice?: number;
    peakSeasonTier1?: number;
    peakSeasonTier2?: number;
    breakfast?: string;
    roomTypeId?: number; // 关联到resourceHotelRooms的roomType
  }[];
  priceResults?: {
    id?: number;
    roomTypeId?: number; // 关联到resourceHotelRooms的id
    roomType?: number; // 房间类型 1:大床房, 2:双床房, 3:套房
    priceType?: number; // 价格类型对应PriceTypeConstant中的code
    price?: number; // 价格值
    breakfast?: string; // 早餐
  }[];
}
export class IPriceEdit {
  hotelPrices?: {
    id?:number;
    resourceHotelInquiryId?:number;
    mainId?:number;
    itemType?:number;
    priceItem?:number;
    price?:number;
  };
  places?:{

  }[];
}
