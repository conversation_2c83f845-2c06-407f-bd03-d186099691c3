<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormInstance,
  FormItem as hFormItem,
  Input as hInput,
  message,
  Modal as hModal,
  Row as hRow,
} from 'ant-design-vue';
import {MinusCircleOutlined, PlusOutlined,} from '@ant-design/icons-vue';
import {getCurrentRouter} from '@haierbusiness-front/utils';
import {PropType, ref, watch} from 'vue';
import {dailyDeptApi} from '@haierbusiness-front/apis/src/daily/dept/dept';
import {
  AnnualPlanTypeStateConstant,
  IAnnualPlanTypeListResponse,
  IAnnualPlanTypeUpdateRequest
} from "@haierbusiness-front/common-libs";
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue'
import {Dayjs} from "dayjs";

const router = getCurrentRouter();
const visible = ref(false);

const emit = defineEmits<{
  (event: 'listApiRun'): void
}>()

const prop = defineProps({
  existYears: Object as PropType<string[]>,
  // 1 查看 2 新增 3 修改
  type: Number,
  // 展示或修改的参数
  typeData: Object as PropType<IAnnualPlanTypeListResponse>,
})

const show = () => {
  visible.value = true;
};


const saveLoading = ref(false);
const submitChangeForm = () => {
  if (prop.type === 1) {
    visible.value = false
  } else {
    formRef.value
        .validate()
        .then(() => {
          submitChange()
        })
  }
};
const submitChange = () => {
  saveLoading.value = true;
  saveParam.value.data?.forEach((it) => {
    if (it) {
      it.state = it.state || AnnualPlanTypeStateConstant.IN_VALID.code
      it.year = saveParam.value.year
    }
  })
  saveParam.value.data?.push(...deletedSaveParam.value.data!!)
  if(prop.type === 2){
    dailyDeptApi.save(saveParam.value)
      .then(() => {
        emit('listApiRun')
        message.success('新增成功！')
        saveParam.value = {data: [{}]}
        visible.value = false
      })
      .finally(() => {
        for (let it of deletedSaveParam.value.data!!) {
          let index = saveParam.value.data?.indexOf(it);
          if (index != undefined && index !== -1) {
            saveParam.value.data?.splice(index, 1);
          }
        }
        saveLoading.value = false;
      })
  }else if(prop.type === 3){
    dailyDeptApi.update(saveParam.value)
      .then(() => {
        emit('listApiRun')
        message.success('更新成功！')
        saveParam.value = {data: [{}]}
        visible.value = false
      })
      .finally(() => {
        for (let it of deletedSaveParam.value.data!!) {
          let index = saveParam.value.data?.indexOf(it);
          if (index != undefined && index !== -1) {
            saveParam.value.data?.splice(index, 1);
          }
        }
        saveLoading.value = false;
      })
  }
}

const directSubmitChangeForm = () => {
  setTimeout(() => {
    if (watchMutex.value) {
      submitChange()
    } else {
      directSubmitChangeForm()
    }
  }, 10)
};

class IAnnualPlanTypeUpdateWrapperRequest extends IAnnualPlanTypeUpdateRequest {
  year?: number
}

const saveParam = ref<IAnnualPlanTypeUpdateWrapperRequest>({
  data: [{}]
});

const deletedSaveParam = ref<IAnnualPlanTypeUpdateRequest>({
  data: []
});
const watchMutex = ref(false)
const watchUpdateData = () => {
  // 查看
  if (prop.type === 1 && prop.typeData) {
    saveParam.value = prop.typeData
    deletedSaveParam.value = {data: []}
    const names = (prop.typeData.name || "").split(" / ")
    const descriptions = (prop.typeData.description || "").split(" / ")
    
  } else if (prop.type === 2 && prop.typeData) {
    saveParam.value = {data: [{}]}
    deletedSaveParam.value = {data: []}
  } else if (prop.type === 3 && prop.typeData) {
    saveParam.value = prop.typeData
    deletedSaveParam.value = {data: []}
    const ids = (prop.typeData.id + "" || "").split(" / ")
    for (let i in names) {
      saveParam.value.data?.push(
          {
            id: ids[i]
          }
      )
    }
  }
  watchMutex.value = true
}

watch([() => prop.type, () => prop.typeData], watchUpdateData);
defineExpose({
  show,
  directSubmitChangeForm,
});
const formRef = ref<FormInstance>({} as FormInstance);

const userNameChange = (userInfo: IUserInfo | undefined, executor: IOperatorsInfo) => {
  console.log(userInfo.username)
    if (!userInfo) {
      executor.managerCode = ''
      executor.managerName = ''
      executor.managerMail = ''
      executor.managerPhone = ''
        return
    }
    executor.managerCode = userInfo.username
    executor.managerName = userInfo.nickName
    executor.managerMail = userInfo.email
    executor.managerPhone = userInfo.phone
}

const params = ref<IUserListRequest>({
    pageNum: 1,
    pageSize: 20
})


const title = () => {
  if (prop.type === 1) {
    return "查看日清部门"
  } else if (prop.type === 2) {
    return "新增日清部门"
  } else if (prop.type === 3) {
    return "修改日清部门"
  } else {
    return ""
  }
}
</script>

<template>
  <h-modal v-model:open="visible"
           :title="title()"
           @ok="submitChangeForm"
           :mask-closable="false"
           :confirmLoading="saveLoading">
    <h-form ref="formRef" name="dynamic_form_item" :model="saveParam">
      <h-row :align="'middle'">
        <h-col :span="5" class="align-right">
          <div class="required lable">部门编码:</div>
        </h-col>
        <h-col :span="18">
          <h-form-item name="code" :rules="[{ required: true, message: '请输入部门编码!' }]" class="fresh-from-item">
            <h-input :disabled="type === 1" v-model:value="saveParam.code" placeholder=" 部门编码"/>
          </h-form-item>
        </h-col>
      </h-row>
      <h-row :align="'middle'">
        <h-col :span="5" class="align-right">
          <div class="required lable">部门名称:</div>
        </h-col>
        <h-col :span="18">
          <h-form-item name="name" :rules="[{ required: true, message: '请输入部门名称!' }]" class="fresh-from-item">
            <h-input :disabled="type === 1" v-model:value="saveParam.name" placeholder=" 部门名称"/>
          </h-form-item>
        </h-col>
      </h-row>
      <h-row :align="'middle'">
        <h-col :span="5" class="align-right">
          <div class="required lable">小微主:</div>
        </h-col>
        <h-col :span="18">
          <h-form-item name="managerName" :rules="[{ required: true, message: '请选择小微主!' }]" class="fresh-from-item">
            <user-select :disabled="type === 1" :value="saveParam.managerName" placeholder="小微主" :cache-key="'processSubUser'" 
              :params="params" @change="(userInfo: IUserInfo | undefined) =>  userNameChange(userInfo, saveParam)" />
          </h-form-item>
        </h-col>
      </h-row>
    </h-form>
  </h-modal>
</template>

<style scoped lang="less">
@import '../../assets/css/main.less';

.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
