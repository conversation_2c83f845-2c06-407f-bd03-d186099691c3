import { get, post } from '../request'
import { IPageResponse, Result } from '@haierbusiness-front/common-libs'
import {
    ServiceProviderFilter,
    ServiceProvider
} from '@haierbusiness-front/common-libs/src/micebid/model/serviceProvider'


export const serviceProviderApi = {
    // 获取列表
    list: (params: ServiceProviderFilter): Promise<IPageResponse<ServiceProvider>> => {
        return get('/mice-bid/api/mice/merchant/getPage', params)
    },
    //获取详情
    get: (id: number): Promise<ServiceProvider> => {
        return get('/mice-bid/api/mice/merchant/getDetails', {
            id
        })
    },
    // 服务商处理详情记录分页查询
    listProcessing: (params: ServiceProviderFilter): Promise<IPageResponse<ServiceProvider>> => {
        return get('merchant/api/merchant/getPage', params)
    },
    // 根据供应商id查询银行合同列表
    listContract: (params: ServiceProviderFilter): Promise<IPageResponse<ServiceProvider>> => {
        return get('merchant/api/merchantContract/getList', params)
    },

    // 查询银行账户列表
    listBank: (params: ServiceProviderFilter): Promise<IPageResponse<ServiceProvider>> => {
        return get('merchant/api/merchantBank/getList', params)
    },


    //处理详情
    getProcessingDetail: (id: number): Promise<ServiceProvider> => {
        return get('merchant/api/merchant/getOneById', {
            id
        })
    },
    // 后台管理考核处理保存接口
    saveMerchant: (params: ServiceProvider): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/service/exam/save/merchant', params)
    },

    // 服务商考核处理保存接口
    saveService: (params: ServiceProvider): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/service/exam/merchant/save/admin', params)
    },
    // 分页获取考核条目
    getExamItem: (params: ServiceProviderFilter): Promise<IPageResponse<ServiceProvider>> => {
        return get('/mice-bid/api/mice/merchant/exam/item/page', params)
    },


    // 查询询价完成的酒店列表
    getHotelList: (params: ServiceProviderFilter): Promise<IPageResponse<ServiceProvider>> => {
        return get('/mice-bid/api/mice/platform/price-inquiry/resource-hotel-price-inquiry/selectList', params)
    },
    // 新增
    addServiceProvider: (params: ServiceProvider): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/add', params)
    },

    // 修改
    updataById: (params: ServiceProvider): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/updateById', params)
    },
    // 获取转正设置详情
    getTrialPeriodSettingDetail: (id: number): Promise<ServiceProvider> => {
        return get(`/mice-bid/api/mice/merchant/getTrialRecordById?id=${id}`)
    },
    // 获取冻结详情
    getFreezeDetail: (id: number): Promise<ServiceProvider> => {
        return get(`/mice-bid/api/mice/merchant/getFreezeRecordById?id=${id}`)
    },
}
