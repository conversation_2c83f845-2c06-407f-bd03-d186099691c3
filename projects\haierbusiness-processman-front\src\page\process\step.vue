<script lang="ts" setup>
import { Select as hSelect, SelectOption as hSelectOption, Row as hRow, Col as hCol, Form as hForm, FormItem as hFormItem,
     Input as hInput, Button as hButton, Space as hSpace, Card as hCard, Divider as hDivider, Modal, message, Popconfirm as hPopconfirm } from 'ant-design-vue';
import { PlusOutlined, MinusCircleOutlined, ExclamationCircleFilled  } from '@ant-design/icons-vue';
import { computed, ref, watch, createVNode } from "vue";
import type { Ref } from "vue";
import {
    IStepsInfo, ProcessRoleConstant, IOperatorsInfo, IUserListRequest, IUserInfo
} from '@haierbusiness-front/common-libs';
import {
    guid
} from '@haierbusiness-front/utils';
import { useRequest, usePagination } from 'vue-request';
import { userApi } from '@haierbusiness-front/apis';
import { debounce, forEach } from 'lodash';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue'

interface Props {
    enterpriseCode: string
    data: Array<IStepsInfo> | null
    assist:boolean
}

// const stepInfo = ref<{ steps: Array<IStepsInfo> }>({ steps: []})

const props = withDefaults(defineProps<Props>(), {
    enterpriseCode: ''
});

const userNameChange = (userInfo: IUserInfo | undefined, executor: IOperatorsInfo) => {
    if (!userInfo) {
        executor.approver = ''
        executor.approverName = ''
        return
    }
    executor.approver = userInfo.username
    executor.approverName = userInfo.nickName
}

const substituteUserNameChange = (userInfo: IUserInfo | undefined, executor: IOperatorsInfo) => {
    if (!userInfo) {
        executor.substituteApprover = ''
        executor.substituteApproverName = ''
        return
    }
    executor.substituteApprover = userInfo.username
    executor.substituteApproverName = userInfo.nickName
}

const roleChange = (value: string, executor: IOperatorsInfo) => {
    const role = roles.value.find(o => o?.code === value)
    if(role) {
        executor.roleName = role?.desc
    }
}

const substituteRoleChange = (value: string, executor: IOperatorsInfo) => {
    const role = roles.value.find(o => o?.code === value)
    if(role) {
        executor.substituteRoleName = role?.desc
    }
}

const stepInfo: Ref<{ steps: Array<IStepsInfo> }> = ref(
    {
        steps: props.data ? [...props.data] as Array<IStepsInfo> : []
    }
);

const addStep = () => {
    stepInfo.value.steps.push({
        guid: guid(),
        operators: []
    })
}

const removeStep = (sguid: string) => {
    Modal.confirm({
        title: '确定要删除吗?',
        icon: createVNode(ExclamationCircleFilled),
        onOk() {
            const stepIndex = stepInfo.value.steps.findIndex(o => o.guid === sguid)
            if(stepIndex > -1) {
                stepInfo.value.steps.splice(stepIndex, 1)
            }
        },
        onCancel() {
          
        },
      });
}

const roles = computed(() => ProcessRoleConstant.toArray())

const emit = defineEmits(["currentChange", "next"]);

const handleOk = () => {
    if (!stepInfo.value || stepInfo.value.steps.length === 0) {
        message.error('请添加步骤！')
        return
    }
    let error = '' // 未添加执行角色/人
    let requireRoleError = ''//执行角色
    let requireError = ''//执行人
    // const errorData = stepInfo.value.steps.find(o => o.operators?.length === 0)

    stepInfo.value.steps.map((item, index) => {
        const step = '步骤' + (index + 1)
        if(!item.operators || item.operators.length === 0) {
            // 筛选出未添加执行角色/人的步骤
            if(error) {
                error += '，' + step
            }
            else
                error += step
        }
        item.operators?.map(item => {
            if(!item.role) {
                requireRoleError += step
            } else if (item.role && item.role === 'Fixed' && !item.approver) {
                // 固定人
                requireError += step
            }
        })
    })
    if(error || requireRoleError || requireError) {
        error && message.error('请为' + error + '添加执行人/角色！');
        requireRoleError && message.error(requireRoleError + '存在未选择的执行角色！');
        requireError && message.error(requireError + '存在未添加的执行人！');
        return
    }

    emit("next", stepInfo.value.steps)
}

const goBack = () => {
    emit("currentChange", -1)
}

const removeExecutor = (eguid: string, sguid: string) => {
    const step = stepInfo.value.steps.find(o => o.guid === sguid)
    if(step) {
        const executorIndex = step.operators!.findIndex(o => o.guid === eguid)
        if(executorIndex > -1) {
            step.operators?.splice(executorIndex, 1)
        }
    }
}

const addExecutor = (sguid: string) => {
    const step = stepInfo.value.steps.find(o => o.guid === sguid)
    if(step) {
        step.operators!.push({
            guid: guid()
        })
        console.log(step)
    }
}

// 用户选择
const params = ref<IUserListRequest>({
    enterpriseCode: props.enterpriseCode,
    pageNum: 1,
    pageSize: 20
})

</script>

<template>
    <h-form
        ref="formRef"
        :model="stepInfo"
        style="margin-top: 20px;"
    >
        <h-row style="margin-top: 20px;" v-for="(step, index) in stepInfo.steps" :key="step.guid">
            <h-col :span="24" >
                <h-card size="small" :title="'步骤' + (index + 1)" style="width: 100%">
                    <template #extra><h-button type="link" @click="removeStep(step.guid!)">删除</h-button></template>
                    <h-form-item
                        label="步骤名称"
                        :name="['steps', index, 'name']"
                        :rules="{
                            required: true,
                            message: '请输入步骤名称',
                        }"
                    >
                        <h-input v-model:value="step.name" placeholder="步骤名称" />
                    </h-form-item>
                    <h-form-item
                        :name="['steps', index, 'type']"
                        :rules="{
                            required: true,
                            message: '请输入步骤名称',
                        }"
                    >
                        <template #label>
                            <span>流转条件</span>
                        </template>
                        <h-select v-model:value="step.type" allow-clear placeholder="步骤名称">
                            <h-select-option :value="1">全部通过则通过,一人驳回则驳回</h-select-option>
                            <h-select-option :value="2">一人通过则通过, 一人驳回则驳回</h-select-option>
                        </h-select>
                    </h-form-item>
                    <h-divider orientation="left">执行人/角色</h-divider>
                    <h-row style="box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);transition: box-shadow 0.3s ease;padding-top:20px;margin-top:6px;" v-for="(executor, executorIndex) in step.operators" :key="executorIndex">
                        <h-col :span="22">
                            <h-row>
                                <h-col :offset="2" :span="10">
                                    <h-form-item
                                        label="执行角色"
                                        :label-col="{ span: 6 }"
                                        :wrapper-col="{ span: 16 }"
                                        :name="['steps', executorIndex, 'role']"
                                    >
                                        <h-select
                                            v-model:value="executor.role"
                                            placeholder="请选择角色"
                                            style="width: 100%"
                                            @change="(value) =>  roleChange(value as string, executor)"
                                            allow-clear
                                        >
                                            <h-select-option v-for="(item, index) in roles" :key="index" :value="item?.code">{{ item?.desc }}</h-select-option>
                                        </h-select>
                                    </h-form-item>
                                </h-col>
                                <h-col :span="10" v-if="executor.role === 'Fixed'">
                                    <h-form-item
                                        :label-col="{ span: 6 }"
                                        :wrapper-col="{ span: 16 }"
                                        label="执行人"
                                        :name="['steps', executorIndex, 'approverName']"
                                    >
                                        <user-select :value="executor.approverName" placeholder="执行人" :cache-key="'processSubUser'" :params="params" @change="(userInfo: IUserInfo | undefined) =>  userNameChange(userInfo, executor)" />
                                    </h-form-item>
                                </h-col>
                            </h-row>
                            <!-- <h-row v-if="props.assist">
                            <h-col :offset="2" :span="10">
                                <h-form-item
                                    label="代审角色"
                                    :label-col="{ span: 6 }"
                                    :wrapper-col="{ span: 16 }"
                                    :name="['steps', executorIndex, 'role']"
                                >
                                    <h-select
                                        v-model:value="executor.substituteRole"
                                        placeholder="请选择角色"
                                        style="width: 100%"
                                        @change="(value) =>  substituteRoleChange(value as string, executor)"
                                        allow-clear
                                    >
                                        <h-select-option v-for="(item, index) in roles" :key="index" :value="item?.code">{{ item?.desc }}</h-select-option>
                                    </h-select>
                                </h-form-item>
                            </h-col>
                            <h-col :span="10" v-if="executor.substituteRole === 'Fixed'">
                                <h-form-item
                                    :label-col="{ span: 6 }"
                                    :wrapper-col="{ span: 16 }"
                                    label="执行人"
                                    :name="['steps', executorIndex, 'approverName']"
                                >
                                    <user-select :value="executor.substituteApproverName" placeholder="执行人" :cache-key="'processSubUser'" :params="params" @change="(userInfo: IUserInfo | undefined) =>  substituteUserNameChange(userInfo, executor)" />
                                </h-form-item>
                            </h-col>                                
                        </h-row> -->
                        </h-col>
                        <h-col :span="2" class="delete">
                            <MinusCircleOutlined @click="removeExecutor(executor.guid!, step.guid!)" />
                        </h-col>
                    </h-row>
                    <h-row>
                        <h-col :span="24">
                            <h-button type="dashed" block @click="addExecutor(step.guid!)" style="margin-top: 20px;">
                                <PlusOutlined />
                                添加角色
                            </h-button>
                        </h-col>
                    </h-row>
                    
                </h-card>
            </h-col>

        </h-row>
        <h-form-item>
            <h-button type="dashed" block @click="addStep" style="margin-top: 20px;">
                <PlusOutlined />
                添加步骤
            </h-button>
        </h-form-item>
        <div class="submit-btn">
            <h-button class="sub-btn" @click="goBack">上一步</h-button>
            <h-popconfirm title="是否确认提交?" @confirm="handleOk">
                <h-button type="primary" html-type="submit" class="sub-btn">提交</h-button>
            </h-popconfirm>
        </div>
    </h-form>
</template>

<style lang="less" scoped>

.submit-btn {
  width: 100%;
  display: flex;
  justify-content: space-around;
  padding-bottom: 20px;
}

.delete {
    display: flex;
    align-items: center;
    padding-bottom: 24px;
}

.userHeader {
    display: flex;
    width: 100%;
    flex-direction: row;
    color: #262626;

    .no {
        display: flex;
        flex: 1;
    }

    .phone {
        display: flex;
        flex: 1;
    }

    .name {
        display: flex;
        flex: 1;
    }

    .email {
        display: flex;
        flex: 1;
    }
}

</style>