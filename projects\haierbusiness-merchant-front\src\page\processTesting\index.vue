<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Input as HInput,
  Select as hSelect,
  SelectOption as hSelectOption,
  Tag as hTag,
  RangePicker as hRangePicker,
  Switch as hSwitch,
  message,
} from 'ant-design-vue';
import { reactive, ref, watch, onMounted, provide, inject } from 'vue';
import { getCurrentRouter, getCurrentRoute } from '@haierbusiness-front/utils';

import hTable from '@haierbusiness-front/components/htable/htable.vue';
import edit from './edit.vue';

const router = getCurrentRouter();

const state = reactive({
  processObj1: {
    id: 0,
    code: '',
    prefix: '',
    name: '流程节点1',
    nodes: [
      // {
      //   id: 0,
      //   name: '流程节点2-1',
      //   isChecked: false,
      // },
    ],
  },
  processObj2: [
    {
      key: '2-1',
      name: '流程节点2-1',
      nodes: [],
      isChecked: false,
    },
    {
      key: '2-2',
      name: '流程节点2-2',
      nodes: [],
      isChecked: false,
    },
    {
      key: '2-3',
      name: '流程节点2-3',
      nodes: [],
      isChecked: false,
    },
  ],
  processObj3: [
    {
      key: '3-1',
      name: '流程节点3-1',
      nodes: [],
      isChecked: false,
    },
    {
      key: '3-2',
      name: '流程节点3-2',
      nodes: [],
      isChecked: false,
    },
    {
      key: '3-3',
      name: '流程节点3-3',
      nodes: [],
      isChecked: false,
    },
    {
      key: '3-4',
      name: '流程节点3-4',
      nodes: [],
      isChecked: false,
    },
    {
      key: '3-5',
      name: '流程节点3-5',
      nodes: [],
      isChecked: false,
    },
  ],
  processObj4: [
    {
      key: '4-1',
      name: '流程节点4-1',
      nodes: [],
      isChecked: false,
    },
    {
      key: '4-2',
      name: '流程节点4-2',
      nodes: [],
      isChecked: false,
    },
    {
      key: '4-3',
      name: '流程节点4-3',
      nodes: [],
      isChecked: false,
    },
    {
      key: '4-4',
      name: '流程节点4-4',
      nodes: [],
      isChecked: false,
    },
  ],

  searchParam: {
    // 搜索项
  },
  tableColumns: [
    // 表头配置
    {
      title: '供应商',
      dataIndex: 'merName',
      width: '150px',
      align: 'center',
      ellipsis: true,
      fixed: 'left',
      slots: { title: 'nameTitle' },
      scopedSlots: { customRender: 'name' },
    },
    {
      title: '供应商',
      dataIndex: 'merCode',
      width: '150px',
      align: 'center',
      ellipsis: true,
      fixed: 'left',
    },
    {
      title: '图标',
      dataIndex: 'icon',
      width: '120px',
      align: 'center',
    },
    {
      title: '分类',
      dataIndex: 'merType',
      width: '120px',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '折扣简述',
      dataIndex: 'discountDesc',
      width: '120px',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '折扣值',
      dataIndex: 'discountValue',
      width: '100px',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '折扣详情',
      dataIndex: 'details',
      width: '300px',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '是否生效',
      dataIndex: 'isEnable',
      width: '100px',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '创建人',
      dataIndex: 'createBy',
      width: '120px',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '生效时间',
      dataIndex: 'takeEffectTime',
      width: '340px',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      width: '160px',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: '_operator',
      width: '180px',
      fixed: 'right',
      align: 'center',
    },
  ],
  tableAction: [
    // 自定义操作
    { name: '查看详情', backFunc: 'viewDetails', key: '1' },
    { name: '编辑', backFunc: 'edit', key: '2' },
    { name: '删除', backFunc: 'del', key: '3' },
  ],
});

const provideName = ref('we are 伐木累！');
provide('provideName', provideName.value);

// 流程点击
function proClick(proObj) {
  console.log('%c [ proObj ]-113', 'font-size:13px; background:pink; color:#bf2c9f;', proObj);
  const { proIndex, idx, isChecked } = proObj;

  if (proIndex === 1) {
    // 节点1赋值
    state.processObj1.isChecked = !isChecked;

    state.processObj1.nodes = isChecked ? [] : [...state.processObj2];
  } else {
    // 节点>1，赋值
    state['processObj' + proIndex].forEach((e) => {
      e.isChecked = false;
    });

    state['processObj' + proIndex][idx].isChecked = !isChecked;

    state['processObj' + proIndex].nodes = isChecked ? [] : [...state['processObj' + (proIndex + 1)]];
  }
}

function subBtn() {
  console.log('%c [ 提交 ]-134', 'font-size:13px; background:pink; color:#bf2c9f;');

  router.push('/merchant/processTesting/edit');
}

// table操作
function tableEmit(param) {
  console.log('%c [ param ]-204', 'font-size:13px; background:pink; color:#bf2c9f;', param);

  switch (param.backFunc) {
    case 'viewDetails':
      // 查看
      message.error('查看详情');
      break;
    case 'edit':
      // 查看
      message.error('编辑');
      break;
    case 'del':
      // 删除
      message.error('删除');
      break;

    default:
      break;
  }
}

onMounted(async () => {});
</script>

<template>
  <!-- 流程编排 -->
  <div class="processTesting">
    <div
      :class="['pro_list', state.processObj1.isChecked ? 'checkedColor' : '']"
      @click="
        proClick({
          proIndex: 1,
          idx: idx,
          isChecked: state.processObj1.isChecked,
        })
      "
    >
      {{ state.processObj1.name }}
    </div>

    <div class="pro_node">
      <div
        :class="['pro_list', item.isChecked ? 'checkedColor' : '']"
        v-for="(item, idx) in state['processObj' + (state.processObj1.id + 1)].nodes"
        @click="
          proClick({
            proIndex: 2,
            idx: idx,
            isChecked: item.isChecked,
          })
        "
      >
        {{ item.name }}
      </div>
    </div>
    <div class="pro_node">
      <div
        :class="['pro_list', item.isChecked ? 'checkedColor' : '']"
        v-for="(item, idx) in state.processObj2.nodes"
        @click="
          proClick({
            proIndex: 3,
            idx: idx,
            isChecked: item.isChecked,
          })
        "
      >
        {{ item.name }}
      </div>
    </div>
    <div class="pro_node">
      <div
        :class="['pro_list', item.isChecked ? 'checkedColor' : '']"
        v-for="(item, idx) in state.processObj3.nodes"
        @click="
          proClick({
            proIndex: 4,
            idx: idx,
            isChecked: item.isChecked,
          })
        "
      >
        {{ item.name }}
      </div>
    </div>
  </div>
  <div class="">
    <h-table
      url="merchant/api/merchantDiscount/getDiscountPage"
      :searchParam="state.searchParam"
      :table-columns="state.tableColumns"
      :table-action="state.tableAction"
      @tableEmit="tableEmit"
    >
    </h-table>
  </div>
  <div class="sub_btn">
    <edit />
    <a-button type="primary" @click="subBtn">提交</a-button>
  </div>
</template>

<style scoped lang="less">
.processTesting {
  width: 100%;
  height: 300px;
  overflow: auto;

  display: flex;
  flex: 0 0 auto;
  align-items: center;

  .pro_node {
  }
  .pro_list {
    margin-right: 36px;
    margin-bottom: 12px;
    width: 200px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    border: 1px solid #ccc;
  }
  .checkedColor {
    background: yellowgreen;
  }
}

.sub_btn {
}
</style>
