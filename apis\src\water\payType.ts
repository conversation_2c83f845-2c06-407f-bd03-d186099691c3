import { download, get, post, filepost, originalGet } from '../request'

export const waterworkPayTypeApi = {
    list: (params: any): Promise<void> => {
        return get('waterworks/api/tpayType/page', params)
    },
    listAll: (params: any): Promise<void> => {
        return get('waterworks/api/tpayType/list', params)
    },
    add: (params: any): Promise<void> => {
        return post('waterworks/api/tpayType/save', params)
    },
    update: (params: object): Promise<void> => {
        return post(`waterworks/api/tpayType/update`, params);
    },
    export: (params: any): Promise<void> => {
        return download('waterworks/api/tpayType/export', params)
    },
    delete: (ids: string): Promise<void> => {
        return post(`waterworks/api/tpayType/delete/${ids}`);
    },
}