import { IPageRequest } from "../../basic";

export class IConsultantFilter extends IPageRequest {
    begin?: string
    end?: string
    consultantName?: string
    creator?: string
    keyword?: string
    createName?: string
    nickName?: string
    username?: string
    state?:number
}


export class IConsultant {
    id?: number | null
    creator?: string
    createTime?: string
    updater?: string
    updateTime?: string
    nickName?: string
    username?: string
    description?: string
    path?: string
    gender?: string
    phone?: string
    seniority?: number
    type?: number
    state?: number

}