@import  "./scroll-style.less";
// 主题色
@import "@haierbusiness-front/components/theme/theme.css";

@font-face {
    font-family: 'HarmonyBold';
    src: url('./fonts/HarmonyBold.ttf'); /* 相对路径或者绝对路径 */
    font-weight: normal;
    font-style: normal;
  }

@baseWidth: 100vw;
@baseHeight: 100vh;

.base-container {
    height: @baseHeight;
    width: @baseWidth;
}

.float-left {
    float: left;
}

.text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}