import { get, post } from '../request'
import {
  Create_3Res,
  ICreatTrip,
  PageRes,
  IOutPerson,
  DataListRes,
  BudgeQueryRes,
  QueryCityListRes,
  MemberBudgetParams,
  MemberBudgetRes
} from '@haierbusiness-front/common-libs'
export const reasonApi = {
  // 原因分页查询
  list: (params: object): Promise<DataListRes> => {
    return post(`trip/api/reason/manage/page`, params);
  },
  // 删除原因
  remove: (id: number): Promise<DataListRes> => {
    return post(`trip/api/reason/manage/delete`, {id});
  },
  // 修改原因
  edit: (params: object): Promise<DataListRes> => {
    return post(`trip/api/reason/manage/update`, params);
  },
  // 新增原因
  save: (params: object): Promise<DataListRes> => {
    return post(`trip/api/reason/manage/create`, params);
  },
}