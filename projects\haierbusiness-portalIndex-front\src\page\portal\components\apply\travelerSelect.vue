<script setup lang="ts">
import { onMounted, ref, watch,onUnmounted } from 'vue';
import { CloseOutlined } from '@ant-design/icons-vue';
const emit = defineEmits(['change'])

interface Props {
    travelType?: number
    leftNum?: string
    list?:any
}

const props = withDefaults(defineProps<Props>(), {
    travelType: 1,
    list: []
});

// 单程，返程
const travelType = ref(props.travelType)

const personList = ref(props.list)

const travelerChange = (vals: string[]) => {
    emit('change', vals)
}


watch(props, (newValue) => {
    travelType.value = newValue.travelType
    personList.value = newValue.list
    restValue()
    value.value = personList.value.map((item: any) => {
        return item.travelUserSyId
    })
    travelerChange(value.value)
})

const value = ref([])

const restValue = () => {
    value.value = []
}

</script>

<template>
    <div class="traveler-select-component">
        <div class="ticket-item" :class="{ 'international-right-width': travelType === 3 }">
            <div class="item-labels">出行人员</div>
            <div class="item-num">
                <a-select class="apply-no" :options="props.list" :fieldNames="{ label: 'travelUserName', value: 'travelUserSyId' }" @change="travelerChange" size="small" placeholder="请添加出行人员" mode="multiple" :show-search="false" v-model:value="value" :max-tag-count="3" >
                    <template #tagRender="{ value, label, closable, onClose, option }">
                        <a-tooltip>
                            <template #title>{{ label }}</template>
                            <div class="tag">
                                <span >{{
                                label.length > 3 ? label.substring(0, 3) + '...' : label
                              }}</span>
                                <close-outlined class="close" @click="onClose" v-if="closable" /> 
                            </div>
                        </a-tooltip>
                        
                    </template>
                    <template #maxTagPlaceholder="omittedValues">
                        <a-tooltip>
                            <template #title>
                                <div v-if="omittedValues && omittedValues.length > 0" v-for="(item, index) in omittedValues" :key="index">{{ item.label[0].children }}</div>
                            </template>
                            <div class="tag">
                                <span>+ {{ omittedValues.length }}... </span>
                            </div>
                        </a-tooltip>
                    </template>
                   
                </a-select>
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
@import url('./common.less');

.tag {
    padding: 1px 10px;
    border-radius: 11px;
    color: rgba(0,0,0,0.65);
    background-color:rgba(31,35,41,0.1);
    height: 100%;
    display: flex;
    align-items: center;
    line-height: normal;
}

.close {
    margin-left: 5px;
    font-size:12px;
}

</style>

<style>
.traveler-select-component .ant-select-selector {
    
  border:none !important;
  box-shadow: none !important;
  height: 22px;
  padding: 0px !important;
  
}

.traveler-select-component .ant-select-sm {
    width: 100% !important;
}

.traveler-select-component .ant-select-selection-placeholder {
  font-size: 16px !important;
  color: rgba(0,0,0,0.35) !important;
  
  margin-left: -7px;
  /* padding-inline-end: 25px !important; */
}

.traveler-select-component .ant-select-selection-item {
  font-size: 14px !important;
  
  color: rgba(0,0,0,0.65);
}

.traveler-select-component .ant-select-selection-overflow-item-rest .ant-select-selection-item{
    background: #ffffff;
    border: none;
    height: 22px;
}


.traveler-select-component .ant-select-item-option-content {
  
}
</style>
