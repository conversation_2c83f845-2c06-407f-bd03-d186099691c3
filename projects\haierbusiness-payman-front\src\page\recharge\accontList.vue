<script setup lang="ts">
import {
  Upload as HUpload,
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps
} from 'ant-design-vue';
import type { UploadChangeParam, UploadProps } from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { DownOutlined, ExclamationCircleOutlined, PlusOutlined, UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { InvoicingApi,fileApi,virtualPayApi } from '@haierbusiness-front/apis';
import {
  PayTypeChildConstant,
  RtoPageParams,

} from '@haierbusiness-front/common-libs';
import { getCurrentRouter , errorModal, routerParam } from '@haierbusiness-front/utils';
import Eloading from '@haierbusiness-front/components/loading/ELoading.vue';
import dayjs, { Dayjs } from 'dayjs';
import { Ref, computed, getCurrentInstance, onMounted, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';

const router = getCurrentRouter()
const columns: ColumnType[] = [
  {
    title: '账户ID',
    dataIndex: 'accountNo',
    fixed: 'left',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '负责人工号',
    dataIndex: 'createBy',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '负责人姓名',
    dataIndex: 'createName',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '账户余额',
    dataIndex: 'amount',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '累计充值',
    dataIndex: 'accumulatedRecharge',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '累计消费',
    dataIndex: 'accumulatedConsumption',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '120px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<any>({type:5})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(virtualPayApi.getTradeUnionAccountPage, {
  ...searchParam.value,
});

const {
  data: exportListData,
  run: exportListApiRun,
  loading: exportListLoading,
} = useRequest(virtualPayApi.exportTradeUnionAccount);

const reset = () => {
  searchParam.value = {type:5 }
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize:10,
  });
}

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};


const gotoAccontDetails = (record: RtoPageParams) => {
  router.push({ path: "/payman/accountDetails", query: { accountNo: record.accountNo } })
}

const getStatus=(status:string)=>{
  if(status==10){
    return '待提交'
  }else if(status==20){
    return '已提交'
  }else if(status==30){
    return '支付中'
  }else if(status==40){
    return '已关闭'
  }else if(status==90){
    return '已完成'
  }
}

onMounted(()=>{
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize: 10,
  });
})
</script>

<template>

  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">

    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="accountNo">账户ID：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="accountNo" v-model:value="searchParam.accountNo" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createBy">负责人工号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="createBy" v-model:value="searchParam.createBy" placeholder=""
              autocomplete="off" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createName">负责人姓名：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="createName" v-model:value="searchParam.createName" placeholder=""
            autocomplete="off" allow-clear />
          </h-col>
        </h-row>
      
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            
            <h-button type="primary" style="margin-right: 10px" :loading="exportListLoading"
              @click="exportListApiRun(searchParam);">
              <UploadOutlined />
              导出
            </h-button>
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ x: 1550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'status'">
              {{getStatus(record.status)}}
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="gotoAccontDetails(record)">账户明细</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
