// 顾问列表页面
<script lang="ts" setup>
import { <PERSON><PERSON>, <PERSON><PERSON>, Drawer, message, Collapse, CollapsePanel } from 'ant-design-vue';
import Advisors from '@haierbusiness-front/components/mice/advisors/index.vue';
import { ref, reactive, computed, onMounted, inject } from 'vue';
import MeetingConsultantDrawer from '@haierbusiness-front/components/meetingConsultantDrawer/index.vue';

import { miceBidManOrderListApi } from '@haierbusiness-front/apis';
import { TestData } from '@haierbusiness-front/common-libs';
import { useRouter, useRoute } from 'vue-router';
// 状态变量定义
const editModalOpen = ref(false); // 编辑抽屉是否打开
const testData = ref<TestData[]>([]); // 顾问列表数据
const testDataSum = ref<TestData[]>([]); // 顾问列表数据备份
const open = ref(false); // 驳回弹窗是否打开
const reason = ref(''); // 驳回原因
const advisorsRef = ref<any>(null);

// 处理驳回操作
const handleReject = () => {
  open.value = true;
  console.log('驳回');
};
const route = useRoute();
// 确认驳回
const handleOk = async () => {
  if (reason.value !== '') {
    const res = await miceBidManOrderListApi.receive_reject({
      miceId: route.query.miceId,
      demandRejectReason: reason.value,
    });
    console.log(res);
    if (res.success) {
      message.success('驳回成功');
      open.value = false;
      router.push({ path: '/bidman/orderList/index', query: { status: '0' } });
    }
  } else {
    message.error('驳回原因不能为空！');
  }
};

// 处理接收订单
const handleReceive = async () => {
  const curDate = new Date();
  // 获取顾问统计数据
  const res = await miceBidManOrderListApi.counsellorUserCount({
    'mainCountRequest.startDateStart': curDate.getFullYear() + '-01-01 00:00:00',
    'mainCountRequest.startDateEnd': curDate.getFullYear() + '-12-31 23:59:59',
    // processNodes: 'DEMAND_PRE_INTERACT,DEMAND_PUSH,SCHEME_APPROVAL,BID_PUSH,BILL_APPROVAL',
    processId: advisorsRef.value.processId || '',
  });
  if (res.length > 0) {
    testData.value = [];
    testDataSum.value = [];
    res.forEach((item) => {
      // 初始化统计数据数组
      let arr = [
        { label: '需求互动', value: 0, code: 'DEMAND_PRE_INTERACT' },
        { label: '需求发布', value: 0, code: 'DEMAND_PUSH' },
        { label: '方案审核', value: 0, code: 'SCHEME_APPROVAL' },
        { label: '竞价推送', value: 0, code: 'BID_PUSH' },
        { label: '账单审核', value: 0, code: 'BILL_APPROVAL' },
      ];
      // 更新统计数据
      item.results.forEach((subItem) => {
        arr.forEach((item1) => {
          if (item1.code === subItem.processNode) {
            item1.value = subItem.counts;
          }
        });
      });
      // 构建顾问数据对象
      testData.value.push({
        name: item.nickName,
        desc: item.description,
        username: item.username,
        isPerson: advisorsRef.value.intentionConsultantUserCode === item.username || '',
        link: item.username,
        phone: item.phone,
        isRecommend: item.recommend,
        path: item.path,
        performance: [
          {
            label: '2025年承担总量',
            value: item.totalCount + '单 ',
            // + item.totalPrice + '元'
          },
          {
            label: '正在进行中',
            value: item.doingCount + '单 ',
            //  + item.doingPrice + '元'
          },
        ],
        statistics: arr,
      });
    });
    // 按推荐状态排序
    testData.value = testData.value.sort((a, b) =>
      b.isRecommend ? b.isRecommend - a.isRecommend : b.isPerson - a.isPerson,
    );
    testDataSum.value = testData.value;
  }
  editModalOpen.value = true;
};
const router = useRouter();
const isCloseLastTab = inject<Ref<boolean>>('isCloseLastTab');
// 分配人员
const assignPerson = async (item) => {
  const res = await miceBidManOrderListApi.userAssign({
    miceId: route.query.miceId,
    consultantUserCode: item.username,
    consultantUserName: item.name,
    consultantUserPhone: item.phone,
  });
  if (res.success) {
    message.success('分配成功');
    editModalOpen.value = false;
    router.push({ path: '/bidman/orderList/index', query: { status: '0' } });
    isCloseLastTab.value = true;
  }
};

// 处理发布
const handlePublish = () => {
  console.log('需求发布');
};

// 搜索关键词
const searchKeyword = (value) => {
  if (value) {
    testData.value = testDataSum.value.filter((item) => {
      if (item.username.includes(value) || item.name.includes(value)) return item;
    });
  } else testData.value = testDataSum.value;
};
</script>
<template>
  <!-- 顾问列表页面主容器 -->
  <div>
    <!-- 顾问列表组件 -->
    <Advisors preview-source="demandOne" ref="advisorsRef">
      <!-- 头部插槽：备忘录和日志按钮 -->
      <template #header>
        <!-- <Button size="small" type="primary" class="mr10">备忘录</Button>
        <Button size="small" type="primary" class="mr10">日志</Button> -->
      </template>
      <!-- 底部插槽：操作按钮组 -->
      <template #footer v-if="!editModalOpen">
        <Button v-show="false" size="small" style="margin-right: 10px" type="primary" @click="handlePublish"
          >需求驳回</Button
        >
        <Button v-show="false" size="small" style="margin-right: 10px" type="primary" @click="handlePublish"
          >需求发布</Button
        >
        <Button
          v-show="route.query.status === '1'"
          size="small"
          style="margin-right: 10px"
          type="primary"
          @click="handleReceive"
          >订单接收</Button
        >
        <Button
          v-show="route.query.status === '1'"
          size="small"
          @click="handleReject"
          style="margin-right: 10px"
          class="reject-btn"
          type="primary"
          danger
          >订单驳回</Button
        >
      </template>
    </Advisors>

    <!-- 驳回原因弹窗 -->
    <a-modal v-model:open="open" title="驳回" @ok="handleOk">
      <p>驳回原因</p>
      <a-textarea v-model:value="reason" :rows="4" :maxLength="200" placeholder="请输入驳回原因" />
    </a-modal>

    <!-- 会务顾问抽屉组件 -->
    <meeting-consultant-drawer
      title="会务顾问"
      v-model="editModalOpen"
      class="meeting-consultant-drawer"
      :items="testData"
      @search="searchKeyword"
      @assign="assignPerson"
    />
  </div>
</template>
<style lang="less" scoped>
:deep(.ant-btn-default) {
  padding: 3px 8px;
  height: 32px;
  width: 80px;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #4e5969;
  line-height: 22px;
  text-align: center;
  font-style: normal;
}
:deep(.ant-btn-primary) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 22px;
  text-align: center;
  font-style: normal;
  padding: 3px 8px;
  height: 32px;
  width: 80px;
  background: #1868db;
  border-radius: 2px;
}
.reject-btn {
  background: #f5222d;
}
</style>
