<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import {
  DownOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
  PhoneFilled
} from '@ant-design/icons-vue';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';

import { getEnumOptions } from '@haierbusiness-front/utils';

import { teamListApi } from '@haierbusiness-front/apis';
import { TGetListParams, IEnterprise,CityItem, TeamListStatusAdminEnum, IUserListRequest, IUserInfo } from '@haierbusiness-front/common-libs';
import { errorModal, routerParam } from '@haierbusiness-front/utils';
import dayjs, { Dayjs } from 'dayjs';
import { computed, onMounted, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useRouter } from 'vue-router';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables';
import WorkOrderList from '../components/dialog/workOrderList.vue';
import { userInfo } from 'os';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';


const store = applicationStore();

const { loginUser } = storeToRefs(store);


const router = useRouter();
const columns: ColumnType[] = [
  {
    title: '申请单号',
    dataIndex: 'destineNo',
    width: '260px',
    align: 'center',
    fixed: 'left',
  },
  {
    title: '出差类型',
    dataIndex: 'evectionType',
    align: 'center',
    width: '100px',
  },
  {
    title: '产品类型',
    dataIndex: 'destineInfo',
    align: 'center',
    width: '200px',
  },

  {
    title: '起止城市',
    dataIndex: 'beginCityName',
    align: 'center',
    width: '200px',
  },
  {
    title: '行程日期',
    dataIndex: 'beginDate',
    width: '200px',
    align: 'center',
  },
  {
    title: '订单状态',
    dataIndex: 'destineStatus',
    align: 'center',
    width: '100px',
  },
  {
    title: '申请人',
    dataIndex: 'createName',
    align: 'center',
    width: '200px',
  },
  {
    title: '申请时间',
    dataIndex: 'gmtCreate',
    align: 'center',
    width: '200px',
  },

  {
    title: '联系人',
    dataIndex: 'contactUserName',
    align: 'center',
    width: '200px',
  },

  {
    title: '订单负责人',
    dataIndex: 'transactorName',
    align: 'center',
    width: '200px',
  },

  {
    title: '汇总金额',
    dataIndex: 'totalAmount',
    align: 'center',
    width: '200px',
  },

  {
    title: '节省金额',
    dataIndex: 'saveAmount',
    align: 'center',
    width: '200px',
  },

  {
    title: '是否出票',
    dataIndex: 'ticketFlag',
    align: 'center',
    width: '200px',
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center',
  },
];

const goToDetail = (id?: string) => {
  router.push({ path: "/teamDetail", query: { id:id  }  } )
}

// 价格录入页面
const goToPrice = (id?: string) => {
  router.push({ path: "/teamPrice", query: { id:id  }  } )
}

const onTimeChange = (dateRange: string[]) => {
  if (dateRange && dateRange.length === 2) {
    searchParam.value.beginDate = dateRange[0];
    searchParam.value.endDate = dateRange[1];
  } else {
    searchParam.value.beginDate = '';
    searchParam.value.endDate = '';
  }
};

// 申请人 
// 用户选择
const params = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});
// 申请人
const userNameChange = (userInfo: IUserInfo) => {
  searchParam.value.userNameSq = userInfo?.nickName || ''
  searchParam.value.userName = userInfo?.username || ''
}
// 联系人
const userNameChangeLx = (userInfo: IUserInfo) => {
  searchParam.value.userNameLx = userInfo?.nickName || ''
  searchParam.value.contactUserCode = userInfo?.username || ''
}
// 负责人
const userNameChangeFz = (userInfo: IUserInfo) => {
  searchParam.value.userNameFz = userInfo?.nickName || ''
  searchParam.value.transactorCode = userInfo?.username || ''
}


// 城市选择
const chosedBeginCityName = ref<string>('');
const chosedEndCityName = ref<string>('');

const chosedBeginCity = (city: CityItem, index: number, i: number) => {
  searchParam.value.beginCityCode = city.citycode;
  chosedBeginCityName.value = city.name;
};

const chosedEndCity = (city: CityItem, index: number, i: number) => {
  searchParam.value.endCityCode = city.citycode;
  chosedEndCityName.value = city.name;
};




// 订单状态
const teamState = computed(() => {
  return getEnumOptions(TeamListStatusAdminEnum, true);
});
const productList = ref([
  {
    label: '国内机票',
    plain: true,
    value: 0,
    disabled: false,
  },
  {
    label: '国际机票',
    plain: true,
    value: 1,
    disabled: false,
  },
  {
    label: '酒店',
    plain: true,
    value: 2,
  },
]);
const toLabel = (str: string) => {
  if (!str) {
    return '-'
  }
  const strList = str.split(',')
  const name:Array<string> = []
  strList.forEach(item => {
    productList.value.forEach(ele => {
      if (item == ele.value) {
        name.push(ele.label)
      }
    });
  })
  return name.join('、')
}



const searchParam = ref<TGetListParams>({});
const workOrderListShow = ref<boolean>(false)
const rowData = ref<any>({})
// 明细弹窗
const detailVisible = ref<boolean>(false);

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(teamListApi.list, {
  defaultParams: [{}],
  manual: false,
});

const reset = () => {
  searchParam.value = {};
  chosedBeginCityName.value = ''
  chosedEndCityName.value = ''
  handleTableChange({ current: 1, pageSize: 10 })
};

const exportList = () => {
  if(pagination.value.total > 1000) {
    hModal.info({
    title: '所选数据超出1000条,无法导出,请分批次导出!',
    onOk() {
      console.log('OK');
    },
    });
    return false
  }
  const params = {
    ...searchParam.value,
    "pageNum": 1,
    "pageSize": 1000,
  }
  teamListApi.exportList(params);
}

const dataSource = computed(
  () =>
    data.value?.records?.filter((item:any) => {
      return item;
    }) || [],
);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

// 新增表单相关
const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } = useEditDialog<
  IEnterprise,
  IEnterprise
>(teamListApi, '通话记录', () =>
  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize,
  }),
);



</script>

<template>
  
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 30px">
        <h-row :align="'middle'">
          <h-col :span="2" style="text-align: right;  padding-right: 10px">
            <label for="mobile">乘机人:</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="mobile"
              v-model:value="searchParam.destineNo"
              
              placeholder="乘机人"
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="mobile">申请人:</label>
          </h-col>
          <h-col :span="4">
            <user-select
              :value="searchParam.userNameSq"
              :params="params"
              placeholder="申请人"
              @change="(userInfo: IUserInfo) =>  userNameChange(userInfo)"
            />
          </h-col>

          


          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="mobile">出差类型:</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchParam.evectionType" style="width: 100%" placeholder="出差类型">
              <h-select-option :value="0">因公</h-select-option>
              <h-select-option :value="1">因私</h-select-option>
            </h-select>
          </h-col> 

          <h-col :span="2" style="text-align: right;  padding-right: 10px">
            <label for="mobile">结算单号:</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="mobile"
              v-model:value="searchParam.destineNo"
              placeholder="结算单号"
              autocomplete="off"
              allow-clear
            />
          </h-col>

          <!-- 
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="mobile">出发城市:</label>
          </h-col>
          <h-col :span="4">
            <city-chose :value="chosedBeginCityName" @chosedCity="chosedBeginCity"></city-chose>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="mobile">目的城市:</label>
          </h-col>
          <h-col :span="4">
            <city-chose :value="chosedEndCityName" @chosedCity="chosedEndCity"></city-chose>
          </h-col> -->
        </h-row>

        <h-row :align="'middle'" style="margin-top: 16px;">
          <h-col :span="2" style="text-align: right;  padding-right: 10px">
            <label for="mobile">订单编号:</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="mobile"
              v-model:value="searchParam.destineNo"
              placeholder="订单编号"
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col :span="2" style="text-align: right;  padding-right: 10px">
            <label for="mobile">原订单编号:</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="mobile"
              v-model:value="searchParam.destineNo"
              placeholder="原订单编号"
              autocomplete="off"
              allow-clear
            />
          </h-col>

          <h-col :span="2" style="text-align: right;  padding-right: 10px">
            <label for="mobile">票号:</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="mobile"
              v-model:value="searchParam.destineNo"
              placeholder="票号"
              autocomplete="off"
              allow-clear
            />
          </h-col>

          <h-col :span="2" style="text-align: right;  padding-right: 10px">
            <label for="mobile">原票号:</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="mobile"
              v-model:value="searchParam.destineNo"
              placeholder="原票号"
              autocomplete="off"
              allow-clear
            />
          </h-col>
          
        </h-row>

     
        <h-row :align="'middle'" style="margin-top: 16px;">
          <h-col :span="2" style="text-align: right;  padding-right: 10px">
            <label for="mobile">申请单号:</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="mobile"
              
              v-model:value="searchParam.destineNo"
              placeholder="申请单号"
              autocomplete="off"
              allow-clear
            />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="mobile">产品类型:</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchParam.destineInfo" placeholder="产品类型" style="width: 100%">
              <h-select-option value="">全部</h-select-option>
              <h-select-option v-for="(item, index) in productList" :key="index" :value="item.value">{{
                item.label
              }}</h-select-option>
            </h-select>
          </h-col>

          <h-col :span="2" style="text-align: right;  padding-right: 10px">
            <label for="mobile">出发时间:</label>
          </h-col>
          <h-col :span="4">
            <h-date-picker
            placeholder="请选择出发时间"
              v-model:value="searchParam.timeRange"
              value-format="YYYY-MM-DD"
              @change="onTimeChange"
              style="width: 100%"
            />
          </h-col>

          <h-col :span="2" style="text-align: right;  padding-right: 10px">
            <label for="mobile">到达时间:</label>
          </h-col>
          <h-col :span="4">
            <h-date-picker
            placeholder="请选择到达时间"
              v-model:value="searchParam.timeRange"
              value-format="YYYY-MM-DD"
              @change="onTimeChange"
              style="width: 100%"
            />
          </h-col>

        </h-row>

        <h-row style="margin-top: 16px;">
          <h-col :span="24" style="text-align: right">
            <h-button style="margin-right: 10px" @click="exportList">导出</h-button>
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :ellipsis="true"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="dataSource"
          :pagination="pagination"
          :scroll="{  x: 1600 }"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            
            <template v-if="column.dataIndex === 'destineNo'">
              <div class="color-main" @click="goToDetail(record.id)">{{record.destineNo}}</div>
            </template>
            <template v-if="column.dataIndex === 'evectionType'">
              {{record.evectionType ? '因私' : '因公'}}
            </template>

            <template v-if="column.dataIndex === 'destineInfo'">
              {{ toLabel(record.destineInfo) }}
            </template>

            <template v-if="column.dataIndex === 'beginCityName'">
              {{ record.beginCityName  }}- {{ record.endCityName }}
            </template>

            <template v-if="column.dataIndex === 'beginDate'">
              {{ record.beginDate  }}- {{ record.endDate }}
            </template>

            <template v-if="column.dataIndex === 'destineStatus'">
              {{ TeamListStatusAdminEnum[record.destineStatus] }}
            </template>

            <template v-if="column.dataIndex === 'createName'">
              {{ `${record.createName}(${record.createBy})`}}
            </template>

            <template v-if="column.dataIndex === 'contactUserName'">
              {{ `${record.contactUserName}(${record.contactUserCode})`}}
            </template>

            <template v-if="column.dataIndex === 'transactorName'">
              <div v-if="record.transactorName">
                {{ `${record.transactorName}(${record.transactorCode})`}}
              </div>
              <div v-else>
                -
              </div>
            </template>

            <template v-if="column.dataIndex === 'totalAmount'">
              {{ `¥ ${record.totalAmount}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') }}
            </template>

            <template v-if="column.dataIndex === 'saveAmount'">
              {{ `¥ ${record.saveAmount}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') }}
            </template>
            
            <template v-if="column.dataIndex === 'ticketFlag'">
              {{ record.ticketFlag? '是': '否' }}
            </template>
            
           
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" v-if="record.destineStatus == '30' && record.transactorCode == loginUser?.username" @click="goToPrice(record.id)">录入价格</h-button>
              <h-button type="link" v-if="record.destineStatus != '30' && !record.transactorCode" @click="goToDetail(record.id)">接收订单</h-button>
              <h-button type="link" @click="goToDetail(record.id)">查看详情</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
:deep(.ant-picker-range ) {
  width: 100%;
}
.color-main {
  color: #1677ff;
  cursor: pointer;
}
</style>
