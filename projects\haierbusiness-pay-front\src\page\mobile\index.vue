<script setup lang="ts">
import {showFailToast, But<PERSON> as <PERSON><PERSON><PERSON><PERSON>, showDialog, showSuccessToast, NoticeBar} from 'vant';
import 'vant/es/notice-bar/style'
import {payApi, compositionPayApi, virtualPayApi} from '@haierbusiness-front/apis';
import {IPayData, IQueryVirtualAccountsResponse, PaySourceConstant } from '@haierbusiness-front/common-libs';
import {computed, PropType, ref} from 'vue';
import {removeStorageItem} from '@haierbusiness-front/utils';
import MobileCompositionPay from './compositionPay.vue'
import MobileCoinHaierPay from './coinHaierPay.vue'
import MobileTravelCoinHaierPay from './travelCoinHaierPay.vue'
import MobileVirtualPay from './virtualPay.vue'
import {useRequest} from 'vue-request'

interface Props {
  payData: IPayData,
  payTypes: number[],
  accounts: IQueryVirtualAccountsResponse[]
}

const props = withDefaults(defineProps<Props>(), {})

const payData = computed(() => props.payData)
const payTypes = computed(() => props.payTypes)

const emit = defineEmits(["setIsPayComplete"]);
// 移动端支付相关
const checked = ref()
// 钱包底部弹出层
const show = ref(false)
const account = ref<number | null>()
const loading = ref(false)

const setAccountChecked = (value: string) => {
  accountChecked.value = value
  checked.value = '5'
  changeVirtualAccount()
}

const setAccount = (value: number | null) => {
  account.value = value
}

const setShow = (value: boolean) => {
  show.value = value
}

const setChecked = (value: string) => {
  checked.value = value
  accountChecked.value = ''
  virtualAccount.value = {}
}

//#region 企业支付相关

const virtualShow = ref(false)
const accountChecked = ref()

const setVirtualShow = (value: boolean) => {
  virtualShow.value = value
}

//#endregion

const payH5 = (type: string) => {
  compositionPayApi.h5(
      {
        orderCode: payData.value.orderCode,
        applicationCode: payData.value.applicationCode,
        payTypes: payData.value.payTypes,
        username: payData.value.username,
        providerCode: payData.value.providerCode,
        amount: Number(payData.value.amount),
        notifyUrl: payData.value.notifyUrl,
        orderDetailsUrl: payData.value.orderDetailsUrl,
        callbackUrl: payData.value.callbackUrl,
        description: payData.value.description,
        payload: payData.value?.payload,
        compositionPayType: type,
        paymentMethod: 2,
        paySource: PaySourceConstant.MOBILE.type
      },
      {
        applicationCode: payData.value.applicationCode,
        nonce: payData.value.hbNonce,
        timestamp: payData.value.hbTimestamp,
        sign: payData.value.sign,
        excludes: "compositionPayType,paymentMethod,paySource"
      }
  ).then(it => {
    if (it.url) {
      window.location.href = it.url
    }
  }).finally(() => {
    loading.value = false
  })
}

const virtualAccountsData = computed(() => props?.accounts || [])

const changeVirtualAccount = () => {
  const selectAccount = virtualAccountsData.value.filter(it => it.accountNo === accountChecked.value)
  virtualAccount.value = selectAccount[0]
}

const virtualAccount = ref<IQueryVirtualAccountsResponse>({})

const {
  data,
  run: payRun,
  loading: payLoading,
} = useRequest(
    virtualPayApi.pay, {
      onSuccess: () => {
        if (checked.value === '5') {
          showSuccessToast('支付成功')
          virtualShow.value = false
        }
        
        payH5Complete(true)
      }
    }
);
const pay = (captcha?: string) => {
  payRun(
      {
        accountNo: accountChecked.value,
        captcha: captcha ?? '',
        // - 通用参数
        orderCode: payData.value?.orderCode,
        applicationCode: payData.value?.applicationCode,
        payTypes: payData.value?.payTypes,
        username: payData.value?.username,
        providerCode: payData.value?.providerCode,
        amount: Number(payData.value?.amount),
        notifyUrl: payData.value?.notifyUrl,
        orderDetailsUrl: payData.value?.orderDetailsUrl,
        callbackUrl: payData.value?.callbackUrl,
        description: payData.value?.description,
        payload: payData.value?.payload,
        paymentMethod: 2,
        paySource: PaySourceConstant.MOBILE.type
      },
      {
        applicationCode: payData.value?.applicationCode,
        excludes: "accountNo,captcha,paymentMethod,paySource",
        nonce: payData.value?.hbNonce,
        timestamp: payData.value?.hbTimestamp,
        sign: payData.value?.sign,
      }
  )
}

const mobilePay = () => {
  if (!checked.value) {
    showFailToast("请选择支付方式!");
    return
  }
  if (checked.value === '1') {
    // 支付宝
    loading.value = true
    payH5('ALPay')
  } else if (checked.value === '2') {
    // 微信
    payH5('WXPay')
  } else if (checked.value === '3') {
    // 云闪付
    payH5('WXPay')
  } else if (checked.value === '4') {
    // 福利积分
    if (!account.value) {
      showDialog({message: '您的账户不存在,请前往“iHaier-福利积分开户”开户，并在“福利积分消费”充值“福利超市金币”。'});
      return
    }
    show.value = true
  } else if (checked.value === '5') {
    // 虚拟卡
    if(virtualAccount.value.smsCaptchaFlag === 2) {
      virtualShow.value = true
    } else {
      pay()
    }
    
  } else if (checked.value === '6') {
    // 福利积分
    if (!account.value) {
      showDialog({message: '您的账户不存在,请前往“iHaier-福利积分开户”开户，并在“福利积分消费”充值“海尔机票预订”。'});
      return
    }
    show.value = true
  }
}

/**
 * 支付完成
 */
const payH5Complete = (result: boolean) => {
  emit('setIsPayComplete', result)
  let countdown = 3;
  removeStorageItem("pay-data-code" + payData.value.orderCode);
  if (payData.value.callbackUrl) {
    setTimeout(() => {
      window.location.href = payData.value.callbackUrl!!
    }, 2000);
  } else {
    setTimeout(() => {
      // 跳转白屏不美观,期望关闭标签页
      window.location.href = "about:blank";
      window.close();
    }, 2000);
  }
}

</script>

<template>
  <div class="mobile-container">
    <notice-bar
      v-if="checked === '6'"
      left-icon="volume-o"
      text="福利积分预定机票,结算方式为企业对公,海尔商旅不对个人开具发票或行程单。"
    />
    <div class="price-container">
      <div class="price">
        <span class="rmb">¥</span>
        <span>{{ Number(payData.amount).toFixed(2) || '???' }}</span>
      </div>
      <div class="order-no">
        订单号:{{ payData.orderCode }}
      </div>
    </div>
    <div class="pay-method">
      <div class="pay-font">支付方式</div>
      <mobile-virtual-pay v-if="virtualAccountsData && virtualAccountsData.length > 0 && payTypes?.includes(5)"
                          :accounts="props.accounts" :checked="checked" :account-checked="accountChecked" :param="payData"
                          @setChecked="setChecked" @setAccountChecked="setAccountChecked" @payComplete="payH5Complete"
                          :show="virtualShow" @setShow="setVirtualShow" @pay="pay" />

      <mobile-composition-pay v-if="payTypes?.includes(2)" :checked="checked" @setChecked="setChecked"/>

      <mobile-coin-haier-pay v-if="payTypes?.includes(4)" :param="payData" :checked="checked" :show="show"
                             @setChecked="setChecked" @setShow="setShow" @setAccount="setAccount"
                             @payComplete="payH5Complete"/>
                             
      <mobile-travel-coin-haier-pay v-if="payTypes?.includes(6)" :param="payData" :checked="checked" :show="show"
                             @setChecked="setChecked" @setShow="setShow" @setAccount="setAccount"
                             @payComplete="payH5Complete"/>
    </div>
    <div class="btn-container">
      <van-button type="primary" class="btn-style" round block @click="mobilePay" :loading="loading">确认交易
      </van-button>
    </div>
  </div>
</template>

<style scoped lang="less">

.mobile-container {
  display: flex;
  width: 100%;
  flex-direction: column;
  background-color: #F5F5F5;
  min-height: 100vh;
  position: relative;

  .complete-logo {
    display: flex;
    width: 100%;
    justify-content: center;
    padding-top: 50px;

    .png {
      display: flex;
      width: 33%;

      .picture {
        width: 100%;
        height: 100%;
      }
    }
  }

  .success-font {
    display: flex;
    justify-content: center;
    padding-top: 8px;

    .success {
      font-size: 24px;
      font-weight: bold;
    }
  }

  .order-container {
    display: flex;
    width: 100%;
    flex-direction: column;
    padding-top: 80px;
    font-size: 20px;
    font-weight: bold;
    align-items: center;

    .order-no {
      display: flex;
    }

    .money {
      display: flex;
      align-items: baseline;

      .amount {
        font-size: 42px;
        padding-left: 5px;
      }
    }
  }

  .price-container {
    display: flex;
    width: 100%;
    padding: 30px 0;
    justify-content: center;
    flex-direction: column;
    align-items: center;

    .price {
      display: flex;
      flex-direction: row;
      font-size: 42px;
      font-weight: bold;
      align-items: baseline;

      .rmb {
        font-size: 28px;
      }
    }

    .order-no {
      display: flex;
      font-size: 14px;
      color: #969799;
    }
  }

  .pay-method {
    display: flex;
    width: 100%;
    flex-direction: column;

    .pay-font {
      display: flex;
      padding: 0 0 10px 16px;
      font-weight: bold;
      color: #646565;
    }


  }

  .btn-container {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 10px 16px;
    display: flex;
    justify-content: center;


    .btn {
      display: flex;
      width: 50%;
      padding-bottom: 80px;

      .btn-style {
        height: 35px;
        //border-radius: 19px;
      }
    }

    .btn-style {
      height: 40px;
      //border-radius: 19px;
    }
  }

  @supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
    .btn-container {
      bottom: calc(constant(safe-area-inset-bottom));
      bottom: calc(env(safe-area-inset-bottom));
    }
  }

}

</style>

<style>
:root:root {
  --van-button-primary-background: #0073E5;
  --van-radio-checked-icon-color: #0073E5;
  --van-password-input-background: #F2F2F2;
}
</style>