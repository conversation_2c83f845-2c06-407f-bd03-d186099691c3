import { get, post } from '../request'

import { workOrderQuery } from './../../../common-libs/src/callcenter/model/callcenter';

import {
  Result,
  RHotelParams,
  RCancelParams,
  RHotel,
  RHotelSingleParams,
  ROrderParams,
  ROrderDetailParams,
  FileUploadResponse
} from '@haierbusiness-front/common-libs'

export const hotelListApi = {
  // 酒店列表查询
  list: (params?: any): Promise<Result> => {
    return get(`hotel/api/hotel/getHotelList`, params);
  },
  // 获取酒店品牌 
  brandlist: (params?: any): Promise<Result> => {
    return get(`hotel/api/hotel/getHotelBrand`, params);
  },
  // 获取酒店详情
  hotelInfo: (params?: any): Promise<Result> => {
    return get(`hotel/api/hotel/getHotel`, params);
  },
  // 获取某个酒店房型列表
  roomTypeList: (params?: any): Promise<Result> => {
    return get(`hotel/api/hotel/getRoomTypeList`, params);
  },
}

export const localHotelApi = {

  // 查询酒店列表
  hotelList: (params: RHotelParams): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/room/hotel/list.json', params, {}, null, true)
  },

  // 获取酒店详情
  hotelSingle: (params: RHotelSingleParams): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/room/hotel/single.json', params, {}, null, true)
  },

  // 获取酒店包间详情
  pagePrivateRoom: (params: RHotelSingleParams): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/hotelSupply/pagePrivateRoom', params, {}, null, true)
  },

  // 查询订餐订单列表
  orderList: (params: ROrderParams): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/room/hotelOrder/list.json', params, {}, null, true)
  },

  // 查询订餐订单详情
  orderDetail: (params: ROrderDetailParams): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/room/hotelOrder/detail.json', params, {}, null, true)
  },

  // 保存餐厅预约
  save: (params: RHotel): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/room/hotelOrder/save.json', params, {
      '_BIZ_ORDER_SOURCE_': params.orderSource
    }, null, true)
  },
  // 获取跳转支付链接
  prePay: (params: RHotel): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/room/pay/prePay', params, {}, null, true)
  },
  

  // 文件上传
  upload: (params: any): Promise<FileUploadResponse> => {
    return post(`/businesstravel/api/common/v1/file/upload?path=localrest%2FseatOrder`, params, { 'content-type': 'multipart/form-data' }, null, true)
  },
  // 文件下载
  download: (id: number): Promise<FileUploadResponse> => {
    return get(`/businesstravel/api/common/v1/file/download/${id}`, { 'content-type': 'multipart/form-data' }, {}, null, true)
  },

  // 订单取消
  cancel: (params: RCancelParams): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/room/hotelOrder/cancel.json', params, {}, null, true)
  },

  // logs: "/api/localhotel/v1/base/logs.json", //订单日志
  logs: (params: RCancelParams): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/base/logs.json', params, {}, null, true)
  },

  //   findCitys: "/api/localhotel/v1/hotelSupply/findCityList", //查询城市列表
  // findRegions: "/api/localhotel/v1/hotelSupply/findRegionList", //查询酒店行政区数据
  // findCateClass: "/api/localhotel/v1/hotelSupply/findCateClass", //查询酒店菜系数据
  // findCateType: "/api/localhotel/v1/hotelSupply/findCateType", //查询酒店餐类数据
  // findService: "/api/localhotel/v1/hotelSupply/findService", //查询酒店特色服务数据
  // findLandMark: "/api/localhotel/v1/hotelSupply/findLandMarkTotalList", //查询酒店地标数据

  // 查询酒店菜系数据
  findCateClass: (params: RCancelParams): Promise<Result> => {
    return get('/businesstravel/api/localhotel/v1/hotelSupply/findCateClass', params, {}, null, true)
  },
  //查询酒店餐类数据
  findCateType: (params: RCancelParams): Promise<Result> => {
    return get('/businesstravel/api/localhotel/v1/hotelSupply/findCateType', params, {}, null, true)
  },

  //查询城市列表
  findCityList: (params: RCancelParams): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/hotelSupply/findCityList', params, {}, null, true)
  },

  //查询酒店行政区数据
  findRegionList: (params: RCancelParams): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/hotelSupply/findRegionList', params, {}, null, true)
  },

  // 查询订餐折扣政策
  // /restaurant/hotel/dataList.json
  dataList: (params: RCancelParams): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/restaurant/hotel/dataList.json', params, {}, null, true)
  },

  // 查询阅读须知
  getReadInstruction: (params: RCancelParams): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/readInstruction/findById', params, {}, null, true)
  },


  // 查询商圈列表
  getBusinessCircle: (params: RCancelParams): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/businessCircle/page', params, {}, null, true)
  },

  // 新增常住人
  saveFrequentUser: (params: any): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/frequentUser/save', params, {}, null, true)
  },

  // 删除常住人
  deleteFrequentUser: (params: any): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/frequentUser/delete', params, {}, null, true)
  },

  // 查询常住人列表
  searchFrequentUser: (params: any): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/frequentUser/search', params, {}, null, true)
  },

  // 更新常住人
  updateFrequentUser: (params: any): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/frequentUser/update', params, {}, null, true)
  },

  // 提交入住人清单
  putCheckInUser: (params: any): Promise<Result> => {
    return post('/businesstravel/api/localhotel/v1/checkInUser/put', params, {}, null, true)
  },
}
