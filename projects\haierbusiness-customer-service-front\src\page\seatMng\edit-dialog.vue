<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  Switch as hSwitch,
  CheckboxGroup as hCheckboxGroup,
  Checkbox as hCheckbox,
  Image as hImage,
  message,
} from 'ant-design-vue';
import {
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { computed, ref, watch } from 'vue';
import { fileApi } from '@haierbusiness-front/apis';
import type { Ref } from 'vue';
import { angtListRes, Datum } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil';
import { HeaderConstant } from '@haierbusiness-front/common-libs';
// import Echarts from "./echarts.vue"
import type { UploadChangeParam, UploadFile } from 'ant-design-vue';
// const extensions = [javascript(), oneDark];
const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false);

interface Props {
  show: boolean;
  data: angtListRes;
  labelList: Array<Datum>;
  knowCenterOptions: any;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: angtListRes = {
  personNum: '',
  personName: '',
  agentNum: '',
  outboundNumber:"",
  agentId: '',
  productId: null,
  id:null,
};

const changeUser = (userInfo: any) => {
  if (userInfo) {
    indexData.value.personNum = userInfo.username;
    indexData.value.personName = userInfo.nickName;
  } else {
    indexData.value.personNum = '';
    indexData.value.personNam = '';
  }
};

const rules: Record<string, Rule[]> = {
  personNum: [{ required: true, message: '请输入工号' }],
  personName: [{ required: true, message: '请输入姓名' }],
  agentNum: [{ required: true, message: '请输入分机号' }],
  outboundNumber:[{ required: true, message: '请输入外呼坐席id' }],
  agentId: [{ required: true, message: '请输入坐席id' }],
  productId: [{ required: true, message: '请选择产品线' }],
};

const indexData: Ref<angtListRes> = ref(props.data ? props.data : defaultData);

watch(props, (newValue) => {
  indexData.value = ({ ...newValue.data } as angtListRes) || defaultData;
});

const emit = defineEmits(['cancel', 'ok']);

const visible = computed(() => props.show);

const handleOk = () => {
  from.value
    .validate()
    .then(() => {
      confirmLoading.value = true;
      emit('ok', indexData.value, () => {
        confirmLoading.value = false;
      });
      confirmLoading.value = false;
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};
</script>

<template>
  <h-modal
    v-model:visible="visible"
    :title="indexData.id ? '编辑坐席' : '新增坐席'"
    :width="600"
    @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading"
    forceRender
    @ok="handleOk"
  >
    <h-form
      v-if="visible"
      ref="from"
      :model="indexData"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18 }"
      :rules="rules"
    >
      <h-form-item label="姓名" name="personName">
        <!-- <h-input v-model:value="indexData.personName" style="width: 100%" /> -->
        <user-select
          :value="indexData.personName"
          placeholder="请选择用户"
          :params="{
            pageNum: 1,
            pageSize: 20,
          }"
          @change="(userInfo: any) =>  changeUser(userInfo)"
        ></user-select>
      </h-form-item>
      <h-form-item label="工号" name="personNum">
        <h-input v-model:value="indexData.personNum" disabled style="width: 100%" />
      </h-form-item>
      <h-form-item label="分机号" name="agentNum">
        <h-input v-model:value="indexData.agentNum" style="width: 100%" />
      </h-form-item>
      <h-form-item label="坐席ID" name="agentId">
        <h-input v-model:value="indexData.agentId" style="width: 100%" />
      </h-form-item>
      <h-form-item label="外呼坐席ID" name="outboundNumber">
        <h-input v-model:value="indexData.outboundNumber" style="width: 100%" />
      </h-form-item>
      <h-form-item label="产品线" name="productId">
        <h-select v-model:value="indexData.productId" style="width: 100%">
          <h-select-option v-for="item in labelList" :value="item.id">{{ item.name }}</h-select-option>
        </h-select>
      </h-form-item>
    </h-form>
  </h-modal>
</template>

<style lang="less" scoped>
.important {
  color: red;
}
.imgItem {
  position: relative;
  display: inline-block;
  margin-right: 4px;
  .deleteIcon {
    position: absolute;
    right: 0px;
    z-index: 9999;
  }
}
</style>
