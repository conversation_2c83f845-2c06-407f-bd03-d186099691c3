<template>
  <div class="addhotel">
    <a-col :span="24" style="margin-bottom: 10px">
      <a-row :align="'middle'" style="padding: 10px 10px 0px 10px">
        <a-col :span="24" style="text-align: right">
          <a-button type="primary" style="margin-right: 10px" @click="downloadTemplete">模板下载</a-button>

          <!-- <h-button style="margin-right: 10px" @click="reset">重置</h-button> -->
          <a-button style="margin-right: 10px" type="primary" @click="openExport"> 模板导入 </a-button>
        </a-col>
      </a-row>
    </a-col>
    <a-table
      :dataSource="dataSource"
      :columns="columns"
      :pagination="false"
      bordered
      size="small"
      :expand-column-width="100"
    >
      <template #bodyCell="{ index, column, record }">
        <template v-if="column.dataIndex === 'travelCtripPrice'">
          <a @click="editDate(index, 'xiecheng')">
            {{ record.travelCtripPrice || '--' }}
          </a>
          <a-tooltip placement="top" :mouseEnterDelay="0.5">
            <template #title>
              <span>编辑</span>
            </template>
            <EditOutlined
              @click="editDate(index, 'xiecheng')"
              style="color: #008dff; cursor: pointer; font-size: 14px; margin-left: 8px"
            />
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex === 'travelQianTaoPrice'">
          <a @click="editDate(index, 'qiantao')">
            {{ record.travelQianTaoPrice || '--' }}
          </a>
          <a-tooltip placement="top" :mouseEnterDelay="0.5">
            <template #title>
              <span>编辑</span>
            </template>
            <EditOutlined
              @click="editDate(index, 'qiantao')"
              style="color: #008dff; cursor: pointer; font-size: 14px; margin-left: 8px"
            />
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <a @click="editRowData(index, record)">编辑</a>
          <a style="margin-left:10px;" @click="delRowData(index)">删除</a>
        </template>
      </template>
      <template #emptyText>
        <a-empty :image="simpleImage">
          <template #description>
            <span> 点击下方添加 </span>
          </template>
        </a-empty>
      </template>
    </a-table>
    <a-button style="margin-top: 16px" block @click="addData">
      <template #icon>
        <PlusOutlined />
      </template>
      新增
    </a-button>
    <div class="submitbtn">
      <a-button style="margin-top: 16px" type="primary" :loading="submitLoading" @click="submit"> 提交 </a-button>
    </div>
    <a-modal
      v-model:open="addvisible"
      :title="editRowIndex!==null ? '编辑' : '新增'"
      :maskClosable="false"
      :footer="null"
      @cancel="handleCancel"
      width="60%"
      class="addModal"
    >
      <a-form
        class="addform"
        :model="formState"
        name="basic"
        ref="formRef"
        :rules="rulesRef"
        :label-col="{ span: 10 }"
        :wrapper-col="{ span: 14 }"
        autocomplete="off"
      >
        <a-divider orientation="left">基础信息</a-divider>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="城市" name="spotCheckCityId">
              <!-- <a-input v-model:value="formState.spotCheckCity" /> -->
              <a-select
                show-search
                @change="handleCityChange"
                :filter-option="filterOption"
                :options="cityList"
                v-model:value="formState.spotCheckCityId"
                style="width: 100%"
              >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="酒店名称" name="spotChecktHotelName">
              <a-input v-model:value="formState.spotChecktHotelName" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="地址" name="spotCheckHotelAddress">
              <a-input v-model:value="formState.spotCheckHotelAddress" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="电话" name="tel">
              <a-input v-model:value="formState.tel" />
            </a-form-item>
          </a-col>
          <!-- <a-col :span="12">
            <a-form-item label="抽检日期" name="spotCheckDate">
              <a-date-picker class="dataSelect" :valueFormat="'YYYY-MM-DD'" v-model:value="formState.spotCheckDate" />
            </a-form-item>
          </a-col> -->
          <a-col :span="12">
            <a-form-item label="入住日期" name="indate">
              <a-date-picker
                class="dataSelect"
                :disabledDate="disabledDateStart"
                :valueFormat="'YYYY-MM-DD'"
                v-model:value="formState.indate"
            /></a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="离店日期" name="outdate">
              <a-date-picker
                class="dataSelect"
                :disabledDate="disabledDateEnd"
                :valueFormat="'YYYY-MM-DD'"
                v-model:value="formState.outdate"
            /></a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="因私因公" name="travelType">
              <a-select v-model:value="formState.travelType" style="width: 100%">
                <a-select-option :value="1">因公</a-select-option>
                <a-select-option :value="0">因私</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="房型" name="spotCheckRoomType">
              <a-input v-model:value="formState.spotCheckRoomType" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="备注" name="remark">
              <a-textarea v-model:value="formState.remark" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-divider orientation="left">商旅携程</a-divider>
          <a-col :span="12">
            <a-form-item
              label="是否有资源"
              name="travelCtripHaveResourcesType"
              :rules="[{ required: true, message: '请选择是否有资源' }]"
            >
              <a-select
                v-model:value="formState.travelCtripHaveResourcesType"
                @change="travelCtripHaveResourcesTypeChange"
                style="width: 100%"
              >
                <a-select-option :value="1">有房</a-select-option>
                <a-select-option :value="2">满房</a-select-option>
                <a-select-option :value="3">此酒店无价格</a-select-option>
                <a-select-option :value="4">此房型无价格</a-select-option>
              </a-select>
            </a-form-item></a-col
          >
          <a-col :span="12">
            <a-form-item
              label="价格"
              name="travelCtripPrice"
              :rules="[
                {
                  required: !(
                    formState.travelCtripHaveResourcesType == 2 ||
                    formState.travelCtripHaveResourcesType == 3 ||
                    formState.travelCtripHaveResourcesType == 4
                  ),
                  message: '请输入价格',
                },
              ]"
            >
              <a-input-number
                v-model:value="formState.travelCtripPrice"
                :disabled="formState.travelCtripHaveResourcesType == 3 || formState.travelCtripHaveResourcesType == 4"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="早餐类型"
              name="travelCtripBreakfastType"
              :rules="[
                {
                  required: !(
                    formState.travelCtripHaveResourcesType == 2 ||
                    formState.travelCtripHaveResourcesType == 3 ||
                    formState.travelCtripHaveResourcesType == 4
                  ),
                  message: '请输入价格',
                },
              ]"
            >
              <a-select
                v-model:value="formState.travelCtripBreakfastType"
                :disabled="formState.travelCtripHaveResourcesType == 3 || formState.travelCtripHaveResourcesType == 4"
                style="width: 100%"
              >
                <a-select-option :value="1">无早</a-select-option>
                <a-select-option :value="2">单早</a-select-option>
                <a-select-option :value="3">双早</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="取消规则"
              name="travelCtripCancelType"
              :rules="[
                {
                  required: !(
                    formState.travelCtripHaveResourcesType == 2 ||
                    formState.travelCtripHaveResourcesType == 3 ||
                    formState.travelCtripHaveResourcesType == 4
                  ),
                  message: '请输入价格',
                },
              ]"
            >
              <a-select
                v-model:value="formState.travelCtripCancelType"
                :disabled="formState.travelCtripHaveResourcesType == 3 || formState.travelCtripHaveResourcesType == 4"
                style="width: 100%"
              >
                <a-select-option :value="1">限时取消</a-select-option>
                <a-select-option :value="2">不可取消</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="截图" name="travelCtripUrl" :label-col="{ span: 5 }">
              <a-image-preview-group>
                <div v-for="(item, index) of imgList" class="imgItem" :key="index">
                  <CloseCircleOutlined class="deleteIcon" @click="deletexiechengImg(index)" />
                  <img :width="100" :height="100" :src="item" @click="lookImg(imgList, index)" />
                </div>
              </a-image-preview-group>

              <a @click="getclipboardImg(1)">获取剪切板图片</a>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-divider orientation="left">商旅千淘</a-divider>
          <a-col :span="12">
            <a-form-item
              label="是否有资源"
              name="travelQianTaoHaveResourcesType"
              :rules="[{ required: true, message: '请选择是否有资源' }]"
            >
              <a-select
                v-model:value="formState.travelQianTaoHaveResourcesType"
                @change="travelQianTaoHaveResourcesTypeChange"
                style="width: 100%"
              >
                <a-select-option :value="1">有房</a-select-option>
                <a-select-option :value="2">满房</a-select-option>
                <a-select-option :value="3">此酒店无价格</a-select-option>
                <a-select-option :value="4">此房型无价格</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="价格"
              name="travelQianTaoPrice"
              :rules="[
                {
                  required: !(
                    formState.travelQianTaoHaveResourcesType == 2 ||
                    formState.travelQianTaoHaveResourcesType == 3 ||
                    formState.travelQianTaoHaveResourcesType == 4
                  ),
                  message: '请输入价格',
                },
              ]"
            >
              <a-input-number
                v-model:value="formState.travelQianTaoPrice"
                :disabled="
                  formState.travelQianTaoHaveResourcesType == 3 || formState.travelQianTaoHaveResourcesType == 4
                "
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="早餐类型"
              name="travelQianTaoBreakfastType"
              :rules="[
                {
                  required: !(
                    formState.travelQianTaoHaveResourcesType == 2 ||
                    formState.travelQianTaoHaveResourcesType == 3 ||
                    formState.travelQianTaoHaveResourcesType == 4
                  ),
                  message: '请选择早餐类型',
                },
              ]"
            >
              <a-select
                v-model:value="formState.travelQianTaoBreakfastType"
                :disabled="
                  formState.travelQianTaoHaveResourcesType == 3 || formState.travelQianTaoHaveResourcesType == 4
                "
                style="width: 100%"
              >
                <a-select-option :value="1">无早</a-select-option>
                <a-select-option :value="2">单早</a-select-option>
                <a-select-option :value="3">双早</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="取消规则"
              name="travelQianTaoCancelType"
              :rules="[
                {
                  required: !(
                    formState.travelQianTaoHaveResourcesType == 2 ||
                    formState.travelQianTaoHaveResourcesType == 3 ||
                    formState.travelQianTaoHaveResourcesType == 4
                  ),
                  message: '请选择取消规则',
                },
              ]"
            >
              <a-select
                v-model:value="formState.travelQianTaoCancelType"
                :disabled="
                  formState.travelQianTaoHaveResourcesType == 3 || formState.travelQianTaoHaveResourcesType == 4
                "
                style="width: 100%"
              >
                <a-select-option :value="1">限时取消</a-select-option>
                <a-select-option :value="2">不可取消</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="截图" name="travelQianTaoUrl" :label-col="{ span: 5 }">
              <a-image-preview-group>
                <div v-for="(item, index) of imgList2" class="imgItem" :key="index">
                  <CloseCircleOutlined class="deleteIcon" @click="deleteshanglvImg(index)" />
                  <img :width="100" :height="100" :src="item" @click="lookImg(imgList2, index)" />
                </div>
              </a-image-preview-group>

              <a @click="getclipboardImg(2)">获取剪切板图片</a>
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item :wrapper-col="{ offset: 8, span: 16 }">
          <a-button type="primary" @click.prevent="addConfirm">确定</a-button>
          <a-button class="btn" @click="handleCancel">取消</a-button>
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal v-model:open="exportVisible" title="导入模板" :maskClosable="false" :footer="null" width="500px">
      <a-form
        :model="fileInfo.list[0]"
        name="basic"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
      >
        <a-form-item label="上传" name="fileList">
          <a-upload
            v-model:file-list="fileInfo.list[0].fileList"
            name="file"
            action="/hbweb/hotel-analysis/hb/hotel-analysis/api/ledger/spot-check/import"
            @change="City"
            :headers="{
              'Hb-Token': token,
            }"
          >
            <a-button>
              <upload-outlined></upload-outlined>
              选择文件
            </a-button>
          </a-upload>
        </a-form-item>
        <a-form-item :wrapper-col="{ offset: 16, span: 16 }">
          <a-button type="primary" class="btn" @click="handleOk">取消</a-button>
        </a-form-item>
      </a-form></a-modal
    >
    <a-modal v-model:open="open" title="修改信息" :maskClosable="false" :footer="null" @cancel="handleEditCancel">
      <a-form
        :model="tradeState.list[0]"
        name="basic"
        ref="formEditRef"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
        @finish="onFinish"
        @finishFailed="onFinishFailed"
      >
        <a-form-item
          label="是否有资源"
          name="haveResourcesType"
          :rules="[{ required: true, message: '请选择是否有资源' }]"
        >
          <a-select
            v-model:value="tradeState.list[0].haveResourcesType"
            @change="editHaveResourcesTypeChange"
            style="width: 100%"
          >
            <a-select-option :value="1">有房</a-select-option>
            <a-select-option :value="2">满房</a-select-option>
            <a-select-option :value="3">此酒店无价格</a-select-option>
            <a-select-option :value="4">此房型无价格</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label="价格"
          name="price"
          :rules="[
            {
              required: !(
                tradeState.list[0].haveResourcesType == 2 ||
                tradeState.list[0].haveResourcesType == 3 ||
                tradeState.list[0].haveResourcesType == 4
              ),
              message: '请输入价格',
            },
          ]"
        >
          <a-input-number
            v-model:value="tradeState.list[0].price"
            :disabled="tradeState.list[0].haveResourcesType == 3 || tradeState.list[0].haveResourcesType == 4"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item
          label="早餐类型"
          name="breakfast"
          :rules="[
            {
              required: !(
                tradeState.list[0].haveResourcesType == 2 ||
                tradeState.list[0].haveResourcesType == 3 ||
                tradeState.list[0].haveResourcesType == 4
              ),
              message: '请选择早餐类型',
            },
          ]"
        >
          <a-select
            v-model:value="tradeState.list[0].breakfast"
            :disabled="tradeState.list[0].haveResourcesType == 3 || tradeState.list[0].haveResourcesType == 4"
            style="width: 100%"
          >
            <a-select-option :value="1">无早</a-select-option>
            <a-select-option :value="2">单早</a-select-option>
            <a-select-option :value="3">双早</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label="取消规则"
          name="cancel"
          :rules="[
            {
              required: !(
                tradeState.list[0].haveResourcesType == 2 ||
                tradeState.list[0].haveResourcesType == 3 ||
                tradeState.list[0].haveResourcesType == 4
              ),
              message: '请选择取消规则',
            },
          ]"
        >
          <a-select
            v-model:value="tradeState.list[0].cancel"
            :disabled="tradeState.list[0].haveResourcesType == 3 || tradeState.list[0].haveResourcesType == 4"
            style="width: 100%"
          >
            <a-select-option :value="1">限时取消</a-select-option>
            <a-select-option :value="2">不可取消</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="截图" name="picAddress">
          <a-image-preview-group>
            <div v-for="(item, index) of editImgList" class="imgItem" :key="index">
              <CloseCircleOutlined class="deleteIcon" @click="deleteImg(index)" />
              <img :width="100" :height="100" :src="item" @click="lookImg(editImgList, index)" />
            </div>
          </a-image-preview-group>

          <a @click="getclipboardImg(3)">获取剪切板图片</a>
        </a-form-item>
        <a-form-item :wrapper-col="{ offset: 8, span: 16 }">
          <a-button type="primary" html-type="submit">确定</a-button>
          <a-button class="btn" @click="handleEditCancel">取消</a-button>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script lang="ts">
import {
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
  EditOutlined,
} from '@ant-design/icons-vue';
import { ref, reactive, toRaw, onMounted } from 'vue';

import { errorModal, getCurrentRouter, routerParam } from '@haierbusiness-front/utils';
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil';
import { message, Empty,Modal } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { HeaderConstant } from '@haierbusiness-front/common-libs';
import { SupplierApi, download } from '@haierbusiness-front/apis';
import { api as viewerApi } from 'v-viewer';
import { fileApi } from '@haierbusiness-front/apis';
import dayjs from 'dayjs';
export default {
  components: {
    UploadOutlined,
    SearchOutlined,
    CloseCircleOutlined,
    PlusOutlined,
    EditOutlined,
  },
  setup() {
    const router = getCurrentRouter();
    const addvisible = ref<boolean>(false);
    const submitLoading = ref<boolean>(false);
    const dataSource = ref<any>([]);
    const exportVisible = ref<boolean>(false);
    const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false);
    const formRef = ref();
    const formEditRef = ref();
    const imgUrl = ref<Array<string>>([]);
    const imgUrl2 = ref<Array<string>>([]);
    const imgList = ref<any>([]);
    const imgList2 = ref<any>([]);
    const newData = ref<any>([]);
    const open = ref(false);
    const editImgList = ref<Array<string>>([]);
    const currentKey = ref<string>('xiecheng');

    const tradeState = reactive<any>({
      list: [{ cancel: '', price: '', breakfast: '', id: '', lowerPriceFlag: '', exclusiveResourcesFlag: '' }],
    });
    // 记录当前编辑行
    const editRowIndex = ref<number | null>(null);

    const formState = reactive<any>({
      indate: '',
      spotCheckDate: '',
      outdate: '',
      travelCtripBreakfastType: '',
      spotCheckCity: '',
      spotCheckCityId: '',
      spotChecktHotelName: '',
      spotCheckBookingDate: '',
      spotCheckRoomType: '',
      tel: '',
      spotCheckHotelAddress: '',
      travelCtripPrice: '',
      travelQianTaoPrice: '',
      travelQianTaoUrl: '',
      travelCtripUrl: '',
      travelQianTaoBreakfastType: '',
      travelType: 1,
      travelQianTaoIsLowerPrice: '',
      travelCtripIsLowerPrice: '',
      travelCtripCancelType: '',
      travelQianTaoCancelType: '',
      travelCtripHaveResourcesType: '',
      travelQianTaoHaveResourcesType: '',
      remark: '',
    });
    // 不能选择之前的日期 和 当天
    const disabledDateEnd = (time: any) => {
      if (time && time < dayjs().subtract(1, 'days').endOf('day')) {
        return true;
      }
      return time && time < dayjs(formState.indate).endOf('day');
    };
    const disabledDateStart = (time: any) => {
      if (time && time < dayjs().subtract(1, 'days').endOf('day')) {
        return true;
      }
      return time && time > dayjs(formState.outdate).subtract(1, 'days').startOf('day');
    };
    const lookImg = (imgList: any, index: number) => {
      const $viewer = viewerApi({
        options: {
          initialViewIndex: index,
        },
        images: imgList,
      });
    };
    // 验证电话号码和手机号
    const validatePassTel = async (_rule: Rule, value: string) => {
      let isPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
      let isMob = /^(?:(?:\d{3}-)?\d{8}|^(?:\d{4}-)?\d{7,8})(?:-\d+)?$/;
      if (isMob.test(value) || isPhone.test(value) || !value) {
        return Promise.resolve();
      } else {
        return Promise.reject('请输入正确的电话号码');
      }
    };
    const rulesRef: Record<string, Rule[]> = {
      // travelQianTaoIsLowerPrice: [{ required: true, message: '商旅千淘是否最低价' }],
      // travelCtripIsLowerPrice: [{ required: true, message: '请选择商旅携程是否最低价' }],
      spotCheckCityId: [{ required: true, message: '请输入城市' }],
      spotChecktHotelName: [{ required: true, message: '请输入酒店' }],
      spotCheckBookingDate: [{ required: true, message: '请选择预定日期' }],
      spotCheckRoomType: [{ required: true, message: '请输入房型' }],
      spotCheckDate: [{ required: true, message: '请选择抽检日期' }],
      indate: [{ required: true, message: '请选择入住日期' }],
      outdate: [{ required: true, message: '请选择离店日期' }],
      travelType: [{ required: true, message: '请选择因公因私' }],
      // travelCtripBreakfastType: [{ required: true, message: '请输入商旅携程-早餐类型' }],
      // travelQianTaoBreakfastType: [{ required: true, message: '请输入商旅千淘-早餐类型' }],
      tel: [{ required: true, message: '请输入电话' }],
      // ,{ validator:validatePassTel, trigger: 'change' }
      spotCheckHotelAddress: [{ required: true, message: '请输入地址' }],
      // travelCtripPrice: [{ required: true, message: '请输入商旅携程-价格' }],
      // travelQianTaoPrice: [{ required: true, message: '请输入商旅千淘-价格' }],
      // travelQianTaoUrl: [{ required: true, message: '请输入商旅千淘-截图地址' }],
      // travelCtripUrl: [{ required: true, message: '请输入商旅携程-截图地址' }],
    };
    const deleteImg = (index: number) => {
      editImgList.value.splice(index, 1);
    };
    const deletexiechengImg = (index: number) => {
      imgList.value.splice(index, 1);
    };
    const deleteshanglvImg = (index: number) => {
      imgList2.value.splice(index, 1);
    };
    const fileInfo = reactive<any>({
      list: [{ fileList: [] }],
    });
    const handleCancel = () => {
      addvisible.value = false;
      formRef.value.resetFields();
      imgList.value = [];
      imgList2.value = [];
    };
    const handleEditCancel = () => {
      open.value = false;
      formEditRef.value.resetFields();
      editImgList.value = [];
    };
    const addConfirm = async (values: any) => {
      formState.travelCtripUrl = imgList.value.join(';');
      formState.travelQianTaoUrl = imgList2.value.join(';');
      await formRef.value
        .validate()
        .then((res: any) => {
          console.log(res, 'values', values, toRaw(formState));
          cityList.value.forEach((item: any) => {
            if (item.value == res.spotCheckCityId) {
              res.spotCheckCity = item.label;
            }
          });
          newData.value = res;
          let newdataSource: any = [];
          if (editRowIndex.value === null) {
            newdataSource = dataSource.value.concat(newData.value);
          } else {
            dataSource.value.splice(editRowIndex.value, 1, newData.value);
            newdataSource = dataSource.value;
          }

          dataSource.value = newdataSource;
          handleCancel();
        })
        .catch((error: any) => {
          console.log('error', error);
        });
    };
    const addData = () => {
      for (let key in formState) {
        if (formState.hasOwnProperty(key)) {
          formState[key] = ''; // foo
        }
      }
      addvisible.value = true;
      editRowIndex.value = null;
    };
    const editDate = (index: string | number, key: string) => {
      console.log(index, key, 'index, key');
      tradeState.list[0].index = index;
      currentKey.value = key;
      if (key == 'xiecheng') {
        tradeState.list[0].breakfast = dataSource.value[index].travelCtripBreakfastType;
        tradeState.list[0].price = dataSource.value[index].travelCtripPrice;
        tradeState.list[0].cancel = dataSource.value[index].travelCtripCancelType;
        tradeState.list[0].haveResourcesType = dataSource.value[index].travelCtripHaveResourcesType;
        editImgList.value = dataSource.value[index].travelCtripUrl
          ? dataSource.value[index].travelCtripUrl.split(';')
          : [];
      } else {
        tradeState.list[0].breakfast = dataSource.value[index].travelQianTaoBreakfastType;
        tradeState.list[0].price = dataSource.value[index].travelQianTaoPrice;
        tradeState.list[0].cancel = dataSource.value[index].travelQianTaoCancelType;
        tradeState.list[0].haveResourcesType = dataSource.value[index].travelQianTaoHaveResourcesType;
        editImgList.value = dataSource.value[index].travelQianTaoUrl
          ? dataSource.value[index].travelQianTaoUrl.split(';')
          : [];
      }

      open.value = true;
    };

    const editRowData = (index: number, row: any) => {
      editRowIndex.value = index;
      console.log(editRowIndex.value);
      addvisible.value = true;
      for (let key in formState) {
        if (formState.hasOwnProperty(key)) {
          formState[key] = row[key]; // foo
        }
      }
    };
    const delRowData = (index: number,) => {
      Modal.confirm({
        title: '确定要删除这条数据吗？',
        onOk() {
          dataSource.value.splice(index, 1);
        },
        onCancel() {
          console.log('Cancel');
        },
        class: 'test',
      });
    };

    const onFinish = (values: any) => {
      const index = tradeState.list[0].index;
      if (currentKey.value == 'xiecheng') {
        dataSource.value[index].travelCtripBreakfastType = tradeState.list[0].breakfast;
        dataSource.value[index].travelCtripPrice = tradeState.list[0].price;
        dataSource.value[index].travelCtripCancelType = tradeState.list[0].cancel;
        dataSource.value[index].travelCtripHaveResourcesType = tradeState.list[0].haveResourcesType;
        dataSource.value[index].travelCtripUrl = editImgList.value.join(';');
      } else {
        dataSource.value[index].travelQianTaoBreakfastType = tradeState.list[0].breakfast;
        dataSource.value[index].travelQianTaoPrice = tradeState.list[0].price;
        dataSource.value[index].travelQianTaoCancelType = tradeState.list[0].cancel;
        dataSource.value[index].travelQianTaoHaveResourcesType = tradeState.list[0].haveResourcesType;
        dataSource.value[index].travelQianTaoUrl = editImgList.value.join(';');
      }
      handleEditCancel();
    };
    const onFinishFailed = (errorInfo: any) => {
      console.log('Failed:', errorInfo);
    };
    const openExport = () => {
      exportVisible.value = true;
    };
    const handleOk = (e: MouseEvent) => {
      exportVisible.value = false;
    };
    const City = (info: { file: { status: string; response: { data: any } } }) => {
      if (info.file.status === 'done') {
        message.success('文件上传成功');
        // info.file.response.data.forEach((item:any)=>{
        //   if(item.spotCheckCityId){
        //     item.spotCheckCityId = Number(item.spotCheckCityId)
        //   }
        // })
        dataSource.value = info.file.response.data;
        fileInfo.list[0].fileList = [];
        exportVisible.value = false;
      } else if (info.file.status === 'error') {
        message.error(`上传失败`);
      }
    };
    const getclipboardImg = (type: number) => {
      // 首先要确保浏览器支持Clipboard API
      if (typeof navigator.clipboard !== 'undefined' && typeof ClipboardItem === 'function') {
        // 从剪贴板读取内容
        navigator.clipboard.read().then(async function (items) {
          console.log(items, 'item');
          for (let item of items) {
            console.log(item.types[0].indexOf('image'), '111');

            if (item.types[0].indexOf('image') !== -1) {
              const blob = await item.getType('image/png');
              const url = URL.createObjectURL(blob);
              if (type == 1) {
                let formData = new FormData();
                formData.append('file', blob, 'image.png');
                fileApi.upload(formData).then((res) => {
                  console.log(res);
                  if (
                    window.location.origin.indexOf('http://127.0.0.1') !== -1 ||
                    window.location.origin.indexOf('http://localhost') !== -1
                  ) {
                    imgList.value.push(`https://businessmanagement-test.haier.net/${res.path}`);
                  } else {
                    imgList.value.push(`${window.location.origin}/${res.path}`);
                  }
                });
              } else if (type == 2) {
                let formData = new FormData();
                formData.append('file', blob, 'image.png');
                fileApi.upload(formData).then((res) => {
                  console.log(res);
                  if (
                    window.location.origin.indexOf('http://127.0.0.1') !== -1 ||
                    window.location.origin.indexOf('http://localhost') !== -1
                  ) {
                    imgList2.value.push(`https://businessmanagement-test.haier.net/${res.path}`);
                  } else {
                    imgList2.value.push(`${window.location.origin}/${res.path}`);
                  }
                });
              } else {
                let formData = new FormData();
                formData.append('file', blob, 'image.png');
                fileApi.upload(formData).then((res) => {
                  console.log(res);
                  if (
                    window.location.origin.indexOf('http://127.0.0.1') !== -1 ||
                    window.location.origin.indexOf('http://localhost') !== -1
                  ) {
                    editImgList.value.push(`https://businessmanagement-test.haier.net/${res.path}`);
                  } else {
                    editImgList.value.push(`${window.location.origin}/${res.path}`);
                  }
                });
              }
              console.log(imgUrl.value, ' imgUrl.value');
              // 创建Image对象
              // var img = new Image();
              // img.src = URL.createObjectURL(blob);

              // // 添加到DOM元素中进行展示
              // document.body.appendChild(img);
            }
          }
        });
      } else {
        console.log('当前浏览器不支持Clipboard API');
      }
    };
    const downloadTemplete = () => {
      SupplierApi.getTemplate();
    };
    const submit = () => {
      if (dataSource.value.length == 0) {
        message.warn('请先添加数据');
        return;
      }
      let noImgArray = [];
      dataSource.value.forEach((item: any) => {
        if (!item.travelCtripUrl || !item.travelQianTaoUrl) {
          noImgArray.push(item);
        }
      });
      if (noImgArray.length) {
        message.warn('请补充完整图片');
        return;
      }
      submitLoading.value = true;
      SupplierApi.createAll(dataSource.value)
        .then((res) => {
          console.log(res, 'res');
          dataSource.value = [];
          submitLoading.value = false;
          router.push({ path: '/hotel-analysis/compare' });
        })
        .catch(() => {
          submitLoading.value = false;
        });
    };
    // 商旅携程是否有资源改变时
    const travelCtripHaveResourcesTypeChange = (type: number) => {
      // 如果选择3 和 4 name清空其他值 并置灰
      if (type == 3 || type == 4) {
        formState.travelCtripPrice = null;
        formState.travelCtripBreakfastType = null;
        formState.travelCtripCancelType = null;
      }
    };
    // 携程千淘是否有资源改变时
    const travelQianTaoHaveResourcesTypeChange = (type: number) => {
      // 如果选择3 和 4 name清空其他值 并置灰
      if (type == 3 || type == 4) {
        formState.travelQianTaoPrice = null;
        formState.travelQianTaoBreakfastType = null;
        formState.travelQianTaoCancelType = null;
      }
    };
    // 修改信息 是否有资源改变时
    const editHaveResourcesTypeChange = (type: number) => {
      // 如果选择3 和 4 name清空其他值 并置灰
      if (type == 3 || type == 4) {
        tradeState.list[0].price = null;
        tradeState.list[0].breakfast = null;
        tradeState.list[0].cancel = null;
      }
    };
    const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
    const cityList = ref<any>([]);
    const getCity = () => {
      SupplierApi.getCtiyList({ level: "city",internationalFlag: "0" }).then((res: any) => {
        // console.log(res)
        res.records.forEach((item: any) => {
          cityList.value.push({ value: item.id, label: item.name });
        });
      });
    };
    const filterOption = (input: string, option: any) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };
    const handleCityChange = (value: number) => {};
    onMounted(() => {
      getCity();
    });
    return {
      handleCityChange,
      simpleImage,
      dataSource,
      formRef,
      columns: [
        {
          title: '城市',
          dataIndex: 'spotCheckCity',
          key: 'spotCheckCity',
        },
        {
          title: '酒店名称',
          dataIndex: 'spotChecktHotelName',
          key: 'spotChecktHotelName',
        },
        {
          title: '入住日期',
          dataIndex: 'indate',
          key: 'indate',
        },
        {
          title: '离店日期',
          dataIndex: 'outdate',
          key: 'outdate',
        },
        {
          title: '地址',
          dataIndex: 'spotCheckHotelAddress',
          key: 'spotCheckHotelAddress',
        },
        {
          title: '电话',
          dataIndex: 'tel',
          key: 'tel',
        },
        // {
        //   title: '预定日期',
        //   dataIndex: 'spotCheckBookingDate',
        //   key: 'spotCheckBookingDate',
        // },
        {
          title: '房型',
          dataIndex: 'spotCheckRoomType',
          key: 'spotCheckRoomType',
        },
        {
          title: '商旅系统价格',
          children: [
            {
              title: '携程',
              dataIndex: 'travelCtripPrice',
              key: 'companyAddress',
              width: 100,
            },
            {
              title: '千淘',
              dataIndex: 'travelQianTaoPrice',
              key: 'travelQianTaoPrice',
              width: 100,
            },
          ],
        },
        {
          title: '备注',
          dataIndex: 'remark',
          key: 'remark',
        },
        {
          title: '操作',
          dataIndex: 'operation',
          key: 'operation',
        },
      ],
      filterOption,
      formState,
      rulesRef,
      addvisible,
      exportVisible, 
      fileInfo,
      token,
      imgList2,
      imgList,
      open,
      tradeState,
      editImgList,

      formEditRef,
      deletexiechengImg,
      deleteshanglvImg,
      deleteImg,
      downloadTemplete,
      handleEditCancel,
      editDate,
      addData,
      handleCancel,
      onFinish,
      addConfirm,
      onFinishFailed,
      openExport,
      handleOk,
      City,
      submit,
      getclipboardImg,
      travelCtripHaveResourcesTypeChange,
      travelQianTaoHaveResourcesTypeChange,
      validatePassTel,
      editHaveResourcesTypeChange,
      submitLoading,
      dayjs,
      disabledDateEnd,
      disabledDateStart,
      getCity,
      cityList,
      lookImg,
      editRowData,
      editRowIndex,
      delRowData
    };
  },
};
</script>
<style lang="less">
.addhotel {
  background: #fff;
  padding: 16px;
  height: 100%;
}
.btn {
  margin-left: 16px;
}
.submitbtn {
  width: 100%;
  display: flex;
  justify-content: center;
}
.dataSelect {
  width: 100%;
}
.addModal {
  .ant-modal-content {
    background: #fff;
    border-radius: 10px;
    .ant-modal-body {
      height: 550px;
      overflow-y: scroll;
      display: flex;
      justify-content: center;
      margin-bottom: 16px;
      .addform {
        width: 80%;
      }
    }
  }

  ::-webkit-scrollbar-thumb {
    background-color: #fff;
    border-radius: 5px;
    width: 0px;
  }
}
.imgItem {
  position: relative;
  display: inline-block;
  margin-right: 4px;
  cursor: pointer;
  .deleteIcon {
    position: absolute;
    right: 0px;
    z-index: 10;
  }
}
.filterTitle {
  font-size: 16px;
  font-size: 500;
  margin-left: 22%;
  padding-bottom: 20px;
}
</style>
