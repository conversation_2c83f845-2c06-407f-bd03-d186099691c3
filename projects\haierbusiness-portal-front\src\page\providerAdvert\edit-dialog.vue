<script lang="ts" setup>
import {
  Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
  Input as hInput, CheckboxGroup as hCheckboxGroup, Checkbox as hCheckbox, Row as hRow, Col as hCol, DatePicker as hDatePicker, message, Upload as hUpload, RadioGroup as hRadioGroup, Radio as hRadio, InputNumber as hInputNumber
} from 'ant-design-vue';
import { computed, ref, watch, onMounted } from "vue";
import type { Ref } from "vue";
import Editor from '@haierbusiness-front/components/editor/Editor.vue'

import {
  IAdvertisementPrividerAccount
} from '@haierbusiness-front/common-libs';
import { useRequest } from 'vue-request';
import { providerAdvertisementApi } from '@haierbusiness-front/apis';
import type { UploadChangeParam, UploadProps } from 'ant-design-vue';
import PlusOutlined from '@ant-design/icons-vue/PlusOutlined';
import LoadingOutlined from '@ant-design/icons-vue/LoadingOutlined';
interface Props {
  show: boolean;
  data: IAdvertisementPrividerAccount | null;
}
const uploadUrl = import.meta.env.VITE_UPLOAD_URL

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: IAdvertisementPrividerAccount = {
  title: '',
  content: '',
  imgUrl: '',
};

const {
  data: enterprises,
  run: userListApiRun
} = useRequest(providerAdvertisementApi.queryAdvertisement, {
  manual: false
});

const rules = {
  title: [{ required: true, message: "请输入标题！" }],
  // content: [{ required: true, message: "请输入描述！" }],
  imgUrl: [{ required: true, message: "请上传图片！" }],
  showStatus: [{ required: true, message: "请选择展示状态！" }],
  showOrder: [{ required: true, message: "请输入排列顺序！" }],
};

const account: Ref<IAdvertisementPrividerAccount> = ref(
  ({ ...props.data } as IAdvertisementPrividerAccount) || defaultData
);
const onEditorChange = (editor: IDomEditor) => {
  account.value.content = editor.getHtml()
}


watch(props, (newValue) => {
  account.value = ({ ...newValue.data } as IAdvertisementPrividerAccount) || defaultData;
});

const emit = defineEmits(["cancel", "ok"]);
function getBase64(img: any, callback: (base64Url: string) => void) {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result as string));
  reader.readAsDataURL(img);
}
const visible = computed(() => props.show);
const fileList = ref([]);
const loading = ref<boolean>(false);
const imageUrl = ref<string>('');
const handleChange = (info: UploadChangeParam) => {
  if (info.file.status === 'uploading') {
    loading.value = true;
    return;
  }
  if (info.file.status === 'done') {
    // Get this url from response in real world.
    console.log('上传成功', info)
    getBase64(info.file.originFileObj, (base64Url: string) => {
      imageUrl.value = base64Url;
      loading.value = false;
    });
    account.value.imgUrl = info.file.response.content.url

  }
  if (info.file.status === 'error') {
    loading.value = false;
    message.error('upload error');
  }
};

const beforeUpload = (file: any) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('只允许上传图片类型');
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('Image must smaller than 2MB!');
  }
  return isJpgOrPng && isLt2M;
};
const handleOk = () => {
  if (!account.value.jumpLinkPc && !account.value.content) {
    message.error('跳转链接和详情页不能同时为空！')
    return
  }
  confirmLoading.value = true;
  from.value.validate()
    .then(() => {
      // const appInfos = appInfoList.value!.map(item => {
      //   return {
      //     applicationCode: item,
      //     scope: 1
      //   }
      // })

      const data = {
        ...account.value,
        // expireDate: dayjs(account.value.expireDate).format("YYYY-MM-DD HH:mm:ss"),
        // appInfoList: appInfos
      }
      emit("ok", data, () => {
        confirmLoading.value = false;
      });
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};
if (props.data) {
  imageUrl.value = props.data.imgUrl ? props.data.imgUrl : ''
}
</script>

<template>
  <h-modal v-model:visible="visible" :title="account.id ? '编辑广告' : '新增广告'" :width="1000" @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading" @ok="handleOk">
    <h-form ref="from" :model="account" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules">
      <h-form-item label="标题" name="title">
        <h-input v-model:value="account.title" />
      </h-form-item>
      <h-form-item label="跳转链接" name="jumpLinkPc">
        <h-input v-model:value="account.jumpLinkPc" />
      </h-form-item>

      <h-form-item label="详情页" name="content">
        <editor height="300px" :modelValue="account.content" @change="onEditorChange" style="z-index: 20"
          :uploadUrl="uploadUrl" />
      </h-form-item>
      <h-form-item label="展示状态" name="showStatus">
        <h-radio-group v-model:value="account.showStatus" name="radioGroup">
          <h-radio :value="1">显示</h-radio>
          <h-radio :value="0">隐藏</h-radio>
        </h-radio-group>
      </h-form-item>
      <h-form-item label="排序" name="showOrder">
        <h-input-number id="inputNumber" v-model:value="account.showOrder" :min="1" :max="10" />
        <!-- <h-input v-model:value="account.showOrder" /> -->
      </h-form-item>
      <h-form-item label="图片" name="imgUrl">
        <h-upload v-model:file-list="fileList" accept="image/png,image/jpeg,image/jpg" list-type="picture-card"
          :max-count="1" name="avatar" class="avatar-uploader" :show-upload-list="false" action="/upload"
          :before-upload="beforeUpload" @change="handleChange">
          <img v-if="imageUrl" :src="imageUrl" class="imgShow" />
          <div v-else>
            <loading-outlined v-if="loading"></loading-outlined>
            <plus-outlined v-else></plus-outlined>
            <div>上传图片</div>
          </div>
          <!-- <div v-else>
              <h-button :loading="loading">
                <upload-outlined></upload-outlined>
                  点击上传
              </h-button>
            </div> -->
        </h-upload>
        请上传图片的尺寸为<span class="important">981*690</span> 大小不超过<span class="important">2MB</span> 格式为<span class="important">png/jpg/jpeg</span>的文件

      </h-form-item>
    </h-form>
  </h-modal>
</template>


<style lang="less" scoped>
.important {
  color: red;
}

.imgShow {
  width: 100%;
}

.avatar-uploader {
  :deep(.bwdv-upload.bwdv-upload-select-picture-card) {
    width: 327px !important;
    height: 230px !important;
    overflow: hidden;
    border-radius: 8px;
  }
}
</style>
  