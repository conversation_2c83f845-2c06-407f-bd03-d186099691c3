<template>
  <div class="order-list" style="padding-bottom: 50px; min-height: 100vh;" >

    <van-sticky>
      <van-search class="list-search" autocomplete="off" shape="round" @search="reSearch" v-model="defaultParams.code" @clear="reSearch" @click-left-icon="reSearch" :clearable="true" show-action placeholder="搜索我的订单">
        <template #action>
          <van-dropdown-menu :close-on-click-outside="false" ref="menuRef">
            <van-dropdown-item ref="itemRef">
              <template #title>
                <van-icon name="filter-o" />
                筛选
              </template>

             
              <van-cell-group title="按出差人查询">
               
                <van-field is-link  @click="showMainPersonList = true" required label-align="top" readonly>
                  

                  <template #input>
                    <div class="color-eee" v-if="!defaultParams.applicantUser">选择出差人</div>
                    <div class="flex" v-else>
                      <div class="color-main mr-5">{{ `${defaultParams.applicantUserName} / ${defaultParams.applicantUser}` }}</div>
                    </div>
                  </template>
                </van-field>

              </van-cell-group>

              <van-row justify="space-around" style="padding: 15px 0">
                <van-button class="list-search-btn" size="small" type="default" round @click="reSet"> 清空 </van-button>
                <van-button class="list-search-btn" size="small" type="primary" round @click="reSearch">
                  确认
                </van-button>
              </van-row>
            </van-dropdown-item>
          </van-dropdown-menu>
        </template>
      </van-search>

      <van-tabs v-model:active="defaultParams.approveState">
        <van-tab v-for="(item, index) in todoStateSelect"  :name="item.type" :title="item.name" :key="index" > </van-tab>
      </van-tabs>

    </van-sticky>

    <van-list
      v-model:loading="orderLoading"
      :finished="orderFinished"
      :finished-text="orderList.length ? '没有更多了' : ''"
      @load="loadorderList"
      class="mt-10"
    >
      <van-cell-group
        inset
        class="mt-10"
        v-for="(item, index) in orderList"
        :key="index"
      >
        <van-cell title-class="large-title">
          <template #title>
            <div class="weight600 font-size-12">审批单号:{{ item?.recordCode }}</div>
          </template>
          <template #label>
            <div>业务单号:{{ item.businessCode }}</div>
            <div>申请人:{{ item.applicantName }}</div>
          </template>

          <template #value>
            <van-tag plain size="medium" :color="ApproveStatelStatusToTagColor[item.approveState] || ''">{{ ApproveStatelStatus[item.approveState] || '' }}</van-tag>
          </template>
        </van-cell>

        <van-cell title="标题" :value="item.title" value-class="large-value">
          
        </van-cell>
        <van-cell title="待办状态" :value="ProcessResultStateConstant.ofType(item.resultState)?.name" >
         
        </van-cell>
        <van-cell title="审批完成时间" :value="item.completeTime">
          
        </van-cell>
        <van-cell title="审批创建时间" :value="item.gmtCreate">
          
        </van-cell>
        <van-cell title="流程名称" :value="item.pdName">
          
        </van-cell>
        <van-cell title="描述" :label="item.description">
          
        </van-cell>

       

        <van-cell>
          <template #value>
            <div>
              <van-button class="mr-10" v-if="loginUser?.username === item.ownerId && item.approveState === 1" type="primary" size="small" @click.stop="handle(item.id)">去处理</van-button>
              <van-button  class="mr-10" type="danger" v-else  size="small" @click.stop="handle(item.id)">去查看</van-button>
            </div>
          </template>
        </van-cell>
      </van-cell-group>
    </van-list>

    <van-popup v-model:show="showMainPersonList" position="bottom">
        <div class="out-pop" style="height: 100vh">
          <van-sticky>
            <div style="background: #fff; padding-bottom: 10px">
              <van-nav-bar title="选择出行人" left-arrow @click-left="closeMainPersonPop">
                <template #left>
                  <van-icon name="arrow-left" color="#000" size="24" />
                </template>
                <template #right>
                  <div class="color-main"></div>
                </template>
              </van-nav-bar>

              <van-cell-group inset style="margin: 16px; background-color: #fff">
                <van-field
                  @update:model-value="searchMainPerson"
                  v-model="searchValue"
                  style="background: #f6f7f9"
                  placeholder="输入工号、姓名"
                />
              </van-cell-group>
            </div>
          </van-sticky>
          <van-list
            v-model:loading="mainPersonLoading"
            :finished="mainPersonFinished"
            :finished-text=" mainPersonList.length ? '没有更多了':'' "
            @load="onLoadMainPerson"
          >
          <div v-if="mainPersonList.length == 0 && mainPersonFinished" style="height: 66vh;" class="flex align-items-center justify-content-center">
            <img class="img_empty" src="../../assets/image/trip/empty.jpg" alt="">
          </div>
          <template v-else>
            <van-cell v-for="(item, index) in mainPersonList" :key="index" @click="choseMainPerson(item)">
              <div class="flex align-items-center out-person-checkbox">
                <div
                  :class="index % 3 == 0 ? 'blue' : 'yellow'"
                  class="mr-20 ml-10 flex align-items-center justify-content-center img-name"
                >
                  {{ item?.nickName ? item?.nickName.slice(-2) : '未知' }}
                </div>
                <div class="main">
                  <div class="user-name color-main">{{ item.nickName }}</div>
                  <div class="phone">{{ item.enterpriseName || '未知' }}</div>
                </div>
              </div>
            </van-cell>
          </template>
           
          </van-list>
        </div>
      </van-popup>

    <van-empty v-if="!orderLoading && orderList.length == 0" description="暂无数据" />

    
  </div>
</template>

<script lang="ts" setup>
import { ref, watch,onActivated, computed } from 'vue';

import {
  IUserListRequest,
  ROrderPayMentStateEnum,
  ROrderPayMentStateEnumColor,
  ROrderApprovalStateEnum,
  ROrderApprovalStateEnumColor,
  ROrderStateEnum,
  ROrderStateEnumColor,
  ProcessTodoStateConstant,
  ProcessResultStateConstant
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { tripApi } from '@haierbusiness-front/apis';
import {
  TripApprovalStatus,
  TripDocumentStatus,
  TripChangeApprovalStatus,
  TripChangeApprovalStatusToTagColor,
  TripChangeStatus,
  TripBudgeStatus,
  ICreatTrip,
  ITraveler,
  TripApprovalStatusToTagColor,
  TripDocumentStatusToTagColor,
  TripChangeStatusToTagColor,
  TripBudgeStatusToTagColor,
  IDataListItem,
  ICity,
  CityItem,
  ApproveStatelStatusToTagColor,
  ApproveStatelStatus
} from '@haierbusiness-front/common-libs';
import type { Ref } from 'vue';
import { getEnumOptions } from '@haierbusiness-front/utils';

import dayjs from 'dayjs';
import { processApi,userApi } from '@haierbusiness-front/apis';
import { debounce, values } from 'lodash';

import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { showConfirmDialog, showSuccessToast,showFailToast } from 'vant';
const store = applicationStore();

const { loginUser } = storeToRefs(store);

const router = getCurrentRouter();
const route = ref();

const baseUrl = import.meta.env.VITE_BUSINESS_URL

// 出行人相关
const showMainPersonList = ref(false);
const mainPersonLoading = ref(false);
const mainPersonFinished = ref(false);
const chosedPerson = ref<ITraveler>({});
const mainPersonTotal = ref(0);

const mainPersonList = ref<Array<ITraveler>>([]);

const defaultParams2: IUserListRequest = {
  pageNum: 0,
  pageSize: 20,
};

const params: Ref<IUserListRequest> = ref(defaultParams2);

const onLoadMainPerson = () => {
  params.value.pageNum++;
  userApi.list(params.value).then((res) => {
    // 加载状态结束
    mainPersonLoading.value = false;
    mainPersonList.value = [...mainPersonList.value, ...res.records];
    mainPersonTotal.value = res.total ?? 0;
    // 数据全部加载完成
    if (mainPersonList.value.length >= mainPersonTotal.value) {
      mainPersonFinished.value = true;
    }
  });
};

const searchMainPerson = debounce((val: string) => {
  params.value.keyWord = val;
  params.value.pageNum = 0;
  params.value.pageSize = 20;
  mainPersonFinished.value = false;
  mainPersonList.value = [];
  onLoadMainPerson();
});

const closeMainPersonPop = () => {
  showMainPersonList.value = false;
};

const choseMainPerson = (item: ITraveler) => {
  defaultParams.value.applicantUser = item?.username
  defaultParams.value.applicantUserName = item?.nickName ?? ''
  showMainPersonList.value = false;
};

const handle = (id: number | undefined) => {
    if(!id) {
      showFailToast('出错了!')
        return
    }
    const url = baseUrl + `hbweb/process/?todoId=${id}#/details`
    location.href = url
}


const todoStateSelect = ProcessTodoStateConstant.toArray().splice(0,2)




// 订单列表
const orderLoading = ref<boolean>(false);
const orderFinished = ref<boolean>(false);
const orderList = ref<Array<any>>([]);
const orderTotal = ref<number>(0);

const defaultParams = ref<any>({
  code: '',
  approveState: 1,
  type: 2,
  pageNum: 1,
  pageSize: 10,
});

const businessTravel = import.meta.env.VITE_BUSINESS_TRIP_URL;




const loadorderList = () => {
  defaultParams.value.pageNum++;
  processApi.list(defaultParams.value).then((res:any) => {
    // 加载状态结束
    orderLoading.value = false;
    orderList.value = [...orderList.value, ...res.records];
    orderTotal.value = res.total ?? 0;
    // 数据全部加载完成
    if (orderList.value.length >= orderTotal.value) {
      orderFinished.value = true;
    }
  });
};

onActivated(() => {
  route.value = getCurrentRoute()
  const type = route.value?.query?.reload
  if ( type == '1') {
    console.log('reload', type)
    reSet()
    reSearch();
  }
})

watch(
  () => defaultParams.value.approveState,
  () => {
    reSearch();
  },
);

// 清空
const reSet = () => {
  defaultParams.value = {
    ownerIsOwn: loginUser.value?.username, //联系人工号
    pageNum: 0,
    pageSize: 20,
    approveState: 1,
    type: 2
  };
};

// 搜索
const menuRef = ref(null);
const reSearch = () => {
  orderLoading.value=true
  orderFinished.value = false
  defaultParams.value.pageNum = 0;
  orderList.value = []
  loadorderList();
  menuRef.value.close();
};


</script>

<style lang='less' scoped>
@import url(../apply/mobile/components/mobile.less);

:deep(.van-dropdown-menu__bar) {
  //  background: var(--van-dropdown-menu-background);
  box-shadow: none;
}
.list-search {
  :deep(.van-search__action) {
    // padding: 0;
  }
}
.list-search-btn {
  width: 110px;
}
:deep(.large-value) {
  min-width: 70%;
}
:deep(.large-title) {
  min-width: 80%;

}
.my-radio {
  :deep(.van-radio__icon) {
    height: auto !important;
  }
}
.btn-com {
  width: 70px;
}
.input-border {
  border: 1px solid #dcdee0;
  border-radius: 20px;
  padding: 2px 10px;
}

.order-list {
  // margin-top: 36px;
  background-color: #f3f3f3;
}
.mt-10 {
  margin-top: 10px;
}
.weight600 {
  font-weight: 600;
}
</style>