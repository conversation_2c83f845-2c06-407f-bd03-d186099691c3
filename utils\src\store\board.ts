//数据中台大屏用
import { defineStore } from "pinia"

type BoardState = {
    date: [string, string] | [];
    company: string,
    dataSource: string;
    budgetDepartment: string,
    areaCode:string,
    ptCode:string
    plCode:string
}

export const useBoardStore = defineStore("board", {
    state: (): BoardState => {
        return {
            date: [],
            company: "",
            dataSource: '',
            budgetDepartment: '',
            areaCode:'',
            ptCode:'',
            plCode:''
        }
    },
    actions: {
        setDate(date: BoardState["date"]) {
            this.date = date;
        },
        setCompany(code: string) {
            this.company = code;
        },
        setData(data: string) {
            this.dataSource = data;

        },
        setBudgetDepartment(code: string) {
            this.budgetDepartment = code;
        },
        setAreaCode(code: string) {
            this.areaCode = code;
        },

        setPtCode(code: string) {
            this.ptCode = code;
        },

        setPlCode(code: string) {
            this.plCode = code;
        },
    }
})