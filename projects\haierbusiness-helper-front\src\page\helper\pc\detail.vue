<script lang="ts" setup>
import { computed, createVNode, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';

import {
  Anchor as hAnchor,
  <PERSON>ton as hButton,
  Tag as hTag,
  Spin as hSpin,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  RadioButton as hRadioButton,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Checkbox as hCheckbox,
  Form as hForm,
  FormItem as hFormItem,
  FormItemRest as hFormItemRest,
  Row as hRow,
  Col as hCol,
  Popconfirm as hPopconfirm,
  Upload as hUpload,
  Input as hInput,
  Modal as hModal,
  Space as hSpace,
  Pagination as hPagination,
  Drawer as hDrawer,
  <PERSON><PERSON> as hA<PERSON>t,
  Divider as hDivider,
  Affix as hAffix,
  Modal,
  Image as hImage,
  ImagePreviewGroup as hImagePreviewGroup,
  Card as hCard
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps, UploadProps } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  PhoneFilled,
  WechatFilled,
  AliwangwangFilled,
  SwapRightOutlined,
  DownloadOutlined,
  VerticalAlignBottomOutlined,
  ExclamationCircleFilled,
  EnvironmentFilled,
  InfoCircleOutlined,
  PlusOutlined,
  ZoomInOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  SendOutlined,
  PhoneOutlined,
  VerticalAlignTopOutlined,
  DeleteOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IHelperThingsTypeEnum,IHelperThingsTypeTagColorMap,IHelperWeightTypeEnum,UserTicketResponseDTO,UserFlightTicketDTO,IHelperStatusEnum,IHelperStateTagColorMap, IHelperSearchParam, IHelperListRes, IHelperReq } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { helperApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import relativeTime from 'dayjs/plugin/relativeTime';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';

import { fileApi } from '@haierbusiness-front/apis';
import type { TableColumnType } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { getEnumOptions } from '@haierbusiness-front/utils';
import { message, Upload } from 'ant-design-vue';
import { get } from 'lodash';

const route = ref(getCurrentRoute());
const router = getCurrentRouter();

const id = route.value?.query?.id;

// 需求详情 ---------
const detail = ref<IHelperReq>()

const getDetail = (id: string) => {
  helperApi.getById(id).then((res: any) => {
    res.acceptObj = res?.acceptRecords.find((item:any) => item.acceptStatus == 20)
    detail.value = res
  }).catch((err:any) => {
  })
}

watch(
  () => id, 
  (newVal, oldVal) => {
    getDetail(newVal)
  },
  {
    immediate: true,
  }
)
</script>
<template>
  <div class="container" style="min-height: 100vh;">
        <h-row justify="center" class="headerTitle" align="middle" style="padding: 40px; font-size: 20px; font-weight: 600">
          需求详情
        </h-row>
        <div class="w-1000">
          <h-card size="default" >
            <h-row :gutter="24">
              <h-col class="mt-20 phone-w" :span="12">发布时间: {{detail?.createTime}}</h-col>
              <h-col class="mt-20 phone-w" :span="12">联系电话: {{ detail?.creatorPhone }}</h-col>
              <h-col class="mt-20 phone-w" :span="12">期望送达时间: {{ dayjs(detail?.expectTimeFrom).format(' M月D日 HH:mm') }} - {{
                dayjs(detail?.expectTimeTo).format('M月D日 HH:mm')}} </h-col>
              <h-col class="mt-20 phone-w" :span="12">需求发布人:  {{`${detail?.createUserName}(${detail?.createUser})` }}</h-col>
              <h-col class="mt-20 phone-w" :span="12">物品类型: {{ IHelperThingsTypeEnum[detail?.objectType] }} </h-col>
              <h-col class="mt-20 phone-w" :span="12">物品重量: {{ IHelperWeightTypeEnum[detail?.objectWeightRange] }} </h-col>

              <h-col class="mt-20 phone-w" :span="24">物品描述: {{ IHelperThingsTypeEnum[detail?.objectType] }} </h-col>

              <h-col class="mt-20 phone-w" :span="24">物品图片: <h-image-preview-group>
                  <h-space style="flex-wrap: wrap">
                    <h-image class=" mr-10" v-for="item,index in detail?.files" :key="index" :width="80" :height="80"
                      :src="item.fileUrl" />
                  </h-space>
                </h-image-preview-group> </h-col>

            </h-row>
          </h-card>
         
        </div>
  </div>
</template>

<style lang="less" scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;

}
.mt-20 {
  margin-top: 20px;
}
@media screen and (max-width: 599px) {
  .w-1000 {
    width: 98%;
    margin: 6px auto;
  }

  .phone-w {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .headerTitle{
    display: none;
  }
  :deep(.ant-card-head){
    min-height: 36px;
  }
  :deep(.ant-card-body){
    padding:12px 24px;
  }
}
</style>