<!-- 询价新增弹框 -->

<script lang="ts" setup>
import {
  Modal,
  Form,
  FormItem,
  Select,
  Input,
  message,
  Button
} from 'ant-design-vue';
import { computed, ref, watch, onMounted, h } from "vue";
import type { Ref } from "vue";
import {
  hotelLevelAllConstant,
  IPriceInquiry,
  IPriceInquiryFilter
} from '@haierbusiness-front/common-libs';
import { priceInquiryApi } from '@haierbusiness-front/apis';
// 使用正确的组件路径
import cityChose from "@haierbusiness-front/components/cityChose/index.vue";


// 防抖函数
const debounce = (fn: Function, delay: number) => {
  let timer: number | null = null;
  return (...args: any[]) => {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      fn(...args);
      timer = null;
    }, delay) as unknown as number;
  };
};

// 扩展IPriceInquiry类型，以便支持platformHotelCode
interface ExtendedPriceInquiry extends IPriceInquiry {
  platformHotelCode: string;
  platformHotelName: string;
  hotelAddress: string;
  cityId?: string | number;
  cityName?: string;
  starLevel?: string;
  regionName?: string;
}

interface Props {
  show: boolean;
  data: IPriceInquiry | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const from = ref();
const confirmLoading = ref(false);
const loading = ref(false);
const hotels = ref<{ value: string | number, label: string, hotelCode: string, code: string, hotelName: string, hotelAddress: string, starLevel: string, regionName: string }[]>([]);
const hotelSearchText = ref('');
// 城市名称作为字符串，必须使用字符串而非对象
const selectedCityName = ref('');

// 确保defaultData包含所有必要字段
const defaultData: ExtendedPriceInquiry = {
  hotelAddress: '',
  platformHotelCode: '',
  platformHotelName: '',
  reason: '',
  id: null,
  cityId: undefined,
  cityName: '',
  starLevel: '',
};

const priceInquiry: Ref<ExtendedPriceInquiry> = ref({ ...defaultData });

const emit = defineEmits(["cancel", "ok"]);

const visible = computed(() => props.show);

// 获取酒店列表
const fetchHotels = async (hotelName = '') => {
  if (!priceInquiry.value.cityId) {
    message.warning('请先选择城市');
    return;
  }

  loading.value = true;
  try {
    const params: any = {
      pageSize: 2000,
      pageNum: 1,
      cityId: priceInquiry.value.cityId
    };

    if (hotelName) {
      params.hotelName = hotelName;
    }

    const result = await priceInquiryApi.listHotel(params);
    console.log(result, 'result');

    if (result && Array.isArray(result)) {
      hotels.value = result.map(hotel => {
        // 确保value有值，如果hotelCode为空，使用id作为value
        const hotelValue = hotel.hotelCode || hotel.id || `hotel_${Math.random().toString(36).substr(2, 9)}`;

        return {
          value: hotelValue, // 确保value不为空
          label: hotel.hotelName || hotel.name || '未命名酒店',
          hotelCode: hotel.hotelCode || hotelValue, // 如果hotelCode为空，使用value
          code: hotel.code || hotel.hotelCode || hotelValue, // 优先使用hotel.code
          hotelName: hotel.hotelName || hotel.name || '未命名酒店',
          hotelAddress: hotel.address || hotel.hotelAddress || '暂无地址',
          starLevel: hotel.starLevel || '',
          regionName: hotel.regionName || ''
        };
      });

    } else {
      message.error('获取酒店列表格式不正确');
    }
  } catch (error) {
    console.error('获取酒店列表失败:', error);
    message.error('获取酒店列表失败');
  } finally {
    loading.value = false;
  }
};

// 监听弹窗显示状态
watch(() => props.show, async (newValue) => {
  if (newValue) {
    // 弹窗显示时，重置数据
    if (props.data) {
      const data = props.data as unknown as ExtendedPriceInquiry;
      priceInquiry.value = {
        ...props.data,
        platformHotelCode: data.hotelId || '',
        // 为了类型安全，明确设置这些字段
        cityId: data.cityId,
        cityName: data.cityName
      } as ExtendedPriceInquiry;

      // 如果有城市信息，设置selectedCityName
      if (data.cityName) {
        selectedCityName.value = data.cityName;
      } else {
        selectedCityName.value = '';
      }

      // 如果有cityId，加载酒店列表
      if (data.cityId) {
        await fetchHotels();
        // 如果已有选中的酒店，需要在获取酒店列表后设置
        if (data.hotelId || data.platformHotelCode) {
          console.log('编辑模式，已选酒店:', data.hotelId || data.platformHotelCode);
        }
      }
    } else {
      priceInquiry.value = { ...defaultData };
      selectedCityName.value = '';
    }
  }
});

// 监听城市变化
watch(() => priceInquiry.value.cityId, (newValue) => {
  if (newValue) {
    // 清空已选酒店
    priceInquiry.value.platformHotelCode = '';
    priceInquiry.value.platformHotelName = '';
    priceInquiry.value.hotelAddress = '';

    // 根据城市ID获取酒店列表
    fetchHotels();
  } else {
    // 清空酒店列表
    hotels.value = [];
  }
});

// 过滤酒店选项
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 搜索酒店
const handleSearch = (value: string) => {
  hotelSearchText.value = value;
  if (value) {
    fetchHotels(value);
  } else {
    fetchHotels();
  }
};

// 处理城市选择
const handleCityChange = (city: { id?: string | number, name?: string, address?: string }) => {
  // 必须处理为字符串
  selectedCityName.value = city.name || '';

  if (city && city.id) {
    priceInquiry.value.cityId = city.id;
    priceInquiry.value.cityName = city.name || '';

    // 如果有地址信息，保存下来
    if (city.address) {
      priceInquiry.value.hotelAddress = city.address;
    }

    // 选择城市后自动获取酒店列表
    fetchHotels();
  } else {
    priceInquiry.value.cityId = undefined;
    priceInquiry.value.cityName = '';
    priceInquiry.value.hotelAddress = '';
    hotels.value = [];
  }
};

const handleOk = async () => {
  confirmLoading.value = true;
  try {
    await from.value.validate();
    console.log(priceInquiry.value, 'priceInquiry.value');


    const submitData = {
      platformHotelCode: priceInquiry.value.platformHotelCode,
      platformHotelName: priceInquiry.value.platformHotelName,
      platformHotelAddress: priceInquiry.value.hotelAddress,
      reason: priceInquiry.value.reason,
      platformHotelStar: String(priceInquiry.value.starLevel) || "0",
      platformHotelArea: priceInquiry.value.regionName,
      isLocal: false
    };
    if (selectedCityName.value == '青岛') {
      submitData.isLocal = true
    }
    message.success('创建成功');
    emit("ok", submitData);
    emit("cancel");
  } catch (validationError) {
    console.error('表单验证失败:', validationError);
  } finally {
    confirmLoading.value = false;
  }
};

const handleHotelChange = (value: any) => {
  console.log('选择的酒店值:', value);
  console.log('当前酒店列表:', hotels.value);

  // 同时尝试通过code和value匹配
  const selectedHotel = hotels.value.find(hotel => {
    return String(hotel.code) === String(value) ||
      String(hotel.value) === String(value) ||
      String(hotel.hotelCode) === String(value);
  });

  if (selectedHotel) {
    console.log('找到匹配酒店:', selectedHotel);
    priceInquiry.value.platformHotelCode = selectedHotel.code || selectedHotel.hotelCode;
    priceInquiry.value.platformHotelName = selectedHotel.hotelName;
    priceInquiry.value.hotelAddress = selectedHotel.hotelAddress || '';
    priceInquiry.value.starLevel = selectedHotel.starLevel || '';
    priceInquiry.value.regionName = selectedHotel.regionName || '';

    setTimeout(() => {
      message.success(`已选择酒店: ${selectedHotel.hotelName}`);
    }, 100);
  } else {
    console.log('未找到匹配酒店，选择值为:', value);
    priceInquiry.value.platformHotelCode = '';
    priceInquiry.value.platformHotelName = '';
    priceInquiry.value.hotelAddress = '';
    priceInquiry.value.starLevel = '';
    priceInquiry.value.regionName = '';
    setTimeout(() => {
      message.warning('未找到匹配的酒店');
    }, 100);
  }
};

// 测试酒店选择函数
const testHotelChange = () => {
  console.log('测试按钮被点击');
  console.log('当前酒店列表:', hotels.value);

  // 如果有酒店数据，使用第一个进行测试
  if (hotels.value.length > 0) {
    const testValue = hotels.value[0].value;
    console.log('将使用第一个酒店进行测试:', testValue);
    handleHotelChange(testValue);
  } else {
    message.warning('酒店列表为空，请先选择城市');
    console.log('酒店列表为空，无法测试');
  }
};

// 查看酒店详情
const showHotelDetail = () => {
  if (!priceInquiry.value.platformHotelCode) {
    message.warning('请先选择酒店');
    return;
  }

  // 查找选中的酒店详情
  const selectedHotel = hotels.value.find(hotel => {
    return String(hotel.code) === String(priceInquiry.value.platformHotelCode) ||
      String(hotel.value) === String(priceInquiry.value.platformHotelCode) ||
      String(hotel.hotelCode) === String(priceInquiry.value.platformHotelCode);
  });

  if (selectedHotel) {
    Modal.info({
      title: '酒店详情',
      width: 600,
      content: h('div', { style: 'max-height: 400px; overflow-y: auto;' }, [
        h('p', {}, [h('strong', {}, '酒店名称：'), selectedHotel.hotelName]),
        h('p', {}, [h('strong', {}, '酒店地址：'), selectedHotel.hotelAddress || '暂无']),
        h('p', {}, [h('strong', {}, '所在城市：'), priceInquiry.value.cityName || '暂无']),
        h('p', {}, [h('strong', {}, '星级：'), hotelLevel(selectedHotel.starLevel) || '暂无']),
        h('p', {}, [h('strong', {}, '区域：'), selectedHotel.regionName || '暂无']),
        h('p', {}, [h('strong', {}, '酒店代码：'), selectedHotel.code || selectedHotel.hotelCode])
      ]),
      okText: '关闭'
    });
  } else {
    message.warning('未找到酒店详情');
  }
};
//查找酒店星级
const hotelLevel = (text:number | string)=>{
  if(Number(text) <= 7 ){
    return hotelLevelAllConstant.ofType(7)?.desc
  }else{
    return hotelLevelAllConstant.ofType(Number(text))?.desc
  }
  
}

// 组件挂载时
onMounted(() => {
  // 如果是编辑模式且有城市ID，则加载酒店列表
  if (props.data?.cityId) {
    // 延迟执行以确保表单已加载
    if (priceInquiry.value.cityId) {
      fetchHotels();
      console.log('已加载酒店列表:', hotels.value.length);

      // 尝试找到并选中正确的酒店
      const hotelId = props.data?.hotelId || props.data?.platformHotelCode;
      if (hotelId) {
        console.log('尝试选中酒店:', hotelId);

        // 确保酒店列表中包含该酒店
        const hotel = hotels.value.find(h => h.hotelCode === hotelId || h.value === hotelId);
        console.log('酒店列表:', hotels.value);
        console.log('尝试选中酒店:', hotel);
        if (hotel) {
          // 手动触发选择事件
          handleHotelChange(hotel.value);
          console.log('已选中酒店:', hotel.hotelName);
        }
      }
    }
  }
});
</script>

<template>
  <Modal :visible="visible" :title="priceInquiry.id ? '编辑询价' : '新增询价'" :width="650" @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading" @ok="handleOk" :destroyOnClose="true">
    <Form ref="from" :model="priceInquiry" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
      <FormItem label="酒店名称" name="platformHotelCode" required>
        <div style="display: flex; align-items: center;">
          <div style="width: 30%; margin-right: 8px;">
            <city-chose :showInternational="false" :value="selectedCityName" @chosedCity="handleCityChange"
              :width="'100%'" placeholder="请选择城市">
            </city-chose>
          </div>
          <div style="display: flex; width: 70%;">
            <Select v-model:value="priceInquiry.platformHotelCode" placeholder="请选择酒店" :options="hotels"
              @change="(val) => { console.log('Select change事件触发', val); handleHotelChange(val); }"
              @search="handleSearch" :loading="loading" style="flex: 1;" allow-clear show-search
              :filter-option="filterOption" :disabled="!priceInquiry.cityId"
              :field-names="{ label: 'label', value: 'code' }">
              <template #option="{ label, value }">
                <div>
                  <div>{{ label }}</div>
                  <div style="color: #999; font-size: 12px;">
                    {{hotels.find(h => h.value === value)?.hotelAddress || '暂无地址信息'}}
                  </div>
                </div>
              </template>
            </Select>
            <Button type="primary" style="margin-left: 8px;" :disabled="!priceInquiry.platformHotelCode"
              @click="showHotelDetail">
              详情
            </Button>
          </div>
        </div>
      </FormItem>

      <FormItem label="引入理由" name="reason" required>
        <Input.TextArea v-model:value="priceInquiry.reason" placeholder="请输入引入理由，最多200字" :maxlength="200"
          :autoSize="{ minRows: 3, maxRows: 5 }" show-count />
      </FormItem>
    </Form>
  </Modal>
</template>

<style lang="less" scoped>
.important {
  color: red;
}

:deep(.ant-form-item-required::before) {
  display: none !important;
}
</style>