<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, DownOutlined, UpOutlined, EyeInvisibleOutlined,EyeOutlined } from '@ant-design/icons-vue';
import { banquetApplyApi } from '@haierbusiness-front/apis';
import {
  BApplyListRecord,
  BApplyPerson,
  BanquetStatusEnum,
  ProcessRecordStepConstant,
  HaierBudgetTypeConstant
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';

import router from '../../router'
// const router = useRouter()
const store = applicationStore();

const currentRouter = ref()
const route = ref(getCurrentRoute());
const id = route.value?.query?.id;
const detail = ref<any>();
const budgetSysCodeArray = ['BCC','GEMS','KEMS','RRSGEMS']

const { loginUser } = storeToRefs(store);

const getDetail = (id: number) => {
  banquetApplyApi.get(id).then((res:any) => {
    res.bookings.push(...res.refunds)
    detail.value = res;
    if(res.processCode){
      getApproveDetail(res.processCode)
    }
  });
};
const approveDetail = ref<any>({})
const showMore = ref(true)
const applicationFormDetailsOpen = ref<boolean>(false)
const informationOpen = ref<boolean>(false)
const isMaskPhoneNumber = ref<boolean>(true)

onMounted(async () => {
  currentRouter.value = await router
  console.log(route.value,"route.value")
})

const getResult = (result?: number) => {
  return ProcessRecordStepConstant.ofType(result)?.name
}

const getInnerPerson = (list?:Array<BApplyPerson>) => {
  if (list && list.length > 0) {
   let resultList = list.filter(item => item.haierUser == true)
   return resultList.map(item => `${item.userName}(${item.userCode})`).join(',')
  }else {
     return ''
  }

}

const getOuterPerson = (list?:Array<BApplyPerson>) => {
  if (list && list.length > 0) {
    let resultList = list.filter(item => !item.haierUser)
    return resultList.map(item => item.userName).join(',')
  }else {
    return ''
  }
}

const downloadFile2 = (url:string, name:string) => {
      var xhr = new XMLHttpRequest();
      xhr.open('GET', url, true);
      xhr.responseType = 'blob';

      xhr.onload = function() {
        if (xhr.status === 200) {
          var blob = xhr.response;
          var a = document.createElement('a');
          var url = URL.createObjectURL(blob);
          a.href = url;
          a.download = name;
          a.click();
          URL.revokeObjectURL(url);
        }
      };

      xhr.send();
    }

const downLoadFile = (url:string, name:string) => {
  window.open(url)
  // downloadFile2(url,name)
}

// 查询审批详情
const getApproveDetail=(code)=>{
  banquetApplyApi.details({code}).then((res) => {
    console.log(res.processRecord.steps)
    approveDetail.value = res.processRecord.steps
  });
}

const toList = (code,type) => {
  if(type==1){
    currentRouter.value.push({
      path: '/reservation',
      query:{
        orderBookingCode:code
      }
    })
  }else if(type==2){
    currentRouter.value.push({
      path: '/apply',
      query:{
        orderCode:code
      }
    })
  }else{
    currentRouter.value.push({
      path: '/refundForm',
      query:{
        refundOrderCode:code
      }
    })
  }
}

const maskPhoneNumber=(phoneNumber:string)=>{
  return phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}

watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);




</script>

<template>
  <div>
    <h-row v-if="route.path!='/apply/onlydetail'" justify="space-between" style="padding: 20px;">
      <h-col style="font-size: 18px;">申请单详情</h-col>
      <h-col><h-button type="link" @click="currentRouter.back(-1)">返回</h-button></h-col>
    </h-row>
    <div :class="{onlydetail:route.path=='/apply/onlydetail'}" style="background-color: #ffff;height: 100%;width: 100%;padding: 30px 60px;overflow: auto;">
      <h-descriptions title="申请单信息" style="margin-bottom: 20px;" bordered>
        <template  #extra>
          <h-button type="link"  @click="applicationFormDetailsOpen = !applicationFormDetailsOpen">
            <template #icon>
              <UpOutlined v-if="applicationFormDetailsOpen"/>

              <DownOutlined v-else />
            </template>
            {{ applicationFormDetailsOpen ? '收起' : '展开' }}
          </h-button>
        </template>
          <h-descriptions-item v-if="applicationFormDetailsOpen" label="申请单号">{{ detail?.orderCode }}</h-descriptions-item>
          <h-descriptions-item v-if="applicationFormDetailsOpen"  label="经办人信息">{{ `${detail?.creatorName}(${detail?.creator})` }}</h-descriptions-item>
          <h-descriptions-item v-if="applicationFormDetailsOpen" label="联系电话">{{ isMaskPhoneNumber?maskPhoneNumber(detail?.creatorPhone):detail?.creatorPhone }} <EyeInvisibleOutlined @click="isMaskPhoneNumber=!isMaskPhoneNumber" v-if="!isMaskPhoneNumber"/> <EyeOutlined  @click="isMaskPhoneNumber=!isMaskPhoneNumber" v-else /></h-descriptions-item>
          <h-descriptions-item v-if="applicationFormDetailsOpen" label="订单类型">{{ detail?.sceneType == 1 ? '宴请' : '外卖' }}</h-descriptions-item>
          <h-descriptions-item v-if="applicationFormDetailsOpen" label="订单状态">{{
            BanquetStatusEnum[detail?.orderStatus] || ''
          }}</h-descriptions-item>
          <h-descriptions-item v-if="applicationFormDetailsOpen" label="申请时间">{{ detail?.applicationTime }}</h-descriptions-item>
          <h-descriptions-item v-if="applicationFormDetailsOpen" label="签单人信息">{{`${detail?.signerName}(${detail?.signerCode})`}}</h-descriptions-item>
          <h-descriptions-item v-if="applicationFormDetailsOpen" label="就餐城市">{{ detail?.mealLocationCity }}</h-descriptions-item>
          <h-descriptions-item v-if="applicationFormDetailsOpen" label="餐厅名称">{{ detail?.restaurantName }}</h-descriptions-item>
          <h-descriptions-item v-if="applicationFormDetailsOpen" label="预计就餐时间">
            {{ dayjs(detail?.estimatedMealTimeStart).format('YYYY-MM-DD')}}至{{ dayjs(detail?.estimatedMealTimeEnd).format('YYYY-MM-DD')}}
            <p style="color:red;">结算截止时间为:{{ dayjs(detail?.estimatedMealTimeEnd).add(1, 'day').format('YYYY-MM-DD') }} 06:00</p>
          </h-descriptions-item>

          <h-descriptions-item v-if="applicationFormDetailsOpen" label="附件信息" :span="2"><div style="color: #2870ff; cursor: pointer;" @click="downLoadFile(detail?.fileUrl, detail?.fileName)">{{ detail?.fileName }}</div></h-descriptions-item>
          <h-descriptions-item v-if="applicationFormDetailsOpen" label="申请事由" :span="3">{{ detail?.banquetReason }}</h-descriptions-item>
          <h-descriptions-item v-if="applicationFormDetailsOpen" label="备注信息" :span="3">{{ detail?.remark }}</h-descriptions-item>
      </h-descriptions>

      <h-descriptions title="预算消费信息" style="margin-bottom: 20px;" bordered>
        <template  #extra>
          <h-button type="link"  @click="informationOpen = !informationOpen">
            <template #icon>
              <UpOutlined v-if="informationOpen"/>

              <DownOutlined v-else />
            </template>
            {{ informationOpen ? '收起' : '展开' }}
          </h-button>
        </template>
        <h-descriptions-item v-if="informationOpen" label="预算申请金额"> <span v-if="detail?.budgetAmount||detail?.budgetAmount==0">{{ detail?.budgetAmount }}元</span></h-descriptions-item>
        <h-descriptions-item v-if="informationOpen" label="实际消费金额"><span v-if="detail?.actualPaymentAmount||detail?.actualPaymentAmount==0">{{ detail?.actualPaymentAmount }}元</span> </h-descriptions-item>
        <h-descriptions-item v-if="informationOpen" label="预算剩余金额"><span v-if="detail?.residualAmount||detail?.residualAmount==0">{{ detail?.residualAmount }}元</span> </h-descriptions-item>
        <h-descriptions-item v-if="informationOpen" label="释放金额"><span v-if="detail?.releaseAmount||detail?.releaseAmount==0">{{ detail?.releaseAmount }}元</span> </h-descriptions-item>
        <h-descriptions-item v-if="informationOpen" label="预算支付单号">{{detail?.payCode}} </h-descriptions-item>
        <h-descriptions-item v-if="informationOpen"  label="预算人信息"><div v-if="detail?.budgeterName">{{ detail?.budgeterName }}({{ detail?.budgeterCode }})</div> </h-descriptions-item>
        <h-descriptions-item v-if="informationOpen" label="预算系统">{{ HaierBudgetTypeConstant.ofCode(detail?.budgetSysCode)?.budgetSysCode }}({{ detail?.budgetSourceCode }})</h-descriptions-item>
        <h-descriptions-item v-if="informationOpen" label="费用科目">{{ detail?.feeItemName }}</h-descriptions-item>
        <h-descriptions-item v-if="informationOpen" label="预算部门">{{ detail?.budgetDepartmentName }}</h-descriptions-item>
      </h-descriptions>


      <!-- <h-descriptions title="就餐人信息" style="margin-bottom: 20px;" bordered>
        <h-descriptions-item label="就餐人数" :span='3'>{{ `${detail?.persons?.length}人` }}</h-descriptions-item>
        <h-descriptions-item label="内部就餐人" :span='3'>{{ getInnerPerson(detail?.persons) }}</h-descriptions-item>
        <h-descriptions-item label="外部就餐人" :span='3'>{{ getOuterPerson(detail?.persons) }}</h-descriptions-item>
      </h-descriptions> -->

      <h-descriptions v-for="(item,index) in detail?.bookings" v-show="showMore?true:index<=0" :title="index==0?'子订单信息':''" style="margin-bottom: 20px;" bordered>
        <template v-if="index==0" #extra>
          <h-button type="link"  @click="showMore = !showMore">
            <template #icon>
              <UpOutlined v-if="showMore"/>

              <DownOutlined v-else />
            </template>
            {{ showMore ? '收起' : '全部订单' }}
          </h-button>
        </template>
        
          <h-descriptions-item v-if="!item?.refundOrderCode" label="预订单号"> <a @click="toList(item?.orderBookingCode,1)">{{ item?.orderBookingCode }}</a></h-descriptions-item>
          <h-descriptions-item v-if="!item?.refundOrderCode" label="餐厅名称">{{ item?.restaurantName }}</h-descriptions-item>
          <h-descriptions-item v-if="!item?.refundOrderCode" label="就餐时间">{{ item?.mealTime }}</h-descriptions-item>
          <h-descriptions-item v-if="!item?.refundOrderCode" label="餐费金额">{{ item?.actualPaymentAmount }}</h-descriptions-item>
          <h-descriptions-item v-if="!item?.refundOrderCode" label="节省金额">{{ item?.totalReduceAmount }}</h-descriptions-item>
          <h-descriptions-item v-if="!item?.refundOrderCode&&item.payType==10" label="服务费率/%">{{ item?.mtServiceRate }}</h-descriptions-item>
          <!-- <h-descriptions-item label="就餐人数">{{ `${item?.persons?.length}人` }}</h-descriptions-item> -->
          <h-descriptions-item v-if="!item?.refundOrderCode&&item.sceneType==1" label="水票附件查看"> <div style="color: #2870ff; cursor: pointer;" @click="downLoadFile(item?.waterTicketInformation, '水票信息')">水票信息</div></h-descriptions-item>

          <h-descriptions-item v-if="item?.refundOrderCode" label="退款单号"> <a  @click="toList(item?.refundOrderCode,3)">{{ item?.refundOrderCode }}</a></h-descriptions-item>
          <h-descriptions-item v-if="item?.refundOrderCode" label="餐厅名称">{{ item?.restaurantName }}</h-descriptions-item>
          <h-descriptions-item v-if="item?.refundOrderCode" label="退款时间">{{ item?.dealTime }}</h-descriptions-item>
          <!-- <h-descriptions-item v-if="item?.refundOrderCode" label="签单人信息">{{ item?.signerName }}({{ item?.signerCode }})</h-descriptions-item> -->
          <!-- <h-descriptions-item label="就餐人数">{{ `${item?.persons?.length}人` }}</h-descriptions-item> -->
          <h-descriptions-item v-if="item?.refundOrderCode" label="退款金额">{{ item.refundAmount }}</h-descriptions-item>
          <h-descriptions-item v-if="item?.refundOrderCode&&item.payType==10" label="服务费率/%">{{ item?.mtServiceRate }}</h-descriptions-item>
      </h-descriptions>

      <h-descriptions v-for="(item,index) in approveDetail" :title="index==0?'审批信息':''" style="margin-bottom: 20px;" bordered>
        <h-descriptions-item :label='index==0?"审批人(预算经理)":"审批人(直线)"'>{{  item?.masterOperators[0].approverName }}({{ item?.masterOperators[0].approverCode}})</h-descriptions-item>
          <h-descriptions-item label="审批时间">{{  item?.masterOperators[0].completeTime }}</h-descriptions-item>
          <h-descriptions-item label="审批结果">{{  getResult(item?.result)}}</h-descriptions-item>
          <h-descriptions-item label="审批意见" :span='3'>{{ item?.masterOperators[0].approverRemark}}</h-descriptions-item>
      </h-descriptions>
      <h-descriptions v-show="budgetSysCodeArray.includes(HaierBudgetTypeConstant.ofCode(detail?.budgetSysCode)?.budgetSysCode)" :title="'审批信息'" style="margin-bottom: 20px;" bordered>
          <h-descriptions-item :span='3'>预算系统审批</h-descriptions-item>
      </h-descriptions>
    </div>
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}
:deep(.ant-descriptions-item-label) {
  width: 200px;
}
:deep(.ant-descriptions-item-content) {
  width: 300px;
}
.onlydetail{
  padding:10px!important;
}
</style>
