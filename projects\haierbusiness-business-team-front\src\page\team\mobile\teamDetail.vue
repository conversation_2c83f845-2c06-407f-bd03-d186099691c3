<template>
  <div class="order-detail">
    <!-- <van-nav-bar title="团队订票/订房详情" left-arrow>
      <template #left>
        <van-icon name="arrow-left" color="#000" @click="goBack" />
      </template>
    </van-nav-bar> -->

    <van-cell-group inset class="mb-10"  title="基本信息">
      <van-cell title="申请人">
        <template #value>
          <div>{{ detail?.createName }}({{ detail?.createBy }})</div>
        </template>
      </van-cell>
      <van-cell title="申请单号" title-style="flex: none;" :value="detail?.destineNo"></van-cell>
      <van-cell title="申请时间" :value="detail?.gmtCreate "></van-cell>
      <van-cell title="联系人">
        <template #value>
          <div>{{ detail?.contactUserName }}({{ detail?.contactUserCode }})</div>
        </template>
      </van-cell>
      <van-cell title="联系电话" :value="detail?.contactUserPhone "></van-cell>
      <van-cell title="目的城市" >
        <template #value>
          <div>{{ detail?.beginCityName }} - {{ detail?.endCityName }}</div>
        </template>
      </van-cell>
      <van-cell title="行程时间"  title-style="flex: none;">
        <template #value>
          <div>{{ detail?.beginDate }} - {{ detail?.endDate }}</div>
        </template>
      </van-cell>
      <van-cell title="出差类型" :value="detail?.evectionType== 0 ?'因公' :'因私'  "></van-cell>
      <!-- <van-cell title="出行人" title-style="flex: none;">
        <template #value>
          <div>
            <span class="color-main" v-if="detail?.travelerFileName">
              {{ detail?.travelerFileName}}
            </span>
            <span v-else>
              -
            </span>
            
          </div>
        </template>

      </van-cell> -->

      <van-cell title="产品类型">

        <template #label>
          <div class="label-box"  v-if="detail.destineInfo">

            <div class="label-item"  v-if="detail.destineInfo.indexOf('0') > -1 || detail.destineInfo.indexOf('1') > -1">
              <div class="label-type">{{ !detail.teamDestinePlaneTicket.planeTicketType ? '国内机票': '国际机票'}}</div>
              <div class="">{{detail.teamDestinePlaneTicket.voyageType ? '往返' : '单程'}}/{{detail.teamDestinePlaneTicket.travelPriod ? '下午' : '上午'}}出行/{{detail.teamDestinePlaneTicket.travelerNum || 0}}人出行</div>
              <div style="word-break: break-all;" class="">{{detail.teamDestinePlaneTicket.otherInfo || '暂无其他说明'}}</div>
            </div>

            <div class="label-item"  v-if="detail.destineInfo.indexOf('2') > -1">
              <div class="label-type">酒店</div>
              <div class="">{{detail.teamDestineHotel.kingRoomNum || 0}}间大床房/{{detail.teamDestineHotel.doubleRoomNum || 0}}间双床房</div>
              <div style="word-break: break-all;" class="">{{detail.teamDestineHotel.otherInfo || '暂无其他说明'}}</div>
            </div>
            
          </div>
        </template>
      </van-cell>
    </van-cell-group>

    <!-- 
    <van-cell-group inset class="mb-10" title="订票信息">
    </van-cell-group>

    <van-cell-group inset class="mb-10">
      <van-cell title="国内机票" class="strong"></van-cell>
      <van-cell title="团队合计金额(含税)" :value="detail.applicant.teamId"></van-cell>
      <van-cell title="非团队合计金额(含税)" :value="detail.applicant.teamId"></van-cell>
      <van-cell title="节省费用" :value="detail.applicant.teamId"></van-cell>
      <van-cell title="是否开票" :value="detail.applicant.teamId"></van-cell>
      <van-cell title="备注信息" :value="detail.applicant.teamId"></van-cell>
      <van-cell title="导入明细" :value="detail.applicant.teamId"></van-cell>
      <van-cell title="附件信息" :value="detail.applicant.teamId"></van-cell>

      <van-cell v-if="showCountryDetail">
            <div  class="mb-10  row-b-b" v-for="item, index in 7" :key="index">
              <van-row justify="space-between" class="mb-5">
                <div>票号</div>
                <div>票号</div>
              </van-row>

              <van-row justify="space-between" class="mb-5">
                <div>乘机人</div>
                <div>乘机人</div>
              </van-row>

              <van-row justify="space-between" class="mb-5">
                <div>起飞时间</div>
                <div>乘机人</div>
              </van-row>

              <van-row justify="space-between" class="mb-5">
                <div>航班号</div>
                <div>乘机人</div>
              </van-row>

              <van-row justify="space-between" class="mb-5">
                <div>订单号</div>
                <div>乘机人</div>
              </van-row>

              <van-row justify="space-between" class="mb-5">
                <div>团队价(含税)</div>
                <div>乘机人</div>
              </van-row>

              <van-row justify="space-between" class="mb-5">
                <div>非团队价(含税)</div>
                <div>乘机人</div>
              </van-row>
            </div>
      </van-cell>
      <van-cell @click="showCountryDetail = !showCountryDetail">
        <div class="align-items-center flex justify-content-center">
          <van-icon v-if="!showCountryDetail" name="arrow-down"  />
          <van-icon v-else name="arrow-up" />
          <span class="ml-10">{{!showCountryDetail ? '展开明细' : '收起'}}</span>
        </div>
      </van-cell>
      
    </van-cell-group>

    <van-cell-group inset class="mb-10">
      <van-cell title="国外机票" class="strong"></van-cell>
      <van-cell title="团队合计金额(含税)" :value="detail.applicant.teamId"></van-cell>
      <van-cell title="非团队合计金额(含税)" :value="detail.applicant.teamId"></van-cell>
      <van-cell title="节省费用" :value="detail.applicant.teamId"></van-cell>
      <van-cell title="是否开票" :value="detail.applicant.teamId"></van-cell>
      <van-cell title="备注信息" :value="detail.applicant.teamId"></van-cell>
      <van-cell title="导入明细" :value="detail.applicant.teamId"></van-cell>
      <van-cell title="附件信息" :value="detail.applicant.teamId"></van-cell>
      <van-cell v-if="showInternationalDetail">
            <div  class="mb-10  row-b-b" v-for="item, index in 7" :key="index">
              <van-row justify="space-between" class="mb-5">
                <div>票号</div>
                <div>票号</div>
              </van-row>

              <van-row justify="space-between" class="mb-5">
                <div>乘机人</div>
                <div>乘机人</div>
              </van-row>

              <van-row justify="space-between" class="mb-5">
                <div>起飞时间</div>
                <div>乘机人</div>
              </van-row>

              <van-row justify="space-between" class="mb-5">
                <div>航班号</div>
                <div>乘机人</div>
              </van-row>

              <van-row justify="space-between" class="mb-5">
                <div>订单号</div>
                <div>乘机人</div>
              </van-row>

              <van-row justify="space-between" class="mb-5">
                <div>团队价(含税)</div>
                <div>乘机人</div>
              </van-row>

              <van-row justify="space-between" class="mb-5">
                <div>非团队价(含税)</div>
                <div>乘机人</div>
              </van-row>
            </div>
      </van-cell>
      <van-cell @click="showInternationalDetail = !showInternationalDetail">
        <div class="align-items-center flex justify-content-center">
          <van-icon v-if="!showInternationalDetail" name="arrow-down"  />
          <van-icon v-else name="arrow-up" />
          <span class="ml-10">{{!showInternationalDetail ? '展开明细' : '收起'}}</span>
        </div>
      </van-cell>
    </van-cell-group>

    <van-cell-group inset class="mb-10">
      <van-cell title="酒店" class="strong"></van-cell>
      <van-cell title="团队合计金额(含税)" :value="detail.applicant.teamId"></van-cell>
      <van-cell title="非团队合计金额(含税)" :value="detail.applicant.teamId"></van-cell>
      <van-cell title="节省费用" :value="detail.applicant.teamId"></van-cell>
      <van-cell title="是否开票" :value="detail.applicant.teamId"></van-cell>
      <van-cell title="备注信息" :value="detail.applicant.teamId"></van-cell>
      <van-cell title="导入明细" :value="detail.applicant.teamId"></van-cell>
      <van-cell title="附件信息" :value="detail.applicant.teamId"></van-cell>

      <van-cell v-if="showHotelDetail">
        <div  class="mb-10  row-b-b" v-for="item, index in 7" :key="index">
              <van-row justify="space-between" class="mb-5">
                <div>票号</div>
                <div>票号</div>
              </van-row>

              <van-row justify="space-between" class="mb-5">
                <div>乘机人</div>
                <div>乘机人</div>
              </van-row>

              <van-row justify="space-between" class="mb-5">
                <div>起飞时间</div>
                <div>乘机人</div>
              </van-row>

              <van-row justify="space-between" class="mb-5">
                <div>航班号</div>
                <div>乘机人</div>
              </van-row>

              <van-row justify="space-between" class="mb-5">
                <div>订单号</div>
                <div>乘机人</div>
              </van-row>

              <van-row justify="space-between" class="mb-5">
                <div>团队价(含税)</div>
                <div>乘机人</div>
              </van-row>

              <van-row justify="space-between" class="mb-5">
                <div>非团队价(含税)</div>
                <div>乘机人</div>
              </van-row>
            </div>
      </van-cell>
      <van-cell @click="showHotelDetail = !showHotelDetail">
        <div class="align-items-center flex justify-content-center">
          <van-icon v-if="!showHotelDetail" name="arrow-down"  />
          <van-icon v-else name="arrow-up" />
          <span class="ml-10">{{!showHotelDetail ? '展开明细' : '收起'}}</span>
        </div>
      </van-cell>
      
    </van-cell-group>
    -->
   
  </div>
</template>

<script lang="ts" setup>
import {
  IUserListRequest,
  TCteateTeam,
  IUserInfo,
  ICity,
  ITripInfo,
  ICreatTrip,
  ITraveler,
  TGetListParams,
  TeamListStatusEnum,
  teamListStateTagColorMap
} from '@haierbusiness-front/common-libs';
import { onMounted, ref, watch } from 'vue';
import type { Ref } from 'vue';

import { teamApi } from '@haierbusiness-front/apis';

import { RHotelParams } from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { Item } from 'ant-design-vue/es/menu';
const router = getCurrentRouter();

const route = ref(getCurrentRoute());

const id = route.value?.query?.id;

// 展示更多
const showCountryDetail = ref(false)

const showInternationalDetail = ref(false)

const showHotelDetail = ref(false)



const activeNames = ref(['1']);

const detail = ref<TCteateTeam>({})
const getDetail = (id: string) => {
  

  teamApi.teamDetail(id).then(res => {
    detail.value = res
  })
};

const goBack = () => {
  router.back(-1);
};

watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);
</script>

<style lang="less" scoped>
@import url(./components/mobile.less);
.order-detail {
  min-height: 100vh;
  background-color: #f6f6f6;
  padding-bottom: 20px;
}
.label-box {
  padding: 10px;
    background: #f8f8f8;
    .label-item {
      border-bottom: 1px solid #f0e7e7;
      margin-bottom: 10px;
      padding: 5px 0;
      text-align: left;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      .label-type {
        font-size: 14px;
      }
      &:last-child {
        margin-bottom: 0;
        border: none;
      }
    }
}
.row-b-b {
  border-bottom: 1px solid #eee;
  &:last-child {
    border:none;
  }
}
</style>