<template>
  <div style="margin-bottom: 80px">
    <div class="title whole-line">附件</div>

    <h-form class="mt-30" ref="formRef" labelAlign="left" :label-col="labelCol" :wrapper-col="wrapperCol" :disabled="isDetail">
      <h-form-item ref="name" name="name">
        <template #label>
          <span>上传附件<a-tooltip>
              <template #title>可上传本次差旅相关的资料文件（如会议通知、邀请函等）</template>
              <question-circleOutlined class="icon" /> </a-tooltip
          ></span>
        </template>
        <div v-if="isDetail && (!creatTripParma?.fileList ||creatTripParma?.fileList?.length < 1)" class="font-color font-size-14">暂无附件</div>
        <h-upload
          v-model:file-list="creatTripParma.fileList"
          name="file"
          list-type="picture"
          :custom-request="upload"
          :class="isDetail ? 'detail' : ''"
          v-else
        >
          <h-button size="small" v-if="!isDetail">
            <upload-outlined class="font-size-14"></upload-outlined>
            <!-- <span class="font-size-14">上传文件</span> -->
            上传文件
          </h-button>
          <!-- <div class="text" v-if="!isDetail">上传支持的文件格式gif、jpg、jpeg、png..</div> -->
        </h-upload>
      </h-form-item>
    </h-form>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import { QuestionCircleOutlined, UploadOutlined } from '@ant-design/icons-vue';
import {
  Anchor as hAnchor,
  Button as hButton,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Upload as hUpload,
} from 'ant-design-vue';
import { ICreatTrip, IFile } from '@haierbusiness-front/common-libs';
import { fileApi } from '@haierbusiness-front/apis';

import type { UploadProps } from 'ant-design-vue';
import { message, Upload } from 'ant-design-vue';

interface Props {
  creatTripParma?: ICreatTrip;
  isDetail?: boolean;
}
const { creatTripParma, isDetail } = defineProps<Props>();

const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const labelCol = { span: 3 };
const wrapperCol = { span: 10 };
// const fileList = ref<UploadProps['fileList']>([])

const uploadLoading = ref(false);

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  if (
    !(file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif' || file.type === 'image/jpg')
  ) {
    message.warning('只能上传 JPG, JPEG, PNG 和GIF文件哦！');
    return Upload.LIST_IGNORE;
  }
  return true;
};
const upload = (options: any) => {
  uploadLoading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);
  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;
      options.file.fileName = options.file.name;
      
      // options.
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};
</script>

<style lang="less" scoped>
@import url(./trip.less);

.text {
  position: absolute;
  color: #999;
  font-size: 12px;
  bottom: -20px;
}
.detail {
  .ant-upload-list-item-action {
    display: none;
  }
}
</style>