<template>
  <a-upload :custom-request="uploadRequest" :max-count="1" accept=".pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx"
    @remove="handleRemove" v-model:file-list="fileList">
    <slot></slot>
  </a-upload>
</template>

<script lang="ts" setup>
import { Form } from 'ant-design-vue';
import { fileApi } from '@haierbusiness-front/apis';
import { computed, ref, watch } from 'vue';

const props = defineProps({
  value: { type: String, required: true }
});

console.log(props.value, "props.value");


const emit = defineEmits(['update:value']);

const formItemContext = Form.useInjectFormItemContext();

const fileList = computed({
  get: () => 
    props.value 
      ? [{
          uid: Date.now().toString(),
          name: props.value.split('/').pop() || 'file',
          status: 'done',
          url: props.value
        }] 
      : [],
  set: (val) => emit('update:value', val[0]?.url || '')
})

const handleRemove = () => {
  emit('update:value', '');
  formItemContext.onFieldChange();
}
const uploadRequest = (options: any) => {
  // uploadLoading.value = true;
  const baseUrl = import.meta.env.VITE_BUSINESS_URL;
  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;
      options.file.fileName = options.file.name;
      
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
      emit('update:value', options.file.filePath);
      formItemContext.onFieldChange();
    })
    .finally(() => {
      // uploadLoading.value = false;
    });
}
</script>