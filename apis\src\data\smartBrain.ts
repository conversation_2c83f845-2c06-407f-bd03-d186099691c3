import { get } from './../request';
import { download, get, post } from '../request'
import { 
    ISmartBrainFilter, 
    ISmartBrain,
    IPageResponse, 
    Result,
    Datum,
    IndicatorData,
    IndexList,
    ISetting,
    IReportManagerInfoListQuery,
    IReportManagerInfoListList,
    deptLabelVo,
    deptParmasVo,
    FetchBrainDataParams,
    IKnowCenterVo,
    IKnowSelectParmasVo,
    IKnowSelectResVo
 } from '@haierbusiness-front/common-libs'

export const smartBrainApi = {

    /**
     * 获取指标表签数据
     */
    getBrainKnowLabelList:():Promise<Array<Datum>> => {
        return get('data/api/brain/echarts-label/getBrainKnowLabelList')
    },
    /**
     * 获取指标管理列表
     */
    list: (params: ISmartBrainFilter): Promise<IPageResponse<IndexList>> => {
        return post('data/api/brain/echarts-cred/getIndicatorEchartsList', params)
    },
    /**
     * 获取用户申请的指标管理列表
     */
    getOwnerBrainReportList: (params:any): Promise<Array<IndexList>> => {
        return get('data/api/brain/manager-indic/getOwnerBrainReportList',params)
    },
    /**
     * 获取用户申请的指标管理列表
     */
    managerCreateOrUpdate: (params: ISetting): Promise<Result> => {
        return post('/data/api/brain/manager-indic/managerCreateOrUpdate', params)
    },
    /**
     * 分析类型下拉
     */
    getOwnerBrainReportManagerInfoList: (params?: IReportManagerInfoListQuery): Promise<Array<IReportManagerInfoListList>> => {
        return get('/data/api/brain/manager-indic/getOwnerBrainReportManagerInfoList', params)
    },
    /**
     * 获取分析类型详情
     */
    getBrainReportManagerVoById: (params?: IReportManagerInfoListQuery): Promise<IReportManagerInfoListList> => {
        return get('/data/api/brain/manager-indic/getBrainReportManagerVoById', params)
    },
    /**
     * 获取结算单位和预算部门
     */
    getBrainBudgetKeyList: (params?: deptParmasVo): Promise<Array<deptLabelVo>> => {
        return get('/data/api/brain/manager-attach/getBrainBudgetKeyList',params)
    },
    /**
     * 申请指标接口
     */
    postPermissionApprove: (params: any): Promise<Result> => {
        return post('data/api/datart/permission-approve/create', params, { 'content-type': 'multipart/form-data' })
    },
    /**
     * 获取指标管理列表
     */
    getKnowCenterList: (params: IKnowCenterVo): Promise<Result> => {
        return get('data/api/brain/echarts-manager/getKnowCenterList', params)
    },
    /**
     * 根据指标id获取详情
     */
    getBrainReportIndicatorById: (params: IKnowCenterVo): Promise<Result> => {
        return get('data/api/brain/echarts-manager/getBrainReportIndicatorById', params)
    },
    /**
     * 可信度评分
     */
    createOrUpdateCredibility: (params: any): Promise<Result> => {
        return post('data/api/brain/echarts-cred/createOrUpdateCredibility', params)
    },
    /**
     * 根据指标id查询可信度评分
     */
    getCredibilityByEchartsId: (params: IKnowCenterVo): Promise<Result> => {
        return get('data/api/brain/echarts-cred/getCredibilityByEchartsId', params)
    },
    /**
     * 知识中心获取分类下拉
     */
    getCategoryList: (params?: IKnowSelectParmasVo): Promise<Array<IKnowSelectResVo>> => {
        return get('/data/api/brain/manager-attach/getCategoryList',params)
    },

    get: (id: number): Promise<ISmartBrain> => {
        return get('data/api/smartBrain/get', {
            id
        })
    },

    save: (params: IndicatorData): Promise<Result> => {
        return post('/data/api/brain/echarts-manager/createOrUpdateIndicator', params)
    },

    edit: (params: IndicatorData): Promise<Result> => {
        return post('/data/api/brain/echarts-manager/createOrUpdateIndicator', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('/data/api/brain/echarts-cred/deleteIndicatorEchartsByIds', { id })
    },
    /**
     * 智慧大脑echart获取数据接口
     */
    queryCommonData: (params: FetchBrainDataParams): Promise<Result> => {
        return post("data/api/bi/brain/data", {
            ...params,
        });
    }
}

export const knowCenterApi = {
    /**
     * 获取知识中心管理列表
     */
    getKnowCenterList: (params: IKnowCenterVo): Promise<Result> => {
        return get('data/api/brain/echarts-manager/getKnowCenterList', params)
    },
    /**
     * 知识中心获取分类下拉
     */
    getCategoryList: (params?: IKnowSelectParmasVo): Promise<Array<IKnowSelectResVo>> => {
        return get('/data/api/brain/manager-attach/getCategoryList',params)
    },

    get: (id: number): Promise<ISmartBrain> => {
        return get('data/api/smartBrain/get', {
            id
        })
    },

    save: (params: IndicatorData): Promise<Result> => {
        return post('/data/api/brain/echarts-manager/createOrUpdateKnowCenter', params)
    },

    edit: (params: IndicatorData): Promise<Result> => {
        return post('/data/api/brain/echarts-manager/createOrUpdateKnowCenter', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('data/api/brain/echarts-manager/deleteKnowCenterByIds', { id })
    },
}

// 预警管理接口
export const warningApi = {
    /**
     * 预警管理列表
     */
    list: (params: any): Promise<Result> => {
        return get('data/api/early/warning/page', params)
    },
    /**
     * 发送预警通知
     */
    send: (params: any): Promise<Result> => {
        return get('data/api/early/warning/onceAgainSend', params)
    },
    /**
     * 获取预警类型
     */
    getEarlyWarningTypeList: (params: any): Promise<Result> => {
        return get('data/api/early/warning/getEarlyWarningTypeList', params)
    },
    /**
     * 导出预警通知
     */
    exportList: (params: any): Promise<Result> => {
        return download('data/api/early/warning/exportEarlyWarningRecord', params)
    }
}

// 预警统计接口
export const warningStatisticsApi = {
    /**
     * 预警管理列表
     */
    list: (params: any): Promise<Result> => {
        return get('data/api/early/warning/getEarlyWarningStatisticsPage', params)
    },
    /**
     * 获取预警类型
     */
    getEarlyWarningTypeList: (params: any): Promise<Result> => {
        return get('data/api/early/warning/getEarlyWarningTypeList', params)
    },
    /**
     * 导出预警通知
     */
    exportList: (params: any): Promise<Result> => {
        return download('data/api/early/warning/exportEarlyWarningStatistics', params)
    }
}

// 领域管理
export const domainApi = {
    
    /**
     * 获取领域管理列表
     */
    list: (params: any): Promise<Result> => {
        return get('data/api/area/page', params)
    },
   
    remove: (id: number): Promise<Result> => {
        return post('data/api/area/delete', {
            id
       })
    },
    getEarlyWarningTypeList: (id: number): Promise<Result> => {
        return get('data/api/area/getAreaList', {
            id
       })
    },

    
    save: (params: any): Promise<Result> => {
        return post('data/api/area/saveOrUpdate', params)
    },
    edit: (params: any): Promise<Result> => {
        return post('data/api/area/saveOrUpdate', params)
    },
}