<script setup lang="ts">
// 基础需求
import {
  Checkbox as hCheckbox,
  CheckboxGroup as hCheckboxGroup,
  Form as hForm,
  FormItem as hFormItem,
  Select as hSelect,
  SelectOption as hSelectOption,
  Input as hInput,
  InputNumber as hInputNumber,
  DatePicker as hDatePicker,
  RangePicker as hRangePicker,
  Textarea as hTextarea,
  Button as hButton,
  Row as hRow,
  Col as hCol,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  Table as hTable,
  Modal as hModal,
  Tooltip as hTooltip,
  Spin as hSpin,
  message,
} from 'ant-design-vue';
import { onMounted, ref, reactive, watch, defineProps, defineEmits, toRaw, defineExpose } from 'vue';
import userSelect from '@haierbusiness-front/components/user/UserSelect.vue';
import DemandHotel from './DemandHotel.vue';

import {
  IUserListRequest,
  IUserInfo,
  DemandSubmitObj,
  HotelsArr,
  MiceTypeConstant,
  DistrictTypeConstant,
  hotelLevelConstant,
  hotelLevelAllConstant,
} from '@haierbusiness-front/common-libs';

const props = defineProps({
  cacheStr: {
    type: String,
    default: '',
  },
  isQingdao: {
    type: String,
    default: '',
  },
  isHotelDemandSubmittable: {
    // 是否可提报多酒店需求
    type: Boolean,
    default: false,
  },
  supportInternational: {
    // 是否支持国际会议提报
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['demandBaseFunc', 'demandHotelFunc']);

const demandBaseFormRef = ref();

// 基础需求表单
const formState = reactive<DemandSubmitObj>({
  miceName: '',
  miceType: null,
  personTotal: null,
  contactUserName: '', // 会议对接人姓名
  contactUserCode: '', // 会议对接人工号
  contactUserPhone: '', // 会议对接人手机号
  contactUserEmail: '', // 会议对接人邮箱

  districtType: 0, // 会议地点类型
  remarks: '', // 备注
});

const tableHead = ['酒店需求', '酒店位置', '酒店星级', '操作'];
const cityShow = ref<Boolean>(false);
const hotelsIndex = ref<Number>(null);
const hotelParams = ref<HotelsArr>({
  // 酒店需求 - 标准
  tempDemandHotelId: null,

  provinceName: '',
  provinceId: '',
  cityName: '', // 酒店所在城市名称
  cityId: null, // 酒店所在城市id
  districtNames: '', // 酒店所在区域名称,支持多区域,逗号分割
  districtIds: '', // 酒店所在区域id,支持多区域,逗号分割

  centerMarker: '', // 需求中心的地标名称
  latitude: '', // 需求中心点经度
  longitude: '', // 需求中心点纬度
  distanceRange: '', // 需求范围:单位米(可选)

  level: null, // 酒店等级
});

const state = reactive<{ HotelsArr: Array }>({
  hotels: [],
});

// 校验
const rules = {
  miceName: [
    { required: true, message: '请填写会议名称', trigger: 'change' },
    { min: 1, max: 50, message: '长度不超过50个字符', trigger: 'blur' },
  ],
  miceType: [{ required: true, message: '请选择会议类型', trigger: 'change' }],
  personTotal: [{ required: true, message: '请填写会议人数', trigger: 'change' }],
  contactUserName: [{ required: true, message: '请选择会议对接人', trigger: 'change' }],
};

// 会议类型
const changeMeetingType = () => {
  // if (formState.districtType == 1) {
  hotelsIndex.value = 0;

  state.hotels = [];

  // // 国际会议
  // state.hotels = [
  //   {
  //     ...hotelParams.value,
  //     key: Date.now(),
  //     tempDemandHotelId: Date.now(),
  //     // 错误提示
  //     errTipShow: false,
  //   },
  // ];
  // }
};

// 锚点 - 用户点击添加需求，需跳转至添加位置
const anchorId = (id: string) => {
  document?.getElementById(id)?.scrollIntoView({
    behavior: 'smooth', //smooth:平滑，auto：直接定位
    block: 'center',
    inline: 'start',
  });
};

// 提交
const onSubmit = async () => {
  let isVerifyPassed = false;

  await demandBaseFormRef.value
    .validate()
    .then(() => {
      state.hotels.forEach((e, i) => {
        e.errTipShow = !e.centerMarker || !e.level;
      });

      // 判断酒店需求是否选择
      const verifyHotels = state.hotels.every((e) => e.centerMarker);

      emit('demandBaseFunc', { ...formState });

      isVerifyPassed = verifyHotels;
    })
    .catch((err) => {
      isVerifyPassed = false;
    });

  if (!isVerifyPassed) {
    anchorId('demandBaseId');
  }

  return isVerifyPassed;
};

// 酒店需求 - 删除
const removeHotel = (item) => {
  const index = state.hotels.indexOf(item);
  if (index !== -1) {
    state.hotels.splice(index, 1);
  }

  emit('demandHotelFunc', [...state.hotels]);
};
// 酒店需求 - 添加
const addHotel = () => {
  state.hotels.push({
    ...hotelParams.value,
    key: Date.now(),
    tempDemandHotelId: Date.now(),
    // 错误提示
    errTipShow: false,
  });
};

// 会议对接人
// 用户选择
const userSelectParams = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});
// 用户选择
const userNameChange = (userInfo: Array<IUserInfo>) => {
  formState.contactUserName = userInfo?.nickName;
  formState.contactUserCode = userInfo?.username;
  formState.contactUserPhone = userInfo?.phone;
  formState.contactUserEmail = userInfo?.email;
};
const blurMeetingNum = () => {
  emit('demandBaseFunc', { ...formState });
};

// 酒店位置
const chooseLocation = (index: Number) => {
  hotelsIndex.value = index;

  cityShow.value = true;
};

// 酒店位置选择
const cityChooseBack = (cityData) => {
  console.log('%c [ 酒店位置选择 ]-213', 'font-size:13px; background:pink; color:#bf2c9f;', cityData);
  state.hotels[hotelsIndex.value] = { ...state.hotels[hotelsIndex.value], ...cityData };

  emit('demandHotelFunc', [...state.hotels]);
};

const closeModal = () => {
  cityShow.value = false;
};

// 暂存
const tempSave = () => {
  emit('demandBaseFunc', { ...formState });
  emit('demandHotelFunc', [...state.hotels]);
};

defineExpose({ onSubmit, tempSave });

onMounted(async () => {
  state.hotels = [
    {
      ...hotelParams.value,
      key: Date.now(),
      tempDemandHotelId: Date.now(),
      // 错误提示
      errTipShow: false,
    },
  ];

  // 反显
  if (props.cacheStr) {
    const cacheObj = JSON.parse(props.cacheStr);
    formState.miceName = cacheObj.miceName || '';
    formState.miceType = cacheObj.miceType || null;
    formState.personTotal = cacheObj.personTotal || null;

    formState.contactUserName = cacheObj.contactUserName || cacheObj.operatorName || ''; // 会议对接人姓名
    formState.contactUserCode = cacheObj.contactUserCode || cacheObj.operatorCode || ''; // 会议对接人工号
    formState.contactUserPhone = cacheObj.contactUserPhone || cacheObj.operatorPhone || ''; // 会议对接人手机号
    formState.contactUserEmail = cacheObj.contactUserEmail || cacheObj.operatorEmail || ''; // 会议对接人邮箱

    formState.districtType = props.isQingdao === '1' || !props.supportInternational ? 0 : cacheObj.districtType || 0; // 会议地点类型
    formState.remarks = cacheObj.remarks || ''; // 备注

    // 酒店列表反显
    state.hotels = [...cacheObj.hotels];
    state.hotels.forEach((e, index) => {
      e.tempDemandHotelId = e.tempDemandHotelId ? e.tempDemandHotelId : Date.now() + index;
    });

    emit('demandHotelFunc', [...state.hotels]);
  }
});
</script>

<template>
  <!-- 基础需求 -->
  <div class="demand_base demand_pad24">
    <div class="demand_title">
      <div class="demand_border"></div>
      <span>基础需求</span>
    </div>

    <h-form class="mt24" ref="demandBaseFormRef" :model="formState" :rules="rules" layout="vertical" hideRequiredMark>
      <h-row :gutter="12" id="demandBaseId">
        <h-col :span="6">
          <h-form-item label="会议名称：" name="miceName">
            <h-input v-model:value="formState.miceName" placeholder="请填写会议名称" :maxlength="50" allow-clear />
          </h-form-item>
        </h-col>
        <h-col :span="6">
          <h-form-item label="会议类型：" name="miceType">
            <h-select v-model:value="formState.miceType" placeholder="请选择会议类型" allow-clear>
              <h-select-option v-for="item in MiceTypeConstant.toArray()" :key="item.code" :value="item.code">
                {{ item.desc }}
              </h-select-option>
            </h-select>
          </h-form-item>
        </h-col>
        <h-col :span="6">
          <h-form-item label="会议对接人：" required name="contactUserName">
            <user-select
              :value="formState.contactUserName"
              :params="userSelectParams"
              placeholder="请选择会议对接人"
              @change="(userInfo: IUserInfo) => userNameChange(userInfo)"
            />
          </h-form-item>
        </h-col>
        <h-col :span="6">
          <h-form-item label="会议人数：" required name="personTotal">
            <h-input-number
              v-model:value="formState.personTotal"
              @blur="blurMeetingNum"
              placeholder="请填写会议人数"
              allow-clear
              :min="1"
              :max="999999"
              style="width: 100%"
            />
          </h-form-item>
        </h-col>
        <h-col :span="24">
          <h-form-item label="" required>
            <h-radio-group
              v-model:value="formState.districtType"
              @change="changeMeetingType"
              :disabled="props.isQingdao === '1' || !props.supportInternational"
            >
              <h-radio v-for="item in DistrictTypeConstant.toArray()" :key="item.code" :value="item.code">
                {{ item.desc + '会议' }}
              </h-radio>
            </h-radio-group>
          </h-form-item>
        </h-col>

        <h-col :span="24">
          <div class="demand_hotel">
            <div class="demand_hotel_text">酒店需求：</div>
            <div>
              <div class="hotel_head">
                <div
                  v-for="(headName, headIndex) in tableHead"
                  :key="headIndex"
                  :class="['table_width' + (headIndex + 1), 'pad012']"
                >
                  {{ headName }}
                </div>
              </div>
              <div v-for="(hotelItem, index) in state.hotels" :key="hotelItem.key" class="hotel_content">
                <div class="table_width1 pad012">
                  {{ '酒店' + (index + 1) }}
                </div>
                <div class="table_width2" @click="chooseLocation(index)">
                  <!-- <h-form-item
                    :name="['hotels', index, 'cityName']"
                    :rules="{
                      required: false,
                      message: '请选择酒店位置' + (index + 1),
                      trigger: 'change',
                    }"
                  > -->
                  <div class="hotel_mask">
                    <!-- <h-input
                        v-model:value="hotelItem.cityName"
                        placeholder=""
                        readonly
                        allow-clear
                        :bordered="false"
                      /> -->
                    <!-- 酒店位置 - 显示内容 -->
                    <div v-show="hotelItem.centerMarker" class="hotel_value text-ellipsis">
                      <h-tooltip placement="topLeft" :title="hotelItem.centerMarker">
                        {{ hotelItem.centerMarker }}
                      </h-tooltip>
                    </div>
                    <span
                      v-show="!hotelItem.centerMarker"
                      :class="['hotel_placeholder', hotelItem.errTipShow ? 'err_color' : '']"
                    >
                      {{ '请选择酒店位置' + (index + 1) }}
                    </span>
                  </div>
                  <!-- </h-form-item> -->
                </div>
                <div class="table_width3" @click="chooseLocation(index)">
                  <!-- <h-form-item
                    label=""
                    :name="['hotels', index, 'level']"
                    :rules="{
                      required: false,
                      message: '请选择酒店星级' + (index + 1),
                      trigger: 'change',
                    }"
                  > -->
                  <div class="hotel_mask">
                    <!-- <h-select
                        v-model:value="hotelItem.level"
                        :placeholder="'请选择酒店星级' + (index + 1)"
                        allow-clear
                        :bordered="false"
                        style="width: 100%"
                      >
                        <h-select-option v-for="item in hotelLevelConstant.toArray()" :key="item.code" :value="item.code">
                          {{ item.desc }}
                        </h-select-option>
                      </h-select> -->
                    <!-- 酒店星级 - 显示内容 -->
                    <div v-show="hotelItem.level" class="hotel_value text-ellipsis">
                      {{ hotelLevelAllConstant.ofType(hotelItem.level)?.desc }}
                    </div>
                    <span
                      v-show="!hotelItem.level"
                      :class="['hotel_placeholder', hotelItem.errTipShow ? 'err_color' : '']"
                    >
                      {{ '请选择酒店星级' + (index + 1) }}
                    </span>
                  </div>
                  <!-- </h-form-item> -->
                </div>
                <div class="table_width4 pad012">
                  <div class="demand_edit mr5 mt1" @click="chooseLocation(index)"></div>
                  <a-popconfirm
                    v-if="state.hotels.length > 1"
                    :title="'确认删除酒店' + (index + 1) + '？'"
                    ok-text="确认"
                    cancel-text="取消"
                    @confirm="removeHotel(hotelItem)"
                  >
                    <div class="demand_del"></div>
                  </a-popconfirm>
                  <div v-else class="demand_del_disabled"></div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="demand_add mt16 ml85"
            v-if="props.isHotelDemandSubmittable || (!props.isHotelDemandSubmittable && state.hotels.length === 0)"
            @click="addHotel"
          >
            <div class="demand_add_img mr8"></div>
            <span>
              {{ state.hotels.length > 0 ? '添加需求（多酒店）' : '添加需求' }}
            </span>
          </div>
        </h-col>
        <h-col :span="24" class="mt42">
          <h-form-item label="" name="remarks">
            <div class="demand_remarks">
              <span class="demand_remarks_text">备注(选填)：</span>
              <h-textarea
                v-model:value="formState.remarks"
                class="demand_textarea"
                :autoSize="{ minRows: 3, maxRows: 3 }"
                allow-clear
                :maxlength="500"
              />
            </div>
          </h-form-item>
        </h-col>
      </h-row>
    </h-form>

    <h-modal v-model:open="cityShow" title="酒店需求选择" width="800px">
      <demand-hotel
        v-if="cityShow"
        :isQingdao="props.isQingdao"
        :hotel-data="state.hotels[hotelsIndex]"
        :meetingType="formState.districtType"
        @cityChooseBack="cityChooseBack"
        @closeModal="closeModal"
      />

      <template #footer></template>
    </h-modal>
  </div>
</template>

<style scoped lang="less">
.demand_base {
  /* 单选框 */
  :deep(.ant-radio-wrapper) {
    margin-inline-end: 24px;
  }

  .demand_hotel {
    display: flex;

    .demand_hotel_text {
      height: 100%;
      vertical-align: top;
      font-size: 14px;
      color: #1d2129;
      line-height: 22px;
    }

    .hotel_head,
    .hotel_content {
      display: flex;
      background: #f2f3f5;
      height: 32px;
      line-height: 32px;
      border-bottom: 1px solid #e5e6eb;

      .table_width1,
      .table_width2,
      .table_width3,
      .table_width4 {
        width: 300px;
      }
      .table_width1 {
        width: 124px;
      }

      .table_width4 {
        width: 82px;
        display: flex;
        align-items: center;
      }
      .pad012 {
        padding: 0 12px;
      }

      .hotel_mask {
        width: 100%;
        height: 100%;

        position: relative;
        cursor: pointer;

        .hotel_value,
        .hotel_placeholder {
          position: absolute;
          left: 12px;
          right: 0;
          top: 0;
          width: 100%;
          height: 100%;
          background: #fff;
        }
        .hotel_placeholder {
          color: rgba(0, 0, 0, 0.25);
        }

        .err_color {
          color: #ff4d4f;
        }
      }

      .demand_del,
      .demand_del_disabled {
        cursor: pointer;
        width: 16px;
        height: 16px;
        background: url('@/assets/image/demand/demand_del.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .demand_edit {
        cursor: pointer;
        width: 16px;
        height: 16px;
        background: url('@/assets/image/demand/demand_edit.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .demand_del {
        &:hover {
          width: 16px;
          height: 16px;
          background: url('@/assets/image/demand/demand_del_red.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }
      }
      .demand_del_disabled {
        cursor: not-allowed;
      }
    }

    .hotel_content {
      height: 40px;
      line-height: 40px;
      background: #fff;

      /* input */
      :deep(.ant-form-item .ant-form-item-control-input) {
        /* padding: 0 12px;
        height: 39px; */
        line-height: 39px;
      }
      /* 下拉框 */
      :deep(.ant-select-single:not(.ant-select-customize-input) .ant-select-selector) {
        padding-left: 11px;
        padding-top: 4px;
        height: 39px;
      }

      :deep(.ant-form-item-explain-error) {
        /* 错误提示 */
        padding-left: 12px;
      }
    }

    .height64 {
      height: 64px;
    }
  }

  .demand_add {
    width: 150px;
    display: flex;
    align-items: center;
    cursor: pointer;

    color: #1868db;
    line-height: 20px;

    &:hover {
      text-decoration: underline;
    }

    .demand_add_img {
      width: 16px;
      height: 16px;
    // background: url('@/assets/image/demand/demand_add.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }

  .demand_remarks {
    height: 72px;

    .demand_remarks_text {
      vertical-align: top;
      font-size: 14px;
      color: #1d2129;
      line-height: 22px;
    }

    .demand_textarea {
      width: 838px;
      height: 100%;
    }
  }
}
</style>
