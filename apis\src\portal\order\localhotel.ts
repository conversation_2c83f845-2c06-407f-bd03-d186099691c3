import {
    LocalrestFilter, LocalroomType, OrderListResult
} from '@haierbusiness-front/common-libs'
import { get, originalPost } from '../../request'

export const localhotelApi = {
    list: (params: LocalrestFilter): Promise<OrderListResult<LocalroomType>> => {
        return originalPost(`/businesstravel/api/localhotel/v1/room/hotelOrder/list.json?pageNum=${params.pageNum}&pageSize=${params.pageSize}`, params)
    }
}