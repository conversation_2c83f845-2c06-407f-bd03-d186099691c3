import { loadEnv } from 'vite'

import vue from '@vitejs/plugin-vue'
import path from 'path';

const CWD = process.cwd();

export default ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);

  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
    },
    plugins: [vue()],
    build: {
      target: ['es2015']
    },
    server: {
      host: '0.0.0.0',
      port: 5175,
      proxy: {
        '/hb/waterworks/api': {
          target: 'https://businessmanagement-test.haier.net/hbweb/waterworksman/hb/waterworks/api/',
          // target: 'http://tstyyczy.w1.luyouxia.net',
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`/hb/waterworks/api`), ''),
        },
        "/hb": {
          target: "https://businessmanagement-test.haier.net/hbweb/waterworksman/hb",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb/, ""),
        },
      },
    }
  }
}
