import axios from 'axios'
import type { CancelToken } from 'axios'
import qs from 'qs'
import { message } from 'ant-design-vue';
import { HeaderConstant } from '@haierbusiness-front/common-libs';
import { errorModal, isMobile, loadDataFromLocal, removeStorageItem, saveDataToLocal } from '@haierbusiness-front/utils';
import { showFailToast } from 'vant';
import 'vant/es/toast/style'

import { storeToRefs } from 'pinia';
import globalPinia from "@haierbusiness-front/utils/src/store/store"

import { applicationStore } from "@haierbusiness-front/utils/src/store/applicaiton";

const store = applicationStore(globalPinia)
const { loginUrl } = storeToRefs(store)

axios.defaults.baseURL = 'hb'

export const download = <T>(url: string, params?: object, headers?: object, errorNotify?: ((error: any) => any) | null): Promise<T> => {
    return axios.get(
        url,
        {
            responseType: 'blob',
            headers: headers,
            params: params,
            paramsSerializer: {
                serialize: (params) => {
                    return qs.stringify(params, { arrayFormat: 'repeat' })
                }
            }
        }
    ).then(res => {

        ajaxDownloadFile(res)
    }).catch(err => {
        if (errorNotify) {
            return errorNotify(err)
        } else {
            errorHttpMessageNotify(err)
            return errorHttpMessageHandle(err)
        }
    })
}

export const downloadPost = <T>(url: string, params?: object, headers?: object, errorNotify?: ((error: any) => any) | null): Promise<T> => {
    return axios.post(url, params, { responseType: 'blob', headers: headers })
        .then(res => {
            ajaxDownloadFile(res)
        }).catch(err => {
            if (errorNotify) {
                return errorNotify(err)
            } else {
                errorHttpMessageNotify(err)
                return errorHttpMessageHandle(err)
            }
        })
}

export const ajaxDownloadFile = (res: any) => {

    const contentType = res.headers["content-type"];
    const blob = new Blob([res.data], { type: contentType })
    const downloadElement = document.createElement('a');
    const href = window.URL.createObjectURL(blob); //创建下载的链接
    downloadElement.href = href;

    const contentDisposition = res.headers["content-disposition"];

    let fileName = "download_Nan";
    if (contentDisposition) {
        const filenameReg = /filename\*=utf-8''(\S+)/;
        const match = contentDisposition.match(filenameReg);
        if (match) {
            fileName = decodeURI(match["1"]);
        }
        downloadElement.download = fileName; //下载后文件名
        document.body.appendChild(downloadElement);
        console.log(downloadElement)
        downloadElement.click(); //点击下载
        document.body.removeChild(downloadElement); //下载完成移除元素
        window.URL.revokeObjectURL(href); //释放掉blob对象
    }
}

export const get = <T>(url: string, params?: object, headers?: object, errorNotify?: ((error: any) => any) | null, original?: boolean | false, cancelToken?: CancelToken): Promise<T> => {
    return axios.get(
        url,
        {
            cancelToken,
            headers: headers,
            params: params,
            paramsSerializer: {
                serialize: (params) => {
                    return qs.stringify(params, { arrayFormat: 'repeat' })
                }
            }
        }
    ).then(res => {
        if (original) {
            return res
        } else {
            return res.data
        }
    }).catch(err => {
        if (errorNotify) {
            return errorNotify(err)
        } else {
            errorHttpMessageNotify(err)
            return errorHttpMessageHandle(err)
        }

    })
}

export const originalGet = (url: string, params?: object, headers?: object, errorNotify?: ((error: any) => any) | null) => {
    return get(url, params, headers, errorNotify, true)
}

export const post = (url: string, params?: object, headers?: object, errorNotify?: ((error: any) => any) | null, original?: boolean | false, cancelToken?: CancelToken) => {
    return axios.post(url, params, { headers: headers, cancelToken })
        .then(res => {
            if (original) {
                return res
            } else {
                return res.data
            }
        })
        .catch(err => {
            if (errorNotify) {
                return errorNotify(err)
            } else {
                errorHttpMessageNotify(err)
                return errorHttpMessageHandle(err)
            }
        })
}

export const originalPost = (url: string, params?: object, headers?: object, errorNotify?: ((error: any) => any) | null) => {
    return post(url, params, headers, errorNotify, true)
}

/** 请求过滤*/
axios.interceptors.request.use(
    config => {
        config.headers[HeaderConstant.TOKEN_KEY.key] = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false);
        return config
    },
    error => {
        errorHttpMessageNotify(error);
        return Promise.reject(error)
    }
)

/** 响应过滤 */
axios.interceptors.response.use(
    response => {
        const token = response.headers[HeaderConstant.TOKEN_KEY.key];
        if (token) {
            saveDataToLocal(HeaderConstant.TOKEN_KEY.key, token, false, 1000 * 60 * 60 * 24)
        }

        // 跳过流响应处理
        if (response.headers["content-type"] === "text/event-stream") {
            return response;
        }

        let data = response.data
        if (data instanceof Blob) {
            const contentType = response.headers["content-type"];
            // 判断请求头
            if (contentType === "application/json") {
                return data.text().then(function (text) {
                    data = JSON.parse(text)
                    if (!data.success) {
                        return Promise.reject(data);
                    } else {
                        return Promise.resolve(response);
                    }
                });
            } else {
                return Promise.resolve(response);
            }
        } else if (data instanceof ArrayBuffer) {
            const contentType = response.headers["content-type"];
            // 判断请求头
            if (contentType === "application/json") {

            }
            return Promise.resolve(response);
        } else if (typeof response.data === 'string') {
            data = JSON.parse(data)
            if (!data.success) {
                return Promise.reject(data);
            } else {
                return Promise.resolve(data);
            }
        } else {
            if (!data.success) {
                return Promise.reject(data);
            } else {
                return Promise.resolve(data);
            }
        }
    },
    error => {
        return Promise.reject(error);
    }
)

/** 统一提示,屏蔽对用户的不友好提示 */
export const errorHttpMessageNotify = (error: any) => {
    if (error.response) {
        let errorMessage: string = "";
        const httpStatus = error.response.status;
        if (httpStatus === 401) {
            errorMessage = '当前未登录,拒绝访问!' + error.response.data.message
            if (isMobile()) {
                showFailToast(errorMessage)
            } else {
                errorModal(errorMessage)
            }
        } else if (httpStatus === 403) {
            errorMessage = (error.response.data.message ?? '') + "请尝试重新登录！"
            if (isMobile()) {
                showFailToast(errorMessage)
            } else {
                errorModal(errorMessage)
            }
        } else if (httpStatus === 503) {
            errorMessage = "服务无效！"
            if (isMobile()) {
                showFailToast(errorMessage)
            } else {
                errorModal(errorMessage)
            }
        } else {
            errorMessage = error.response.data.message;
            if (isMobile()) {
                showFailToast(errorMessage ?? '请求异常！')
            } else {
                errorModal(errorMessage ?? '请求异常！')
            }
        }
    } else {
        if (isMobile()) {
            showFailToast(error.message ?? '请求异常')
        } else {
            errorModal(error.message ?? '请求异常')
        }
    }
}

/** 统一处理产生错误后的行为 */
export const errorHttpMessageHandle = (error: any) => {
    if (error.response) {
        const httpStatus = error.response.status;
        if (httpStatus === 401) {
            removeStorageItem(HeaderConstant.TOKEN_KEY.key, false)
            if (loginUrl.value) {
                window.location.href = loginUrl.value + '&redirect_url=' + encodeURIComponent(window.location.href)
            } else {
                message.error("没有权限访问！获取登录路径失败！")
            }
        }
    }

    return Promise.reject(error);
}