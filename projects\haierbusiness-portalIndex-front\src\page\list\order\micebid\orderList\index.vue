<script lang="ts" setup>
// 用户端-订单列表
import OrderList from '@haierbusiness-front/components/mice/orderList/index.vue';
</script>
<template>
  <div class="mice-bid-order-list">
    <OrderList type="user" />
  </div>
</template>
<style lang="less" scoped>
.mice-bid-order-list {
  width: 100%;
  overflow: auto;
}
.container {
  min-height: none;
  background: #fff;
}
:deep(.order-list .order-item) {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
  border: 1px solid #f5f5f5 !important;
}
:deep(.order-list) {
  margin-top: 0px;
}
:deep(.top-section) {
  position: relative;
}
</style>
