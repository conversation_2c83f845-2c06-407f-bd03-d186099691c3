import { download, get, post } from '../request'
import { 
    IFinancialReceiptsFilter, 
    IFinancialReceipts,
    IPageResponse, 
    Result } from '@haierbusiness-front/common-libs'


export const financialReceiptsApi = {
    list: (params: IFinancialReceiptsFilter): Promise<IPageResponse<IFinancialReceipts>> => {
        return get('merchant/api/financialReceipts/list', params)
    },

    get: (id: number): Promise<IFinancialReceipts> => {
        return get('merchant/api/financialReceipts/get', {
            id
        })
    },

    save: (params: IFinancialReceipts): Promise<Result> => {
        return post('merchant/api/financialReceipts/save', params)
    },

    edit: (params: IFinancialReceipts): Promise<Result> => {
        return post('merchant/api/financialReceipts/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('merchant/api/financialReceipts/delete', { id })
    },
}
