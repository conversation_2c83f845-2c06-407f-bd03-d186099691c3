import { get, post } from '../request'
import {
  Result,
  IHelperReq,
  IHelperSearchParam,
  UserTicketResponseDTO
} from '@haierbusiness-front/common-libs'
export const helperApi = {

  // 创建捎带请求
  create: (params: IHelperReq): Promise<Result> => {
    return post('/hb-helper/api/piggybackRequirement/create', params)
  },
  update: (params: IHelperReq): Promise<Result> => {
    return post('/hb-helper/api/piggybackRequirement/update', params)
  },

  // 联系接受人
  contactAcceptor: (id:number): Promise<Result> => {
    return get(`/hb-helper/api/piggybackRequirement/contactAcceptor/${id}`)
  },
  
  // 联系咨询人
  getContactsOpenid: (id:number): Promise<Result> => {
    return get(`/hb-helper/api/piggybackAcceptRecord/getContactsOpenid/${id}`)
  },
  
  // 取消请求
  cancelAccept: (id: any): Promise<Result> => {
    return post(`/hb-helper/api/piggybackRequirement/cancel/${id}`)
  },

  // 接受请求
  acceptRequirement: (params: any): Promise<Result> => {
    return post('/hb-helper/api/piggybackRequirement/acceptRequirement', params)
  },

  // 查询列表
  page: (params: IHelperSearchParam): Promise<Result> => {
    return post('/hb-helper/api/piggybackRequirement/page', params)
  },

  // 查询详情
  getById: (id: string): Promise<Result> => {
    return get(`/hb-helper/api/piggybackRequirement/getById/${id}`)
  },
  // 根据id删除
  deleteById: (id: string): Promise<Result> => {
    return post(`/hb-helper/api/piggybackRequirement/deleteById/${id}`)
  },
  
  // 删除沟通记录
  deleteRecord: (params: any): Promise<Result> => {
    return post(`/hb-helper/api/piggybackAcceptRecord/deleteAcceptedRecord`,params)
  },

  // 查询行程
  userTickets: (): Promise<UserTicketResponseDTO> => {
    return get(`/hb-helper/api/userTickets/lsit`)
  },
}