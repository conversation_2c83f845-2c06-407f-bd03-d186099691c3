<template>
  <div class="order-log">
    <van-nav-bar :fixed="true" title="订单操作日志" left-arrow>
      <template #left>
        <van-icon name="arrow-left" color="#000" @click="goBack" />
      </template>
    </van-nav-bar>

    <van-cell-group inset class="mb-10" v-for="log, index in logs" :key="index">
      <van-cell title="步骤" :value="ROrderStateEnum[log.step]"></van-cell>
      <van-cell title="状态" :value="log.state == 1 ? '成功' :'失败'"></van-cell>
      <van-cell title="操作时间" :value="log.gmtCreate"></van-cell>
      <van-cell title="创建人" :value="log.createBy"></van-cell>
      <van-cell title="业务类型" value="青岛订餐"></van-cell>
      <van-cell title="内容" class="short-cell"  :value="log.content"></van-cell>

    </van-cell-group>


  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import type { Ref } from 'vue';

import { restaurantApi } from '@haierbusiness-front/apis';

import {
  RHotelParams,
  RpayType,
  ROrderPayMentStateEnum,
  ROrderPayMentStateEnumColor,
  ROrderApprovalStateEnum,
  ROrderApprovalStateEnumColor,
  ROrderStateEnum,
  ROrderStateEnumColor,
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { Item } from 'ant-design-vue/es/menu';
const router = getCurrentRouter();

const route = ref(getCurrentRoute());

const orderCode = route.value?.query?.orderCode;


const logs = ref([])

const getLogs = (orderCode: string) => {
  const params = {
    orderCode,
  };

  restaurantApi.logs(params).then((res) => {
    logs.value = res.data;
  });
};

const goBack = () => {
  router.back(-1);
};

watch(
  () => orderCode,
  (val: string) => {
    getLogs(val);
  },
  {
    immediate: true,
  },
);
</script>

<style lang="less" scoped>
@import url(./common.less);
.order-log {
  background: #eee;
  padding: 46px 0 20px;
  min-height: 100vh;
}
.short-cell {
  :deep(.van-cell__title) {
  flex: none;
    width: 60px;
  }
}
</style>