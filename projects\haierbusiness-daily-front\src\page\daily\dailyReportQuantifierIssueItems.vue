<script setup lang="ts">
import {
  Modal,
  Tag as hTag,
  Form as hForm,
  Input as hInput,
  Popover as hPopover,
  Popconfirm as hPopconfirm,
  FormItem as hFormItem,
  Select as hSelect,
  SelectOption as hSelectOption,
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Spin as hSpin,
  Table as hTable,
  Textarea as hTextarea,
  DatePicker as hDatePicker,
  CollapsePanel as hCollapsePanel,
  Collapse as hCollapse,
  FormInstance,
  message,
  TableColumnsType,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { PlusOutlined, SearchOutlined, ExpandAltOutlined, MinusCircleOutlined } from '@ant-design/icons-vue';
import {
  IAnnualPlanSaveOrUpdateRequestDTO,
  AnnualPlanTypeStateConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
  IHaierAccountBillInfo,
  UserGroupSystemConstant,
  IUserInfo,
} from '@haierbusiness-front/common-libs';
import { checkUserGroup, getCurrentRouter, resolveParam } from '@haierbusiness-front/utils';
import { ref, createVNode, PropType, computed } from 'vue';

import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';
import dayjs, { Dayjs } from 'dayjs';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import {
  EvaluateControlConstant,
  IAnnualPlanDetailRequestDTO,
  IAnnualPlanDetailResponseDTO,
  IAnnualPlanTypeListRequest,
  IDailyPersonalResponse,
  IDailyReportConferenceRequestDTO,
  IDailyReportDetailResponseDTO,
  IDailyReportListResponseDTO,
  IDailyReportProjectRequestDTO,
  IDailyReportQuantifierIssueItemRequestDTO,
  IMonthPlanDetailResponseDTO,
  PlanTypeConstant,
  DailyReportStateConstant,
  QuantifierStateConstant,
} from '@haierbusiness-front/common-libs/src/daily';
import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
const { loginUser } = storeToRefs(applicationStore(globalPinia));

const router = getCurrentRouter();
const prop = defineProps({
  data: Object as PropType<IDailyReportQuantifierIssueItemRequestDTO[]>,
  dailyReport: Object as PropType<IDailyReportListResponseDTO>,
  monthData: Object as PropType<IMonthPlanDetailResponseDTO>,
  fold: Boolean as PropType<Boolean>,
  dailyPerson: Object as PropType<IDailyPersonalResponse[]>,
  reportDetail: Object as PropType<IDailyReportDetailResponseDTO>,
});

const columns: ColumnType[] = [
  {
    title: '量化人',
    dataIndex: 'quantifierUsername',
    width: '140px',
    align: 'center',
    fixed: 'left',
    customCell: (record) => {
      return { rowSpan: record.rowSpan };
    },
  },
  {
    title: '日量化问题',
    dataIndex: 'issue',
    width: '220px',
    align: 'center',
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '关差措施及要求',
    dataIndex: 'dispose',
    width: '220px',
    align: 'center',
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '责任人',
    dataIndex: 'principalUsercode',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '人员状态',
    dataIndex: 'state',
    width: '90px',
    align: 'center',
    ellipsis: true,
    customCell: (record) => {
      return { rowSpan: record.rowSpan };
    },
  },
  {
    title: '关差时间',
    dataIndex: 'dispose_date',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '40px',
    fixed: 'right',
    align: 'center',
  },
];

const tableData = computed(() => {
  const result = prop?.data?.sort((a: any, b: any) => {
    const x = parseInt(a.quantifierUsercode || 0);
    const y = parseInt(b.quantifierUsercode || 0);
    if (x < y) {
      return -1;
    } else if (x == y) {
      return 0;
    } else {
      return 1;
    }
  });

  const completedList: any = [];
  result?.forEach((it) => {
    const alreadyExists = completedList.filter((cit: any) => {
      return it.quantifierUsercode === cit;
    });
    if (!alreadyExists || alreadyExists.length <= 0) {
      completedList.push(it.quantifierUsercode);
      (it as any).rowSpan = 0;
      result?.forEach((rit) => {
        if (rit.quantifierUsercode === it.quantifierUsercode) {
          (it as any).rowSpan += 1;
        }
      });
    } else {
      (it as any).rowSpan = 0;
    }
  });
  return result;
});
const collapseActiveKey = ref([1]);
{
  if (prop?.fold) {
    collapseActiveKey.value = [];
  }
}

const allowRemoveDomain = (item: IDailyReportQuantifierIssueItemRequestDTO) => {
  const result = prop?.data?.filter((it) => {
    return it.quantifierUsercode === item.quantifierUsercode;
  });
  return (result?.length || 0) > 1;
};

const removeDomain = (item: any) => {
  let index = prop?.data?.indexOf(item);
  if (index != undefined && index !== -1) {
    prop?.data?.splice(index, 1);
  }
};

const addDomain = (item: IDailyReportQuantifierIssueItemRequestDTO) => {
  prop?.data?.push({
    drqiId: item.drqiId,
    state: item.state,
    quantifierUsercode: item.quantifierUsercode,
    quantifierUsername: item.quantifierUsername,
    quantifierDeptCode: item.quantifierDeptCode,
    quantifierDeptName: item.quantifierDeptName,
  });
};

const getIndex = (record: any) => {
  return prop.data?.indexOf(record) || 0;
};
const userNameChange = (userInfo: IUserInfo, record: IDailyReportQuantifierIssueItemRequestDTO) => {
  record.principalUsercode = userInfo.username;
  record.principalUsername = userInfo.nickName;
};
const managerUserNameChange = (userInfo: IUserInfo, record: IDailyReportQuantifierIssueItemRequestDTO) => {
  record.managerUsercode = userInfo.username;
  record.managerUsername = userInfo.nickName;
};
const currentData = computed(() => {
  return dayjs();
});
const unsaved = () => {
  const quantifierUsers = prop?.data?.filter((it) => {
    return it.quantifierUsercode === loginUser.value?.username && it.state === QuantifierStateConstant.COMPLETED.code;
  });
  return !quantifierUsers || quantifierUsers?.length <= 0;
};
const allowChange = (record: IDailyReportQuantifierIssueItemRequestDTO) => {
  return (
    checkUserGroup(UserGroupSystemConstant.DAILY_MICRO.groupId) ||
    (unsaved() && record.quantifierUsercode === loginUser.value?.username)
  );
};
</script>

<template>
  <h-collapse
    v-model:activeKey="collapseActiveKey"
    :bordered="false"
    style="background-color: white"
    :collapsible="'icon'"
  >
    <h-collapse-panel key="1">
      <template #header>
        <div style="display: flex">
          <div style="font-size: 16px; font-weight: 600; margin-left: 10px">日量化问题</div>
        </div>
      </template>
      <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%"></div>
      <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px">
        <h-row :gutter="24" style="margin-top: 15px">
          <h-col :span="24">
            <h-table
              style="margin-left: 5px"
              :size="'small'"
              :rowKey="(record) => record.id"
              :bordered="true"
              :columns="columns"
              :data-source="tableData"
              :pagination="false"
            >
              <template #bodyCell="{ record, column, text }">
                <template v-if="['quantifierUsername'].includes(column.dataIndex as any)">
                  <div style="text-align: center;">
                    <div style="float: left; width: 80px; line-height: 30px">{{ record.quantifierUsername }}</div>
                    <div style="float: left; width: 40px">
                      <div>
                        <h-button v-if="allowChange(record)" type="dashed" @click="addDomain(record)">
                          <PlusOutlined />
                        </h-button>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-if="['issue'].includes(column.dataIndex as any)">
                  <div>
                    <h-input :disabled="!allowChange(record)" v-model:value="data!![getIndex(record)].issue" allowClear>
                      <template #addonAfter>
                        <h-popover trigger="click">
                          <template #content>
                            <h-textarea
                              :disabled="!allowChange(record)"
                              v-model:value="data!![getIndex(record)].issue"
                              style="width: 500px"
                              :rows="4"
                              allowClear
                            />
                          </template>
                          <ExpandAltOutlined />
                        </h-popover>
                      </template>
                    </h-input>
                  </div>
                </template>
                <template v-if="['dispose'].includes(column.dataIndex as any)">
                  <div>
                    <h-input
                      :disabled="!allowChange(record)"
                      v-model:value="data!![getIndex(record)].dispose"
                      allowClear
                    >
                      <template #addonAfter>
                        <h-popover trigger="click">
                          <template #content>
                            <h-textarea
                              :disabled="!allowChange(record)"
                              v-model:value="data!![getIndex(record)].dispose"
                              style="width: 500px"
                              :rows="4"
                              allowClear
                            />
                          </template>
                          <ExpandAltOutlined />
                        </h-popover>
                      </template>
                    </h-input>
                  </div>
                </template>
                <template v-if="['principalUsercode'].includes(column.dataIndex as any)">
                  <user-select
                    :disabled="!allowChange(record)"
                    style="width: 100%"
                    :value="data!![getIndex(record)].principalUsername"
                    placeholder="责任人"
                    :params="{
                      pageNum: 1,
                      pageSize: 20,
                    }"
                    @change="(userInfo: IUserInfo) =>  userNameChange(userInfo, data!![getIndex(record)])"
                  ></user-select>
                </template>
                <template v-if="['state'].includes(column.dataIndex as any)">
                  <template
                    v-if="DailyReportStateConstant.WAIT.code !== dailyReport!!.state && data!![getIndex(record)].state === QuantifierStateConstant.WAIT.code"
                  >
                    <h-select
                      v-if="data!![getIndex(record)].state !== QuantifierStateConstant.COMPLETED.code"
                      :disabled="!allowChange(record)"
                      :value="99"
                      style="width: 100%"
                      placeholder="选择状态"
                    >
                      <h-select-option :value="QuantifierStateConstant.WAIT.code">正常 </h-select-option>
                      <h-select-option :value="QuantifierStateConstant.HOLIDAY.code"
                        >{{ QuantifierStateConstant.HOLIDAY.desc }}
                      </h-select-option>
                      <h-select-option :value="QuantifierStateConstant.LEAVE.code"
                        >{{ QuantifierStateConstant.LEAVE.desc }}
                      </h-select-option>
                      <h-select-option :value="QuantifierStateConstant.OTHER.code"
                        >{{ QuantifierStateConstant.OTHER.desc }}
                      </h-select-option>
                      <h-select-option :value="QuantifierStateConstant.NOT_ENTERED.code"   >
                        {{ QuantifierStateConstant.NOT_ENTERED.desc }}
                      </h-select-option>
                    </h-select>
                  </template>
                  <template v-else>
                    <h-select
                      v-if="data!![getIndex(record)].state !== QuantifierStateConstant.COMPLETED.code"
                      :disabled="!allowChange(record)"
                      style="width: 100%"
                      v-model:value="data!![getIndex(record)].state"
                      placeholder="选择状态"
                    >
                      <h-select-option :value="QuantifierStateConstant.WAIT.code">正常 </h-select-option>
                      <h-select-option :value="QuantifierStateConstant.HOLIDAY.code"
                        >{{ QuantifierStateConstant.HOLIDAY.desc }}
                      </h-select-option>
                      <h-select-option :value="QuantifierStateConstant.LEAVE.code"
                        >{{ QuantifierStateConstant.LEAVE.desc }}
                      </h-select-option>
                      <h-select-option :value="QuantifierStateConstant.OTHER.code"
                        >{{ QuantifierStateConstant.OTHER.desc }}
                      </h-select-option>
                      <h-select-option :value="QuantifierStateConstant.NOT_ENTERED.code"
                        >{{ QuantifierStateConstant.NOT_ENTERED.desc }}
                      </h-select-option>
                    </h-select>
                    <h-select
                      v-else
                      :disabled="!allowChange(record)"
                      style="width: 100%"
                      v-model:value="data!![getIndex(record)].state"
                      placeholder="选择状态"
                    >
                      <h-select-option :value="QuantifierStateConstant.WAIT.code">正常 </h-select-option>
                      <h-select-option :value="QuantifierStateConstant.HOLIDAY.code"
                        >{{ QuantifierStateConstant.HOLIDAY.desc }}
                      </h-select-option>
                      <h-select-option :value="QuantifierStateConstant.LEAVE.code"
                        >{{ QuantifierStateConstant.LEAVE.desc }}
                      </h-select-option>
                      <h-select-option :value="QuantifierStateConstant.OTHER.code"
                        >{{ QuantifierStateConstant.OTHER.desc }}
                      </h-select-option>
                      <h-select-option :value="QuantifierStateConstant.COMPLETED.code"
                        >{{ QuantifierStateConstant.COMPLETED.desc }}
                      </h-select-option>
                      <h-select-option :value="QuantifierStateConstant.NOT_ENTERED.code"
                        >{{ QuantifierStateConstant.NOT_ENTERED.desc }}
                      </h-select-option>
                    </h-select>
                  </template>
                </template>
                <template v-if="['dispose_date'].includes(column.dataIndex as any)">
                  <h-date-picker
                    :disabled="!allowChange(record)"
                    :defaultPickerValue="currentData"
                    v-model:value="data!![getIndex(record)].disposeDate"
                    value-format="YYYY-MM-DD HH:mm:ss"
                  />
                </template>
                <template v-if="['_operator'].includes(column.dataIndex as any)">
                  <h-popconfirm
                    title="确认删除?"
                    v-if="allowRemoveDomain(record) && allowChange(record)"
                    @confirm="removeDomain(record)"
                  >
                    <h-button type="link" danger>
                      <template #icon>
                        <MinusCircleOutlined :style="{ fontSize: '20px' }" />
                      </template>
                    </h-button>
                  </h-popconfirm>
                </template>
              </template>
            </h-table>
          </h-col>
        </h-row>
      </div>
    </h-collapse-panel>
  </h-collapse>
</template>

<style scoped lang="less">
@import '../../assets/css/main.less';
</style>
