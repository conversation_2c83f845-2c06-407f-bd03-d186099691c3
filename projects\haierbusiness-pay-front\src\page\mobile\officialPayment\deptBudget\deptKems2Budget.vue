<script setup lang="ts">
import {
  showF<PERSON>Toast,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
  Form as <PERSON><PERSON><PERSON>,
  Field as <PERSON><PERSON><PERSON>,
  CellGroup as VanCellGroup,
  showDialog,
  showSuccessToast,
  NoticeBar,
  Popup as VanPopup,
  showToast,
  showLoadingToast
} from "vant";
import "vant/es/notice-bar/style";
import {
  payApi,
  compositionPayApi,
  virtualPayApi,
  budgetHaierPayApi
} from "@haierbusiness-front/apis";
import {
  IPayData,
  IQueryVirtualAccountsResponse,
  PaySourceConstant,
  HaierBudgetSourceConstant,
  IloginUser,
  IBudgetHaierTypesResponse
} from "@haierbusiness-front/common-libs";
import { computed, PropType, ref } from "vue";
import { removeStorageItem,ITraveler,isMobile } from "@haierbusiness-front/utils";
import { useRequest } from "vue-request";
import userSelectM from "@/components/userSelectM.vue"

interface Props {
  applicationCode: IPayData;
  budgetType:string,
  param:any
}

const props = withDefaults(defineProps<Props>(), {});

// 表单的值 
const budgeterCode = ref();
const budgeterName = ref();

const budgetDepartmentCode = ref();
const budgetDepartmentName = ref();


const companyCode = ref();
const companyName = ref();

const unitCode = ref();
const unitName = ref();

const leftAmt = ref();

const payLoading = ref(false);
const userLoading = ref(false);


const emit = defineEmits(["setIsPayComplete",'payComplete',"isPayLoading"]);

const payComplete = () => {
  emit('payComplete', true);
};
// 费用科目
const {
  data: searchFeeItemsData,
  run: searchFeeItemsRun,
  loading: searchFeeItemsLoading,
} = useRequest(
    budgetHaierPayApi.searchFeeItems, {
      manual: false,
      defaultParams: [
        {
          applicationCode: props?.applicationCode,
          budgetSysCode: HaierBudgetSourceConstant.GEMS.code,
          businessType:props.param?.businessType,
        }
      ]
    }
);
// 费用科目选项
const feeItemOptions = computed(() => {
  if (searchFeeItemsData.value) {
    return searchFeeItemsData.value.map(it => {
      return {"value": it.itemCode, "text": it.itemName}
    })
  } else {
    return []
  }
});

// 费用科目弹窗
const feeItem = ref<any>();
const feeItemName = ref<any>('');
const showPicker = ref<boolean>(false);

// 选择费用科目
const onConfirm = ({ selectedOptions }) => {
    feeItemName.value = selectedOptions[0]?.text
    feeItem.value = selectedOptions[0]?.value
    showPicker.value = false;
    // 如果已经选择了预算人 执行搜索
    if(budgeterCode.value){
      onSearch()
    }
};
// 选择预算人
const selectBudgeter = (item:ITraveler) => {
  console.log(item,"-----------")
  budgeterCode.value = item.username
  budgeterName.value = item.nickName
  budgetDepartmentCode.value = item.departmentCode
  budgetDepartmentName.value = item.departmentName
  onSearch()
};

const onSearch = () => {
  if (!feeItem.value) {
    showToast("请选择费用科目!")
    return;
  }
  if (!budgeterCode.value) {
    showToast("请输入预算人!")
    return;
  }
  const toast = showLoadingToast({
    duration: 0,
    forbidClick: true,
    message: '请求中...',
  });

  userLoading.value = true;
  // 查询前上次结果清空
  companyCode.value = null
  companyName.value = null
  unitCode.value = null
  unitName.value = null
  budgetDepartmentCode.value = null
  budgetDepartmentName.value = null
  leftAmt.value = null
  budgetHaierPayApi.searchBudget({
    businessType: props.param?.businessType,

    haierBudgetType: props.budgetType,
    applicationCode: props?.applicationCode,
    budgeterCode: budgeterCode.value,
    budgetDepartmentCode: budgetDepartmentCode.value,
    feeItem: feeItem.value,
  }).then(it => {
    companyCode.value = it.companyCode
    companyName.value = it.companyName

    unitCode.value = it.unitCode
    unitName.value = it.unitName

    budgetDepartmentCode.value = it.entityCode
    budgetDepartmentName.value = it.entityName

    leftAmt.value = it.leftAmt
    toast.close()
  }).finally(() => {
    userLoading.value = false;
  })
};

// 点击支付
const pay = () => {
  if (!feeItem.value) {
   showFailToast("请选择费用科目!")
    return;
  }
  if (!budgeterCode.value) {
   showFailToast("请输入预算人!")
    return;
  }
  if (!leftAmt.value) {
   showFailToast("获取可用余额失败!")
    return;
  }
  if (leftAmt.value <= 0) {
   showFailToast("可用余额不足!")
    return;
  }
  emit('isPayLoading',true)
  budgetHaierPayApi.occupyBudget(
      {
        haierBudgetType: props.budgetType,
        feeItem: feeItem.value,
        feeItemName: feeItemName.value,
        budgeterCode: budgeterCode.value,
        budgeterName: budgeterName.value,
        budgetDepartmentCode: budgetDepartmentCode.value,
        budgetDepartmentName: budgetDepartmentName.value,
        accountCompanyCode: companyCode.value,
        accountCompanyName: companyName.value,
        unitCode: unitCode.value,
        unitName: unitName.value,

        // - 通用参数
        businessType:props.param?.businessType,
        orderCode: props.param?.orderCode,
        applicationCode: props.param?.applicationCode,
        extJsonParam: props.param?.extJsonParam,
        startApproveFlag:props.param?.startApproveFlag,
        payTypes: props.param?.payTypes,
        username: props.param?.username,
        providerCode: props.param?.providerCode,
        amount: Number(props.param?.amount),
        orderDetailsUrl: props.param?.orderDetailsUrl,
        notifyUrl: props.param?.notifyUrl,
        callbackUrl: props.param?.callbackUrl,
        description: props.param?.description,
        payload: props.param?.payload,
        paySource: isMobile() ? PaySourceConstant.MOBILE.type : PaySourceConstant.PC.type,
        paymentMethod: 2
      },
      {
        applicationCode: props.param?.applicationCode,
        excludes: "paySource,haierBudgetType,feeItem,feeItemName,budgeterCode,budgeterName,budgetDepartmentCode,budgetDepartmentName,accountCompanyCode,accountCompanyName,unitCode,unitName,paymentMethod,startApproveFlag",
        nonce: props.param?.hbNonce,
        timestamp: props.param?.hbTimestamp,
        sign: props.param?.sign,
      }
  ).then(it => {
    payComplete()
    emit('isPayLoading',false)
  }).finally(() => {
    emit('isPayLoading',false)
  })
};

defineExpose({pay})

</script>

<template>
  <div class="contentBox">
    <van-form>
      <van-field
        v-model="feeItemName"
        required
        is-link
        readonly
        input-align="right"
        name="feeItem"
        label="费用科目"
        placeholder="点击选择费用科目"
        @click="showPicker = true"
      />
      <userSelectM label="预算人" :value="budgeterName" @chose="selectBudgeter" />
      <van-field
        v-model="budgetDepartmentName"
        input-align="right"
        :disabled="true"
        name="预算部门"
        label="预算部门"
        placeholder="预算部门"
        :rules="[{ required: true, message: '请填写预算部门' }]"
      />
      <van-field
        v-model="unitName"
        :disabled="true"
        input-align="right"
        name="预算单元"
        label="预算单元"
        placeholder="预算单元"
        :rules="[{ required: true, message: '请填写预算单元' }]"
      />
      <van-field
        v-model="companyName"
        :disabled="true"
        input-align="right"
        name="付款公司"
        label="付款公司"
        placeholder="付款公司"
        :rules="[{ required: true, message: '请填写付款公司' }]"
      />
      <van-field
        v-model="leftAmt"
        input-align="right"
        :disabled="true"
        name="可用余额"
        label="可用余额"
        placeholder="可用余额"
        :rules="[{ required: true, message: '请填写可用余额' }]"
      />
    </van-form>
  </div>
  <!-- 费用科目弹窗  -->
  <van-popup v-model:show="showPicker" position="bottom">
    <van-picker
      :columns="feeItemOptions"
      @confirm="onConfirm"
      @cancel="showPicker = false"
      :columns-field-names="customFieldName"
    />
  </van-popup>
</template>

<style scoped lang="less">
</style>

<style>
:root:root {
  --van-button-primary-background: #0073e5;
  --van-radio-checked-icon-color: #0073e5;
  --van-password-input-background: #f2f2f2;
}
</style>