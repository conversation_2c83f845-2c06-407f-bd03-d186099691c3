import { IPageResponse, MiceBidManNotice } from '@haierbusiness-front/common-libs';
import { get, post } from '../request';

export const miceBidManNoticeApi = {
    getList: (params = {}): Promise<Promise<IPageResponse<MiceBidManNotice>>> => {
        return get('/mice-bid/api/allin/from/page', params);
    },
    getDetail: (params = {}): Promise<MiceBidManNotice> => {
        return get('/mice-bid/api/allin/from/valid', params);
    },
    add: (params = {}) => {
        return post('/mice-bid/api/allin/from/add', params);
    },
    edit: (params = {}) => {
        return post('/mice-bid/api/allin/from/update', params);
    },
    delete: (params = {}) => {
        return post('/mice-bid/api/allin/from/delete', params);
    },
}
