import { get, post } from '../../request'
import {
    Result
} from '@haierbusiness-front/common-libs';
import { getCommonFilter, getCommonFilterSynchronism } from "./index";

const getTypeKey = (url: number | string) => {
    const resultMap: any = {
        "/data/board/booking-hotel": "localHotel",
        "/data/board/ordering-food": "localRestaurant",
        "/data/board/travel/internal": "domesticAirTickets",
        "/data/board/travel/external": "internationalAirfare",
        "/data/board/travel/hotel": "hotel",
        "/data/board/travel/taxi": "taxi",
        "/data/board/travel/train": "trainTicket",
        "/data/board/mice/offsite": "miceOffsite",
        "/data/board/mice/local": "miceLocal",
        "/data/board/travel/index": "travelOverview",
        default: "",
    };
    return resultMap[url] || resultMap.default;
};

const commonParams = {
    moduleType: 2,
    type: getTypeKey(window.location.hash.split('#')[1]),
}
const commonKey = {
    cacheExpires: 0,
    concurrencyControl: true,
    concurrencyControlMode: "DIRTYREAD",
     
};
const queryCommonData = (params): Promise<Result> => {
    return post("data/api/bi/common/data", {
        concurrencyControl: true,
        concurrencyControlMode: "DIRTYREAD",
        ...params,
    });
};
//以下是电商接口
const businessCommonKey = {
    ...commonKey,
    viewId: "bf2db56c16f54d39886f4b4aa7e98f7f",
};
export const querybusinessAccumulative = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "order_date",
    });
    return queryCommonData({
        // ...commonParams,
        ...businessCommonKey,
        a: "累计成交",
        aggregators: [
            {
                "alias": "订单数",
                "column": [
                  "order_number"
                ],
                  "sqlOperator": "COUNT_DISTINCT"
              },
              {
                "alias": "成交金额",
                "column": [
                  "total_price"
                ],
                "sqlOperator": "SUM"
              },{
                "alias": "结算单位",
                "column": [
                  "account_company_code"
                ],
                  "sqlOperator": "COUNT_DISTINCT"
              },{
                "alias": "预算部门",
                "column": [
                  "budget_department_code"
                ],
                  "sqlOperator": "COUNT_DISTINCT"
              },{
                "alias": "成交产品种类",
                "column": [
                  "three_level_categrory_name"
                ],
                  "sqlOperator": "COUNT_DISTINCT"
              },{
                "alias": "成交产品数量",
                "column": [
                  "num"
                ],
                "sqlOperator": "SUM"
              }
         ],
         groups: [],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns: [],
        columns: [],
        script: true
        
    });
};
export const querySynchronismTaxiTrend = (
    { functionColumns }: any,
    name: string | null,
    from: string | null,
    dateParams: any,
): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilterSynchronism({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "order_date",
        dateParams,
    });
    return queryCommonData({
        ...commonParams,
        ...businessCommonKey,
        aggregators: [
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
            {
                alias: "成交金额",
                column: ["jg_ysje"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "dd_ydsj_group",
                column: ["dd_ydsj_group"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns,
    });
};
export const querybusinessSalesTrend = (
    { functionColumns }: any,
    name: string | null,
    from: string | null,
): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "order_date",
    });
    return queryCommonData({
        ...commonParams,
        ...businessCommonKey,
        aggregators: [
            {
                "alias": "销售金额",
                "column": [
                    "total_price"
                ],
                "sqlOperator": "SUM"
            },
            {
                "alias": "订单数量",
                "column": [
                    "order_number"
                ],
                "sqlOperator": "COUNT_DISTINCT"
            }
        ],
        groups: [
            {
                "alias": "order_date_group",
                "column": [
                    "order_date_group"
                ]
            }
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns,
    });
};
export const queryBusinessPercentage = (name: string | null, from: string | null): Promise<Result> => {
    // params
    let filtersArr: any = [
         
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "order_date",
    });
    return queryCommonData({
        ...commonParams,
        ...businessCommonKey,
        a: "渠道占比",
        aggregators: [
            {
                "alias": "数据来源数量",
                "column": [
                  "data_source"
                ],
                "sqlOperator": "COUNT"
              }
        ],
        groups: [
            {
                "alias": "数据来源名称",
                "column": [
                  "data_source"
                ]
              }
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
        functionColumns: []
    });
};
 
export const queryBusinessSettleRank = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "order_date",
    });
    return queryCommonData({
        ...commonParams,
        ...businessCommonKey,
        aggregators: [
            {
                "alias": "成交金额",
                "column": [
                  "total_price"
                ],
                "sqlOperator": "SUM"
              }
        ],
        groups: [
            {
                "alias": "商品名称",
                "column": [
                  "goods_name"
                ]
              }
        ],
        orders: [{
            "column": [
                "total_price"
            ],
            "operator": "DESC",
            "aggOperator": "SUM"
        }],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
        functionColumns: []
    });
};
 
export const querybusinessTrend = (name: string | null, from: string | null): Promise<Result> => {
    const filterArr = [
       
    ] as any;
    if (name) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
     filterArr.push({
        "aggOperator": null,
        "column": [
            "payment_type_name"
        ],
        "sqlOperator": "NOT_NULL",
        "values": []
    });
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn: "order_date",
    });
    return queryCommonData({
        ...commonParams,
        ...businessCommonKey,
        a: "支付类型",
        aggregators: [
            {
              "alias": "支付类型数量",
              "column": [
                "payment_type_name"
              ],
              "sqlOperator": "COUNT"
            }
          ],
        groups: [
            {
                "alias": "支付类型名称",
                "column": [
                  "payment_type_name"
                ]
              }
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
export const queryBusinessType = (name: string | null, from: string | null): Promise<Result> => {
    const filterArr = [
        {
            "aggOperator": null,
            "column": [
                "budget_source"
            ],
            "sqlOperator": "NOT_NULL",
            "values": []
        }
         
    ] as any;
    if (name) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn: "order_date",
    });
    return queryCommonData({
        ...commonParams,
        ...businessCommonKey,
        aggregators: [
            {
                "alias": "COUNT(budget_source)",
                "column": [
                    "budget_source"
                ],
                "sqlOperator": "COUNT"
            },
        ],
        groups: [{
            "alias": "budget_source",
            "column": [
                "budget_source"
            ]
        }],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns: []
    });
};
export const queryBusinessMapData = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [
        {
            "aggOperator": null,
            "column": [
                "province_code"
            ],
            "sqlOperator": "NOT_NULL",
            "values": []
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "order_date",
    });
    return queryCommonData({
        ...commonParams,
        ...businessCommonKey,
        aggregators: [
            {
                "alias": "订单数量",
                "column": [
                  "order_number"
                ],
                "sqlOperator": "COUNT_DISTINCT"
              }
        ],
        groups: [
            {
              "alias": "省份名称",
              "column": [
                "province_name"
              ]
            },
            {
              "alias": "省份code",
              "column": [
                "province_code"
              ]
            },
            {
              "alias": "省份经度",
              "column": [
                "province_lng"
              ]
            },
            {
              "alias": "省份维度",
              "column": [
                "province_lat"
              ]
            }
      
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns: []
    });
};
export const queryBusinessCityData = (provinceCode): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: [
            {
                "aggOperator": null,
                "column": [
                    "province_code"
                ],
                "sqlOperator": "EQ",
                "values": [{
                    "value": provinceCode,
                    "valueType": "STRING"
                }]
            },
            {
                "aggOperator": null,
                "column": [
                    "province_code"
                ],
                "sqlOperator": "NOT_NULL",
                "values": []
            }
        ] as any,
        companyColumn: "account_company_code",
        dateColumn: "order_date",
    });
    return queryCommonData({
        ...commonParams,
        ...businessCommonKey,
        aggregators: [
            {
                "alias": "订单数量",
                "column": [
                    "order_number"
                ],
                "sqlOperator": "COUNT_DISTINCT"
            }
        ],
        groups: [
            {
                "alias": "省份code",
                "column": [
                    "province_code"
                ]
            },
            {
                "alias": "省份名称",
                "column": [
                    "province_name"
                ]
            }
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 1000,
        },
    });
};
 //以下是创客接口
const makerCommonKey = {
    ...commonKey,
    viewId: "fc69b62a1e6d4efb9305207e216c67b5",
};

 //以下是创客接口
 const payTypeCommonKey = {
    ...commonKey,
    viewId: "5e8201ff1a314c968e6ef9ab1f396685",
};
export const querymakerAccumulative = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "order_date",
    });
    return queryCommonData({
        // ...commonParams,
        ...makerCommonKey,
        a: "累计成交",
        aggregators: [
            {
             "alias": "订单数",
             "column": [
               "order_number"
             ],
               "sqlOperator": "COUNT_DISTINCT"
           },
           {
             "alias": "成交金额",
             "column": [
               "statistic_price"
             ],
             "sqlOperator": "SUM"
           },{
             "alias": "成本",
             "column": [
               "cost_price"
             ],
               "sqlOperator": "SUM"
           },{
             "alias": "毛利",
             "column": [
               "profit_price"
             ],
               "sqlOperator": "SUM"
           },{
             "alias": "毛利率",
             "column": [
               "profit_price_rate"
             ] 
           }
         ],
         groups: [],
        filters,
        pageInfo: {
            "countTotal": false,
            "pageSize": 100
          },
          functionColumns: [{
                    "alias": "profit_price_rate",
                    "snippet": "SUM(profit_price)/SUM(statistic_price)"
                }],
        
        columns: [],
        script: true
        
    });
};
 
export const queryMakerSalesTrend = (
    { functionColumns }: any,
    name: string | null,
    from: string | null,
): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "order_date",
    });
    return queryCommonData({
        ...commonParams,
        ...makerCommonKey,
        aggregators: [
            {
                "alias": "销售金额",
                "column": [
                    "statistic_price"
                ],
                "sqlOperator": "SUM"
            } , {
                "alias": "订单数量",
                "column": [
                    "order_number"
                ],
                "sqlOperator": "COUNT_DISTINCT"
            } 
            
        ],
        groups: [
            {
                "alias": "order_date_group",
                "column": [
                    "order_date_group"
                ]
            }
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
        functionColumns,
    });
};
export const queryMakerPercentage = (name: string | null, from: string | null): Promise<Result> => {
    // params
    let filtersArr: any = [
         
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "order_date",
    });
    return queryCommonData({
       
        ...makerCommonKey,
        a: "园区占比",
        aggregators: [
            {
                "alias": "销售金额",
                "column": [
                  "statistic_price"
                ],
                "sqlOperator": "SUM"
              }
        ],
        groups: [
            {
                "alias": "园区名称",
                "column": [
                  "warehouse_name"
                ]
              }
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
        functionColumns: []
    });
};
 
export const querySupplierRank = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    filtersArr.push({
        aggOperator: null,
        column: ["supplier_name"],
        sqlOperator: "NOT_NULL",
        values: [],
    });
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "order_date",
    });
    return queryCommonData({
        ...commonParams,
        ...makerCommonKey,
        aggregators: [
            {
                "alias": "成交金额",
                "column": [
                    "statistic_price"
                ],
                "sqlOperator": "SUM"
            }
        ],
        groups: [
            {
                "alias": "供应商名称",
                "column": [
                    "supplier_name"
                ]
            }
        ],
        orders: [{
            "column": [
                "statistic_price"
            ],
            "operator": "DESC",
            "aggOperator": "SUM"
        }],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 10,
        },
        functionColumns: []
    });
};
export const queryAccountRank = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    filtersArr.push({
        aggOperator: null,
        column: ["account_company_code"],
        sqlOperator: "NOT_NULL",
        values: [],
    });
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "order_date",
    });
    return queryCommonData({
        ...commonParams,
        ...payTypeCommonKey,
        aggregators: [
            {
                "alias": "成交金额",
                "column": [
                    "amount"
                ],
                "sqlOperator": "SUM"
            }
        ],
        groups: [
            {
                "alias": "结算单位名称",
                "column": [
                    "account_company_name"
                ]
            }
        ],
        orders: [{
            "column": [
                "amount"
            ],
            "operator": "DESC",
            "aggOperator": "SUM"
        }],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 10,
        },
        functionColumns: []
    });
};

export const queryMakerTrend = (name: string | null, from: string | null): Promise<Result> => {
    const filterArr = [
       
    ] as any;
    if (name) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    filterArr.push({
        aggOperator: null,
        column: ["pay_type_name"],
        sqlOperator: "NOT_NULL",
        values: [],
    });
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn: "order_date",
    });
    return queryCommonData({
        ...commonParams,
        ...payTypeCommonKey,
        a: "支付类型",
        aggregators: [
            {
                "alias": "支付类型数量",
                "column": [
                  "pay_type_name"
                ],
                "sqlOperator": "COUNT"
              }
          ],
        groups: [
            {
                "alias": "支付类型名称",
                "column": [
                  "pay_type_name"
                ]
              }
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};
// export const queryBusinessType = (name: string | null, from: string | null): Promise<Result> => {
//     const filterArr = [
//         {
//             "aggOperator": null,
//             "column": [
//                 "budget_source"
//             ],
//             "sqlOperator": "NOT_NULL",
//             "values": []
//         }
         
//     ] as any;
//     if (name) {
//         filterArr.push({
//             aggOperator: null,
//             column: [from],
//             sqlOperator: "EQ",
//             values: [
//                 {
//                     value: name,
//                     valueType: "STRING",
//                 },
//             ],
//         });
//     }
//     const filters = getCommonFilter({
//         dataSourceKey: 'budget_source',
//         defaultFilters: filterArr,
//         companyColumn: "account_company_code",
//         dateColumn: "order_date",
//     });
//     return queryCommonData({
//         ...commonParams,
//         ...businessCommonKey,
//         aggregators: [
//             {
//                 "alias": "COUNT(budget_source)",
//                 "column": [
//                     "budget_source"
//                 ],
//                 "sqlOperator": "COUNT"
//             },
//         ],
//         groups: [{
//             "alias": "budget_source",
//             "column": [
//                 "budget_source"
//             ]
//         }],
//         filters,
//         pageInfo: {
//             countTotal: true,
//             pageSize: 100,
//         },
//         functionColumns: []
//     });
// };
// export const queryBusinessMapData = (name: string | null, from: string | null): Promise<Result> => {
//     let filtersArr: any = [
//         {
//             "aggOperator": null,
//             "column": [
//                 "province_code"
//             ],
//             "sqlOperator": "NOT_NULL",
//             "values": []
//         },
//     ];
//     if (name) {
//         filtersArr.push({
//             aggOperator: null,
//             column: [from],
//             sqlOperator: "EQ",
//             values: [
//                 {
//                     value: name,
//                     valueType: "STRING",
//                 },
//             ],
//         });
//     }
//     const filters = getCommonFilter({
//         dataSourceKey: 'budget_source',
//         defaultFilters: filtersArr,
//         companyColumn: "account_company_code",
//         dateColumn: "order_date",
//     });
//     return queryCommonData({
//         ...commonParams,
//         ...businessCommonKey,
//         aggregators: [
//             {
//                 "alias": "订单数量",
//                 "column": [
//                   "order_number"
//                 ],
//                 "sqlOperator": "COUNT_DISTINCT"
//               }
//         ],
//         groups: [
//             {
//               "alias": "省份名称",
//               "column": [
//                 "province_name"
//               ]
//             },
//             {
//               "alias": "省份code",
//               "column": [
//                 "province_code"
//               ]
//             },
//             {
//               "alias": "省份经度",
//               "column": [
//                 "province_lng"
//               ]
//             },
//             {
//               "alias": "省份维度",
//               "column": [
//                 "province_lat"
//               ]
//             }
      
//         ],
//         filters,
//         pageInfo: {
//             countTotal: true,
//             pageSize: 100,
//         },
//         functionColumns: []
//     });
// };
export const queryMakerOrderType = (name: string | null, from: string | null): Promise<Result> => {
    // params
    let filtersArr: any = [
         
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "order_date",
    });
    filters.push({
        aggOperator: null,
        column:   ["data_source"],
        sqlOperator: "IN",
        values: [
            {
                value: "平台直引单",
                valueType: "STRING",
            }, {
                value: "电商节日",
                valueType: "STRING",
            },{
                value: "电商高温",
                valueType: "STRING",
            },{
                value: "超市日常单",
                valueType: "STRING",
            },{
                value: "销售下单",
                valueType: "STRING",
            }
        ],
    });
    return queryCommonData({
        cache: false,
        ...makerCommonKey,
        a: "订单类型",
        aggregators: [
            {
                "alias": "销售金额",
                "column": [
                  "total_price"
                ],
                "sqlOperator": "SUM"
              }
        ],
        groups: [
            {
                "alias": "数据来源",
                "column": [
                  "data_source"
                ]
              }
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
        orders: [],
        functionColumns: [],
        columns: [],
        script: true
    });
};