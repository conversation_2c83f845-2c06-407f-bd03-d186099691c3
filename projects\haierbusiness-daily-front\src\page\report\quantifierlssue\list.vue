<script setup lang="ts">
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Input as hInput,
  Table as hTable,
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Dropdown as hDropdown,
  Menu as hMenu,
  MenuItem as hMenuItem,
  DatePicker as hDatePicker,
  RangePicker as hRangePicker,
} from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import { computed, onMounted, ref ,watch  } from 'vue';
import { PlusOutlined, SearchOutlined, BarsOutlined, DownOutlined, DownloadOutlined } from '@ant-design/icons-vue';
import { userApi, enterpriseApi, processApi } from '@haierbusiness-front/apis';
import { dailyReportApi, dailyDeptApi } from '@haierbusiness-front/apis/src/daily';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue'
import { usePagination, useRequest } from "vue-request";
import { IMonthPlantDTO, UserGroupSystemConstant} from "@haierbusiness-front/common-libs";
import { useEditDialog, useDelete } from "@haierbusiness-front/composables";
import { getCurrentRouter, errorModal, routerParam, checkUserGroup } from "@haierbusiness-front/utils";

const router = getCurrentRouter();

const columns: ColumnType[] = [
  {
    title: '部门',
    dataIndex: 'deptName',
    width: '8%',
    align: 'center',
  },
  {
    title: '量化人',
    dataIndex: 'quantifierUsername',
    width: '5%',
    align: 'center',
  },
  {
    title: '日量化问题',
    dataIndex: 'issue',
    width: '10%',
    align: 'center',
  },
  {
    title: '关差措施及要求',
    dataIndex: 'dispose',
    width: '10%',
    align: 'center',
  },
  {
    title: '责任人 ',
    dataIndex: 'principalUsername',
    width: '5%',
    align: 'center',
  },
  {
    title: '人员状态 ',
    dataIndex: 'quantifierState',
    width: '5%',
    align: 'center',
  },
  {
    title: '关差时间 ',
    dataIndex: 'disposeDate',
    width: '8%',
    align: 'center',
  },
  {
    title: '创建时间 ',
    dataIndex: 'gmtCreate',
    width: '8%',
    align: 'center',
  },
];


const searchParam = ref<IMonthPlantDTO>({
});

const reset = () => {
  searchParam.value = {};
  startBeginAndEnd.value = [];
};

const {
  data,
  run: processApiRun,
  loading,
  current,
  pageSize,
} = usePagination(dailyReportApi.getQuantifierlssueList, {
  manual: false,
});

const {
  data: quantifierStateList,
  loading: quantifierStateLoading,
} = usePagination(dailyReportApi.getQuantifierStateList, {
  manual: false,
});


const {
  data: deptList,
  loading: deptListLoading,
} = usePagination(dailyDeptApi.list, {
  manual: false,
});

const deptSelect = computed(() => {
  let deptArr = [];
  for (let i in deptList.value) {
    deptArr.push(
        {
          value: deptList.value[i].code,
          label: deptList.value[i].name,
        }
    )
  }
  return deptArr;
});

const params = ref<IUserListRequest>({
    pageNum: 1,
    pageSize: 20
})

const quantifierState = computed(() => {
  return quantifierStateList.value;
});

const checkUserPlatFrom = computed(() => {
  return checkUserGroup(UserGroupSystemConstant.DAILY_PLATFORM_MANAGER.groupId);
});

const checkUserAdmin = computed(() => {
  return checkUserGroup(UserGroupSystemConstant.SUPER_MANAGE.groupId);
});


const {
  data: exportData,
  run: exportApiRun,
  loading: detailsExportLoading,
} = useRequest(dailyReportApi.exportQuantifierlssueReport);


const dataSource = computed(() => data.value?.records || []);
const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  processApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const exportQuantifierlssueReport = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  exportApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const userNameChange = (userInfo: IUserInfo | undefined) => {
    if (!userInfo) {
        searchParam.value.quantifierUsercode = ''
        searchParam.value.quantifierUsername = ''
        return
    }
    searchParam.value.quantifierUsercode = userInfo.username
    searchParam.value.quantifierUsername = userInfo.nickName
}

const userNameChange2 = (userInfo: IUserInfo | undefined) => {
    if (!userInfo) {
        searchParam.value.principalUsercode = ''
        searchParam.value.principalUsername = ''
        return
    }
    searchParam.value.principalUsercode = userInfo.username
    searchParam.value.principalUsername = userInfo.nickName
}

const startBeginAndEnd = ref<[Dayjs, Dayjs]>()
watch(() => startBeginAndEnd.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.startDate = n[0]
    searchParam.value.endDate = n[1]
  } else {
    searchParam.value.startDate = undefined
    searchParam.value.endDate = undefined
  }
});

</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :gutter="[12, 24]" :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px" v-if="checkUserAdmin || checkUserPlatFrom">
            <label for="deptCode">部门：</label>
          </h-col>
          <h-col :span="4" v-if="checkUserAdmin || checkUserPlatFrom">          
            <a-select
              v-model:value="searchParam.deptCode"
              show-search
              placeholder="请选择部门"
              style="width: 200px"
              allowClear
              :options="deptSelect"
            >
            </a-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="quantifierUsercode">量化人：</label>
          </h-col>
          <h-col :span="4">
              <user-select :value="searchParam.quantifierUsername" style="width: 200px"
                :params="params" @change="(userInfo: IUserInfo) =>  userNameChange(userInfo)"/>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="principalUsercode">责任人：</label>
          </h-col>
          <h-col :span="4">
              <user-select :value="searchParam.principalUsername" style="width: 200px"
                :params="params" @change="(userInfo: IUserInfo) =>  userNameChange2(userInfo)"/>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="quantifierState">人员状态：</label>
          </h-col>
          <h-col :span="4">
            <a-select
              v-model:value="searchParam.quantifierState"
              show-search
              placeholder="人员状态"
              style="width: 200px"
              allowClear
              :options="quantifierState"
            >
            </a-select>
          </h-col> 
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="startBeginAndEnd">关差时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="startBeginAndEnd" value-format="YYYY-MM-DD HH:mm:ss" style="width: 200px;" />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">

        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :offset="18" :span="6" style="text-align: right">
            <h-button type="primary" @click="exportQuantifierlssueReport({ current: 1, pageSize: 10 })">
              <template #icon>
                <DownloadOutlined />
              </template>
              导出
            </h-button>
            <h-button style="margin-left: 10px" type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined /> 查询
            </h-button>
            <h-button style="margin-left: 10px" @click="reset">重置</h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :row-key="(record) => record.id"
          :data-source="dataSource"
          :pagination="pagination"
          :size="'small'"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'quantifierState'">
              <template v-if="record.quantifierState == 10">等待录入</template>
              <template v-if="record.quantifierState == 21">休假</template>
              <template v-if="record.quantifierState == 22">离职</template>
              <template v-if="record.quantifierState == 23">其他</template>
              <template v-if="record.quantifierState == 30">录入中</template>
              <template v-if="record.quantifierState == 40">已录入完成</template>
              <template v-if="record.quantifierState == 99">未录入</template>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}
</style>
