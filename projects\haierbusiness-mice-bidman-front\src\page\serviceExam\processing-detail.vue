<!-- 服务商考核详情中的列表 -->
<script setup lang="ts">
import {
    Modal as hModal,
    Button as hButton,
} from 'ant-design-vue';

const props = defineProps<{
    visible: boolean;
    data: any;
}>();
const emit = defineEmits(['cancel']);

const handleCancel = () => {
    emit('cancel');
};

defineExpose({
    handleCancel
});
</script>

<template>
    <div>
        <h-modal :visible="visible" title="处理详情" @cancel="handleCancel" :ok-text="'确定'" :cancel-text="null"
            @ok="handleCancel" width="600px">
            <div class="processing-detail">
                <div class="detail-item">
                    <span class="label">说明：</span>
                    <span class="value">{{ data?.illustrate }}</span>
                </div>
                <div class="detail-item">
                    <span class="label">付款凭证：</span>
                    <span class="value">
                        <template v-if="data?.path && data.path.length > 0">
                            <a v-for="(item, index) in data.path" :key="index" :href="item.path" target="_blank"
                                style="color: #1890ff; text-decoration: none; margin-right: 4px;">
                                文件{{ index + 1 }}
                                <span v-if="index < data.path.length - 1">, </span>
                            </a>
                        </template>
                        <span v-else>-</span>
                    </span>
                </div>
                <div class="detail-item">
                    <span class="label">申请时间：</span>
                    <span class="value">{{ data?.applyTime }}</span>
                </div>


            </div>
        </h-modal>
    </div>
</template>

<style scoped lang="less">
.processing-detail {
    .detail-item {
        margin-bottom: 16px;
        line-height: 1.5;
    }

    .label {
        font-weight: bold;
        margin-right: 8px;
    }

    .value {
        color: #666;
    }

    .result-section {
        margin-top: 24px;
        border-top: 1px solid #f0f0f0;
        padding-top: 16px;

        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 16px;
        }
    }
}
</style>