<template>
  <div>
    <div class="title flex justify-content-between align-items-center">
      <div class="flex align-items-center justify-content-center">
        <span class="shu"></span>
        <div class="text">行程计划与费用预算</div>
      </div>
    </div>
    <van-cell-group inset class="mb-10">
      <van-field v-if="creatTripParma.travelReserveFlag && !props.isChange" required :error-message="budgetErrorMessage" label-align="top" readonly>
        <template #label>
          <div class="my_field_label flex justify-content-between align-items-center">
            <div class="left">
              <span class="mr-5">预算余额</span>
              <van-icon name="question-o" @click.stop="showMessage('选择部门国内差旅费预算承接人。')" color="#cccccc" />
            </div>
            <div class="right">
              <div class="right other-text" @click.stop="choseBudgeDialog = true">选择其他预算<van-icon name="arrow"
                color="#8c8c8c" /></div>
            </div>
          </div>
        </template>

        <template #input>
          <div class="flex align-items-center justify-content-between budge-box" style="width: 100%">
            <div class="left flex align-items-center">
              <div class="user-text mr-5" v-if="!props.creatTripParma?.haierBudgetPayOccupyRequest?.isQueryDept">{{
                props.creatTripParma?.haierBudgetPayOccupyRequest?.budgeterName || mainPerson()?.travelUserName }}的个人预算
              </div>
              <div class="user-text mr-5" v-else>{{
                props.creatTripParma?.haierBudgetPayOccupyRequest?.budgetDepartmentName }}的部门预算</div>

              <div>
                <span class="money-text mr-5">{{
                  ` ${props.creatTripParma?.haierBudgetPayOccupyRequest?.leftAmt || 0}`.replace(/\B(?=(\d{3})+(?!\d))/g,
                  ',')
                  }}</span>
                <span class="yuan">元</span>
              </div>
            </div>
            
          </div>
        </template>
      </van-field>

      <!-- 详情页、变更页预算信息展示 -->
      <van-field v-if="creatTripParma.travelReserveFlag && props.isChange"  label-align="top" readonly>
        <template #label>
          <div class="my_field_label flex justify-content-between align-items-center">
            <div class="left">
              <span class="mr-5">预算信息</span>
              <van-icon name="question-o" @click.stop="showMessage('选择部门国内差旅费预算承接人。')" color="#cccccc" />
            </div>
            <div class="right"></div>
          </div>
        </template>

        <template #input>
          <div class="flex align-items-center justify-content-between budge-box" style="width: 100%">
            <div class="left flex align-items-center">
              <div class="user-text mr-5" v-if="!props.creatTripParma?.haierBudgetPayOccupyRequest?.isQueryDept">{{
                props.creatTripParma?.haierBudgetPayOccupyRequest?.budgeterName || mainPerson()?.travelUserName }}的个人预算
              </div>
              <div class="user-text mr-5" v-else>{{
                props.creatTripParma?.haierBudgetPayOccupyRequest?.budgetDepartmentName }}的部门预算</div>

              <div>

              </div>
            </div>
            <div class="right other-text" @click.stop="showBudgeDetail = true">预算详情<van-icon name="arrow"
                color="#8c8c8c" /></div>
          </div>
        </template>
      </van-field>
    </van-cell-group>

    <!-- 行程计划 -->
    <van-cell-group inset class="mb-10">
      <van-field required :error-message="planErrorMessage" label-align="top" readonly>
        <template #label>
          <div class="my_field_label flex justify-content-between align-items-center">
            <div class="left">
              <span class="mr-5">行程计划</span>
              <van-icon name="question-o" @click.stop="showMessage('按照出差任务选择始发、目的地以及中转地，务必包含全部途径地及停留时间。事前做好差旅行为计划，提前规划行程（国内机票建议提前5天预订出票），在满足出行需求的前提下，确保成本最优；')" color="#cccccc" />
            </div>
            <div class="color-main font-size-12" @click.stop="showAddCity">
              <van-icon name="location-o" />
              添加行程
            </div>
          </div>
        </template>

        <template #input>
          <div class="city-list mt-20 flex flex-column">
            <!-- 已经添加的城市列表 -->
            <div v-for="(item, index) in cityList" :key="index"
              class="city-item mb-30 flex justify-content-between align-items-center">
              <div class="left flex align-items-center">
                <van-icon class="mr-10" name="location" color="#0073e5" />
                <div class="item-city" @click.stop="showCityPop('list', index)">
                  <span>{{ item.city }}</span>
                  <van-icon class="ml-5" name="arrow" />
                </div>
              </div>
              <div class="right">
                <div class="item-time" @click.stop="openTimePicker('list', index)" v-if="index != 0">
                  <span>{{ formatDate(cityList[index - 1].date) }}</span>
                  <van-icon class="ml-5 mr-5" name="minus" />
                  <span>{{ formatDate(item.date) }}</span>
                </div>
                <div class="item-time" @click.stop="openTimePicker('list', index)" v-else>
                  <span>{{ formatDate(item.date) }}</span>
                </div>
              </div>
              <div class="shu" v-if="index + 1 != cityList.length"></div>
            </div>

            <!-- 新增第一个起始点城市 -->
            <div v-if="showFirstAddCity" class="city-item mb-30 flex justify-content-between align-items-center">
              <div class="left flex align-items-center">
                <van-icon class="mr-10" name="location" color="#919191" />
                <div class="item-city add-color" @click.stop="showCityPop('begin', 0)">
                  <span class="add-color">{{ newBeginCity.name || '选择城市' }}</span>
                  <van-icon class="add-color ml-5" name="arrow" />
                </div>
              </div>
              <div class="right add-color">
                <div class="item-time add-color" @click.stop="openTimePicker('begin', 0)">
                  <span>{{ formatDate(beginTime) || '离开日期' }}</span>
                </div>
              </div>
            </div>

            <!-- 新增后续城市 -->
            <div v-if="showNextAddCity" class="city-item mb-30 flex justify-content-between align-items-center">
              <div class="left flex align-items-center">
                <van-icon class="mr-10" name="location" color="#919191" />
                <div class="item-city add-color" @click.stop="showCityPop('next', 0)">
                  <span class="add-color">{{ newEndCity.name || '选择城市' }}</span>
                  <van-icon class="add-color ml-5" name="arrow" />
                </div>
              </div>
              <div class="right add-color">
                <div class="item-time add-color" @click.stop="openTimePicker('end', 0)">
                  <span>{{ formatDate(beginTime) || formatDate(props?.creatTripParma.endDate) || '出发日期' }}</span>
                  <van-icon class="ml-5 mr-5" name="minus" />
                  <span>{{ formatDate(endTime) || '离开日期' }}</span>
                </div>
              </div>
              <div class="dashed"></div>
            </div>
          </div>
        </template>
      </van-field>

      <!-- 时间选择 -->
      <van-popup v-model:show="showTimePicker" position="bottom">
        <van-date-picker v-model="currentDate" :min-date="minDate" :max-date="maxDate" @confirm="confirmTime"
          @cancel="showTimePicker = false" />
      </van-popup>

      <!-- 城市选择 -->
      <van-popup v-model:show="showCityPicker" closeable round position="bottom">
        <van-cell title="请选择城市" style="font-weight:600; " ></van-cell>
        <van-search  autocomplete="off" v-model="searchParams.name" placeholder="请输入城市名称" />
        
        <van-list
          class="city-search-list"
          v-if="searchCityList?.length > 0 && searchParams.name"
          :immediate-check="false"
          v-model:loading="cityListLoading"
          :finished="cityListFinished"
          finished-text="没有更多了"
          @load="onLoadCityList"
        >
          <van-cell v-for="item in searchCityList" :key="item"  @click="searchCityChose(item)">
            <template #title>
              {{item.provinceName}} - {{ item.name}}
            </template>
          </van-cell>
        </van-list>

        <van-cascader v-else v-model="chosedCity" :show-header="false" :closeable="false" :options="cityDict" 
          :field-names="{ text: 'name', value: 'id', children: 'children' }" 
          @finish="finishCityChose" />
      </van-popup>
    </van-cell-group>

    <!-- 具体行程 -->
    <div class="title-mini flex justify-content-between align-items-center">
      <div class="flex align-items-center justify-content-center">
        <div class="text">具体行程</div>
      </div>
    </div>
    <div v-for="(trip, index) in props.creatTripParma?.tripList" :key="index">

      <van-cell-group inset class="mb-10" style="position: relative;  overflow: visible;">
        <div class="padding-16" style="position: relative;">
          <div class="transparent-bg" style="width: 100%;height: 100%; position: absolute; top: 0; left: 0"></div>
          <div class="my_field_label flex justify-content-between align-items-center mb-20 ">
            <div class="left">
              <span class="mr-5" style="color: #898c8c">行程{{ index + 1 }}</span>
            </div>
            <div class="flex align-items-center color-main font-size-12">
              <div class="mr-10 clbz" @click="addMoney(index, trip)" v-if="props.creatTripParma.travelReserveFlag">
                <van-icon name="add-o" />
                添加费用
              </div>
              <van-icon name="close" @click.stop="delTrip(index)" color="red" class="font-size-14" />
            </div>
          </div>

          <div class="flex flex-column" style="width: 100%">
            <div class="flex align-items-center mb-20 ">
              <div class="item-city">
                <span class="strong">{{ trip.beginCityName }}</span>
                <van-icon class="ml-5" name="arrow" />
              </div>
              <van-icon class="ml-10 mr-10" name="minus" />
              <div class="item-city">
                <span class="strong">{{ trip.endCityName }}</span>
                <van-icon class="ml-5" name="arrow" />
              </div>
            </div>

            <div class="flex justify-content-between mb-10">
              <div class="flex align-items-center range-font">
                <div class="item-city">
                  <span class="">{{ formatDate(trip.beginDate) }}</span>
                </div>
                <van-icon class="ml-5 mr-5" name="minus" />
                <div class="item-city">
                  <span class="">{{ formatDate(trip.endDate) }}</span>
                  <van-icon class="ml-5" name="arrow" />
                </div>
              </div>

              <div class="right color-eee font-size-13 clbz" @click.stop="getClbz">差旅标准</div>
            </div>
          </div>
        </div>
        <div v-if="trip.tripDetailMapList?.length > 0"
          class="show-more-box flex align-items-center justify-content-center" @click="trip.showMore = !trip.showMore">
          <div style="width:100%; height: 100%"
            :class="trip.showMore == true ? 'show-more-box-close' : 'show-more-box-open'"></div>
        </div>
      </van-cell-group>
      <!-- 费用列表 -->
      <div v-if="trip.showMore">
        <van-cell-group inset class="mb-10" v-for="(detail, dIndex) in trip.tripDetailMapList" :key="dIndex">
          <!-- 出行方式 -->
          <van-cell is-link>
            <template #title>
              <div class="flex align-items-center" @click.stop="openTravelPicker(index, dIndex)">
                <div class="money-title flex align-items-center">
                  <div v-if="detail.productCode" class="flex align-items-center">
                    <img class="mr-5" style="width: 16px"
                      :src="travelList?.filter((item) => item.productCode == detail.productCode)[0]?.iconUrl" alt="" />
                    <span class="mr-5 font-size-14">{{ detail.productName }}</span>
                  </div>
                  <div v-else>选择费用</div>

                  <van-icon class="font-size-14" name="arrow-down" />
                </div>
              </div>
            </template>

            <template #right-icon>
              <div class="flex align-items-center">
                <icon12306 v-if="detail.productCode == '02'" class="mr-10"
                  @click.stop="showTrainPickerFun(trip.beginCityCode, trip.endCityCode, index, dIndex)"></icon12306>
                <van-icon class="mr-10" name="search" />
                <van-icon name="close" color="red" @click.stop="deleteItem(trip.tripDetailMapList, dIndex)" />
              </div>
            </template>
          </van-cell>
          <!-- 出行人 -->
          <van-field is-link @click="openApplyChosePop(index, dIndex)" label-align="top" readonly>
            <template #label>
              <div class="my_field_label flex justify-content-between align-items-center">
                <div class="left">
                  <span class="mr-5">出行人</span>
                </div>
              </div>
            </template>

            <template #input>
              <div class="color-eee" v-if="detail.travelApplyTripDetailList?.length < 1">选择出行人</div>
              <div class="flex flex-warp" v-else>
                <van-tag class="mb-5 mr-5" v-for="(applyItem, applyIndex) in detail.travelApplyTripDetailList"
                  :key="applyIndex" closeable size="medium" type="primary"
                  @close.stop="delApplyItem(index, dIndex, detail.travelApplyTripDetailList, applyIndex)">
                  {{ applyItem.travelUserName }}
                </van-tag>
              </div>
            </template>
          </van-field>

          <!-- 费用预算 -->
          <van-field @blur="getMemberBudget(trip, detail, false)" v-model="detail.budgetAmount" label-align="left"
            :error-message="detail?.excessiveFlag && detail.productCode != '03'  && detail.productCode != '05'  ? errorAmountText : ''" placeholder="预算金额" input-align="right" type="number" class="money-field">
            <template #label>
              <div class="my_field_label flex justify-content-between align-items-center">
                <div class="left">
                  <div>
                    <span class="mr-5">费用预算</span>
                    <van-icon class="mr-5" name="question-o" @click.stop="showMessage('按照个人差旅标准计算默认金额，可修改金额，结算以实际订单金额为准，剩余预算将在出差申请单行程确认后释放。机票预算：默认差标舱位全票价金额；火车票预算：默认出行当日差标坐席最高价；酒店预算：入住城市差标。')" color="#cccccc" />

                  </div>
                  <span class="font-size-13 color-eee" @click="showBudgetDetail(detail)">明细</span>
                </div>
              </div>
            </template>
            <template #button> 元 </template>
          </van-field>

          <!-- 超标原因 -->
          <van-field is-link v-if="detail.excessiveFlag && detail.productCode != '03'  && detail.productCode != '05'" @click="openResonPicker(index, dIndex, detail.productCode)" label-align="left"
            readonly>
            <template #label>
              <div class="my_field_label flex justify-content-between align-items-center">
                <div class="left">
                  <span class="mr-5">超标原因</span>
                  <van-icon class="mr-5" name="question-o" @click.stop="showMessage('差旅预订超标原因，便于后续差旅数据分析及差旅管理')" color="#cccccc" />

                </div>
                <div class="right"></div>
              </div>
            </template>

            <template #input>
              <div class="color-eee" v-if="!detail.excessiveReasonId">选择超标原因</div>
              <div class="flex" style="width:100%;" v-else>
                <div class="" style="width:100%; text-align:right;">{{ resonList.filter((item) => item.id == detail.excessiveReasonId)[0]?.reasonInfo }}</div>
              </div>
            </template>
          </van-field>

          <!-- 其他超标原因 -->
          <van-field  v-if="isShowReasonText(detail?.excessiveReasonId)" v-model="detail.excessiveOtherReasonDesc" label-align="left"
             placeholder="请填写其他原因" autocomplete="off" input-align="right" class="money-field">
            <template #label>
              <div class="my_field_label flex justify-content-between align-items-center">
                <div class="left">
                  <span class="mr-5">其他原因</span>
                </div>
              </div>
            </template>
          </van-field>

          <!-- 保险 -->
          <van-cell center title="保险" v-if="detail.productCode && detail.insuranceAmount">
            <template #right-icon>
              <div>
                {{ travelList?.filter((item) => item.productCode === detail.productCode)[0]?.insuranceAmount || 0 }} 元
              </div>
              <van-switch class="ml-10" @change="switchInsurance" v-model="detail.insuranceFlag" />
            </template>
          </van-cell>
        </van-cell-group>
      </div>

    </div>

    <!-- 预算合计 -->
    <div v-if="props.creatTripParma?.amountSum">
      <div class="title-mini flex justify-content-between align-items-center">
        <div class="flex align-items-center justify-content-center">
          <div class="text">预算合计</div>
        </div>
      </div>
      <van-cell-group inset class="mb-10">
        <van-collapse @click="getMoneyDetailList2" v-model="activeNames">
          <van-collapse-item name="1">
            <template #title>
              <div class="flex align-items-center justify-content-between">
                <div class="left">
                  <span class="mr-5">费用预算合计</span>
                  <van-icon name="question-o" />
                </div>
                <div class="right mr-10">
                  <span class="color-orange mr-5">{{
                    ` ${props.creatTripParma.amountSum || 0}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                    }}</span>元
                </div>
              </div>
            </template>
            <div class="flex flex-column">
              <div class="flex align-items-center justify-content-between mb-10" v-for="item, index in budgetDetailData"
                :key="index">
                <div class="name">{{ item.travelUserName }}</div>
                <div class="money"><span class="cmr-5">{{ item.amountSum }}</span>元</div>
              </div>
            </div>
          </van-collapse-item>
        </van-collapse>
      </van-cell-group>
    </div>

    <!-- 变更页展示预算详情 -->
     <van-popup v-model:show="showBudgeDetail" round position="bottom">
      <van-cell-group round class="mb-10">
        <van-cell title="预算信息：" :value="props.creatTripParma?.haierBudgetPayOccupyRequest.budgeterName"></van-cell>
          <van-cell title="预算系统：" :value="props.creatTripParma?.haierBudgetPayOccupyRequest.budgetSystemCode"></van-cell>

          <van-cell title="执行主体：" :value="props.creatTripParma?.haierBudgetPayOccupyRequest.performName"></van-cell>
          <van-cell title="预算主体：" :value="props.creatTripParma?.haierBudgetPayOccupyRequest.budgetDepartmentName"></van-cell>
          <!-- <van-cell title="预算金额：" :value="props.creatTripParma?.haierBudgetPayOccupyRequest.leftAmt"></van-cell> -->
          <van-cell title="费用科目：" :value="props.creatTripParma?.haierBudgetPayOccupyRequest.feeItemName"></van-cell>
          <van-cell title="出账法人：" :value="props.creatTripParma?.haierBudgetPayOccupyRequest.accountCompanyName"></van-cell>
          <van-cell title="成本中心：" :value="props.creatTripParma?.haierBudgetPayOccupyRequest.costCenterName"></van-cell>
      </van-cell-group>
     </van-popup>

    <!-- 选择出行人 -->
    <van-popup v-model:show="showChoseApplyList" position="bottom">
      <div class="out-pop" style="height: 100vh">
        <van-sticky>
          <div style="background: #fff; padding-bottom: 10px">
            <van-nav-bar title="选择出行人" left-arrow @click-left="showChoseApplyList = false">
              <template #left>
                <van-icon name="arrow-left" color="#000" size="24" />
              </template>
              <template #right>
                <div class="color-main" @click.stop="saveChosedPerson">确定</div>
              </template>
            </van-nav-bar>
          </div>
        </van-sticky>
        <van-list>
          <van-cell v-for="(item, index) in activeTravelList" :key="index">
            <van-checkbox v-model="item.checked" class="flex align-items-center out-person-checkbox">
              <div :class="index % 3 == 0 ? 'blue' : 'yellow'"
                class="mr-20 ml-10 flex align-items-center justify-content-center img-name">
                {{ item?.travelUserName.slice(-2) }}
              </div>
              <div class="main">
                <div class="user-name color-main">{{ item.travelUserName }}</div>
                <div class="phone">{{ item.travelUserDeptName || '13122223333' }}</div>
              </div>
            </van-checkbox>
          </van-cell>
        </van-list>
      </div>
    </van-popup>

    <!-- 选择出行方式 -->
    <van-popup v-model:show="showTravelPicker" round position="bottom">
      <van-picker title="选择出行方式" :columns-field-names="{ text: 'productName', value: 'productCode' }"
        :columns="travelList" @cancel="showTravelPicker = false" @confirm="travelPickerConfirm" />
    </van-popup>

    <!-- 选择超标原因 -->
    <van-popup v-model:show="showResonPicker" round position="bottom">
      <van-picker title="选择超标原因" :columns-field-names="{ text: 'reasonInfo', value: 'id' }" :columns="newResonList"
        @cancel="showResonPicker = false" @confirm="resonPickerConfirm" />
    </van-popup>

    <!-- 选择预算 -- old -->
    <!-- <van-action-sheet v-model:show="choseBudgeDialog" title="预算选择">
      <van-form @submit="submitBudge">
        <van-cell-group inset>
          <van-field autocomplete="off" input-align="right" error-message-align="right"
            v-model="choseBudgeForm.budgetType" name="费用项目" label="费用项目" placeholder="费用项目" required is-link readonly
            :rules="[{ required: true, message: '请选择费用项目' }]" />


          <user-select-m palceholder="请选择预算人" label="预算人" :value="choseBudgeForm.nickName"
            @chose="chosedPerson"></user-select-m>

          <van-field autocomplete="off" input-align="right" error-message-align="right"
            v-model="choseBudgeForm.budgetType" name="预算系统" label="预算系统" placeholder="预算系统" required is-link readonly
            :rules="[{ required: true, message: '请选择预算系统' }]" />

          <van-field autocomplete="off" input-align="right" error-message-align="right"
            v-model="choseBudgeForm.budgetType" name="预算部门" label="预算部门" placeholder="预算部门" required is-link readonly
            :rules="[{ required: true, message: '请选择预算部门' }]" />

          <van-field autocomplete="off" input-align="right" error-message-align="right"
            v-model="choseBudgeForm.budgetType" name="结算单位" label="结算单位" placeholder="结算单位" is-link readonly
            :rules="[{ required: true, message: '请选择结算单位' }]" />

          <van-field autocomplete="off" input-align="right" error-message-align="right"
            v-model="choseBudgeForm.budgetType" name="成本中心" label="成本中心" placeholder="成本中心" disabled readonly
            :rules="[{ required: true, message: '请先选择预算人' }]" />

          <van-field autocomplete="off" input-align="right" error-message-align="right"
            v-model="choseBudgeForm.budgetType" name="本月可用预算" label="本月可用预算" placeholder="本月可用预算" disabled readonly
            :rules="[{ required: true, message: '请先选择预算人' }]" />

        </van-cell-group>

        <div style="margin: 16px; background-color: #fff">
          <van-button style="width: 300px; margin:0 auto;" round block type="primary"
            native-type="submit">确认</van-button>
        </div>
      </van-form>
    </van-action-sheet> -->


    <!--选择预算 hbc2 -->
    <van-popup v-model:show="choseBudgeDialog" position="bottom">
      <div class="out-pop" style="height: 100vh">
        <van-sticky>
          <div style="background: #fff; padding-bottom: 10px">
            <van-nav-bar title="选择预算" left-arrow @click-left="choseBudgeDialog = false">
              <template #left>
                <van-icon name="arrow-left" color="#000" size="24" />
              </template>
            </van-nav-bar>
          </div>
        </van-sticky>

        <hbc2Budget :source="props.creatTripParma?.originApp" budgetType="hbc2" applicationCode="haierbusiness-business-trip" :haierBudgetPayOccupyRequest="props.creatTripParma?.haierBudgetPayOccupyRequest" @choosedBudget="choosedBudget"></hbc2Budget>
      </div>
    </van-popup>

    <!-- 选择火车站 -->
    <van-action-sheet v-model:show="showTrainPicker" cancel-text="确定" title="选择火车站点">
      <van-cell-group inset>
        <van-field input-align="right"
          v-model="props.creatTripParma.tripList[currentTripIndex].tripDetailMapList[currentTripDetailIndex].startTrainName"
          label="始发站" is-link readonly @click="chosedTrainStation('begin')" />
        <van-field input-align="right"
          v-model="props.creatTripParma.tripList[currentTripIndex].tripDetailMapList[currentTripDetailIndex].endTrainName"
          label="终点站" is-link readonly @click="chosedTrainStation('end')" />
      </van-cell-group>
    </van-action-sheet>

    <!-- 火车站选择 -->
    <van-popup v-model:show="showChoseTrainPicker" round position="bottom">
      <van-cascader v-model="chosedCity" title="选择火车站点" :options="currentTrainList"
        :field-names="{ text: 'stationName', value: 'stationCode', children: 'zdList' }"
        @close="showChoseTrainPicker = false" @finish="finishTrainStationChose" />
    </van-popup>

    <!-- 差旅标准 -->
    <van-dialog v-model:show="showClbz" title="差旅标准" >
      <div style="height: 400px; overflow:auto;" v-if="standardList?.length > 0">
        <van-cell-group v-for="item, index in standardList" :key="index">
          <div style="text-align:center; padding:10px; font-size: 14px; font-weight: 500;">{{nameList[index]}}的差旅标准</div>

          <div v-if="item && item.length > 0">
            <van-cell v-for="item2,index2 in item" :key="index2" :title="item2.productName">
              <template #label>
                <div v-html="item2?.describes"></div>
              </template>
            </van-cell>
          </div>
          <van-empty v-else description="暂无数据" />

        </van-cell-group>
      </div>

      <van-empty v-else description="暂无数据" />
    </van-dialog>


    <!-- 费用明细 -->
    <van-dialog v-model:show="budgetDetailModal" title="费用预算"  >
      <h-table style="margin: 10px;" :columns="descColumns" :data-source="activeBudgetDetail" size="small"
        :pagination="false" bordered>
      </h-table>
    </van-dialog>
  </div>
</template>

<script lang="ts" setup>
import { createVNode, onMounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref } from 'vue';
import hbc2Budget from './hbc2Budget.vue';
import icon12306 from '../../components/icon12306.vue';
import { showLoadingToast, showDialog} from 'vant';

import {
  IUserListRequest,
  IUserInfo,
  ICity,
  CityItem,
  ITripInfo,
  ITraveler,
  ICreatTrip,
  ITripList,
  ITripDetailMap,
  MemberBudgetParams,
  TrainStandardRes
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { tripApi, SupplierApi, reasonApi, cityApi } from '@haierbusiness-front/apis';
import { userApi } from '@haierbusiness-front/apis';
import { cloneDeep, debounce, values } from 'lodash';
import { showSuccessToast, showFailToast, showToast } from 'vant';

import { showConfirmDialog } from 'vant';
import userSelectM from './userSelectM.vue'
import Detail from '../../detail.vue';
import {
  Anchor as hAnchor,
  Button as hButton,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  InputNumber as hInputNumber,
  Form as hForm,
  FormItem as hFormItem,
  Tag as hTag,
  Tooltip as hTooltip,
  Cascader as hCascader,
} from 'ant-design-vue';

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const showBudgeDetail = ref<boolean>(false);

interface Props {
  creatTripParma?: any; // 人员
  isDetail?: boolean;
  isChange?: boolean;
  chosedNow?: string;
}
const props = defineProps<Props>();

const emit = defineEmits(['show', 'hide']);


// 差旅标准
const showClbz = ref<boolean>(false)
const standardList = ref<Array<TrainStandardRes>>([])
  const queryTrainStandard = (params:any) => {
  tripApi.queryTrainStandard(params).then((res:any) => {
    standardList.value = res || []
  })
}

const codeList = ref<any>([]) 
const nameList = ref<any>([]) 
const getClbz = () => {
  codeList.value =  props.creatTripParma?.travelerList?.filter((item:any) => item.travelUserType == 0).map(item2 => item2.travelUserNo)
  nameList.value =  props.creatTripParma?.travelerList?.filter((item:any) => item.travelUserType == 0).map(item2 => item2.travelUserName)

  // 判断经办人的工号是否在出差人工号列表里面
  if(codeList.value.indexOf(loginUser.value?.username)== -1 ) {
    codeList.value = [loginUser.value?.username, ...codeList.value]
    nameList.value = [loginUser.value?.nickName, ...nameList.value]
  }
  queryTrainStandard({userCodes:codeList.value});
  showClbz.value = true
}

// 获取主出差人信息
const mainPerson = () => {
  return props.creatTripParma?.travelerList?.filter((item) => item.travelUserType=='0' && item.mainFlag == '1')[0];
};

/*
  新增城市相关
*/
const showNextAddCity = ref<boolean>(false);
const showFirstAddCity = ref<boolean>(false);

const newBeginCity = ref<CityItem>({
  name: '',
  citycode: '',
  syId: '',
});

const newEndCity = ref<CityItem>({
  name: '',
  citycode: '',
  syId: '',
});

const activeNames = ref(['']);

// 打开弹窗的类型 begin next list
const cityChoseType = ref<string>('');
// 点击选择的城市下标
const cityChoseIndex = ref<number>(0);

const chosedCity = ref();

const budgetErrorMessage = ref<string>('');
const planErrorMessage = ref<string>('');

const showMessage = (message: string) => {
  showToast(message);
}

// 城市关键词搜索
const searchCityList = ref<Array<any>>([])
const cityListLoading = ref(false)
const cityListFinished = ref(false)
const cityListTotal = ref<number>(0)


const searchParams = ref<any>(
  {
    name: '',
    pageSize: 20,
    pageNum: 0,
    types: '1,2,3,4',
    internationalFlag: 0,
    providerCode: 'VETECH'  
  }
)

const onLoadCityList = () => {
  searchParams.value.pageNum++;
  cityApi.getCityList(searchParams.value).then((res) => {
    // 加载状态结束
    cityListLoading.value = false;
    searchCityList.value = [...searchCityList.value, ...res.records];
    cityListTotal.value = res.total ?? 0;
    // 数据全部加载完成
    if (searchCityList.value.length >= cityListTotal.value) {
      cityListFinished.value = true;
    }
  });
};

watch(
  () => searchParams.value.name,
  (newVal,oldVal) => {
    if(newVal) {
      searchParams.value.pageNum = 0
      onLoadCityList()
    }else {
      cityListFinished.value = false
      searchCityList.value = []
    }
  }
)

// 城市选择相关
const showCityPicker = ref<boolean>(false);
const showCityPop = (type: string, index: number) => {
  chosedCity.value = undefined;
  if (type == 'begin') {
    chosedCity.value = newBeginCity.value.citycode;
  } else if (type == 'next') {
    chosedCity.value = newEndCity.value.citycode;
  } else {
    chosedCity.value = cityList.value[index].cityCode;
    cityChoseIndex.value = index;
  }
  cityChoseType.value = type;
  showCityPicker.value = true;
};

const searchCityChose = (item:any) => {
  if(!item?.providerMapList) {
    showToast('暂不支持此城市!')
    return
  }

  if (cityChoseType.value == 'begin') {
    newBeginCity.value.name = item.name;
    newBeginCity.value.citycode = item.id;
    newBeginCity.value.syId = item?.providerMapList[0]?.districtId;
  } else if (cityChoseType.value == 'next') {
    newEndCity.value.name = item.name;
    newEndCity.value.citycode = item.id;
    newEndCity.value.syId = item?.providerMapList[0]?.districtId;
  } else {
    cityList.value[cityChoseIndex.value].city = item.name;
    cityList.value[cityChoseIndex.value].cityCode = item.id;
    cityList.value[cityChoseIndex.value].syId = item?.providerMapList[0]?.districtId;
    // 根据下标判断 修改完城市后需要修改的值
    if (cityChoseIndex.value == 0) {
      props.creatTripParma['beginCityName'] = item.name;
      props.creatTripParma['beginCityCode'] = item.id;
      props.creatTripParma['beginCityCodeSy'] = item?.providerMapList[0]?.districtId;

      if (props.creatTripParma.tripList?.length > 0) {
        props.creatTripParma.tripList[0].beginCityName = item.name;
        props.creatTripParma.tripList[0].beginCityCode = item.id;
        props.creatTripParma.tripList[0].beginCityCodeSy = item?.providerMapList[0]?.districtId;
      }

      props.creatTripParma.tripList[0]['tripDetailMapList'] = []

    } else if (cityChoseIndex.value + 1 == cityList.value.length) {
      props.creatTripParma['endCityName'] = item.name;
      props.creatTripParma['endCityCode'] = item.id;
      props.creatTripParma['endCityCodeSy'] = item?.providerMapList[0]?.districtId;

      props.creatTripParma.tripList[cityChoseIndex.value - 1]['endCityName'] =
        item.name;
      props.creatTripParma.tripList[cityChoseIndex.value - 1]['endCityCodeSy'] =
        item?.providerMapList[0]?.districtId;
      props.creatTripParma.tripList[cityChoseIndex.value - 1]['endCityCode'] =
        item.id;

      props.creatTripParma.tripList[cityChoseIndex.value - 1]['tripDetailMapList']  = []

    } else {
      props.creatTripParma.tripList[cityChoseIndex.value]['beginCityName'] =
        item.name;
      props.creatTripParma.tripList[cityChoseIndex.value]['beginCityCode'] =
        item.id;
      props.creatTripParma.tripList[cityChoseIndex.value]['beginCityCodeSy'] =
        item?.providerMapList[0]?.districtId;

      props.creatTripParma.tripList[cityChoseIndex.value - 1]['endCityName'] =
        item.name;
      props.creatTripParma.tripList[cityChoseIndex.value - 1]['endCityCode'] =
        item.id;
      props.creatTripParma.tripList[cityChoseIndex.value - 1]['endCityCodeSy'] =
        item?.providerMapList[0]?.districtId;
      
      props.creatTripParma.tripList[cityChoseIndex.value - 1]['tripDetailMapList']  = []
      props.creatTripParma.tripList[cityChoseIndex.value]['tripDetailMapList']  = []
    }
  }

  // 判断是否选择完毕并添加到数组
  if (cityChoseType.value != 'end') {
    addCity();
  }

  showCityPicker.value = false;

  searchParams.value.name = ''
}

const finishCityChose = ({ selectedOptions }) => {
  
  console.log('城市选择完成----->>>', selectedOptions, chosedCity.value);
  if(!selectedOptions[selectedOptions.length - 1]?.providerMapList) {
    showToast('暂不支持此城市!')
    return
  }

  if (cityChoseType.value == 'begin') {
    newBeginCity.value.name = selectedOptions[selectedOptions.length - 1].name;
    newBeginCity.value.citycode = selectedOptions[selectedOptions.length - 1].id;
    newBeginCity.value.syId = selectedOptions[selectedOptions.length - 1]?.providerMapList[0]?.districtId;
  } else if (cityChoseType.value == 'next') {
    newEndCity.value.name = selectedOptions[selectedOptions.length - 1].name;
    newEndCity.value.citycode = selectedOptions[selectedOptions.length - 1].id;
    newEndCity.value.syId = selectedOptions[selectedOptions.length - 1]?.providerMapList[0]?.districtId;
  } else {
    cityList.value[cityChoseIndex.value].city = selectedOptions[selectedOptions.length - 1].name;
    cityList.value[cityChoseIndex.value].cityCode = selectedOptions[selectedOptions.length - 1].id;
    cityList.value[cityChoseIndex.value].syId = selectedOptions[selectedOptions.length - 1]?.providerMapList[0]?.districtId;
    // 根据下标判断 修改完城市后需要修改的值
    if (cityChoseIndex.value == 0) {
      props.creatTripParma['beginCityName'] = selectedOptions[selectedOptions.length - 1].name;
      props.creatTripParma['beginCityCode'] = selectedOptions[selectedOptions.length - 1].id;
      props.creatTripParma['beginCityCodeSy'] = selectedOptions[selectedOptions.length - 1]?.providerMapList[0]?.districtId;

      if (props.creatTripParma.tripList?.length > 0) {
        props.creatTripParma.tripList[0].beginCityName = selectedOptions[selectedOptions.length - 1].name;
        props.creatTripParma.tripList[0].beginCityCode = selectedOptions[selectedOptions.length - 1].id;
        props.creatTripParma.tripList[0].beginCityCodeSy = selectedOptions[selectedOptions.length - 1]?.providerMapList[0]?.districtId;
      }
      props.creatTripParma.tripList[0]['tripDetailMapList'] = []

    } else if (cityChoseIndex.value + 1 == cityList.value.length) {
      props.creatTripParma['endCityName'] = selectedOptions[selectedOptions.length - 1].name;
      props.creatTripParma['endCityCode'] = selectedOptions[selectedOptions.length - 1].id;
      props.creatTripParma['endCityCodeSy'] = selectedOptions[selectedOptions.length - 1]?.providerMapList[0]?.districtId;

      props.creatTripParma.tripList[cityChoseIndex.value - 1]['endCityName'] =
        selectedOptions[selectedOptions.length - 1].name;
      props.creatTripParma.tripList[cityChoseIndex.value - 1]['endCityCodeSy'] =
        selectedOptions[selectedOptions.length - 1]?.providerMapList[0]?.districtId;
      props.creatTripParma.tripList[cityChoseIndex.value - 1]['endCityCode'] =
        selectedOptions[selectedOptions.length - 1].id;

      props.creatTripParma.tripList[cityChoseIndex.value - 1]['tripDetailMapList']  = []
    } else {
      props.creatTripParma.tripList[cityChoseIndex.value]['beginCityName'] =
        selectedOptions[selectedOptions.length - 1].name;
      props.creatTripParma.tripList[cityChoseIndex.value]['beginCityCode'] =
        selectedOptions[selectedOptions.length - 1].id;
      props.creatTripParma.tripList[cityChoseIndex.value]['beginCityCodeSy'] =
        selectedOptions[selectedOptions.length - 1]?.providerMapList[0]?.districtId;

      props.creatTripParma.tripList[cityChoseIndex.value - 1]['endCityName'] =
        selectedOptions[selectedOptions.length - 1].name;
      props.creatTripParma.tripList[cityChoseIndex.value - 1]['endCityCode'] =
        selectedOptions[selectedOptions.length - 1].id;
      props.creatTripParma.tripList[cityChoseIndex.value - 1]['endCityCodeSy'] =
        selectedOptions[selectedOptions.length - 1]?.providerMapList[0]?.districtId;

      props.creatTripParma.tripList[cityChoseIndex.value - 1]['tripDetailMapList']  = []
      props.creatTripParma.tripList[cityChoseIndex.value]['tripDetailMapList']  = []


    }
  }

  // 判断是否选择完毕并添加到数组
  if (cityChoseType.value != 'end') {
    addCity();
  }

  showCityPicker.value = false;
};

const cityList = ref<Array<ICity>>([]);
const cityDict = ref([]);

defineExpose({
  cityList,
  budgetErrorMessage,
  planErrorMessage
});
const showAddCity = () => {
  if (cityList.value?.length > 0) {
    if (showNextAddCity.value) {
      showFailToast('请先完善当前城市信息!');
      return;
    }
    showNextAddCity.value = true;
  } else {
    if (showFirstAddCity.value) {
      showFailToast('请先完善当前城市信息!');
      return;
    }
    showFirstAddCity.value = true;
    showNextAddCity.value = true;
  }
};

/*
  时间选择相关
*/
const showTimePicker = ref<boolean>(false);
const beginTime = ref('');
const endTime = ref('');

const minDate = ref(new Date(2024, 0, 1));
const maxDate = ref(new Date(2026, 0, 1));

const currentDate = ref<Array<any>>([]);

const currentDateIndex = ref<number>(0);
const choseTimeType = ref('');
const activeTimeItem = ref({});

const openTimePicker = (type: string, index: number) => {
  currentDateIndex.value = index;
  currentDate.value = [new Date().getFullYear(), new Date().getMonth() + 1, new Date().getDate()];
  choseTimeType.value = type;
  if (type == 'begin') {
    minDate.value = new Date(2024, 0, 1);
    maxDate.value = new Date(2026, 0, 1);
    if( beginTime.value) {
      currentDate.value = beginTime.value?.split('-');
    }
  } else if (type == 'end') {
    if (cityList.value.length > 0) {
      const dateArr = cityList.value[cityList.value.length - 1].date.split('-');
      minDate.value = new Date(parseInt(dateArr[0]), parseInt(dateArr[1]) - 1, parseInt(dateArr[2]));
    } else if (beginTime.value) {
      const dateArr = beginTime.value.split('-');
      minDate.value = new Date(parseInt(dateArr[0]), parseInt(dateArr[1]) - 1, parseInt(dateArr[2]));
    } else {
      minDate.value = new Date(2024, 0, 1);
    }

    maxDate.value = new Date(2026, 0, 1);
    if(endTime.value) {
      currentDate.value = endTime.value?.split('-');
    }
  } else {
    if (index == 0) {
      minDate.value = new Date(2024, 0, 1);
      if (cityList.value[1]?.date) {
        const dateArr = cityList.value[1].date.split('-');
        maxDate.value = new Date(parseInt(dateArr[0]), parseInt(dateArr[1]) - 1, parseInt(dateArr[2]));
      } else {
        maxDate.value = new Date(2026, 0, 1);
      }
    } else if (index + 1 == cityList.value.length) {
      const dateArr = cityList.value[cityList.value.length - 2].date.split('-');
      minDate.value = new Date(parseInt(dateArr[0]), parseInt(dateArr[1]) - 1, parseInt(dateArr[2]));
      maxDate.value = new Date(2026, 0, 1);
    } else {
      const dateArr = cityList.value[index - 1].date.split('-');
      minDate.value = new Date(parseInt(dateArr[0]), parseInt(dateArr[1]) - 1, parseInt(dateArr[2]));

      const dateArr2 = cityList.value[index + 1].date.split('-');
      maxDate.value = new Date(parseInt(dateArr2[0]), parseInt(dateArr2[1]) - 1, parseInt(dateArr2[2]));
    }
    currentDate.value = cityList.value[index].date?.split('-');
  }

  showTimePicker.value = true;
};

let cityItem: ITripList = {
  beginCityName: '',
  beginCityCodeSy: '',
  endCityCodeSy: '',
  endCityName: '',
  beginDate: '',
  endDate: '',
  tripDetailMapList: [],
  showMore: true,
};

const confirmTime = ({ selectedValues }) => {
  if (choseTimeType.value == 'begin') {
    beginTime.value = selectedValues.join('-');
  } else if (choseTimeType.value == 'end') {
    endTime.value = selectedValues.join('-');
  } else {
    cityList.value[currentDateIndex.value].date = selectedValues.join('-');

    // 根据下标判断 修改完时间后需要修改的值
    if (currentDateIndex.value == 0) {
      props.creatTripParma['beginDate'] = selectedValues.join('-');
      if (props.creatTripParma.tripList?.length > 0) {
        props.creatTripParma.tripList[0]['beginDate'] = selectedValues.join('-');

        props.creatTripParma.tripList[0]['tripDetailMapList'] = []
      }
    } else if (currentDateIndex.value + 1 == cityList.value.length) {
      props.creatTripParma['endDate'] = selectedValues.join('-');
      props.creatTripParma.tripList[currentDateIndex.value - 1]['endDate'] = selectedValues.join('-');

      props.creatTripParma.tripList[currentDateIndex.value - 1]['tripDetailMapList'] = []

    } else {
      props.creatTripParma.tripList[currentDateIndex.value]['beginDate'] = selectedValues.join('-');
      props.creatTripParma.tripList[currentDateIndex.value - 1]['endDate'] = selectedValues.join('-');

      props.creatTripParma.tripList[currentDateIndex.value - 1]['tripDetailMapList'] = []
      props.creatTripParma.tripList[currentDateIndex.value]['tripDetailMapList'] = []

    }
  }
  // 判断是否选择完毕并添加到数组
  if (choseTimeType.value != 'list') {
    addCity();
  }
  showTimePicker.value = false;
};

//------------ 选择火车站点 ---------
const showTrainPicker = ref(false)
const showChoseTrainPicker = ref(false)
const currentBeginCityCode = ref<number>()
const currentEndCityCode = ref<number>()

const currentTrainList = ref<any>([])
const currentType = ref('begin')

const currentTripIndex = ref<number>(0)
const currentTripDetailIndex = ref<number>(0)

const showTrainPickerFun = (beginCode: number,endCode: number, index:number, dIndex:number) => {
  currentTripIndex.value = index
  currentTripDetailIndex.value = dIndex

  currentBeginCityCode.value = beginCode
  currentEndCityCode.value = endCode
  showTrainPicker.value = true
}
const chosedTrainStation = async(type:string) => {
  currentType.value = type
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
  });
  await getTrainList(type)
  showChoseTrainPicker.value = true
}


// 根据原因id返回是否要展示填写具体原因

const isShowReasonText = (id?: number|string ) => {
  if (!id) return false
  return resonList.value.find(item => item.id == id)?.reasonInfo == '其他原因'
}

const getTrainList = async(type: string) => {
  
  const param = {
    cityId: type == 'begin' ? currentBeginCityCode.value : currentEndCityCode.value
  }
  // 查询火车三字码列表
  const res = await cityApi.trainStationGroupingByCityId(param)
  res[0].lowerList.forEach(item => {
    item.stationName = item.cityName
    item.stationCode = item.cityId
  })
  currentTrainList.value = res[0].lowerList || [];
}

const finishTrainStationChose = ({ selectedOptions }) => {
  if (currentType.value == 'begin') {
    props.creatTripParma.tripList[currentTripIndex.value].tripDetailMapList[currentTripDetailIndex.value]['startTrainCode'] = selectedOptions[1].stationCode
    props.creatTripParma.tripList[currentTripIndex.value].tripDetailMapList[currentTripDetailIndex.value]['startTrainName'] = selectedOptions[1].stationName
  } else {
    props.creatTripParma.tripList[currentTripIndex.value].tripDetailMapList[currentTripDetailIndex.value]['endTrainCode'] = selectedOptions[1].stationCode
    props.creatTripParma.tripList[currentTripIndex.value].tripDetailMapList[currentTripDetailIndex.value]['endTrainName'] = selectedOptions[1].stationName
 }
 showChoseTrainPicker.value = false;
}


//------------ 选择火车站点 ---------

const addCity = () => {
  let leg: number = cityList.value.length;
  if (leg > 0) {
    if (!newEndCity.value.citycode || !endTime.value) {
      return;
    }

    if (newEndCity.value.citycode == cityList.value[leg - 1].cityCode) {
      showToast('出发城市与目的城市一致!');
    }
    cityItem = {
      endCityCodeSy: newEndCity.value.syId,
      beginCityCodeSy: cityList.value[leg - 1].syId,
      beginCityCode: cityList.value[leg - 1].cityCode,
      endCityCode: newEndCity.value.citycode,
      beginCityName: cityList.value[leg - 1].city,
      endCityName: newEndCity.value.name,
      beginDate: cityList.value[leg - 1].date,
      endDate: endTime.value,
      tripDetailMapList: [],
      showMore: true,
      detailMap: true,
    };
    props?.creatTripParma?.tripList.push(cityItem);
    cityList.value.push({
      cityCode: newEndCity.value.citycode,
      city: newEndCity.value.name,
      syId: newEndCity.value.syId,
      date: endTime.value,
      active: false,
    });
    newEndCity.value = {
      name: '',
      citycode: '',
    };
    endTime.value = '';
    showNextAddCity.value = false;
  } else {
    // 一次添加两个
    if (!newBeginCity.value.citycode || !beginTime.value) {
      return;
    }
    if (!newEndCity.value.citycode || !endTime.value) {
      return;
    }
    if (newEndCity.value.citycode == newBeginCity.value.citycode) {
      showToast('出发城市与目的城市一致!');
    }
    cityList.value.push({
      cityCode: newBeginCity.value.citycode,
      city: newBeginCity.value.name,
      date: beginTime.value,
      syId: newBeginCity.value.syId,
      active: false,
    });

    cityItem = {
      endCityCodeSy: newEndCity.value.syId,
      beginCityCodeSy: newBeginCity.value.syId,
      beginCityCode: newBeginCity.value.citycode,
      endCityCode: newEndCity.value.citycode,
      beginCityName: newBeginCity.value.name,
      endCityName: newEndCity.value.name,
      beginDate: beginTime.value,
      endDate: endTime.value,
      tripDetailMapList: [],
      showMore: true,
      detailMap: true,
    };
    if (!props.creatTripParma.tripList) {
      props.creatTripParma.tripList = [];
      props.creatTripParma.tripList.push(cityItem);
    } else {
      props.creatTripParma.tripList.push(cityItem);
    }
    cityList.value.push({
      cityCode: newEndCity.value.citycode,
      city: newEndCity.value.name,
      date: endTime.value,
      syId: newEndCity.value.syId,
      active: false,
    });

    // startCityCodeStrs.value = newBeginCity.value;
    newBeginCity.value = {
      name: '',
      citycode: '',
      syId: '',
    };
    beginTime.value = '';
    newEndCity.value = {
      name: '',
      citycode: '',
      syId: '',
    };
    endTime.value = '';
  }

  showNextAddCity.value = false;
  showFirstAddCity.value = false;

  // 总行程的出发地、目的地、出发时间、到达时间、syid
  props.creatTripParma.beginDate = cityList.value[0].date;
  props.creatTripParma.beginCityCodeSy = cityList.value[0].syId;
  props.creatTripParma.beginCityCode = cityList.value[0].cityCode;
  props.creatTripParma.beginCityName = cityList.value[0].city;

  props.creatTripParma.endCityCode = cityList.value[cityList.value.length - 1].cityCode;
  props.creatTripParma.endDate = cityList.value[cityList.value.length - 1].date;
  props.creatTripParma.endCityCodeSy = cityList.value[cityList.value.length - 1].syId;
  props.creatTripParma.endCityName = cityList.value[cityList.value.length - 1].city;
};

// 时间显示格式转换  2024-04-23 -> 04/23
const formatDate = (date: string) => {
  if (!date) {
    return ''
  }
  const arr = date?.split('-');
  return arr?.join('/');
};


// 添加费用预算相关
const tripItem = ref<ITripList>({});

// 超标费用提示 预算超标
const errorAmountText = ref('预算超标');
const showNotice = ref(false)

const addMoney = (index: number, trip: any) => {
  if (props.isDetail) {
    return;
  }
  // 如果未添加返程信息,弹窗询问是否需要添加
  if(props.creatTripParma.beginCityCode != props.creatTripParma.endCityCode && !showNotice.value && !props.isChange) {
    showConfirmDialog({
      title: '提示',
      message: '您未填写返回出发地的行程计划，需要为您加上吗？',
      // theme: 'round-button',
      confirmButtonText: '加上吧',
      cancelButtonText: '不了',
    }).then(() => {
      // on close
      cityList.value = [...cityList.value, {
          cityCode: props.creatTripParma.beginCityCode,
          city: props.creatTripParma.beginCityName,
          syId: props.creatTripParma.beginCityCodeSy,
          date: props.creatTripParma.endDate,
        }]
        cityItem = {
          endCityCodeSy: props.creatTripParma.beginCityCodeSy,
          endCityCode: props.creatTripParma.beginCityCode,
          endCityName: props.creatTripParma.beginCityName,
          endDate: props.creatTripParma.endDate,

          beginCityCodeSy: cityList.value[cityList.value.length-2].syId,
          beginCityCode: cityList.value[cityList.value.length-2].cityCode,
          beginCityName: cityList.value[cityList.value.length-2].city,
          beginDate: cityList.value[cityList.value.length-2].date,

          tripDetailMapList: [],
          detailMap: true,
        };

        if (!props.creatTripParma.tripList) {
          props.creatTripParma.tripList = [];
          props.creatTripParma.tripList.push(cityItem);
        } else {
          props.creatTripParma.tripList.push(cityItem);
        }

        props.creatTripParma.endCityCode = cityList.value[cityList.value.length - 1].cityCode;
        props.creatTripParma.endDate = cityList.value[cityList.value.length - 1].date;
        props.creatTripParma.endCityCodeSy = cityList.value[cityList.value.length - 1].syId;
        props.creatTripParma.endCityName = cityList.value[cityList.value.length - 1].city;
        
        showNotice.value =  true
    }).catch(() => {
      // on cancel
      showNotice.value =  true

    });
  }


  tripItem.value = {
    personIdList: props.creatTripParma.travelerList.map((item) => item.travelUserSyId),
    travelApplyTripDetailList: cloneDeep(props.creatTripParma.travelerList),
    productName: undefined,
    productCode: '', //
    budgetAmount: 0, // 总金额
    excessiveReasonId: undefined, // 超标原因
    excessiveFlag: 0,
    insuranceFlag: true, // 是否购买保险
  };
  props.creatTripParma.tripList[index].tripDetailMapList
    ? (props.creatTripParma.tripList[index].tripDetailMapList = [
      ...props.creatTripParma.tripList[index].tripDetailMapList,
      tripItem.value,
    ])
    : (props.creatTripParma.tripList[index].tripDetailMapList = [tripItem.value]);

  getAllBugde();
  trip.showMore = true
};

// 删除行程
const delTrip = (index: number) => {
  showConfirmDialog({
    title: '确认要删除此行程吗',
    message: '删除行程无法恢复,其中的费用信息也会被删除,您确定吗?',
  })
    .then(() => {

      // 如果恰好只有一个行程 全部删除
      if(cityList.value?.length == 2) {
        cityList.value = [];
        props.creatTripParma.tripList = [];
        props.creatTripParma['beginDate'] =''
        props.creatTripParma['endDate'] =''

        props.creatTripParma['beginCityCode'] =''
        props.creatTripParma['beginCityCodeSy'] =''
        props.creatTripParma['beginCityName'] =''
        props.creatTripParma['endCityCode'] =''
        props.creatTripParma['endCityCodeSy'] =''
        props.creatTripParma['endCityName'] =''
        return
      }
      // on confirm
      // 删除中间城市 清空后边城市的行程
      if (index + 1 !== props.creatTripParma.tripList?.length) {
        props.creatTripParma.tripList[index + 1].tripDetailMapList = [];
      }

      cityList.value.splice(index + 1, 1);
      props?.creatTripParma?.tripList.splice(index, 1);

      // 删除中间城市导致 起始地-终点错误
      cityList.value.forEach((i: ICity, ii: number) => {
        if (ii > 0) {
          props.creatTripParma.tripList[ii - 1].beginCityName = cityList.value[ii - 1].city;
          props.creatTripParma.tripList[ii - 1].endCityName = cityList.value[ii].city;
          props.creatTripParma.tripList[ii - 1].beginCityCode = cityList.value[ii - 1].cityCode;
          props.creatTripParma.tripList[ii - 1].endCityCode = cityList.value[ii].cityCode;
          props.creatTripParma.tripList[ii - 1].beginCityCodeSy = cityList.value[ii - 1].syId;
          props.creatTripParma.tripList[ii - 1].endCityCodeSy = cityList.value[ii].syId;
          if (ii > 1) {
            props.creatTripParma.tripList[ii - 1].beginDate = cityList.value[ii - 1].date;
          } else {
            props.creatTripParma.tripList[ii - 1].beginDate = cityList.value[ii - 1].date;
          }
        }
      });

      if (cityList.value.length > 1) {
        props.creatTripParma['beginDate'] = cityList.value[0].date;
        props.creatTripParma['endDate'] = cityList.value[cityList.value.length - 1]?.date;
      } else {
        props.creatTripParma['beginDate'] = cityList.value[0].date;
        props.creatTripParma['endDate'] = cityList.value[cityList.value.length - 1].date;
      }
    })
    .catch(() => {
      // on cancel
    });
};

// 删除某条费用
const deleteItem = (list:any, index: number) => {
  showConfirmDialog({
    title: '确认要删除此条费用信息吗',
    message: '删除的费用信息无法恢复,您确定吗?',
  }).then(() => {
    list.splice(index, 1);
    getAllBugde();
  });
};

// 删除某个出差人
const delApplyItem = (index: number, dIndex: number, list:any, applyIndex: number) => {
  openPersonPopBudgeIndex.value = index;
  openPersonPopMoneyIndex.value = dIndex;

  list.splice(applyIndex, 1);
  getMemberBudget(
    props.creatTripParma.tripList[openPersonPopBudgeIndex.value],
    props.creatTripParma.tripList[openPersonPopBudgeIndex.value].tripDetailMapList[openPersonPopMoneyIndex.value],
    true,
  );
};

const allowBugde = ref<Array<ITripDetailMap>>([]);

// 切换选择保险
const switchInsurance = () => {
  activeNames.value = ['']
  getAllBugde()
}

// 计算所有行程的总费用
const getAllBugde = () => {
  allowBugde.value = [];
  props.creatTripParma.amountSum = 0;
  // 所有费用
  props.creatTripParma.tripList.forEach((item:any) => {
    allowBugde.value = [...allowBugde.value, ...(item.tripDetailMapList || [])];
  });
  allowBugde.value.forEach((item) => {
    if(item.insuranceFlag && item.insuranceAmount) {
      props.creatTripParma.amountSum += item.insuranceAmount * item.travelApplyTripDetailList?.length
    }
    props.creatTripParma.amountSum += (item.budgetAmount * 1) ?? 0;
  });
};
interface IbudgetDetail {
  amountSum: string;
  travelUserName: string;
  travelUserNo: string;
  travelUserSyId: string;
}
const budgetDetailData = ref<Array<IbudgetDetail>>([]);
// 获取具体费用明细
const getMoneyDetailList = () => {
  tripApi.memberAmountList(props.creatTripParma).then((res) => {
    budgetDetailData.value = res;
  });
}

const getMoneyDetailList2 = () => {
  getMoneyDetailList()
}

const showChoseApplyList = ref<boolean>(false);

const activeTravelList = ref<Array<ITraveler>>([]);

// 选择出行人当前行程的下标
const openPersonPopBudgeIndex = ref<number>(0);
// 选择出行人当前费用的下标
const openPersonPopMoneyIndex = ref<number>(0);

const openApplyChosePop = (index: number, dIndex: number) => {
  openPersonPopBudgeIndex.value = index;
  openPersonPopMoneyIndex.value = dIndex;

  showChoseApplyList.value = true;
  activeTravelList.value = cloneDeep(props.creatTripParma.travelerList);
  // todo 判断出行人员可选状态
  const currentList = props.creatTripParma?.tripList[openPersonPopBudgeIndex.value].tripDetailMapList[openPersonPopMoneyIndex.value].travelApplyTripDetailList;
  activeTravelList.value.forEach((item:any) => {
    item['checked'] = false;
    if (currentList && currentList.length > 0) {
      currentList.forEach((current) => {
        if (current.travelUserSyId == item.travelUserSyId) {
          item['checked'] = true;
        }
      });
    }
  });
};

const saveChosedPerson = () => {
  props.creatTripParma.tripList[openPersonPopBudgeIndex.value].tripDetailMapList[
    openPersonPopMoneyIndex.value
  ].travelApplyTripDetailList = activeTravelList.value.filter((item) => item.checked);
  showChoseApplyList.value = false;

  getMemberBudget(
    props.creatTripParma.tripList[openPersonPopBudgeIndex.value],
    props.creatTripParma.tripList[openPersonPopBudgeIndex.value].tripDetailMapList[openPersonPopMoneyIndex.value],
    true,
  );
};

// 获取出行方式
const travelList = ref();
const showTravelPicker = ref<boolean>(false);
const openTravelPicker = (index: number, dIndex: number) => {
  openPersonPopBudgeIndex.value = index;
  openPersonPopMoneyIndex.value = dIndex;
  showTravelPicker.value = true;
};

const travelPickerConfirm =  ({ selectedOptions }) => {
  props.creatTripParma.tripList[openPersonPopBudgeIndex.value].tripDetailMapList[
    openPersonPopMoneyIndex.value
  ].productName = selectedOptions[0]?.productName;
  props.creatTripParma.tripList[openPersonPopBudgeIndex.value].tripDetailMapList[
    openPersonPopMoneyIndex.value
  ].productCode = selectedOptions[0]?.productCode;
  props.creatTripParma.tripList[openPersonPopBudgeIndex.value].tripDetailMapList[
    openPersonPopMoneyIndex.value
  ].insuranceAmount = selectedOptions[0]?.insuranceAmount;
  props.creatTripParma.tripList[openPersonPopBudgeIndex.value].tripDetailMapList[
    openPersonPopMoneyIndex.value
  ].insuranceFlag = selectedOptions[0]?.insuranceAmount ? true : false;

  // 更换出行方式的时候 如果是火车、飞机删掉其他已经添加费用的人员
  // if(selectedOptions[0]?.productCode == '01' || selectedOptions[0]?.productCode == '02') {
  //   props.creatTripParma.tripList[openPersonPopBudgeIndex.value]?.tripDetailMapList?.map(item => {
  //     if(item.productCode == '01' || item.productCode == '02') {
  //       return item
  //     }
  //   })
  // }

  showTravelPicker.value = false;

  // 如果选择的是火车,展示火车站选择弹窗
  if (selectedOptions[0]?.productCode == '02') {
    
    currentBeginCityCode.value = props.creatTripParma.tripList[openPersonPopBudgeIndex.value].beginCityCode
    currentEndCityCode.value = props.creatTripParma.tripList[openPersonPopBudgeIndex.value].endCityCode

    cityApi.trainStationGroupingByCityId({cityId:currentBeginCityCode.value }).then(res => {
      props.creatTripParma.tripList[openPersonPopBudgeIndex.value].tripDetailMapList[openPersonPopMoneyIndex.value]['startTrainCode'] = res[0]?.mainStation?.stationCode
      props.creatTripParma.tripList[openPersonPopBudgeIndex.value].tripDetailMapList[openPersonPopMoneyIndex.value]['startTrainName'] = res[0]?.mainStation?.stationName
    })
    cityApi.trainStationGroupingByCityId({cityId:currentEndCityCode.value }).then(res => {
      props.creatTripParma.tripList[openPersonPopBudgeIndex.value].tripDetailMapList[openPersonPopMoneyIndex.value]['endTrainCode'] = res[0]?.mainStation?.stationCode
      props.creatTripParma.tripList[openPersonPopBudgeIndex.value].tripDetailMapList[openPersonPopMoneyIndex.value]['endTrainName'] = res[0]?.mainStation?.stationName
    })
    
    showTrainPickerFun(currentBeginCityCode.value, currentEndCityCode.value, openPersonPopBudgeIndex.value, openPersonPopMoneyIndex.value)
  }

  getMemberBudget(
    props.creatTripParma.tripList[openPersonPopBudgeIndex.value],
    props.creatTripParma.tripList[openPersonPopBudgeIndex.value].tripDetailMapList[openPersonPopMoneyIndex.value],
    true,
  );
};

// 超标原因
const resonList = ref([]);
const newResonList = ref([]);
const showResonPicker = ref<boolean>(false);

const openResonPicker = (index: number, dIndex: number, productCode: string) => {
  openPersonPopBudgeIndex.value = index;
  openPersonPopMoneyIndex.value = dIndex;
  if(productCode) {
    newResonList.value = resonList.value?.filter(item => item.productCode == productCode)
  }
  showResonPicker.value = true;
};

const resonPickerConfirm = ({ selectedOptions }) => {
  props.creatTripParma.tripList[openPersonPopBudgeIndex.value].tripDetailMapList[
    openPersonPopMoneyIndex.value
  ].excessiveReasonId = selectedOptions[0]?.id;
  props.creatTripParma.tripList[openPersonPopBudgeIndex.value].tripDetailMapList[
    openPersonPopMoneyIndex.value
  ].excessiveOtherReasonDesc = null

  showResonPicker.value = false;
};

// 获取预算费用明细
const memberBudgetParams = ref<MemberBudgetParams>({});
const getMemberBudget = (trip: ITripList, item: ITripDetailMap, reGet: boolean) => {

  if(!item.productCode) {
    return;
  }
  
  memberBudgetParams.value = {
    operatorUserCode: loginUser.value?.username,
    operatorUserDeptId: loginUser.value?.departmentCode,

    beginCityCode: trip.beginCityCode,
    beginCityName: trip.beginCityName,
    endCityName: trip.endCityName,
    endCityCode: trip.endCityCode,

    startDate: trip.beginDate,
    endDate: trip.endDate,
    productNo: item.productCode,
    insuranceFlag: item.insuranceFlag,
    memberList: item.travelApplyTripDetailList,
    budgetAmountBz: reGet ? 0 : item.budgetAmount == item.budgetAmountBz ? 0 : item.budgetAmount,
    excessiveFlag: item.excessiveFlag ? 1 : 0,
  };
  emit('show');

  tripApi.memberBudget(memberBudgetParams.value).then((res) => {
    // 如果输入了最小值
    if (res.minAmountBz) {
      item.budgetAmount = res.minAmountBz;
      showToast(`最低费用预算为${item.budgetAmount}!`);
    } else {
      item.budgetAmount = res?.budgetAmountSum || 0; // 可更改预算
      item.budgetAmountBz = res?.budgetAmountSumBz || 0; // 预算的最大值
    }

    item.budgetAmountDesc = res?.memberBudgetList;

    if (item.budgetAmount > item.budgetAmountBz) {
      item.excessiveFlag = 1;
    } else {
      item.excessiveFlag = 0;
      item.excessiveReasonId = '';
    }

    item?.travelApplyTripDetailList.forEach((ii) => {
      res?.memberList?.forEach((jj) => {
        if (ii.travelUserSyId == jj.travelUserSyId) {
          ii.differentialStandard = jj.differentialStandard;
        }
      });
    });

    // props?.creatTripParma?.travelerList.forEach((ii) => {
    //   res?.memberList?.forEach((jj) => {
    //     if (ii.travelUserSyId == jj.travelUserSyId) {
    //       ii.differentialStandard = jj.differentialStandard;
    //     }
    //   });
    // });

    getAllBugde();

    emit('hide');


    setTimeout(() => {
      getMoneyDetailList()
    }, 1000)
  }).catch((err) => {
    emit('hide')
  });
};

// 展示费用明细
const showBudgetDetail = (detail:any) => {
  budgetDetailModal.value = true
  activeBudgetDetail.value = detail.budgetAmountDesc
}
const budgetDetailModal = ref(false);
const activeBudgetDetail = ref<any>([])
const descColumns = [
  { align: 'center', title: '出差人员', dataIndex: 'travelUserName', key: 'travelUserName', width: '300px' },
  { align: 'center', title: '费用名称', dataIndex: 'productName', key: 'productName', width: '300px' },
  { align: 'center', title: '差标', dataIndex: 'differentialStandard', key: 'differentialStandard', width: '300px' },
  { align: 'center', title: '平台使用费', dataIndex: 'serviceAmount', key: 'serviceAmount', width: '300px' },
  // { align: 'center', title: '保险费', dataIndex: 'insuranceAmount', key: 'insuranceAmount', width: '300px' },
];



onMounted(async () => {
  // 获取省市
  const res = await tripApi.district({ 
    providerCode: 'VETECH',
    type: '1,2,3,4'
  });
  cityDict.value = res.children;

  // 获取出行方式
  travelList.value = await tripApi.dataList();

  // 超标原因
  const resonRes = await reasonApi.list({ pageNum: 1, reasonType: 20, pageSize: 50 });
  resonList.value = resonRes.records || [];

  if(!props.isChange && cityList.value.length == 0) {
    showAddCity()
  }
});


// ---------------------老预算选择开始
const choseBudgeDialog = ref<boolean>(false)

const choseBudgeForm = ref({
  budgetType: ''
})

const submitBudge = () => {

}

const chosedPerson = (item: ITraveler) => {
  choseBudgeForm.value.nickName = item.nickName
  choseBudgeForm.value.username = item.username

}

const showMoreBox = ref<Array<boolean>>([])

// ---------------------老预算选择结束


// hbc2 新预算----------

const choosedBudget = (item: any) => {
  choseBudgeDialog.value = false
  props.creatTripParma.haierBudgetPayOccupyRequest = item
}

// hbc2 新预算----------

</script>

<style lang="less" scoped>
@import url(./mobile.less);
.show-more-box {
  width: 22px;
  height: 22px;
  background: #fff;
  position: absolute;
  bottom: -10px;
  left: calc(50% - 11px);
  border-radius: 10px;
  padding: 4px;
}
.show-more-box-open {
  background-image: url('@/assets/image/trip/open.png');
  background-size: 100% 100%;
}
.show-more-box-close {
  background-image: url('@/assets/image/trip/retract.png');
  background-size: 100% 100%;
}
</style>