<script setup lang="ts">
import { WaterApplicationModalForm } from '@haierbusiness-front/common-libs';
import { Button as hButton, Modal as hModal, Table as hTable, message } from 'ant-design-vue';
import { computed, defineEmits, defineProps, ref, watch } from 'vue';
const props = defineProps<{
  visible: boolean;
  record?: any;
}>();
const emit = defineEmits(['cancel', 'ok']);

/**
 * @表单相关
 * */
const formData = ref<WaterApplicationModalForm>({
  applicationDate: '',
  type: '',
  deliveryArea: '',
  receiver: '',
  remark: '',
  deliveryAddress: '',
  contactPhone: '',
});
const confirmLoading = ref(false);

/**
 * @表格相关
 * */
const tableData = ref<any>([
  {
    key: '1',
    index: 1,
    category: '2加仑',
    unitPrice: 25,
    quantity: 0,
    amount: 0,
    remark: '2加仑330ml瓶装',
  },
  {
    key: '2',
    index: 2,
    category: '5加仑',
    unitPrice: 15,
    quantity: 0,
    amount: 0,
    remark: '5加仑330ml瓶装水33',
  },
  {
    key: '3',
    index: 3,
    category: '330ml瓶装水',
    unitPrice: 36,
    quantity: 0,
    amount: 0,
    remark: '330ml瓶装水330m',
  },
]);

const tableProps = ref<any>({
  scroll: { x: 'max-content' },
  dataSource: tableData.value,

  pagination: computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: 100,
    current: 1,
    pageSize: 10,
    style: { justifyContent: 'center' },
  })),
  columns: [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
    },
    {
      title: '送水单号',
      dataIndex: 'category',
      key: 'category',
      sorter: (a: any, b: any) => a.category.localeCompare(b.category),
    },
    {
      title: '类型',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      sorter: (a: any, b: any) => a.unitPrice - b.unitPrice,
    },
    {
      title: '申请单位',
      dataIndex: 'quantity',
      key: 'quantity',
      sorter: (a: any, b: any) => a.quantity - b.quantity,
    },
    {
      title: '申请时间',
      dataIndex: 'quantity',
      key: 'quantity',
      sorter: (a: any, b: any) => a.quantity - b.quantity,
    },
    {
      title: '送水时间',
      dataIndex: 'amount',
      key: 'amount',
      sorter: (a: any, b: any) => a.amount - b.amount,
    },
    {
      title: '送水地址',
      dataIndex: 'amount',
      key: 'amount',
      sorter: (a: any, b: any) => a.amount.localeCompare(b.amount),
    },
    {
      title: '联系电话',
      dataIndex: 'amount',
      key: 'amount',
      sorter: (a: any, b: any) => a.amount - b.amount,
    },
    {
      title: '5加仑',
      dataIndex: 'remark',
      key: 'remark',
      children: [
        {
          title: '申请数量',
          dataIndex: 'remark',
          key: 'remark',
        },
        {
          title: '实际数量',
          dataIndex: 'remark',
          key: 'remark',
        },
      ],
    },
    {
      title: '339ml瓶装水',
      dataIndex: 'remark',
      key: 'remark',
      children: [
        {
          title: '申请数量',
          dataIndex: 'remark',
          key: 'remark',
        },
        {
          title: '实际数量',
          dataIndex: 'remark',
          key: 'remark',
        },
      ],
    },
    {
      title: '申请总数量',
      dataIndex: 'remark',
      key: 'remark',
    },
    {
      title: '核对总数量',
      dataIndex: 'remark',
      key: 'remark',
    },
    {
      title: '桶',
      dataIndex: 'remark',
      key: 'remark',
      children: [
        {
          title: '应交数量',
          dataIndex: 'remark',
          key: 'remark',
        },
        {
          title: '实交数量',
          dataIndex: 'remark',
          key: 'remark',
        },
        {
          title: '欠数量',
          dataIndex: 'remark',
          key: 'remark',
        },
      ],
    },
    {
      title: '状态',
      dataIndex: 'remark',
      key: 'remark',
      sorter: (a: any, b: any) => a.remark.localeCompare(b.remark),
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      sorter: (a: any, b: any) => a.remark.localeCompare(b.remark),
    },
  ],
});
/**
 * @弹窗相关
 * */

const localVisible = ref(false);
watch(
  () => props.visible,
  (newVal) => {
    localVisible.value = newVal;
  },
  { immediate: true },
);

const handleCancel = () => {
  emit('cancel');
};

const handleOk = () => {
  confirmLoading.value = true;
  message.success('暂无接口');
  setTimeout(() => {
    confirmLoading.value = false;
  }, 1000);
};
</script>

<template>
  <div inert class="water-info-modal">
    <h-modal
      :title="'明细'"
      v-model:open="localVisible"
      @cancel="handleCancel"
      @ok="handleOk"
      width="80%"
      :confirmLoading="confirmLoading"
    >
      <template #footer>
        <div style="display: flex; justify-content: center">
          <h-button size="large" type="primary" @click="handleOk" :loading="confirmLoading">提交</h-button>
          <h-button size="large" @click="handleCancel">取消</h-button>
        </div>
      </template>
      <!-- 主体内容 -->
      <div style="height: 600px; overflow: auto">
        <div class="title-text">数据列表</div>
        <h-table v-bind="tableProps" size="middle">
          <template #bodyCell="{ column, record }"> </template>
        </h-table>
      </div>
    </h-modal>
  </div>
</template>

<style lang="less" scoped>
:deep(.ant-form-item) {
  margin-bottom: 0;
}
.title-text {
  font-size: 12px;
  margin: 10px 0;
  font-weight: bold;
  color: #606266;
}
</style>
