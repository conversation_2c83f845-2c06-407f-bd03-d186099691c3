<script lang="ts" setup>
import { Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
     Input as hInput, Textarea as hTextarea, Upload as hUpload, Button as hButton } from 'ant-design-vue';
import { computed, ref, watch } from "vue";
import type { Ref } from "vue";
import { UploadOutlined, UserOutlined, NotificationOutlined, AppstoreOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons-vue';
import { fileApi } from '@haierbusiness-front/apis';
import { message,Upload } from 'ant-design-vue';
import type { UploadProps } from 'ant-design-vue';

import {
  IEnterprise
} from '@haierbusiness-front/common-libs';

interface IReason {
  reasonInfo?: string;
  reasonType?: number
}
interface Props {
    show: boolean;
    data: IReason | null;
}

const props = withDefaults(defineProps<Props>(), {
show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: IReason = {
  reasonInfo: '',
  reasonType: 20
};

const rules = {
  reasonType: [{ required: true, message: "请选择原因类型！" }],
  reasonInfo: [{ required: true, message: "请输入原因！" }],
  reasonCode: [{ required: true, message: "请输入原因编码！" }],
  productCode:[{ required: true, message: "请选择关联产品！" }],
};

const enterprise: Ref<IReason> = ref(
({ ...props.data } as IReason) || defaultData
);

watch(props, (newValue) => {
    enterprise.value = ({ ...newValue.data } as IReason) || defaultData;
});

const emit = defineEmits(["cancel", "ok"]);

const visible = computed(() => props.show);

const handleOk = () => {
  if (!enterprise.value.reasonType) {
    enterprise.value.reasonType = 20
  }
confirmLoading.value = true;
from.value
    .validate()
    .then(() => {
    enterprise.value.files && enterprise.value.files.length > 0 ?enterprise.value.iconUrl  = enterprise.value.files[0].filePath || enterprise.value.files[0].thumbUrl : '';
    emit("ok", enterprise.value, () => {
        confirmLoading.value = false;
    });
    // 不管是否成功 延时一秒关闭loading
    setTimeout(() => {
      confirmLoading.value ? confirmLoading.value = false: '';
    }, 1000)
    })
    .catch(() => {
        confirmLoading.value = false;
    });
};
const uploadLoading = ref(false);

const baseUrl = import.meta.env.VITE_BUSINESS_URL;

</script>

<template>
    <h-modal
      v-model:open="visible"
      :title="enterprise.id ? '编辑原因' : '新增原因'"
      :width="600"
      @cancel="$emit('cancel')"
      :confirmLoading="confirmLoading"
      @ok="handleOk"
    >
      <h-form
        ref="from"
        :model="enterprise"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
        :rules="rules"
      >
        <h-form-item label="原因类型" name="reasonType" >
          <h-select ref="select" v-model:value="enterprise.reasonType" allow-clear>
              <h-select-option :value="10">
                未预订原因
              </h-select-option>
              <h-select-option :value="20">
                超标原因
              </h-select-option>
            </h-select>
        </h-form-item>

        <h-form-item v-if="enterprise.reasonType != 10" label="关联产品" name="productCode" >
          <h-select ref="select" v-model:value="enterprise.productCode" allow-clear>
              <h-select-option value="01">
                飞机
              </h-select-option>
              <h-select-option value="02">
                火车
              </h-select-option>
              <h-select-option value="04">
                酒店
              </h-select-option>
            </h-select>
        </h-form-item>
        

        <h-form-item label="原因编码" name="reasonCode">
          <h-input v-model:value="enterprise.reasonCode" />
        </h-form-item>

        <h-form-item label="原因" name="reasonInfo">
          <h-input v-model:value="enterprise.reasonInfo" />
        </h-form-item>

      </h-form>
    </h-modal>
</template>


<style lang="less" scoped>
.important {
color: red;
}
</style>
  