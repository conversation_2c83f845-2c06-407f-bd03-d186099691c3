import { IApplicationInfoRequest, IApplicationInfo, IApplicationLoginUrlRequest, IPageResponse, IApplicationInfoSaveRequest, IApplicationInfoUpdateRequest, IApplicationInfoDeleteRequest, IApplicationLinkResourceRequest } from '@haierbusiness-front/common-libs'
import { get, post } from '../request'

export const applicationApi = {

    /**
     * 查询应用信息
     */
    list: (params: IApplicationInfoRequest): Promise<IPageResponse<IApplicationInfo>> => {
        return get('system/api/application/list', params)
    },

    /**
     * 新增应用
     */
    save: (params: IApplicationInfoSaveRequest): Promise<void> => {
        return post('system/api/application/save', params)
    },

    /**
     * 修改应用
     */
    update: (params: IApplicationInfoUpdateRequest): Promise<void> => {
        return post('system/api/application/update', params)
    },

    /**
     * 删除应用
     */
    delete: (params: IApplicationInfoDeleteRequest): Promise<void> => {
        return post('system/api/application/delete', params)
    },

    /**
     * 关联资源
     */
    linkResource: (params: IApplicationLinkResourceRequest): Promise<void> => {
        return post('system/api/application/link/resource', params)
    },

    /**
     * 获取应用登录地址
     */
    loginUrl: (params: IApplicationLoginUrlRequest): Promise<string> => {
        return get('system/api/application/login/url', params)
    },
}