<script setup lang="ts">
import { computed, ref } from 'vue';
import { usePortalStore } from '../store';
import { message } from 'ant-design-vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
  },
  processNotice: {
    type: String,
    default: '',
  },
});
const emit = defineEmits(['update:modelValue']);

const store = usePortalStore();

const modelOpen = computed({
  get: () => props.modelValue,
  set: (v) => emit('update:modelValue', v),
});

const agree = async () => {
  store.consultantModalOpen = true;
  store.instructionModalOpen = false;
};
</script>

<template>
  <a-modal :width="1056" v-model:open="modelOpen" class="mice-bid-instruction-modal">
    <!-- 使用须知 -->
    <div v-html="processNotice"></div>

    <template #footer>
      <div class="flex-center">
        <a-button type="primary" @click="agree" :style="{ width: '88px', borderRadius: '4px' }">同意</a-button>
      </div>
    </template>
  </a-modal>
</template>

<style lang="less">
.mice-bid-instruction-modal {
  .ant-modal-content {
    background: linear-gradient(181deg, #e4efff 0%, #ffffff 200px, #ffffff 100%) !important;
    border-radius: 16px !important;
    padding: 20px 64px;

    .ant-modal-header {
      background: transparent;
      .ant-modal-title {
        text-align: center;
        font-weight: 500;
        font-size: 24px;
        color: #1d2129;
      }
    }
  }
  .ant-modal-footer {
    margin-top: 28px;
  }
}
</style>
