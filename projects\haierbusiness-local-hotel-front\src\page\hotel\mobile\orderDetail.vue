
<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import type { Ref } from 'vue';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

import { localHotelApi } from '@haierbusiness-front/apis';
import { LocalHotelRoomTypeEnum, LocalHotelBreakfastTypeEnum, LocalHotelBedTypeEnum } from '@haierbusiness-front/common-libs';

import {
  RHotelParams,
  RpayType,
  ROrderPayMentStateEnum,
  ROrderPayMentStateEnumColor,
  ROrderApprovalStateEnum,
  ROrderApprovalStateEnumColor,
  ROrderStateEnum,
  ROrderStateEnumColor,
  RHotel,
  BizOrderSource
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { Item } from 'ant-design-vue/es/menu';
import { showFailToast } from 'vant';


const router = getCurrentRouter();

const route = ref(getCurrentRoute());
dayjs.extend(relativeTime)

const orderCode = route.value?.query?.orderCode;

const priceToYuan = (value: number) => {
  if (!value) return 0;
  let yuan: number = value / 100;
  yuan = yuan.toFixed(2) * 1;
  return yuan;
};

const businessList = import.meta.env.VITE_BUSINESSTRAVEL_URL;
const store = applicationStore();

const { loginUser } = storeToRefs(store);

const detail = ref<RHotel>({
  //业务申请人
  applicant: {
    email: loginUser.value?.email, //联系人邮箱
    mobile: loginUser.value?.phone, //联系人电话
    name: loginUser.value?.nickName, //联系人名称,
    username: loginUser.value?.username, //联系人工号
    departmentName: loginUser.value?.departmentName || loginUser.value?.enterpriseName,
    orderCode: loginUser.value?.username, //联系人工号
    phone: loginUser.value?.phone, //联系人电话
  },
  applicantId: loginUser.value?.username, //业务申请人工号
  applyCause: '', //申请事由
  businessAim: '', //业务目标
  //预算信息
  budget: {
    paymentType: '',
    paymentCard: '', //酒店储值卡
  },
  hotelInfo: {
    fullname: '',
    discountPolicy: ''
  },
  cateTypeInfo: {
    name: ''
  },
  payType: '',
  cateTypeId: 0, //餐类ID
  cateTypeText: '',
  consumptionSeat: 0, //餐位费；分； 用于计算预算金额
  consumptionStandard: undefined, //就餐标准 含酒水。PS：所有人加起来的
  eatingTime: '', //就餐时间
  eatingDay: '', //就餐日期
  hotelId: '', //酒店ID
  //为选择内部酒店原因
  outerHotel: {
    reason: '1',
    remark: '',
  },
  //通知信息
  notifying: {
    email: '',
    needEmail: 0, // 0, //是否邮件通知 0：否；1：是
    mobile: '',
    needCall: 0, //0 //是否电话通知 0：否；1：是
    needSms: 0, // 0, //是否短信通知 0：否；1：是
    orderCode: '',
    phone: '',
  },
  orderCode: '',
  //经办人
  owner: {
    email: loginUser.value?.email, //联系人邮箱
    mobile: loginUser.value?.phone, //联系人电话
    name: loginUser.value?.nickName, //联系人名称,
    username: loginUser.value?.username, //联系人工号
    departmentName: loginUser.value?.departmentName || loginUser.value?.enterpriseName,
    orderCode: loginUser.value?.username, //联系人工号
    phone: loginUser.value?.phone, //联系人电话
  },
  ownerId: loginUser.value?.username, //经办人工号
  providerCode: '', //供应商编码
  remark: '', //禁忌和补充说明
  serviceFee: 0, //服务费率
  //订单招待信息
  treatInfo: {
    accompanyCount: undefined, //陪同人数
    accompanyLeader: '', //我方主要陪同领导
    guestCompany: '', //来宾单位
    guestCount: undefined, //来宾人数
    mainGuestNames: '', //主宾姓名，可多个人
    mainGuestPosition: '', //主宾职务
    needParking: 0, // 0, //是否预留车位，0：否；1：是；
    needSeatCard: 0, // 0, //需要座牌
    orderCode: '',
    seatOrderId: 0, //upload_file ID，座次文件ID
    signerMobile: '', //签单人手机
    signerName: '', //签单人姓名
    vehicleNo: '', //车牌号，可多个
  },
  type: 1, //订单类型 1:订餐 , 2:特产
  workingLunchCount: undefined, //工作餐提取人数
  workingLunchFee: 0, //工作餐金额 工作餐提取人数 × 50
  // platType: '1', // 客户端类型 0：PC 1：H5
  businessFlag: '', // 因公因私 0：因公 1：因私
});

const getDetail = (orderCode: string) => {
  const params = {
    orderCode,
  };

  localHotelApi.orderDetail(params).then((res) => {
    detail.value = res.data;
  });
};

const goBack = () => {
  router.back(-1);
};

const goToLog = () => {
  router.push({ path: '/hotel/order/orderLog', query: { orderCode: orderCode } });
};

const processUrl = import.meta.env.VITE_BUSINESS_PROCESS_URL;

const goToApproval = () => {
  if (!detail.value?.processCode) {
    showFailToast('暂无审批记录!');
    return
  }
  const url = processUrl + `?code=${detail.value?.processCode}#/details`
  window.location.href = url;
}


watch(
  () => orderCode,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);
</script>
<template>
  <div class="order-detail">

    <van-cell-group inset class="mb-20">
      <van-cell title="订单编号" :value="detail?.applicant.orderCode"></van-cell>
      <van-cell title="支付方式"
        :value="detail?.payType == '1' ? `${RpayType[detail?.payType]}(预算支付)` : RpayType[detail?.payType]"></van-cell>
      <van-cell title="订单来源">
        <template #value>
          <div>{{ BizOrderSource[detail?.orderSource] }}</div>
        </template>
      </van-cell>
    </van-cell-group>

    <van-cell-group inset class="mb-20">
      <van-cell title="酒店名称" :value="detail?.fullname"></van-cell>
      <van-cell title="入住日期" :value="detail?.checkIn"></van-cell>
      <van-cell title="离店日期" :value="detail?.checkOut"></van-cell>

      <van-cell title="订单预算金额">
        <template #value>
          <span class="color-red">¥{{ detail?.budgetAmount / 100 }}
            <!-- <span>超标请现付</span>  -->
          </span>
        </template>
      </van-cell>
      <van-cell title="实际金额">
        <template #value>
          <span class="color-red">¥{{ detail?.actualAmount / 100 }}
            <!-- <span>超标请现付</span>  -->
          </span>
        </template>
      </van-cell>
      <van-cell title="房间信息" class="room-list">
        <template #label>

          <div class="room-item-con mb-20" v-for="(room, index) in detail?.orderRooms" :key="room.id">
            <div class="item-label-title flex">
              <span class="mr-5 blue-text">{{ room.name }}</span>
              <span class="mr-5">{{ LocalHotelRoomTypeEnum[room.roomInfo.type] }}</span>
              <span>[{{ LocalHotelBedTypeEnum[room.roomInfo.bedType] }} {{ room.roomInfo.bedName }}]</span>
            </div>
            <div class="item-lable-more flex">
              <span class="shuliang">{{ room.num }}间{{ dayjs(detail?.checkOut).diff(detail?.checkIn, 'day') }}晚</span>
              <span>{{ LocalHotelBreakfastTypeEnum[room.roomInfo.breakfastType] }} (¥{{ room.roomInfo.breakfastPrice / 100
              }})</span>
              <van-divider vertical />
              <span>{{ room.roomInfo.hasInternet ? '有宽带' : '无宽带' }}</span>
            </div>
            <div class="item-label-price flex justify-content-between">
              <div class="flex align-items-center">
                <span class="item-price mr-5">¥<span>{{ (room.roomPrice[0].priceInfo.individualPrice * room.num) / 100
                }}</span></span>
              </div>

            </div>
          </div>


        </template>
      </van-cell>
    </van-cell-group>


    <!-- 因公 -->
    <van-cell-group inset class="mb-20">
      <!-- <van-cell v-if="detail?.payType == 1" title="来宾单位" :value="detail?.treatInfo.guestCompany"></van-cell>
      <van-cell v-if="detail?.payType == 1" title="主宾姓名" :value="detail?.treatInfo.guestNames"></van-cell> -->
      <van-cell title="入住人数" :value="detail?.treatInfo.guestCount + '人'"></van-cell>
      <van-cell title="期望保留时间" :value="detail?.keepTime"></van-cell>

      <!-- <van-cell title="需要座牌">

        <template #value>
          <span  class="color-red">{{detail?.treatInfo.needSeatCard ? '需要' :'不需要'}}</span>
        </template>
      </van-cell>
      <van-cell title="座次图">
        <template #value>
          <a v-if="detail?.treatInfo.seatOrderId" class="link-type" :href="`${businessList}/api/common/v1/file/download/${detail?.treatInfo.seatOrderId}`"
                  target="_blank">座次文件</a>
        </template>
      </van-cell> -->


      <van-cell title="预留车位">
        <template #value>
          <span class="color-red">{{ detail?.treatInfo.needParking ? '预留' : '不预留' }}</span>
        </template>
      </van-cell>
      <van-cell v-if="detail?.treatInfo.needParking" title="车牌号" :label="detail?.treatInfo.vehicleNo || ''">

      </van-cell>
    </van-cell-group>

    <van-cell-group inset class="mb-20">
      <van-cell title="备注">
        <template #label>
          <van-text-ellipsis style="word-break: break-all;" :content="detail?.remark" expand-text="展开"
            collapse-text="收起" />
        </template>
      </van-cell>
      <van-cell title="客服补充说明" :label="detail?.serverRemark || '无'"></van-cell>
      <van-cell title="审批状态" v-if="detail?.payType == '1'" :value="detail?.eatingTime">
        <template #value>
          <van-tag plain :color="ROrderApprovalStateEnumColor[detail?.approvalState]" size="medium">{{
            ROrderApprovalStateEnum[detail?.approvalState] || ''
          }}</van-tag>
        </template>
      </van-cell>
      <van-cell title="支付状态" v-if="detail?.payType == '1'" :value="detail?.eatingTime">
        <template #value>
          <van-tag plain :color="ROrderPayMentStateEnumColor[detail?.paymentState]" size="medium">{{
            ROrderPayMentStateEnum[detail?.paymentState] || ''
          }}</van-tag>
        </template>
      </van-cell>
      <van-cell title="订单状态" :value="detail?.eatingTime">
        <template #value>
          <van-tag plain :color="ROrderStateEnumColor[detail?.state]" size="medium">{{
            ROrderStateEnum[detail?.state] || ''
          }}</van-tag>
        </template>
      </van-cell>
    </van-cell-group>

    <van-cell-group inset class="mb-20">
      <van-cell title="经办人" :value="detail?.owner.name"></van-cell>
      <van-cell title="手机号" :value="detail?.owner.mobile"></van-cell>
      <van-cell title="联系电话" :value="detail?.owner.phone"></van-cell>
      <van-cell title="邮箱" :value="detail?.owner.email"></van-cell>


    </van-cell-group>
    <van-cell-group inset class="mb-20">
      <van-cell title="业务申请人" :value="detail?.applicant.name"></van-cell>
      <van-cell title="手机号" :value="detail?.applicant.mobile"></van-cell>
      <van-cell title="联系电话" :value="detail?.applicant.phone"></van-cell>
      <van-cell title="邮箱" :value="detail?.applicant.email"></van-cell>
      <!-- <van-cell title="申请事由" :label="detail?.applyCause"></van-cell> -->
    </van-cell-group>

    <van-cell-group inset class="mb-30">
      <van-cell :title="detail?.payType == '1' ? '签单人' : '联系人'" :value="detail?.treatInfo.signerName"></van-cell>
      <van-cell title="手机号" :value="detail?.treatInfo.signerMobile"></van-cell>
    </van-cell-group>

    <van-row style="height: 40px; color: rgb(36 111 255);" justify="space-around" align-itemts="center">
      <van-col span="8" @click="goToLog" class="flex-center">订单操作日志</van-col>
      <van-col span="8" @click="goToApproval" class="flex-center">订单审批详情</van-col>

    </van-row>
  </div>
</template>

<style lang="less" scoped>
@import url(./common.less);

.room-item-con {
  width: 100%;
  height: 164px;
  background: #F6F7F9;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .item-label-title {
    font-weight: 500;
    font-size: 28px;
    color: #262626;
    line-height: 40px;
    display: flex;
    align-items: baseline;

    .blue-text {
      color: #2681FF;
      font-size: 28px;
    }
  }

  .item-lable-more {
    font-weight: 400;
    font-size: 24px;
    color: #595959;
    line-height: 33px;

    .shuliang {
      font-weight: 500;
      font-size: 24px;
      color: #262626;
      line-height: 33px;
      margin-right: 20px;
    }
  }

  .shu {
    border-color: #D9D9D9;
  }

  .item-price {
    color: rgba(255, 83, 57, 1);
    font-size: 20px;

    >span {
      font-size: 28px;
    }
  }

  .shouri {
    font-weight: 400;
    font-size: 22px;
    color: #8C8C8C;
    line-height: 30px;
  }

  .item-label-price {
    .blue-text {
      font-weight: 400;
      font-size: 22px;
      color: #2681FF;
      line-height: 30px;
      margin-left: 16px;
    }
  }
}</style>