<script lang="ts" setup>
import { computed, onMounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';
import outPersonModal from './components/outPersonModal.vue';
import budgetModal from './components/budgetModal.vue';
import budgetDetailModal from './components/budgetDetailModal.vue';
import budgetModalShow from './components/budgetModalShow.vue';

import travelStandardsModal from './components/travelStandardsModal.vue';

import baseInfo from './components/baseInfo.vue';
import planBudge from './components/planBudge.vue';
import fileUpload from './components/fileUpload.vue';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { TabsProps } from 'ant-design-vue/es/tabs';
import tripDetail from './components/tripDetail.vue';


const route = ref(getCurrentRoute());

import {
  Anchor as hAnchor,
  Button as hButton,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Row as hRow,
  Col as hCol,
  Dropdown as hDropdown,
  Menu as hMenu,
  MenuItem as hMenuItem,
  Modal as hModal,
  Collapse as hCollapse,
  CollapsePanel as hCollapsePanel
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { tripApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
const store = applicationStore();
const { loginUser } = storeToRefs(store);
const planBudgeRef = ref('');

const creatTripParma = ref<ICreatTrip>({
  tripList: [],
  travelerList: [
    {
      travelUserName: loginUser.value.nickName,
      travelUserSyId: loginUser.value?.username,
      travelUserDeptName: loginUser.value?.departmentName,
      travelUserDeptId: loginUser.value?.departmentCode,
      travelUserNo: loginUser.value?.username,
      username: loginUser.value?.username,
      travelUserType: '0',
      mainFlag: '1',
    },
  ],
  fileList: [],
  travelReason: undefined,
  travelReserveFlag: 1,
  travelUserName: loginUser.value.nickName,
});

const anchorItems = [
  {
    key: '1',
    href: '#base-info',
    title: '基本信息',
  },
  {
    key: '2',
    href: '#plan-budget',
    title: '行程计划与费用预算',
  },
  {
    key: '3',
    href: '#file',
    title: '附件',
  },
];

const chosedNow = ref<string>('now');
const tabVersion = (key: number) => {

  let version = key==1? 'now' :'changeing'
  if (chosedNow.value == version) {
    return;
  }
  chosedNow.value = version;
  if (version == 'now') {
    reloadPage(applyId);
  } else {
    reloadPage(changeNow.value.id);
  }
};

// 用户选择
const params = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});
// 出差人
const name = ref('');
const userNameChange = (userInfo: IUserInfo) => {
  name.value = userInfo?.nickName ?? '';
};
const handleClick: AnchorProps['onClick'] = (e, link) => {
  e.preventDefault();
  console.log(link);
};

const info = ref();
const baseInfoCom = ref();

//#region 为了表单临时写的一些变量    后续需要删除整合到别的地方

const isTravel = ref(1);


const headers = {
  authorization: 'authorization-text',
};
// 上传附件结束

const checked = ref<boolean>(false);

//#endregion

// 打开预算弹窗
const budget = ref();
const openBudgetModal = () => {
  budget?.value.show();
};

//预算详情弹窗

const budgetDetailDialog = ref();
const openBudgetDetailShow = (res) => {
  budgetDetailDialog?.value.show(res || {});
};

// 打开预算明细弹窗
const budgetDetail = ref();
interface IbudgetDetail {
  amountSum: string;
  travelUserName: string;
  travelUserNo: string;
  travelUserSyId: string;
}
const budgetDetailData = ref<Array<IbudgetDetail>>([]);
const openBudgetDetailModal = (data: Array<IbudgetDetail>) => {
  budgetDetailData.value = data;
  budgetDetail?.value.show();
};

// 差旅标准弹窗
const travelStandards = ref();
const openTravelStandardslModal = () => {
  let codeList = creatTripParma.value?.travelerList?.filter((item:any) => item.travelUserType == 0).map(item2 => item2.travelUserNo)
  let nameList = creatTripParma.value?.travelerList?.filter((item:any) => item.travelUserType == 0).map(item2 => item2.travelUserName)

  // 判断经办人的工号是否在出差人工号列表里面
  if(codeList.indexOf(loginUser.value?.username)== -1 ) {
    codeList = [loginUser.value?.username, ...codeList]
    nameList = [loginUser.value?.nickName, ...nameList]
  }

  travelStandards?.value.show(codeList, nameList);
  // travelStandards?.value.show();
};

// 外部联系人弹窗
const outPerson = ref();
const outPersonOpen = () => {
  outPerson?.value.show();
};
// 创建申请单请求参数


// 根据部门、个人查询预算
const {
  data: applyCreatData,
  run: applyCreatiRun,
  loading: applyCreatLoading,
} = useRequest(tripApi.applyCreat, {
  defaultParams: [creatTripParma.value],
});
// 提交申请单
const addApplyForm = () => {
  applyCreatiRun(creatTripParma.value);
};

const processUrl = import.meta.env.VITE_BUSINESS_PROCESS_URL

// 审批流页面
const goToApproval = () => {
  const url = processUrl + `?code=${creatTripParma.value.workFlowId}#/details`
  window.open(url);
}

// 实际行程

const travelerList = ref<any>([])
const showRealTrip = ref(false);

const confirmTrip = ref<any>([])
const getAllRealTrips = async() => {
  let results = await Promise.all(travelerList.value.map(item => {
    if (item.travelUserNo) {
      const params = {
        applyId: applyId,
        userCode: item.travelUserNo
      }
      return tripApi.getRealTrip(params)
    }else {
      return ''
    }
  }));
  results = results.filter(item => item)
  console.log('getAllRealTrips------->>>', results)

  
  results?.forEach((item, index) => {
    item.cityList= []
    item?.tripList?.forEach((element: any, eindex: number) => {
      element.beginCityCode = element.realBeginCityCode
      element.beginCityName = element.realBeginCityName
      element.beginDate = element.realBeginDate
      element.endCityCode = element.realEndCityCode
      element.endCityName = element.realEndCityName
      element.endDate = element.realEndDate
      element.tripDetailMapList = []

      if (eindex == 0) {
        item.cityList.push({
          cityCode: element.beginCityCode,
          city: element.beginCityName,
          syId: element.beginCityCodeSy,
          date: element.beginDate,
        });
      }
      item.cityList.push({
        cityCode: element.endCityCode,
        city: element.endCityName,
        syId: element.endCityCodeSy,
        date: element.endDate,
      });
    })
  });
  showRealTrip.value = true
  console.log('results', results)

  confirmTrip.value = results
  
}

const activeCollapse = ref(['1'])

const changeCollapse = (key:any) => {
}


const oldApplyList = ref([]);
const changeNow = ref({});
// 获取变更历史
const getDataList = () => {
  let params = {
    applyNo: creatTripParma.value.applyNo,
    pageNum: 1,
    pageSize: 10,
  };
  tripApi.getDataList(params).then((res:any) => {
    changeNow.value = {};
    oldApplyList.value = [];
    res.forEach((item:any) => {
      // changeStatus	变更状态 10正常 20变更中 30已作废  40历史版本
      if (item.changeStatus == '40') {
        oldApplyList.value.push(item);
      }
      if (item.changeStatus == '20') {
        changeNow.value = item;
      }
    });
  });
};
const reloadPage = async (id: string) => {
  creatTripParma.value = await tripApi.queryDetailByApplyNo(id);
  // 数据回显处理
  baseInfoCom.value.chosedPerson = creatTripParma.value.travelerList?.filter((item) => item.travelUserType == '0' && item.mainFlag == '1')[0];
  baseInfoCom.value.chosedInPersenList = creatTripParma.value.travelerList?.filter((item) => item.travelUserType == '0' && item.mainFlag !='1');

  creatTripParma.value.travelUserName = creatTripParma.value.travelerList.filter(
    (item) => item.mainFlag=='1'
  )[0].travelUserName;
  creatTripParma.value.outPersonId = [];
  creatTripParma.value.travelerList.forEach((item) => {
    if (item.travelUserType == '1') {
      creatTripParma.value.outPersonId.push(item.travelUserSyId);
    }

    item.personIdList = [];
    item.personIdList = [...item.personIdList, item.travelUserSyId];
  });
  planBudgeRef.value.cityList = [];
  creatTripParma.value.tripList.forEach((item, index) => {
    if (index == 0) {
      planBudgeRef.value.cityList.push({
        cityCode: item.beginCityCode,
        city: item.beginCityName,
        syId: item.beginCityCodeSy,
        date: item.beginDate,
      });
    }
    planBudgeRef.value.cityList.push({
      cityCode: item.endCityCode,
      city: item.endCityName,
      syId: item.endCityCodeSy,
      date: item.endDate,
    });

    item?.tripDetailMapList?.forEach((tripMap) => {
      tripMap.personIdList = tripMap.travelApplyTripDetailList.map((tt) => tt.travelUserSyId);
    });
  });

  creatTripParma.value?.fileList?.forEach((file) => {
    file.name = file.fileName;
    file.thumbUrl = file.filePath;
  });

};
const applyId = route.value?.query?.id;

onMounted(async () => {
  creatTripParma.value = await tripApi.queryDetailByApplyNo(applyId);
  // 数据回显处理
  // 根据登陆人初始化数据
  baseInfoCom.value.chosedPerson = creatTripParma.value.travelerList?.filter((item) => item.travelUserType == '0'&& item.mainFlag == '1')[0];
  baseInfoCom.value.chosedInPersenList = creatTripParma.value.travelerList?.filter((item) => item.travelUserType == '0' && item.mainFlag !='1');
  creatTripParma.value.travelUserName = creatTripParma.value.travelerList.filter(
    (item) => item.mainFlag == '1'
  )[0].travelUserName;
  creatTripParma.value.outPersonId = [];
  creatTripParma.value.travelerList.forEach((item) => {
    if (item.travelUserType == '1') {
      creatTripParma.value.outPersonId.push(item.travelUserSyId);
    }

    item.personIdList = [];
    item.personIdList = [...item.personIdList, item.travelUserSyId];
  });
  planBudgeRef.value.cityList;
  creatTripParma.value.tripList.forEach((item, index) => {
    if (index == 0) {
      planBudgeRef.value.cityList.push({
        cityCode: item.beginCityCode,
        city: item.beginCityName,
        syId: item.beginCityCodeSy,
        date: item.beginDate,
      });
    }
    planBudgeRef.value.cityList.push({
      cityCode: item.endCityCode,
      city: item.endCityName,
      syId: item.endCityCodeSy,
      date: item.endDate,
    });

    item?.tripDetailMapList?.forEach((tripMap) => {
      tripMap.personIdList = tripMap.travelApplyTripDetailList.map((tt) => tt.travelUserSyId);
    });
  });

  creatTripParma.value?.fileList?.forEach((file) => {
    file.name = file.fileName;
    file.thumbUrl = file.filePath;
  });
  travelerList.value = creatTripParma.value?.travelerList?.filter((item: any) => item.travelUserNo);
  travelerList.value.forEach((item: any) => {
    item.cityList = [];
  });
  if(!creatTripParma.value.haierBudgetPayOccupyRequest) {
    creatTripParma.value.haierBudgetPayOccupyRequest = {}
  }

  getDataList();
});
const businessTravel = import.meta.env.VITE_BUSINESS_TRIP_URL;
const goToDetail = (id: string) => {
  const url = businessTravel + '#' + '/detail?id=' + id;
  window.open(url);
};

const TRIP_SINGLE = import.meta.env.VITE_BUSINESS_TRIP_SINGLE
// 跳转商旅系统查看预订详情
const goToBookDetail = () => {
  const url = `${TRIP_SINGLE}fcc/fcapply/ccsqd/add.html?skipType=9900107&state=3&djbh=${creatTripParma.value?.applyNo}&defaultpage=orderlist-page`
  window.open(url)
}

const tabPosition = ref<TabsProps['tabPosition']>('top');
const activeKey = ref('1');
</script>

<template>
  <div class="container">
    <div class="row flex">
      <div class="" style="position: relative">
        <!-- 变更单 -->
        <div class="change-title">
          <h-row class="mb-10">
            <h-col :span="6" style="color: #00000073">申请单号:</h-col>
            <h-col :span="18">{{ creatTripParma.applyNo }}</h-col>
          </h-row>
          <h-row class="mb-10">
            <h-col :span="6" style="color: #00000073">申请时间:</h-col>
            <h-col :span="18">{{ creatTripParma.gmtCreate }}</h-col>
          </h-row>

          
        </div>
       

        <div class="main-title">
          <!-- <img src="../../assets/image/trip/title.png" alt=""> -->
          <span>出差申请单</span>
        </div>
        <a-tabs  @change="tabVersion" v-model:activeKey="activeKey" :tab-position="tabPosition" animated type="card">
          <a-tab-pane  v-if="changeNow?.id" key="1"  tab="当前生效版本"></a-tab-pane>
          <a-tab-pane  v-if="changeNow?.id" key="2"  tab="变更中版本"></a-tab-pane>
          
          <template #rightExtra v-if="oldApplyList && oldApplyList.length > 0">
            <h-dropdown>
              <a class="ant-dropdown-link font-size-14" @click.prevent> 查看历史变更 </a>
              <template #overlay>
                <h-menu>
                  <h-menu-item v-for="(item, index) in oldApplyList" :key="index" @click="goToDetail(item.id)">
                    <a href="javascript:;">{{ `v${index + 1}` }}</a>
                  </h-menu-item>
                </h-menu>
              </template>
            </h-dropdown>
          </template>
        </a-tabs>
        <div class="apply-con flex">
          <!-- 驳回原因 -->
          <!-- status=10 && auditStatus = 40 && reatTripParma?.workFlowFailInfo -->
          <div
            class="whole-line reject"
            v-if="creatTripParma?.workFlowFailInfo && creatTripParma?.status == 10 && creatTripParma?.auditStatus == 40"
          >
            <h-row>
              <h-col :span="2">驳回原因:</h-col>
              <a-tooltip>
                <template #title>{{ creatTripParma?.workFlowFailInfo }}</template>
                <h-col :span="20">{{ creatTripParma?.workFlowFailInfo }}</h-col>
              </a-tooltip>
            </h-row>
          </div>
          <div
            class="reject-bg"
            v-if="creatTripParma?.workFlowFailInfo && creatTripParma?.status == 10 && creatTripParma?.auditStatus == 40"
          ></div>

           <!-- 查看审批流 -->
           <div class="approval-btns">
            <h-button class="common-btn-height" v-if="creatTripParma?.type != 1 && creatTripParma?.travelReserveFlag != 0" size="small" @click="goToApproval">查看审批流</h-button>
            <h-button class="common-btn-height mr-10" v-if="creatTripParma?.shengyiOrderPushStatus == 20 && creatTripParma?.travelReserveFlag != 0" @click="goToBookDetail" size="small">查看预订信息</h-button>
            <h-button class="common-btn-height mr-10" size="small" @click="getAllRealTrips">查看实际行程</h-button>

          </div>
         
          <!-- 基本信息 -->
          <base-info
            id="base-info"
            ref="baseInfoCom"
            :chosedNow="chosedNow"
            :isDetail="true"
            :creatTripParma="creatTripParma"
            @outPersonOpen="outPersonOpen"
            @showStandardOpen="openTravelStandardslModal"
          ></base-info>
          <!-- 行程与费用 -->
          <div id="plan-budget" class="whole-line">
            <plan-budge
              ref="planBudgeRef"
              :isDetail="true"
              :creatTripParma="creatTripParma"
              @openBudgetModal="openBudgetModal"
              @openBudgetDetailShow="openBudgetDetailShow"
              @showStandardOpen="openTravelStandardslModal"
              @showBudgetDetailModal="(data) => openBudgetDetailModal(data)"
            ></plan-budge>
          </div>
          <!-- 附件 -->
          <div id="file" class="whole-line block-con">
            <file-upload :isDetail="true" :creatTripParma="creatTripParma"></file-upload>
          </div>

          
        </div>
      </div>
      <div class="anchor-con flex">
        <h-anchor :items="anchorItems" @click="handleClick" />
      </div>

      <!-- 差旅标准弹窗 -->
      <travel-standards-modal ref="travelStandards" />

      <!--新增外部联系人 -->
      <out-person-modal ref="outPerson" />

      <!-- 预算归属弹窗 -->
      <budget-modal ref="budget" />

      <!-- 预算归属明细弹窗 -->
      <budgetModalShow ref="budgetDetailDialog" />

      <!-- 预算明细弹窗 -->
      <budget-detail-modal :tableData="budgetDetailData" ref="budgetDetail" />

      <!-- 实际行程弹窗 -->
      <h-modal v-model:open="showRealTrip" width="1200px"  title="实际行程" :footer="null" >
        <div class="real-trip-modal" v-if="confirmTrip.length > 0">
          <h-collapse @change="changeCollapse" v-model:activeKey="activeCollapse">
            <h-collapse-panel  v-for="(item,index) in confirmTrip"  :key="index"  :collapsible="item.reimburFinish!=20">

              <template #header>
                <h-row>
                  <h-col :span="6">{{ `${item.travelUserName}(${item.travelUserNo})` }}</h-col>
                  <h-col :span="6">确认人:{{  `${item?.tripList[0]?.confirmUserName}(${item?.tripList[0]?.confirmUser})` }}</h-col>
                  <h-col :span="6">确认时间:{{ item?.tripList[0]?.confirmDate }}</h-col>
                  <h-col :span="6" style="text-align: right;">
                    <a-tag color="blue" v-if="item?.tripList[0]?.confirmUser == 'SYSTEM'">系统确认</a-tag>
                    <a-tag color="green" v-else>手动确认</a-tag>
                  </h-col>
                </h-row>
              </template>

              <trip-detail :key="index" :cityList="item.cityList" :creatTripParma="item" :isDetail="true"
              ></trip-detail>
            </h-collapse-panel>
          </h-collapse>
        </div>
        <div class="real-trip-modal flex-center " style="justify-content:center" v-else>
          <a-empty />
        </div>
        
      </h-modal>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url(./components/trip.less);
:deep(.ant-tabs-nav) {
  margin: 0;
}
:deep(.ant-tabs-nav) {
  padding-right: 24px;
}
</style>