<script lang="ts" setup>
import { computed, createVNode, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';

import {
  Anchor as hAnchor,
  <PERSON><PERSON> as hButton,
  Spin as hSpin,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Checkbox as hCheckbox,
  Form as hForm,
  FormItem as hFormItem,
  Row as hRow,
  Col as hCol,
  Popconfirm as hPopconfirm,
  Upload as hUpload,
  Input as hInput,
  Modal as hModal,
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  DownloadOutlined,
  VerticalAlignBottomOutlined,
  ExclamationCircleFilled,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  SendOutlined,
  PhoneOutlined,
  VerticalAlignTopOutlined,
  DeleteOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { download, tripApi, teamListApi, rechargeApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import relativeTime from 'dayjs/plugin/relativeTime';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';

import { CityResponse, CityItem, TCteateTeam, RRechargeParmas } from '@haierbusiness-front/common-libs';
import { fileApi } from '@haierbusiness-front/apis';
import type { TableColumnType } from 'ant-design-vue';
import { useRouter } from 'vue-router';

const route = ref(getCurrentRoute());
const router = useRouter();
const exportVisible = ref<boolean>(false);

const id = route.value?.query?.id;

dayjs.extend(relativeTime);

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const labelCol = { span: 3 };
const wrapperCol = { span: 21 };
const spinning = ref<boolean>(false);

const rechargeForm = ref<RRechargeParmas>({
  personList: [
    {
      num: 1,
      name: '李忘',
      personId: '1212',
      money: '112元',
    },
    {
      num: 1,
      name: '李忘',
      personId: '1212',
      money: '112元',
    },
  ],
  payType: 0,
  checked: false,
});

const validateTrue = (_: any, value: boolean) =>
  value === true ? Promise.resolve() : Promise.reject(new Error('请确认明细数据'));

const rules: Record<string, Rule[]> = {
  payType: [{ required: true, message: '请选择支付类型', trigger: 'change' }],
  checked: [{ required: true, validator: validateTrue, trigger: 'change' }],
  personList: [{ required: true, message: '请上传人员明细数据', trigger: 'change' }],
};

const formRef = ref();
const openExport = () => {
  exportVisible.value = true;
};
const handleImportOk = (e: MouseEvent) => {
  exportVisible.value = false;
};
// 弹窗
const open = ref<boolean>(false);
const loading = ref<boolean>(false);
const searchLoading = ref<boolean>(false);

const handleOk = () => {};
const cancel = () => {
  open.value = false;
};

const onSearch = () => {
  searchLoading.value = true;
  setTimeout(() => {
    searchLoading.value = false;
  }, 5000);
};

// 提交
const onSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      open.value = true;
    })
    .catch((error) => {});
};
// 清空
const resetList = () => {
  rechargeForm.value.personList = [];
};

// 下载人员明细模板
const ticketTempFile = new URL('@/assets/template/team_recharge_template.xlsx', import.meta.url).href;

const downloadRechargeTemp = (filePath: string, name: string) => {
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = filePath;
  link.setAttribute('download', name);
  document.body.appendChild(link);
  link.click();
};

// 上传相关

const uploadLoading = ref(false);
const uploadPeople = (options: any) => {
  console.log('🚀 ~ uploadPeople ~ options:', options);
  uploadLoading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);
  rechargeApi
    .peopleImport(formData)
    .then((it) => {
      console.log('🚀 ~ .then ~ it:', it);
      // options.file.filePath = baseUrl + it.path;
      // options.file.fileName = options.file.name;
      // teamForm.value.travelerFileUrl = baseUrl + it.path;
      // teamForm.value.travelerFileName = options.file.name;
      // options.
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .catch((e) => {
      // teamForm.value.fileList = [];
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};
const detail = ref<TCteateTeam>({});

const getDetail = (id: string) => {};

const columns = [
  {
    title: '序号',
    dataIndex: 'num',
    align: 'center',
  },
  {
    title: '员工姓名',
    dataIndex: 'name',
    align: 'center',
  },
  {
    title: '员工工号',
    dataIndex: 'personId',
    align: 'center',
  },

  {
    title: '充值金额',
    dataIndex: 'money',
    align: 'center',
  },
];

watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);
</script>

<template>
  <div class="container">
    <div class="row flex">
      <div class="apply-con flex">
        <h-row justify="center" align="middle" style="padding: 40px; font-size: 20px; font-weight: 600">
          达产激励充值
        </h-row>
        <h-form
          class="mt-30"
          ref="formRef"
          :model="rechargeForm"
          :label-col="labelCol"
          :rules="rules"
          :wrapper-col="wrapperCol"
          labelAlign="left"
        >
          <!-- 人员明细 -->
          <h-form-item :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }" name="personList">
            <template #label>
              <span
                >人员明细123<a-tooltip>
                  <template #title>人员明细</template>
                  <question-circleOutlined class="icon" /> </a-tooltip
              ></span>
            </template>
            <h-row style="margin-bottom: 20px">
              <h-col :span="18" class="flex">
                <a-button type="primary" class="mr-20" @click="downloadRechargeTemp(ticketTempFile, '人员明细模板')">
                  <template #icon><VerticalAlignBottomOutlined /></template>
                  下载模板
                </a-button>

                <h-upload name="file" :custom-request="uploadPeople" :max-count="1" accept=".xls,.xlsx">
                  <h-button type="primary" class="mr-20" :loading="ticketBtnLoading">
                    <VerticalAlignTopOutlined />
                    <span class="font-size-14">数据导入</span>
                  </h-button>
                </h-upload>

                <h-popconfirm
                  title="确定要清空数据吗?"
                  ok-text="确认"
                  cancel-text="取消"
                  @confirm="resetList"
                  :disabled="!rechargeForm?.personList?.length > 0"
                >
                  <a-button type="primary" :disabled="!rechargeForm?.personList?.length > 0">
                    <template #icon> <DeleteOutlined /></template>

                    清空数据</a-button
                  >
                </h-popconfirm>
              </h-col>
              <h-col :span="6" style="display: flex; align-items: center; flex-flow: row-reverse">
                <span style="margin-left: 5px; font-weight: 600">待确认</span>
                <a-switch v-model:checked="checked" />
              </h-col>
            </h-row>

            <a-table :columns="columns" :data-source="rechargeForm?.personList" size="small" bordered>
              <template #bodyCell="{ column, text }">
                <template v-if="column.dataIndex === 'name'">
                  <a>{{ text }}</a>
                </template>
              </template>
            </a-table>
          </h-form-item>
          <!-- 总计金额 -->
          <h-form-item name="payType">
            <template #label>
              <span
                >总计金额<a-tooltip>
                  <template #title>导入数据总额</template>
                  <question-circleOutlined class="icon" /> </a-tooltip
              ></span>
            </template>
            <div>219090 元</div>
          </h-form-item>

          <!-- 账户余额 -->
          <h-form-item name="payType">
            <template #label>
              <span
                >账户余额<a-tooltip>
                  <template #title>账户余额</template>
                  <question-circleOutlined class="icon" /> </a-tooltip
              ></span>
            </template>
            <div>45012356 元</div>
          </h-form-item>
          <!-- 是否通知 -->
          <h-form-item name="payType">
            <template #label>
              <span
                >是否通知<a-tooltip>
                  <template #title>可选充值后是否通知</template>
                  <question-circleOutlined class="icon" /> </a-tooltip
              ></span>
            </template>
            <h-radio-group v-model:value="rechargeForm.payType">
              <h-radio :value="0">是</h-radio>
              <h-radio :value="1">否</h-radio>
            </h-radio-group>
          </h-form-item>

          <!-- 激励留言 -->
          <h-form-item name="remark">
            <template #label>
              <span
                >激励留言<a-tooltip>
                  <template #title>该留言将会直接发送至被激励员工,请谨慎填写</template>
                  <question-circleOutlined class="icon" /> </a-tooltip
              ></span>
            </template>
            <h-textarea
              v-model:value="rechargeForm.remark"
              placeholder="请输入留言信息"
              :auto-size="{ minRows: 3, maxRows: 5 }"
              allow-clear
            />
          </h-form-item>

          <!-- 注意事项 -->
          <h-form-item name="checked">
            <h-checkbox v-model:checked="rechargeForm.checked">
              <span style="color: red">注意事项:数据提交后不可撤回及修改,提交前请先确认人员明细数据是否正确</span>
            </h-checkbox>
          </h-form-item>
          <!--  -->
          <h-form-item :wrapper-col="{ offset: 10 }">
            <h-button type="primary" @click="onSubmit">提交</h-button>
            <h-button style="margin-left: 50px">取消</h-button>
          </h-form-item>
        </h-form>

        <h-modal :destroyOnClose="true" :maskClosable="false" v-model:open="open" title="短信验证" @ok="handleOk">
          <template #footer>
            <h-button key="back" @click="cancel">返回</h-button>
            <h-button key="submit" type="primary" :loading="loading" @click="handleOk">提交</h-button>
          </template>

          <h-row class="mb-10">订单提交后不支持修改及撤回操作,请确认信息后提交</h-row>
          <h-row class="mb-10">
            <h-col :span="12"> 人员数据: 12人 </h-col>
            <h-col :span="12"> 总计金额: 8950.00元 </h-col>
          </h-row>
          <h-row class="mb-10">激励留言:{{ rechargeForm.remark }}</h-row>
          <h-row>
            <h-col :span="12"> 手机号:{{ loginUser.phone }} </h-col>
            <h-col :span="12">
              <a-input-search
                :loading="searchLoading"
                v-model:value="value"
                placeholder="输入验证码"
                enter-button="获取验证码"
                size="mini"
                @search="onSearch"
              />
            </h-col>
          </h-row>
        </h-modal>
      </div>
    </div>
    <a-modal v-model:open="exportVisible" title="导入模板" :maskClosable="false" :footer="null" width="500px">
      <a-form
        :model="fileInfo.list[0]"
        name="basic"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
      >
        <a-form-item label="上传" name="fileList">
          <a-upload
            v-model:file-list="fileInfo.list[0].fileList"
            name="file"
            action="/hbweb/hotel-analysis/hb/hotel-analysis/api/ledger/spot-check/import"
            @change="City"
            :headers="{
              'Hb-Token': token,
            }"
          >
            <a-button>
              <upload-outlined></upload-outlined>
              选择文件
            </a-button>
          </a-upload>
        </a-form-item>
        <a-form-item :wrapper-col="{ offset: 16, span: 16 }">
          <a-button type="primary" class="btn" @click="handleOk">取消</a-button>
        </a-form-item>
      </a-form></a-modal
    >
  </div>
</template>

<style lang="less" scoped>
@import url(../recharge/recharge.less);

.change-title {
  position: absolute;
  right: 0;
  top: -80px;
  width: 300px;

  .ant-col-6,
  .ant-col-18 {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #000000d9;
  }
}
.mt-5 {
  margin-top: 5px;
}

.ml-5 {
  margin-left: 5px;
}
.color-eee {
  color: #a8a7a7;
}

.com-box {
  background: #f5f5f5;
  padding: 20px;
  margin-top: 20px;

  .whole-line {
    height: 40px;
    font-weight: 600;
  }
  .info-box {
    display: flex;
    align-items: flex-start;
    padding: 10px 20px;
    border: 1px solid #ffdb5c;
    background: #fffbd8;
  }
  :deep(.ant-form-item-control-input-content) {
    display: flex;
    align-items: center;
  }
}
.title {
  height: 60px;
}
.download-btn {
  color: #408cff;
  cursor: pointer;
}
:deep(.city-chose-box) {
  width: 200px !important;
}
:deep(.mr-10) {
  margin-right: 10px;
}

.background-eee {
  padding: 10px;
  background: #f4f4f4;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
