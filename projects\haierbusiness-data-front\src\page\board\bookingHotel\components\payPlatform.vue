<template>
    <div background="rgba(0,0,0,0)" :style="{ height: props.height + 'vh' }">
        <div :id="circleId" :style="{ height: props.height! - 12 + 'vh', background: 'rgba(0,0,0,0) !important' }"></div>
        <div class="tips">
            <div class="tips-main">
                <div class="tip" v-for="(row, index) in payTypeDetailRows" :key="row.name">
                    <span class="tip-dot" :style="{ borderColor: colors[index] }"></span>
                    <span class="tip-percent">{{
                        ((row.value / payTypeDetailTotal) * 100).toFixed(0)
                    }}%</span>
                    <span class="tip-title">{{ row.name }}</span>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { circle, colors } from "../../data";
import * as echarts from "echarts";
import { queryPayTypeDetail } from "@haierbusiness-front/apis/src/data/board";
import { EventBus } from "../../eventBus";
import { useRouter, useRoute } from "vue-router";
const route = useRoute();
const props = defineProps({
    height: Number,
});
const loading = ref(false);
const circleId = ref("circle-" + Date.now());
const payTypeDetailRows: any = ref([]);
const payTypeDetailTotal = ref(0);
let circleChartDom, circleChart: any;
const payTypeCheck = ref<string>("");
onMounted(() => {
    queryCircle();

    circleChartDom = document.getElementById(circleId.value);
    circleChart = echarts.init(circleChartDom as any, "dark");
    circleChart.on("click", (params: { from: string, name: string }) => {
        if (
            params.from != "budget_source" &&
            params.name != payTypeCheck.value
        ) {
            EventBus.emit("refresh", {
                ...params,
                from: "budget_source",
            });
        } else {
            payTypeCheck.value = "";
            EventBus.emit("refresh");
        }
    });
});
EventBus.on((event, params) => {
    if (event == "refresh") {
        if (!params) queryCircle();
        if (params && params.from != "budget_source") queryCircle(params);
        if (params && params.from == "budget_source") {
            queryCircle().then(() => {
                payTypeCheck.value = params.data.name;
                circleChart.dispatchAction({
                    type: "highlight",
                    seriesIndex: 0,
                    dataIndex: params.dataIndex,
                });
            });
        }
    }
});
const queryCircle = async (params?: {
    data: { name: string };
    from: string;
}) => {
    loading.value = true;
    const data = await queryPayTypeDetail(
        params ? params.data.name : null,
        params ? params.from : null
    );
    loading.value = false;
    const payTypeDetailData: any = [];
    let total = 0;
    data.rows.forEach((item: any) => {
        total += item[1];
        payTypeDetailData.push({
            name: item[0],
            value: item[1],
        });
    });
    payTypeDetailTotal.value = total;
    payTypeDetailRows.value = payTypeDetailData;

    const { series }: any = circle;
    series[0].color = colors;
    series[0].data = payTypeDetailData;
    series[0].radius = ["50%", "80%"];
    circleChart.clear();
    circleChart.setOption(circle);
};
</script>
<style scoped lang="less">
.tips {
    display: flex;
    justify-content: center;
}

.tips-main {
    display: flex;
    flex-wrap: wrap;
    // width: 260px;
}

.tip {
    width: 110px;

    &-dot {
        display: inline-block;
        width: 10px;
        height: 10px;
        border: 3px solid #ffd700;
        border-radius: 50%;
    }

    &-percent {
        font-size: 16px;
        margin: 0 5px 0 7px;
    }

    &-title {
        font-size: 12px;
    }
}

@media screen and (max-width: 1500px) {
    .tip {
        width: 100px;

        &-dot {
            display: inline-block;
            width: 6px;
            height: 6px;
            border: 2px solid #ffd700;
            border-radius: 50%;
        }

        &-percent {
            font-size: 14px;
            margin: 0 3px 0 5px;
        }

        &-title {
            font-size: 10px;
        }
    }
}
</style>
<style lang="less"></style>
