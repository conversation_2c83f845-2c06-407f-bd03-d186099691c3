import { IPageRequest } from "../../basic";

export class IBannerRequest extends IPageRequest {
    id?: number | null
    imgUrl?: string  
    jumpLinkApp?: string  
    jumpLinkPc?: string  
    showOrder?: number  
    showStatus?: number  
    adSubject?: string
}

export class IBannerAccount {
    id?: number
    creator?:string 
    createTime?: string 
    updater?: string 
    updateTime?: string 
    imgUrl?: string 
    jumpLinkApp?: string 
    jumpLinkPc?: string 
    showOrder?: number 
    showStatus?: number 
    adSubject?: string
    isTopping?: boolean
    content?: string
    author?: string
}

export class IBannerResponse {
    id?: number
    creator?:string 
    createTime?: string 
    updater?: string 
    updateTime?: string 
    imgUrl?: string 
    jumpLinkApp?: string 
    jumpLinkPc?: string 
    showOrder?: number 
    showStatus?: number 
    adSubject?: string


}
