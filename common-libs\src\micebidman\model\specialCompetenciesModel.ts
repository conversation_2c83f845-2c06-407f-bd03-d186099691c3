import { IPageRequest } from "../../basic";

export class ISpecialCompetenciesFilter extends IPageRequest {
    begin?:string
    end?:string
    //会议订单号
    mainCode?:string
    //会议名称
    miceName?:string
    //特殊权限
    type?:string
    //状态
    state?:string
    //经办人名称
    operatorName?:string
    //开通人名称
    openName?:string
    //申请开始时间
    gmtCreateTimeStart?:string
    //申请结束时间
    gmtCreateTimeEnd	?:string
    //授权开始时间
    imPowerTimeStart?:string
    //授权结束时间
    imPowerTimeEnd?:string
    //生效开始时间
    operantTimeStart?:string
     //生效结束时间
     operantTimeEnd?:string
}


export class ISpecialCompetencies {
    id?: number | null
    begin?:string
    end?:string
    //会议订单号
    mainCode?:string
    //会议名称
    miceName?:string
    //特权类型
    type?:string
    //状态
    state?:string
    //经办人名称
    operatorName?:string
    //开通人名称
    openName?:string
    //授权时间
    imPowerTime?:string
    //生效时间
    onTime?:string
     //关闭原因
     offReason?:string
     //关闭时间
     offTime?:string
     //开通原因
     reason?:string
     //开通状态
     openState?:string
     //开通状态
     gmtCreate?:string
}