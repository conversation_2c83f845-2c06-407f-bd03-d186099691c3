<template>
  <div v-if="$route.matched.length <= 2" style="min-height: 100vh" class="homeBox">
    <div class="searchContent">
      <div class="topSearchBox">
        <div class="searchItem">
          <cityChose  @chosedCity="chosedCity" :value="searchForm.city" />
          <!-- <span><a-button @click="searchMap">经纬度搜索</a-button></span> -->
        </div>
        <div class="searchItem date">
          <a-range-picker
            style="width: 100%"
            v-model:value="searchForm.date"
            valueFormat="YYYY-MM-DD"
          />
        </div>
        <div class="searchItem">
          <h-input
            allowClear
            placeholder="请输入酒店名称"
            v-model:value="searchForm.name"
            style="width: 100%"
          />
        </div>
        <a-button @click="getHotelList(1)" :icon="h(SearchOutlined)" type="primary">搜索</a-button>
      </div>
    </div>
    <div class="searchCard">
      <div class="searchItem">
        <div class="searchTitle">酒店星级</div>
        <div class="rightContent">
          <a-checkbox-group
            @change="getHotelList(1)"
            v-model:value="searchForm.starLevels"
            :options="options"
          />
        </div>
      </div>
      <div class="searchItem">
        <div class="searchTitle">更多条件</div>
        <div class="rightContent no-select">
          <!-- <a-checkbox-group @change="getHotelList(1)" v-model:value="searchForm.starLevel" :options="options" /> -->
          <div class="more" @click="showBrandBox = !showBrandBox">
            酒店品牌
            <CaretUpOutlined v-if="showBrandBox" />
            <CaretDownOutlined v-else />
          </div>
          <div @click="searchMap" style="margin-left:10px;" class="more">
            <a-tag>经纬度搜索</a-tag> <a-tag style="margin-left:10px;" @close="tagClose" color="purple" v-if="searchForm.lon" :bordered="false" closable>经度：{{searchForm.lon}} 纬度：{{searchForm.lat}}  方圆{{ searchForm.distance?searchForm.distance:'5' }}公里内</a-tag>
          </div>
        </div>
      </div>
      <div v-show="showBrandBox" class="searchItem no-select">
        <div class="searchTitle"></div>
        <div class="rightContent">
          <a-tabs v-model:activeKey="activeKey">
            <a-tab-pane key="1" tab="按等级分类">
              <div class="brandListItem" v-for="item in brandList.brandList">
                <div class="brandName">{{ item.name }}</div>
                <!-- <a-checkbox-group v-model:value="searchForm.starLevel" :options="item.children" /> -->
                <div class="brandItem">
                  <div class="item ellipsis" v-for="v in item.childList">
                    <a-checkbox @click="changeBrandList(v.code)">
                      <a-tooltip
                        :mouseEnterDelay="1"
                        placement="topLeft"
                        :title="v.name"
                      >{{ v.name }}</a-tooltip>
                    </a-checkbox>
                  </div>
                </div>
              </div>
            </a-tab-pane>
            <a-tab-pane key="2" tab="按集团分类" force-render>
              <div class="brandListItem" v-for="item in brandList.groupBrandList">
                <div class="brandName">{{ item.name }}</div>
                <!-- <a-checkbox-group v-model:value="searchForm.starLevel" :options="item.children" /> -->
                <div class="brandItem">
                  <div class="item ellipsis" v-for="v in item.childList">
                    <a-checkbox @click="changeBrandList(v.code)">
                      <a-tooltip
                        :mouseEnterDelay="1"
                        placement="topLeft"
                        :title="v.name"
                      >{{ v.name }}</a-tooltip>
                    </a-checkbox>
                  </div>
                  <!-- <a-checkbox @click="changeBrandList(v.code)" v-for="v in item.childList">{{ v.name }}</a-checkbox> -->
                </div>
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </div>
    <div class="list-content">
      <div class="hotelList">
        <div class="topFilterBox">
          <div
            class="topFilter"
            @click="selectFilterKey(item)"
            v-for="item in filterArray"
            :class="{ active: filterKey.indexOf(item) != -1 }"
          >
            {{ item }}
            <div v-if="item != '推荐'">
              <CaretUpOutlined
                v-if="filterKey.indexOf('1') != -1 && filterKey.indexOf(item) != -1"
              />
              <CaretDownOutlined
                v-if="filterKey.indexOf('2') != -1 && filterKey.indexOf(item) != -1"
              />
            </div>
          </div>
        </div>
        <div v-if="total != 0" class="listBox">
          <a-spin :spinning="spinning">
            <div v-for="item in hotelList" :key="item.id" class="list-item-target">
              <div class="leftBox">
                <div class="imgBox">
                  <div class="recommend" v-if="item.recommendFlag == 1">推荐</div>
                  <img v-lazy="item.image" :key="item.code" />
                  <!-- <div class="noImg" v-else>暂无图片</div> -->
                </div>
                <div class="info">
                  <span class="title">
                    {{ item.name }}
                    <span class="type" v-if="item.starLevel == 5" >[豪华型]</span>
                    <span class="type" v-if="item.starLevel == 4">[高档型]</span>
                    <span class="type" v-if="item.starLevel ==3">[舒适型]</span>
                    <span class="type" v-if="item.starLevel ==2 || item.starLevel ==1 || item.starLevel ==0 ">[经济型]</span>
                    <span>
                      <SketchOutlined
                        v-for="v in item.starLevel"
                        :style="{ fontSize: '14px', color: '#ff6600' }"
                      ></SketchOutlined>
                    </span>
                  </span>
                  <p class="transport">
                    <span class="position">{{ item.address }}</span>
                  </p>
                  <p v-if="item.distance" class="transport">
                    距离搜索点 <span style="color: #ff6600">{{item.distance}}</span>米
                  </p>
                </div>
              </div>
              <div class="rightBox">
                <!-- <div class="score">{{ item.commentSorceTotal }}</div> -->
                <div class="hotel-price">
                  <h2 v-if="item.price">
                    <b>¥</b> {{item.price}} <span style="font-size：14px">起</span>
                  </h2>
                  <h2 v-else>
                    <span style="color:#ff6600">暂无价格</span>
                  </h2>
                  <p>
                    <i>{{ item.commentScoreTotal ? item.commentScoreTotal : '暂无评分' }}</i>
                    <span style="margin-left: 5px" v-if="item.commentScoreTotal">
                      {{
                      getScoreTitle(item.commentScoreTotal)
                      }}
                    </span>
                  </p>
                  <p
                    class="jgjhxqwz"
                    style="display: none; height: 18px; font-size: 7px; color: #848383; margin-top: -10px"
                  >以详情页价格为准</p>
                  <a-button
                    @mouseenter="addMaker(item)"
                    @click="toHotelInfo(item)"
                    type="primary"
                  >查看详情</a-button>
                </div>
              </div>
            </div>
            <!-- <div class="noMore">暂时没有更多房源~~</div> -->
            <div v-if="total != 0" class="noMore">
              <a-pagination
                @change="getHotelList"
                v-model:pageSize="page.pageSize"
                v-model:current="page.pageNum"
                :total="total"
                show-less-items
              />
            </div>
          </a-spin>
        </div>
        <a-empty style="margin-top: 200px" v-if="!spinning && total == 0" />
      </div>
      <div class="mapBox" id="map">
        <a-map ref="mapRef"></a-map>
      </div>
    </div>
  </div>
  <router-view v-if="$route.matched.length > 2"></router-view>
  <a-modal width="850px" v-model:open="mapBoxShow" title="经纬度搜索" @ok="handleOk">
    <p>当前点经度：<a-input disabled v-model:value="lon" placeholder="请点击地图选择" size="small" style="width:120px;"></a-input>  当前点维度：<a-input  placeholder="请点击地图选择" disabled v-model:value="lat" size="small" style="width:120px;"></a-input> 方圆 <a-input v-model:value="distance" size="small" style="width:60px;"></a-input> 公里</p>
    <div class="mapBoxmodal">
      <aMapSelect :key="1" @selectMark="selectMark" ref="mapRef1"></aMapSelect>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  Input as hInput,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Card as hCard,
  message
} from "ant-design-vue";
import {
  SketchOutlined,
  SearchOutlined,
  CaretUpOutlined,
  CaretDownOutlined
} from "@ant-design/icons-vue";
import { computed, onMounted, ref, watch, h } from "vue";
import { useRouter, useRoute, RouteRecordRaw, Router } from "vue-router";
// import eFooter from '@haierbusiness-front/components/layout/Footer.vue';
// import eHeader from '@haierbusiness-front/components/layout/Header.vue';
import cityChose from "@haierbusiness-front/components/cityChose/index.vue";
import { hotelListApi } from "@haierbusiness-front/apis";
import {
  ISmartBrainFilter,
  ISmartBrain,
  Datum
} from "@haierbusiness-front/common-libs";
import { getCurrentRouter, routerParam } from "@haierbusiness-front/utils";
import aMap from "../components/aMap/index.vue";
import aMapSelect from "../components/aMapSelect/index.vue";

const router = getCurrentRouter();
// const currentRouter = useRouter()
// const appStore = useAppStore()
// const orderStore = useOrderStore()
// const router = getCurrentRouter()
const showBrandBox = ref<boolean>(false);
const city = ref<any>({});
const mapRef = ref();
const mapRef1 = ref();
const dateFormat = "YYYY/MM/DD";
const spinning = ref<boolean>(false);
const options = [
  { label: "五星/豪华", value: "50" },
  { label: "四星/高档", value: "40" },
  { label: "三星/舒适", value: "30" },
  { label: "二星", value: "20" },
  { label: "经济", value: "10" },
  { label: "其他", value: "0" }
];
const activeKey = ref<string>("1");
const filterArray = ["推荐", "评分", "星级"];
const hotelList = ref<any>([]);
// 按照什么排序 默认按照推荐
const filterKey = ref<string>("推荐");

const total = ref<number>(0);

const brandList = ref<any>({});

const currentRouter = ref();

const searchForm = ref<any>({
  city: "青岛",
  cityId: 59,
  brandIds: [],
  name: null,
  starLevels: [],
  date: []
});

const page = ref<any>({
  pageNum: 1,
  pageSize: 10
});

const changeBrandList = (code: string) => {
  if (!searchForm.value.brandIds.includes(code)) {
    searchForm.value.brandIds.push(code);
  } else {
    searchForm.value.brandIds = searchForm.value.brandIds.filter(
      (item: any) => {
        return item != code;
      }
    );
  }
  getHotelList(1);
};
const mapBoxShow = ref<boolean>(false)
// 经纬度搜索
const searchMap = () =>{
  mapBoxShow.value = true
  // lon.value = ""
  // lat.value = ""
  // distance.value = ""
  setTimeout(()=>{
    mapRef1.value.createByMap(lon.value?lon.value:120.571797, lat.value?lat.value:36.117137,lat.value?14:null);
  },500)
}
const tagClose = (value) =>{
  lon.value = null
  lat.value = null
  distance.value = null
  searchForm.value.lon = null
  searchForm.value.lat = null
  delete searchForm.value.distance 
  getHotelList(1);
}
const lon = ref<string>('')
const lat = ref<string>('')
const distance = ref<string>('')

// 选点
const selectMark = (item:any) =>{
  console.log(item)
  lon.value = item.lon
  lat.value = item.lat
}
// handleOk()
const handleOk = () =>{
  if(!lon.value){
   message.error('请点击地图选择坐标点')
   return
  }
 searchForm.value.lon = lon.value
 searchForm.value.lat =lat.value 
 searchForm.value.distance = distance.value
 mapBoxShow.value = false
 getHotelList(1);
}
const currentUrl = ref("");

const orderLinks = ref<Array<RouteRecordRaw>>();

const controlLinks = ref<Array<RouteRecordRaw>>();

// 选中筛选
const selectFilterKey = (key: string) => {
  console.log(key);
  if (key != "推荐") {
    if (filterKey.value == key + 2) {
      filterKey.value = "";
    } else if (!filterKey.value) {
      filterKey.value = key + 1;
    } else {
      filterKey.value = key + 2;
    }
    getHotelList(1);
  } else {
    filterKey.value = key;
    getHotelList(1);
  }
};

const addMaker = (item: any) => {
   let stringLonLat= getLonLat(item);
   if(stringLonLat.indexOf(",")>0){
    let lonlatArray= stringLonLat.split(",");
    let lon=  parseFloat(lonlatArray[0]);
    let lat=  parseFloat(lonlatArray[1]);
    mapRef.value.addMarker(lon, lat);
    mapRef.value.setZoomAndCenter(lon, lat);
   }

};

const getHotelList = async (type: any) => {
  if (type == 1) {
    page.value.pageNum = 1;
  }
  // const obj = searchForm.value
  spinning.value = true;
  let obj: any = {
    sort: null,
  };
  Object.assign(obj, searchForm.value, page.value);
  console.log(filterKey.value,"-----------------")
  if (filterKey.value == "星级1") {
    obj.sort = "+2"
  } else if (filterKey.value == "星级2") {
    obj.sort = "-2"
  } else if (filterKey.value == "距离1"||obj.lon) {
    obj.sort = "+3"
  } else if (filterKey.value == "距离2") {
    obj.sort = "-3"
  } else if (filterKey.value == "评分1") {
    obj.sort = "+4"
  } else if (filterKey.value == "评分2") {
    obj.sort = "-4"
  }else if (filterKey.value == "推荐配置1") {
    obj.sort = "+5"
  } else if (filterKey.value == "推荐配置2") {
    obj.sort = "-5"
  } else if (filterKey.value == "推荐酒店分数1") {
    obj.sort = "+5"
  } else if (filterKey.value == "推荐酒店分数2") {
    obj.sort = "-5"
  }  else {
    obj.sort=null
  }
  if (filterKey.value == "推荐" && !obj.name) {
    obj.recommendFlag = 1;
  } else {
    obj.recommendFlag = 0;
  }
  const res = await hotelListApi.list(obj);
  hotelList.value = res.records;
  total.value = res.total;
  spinning.value = false;
  if (res.records && res.records.length) {
    res.records.forEach((item: any) => {
      if (item.starLevel == 50) {
        item.starLevel = 5;
      } else if (item.starLevel == 40) {
        item.starLevel = 4;
      } else if (item.starLevel == 30) {
        item.starLevel = 3;
      } else if (item.starLevel == 20) {
        item.starLevel = 2;
      } else {
        item.starLevel = 0;
      }
    });
    let stringLonLat= getLonLat(res.records[0]);
    if(stringLonLat.indexOf(",")>0){
      let lonlatArray= stringLonLat.split(",");
      let lon=  parseFloat(lonlatArray[0]);
      let lat=  parseFloat(lonlatArray[1]);
      mapRef.value.createByMap(lon,lat);
    }
  }
};
// 获取酒店品牌
const getbrandlist = async () => {
  const res = await hotelListApi.brandlist();
  console.log(res, "Pnpai");
  brandList.value = res;

  //   hotelList.value = res;
};
// 选择城市
const chosedCity = (city: any) => {
  searchForm.value.city = city.name;
  searchForm.value.cityId = city.id;
  city.value = city;
};

// 点击查看详情
const toHotelInfo = (item: any) => {
  let routeUrl = router.resolve({
    path: "/hotel-analysis/hotelInfo",
    query: {
      id: item.id,
      code: item.code,

      date:
        searchForm.value.date && searchForm.value.date.length
          ? JSON.stringify(searchForm.value.date)
          : null
    }
  });
  window.open(routeUrl.href, "_blank");
};
const getScoreTitle = (score: number) => {
  if (score == 5) {
    return "完美";
  } else if (score == 4.9) {
    return "超棒";
  } else if (score > 4.5 && score <= 4.8) {
    return "很棒";
  } else if (score > 4.0 && score <= 4.5) {
    return "很好";
  } else if (score > 3.0 && score <= 4.0) {
    return "不错";
  } else if (score >= 0 && score <= 3.0) {
    return "一般";
  } else {
    return "一般";
  }
};

const getLonLat = (item: any) => {
    if(item.gdLon && item.gdLat){
      return item.gdLon+","+item.gdLat;
    }
    if(item.gLon && item.gLat){
      return item.gLon+","+item.gLat;
    }
    if(item.bdLon && item.bdLat){
      return item.bdLon+","+item.bdLat;
   }
   return "";
};


watch(
  () => router.currentRoute.value.path,
  () => {
    getHotelList(1);
    getbrandlist();
  },
  { immediate: true }
);

onMounted(() => {
  getHotelList(1);
  getbrandlist();
});
</script>
<style lang="less" scoped>
.pointer {
  cursor: pointer;
}

.homeBox {
  //   font-family: 'HarmonyBold';
  //   background: #f5f7fa;
  background: #f5f7fa;
  padding-bottom: 24px;
  .mr-20 {
    margin-right: 20px;
  }

  .mr-13 {
    margin-right: 13px;
  }

  .mr-32 {
    margin-right: 13px;
  }

  .mr-26 {
    margin-right: 13px;
  }

  .font-10 {
    font-size: 10px;
  }

  .icon {
    width: 22px;
    height: 22px;
  }

  .vertical {
    width: 1px;
  }

  .pl-8 {
    padding-left: 8px;
  }

  .flex {
    display: flex;
  }
  .align-items {
    align-items: center;
  }
}

.header {
  display: flex;
  width: 100%;
  box-shadow: 0 0 3px #b2b2b2;
  height: 61px;
  justify-content: center;
  align-items: center;

  .logo {
    display: flex;
    width: 1400px;
    height: 28px;
  }
}

.content {
  background-color: #fff;
  min-height: calc(100vh - 86px) !important;
  // display: flex;
  // justify-content: center;
  width: 100%;
  font-family: "Microsoft YaHei";

  .centerContent {
    // display: flex;
    margin: 0 auto;
    width: 1400px;
    display: flex;
    flex-direction: row;
    margin-top: 24px;

    .left {
      display: flex;
      width: 180px;
      flex-shrink: 0;
      border: 1px solid #eaeaea;
      min-height: calc(100vh - 106px) !important;
      flex-direction: column;
      margin-bottom: 20px;

      a {
        color: #000;
        &:hover {
          color: #0052d9;
        }
      }

      .menu-type {
        padding-top: 24px;
        padding-left: 33px;
        margin-bottom: 16px;
        border-left: 2px solid #fff;
        font-size: 16px;
        font-weight: 700;
        line-height: 18px;
      }

      .menu {
        padding-left: 43px;
        margin-bottom: 16px;
        cursor: pointer;
        border-left: 2px solid #fff;
        font-size: 14px;
      }

      .active {
        color: #0052d9;
        border-left: 2px solid #0052d9;

        a {
          color: #0052d9;
        }
      }
    }
    .right {
      display: flex;
      margin-left: 20px;
      width: 1280px;
    }
  }
}
.searchContent {
  background: #fff;
  margin-bottom: 20px;
  position: sticky;
  top: 0; /* 离顶部的距离 */
  z-index: 99;
  .topSearchBox {
    width: 1280px;
    margin: 0 auto;
    padding: 20px 0;
    display: flex;
    justify-content: space-between;
    .searchItem {
      width: 350px;
      span {
        font-size: 14px;
        margin-left:40px;      
        cursor: pointer;
      }
    }
    .date {
      width: 450px;
    }
  }
}

.searchCard {
  width: 1280px;
  margin: 0 auto;
  background: #fff;
  box-shadow: -1px 2px 6px 0 rgba(15, 41, 77, 0.12);
  border-radius: 2px 2px 0 0;
  margin-bottom: 16px;
  .searchItem {
    display: flex;
    border-bottom: 1px solid #dadfe6;
    .searchTitle {
      font-size: 14px;
      color: #0f294d;
      line-height: 18px;
      text-align: center;
      font-weight: bold;
      width: 104px;
      background: #f0f2f5;
      flex-shrink: 0;
      display: flex;
      align-items: flex-start;
      justify-content: center;
      padding-top: 16px;
    }
    .rightContent {
      padding: 10px 24px;
      display: flex;
      align-items: center;
      flex: 1;
      min-height: 48px;
      .more {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.88);
        cursor: pointer;
      }
      .brandListItem {
        display: flex;
        flex-wrap: wrap;
        padding: 10px;
        border-bottom: 1px solid #dadfe6;
        &:last-child {
          border-bottom: none;
        }
        .brandName {
          width: 100px;
        }
        .brandItem {
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          .item {
            width: 100px;
            :deep(span) {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: 80px;
            }
          }
        }
      }
    }
  }
}
.list-content {
  width: 1280px;
  margin: 0 auto;
  display: flex;
  min-height: 900px;
  .hotelList {
    flex: 1;
    background: #fff;
    .listBox {
      // min-height: 400px;
      // background: #fff;
    }
    // background: red;
    .topFilterBox {
      background: #fff;
      position: relative;
      cursor: pointer;
      display: flex;
      height: 40px;
      border-bottom: 1px solid #dfdfdf;
      padding: 0 20px;
      .topFilter {
        font-size: 14px;
        line-height: 40px;
        font-weight: 600;
        margin-right: 24px;
        display: flex;
        align-items: center;
        div {
          display: flex;
          flex-flow: column;
          justify-content: flex-start;
        }
      }
      .active {
        color: #287dfa;
      }
    }
    .list-item-target {
      padding: 16px;
      background: #fff;
      display: flex;
      box-sizing: border-box;
      width: 100%;
      min-height: 204px;
      margin-top: 10px;
      display: flex;
      border-bottom: 1px solid #dfdfdf;
      .rightBox {
        width: 230px;
        padding-left: 8px;
        .hotel-price {
          position: relative;
          float: right;
          width: 190px;
          height: 100%;
          padding: 30px 22px 0 22px;
          box-sizing: border-box;
          border-radius: 5px;
          //   background: #f8f8f8;
          text-align: center;
          h2 {
            font-size: 24px;
            color: #ff6600;
            font-weight: 700;
            b {
              font-size: 18px;
            }
            span {
              font-size: 14px;
              color: #333;
              font-weight: 400;
            }
          }
          p {
            height: 30px;
            color: #333;
            i {
              color: #ff6600;
            }
          }
        }
      }
      .leftBox {
        border-right: 1px solid #dadfe6;
        padding-right: 16px;
        display: inline-flex;
        flex: 1;
        .info {
          .title {
            font-size: 18px;
            color: #0f294d;
            line-height: 24px;
            margin-bottom: 8px;
            cursor: pointer;
            font-weight: 600;
            .type {
              display: inline-block;
              font-size: 12px;
              color: #666;
              font-weight: normal;
            }
          }
          .transport {
            color: #455873;
            line-height: 18px;
            font-size: 14px;
            margin-bottom: 8px;
            margin-top: 16px;
          }
        }
        .imgBox {
          flex-shrink: 0;
          width: 192px;
          height: 172px;
          position: relative;
          border-radius: 4px;
          overflow: hidden;
          margin-right: 16px;
          .recommend {
            position: absolute;
            top: 0;
            left: 0;
            background: #ff6600;
            color: #fff;
            font-size: 12px;
            text-align: center;
            line-height: 20px;
            padding: 1px 5px;
            border-radius: 2px 0 0 0;
          }
          img {
            width: 100%;
            height: 100%;
          }
          .noImg {
            width: 100%;
            height: 100%;
            background: #f8f6f6;
            color: #333;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
  .mapBox {
    width: 320px;
    height: 460px;
    margin-left: 10px;
    position: sticky;
    top: 80px; /* 离顶部的距离 */
    z-index: 100;
  }
  .noMore {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #b2b2b2;
    // height: 30px;
    background: #fff;
    padding: 10px;
  }
}
.ellipsis {
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 使用省略符号表示被截断的文本 */
}
.no-select {
  -webkit-user-select: none; /* Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE/Edge */
  user-select: none; /* 标准语法 */
}

img {
  object-fit: cover;
}
.mapBoxmodal{
  width:800px;
  height: 600px;
}
</style>
