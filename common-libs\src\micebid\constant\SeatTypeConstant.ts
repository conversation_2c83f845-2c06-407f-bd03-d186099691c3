// 用车车型

type keys = 'FOUR' | 'FIVE' | 'SEVEN' | 'TWELVE' | 'NINETEEN' | 'THIRTY_ONE' | 'FORTY_NINE' | 'FIFTY_ONE';

export const SeatTypeConstant = {
  FOUR: { code: 4, desc: '4座' },
  FIVE: { code: 5, desc: '5座' },
  SEVEN: { code: 7, desc: '7座' },
  TWELVE: { code: 12, desc: '12座' },
  NINETEEN: { code: 19, desc: '19座' },
  THIRTY_ONE: { code: 31, desc: '31座' },
  FORTY_NINE: { code: 49, desc: '49座' },
  FIFTY_ONE: { code: 51, desc: '51座' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in SeatTypeConstant) {
      const item = SeatTypeConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(SeatTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return SeatTypeConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
