<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  Textarea as hTextarea,
  message,
  TableProps
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { DownOutlined, ExclamationCircleOutlined, PlusOutlined, UploadOutlined, SearchOutlined, UpOutlined,ExclamationCircleFilled } from '@ant-design/icons-vue';
import { processApi } from '@haierbusiness-front/apis';
import {
  IProcessRecordListRequest,
  IUserListRequest,
  IUserInfo,
  PayTypeConstant
} from '@haierbusiness-front/common-libs';
import { errorModal, routerParam } from '@haierbusiness-front/utils';
import Eloading from '@haierbusiness-front/components/loading/ELoading.vue';
import dayjs, { Dayjs } from 'dayjs';
import { computed, onMounted, ref, watch, createVNode } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useRouter } from "vue-router";
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue'
import type { Rule } from 'ant-design-vue/es/form';
const router = useRouter()
const columns: ColumnType[] = [
  {
    title: '审批人',
    dataIndex: 'nickName',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '代审人',
    dataIndex: 'agentName',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '主流程名称',
    dataIndex: 'scopeProcessDefinedName',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '流程名称',
    dataIndex: 'scopeProcessSubDefinedName',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '关联场景',
    dataIndex: 'sceneName',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '180px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<any>({})
const addBoxShow = ref<boolean>(false)
const editBoxShow = ref<boolean>(false)
const batchEditBoxShow = ref<boolean>(false)
const confirmLoading =  ref<boolean>(false)
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(processApi.searchAgentList, {
  manual: false
})

const reset = () => {
  searchParam.value = {}
  handleTableChange({ current: 1, pageSize: 10 })
}

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const proxyReviewform = ref<any>({})
const selectedRowKey= ref<any>(null)
const selectedRow = ref<any>([])
// 流程下拉
const listDefined = ref<any>([])
// 子流程
const pdSubIdList = ref<any>([])

const addReviewform = ref()

const editRow = ref<any>({})

const editForm = ref()

// 必填验证
const rules: Record<string, Rule[]> = {
  username: [{ required: true, message: '请选择审批人', trigger: 'change' }],
  agentUser: [{ required: true, message: '请选择代审人', trigger: 'change'}],
  pdSubId: [{ required: true, message: '请选择流程', trigger: 'change'}],
  subId: [{ required: true, message: '请选择主流程', trigger: 'change'}],

};

const onSelectChange = (selectedRowKeys: Key[],selectedRows:any) => {
  console.log(selectedRows)
  selectedRowKey.value = selectedRowKeys;
  selectedRow.value = selectedRows
};

// 用户选择
const params = ref<IUserListRequest>({
    pageNum: 1,
    pageSize: 20
})

const name = ref('')
// 审批人选择
const userNameChange = (userInfo: IUserInfo) => {
  if (!userInfo) {
        proxyReviewform.value.username = ''
        proxyReviewform.value.nickName = ''
        return
    }
    proxyReviewform.value.username = userInfo?.username ?? ''
    proxyReviewform.value.nickName = userInfo?.nickName ?? ''
}
// 代审人选择
const agentNameChange = (userInfo: IUserInfo) => {
    if (!userInfo) {
        proxyReviewform.value.agentUser = ''
        proxyReviewform.value.agentName = ''
        return
    }
    proxyReviewform.value.agentUser = userInfo?.username ?? ''
    proxyReviewform.value.agentName = userInfo?.nickName ?? ''
}

// 修改审批人
const editRowChange= (userInfo: IUserInfo) => {
    if (!userInfo) {
        editRow.value.agentUser = ''
        editRow.value.agentName = ''
        return
    }
    editRow.value.agentUser = userInfo?.username ?? ''
    editRow.value.agentName = userInfo?.nickName ?? ''
}

// 修改代审人确定
const editHandleOk = ()=>{
  editForm.value
    .validate()
    .then(() => {
      confirmLoading.value = true
      processApi.updateAgentById(editRow.value).then(()=>{
        message.success('修改成功')
        batchEditBoxShow.value = false
        listApiRun({
          ...searchParam.value,
          pageNum: current.value,
          pageSize: pageSize.value,
        });
        selectedRowKey.value = []
        selectedRow.value = []
        confirmLoading.value = false
      })
    })
    .catch(error => {
      console.log('error', error);
      confirmLoading.value = false
    });
}

// 新增代审
const addProxyReview = () =>{
  addBoxShow.value = true
  getMasterList()
  pdSubIdList.value = []
}
// 检验方法
const isFieldUniform=(arr, field)=> {
  // 处理空数组情况（根据需求决定返回值）
  if (arr.length === 0) return true;

  // 获取第一个元素的字段值
  const firstValue = arr[0][field];
  
  // 使用every方法验证所有元素的字段值
  return arr.every(obj => obj[field] === firstValue);
}

// 批量编辑代审
const editBatchProxyReview = () =>{
  if(!selectedRow.value.length){
    message.warning('请选择代审数据')
    return
  }
  if(!isFieldUniform(selectedRow.value,'username')){
    message.warning('请选择同一审批人的数据')
    return
  }
  editRow.value.ids = []
  editRow.value.scopeProcessSubDefinedName = []
  selectedRow.value.forEach((item:any)=>{
    editRow.value.ids.push(item.id)
    editRow.value.scopeProcessSubDefinedName.push(item.scopeProcessSubDefinedName)
    editRow.value.nickName = item.nickName
    editRow.value.username = item.username
  })
  editRow.value.scopeProcessSubDefinedName = editRow.value.scopeProcessSubDefinedName.toString()
  // 判断是否是同一个审批人

  batchEditBoxShow.value = true
}


// 获取流程列表
const getMasterList = () =>{
    processApi.listDefined().then((res:any)=>{
      listDefined.value = res
    })
}
// 选择主流程
const subIdChange = (val:string[]) =>{
  getChildProcessList(val)
}
const filterOption = (input: string, option: any) => {
  return option.text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
// 根据所选流程请求子流程
const getChildProcessList = (pdsId:string[]) =>{
  pdSubIdList.value = []
  processApi.detailsBatch(pdsId).then((res:any)=>{
    if(res){
      res.forEach((item:any)=>{
        pdSubIdList.value.push(...item.definedSubsets)
      })
    }
  })
}
// 修改代审人
const changeAgent =(row:any)=>{
  editRow.value = JSON.parse(JSON.stringify(row))
  editRow.value.ids = [row.id]
  batchEditBoxShow.value = true
}
// 删除
const handleDelete = (id: number) => {
  hModal.confirm({
      title: '确认要删除吗？',
      icon: createVNode(ExclamationCircleFilled),
      onOk: () => {
        processApi.deleteAgentById(id).then(() => {
            message.success('删除成功！')
            listApiRun({
            ...searchParam.value,
            pageNum: current.value,
            pageSize: pageSize.value,
          });
        })
      },
      onCancel: async () => {

      },
  })
}

// 新增代审
const handleOk = () =>{
  // console.log(proxyReviewform.value,"proxyReviewform")
  addReviewform.value
    .validate()
    .then(() => {
      confirmLoading.value = true
      processApi.addAgent(proxyReviewform.value).then(()=>{
        message.success('新增成功')
        addBoxShow.value = false
        listApiRun({
          ...searchParam.value,
          pageNum: current.value,
          pageSize: pageSize.value,
        });
        confirmLoading.value = false
      })
    })
    .catch(error => {
      console.log('error', error);
      confirmLoading.value = false
    });
}
</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">

    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchAccountCode">主流程名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="searchAccountCode" v-model:value="searchParam.scopeProcessDefinedName" placeholder="请输入主流程名称" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchAccountCode">流程名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="searchAccountCode" v-model:value="searchParam.scopeProcessSubDefinedName" placeholder="请输入流程名称" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="applicantUser">审核人：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="searchAccountCode" v-model:value="searchParam.username" placeholder="请输入姓名或者工号" autocomplete="off"
            allow-clear />
              <!-- <user-select :value="name" placeholder="审批人" :params="params" @change="(userInfo: IUserInfo) => userNameChange(userInfo)" style="width: 100%;" /> -->
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="applicantUser">代审人：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="searchAccountCode" v-model:value="searchParam.agentName" placeholder="请输入姓名或者工号" autocomplete="off"
            allow-clear />
              <!-- <user-select :value="name" placeholder="审批人" :params="params" @change="(userInfo: IUserInfo) => userNameChange(userInfo)" style="width: 100%;" /> -->
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="applicantUser">场景：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="searchAccountCode" v-model:value="searchParam.sceneName" placeholder="请输入场景" autocomplete="off"
            allow-clear />
              <!-- <user-select :value="name" placeholder="审批人" :params="params" @change="(userInfo: IUserInfo) => userNameChange(userInfo)" style="width: 100%;" /> -->
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: left">
            <h-button type="primary" @click="addProxyReview">
              <PlusOutlined /> 新增代审
            </h-button>
            <h-button style="margin-left:10px;" type="primary" @click="editBatchProxyReview">
            批量修改
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)"  :row-selection="{ selectedRowKeys: selectedRowKey , onChange: onSelectChange }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'nickName'">
              {{ record.nickName }}({{ record.username }})
            </template>
            <template v-if="column.dataIndex === 'agentName'">
              {{ record.agentName }}({{ record.agentUser }})
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button @click="changeAgent(record)" type="link">修改代审人</h-button>
              <h-button danger @click="handleDelete(record.id)" type="text">删除</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

  </div>
  <!-- 新增代审弹窗 -->
  <h-modal
      v-model:visible="addBoxShow"
      :title="proxyReviewform.id ? '编辑代审' : '新增代审'"
      :width="600"
      class="modal"
      @cancel="$emit('cancel')"
      :confirmLoading="confirmLoading"
      @ok="handleOk"
    >
    <h-form
        ref="addReviewform"
        :model="proxyReviewform"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
        :rules="rules"
      >
        <!-- <h-form-item label="企业编码" name="enterpriseCode">
          <h-select ref="select" v-model:value="proxyReviewform.enterpriseCode" @change="(value) =>  enterpriseCodeChange(value as number)">
            <h-select-option v-for="(item, index) in enterpriseSelect" :value="item.id" :key="index">{{ item.name }}</h-select-option>
          </h-select>
        </h-form-item> -->
        <h-form-item label="审批人" name="username">
          <user-select :value="proxyReviewform.nickName" placeholder="执行人" :cache-key="'processSubUser'" :params="params" @change="(userInfo: IUserInfo | undefined) =>  userNameChange(userInfo)" />
        </h-form-item>
        <h-form-item label="主流程名称" name="subId">
          <h-select ref="select" mode="multiple" v-model:value="proxyReviewform.subId" @change="subIdChange" :filter-option="filterOption" show-search>
              <h-select-option v-for="(item, index) in listDefined" :text="item.name" :value="item.id.toString()" :key="index">{{ item.name }}</h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="流程名称" name="pdSubId">
          <h-select ref="select" mode="multiple" v-model:value="proxyReviewform.pdSubId" :filter-option="filterOption" show-search>
              <h-select-option v-for="(item, index) in pdSubIdList" :text="item.name" :value="item.id.toString()" :key="index">{{ item.name }}</h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="代审人" name="agentUser">
          <user-select :value="proxyReviewform.agentName" placeholder="代审人" :cache-key="'processSubUser'" :params="params" @change="(userInfo: IUserInfo | undefined) =>  agentNameChange(userInfo)" />
        </h-form-item>
      </h-form>
    </h-modal>

    <!-- 批量代审分配 -->
    <h-modal
      v-model:visible="batchEditBoxShow"
      title="代审分配"
      :width="600"
      class="modal"
      @cancel="$emit('cancel')"
      :confirmLoading="confirmLoading"
      @ok="editHandleOk"
    >
    <h-form
        ref="editForm"
        :model="editRow"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
        :rules="rules"
      >
        <!-- <h-form-item label="企业编码" name="enterpriseCode">
          <h-select ref="select" v-model:value="proxyReviewform.enterpriseCode" @change="(value) =>  enterpriseCodeChange(value as number)">
            <h-select-option v-for="(item, index) in enterpriseSelect" :value="item.id" :key="index">{{ item.name }}</h-select-option>
          </h-select>
        </h-form-item> -->
        <h-form-item label="流程名称" name="name">
          {{ editRow.scopeProcessSubDefinedName }}
        </h-form-item>
        <h-form-item label="审核人" name="description">
          {{ editRow.nickName }}({{ editRow.username }})
        </h-form-item>
        <h-form-item label="代审人" name="agentUser">
          <user-select :value="editRow.agentName" placeholder="代审人" :cache-key="'processSubUser'" :params="params" @change="(userInfo: IUserInfo | undefined) =>  editRowChange(userInfo)" />
        </h-form-item>
      </h-form>
    </h-modal>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
