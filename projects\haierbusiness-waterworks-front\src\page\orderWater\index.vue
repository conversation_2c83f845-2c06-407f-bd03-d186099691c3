<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import { RouteRecordRaw } from 'vue-router';
import FloatButton from '../../components/float-button.vue';
import router from '../../router';
// const currentRouter = useRouter()
// const appStore = useAppStore()
// const orderStore = useOrderStore()

const currentRouter = ref();

onMounted(async () => {
  currentRouter.value = await router;
});

watch(
  currentRouter,
  (newValue) => {
    if (newValue) {
      currentUrl.value = newValue.currentRoute.path;
    }
  },
  { immediate: true, deep: true },
);

const currentUrl = ref('');

const orderLinks = ref<Array<RouteRecordRaw>>();

const controlLinks = ref<Array<RouteRecordRaw>>();

onMounted(async () => {
  const thisRouter = await router;
  const routes = thisRouter.getRoutes();
  // 用户中心
  const orderTypes = routes.filter((o) => o.path.indexOf('/userCenter') != -1);
  if (orderTypes && orderTypes.length > 0) {
    orderLinks.value = orderTypes;
  }
  // 订单中心
  const controlTypes = routes.filter((o) => o.path.indexOf('/orderWater') != -1);
  if (controlTypes && controlTypes.length > 0) {
    controlLinks.value = controlTypes;
  }
});
</script>
<template>
  <FloatButton></FloatButton>
  <div style="min-height: 100vh" class="home">
    <div class="content">
      <div class="centerContent">
        <div class="left">
          <template v-if="orderLinks && orderLinks.length > 0">
            <div class="menu-type">用户中心</div>
            <div
              v-for="(item, index) in orderLinks"
              :key="index"
              class="menu"
              :class="{ active: item.path === currentUrl }"
            >
              <router-link :to="item.path"> {{ item.meta?.title }} </router-link>
            </div>
          </template>

          <template v-if="controlLinks && controlLinks.length > 0">
            <div class="menu-type">订单中心</div>
            <div
              v-for="(item, index) in controlLinks"
              :key="index"
              class="menu"
              :class="{ active: item.path === currentUrl }"
            >
              <router-link :to="item.path"> {{ item.meta?.title }} </router-link>
            </div>
          </template>
        </div>
        <div class="right">
          <router-view />
        </div>
      </div>
    </div>
    <!-- <e-footer></e-footer> -->
  </div>
</template>
<style lang="less">
.pointer {
  cursor: pointer;
}

.home {
  font-family: 'HarmonyBold';
  .mr-20 {
    margin-right: 20px;
  }

  .mr-13 {
    margin-right: 13px;
  }

  .mr-32 {
    margin-right: 13px;
  }

  .mr-26 {
    margin-right: 13px;
  }

  .font-10 {
    font-size: 10px;
  }

  .icon {
    width: 22px;
    height: 22px;
  }

  .vertical {
    width: 1px;
  }

  .pl-8 {
    padding-left: 8px;
  }

  .flex {
    display: flex;
  }
  .align-items {
    align-items: center;
  }
}

.header {
  display: flex;
  width: 100%;
  box-shadow: 0 0 3px #b2b2b2;
  height: 61px;
  justify-content: center;
  align-items: center;

  .logo {
    display: flex;
    width: 1400px;
    height: 28px;
  }
}

.content {
  background-color: #fff;
  min-height: calc(100vh - 86px) !important;
  // display: flex;
  // justify-content: center;
  width: 100%;
  font-family: 'Microsoft YaHei';

  .centerContent {
    // display: flex;
    margin: 0 auto;
    width: 1400px;
    display: flex;
    flex-direction: row;
    margin-top: 24px;

    .left {
      display: flex;
      width: 180px;
      flex-shrink: 0;
      border: 1px solid #eaeaea;
      min-height: calc(100vh - 106px) !important;
      flex-direction: column;
      margin-bottom: 20px;

      a {
        color: #000;
        &:hover {
          color: #0052d9;
        }
      }

      .menu-type {
        padding-top: 24px;
        padding-left: 33px;
        margin-bottom: 16px;
        border-left: 2px solid #fff;
        font-size: 16px;
        font-weight: 700;
        line-height: 18px;
      }

      .menu {
        padding-left: 43px;
        margin-bottom: 16px;
        cursor: pointer;
        border-left: 2px solid #fff;
        font-size: 14px;
      }

      .active {
        color: #0052d9;
        border-left: 2px solid #0052d9;

        a {
          color: #0052d9;
        }
      }
    }
    .right {
      display: flex;
      margin-left: 20px;
      width: 1200px;
    }
  }
}
</style>
