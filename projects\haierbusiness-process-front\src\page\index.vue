<script setup lang="ts">
import { showFailToast, <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>, showDialog } from 'vant';
import 'vant/es/toast/style'
import 'vant/es/button/style'
import 'vant/es/dialog/style'
import d from '@/assets/image/demo/d.png';
import xx from '@/assets/image/demo/xx.png';
import yszy from '@/assets/image/demo/yszy.png';
import logo from '@/assets/image/logo.png';
import complete from '@/assets/image/complete.png'
import payBanner from '@/assets/image/payBanner.jpeg';
import { Divider as hDivider, Space as hSpace, <PERSON><PERSON> as hButton, Col as hCol, Result as hResult, Row as hRow, TabPane as hTabPane, Tabs as hTabs, message } from 'ant-design-vue';


</script>

<template>
 
</template>

<style scoped lang="less">

</style>

<style>
:root:root {
  --van-button-primary-background: #0073E5;
  --van-radio-checked-icon-color: #0073E5;
  --van-password-input-background: #F2F2F2;
}
</style>
