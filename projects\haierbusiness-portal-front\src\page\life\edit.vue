<script lang="ts" setup>
import {
  Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
  Input as hInput, CheckboxGroup as hCheckboxGroup, Checkbox as hCheckbox, Row as hRow, Col as hCol, DatePicker as hDatePicker, message,
  Upload as hUpload, RadioGroup as hRadioGroup, Radio as hRadio, InputNumber as hInputNumber,
  Textarea as hTextarea, <PERSON><PERSON> as hButton, Switch as hSwitch
} from 'ant-design-vue';
import { computed, ref, watch, onMounted } from "vue";
import type { Ref } from "vue";
import {
  ILifeAccount, HeaderConstant
} from '@haierbusiness-front/common-libs';
import { useRequest } from 'vue-request';
import { lifeListApi, fileApi } from '@haierbusiness-front/apis';
import type { UploadChangeParam, UploadProps } from 'ant-design-vue';
import PlusOutlined from '@ant-design/icons-vue/PlusOutlined';
import LoadingOutlined from '@ant-design/icons-vue/LoadingOutlined';
import { UploadOutlined } from '@ant-design/icons-vue'
import type { Rule } from "ant-design-vue/es/form";
import { useRoute, useRouter } from 'vue-router'
import { toNumber } from "lodash-es"
import Editor from '@haierbusiness-front/components/editor/Editor.vue'
import type { IDomEditor } from "@wangeditor/editor"
import router from '../../router'
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil'

const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false)
const baseUrl = import.meta.env.VITE_BUSINESS_URL
// const router = useRouter()

const currentRouter = ref()


const from = ref();
const confirmLoading = ref(false);

const defaultData: ILifeAccount = {
  imgUrl: '',
  jumpLinkApp: '',
  jumpLinkPc: '',
  adSubject: ''
};

const checkDisUrlTip = (url: string) => {
  const reg = /^((https|http|ftp|rtsp|mms)?:\/\/)[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/;
  if (reg.test(url)) {
    return true;
  } else {
    return false;
  }
}

const id = ref<number>()
const isEdit = ref(false)

onMounted(async () => {
    currentRouter.value = await router
    isEdit.value = true
    const currentId = currentRouter.value.currentRoute.query?.id
    id.value = toNumber(currentId)
    if (id.value) {
      await get(id.value)
    }
    else {
        life.value = { showStatus: 1 }
        imageUrl.value = ''
    }
})

watch(() => currentRouter.value?.currentRoute.query, (newValue, oldValue) => {
  id.value = toNumber(newValue.id)
  if (id.value) {
    get(id.value)
  } 
  else {
    life.value = { showStatus: 1 }
    imageUrl.value = ''
  }
})

const get = async (id: number) => {
  const data = await lifeListApi.get(id)
  if(data && data.id) {
    life.value = data
    imageUrl.value = data.imgUrl || ''
  } 
}

const validateUrl = (_rule: Rule, value: string, name: string) => {
  if (value && value !== "") {
    if (checkDisUrlTip(value)) {
      return Promise.resolve();
    } else {
      return Promise.reject('必须以http或https开头的网页链接！');
    }
  } 
  return Promise.resolve();
}
const rules = {
  author: [{ required: true, message: "请输入作者！" }],
  jumpLinkApp: [
    { validator: (_rule: Rule, value: string) => validateUrl(_rule, value, '移动端链接'), trigger: 'change' }
  ],
  jumpLinkPc: [
    { validator: (_rule: Rule, value: string) => validateUrl(_rule, value, 'PC端链接'), trigger: 'change' }
  ],
  showStatus: [
    { required: true, message: '请选择展示状态！' }
  ],
  adSubject: [
    { required: true, message: '请填写标题！' }
  ],
};

const life = ref<ILifeAccount>({});

const fileList = ref<Array<any>>([]);
const loading = ref<boolean>(false);
const imageUrl = ref<string>('');

const upload = async (options:any) => {
  loading.value = true;
  const formData = new FormData()
  formData.append('file', options.file)
  const res = await fileApi.upload(formData)
  const file = {
      ...options.file,
      name: options.file.name,
      url: baseUrl + res.path
  }
  loading.value = false;
  fileList.value = [...fileList.value, file]
  imageUrl.value = baseUrl + res.path
  life.value.imgUrl = baseUrl + res.path
  
  options.onProgress(100)
  options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
}

const beforeUpload = (file: any) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('只允许上传图片类型');
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('Image must smaller than 2MB!');
  }
  return isJpgOrPng && isLt2M;
};
const handleOk = () => {
  confirmLoading.value = true;
  if (!life.value.content) {
        if(!life.value.jumpLinkApp && !life.value.jumpLinkPc) {
          message.error(`链接和详情页不能同时为空!`);
          confirmLoading.value = false;
          return
        }
        else if(life.value.jumpLinkApp && life.value.jumpLinkPc) {

        }
        else {
          message.error(`PC端链接和移动端链接需要同时存在!`);
          confirmLoading.value = false;
          return
        }
      }
  from.value.validate()
    .then(() => {
      
      const data = {
        ...life.value,
      }
      if (data.id) {
        lifeListApi.edit(data).then(res => {
          message.success(`编辑成功!`);
          currentRouter.value.push({ path: "/portal/life/list"})
        })
      } else {
        lifeListApi.save(data).then(res => {
          message.success(`新增成功!`);
          currentRouter.value.push({ path: "/portal/life/list"})
        })
      }
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};
const handleRemove = () => {
  fileList.value = []
  imageUrl.value = ''
}

const onEditorChange = (editor: IDomEditor) => {
  life.value.content = editor.getHtml()
}

const uploadUrl = import.meta.env.VITE_UPLOAD_URL

const key = ref(0)

</script>
<script lang="ts">
  export default {
    name: "lifeEdit",
  };
</script>

<template>
    <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
        <h-form ref="from" :model="life" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules" @finish="handleOk">
            <h-form-item label="标题" name="adSubject">
              <h-input v-model:value="life.adSubject" />
            </h-form-item>
            <h-form-item label="作者" name="author">
              <h-input v-model:value="life.author" />
            </h-form-item>
            <h-form-item label="图片" name="imgUrl" class="upload">
              <h-row>
                  <h-col :span="24">
                    <h-upload
                        list-type="picture-card"
                        class="avatar-uploader"
                        :file-list="fileList"
                        :max-count="1"
                        :show-upload-list="false"
                        name="avatar"
                        :custom-request="upload"
                        :before-upload="beforeUpload"
                        :headers="{
                            'Hb-Token': token
                        }"
                    >
                      <img v-if="imageUrl" :src="imageUrl" class="imgShow" />
                      <div v-else>
                        <loading-outlined v-if="loading"></loading-outlined>
                        <plus-outlined v-else></plus-outlined>
                        <div>上传图片</div>
                      </div>
                    </h-upload>
                  </h-col>
                  <h-col :span="24">
                  请上传图片的尺寸为<span class="important">981*765</span> 大小不超过<span class="important">2MB</span> 格式为<span
                      class="important">png/jpg/jpeg</span>的文件
                  </h-col>
              </h-row>
            </h-form-item>
            <h-form-item label="PC端链接" name="jumpLinkPc">
              <h-textarea v-model:value="life.jumpLinkPc" />
            </h-form-item>
            <h-form-item label="移动端链接" name="jumpLinkApp">
              <h-textarea v-model:value="life.jumpLinkApp" />
            </h-form-item>
            <h-form-item v-if="isEdit" label="详情页" name="content">
                <editor :currentKey="key" height="300px" :modelValue="life.content" @change="onEditorChange" style="z-index: 20"
                :uploadUrl="uploadUrl" />
            </h-form-item>
            <h-form-item label="是否置顶" name="isTopping">
              <h-switch v-model:checked="life.isTopping" :checked-value="1" :un-checked-value="0" />
            </h-form-item>
            <h-form-item label="展示状态" name="showStatus">
            <h-radio-group v-model:value="life.showStatus">
                <h-radio :value=1>展示</h-radio>
                <h-radio :value=0>隐藏</h-radio>
            </h-radio-group>
            </h-form-item>
            <div class="submit-btn">
                <h-button type="primary" shape="round" html-type="submit" class="sub-btn">提交</h-button>
            </div>
        </h-form>
    </div>

</template>


<style lang="less" scoped>
.submit-btn {
  width: 100%;
  display: flex;
  justify-content: center;
  padding-bottom: 20px;
}

.sub-btn {
  width: 200px;
  
}

.important {
  color: red;
}

.imgShow {
  width: 100%;
}

.avatar-uploader {
  :deep(.bwdv-upload.bwdv-upload-select-picture-card) {
    width: 327px !important;
    height: 255px !important;
    overflow: hidden;
    border-radius: 8px;
  }
}
</style>
<style>
.upload .ant-form-item-label >label::before {
    display: inline-block;
    margin-inline-end: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun,sans-serif;
    line-height: 1;
    content: "*";
}
</style>
  