<script setup lang="ts">
import { onMounted, ref, watch,onUnmounted } from 'vue';
import dayjs from 'dayjs';
import { Dayjs } from 'dayjs';

interface Props {
    travelType?: number
    showReturn?: boolean
    beginTime?:string
    endTime?:string
    planBeginDate?: string;
    planEndDate?: string;
}

const props = withDefaults(defineProps<Props>(), {
    travelType: 1,
    showReturn: true,
    beginTime: '',
    planBeginDate:'',
    planEndDate:'',
    endTime: ''
});

const emit = defineEmits(['setBeginTime', 'setEndTime'])


const beginTime = ref(props.beginTime)
const endTime = ref(props.endTime)


// 单程，返程
const travelType = ref(props.travelType)
const showReturn = ref(props.showReturn)

watch(props, (newValue) => {
    travelType.value = newValue.travelType
    beginTime.value = newValue.beginTime
    endTime.value = newValue.endTime

})


watch(beginTime, (newValue) => {
    emit('setBeginTime', newValue)

})

watch(endTime, (newValue) => {
    emit('setEndTime', newValue)

})

const disabledDateBegin = (current: Dayjs) => {
  if(props.planBeginDate && props.planEndDate) {
    return (current && current < dayjs(props.planBeginDate).endOf('day')) || (current && current > dayjs(props.planEndDate).endOf('day'))
  }else {
    return false
  }
};

// 开始时间作为禁选时间
const disabledDateEnd = (current: Dayjs) => {
  if (beginTime.value) {
    return current && current < dayjs(dayjs(beginTime.value).format());
  } else {
    return current && current < dayjs(dayjs().subtract(1, 'day').add(1, 'minute').format());
  }
};

console.log('travelType:' + travelType.value)
</script>

<template>
    <div class="apply-date-picker-component">
        <div class="ticket-item" :class="{ 'international-right-width': travelType === 3 }">
            <div class="item-row">
                <div class="item-start">
                    <div class="item-labels">出发日期</div>
                    <a-date-picker :disabled-date="disabledDateBegin" v-model:value="beginTime" valueFormat="YYYY-MM-DD" placeholder="请选择" :bordered="false" class="item-time">
                    <template #suffixIcon>  
                    </template>
                    </a-date-picker>
                </div>
                <div class="to" v-if="travelType === 2"></div>
                <div class="item-end" v-if="travelType === 2">
                    <div class="item-labels">返回日期</div>
                    <a-date-picker :disabled-date="disabledDateEnd" v-model:value="endTime" valueFormat="YYYY-MM-DD" placeholder="请选择" :bordered="false" class="item-end-time">
                    <template #suffixIcon>  
                    </template>
                    </a-date-picker>
                </div>
            </div>
            <div class="add-return pointer" @click="$emit('change', 2)" v-if="travelType === 1 && showReturn">添加返程</div>
        </div>
    </div>
</template>

<style lang="less" scoped>
@import url('./common.less');

.pointer {
    cursor: pointer;
}


</style>

<style>

</style>
