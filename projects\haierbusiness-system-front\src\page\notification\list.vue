<script setup lang="ts">
import {
  DatePicker as hDatePicker,
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Table as hTable,
  BreadcrumbItem as hBreadcrumbItem,
  Breadcrumb as hBreadcrumb,
  LayoutSider as hLayoutSider,
  LayoutFooter as hLayoutFooter,
  LayoutContent as hLayoutContent,
  LayoutHeader as hLayoutHeader,
  Layout as hLayout,
  MenuItem as hMenuItem,
  MenuItemGroup as hMenuItemGroup,
  SubMenu as hSubMenu,
  Menu as hMenu,
  Divider as hDivider,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Result as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Popconfirm as hPopconfirm,
  Tabs as hTabs,
  message,
  TableProps,
  RangePicker as hRangePicker
} from "ant-design-vue";
import { computed, onMounted, ref } from "vue";
import {
  UploadOutlined,
  UserOutlined,
  NotificationOutlined,
  AppstoreOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PlusOutlined,
  SearchOutlined
} from "@ant-design/icons-vue";
import { notificationApi } from "@haierbusiness-front/apis";
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { usePagination, useRequest } from "vue-request";
import dayjs from 'dayjs'
import {
  SearchListRes,
  sendStatusConstant,
  approvalStatusConstant
} from "@haierbusiness-front/common-libs";
import { getEnumOptions } from "@haierbusiness-front/utils";
import router from "../../router";

const sendStatus = computed(() => {
  return getEnumOptions(sendStatusConstant, true);
});

const approvalStatus = computed(() => {
  return getEnumOptions(approvalStatusConstant, true);
});

const columns:ColumnType[] = [
  {
    title: "通知编码",
    dataIndex: "serialNumber",
     align: 'center',
    ellipsis: true,
    width: "200px"
  },
  {
    title: "经办人",
    dataIndex: "operatorName",
     align: 'center',
    ellipsis: true,
    width: "150px"
  },
  {
    title: "预计发送时间",
    dataIndex: "scheduledTime",
     align: 'center',
    ellipsis: true,
    width: "180px"
  },
    {
    title: "实际发送时间",
    dataIndex: "actualSendTime",
     align: 'center',
    ellipsis: true,
    width: "180px"
  },
  {
    title: "通知标题",
    dataIndex: "title",
     align: 'center',
    ellipsis: true,
    width: "200px"
  },
    {
    title: "通知状态",
    dataIndex: "sendStatus",
    align: 'center',
    ellipsis: true,
    width: "100px"
  },
  {
    title: "审核状态",
    dataIndex: "approvalStatus",
    align: 'center',
    ellipsis: true,
    width: "100px"
  },
  {
    title: "接收角色",
    dataIndex: "receiverRole",
     align: 'center',
    ellipsis: true,
    width: "200px"
  },
  {
    title: "应发/实发/已读",
    dataIndex: "readDesc",
     align: 'center',
    ellipsis: true,
    width: "150px"
  },
  {
    title: "操作",
    dataIndex: "operator",
     align: 'center',
    width: "180px",
    fixed: 'right',
  }
];
const currentRouter = ref<any>(null);
const { data, run: listRun, loading, current, pageSize } = usePagination(
  notificationApi.list
);
const roleList = ref<any>([]);
const searchForm = ref<SearchListRes>({});
const dataSource = computed(() => data.value?.records || []);
const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: "center" }
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any
) => {
  listRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
    ...searchForm.value
  });
};

// 新增
const gotoSave = () => {
  currentRouter.value.push("/notification/add");
};

// 查看详情
const gotoDetails = (id: string | number) => {
  currentRouter.value.push("/notification/details?id=" + id);
};

// 新增表单相关
const userNewForm = ref();
const confirmLoading = ref(false);
const visibleNew = ref(false);
const labelCol = { span: 5 };
const wrapperCol = { span: 14 };
const handleOk = () => {
  userNewForm.value.validate().then(() => {
    confirmLoading.value = true;
    notificationApi
      .save(searchForm.value)
      .then(it => {
        message.success("操作成功！");
        visibleNew.value = false;
        handleTableChange({ current: 1, pageSize: 10 });
        searchForm.value = {};
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  });
};

// 获取所有接收角色
const getAllRole = () => {
  notificationApi.getAllRole().then(res => {
    roleList.value = res;
  });
};

const scheduledTimeChange = (val: any) => {
  if (val) {
    searchForm.value.scheduledTimeBegin = val[0];
    searchForm.value.scheduledTimeEnd = val[1];
  } else {
    searchForm.value.scheduledTimeBegin = "";
    searchForm.value.scheduledTimeEnd = "";
  }
};
// 撤回消息
const revokeMessage = (id: string) => {
  notificationApi.getRevokeMessage(id).then(() => {
    message.success("撤回成功！");
    listRun({
      pageNum: 1,
      pageSize: 10
    });
  });
};

// 终止消息
const endMessage = (id: string) => {
  notificationApi.getBreakSend(id).then(() => {
    message.success("终止成功！");
    listRun({
      pageNum: 1,
      pageSize: 10
    });
  });
};

const getSendStatusTagColor = (sendStatus:string|number) =>{
  if(sendStatus==20){
    return 'green'
  }else if(sendStatus==10){
    return 'cyan'
  }else if(sendStatus==15){
    return 'blue'
  }else if(sendStatus==30){
    return 'pink'
  }else if(sendStatus==35){
    return 'orange'
  }else if(sendStatus==40){
    return 'red'
  }else{
    return ''
  }
}

const getApprovalStatusTagColor = (approvalStatus:string|number) =>{
  if(approvalStatus==20){
    return 'green'
  }else if(approvalStatus=='00'){
    return 'cyan'
  }else if(approvalStatus==10){
    return 'blue'
  }else if(approvalStatus==40){
    return 'pink'
  }else if(approvalStatus==30){
    return 'red'
  }else{
    return ''
  }
}


// 重置
const rest = () => {
  searchForm.value = {};
  listRun({
    pageNum: 1,
    pageSize: 10
  });
};

const disabledDateEnd = (current:any) =>{
  if(!searchForm.value.scheduledTimeBegin){
    return
  }else{
    return current && current < dayjs(searchForm.value.scheduledTimeBegin).startOf('day');
  }
}

const disabledDateTimeEnd=(current:any)=>{
   console.log(current,new Date(searchForm.value.scheduledTimeBegin).getHours(),new Date(),'current')
    if(!searchForm.value.scheduledTimeBegin){
      return
    }
    if(dayjs(searchForm.value.scheduledTimeBegin).format('YYYY-MM-DD')==dayjs(current).format('YYYY-MM-DD')){
      let nowHour = new Date(searchForm.value.scheduledTimeBegin).getHours()
      let nowMinutes = new Date(searchForm.value.scheduledTimeBegin).getMinutes()
      return {
        disabledHours: () => range(0, nowHour),
        disabledMinutes: () => range(0, nowMinutes),
        disabledSeconds: () => [0, 60],
      }
    }
}

const disabledDateBegin = (current:any) =>{
  if(!searchForm.value.scheduledTimeEnd){
    return
  }else{
    return current && current > dayjs(searchForm.value.scheduledTimeEnd).startOf('day');
  }
}

const disabledDateTimeBegin=(current:any)=>{
   console.log(current,new Date(searchForm.value.scheduledTimeEnd).getHours(),new Date(),'current')
    if(!searchForm.value.scheduledTimeEnd){
      return
    }
    if(dayjs(searchForm.value.scheduledTimeEnd).format('YYYY-MM-DD')==dayjs(current).format('YYYY-MM-DD')){
      let nowHour = new Date(searchForm.value.scheduledTimeEnd).getHours()
      let nowMinutes = new Date(searchForm.value.scheduledTimeEnd).getMinutes()
      return {
        disabledHours: () => range(nowHour,24),
        disabledMinutes: () => range(nowMinutes,60),
        disabledSeconds: () => [0, 60],
      }
    }
}

const range = (start, end)=>{
      const result = []
      for (let i = start; i < end; i++) {
        result.push(i)
      }
      return result
}

onMounted(async () => {
  currentRouter.value = await router;
  listRun({
    pageNum: 1,
    pageSize: 10
  });
  getAllRole();
});
</script>

<template>
  <div
    style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;"
  >
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="operator">经办人工号：</label>
          </h-col>
          <h-col :span="4">
            <h-input allowClear v-model:value="searchForm.operator" placeholder autocomplete="off" />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="title">消息标题：</label>
          </h-col>
          <h-col :span="4">
            <h-input allowClear v-model:value="searchForm.title" placeholder autocomplete="off" />
          </h-col>
                    <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="receiverRole">接收角色：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value.prop="searchForm.receiverRole"
              style="width: 100%"
              allow-clear
            >
              <h-select-option v-for="item in roleList" :value="item.roleCode">{{item.roleName}}</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="sendStatus">通知状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value.prop="searchForm.sendStatus"
              style="width: 100%"
              allow-clear
            >
              <h-select-option v-for="item in sendStatus" :value="item.value">{{item.label}}</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="approvalStatus">审批状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value.prop="searchForm.approvalStatus"
              style="width: 100%"
              allow-clear
            >
              <h-select-option v-for="item in approvalStatus" :value="item.value">{{item.label}}</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="scheduledTime">预计开始时间：</label>
          </h-col>
          <h-col :span="4">
            <h-date-picker
              valueFormat="YYYY-MM-DD HH:mm:ss"
              show-time
              :disabledDate="disabledDateBegin"
              :disabledTime="disabledDateTimeBegin"
              v-model:value="searchForm.scheduledTimeBegin"
              style="width:100%;"
            />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="scheduledTime">预计结束时间：</label>
          </h-col>
          <h-col :span="4">
            <h-date-picker
              valueFormat="YYYY-MM-DD HH:mm:ss"
              show-time
              :disabledDate="disabledDateEnd"
              :disabledTime="disabledDateTimeEnd"
              v-model:value="searchForm.scheduledTimeEnd"
              style="width:100%;"
            />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :offset="20" :span="4" style="text-align: right;">
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
            <h-button @click="rest" style="margin-left: 10px">重置</h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: left;">
            <h-button type="primary" @click="gotoSave">
              <PlusOutlined />新增通知
            </h-button>
            <!-- <h-button style="margin-left: 10px">重置</h-button> -->
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :row-key="record => record.id"
          :data-source="dataSource"
          :pagination="pagination"
          size="small"
          :loading="loading"
          :scroll="{ y: 550,x:1600 }"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template
              v-if="column.dataIndex === 'enterprise' && record.enterpriseCode"
            >{{ record.enterpriseName }}({{ record.enterpriseCode }})</template>
            <template
              v-if="column.dataIndex === 'receiverRole'"
            >{{roleList.find(obj => obj.roleCode === record.receiverRole)?.roleName}}</template>
            <template
              v-if="column.dataIndex === 'operatorName'"
            >{{ record.operatorName }}({{record.operator}})</template>
            <template
              v-if="column.dataIndex === 'sendStatus'"
            >
              <a-tag :color="getSendStatusTagColor(record.sendStatus)">{{ sendStatusConstant[record.sendStatus] }}</a-tag>
            </template>
            <template
              v-if="column.dataIndex === 'approvalStatus'"
            >
              <a-tag :color="getApprovalStatusTagColor(record.approvalStatus)">{{ approvalStatusConstant[record.approvalStatus] }}</a-tag>
            </template>
            <template v-if="column.dataIndex === 'operator'">
              <h-button @click="gotoDetails(record.id)" type="link" size="small">查看详情</h-button>
              <h-popconfirm
                v-if="record.sendStatus == 20 || record.sendStatus == 40"
                title="确定要撤回此消息吗?"
                @confirm="revokeMessage(record.id)"
              >
                <h-button type="link" size="small">撤回消息</h-button>
              </h-popconfirm>
              <h-popconfirm
                v-if="record.sendStatus == 15"
                title="确定要终止发送此消息吗?"
                @confirm="endMessage(record.id)"
              >
                <h-button type="link" size="small">终止发送</h-button>
              </h-popconfirm>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}
</style>
