<script setup lang="ts">
import { Tag as hTag,Card as hCard, <PERSON>lapse as hCollapse, CollapsePanel as hCollapsePanel, DatePicker as hDatePicker, Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem, Input as hInput, Table as hTable, BreadcrumbItem as hBreadcrumbItem, Breadcrumb as hBreadcrumb, LayoutSider as hLayoutSider, LayoutFooter as hLayoutFooter, LayoutContent as hLayoutContent, LayoutHeader as hLayoutHeader, Layout as hLayout, MenuItem as hMenuItem, MenuItemGroup as hMenuItemGroup, SubMenu as hSubMenu, Menu as hMenu, Divider as hDivider, Space as hSpace, Button as hButton, Col as hCol, Result as hResult, Row as hRow, TabPane as hTabPane, Tabs as hTabs, message, TableProps } from 'ant-design-vue';
import { computed, onMounted, ref, watch } from 'vue';
import { UploadOutlined, UserOutlined, NotificationOutlined, AppstoreOutlined, MenuFoldOutlined, MenuUnfoldOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { b2bWyyBalanceApi, budgetHaierPayRecordApi, payApi } from '@haierbusiness-front/apis';
import { usePagination, useRequest } from 'vue-request';
import { PayNotifyStateConstant,RefundStatusConstant,BudgetNotifiedReleaseCvpStateConstant, BalanceStatusConstant, IHaierAccountBillInfo, IUserSaveUpdateRequest, IWyyB2bAccountRequest, IWyyB2bDetailsRequest, IPaymentRecordListResponse, PayTypeConstant, PayStatusConstant, HaierBudgetTypeConstant, HaierBudgetSourceConstant } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { resolveParam ,copyValue} from '@haierbusiness-front/utils';
import { ColumnType } from 'ant-design-vue/lib/table';

const columns: ColumnType[] = [

  {
    title: '类型',
    dataIndex: 'bizTypeName',
    width: '70px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '步骤',
    dataIndex: 'stepName',
    width: '70px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '70px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '内容',
    dataIndex: 'content',
    width: '700px',
    align: 'center'
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  }
];



const props = defineProps({
  query: Object
});
const recordData = computed(() => <IPaymentRecordListResponse>resolveParam(props.query?.record));
const {
  data,
  run: logsApiRun,
  loading,
} = useRequest(payApi.logs, {
  defaultParams: [
    {
      recordCode: recordData.value.code
    }
  ],
  manual: false
});

// 查询预算相关信息
const {
  data: budgetHaierPayRecordApiData,
  run: budgetHaierPayRecordApiRun,
  loading: budgetHaierPayRecordApiLoading
} = useRequest(budgetHaierPayRecordApi.list, {
  defaultParams: [
    {
      paymentCode: recordData.value.code
    }
  ],
  manual: false
});

const budgetDataSource = computed(() => budgetHaierPayRecordApiData.value?.records || []);

// 查询退款信息
const {
  data: refundRecordAllApiData,
  run: refundRecordAllApiRun,
  loading: refundRecordAllApiLoading
} = useRequest(payApi.refundRecordAllList, {
  defaultParams: [
    {
      paymentRecordCode: recordData.value.code
    }
  ],
  manual: false
});

const refundDataSource = computed(() => refundRecordAllApiData.value?.records || []);

const {
  data: detailsExportData,
  run: detailsExportApiRun,
  loading: detailsExportLoading,
} = useRequest(b2bWyyBalanceApi.detailsExport);

const dataSource = computed(() => data.value?.records || []);
const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

watch(() => props.query, (n: any, o: any) => {
  budgetHaierPayRecordApiRun({ paymentCode: recordData.value.code })
  refundRecordAllApiRun({ paymentRecordCode: recordData.value.code })
  logsApiRun({ recordCode: recordData.value.code })
});

const gotoAttachment = (url: string) => {
  window.open(url)
}
const collapseAactiveKey = ref(["1","2","3"])
</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-collapse v-model:activeKey="collapseAactiveKey" style="font-size: 14px;font-weight: 600;" ghost>
      <h-collapse-panel key="1">
        <template #header>
          <div style="display: flex;">
            <div style="height: 22px;width: 5px;border-radius: 2px;background-color: #0073e5;margin-right: 10px;"></div>
            <span style="font-weight: 600;font-size: 15px;">支付记录详情</span>
          </div>
        </template>
        <div style="padding: 0 20px 0px 20px;line-height: 28px;">
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">支付单号：</h-col>
            <h-col span="5">{{ recordData.code }}</h-col>
            <h-col span="3" style="text-align: right;">业务单号：</h-col>
            <h-col span="5">{{ recordData.businessCode }}</h-col>
            <h-col span="3" style="text-align: right;">支付金额：</h-col>
            <h-col span="5">{{ recordData.amount }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">支付状态：</h-col>
            <h-col span="5">
              <h-tag v-if="recordData.state === PayStatusConstant.CANCEL.type" color="default">{{
                PayStatusConstant.CANCEL.name
              }}
              </h-tag>
              <h-tag v-if="recordData.state === PayStatusConstant.SAVE.type" color="processing">{{
                PayStatusConstant.SAVE.name
              }}
              </h-tag>
              <h-tag v-if="recordData.state === PayStatusConstant.ADVANCE.type" color="warning">{{
                PayStatusConstant.ADVANCE.name
              }}
              </h-tag>
              <h-tag v-if="recordData.state === PayStatusConstant.SUCCESS.type" color="success">{{
                PayStatusConstant.SUCCESS.name
              }}
              </h-tag>
              <h-tag v-if="recordData.state === PayStatusConstant.ERROR.type" color="error">{{
                PayStatusConstant.ERROR.name
              }}
              </h-tag>
            </h-col>
            <h-col span="3" style="text-align: right;">支付类型：</h-col>
            <h-col span="5">{{ PayTypeConstant.ofType(recordData.payType)?.name }}</h-col>
            <h-col span="3" style="text-align: right;">支付子类型：</h-col>
            <h-col span="5">{{ recordData.paySubtype }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">支付平台单号：</h-col>
            <h-col span="5" class="text-ellipsis" :title="recordData.notifyUrl" @dblclick="copyValue(recordData.notifyUrl)">{{ recordData.providerOrderCode }}</h-col>
            <h-col span="3" style="text-align: right;">二级支付单号：</h-col>
            <h-col span="5">{{ recordData.secondProviderOrderCode }}</h-col>
            <h-col span="3" style="text-align: right;">支付账户：</h-col>
            <h-col span="5">{{ recordData.accountCode }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">支付人：</h-col>
            <h-col span="5">{{ recordData.owner }}/{{ recordData.ownerName }}</h-col>
            <h-col span="3" style="text-align: right;">应用编码：</h-col>
            <h-col span="5">{{ recordData.applicationCode }}</h-col>
            <h-col span="3" style="text-align: right;">企业编码：</h-col>
            <h-col span="5">{{ recordData.enterprise }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">发起支付时间：</h-col>
            <h-col span="5">{{ recordData.payTime }}</h-col>
            <h-col span="3" style="text-align: right;">支付成功时间：</h-col>
            <h-col span="5">{{ recordData.successPayTime }}</h-col>
            <h-col span="3" style="text-align: right;">创建人：</h-col>
            <h-col span="5">{{ recordData.createBy }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">通知回调状态：</h-col>
            <h-col span="5" class="text-ellipsis">
              <h-tag v-if="recordData.notifyState === PayNotifyStateConstant.SUCCESS.code" color="success">{{
                  PayNotifyStateConstant.SUCCESS.name
                }}
              </h-tag>
              <h-tag v-if="recordData.notifyState === PayNotifyStateConstant.RETRY.code" color="warning">{{
                  PayNotifyStateConstant.RETRY.name
                }}
              </h-tag>
              <h-tag v-if="recordData.notifyState === PayNotifyStateConstant.ERROR.code" color="error">{{
                  PayNotifyStateConstant.ERROR.name
                }}
              </h-tag>
            </h-col>
            <h-col span="3" style="text-align: right;">已重试次数：</h-col>
            <h-col span="5">{{ recordData.notifyRetry }}</h-col>
            <h-col span="3" style="text-align: right;">通知失败原因：</h-col>
            <h-col span="5" class="text-ellipsis" :title="recordData.notifyErrorMessage" @dblclick="copyValue(recordData.notifyErrorMessage)">{{ recordData.notifyErrorMessage }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">预支付通知回调状态：</h-col>
            <h-col span="5" class="text-ellipsis">
              <h-tag v-if="recordData.preNotifyState === PayNotifyStateConstant.SUCCESS.code" color="success">{{
                  PayNotifyStateConstant.SUCCESS.name
                }}
              </h-tag>
              <h-tag v-if="recordData.preNotifyState === PayNotifyStateConstant.RETRY.code" color="warning">{{
                  PayNotifyStateConstant.RETRY.name
                }}
              </h-tag>
              <h-tag v-if="recordData.preNotifyState === PayNotifyStateConstant.ERROR.code" color="error">{{
                  PayNotifyStateConstant.ERROR.name
                }}
              </h-tag>
            </h-col>
            <h-col span="3" style="text-align: right;">预支付已重试次数：</h-col>
            <h-col span="5">{{ recordData.preNotifyRetry }}</h-col>
            <h-col span="3" style="text-align: right;">预支付通知失败原因：</h-col>
            <h-col span="5" class="text-ellipsis" :title="recordData.preNotifyErrorMessage" @dblclick="copyValue(recordData.preNotifyErrorMessage)">{{ recordData.preNotifyErrorMessage }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">支付回调地址：</h-col>
            <h-col span="5" class="text-ellipsis" :title="recordData.notifyUrl" @dblclick="copyValue(recordData.notifyUrl)">{{ recordData.notifyUrl }}</h-col>
            <h-col span="3" style="text-align: right;">跳转地址：</h-col>
            <h-col span="5" class="text-ellipsis" :title="recordData.callbackUrl" @dblclick="copyValue(recordData.notifyUrl)">{{ recordData.callbackUrl }}</h-col>
            <h-col span="3" style="text-align: right;">创建时间：</h-col>
            <h-col span="5">{{ recordData.gmtCreate }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">最后修改人：</h-col>
            <h-col span="5">{{ recordData.lastModifiedBy }}</h-col>
            <h-col span="3" style="text-align: right;">最后修改时间：</h-col>
            <h-col span="5">{{ recordData.gmtModified }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="24"><h-divider /></h-col>
          </h-row>
        </div>
      </h-collapse-panel>
      <h-collapse-panel v-if="budgetDataSource && budgetDataSource.length > 0" key="2">
        <template #header>
          <div style="display: flex;align-items: center;">
            <div style="height: 22px;width: 5px;border-radius: 2px;background-color: #0073e5;margin-right: 10px;"></div>
            <span style="font-weight: 600;font-size: 15px;">预算支付信息</span>
          </div>
        </template>
        <div v-if="budgetDataSource[0].budgetSysCode === HaierBudgetSourceConstant.BCC.code" style="padding: 0 20px 20px 20px;line-height: 28px;">
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">预算单号：</h-col>
            <h-col span="5">{{ budgetDataSource[0].budgetCode }}</h-col>
            <h-col span="3" style="text-align: right;">预算系统：</h-col>
            <h-col span="5">{{ budgetDataSource[0].budgetSysCode }}</h-col>
            <h-col span="3" style="text-align: right;">预算来源：</h-col>
            <h-col span="5">{{ budgetDataSource[0].source }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">预算部门：</h-col>
            <h-col span="5">{{ budgetDataSource[0].budgetDepartmentCode }}/{{ budgetDataSource[0].budgetDepartmentName
            }}</h-col>
            <h-col span="3" style="text-align: right;">费用项目：</h-col>
            <h-col span="5">{{ budgetDataSource[0].feeItem }}/{{ budgetDataSource[0].feeItemName }}</h-col>
            <h-col span="3" style="text-align: right;">立项：</h-col>
            <h-col span="5">{{ budgetDataSource[0].itemCode }}/{{ budgetDataSource[0].itemName }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">结算单位：</h-col>
            <h-col span="5">{{ budgetDataSource[0].accountCompanyCode }}/{{ budgetDataSource[0].accountCompanyName
            }}</h-col>
            <h-col span="3" style="text-align: right;">客户：</h-col>
            <h-col span="5">{{ budgetDataSource[0].customCode }}/{{ budgetDataSource[0].customName }}</h-col>
            <h-col span="3" style="text-align: right;">供应商：</h-col>
            <h-col span="5">{{ budgetDataSource[0].providerName }}（{{ budgetDataSource[0].providerCode }}/{{
              budgetDataSource[0].providerOverseasCode }}）</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">申请人：</h-col>
            <h-col span="5">{{ budgetDataSource[0].applicantCode }}/{{ budgetDataSource[0].applicantName }}</h-col>
            <h-col span="3" style="text-align: right;">研发项目：</h-col>
            <h-col span="5">{{ budgetDataSource[0].projectCode }}/{{ budgetDataSource[0].projectName }}</h-col>
            <h-col span="3" style="text-align: right;">WBS：</h-col>
            <h-col span="5">{{ budgetDataSource[0].wbsCode }}/{{ budgetDataSource[0].wbsName }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">销售类型：</h-col>
            <h-col span="5">
              <span v-if="budgetDataSource[0].saleType === 1">内销</span>
              <span v-if="budgetDataSource[0].saleType === 2">外销</span>
            </h-col>
            <h-col span="3" style="text-align: right;">地产项目：</h-col>
            <h-col span="5">{{ budgetDataSource[0].dcProjectCode }}/{{ budgetDataSource[0].dcProjectName }}</h-col>
            <h-col span="3" style="text-align: right;">地产分期：</h-col>
            <h-col span="5">{{ budgetDataSource[0].dcItemCode }}/{{ budgetDataSource[0].dcItemName }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">创建人：</h-col>
            <h-col span="5">{{ recordData.createBy }}</h-col>
            <h-col span="3" style="text-align: right;">创建时间：</h-col>
            <h-col span="5">{{ recordData.gmtCreate }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">最后修改人：</h-col>
            <h-col span="5">{{ recordData.lastModifiedBy }}</h-col>
            <h-col span="3" style="text-align: right;">最后修改时间：</h-col>
            <h-col span="5">{{ recordData.gmtModified }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="24"><h-divider /></h-col>
          </h-row>
        </div>
        <div v-if="budgetDataSource[0].budgetSysCode !== HaierBudgetSourceConstant.BCC.code" style="padding: 0 20px 20px 20px;line-height: 28px;">
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">预算单号：</h-col>
            <h-col span="5">{{ budgetDataSource[0].budgetCode }}</h-col>
            <h-col span="3" style="text-align: right;">预算系统：</h-col>
            <h-col span="5">{{ budgetDataSource[0].budgetSysCode }}</h-col>
            <h-col span="3" style="text-align: right;">预算来源：</h-col>
            <h-col span="5">{{ budgetDataSource[0].source }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">预算人：</h-col>
            <h-col span="5">{{ budgetDataSource[0].budgeterCode }}/{{ budgetDataSource[0].budgeterName }}</h-col>
            <h-col span="3" style="text-align: right;">预算部门：</h-col>
            <h-col span="5">{{ budgetDataSource[0].budgetDepartmentCode }}/{{ budgetDataSource[0].budgetDepartmentName
            }}</h-col>
            <h-col span="3" style="text-align: right;">费用项目：</h-col>
            <h-col span="5">{{ budgetDataSource[0].feeItem }}/{{ budgetDataSource[0].feeItemName }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">结算单位：</h-col>
            <h-col span="5">{{ budgetDataSource[0].accountCompanyCode }}/{{ budgetDataSource[0].accountCompanyName
            }}</h-col>
            <h-col span="3" style="text-align: right;">客户：</h-col>
            <h-col span="5">{{ budgetDataSource[0].customCode }}/{{ budgetDataSource[0].customName }}</h-col>
            <h-col span="3" style="text-align: right;">供应商：</h-col>
            <h-col span="5">{{ budgetDataSource[0].providerName }}（{{ budgetDataSource[0].providerCode }}/{{
              budgetDataSource[0].providerOverseasCode }}）</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">申请人：</h-col>
            <h-col span="5">{{ budgetDataSource[0].applicantCode }}/{{ budgetDataSource[0].applicantName }}</h-col>
            <h-col span="3" style="text-align: right;">GEMS预算单元：</h-col>
            <h-col span="5">{{ budgetDataSource[0].unitCode }}/{{ budgetDataSource[0].unitName }}</h-col>
            <h-col span="3" style="text-align: right;">创建时间：</h-col>
            <h-col span="5">{{ recordData.gmtCreate }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">创建人：</h-col>
            <h-col span="5">{{ recordData.createBy }}</h-col>
            <h-col span="3" style="text-align: right;">最后修改人：</h-col>
            <h-col span="5">{{ recordData.lastModifiedBy }}</h-col>
            <h-col span="3" style="text-align: right;">最后修改时间：</h-col>
            <h-col span="5">{{ recordData.gmtModified }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="24"><h-divider /></h-col>
          </h-row>
        </div>
      </h-collapse-panel>
      <h-collapse-panel  v-if="refundDataSource && refundDataSource.length > 0" key="3">
        <template #header>
          <div style="height: 22px;width: 5px;border-radius: 2px;background-color: #0073e5;margin-right: 10px;"></div>
          <span style="font-weight: 600;font-size: 15px;">退款记录详情</span>
        </template>
        <div style="padding: 0 20px 0px 20px;line-height: 28px;" v-for="i in refundDataSource" :key="i?.code">
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">退款支付单：</h-col>
            <h-col span="5">{{ i.code }}</h-col>
            <h-col span="3" style="text-align: right;">退款业务单：</h-col>
            <h-col span="5">{{ i.businessCode }}</h-col>
            <h-col span="3" style="text-align: right;">退款金额：</h-col>
            <h-col span="5">{{ i.amount }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">退款状态：</h-col>
            <h-col span="5">
              <h-tag v-if="i.state === RefundStatusConstant.CANCEL.type" color="default">{{
                RefundStatusConstant.CANCEL.name
              }}
              </h-tag>
              <h-tag v-if="i.state === RefundStatusConstant.SAVE.type" color="processing">{{
                RefundStatusConstant.SAVE.name
              }}
              </h-tag>
              <h-tag v-if="i.state === RefundStatusConstant.ADVANCE.type" color="warning">{{
                RefundStatusConstant.ADVANCE.name
              }}
              </h-tag>
              <h-tag v-if="i.state === RefundStatusConstant.SUCCESS.type" color="success">{{
                RefundStatusConstant.SUCCESS.name
              }}
              </h-tag>
              <h-tag v-if="i.state === RefundStatusConstant.ERROR.type" color="error">{{
                RefundStatusConstant.ERROR.name
              }}
              </h-tag>
            </h-col>
            <h-col span="3" style="text-align: right;">退款支付时间：</h-col>
            <h-col span="5">{{ i.refundTime }}</h-col>
            <h-col span="3" style="text-align: right;">退款成功时间：</h-col>
            <h-col span="5">{{ i.successRefundTime }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            
            <h-col span="3" style="text-align: right;">通知回调状态：</h-col>
            <h-col span="5" class="text-ellipsis">
              <h-tag v-if="i.notifyState === PayNotifyStateConstant.SUCCESS.code" color="success">{{
                  PayNotifyStateConstant.SUCCESS.name
                }}
              </h-tag>
              <h-tag v-if="i.notifyState === PayNotifyStateConstant.RETRY.code" color="warning">{{
                  PayNotifyStateConstant.RETRY.name
                }}
              </h-tag>
              <h-tag v-if="i.notifyState === PayNotifyStateConstant.ERROR.code" color="error">{{
                  PayNotifyStateConstant.ERROR.name
                }}
              </h-tag>
            </h-col>
            <h-col span="3" style="text-align: right;">已重试次数：</h-col>
            <h-col span="5">{{ i.notifyRetry }}</h-col>
            <h-col span="3" style="text-align: right;">通知失败原因：</h-col>
            <h-col span="5" class="text-ellipsis" :title="i.notifyErrorMessage" @dblclick="copyValue(i.notifyErrorMessage)">{{ i.notifyErrorMessage }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
           <h-col span="3" style="text-align: right;">支付回调地址：</h-col>
            <h-col span="5" class="text-ellipsis" :title="i.notifyUrl" @dblclick="copyValue(i.notifyUrl)">{{ i.notifyUrl }}</h-col>
            <h-col span="3" style="text-align: right;">创建时间：</h-col>
            <h-col span="5">{{ i.gmtCreate }}</h-col>
            <h-col span="3" style="text-align: right;">创建人：</h-col>
            <h-col span="5">{{  i.createBy }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">最后修改人：</h-col>
            <h-col span="5">{{  i.lastModifiedBy }}</h-col>
            <h-col span="3" style="text-align: right;">最后修改时间：</h-col>
            <h-col span="5">{{  i.gmtModified }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="24"><h-divider /></h-col>
          </h-row>
        </div>
      </h-collapse-panel>
    </h-collapse>

    <h-row :align="'middle'">
      <h-col :span="24">
        <h-card  :bordered="false" style="width: 100%">
          <template #title>
            <div style="height: 22px;width: 5px;border-radius: 2px;background-color: #0073e5;margin-right: 10px;margin-left: 24px;display:  inline-block;vertical-align:middle"></div>
            <span style="font-weight: 600;font-size: 15px">操作日志</span>
          </template>
          <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"  :scroll="{ y: 550 }" :loading="loading">
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'state'">
                <h-tag v-if="record.state === 1" color="success">
                  成功
                </h-tag>
                <h-tag v-if="record.state === 0" color="error">
                  失败
                </h-tag>
              </template>
            </template>
          </h-table>
        </h-card>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}
</style>
