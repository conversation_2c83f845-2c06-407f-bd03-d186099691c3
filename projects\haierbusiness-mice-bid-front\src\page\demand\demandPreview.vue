<script setup lang="ts">
// 需求预览
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { resolveParam, routerParam } from '@haierbusiness-front/utils';
import advisors from '@haierbusiness-front/components/mice/advisors/index.vue';

const route = useRoute();
const router = useRouter();
const previewSource = ref<string>('');

// 关闭预览
const closePreview = () => {
  router.push({ path: '/demand/index', query: { record: routerParam(route.query.record) } });
};

onMounted(async () => {
  const record = resolveParam(route.query.record);

  // DEMAND_SUBMIT - 需求提报
  // DEMAND_PRE_INTERACT - 需求事先交互
  previewSource.value = record.processNode === 'DEMAND_PRE_INTERACT' ? 'demandContrast' : 'demandFromUser'; // 需求交互，对比
});
</script>

<template>
  <!-- 需求预览 -->
  <div class="wid1280 demand_preview">
    <advisors v-if="previewSource" :preview-source="previewSource" :isManagePage="false">
      <template #header> </template>
      <template #footer>
        <a-button size="small" @click="closePreview()">关闭预览</a-button>
      </template>
    </advisors>
  </div>
</template>

<style scoped lang="less">
.demand_preview {
}
</style>
