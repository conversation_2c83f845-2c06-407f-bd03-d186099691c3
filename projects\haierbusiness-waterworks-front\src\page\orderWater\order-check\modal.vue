<script setup lang="ts">
import {
  Modal as hModal,
  Input as hInput,
  Table as hTable,
  Button as hButton,
  Form as hForm,
  FormItem as hFormItem,
  Textarea as hTextarea,
  message,
} from 'ant-design-vue';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { ref, watch } from 'vue';
const props = defineProps<{
  visible: boolean;
}>();
const emit = defineEmits(['update:visible', 'confirm']);

const handleModalCancel = () => {
  emit('update:visible', false);
};

// 搜索关键字
const keyWord = ref('');
const addAddressModalVisible = ref(false);
const addressFormData = ref({
  name: '',
  phone: '',
  address: '',
});
// ref
const addAddressFormRef = ref();

// 弹窗确认
const handleAddAddressModalConfirm = () => {
  addAddressFormRef.value.validateFields().then(() => {
    console.log(addressFormData.value, '-- 新增地址弹窗确认');
  });
  handleAddAddressModalCancel();
};

// 弹窗取消
const handleAddAddressModalCancel = () => {
  addAddressFormRef.value.resetFields();
};

/**
 * @表格相关
 * */
const addressList = ref([
  {
    id: 1,
    name: '张三',
    phone: '13800138000',
    address: '北京市海淀区中关村大街1号',
  },
  {
    id: 2,
    name: '李四',
    phone: '13800138001',
    address: '北京市海淀区中关村大街2号',
  },
  {
    id: 3,
    name: '王五',
    phone: '13800138002',
    address: '北京市海淀区中关村大街3号',
  },
  {
    id: 4,
    name: '赵六',
    phone: '13800138003',
    address: '北京市海淀区中关村大街4号',
  },
  {
    id: 5,
    name: '孙七',
    phone: '13800138004',
    address: '北京市海淀区中关村大街5号',
  },
  {
    id: 6,
    name: '周八',
    phone: '13800138005',
    address: '北京市海淀区中关村大街6号',
  },
  {
    id: 7,
    name: '吴九',
    phone: '13800138006',
    address: '北京市海淀区中关村大街7号',
  },
  {
    id: 8,
    name: '郑十',
    phone: '13800138007',
    address: '北京市海淀区中关村大街8号',
  },
]);
const tableProps = ref({
  // 待接入真实数据
  dataSource: addressList.value,
  pagination: {
    pageSize: 10,
    current: 1,
    total: 100,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) => `共${total}条`,
  },
  columns: [
    {
      title: '姓名',
      dataIndex: 'name',
      width: '100px',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      width: '100px',
    },
    {
      title: '地址',
      dataIndex: 'address',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: '100px',
    },
  ],
});

// 选择地址
const handleTableSelect = (record: any) => {
  console.log(record, '-- 选择地址');
  emit('confirm', record);
  handleModalCancel();
};

/**
 * @模态框初始化
 **/
const fatchAddressList = () => {
  // 请求数据
  console.log('请求数据');
};

// 监听弹窗是否打开
watch(props, (newVal) => {
  if (newVal.visible) {
    keyWord.value = '';
    // 请求数据
    fatchAddressList();
  }
});
</script>

<template>
  <h-modal width="750px" title="请选择地址" v-model:open="props.visible" @cancel="handleModalCancel">
    <template #footer>
      <h-button @click="handleModalCancel">取消</h-button>
    </template>
    <div style="margin-bottom: 12px">
      <h-input v-model:value="keyWord" style="width: 300px" placeholder="请输入姓名/电话/地址查询"></h-input>
      <h-button type="primary" style="margin-left: 10px"> <SearchOutlined />查询</h-button>
      <h-button style="margin-left: 10px" @click="addAddressModalVisible = true"><PlusOutlined /> 新增地址</h-button>
    </div>
    <div>
      <h-table v-bind="tableProps" size="small">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <h-button type="link" size="small" @click="handleTableSelect(record)">选择</h-button>
          </template>
        </template>
      </h-table>
    </div>
  </h-modal>
  <h-modal
    v-model:open="addAddressModalVisible"
    title="新增地址"
    @ok="handleAddAddressModalConfirm"
    @cancel="handleAddAddressModalCancel"
  >
    <h-form ref="addAddressFormRef" :model="addressFormData" :labelCol="{ span: 4 }" style="margin-top: 24px">
      <h-form-item label="收货人" name="name" :rules="[{ required: true, message: '请输入收货人' }]">
        <h-input v-model:value="addressFormData.name" placeholder="请输入收货人" />
      </h-form-item>
      <h-form-item label="电话" name="phone" :rules="[{ required: true, message: '请输入电话' }]">
        <h-input v-model:value="addressFormData.phone" placeholder="请输入电话" />
      </h-form-item>
      <h-form-item label="收货地址" name="address" :rules="[{ required: true, message: '请输入收货地址' }]">
        <h-textarea
          v-model:value="addressFormData.address"
          placeholder="请输入收货地址"
          :rows="3"
          :maxlength="50"
          showCount
        />
      </h-form-item>
    </h-form>
  </h-modal>
</template>

<style lang="less" scoped></style>
