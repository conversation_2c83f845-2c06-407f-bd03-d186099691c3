<template>
  <div class="container">
    <h-form
      ref="from"
      :model="searchKey"
      @finish="onReFilterChange"
      style="width: 100%"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <h-row :gutter="24">
        <h-col :span="8">
          <h-form-item has-feedback label="出差人" name="travelerKeyword">
            <h-input v-model:value="searchKey.travelerKeyword" placeholder="出差人名称/工号" allow-clear />
          </h-form-item>
        </h-col>
        <h-col :span="8">
          <h-form-item has-feedback label="申请单号" name="applyNo">
            <h-input v-model:value="searchKey.applyNo" placeholder="申请单号" allow-clear />
          </h-form-item>
        </h-col>
        <h-col :span="8">
          <h-form-item has-feedback label="审批状态" name="auditStatus">
            <h-select v-model:value="searchKey.auditStatus" placeholder="审批状态">
              <h-select-option value="">全部</h-select-option>
              <h-select-option v-for="(item, index) in approvalState" :key="index" :value="item.value">{{
                item.label
              }}</h-select-option>
            </h-select>
          </h-form-item>
        </h-col>
        <h-col :span="16">
          <h-form-item
            has-feedback
            label="出差日期"
            name="applyTime"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 20 }"
          >
            <h-range-picker
              v-model:value="searchKey.applyTime"
              value-format="YYYY-MM-DD"
              @change="onCreateTimeChange"
              style="width: 100%"
            />
          </h-form-item>
        </h-col>

        <h-col :span="8">
          <h-form-item has-feedback label="单据状态" name="status">
            <h-select v-model:value="searchKey.status" placeholder="单据状态">
              <h-select-option value="">全部</h-select-option>
              <h-select-option v-for="(item, index) in documentState" :key="index" :value="item.value">{{
                item.label
              }}</h-select-option>
            </h-select>
          </h-form-item>
        </h-col>

        <h-col :span="8">
          <h-form-item has-feedback label="变更状态" name="changeStatus">
            <h-select v-model:value="searchKey.changeStatus" placeholder="变更状态">
              <h-select-option value="">全部</h-select-option>
              <h-select-option v-for="(item, index) in changeState" :key="index" :value="item.value">{{
                item.label
              }}</h-select-option>
            </h-select>
          </h-form-item>
        </h-col>

        <h-col :span="8">
          <h-form-item has-feedback label="出差事由" name="travelReason">
            <h-input v-model:value="searchKey.travelReason" placeholder="出差事由" allow-clear />
          </h-form-item>
        </h-col>

        <h-col :span="8">
          <h-form-item has-feedback label="审批失败原因" name="workFlowFailInfo">
            <h-input v-model:value="searchKey.workFlowFailInfo" placeholder="审批失败原因" allow-clear />
          </h-form-item>
        </h-col>
        <h-col :span="8">
          <h-form-item :auto-link="false" has-feedback label="出发地" name="beginCityCodeStrs">
            <city-chose width="100%"  :value="chosedBeginCityName" @chosedCity="chosedBeginCity"></city-chose>
          </h-form-item>
        </h-col>
        <h-col :span="8">
          <h-form-item :auto-link="false" has-feedback label="目的地" name="endCityCodeStrs">
            <city-chose width="100%"  :value="chosedEndCityName" @chosedCity="chosedEndCity"></city-chose>
          </h-form-item>
        </h-col>
      </h-row>
      <h-row justify="center">
        <div class="flexCon">
          <h-space>
            <h-button @click="handleReset">重置</h-button>
            <h-button type="primary" html-type="submit">查询</h-button>
            <h-button type="primary" @click="historyList">历史订单</h-button>
          </h-space>
        </div>
      </h-row>
    </h-form>

    <h-spin :spinning="dataLoading">
      <template v-if="dataList && dataList.length > 0">
        <div class="banner-contain list" v-for="(data, index) in dataList" :key="index">
          <div class="banner-contain-bottom">
            <div class="banner-contain-data">
              <div>
                <div class="data-one">
                  {{ data.applyNo }}
                </div>
                <div class="data-time" v-if="data.beginDate && data.endDate">
                  <img src="../../../../assets/image/banner/ban-time.png" alt="" />
                  <div>{{ data.beginDate }} ~ {{ data.endDate }}</div>
                </div>
                <div class="data-price data-time">
                  <img src="../../../../assets/image/banner/money.png" alt="" />
                  <div class="peice-mar">
                    <span>{{ `¥ ${data.amountSum}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') }}</span>
                    <span style="color: #3983e5">(总)</span>
                  </div>
                  <!-- <div>
                    <span>{{ `¥ ${data.amountSum - data.realAmountSum}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') }}</span>
                    <span style="color: #52c41a">(可用)</span>
                  </div> -->
                </div>
              </div>
              <div class="data-btn">
                <!-- 审批状态 -->
                <a-tooltip>
                  <template #title>审批状态</template>
                  <h-tag class="pointer" v-if="data.status != '90' && data?.type != 1 && data?.travelReserveFlag != 0" :color="TripApprovalStatusToTagColor[data.auditStatus] || 'blue'" @click="goToAudit(data?.workFlowId)">
                    <span>{{ TripApprovalStatus[data.auditStatus] || '' }} <QuestionCircleOutlined /></span>
                  </h-tag>
                </a-tooltip>
                <!-- 变更状态 -->
                <a-tooltip>
                  <template #title>变更状态</template>
                  <h-tag
                    v-if="data.changeStatus != '10'"
                    :color="TripChangeStatusToTagColor[data.changeStatus] || 'blue'"
                  >
                    <span>{{ TripChangeStatus[data.changeStatus] || '' }} <QuestionCircleOutlined /></span>
                  </h-tag>
                </a-tooltip>

                <!-- 单据状态 -->
                <a-tooltip>
                  <template #title>单据状态</template>
                  <h-tag :color="TripDocumentStatusToTagColor[data.status] || 'blue'">
                    <span>{{ TripDocumentStatus[data.status] || '' }} <QuestionCircleOutlined /></span>
                  </h-tag>
                </a-tooltip>

                <!-- 变更单审批状态 -->
                <a-tooltip>
                  <template #title>变更单审批状态</template>
                  <h-tag
                    v-if="data.changeApplyStatus == '20' || data.changeApplyStatus == '40'"
                    :color="TripChangeApprovalStatusToTagColor[data.changeApplyStatus] || 'blue'"
                  >
                    <span>{{ TripChangeApprovalStatus[data.changeApplyStatus] || '' }} <QuestionCircleOutlined /></span>
                  </h-tag>
                </a-tooltip>
              </div>
            </div>
            <div class="banner-detail">
              <!-- 个人信息 -->
              <div>
                <div class="detail-info-i">
                  <div class="info-i-l">
                    <img src="../../../../assets/image/banner/person.png" alt="" />
                    <span style="padding-left: 5px">经办人</span>
                  </div>
                  <div class="info-main">
                    <a-tooltip>
                      <template #title>{{ data.operUserName  }}</template>
                      <span class="names">{{ data.operUserName }}</span>
                    </a-tooltip>
                  </div>
                </div>
                <div class="detail-info-i">
                  <div class="info-i-l">
                    <img src="../../../../assets/image/banner/person.png" alt="" />
                    <span style="padding-left: 5px">出差人</span>
                  </div>
                  <div class="info-main">
                    <a-tooltip>
                      <template #title>{{ getMainPerson(data.travelerList) }}</template>
                      <span class="names">{{ getMainPerson(data.travelerList) }}</span>
                    </a-tooltip>
                  </div>
                </div>
                <div class="detail-info-i">
                  <div class="info-i-l">
                    <img src="../../../../assets/image/banner/friend.png" alt="" />
                    <span style="padding-left: 5px">同行人</span>
                  </div>
                  <div class="info-main">
                    <a-tooltip>
                      <template #title>{{ getOutPerson(data.travelerList) }}</template>
                      <span class="names">{{ getOutPerson(data.travelerList) }}</span>
                    </a-tooltip>
                  </div>
                </div>
                <div class="detail-info-i">
                  <div class="info-i-l">
                    <img src="../../../../assets/image/banner/reason.png" alt="" />
                    <span style="padding-left: 5px">出差事由</span>
                  </div>
                  <div class="info-main">
                    <a-tooltip>
                      <template #title>{{ data.travelReason }}</template>
                      <div class="names">{{ data.travelReason }}</div>
                    </a-tooltip>
                  </div>
                </div>
              </div>
              <!-- 行程 -->
              <div class="banner-step">
                <div
                  v-for="(item, index) in data.stepData"
                  :key="index"
                  class="step-item"
                  :class="index == 0 ? 'step-item-start' : ''"
                >
                  <div class="idol" v-if="index != 0">
                    <div class="idol-img">
                      <img src="../../../../assets/image/banner/icon-air.png" alt="" v-if="item.isFly" />
                      <img src="../../../../assets/image/banner/icon-train.png" alt="" v-if="item.isTrain" />
                    </div>
                  </div>
                  <div class="step-name">
                    <span class="step-c">
                      <a-tooltip>
                        <template #title>{{ item.name }}</template>
                        <span class="city-name">{{ item.name }}</span>
                      </a-tooltip>
                      <img
                        class="hotal-img"
                        src="../../../../assets/image/banner/yding-hotel.png"
                        alt=""
                        v-if="item.isHotal"
                    /></span>
                    <span class="step-t">{{ item.timer }}</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="btn-contain">
              <div
                class="btn btn-yu mr-10"
                v-if="data.status == '30' && data.travelReserveFlag == 1 && data.scheduleList.length > 0 && data.type!=1"
                @mouseenter="handelSchedule(data)"
                @mouseleave="handelScheduleLeave(data)"
              >
                <a-button type="primary" size="small" :icon="h(HighlightOutlined)"
                  >预订</a-button
                >

                <div class="card-list" v-if="data.schedule">
                  <div class="triangle-con">
                    <img src="../../../../assets/image/banner/triangle.png" alt="" class="triangle" />
                  </div>
                  <template v-for="(item, index) in data.scheduleList">
                  <div class="card-list-item" :key="index" v-if="item.type != 'car'">
                    
                    <img src="../../../../assets/image/banner/yding-air.png" alt="" class="item-l"  v-if="item.type == 'air'" />
                    <img src="../../../../assets/image/banner/yding-hotel.png" alt="" class="item-l" v-if="item.type == 'hotel'" />
                    <img src="../../../../assets/image/banner/yding-train.png" alt="" class="item-l" v-if="item.type == 'train'"  />
                    <img src="../../../../assets/image/banner/groundServices1.png" alt="" class="item-l"  v-if="item.type == 'groundServices'" />

                    <div class="banner-step">
                      <div class="step-name_">
                        <a-tooltip>
                          <template #title>{{item.start }}</template>
                          <span>{{ item.start }}</span>
                        </a-tooltip>
                        
                        <span class="step-t">{{ formateTime(item.startTime) }}</span>
                      </div>
                      <div class="idol_"></div>
                      <div class="step-name_">
                        <a-tooltip>
                          <template #title>{{item.last }}</template>
                          <span>{{ item.last }}</span>
                        </a-tooltip>
                        <span class="step-t">{{ formateTime(item.lastTime) }}</span>
                      </div>
                    </div>
                    <div
                      :class="reserve == index ? 'btn-reserve' : ''"
                      class="btn-step_"
                      @mouseenter="handelReserve(index)"
                      @mouseleave="handelReserveLeave"
                      @click="reserveByType(data,item)"
                    >
                      预订
                    </div>
                  
                  </div>
                </template>
                </div>
              </div>

              <a-button
                class="mr-10"
                type="primary"
                size="small"
                :icon="h(UndoOutlined)"
                v-if="data.status == '50'"
                @click="goToApply(data.id)"
                >重新审批</a-button
              >

              

              <a-popover >
                <template #content>
                  <a-row style="width: 200px; margin-bottom: 5px;" v-for="item, index in data.travelerList" :key="index"  v-show="item.travelUserType =='0'" >
                    <template v-if="item.travelUserType =='0'">
                      <a-col :span="16">{{ `${item.travelUserName}(${item.travelUserNo })` }}</a-col>
                      <a-col :span="8">
                        <a-tag color="green" v-if="item.reimburseNum != 0">已报销</a-tag>
                        <a-tag color="orange" v-else>未报销</a-tag>
                      </a-col>
                    </template>
                  </a-row>
                </template>
                <!-- 报销 -->
                <a-button class="mr-10" type="primary"  @click="showReimburseDialog(data)" size="small" :icon="h(DollarOutlined)" v-if="data.reimbursementButton"
                >去报销</a-button>
              </a-popover>


              <a-button
                class="mr-10"
                type="primary"
                size="small"
                :icon="h(EditOutlined)"
                v-if="data.status == '10' && data.status != '90' && loginUser?.username == data.createBy"
                @click="goToApply(data.id)"
                >编辑</a-button
              >
              <a-button
                class="mr-10"
                type="primary"
                size="small"
                :icon="h(ContainerOutlined)"
                @click="goToDetail(data.id)"
                >详情</a-button
              >
              

              <a-popover >
                <template #content>
                  <a-row style="width: 200px; margin-bottom: 5px;" v-for="item, index in data.travelerList" :key="index" v-show="item.travelUserType =='0'">
                    <template v-if="item.travelUserType =='0'">
                      <a-col :span="16">{{ `${item.travelUserName}(${item.travelUserNo })` }}</a-col>
                      <a-col :span="8">
                        <a-tag color="orange" v-if="item.reimburFinish == '10'">未确认</a-tag>
                        <a-tag color="green" v-else-if="item.reimburFinish == '20'">已确认</a-tag>
                        <a-tag color="blue" v-else>系统确认</a-tag>
                      </a-col>
                    </template>
                  </a-row>
                </template>
                <!-- 行程确认 -->
                <a-button
                  class="mr-10"
                  type="primary"
                  size="small"
                  v-if="!data.changeApplyStatus ? data.confirmButton : (data.confirmButton && data.changeApplyStatus == '30')"
                  :icon="h(CheckOutlined)"
                  @click="goToConfirm(data)"
                  >行程确认</a-button
                > 
              </a-popover>

              <a-dropdown size="small" :trigger="['click']" >
                <template #overlay>
                  <a-menu @click="handleMenuClick" >
                    <a-menu-item
                      v-if="data.status == '30' && data.changeApplyStatus != '20' && data.type != 1"
                      key="1"
                      @click="goToChange(data)"
                      >变更</a-menu-item
                    >

                    <a-popconfirm
                      key="2"
                      title="确定要作废此条记录吗!"
                      ok-text="确定"
                      cancel-text="取消"
                      @confirm="cancelApply(data.id)"
                    >
                      <a-menu-item id="zf"  v-if="(data.status == '10' || data.status == '20' || data.status == '30') && data.changeApplyStatus != '20' &&  data.auditStatus!='20' && loginUser?.username == data.createBy"
                        >作废</a-menu-item
                      >
                    </a-popconfirm>

                    <!-- 申请单撤回 -->
                    <a-menu-item id="ch" key="3" @click="recallApply(data.id)" v-if="data.status == '20' && data.auditStatus == '20'">撤回</a-menu-item>
                    <!-- 变更单撤回 -->
                    <a-menu-item id="bgch" key="4" @click="recallBgApply(data.applyNo)" v-if="data.changeApplyStatus == '20'">撤回</a-menu-item>

                  </a-menu>
                </template>
                <a-button v-if="showMoreBtn(data)" type="primary" size="small">
                  更多
                  <DownOutlined size="small" />
                </a-button>
              </a-dropdown>

           
            </div>
          </div>
        </div>
      </template>
      <div class="page" v-show="pagination.total && pagination.total > 0">
        <h-pagination
          v-model:current="pagination.current"
          show-size-changer
          show-quick-jumper
          :total="pagination.total"
          @change="onPageChange"
        />
      </div>

      <div class="empty" v-if="!dataList || dataList.length === 0">
        <h-empty />
      </div>
    </h-spin>

    <h-modal v-model:open="reimburseDialog" style="width:500px;" title="去报销" @cancel="closeReimburseDialog" @ok="goToEES">
      <h-row style="margin-bottom: 20px; margin-top: 10px;">
        <h-col :span="24">
          <h-alert message="每人每个申请单最多可报销两次" type="warning" show-icon />
        </h-col>

      </h-row>
      <h-row>
        <h-col :span="6" class="flex" style="align-items: center; text-align: right; padding-right: 10px;">报销人:</h-col>
        <h-col :span="18">
          <h-select v-model:value="reimburseUserCode" placeholder="请选择报销人" style="width: 100%">
            <h-select-option v-for="item in ReimburseTravelerList" :key="item.travelUserNo" :value="item.travelUserNo">{{ item.travelUserName }} / {{ item.travelUserNo }}</h-select-option>
          </h-select>
        </h-col>
      </h-row>
    </h-modal>
  </div>
</template>
<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Tag as hTag,
  Spin as hSpin,
  Empty as hEmpty,
  Space as hSpace,
  message as hMessage,

  Pagination as hPagination,
  Checkbox as hCheckbox,
  message,
  FormItemRest as hFormItemRest,
  Modal as hModal,
  Select as hSelsect,
  Alert as hAlert,
} from 'ant-design-vue';
import {
  TripApprovalStatus,
  TripDocumentStatus,
  TripChangeApprovalStatus,
  TripChangeApprovalStatusToTagColor,
  TripChangeStatus,
  TripBudgeStatus,
  ICreatTrip,
  ITraveler,
  TripApprovalStatusToTagColor,
  TripDocumentStatusToTagColor,
  TripChangeStatusToTagColor,
  TripBudgeStatusToTagColor,
  IDataListItem,
  ICity,
  CityItem,
} from '@haierbusiness-front/common-libs';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';

import { computed, onMounted, reactive, ref, watch, h,createVNode } from 'vue';
import { localrestApi,loginApi,cityApi, rechargeApi } from '@haierbusiness-front/apis';
import {
  PaymentTypeEnum,
  RestaurantOrderStateEnum,
  orderStateTagColorMap,
  LocalHotelPaymentTypeEnum,
  RestaurantOrderApprovalStateEnum,
  approvalStateTagColorMap,
  RestaurantOrderPayMentStateEnum,
  paymentStateTagColorMap,
} from '@haierbusiness-front/common-libs';
import { getEnumOptions } from '@haierbusiness-front/utils';
// import { useOrderStore } from "@/store"
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { tripApi } from '@haierbusiness-front/apis';
import type { PaginationProps } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  QuestionCircleOutlined,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  DeleteOutlined,
  CheckOutlined,
  ExclamationCircleOutlined,
  CloseOutlined,
  UploadOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  UndoOutlined,
  HighlightOutlined,
  ShareAltOutlined,
  DollarOutlined,
  TagFilled,
  SearchOutlined,
  MoreOutlined,
  DownOutlined,
  ContainerOutlined,
  
} from '@ant-design/icons-vue';

import voidImg from '@/assets/image/banner/void.png';
import voidAct from '@/assets/image/banner/void-act.png';
import withdraw from '@/assets/image/banner/withdraw.png';
import withdrawAct from '@/assets/image/banner/withdraw-act.png';
import change from '@/assets/image/banner/change.png';
import changeAct from '@/assets/image/banner/change-act.png';
import { removeStorageItem } from '@haierbusiness-front/utils';
import {
  HeaderConstant
} from '@haierbusiness-front/common-libs'
const { loginUser } = storeToRefs(applicationStore(globalPinia));

const route = useRoute();

const labelCol = {
  span: 8,
};
const wrapperCol = {
  span: 16,
};
const from = ref();
// 本地跳转用于调试 跳转变更单 跳转详情
const businessTravel = import.meta.env.VITE_BUSINESS_TRIP_URL;
const goToDetail = (id: string) => {
  const url = businessTravel + '#' + '/detail?id=' + id;
  window.open(url);
};

const goToConfirm = (data:any) => {
  // 未推送成功不能确认
  if (data.shengyiOrderPushStatus != 20 && data.type != 1) {
    hMessage.error('订单推送商旅系统失败,请联系管理员后重试!')
    return
  }
  const url = businessTravel + '#' + '/confirmTrip?applyNo=' + data.applyNo;
  window.open(url);
}

const goToApply = (id: string) => {
  const url = businessTravel + '#' + '/apply?id=' + id;
  window.open(url);
};

const chosedBeginCityName = ref<string>('');
const chosedEndCityName = ref<string>('');

const chosedBeginCity = (city: CityItem, index: number, i: number) => {
  searchKey.beginCityCode = city.citycode;
  chosedBeginCityName.value = city.name;
};

const chosedEndCity = (city: CityItem, index: number, i: number) => {
  searchKey.endCityCode = city.citycode;
  chosedEndCityName.value = city.name;
};

const goToChange = (data) => {
  // if(data.changeApplyStatus) {
  //   message.warning('已存在变更中数据!')
  //   return
  // }
  const url = businessTravel + '#' + '/update?applyNo=' + data.applyNo;
  window.open(url);
};

const businesstravel = import.meta.env.VITE_BUSINESSTRAVEL_URL;

const getMainPerson = (list: Array<ITraveler>) => {
  return list.filter((item) => item.mainFlag == '1')[0]?.travelUserName;
};

// 去报销功能
const reimburseDialog = ref(false)
const eesGingle = import.meta.env.VITE_BUSINESS_EES_SINGLE;
const eesUrl = ref('')

// 可报销的人员列表
const ReimburseTravelerList = ref<Array<any>>([])
// 选择的报销人
const reimburseUserCode = ref(null)
// 选中的申请单数据
const activeApplyData = ref<any>({})

const logout = () => {
  loginApi.haierIamTokenLogout({ token: loginUser?.value?.extended?.iamToken }).finally(() => {
    removeStorageItem(HeaderConstant.TOKEN_KEY.key, false)
    window.location.reload()
  })
}

const showReimburseDialog = (data:any) => {
  activeApplyData.value = data
  // 如果是经办人可以给所有人报销
  // 否则只能给自己报销
  if(loginUser?.value?.username == data.operUserNo) {
    ReimburseTravelerList.value = data?.travelerList?.filter((item:any) => item.travelUserType == 0 && item.reimburseNum <= 1)
    if ( ReimburseTravelerList.value.length == 0) {
      hMessage.warning('暂无可报销人员!')
      return
    }
    reimburseUserCode.value = null
    reimburseDialog.value = true
  }else {
    reimburseUserCode.value = loginUser?.value?.username
    goToEES()
  }
}
const closeReimburseDialog = () => {
  reimburseUserCode.value = null
  reimburseDialog.value = false
  ReimburseTravelerList.value = []

}
const goToEES = () => {
  if(!reimburseUserCode.value) {
    hMessage.warning('请选择报销人员!')
    return
  }
  /*

  首先验证iamtoken是否过期
  1、如果过期需要用户去账号中心登录
   1.1、登录后重定向回来
   1.2、根据路由参数 isReimburse 判断是否要去往上一次点击的去报销字段
   1.3、
  2、如果没过期则直接跳转
  
  */ 
  const token = loginUser?.value?.extended?.iamToken

  loginApi.checkIamToken({token}).then(res => {
    console.log('验证iam_token是否有效----->>', res)
    if (res.data) {
      eesUrl.value = `${eesGingle}?travelCode=${activeApplyData.value.applyNo}&accountCode=${reimburseUserCode.value}&creatorCode=${loginUser.value?.username}&accessToken=${token}&fromOrigin=SLPC`
      // 清除上次本地缓存的数据
      console.log('去报销----->>', eesUrl.value)
      window.open(eesUrl.value)
      localStorage.removeItem('reimburseData')
      reimburseUserCode.value = null
      reimburseDialog.value = false
      ReimburseTravelerList.value = []
    }else {
      // 如果token过期
      hModal.info({
        title: '提示',
        okText: '确定',
        content: h('div', {}, [
          h('p', '当前集团统一认证系统已过期，请重新登录！'),
        ]),
        onOk() {
          console.log('ok');
          // 将这次跳转去报销的数据本地缓存起来
          localStorage.setItem('reimburseCode', JSON.stringify(reimburseUserCode.value))
          localStorage.setItem('reimburseData', JSON.stringify(activeApplyData.value))
          logout()
        },
      });

    }
  })

   
  
}

const showMoreBtn = (data:any) => {
  let showBg = data.status == '30' && data.changeApplyStatus != '20' && data.type != 1
  let showZf = (data.status == '10' || data.status == '20' || data.status == '30')
  let showCh = data.status == '20' && data.auditStatus == '20'
  let showBgCh = data.changeApplyStatus == '20' 

 return data.status != '90' && data.type != 1 && (showBg || showZf || showCh || showBgCh) && data.createBy == loginUser.value?.username &&  data.originApp != 'HWORK'
}

const goToAudit = (code: string) => {
  if (code) {
    const url = processUrl + `?code=${code}#/details`;
    window.open(url);
  }
};

// 预订按钮  鼠标移入
// let schedule=ref(false)
const handelSchedule = (data) => {
  data.schedule = true;
};
const handelScheduleLeave = (data) => {
  data.schedule = false;
};
const reserve = ref(-1);
const handelReserve = (value: number) => {
  reserve.value = value;
};
const handelReserveLeave = () => {
  reserve.value = -1;
};

let listTemp = [];
const getOutPerson = (list: Array<ITraveler>) => {
  listTemp = [];
  list.forEach((item) => {
    if (item.mainFlag != '1') {
      if (item.travelUserType == '0') {
        listTemp.push(item.travelUserName);
      }else {
        listTemp.push(`${item.travelUserName}[外部]`);
      }
      
    }
  });
  return listTemp.join(',');
};
// 重新审批

const searchKey = reactive<ICreatTrip>({
  travelerKeyword: '', // 出差人名称或者工号
  applyNo: '', //申请单号
  applyTime: [], //申请日期
  beginDate: '',
  endDate: '',
  auditStatus: '', //审批状态
  status: '', //单据状态
  changeStatus: '', // 变更状态
  budgetStatus: '', //预算状态
  travelReason: '', //出差事由
  workFlowFailInfo: '', // 审批失败原因
  beginCityCode: '', //出发地
  endCityCode: '', // 目的地
  pageNum: 1,
  pageSize: 10,
});
const pagination = reactive<PaginationProps>({
  current: 1,
  total: 0,
  pageSize: 10,
});


const onReFilterChange = () => {
  searchKey.pageNum = 1;
  searchKey.pageSize = 10;
  pagination.pageSize = 10;
  pagination.current = 1;
  onFilterChange();
};

onMounted(() => {
  onFilterChange();
  if(localStorage.getItem('reimburseData')) {
    activeApplyData.value = JSON.parse(localStorage.getItem('reimburseData'));
    reimburseUserCode.value = JSON.parse(localStorage.getItem('reimburseCode'));

    goToEES()
  }

});

// 转换时间格式  2.1
const formateTime = (time: string) => {
  return ` ${new Date(time).getMonth() + 1}月${new Date(time).getDate()}日`;
};

// 获取出差单列表

const dataList = ref<Array<IDataListItem>>([]);

const productNameFamatter = (value: string) => {
  switch (value) {
    case '飞机':
      return 'air';
    case '火车':
      return 'train';
    case '酒店':
      return 'hotel';
    case '地面服务':
      return 'groundServices';
    
    case '用车':
      return 'car';
    case '租车':
      return 'car';

    default:
      return 'air';
  }
}

const dataLoading = ref<boolean>(false);
const onFilterChange = async () => {
  dataLoading.value = true;

  const res = await tripApi.getApplyPage(searchKey);
  pagination.total = res?.total || 0;
  dataList.value = res?.records || [];
  dataLoading.value = false;
  dataList.value.forEach((item:any) => {
    item.stepData = [];
    item.reserve = -1;

    // 预订按钮  鼠标移入
    item.schedule = false;
    item.scheduleList = [];
    // 更多按钮显示
    item.moreListShow = false;
    item.isMouseInter = false;

    item?.tripList?.forEach((trip:any, index:number) => {
      trip?.tripDetailMapList?.forEach((detail:any) => {
        item.scheduleList = [...item.scheduleList,
          {
            start: trip.beginCityName,
            beginCityCode: trip.beginCityCode,
            beginCityCodeSy: trip.beginCityCodeSy,
            endCityCode: trip.endCityCode,
            endCityCodeSy: trip.endCityCodeSy,
            startTime: trip.beginDate ,
            last: trip.endCityName,
            lastTime: trip.endDate,
            travelerList: detail.travelApplyTripDetailList,
            type: productNameFamatter(detail.productName)
          }
        ]
      });
      
      if (index == 0) {
        item.stepData = [
          ...item.stepData,
          {
            name: trip.beginCityName,
            timer: trip.beginDate,
            isTrain: trip.travelModeAggregate ? trip.travelModeAggregate.indexOf('火车') > -1 : false,
            isFly: trip.travelModeAggregate ? trip.travelModeAggregate.indexOf('飞机') > -1 : false,
            isHotal: trip.travelModeAggregate ? trip.travelModeAggregate.indexOf('酒店') > -1 : false,
          },
          {
            name: trip.endCityName,
            timer: trip.endDate,
            isTrain: trip.travelModeAggregate ? trip.travelModeAggregate.indexOf('火车') > -1 : false,
            isFly: trip.travelModeAggregate ? trip.travelModeAggregate.indexOf('飞机') > -1 : false,
            isHotal: trip.travelModeAggregate ? trip.travelModeAggregate.indexOf('酒店') > -1 : false,
          },
        ];
      } else {
        item.stepData = [
          ...item.stepData,
          {
            name: trip.endCityName,
            timer: trip.endDate,
            isTrain: trip.travelModeAggregate ? trip.travelModeAggregate.indexOf('火车') > -1 : false,
            isFly: trip.travelModeAggregate ? trip.travelModeAggregate.indexOf('飞机') > -1 : false,
            isHotal: trip.travelModeAggregate ? trip.travelModeAggregate.indexOf('酒店') > -1 : false,
          },
        ];
      }
    });
  });
};
// 根据选择产品名称跳转第三方预定页面查看预定信息
const TRIP_SINGLE = import.meta.env.VITE_BUSINESS_TRIP_SINGLE;
const processUrl = import.meta.env.VITE_BUSINESS_PROCESS_URL;

// 根据城市id获取机场数据
const getAirportList =  async(cityIds: string) => {
  const res = await cityApi.getAirportByCityId({ cityIds })
  return res
}

// 根据城市id获取航站楼数据
const getAirportList2 =  async (cityIds: string) => {
  const res = await cityApi.getAirportByCityId({ cityIds, isNeedAirportTerminal: true,domesticInternationalType: 1  })
  return res
}
// 根据城市id获取车站数据
const getTrainStationByCityId =  async(cityIds: string) => {
  const res = await cityApi.getTrainStationByCityId({ cityIds })
  return res
}
// 传入对象生成拼接字符串
const createUrlParams = (obj: { [key: string]: any }): string  => {
  const params = new URLSearchParams();
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      params.append(key, obj[key]?.toString());
    }
  }
  return params.toString();
}
// 格式化出行人id部门id数据  格式：用户id:部门id
const formatPerson = (list?: any[]) => {
  return list?.map(item => `${item.travelUserNo || item.username || item.travelUserSyId }:${item.travelUserDeptId || item.departmentCode || ""}`)?.join(',')
}

// 根据类型预订
const reserveByType = async(trip:any,item:any) => {
  // 未推送成功不能确认
  if (trip.shengyiOrderPushStatus != 20 && trip.type != 1) {
    hMessage.error('订单推送商旅系统失败,请联系管理员后重试!')
    return
  }
  let params = {}
  let url = ''
  if(item.type =='air') {
    // 1、飞机首先根据城市 id获取 出发机场、到达机场编号
    const beginAirport = await getAirportList(item.beginCityCode)
    const endAirport = await getAirportList(item.endCityCode)
    if (beginAirport[0].threeCharacterCode && endAirport[0].threeCharacterCode) {
      params = {
        skipType: '010010',
        ygys:  trip?.travelReserveFlag ? 1 : 2, // 因公因私 1因公 2因私
        ccsqdh:  trip?.applyNo || '',
        xclx: 1, // 行程类型 1单程 2往返 3多程
        sqdlx: 0, // 申请单类型  0为出差申请单
        cxr: item?.travelerList?.map(i => i.travelUserName).join(','), // 出行人
        cxr_hid: formatPerson(item?.travelerList),  // 出行人id，格式：用户id:部门id
        cfcs:  item?.start, 
        cfcs_hid:  beginAirport[0].threeCharacterCode,
        cfcs_iscity: 1,
        cfrq:  item?.startTime,
        ddcs:  item?.last,
        ddcs_hid:  endAirport[0].threeCharacterCode,
        ddcs_iscity: 1,// 到达城市识别标识 识别城市还是机场 1城市 0机场
        ddrq:  item?.lastTime,
        cxrlx: covertLx(item?.travelerList), // 出行人类型 1内部员工 2外部人员
      }
      url = TRIP_SINGLE + 'fcc/ticket/lticketbook/search/single/index.html?' + createUrlParams(params)


    }else { 
      hMessage.error('出发城市或到达城市没有机场,请重新选择!')
      return
    }

  }else if(item.type == 'train'){
    // 2、火车首先根据城市 id获取 出发、到达站点编号
    const beginTrain = await getTrainStationByCityId(item.beginCityCode)
    const endTrain = await getTrainStationByCityId(item.endCityCode)
    if (beginTrain[0].threeCharacterCode && endTrain[0].threeCharacterCode) {
      params = {
        skipType: '010014',
        cxr: item?.travelerList?.map(item => item.travelUserName).join(','), // 出行人
        cxr_hid: formatPerson(item?.travelerList),  // 出行人id，格式：用户id:部门id
        cxrlx: covertLx(item?.travelerList), // 出行人类型 1内部员工 2外部人员

        cfcs: item?.start,
        cfcs_hid: beginTrain[0]?.threeCharacterCode,
        ddcs: item?.last,
        ddcs_hid: endTrain[0]?.threeCharacterCode,
        cfrq: item?.startTime,
        ygys: trip?.travelReserveFlag ? 1 : 2,
        xclx: 1,
        ccsqdh:  trip?.applyNo || '',
        // ccsqdcx: 1,
        // sfkqjcx: 1
      }
      url = TRIP_SINGLE + 'view/fcc/train/book/search.html?' + createUrlParams(params)

    }else {
      hMessage.error('出发城市或到达城市没有火车站,请重新选择!')
      return
    }

  }else if(item.type=='hotel') {
    params = {
      skipType: '010013',
      // onlySearch:1,
      ygys: trip?.travelReserveFlag ? 1 : 2, // 因公因私 1因公 2因私
      ccsqdh:trip?.applyNo || '',
      
      cxr: item?.travelerList?.map(person => person.travelUserName).join(','), // 出行人
      cxr_hid: formatPerson(item?.travelerList),   // 出行人id，格式：用户id:部门id
      cxrlx: covertLx(item?.travelerList), // 出行人类型 1内部员工 2外部人员
      cxrzj: await convertZJ(item?.travelerList), //出行人职级

      cfcs: item?.last, 
      cfcs_hid: item?.endCityCodeSy,
      
      cfrq: item?.startTime,
      ddrq: item?.lastTime,
      
    }
    url = TRIP_SINGLE + 'fcc/hotel/list/list.html?' + createUrlParams(params)
  }else if (item.type == 'groundServices') {
    // 根据城市查询航站楼信息
    const endCityHzl = await getAirportList2(item.endCityCode)

    params = {
      skipType: '010012',
      ygys: trip?.travelReserveFlag ? 1 : 2, // 因公因私 1因公 2因私
      ydsj: item?.lastTime,
      fwzdmc: `${endCityHzl[0].airportList[0].name}${endCityHzl[0].airportList[0].airportTerminalList[0].terminal}航站楼`,
      fwzdid: endCityHzl[0].airportList[0].vetechAirportBh,
      hzl: endCityHzl[0].airportList[0].airportTerminalList[0].terminal,
      fwcsid: item?.endCityCodeSy,
      fwcsmc: `${endCityHzl[0].airportList[0].name}${endCityHzl[0].airportList[0].airportTerminalList[0].terminal}航站楼`,
    }
    url = TRIP_SINGLE + 'fcc/airservice/book/search.html?' + createUrlParams(params)

  }
  window.open(url,'_blank')
}


const cityName = ref('');
// 根据id递归获取城市名
const getCityName = (code: string, options) => {
  options.forEach((item) => {
    if (item.districts && item.districts.length) {
      getCityName(code, item.districts);
    } else {
      if (code === item.adcode) {
        cityName.value = item.name;
      }
    }
  });
  return cityName.value;
};


const covertLx = (list:any) => {
  if(!list || !list.length) {
    return ''
  }
  const res = list.map((item:any) => {
    if(item.travelUserNo) {
      return 1
    }else {
      return 2
    }
  })
  return res.join(',')
}
// 根据工号查询职级
const getDirectLineByUserCode = async (username:any) => {
  const res = await rechargeApi.getDirectLine({username: username})
  console.log('----根据工号查询职级------>', res.brandCode)
  return res
}
// 根据出行人转换成职级字符串
const convertZJ = async (list?: any) => {
  if(!list || !list.length) {
    return ''
  }
  let usernameList:any = []
  list.forEach((item: any) => {
      usernameList.push(item.travelUserNo || item.username)
  })
  // 使用 Promise.all 并行执行异步操作
  const results = await Promise.all(usernameList.map(username => {
    if (username) {
      return getDirectLineByUserCode(username)
    }else {
      return ''
    }
  }));
  let zjstr = results.map(res => res?.brandCode || '');
  console.log('----根据出行人转换成职级字符串------>', zjstr)
  return zjstr.join(',')
  
}

const onPageChange = (page: number, pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = page;
  searchKey.pageNum = page;
  searchKey.pageSize = pageSize;
  onFilterChange();
};

const handleReset = () => {
  from.value && from.value.resetFields();
  if (!searchKey.applyTime || searchKey.applyTime.length != 2) {
    searchKey.beginDate = '';
    searchKey.endDate = '';
  }
  chosedEndCityName.value = '';
  chosedBeginCityName.value = '';
  searchKey.beginCityCode = '';
  searchKey.endCityCode = '';
  onReFilterChange();
};

const historyList = () => {
  const url = TRIP_SINGLE + 'fcc/fcapply/ccsqd/initiator.html?djlx=99001&skipType=9900103'
  window.open(url)
}

const onCreateTimeChange = (dateRange: string[]) => {
  if (dateRange && dateRange.length === 2) {
    searchKey.beginDate = dateRange[0];
    searchKey.endDate = dateRange[1];
  } else {
    searchKey.beginDate = '';
    searchKey.endDate = '';
  }
};
// 审批状态
const approvalState = computed(() => {
  return getEnumOptions(TripApprovalStatus, true);
});

// 单据状态
const documentState = computed(() => {
  return getEnumOptions(TripDocumentStatus, true);
});

// 变更状态
const changeState = computed(() => {
  return getEnumOptions(TripChangeStatus, true);
});

// 预算状态

const budgeState = computed(() => {
  return getEnumOptions(TripBudgeStatus, true);
});



const cancelApply = (id: number) => {
  tripApi.cancelApply({ id }).then((res) => {
    hMessage.success('作废成功!');
    onFilterChange();
  });
};
const recallApply = (id: number) => {
  hModal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', {  }, '撤回后再提交,所有审批节点需重审,请确认是否继续进行此操作!'),
    onOk() {
      tripApi.recallApply({ id }).then((res) => {
        hMessage.success('撤回成功!');
        onFilterChange();
      });
    },
    onCancel() {
      console.log('取消');
    },
    class: 'test',
  });

};
const recallBgApply = (applyNo: number) => {
  hModal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', {  }, '撤回后再提交,所有审批节点需重审,请确认是否继续进行此操作!'),
    onOk() {
      tripApi.recallBgApply(applyNo).then((res) => {
        hMessage.success('撤回成功!');
        onFilterChange();
      });
    },
    onCancel() {
      console.log('取消');
    },
    class: 'test',
  });

};

const handleMenuClick = (e) => {
  console.log('0999', e);
};
</script>

<style scoped lang="less">
.empty {
  margin-top: 50px;
  border: 1px solid #f0f0f0;
  padding: 42px 24px 50px;
}
.container {
  display: flex;
  width: 100%;
  flex-direction: column;

  .search {
    display: flex;
    width: 100%;
  }
}

.list {
  display: flex;
  margin-top: 20px;
  width: 100%;
  flex-direction: column;

  .order-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-bottom: 20px;

    .order-header {
      width: 100%;
      background-color: #f5f5f5;
      height: 43px;
      line-height: 43px;
      padding-left: 20px;
      color: #333;
      font-size: 12px;
      border: 1px solid #eaeaea;
      display: flex;
      justify-content: space-between;

      .order-header-left {
        display: flex;
      }
      .order-header-right {
        display: flex;
        align-items: center;
      }
    }
    .order-body {
      display: flex;
      width: 100%;
      border: 1px solid #eaeaea;
      border-top: 0;
      flex-direction: row;

      .first {
        display: flex;
        flex: 3;
        padding: 24px 10px;
      }
      .second {
        display: flex;
        flex: 2;
        border-left: 1px solid #eaeaea;
        padding: 24px 10px;
      }
      .three {
        display: flex;
        flex: 2;
        border-left: 1px solid #eaeaea;
        padding: 24px 10px;
      }
      .last {
        display: flex;
        flex: 3;
        border-left: 1px solid #eaeaea;
        padding: 24px 10px;
      }

      .second,
      .three {
        .title {
          width: 80px;
          text-align: right;
          line-height: 20px;
        }

        .value {
          padding-left: 5px;
          width: calc(100% - 80px);
          line-height: 20px;
        }
      }

      .first,
      .last {
        .title {
          width: 100px;
          text-align: right;
          line-height: 20px;
        }

        .value {
          padding-left: 5px;
          width: calc(100% - 100px);
          line-height: 20px;
        }
      }
    }
    .order-footer {
      width: 100%;
      background-color: #f5f5f5;
      height: 43px;
      line-height: 43px;
      padding-right: 8px;
      color: #333;
      font-size: 12px;
      border-left: 1px solid #eaeaea;
      border-right: 1px solid #eaeaea;
      border-bottom: 1px solid #eaeaea;
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
    }
  }
}

.page {
  display: flex;
  width: 100%;
  flex-direction: row-reverse;
  margin-bottom: 20px;
}

.banner-contain {
  width: 100%;
  padding-top: 16px;
  margin-bottom: 20px;
  box-sizing: border-box;
  // background-image: url('@/assets/image/banner/baner-contain-bac.png');
  background-size: 100% 100%;
  .ban-con-title {
    display: flex;
    justify-content: space-between;
    padding-right: 24px;
    position: relative;
    .ban-title-left {
      font-weight: 600;
      font-size: 18px;
      color: #3983e5;
      padding-left: 24px;
      .ban-border {
        position: absolute;
        top: 0;
        left: 2px;
        width: 4px;
        height: 21px;
        background: linear-gradient(180deg, #3983e5 0%, #6bb9f4 100%);
      }
    }
    .ban-title-right {
      /* font-weight: 500; */
      font-size: 14px;
      color: #3983e5;
      display: flex;
      align-items: center;
      img {
        width: 18px;
        height: 18px;
      }
    }
  }
  .banner-contain-bottom {
    width: 100%;
    background-color: #fff;
    border-radius: 8px;
    .banner-contain-data {
      background-color: #f5f5f5;
      display: flex;
      height: 40px;
      align-items: center;
      padding: 0 12px;
      color: #262626;
      justify-content: space-between;
      font-weight: 400;
      border-bottom: 1px solid #eaeaea;
      width: 100%;
      background-color: #f5f5f5;
      height: 43px;
      line-height: 43px;
      padding-left: 20px;
      color: #333;
      font-size: 12px;
      border: 1px solid #eaeaea;
      div {
        display: flex;
        align-items: center;
      }
      .data-one {
        width: 195px;
        height: 28px;
        border-radius: 2px;
        box-sizing: border-box;
        padding-left: 8px;
        :deep(.ant-select-selector) {
          width: 195px;
          height: 28px !important;
          line-height: 28px;
          background: #fafafa;
          border: 0;
        }
        :deep(.ant-select-selection-item) {
          line-height: 28px;
          font-weight: 500;
          box-shadow: 0 0 0 0 rgba(0, 0, 0, 0) !important;
        }
      }
      .data-time {
        font-size: 13px;
        margin-left: 20px;
        img {
          width: 16px;
          height: 16px;
          margin-right: 2px;
        }
        .peice-mar {
          margin-right: 8px;
        }
      }
      .data-btn {
        display: flex;
        margin-left: 33px;
        .data-sta {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 78px;
          height: 22px;
          font-size: 12px;
          color: #10a710;
          background: #f6ffed;
          border-radius: 2px;
          border: 1px solid #b7eb8f;
          img {
            width: 12px;
            height: 12px;
          }
        }
        .data-tra {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 52px;
          height: 22px;
          background: #f0f9ff;
          border-radius: 2px;
          border: 1px solid #a1d1ff;
          font-size: 12px;
          color: #0073e5;
          margin-left: 4px;
        }
      }
    }
    .banner-detail {
      padding: 20px 40px;
      background: #ffffff;
      box-sizing: border-box;
      font-weight: 400;
      display: flex;
      justify-content: space-between;
      padding-right: 16px;
      font-family: '';
      display: flex;
      align-items: center;
      width: 100%;
      border: 1px solid #eaeaea;
      border-top: 0;
      flex-direction: row;
      .detail-info-i {
        display: flex;
        font-size: 14px;
        color: #8c8c8c;
        height: 20px;
        align-items: center;
        margin-bottom: 5px;

        .info-i-l {
          display: flex;
          align-items: center;
          width: 84px;
        }
        img {
          width: 16px;
          height: 16px;
        }
        .names {
          color: #262626;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .info-main {
          width: 200px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-family: '';
          /* margin-left: 8px; */
        }
      }
    }
    .banner-step {
      overflow: auto;
      display: flex;
      width: 800px;
      height: 72px;
      background: #fafafa;
      border-radius: 4px;
      display: flex;
      box-sizing: border-box;
      padding: 0 30px;
      color: rgba(0, 0, 0, 0.85);
      .step-item {
        display: flex;
        min-width: 200px;
        padding-top: 15px;
      }
      .step-item-start {
        min-width: 60px;
      }
      .step-name {
        // min-width: 145px;
        width: 45px;
        position: relative;
        > span {
          font-size: 14px;
        }
        .step-c {
          position: absolute;
          color: #8c8c8c;
          bottom: 32px;
          left: 0;
          width: 100px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          .city-name {
            flex: 1;
            overflow: hidden;
            display: inline-block;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .step-t {
          position: absolute;
          color: #8c8c8c;
          bottom: 14px;
          left: 0;
          width: 140px;
        }
      }
      .idol {
        border-bottom: 1px dashed #d9d9d9;
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        height: 11px;
        max-width: 100%;
        margin: 0 23px;
        min-width: 100px;
        .idol-img {
          position: relative;
          top: 3px;
          /* position: absolute;
                  top: -2px;
                  left: 50%;
                  transform: translateX(-50%); */
        }
        img {
          width: 24px;
          height: 24px;
          margin-left: 4px;
        }
      }
    }
    .hotal-img {
      width: 24px;
      height: 24px;
      margin-left: 4px;
    }
  }

  .btn-contain {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 16px;
    width: 100%;
    background-color: #f5f5f5;
    height: 43px;
    // line-height: 43px;
    padding-right: 8px;
    color: #333;
    font-size: 12px;
    border-left: 1px solid #eaeaea;
    border-right: 1px solid #eaeaea;
    border-bottom: 1px solid #eaeaea;
    display: flex;
    align-items: center;
    .btn {
      cursor: pointer;
      // width: 76px;
      // height: 24px;
      // background: linear-gradient(180deg, #6bb9f4 0%, #3983e5 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: #ffffff;
      font-weight: 400;
      border-radius: 3px;
      img {
        width: 14px;
        height: 14px;
        margin-right: 4px;
      }
    }
    .btn-yu {
      position: relative;
      .card-list {
        position: absolute;
        top: 28px;
        right: -10px;
        z-index: 999;
        width: 340px;
        /* height: 202px; */
        background: #ffffff;
        box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08),
          0px 3px 6px -4px rgba(0, 0, 0, 0.12);
        border-radius: 8px;
        padding: 20px;
        box-sizing: border-box;

        .triangle-con {
          position: absolute;
          width: 70px;
          height: 15px;
          top: -15px;
          right: 10px;
        }

        .triangle {
          float: right;
          margin-right: 10px;
          margin-top: 5px;
        }
        .card-list-item {
          display: flex;
          // justify-content: space-between;
          flex-direction: row;
          border-bottom: 1px solid #eee;
          padding-bottom: 17px;
          margin-top: 12px;
          &:first-child {
            margin-top: 0px;
          }
          &:last-child {
            border-bottom: 0px;
            padding-bottom: 0px;
          }
          /* align-items: center; */
          .item-l {
            width: 34px;
            height: 34px;
            margin-right: 0;
          }
          .banner-step {
            margin-left: 12px;
            width: 210px;
            height: 30px;
            overflow: inherit;
            display: flex;
            box-sizing: border-box;
            flex-direction: row;
            justify-content: start;
            padding: 0;
            background: #fff;
            .step-name_ {
              width: 75px;
              position: relative;
              font-family: '';
              span {
                overflow: hidden;
                white-space: nowrap;
                display: inline-block;
                width: 100%;
                text-overflow: ellipsis;
              }
              .step-t {
                bottom: 0px;
                position: absolute;
                font-size: 12px;
                color: #8c8c8c;
                left: 0;
                width: 55px;
                font-weight: 400;
              }
            }
            .idol_ {
              width: 40px;
              border-bottom: 1px dashed #d9d9d9;
              position: relative;
              margin: 0 7px;
              top: -6px;
            }
          }
          .btn-step_ {
            width: 44px;
            height: 24px;
            background: rgba(107, 185, 244, 0.1);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(57, 131, 229, 0.8);
            font-size: 14px;
          }
          .btn-reserve {
            background: linear-gradient(180deg, #6bb9f4 0%, #3983e5 100%);
            border-radius: 12px;
            color: #fff;
          }
        }
      }
      .card-more-ban {
        width: 82px;
        // height: 100px;
        background: #ffffff;
        box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08),
          0px 3px 6px -4px rgba(0, 0, 0, 0.12);
        border-radius: 4px;
        position: absolute;
        top: 30px;
        right: -10px;
        padding: 6px 17px 0;
        box-sizing: border-box;
        z-index: 99;

        .triangle-con {
          position: absolute;
          width: 70px;
          height: 15px;
          top: -15px;
          right: 9px;
        }

        .triangle {
          float: right;
          margin-right: 10px;
          margin-top: 5px;
        }

        // .triangle{
        //   position: absolute;
        //   width:20px;
        //   height: 20px;
        //   top: -15px;
        //   right: 20px;
        // }
        .more-ban-items:hover {
          color: rgba(255, 77, 79, 1);
        }
        .more-ban-items {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;
          height: 30px;
          line-height: 30px;
          color: rgba(0, 0, 0, 0.85);
          img {
            width: 14px;
            height: 14px;
          }
        }
      }
    }

    .btn-et:hover {
      border: 1px solid #2793f2;
    }

    .btn-et {
      border: 1px solid #3983e5;
      background: #fff;
      margin-left: 6px;
      color: #3983e5;
    }
    .btnMouseInter {
      color: #40a9ff;
      border: 1px solid #40a9ff;
    }
  }

  .add-apply:hover {
    background: linear-gradient(180deg, #3983e5 0%, #6bb9f4 100%);
  }

  .add-apply {
    width: 202px;
    height: 48px;
    margin-top: 8px;
    margin: 8px auto 0;
    background: linear-gradient(180deg, #6bb9f4 0%, #3983e5 100%);
    box-shadow: 0px 2px 8px 0px rgba(60, 134, 230, 0.26);
    border-radius: 24px;
    font-weight: 500;
    font-size: 18px;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    img {
      width: 28px;
      height: 28px;
      margin-right: 8px;
    }
  }
}
.mr-10 {
  margin-right: 10px;
}
</style>