<!-- inputNumber.vue -->
<template>
  <div class="input-wrapper">
    <label v-if="label" :for="id">{{ label }}</label>
    <input
      :id="id"
      :type="type"
      :value="modelValue"
      @input="$emit('update:modelValue', $event.target.value)"
      :placeholder="placeholder"
      :disabled="disabled"
      :class="{ error: error }"
    />
    <span v-if="error" class="error-message">{{ error }}</span>

    <h-button
      class="decrease-btn"
      :disabled="modelValue <= 1 || disabled"
      @click="modelValue > 1 && modelValue--"
    >
      -
    </h-button>
    <h-input-number
      :controls="false"
      :step="1"
      :min="1"
      :precision="0"
      v-model:value="modelValue"
      style="width: 80px; text-align: center"
      @blur="
        () => {
          if (modelValue === null || isNaN(modelValue)) {
            modelValue = 1 // 重置为最小值
          }
        }
      "
    />
    <h-button class="increase-btn" @click="modelValue++" :disabled="disabled">+</h-button>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: '',
  },
  min: {
    type: [Number, Object],
    default: undefined,
  },
  max: {
    type: [Number, Object],
    default: undefined,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const inputProps = computed(() => {
})

defineEmits(['update:modelValue'])
</script>

<style scoped></style>
