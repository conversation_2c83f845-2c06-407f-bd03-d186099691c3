<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px; overflow: auto">
    <!-- 标题和添加按钮区域 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
      <h2 style="margin: 0; font-size: 18px;">保险产品</h2>
      <h-button type="primary" @click="handleAdd">
        <PlusOutlined />
      </h-button>
    </div>

    <!-- 卡片区域 -->
    <h-spin :spinning="loading">
      <div class="insurance-list">
        <div v-for="(item, index) in dataSource" :key="item.id || index" class="insurance-item">
          <div class="insurance-card">
            <!-- 卡片头部 -->
            <div class="card-header">
              <div class="insurance-title">{{ item.insuranceName || '保险产品' }}</div>
              <div class="delete-action">
                <h-popconfirm
                  title="确定要删除这个保险吗？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="handleDelete(item)"
                >
                  <h-button type="text" danger size="small" style="padding: 4px; min-width: auto">
                    <DeleteOutlined />
                  </h-button>
                </h-popconfirm>
              </div>
            </div>

            <!-- 卡片内容 -->
            <div class="card-content">
              <div class="info-item">
                <span class="info-label">保费：</span>
                <span class="info-value price">{{ item.price || '暂无' }}元/人/天</span>
              </div>
              <div class="info-item">
                <span class="info-label">年龄要求：</span>
                <span class="info-value">{{ item.ageRequire }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">等待期：</span>
                <a-tooltip placement="bottomRight">
                  <template #title>
                    <span>{{ item.waitingPeriod }}</span>
                  </template>
                  <span class="info-value" >{{ item.waitingPeriod }}</span>
                </a-tooltip>
              </div>
            </div>

            <!-- 卡片底部操作按钮 -->
            <div class="card-footer">
              <div class="footer-section">
                <h-button type="link" @click="handlePreview(item)">
                  <EyeOutlined />
                  产品浏览
                </h-button>
              </div>
              <div class="footer-section">
                <h-button type="link" @click="handleEdit(item)">
                  <EditOutlined />
                  编辑
                </h-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </h-spin>
    <div class="footer-pagination" style="margin-top: 20px;">
      <a-pagination v-model:current="pagination.current" show-quick-jumper show-size-changer :total="pagination.total" @change="onChange" @showSizeChange="onShowSizeChange"/>
    </div>

    <!-- 空状态 -->
    <div v-if="dataSource.length === 0" class="empty-state">
      <div style="padding: 40px; text-align: center; color: #999">
        <div style="font-size: 16px; margin-bottom: 8px">暂无数据</div>
      </div>
    </div>



    <!-- 保险详情弹框 -->
    <InsuranceDetails
      v-model:open="detailModalOpen"
      modal-type="product"
      :insurance-detail="selectedInsurance"
      @close="handleDetailModalClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Button as hButton, message, Spin as hSpin, Popconfirm as hPopconfirm } from 'ant-design-vue';
import { DeleteOutlined, EyeOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { pascalCaseApi } from '@haierbusiness-front/apis';
import type { IPascalCaseFilter, IPascalCase } from '@haierbusiness-front/common-libs';
import { useRouter } from 'vue-router';
import InsuranceDetails from '@haierbusiness-front/components/mice/insuranceDetails/index.vue';

// 路由实例
const router = useRouter();

const current = ref<number>(1);
const onChange = (pageNumber: number,pageSize:number) => {
  console.log('Page: ', pageNumber,pageSize);
  pagination.value.current = pageNumber
  pagination.value.pageSize = pageSize
  fetchInsuranceList()
};
const onShowSizeChange = (current: number, pageSize: number) => {
  console.log(current, pageSize);
};

// 保险产品数据
const dataSource = ref<IPascalCase[]>([]);
const loading = ref(false);
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
});

// 弹框相关状态
const detailModalOpen = ref(false);
const selectedInsurance = ref<IPascalCase | null>(null);

// 获取保险列表数据
const fetchInsuranceList = async (params?: IPascalCaseFilter) => {
  try {
    loading.value = true;
    const queryParams = {
      pageNum: pagination.value.current,
      pageSize: pagination.value.pageSize,
      ...params,
    };

    const response = await pascalCaseApi.insuranceList(queryParams);

    if (response && response.records) {
      // 确保数据不重复
      const uniqueRecords = Array.from(new Map(response.records.map(item => 
        [item.id, item])).values());
      dataSource.value = uniqueRecords;
      pagination.value.total = response.total || 0;
      pagination.value.current = response.pageNum || 1;
      pagination.value.pageSize = response.pageSize || 10;
      
      console.log('获取到的保险数据:', dataSource.value);
    }
  } catch (error) {
    console.error('获取保险列表失败:', error);
    message.error('获取保险列表失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时获取数据 - 确保只调用一次
onMounted(() => {
  // 清空数据源，防止可能的累加
  dataSource.value = [];
  fetchInsuranceList();
});

const handleAdd = () => {
  // 跳转到新增保险页面
  router.push('/mice-merchant/pascalCase/component/insuranceEdit');
};

const handleDelete = async (item: IPascalCase) => {
  try {
    loading.value = true;
    const id = item.id;
    if (!id) {
      message.error('无效的保险ID');
      return;
    }
    await pascalCaseApi.insuranceRemove(id);
    const itemName = item.insuranceName || '保险产品';
    message.success(`删除保险"${itemName}"成功！`);
    // 重新获取列表数据
    await fetchInsuranceList();
  } catch (error) {
    console.error('删除保险失败:', error);
    const itemName = item.insuranceName || '保险产品';
    message.error(`删除保险"${itemName}"失败，请重试`);
  } finally {
    loading.value = false;
  }
};

const handlePreview = (item: IPascalCase) => {
  // 设置选中的保险数据并打开弹框
  selectedInsurance.value = item;
  detailModalOpen.value = true;
};

const handleEdit = (item: IPascalCase) => {
  // 跳转到编辑保险页面，传递保险ID
  router.push(`/mice-merchant/pascalCase/component/insuranceEdit?id=${item.id}`);
};

const handleDetailModalClose = () => {
  // 关闭弹框时清空选中的保险数据
  selectedInsurance.value = null;
  detailModalOpen.value = false;
};
</script>

<style lang="less" scoped>
.insurance-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 260px));
  gap: 16px;
  justify-content: flex-start;
}

.insurance-item {
  display: flex;
}

.insurance-card {
  width: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 0 10px 2px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 0 15px 3px rgba(0, 0, 0, 0.15);
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f5f5f5;
  min-height: 48px;
}

.insurance-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  flex: 1;
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.delete-action {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.card-content {
  padding: 10px 16px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  min-height: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  font-size: 14px;
  color: #666;
  min-width: 80px;
  width: 80px;
  font-weight: 500;
  text-align: left;
  flex-shrink: 0;
}

.info-value {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &.price {
    color: #ff9500;
    font-weight: 600;
    font-size: 15px;
  }
}

.card-footer {
  display: flex;
  border-top: 1px solid #f0f0f0;
  padding: 0;
}

.footer-section {
  flex: 1;
  text-align: center;
  padding: 8px 0;
  border-right: 1px solid #f0f0f0;

  &:last-child {
    border-right: none;
  }

  button {
    margin: 0;
    padding: 0;
    font-size: 14px;
    font-weight: 500;
  }
}

.empty-state {
  margin-top: 40px;
}

// 删除按钮样式调整
:deep(.ant-btn-text.ant-btn-dangerous:hover) {
  background-color: #fff2f0;
}
.footer-pagination{
  display: flex;
  justify-content: center;
}
</style>
