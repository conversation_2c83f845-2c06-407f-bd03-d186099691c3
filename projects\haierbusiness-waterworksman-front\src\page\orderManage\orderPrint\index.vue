<script setup lang="ts">
import { SearchOutlined, DownloadOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { userApi } from '@haierbusiness-front/apis';
import {
  Button as hButton,
  Col as hCol,
  Input as hInput,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  DatePicker as hDatePicker,
  message,
} from 'ant-design-vue';
import { computed, ref } from 'vue';
import { usePagination } from 'vue-request';
import { ColumnType } from 'ant-design-vue/es/table';
import PrintModal from './print-modal.vue';
type MyColumn = ColumnType & {
  children?: ColumnType[];
};

const { data, run: userApiRun, loading, current, pageSize } = usePagination(userApi.list);
const sorter = (a: any, b: any) => a.applicationForm.localeCompare(b.applicationForm);

/**
 * @表格相关
 * */
const selectedItems = ref<any>([]);

const tableProps = computed(() => ({
  rowKey: 'id',
  scroll: { x: 'max-content' },
  dataSource: data.value?.records || [],
  pagination: {
    showSizeChanger: true,
    showQuickJumper: true,
    total: data.value?.total,
    current: data.value?.pageNum,
    pageSize: data.value?.pageSize,
    style: { justifyContent: 'center' },
  },
  columns: [
    {
      title: '序号',
      dataIndex: 'index',
    },
    {
      title: '出库单类型',
      dataIndex: 'id',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '出库单号',
      dataIndex: '出库单号',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '下单日期',
      dataIndex: '下单日期',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '类型及数量',
      dataIndex: '类型及数量',
      children: [
        {
          title: '5加仑',
          dataIndex: '5加仑',
        },
        {
          title: '330ml瓶装水',
          dataIndex: '330ml瓶装水',
        },
        {
          title: '2加仑',
          dataIndex: '2加仑',
        },
      ],
    },
    {
      title: '状态',
      dataIndex: '状态',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ],
  // 支持多选
  rowSelection: {
    selectedRowKeys: selectedItems.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      console.log(selectedRowKeys, selectedRows);
      selectedItems.value = selectedRowKeys;
      console.log(selectedItems.value);
    },
  },
}));

const handleOemOrder = () => {
  console.log(selectedItems.value);
  message.info('暂无接口');
};

const handleStockOrder = () => {
  console.log(selectedItems.value);
  message.info('暂无接口');
};

const handleOrderReturn = () => {
  console.log(selectedItems.value);
  message.info('暂无接口');
};

/**
 * @表单相关
 * */
type OrderFormData = {
  applicationForm: string; // 申请单号
  orderDown: string; // 申请单位
  orderPrint: string; // 结算公司
  orderState: string | null; // 订单状态
  payState: string | null; // paymentStatus
};

const formData = ref<OrderFormData>({
  applicationForm: '', // 申请单号
  orderDown: '', // 申请单位
  orderPrint: '', // 结算公司
  orderState: null, // 订单状态
  payState: null, // paymentStatus
});

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  userApiRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
    ...formData.value,
  });
};

const handleTableExport = () => {
  message.info('暂无接口');
  console.log(formData.value, ' -- formData');
};

/**
 * 打印弹窗相关
 * */

const printModal = ref(false);
const openPrintModal = (record: any) => {
  printModal.value = true;
};

const closePrintModal = () => {
  printModal.value = false;
};
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <!-- 打印弹窗 -->
    <print-modal v-model:visible="printModal" @cancel="closePrintModal" @ok="closePrintModal">
    </print-modal>
    <!-- 页面主体 -->
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px" :gutter="[0, 10]">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="applicationForm">出库单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="applicationForm"
              v-model:value="formData.applicationForm"
              placeholder="请输入"
              autocomplete="off"
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="orderPrint">下单日期：</label>
          </h-col>
          <h-col :span="4">
            <h-date-picker
              style="width: 100%"
              id="orderPrint"
              v-model:value="formData.orderPrint"
              placeholder="请输入"
              value-format="YYYY-MM-DD"
              autocomplete="off"
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="orderState">订单状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              id="orderState"
              v-model="formData.orderState"
              placeholder="请选择"
              autocomplete="off"
              style="width: 100%"
            >
              <h-select-option value="10">已出库</h-select-option>
              <h-select-option value="20">未出库</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined /> 查询
            </h-button>
            <h-button style="margin-left: 10px" @click="handleTableExport"> <DownloadOutlined /> 导出 </h-button>
            <!-- <h-button style="margin-left: 10px" @click="handleTableReset">重置</h-button> -->
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table v-bind="tableProps" :loading="loading"  size="middle" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <h-button type="link" @click="openPrintModal(record)">打印 </h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}
</style>
