<script setup lang="ts">
import {
  showFailToast,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
  Form as <PERSON><PERSON><PERSON>,
  Field as <PERSON><PERSON><PERSON>,
  CellGroup as VanCellGroup,
  showDialog,
  showSuccessToast,
  NoticeBar,
  Popup as VanPopup,
  showLoadingToast,
  showToast
} from "vant";
import "vant/es/notice-bar/style";
import {
  payApi,
  compositionPayApi,
  virtualPayApi,
  budgetHaierPayApi
} from "@haierbusiness-front/apis";
import {
  IPayData,
  IQueryVirtualAccountsResponse,
  PaySourceConstant,
  HaierBudgetSourceConstant,
  IloginUser,
  IBudgetHaierTypesResponse
} from "@haierbusiness-front/common-libs";
import { computed, PropType, ref } from "vue";
import { removeStorageItem,ITraveler,isMobile } from "@haierbusiness-front/utils";
import { useRequest } from "vue-request";
import userSelectM from "@/components/userSelectM.vue"

interface Props {
  applicationCode: IPayData;
  budgetType:string,
  param:IPayData;
}

const props = withDefaults(defineProps<Props>(), {});

// 预算主体
const budgetOrganization = ref();
const budgetOrganizationName = ref();
// 受益主体
const beneficialOrganization = ref();
const beneficialOrganizationName = ref();
// 法人
const legalPerson = ref();
const legalPersonName = ref();
// 成本中心
const costCenter = ref();
const costCenterName = ref();
// 执行主体
const performCode = ref();
const performName = ref();

const budgeterCode = ref();
const budgeterName = ref();
// 查询的列表
const searchDataList = ref([]);


const emit = defineEmits(["setIsPayComplete",'payComplete',"isPayLoading"]);


// 费用科目
const {
  data: searchFeeItemsData,
  run: searchFeeItemsRun,
  loading: searchFeeItemsLoading,
} = useRequest(
    budgetHaierPayApi.searchFeeItems, {
      manual: false,
      defaultParams: [
        {
          applicationCode: props?.applicationCode,
          budgetSysCode: HaierBudgetSourceConstant.HBC.code,
          businessType:props.param?.businessType,
        }
      ]
    }
);
// 费用科目选项
const feeItemOptions = computed(() => {
  if (searchFeeItemsData.value) {
    return searchFeeItemsData.value.map(it => {
      return {"value": it.itemCode, "text": it.itemName}
    })
  } else {
    return []
  }
});

// 费用科目弹窗
const feeItem = ref<any>();
const feeItemName = ref<any>('');
const showPicker = ref<boolean>(false);

// 选择费用科目
const onConfirm = ({ selectedOptions }) => {
    feeItemName.value = selectedOptions[0]?.text
    feeItem.value = selectedOptions[0]?.value
    showPicker.value = false;
    // 如果已经选择了预算人 执行搜索
    if(budgeterCode.value){
      onSearch()
    }
};
// 选择预算人
const selectBudgeter = (item:ITraveler) => {
  budgeterCode.value = item.username
  budgeterName.value = item.nickName
  // budgetDepartmentCode.value = item.departmentCode
  // budgetDepartmentName.value = item.departmentName
  onSearch()
};

const onSearch = () => {
  if (!feeItem.value) {
    showToast('请选择费用科目!');
    return;
  }
  if (!budgeterCode.value) {
    showToast('请输入预算人!');
    return;
  }
  const toast = showLoadingToast({
    duration: 0,
    forbidClick: true,
    message: '请求中...',
  });
  // userLoading.value = true;
  // 查询前上次结果清空
  // 清空
  budgetOrganization.value = null;
  budgetOrganizationName.value = null;

  beneficialOrganization.value = null;
  beneficialOrganizationName.value = null;
  legalPerson.value = null;
  legalPersonName.value = null;
  costCenter.value = null;
  costCenterName.value = null;
  performCode.value = null;
  performName.value = null;

  budgetHaierPayApi
    .queryBudgetInfo({
      estimatorCode: budgeterCode.value,
      itemCode: feeItem.value,
    })
    .then((res: any) => {
      res.forEach((item:any,index:number)=>{
        item.index = index
      })
      toast.close()
      searchDataList.value = res;
      if (res && res.length) {
        budgetOrganization.value = res[0].budgetOrganization;
        budgetOrganizationName.value = res[0].budgetOrganizationName;
        beneficialOrganization.value = res[0].beneficialOrganization;
        beneficialOrganizationName.value = res[0].beneficialOrganizationName;
        legalPerson.value = res[0].legalPerson;
        legalPersonName.value = res[0].legalPersonName;
        costCenter.value = res[0].costCenter;
        costCenterName.value = res[0].costCenterName;
        performCode.value = res[0].performCode;
        performName.value = res[0].performName;
      }
    })
    .finally(() => {
      // userLoading.value = false;
    });
};


const pay = () => {
  if (!feeItem.value) {
    showToast('请选择费用科目!');
    return;
  }
  if (!budgeterCode.value) {
    showToast('请输入预算人!');
    return;
  }
  if (!budgetOrganization.value) {
    showToast("暂无预算主体，请点击搜索预算主体!")
    return;
  }
  if (!performCode.value) {
    showToast("暂无执行主体，请点击搜索执行主体!")
    return;
  }
  if (!legalPerson.value) {
    showToast("暂无出账法人，请点击搜索出账法人!")
    return;
  }
  const payComplete = () => {
    emit('payComplete', true);
  };
  emit('isPayLoading',true)
  // payLoading.value = true;
  budgetHaierPayApi
    .occupyBudget(
      {
        haierBudgetType: props.budgetType,
        feeItem: feeItem.value,
        feeItemName: feeItemName.value,
        budgeterCode: budgeterCode.value,
        budgeterName: budgeterName.value,
        budgetDepartmentCode: budgetOrganization.value,
        budgetDepartmentName:budgetOrganizationName.value,
        performCode:performCode.value,
        performName: performName.value,
        accountCompanyCode: legalPerson.value,
        accountCompanyName: legalPersonName.value,
        beneficialCode: beneficialOrganization.value,
        beneficialName: beneficialOrganizationName.value,
        costCenter:costCenter.value,
        costCenterName:costCenterName.value,
 
        // - 通用参数
        businessType:props.param?.businessType,
        orderCode: props.param?.orderCode,
        applicationCode: props.param?.applicationCode,
        payTypes: props.param?.payTypes,
        username: props.param?.username,
        providerCode: props.param?.providerCode,
        amount: Number(props.param?.amount),
        orderDetailsUrl: props.param?.orderDetailsUrl,
        notifyUrl: props.param?.notifyUrl,
        callbackUrl: props.param?.callbackUrl,
        description: props.param?.description,
        payload: props.param?.payload,
        paySource: isMobile() ? PaySourceConstant.MOBILE.type : PaySourceConstant.PC.type,
        paymentMethod: 2
      },
      {
        applicationCode: props.param?.applicationCode,
        excludes:
          'paySource,haierBudgetType,feeItem,feeItemName,budgeterCode,budgeterName,budgetDepartmentCode,budgetDepartmentName,performCode,performName,accountCompanyCode,accountCompanyName,beneficialCode,beneficialName,costCenter,costCenterName,paymentMethod',
        nonce: props.param?.hbNonce,
        timestamp: props.param?.hbTimestamp,
        sign: props.param?.sign,
      },
    )
    .then((it) => {
      payComplete();
      emit('isPayLoading',false)
    })
    .finally(() => {
      // payLoading.value = false;
      emit('isPayLoading',false)
    });
};

defineExpose({pay})

</script>

<template>
  <div class="contentBox">
    <van-form>
      <van-field
        v-model="feeItemName"
        required
        is-link
        readonly
        input-align="right"
        name="feeItem"
        label="费用科目"
        placeholder="点击选择费用科目"
        @click="showPicker = true"
      />
      <userSelectM label="预算人" :value="budgeterName" @chose="selectBudgeter" />
      <van-field
        v-model="performName"
        input-align="right"
        :disabled="true"
        name="执行主体"
        label="执行主体"
        placeholder="执行主体"
        :rules="[{ required: true, message: '请填写执行主体' }]"
      />
      <van-field
        v-model="budgetOrganizationName"
        input-align="right"
        :disabled="true"
        name="预算主体"
        label="预算主体"
        placeholder="预算主体"
        :rules="[{ required: true, message: '请填写预算主体' }]"
      />
      <van-field
        v-model="beneficialOrganizationName"
        :disabled="true"
        input-align="right"
        name="受益主体"
        label="受益主体"
        placeholder="受益主体"
        :rules="[{ required: true, message: '请填写受益主体' }]"
      />
      <van-field
        v-model="legalPersonName"
        :disabled="true"
        input-align="right"
        name="出账法人"
        label="出账法人"
        placeholder="出账法人"
        :rules="[{ required: true, message: '请填写出账法人' }]"
      />
      <van-field
        v-model="costCenterName"
        input-align="right"
        :disabled="true"
        name="成本中心"
        label="成本中心"
        placeholder="成本中心"
        :rules="[{ required: true, message: '请填写成本中心' }]"
      />
    </van-form>
  </div>
  <!-- 费用科目弹窗  -->
  <van-popup v-model:show="showPicker" position="bottom">
    <van-picker
      :columns="feeItemOptions"
      @confirm="onConfirm"
      @cancel="showPicker = false"
      :columns-field-names="customFieldName"
    />
  </van-popup>
</template>

<style scoped lang="less">
</style>

<style>
:root:root {
  --van-button-primary-background: #0073e5;
  --van-radio-checked-icon-color: #0073e5;
  --van-password-input-background: #f2f2f2;
}
</style>