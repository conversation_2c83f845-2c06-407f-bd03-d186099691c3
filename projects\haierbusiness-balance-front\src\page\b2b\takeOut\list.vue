<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { DownOutlined, ExclamationCircleOutlined, PlusOutlined,UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { b2bTakeOutBalanceApi } from '@haierbusiness-front/apis';
import {
  BalanceStatusConstant,
  BudgetNotifiedReleaseCvpStateConstant,
  HaierBudgetPayFeeItemConstant,
  HaierBudgetSourceConstant,
  IHaierAccountBillInfo,
  IJdB2bAccountRequest,
  IJdB2bCancelRequest,
  IJdB2bConfirmBalanceOrderBudgetRequest,
  IJdB2bConfirmRequest,
  IJdB2bListRequest,
  IJdB2bMarkReadRequest,
  IJdB2bRevokeConfirmRequest
} from '@haierbusiness-front/common-libs';
import {errorModal, getCurrentRouter, routerParam} from '@haierbusiness-front/utils';
import Eloading from '@haierbusiness-front/components/loading/ELoading.vue';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
const router = getCurrentRouter()
const columns: ColumnType[] = [
  {
    title: '汇总单号',
    dataIndex: 'code',
    fixed: 'left',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '结算状态',
    dataIndex: 'state',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '账单所属年月',
    dataIndex: 'periodYearMonth',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '汇总周期-开始日期',
    dataIndex: 'periodBegin',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '汇总周期-结束日期',
    dataIndex: 'periodEnd',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '总结算金额(含税)',
    dataIndex: 'totalAmount',
    width: '180px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '总税额',
    dataIndex: 'totalTaxAmount',
    width: '100px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '总结算金额(不含税)',
    dataIndex: 'exTaxTotalAmount',
    width: '180px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '预算系统',
    dataIndex: 'budgetSysCode',
    width: '90px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '预算释放状态',
    dataIndex: 'notifiedReleaseCvp',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '商互通单号',
    dataIndex: 'cvpCode',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '供应商编码',
    dataIndex: 'providerCode',
    width: '100px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '供应商名称',
    dataIndex: 'providerName',
    width: '180px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '预算部门编码',
    dataIndex: 'departmentCode',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '预算部门名称',
    dataIndex: 'departmentName',
    width: '260px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '结算单位编码',
    dataIndex: 'accountCompanyCode',
    width: '110px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '结算单位名称',
    dataIndex: 'accountCompanyName',
    width: '260px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '客户编码',
    dataIndex: 'customCode',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '客户名称',
    dataIndex: 'customName',
    width: '260px',
    align: 'center',
    ellipsis: true
  },
  {
    title: 'WBS编码',
    dataIndex: 'wbsCode',
    width: '180px',
    align: 'center',
    ellipsis: true
  },
  {
    title: 'WBS名称',
    dataIndex: 'wbsName',
    width: '180px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '研发项目编码',
    dataIndex: 'projectCode',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '研发项目名称',
    dataIndex: 'projectName',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '地产项目编码',
    dataIndex: 'dcProjectCode',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '地产项目名称',
    dataIndex: 'dcProjectName',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '地产分期编码',
    dataIndex: 'dcItemCode',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '地产分期名称',
    dataIndex: 'dcItemName',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '费用项目编码',
    dataIndex: 'feeItem',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '费用项目名称',
    dataIndex: 'feeItemName',
    width: '230px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '成本中心编码',
    dataIndex: 'costCenter',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '成本中心名称',
    dataIndex: 'costCenterName',
    width: '230px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '执行主体编码',
    dataIndex: 'performCode',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '执行主体名称',
    dataIndex: 'performName',
    width: '230px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '受益主体编码',
    dataIndex: 'beneficialCode',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '受益主体名称',
    dataIndex: 'beneficialName',
    width: '230px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '合同号',
    dataIndex: 'contractCode',
    width: '190px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '合同名称',
    dataIndex: 'contractName',
    width: '220px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '付款流水单号',
    dataIndex: 'balancePaymentCode',
    width: '180px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '付款时间',
    dataIndex: 'balancePaymentTime',
    width: '180px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '记账号',
    dataIndex: 'bookVoucherCode',
    width: '180px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改人',
    dataIndex: 'lastModifiedBy',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改时间',
    dataIndex: 'gmtModified',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '180px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<IJdB2bListRequest>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(b2bTakeOutBalanceApi.list);

const {
  data:exportListData,
  run:exportListApiRun,
  loading:exportListLoading,
} = useRequest(b2bTakeOutBalanceApi.exportList);

const {
  data: stateAccountApiData,
  run: stateAccountApiRun,
  loading: stateAccountApiLoading,
} = useRequest(
  b2bTakeOutBalanceApi.stateAccount,
  {
    manual: false
  }
);

const {
  data: cvpErrorAccountApiData,
  run: cvpErrorAccountApiRun,
  loading: cvpErrorAccountApiLoading,
} = useRequest(
  b2bTakeOutBalanceApi.cvpErrorAccount,
  {
    manual: false
  }
);


const {
  data: pushCvpErrorAccountApiData,
  run: pushCvpErrorAccountApiRun,
  loading: pushCvpErrorAccountApiLoading,
} = useRequest(
  b2bTakeOutBalanceApi.pushCvpErrorAccount,
  {
    manual: false
  }
);

const reset = () => {
  beginAndEnd.value = undefined
  searchParam.value = {}
}

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
  stateAccountApiRun(
    searchParam.value
  )
  cvpErrorAccountApiRun(
    searchParam.value
  )
  pushCvpErrorAccountApiRun(
    searchParam.value
  )
};

const accountedCount = computed(() => {
  if (stateAccountApiData.value && stateAccountApiData.value[10]) {
    return stateAccountApiData.value[10]
  } else {
    return 0
  }
});

const cvpErrorAccount = computed(() => {
  if (cvpErrorAccountApiData.value) {
    return cvpErrorAccountApiData.value
  } else {
    return 0
  }
});

const pushCvpErrorAccount = computed(() => {
  if (pushCvpErrorAccountApiData.value) {
    return pushCvpErrorAccountApiData.value
  } else {
    return 0
  }
});

// 汇总相关
const gotoAccount = () => {
  visibleNew.value = true
}
// 新增表单相关
const accountNewForm = ref()
const confirmLoading = ref(false)
const visibleNew = ref(false)
const newAccountForm = ref<IJdB2bAccountRequest>({})
const labelCol = { span: 10 }
const wrapperCol = { span: 10 }
const handleOk = () => {

  accountNewForm.value.validate().then(
    () => {
      confirmLoading.value = true;

      visibleNew.value = false;
      b2bTakeOutBalanceApi.account(
        newAccountForm.value
      ).then(it => {
        message.success("操作成功！");
        newAccountForm.value = {}
        handleTableChange({ current: 1, pageSize: pageSize.value | 10 })
      }).finally(() => {
        setTimeout(() => {
          confirmLoading.value = false;
        }, 100);
      });
    }
  )
}
const selectPeriodYearMonth = (item: any) => {
  const selectDay = dayjs(item, "YYYY-MM")
  newAccountForm.value.begin = selectDay.date(1) as unknown as string
  newAccountForm.value.end = selectDay.date(selectDay.daysInMonth()) as unknown as string
}

const gotoDetails = (record: IHaierAccountBillInfo) => {
  router.push({ path: "/balance/b2b/takeOut/details", query: { record: routerParam(record) } })
}
const confirmParam = ref<IJdB2bConfirmRequest>({})
const confirmAccountLoading = ref(false)
const confirmAccount = (records: IHaierAccountBillInfo[]) => {
  const illegalRecords = records.filter(it => {
    return it.state !== BalanceStatusConstant.ACCOUNTED.code
  })
  if (illegalRecords && illegalRecords.length > 0) {
    message.warning("汇总单[" + illegalRecords.map(it => {
      return it.code
    }).reduce((acc, it) => acc + ',' + it) + "]不为已汇总状态,不可确认");
    confirmAccountLoading.value = false
    return
  }
  confirmParam.value.accounts = records.map(it => {
    return { accountCode: it.code }
  })
  return b2bTakeOutBalanceApi.confirm(confirmParam.value)
    .then(res => {
      message.success("汇总单[" + records.map(it => {
        return it.code
      }).reduce((acc, it) => acc + ',' + it) + "]已确认成功");
      handleTableChange({ current: current.value, pageSize: pageSize.value })
    })
    .finally(() => {
      confirmAccountLoading.value = false
      accountSelectedRowKeys.value = []
      accountSelectedRows.value = []
    });
}
const revokeConfirmParam = ref<IJdB2bRevokeConfirmRequest>({})
const revokeConfirmLoading = ref(false)
const revokeConfirm = (records: IHaierAccountBillInfo[]) => {
  const illegalRecords = records.filter(it => {
    return it.state !== BalanceStatusConstant.CONFIRMED.code || it.cvpRollback === true
  })
  if (illegalRecords && illegalRecords.length > 0) {
    message.warning("汇总单[" + illegalRecords.map(it => {
      return it.code
    }).reduce((acc, it) => acc + ',' + it) + "]状态不可撤回");
    revokeConfirmLoading.value = false
    return
  }
  revokeConfirmParam.value.accountCodes = records.map(it => {
    return it.code
  })
  return b2bTakeOutBalanceApi.revokeConfirm(revokeConfirmParam.value)
    .then(res => {
      message.success("汇总单[" + records.map(it => {
        return it.code
      }).reduce((acc, it) => acc + ',' + it) + "]已成功撤销确认!");
      handleTableChange({ current: current.value, pageSize: pageSize.value })
    })
    .finally(() => {
      revokeConfirmLoading.value = false
      accountSelectedRowKeys.value = []
      accountSelectedRows.value = []
    });
}

const cancelParam = ref<IJdB2bCancelRequest>({})
const cancelLoading = ref(false)
const cancel = (records: IHaierAccountBillInfo[]) => {
  const illegalRecords = records.filter(it => {
    return it.state !== BalanceStatusConstant.ACCOUNTED.code
  })
  if (illegalRecords && illegalRecords.length > 0) {
    message.warning("汇总单[" + illegalRecords.map(it => {
      return it.code
    }).reduce((acc, it) => acc + ',' + it) + "]不为已汇总状态,不可取消");
    cancelLoading.value = false
    return
  }
  cancelParam.value.accountCodes = records.map(it => {
    return it.code
  })
  return b2bTakeOutBalanceApi.cancel(cancelParam.value)
    .then(res => {
      message.success("汇总单[" + records.map(it => {
        return it.code
      }).reduce((acc, it) => acc + ',' + it) + "]已取消确认!");
      handleTableChange({ current: current.value, pageSize: pageSize.value })
    })
    .finally(() => {
      cancelLoading.value = false
      accountSelectedRowKeys.value = []
      accountSelectedRows.value = []
    });
}

const confirmBudgetParam = ref<IJdB2bConfirmBalanceOrderBudgetRequest>({})

const confirmBudget = (record: IHaierAccountBillInfo) => {
  confirmBudgetParam.value.accountCode = record.code
  return b2bTakeOutBalanceApi.confirmBalanceOrderBudget(confirmBudgetParam.value).then(it => {
    message.success("汇总单[" + record.code + "]已全部释放预算成功并通知商户通!");
    handleTableChange({ current: current.value, pageSize: pageSize.value })
  });
}

const markReadParam = ref<IJdB2bMarkReadRequest>({})
const markReadLoading = ref(false)
const markRead = (records: IHaierAccountBillInfo[]) => {

  markReadParam.value.accountCodes = records.map(it => {
    return it.code
  })
  return b2bTakeOutBalanceApi.markRead(markReadParam.value)
    .then(res => {
      message.success("汇总单[" + records.map(it => {
        return it.code
      }).reduce((acc, it) => acc + ',' + it) + "]已标记已读!");
      handleTableChange({ current: current.value, pageSize: pageSize.value })
    })
    .finally(() => {
      markReadLoading.value = false
      accountSelectedRowKeys.value = []
      accountSelectedRows.value = []
    });
}

const advancedSearchVisible = ref(false)
const gotoAdvancedSearch = () => {
  if (advancedSearchVisible.value) {
    advancedSearchVisible.value = false
  } else {
    advancedSearchVisible.value = true
  }
}

const beginAndEnd = ref<[Dayjs, Dayjs]>()
watch(() => beginAndEnd.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.begin = n[0]
    searchParam.value.end = n[1]
  } else {
    searchParam.value.begin = undefined
    searchParam.value.end = undefined
  }
});

const accountSelectedRowKeys = ref<Key[]>([])
const accountSelectedRows = ref()
const accountSelection: TableProps['rowSelection'] = {
  selectedRowKeys: accountSelectedRowKeys as any,
  onChange: (selectedRowKeys: Key[], selectedRows: DataType[]) => {
    accountSelectedRowKeys.value = selectedRowKeys
    accountSelectedRows.value = selectedRows
  },
  getCheckboxProps: (record: DataType) => ({
    disabled: record.id === 2, // Column configuration not to be checked
    name: record.name,
  }),
};
</script>

<template>
  <Eloading :loading="confirmLoading"></Eloading>
  <Eloading :loading="confirmAccountLoading"></Eloading>
  <Eloading :loading="revokeConfirmLoading"></Eloading>
  <Eloading :loading="cancelLoading"></Eloading>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <!-- 新增汇总弹窗开始 -->
    <h-modal v-model:visible="visibleNew" :title="'生成结算汇总单'" @ok="handleOk">
      <h-form ref="accountNewForm" :model="newAccountForm" :label-col="labelCol" :wrapper-col="wrapperCol">
        <h-form-item label="汇总月份" name="periodYearMonth" :rules="[{ required: true, message: '请选择汇总月份!' }]">
          <h-date-picker v-model:value="newAccountForm.periodYearMonth" value-format="YYYY-MM" picker="month"
            @change="selectPeriodYearMonth" />
        </h-form-item>
        <h-form-item label="汇总开始日期" name="begin" :rules="[{ required: true, message: '请输入开始日期!' }]">
          <h-date-picker v-model:value="newAccountForm.begin" />
        </h-form-item>
        <h-form-item label="汇总结束日期" name="end" :rules="[{ required: true, message: '请输入结束日期!' }]">
          <h-date-picker v-model:value="newAccountForm.end" />
        </h-form-item>
      </h-form>
    </h-modal>
    <!-- 新增汇总弹窗结束 -->

    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchAccountCode">汇总单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="searchAccountCode" v-model:value="searchParam.accountCode" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchState">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.state" style="width: 100%" allow-clear>
              <h-select-option :value="BalanceStatusConstant.CANCEL.code">{{
                BalanceStatusConstant.CANCEL.name
              }}
              </h-select-option>
              <h-select-option :value="BalanceStatusConstant.ACCOUNTED.code">{{
                BalanceStatusConstant.ACCOUNTED.name
              }}
              </h-select-option>
              <h-select-option :value="BalanceStatusConstant.CONFIRMED.code">{{
                BalanceStatusConstant.CONFIRMED.name
              }}
              </h-select-option>
              <h-select-option :value="BalanceStatusConstant.SETTLED.code">{{
                BalanceStatusConstant.SETTLED.name
              }}
              </h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="periodYearMonth">账单月份：</label>
          </h-col>
          <h-col :span="4">
            <h-date-picker v-model:value="searchParam.periodYearMonth" value-format="YYYY-MM" picker="month"
              style="width: 100%;" />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="cvpCode">CVP单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="cvpCode" v-model:value="searchParam.cvpCode" placeholder="" autocomplete="off" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="budgetSysCode">预算类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.budgetSysCode" style="width: 100%" allow-clear>
              <h-select-option :value="HaierBudgetSourceConstant.BCC.code">{{
                HaierBudgetSourceConstant.BCC.name
              }}
              </h-select-option>
              <h-select-option :value="HaierBudgetSourceConstant.GEMS.code">{{
                HaierBudgetSourceConstant.GEMS.name
              }}
              </h-select-option>
              <h-select-option :value="HaierBudgetSourceConstant.HBC.code">{{
                HaierBudgetSourceConstant.HBC.name
              }}
              </h-select-option>
              <h-select-option :value="HaierBudgetSourceConstant.KEMS.code">{{
                HaierBudgetSourceConstant.KEMS.name
              }}
              </h-select-option>
              <h-select-option :value="HaierBudgetSourceConstant.RRSGEMS.code">{{
                HaierBudgetSourceConstant.RRSGEMS.name
              }}
              </h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="feeItem">费用项目：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.feeItem" style="width: 100%" allow-clear>
              <h-select-option :value="HaierBudgetPayFeeItemConstant.LP.code">
                {{ HaierBudgetPayFeeItemConstant.LP.name }}
              </h-select-option>
              <h-select-option :value="HaierBudgetPayFeeItemConstant.ZGHD.code">
                {{ HaierBudgetPayFeeItemConstant.ZGHD.name }}
              </h-select-option>
              <h-select-option :value="HaierBudgetPayFeeItemConstant.BMZF.code">
                {{ HaierBudgetPayFeeItemConstant.BMZF.name }}
              </h-select-option>
              <h-select-option :value="HaierBudgetPayFeeItemConstant.NDJDFLSR.code">
                {{ HaierBudgetPayFeeItemConstant.NDJDFLSR.name }}
              </h-select-option>
              <h-select-option :value="HaierBudgetPayFeeItemConstant.ZGHDAQ.code">
                {{ HaierBudgetPayFeeItemConstant.ZGHDAQ.name }}
              </h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="accountCompanyCode">结算单位编码：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="accountCompanyCode" v-model:value="searchParam.accountCompanyCode" placeholder="请输入完整编码"
              autocomplete="off" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="departmentCode">预算部门编码：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="departmentCode" v-model:value="searchParam.departmentCode" placeholder="请输入完整编码"
              autocomplete="off" allow-clear />
          </h-col>
        </h-row>
        <template v-if="advancedSearchVisible">
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
            <h-col :span="2" style="text-align: right;">
              <label for="projectCode">研发项目编码：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="projectCode" v-model:value="searchParam.projectCode" placeholder="请输入完整编码" autocomplete="off"
                allow-clear />
            </h-col>
            <h-col :span="2" style="text-align: right;">
              <label for="projectName">研发项目名称：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="projectName" v-model:value="searchParam.projectName" placeholder="模糊匹配" autocomplete="off"
                allow-clear />
            </h-col>
            <h-col :span="2" style="text-align: right;">
              <label for="wbsCode">WBS编码：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="wbsCode" v-model:value="searchParam.wbsCode" placeholder="请输入完整编码" autocomplete="off"
                allow-clear />
            </h-col>
            <h-col :span="2" style="text-align: right;">
              <label for="wbsName">WBS名称：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="wbsName" v-model:value="searchParam.wbsName" placeholder="模糊匹配" autocomplete="off"
                allow-clear />
            </h-col>
          </h-row>
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
            <h-col :span="2" style="text-align: right;">
              <label for="dcProjectCode">地产项目编码：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="dcProjectCode" v-model:value="searchParam.dcProjectCode" placeholder="请输入完整编码"
                autocomplete="off" allow-clear />
            </h-col>

            <h-col :span="2" style="text-align: right;">
              <label for="dcProjectName">地产项目名称：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="dcProjectName" v-model:value="searchParam.dcProjectName" placeholder="模糊匹配" autocomplete="off"
                allow-clear />
            </h-col>

            <h-col :span="2" style="text-align: right;">
              <label for="dcItemCode">地产分期编码：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="dcItemCode" v-model:value="searchParam.dcItemCode" placeholder="请输入完整编码" autocomplete="off"
                allow-clear />
            </h-col>

            <h-col :span="2" style="text-align: right;">
              <label for="dcItemName">地产分期名称：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="dcItemName" v-model:value="searchParam.dcItemName" placeholder="模糊匹配" autocomplete="off"
                allow-clear />
            </h-col>
          </h-row>
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">

            <h-col :span="2" style="text-align: right;">
              <label for="accountCompanyName">结算单位名称：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="accountCompanyName" v-model:value="searchParam.accountCompanyName" placeholder="模糊匹配"
                autocomplete="off" allow-clear />
            </h-col>
            <h-col :span="2" style="text-align: right;">
              <label for="departmentName">预算部门名称：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="departmentCode" v-model:value="searchParam.departmentName" placeholder="模糊匹配"
                autocomplete="off" allow-clear />
            </h-col>
            <h-col :span="2" style="text-align: right;">
              <label for="customCode">客户编码：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="customCode" v-model:value="searchParam.customCode" placeholder="请输入完整编码" autocomplete="off"
                allow-clear />
            </h-col>
            <h-col :span="2" style="text-align: right;">
              <label for="customName">客户名称：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="customName" v-model:value="searchParam.customName" placeholder="模糊匹配" autocomplete="off"
                allow-clear />
            </h-col>
          </h-row>
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
            <h-col :span="2" style="text-align: right;">
              <label for="detailBusinessCode">业务单号：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="detailBusinessCode" v-model:value="searchParam.detailBusinessCode" placeholder=""
                autocomplete="off" allow-clear />
            </h-col>
            <h-col :span="2" style="text-align: right;">
              <label for="detailPaymentCode">业务支付单号：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="detailPaymentCode" v-model:value="searchParam.detailPaymentCode" placeholder=""
                autocomplete="off" allow-clear />
            </h-col>
            <h-col :span="2" style="text-align: right;">
              <label for="detailRefundBusinessCode">退款单号：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="detailRefundBusinessCode" v-model:value="searchParam.detailRefundBusinessCode" placeholder=""
                autocomplete="off" allow-clear />
            </h-col>
            <h-col :span="2" style="text-align: right;">
              <label for="detailRefundPaymentCode">退款支付单号：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="detailRefundPaymentCode" v-model:value="searchParam.detailRefundPaymentCode" placeholder=""
                autocomplete="off" allow-clear />
            </h-col>
          </h-row>

          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
            <h-col :span="2" style="text-align: right;">
              <label for="detailBudgetCode">预算单号：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="detailBudgetCode" v-model:value="searchParam.detailBudgetCode" placeholder=""
                autocomplete="off" allow-clear />
            </h-col>

            <h-col :span="2" style="text-align: right;">
              <label for="beginAndEnd">汇总日期：</label>
            </h-col>
            <h-col :span="4">
              <h-range-picker v-model:value="beginAndEnd" value-format="YYYY-MM-DD" style="width: 100%;" />
            </h-col>
            <h-col :span="2" style="text-align: right;">
              <label for="pushErrorState">预算推送错误：</label>
            </h-col>
            <h-col :span="4">
              <h-select ref="select" v-model:value="searchParam.pushErrorState" style="width: 100%" allow-clear>
                <h-select-option :value="1">
                  全部
                </h-select-option>
                <h-select-option :value="2">
                  未读
                </h-select-option>
              </h-select>
            </h-col>
            <h-col :span="2" style="text-align: right;">
              <label for="notifiedReleaseCvp">预算释放通知：</label>
            </h-col>
            <h-col :span="4">
              <h-select ref="select" v-model:value="searchParam.notifiedReleaseCvp" style="width: 100%" allow-clear>
                <h-select-option :value="BudgetNotifiedReleaseCvpStateConstant.NOT.code">
                  {{ BudgetNotifiedReleaseCvpStateConstant.NOT.name }}
                </h-select-option>
                <h-select-option :value="BudgetNotifiedReleaseCvpStateConstant.SUCCESS.code">
                  {{ BudgetNotifiedReleaseCvpStateConstant.SUCCESS.name }}
                </h-select-option>
                <h-select-option :value="BudgetNotifiedReleaseCvpStateConstant.ERROR.code">
                  {{ BudgetNotifiedReleaseCvpStateConstant.ERROR.name }}
                </h-select-option>
              </h-select>
            </h-col>
          </h-row>
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
            <h-col :span="2" style="text-align: right;">
              <label for="costCenter">成本中心编码：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="costCenter" v-model:value="searchParam.costCenter" placeholder="请输入完整编码"
                autocomplete="off" allow-clear />
            </h-col>
            <h-col :span="2" style="text-align: right;">
              <label for="costCenterName">成本中心名称：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="costCenterName" v-model:value="searchParam.costCenterName" placeholder="模糊匹配"
                autocomplete="off" allow-clear />
            </h-col>
            <h-col :span="2" style="text-align: right;">
              <label for="performCode">执行主体编码：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="performCode" v-model:value="searchParam.performCode" placeholder="请输入完整编码" autocomplete="off"
                allow-clear />
            </h-col>
            <h-col :span="2" style="text-align: right;">
              <label for="performName">执行主体名称：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="performName" v-model:value="searchParam.performName" placeholder="模糊匹配" autocomplete="off"
                allow-clear />
            </h-col>
          </h-row>
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
            <h-col :span="2" style="text-align: right;">
              <label for="beneficialCode">受益主体编码：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="beneficialCode" v-model:value="searchParam.beneficialCode" placeholder="请输入完整编码"
                autocomplete="off" allow-clear />
            </h-col>
            <h-col :span="2" style="text-align: right;">
              <label for="beneficialName">受益主体名称：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="beneficialName" v-model:value="searchParam.beneficialName" placeholder="模糊匹配"
                autocomplete="off" allow-clear />
            </h-col>
          </h-row>
        </template>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button type="link" @click="gotoAdvancedSearch()">
              <UpOutlined v-if="advancedSearchVisible" />
              <DownOutlined v-else />
              高级搜索
            </h-button>
            <h-button type="primary" style="margin-right: 10px" :loading="exportListLoading"  @click="exportListApiRun(searchParam);">
              <UploadOutlined />
              导出
            </h-button>
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
            <h-button type="primary" @click="gotoAccount"
              :disabled="accountSelectedRowKeys && accountSelectedRowKeys.length > 0">
              <PlusOutlined />
              生成汇总单
            </h-button>
            <h-popconfirm title="确认汇总并推送商户通?" :disabled="!(accountSelectedRowKeys && accountSelectedRowKeys.length > 0)"
              @confirm="confirmAccountLoading = true, confirmAccount(accountSelectedRows)">
              <h-button type="primary" style="margin-left: 10px;"
                :disabled="!(accountSelectedRowKeys && accountSelectedRowKeys.length > 0)">确认</h-button>
            </h-popconfirm>
            <h-popconfirm title="确认取消汇总?" :disabled="!(accountSelectedRowKeys && accountSelectedRowKeys.length > 0)"
              @confirm="cancelLoading = true, cancel(accountSelectedRows)">
              <h-button type="primary" style="margin-left: 10px;"
                :disabled="!(accountSelectedRowKeys && accountSelectedRowKeys.length > 0)">取消</h-button>
            </h-popconfirm>
            <h-popconfirm title="确认撤回?(同时撤回商户通推送)"
              :disabled="!(accountSelectedRowKeys && accountSelectedRowKeys.length > 0)"
              @confirm="revokeConfirmLoading = true, revokeConfirm(accountSelectedRows)">
              <h-button type="primary" style="margin-left: 10px;"
                :disabled="!(accountSelectedRowKeys && accountSelectedRowKeys.length > 0)">撤回</h-button>
            </h-popconfirm>
            <h-button style="margin-left: 10px;" type="primary"
              :disabled="!(accountSelectedRowKeys && accountSelectedRowKeys.length > 0)"
              @click="accountSelectedRows = [], accountSelectedRowKeys = []">
              取消多选
            </h-button>
            <span style="margin-left: 8px">
              <template v-if="accountSelectedRowKeys && accountSelectedRowKeys.length > 0">
                {{ `已选择 ${accountSelectedRowKeys.length} 条汇总单` }}
              </template>
            </span>
          </h-col>
          <h-col :span="12" style="text-align: right;">
            <template v-if="accountedCount > 0">
              <h-popconfirm title="检索当前条件下“已汇总”记录?"
                @confirm="searchParam.state = BalanceStatusConstant.ACCOUNTED.code, handleTableChange({ current: current, pageSize: pageSize })">
                <span style="padding-right: 30px;">
                  <h-badge :count="accountedCount" show-zero>
                    <h-tag color="warning" style="line-height: 25px;cursor: pointer;">已汇总</h-tag>
                  </h-badge>
                </span>
              </h-popconfirm>
            </template>
            <template v-else>
              <span style="padding-right: 30px;">
                <h-badge :count="accountedCount" show-zero>
                  <h-tag color="warning" style="line-height: 25px;cursor: pointer;">已汇总</h-tag>
                </h-badge>
              </span>
            </template>
            <template v-if="cvpErrorAccount > 0">
              <h-popconfirm title="检索当前条件下“释放通知异常”记录?"
                @confirm="searchParam.notifiedReleaseCvp = BudgetNotifiedReleaseCvpStateConstant.ERROR.code, handleTableChange({ current: current, pageSize: pageSize })">
                <span style="padding-right: 30px;">
                  <h-badge :count="cvpErrorAccount" show-zero>
                    <h-tag color="error" style="line-height: 25px;cursor: pointer;">通知异常</h-tag>
                  </h-badge>
                </span>
              </h-popconfirm>
            </template>
            <template v-else>
              <span style="padding-right: 30px;">
                <h-badge :count="cvpErrorAccount" show-zero>
                  <h-tag color="error" style="line-height: 25px;cursor: pointer;">通知异常</h-tag>
                </h-badge>
              </span>
            </template>
            <template v-if="pushCvpErrorAccount > 0">
              <h-popconfirm title="检索当前条件下所有未读的“推送异常”记录?"
                @confirm="searchParam.pushErrorState = 2, handleTableChange({ current: current, pageSize: pageSize })">
                <span style="padding-right: 10px;">
                  <h-badge :count="pushCvpErrorAccount" show-zero>
                    <h-tag color="error" style="line-height: 25px;cursor: pointer;">推送异常</h-tag>
                  </h-badge>
                </span>
              </h-popconfirm>
            </template>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :row-selection="accountSelection" :columns="columns" :row-key="record => record.id" :size="'small'"
          :data-source="dataSource" :pagination="pagination" :scroll="{ y: 550 }" :loading="loading"
          @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'code'">
              <h-button type="link" @click="gotoDetails(record)">{{ record.code }}</h-button>
            </template>
            <template v-if="column.dataIndex === 'notifiedReleaseCvp'">
              <h-tag v-if="record.notifiedReleaseCvp === BudgetNotifiedReleaseCvpStateConstant.NOT.code"
                color="default">{{
                  BudgetNotifiedReleaseCvpStateConstant.NOT.name
                }}
              </h-tag>
              <h-tag v-if="record.notifiedReleaseCvp === BudgetNotifiedReleaseCvpStateConstant.SUCCESS.code"
                color="processing">{{
                  BudgetNotifiedReleaseCvpStateConstant.SUCCESS.name
                }}
              </h-tag>
              <h-tag v-if="record.notifiedReleaseCvp === BudgetNotifiedReleaseCvpStateConstant.ERROR.code"
                color="error">{{
                  BudgetNotifiedReleaseCvpStateConstant.ERROR.name
                }}
              </h-tag>
            </template>
            <template v-if="column.dataIndex === 'state'">
              <h-tag v-if="record.state === BalanceStatusConstant.CANCEL.code" color="default">{{
                BalanceStatusConstant.CANCEL.name
              }}
              </h-tag>
              <h-tag v-if="record.state === BalanceStatusConstant.ACCOUNTED.code" color="warning">{{
                BalanceStatusConstant.ACCOUNTED.name
              }}
              </h-tag>
              <h-tag v-if="record.state === BalanceStatusConstant.CONFIRMED.code" color="processing">{{
                BalanceStatusConstant.CONFIRMED.name
              }}
              </h-tag>
              <h-tag v-if="record.state === BalanceStatusConstant.SETTLED.code" color="success">{{
                BalanceStatusConstant.SETTLED.name
              }}
              </h-tag>
              <h-popconfirm v-if="record.cvpErrorMessage" :title="record.cvpErrorMessage" :ok-text="'已读'"
                @confirm="markRead([record])" :showCancel="false">
               <template v-if="!record.readCvpErrorMessage">
                  <h-badge dot>
                    <ExclamationCircleOutlined style="color: red;" />
                  </h-badge>
               </template>
               <template v-else>
                   <ExclamationCircleOutlined style="color: red;" />
                </template>
              </h-popconfirm>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <template v-if="accountSelectedRowKeys && accountSelectedRowKeys.length > 0">
                <h-button style="color: #d1d0d0da;" type="text"
                  v-if="record.state === BalanceStatusConstant.ACCOUNTED.code">确认</h-button>
                <h-button style="color: #d1d0d0da;" type="text"
                  v-if="record.state === BalanceStatusConstant.ACCOUNTED.code">取消</h-button>
                <h-button style="color: #d1d0d0da;" type="text"
                  v-if="record.state === BalanceStatusConstant.CONFIRMED.code && record.cvpRollback !== true">撤回
                </h-button>
                <h-button style="color: #d1d0d0da;" type="text"
                  v-if="record.notifiedReleaseCvp === BudgetNotifiedReleaseCvpStateConstant.ERROR.code">重新通知
                </h-button>
              </template>
              <template v-else>
                <h-popconfirm v-if="record.state === BalanceStatusConstant.ACCOUNTED.code" title="确认汇总并推送商户通?"
                  @confirm="confirmAccount([record])">
                  <h-button type="link">确认</h-button>
                </h-popconfirm>
                <h-popconfirm v-if="record.state === BalanceStatusConstant.ACCOUNTED.code" title="确认取消汇总?"
                  @confirm="cancel([record])">
                  <h-button type="link">取消</h-button>
                </h-popconfirm>
                <h-popconfirm v-if="record.state === BalanceStatusConstant.CONFIRMED.code && record.cvpRollback !== true"
                  title="确认撤回?(同时撤回商户通推送)" @confirm="revokeConfirm([record])">
                  <h-button type="link">撤回</h-button>
                </h-popconfirm>
                <h-popconfirm v-if="record.notifiedReleaseCvp === BudgetNotifiedReleaseCvpStateConstant.ERROR.code"
                  title="确认重新通知商户通?（全部释放预算成功后将通知商户通）" @confirm="confirmBudget(record)">
                  <h-button type="link" style="color: red;">重新通知</h-button>
                </h-popconfirm>
              </template>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
