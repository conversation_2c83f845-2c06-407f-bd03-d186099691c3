import { download, get, post } from '../request'
import { 
    IMeetingHandoversFilter, 
    IMeetingHandovers,
    IPageResponse, 
    Result } from '@haierbusiness-front/common-libs'


export const meetingHandoversApi = {
    list: (params: IMeetingHandoversFilter): Promise<IPageResponse<IMeetingHandovers>> => {
        return get('/mice-bid/api/mice/connect/list', params)
    },

    details: (id: number): Promise<IMeetingHandovers> => {
        return get('/mice-bid/api/mice/connect/getConnect', {
            id
        })
    },

    save: (params: IMeetingHandovers): Promise<Result> => {
        return post('merchant/api/meetingHandovers/save', params)
    },

    edit: (params: IMeetingHandovers): Promise<Result> => {
        return post('merchant/api/meetingHandovers/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post(`/mice-bid/api/mice/connect/delete?id=${id}`, {})
    },
}
