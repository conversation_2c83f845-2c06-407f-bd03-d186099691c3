<script setup>
import { onMounted, onUnmounted } from "vue";
import AMapLoader from "@amap/amap-jsapi-loader";

let map = null;

onMounted(() => {
  window._AMapSecurityConfig = {
    securityJsCode: "610db7cc7881574494e34fd00b13ab97",
  };
});

const createByMap = (lon,lat) =>{
  AMapLoader.load({
    key: "25569e43d6c6bcfa4d39a1b920d8d2d1", // 申请好的Web端开发者Key，首次调用 load 时必填
    version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    plugins: ["AMap.Scale"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
  })
    .then((AMap) => {
        map = new AMap.Map("map", {
          // 设置地图容器id
          viewMode: "3D", // 是否为3D地图模式
          zoom: 11, // 初始化地图级别
          center: [lon, lat], // 初始化地图中心点位置
        });
        AMap.plugin("AMap.PlaceSearch", function () {
          var placeSearch = new AMap.PlaceSearch({
            //city 指定搜索所在城市，支持传入格式有：城市名、citycode 和 adcode
            city: "",
            pageSize: 50, //每页结果数,默认10
            pageIndex: 1, //请求页码，默认1
          });
          var cpoint = [lon,lat]; //中心点坐标
          placeSearch.searchNearBy(null, cpoint, 200, function (status, result) {
            //查询成功时，result 即对应匹配的 POI 信息
            console.log(result,"*-*-*---*********---");
          });
        });
    })
    .catch((e) => {
      console.log(e);
    });
}

const addMarker = (lon,lat) =>{
  //创建一个 Marker 实例：
  const marker = new AMap.Marker({
    position: new AMap.LngLat(lon, lat), //经纬度对象，也可以是经纬度构成的一维数组[116.39, 39.9]
  });
  //将创建的点标记添加到已有的地图实例：
  map.add(marker);
}

const setZoomAndCenter = (lon,lat) =>{
  map.setZoomAndCenter(14, [lon, lat]);
}


defineExpose({
  addMarker,
  createByMap,
  setZoomAndCenter
});
onUnmounted(() => {
  map?.destroy();
});
</script>

<template>
<div class="mapContentBox">
  <div  id="map"></div>
</div>
</template>

<style lang="less" scoped>
.mapContentBox{
    width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  .mapRightInfo{
    flex: 1;
    height: 100%;
    margin-left:10px;
  }
}
#map {
  width:100%;
  height: 100%;
}
</style>
