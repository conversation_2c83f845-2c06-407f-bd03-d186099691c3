<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import {
  DownOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
  PhoneFilled,
} from '@ant-design/icons-vue';
import { callHistoryApi,callCenterApi } from '@haierbusiness-front/apis';
import { IEnterpriseListRequest, IEnterprise } from '@haierbusiness-front/common-libs';
import { errorModal, routerParam } from '@haierbusiness-front/utils';
import dayjs, { Dayjs } from 'dayjs';
import { computed, onMounted, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useRouter } from 'vue-router';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables';
import WorkOrderList from '../components/dialog/workOrderList.vue';
import workOrderListLook from '../components/dialog/workOrderListLook.vue';
import duration from 'dayjs/plugin/duration';
import Axios from 'axios';
const account = import.meta.env.VITE_ACCOUNT;
const pwd = import.meta.env.VITE_PWD;

dayjs.extend(duration);
const router = useRouter();
const productList = ref<any>([]);
const columns: ColumnType[] = [
  {
    title: '电话',
    dataIndex: 'phone',
    align: 'left',
  },
  {
    title: '联系人',
    dataIndex: 'user',
    align: 'center',
  },
  {
    title: '通话开始时间',
    dataIndex: 'callbegin',
    align: 'center',
  },
  {
    title: '通话结束时间',
    dataIndex: 'callend',
    align: 'center',
  },
  {
    title: '呼叫类型',
    dataIndex: 'callType',
    align: 'center',
  },
  {
    title: '通话时长/秒',
    dataIndex: 'callTime',
    align: 'center',
  },
  {
    title: '响铃时长/秒',
    // dataIndex: 'answeringDuration',
    dataIndex: 'ackTime',
    align: 'center',
  },
  {
    title: '客服工号',
    dataIndex: 'personNum',
    align: 'center',
  },
  {
    title: '客服姓名',
    dataIndex: 'personName',
    align: 'center',
  },
  {
    title: '产品线',
    dataIndex: 'productName',
    align: 'center',
  },
  {
    title: '满意度',
    dataIndex: 'score',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '320px',
    fixed: 'right',
    align: 'center',
  },
];
const searchParam = ref<IEnterpriseListRequest>({
  Date:[dayjs().subtract(0, 'month').startOf('month').format("YYYY-MM-DD"), dayjs().subtract(0, 'month').endOf('month').format('YYYY-MM-DD')],
  startTime:dayjs().subtract(0, 'month').startOf('month').format("YYYY-MM-DD")+ ' 00:00:00',
  endTime:dayjs().subtract(0, 'month').endOf('month').format('YYYY-MM-DD') + ' 23:59:59'
});
const workOrderListShow = ref<boolean>(false);
const workOrderListLookShow = ref<boolean>(false);
const manageParams = ref();
const editDetail = ref<any>({});
const rowData = ref<any>({});
const callid = ref<any>();
const audioBox = ref<boolean>(false)
const audioSrc = ref<string>("")
// 明细弹窗
const detailVisible = ref<boolean>(false);

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(callHistoryApi.list, {
  ...searchParam,
  manual: true,
});

const reset = () => {
  searchParam.value = {};
  handleTableChange({ current: 1, pageSize: 10 })
};

const rangePresets = ref([
  { label: '最近一年', value: [dayjs().add(-365, 'd'), dayjs()] },
  { label: '最近半年', value: [dayjs().add(-180, 'd'), dayjs()] },
  { label: '最近30天', value: [dayjs().add(-30, 'd'), dayjs()] },
  { label: '最近7天', value: [dayjs().add(-7, 'd'), dayjs()] },
]);


const dataSource = computed(
  () =>
    data.value?.records?.filter((item: any) => {
      return item;
    }) || [],
);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

// 新增表单相关
const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } = useEditDialog<
  IEnterprise,
  IEnterprise
>(callHistoryApi, '通话记录', () =>
  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize,
  }),
);

// 点击关联工单
const relatedWorkOrders = (row: any) => {
  workOrderListShow.value = true;
  rowData.value = row;
};

// 关闭窗口
const workOrderListCancel = () => {
  workOrderListShow.value = false;
};

const workOrderListLookCancel = () => {
  workOrderListLookShow.value = false;
};

const lookWorkOrder = (row: any) => {
  callid.value = row.callid;
  workOrderListLookShow.value = true;
};

const EditDetailDialogCancel = () => {
  detailVisible.value = false;
};

// 获取通话时间
const getTime = (record: any) => {
  const seconds = dayjs(record.callend).diff(dayjs(record.callbegin), 'seconds');
  return seconds;
};

const getAnsweringDuration = (record: any) => {
  const seconds = dayjs(record.ackend).diff(dayjs(record.ackbegin), 'seconds');
  return seconds;
};

// 修改日期
const changeDate = (val: any) => {
  if (val) {
    searchParam.value.startTime = val[0] + ' 00:00:00';
    searchParam.value.endTime = val[1] + ' 23:59:59';
  } else {
    searchParam.value.startTime = null;
    searchParam.value.endTime = null;
  }
};

const getError = (error: any) => {
  if (!error.result) {
    message.error('暂未获取到录音');
  }
};
const downloading = ref<boolean>(false)
const tableId = ref<any>(null)
// 下载录音
const downLoadRecord = async (record: any,type:number) => {
  tableId.value = record.tableId
  downloading.value = true
  // // 获取token
  // const res = await callHistoryApi.login('/aiccrecord/api/login?account=' + account + '&password=' + pwd);
  // console.log(res);
  // // 获取通话记录
  try {
    const resp = await callHistoryApi.downLoadFile(
    {
      begin: record.callbegin.replaceAll('-', '').substring(0, 8) + '000000',
      end: record.callend.replaceAll('-', '').substring(0, 8) + '235959',
      callId: record.callid,
    }
  );
  if(resp.url){
      if(type==1){
        // 下载录音
        window.open(resp.url, '_self');
      }else{
        audioBox.value = true
        audioSrc.value = resp.url
      }
      downloading.value = false
  }
  } catch (error) {
    downloading.value = false
  }
};
const audioRef = ref()
const downloadLoading = ref<boolean>(false)
const download = () => {
  downloadLoading.value = true
  // 获取总条数
  callHistoryApi.list(
    {
    ...searchParam.value,
    pageNum:1,
    pageSize:1,
  }
  ).then(res=>{
    console.log(res.total)
    callHistoryApi.exportExcel({
      ...searchParam.value,
      pageSize:res.total
    }).then(()=>{
       downloadLoading.value = false
    }).catch(()=>{
      downloadLoading.value = false
    })
  })
  .catch(()=>{
      downloadLoading.value = false
    })
  // callHistoryApi.exportExcel(searchParam.value);
};

// 获取产品线的数据
const getProductList = () => {
  callCenterApi.getProductList().then((res: any) => {
    productList.value = res;
  });
};

const audioBoxClose = ()=>{
  audioRef.value.pause();
  audioRef.value.currentTime = 0;
}

// const rangePresets = ref([
//   { label: '最近一年', value: [dayjs().add(-365, 'd'), dayjs()] },
//   { label: '最近半年', value: [dayjs().add(-180, 'd'), dayjs()] },
//   { label: '最近30天', value: [dayjs().add(-30, 'd'), dayjs()] },
//   { label: '最近7天', value: [dayjs().add(-7, 'd'), dayjs()] },
// ]);

const getScore = (val: string | number) => {
  if (val == 0) {
    return '未评价';
  } else if (val == 1) {
    return '非常满意';
  } else if (val == 2) {
    return '满意';
  } else if (val == 3) {
    return '不满意';
  } else {
    return '未邀评';
  }
};

// 初始化
onMounted(async () => {
  getProductList();
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize:10,
  });
});
</script>

<template>
  <!-- <Eloading :loading="confirmLoading"></Eloading>
  <Eloading :loading="confirmAccountLoading"></Eloading>
  <Eloading :loading="revokeConfirmLoading"></Eloading>
  <Eloading :loading="cancelLoading"></Eloading> -->
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 30px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col style="text-align: right; padding-right: 10px; width: 80px">
            <label for="mobile">来电号码：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="mobile" v-model:value="searchParam.callerno" placeholder="" autocomplete="off" allow-clear />
          </h-col>
          <h-col style="text-align: right; padding-right: 10px; width: 100px">
            <label for="mobile">去电号码：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="mobile" v-model:value="searchParam.calleeno" placeholder="" autocomplete="off" allow-clear />
          </h-col>
          <h-col style="text-align: right; padding-right: 10px; width: 100px">
            <label for="customerNum">客服工号：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="customerNum"
              v-model:value="searchParam.customerNum"
              placeholder=""
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col style="text-align: right; padding-right: 10px; width: 100px">
            <label for="customerNum">通话日期：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker
              valueFormat="YYYY-MM-DD"
              :presets="rangePresets"
              @change="changeDate"
              v-model:value="searchParam.Date"
            />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col style="text-align: right; padding-right: 10px; width: 80px">
            <label for="customerNum">满意度：</label>
          </h-col>
          <h-col :span="4">
            <h-select allowClear v-model:value="searchParam.score" style="width: 100%">
              <h-select-option :value="1">非常满意</h-select-option>
              <h-select-option :value="2">满意</h-select-option>
              <h-select-option :value="3">不满意</h-select-option>
              <h-select-option :value="0">未评价</h-select-option>
              <h-select-option :value="9">未邀评</h-select-option>
            </h-select>
          </h-col>
          <h-col style="text-align: right;padding-right: 10px;  width: 100px">
            <label for="mobile">产品线：</label>
          </h-col>
          <h-col :span="4">
            <h-select allowClear v-model:value="searchParam.productId" style="width: 100%">
              <h-select-option v-for="item in productList" :value="item.id">{{ item.name }}</h-select-option>
            </h-select>
          </h-col>
          <h-col style="text-align: right;padding-right: 10px;  width: 100px">
            <label for="mobile">响铃时长：</label>
          </h-col>
          <h-col :span="4">
            <h-select allowClear v-model:value="searchParam.waitTime" style="width: 100%">
              <h-select-option  :value="10">大于十秒</h-select-option>
            </h-select>
          </h-col>
          <h-col style="text-align: right; padding-right: 10px; width: 100px">
            <label for="customerNum">是否接听：</label>
          </h-col>
          <h-col :span="4">
            <h-select allowClear v-model:value="searchParam.answerState" style="width: 100%">
              <h-select-option :value="true">已接</h-select-option>
              <h-select-option :value="false">未接</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col style="text-align: right; padding-right: 10px; width: 80px">
            <label for="customerNum">呼叫类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select allowClear v-model:value="searchParam.callType" style="width: 100%">
              <h-select-option :value="0">呼入</h-select-option>
              <h-select-option :value="7">呼出</h-select-option>
              <h-select-option :value="10">转接</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row>
          <h-col :span="24" style="text-align: right; margin-top: 20px">
            <h-button style="margin-right: 10px" :loading="downloadLoading" @click="download">导出</h-button>
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :ellipsis="true"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="dataSource"
          :pagination="pagination"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'phone'">
            <span  v-if="record.calltype != 7" class="icon" :style="{'color':record.callbegin==record.callend?'red':'blue','font-weight':900}"><i style="font-size: 18px;" class="iconfont icon-huru"></i></span>
            <span v-else class="icon" :style="{'color':record.callbegin==record.callend?'red':'blue','font-weight':900}"><i style="font-size: 18px;" class="iconfont icon-huchu"></i></span>
              {{ record.calltype == 7 ? record.calleeno : record.callerno }}
            </template>
            <template v-if="column.dataIndex === 'user'">
              {{ record.userVo?.username }}
            </template>
            <template v-if="column.dataIndex === 'personNum'">
              {{ record.agentCustomerVo.personNum }}
            </template>
            <template v-if="column.dataIndex === 'personName'">
              {{ record.agentCustomerVo.personName }}
            </template>
            <template v-if="column.dataIndex === 'productName'">
              {{ record.agentCustomerVo.productName }}
            </template>
            <template v-if="column.dataIndex === 'callType'">
              {{ record.calltype==0?'呼入':record.calltype==7?'呼出':'转接' }}
            </template>
            <template v-if="column.dataIndex === 'callTime'">
              {{ getTime(record) }}
            </template>
            <template v-if="column.dataIndex === 'answeringDuration'">
              {{ getAnsweringDuration(record) }}
            </template>
            <!-- <template v-if="column.dataIndex === 'calltype'">
              <a-tag color="#2db7f5" v-if="record.calltype == 7">呼出</a-tag>
              <a-tag color="#87d068" v-else>呼入</a-tag>
            </template> -->
            <!-- 滿意度 score -->
            <template v-if="column.dataIndex === 'score'">
              {{ record.callbegin==record.callend?'未邀评':getScore(record.score) }}
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" v-if="record.workIds" @click="lookWorkOrder(record)">查看工单</h-button>
              <h-button type="link" @click="relatedWorkOrders(record)">关联工单</h-button>
              <h-button v-if="record.callbegin!=record.callend" :loading="record.tableId==tableId&&downloading" type="link"  @click="downLoadRecord(record,2)"
                >播放录音
              </h-button>
              <!-- <h-button v-if="record.callbegin!=record.callend" type="link" @click="downLoadRecord(record,1)"
                >下载录音
              </h-button> -->
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    <!-- 播放录音弹窗 -->
    <h-modal :mask="false" :width="600" :footer="null"  v-model:open="audioBox" @cancel="audioBoxClose" :maskClosable="false" title="通话录音">
      <audio
      ref="audioRef" 
      :src="audioSrc" 
      autoplay
      style="width:100%;height:50px;margin:26px 0;"
      controls
      />
    </h-modal>
    <WorkOrderList
      v-if="workOrderListShow"  
      @cancel="workOrderListCancel"
      :callHistoryInfo="rowData"
      :show="workOrderListShow"
    />
    <workOrderListLook
      v-if="workOrderListLookShow"
      @cancel="workOrderListLookCancel"
      :callid="callid"
      :show="workOrderListLookShow"
    ></workOrderListLook>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
