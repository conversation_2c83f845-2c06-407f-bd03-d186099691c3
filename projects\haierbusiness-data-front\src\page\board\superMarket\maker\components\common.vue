<template>
  <h-col
    v-for="(item, i) in props.data"
    :span="item == 'BusinessTrend' ? 11 : item == 'SettleRank' || item == 'supplierRank' ? 5 : 6"
    :key="i"
  >
    <template v-if="item == 'BusinessTrend'">
      <div class="col-title col-title-l">
        {{ getName(item) }}
        <div class="col-title-extra">
          <span class="button" v-for="(tab, index) in tabs" :key="index" @click="active = index">
            {{ tab }}
          </span>
          <img
            :style="{
              transform: `translate(${active * 52}px)`,
            }"
            class="active"
            src="@/assets/image/bigscreen/icon-title-btn-acitve.png"
            alt=""
          />
        </div>
      </div>
      <div class="col-chart">
        <component :is="components.get(item)" :date-type="active"></component>
      </div>
    </template>

    <template v-else>
      <div class="col-title">{{ getName(item) }}</div>
      <div class="col-chart">
        <component v-if="item == 'Percentage'" :is="components.get(item)" :height="35"> </component>
        <component v-else :is="components.get(item)" :height="33"> </component>
      </div>
    </template>
  </h-col>
</template>
<script setup lang="ts">
import { ref, onMounted, markRaw, defineAsyncComponent } from 'vue';
import { Badge as hBadge, Progress as hProgress, Button as hButton, Col as hCol } from 'ant-design-vue';
const components = markRaw(new Map<string, any>());
//组件Accumulative
components.set(
  'Accumulative',
  defineAsyncComponent(() => import('./accumulative.vue')),
);
components.set(
  'OrderType',
  defineAsyncComponent(() => import('./orderType.vue')),
);
components.set(
  'Percentage',
  defineAsyncComponent(() => import('./Percentage.vue')),
);
components.set(
  'supplierRank',
  defineAsyncComponent(() => import('./supplierRank.vue')),
);

components.set(
  'PayType',
  defineAsyncComponent(() => import('./payType.vue')),
);

components.set(
  'SettleRank',
  defineAsyncComponent(() => import('./settleRank.vue')),
);
components.set(
  'BusinessTrend',
  defineAsyncComponent(() => import('./businessTrend.vue')),
);

const tabs = ['年', '月', '日'];
const active = ref(1);

const props = defineProps({
  data: Array<string>,
});

// 获取name
const getName = (status: number | string) => {
  const resultMap: any = {
    OrderType: '订单类型',

    Accumulative: '累计成交',

    supplierRank: '供应商排行Top10',
    SettleRank: '结算单位排行Top10',
    Percentage: '园区占比',

    PayType: '支付类型与平台',
    BusinessTrend: '业务销售趋势',

    default: '',
  };
  return resultMap[status] || resultMap.default;
};
</script>
<style scoped lang="less">
@import url(../../../main.less);

.mt-10 {
  margin-top: 10px;
}
</style>
