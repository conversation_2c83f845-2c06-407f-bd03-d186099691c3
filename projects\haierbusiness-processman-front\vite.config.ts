import { loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path';

const CWD = process.cwd();

export default ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);

  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
    },
    plugins: [vue()],
    build:{
      target:['es2015'],
      sourcemap: true
    },
    server: {
      port: 5183,
      proxy: {
        "/hb": {
          target: "https://businessmanagement-test.haier.net/hbweb/processman/hb",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb/, ""),
        },
      },
    }
  }
}
