import { download, get, post, originalPost } from '../request';
import {
  IDiscountFilter,
  IDiscount,
  IPageResponse,
  Result,
  IMerchantType,
  IProductFilter,
} from '@haierbusiness-front/common-libs';

export const discountApi = {
  page: (params: IDiscountFilter): Promise<IPageResponse<IDiscount>> => {
    return get('merchant/api/merchantDiscount/getDiscountPage', params);
  },

  list: (params: IDiscountFilter): Promise<Array<IDiscount>> => {
    return get('merchant/api/merchantDiscount/getList', params);
  },

  enable: (id: number, merCode: string, merType: string): Promise<Result> => {
    return originalPost('merchant/api/merchantDiscount/enable', {
      id,
      merCode,
      merType,
    });
  },

  cancelEnable: (id: number): Promise<Result> => {
    return originalPost('merchant/api/merchantDiscount/cancelEnable', {
      id,
    });
  },

  getTypeList: (params: IDiscountFilter): Promise<Array<IMerchantType>> => {
    return get('merchant/api/merchantDiscount/typeList', params);
  },

  get: (id: number): Promise<IDiscount> => {
    return get('merchant/api/merchantDiscount/getOne', {
      id,
    });
  },

  save: (params: IDiscount): Promise<Result> => {
    return post('merchant/api/merchantDiscount/insert', params);
  },

  edit: (params: IDiscount): Promise<Result> => {
    return post('merchant/api/merchantDiscount/update', params);
  },

  remove: (id: number): Promise<Result> => {
    return post('merchant/api/merchantDiscount/delete', { id });
  },

  searchProducts: (params: IProductFilter): Promise<any> => {
    return post('/businesstravel/api/jd/v1/api/product/search.json', params);
  },
};
