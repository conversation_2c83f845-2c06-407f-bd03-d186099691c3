// 响应接口
export interface GetHotelPageRes {
  /* */
  data: {
    /* */
    pageNum: number;

    /* */
    pageSize: number;

    /* */
    total: number;

    /* */
    totalPage: number;

    /* */
    records: {
      /*酒店code 唯一标识 */
      code: string;

      /*酒店名称 */
      name: string;

      /*酒店英文名称 */
      enName: string;

      /*酒店地址 */
      address: string;

      /*酒店英文地址 */
      enAddress: string;

      /*酒店电话 */
      phone: string;

      /*酒店主图 */
      image: string;

      /*酒店城市id */
      cityId: number;

      /*酒店城市名称 */
      cityName: string;

      /*酒店区域id */
      regionId: number;

      /*酒店区域名称 */
      regionName: string;

      /*酒店品牌id */
      brandId: number;

      /*酒店品牌名称 */
      brandName: string;

      /*酒店经度 */
      lon: string;

      /*酒店纬度 */
      lat: string;

      /*酒店星级 */
      starLevel: string;

      /*映射供应商酒店列表 */
      mappingHotelList: {
        /*供应商code 美团：MT */
        providerCode: string;

        /*供应商名称 */
        providerCodeName: string;

        /*供应商酒店名称 */
        name: string;

        /*供应商酒店code */
        providerHotelCode: string;

        /*供应商酒店地址 */
        address: string;

        /*供应商酒店城市code */
        cityCode: string;

        /*供应商酒店城市名称 */
        cityName: string;

        /*供应商酒店区域code */
        regionCode: string;

        /*供应商酒店区域名称 */
        regionName: string;

        /*供应商酒店品牌code */
        brandCode: string;

        /*供应商酒店品牌名称 */
        brandName: string;

        /*供应商酒店集团code */
        groupCode: string;

        /*供应商酒店集团名称 */
        groupName: string;

        /*供应商酒店主图 */
        image: string;

        /*供应商酒店电话 */
        phone: string;

        /*供应商酒店星级 */
        starLevel: string;

        /*百度经度 */
        bdLon: Record<string, unknown>;

        /*百度维度 */
        bdLat: Record<string, unknown>;

        /*高德经度 */
        gdLon: Record<string, unknown>;

        /*高德维度 */
        gdLat: Record<string, unknown>;

        /*映射城市id */
        mappingCityId: number;

        /*映射城市名称 */
        mappingCityName: string;

        /*映射区域id */
        mappingRegionId: number;

        /*映射区域名称 */
        mappingRegionName: string;

        /*映射品牌集团id */
        mappingGroupId: number;

        /*映射品牌集团名称 */
        mappingGroupName: string;

        /*映射品牌id */
        mappingBrandId: number;

        /*映射品牌名称 */
        mappingBrandName: string;

        /* */
        glon: Record<string, unknown>;

        /* */
        glat: Record<string, unknown>;
      }[];
    }[];
  };

  /* */
  code: string;

  /* */
  message: string;

  /* */
  msg: string;

  /* */
  success: boolean;
}


export interface GetMappingHotelListRes {
  /*供应商code 美团：MT */
  providerCode: string;

  /*供应商名称 */
  providerCodeName: string;

  /*供应商酒店名称 */
  name: string;

  /*供应商酒店code */
  providerHotelCode: string;

  /*供应商酒店地址 */
  address: string;

  /*供应商酒店城市code */
  cityCode: string;

  /*供应商酒店城市名称 */
  cityName: string;

  /*供应商酒店区域code */
  regionCode: string;

  /*供应商酒店区域名称 */
  regionName: string;

  /*供应商酒店品牌code */
  brandCode: string;

  /*供应商酒店品牌名称 */
  brandName: string;

  /*供应商酒店集团code */
  groupCode: string;

  /*供应商酒店集团名称 */
  groupName: string;

  /*供应商酒店主图 */
  image: string;

  /*供应商酒店电话 */
  phone: string;

  /*供应商酒店星级 */
  starLevel: string;

  /*百度经度 */
  bdLon: Record<string, unknown>;

  /*百度维度 */
  bdLat: Record<string, unknown>;

  /*高德经度 */
  gdLon: Record<string, unknown>;

  /*高德维度 */
  gdLat: Record<string, unknown>;

  /*映射城市id */
  mappingCityId: number;

  /*映射城市名称 */
  mappingCityName: string;

  /*映射区域id */
  mappingRegionId: number;

  /*映射区域名称 */
  mappingRegionName: string;

  /*映射品牌集团id */
  mappingGroupId: number;

  /*映射品牌集团名称 */
  mappingGroupName: string;

  /*映射品牌id */
  mappingBrandId: number;

  /*映射品牌名称 */
  mappingBrandName: string;

  /* */
  glon: Record<string, unknown>;

  /* */
  glat: Record<string, unknown>;
}

export interface HotelSyncRecordRes {
  /*唯一id */
  id: number;

  /*供应商code */
  providerCode: string;

  /*供应商编码名称 */
  providerCodeName: string;

  /*类型 1: 新增  2:酒店信息修改  3:酒店信息删除 */
  type: number;

  /*供应商酒店code */
  providerHotelCode: string;

  /*供应商酒店名称 */
  name: string;

  /*酒店电话 */
  phone: string;

  /*酒店地址 */
  address: string;

  /*供应商酒店原始城市code */
  cityCode: string;

  /*供应商酒店原始城市名称 */
  cityName: string;

  /*供应商酒店原始区域code */
  regionCode: string;

  /*供应商酒店原始区域name */
  regionName: string;

  /*供应商酒店映射城市id */
  mappingCityId: number;

  /*供应商酒店映射城市名称 */
  mappingCityName: string;

  /*供应商酒店映射区域id */
  mappingRegionId: number;

  /*供应商酒店映射区域名称 */
  mappingRegionName: string;

  /*同步id */
  hotelSyncId: number;

}


export interface GetHotelSyncRes {
    /*开始时间 */
    startTime: Record<string, unknown>;

    /*结束时间 */
    endTime: Record<string, unknown>;

    /*mapping聚合数量游标 */
    mappingCursor: number;

    /*同步酒店数量游标 */
    syncCursor: number;

    /*同步酒店总数量 */
    syncTotal: number;

    /*供应商code */
    providerCode: string;

    /*供应商providerCodeName */
    providerCodeName: string;

    /*同步状态 */
    syncStatus: number;

    /*聚合状态 */
    mappingStatus: number;
  }

  