
import { createApp } from 'vue'
import router from './router';
import './assets/css/main.less'
import App from './App.vue'
import globalPinia from "@haierbusiness-front/utils/src/store/store"
import { setGlobalOptions } from 'vue-request';
import Bwdv from 'ant-design-vue'
import 'element-plus/dist/index.css'
import 'ant-design-vue/dist/reset.css';
//在你的全局配置里引入中文文件
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import vue3Seamless from "vue3-seamless-scroll";
import VueGridLayout from "vue-grid-layout"
setGlobalOptions({
    manual: true,
    pagination: {
        currentKey: 'pageNum',
        pageSizeKey: 'pageSize',
    },
});
const app = createApp(App)
app.use(Bwdv)
app.use(VueGridLayout)
app.use(vue3Seamless);
router.then(it => {
        app.use(globalPinia as any)
        .use(it  as any)
        .use(ElementPlus, {
            locale: zhCn,
        })
        .mount('#app');

    app.config.globalProperties.$hbRouter = it
}
)



