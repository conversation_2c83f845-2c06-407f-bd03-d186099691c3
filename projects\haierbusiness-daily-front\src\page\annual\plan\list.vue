<script setup lang="ts">
import {
  Modal,
  TabPane as hTabPane,
  Tabs as hTabs,
  Tag as hTag,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  FloatButton as hFloatButton,
  Row as hRow,
  Table as hTable,
} from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import {
  AnnualPlanTypeStateConstant,
  BalanceStatusConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
  UserGroupSystemConstant,
} from '@haierbusiness-front/common-libs';
import { checkUserGroups, getCurrentRouter } from '@haierbusiness-front/utils';
import { ref, computed, watch, provide, inject } from 'vue';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import { IAnnualPlanTypeListRequest, IDailyPersonalResponse } from '@haierbusiness-front/common-libs/src/daily';
import planCard from './planCard.vue';
import addPlanCard from './addPlanCard.vue';
import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
import { getDailyPersonUser } from '../../../utils/dailyPersonUtil';

const router = getCurrentRouter();
const { loginUser } = storeToRefs(applicationStore(globalPinia));
const prop = defineProps({
  query: Object,
});

watch(
  [() => prop.query],
  () => {
    if (prop.query) {
      listApiRun();
    }
  },
  { deep: true },
);
const listType = ref(parseInt(prop.query?.listType) || 1);
watch(
  [() => prop.query?.listType],
  () => {
    listType.value = parseInt(prop.query?.listType);
  },
  { deep: true },
);

const searchListParam = ref<IAnnualPlanListRequestDTO>({});
const listLoading = ref(false);
const listData = ref<IAnnualPlanListResponseDTO[]>([]);
const listDataComputed = computed(() => {
  if (listType.value === 1) {
    return listData.value;
  } else {
    return listData.value.filter((it) => {
      return it.year === changeYear.value;
    });
  }
});
const listApiRun = () => {
  listLoading.value = true;
  dailyAnnualPlanApi
    .list({
      ...searchListParam.value,
    })
    .then((it) => {
      listData.value = it;
    })
    .finally(() => {
      listLoading.value = false;
    });
};

const dailyPersonUser = ref<IDailyPersonalResponse>();
const refreshDailyPersonUser = () => {
  return getDailyPersonUser(loginUser?.value?.username!!).then(
    (it) => (
      (dailyPersonUser.value = it),
      (searchListParam.value.deptCode = listType.value === 1 ? dailyPersonUser?.value?.deptCode : undefined),
      (searchListParam.value.deptName = listType.value === 1 ? dailyPersonUser?.value?.deptName : undefined)
    ),
  );
};
{
  refreshDailyPersonUser().then(() => {
    listApiRun();
  });
}
const allYears = computed(() => {
  return new Set(
    listData.value.map((it) => {
      return it.year;
    }),
  );
});

const changeYear = ref(dayjs().year());
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <h-row :align="'middle'">
      <h-col :span="24" style="text-align: center; margin-bottom: 8px; font-size: 16px">
        <div v-if="listType === 1" style="font-weight: 700">{{ loginUser?.departmentName }}</div>
        <h-tabs v-if="listType === 2" v-model:activeKey="changeYear" type="card">
          <h-tab-pane v-for="year in allYears" :key="year">
            <template #tab>
              <div style="width: 188px">{{ year }}</div>
            </template>
          </h-tab-pane>
        </h-tabs>
      </h-col>
    </h-row>
    <h-row :align="'middle'">
      <h-col :span="6" v-for="(domain, index) in listDataComputed">
        <plan-card :data="domain" :type="listType"></plan-card>
      </h-col>
      <h-col :span="6">
        <add-plan-card v-if="listType === 1" :exist-years="allYears"></add-plan-card>
      </h-col>
    </h-row>
    <h-row :align="'middle'" v-if="listType === 2 && (!listDataComputed || listDataComputed.length < 1)">
      <div
        style="
          display: flex;
          align-items: center;
          justify-content: center;
          height: 400px;
          width: 100%;
          color: rgb(163, 163, 163);
          font-size: 18px;
          font-weight: 500;
        "
      >
        当前年度计划不存在
      </div>
    </h-row>
  </div>
</template>

<style scoped lang="less"></style>
