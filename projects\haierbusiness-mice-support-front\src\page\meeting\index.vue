<script lang="ts" setup>
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Upload as hUpload,
  message,
  Modal,
} from 'ant-design-vue';
import { computed, h, onMounted, reactive, ref, RendererElement, RendererNode, VNode } from 'vue';
import type { SizeType } from 'ant-design-vue/es/config-provider';
import { useRoute, useRouter } from 'vue-router';
import { meetingAttendeeApi } from '@haierbusiness-front/apis';
import { IMeetingDetails } from '@haierbusiness-front/common-libs';
const currentRouter = ref();
const route = useRoute();
const router = useRouter();
const size = ref<SizeType>('large');
const meetingDetails = ref<IMeetingDetails>({});

const meetingDetail = async () => {
  const response = await meetingAttendeeApi.details(1);
  if (response) {
    meetingDetails.value = response;
  }
  console.log(response, 'meetingDetails.value');
};

onMounted(() => {
  console.log(router, 'router');
  meetingDetail();
});

const buttonMore = [
  {
    title: '编辑',
    key: 1,
  },
  {
    title: '参会人管理',
    key: 2,
  },
  {
    title: '议程管理',
    key: 3,
  },
  {
    title: '交互设置',
    key: 4,
  },
  {
    title: '现场联系人员',
    key: 5,
  },
  {
    title: '111111',
    key: 6,
  },
];

const handleClick = (res: number) => {
  switch (res) {
    case 1:
      router.push({
        path: '/support/meeting/edit',
        query: {
          id: meetingDetails?.value.id,
        },
      });
      break;
    case 2:
      router.push({
        path: '/support/attendeeMeeting/index',
        query: {
          id: meetingDetails?.value.id,
        },
      });
      break;
    case 3:
      router.push({
        path: '/support/meeting/agenda',
        query: {
          id: meetingDetails?.value.id,
        },
      });
      break;
    case 4:
      router.push({
        path: '/support/meeting/guest',
        query: {
          id: meetingDetails?.value.id,
        },
      });
      break;
    case 5:
      // 代码块2
      break;
    case 6:
      router.push({
        path: '/support/signIn/index',
        query: {
          miceInfoId: meetingDetails?.value.id,
          miceInfoName: meetingDetails?.value.miceName,
        },
      });
      break;
  }
};

const formatDateRange = (dateStr: string): string => {
  try {
    const [start, end] = dateStr.split('~').map((s) => s.trim());

    // 增强的正则表达式，匹配带或不带时间的日期格式
    const dateRegex = /(\d{4})[年/-](\d{1,2})[月/-](\d{1,2})(?:日|\s|$)/;
    const startParts = start.match(dateRegex);
    const endParts = end.match(dateRegex);

    if (!startParts || !endParts) return dateStr;

    // 如果年份相同
    if (startParts[1] === endParts[1]) {
      // 处理三种可能的日期分隔符：年、/、-
      const separators = ['年', '/', '-'];
      let separatorUsed = '年';
      for (const sep of separators) {
        if (end.includes(sep)) {
          separatorUsed = sep;
          break;
        }
      }

      // 保留原始字符串中的时间部分（如果有）
      return `${start} ~ ${end.replace(new RegExp(`${endParts[1]}\\${separatorUsed}`), '')}`;
    }
    return `${start} ~ ${end}`;
  } catch {
    return dateStr;
  }
};

//会议来源
const meetingScoure = (res: number) => {
  let ScoureName = '';
  if (res) {
    ScoureName = res == 1 ? '会务导入' : '会中创建';
  }

  return ScoureName;
};
</script>
<template>
  <div class="container">
    <div class="content">
      <div class="content-top">会议首页/会议详情</div>
      <div class="main">
        <div class="main-top">
          <div class="top-left">
            <h3>{{ meetingDetails?.miceName }}</h3>
          </div>
          <div class="top-right">
            <h-button style="margin-right: 10px">取消会议</h-button>
            <h-button type="primary">完成会议</h-button>
          </div>
        </div>
        <div class="main-middle">
          <h-row :gutter="[16, 16]" class="row-top">
            <h-col :span="4" style="text-align: right">会议名称：</h-col>
            <h-col :span="7">{{ meetingDetails?.miceName }}</h-col>
            <h-col :span="4" style="text-align: right">会议来源：</h-col>
            <h-col :span="7">{{ meetingScoure(meetingDetails?.miceSource) }}</h-col>
          </h-row>
          <h-row :gutter="[16, 16]" class="row-top">
            <h-col :span="4" style="text-align: right">会议时间：</h-col>
            <h-col :span="7">
              <div>{{ formatDateRange(meetingDetails?.miceStartDate + '~' + meetingDetails?.miceEndDate) }}</div>
            </h-col>
            <h-col :span="4" style="text-align: right">关联会议：</h-col>
            <h-col :span="7">{{ meetingDetails?.miceConnectMeetingId }}</h-col>
          </h-row>
          <h-row :gutter="[16, 16]" class="row-top">
            <h-col :span="4" style="text-align: right">会议酒店：</h-col>
            <h-col :span="20">{{ meetingDetails?.miceHotelName }}</h-col>
          </h-row>
          <h-row :gutter="[16, 16]" class="row-top">
            <h-col :span="4" style="text-align: right">经办人：</h-col>
            <h-col :span="7">{{ meetingDetails?.operatorName }}({{ meetingDetails?.operatorCode }})</h-col>
            <h-col :span="4" style="text-align: right">会议顾问：</h-col>
            <h-col :span="7">{{ meetingDetails?.consultantUsername }}({{ meetingDetails?.consultantUserCode }})</h-col>
          </h-row>
          <!-- <h-row :gutter="[16, 16]" class="row-top">
            <h-col :span="4" style="text-align: right;">会议二维码：</h-col>
            <h-col :span="7" style="color: blue;">点击查看</h-col>
          </h-row> -->
        </div>
        <div class="main-bottom">
          <h-button :size="size" @click="handleClick(item.key)" v-for="item in buttonMore" :key="item.key">{{
            item.title
          }}</h-button>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
* {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
}

ul,
p {
  margin-bottom: 0;
}

ul li {
  list-style: none;
}

.container {
  width: 100%;
  background: #f6f7f9;
}

.content {
  width: 1280px;
  height: auto;
  margin: 0 auto;

  .main {
    width: 100%;
    height: 800px;
    background-color: #fff;

    .main-top {
      width: 100%;
      border: 1px solid #e5e6eb;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 18px 24px;
    }

    .row-top {
      margin-top: 40px;
    }
    .main-bottom {
      display: flex;
      margin-top: 50px;
      justify-content: center;
      align-items: center;
      grid-gap: 15px;
    }
  }
}

.content-top {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #86909c;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  padding: 16px 0px;
}

.ant-btn {
  border-radius: 4px;
}
.top-left {
  h3 {
    height: 100%;
    line-height: 40px;
    margin-bottom: 0;
  }
}
</style>
