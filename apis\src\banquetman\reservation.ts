import { downloadPost, get, post } from '../request'
import { 
    IReservationReq, 
    IReservationRes,
    IPageResponse, 
    Result 
} from '@haierbusiness-front/common-libs'


export const banquetReservationApi = {
    list: (params: IReservationReq): Promise<IPageResponse<IReservationRes>> => {
        return post('banquet/api/banquetBooking/page', params)
    },

    get: (id: number): Promise<IReservationRes> => {
        return get('banquet/api/banquetBooking/info', {
            id
        })
    },
    exportList: (params: IReservationReq): Promise<IPageResponse<IReservationRes>> => {
        return downloadPost('banquet/api/banquetBooking/exportBookingList', params)
    },
    exportReportList: (params: IReservationReq): Promise<IPageResponse<IReservationRes>> => {
        return downloadPost('banquet/api/admin/report/bookingExport', params)
    },
    /**
     * 查询审批详情
     */
    details: (params: any): Promise<any> => {
        return get('process/api/banquetBooking/info', params)
    },
    // 预订单报表
    listRepot: (params: IReservationReq): Promise<IPageResponse<IReservationRes>> => {
        return post('banquet/api/admin/report/bookingPage', params)
    },
}
