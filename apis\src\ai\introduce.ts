import { get, post } from '../request'
import {
    IAiIntroduceListRequest,
    AiIntroduceDO,
    AiIntroduceResult
} from '@haierbusiness-front/common-libs/src/ai/model/agentsModel'

export const introduceApi = {
    // 查询所有AI功能介绍
    list: (param: IAiIntroduceListRequest): Promise<AiIntroduceResult[]> => {
        return get('/ai/api/ai/introduce/list', param)
    },

    // 根据ID查询AI功能介绍
    getById: (id: number): Promise<AiIntroduceDO> => {
        return get('/ai/api/ai/introduce/get', { id })
    },

    // 新增AI功能介绍
    save: (data: AiIntroduceDO): Promise<boolean> => {
        return post('/ai/api/ai/introduce/save', data)
    },

    // 更新AI功能介绍
    updateById: (data: AiIntroduceDO): Promise<boolean> => {
        return post('/ai/api/ai/introduce/update', data)
    },

    // 删除AI功能介绍
    removeById: (id: number): Promise<boolean> => {
        return post('/ai/api/ai/introduce/delete', { id })
    }
}
