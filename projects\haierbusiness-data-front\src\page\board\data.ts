export const barline = {
    backgroundColor: "transparent",
    tooltip: {
        trigger: "axis",
        axisPointer: {
            type: "cross",
            crossStyle: {
                color: "#999",
            },
        },
    },
    legend: {
        data: ["订单数", "间夜数"],
    },
    xAxis: [
        {
            type: "category",
            data: ["<PERSON>", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "Fri", "Sat", "Sun"],
        },
    ],
    yAxis: [
        {
            type: "value",
            name: "订单数",
        },
        {
            type: "value",
            name: "间夜数",
        },
    ],
    series: [
        {
            name: "订单数",
            type: "bar",
            color: "rgba(0,240,255,0.4)",
            itemStyle: {
                borderColor: "#00F0FF",
            },
            data: [
                2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4,
                3.3,
            ],
        },
        {
            name: "间夜数",
            type: "line",
            yAxisIndex: 1,
            color: "#FFD700",
            smooth: true,
            symbol: "none",
            data: [
                2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0,
                6.2,
            ],
        },
    ],
};
export const barStack = {
    backgroundColor: "transparent",
    tooltip: {
        trigger: "axis",
        axisPointer: {
            type: "cross",
            crossStyle: {
                color: "#999",
            },
        },
    },
    legend: {
        data: [],
    },
    xAxis: [
        {
            type: "category",
            data: [],
        },
    ],
    yAxis: [
        {
            type: "value",
            name: "单",
        },
    ],
    series: [],
};

export const pie = {
    backgroundColor: "transparent",
    tooltip: {
        trigger: "item",
    },
    series: [
        {
            label: {
                show: false,
            },
            type: "pie",
            radius: 50,
        },
    ],
};
export const circle = {
    backgroundColor: "transparent",
    title: {
        text: "对公",
        left: "center",
        top: "center",
        textStyle: {
            fontSize: 12,
            color: "#E2F5FE",
        },
    },
    tooltip: {
        trigger: "item",
    },
    series: [
        {
            label: {
                show: false,
            },
            selectedMode: "single", //鼠标点击是否突出该区域
            type: "pie",
            radius: [35, 50],
            // radius: ["60%", "90%"],
            data: [],
        },
    ],
};
export const circle2 = {
    backgroundColor: "transparent",
    tooltip: {
        trigger: "item",
        // formatter:"{b}：{c}"
    },
    legend: {
        bottom: "5%",
        left: "center",
        icon: "circle",
    },
    label: {
        formatter: "{d}%",
    },
    series: [
        {
            type: "pie",
            selectedMode: "single", //鼠标点击是否突出该区域
            radius: ["30%", "50%"],
            center: ["45%", "35%"],
            // data: [
            //     { value: 1048, name: '100-300元' },
            //     { value: 735, name: '300-600元' },
            //     { value: 580, name: '600-1000元' },
            //     { value: 300, name: '≥1000元' }
            // ]
        },
    ],
};
export const colors = ["#00E9FF", "#FBBA24", "#FF8918", "#1BC78B", "#22A4F8", "#ff6600", "#2e4e7e", "#665757", "#c0ebd7", "#a1afc9"];
export const colorsforSmart = ["#5670c1", "#0073E5", "#73c0de", "#a3d18d", "#f1c96d", "#ed7777", "#50b184", "#c17edf", "#ed9069", "#a1afc9"];
export const chinaMapOptions = {
    backgroundColor: "transparent",
    geo: {
        name: "地图",
        map: "china", //地图类型。
        zlevel: 0,
        roam: true,
        show: true,
        layoutCenter: ["50%", "50%"],
        layoutSize: "100%",
        itemStyle: {
            //地图区域的多边形 图形样式。
            emphasis: {
                //高亮状态下的样试
                label: {
                    show: true,
                },
                areaColor: "#FFB800",
            },
            normal: {
                areaColor: "rgba(239,243,255,0.05)",
                borderWidth: 1, //设置外层边框
                borderColor: "rgb(93,112,146)",
            },
        },
    },
};

export const worldMapOptions = {
    backgroundColor: "transparent",
    geo: {
        name: "地图",
        map: "WORLD", //地图类型。
        zlevel: 0,
        roam: true,
        show: true,
        layoutCenter: ["50%", "50%"],
        layoutSize: "120%",
        itemStyle: {
            //地图区域的多边形 图形样式。
            emphasis: {
                //高亮状态下的样试
                label: {
                    show: true,
                },
                areaColor: "#FFB800",
            },
            normal: {
                areaColor: "rgba(239,243,255,0.05)",
                borderWidth: 1, //设置外层边框
                borderColor: "rgb(93,112,146)",
            },
        },
    },
};
export const planePath =
    "path://M1705.06,1318.313v-89.254l-319.9-221.799l0.073-208.063c0.521-84.662-26.629-121.796-63.961-121.491c-37.332-0.305-64.482,36.829-63.961,121.491l0.073,208.063l-319.9,221.799v89.254l330.343-157.288l12.238,241.308l-134.449,92.931l0.531,42.034l175.125-42.917l175.125,42.917l0.531-42.034l-134.449-92.931l12.238-241.308L1705.06,1318.313z";
export const trainPath =
    "path://M875.737452 928.123552l-127.505792 0-26.687259-60.293436-456.648649 0-26.687259 60.293436-127.505792 0 130.471042-188.787645q-38.548263-10.872587-63.258687-43.490347t-24.710425-75.119691l0-432.926641q0-25.698842 13.343629-48.926641t32.617761-42.501931 39.53668-33.111969 35.088803-20.756757 32.617761-14.826255 42.501931-13.837838 57.328185-9.88417 76.108108-3.953668q49.420849 0 86.980695 4.447876t65.72973 11.861004 49.420849 16.803089 39.042471 19.274131 37.559846 22.733591 35.583012 29.158301 26.19305 37.065637 10.378378 46.455598l0 432.926641q0 42.501931-25.204633 75.119691t-63.752896 43.490347zM400.30888 64.247104q-12.849421 0-21.745174 8.895753t-8.895753 22.733591q0 12.849421 8.895753 21.745174t21.745174 8.895753l187.799228 0q12.849421 0 21.745174-8.895753t8.895753-21.745174q0-13.837838-8.895753-22.733591t-21.745174-8.895753l-187.799228 0zM214.486486 373.621622l556.478764 0 0-185.822394-556.478764 0 0 185.822394zM246.11583 694.857143q17.791506 0 30.640927-12.849421t12.849421-30.640927q0-18.779923-12.849421-31.629344t-30.640927-12.849421q-18.779923 0-31.629344 12.849421t-12.849421 31.629344q0 17.791506 12.849421 30.640927t31.629344 12.849421zM692.880309 806.548263l-27.675676-62.27027-343.969112 0-28.664093 62.27027 400.30888 0zM695.84556 651.366795q0 17.791506 12.849421 30.640927t31.629344 12.849421 31.629344-12.849421 12.849421-30.640927q0-18.779923-12.849421-31.629344t-31.629344-12.849421-31.629344 12.849421-12.849421 31.629344z";
