{"name": "vue-count-to", "description": "It's a vue component that will count to a target number at a specified duration", "version": "1.0.13", "author": "Pan <<EMAIL>>", "main": "src/index.js", "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server --open --hot --content-base='./demo/'", "build": "cross-env NODE_ENV=production webpack --progress --hide-modules"}, "license": "MIT", "homepage": "http://panjiachen.github.io/countTo/demo/", "devDependencies": {"babel-core": "^6.0.0", "babel-loader": "^6.0.0", "babel-plugin-transform-runtime": "^6.15.0", "babel-preset-es2015": "^6.14.0", "babel-preset-stage-2": "^6.13.0", "babel-runtime": "^6.11.6", "cross-env": "^3.0.0", "css-loader": "^0.25.0", "file-loader": "^0.9.0", "vue-loader": "^11.1.4", "vue-template-compiler": "^2.2.1", "webpack": "^2.2.0", "webpack-dev-server": "^2.2.0", "babel-eslint": "7.1.1", "eslint": "3.14.1", "eslint-friendly-formatter": "2.0.7", "eslint-loader": "1.6.1", "eslint-plugin-html": "2.0.0", "eslint-config-airbnb-base": "11.0.1", "eslint-import-resolver-webpack": "0.8.1", "eslint-plugin-import": "2.2.0"}}