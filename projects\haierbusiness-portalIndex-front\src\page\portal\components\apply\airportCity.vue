<script setup lang="ts">
import { onMounted, ref, watch,onUnmounted, computed } from 'vue';
import type { CascaderProps } from 'ant-design-vue';
import { Cascader } from 'ant-design-vue';
import cityChose from '@haierbusiness-front/components/airportChose/index.vue';
import { CityResponse, CityItem, TCteateTeam, TTicket } from '@haierbusiness-front/common-libs';

interface Props {
    travelType?: number
    leftNum?: string
    teamForm?: TCteateTeam
    showInternational?: boolean
    onlyInternational?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    travelType: 1,
    showInternational: true,
    onlyInternational: false
});

const emit = defineEmits(['chosedBeginCity', 'chosedEndCity'])


// 单程，返程
const travelType = ref(props.travelType)
const leftNum = ref(props.leftNum)
const showInternational = ref(props.showInternational)

// 是否只显示国际
const onlyInternational = ref(props.onlyInternational)
const teamForm = ref(props.teamForm)

watch(props, (newValue) => {
    travelType.value = newValue.travelType
    leftNum.value = newValue.leftNum ?? '1'
    teamForm.value = props.teamForm
    showInternational.value = props.showInternational
    onlyInternational.value = props.onlyInternational

})

// 获取申请单中的所有城市
const cityList = computed(() => {
    let list:any = []
    props?.teamForm?.tripList?.forEach((item:any) => {
        list.push({
            cityName: item.beginCityName,
            cityId: item.beginCityCode
        })
        list.push({
            cityName: item.endCityName,
            cityId: item.endCityCode
        })
    })
    
    const uniqueCities = list.reduce((acc: any[], current: any) => {
    const isExist = acc.some(item => item.cityId === current.cityId);
    if (!isExist) {
        acc.push(current);
    }
    return acc;
    }, []);
    return uniqueCities
})

const value = ref<string[]>([]);
const value1 = ref<string[]>([]);

const chosedBeginCity = (city: CityItem) => {
    emit('chosedBeginCity', city)
};
const chosedEndCity = (city: CityItem) => {
    emit('chosedEndCity', city)
};
const eliminateSearchProvinceIds = [7,16,17]

const exchangePositon = () => {
    const temp = teamForm.value.beginCityName
    const temp1 = teamForm.value.beginCityHid
    const temp2 = teamForm.value.beginCityCode

    teamForm.value.beginCityHid = teamForm.value.endCityHid
    teamForm.value.beginCityName = teamForm.value.endCityNameFirst
    teamForm.value.beginCityCode = teamForm.value.endCityCodeFirst

    // teamForm.value.endCityName = temp
    teamForm.value.endCityNameFirst = temp
    teamForm.value.endCityCodeFirst = temp2
    teamForm.value.endCityHid = temp1

}

</script>


<template>
    <div class="apply-city-component" :class="{ 'international-left-width': travelType === 3 }">
        <div v-if="travelType === 3" class="left">{{ leftNum }}</div>
        <div class="ticket-item-city" >
            <div class="ticket-item-ai" :class="{ 'left-radius': travelType === 3 }">
                <div class="item-labels">出发城市</div>
                
                <city-chose
                  class="mr-10 city-box-index"
                  placeholder="出发城市"
                  width="100%"
                  :default-value="teamForm?.applyNo ? teamForm?.beginCityCode : ''"
                  :cityList="cityList"
                  :value="teamForm?.beginCityName"
                  :showInternational="showInternational"
                  :onlyInternational="onlyInternational"
                  :bordered="false"
                  :eliminateSearchProvinceIds="eliminateSearchProvinceIds"
                  @chosedCity="chosedBeginCity"
                ></city-chose>
            </div>
            <img src="../../../../assets/image/banner/turn.png" @click="exchangePositon" alt="" >
            <div class="ticket-item-ai">
                <div class="item-labels">到达城市</div>
                <city-chose
                  class="mr-10 city-box-index"
                  width="100%"
                  placeholder="到达城市"
                  :default-value="teamForm?.applyNo ? teamForm?.endCityCodeFirst : ''"
                  :cityList="cityList"
                  :value="teamForm?.endCityNameFirst"
                  :showInternational="showInternational"
                  :onlyInternational="onlyInternational"
                  :bordered="false"
                  :eliminateSearchProvinceIds="eliminateSearchProvinceIds"
                  @chosedCity="chosedEndCity"
                ></city-chose>
            </div>
        </div>
    </div>
    
</template>

<style lang="less" scoped>
@import url('./common.less');

.apply-city-component {
    display: flex;
    flex-direction: row;
    .left {
    width: 18px;
    height: 54px;
    border-radius: 8px 0px 0px 8px;
    background: #3983E5;
    display: flex;
    font-size: 14px;
    color: #FFFFFF;
    line-height: 20px;
    justify-content: center;
    align-items: center;
  }
}

</style>

<style>
.apply-city-component .ant-select-selector {
  border:none !important;
  box-shadow: none !important;
  height: 22px !important;
  padding: 0px !important;
  
}

.apply-city-component .ant-select-selection-placeholder {
    font-size: 16px !important;
    color: rgba(0,0,0,0.35) !important;
    
    padding-inline-end: 25px !important;
    display: flex;
    align-items: center;
}

.apply-city-component .ant-select-selection-item {
    display: flex;
    align-items: center;
    font-size: 16px !important;
    
    color: rgba(0,0,0,0.85);
}

.apply-city-component .ant-select-item-option-content {
  
}

.apply-city-component .ant-cascader {
    width: 100%;
}

.ant-cascader-menus {
    
}
.city-box-index  {
        border: none;
        height: 22px;
        line-height: 22px;
        padding: 0;
}
</style>
