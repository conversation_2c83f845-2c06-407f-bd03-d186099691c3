<script setup lang="ts">
import { Carousel as hCarousel, message } from 'ant-design-vue';
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
import { ref, computed, onMounted } from 'vue';
import { useRequest } from 'vue-request';
import { bannerListApi } from '@haierbusiness-front/apis';
import router from '../../router'

const currentRouter = ref()
onMounted(async () => {
    currentRouter.value = await router
})

// 活动
const {
  data: bannerData,
  run: bannerListApiRun,
  loading: bannerLoading,
} = useRequest(bannerListApi.banners, {
  defaultParams: [
    {
      showStatus: 1
    }
  ],
  manual: false
});

const bannerDataSource = computed(() => bannerData.value || []);

// hover效果
const isActivityHover = ref(false)

const setIsActivityHover = (value: boolean) => {
  isActivityHover.value = value
}

const gotoUrlOrDetail = (data: {jumpLinkPc?: string, id?: number}, url: string) => {
  if (data.jumpLinkPc) {
    window.open(data.jumpLinkPc)
  } else {
    if(!data.id) {
      message.error('未对应信息！')
      return
    }
    const thisUrl = currentRouter.value.resolve({
      path: url,
    })
    window.open(thisUrl.href + '?id=' + data.id)
  }
}

const openLink = (url: string) => {
    window.open(url)
}

</script>

<template>
    <div class="activity">
        <div class="card-header">
            <div class="card-title">
            活动专区
            </div>
            <div class="card-more" @click="openLink('https://ihaier.feishu.cn/wiki/WyCkws028ilzchkkkL4cGMsanib?fromScene=spaceOverview')">
              查看更多>
            </div>
        </div>
        <div class="activity-con">
            <h-carousel :autoplay="true" arrows>
            <template #prevArrow>
                <div :class="isActivityHover ? 'custom-slick-arrow' : ''" style="left: 10px; z-index: 1" @mousemove="setIsActivityHover(true)">
                <LeftOutlined />
                </div>
            </template>
            <template #nextArrow>
                <div :class="isActivityHover ? 'custom-slick-arrow' : ''" style="right: 10px" @mousemove="setIsActivityHover(true)">
                <RightOutlined />
                </div>
            </template>
            <div v-for="(item, index) in bannerDataSource" :key="index" @mousemove="setIsActivityHover(true)" @mouseleave="setIsActivityHover(false)" @click="gotoUrlOrDetail(item, '/travel/bannerDetail')">
                <div class="activity-img pointer" :style="{ backgroundImage: 'url('+ item.imgUrl +')' }">
                <!-- <img :src="item.imgUrl" class="img" > -->
                </div>
            </div>
            </h-carousel>
        </div>
    </div>
</template>

<style scoped lang="less">
.pointer {
  cursor: pointer;
}

.card-header {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;

    .card-title {
      color: #000;
      font-size: 24px;
      font-weight: 600;
      line-height: 20px;
      display: flex;
    }

    .card-more {
      display: flex;
      color: #3983E5;
      font-size: 14px;
      font-weight: 400;
      line-height: 14px;
      cursor: pointer;
    
    }
}

.activity {
    width: 896px;
    height: 290px;

    .activity-con {
      margin-top: 16px;

      .activity-img {
          width: 100%;
          height: 247px;
          border-radius: 12px;
          overflow: hidden;
          background-repeat: no-repeat;
          background-size: auto 100%;
          background-position: 50%;
          background-color: #fff;
          padding-left: 1px;

          // .img{
          //   width: 833px; /(230 / 97)
          //   height: 230px;
          //   border-radius: 8px;
          // }
      }
    }
}

</style>

<style>
.activity .slick-arrow.custom-slick-arrow {
  width: 36px;
  height: 36px;
  font-size: 36px;
  color: #fff;
  opacity: 0.8;
  z-index: 1;
}

.activity .custom-slick-arrow:before
{
  display: none;
}
</style>