<script lang="ts" setup>
import { computed, createVNode, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';

import {
  Anchor as hAnchor,
  <PERSON><PERSON> as hButton,
  Spin as hSpin,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Checkbox as hCheckbox,
  Form as hForm,
  FormItem as hFormItem,
  Row as hRow,
  Col as hCol,
  Popconfirm as hPopconfirm,
  Upload as hUpload,
  Input as hInput,
  Modal as hModal,
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  DownloadOutlined,
  VerticalAlignBottomOutlined,
  ExclamationCircleFilled,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  SendOutlined,
  PhoneOutlined,
  VerticalAlignTopOutlined,
  DeleteOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { download, tripApi, teamListApi, rechargeApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import relativeTime from 'dayjs/plugin/relativeTime';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';

import { CityResponse, CityItem, TCteateTeam, RRechargeHandoverParams } from '@haierbusiness-front/common-libs';
import { fileApi } from '@haierbusiness-front/apis';
import type { TableColumnType } from 'ant-design-vue';
import { useRouter } from 'vue-router';

const route = ref(getCurrentRoute());
const router = useRouter();

const id = route.value?.query?.id;

dayjs.extend(relativeTime);

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const labelCol = { span: 6 };
const wrapperCol = { span: 16 };
// 用户选择
const params = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});

const rechargeForm = ref<RRechargeHandoverParams>({
  // 当前登陆人信息
  handoverDeptCode: loginUser.value?.departmentCode,
  handoverDeptName: loginUser.value?.departmentName || loginUser.value?.enterpriseName, // 部门名称
  handoverCode: loginUser.value?.username, //联系人工号
  handoverName: loginUser.value?.nickName, //联系人名称
  handoverTel: loginUser.value?.phone, //联系人电话
  handoverEmail: loginUser.value?.email, //联系人邮箱

  // 交接人直线是否审批
  isSp: true,
  // 是否主动交接
  handoverType: 1,

  handoverReason: '',

  // 承接人信息
  undertakeDeptName: '',
  undertakeDeptCode: '',
  undertakeCode: '',
  undertakeName: '',
  undertakeTel: '',
  undertakeEmail: '',

  // 承接人上级工号
  undertakeSuperiorCode: '',
  // 承接人上级名称
  undertakeSuperiorName: '',
});
onMounted(() => {
  getAccountNo();
});
/**
 * 获取并初始化工会账户信息，更新余额状态。
 */
const getAccountNo = () => {
  rechargeApi.getAndInitTradeUnionAccount().then((res) => {
    console.log(
      res,
      '-------------------------------------------=======================----------------------------------------',
    );
    handoverAmount.value = res.amount;
  });
};
const validateTrue = (_: any, value: boolean) =>
  value === true ? Promise.resolve() : Promise.reject(new Error('请确认明细数据'));
// 验证电话号码和手机号
const validatePassTel = async (_rule: Rule, value: string) => {
  let isPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  let isMob = /^(?:(?:\d{3}-)?\d{8}|^(?:\d{4}-)?\d{7,8})(?:-\d+)?$/;
  if (isMob.test(value) || isPhone.test(value) || !value) {
    return Promise.resolve();
  } else {
    return Promise.reject('请输入正确的电话号码');
  }
};
const rules: Record<string, Rule[]> = {
  undertakeName: [{ required: true, message: '请选择承接人', trigger: 'change' }],
  undertakeCode: [{ required: true, message: '请选择承接人', trigger: 'change' }],
  undertakeTel: [{ required: true, message: '请输入电话', validator: validatePassTel, trigger: 'change' }],
  // handoverReason: [{ required: true, message: '请输入交接原因', trigger: 'change' }],
};

const formRef = ref();
const handoverAmount = ref(0);
const submitLoading = ref(false)
// 提交
const onSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value = true
      const params = { ...rechargeForm.value, handoverAmount: handoverAmount.value };
      console.log(' ~ onSubmit ~ params:', params);
      rechargeApi.submitHandover(params).then(res => {
        submitLoading.value = false
        hMessage.success('交接成功,1秒后自动跳转列表页');
        setTimeout(() => {
          router.push('/card-order/recharge')
        }, 1000);
      }).catch(err => {
        submitLoading.value = false

      })
    })
    .catch((error: any) => {
      console.error(' onSubmit ~ error:', error);
    });
};
// handoverAmount	交接金额
// handoverCode	交接人工号
// handoverName	交接人名称
// handoverDeptCode	交接人部门编码
// handoverDeptName	交接人部门名称
// handoverTel	交接人电话
// handoverEmail	交接人邮箱
// undertakeCode	承接人工号
// undertakeName	承接人名称
// undertakeDeptCode	承接人部门编码
// undertakeDeptName	承接人部门名称
// undertakeTel	承接人电话
// undertakeEmail	承接人邮箱
// handoverReason	交接原因
// undertakeSuperiorCode	承接人上级工号
// undertakeSuperiorName	承接人上级名称

// 选择人
const userNameChange = (userInfo: IUserInfo) => {
  if (userInfo?.username){
    rechargeApi.getDirectLine({ username: userInfo?.username }).then((res) => {
      console.log(' ~ getDirectLine ~ res:', res);
      rechargeForm.value.undertakeSuperiorCode = res?.managerUsername;
      rechargeForm.value.undertakeSuperiorName = res?.managerNickName;
    });
  }else {
    rechargeForm.value.undertakeSuperiorCode = '';
    rechargeForm.value.undertakeSuperiorName = ''
  }
  console.log('🚀 ~ userNameChange ~ userInfo:', userInfo);
  rechargeForm.value.undertakeDeptCode = userInfo?.departmentCode;
  rechargeForm.value.undertakeDeptName = userInfo?.departmentName || userInfo?.enterpriseName;
  rechargeForm.value.undertakeCode = userInfo?.username;
  rechargeForm.value.undertakeName = userInfo?.nickName;
  rechargeForm.value.undertakeTel = userInfo?.phone;
  rechargeForm.value.undertakeEmail = userInfo?.email;
    
};
</script>

<template>
  <div class="container">
    <div class="row flex">
      <div class="column flex">
        <h-row justify="center" align="middle" style="padding: 40px; font-size: 20px; font-weight: 600">
          工会激励交接
        </h-row>
        <h-form
          class="mt-30"
          ref="formRef"
          :model="rechargeForm"
          :label-col="labelCol"
          :rules="rules"
          :wrapper-col="wrapperCol"
          labelAlign="left"
        >
          <!-- 当前登陆人 -->
          <a-card style="width: 100%; margin-bottom: 40px">
            <h2 class="mb10">基础信息</h2>
            <h-row>
              <h-col :span="8">
                <h-form-item name="handoverName" label="交接人">
                  <h-input redaonly disabled v-model:value="rechargeForm.handoverName" placeholder="请输入负责人" />
                </h-form-item>
              </h-col>

              <h-col :span="8">
                <h-form-item name="handoverCode" label="工号">
                  <h-input redaonly disabled v-model:value="rechargeForm.handoverCode" placeholder="请输入工号" />
                </h-form-item>
              </h-col>

              <h-col :span="8">
                <h-form-item name="handoverDeptName" label="部门">
                  <h-input redaonly disabled v-model:value="rechargeForm.handoverDeptName" placeholder="请输入部门" />
                </h-form-item>
              </h-col>

              <h-col :span="8">
                <h-form-item name="handoverTel" label="联系电话">
                  <h-input v-model:value="rechargeForm.handoverTel" placeholder="请输入联系电话" />
                </h-form-item>
              </h-col>

              <h-col :span="8">
                <h-form-item name="handoverEmail" label="邮箱">
                  <h-input v-model:value="rechargeForm.handoverEmail" placeholder="请输入邮箱" />
                </h-form-item>
              </h-col>
            </h-row>
          </a-card>

          <a-card style="width: 100%">
            <div>
              <h2 class="mb10">承接人信息</h2>
              <h-row>
                <h-col :span="8">
                  <h-form-item name="undertakeName" label="承接人">
                    <user-select
                      :value="rechargeForm.undertakeName"
                      placeholder="选择承接人"
                      :params="params"
                      @change="(userInfo: IUserInfo) => userNameChange(userInfo)"
                      class="whole-line font-size-14"
                    />
                  </h-form-item>
                </h-col>

                <h-col :span="8">
                  <h-form-item name="undertakeCode" label="工号">
                    <h-input redaonly disabled v-model:value="rechargeForm.undertakeCode" placeholder="请输入工号" />
                  </h-form-item>
                </h-col>

                <h-col :span="8">
                  <h-form-item name="undertakeDeptName" label="部门">
                    <h-input
                      redaonly
                      disabled
                      v-model:value="rechargeForm.undertakeDeptName"
                      placeholder="请输入部门"
                    />
                  </h-form-item>
                </h-col>

                <h-col :span="8">
                  <h-form-item label-width="120px" name="undertakeTel" label="联系电话">
                    <h-input v-model:value="rechargeForm.undertakeTel" placeholder="请输入联系电话" />
                  </h-form-item>
                </h-col>

                <h-col :span="8">
                  <h-form-item name="undertakeEmail" label="邮箱">
                    <h-input v-model:value="rechargeForm.undertakeEmail" placeholder="请输入邮箱" />
                  </h-form-item>
                </h-col>
              </h-row>
              <h-row>
                <h-col :span="8">
                  <h-form-item name="undertakeSuperiorName" label="承接人直线">
                    <h-input
                      redaonly
                      disabled
                      :value="rechargeForm.undertakeSuperiorName ? `${rechargeForm.undertakeSuperiorName} / ${rechargeForm.undertakeSuperiorCode}` : ''"
                      placeholder="请输入承接人直线"
                    />
                  </h-form-item>
                </h-col>
              </h-row>

              <h-row>
                <h-col :span="16">
                  <h-form-item :label-col="{ span: 3 }" name="handoverReason" label="交接原因">
                    <a-textarea v-model:value="rechargeForm.handoverReason" placeholder="请输入交接原因" allow-clear />
                  </h-form-item>
                </h-col>
              </h-row>
            </div>
          </a-card>
          <h-form-item :wrapper-col="{ offset: 10 }" style="margin-top: 30px">
            <h-button type="primary" @click="onSubmit" :loading="submitLoading">提交</h-button>
            <h-button style="margin-left: 50px" @click="router.go(-1)">取消</h-button>
          </h-form-item>
        </h-form>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url(./recharge.less);

.change-title {
  position: absolute;
  right: 0;
  top: -80px;
  width: 300px;

  .ant-col-6,
  .ant-col-18 {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #000000d9;
  }
}

.mt-5 {
  margin-top: 5px;
}

.ml-5 {
  margin-left: 5px;
}

.color-eee {
  color: #a8a7a7;
}

.com-box {
  background: #f5f5f5;
  padding: 20px;
  margin-top: 20px;

  .whole-line {
    height: 40px;
    font-weight: 600;
  }

  .info-box {
    display: flex;
    align-items: flex-start;
    padding: 10px 20px;
    border: 1px solid #ffdb5c;
    background: #fffbd8;
  }

  :deep(.ant-form-item-control-input-content) {
    display: flex;
    align-items: center;
  }
}

.title {
  height: 60px;
}

.download-btn {
  color: #408cff;
  cursor: pointer;
}

:deep(.city-chose-box) {
  width: 200px !important;
}

:deep(.mr-10) {
  margin-right: 10px;
}

.background-eee {
  padding: 10px;
  background: #f4f4f4;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
