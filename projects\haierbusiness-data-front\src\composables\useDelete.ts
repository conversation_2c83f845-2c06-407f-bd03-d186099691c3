import {message, Modal} from 'ant-design-vue'
import type {Deleteable} from "@/api/types"
import { ExclamationCircleFilled } from '@ant-design/icons-vue'
import { createVNode } from 'vue'

export const useDelete = (api: Deleteable, fetchData: () => void) => {

    const handleDelete = async (id: number, modelLabel: string = "删除") => {
        Modal.confirm({
            title: `确认要${modelLabel}吗？`,
            icon: createVNode(ExclamationCircleFilled),
            onOk: () => {
                api.remove(id).then(res => {
                    if (res.code == 0) {
                        message.success(`${modelLabel}成功！`)
                        fetchData()
                    } else {
                        message.error(res.msg)
                    }
                })
            },
            onCancel: async () => {

            },
        })
    }

    return {
        handleDelete
    }
}
