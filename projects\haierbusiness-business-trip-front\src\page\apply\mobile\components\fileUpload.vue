<template>
  <div>
    <div class="title flex justify-content-between align-items-center">
      <div class="flex align-items-center justify-content-center">
        <span class="shu"></span>
        <div class="text">附件</div>
      </div>
    </div>
    <van-cell-group inset style="padding: 20px">
      <van-uploader
        :after-read="afterRead"
        :preview-image="false"
        v-model="props.creatTripParma.fileList"
        accept=".pdf"
        style="width: 100%"
        :max-size="10 * 1000 * 1024"
        @oversize="onOversize"
      >
        <van-button class="mb-10" size="small" icon="plus" type="primary">上传文件</van-button>
        <div class="color-eee font-size-12">可上传本次差旅相关的资料文件（如会议通知、邀请函等）,建议上传不超过2M的pdf文件,最大不超过10M</div>
        
      </van-uploader>
      <div class="file-list">
        <div
          v-for="(item, index) in props.creatTripParma.fileList"
          :key="index"
          class="flex align-items-center justify-content-between file-item"
        >
          <div class="file-name">{{ item.fileName }}</div>
          <van-icon @click="delFile(item, index)" class="del-icon" name="cross" />
        </div>
      </div>
    </van-cell-group>
  </div>
</template>

<script lang="ts" setup>
import { createVNode, onMounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref } from 'vue';

import { IUserListRequest, IUserInfo, ICity, ITripInfo, ITraveler, ICreatTrip } from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { reasonApi } from '@haierbusiness-front/apis';
import { userApi } from '@haierbusiness-front/apis';
import { debounce, values } from 'lodash';

import { showConfirmDialog } from 'vant';
import { fileApi } from '@haierbusiness-front/apis';
import { showFailToast } from 'vant';

interface Props {
  creatTripParma?: ICreatTrip; // 人员
  isDetail?: boolean;
  isChange?: boolean;
  chosedNow?: string;
}
const props = defineProps<Props>();
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const emit = defineEmits(['show', 'hide']);

const onOversize = (file) => {
  console.log(file);
  showFailToast('文件大小不能超过 10M');
};

const afterRead = (options) => {
  // options.status = 'uploading';
  // options.message = '上传中...';
  emit('show');
  const formData = new FormData();
  formData.append('file', options.file);
  fileApi
    .upload(formData)
    .then((it) => {
      options.filePath = baseUrl + it.path;
      options.fileName = options.file.name;
      // options.
      emit('hide');
    })
    .catch(() => {
      emit('hide');
    })
    .finally(() => {
      // uploadLoading.value = false;
      emit('hide');
    });
};

const delFile = (item, index) => {
  props.creatTripParma.fileList?.splice(index, 1)
};
</script>

<style lang="less" scoped>
@import url(./mobile.less);
:deep(.van-uploader__wrapper) {
  width: 100%;
}
:deep(.van-uploader__input-wrapper) {
  width: 100%;
}
</style>