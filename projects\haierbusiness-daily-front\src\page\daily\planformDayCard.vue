<script setup lang="ts">
import {
  Modal,
  Tag as hTag,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Table as hTable,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import {
  EyeOutlined,
  FormOutlined,
  PlusOutlined,
  SearchOutlined,
  SaveOutlined,
  SubnodeOutlined,
  SafetyOutlined,
  FundOutlined,
} from '@ant-design/icons-vue';
import {
  AnnualPlanStateConstant,
  AnnualPlanTypeStateConstant,
  EvaluateTypeConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
  UserGroupSystemConstant,
} from '@haierbusiness-front/common-libs';
import { checkUserGroup, getCurrentRouter, routerParam } from '@haierbusiness-front/utils';
import { ref, createVNode, PropType, watch, computed } from 'vue';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import {
  DailyReportStateConstant,
  IAnnualPlanTypeListRequest,
  IDailyReportListResponseDTO,
  IMonthPlanListResponseDTO,
  MonthPlanStateConstant,
} from '@haierbusiness-front/common-libs/src/daily';
import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import VChart from 'vue-echarts';
import dayjs, { Dayjs } from 'dayjs';
import { message } from 'ant-design-vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { BarChart, PieChart, LineChart } from 'echarts/charts';
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
const { loginUser } = storeToRefs(applicationStore(globalPinia));
const router = getCurrentRouter();
use([CanvasRenderer, PieChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent, BarChart, LineChart]);
const prop = defineProps({
  data: Object as PropType<IDailyReportListResponseDTO>,
});

/**
 * 计算当前用户权限的权限bitmap值,当评价模式时使用
 */
const dailyGroupValue = computed(() => {
  let result = 0;
  if (checkUserGroup(UserGroupSystemConstant.DAILY_STRATEGY.groupId)) {
    result += 2;
  }
  if (checkUserGroup(UserGroupSystemConstant.DAILY_RISK_MANAGEMENT.groupId)) {
    result += 4;
  }
  if (checkUserGroup(UserGroupSystemConstant.DAILY_FINANCE.groupId)) {
    result += 8;
  }
  if (checkUserGroup(UserGroupSystemConstant.DAILY_HUMAN_RESOURCES.groupId)) {
    result += 16;
  }
  return result;
});

const gotoDailyReport = () => {
  router.push({
    path: '/daily/daily/report',
    query: { data: routerParam(prop.data) },
  });
};

const dataColor = {
  color: ['rgb(230,232,234)', 'rgb(145,205,118)', 'rgb(238,102,102)'],
};
// 基础数据
// 总数
const itemCount = computed(() => prop.data?.itemCount || 0);
// 已录入
const entered = computed(() => prop.data?.completeItemCount || 0);
// 待录入
const wait = computed(() => itemCount.value - entered.value - needNot.value || 0);
// 无需录入
const needNot = computed(() => prop.data?.nonItemCount || 0);

const annualListLoading = ref(false);
const annualData = ref<IAnnualPlanListResponseDTO>({});

const annualListApiRun = () => {
  annualListLoading.value = true;
  dailyAnnualPlanApi
    .list({
      year: prop?.data?.year,
      deptCode: prop?.data?.deptCode,
    })
    .then((it) => {
      annualData.value = it[0];
    })
    .finally(() => {
      annualListLoading.value = false;
    });
};
annualListApiRun();
const pieEchartsOption = ref({
  tooltip: {
    trigger: 'item',
  },
  ...dataColor,
  legend: {
    top: '25%',
    left: 'right',
    orient: 'verticalAlign',
  },
  series: [
    {
      left: 'left',
      center: ['25%', '50%'],
      name: 'Access From',
      type: 'pie',
      radius: ['50%', '80%'],
      label: {
        show: true,
        position: 'center',
        formatter: '0%',
        fontWeight: 'bold',
        fontSize: 16,
      },
      labelLine: {
        show: false,
      },
      data: [
        {
          value: 0,
          name: '无需录入：0',
        },
        { value: 0, name: '已录入：0' },
        { value: 0, name: '未录入：0' },
      ],
    },
  ],
});

const subscriptColorStyle = computed(() => {
  if (DailyReportStateConstant.WAIT.code === prop?.data?.state) {
    return { 'background-color': 'rgb(250,173,20)' };
  }
  if (DailyReportStateConstant.RUNNING.code === prop?.data?.state) {
    return { 'background-color': 'rgb(0,115,229)' };
  }
  if (DailyReportStateConstant.FINISH.code === prop?.data?.state) {
    return { 'background-color': 'rgb(82,196,26)' };
  }
});

const updateChartsData = () => {
  // 设置pie图
  pieEchartsOption.value.series[0].data = [
    {
      value: needNot.value,
      name: '免录入：' + needNot.value,
    },
    { value: entered.value, name: '已录入：' + entered.value },
    { value: wait.value, name: '未录入：' + wait.value },
  ];
  pieEchartsOption.value.series[0].label.formatter =
    ((needNot.value + entered.value) / itemCount.value / 0.01).toFixed(0) + '%';
};
{
  updateChartsData();
}

watch(() => prop.data, updateChartsData);

const showAction = ref(false);
const onMouseover = () => {
  showAction.value = true;
};
const onMouseout = () => {
  showAction.value = false;
};
</script>

<template>
  <div class="card">
    <div class="card-mask" @mouseenter="onMouseover" @mouseleave="onMouseout">
      <div v-if="showAction" style="height: 100%">
        <div style="display: flex; align-items: center; flex-wrap: wrap; justify-content: center; height: 100%">
          <div style="margin-top: 10px">
            <h-space>
              <h-button @click="gotoDailyReport" type="primary">
                <template #icon>
                  <FormOutlined />
                </template>
                处理
              </h-button>
            </h-space>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div :style="subscriptColorStyle" class="subscript">
        <div style="margin-top: 8%">{{ prop?.data?.stateName }}</div>
      </div>
      <div style="height: 50%; width: 90%">
        <v-chart :option="pieEchartsOption" :autoresize="true" />
      </div>
      <div style="height: 75%">
        <!-- <v-chart :option="barEchartsOption" :autoresize="true" style="position: relative; top: -13%" /> -->
        <div style="font-size: 16px; position: relative; top: 40px">
          日决议数：{{ prop?.data?.conferenceCount || 0 }}
          <br />
          <br />
          日激励数：{{ prop?.data?.dailyEvaluateTotal || 0 }}
        </div>
      </div>
    </div>
    <div class="footer">
      {{ prop?.data?.deptName }} &nbsp; {{ prop?.data?.year }}-{{ prop?.data?.month }}-{{ prop?.data?.day }}
    </div>
  </div>
</template>

<style scoped lang="less">
.subscript {
  position: absolute;
  width: 23%;
  height: 8%;
  right: 2.5%;
  color: white;
  font-size: 12px;
  text-align: center;
  vertical-align: middle;
  border-bottom-left-radius: 5px;
  border-top-right-radius: 5px;
}

.card {
  box-shadow: 0 0 2px rgba(189, 250, 255, 0.7);
  border: 0.5px solid #eaeaea;
  border-radius: 5px;
  text-align: center;
  height: calc(37vh);
  min-height: 290px;
  width: 95%;
  margin: 10px auto;

  &:hover {
    box-shadow: 0 0 10px rgba(189, 250, 255, 0.7);
    border: 0.5px solid rgba(189, 250, 255, 0.7);
  }

  .content {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    height: 90%;
  }

  .footer {
    padding-top: 5px;
    text-align: center;
    font-weight: 700;
    font-size: 16px;
    border-top: 0.5px solid #eaeaea;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    background-color: rgb(247, 249, 250);
    height: 10%;
  }
}

.card-mask {
  border-radius: 5px;
  cursor: pointer;
  position: absolute;
  height: calc(37vh);
  min-height: 290px;
  width: 95%;
  z-index: 10;
  &:hover {
    background-color: #424242c8;
    transition: all 0.3s;
    animation-name: gradually;
    animation-duration: 1s;
  }
}
</style>
