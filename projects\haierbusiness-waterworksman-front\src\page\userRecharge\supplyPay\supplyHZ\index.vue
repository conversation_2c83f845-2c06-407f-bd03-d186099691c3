<script setup lang="ts">
import { DownloadOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { userApi } from '@haierbusiness-front/apis';
import {
  Button as hButton,
  Col as hCol,
  Input as hInput,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  message,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/es/table';
import { computed, ref } from 'vue';
import { usePagination } from 'vue-request';
type MyColumn = ColumnType & {
  children?: ColumnType[];
};

const { data, run: userApiRun, loading, current, pageSize } = usePagination(userApi.list);
const sorter = (a: any, b: any) => a.applicationForm.localeCompare(b.applicationForm);

/**
 * @表格相关
 * */
const selectedItems = ref<any>([]);

const tableProps = computed(() => ({
  rowKey: 'id',
  dataSource: data.value?.records || [],
  pagination: {
    showSizeChanger: true,
    showQuickJumper: true,
    total: data.value?.total,
    current: data.value?.pageNum,
    pageSize: data.value?.pageSize,
    style: { justifyContent: 'center' },
  },
  columns: [
    {
      title: '序号',
      dataIndex: 'index',
    },
    {
      title: '汇总单号',
      dataIndex: 'id',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '汇总日期',
      dataIndex: '汇总日期',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '汇总金额',
      dataIndex: 'orderDown',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '汇总状态',
      dataIndex: '送水区域',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '支付状态',
      dataIndex: '送水地址',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '备注',
      dataIndex: 'remake',
      sorter: (a: any, b: any) => sorter(a, b),
    },
  ],
  // 支持多选
  rowSelection: {
    selectedRowKeys: selectedItems.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      console.log(selectedRowKeys, selectedRows);
      selectedItems.value = selectedRowKeys;
      console.log(selectedItems.value);
    },
  },
}));

const handleOemOrder = () => {
  console.log(selectedItems.value);
  message.info('暂无接口');
};

const handleStockOrder = () => {
  console.log(selectedItems.value);
  message.info('暂无接口');
};

const handleOrderReturn = () => {
  console.log(selectedItems.value);
  message.info('暂无接口');
};

/**
 * @表单相关
 * */
type OrderFormData = {
  applicationForm: string; // 申请单号
  orderDown: string; // 申请单位
  orderPrint: string; // 结算公司
  orderState: string | null; // 订单状态
  payState: string | null; // paymentStatus
};

const formData = ref<OrderFormData>({
  applicationForm: '', // 申请单号
  orderDown: '', // 申请单位
  orderPrint: '', // 结算公司
  orderState: null, // 订单状态
  payState: null, // paymentStatus
});

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  userApiRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
    ...formData.value,
  });
};

const handleTableReset = () => {
  formData.value = {
    applicationForm: '',
    orderDown: '',
    orderPrint: '',
    orderState: null,
    payState: null,
  };
  // handleTableChange({ current: 1, pageSize: 10 });
};
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <!-- 页面主体 -->
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px" :gutter="[0, 10]">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="applicationForm">汇总单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="applicationForm"
              v-model:value="formData.applicationForm"
              placeholder="请输入"
              autocomplete="off"
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="orderDown">汇总单状态</label>
          </h-col>
          <h-col :span="4">
            <h-select
              id="payState"
              v-model="formData.payState"
              placeholder="请选择"
              autocomplete="off"
              style="width: 100%"
            >
              <h-select-option value="10">已汇总</h-select-option>
              <h-select-option value="20">未汇总</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="orderState">支付状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              id="orderState"
              v-model="formData.orderState"
              placeholder="请选择"
              autocomplete="off"
              style="width: 100%"
            >
              <h-select-option value="10">待支付</h-select-option>
              <h-select-option value="20">已支付</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined /> 查询
            </h-button>
            <h-button type="primary" @click="handleOemOrder" style="margin-left: 10px"> 核对 </h-button>
            <h-button @click="handleOrderReturn" style="margin-left: 10px"> <DownloadOutlined /> 导出 </h-button>
            <!-- <h-button style="margin-left: 10px" @click="handleTableReset">重置</h-button> -->
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table v-bind="tableProps" :loading="loading" size="middle" @change="handleTableChange($event as any)"> </h-table>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}
</style>
