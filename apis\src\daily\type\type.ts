import {
    IAnnualPlanTypeListRequest,
    IAnnualPlanTypeListResponse,
    IAnnualPlanTypeUpdateRequest
} from '@haierbusiness-front/common-libs'
import {get, post} from '../../request'


export const dailyTypeApi = {

    list: (params: IAnnualPlanTypeListRequest): Promise<IAnnualPlanTypeListResponse[]> => {
        return get('/daily/api/annual-type/list', params)
    },

    update: (params: IAnnualPlanTypeUpdateRequest): Promise<void> => {
        return post('/daily/api/annual-type/update', params)
    },

}