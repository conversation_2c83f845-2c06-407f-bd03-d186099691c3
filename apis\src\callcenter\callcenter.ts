import { workOrderQuery } from './../../../common-libs/src/callcenter/model/callcenter';
import { get, post,downloadPost } from '../request'
import {
  callCentertRes,
  AddressBookListParams
} from '@haierbusiness-front/common-libs'

export const callCenterApi = {
  // 通讯录查询
  list: (params?: AddressBookListParams): Promise<callCentertRes> => {
    return post(`customer/api/call/book/getAddressBookList`, params);
  },
  // 删除通讯录
  remove: (ids:string[]): Promise<callCentertRes> => {
    return post(`customer/api/call/book/deleteAddressBook`, {ids});
  },
  // 新增通讯录
  save: (params: object): Promise<callCentertRes> => {
    return post(`customer/api/call/book/saveOrUpdateAddressBook`, params);
  },
  // 保存或者修改工单 
  saveOrUpdateWorkOrder: (params: object): Promise<callCentertRes> => {
    return post(`customer/api/call/work/saveOrUpdateWorkOrder`, params);
  },
  // 工单管理分页列表
  WorkOrderList: (params: object): Promise<callCentertRes> => {
    return post(`customer/api/call/work/getWorkOrderList`, params);
  },
  // 来电页面查询来电信息、工单信息、通话信息
  CallHistoryGetVoInfo: (params: object): Promise<callCentertRes> => {
    return post(`customer/api/call/history/getCallHistoryGetVoInfo`, params);
  },
  // 坐席管理列表 
  AgentCustomerList: (params: object): Promise<callCentertRes> => {
    return post(`customer/api/customer/agent/getAgentCustomerList`, params);
  },
  // 保存 修改坐席 
  createOrUpdateAgentCustomer: (params: object): Promise<callCentertRes> => {
    return post(`customer/api/customer/agent/createOrUpdateAgentCustomer`, params);
  },
  // 获取产品线数据
  getProductList: (): Promise<callCentertRes> => {
    return get(`customer/api/customer/agent/getProductList`);
  },
  // 删除坐席
  deleteAgentCustomer: (id:number[]): Promise<callCentertRes> => {
    return post(`customer/api/customer/agent/deleteAgentCustomer`, {id});
  },
  // 示忙示闲
  leisureSave: (params: object): Promise<callCentertRes> => {
    return post(`customer/api/leisure/save`, params);
  },
}


export const workOrderApi = {
  // 工单查询
  list: (params?: AddressBookListParams): Promise<callCentertRes> => {
    return post(`customer/api/call/work/getWorkOrderList`, params);
  },
  // 删除工单
  remove: (ids:string[]): Promise<callCentertRes> => {
    return post(`customer/api/call/book/deleteAddressBook`, {ids});
  },
  // 新增工单
  save: (params: object): Promise<callCentertRes> => {
    return post(`customer/api/call/work/saveOrUpdateWorkOrder`, params);
  },
  // 编辑工单
  edit: (params: object): Promise<callCentertRes> => {
    return post(`customer/api/call/work/saveOrUpdateWorkOrder`, params);
  },
  // 根据id获取工单详情
  getInfoById: (params: object): Promise<callCentertRes> => {
    return get(`customer/api/call/work/getWorkOrderById`, params);
  },
  // 新增明細 customer/api/call/work/saveWorkOrderDetails
  saveWorkOrderDetails: (params: object): Promise<callCentertRes> => {
    return post(`customer/api/call/work/saveWorkOrderDetails`, params);
  },
  // 工单闭合 
  finishWorkOrder: (params: object): Promise<callCentertRes> => {
    return get(`customer/api/call/work/finishWorkOrder`, params);
  },
  // 根据callID 获取相关联的工单 
  getWorkListByCallId: (params: any): Promise<callCentertRes> => {
    return post(`customer/api/call/work/getWorkListByCallId`,params);
  },
}

export const reportVoPageResultApi = {
  list: (params?: AddressBookListParams): Promise<callCentertRes> => {
    return post(`customer/api/call/history/reportVoPageResult`, params);
  },
  exportExcel: (params: any): Promise<void> => {
    return downloadPost('customer/api/call/history/reportVoPageResultExcel',params)
  },
}

export const callHistoryApi = {
  // 工单查询
  list: (params?: AddressBookListParams): Promise<callCentertRes> => {
    return post(`customer/api/call/history/getCallHistoryListByProduct`, params);
  },
  // 删除工单
  remove: (ids:string[]): Promise<callCentertRes> => {
    return post(`customer/api/call/book/deleteAddressBook`, {ids});
  },
  // 新增工单
  save: (params: object): Promise<callCentertRes> => {
    return post(`customer/api/call/work/saveOrUpdateWorkOrder`, params);
  },
  // 编辑工单
  edit: (params: object): Promise<callCentertRes> => {
    return post(`customer/api/call/work/saveOrUpdateWorkOrder`, params);
  },
  // 根据id获取工单详情
  getInfoById: (params: object): Promise<callCentertRes> => {
    return get(`customer/api/call/work/getWorkOrderById`, params);
  },
  // 新增明細 customer/api/call/work/saveWorkOrderDetails
  saveWorkOrderDetails: (params: object): Promise<callCentertRes> => {
    return post(`customer/api/call/work/saveWorkOrderDetails`, params);
  },
  

  exportExcel: (params: any): Promise<void> => {
    return downloadPost('customer/api/call/history/getCallHistoryExcel',params)
  },
  downLoadFile: (params: any): Promise<void> => {
    return get('customer/api/call/history/getFileDownloadPath',params)
  },
  login: (url:string): Promise<callCentertRes> => {
    return post(url);
  },
  getRecordList: (params: object,headers:any,func:any): Promise<callCentertRes> => {
    return post(`aiccrecord/api/record/query/general`, params,headers,func);
  },
  // 下载文件
  getRecordName: (params: object,headers:any,func:any): Promise<callCentertRes> => {
    return get(`aiccrecord/api/record/play/general`, params,headers,func);
  },
}