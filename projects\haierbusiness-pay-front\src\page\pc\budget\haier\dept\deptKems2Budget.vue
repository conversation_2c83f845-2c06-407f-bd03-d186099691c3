<script setup lang="ts">
import { computed, onMounted, PropType, reactive, ref, watch } from 'vue'
import { Table as hTable, Divider as hDivider, Select as hSelect, RadioGroup as hRadioGroup, Radio as hRadio, RadioButton as hRadioButton, Modal as hModal, Input as hInput, InputSearch as hInputSearch, Loading as hLoading, Space as hSpace, Button as hButton, Row as hRow, Col as hCol, Tabs as hTabs, TabPane as hTabPane, Image as hImage, message, TableProps } from 'ant-design-vue';
import { IPayData } from '@haierbusiness-front/common-libs/src/pay/model/basicModel';
import { budgetHaierPayApi } from '@haierbusiness-front/apis/src/pay/budgetHaierPay';
import {
  HaierBudgetSourceConstant,
  IUserInfo,
  IUserListRequest,
  PaySourceConstant
} from '@haierbusiness-front/common-libs';
import { DataType, usePagination, useRequest } from 'vue-request';
import { userApi } from '@haierbusiness-front/apis';
import { Key, TablePaginationConfig } from 'ant-design-vue/lib/table/interface';
import {isMobile} from "@haierbusiness-front/utils";

const props = defineProps<{ budgetType?: string, param?: IPayData }>()

const emit = defineEmits<{
  (e: 'payComplete', isPayComplete: boolean): void
}>()


const budgeterCode = ref();
const budgeterName = ref();

const budgetDepartmentCode = ref();
const budgetDepartmentName = ref();


const companyCode = ref();
const companyName = ref();

const unitCode = ref();
const unitName = ref();

const leftAmt = ref();

const payLoading = ref(false);
const userLoading = ref(false);


// 费用科目
const {
  data: searchFeeItemsData,
  run: searchFeeItemsRun,
  loading: searchFeeItemsLoading,
} = useRequest(
    budgetHaierPayApi.searchFeeItems, {
      manual: false,
      defaultParams: [
        {
          applicationCode: props.param?.applicationCode,
          budgetSysCode: HaierBudgetSourceConstant.KEMS.code,
          businessType: props.param?.businessType,

        }
      ]
    }
);
const feeItemOptions = computed(() => {
  if (searchFeeItemsData.value) {
    return searchFeeItemsData.value.map(it => {
      return {"value": it.itemCode, "label": it.itemName}
    })
  } else {
    return []
  }
});

const feeItem = ref()
const feeItemName = ref();
watch(feeItem, (newValue, oldValue) => {
  feeItemName.value = feeItemOptions.value.find(it => {
    return it.value === newValue
  })?.label
});

const onSearchCode = () => {
  if (!feeItem.value) {
    message.error("请选择费用科目!")
    return;
  }
  if (!budgeterCode.value) {
    message.error("请输入预算人!")
    return;
  }

  const reg = /^(^[0-9]*$)|(^[A-Za-z]+$)/
  if (!reg.test(budgeterCode.value)) {
    visibleUserSearch.value = true
    searchUserNameParams.value.nickName = budgeterCode.value
    handleSearchUserRun({current: 1, pageSize: 10})
  } else {
    onSearch()
  }
}

const onSearchOk = () => {
  budgeterCode.value = userSelectedRowKeys.value[0]
  if (budgeterCode.value) {
    onSearch()
  }
  visibleUserSearch.value = false
  userSelectedRowKeys.value = []
  userSelectedRowRecord.value = {}
}

const onSearch = () => {
  if (!feeItem.value) {
    message.error("请选择费用科目!")
    return;
  }
  if (!budgeterCode.value) {
    message.error("请输入预算人!")
    return;
  }

  userLoading.value = true;
  // 查询前上次结果清空
  companyCode.value = null
  companyName.value = null
  unitCode.value = null
  unitName.value = null
  budgetDepartmentCode.value = null
  budgetDepartmentName.value = null
  leftAmt.value = null

  budgetHaierPayApi.searchBudget({
    businessType: props.param?.businessType,

    haierBudgetType: props.budgetType,
    applicationCode: props?.param?.applicationCode,
    budgeterCode: budgeterCode.value,
    budgetDepartmentCode: budgetDepartmentCode.value,
    feeItem: feeItem.value,
  }).then(it => {
    companyCode.value = it.companyCode
    companyName.value = it.companyName

    unitCode.value = it.unitCode
    unitName.value = it.unitName

    budgetDepartmentCode.value = it.entityCode
    budgetDepartmentName.value = it.entityName

    leftAmt.value = it.leftAmt
  }).finally(() => {
    userLoading.value = false;
  })
};


const payComplete = () => {
  emit('payComplete', true)
}

const pay = () => {
  if (!feeItem.value) {
    message.error("请选择费用科目!")
    return;
  }
  if (!budgeterCode.value) {
    message.error("请输入预算人!")
    return;
  }
  if (!leftAmt.value) {
    message.error("获取可用余额失败!")
    return;
  }
  if (leftAmt.value <= 0) {
    message.error("可用余额不足!")
    return;
  }
  payLoading.value = true;
  budgetHaierPayApi.occupyBudget(
      {
        haierBudgetType: props.budgetType,
        feeItem: feeItem.value,
        feeItemName: feeItemName.value,
        budgeterCode: budgeterCode.value,
        budgeterName: budgeterName.value,
        budgetDepartmentCode: budgetDepartmentCode.value,
        budgetDepartmentName: budgetDepartmentName.value,
        accountCompanyCode: companyCode.value,
        accountCompanyName: companyName.value,
        unitCode: unitCode.value,
        unitName: unitName.value,

        // - 通用参数
        orderCode: props.param?.orderCode,
        extJsonParam: props.param?.extJsonParam,
        startApproveFlag:props.param?.startApproveFlag,
        applicationCode: props.param?.applicationCode,
        payTypes: props.param?.payTypes,
        username: props.param?.username,
        providerCode: props.param?.providerCode,
        amount: Number(props.param?.amount),
        orderDetailsUrl: props.param?.orderDetailsUrl,
        businessType: props.param?.businessType,

        notifyUrl: props.param?.notifyUrl,
        callbackUrl: props.param?.callbackUrl,
        description: props.param?.description,
        payload: props.param?.payload,
        paySource: isMobile() ?  PaySourceConstant.MOBILE.type : PaySourceConstant.PC.type,
        paymentMethod: 2
      },
      {
        applicationCode: props.param?.applicationCode,
        excludes: "paySource,haierBudgetType,feeItem,feeItemName,budgeterCode,budgeterName,budgetDepartmentCode,budgetDepartmentName,accountCompanyCode,accountCompanyName,unitCode,unitName,paymentMethod,startApproveFlag",
        nonce: props.param?.hbNonce,
        timestamp: props.param?.hbTimestamp,
        sign: props.param?.sign,
      }
  ).then(it => {
    payComplete()
  }).finally(() => {
    payLoading.value = false;
  })
};

// 结算单位
const {
  data: searchUserData,
  run: searchUserRun,
  loading: searchUserLoading,
  current: searchUserCurrent,
  pageSize: searchUserPageSize,
  totalPage: searchUserTotalPage,
} = usePagination(
    userApi.list,
    {
      onSuccess(data, params) {
        if (data && data.records && data.records.length > 0) {
          const record = data.records[0]
          const username = record.username || ''
          userSelectedRowKeys.value = [username]
          userSelectedRowRecord.value = record
        }
      },
    }
);
const searchUserNameParams = ref<IUserListRequest>({})
const visibleUserSearch = ref(false)
const handleSearchUserRun = (
    pag: { current: number; pageSize: number },
) => {
  searchUserRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
    ...searchUserNameParams.value,
    enterpriseCode: 'haier'
  })
};

const userSelectedRowKeys = ref<Key[]>([1])
const userSelectedRowRecord = ref<IUserInfo>()

const userDepartmentsPagination = computed<TablePaginationConfig>(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: searchUserData.value?.total,
  current: searchUserData.value?.pageNum,
  pageSize: searchUserData.value?.pageSize,
  size: 'default',
  style: {justifyContent: 'center'},

}));
const userTableColumns = [
  {
    title: '',
    dataIndex: '',
    width: '5%',
  },
  {
    title: '工号',
    dataIndex: 'username',
    width: '35%',
  },
  {
    title: '姓名',
    dataIndex: 'nickName',
    width: '60%',
  },
];


const selectUserSelection: TableProps['rowSelection'] = {
  type: 'radio',
  selectedRowKeys: userSelectedRowKeys as any,
  onChange: (selectedRowKeys: Key[], selectedRows: DataType[]) => {
    userSelectedRowKeys.value = selectedRowKeys
    userSelectedRowRecord.value = selectedRows[0] as unknown as IUserInfo
  },
};
</script>
<template>
  <h-modal v-model:visible="visibleUserSearch" :title="'用户查询选择'" style="width: 1000px;" @ok="onSearchOk">
    <h-row :align="'middle'">
      <h-col :span="24">
        <h-table :columns="userTableColumns" :row-key="record => record.username" size="small"
                 :row-selection="selectUserSelection" :data-source="searchUserData?.records"
                 :pagination="userDepartmentsPagination" :loading="searchUserLoading"
                 @change="handleSearchUserRun($event as any)">
        </h-table>
      </h-col>
    </h-row>
  </h-modal>
  <h-row style="margin-top: 4vh;text-align: right;" :align="'middle'">
    <h-col span="2" style="font-size: 12px">
      费用科目：
    </h-col>
    <h-col span="6" style="text-align: left;">
      <h-select v-model:value="feeItem" :size="'large'" class="budget-input" :options="feeItemOptions"></h-select>
    </h-col>
    <h-col span="2" style="font-size: 12px">
      预算人：
    </h-col>
    <h-col span="6" style="text-align: left;">
      <h-input-search v-model:value="budgeterCode" placeholder="工号/姓名" :size="'large'" class="budget-input"
                      :loading="userLoading" enter-button @search="onSearchCode"/>
    </h-col>
    <h-col span="2" style="font-size: 12px">
      结算单位：
    </h-col>
    <h-col span="6" style="text-align: left;">
      <h-input v-model:value="budgetDepartmentName" :disabled="true" placeholder="" :size="'large'"
               :title="budgetDepartmentCode + '/' + budgetDepartmentName" class="budget-input-readonly"/>
    </h-col>
  </h-row>
  <h-row style="margin-top: 4vh;text-align: right;" :align="'middle'">
    <h-col span="2" style="font-size: 12px">
      预算单元：
    </h-col>
    <h-col span="6" style="text-align: left;">
      <h-input v-model:value="unitName" :disabled="true" placeholder="" :size="'large'"
               :title="unitCode + '/' + unitName" class="budget-input-readonly"/>
    </h-col>
    <h-col span="2" style="font-size: 12px">
      付款公司：
    </h-col>
    <h-col span="6" style="text-align: left;">
      <h-input v-model:value="companyName" :disabled="true" placeholder="" :size="'large'"
               :title="companyCode + '/' + companyName" class="budget-input-readonly"/>
    </h-col>
    <h-col span="2" style="font-size: 12px">
      可用余额：
    </h-col>
    <h-col span="6" style="text-align: left;">
      <h-input v-model:value="leftAmt" :disabled="true" placeholder="" :size="'large'"
               class="budget-input-readonly"/>
    </h-col>
  </h-row>
  <h-row>
    <h-divider></h-divider>
  </h-row>
  <h-row style="line-height: 14vh;" :align="'middle'">
    <h-col span="2" offset="11">
      <h-button type="primary" style="width: 100%;" @click="pay" :loading="payLoading"
                size="large">&nbsp;支&nbsp;&nbsp;付&nbsp;
      </h-button>
    </h-col>
  </h-row>
</template>

<style scoped lang="less">
.budget-input {
  width: 12vw;
}

.budget-input-readonly {
  width: 12vw;
  background-color: rgb(245, 245, 245);
  color: #2e2e2e;
}
</style>
