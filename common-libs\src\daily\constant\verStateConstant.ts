type keys = "CURRENT" | "HISTORY" | "WAIT" | "DISCARD";

/**
 * 版本状态
 * 0：生效版本 1：历史版本 2：待生效版本 3：废弃版本
 *
 * 数据某个版本生命周期
 *      1：待生效版本 -> 生效版本 -> 历史版本
 *      2：待生效版本 -> 废弃版本
 */
export const VerStateConstant = {
    /**
     * 生效版本，任何数据最多只有一条
     */
    CURRENT: {"code": 0, "desc": "生效版本"},

    /**
     * 历史版本(从生效版本演化而来)
     */
    HISTORY: {"code": 1, "desc": "历史版本"},

    /**
     * 待生效版本，任何数据最多只有一条
     */
    WAIT: {"code": 2, "desc": "待生效版本"},

    /**
     * 废弃版本(从待生效版本演化而来)
     */
    DISCARD: {"code": 3, "desc": "废弃版本"},

    ofCode: (code?: number): { "code": number, "desc": string } | null => {
        for (const key in VerStateConstant) {
            const item = VerStateConstant[key as keys];
            if (code === item.code) {
                return item;
            }
        }
        return null;
    }
}