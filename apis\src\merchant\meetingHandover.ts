import { download, get, post } from '../request'
import {
    IMeetingHandoverFilter,
    IMeetingHandover,
    IPageResponse,
    Result
} from '@haierbusiness-front/common-libs'


export const meetingHandoverApi = {
    list: (params: IMeetingHandoverFilter): Promise<IPageResponse<IMeetingHandover>> => {
        return get('merchant/api/meetingHandover/list', params)
    },
    // 通过经办人姓名获取需要交接的会议单号
    get: (params: IMeetingHandoverFilter): Promise<IPageResponse<IMeetingHandover>> => {
        return get('/mice-bid/api/mice/connect/getConnectMice', params)
    },
    // 通过会议单号获取会议信息

    getMiceInfo: (params: IMeetingHandoverFilter): Promise<IPageResponse<IMeetingHandover>> => {
        return get('/mice-bid/api/mice/connect/getMiceInfo', params)
    },
    // 会议交接
    save: (params: IMeetingHandover): Promise<Result> => {
        return post('/mice-bid/api/mice/connect/mice-connect', params)
    },
    // 会议交接人直线信息
    getMeetUser: (params: IMeetingHandoverFilter): Promise<IPageResponse<IMeetingHandover>> => {
        return get('/mice-bid/api/mice/connect/getConnectMiceInfo', params)
    },
    edit: (params: IMeetingHandover): Promise<Result> => {
        return post('merchant/api/meetingHandover/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('merchant/api/meetingHandover/delete', { id })
    },
}
