<template>
  <div class="home">

    <!-- 内容 -->
    <div class="home-main">

      <!-- 轮播图 -->
      <van-loading class="home-main-img" v-if="showSwipeLoading" type="spinner" size="30"
        style="align-items: center; justify-content: center; " />
      <van-swipe v-else class="home-main-img" :autoplay="3000" indicator-color="white">
        <van-swipe-item v-for="(item, index) in urlList" :key="index">
          <van-image radius="10" class="home-main-img " :src="item" />
        </van-swipe-item>
      </van-swipe>

      <!-- 通知通告 -->
      <div class="home-main-notice flex">
        <div class="left flex align-items-center">
          <div class="flex align-items-center ">
            <div class="notice-icon"></div>
            <div class="notice-text">通知公告:</div>
          </div>
        </div>
        <!-- <van-divider vertical class="middle" /> -->
        <div class="right">
          <van-swipe v-if="noticeList && noticeList.length > 0" style="height: 100%;" vertical :autoplay="3000"
            :show-indicators="false">
            <van-swipe-item v-for="item, index in noticeList" :key="index" @click="goToNewsDetail(item.id)">
              <div style="width: 100%; height: 100%;" class="flex flex-column justify-content-center">
                <van-text-ellipsis rows="1" :content="item.title" />
                <!-- <div style="color: #c7c7c7; text-align: right; font-size: 12px;">{{ item.updateTime }}</div> -->
              </div>
            </van-swipe-item>
          </van-swipe>
          <div class="notice-empty" v-else>
            暂无公告
          </div>
        </div>
      </div>
      <!-- 我的申请单 -->
      <div class="home-mine mb-5">
        <div class="mine-title flex mb-5">
          <div class="left">我的申请单</div>
          <div class="right" @click="goApplyList('')">查看全部</div>
        </div>
        <div class="mine-content flex ">


          <div class="mine-content-item color4" @click="goApplyList('15')">
            <div>{{ homeCount?.submittedCount || 0 }}</div>
            <div style="font-family: Open Sans, Open Sans;font-size: 14px; color: #000;">待占预算</div>
          </div>

          <div class="mine-content-item color2" @click="goApplyList('20')">
            <div>{{ homeCount?.processCount || 0 }}</div>
            <div style="font-family: Open Sans, Open Sans;font-size: 14px; color: #000;">审批中</div>
          </div>



          <div class="mine-content-item color3" @click="goApplyList('30')">
            <div>{{ homeCount?.effectiveCount || 0 }}</div>
            <div style="font-family: Open Sans, Open Sans;font-size: 14px; color: #000;">已生效</div>
          </div>

          <div class="mine-content-item color1" @click="goApplyList('25')">
            <div>{{ homeCount?.pendingCount || 0 }}</div>
            <div style="font-family: Open Sans, Open Sans;font-size: 14px; color: #000;">待生效</div>
          </div>

        </div>
        <van-button class="mt-10" type="primary" @click="goToBook" round style="width: 100%;">创建就餐申请</van-button>
      </div>

      <!-- 餐厅预订 -->
      <van-form @submit="onSubmit" @failed="onFailed">
        <van-cell-group class="">
          <van-cell title="餐厅预订" style="font-size: 14px; color: #000; font-weight: bold;"></van-cell>
          <!-- <van-field autocomplete="off" readonly >
            <template #label>
              <div style="font-size: 14px; color: #000; font-weight: bold;">餐厅预订</div>
            </template>
             <template #input>
              <div style="font-size: 12px; color: #FF5533; ">部分餐厅仅支持团购券消费，单点部分需要个人付款，请注意甄别</div>
            </template> 
          </van-field> -->

          <!-- <van-field autocomplete="off" input-align="right" error-message-align="right" v-model.trim="form.mobile"
            :rules="[{ required: true, message: '请输入手机号' }, { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式错误！' }]"
            required type="tel" label="联系方式" placeholder="请填写手机号" /> -->

          <van-field autocomplete="off" required readonly input-align="right" error-message-align="right" label="订餐类型"
            placeholder="请选择订餐类型" >

            <template #input>
              <van-radio-group class="flex" v-model="form.sceneType">
                <van-radio :name="1" class="my-radio">
                  <template #icon="{ checked }">
                    <van-button class="btn-com btn " size="small" round
                      :type="checked ? 'primary' : 'default'">宴请</van-button>
                  </template>
                </van-radio>
                <van-radio :name="2" class="my-radio ml-5">
                  <template #icon="{ checked }">
                    <van-button class="btn-com btn " size="small" round
                      :type="checked ? 'primary' : 'default'">外卖</van-button>
                  </template>
                </van-radio>
              </van-radio-group>
            </template>

          </van-field>

          <van-field autocomplete="off" required readonly is-link input-align="right" error-message-align="right"
            @click="choseApply" label="申请单" placeholder="请选择申请单" :rules="[{ required: true, message: '请选择申请单' }]"
            v-model="form.externalApplyNo" />




          <van-cell title="预计就餐日期" value-class="large-value">
            <template #title>
              <div class="flex justify-content-between " :class="form.externalApplyNo ? 'color-eee' : ''"
                style="width: 100%;">
                <div>预计就餐日期</div>
                <div class="font-size-10" v-if="form.estimatedMealTimeStart && form.estimatedMealTimeEnd"> {{
                  form?.estimatedMealTimeStart }} 至 {{ form?.estimatedMealTimeEnd }}</div>
              </div>
            </template>
          </van-cell>

          <!-- <van-field autocomplete="off" :disabled="form.externalApplyNo ? true : false" readonly is-link
            input-align="right" error-message-align="right" label="就餐日期" placeholder="就餐日期"
            @click="openTimePicker('begin')" :value="`${form.estimatedMealTimeStart}-${form.estimatedMealTimeEnd}`" /> -->


          <van-field label="就餐城市" :disabled="form.externalApplyNo ? true : false"
            @click.stop="showCityPop(form.mealLocationCityCode)" readonly is-link name="cityCode" placeholder="请选择"
            input-align="right" v-model="form.mealLocationCity"></van-field>



          <van-field autocomplete="off" readonly label-align="top">

            <template #input>
              <div class="home-main-btns">
                <van-button round class="btn mr-10" size="small" plain type="primary" :loading="searchLoading"
                  @click="goToMt">仅查询</van-button>
                <van-button round class="btn" size="small" type="primary" :loading="payLoading"
                  native-type="submit">预订</van-button>
              </div>
            </template>
          </van-field>

        </van-cell-group>
      </van-form>

    </div>

    <!-- 底部 -->
    <van-tabbar v-model="active" route class="tabbar-bottom">
      <van-tabbar-item replace to="/banquet/home">
        <span>首页</span>
        <template #icon="props">
          <img :src="props.active ? bookIcon.active : bookIcon.inactive" />
        </template>
      </van-tabbar-item>

      <van-tabbar-item replace to="/banquet/mine">
        <span>我的</span>
        <template #icon="props">
          <img :src="props.active ? orderIcon.active : orderIcon.inactive" />
        </template>
      </van-tabbar-item>
    </van-tabbar>

    <!-- 申请单选择 -->
    <van-popup v-model:show="showApplyPicker" round position="bottom">
      <van-picker title="选择申请单" :columns="columnsApplyList" :columns-field-names="{ text: 'orderCode', value: 'id' }"
        @cancel="showApplyPicker = false" @confirm="onConfirmApply">
        <template #option="{ orderCode, budgetAmount }">
          <div>
            <div>{{ orderCode }} <span style="color:red; ">¥{{ budgetAmount }}</span></div>
          </div>

        </template>
      </van-picker>
    </van-popup>

    <!-- 申请类型选择 -->
    <van-popup v-model:show="showTypePicker" round position="bottom">
      <van-picker title="选择订餐类型" :columns="columnsCl" :columns-field-names="{ text: 'label', value: 'value' }"
        @cancel="showTypePicker = false" @confirm="onConfirmCl" />
    </van-popup>

    <!-- 温馨提示 -->

    <van-dialog v-model:show="showTips" style="padding: 10px;" title="温馨提示">
      <div class="info-dialog" v-html="homeTips?.content">
      </div>

      <template #footer>
        <van-row class="info-row" justify="center">
          <van-col :span="18">
            <van-button style="width: 100%;" class="info-btn" @click="hideTips" round type="primary">我已知晓</van-button>
          </van-col>
        </van-row>
      </template>
    </van-dialog>

    <!-- 就餐日期选择 -->
    <van-popup v-model:show="showRqPicker" round position="bottom">
      <van-date-picker v-model="currentRq" title="选择日期" :min-date="minDate" :max-date="maxDate" @confirm="confirmRq"
        @cancel="showRqPicker = false" />
    </van-popup>

    <!-- 1、选择城市管控 -->

    <!-- 城市选择 -->
    <van-popup v-model:show="showCityPicker" position="bottom">
      <van-cascader v-model="chosedCity" :show-header="false" :options="cityDict?.children"
        :field-names="{ text: 'name', value: 'id', children: 'children' }" @finish="finishCityChose" />
    </van-popup>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch, reactive, computed } from 'vue';
import { MMeiTuanReqIo, BNotification, BPoliciesRes, BHomepagePicRes, BHomeGetCountRes, BanquetApplicationTypeEnum } from '@haierbusiness-front/common-libs';
import { banquetApi, cityApi } from '@haierbusiness-front/apis';

import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute, getEnumOptions, guid } from '@haierbusiness-front/utils';
import dayjs from 'dayjs';
import axios from 'axios'
import { showDialog } from 'vant';
import { showSuccessToast, showFailToast } from 'vant';

// import { useAppStore } from "@/store"




const store = applicationStore();

const { loginUser } = storeToRefs(store);

const router = getCurrentRouter();

const route = ref(getCurrentRoute());

const active = ref(0);

const bookIcon = {
  active: new URL('@/assets/image/banquet/home/<USER>', import.meta.url).href,
  inactive: new URL('@/assets/image/banquet/home/<USER>', import.meta.url).href,
};
const orderIcon = {
  active: new URL('@/assets/image/banquet/home/<USER>', import.meta.url).href,
  inactive: new URL('@/assets/image/banquet/home/<USER>', import.meta.url).href,
};

const form = ref<MMeiTuanReqIo>({
  name: loginUser.value?.nickName, //联系人名称,
  mobile: loginUser.value?.phone, //联系人电话
  sceneType: 1
})


const showDcType = () => {
  // if (form.value.externalApplyNo) {
  //   return
  // }
  showTypePicker.value = true
}

// 获取城市id
const getCityIdBycode = (code?: string | number) => {
  if (!code) {
    return ''
  }
  let id = ''
  cityDict.value.children?.forEach(province => {
    province?.children?.forEach(city => {
      if (city?.providerMapList && city?.providerMapList.length > 0) {
        if (city?.providerMapList[0]?.districtId == code) {
          id = city.id
        }
      }
    });
  });
  return id
}

const choseApply = () => {
  if (columnsApplyList.value && columnsApplyList.value.length > 0) {
    showApplyPicker.value = true
  } else {
    showDialog({
      message: '您暂无申请单',
      title: '提示',
      confirmButtonText: '去创建',
      closeOnClickOverlay: true
    }).then(() => {
      router.push('/banquet/book');
    });
  }
}

// 城市选择相关
const chosedCity = ref();
const showCityPicker = ref<boolean>(false);
const showCityPop = (code?: string | number) => {
  if (form.value.externalApplyNo) {
    return
  }
  // 根据 code 获取城市id
  chosedCity.value = getCityIdBycode(code)
  showCityPicker.value = true;
};
const finishCityChose = ({ selectedOptions }: any) => {
  console.log('1111', selectedOptions);
  form.value.mealLocationProvinceCode = selectedOptions[0].providerMapList[0].districtId;
  form.value.mealLocationProvince = selectedOptions[0].providerMapList[0].districtName;

  form.value.mealLocationCityCode = selectedOptions[selectedOptions.length - 1].providerMapList[0].districtId;
  form.value.mealLocationCity = selectedOptions[selectedOptions.length - 1].providerMapList[0].districtName;

  form.value.mealLocationCityAreaCode = selectedOptions[selectedOptions.length - 1].areaCode;

  form.value.lng = selectedOptions[selectedOptions.length - 1].lng
  form.value.lat = selectedOptions[selectedOptions.length - 1].lat

  showCityPicker.value = false;
};
const policies = ref<BPoliciesRes>()
// 获取当前用户受管控信息
const getPolicies = async () => {
  policies.value = await banquetApi.getPolicies()
}
// 获取美团城市列表
const cityDict = ref<Array<any>>([])
const getCityList = async () => {

  getPolicies()

  const params = {
    id: 1,
    providerCode: 'MT',
    subdistrict: 2
  }
  cityDict.value = await cityApi.getCityTree(params)
  
  if (!policies.value?.restrictedCity || policies.value?.restrictedCity?.length == 0) {
    return 
  }
  // 所有省份数组
  const priceArray = policies.value?.restrictedCity?.map(res => {
    return res.mealLocationProvinceCode
  })
  // 所有市区数组
  const cityArray = policies.value?.restrictedCity?.map(res => {
    return res.policyObjectCode
  })
  cityDict.value.children = cityDict.value.children?.filter(item => {
    return item?.providerMapList && item?.providerMapList.length ? priceArray?.includes(item?.providerMapList[0]?.districtId) : ''
  })
  // 根据管控信息 展示 选择城市
  setDisabledCity(cityDict.value.children, cityArray)
}

const setDisabledCity = (list, cityList) => {

  list?.forEach(item => {
    let temp = []
    item?.children.forEach(item2 => {
      if (cityList?.includes(item2?.providerMapList[0]?.districtId)) {
        temp.push(item2)
      }
    })
    item.children = temp
  })

}
// 获取通知通告列表
const noticeList = ref<Array<BNotification>>([])
const getNoticeList = () => {
  const params = {
    "pageNum": 1,
    "pageSize": 5,
  }
  banquetApi.getNotification(params).then(res => {
    noticeList.value = res.records
  })
}

// 跳转通知详情
const goToNewsDetail = (id: string | number) => {
  router.push({
    name: 'newsDetail',
    query: {
      id
    }
  })
}

// 获取首页图片
const urlList = ref<Array<string>>([])
const showSwipeLoading = ref(false)
const getHomePagePic = () => {
  showSwipeLoading.value = true
  banquetApi.getHomepagePic().then(res => {
    
    urlList.value = []
    // 获取首页轮播图
    const contentList = res?.map((item:BHomepagePicRes) => item.content)
    contentList.forEach(item => {
      if (item) {
        urlList.value.push(JSON.parse(item)[0].url)
      }
    })
    showSwipeLoading.value=false
  })
}



// 获取首页数量
const homeCount = ref<BHomeGetCountRes>()
const getHomeCount = () => {

  banquetApi.getCount().then(res => {
    homeCount.value = res
  })
}

// 获取温馨提示
const showTips = ref<boolean>(false)
const homeTips = ref<BHomepagePicRes>()
const getHomeTips = () => {

  banquetApi.getTips().then(res => {
    homeTips.value = res
    if (sessionStorage.getItem('homeTips')) {
      return
    }

    showTips.value = true
  })
}
const hideTips = () => {
  sessionStorage.setItem('homeTips', '1')
  showTips.value = false
}
// 申请单选择弹窗
const showApplyPicker = ref<boolean>(false);
const columnsApplyList = ref<any>([])

watch(
  () => form.value.sceneType,
  (newVal,oldVal) => {

    form.value = {
      name: loginUser.value?.nickName, //联系人名称,
      mobile: loginUser.value?.phone, //联系人电话
      sceneType: newVal
    }
    
    getApplyList()
  }
)

const getApplyList = () => {
  const params = {
    type: form.value.sceneType
  }
  banquetApi.homeApplyList(params).then(res => {
    columnsApplyList.value = res
  })
}
const onConfirmApply = ({ selectedOptions }: any) => {
  showApplyPicker.value = false;
  // 城市
  form.value.mealLocationCity = selectedOptions[0].mealLocationCity;
  form.value.mealLocationCityCode = selectedOptions[0].mealLocationCityCode;

  // 就餐日期
  form.value.estimatedMealTimeStart = selectedOptions[0].estimatedMealTimeStart && selectedOptions[0].estimatedMealTimeStart.length >= 18 ? selectedOptions[0].estimatedMealTimeStart.substring(0, 10) : selectedOptions[0].estimatedMealTimeStart
  form.value.estimatedMealTimeEnd = selectedOptions[0].estimatedMealTimeEnd && selectedOptions[0].estimatedMealTimeEnd.length >= 18 ? selectedOptions[0].estimatedMealTimeEnd.substring(0, 10) : selectedOptions[0].estimatedMealTimeEnd

  // 订餐类型
  // form.value.sceneType = selectedOptions[0].sceneType;

  form.value.applyNo = selectedOptions[0].mtApplyCode;
  form.value.externalApplyNo = selectedOptions[0].orderCode;
  form.value.restaurantId = selectedOptions[0].restaurantId || '';


};

// 申请类型选择弹窗
const showTypePicker = ref<boolean>(false);
const columnsCl = computed(() => {
  return getEnumOptions(BanquetApplicationTypeEnum, true);
});
const onConfirmCl = ({ selectedOptions }: any) => {
  showTypePicker.value = false;
  form.value.sceneType = selectedOptions[0].value;
  form.value.applicationTypeText = selectedOptions[0].label;

  form.value.mealLocationCity =''
  form.value.mealLocationCityCode =''
  form.value.estimatedMealTimeStart = ''
  form.value.estimatedMealTimeEnd =''
  form.value.applyNo =''
  form.value.externalApplyNo = ''
  form.value.restaurantId = ''

  getApplyList()
};

// 就餐日期
const showRqPicker = ref<boolean>(false);
const minDate = ref(new Date());
const maxDate = ref(new Date(2025, 10, 1));
const currentRq = ref<Array<string>>([]);
const confirmRq = ({ selectedValues }: any) => {
  form.value.estimatedMealTimeStart = selectedValues.join('-');
  showRqPicker.value = false;
};

const openTimePicker = (type: string) => {
  if (form.value.externalApplyNo) {
    return
  }
  currentRq.value = []
  currentRq.value = form.value.estimatedMealTimeStart?.split('-');
  showRqPicker.value = true;
}

// 跳转申请页
const goToBook = () => {
  localStorage.removeItem('banquet_book_form')
  router.push({
    name: 'book'
  })
}

onMounted(() => {
  getApplyList()
  getNoticeList()
  getHomePagePic()
  getHomeCount()
  getHomeTips()
  getCityList()
  // getHomeTips()
});

// 根据restaurantId查询详情
const getRestaurantDetail = (id: string) => {

  return banquetApi.getRestaurant({
    restaurantId: id
  })
}
const searchLoading = ref(false)
const payLoading = ref(false)

// 仅查询
// 首页仅查询跳转美团不需要传budgetKey
// 默认 类型 STAFF_CY
const goToMt = () => {
  if (form.value.sceneType == 1) {
    showDialog({
      message: '部分餐厅仅支持团购券消费，单点部分需要个人付款，请注意甄别',
    }).then(async () => {
      onlySearch()
    })
  }else {
    onlySearch()
  }
  
}

// 仅查询
const onlySearch = () => {
  localStorage.removeItem('banquet_book_form')

  searchLoading.value = true
  const params = {
    type: form.value.sceneType == 2 ? 'STAFF_WM_SELECT' : 'STAFF_CY',
    bizParam: {
      // 生成唯一uuid
      // budgetKey: `mo-${guid()}`,
      // restaurantType: '1',
      // lockCityType: 1
    }
  }
  if (form.value.lng) {
    params.bizParam.location = {
      longitude: form.value.lng,
      latitude: form.value.lat,
      geotype: 'gcj02', // gcj02
      address: form.value.mealLocationProvince + form.value.mealLocationCity
    }
  }
  banquetApi.clientInvokeLogin(params).then(res => {
    setTimeout(() => {
      searchLoading.value = false
    }, 3000);
    window.location.href = res
  }).catch(err => {
    setTimeout(() => {
      searchLoading.value = false
    }, 3000);
  })
}

// 预订支付
const onSubmit = async () => {
  if (form.value.sceneType == 1) {
    showDialog({
      message: '部分餐厅仅支持团购券消费，单点部分需要个人付款，请注意甄别',
    }).then(async () => {
      book()
    });
  }else {
    book()
  }
};

// 预定方法
const book = async() => {
  payLoading.value = true
    let location = {
      longitude: '',
      latitude: ''
    }
    // 如果选择了城市 用选择城市的地址
    if (form.value.lng) {
      params.bizParam.location = {
        longitude: form.value.lng,
        latitude: form.value.lat,
        geotype: 'gcj02', // gcj02
        // address: '山东省德州市陵城区'
        address: form.value.mealLocationProvince + form.value.mealLocationCity
      }
    }

    // 如果申请单已经选择餐厅,查询餐厅详情获取经纬度信息
    let restaurantDetail = {}
    if (form.value.restaurantId) {
      restaurantDetail = await getRestaurantDetail(form.value.restaurantId)
      location = {
        longitude: restaurantDetail?.longitude,
        latitude: restaurantDetail?.latitude,
        geotype: 'gcj02', // gcj02
        address: restaurantDetail?.restaurantProvinceName + restaurantDetail?.restaurantCityName + restaurantDetail?.restaurantDistrictName + restaurantDetail?.restaurantAddress
      }
    }

    const params = {
      type: form.value.sceneType == 1 ? 'STAFF_INFO' : 'STAFF_WM',
      bizParam: {
        // budgetKey: `mo-${crypto.randomUUID()}`,
        restaurantType: form.value.restaurantId ? 2 : 1,
        restaurantId: form.value.restaurantId || '',
        repastApplyExtraJson: {
          applyNo: form.value.applyNo,
          externalApplyNo: form.value.externalApplyNo
        },
        location: location
      }
    }
    restaurantDetail?.budgetKey ? params.bizParam.budgetKey = restaurantDetail?.budgetKey : ''

    setTimeout(() => {
      payLoading.value = false
    }, 3000);

    let url = await banquetApi.clientInvokeLogin(params)
    window.location.href = url
};

const onFailed = (values) => {
  // scrollToErrorField()
}

const goBack = () => {
  router.back(-1);
};

// 跳转申请单列表
const goApplyList = (val: number | string) => {
  router.push({
    name: 'applyList',
    query: {
      status: val
    },
  })
};

</script>

<style lang='less' scoped>
@import url(./common.less);

.home {
  background: #FAFBFD;
  position: relative;
  background-image: url('@/assets/image/banquet/home/<USER>');
  background-repeat: no-repeat;
  background-size: contain;
}

.tabbar-bottom {
  z-index: 11;
}

.top-bg-color {
  background-color: #2781FF;
  color: #fff;
  z-index: 11;

  :deep(.van-nav-bar__title) {
    color: #fff;

  }

}

.home-top-bg {
  position: absolute;
  z-index: 2;
  width: 100%;
  height: 75px;
  background-color: #2781FF;
  border-radius: 0px 0px 20px 20px;
}

.mt-2 {
  margin-top: 2px
}

.home-main {
  position: relative;
  z-index: 10;
  width: 100vw;
  min-height: 100vh;
  padding: 12px 10px 40px;

  .home-main-img {
    margin: 0 auto;
    width: 100%;
    height: 95px;
    display: flex;
  }

  .home-main-notice {

    width: 100%;
    height: 40px;
    background: #FFFFFF;
    box-shadow: 0px 8px 8px 0px rgba(0, 0, 0, 0.1);
    border-radius: 10px 10px 10px 10px;
    padding: 0 10px;
    margin-top: 10px;

    .left {
      height: 100%;
      width: 80px;

      .notice-icon {
        width: 20px;
        height: 20px;
        background-image: url('@/assets/image/banquet/home/<USER>');
        background-size: cover;
        background-size: 100% 100%;
        margin-right: 3px;
      }

      .notice-text {
        color: rgba(38, 38, 38, 1);
      }

      .left-text {
        display: flex;
        justify-content: space-between;
        flex: 1;
        width: 100%;
        font-size: 13px;

        .color-blue {
          color: #2681FF;
        }
      }
    }

    .middle {
      height: 100%;
    }

    .right {
      // margin-left: 10px;
      flex: 1;
    }
  }

  .home-mine {
    margin-top: 10px;

    .mine-title {
      justify-content: space-between;
      align-items: center;
      height: 26px;

      .left {
        font-family: 'Open Sans-Bold';
        font-weight: bold;
        font-size: 12px;
      }

      .right {
        font-size: 10px;
        font-weight: 400;
        font-family: Open Sans, Open Sans;
        color: #2681FF;
      }
    }

    .mine-content {
      width: 100%;
      height: 60px;
      align-items: center;
      justify-content: space-between;

      .mine-content-item {
        width: 23%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        font-weight: bold;
        font-size: 26px;
      }

      .color1 {

        // background-color: rgba(239, 244, 255, 1);
        background: linear-gradient(to bottom, rgba(236, 242, 255, 1), rgba(247, 250, 255, 1));
        color: #2681FF;
      }

      .color2 {
        background: linear-gradient(to bottom, rgba(255, 247, 236, 1), rgba(255, 252, 247, 1));
        color: rgba(253, 181, 0, 1);
      }

      .color3 {
        background: linear-gradient(to bottom, rgba(234, 253, 255, 1), rgba(246, 255, 255, 1));
        color: rgba(65, 217, 165, 1);
      }

      .color4 {
        background: linear-gradient(to bottom, rgba(239, 236, 255, 1), rgba(248, 247, 255, 1));
        color: rgba(150, 129, 232, 1);
      }
    }
  }

  .home-main-btns {
    margin-left: 30%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .btn {
      width: 45%;
    }
  }
}

@import url(./common.less);

.reservation-content {
  margin-top: 36px;
  background: #f8f8f8;
}

.submit-dialog-content {
  padding: 20px;
  color: red;
  display: flex;
}

:deep(.van-field__label--required:before) {
  position: absolute;
  left: 6px;
}

:deep(.van-hairline--bottom:after) {
  border-bottom-width: 0;
}

:deep(.van-divider--vertical:before) {
  border-width: 0 0 0 3px;

}

:deep(.van-swipe-item) {
  overflow: hidden;
}

.action-main {
  padding: 20px 20px 20px;
}

.info-dialog {
  width: 100%;
  margin: 10px 0;
  padding: 0 10px;
  height: 240px;
  overflow-y: scroll;
  color: #7f7e82;

  :deep(img) {
    max-width: 100%;
  }
}

:deep(.van-dialog__header) {
  padding-top: 10px;
}

.color-eee {
  color: #c8c9cc;
}

.notice-empty {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

</style>