<script setup lang="ts">
name: 'VisibleTable';
import { reactive } from 'vue';
import type { VxeGridProps } from 'vxe-table';
let props = defineProps({
  gridOptions: {
    type: Object,
    default() {
      return {};
    },
  },
});
const gridOptions = reactive<VxeGridProps>(props.gridOptions);
</script>
<template>
  <div class="tableC">
    <vxe-grid
      size="mini"
      :auto-resize="true"
      :min-height="0"
      :column-config="{
        useKey: true,
        resizable: true,
      }"
      :row-config="{
        useKey: true,
      }"
      class="mygrid-style"
      v-bind="gridOptions"
    >
    </vxe-grid>
  </div>
</template>
<style lang="less" scoped>
.tableC {
  padding: 20px;
  padding-top: 0;
  width: 100%;
  height: 100%;
}
:deep(.vxe-header--row) {
  background: #fff;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #86909c;
  line-height: 20px;
  text-align: left;
  font-style: normal;
}
:deep(.vxe-cell--label) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  // color: #1d2129;
  line-height: 20px;
  padding: 0 11px;
  text-align: left;
  font-style: normal;
}
:deep(.vxe-header--column) {
  padding: 0 11px;
}
::v-deep(.mygrid-style) {
  // .full-border {
  //   border-right: 1px solid #e9eaec;
  // }
  .filed-first2 {
    border-left: 1px solid #e9eaec;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-size: 14px;
    color: #86909c;
  }
  .filed-first3 {
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-size: 14px;
    color: #86909c;
  }
  .filed-first4 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    // color: #1d2129;
    line-height: 20px;
    // padding: 0 11px;
    text-align: left;
    font-style: normal;
    color: #007aff;
    cursor: pointer;
    /* text-decoration: underline; */
  }
  .filed-first {
    border-left: 1px solid #e9eaec;
    padding: 0 11px;
    text-align: right;
    font-family: PingFangSC, PingFang SC;
    font-size: 14px;
    color: #86909c;
  }
  .vxe-cell--html {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    height: 100%;
    padding-left: 11px;
  }
  .row-bg1 {
    text-align: left;
    background-color: #f2f3f5;
    color: #1d2129 !important;
  }
  .cell-bg1 {
    background-color: #f1faee;
    color: #1d2129 !important;
  }
  .cell-bg2 {
    background-color: #fff9ed;
    color: #1d2129 !important;
  }
  .cell-bg3 {
    background-color: #fff1f0;
    color: #1d2129 !important;
  }
}
</style>
