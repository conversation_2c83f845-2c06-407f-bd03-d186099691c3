<template>
  <div class="merchant-freeze-detail">
    <!-- 页面标题 -->
    <div class="page-header" v-if="record?.title">
      <h3>{{ record.title }}</h3>
    </div>

    <div class="table-container">
      <a-spin :spinning="loading" tip="正在加载数据...">
        <a-table
          :columns="statusChangeColumns"
          :data-source="tableData"
          :pagination="false"
          size="small"
          :bordered="true"
          :scroll="{ x: 'max-content' }"
          :rowKey="(record: any) => record.id"
          class="freeze-detail-table"
        >
          <template #emptyText>
            <div style="text-align: center; padding: 40px">
              <div>暂无数据</div>
            </div>
          </template>
        </a-table>
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, h, inject, type Ref } from 'vue';
import { useRoute } from 'vue-router';
import { resolveParam, routerParam } from '@haierbusiness-front/utils';
import { serviceProviderApi } from '@haierbusiness-front/apis';
import { Spin as aSpin, Table as aTable, Tag, Button as hButton } from 'ant-design-vue';
import {
  getFreezeTypeInfo,
  ServiceProviderFreezeStatusEnum,
  ServiceProviderFreezeStatusMap,
} from '@haierbusiness-front/common-libs';

const statusChangeColumns = [
  {
    title: '类型',
    dataIndex: 'freezeType',
    width: '100px',
    ellipsis: true,
    customRender: ({ text }: { text: number }) => {
      if (text === undefined || text === null) return '-';
      const typeInfo = getFreezeTypeInfo(text);
      return typeInfo ? typeInfo.name : '-';
    },
  },
  {
    title: '理由',
    dataIndex: 'freezeReason',
    width: '100px',
    ellipsis: true,
  },
  {
    title: '见证性材料',
    dataIndex: 'attachmentFiles',
    width: '100px',
    customRender: ({ text }: { text: any[] }) => {
      if (!text || !Array.isArray(text) || text.length === 0) return '-';
      return h(
        'div',
        {
          class: 'attachment-list',
        },
        text.map((attachment, index) =>
          h(
            hButton,
            {
              type: 'link',
              size: 'small',
              key: index,
              onClick: () => {
                if (attachment && attachment.path) {
                  window.open(attachment.path, '_blank');
                }
              },
            },
            () => `附件${index + 1}`,
          ),
        ),
      );
    },
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '100px',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '100px',
    ellipsis: true,
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
    width: '100px',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '100px',
    customRender: ({ text }: { text: number }) => {
      if (text === undefined || text === null) return '-';
      return ServiceProviderFreezeStatusMap[text as ServiceProviderFreezeStatusEnum] || '-';
    },
  },
];
// 类型定义
interface PageRecord {
  id?: string | number;
  title?: string;
  hideBtn?: string;
}

const route = useRoute();
const recordParam = route.query.record as string;
const record: PageRecord | null = recordParam ? resolveParam(recordParam) : null;
const hideBtn = record?.hideBtn || '';

// 获取并设置frameModel
const frameModel = inject<Ref<number>>('frameModel');
if (frameModel) {
  frameModel.value = hideBtn === '1' ? 1 : 0;
}

// 表格数据
const tableData = ref<any[]>([]);
// 加载状态
const loading = ref(false);

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
});

// 表格变化处理
const handleTableChange = (paginationInfo: any) => {
  pagination.value.current = paginationInfo.current;
  pagination.value.pageSize = paginationInfo.pageSize;
  loadData();
};

// 数据加载
const loadData = async (): Promise<void> => {
  loading.value = true;
  try {
    if (!record?.id) {
      tableData.value = [];
      return;
    }

    const response = await serviceProviderApi.getFreezeDetail(Number(record.id));

    if (response) {
      // 检查响应是否包含 merchantFreezeRecords 数组
      if ((response as any).merchantFreezeRecords && Array.isArray((response as any).merchantFreezeRecords)) {
        tableData.value = (response as any).merchantFreezeRecords;
      } else {
        // 如果响应是单个冻结记录对象，检查是否包含冻结记录的特征字段
        if ((response as any).freezeType !== undefined || (response as any).freezeReason !== undefined) {
          tableData.value = [response];
        } else {
          tableData.value = [];
        }
      }
    } else {
      tableData.value = [];
    }
  } catch (error) {
    console.error('加载商户冻结详情失败:', error);
    tableData.value = [];
  } finally {
    loading.value = false;
  }
};

const initData = (): void => {
  if (record?.id) {
    loadData();
  } else {
    console.warn('未找到有效的记录ID');
  }
};

const refreshData = (): void => {
  initData();
};

defineExpose({
  refreshData,
});

onMounted(() => {
  initData();
});
</script>

<style lang="scss" scoped>
.merchant-freeze-detail {
  padding: 20px;
  background: #fff;
  border-radius: 8px;

  .page-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e4e7ed;

    h3 {
      margin: 0;
      color: #303133;
      font-size: 18px;
      font-weight: 600;
    }
  }

  .table-container {
    position: relative;

    .freeze-detail-table {
      :deep(.ant-table-thead > tr > th) {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 600;
        text-align: center;
      }

      :deep(.ant-table-tbody > tr > td) {
        text-align: center;
        vertical-align: middle;
      }

      :deep(.ant-table-tbody > tr:hover > td) {
        background-color: #f5f7fa;
      }

      :deep(.ant-table-placeholder) {
        border: none;
      }

      :deep(.ant-table-cell) {
        padding: 12px 8px;
      }
    }

    .attachment-list {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }
  }
}
</style>
