<template>
    <div style="height: 33vh" background="rgba(0,0,0,0)">
        <Rank :data="rankData" unit="人次" />
    </div>
</template>
<script setup lang="ts">
import Rank from "../../../components/rank.vue";
import { queryTaxiCityRank } from "@haierbusiness-front/apis/src/data/board/travel";
import { ref, onMounted } from "vue";
import { EventBus } from "../../../eventBus";
const rankData = ref([]);
const loading = ref(false);
EventBus.on((event, params) => {
    if (event == "refresh") {
        if (!params) queryData();
        if (params) queryData(params);
    }
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
    loading.value = true;
    const data = await queryTaxiCityRank(
        params ? params.data.name : null,
        params ? params.from : null
    );
    loading.value = false;
    const rows: any = [];
    data.rows.forEach((item) => {
        rows.push({
            name: item[0],
            value: item[1],
        });
    });
    rows.sort((a, b) => b.value - a.value);
    rankData.value = rows;
};
onMounted(() => {
    queryData()
})
</script>
