<script setup lang="ts">
import { onMounted, ref, computed } from "vue";
import { MerchantTypeConstant,ITriveFilter } from "@haierbusiness-front/common-libs";
import { travelSummaryApi } from "@haierbusiness-front/apis";
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
const store = applicationStore();

const { loginUser } = storeToRefs(store);
const avatar = ref<string>("")
const travelHabitShow = ref<boolean>(true)
const isTravelHabitShowUserList = ref([
  '00880367','00910069','00920102'
])

const travelSummary = ref<ITriveFilter>({})
// 获取报表数据
const getReport = ()=>{
  travelSummaryApi.getReport({username:loginUser.value.username}).then((res:ITriveFilter)=>{
    travelSummary.value = res
  })
}
// 获取登录人ihaier头像
const getUserAvatar = ()=>{
  travelSummaryApi.getUserAvatar({username:loginUser.value.username}).then((res:any)=>{
      avatar.value = res.avatar?.avatar_240
  })
}

// 是否已读
const readNotify= (username?:string)=>{
  travelSummaryApi.readNotify({username:loginUser.value.username}).then((res:any)=>{
      avatar.value = res.avatar?.avatar_240
  })
}

onMounted(()=>{
  if(isTravelHabitShowUserList.value.includes(loginUser.value.username)){
      travelHabitShow.value = false
    }
  getReport()
  getUserAvatar()
  readNotify()
})
</script>

<template>
  <div class="container">
    <div class="contentBox">
        <div class="title">2024年员工个人年度差旅大揭秘</div>
        <div class="userInfoBox"> 
          <div class="userImgBox">
            <img :src="avatar" alt="">
          </div>
           @{{ loginUser?.nickName }}
        </div>
        <div class="infoBox">
          <!-- 集团差旅业务总结 -->
            <p class="title">
              1、集团差旅业务总览
            </p>
            <div class="paragraph">
              <p>2024年，海尔商旅火力全开，为集团的小伙伴们安排了 <span class="tip">{{ travelSummary.travelCount }}</span> 次说走就走的出差之旅！</p>
              <p>机票预订量狂飙至 <span class="tip">{{ travelSummary.planeCount }}</span>次，酒店预订更是高达<span class="tip">{{ travelSummary.hotelCount }}</span>，打车服务也轻松破<span class="tip">{{ travelSummary.taxiCount }}</span>次，火车票预订也是稳稳地<span class="tip">{{ travelSummary.trainCount }}</span>张。
              海尔人的脚步遍布全国<span class="tip">{{ travelSummary.provinceCount }}</span>个城市，还跨出国门，游历了全球<span class="tip">{{ travelSummary.countryCount }}</span>个国家。</p>
              <p>
                每一次出行，都是海尔人对工作的热爱与品质追求的完美诠释。商务服务平台商旅链群统一集采差旅资源，实现资源最大化利用，全年节省<span class="tip">{{ travelSummary.saveAmount }}</span>！更有免费机场休息区、优先选座特权、因私出行专区、特价停车场等贴心服务，让您的差旅生活更加惬意。
              </p>
            </div>
           <!-- 2、个人年度差旅行为报告 -->
           <p class="title">
              2、个人年度差旅行为秀
            </p>
            <div class="paragraph">
              <p>
                差旅行为大起底！<span v-if="travelHabitShow">您是<span class="tip">{{ travelSummary.travelHabit }}</span>！</span>数据显示，您提前4天创建出差申请单的占比高达<span class="tip">{{ travelSummary.advanceRate }}</span>%。<br>虽然临时出差在所难免，但提前规划能让您的旅途更加从容。商务服务平台智慧大脑系统时刻在线，记录您的差旅行为，分析数据，为各领域产业提供降费秘籍，培养合规差旅习惯，助力集团差旅管理升级，实现降本增效。
              </p>
              <p v-if="travelSummary.userSavedAmountAll!='0元'">
                差旅省钱小能手就是您！根据海尔商旅数据，您本年度为集团节省了<span class="tip">{{ travelSummary.userSavedAmountAll }}</span>大洋！<br>其中，酒店合住贡献<span class="tip">{{ travelSummary.userSavedAmountHotel }}</span>，提前4天预订机票节省<span class="tip">{{ travelSummary.userSavedAmountPlane }}</span>。海尔商旅将根据您出行节省金额，按照比例分享至您个人，以此鼓励您出色的差旅行为！
              </p>
              <p v-if="travelSummary.userSavingAmountAll!='0元'">
                不过，您还有<span class="tip">{{ travelSummary.userSavingAmountAll }}</span>的开支可以再省省哦！未提前预订损失<span class="tip">{{ travelSummary.userSavingAmountPlaneAdvance }}</span>，退改签又花了<span class="tip">{{ travelSummary.userSavingAmountHotelModify }}</span>，机票没抢到前后一小时最低价也亏了<span class="tip">{{ travelSummary.userSavingAmountPlaneHour }}</span>。<br>钞票如流水，且省且珍惜！海尔商旅已将您的可再节省打包成差旅报告，发送至部门，赋能差旅管理新高度。
              </p>
            </div>
            <!-- 3、展望未来 -->
            <p class="title">
              3、展望未来
            </p>
            <div class="paragraph">
              <p>
                展望2025年，海尔商旅将继续秉承“以用户为中心”的服务理念，以集团差旅成本合理化和员工服务体验迭代为导向，不断优化和提升商旅各版块的优质服务资源及价格竞争力。我们将一直陪伴着您，为您的每一次出行提供便捷、高效、温馨的服务。
              </p>
              <p>
                感谢您在2024年的辛勤付出与贡献，让我们携手并进，共创海尔更加辉煌的未来！助力海尔再攀高峰！ 
              </p>
            </div>
        </div>
    </div>
  </div>
</template>

<style lang="less">
.container{
    height:100vh;
    background: #F6F7F9;
    overflow: auto;
    display: flex;
    flex-flow: column;
    // font-family: AlibabaPuHuiTiR!important;
    // padding-bottom: 20px;
}

.contentBox {
    flex:1;
    // width: 1200px;
    // margin-top: 30px;
    // height: 100%;
    background: #FFFFFF;
    box-shadow: 0px 4px 16px 0px rgba(0,20,49,0.01);
    border-radius: 12px;
    // margin:32px auto;
    padding:34px 48px;
    .title{
      font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
      font-weight: bold;
      font-size: 34px;
      // color: #3983E5;
      line-height: 44px;
      text-align: left;
      font-style: normal;
      background: -webkit-linear-gradient(82.15024363856253deg, #6BB9F4 0%, #3983E5 00%);
      width: 500px;
      background: linear-gradient(82.15024363856253deg, #6BB9F4 0%, #3983E5 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
    .userInfoBox{
      width: 172px;
      height: 60px;
      background: linear-gradient( 270deg, rgba(194,229,255,0) 0%, 
      rgba(56, 142, 255, 0.17) 100%);
      border-radius: 100px 0px 0px 100px;
      // opacity: 0.17;
      padding-left:6px;
      margin-top:10px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      font-weight: bold;
      font-size: 22px;
      color: #595959;
      line-height: 22px;
      .userImgBox{
        width: 40px;
        height: 40px;
        border-radius: 50%;
        // background: #388EFF;
        margin-right: 10px;
        img{
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
    }
    .infoBox{
      .title{
        margin-top: 28px;
        font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
        font-weight: bold;
        font-size: 28px;
        color: #3983E5;
        line-height: 25px;
        text-align: left;
        font-style: normal;
        margin-bottom: 12px;
      }
      .paragraph{
        margin-bottom: 10px;
        font-family: AlibabaPuHuiTiR;
        font-size: 26px;
        color: #595959;
        line-height: 46px;
        text-align: left;
        font-style: normal;
        letter-spacing: 1.5px;
        p{
          margin-bottom: 16px;
        }
        .tip{
          color: rgba(57, 131, 229, 1);
          font-weight: 600;
          font-size: 28px;
        }
      }
    }
}

.flex {
  display: flex;
}

.pointer {
  cursor: pointer;
}

</style>
