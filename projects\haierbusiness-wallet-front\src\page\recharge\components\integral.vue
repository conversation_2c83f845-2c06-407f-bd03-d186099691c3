<template>
  <div class="wallet">
    <!-- <van-nav-bar
      title="福利积分充值"
      left-text="返回"
      left-arrow
      @click-left="onClickLeft"
      right-text="查询流水"
      @click-right="onClickRight"
    /> -->
    <div class="wallet-nav mt-20 mb-10">
      <van-cell icon="gold-coin-o" title="账户余额">
        <template #value>
          <span style="color: red">{{ amount }}元</span></template
        >
        <template #label>
          <a class="wallet-label" @click="goBill">查询流水</a>
        </template>
      </van-cell>
    </div>
    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <van-field
          v-model="money"
          type="number"
          name="充值金额"
          label="充值金额"
          maxlength="10"
          placeholder="充值金额"
         
          :rules="[{ required: true, message: '请填写充值金额' }]"
        />
      </van-cell-group>
      <div class="wallet-nav mt-20">
        <van-button round block type="primary" native-type="submit" :disabled="!isPay" > 充值 </van-button>
      </div>
    </van-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { payPhoneApi } from "@haierbusiness-front/apis";
import { showFailToast } from 'vant';
import { getCurrentRouter } from "@haierbusiness-front/utils";
const router = getCurrentRouter();
const onClickLeft = () => {
  router.go(-1);
};

const isPay = computed(() => {
  return (money != undefined && money.value != 0 && (money.value! - amount.value <= 0))
})

const goBill = () => {
  router.push("/bill");
};
const money = ref<number>();
const onSubmit = async (values) => {
  let system = navigator.userAgent
  let isAndroid = system.indexOf('Android') > -1 || system.indexOf('Adr') > -1 // android终端
  let isiOS = !!system.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) //ios终端

  const res = await payPhoneApi.coinRechargePrePay({
    amount: money.value!,
    callbackUrl: window.location.origin + "/hbweb/wallet/#/bill",
  });

  if(isiOS) {
    window.location.href = res
  } else {
    window.open(res);
  }
};

const amount = ref(0);
const getCoinAccount = async () => {
  const res = await payPhoneApi.getCoinAccount();
  if (!res) {
    showFailToast({
      message: '您的账户不存在,请前往“iHaier-福利积分开户”开户，并在“福利积分消费”充值“福利超市金币”。',
      iconSize: '36px'
    })
  }
  amount.value = res?.balance;
};

onMounted(() => {
  getCoinAccount();
});
</script>

<style lang="scss" scoped>
.wallet {
  background: #f3f3f3;
  height: 100vh;
  &-label {
    color: #1989fa;
    font-size: 12px;
  }
  &-qrcode {
    display: flex;
    justify-content: center;
    width: 100%;
  }
  &-nav {
    margin: 0 15px 10px;
    border-radius: 10px;
    overflow: hidden;
  }
  &-bg {
    background: url("../../assets/image/blurry-gradient-haikei.png");
    background-size: 100% 100%;
    height: 200px;
    padding-top: 20px;
    // border-bottom-left-radius: 20%;
    // border-bottom-right-radius: 20%;
  }
  &-code {
    background: #fff;
    height: 55vh;
  }

  &-code1 {
    display: flex;
    justify-content: center;
    margin-top: 25px;
    margin-bottom: 20px;
  }
  &-user {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
    color: #fff;
    img {
      width: 70px;
      height: 70px;
      margin-right: 10px;
    }
  }
}
.user {
  &-poster {
    width: 100%;
    height: 53vw;
    display: block;
  }

  &-group {
    margin-bottom: 15px;
  }

  &-links {
    padding: 15px 0;
    font-size: 12px;
    text-align: center;
    background-color: #fff;

    .van-icon {
      display: block;
      font-size: 24px;
    }
  }
}
.mt-20 {
  margin-top: 20px;
}
.mb-10 {
  margin-bottom: 10px;
}
.pb-10 {
  padding-bottom: 10px;
}
</style>

<style>
:root:root {
  --van-toast-icon-size: 72px !important;
  --van-toast-default-width: 176px;
}
</style>