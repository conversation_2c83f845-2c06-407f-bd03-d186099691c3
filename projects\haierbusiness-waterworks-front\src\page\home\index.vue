<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Carousel as hCarousel, Button as hButton, message } from 'ant-design-vue'
import { LeftOutlined, RightOutlined, ShoppingCartOutlined } from '@ant-design/icons-vue'
import { debounce, throttle } from 'lodash'
import FloatButton from '../../components/float-button.vue'
import { useCartStore } from '../../store/goods-cart'
import banner1 from '@/assets/image/banner/1.jpg'
import banner2 from '@/assets/image/banner/2.jpg'
import { waterworkProdApi as api } from '@haierbusiness-front/apis'
const { addToCart } = useCartStore()
const carouselBtnShow = ref(false)
const carouselList = ref([
  {
    id: 1,
    title: '轮播图1',
    url: banner1,
  },
  {
    id: 2,
    title: '轮播图2',
    url: banner2,
  },
])
const goodsList = ref(<any>[])

onMounted(async () => {
  getProdList()
})

const getProdList = async () => {
  let res = await api.listAll({ status: 1 })
  goodsList.value = res
  console.log(res, '产品列表')
}

// 购物车相关
const handleAddCart = (item: any) => {
  message.success('加入成功')
  console.log(item)
  addToCart({
    id: item.id,
    name: item.name,
    price: item.price,
    count: 1,
  })
}
</script>
<template>
  <float-button></float-button>
  <div class="container">
    <div class="row">
      <div class="carousel">
        <h-carousel :autoplay="true" arrows>
          <template #prevArrow>
            <div
              :class="[
                'custom-slick-arrow',
                carouselBtnShow ? 'left-arrow' : 'arrow-hide-left',
              ]"
              style="left: 10px; z-index: 1"
              @mousemove="carouselBtnShow = true"
            >
              <left-outlined :size="12" />
            </div>
          </template>
          <template #nextArrow>
            <div
              :class="[
                'custom-slick-arrow',
                carouselBtnShow ? 'right-arrow' : 'arrow-hide-right',
              ]"
              style="right: 10px; z-index: 1"
              @mousemove="carouselBtnShow = true"
            >
              <right-outlined />
            </div>
          </template>
          <img
            @mousemove="carouselBtnShow = true"
            @mouseleave="carouselBtnShow = false"
            class="carousel-img"
            v-for="item in carouselList"
            :src="item.url"
            :key="item.id"
            alt=""
          />
        </h-carousel>
      </div>
    </div>

    <div class="row hot-goods">
      <div class="card-title">热销商品</div>
      <div class="prod-list">
        <div class="prod-item" v-for="item in goodsList" @click="handleAddCart(item)">
          <div class="prod-img">
            <img class="el-img" :src="item.productUrl" alt="" />
            <div class="cart-btn">
              <ShoppingCartOutlined style="margin-right: 10px" />
              加入购物车
            </div>
          </div>
          <div class="prod-name">{{ item.productNm }}</div>
          <div class="prod-price">
            <div class="price-unit">￥</div>
            <div class="price-value">{{ item.price }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="row company-info">
      <div class="card-title">企业简介</div>
      <div class="company-content">
        <div class="card-left-info">
          <p>
            我们一直以来都以卓越的品质和大胆开创性的尝试，成为业界的标杆式企业。每一个员工的坚守成就了我们几十年的品质。
          </p>
          <h-button type="primary" size="large" ghost
            >查看详情 <RightOutlined
          /></h-button>
        </div>
        <div class="card-right-carousel">
          <h-carousel :autoplay="true" arrows>
            <template #prevArrow>
              <div
                :class="[
                  'custom-slick-arrow',
                  carouselBtnShow ? 'left-arrow' : 'arrow-hide-left',
                ]"
                style="left: 10px; z-index: 1"
                @mousemove="carouselBtnShow = true"
              >
                <left-outlined :size="12" />
              </div>
            </template>
            <template #nextArrow>
              <div
                :class="[
                  'custom-slick-arrow',
                  carouselBtnShow ? 'right-arrow' : 'arrow-hide-right',
                ]"
                style="right: 10px; z-index: 1"
                @mousemove="carouselBtnShow = true"
              >
                <right-outlined />
              </div>
            </template>
            <img
              @mousemove="carouselBtnShow = true"
              @mouseleave="carouselBtnShow = false"
              class="carousel-img"
              v-for="item in carouselList"
              src="../../assets/image/intro.jpg"
              :key="item.id"
              alt=""
            />
          </h-carousel>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  padding-bottom: 85px;
  .row {
    padding: 24px 8% 0;
  }
  .card-title {
    color: #000;
    font-size: 24px;
    font-weight: 600;
    line-height: 20px;
    margin-bottom: 24px;
    display: flex;
    margin-top: 20px;
  }
  .carousel {
    .carousel-img {
      width: 100%;
      height: 330px;
      object-fit: cover;
      border-radius: 12px;
    }
    .custom-slick-arrow {
      width: 32px;
      height: 32px;
      font-size: 16px;
      text-align: center;
      line-height: 32px;
      color: rgba(235, 235, 235, 0.8);
      background-color: rgba(31, 45, 61, 0.15);
      border-radius: 50%;
      opacity: 0.8;
      z-index: 1;
      &:hover {
        background-color: rgba(31, 45, 61, 0.3);
        opacity: 0.8;
      }
    }
    ::before {
      display: none;
    }

    /* For demo */
    :deep(.slick-slide) {
      text-align: center;
      width: 100%;
      height: 320px;
      line-height: 320px;
      background: #364d79;
      border-radius: 12px;
      overflow: hidden;
    }
  }

  .left-arrow {
    animation: show 0.3s ease-in-out;
  }
  .right-arrow {
    animation: right-show 0.3s ease-in-out;
  }

  .arrow-hide-left {
    animation: hide-left 0.3s ease-in forwards !important;
    pointer-events: none; /* 禁止事件 防止动画期间误触 */
  }
  .arrow-hide-right {
    animation: hide-right 0.3s ease-in forwards !important;
    pointer-events: none; /* 禁止事件 防止动画期间误触 */
  }
  .hot-goods {
    .prod-list {
      display: flex;
      flex-wrap: nowrap;
      text-align: center;
      // margin-top: 40px;

      .prod-item {
        width: 20%;
        margin-right: 20px;
        box-sizing: border-box;
        border: 1px solid;
        border-color: #0000;
        cursor: pointer;
        position: relative;

        &:last-of-type {
          margin-right: 0;
        }

        .prod-img {
          width: 100%;
          height: auto;
          aspect-ratio: 1;
          position: relative;
          display: flex;
          border-radius: 8px;
          // border: 1px solid red;

          .el-img {
            width: 100%;
            height: 100%;
            border-radius: 8px;
            object-fit: contain;
            background: #fff;
          }
        }

        .prod-name {
          font-size: 14px;
          line-height: 24px;
          margin-top: 5px;
        }

        .prod-price {
          color: #2a82db;
          display: flex;
          align-items: baseline;
          justify-content: center;

          .price-unit {
            font-size: 14px;
          }

          .price-value {
            font-size: 20px;
            font-weight: 700;
          }
        }

        .cart-btn {
          width: 100%;
          // height: 50px;
          height: 100%;
          display: flex;
          top: 0;
          left: 0;
          align-items: center;
          justify-content: center;
          // background: #2a82db;
          background: linear-gradient(45deg, #2a82db, transparent);
          color: #fff;
          cursor: pointer;
          font-size: 20px;
          position: absolute;
          bottom: 60px;
          transition: 0.2s;
          backdrop-filter: blur(0px);
          opacity: 0;
          border-radius: 8px;
          // display: none;

          i {
            font-size: 28px;
            margin-right: 10px;
          }
        }

        &:hover {
          .cart-btn {
            // display: flex;
            backdrop-filter: blur(3px);
            opacity: 1;
          }

          // border: 1px solid red;
        }
      }
    }
  }

  .company-info {
    .company-content {
      background-color: #fff;
      border-radius: 12px;
      height: 320px;
      width: 100%;
      display: flex;
      .card-left-info {
        min-width: 250px;
        max-width: 30%;
        height: 100%;
        background-color: #fff;
        border-radius: 12px;
        padding: 40px;
        p {
          margin-bottom: 60px;
          line-height: 32px;
          color: #666;
          font-size: 14px;
        }
      }
      .card-right-carousel {
        width: 70%;
        height: 320px;
        background-color: #fff;
        border-radius: 12px;
        .carousel-img {
          width: 100%;
          height: 320px;
          object-fit: cover;
        }
        .custom-slick-arrow {
          width: 32px;
          height: 32px;
          font-size: 16px;
          text-align: center;
          line-height: 32px;
          color: rgba(235, 235, 235, 0.8);
          background-color: rgba(31, 45, 61, 0.15);
          border-radius: 50%;
          opacity: 0.8;
          z-index: 1;
          &:hover {
            background-color: rgba(31, 45, 61, 0.3);
            opacity: 0.8;
          }
        }
        ::before {
          display: none;
        }
      }
    }
  }
  @keyframes show {
    0% {
      transform: translateX(-10px);
      opacity: 0;
    }
    100% {
      transform: translateX(0);
      opacity: 0.8;
    }
  }

  @keyframes right-show {
    0% {
      transform: translateX(10px);
      opacity: 0;
    }
    100% {
      transform: translateX(0);
      opacity: 0.8;
    }
  }
  @keyframes hide-left {
    0% {
      transform: translateX(0);
      opacity: 0.8;
    }
    100% {
      transform: translateX(-10px);
      opacity: 0;
      display: none;
    }
  }
  @keyframes hide-right {
    0% {
      transform: translateX(0);
      opacity: 0.8;
    }
    100% {
      transform: translateX(10px);
      opacity: 0;
      display: none;
    }
  }
  @keyframes mask {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
}
</style>
