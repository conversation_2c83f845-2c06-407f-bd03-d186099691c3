// 会议服务商类型

type keys = 'SELF' | 'ALL' | 'GRADE'

export const MiceSchemePushStrategy = {
  // ZERO: { code: 0, desc: '/', disabled:true, },
  ALL: { code: 2, desc: '全员推送' },
  GRADE: { code: 3, desc: '级别推送' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in MiceSchemePushStrategy) {
      const item = MiceSchemePushStrategy[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(MiceSchemePushStrategy).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return MiceSchemePushStrategy[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
