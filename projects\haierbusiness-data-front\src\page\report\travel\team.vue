<template>
  <div
    style="
      background-color: #ffff;
      height: 100%;
      width: 100%;
      padding: 10px 10px 0px 10px;
      overflow: auto;
    "
  >
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="destine_no">团队票单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="destine_no"
              v-model:value="searchKey.destine_no"
              placeholder
              autocomplete="off"
              allow-clear
            />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="begin_date">出发日期：</label>
          </h-col>
          <h-col :span="7">
            <h-range-picker
              v-model:value="searchKey.begin_date"
              style="width: 100%"
              :show-time="{ format: 'HH:mm:ss' }"
              format="YYYY-MM-DD HH:mm:ss"
            />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="end_date">返回日期：</label>
          </h-col>
          <h-col :span="7">
            <h-range-picker
              v-model:value="searchKey.end_date"
              style="width: 100%"
              :show-time="{ format: 'HH:mm:ss' }"
              format="YYYY-MM-DD HH:mm:ss"
            />
          </h-col>
        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="contact_user_name">联系人名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="contact_user_name"
              v-model:value="searchKey.contact_user_name"
              placeholder
              autocomplete="off"
              allow-clear
            />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="contact_user_code">联系人工号：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="contact_user_code"
              v-model:value="searchKey.contact_user_code"
              placeholder
              autocomplete="off"
              allow-clear
            />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="begin_city_name">出发地：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="begin_city_name"
              v-model:value="searchKey.begin_city_name"
              placeholder
              autocomplete="off"
              allow-clear
            />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="end_city_name">目的地：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="end_city_name"
              v-model:value="searchKey.end_city_name"
              placeholder
              autocomplete="off"
              allow-clear
            />
          </h-col>
        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="evection_type">出差类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              placeholder="请选择出差类型"
              v-model:value="searchKey.evection_type"
              :options="orderTypeOptions"
              allow-clear
              style="width: 100%"
              :field-names="{ label: 'name', value: 'code' }"
            ></h-select>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="ticket_flag">是否出票：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              placeholder="请选择是否出票"
              v-model:value="searchKey.ticket_flag"
              :options="ticketFlagOptions"
              allow-clear
              style="width: 100%"
              :field-names="{ label: 'name', value: 'code' }"
            ></h-select>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="destine_info">产品类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              placeholder="请选择产品类型"
              v-model:value="searchKey.destine_info"
              :options="productTypeOptions"
              allow-clear
              style="width: 100%"
              :field-names="{ label: 'name', value: 'code' }"
            ></h-select>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="destine_status">订单状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              placeholder="请选择订单状态"
              v-model:value="searchKey.destine_status"
              :options="destineStatusOptions"
              allow-clear
              style="width: 100%"
              :field-names="{ label: 'name', value: 'code' }"
            ></h-select>
          </h-col>
        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button style="margin-right: 10px" @click="handleReset">重置</h-button>
            <h-button style="margin-right: 10px" type="primary" @click="onFilterChange">
              <SearchOutlined />查询
            </h-button>
            <h-button
              type="primary"
              style="margin-right: 10px"
              v-if="!pagination.disabled"
              :loading="downloading"
              @click="download"
            >
              <UploadOutlined />导出
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="data"
          :pagination="pagination"
          :scroll="{ y: 550, x: 6000 }"
          :loading="loading"
          @change="onPageChange"
        >
          <template
            #customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
          >
            <!-- 日期选择器 -->
            <div
              v-if="column.title.indexOf('时间')!=-1||column.title.indexOf('日期')!=-1"
              style="padding: 8px"
            >
              <h-range-picker
                v-model:value="searchKey[column.key]"
                value-format="YYYY-MM-DD"
                style="width: 218px; margin-bottom: 8px;"
              />
              <div style="display: block">
                <a-button
                  type="primary"
                  size="small"
                  style="width: 90px; margin-right: 8px"
                  @click="onFilterChange"
                >
                  <template #icon>
                    <SearchOutlined />
                  </template>
                  搜索
                </a-button>
                <a-button size="small" style="width: 90px" @click="handleReset">重置</a-button>
              </div>
            </div>
            <!-- 文本搜索框 -->
            <div v-else style="padding: 8px">
              <h-input
                ref="searchInput"
                :placeholder="`搜索${column.title}`"
                v-model:value="searchKey[column.key]"
                style="width: 188px; margin-bottom: 8px;"
                allow-clear
                @pressEnter="onFilterChange"
              />
              <div style="display: block">
                <a-button
                  type="primary"
                  size="small"
                  style="width: 90px; margin-right: 8px"
                  @click="onFilterChange"
                >
                  <template #icon>
                    <SearchOutlined />
                  </template>
                  搜索
                </a-button>
                <a-button size="small" style="width: 90px" @click="handleReset">重置</a-button>
              </div>
            </div>
          </template>
          <template #emptyText v-if="pagination.disabled">
            <div>
              暂无权限，
              <a @click="goApplyDetail">去申请</a>
            </div>
          </template>
          <template #customFilterIcon="{ filtered }">
              <SearchOutlined :style="{ color: filtered ? '#108ee9' : undefined }" />
            </template>
        </h-table>
      </h-col>
    </h-row>
  </div>

  <!-- <div v-if="visible">
  <edit-dialog
        :show="visible"
        :data="editData"
        @cancel="onDialogClose"
        @ok="handleOk"
    >
    </edit-dialog> 
  </div>-->
</template>

<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  Button as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps
} from "ant-design-vue";
import { reactive, ref, computed, onMounted } from "vue";
import {
  PlusOutlined,
  SearchOutlined,
  UploadOutlined  
} from "@ant-design/icons-vue";

import { reset } from "@haierbusiness-front/utils/src/commonUtil";
import { checkUserGroups } from "@haierbusiness-front/utils/src/authorityUtil";
import { useSearch } from "../../../composables/useSearch";
import {
  ReportFilter,
  ReportType,
  ApplyCompanyType,
  UserGroupSystemConstant
} from "@haierbusiness-front/common-libs";
import { reportApi } from "@haierbusiness-front/apis";
import {
  teamColumns,
  aggregatorsToColumn,
  budgetTypeOptions
} from "../columns";
import { getCurrentRouter, routerParam } from "@haierbusiness-front/utils";
const router = getCurrentRouter();

const columns = aggregatorsToColumn(
  checkUserGroups(
    [
      UserGroupSystemConstant.SUPER_MANAGE.groupId,
      UserGroupSystemConstant.REPORT_CONTROL.groupId
    ],
    "OR"
  )
    ? teamColumns
    : teamColumns.filter(
        item =>
          item.alias != "政策节省" &&
          item.alias != "舱位码" &&
          item.alias != "供应商" &&
          item.alias != "服务费" &&
          item.alias != "因公因私"
      )
);

const orderTypeOptions = [
  {
    name: "因公",
    code: "0"
  },
  {
    name: "因私",
    code: "1"
  }
];

const productTypeOptions = [
  {
    name: "全部",
    code: ""
  },
  {
    name: "国内机票",
    code: "0"
  },
  {
    name: "国际机票",
    code: "1"
  },
  {
    name: "酒店",
    code: "2"
  }
];
const ticketFlagOptions = [
  {
    name: "全部",
    code: ""
  },
  {
    name: "未出票",
    code: "0"
  },
  {
    name: "已出票",
    code: "1"
  }
];

const destineStatusOptions = [
  {
    name: "全部",
    code: ""
  },
  {
    name: "已提交",
    code: "20"
  },
  {
    name: "已接收",
    code: "30"
  },
  {
    name: "已完成",
    code: "90"
  }
];

const searchKey = reactive<ReportFilter>({
  // ddbh: null,
  // cxr_xm: null,
  // dd_ydsj: [] as string[],
  // account_company_code: null,
  datartParams: {
    moduleType: 1,
    type: "teamTicket",
    viewId: "4f66b354735cd5c98e79c697ef846154",
    aggregators: checkUserGroups(
      [
        UserGroupSystemConstant.SUPER_MANAGE.groupId,
        UserGroupSystemConstant.REPORT_CONTROL.groupId
      ],
      "OR"
    )
      ? teamColumns
      : teamColumns.filter(
          item =>
            item.alias != "政策节省" &&
            item.alias != "舱位码" &&
            item.alias != "供应商" &&
            item.alias != "服务费" &&
            item.alias != "因公因私"
        ),
    defaultFilters: [
      // {
      //   aggOperator: null,
      //   column: ["gngj"],
      //   sqlOperator: "EQ",
      //   values: [
      //     {
      //       value: "1",
      //       valueType: "STRING",
      //     },
      //   ],
      // },
    ],
    orders: [
      //   {
      //     "column": [
      //         "dd_ydsj"
      //     ],
      //     "operator": "DESC"
      // }
    ],
    functionColumns: [
      {
        alias: "cxr_xm_filter",
        snippet: "if(filter_flag=1,'***',cxr_xm)"
      },
      {
        alias: "cxr_gh_filter",
        snippet: "if(filter_flag=1,'***',cxr_gh)"
      },
      {
        alias: "pnr_cfcs_mc_filter",
        snippet: "if(filter_flag=1,'***',pnr_cfcs_mc)"
      },
      {
        alias: "pnr_ddcs_mc_filter",
        snippet: "if(filter_flag=1,'***',pnr_ddcs_mc)"
      },
      {
        alias: "pnr_hbh_filter",
        snippet: "if(filter_flag=1,'***',pnr_hbh)"
      },
      {
        alias: "sqrgh_filter",
        snippet: "if(filter_flag=1,'********',sqrgh)"
      },
      {
        alias: "sqr_filter",
        snippet: "if(filter_flag=1,'***',sqr)"
      },
      {
        alias: "jp_ddsj_filter",
        snippet: "if(filter_flag=1,'**********',jp_ddsj)"
      },
      {
        alias: "jp_cfsj_filter",
        snippet: "if(filter_flag=1,'**********',jp_cfsj)"
      },
      {
        alias: "sftp_state",
        snippet: "if(sftp=1,'已退票','未退票')"
      },
      {
        alias: "sfgq_state",
        snippet: "if(sfgq=1,'已被改签','未被改签')"
      }
    ]
  },
  fileName: "团队票"
});

const goApplyDetail = () => {
  router.push("/data/report/permission/apply");
};

const handleReset = () => {
  reset(searchKey, ["datartParams"]);
  onFilterChange();
};

const {
  data,
  fetchData,
  pagination,
  loading,
  onPageChange,
  onTimeChange,
  downloading,
  download,
  onFilterChange,
  powerCompany,
  powerDepartment
} = useSearch<ReportType, ReportFilter>(
  reportApi,
  searchKey,
  "travel-internal"
);
const onCreateTimeChange = (dateRange: any) => {
  searchKey.order_create_datetime = onTimeChange(dateRange);
};
const settleCompany = ref([] as Array<ApplyCompanyType>);
const querySettleCompany = async (keyword: string) => {
  //查询结算单位
  const res = await reportApi.querySettleCompany(keyword);
  settleCompany.value = res;
};
 
const settleDepartment = ref([]);
const querySettleDepartment = async (keyword: string) => {
  //查询部门
  const data = await reportApi.querySettleDepartment(keyword);
  if (data && data.length > 0) {
    settleDepartment.value = data;
  }
};

const budgetTypeList = ref([]);
const queryBudgetTypeList = async params => {
  //查询部门
  const data = await reportApi.queryBudgetTypeList(params);
  if (data && data.length > 0) {
    budgetTypeList.value = data;
  }
};
onMounted(() => {
});
</script>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
