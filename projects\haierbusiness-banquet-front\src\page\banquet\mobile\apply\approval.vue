<template>

  <div class="order-list" style="padding-top: 20px; min-height: 100vh;">
  
    <!-- <van-nav-bar  class="top-bg-color" :fixed="true"  title="订单审批记录" left-arrow>
      <template #left>
        <van-icon name="arrow-left" color="#000" @click="goBack" />
      </template>
    </van-nav-bar> -->

    <!-- 预算信息 -->
    <van-cell-group inset class="mb-10 mt-10" v-for="item,index in detail?.processRecord?.steps" :key="index">
      <van-cell title="审批人" :value="getPersonName(item.masterOperators)" />
      <van-cell title="审批结果" :value="getResult(item?.result)" />
      <van-cell title="审批时间" :value="item?.masterOperators[0]?.completeTime" />
      <van-cell title="审批意见"  :label="item?.masterOperators[0]?.approverRemark" />
    </van-cell-group>
   
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onActivated } from 'vue';

import {
  IUserListRequest,
  ROrderPayMentStateEnum,
  ROrderPayMentStateEnumColor,
  ROrderApprovalStateEnum,
  ROrderApprovalStateEnumColor,
  ROrderStateEnum,
  ROrderStateEnumColor,
  BanquetStatusEnumMobile,
  BanquetStateTagColorMap,
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { processApi } from '@haierbusiness-front/apis';
import { ROrderParams, IControlProcessResponse, ProcessRecordStepConstant, IProcessRecordStepOperator } from '@haierbusiness-front/common-libs';
import type { Ref } from 'vue';

import dayjs from 'dayjs';

import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { showConfirmDialog, showSuccessToast } from 'vant';
const store = applicationStore();

const { loginUser } = storeToRefs(store);

const router = getCurrentRouter();

const route = ref(getCurrentRoute());
const processCode = route.value?.query?.processCode;

const detail = ref<IControlProcessResponse>()

const getResult = (result?: number) => {
  return ProcessRecordStepConstant.ofType(result)?.name
}

const getPersonName = (arr?: Array<IProcessRecordStepOperator>) => {
  return arr?.map(item => item.approverName).join('，')
}
const goBack = () => {
  router.back(-1);
};

const getDetail = (processCode: string) => {
  const params = {
    code: processCode,
  };
  processApi.details(params).then(res => {
    detail.value = res
  })
  
};


watch(
  () => processCode,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);



</script>

<style lang='less' scoped>
@import url(../common.less);

:deep(.van-dropdown-menu__bar) {
  //  background: var(--van-dropdown-menu-background);
  box-shadow: none;
}

.list-search {
  :deep(.van-search__action) {
    // padding: 0;
  }
}

.list-search-btn {
  width: 110px;
}

.my-radio {
  :deep(.van-radio__icon) {
    height: auto !important;
  }
}

.btn-com {
  width: 70px;
}

.input-border {
  border: 1px solid #dcdee0;
  border-radius: 20px;
  padding: 2px 10px;
}

.van-list-box {
  padding: 16px 10px;
}

.order-item {
  height: 180px;

  .order-item-left {
    width: 10px;
    height: 100%;
    background: url('@/assets/image/banquet/order/step-mini.png') no-repeat;
    background-size: contain;

  }

  .order-item-right {
    flex: 1;
    flex-direction: column;

    .order-item-title-left {
      font-size: 10px;
      color: rgba(0,0,0,0.5);
    }
    .order-content {
      :deep(.van-cell__title) {
        color: rgba(20,21,3,0.6);      }
      :deep(.van-cell__value) {
        color: rgba(20,21,3,0.8);     
      }
      :deep(.van-cell__label) {
        color: rgba(20,21,3,0.8);  
        font-size: 11px;
      }
      
    }

    .order-title-cell {
      :deep(.van-cell__title) {
        color: rgba(0,0,0,0.9) !important;
      }
    }
    
  }
}
</style>