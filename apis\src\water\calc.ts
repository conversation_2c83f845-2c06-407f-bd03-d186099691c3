import { download, get, post, filepost, originalGet } from '../request'

export const waterworkCalcApi = {
    list: (params: any): Promise<void> => {
        return get('waterworks/api/tcostType/page', params)
    },
    listAll: (params: any): Promise<void> => {
        return get('waterworks/api/tcostType/list', params)
    },
    add: (params: any): Promise<void> => {
        return post('waterworks/api/tcostType/save', params)
    },
    update: (params: object): Promise<void> => {
        return post(`waterworks/api/tcostType/update`, params);
    },
    export: (params: any): Promise<void> => {
        return download('waterworks/api/tcostType/export', params)
    },
    delete: (ids: string): Promise<void> => {
        return post(`waterworks/api/tcostType/delete/${ids}`);
    },
}