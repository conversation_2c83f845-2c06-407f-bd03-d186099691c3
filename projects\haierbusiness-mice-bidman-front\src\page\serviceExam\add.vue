<!-- 新增服务商考核弹框 -->
<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  DatePicker as hDatePicker,
  Upload as hUpload,
  message,
  Table as hTable,
  Button as hButton,
  Card as hCard,
  Modal as hModal,
} from 'ant-design-vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import { computed, ref, watch, onMounted, nextTick } from 'vue';
import type { Ref } from 'vue';
import {
  ServiceExam,
  AssessmentItem,
  UploadFile,
  ServiceExamFilter,
  TablePagination,
  FileTypeConstant,
} from '@haierbusiness-front/common-libs';
import { fileApi, serviceExamApi, miceBidManServiceProviderApi } from '@haierbusiness-front/apis';
import { useRouter } from 'vue-router';
import dayjs, { Dayjs } from 'dayjs';
import { usePagination } from 'vue-request';
import type { TablePaginationConfig } from 'ant-design-vue';
import type { ColumnType } from 'ant-design-vue/lib/table/interface';

// 声明全局变量类型
declare global {
  interface Window {
    merchantMap: Map<string, number>;
  }
}

// 初始化全局merchantMap
window.merchantMap = new Map();

const router = useRouter();
const from = ref();
const confirmLoading = ref(false);
const assessmentDialogVisible = ref(false);
const searchKeyword = ref('');

// 审批流程相关
const businessProcess = import.meta.env.VITE_BUSINESS_PROCESS_URL;
const approvalModalShow = ref(false);
const approveCode = ref<string>(''); // 审批流程Code

// 时间设置
const violationTime = ref<Dayjs>();
const violationDisposeEndTime = ref<Dayjs>();

// 监听时间变化
watch(
  () => violationTime.value,
  (newVal) => {
    if (newVal) {
      const formattedDate = newVal.format('YYYY-MM-DD') + ' 00:00:00';
      serviceExam.value.violationTime = formattedDate;
    } else {
      serviceExam.value.violationTime = null;
    }
  },
);

watch(
  () => violationDisposeEndTime.value,
  (newVal) => {
    if (newVal) {
      // 日期后拼接 23:59:00
      const formattedDate = newVal.format('YYYY-MM-DD') + ' 23:59:00';
      serviceExam.value.violationDisposeEndTime = formattedDate;
    } else {
      serviceExam.value.violationDisposeEndTime = null;
    }
  },
);

// 添加分页相关代码
const searchParam = ref<ServiceExamFilter & { keyword?: string }>({});
const {
  data: assessmentItemsData,
  run: listAssessmentItems,
  loading: assessmentItemsLoading,
} = usePagination((params: TablePagination) =>
  serviceExamApi.getExamItem({
    ...searchParam.value,
    pageNum: params.current || 1,
    pageSize: params.pageSize || 5,
    state: 10,
  }),
);

const assessmentItems = computed(() => assessmentItemsData.value?.records || []);
const assessmentItemsPagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: assessmentItemsData.value?.total || 0,
  current: assessmentItemsData.value?.pageNum || 1,
  pageSize: assessmentItemsData.value?.pageSize || 5,
}));

const defaultData: ServiceExam = {
  id: undefined,
  merchantName: undefined,
  mainCode: undefined,
  miceName: '',
  entry: '',
  details: '',
  type: '',
  score: 0,
  fine: 0,
  status: 0,
  attachment: '',
  violationTime: null,
  violationDisposeEndTime: null,
  violationDesc: '',
  evidenceMaterial: '',
  merchantExamItemId: null,
};

const serviceExam = ref<ServiceExam>({ ...defaultData });
const selectedExamType = ref<string | number>(''); // 存储考核条目的examType

// 监听 serviceExam 的变化
watch(
  serviceExam,
  (newVal) => {
    console.log('serviceExam changed:', newVal);
  },
  { deep: true },
);

const fileList = ref<UploadFile[]>([]);
const uploadLoading = ref<boolean>(false);
const serviceProviders = ref<{ label: string; value: string; data: any }[]>([]);
const orderNumbers = ref<{ label: string; value: string }[]>([]);
const meetingList = ref<{ miceId: number; mainCode: string; name: string }[]>([]);
const loading = ref<boolean>(false);
const selectedMerchantId = ref<any>(null); // 用于绑定服务商选择器

// 将状态同步到控制台，方便调试
watch(
  [orderNumbers, serviceProviders, meetingList],
  (newVals) => {
    console.log('反应式变量更新:', {
      orderNumbers: newVals[0].length,
      orderNumbersData: JSON.stringify(newVals[0]),
      serviceProviders: newVals[1].length,
      meetingList: newVals[2].length,
    });
  },
  { deep: true },
);

// 获取服务商列表
const getBussinessList = async () => {
  loading.value = true;
  try {
    const response = await miceBidManServiceProviderApi.getBussinessList();
    console.log(response);

    if (response?.records && Array.isArray(response.records)) {
      // 将服务商数据保存到全局变量中，以便通过名称查找id
      const merchantMap = new Map();
      response.records.forEach((item) => {
        merchantMap.set(item.name, item.id);
      });

      // 保存服务商列表
      serviceProviders.value = response.records.map((item) => ({
        label: item.merchantName || '',
        value: item.id || '',
        data: item, // 保存完整数据对象
      }));

      // 在控制台记录数据，方便调试
      console.log('服务商数据：', response.records);
      console.log('服务商选项：', serviceProviders.value);

      // 将merchantMap保存为全局变量
      window.merchantMap = merchantMap;
    }
  } catch (error) {
    console.error('获取服务商列表失败:', error);
    message.error('获取服务商列表失败');
  } finally {
    loading.value = false;
  }
};

// 根据服务商名称获取会议信息
const getMeetingInfoByMerchantName = async (merchantName: string) => {
  if (!merchantName) return;

  loading.value = true;
  try {
    const response = await serviceExamApi.getMeetingInfo({ id: merchantName });

    // 根据返回结果判断处理方式 - 如果本身就是数组，直接使用
    if (Array.isArray(response)) {
      // 将接口返回的字段映射到代码期望的字段
      meetingList.value = response.map((item: any) => ({
        miceId: item.id,
        mainCode: item.mainCode,
        name: item.miceName,
      }));
      orderNumbers.value = meetingList.value.map((item) => ({
        label: item.mainCode || '',
        value: item.mainCode || '',
      }));
    }
    // 如果是对象且包含data字段是数组
    else if (
      response &&
      typeof response === 'object' &&
      (response as any).data &&
      Array.isArray((response as any).data)
    ) {
      // 将接口返回的字段映射到代码期望的字段
      meetingList.value = (response as any).data.map((item: any) => ({
        miceId: item.id,
        mainCode: item.mainCode,
        name: item.miceName,
      }));
      orderNumbers.value = meetingList.value.map((item) => ({
        label: item.mainCode || '',
        value: item.mainCode || '',
      }));
    }
    // 如果是对象且包含records字段是数组
    else if (response && typeof response === 'object' && response.records && Array.isArray(response.records)) {
      // 将接口返回的字段映射到代码期望的字段
      meetingList.value = response.records.map((item: any) => ({
        miceId: item.id,
        mainCode: item.mainCode,
        name: item.miceName,
      }));
      orderNumbers.value = meetingList.value.map((item) => ({
        label: item.mainCode || '',
        value: item.mainCode || '',
      }));
    } else {
      console.warn('未找到会议数据或格式不正确, response:', response);
      meetingList.value = [];
      orderNumbers.value = [];
    }
  } catch (error) {
    console.error('获取会议信息失败:', error);
    message.error('获取会议信息失败');
  } finally {
    loading.value = false;
  }
};

// 移除了会导致重复调用的 watch 监听器，改为在 handleMerchantChange 中直接调用

// 监听订单号变化，自动填充会议名称
watch(
  () => serviceExam.value.mainCode,
  (newVal, oldVal) => {
    if (newVal && meetingList.value.length > 0) {
      const selectedMeeting = meetingList.value.find((item) => item.mainCode === newVal);
      if (selectedMeeting) {
        serviceExam.value.miceName = selectedMeeting.name;
      } else {
        console.warn('未找到匹配的会议信息');
      }
    }
  },
  { immediate: true },
);

// 上传附件
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const uploadRequest = (options: any) => {
  uploadLoading.value = true;
  confirmLoading.value = true; // 上传时保存按钮也显示loading状态

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      console.log(it, it);

      const file: UploadFile = {
        uid: options.file.uid,
        name: options.file.name,
        status: 'done',
        filePath: baseUrl + it.path,
        fileName: options.file.name,
      };
      fileList.value = [file];
      options.onProgress(100);
      options.onSuccess(it, options.file);
    })
    .catch((error) => {
      message.error('文件上传失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
      confirmLoading.value = false; // 上传完成后取消保存按钮的loading状态
    });
};

// 移除文件
const handleRemove = (file: UploadFile) => {
  const index = fileList.value.indexOf(file);
  if (index !== -1) {
    fileList.value.splice(index, 1);
  }
};

const handleSubmit = () => {
  // 验证必填字段
  const requiredFields = [
    { field: 'merchantName', msg: '请选择服务商' },
    { field: 'entry', msg: '请选择考核条目' },
    { field: 'violationTime', msg: '请选择违规时间' },
    { field: 'violationDesc', msg: '请输入违规描述' },
  ];

  // 如果类型是违规，则申诉截止时间也是必填的
  if (serviceExam.value.type === '违规') {
    requiredFields.push({ field: 'violationDisposeEndTime', msg: '请选择处理截止时间' });
  }

  // 检查必填字段
  for (const { field, msg } of requiredFields) {
    const value = serviceExam.value[field as keyof ServiceExam];
    if (!value) {
      message.error(msg);
      return;
    }
  }

  // 检查是否上传了见证性材料
  if (fileList.value.length === 0) {
    message.error('请上传见证性材料');
    return;
  }

  // 确保有服务商ID
  if (!serviceExam.value.id) {
    console.error('未找到服务商ID');
    message.error('服务商信息不完整，请重新选择服务商');
    return;
  }

  console.log('提交前的serviceExam:', serviceExam.value);

  confirmLoading.value = true;
  from.value
    .validate()
    .then(() => {
      // 获取当前选中订单对应的miceId
      let miceId = null;
      if (serviceExam.value.mainCode && meetingList.value.length > 0) {
        const selectedMeeting = meetingList.value.find((item) => item.mainCode === serviceExam.value.mainCode);
        if (selectedMeeting) {
          miceId = selectedMeeting.miceId;
        }
      }
      const formData: ServiceExam = {
        merchantId: serviceExam.value.id, // 使用服务商ID
        // 服务商信息
        merchantName: serviceExam.value.merchantName,
        mainCode: serviceExam.value.mainCode,
        miceName: serviceExam.value.miceName,

        // 考核信息
        entry: serviceExam.value.entry,
        details: serviceExam.value.details,
        type: selectedExamType.value,
        score: serviceExam.value.score,
        fine: serviceExam.value.fine,
        merchantExamItemId: serviceExam.value.merchantExamItemId,
        // 违规信息
        violationTime: serviceExam.value.violationTime,
        violationDisposeEndTime: serviceExam.value.violationDisposeEndTime,
        violationDesc: serviceExam.value.violationDesc,
        miceId: miceId,
        // 见证性材料
        path: fileList.value.map((file) => file.filePath).filter((path): path is string => path !== undefined),
        pathType: FileTypeConstant.WITNESS_MATERIALS.code,
      };

      console.log('即将提交的数据:', formData);

      // 调用保存API，传递额外的miceId参数
      serviceExamApi
        .createExam({ ...formData })
        .then((response) => {
          message.success('保存成功');
          console.log(response, 'response');

          // 检查返回结果中是否有审批流程code
          if (response) {
            approveCode.value = response;
            approvalModalShow.value = true;
          } else {
            // 如果没有审批流程，直接跳转到列表页
            router.push('/bidman/serviceExam/list');
          }
        })
        .catch((error) => {
          console.error('保存失败:', error);
          message.error('保存失败，请重试');
        })
        .finally(() => {
          confirmLoading.value = false;
        });
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};

const showAssessmentDialog = () => {
  assessmentDialogVisible.value = true;
};

const handleAssessmentDialogCancel = () => {
  assessmentDialogVisible.value = false;
};

const handleSelectAssessment = async (record: AssessmentItem) => {
  const currentMiceName = serviceExam.value.miceName;
  const currentMerchantName = serviceExam.value.merchantName;
  const currentMainCode = serviceExam.value.mainCode;

  // 将type转换为数字进行比较
  const typeNumber = Number(record.examType);
  const examType = typeNumber === 1 ? '违规' : '整改';
  const examTypeValue = typeNumber === 1 ? 1 : 2;

  // 保存examType到单独的ref中
  selectedExamType.value = record.examType;

  const updatedExam = {
    ...serviceExam.value,
    details: record.detail,
    type: examType,
    typeValue: examTypeValue,
    score: record.score,
    fine: record.money,
    merchantExamItemId: Number(record.id),
    entry: record.name,
    miceName: currentMiceName,
    merchantName: currentMerchantName,
    mainCode: currentMainCode,
  };

  serviceExam.value = updatedExam;

  await nextTick();
  assessmentDialogVisible.value = false;
};

const handleSearch = () => {
  searchParam.value = {
    ...searchParam.value,
    name: searchKeyword.value,
  };
  listAssessmentItems({
    current: 1,
    pageSize: 5,
    total: 0,
  });
};

const handleTableChange = (pagination: TablePaginationConfig) => {
  listAssessmentItems({
    current: pagination.current || 1,
    pageSize: pagination.pageSize || 5,
    total: pagination.total || 0,
  });
};

const columns: ColumnType[] = [
  {
    title: '考核条目',
    dataIndex: 'name',
    width: '150px',
    key: 'name',
  },
  {
    title: '考核明细',
    dataIndex: 'detail',
    key: 'detail',
  },
  {
    title: '分数',
    dataIndex: 'score',
    width: '100px',
    key: 'score',
  },
  {
    title: '金额',
    dataIndex: 'money',
    width: '150px',
    key: 'money',
    customRender: ({ text }) => (text ? `${text} 元` : '-'),
  },
  {
    title: '操作',
    key: 'action',
    slots: { customRender: 'action' },
  },
];

// 添加初始化加载
onMounted(() => {
  getBussinessList();
  listAssessmentItems({
    current: 1,
    pageSize: 5,
    total: 0,
  });
});

// 处理服务商选择变化
const handleMerchantChange = (value: any, option: any) => {
  console.log('服务商选择变更:', value, option);

  if (value) {
    // 设置服务商ID
    serviceExam.value.id = value;

    // 设置服务商名称 - 从option.label获取真实的服务商名称
    if (option && option.label) {
      serviceExam.value.merchantName = option.label;
      console.log('已设置服务商名称:', serviceExam.value.merchantName);
    } else {
      // 如果没有option.label，从serviceProviders中查找
      const provider = serviceProviders.value.find((item) => item.value === value);
      if (provider) {
        serviceExam.value.merchantName = provider.label;
        console.log('从serviceProviders列表获取服务商名称:', serviceExam.value.merchantName);
      } else {
        console.warn('未能找到服务商名称');
      }
    }

    // 清空之前的订单和会议信息
    orderNumbers.value = [];
    serviceExam.value.mainCode = '';
    serviceExam.value.miceName = '';

    // 只调用一次接口，传递服务商ID
    getMeetingInfoByMerchantName(value);
  }
};

// 处理订单号选择变化
const handleOrderChange = (value: any) => {
  if (value && meetingList.value.length > 0) {
    const selectedMeeting = meetingList.value.find((item) => item.mainCode === value);
    if (selectedMeeting) {
      serviceExam.value = {
        ...serviceExam.value,
        miceName: selectedMeeting.name,
      };
    } else {
      console.warn('未找到匹配的会议信息');
    }
  }
};

// 服务商下拉框模糊搜索
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
</script>

<template>
  <div class="create-page">
    <div class="page-header">
      <h1>新增服务商考核</h1>
    </div>
    <h-card>
      <h-form ref="from" :model="serviceExam" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
        <h-form-item label="服务商" name="merchantName">
          <h-select
            v-model:value="selectedMerchantId"
            placeholder="请选择服务商"
            :options="serviceProviders"
            @change="handleMerchantChange"
            show-search
            :filter-option="filterOption"
          />
        </h-form-item>

        <h-form-item label="订单号" name="mainCode">
          <h-select
            v-model:value="serviceExam.mainCode"
            placeholder="请选择订单号(非必填)"
            :options="orderNumbers"
            @change="handleOrderChange"
          />
        </h-form-item>

        <h-form-item label="会议名称" name="miceName">
          <h-input v-model:value="serviceExam.miceName" disabled />
        </h-form-item>

        <h-form-item label="考核条目" name="entry">
          <div style="display: flex; gap: 8px">
            <h-input v-model:value="serviceExam.entry" placeholder="请选择考核条目" @click="showAssessmentDialog" />
          </div>
        </h-form-item>

        <h-form-item label="考核明细" name="details">
          <h-input v-model:value="serviceExam.details" disabled />
        </h-form-item>

        <h-form-item label="考核类型" name="type">
          <h-input v-model:value="serviceExam.type" disabled />
        </h-form-item>

        <h-form-item label="考核分数" name="score">
          <h-input v-model:value="serviceExam.score" disabled />
        </h-form-item>

        <h-form-item label="违规金额" name="fine">
          <h-input v-model:value="serviceExam.fine" disabled suffix="元" />
        </h-form-item>

        <h-form-item label="违规时间" name="violationTime">
          <h-date-picker v-model:value="violationTime" />
        </h-form-item>

        <h-form-item label="处理截止时间" name="violationDisposeEndTime" v-if="serviceExam.type === '违规'">
          <h-date-picker v-model:value="violationDisposeEndTime" />
        </h-form-item>

        <h-form-item label="违规描述" name="violationDesc">
          <h-textarea
            v-model:value="serviceExam.violationDesc"
            :rows="4"
            :maxlength="200"
            show-count
            placeholder="请输入违规描述"
          />
        </h-form-item>

        <h-form-item label="见证性材料" name="evidenceMaterial">
          <h-upload
            :file-list="fileList"
            :custom-request="uploadRequest"
            :multiple="false"
            :max-count="1"
            @remove="handleRemove"
            accept=".pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx"
            :show-upload-list="true"
          >
            <h-button :loading="uploadLoading">
              <upload-outlined></upload-outlined>
              上传文件
            </h-button>
          </h-upload>
        </h-form-item>

        <h-form-item :wrapper-col="{ offset: 4, span: 18 }">
          <h-button type="primary" :loading="confirmLoading" @click="handleSubmit">保存</h-button>
        </h-form-item>
      </h-form>
    </h-card>

    <!-- 考核条目选择弹窗 -->
    <h-modal
      :visible="assessmentDialogVisible"
      title="服务商考核"
      :width="900"
      :footer="null"
      @cancel="handleAssessmentDialogCancel"
    >
      <div style="margin-bottom: 16px">
        <h-form layout="inline">
          <h-form-item label="考核条目">
            <h-input v-model:value.trim="searchKeyword" placeholder="请输入考核条目" />
          </h-form-item>
          <h-form-item>
            <h-button type="primary" @click="handleSearch">查询</h-button>
          </h-form-item>
        </h-form>
      </div>
      <h-table
        :columns="columns"
        :data-source="assessmentItems"
        :pagination="assessmentItemsPagination"
        :loading="assessmentItemsLoading"
        @change="handleTableChange"
      >
        <template #action="{ record }">
          <h-button type="link" @click="handleSelectAssessment(record)">选择</h-button>
        </template>
      </h-table>
    </h-modal>

    <!-- 审批流程模态框 -->
    <h-modal
      v-model:open="approvalModalShow"
      title="已提交如下人员审批"
      width="80%"
      :keyboard="false"
      :maskClosable="false"
      :closable="false"
    >
      <div>
        <iframe width="100%" :src="businessProcess + '?code=' + approveCode + '#/detailsPcSt'" frameborder="0"></iframe>
      </div>
      <template #footer>
        <h-button
          @click="
            approvalModalShow = false;
            router.push('/bidman/serviceExam/list');
          "
          >确定</h-button
        >
      </template>
    </h-modal>
  </div>
</template>

<style lang="less" scoped>
.create-page {
  padding: 24px;
  background: #fff;
  min-height: 100vh;

  .page-header {
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 16px;

    h1 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }
  }
}

.important {
  color: red;
}

.support_extend_tip {
  color: #86909c;
  line-height: 22px;
  margin-top: 8px;
}

/* 设置输入框的宽度 */
:deep(.ant-form-item-control-input) {
  max-width: 360px;
}

/* 确保时间选择器的宽度一致 */
:deep(.ant-picker) {
  width: 100%;
}
</style>
