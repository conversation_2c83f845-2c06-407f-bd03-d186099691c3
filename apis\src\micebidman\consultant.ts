import { download, get, post } from '../request'
import {
    IConsultantFilter,
    IConsultant,
    IPageResponse,
    Result
} from '@haierbusiness-front/common-libs'


export const consultantApi = {
    // 会议顾问分页列表
    list: (params: IConsultantFilter): Promise<IPageResponse<IConsultant>> => {
        return get('/mice-bid/api/counsellor/user/page', params)
    },

    get: (id: number): Promise<IConsultant> => {
        return get('merchant/api/consultant/get', {
            id
        })
    },
    // 新增会议顾问
    save: (params: IConsultant): Promise<Result> => {
        return post('/mice-bid/api/counsellor/user/add', params)
    },

    // 编辑会议顾问
    edit: (params: IConsultant): Promise<Result> => {
        return post('/mice-bid/api/counsellor/user/edit', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('merchant/api/consultant/delete', { id })
    },
    // 停用会议顾问
    userDisable: (params: { id: number, state: number }): Promise<Result> => {
        return post('/mice-bid/api/counsellor/user/disable', params)
    },
}
