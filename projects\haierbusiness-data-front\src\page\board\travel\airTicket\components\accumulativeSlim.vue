<template>
  <h-row
    type="flex"
    :gutter="[30, 10]"
    style="margin-left: 0; margin-right: 0; height: 33.3vh"
    background="rgba(0,0,0,0)"
  >
    <h-col :span="8" class="content" v-for="(column, index) in columns" :key="index">
      <div>
        <div class="num">
          <CountTo :start-val="0" :end-val="rows[index]" :decimals="column.name[0] == '平均折扣' ? 2 : 0" />
          <span class="unit" v-if="column.name[0] == '成交金额' || column.name[0] == '政策节省'">w</span>
          <span
            class="unit"
            v-if="column.name[0] == '退票率' || column.name[0] == '改期率' || column.name[0] == '投保率'"
            >%</span
          >
        </div>
        <div class="title"><span>▶</span>{{ column.name[0] }}</div>
      </div>
    </h-col>
  </h-row>
</template>
<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
} from 'ant-design-vue';
import CountTo from '@/components/vue-count-to/src';
import { queryAccumulativePerson, queryAccumulativeOther } from '@haierbusiness-front/apis/src/data/board/travel';
import { onMounted, ref } from 'vue';
import { EventBus } from '../../../eventBus';

const props = defineProps({
  gngj: {
    type: [String, Number],
    default: '1',
  },
});

const columns = ref(
  [] as Array<{
    name: string;
    value: string | number;
  }>,
);
const rows = ref([]);
const loading = ref(false);
onMounted(() => {
  queryData();
});
EventBus.on((event, params) => {
  if (event == 'refresh') queryData(params ? params : null);
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
  loading.value = true;
  const { gngj } = props;
  const data = await queryAccumulativeOther({ gngj }, params ? params.data.name : null, params ? params.from : null);
  columns.value = data.columns ?? [];
  data.columns.forEach((item, index) => {
    if (item.name[0] == '成交金额' || item.name[0] == '政策节省') {
      data.rows[0][index] = ((data.rows[0][index] / 10000).toFixed(0) as any) - 0;
    }
    if (item.name[0] == '退票率' || item.name[0] == '改期率' || item.name[0] == '投保率') {
      data.rows[0][index] = ((data.rows[0][index] * 100).toFixed(0) as any) - 0;
    }
  });
  rows.value = data.rows[0];
  loading.value = false;

  let arrColums = [];
  if (columns.value.length > 0) {
    columns.value.forEach((item, i) => {
      if (item.name[0] != '政策节省') {
        arrColums.push(item);
      }
      if (item.name[0] == '政策节省') {
        rows.value = rows.value.filter((each, index) => index != i);
      }
    });
  }
  columns.value = arrColums;
};
</script>
<style scoped lang="less">
@import url(@/assets/style/board/accumulative.less);
</style>
