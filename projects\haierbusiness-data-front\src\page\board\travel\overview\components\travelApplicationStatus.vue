<template>
    <div :style="{ height: height + 'vh' }" background="rgba(0,0,0,0)">
        <bar-line :from="props.from ? props.from : ''" :height="height" v-if="loaded" :legend="legend" :x-axis="xAxis"
            :y-axis="yAxis" :series="series" />
    </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import BarLine from "../../../components/barLine.vue";
import { queryOverviewTravelBudget } from "@haierbusiness-front/apis/src/data/board/travel";
import { EventBus } from "../../../eventBus";
const props = defineProps({
    dateType: Number,
    height: {
        type: Number,
        default: 30,
    },
    from: {
        type: String,
        default: "",
    },
});
const loaded = ref(false);
const loading = ref(false);
const legend: any = ref([]);
const xAxis = ref([]);
const yAxis = [
    {
        type: "value",
        name: "万元",
        splitNumber: 5,
        axisLabel: {
            formatter(value) {
                return value / 10000;
            },
        },
    },
];
const series: any = ref([]);
EventBus.on((event) => {
    if (event == "refresh") {
        queryData();
    }
});
const getFunctionColumns = () => {
    if (props.dateType == 0) {
        return [
            {
                alias: "sqsj_date_group",
                snippet: "AGG_DATE_YEAR([sqsj_date_key])",
            },
        ];
    }
    if (props.dateType == 1) {
        return [
            {
                alias: "sqsj_date_group",
                snippet: "AGG_DATE_MONTH([sqsj_date_key])",
            },
        ];
    }
    return [
        {
            alias: "sqsj_date_group",
            snippet: "AGG_DATE_DAY([sqsj_date_key])",
        },
    ];
};

const queryData = async () => {
    legend.value = [];
    series.value = [];
    loaded.value = false;
    loading.value = true;
    const functionColumns = getFunctionColumns();
    const data = await queryOverviewTravelBudget({ functionColumns });
    loading.value = false;
    const xData: any = [];
    data.columns.forEach((item, index) => {
        if (index == 0) return;
        legend.value.push(item.name[0]);
        series.value.push({
            name: item.name[0],
            type: "bar",
            stack: "status",
            data: [],
        });
    });
    console.log("series.value", series.value);
    data.rows.forEach((item, index) => {
        xData.push(item[0]);
        series.value.forEach((s, j) => {
            s.data.push(item[j + 1]);
        });
    });
    xAxis.value = xData;
    loaded.value = true;
};
onMounted(() => {
    queryData()
})
watch(() => props.dateType, queryData);
</script>
