import { IPageResponse, IWyyB2bListRequest,IHaierAccountBillInfo, IWyyB2bAccountRequest, IWyyB2bDetailsRequest, IHabDetailInfo, IWyyB2bConfirmRequest, IWyyB2bRevokeConfirmRequest, IWyyB2bConfirmBalanceOrderBudgetRequest, IWyyB2bCancelRequest, IWyyB2bMarkReadRequest } from '@haierbusiness-front/common-libs'
import { download, get, post } from '../request'

export const b2bWyyBalanceApi = {


     /**
     * 进行汇总
     */
     account: (params: IWyyB2bAccountRequest): Promise<void> => {
        return post('balance/api/b2b/wyy/account', params)
    },

    /**
     * 确认汇总
     */
    confirm: (params: IWyyB2bConfirmRequest): Promise<void> => {
        return post('balance/api/b2b/wyy/confirm', params)
    },

     /**
     * 确认并释放全部预算,全部确认成功则通知商户通
     */
     confirmBalanceOrderBudget: (params: IWyyB2bConfirmBalanceOrderBudgetRequest): Promise<IHabDetailInfo[]> => {
        return post('balance/api/b2b/wyy/revoke/confirm/budget', params)
    },

    /**
     * 撤回已确认状态
     */
    revokeConfirm: (params: IWyyB2bRevokeConfirmRequest): Promise<void> => {
        return post('balance/api/b2b/wyy/revoke/confirm', params)
    },

    /**
     * 取消汇总
     */
    cancel: (params: IWyyB2bCancelRequest): Promise<void> => {
        return post('balance/api/b2b/wyy/cancel', params)
    },
    
    /**
     * 获取结算列表
     */
    list: (params: IWyyB2bListRequest): Promise<IPageResponse<IHaierAccountBillInfo>> => {
        return get('balance/api/b2b/wyy/list', params)
    },

    /**
     * 导出结算列表
     */
    exportList: (params: IWyyB2bListRequest): Promise<void> => {
        return download('balance/api/b2b/wyy/list/export', params)
    },

    /**
     * 获取结算状态数量
     */
    stateAccount: (params: IWyyB2bListRequest): Promise<any> => {
        return get('balance/api/b2b/wyy/state/account', params)
    },

    /**
     * 获取预算释放失败,cvp通知失败数量
     */
    cvpErrorAccount: (params: IWyyB2bListRequest): Promise<number> => {
        return get('balance/api/b2b/wyy/cvp/error/account', params)
    },

    /**
     * 获取结算详情
     */
    details: (params: IWyyB2bDetailsRequest): Promise<IPageResponse<IHabDetailInfo>> => {
        return get('balance/api/b2b/wyy/details', params)
    },

    /**
     * 导出结算详情
     */
    detailsExport: (params: IWyyB2bDetailsRequest): Promise<void> => {
        return download('balance/api/b2b/wyy/details/export', params)
    },

    /**
     * 已读CVP错误消息
     */
    markRead: (params: IWyyB2bMarkReadRequest): Promise<void> => {
        return post('balance/api/b2b/wyy/mark/read', params)
    },

    /**
     * 获取推送CVP失败且未读的汇总单
     */
    pushCvpErrorAccount: (params: IWyyB2bListRequest): Promise<number> => {
        return get('balance/api/b2b/wyy/push/cvp/error/account', params)
    },
}