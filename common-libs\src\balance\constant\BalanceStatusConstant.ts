
type keys = 'CANCEL' | 'ACCOUNTED' | 'CONFIRMED' | 'SETTLED';

/**
 * 结算状态
 */
export const BalanceStatusConstant = {
  CANCEL: { "code": 0, "name": "已取消" },
  ACCOUNTED: { "code": 10, "name": "已汇总" },
  CONFIRMED: { "code": 20, "name": "已确认" },
  SETTLED: { "code": 30, "name": "已结算" },

  ofCode: (code?: number): { "code": number, "name": string } | null => {
    for (const key in BalanceStatusConstant) {
      const item = BalanceStatusConstant[key as keys];
      if (code === item.code) {
        return item;
      }
    }
    return null;
  }
}