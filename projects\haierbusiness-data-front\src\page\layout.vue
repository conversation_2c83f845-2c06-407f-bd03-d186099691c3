<script setup lang="ts">
import {
  Dropdown as hDropdown,
  Avatar as hAvatar,
  BreadcrumbItem as hBreadcrumbItem,
  <PERSON>readcrumb as hBreadcrumb,
  LayoutSider as hLayoutSider,
  LayoutFooter as hLayoutFooter,
  Layout<PERSON>ontent as hLayoutContent,
  LayoutHeader as hLayoutHeader,
  Layout as hLayout,
  MenuItem as hMenuItem,
  MenuItemGroup as hMenuItemGroup,
  SubMenu as hSubMenu,
  Menu as hMenu,
  Divider as hDivider,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Result as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Tabs as hTabs,
  message,
} from 'ant-design-vue';
import { PropType, onMounted, ref, toRef } from "vue";
import {
  RightOutlined,
  LeftOutlined,
  UploadOutlined,
  UserOutlined,
  NotificationOutlined,
  AppstoreOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons-vue';

import logoWhite from "@/assets/image/logo-white.png";
import {
  HeaderConstant,
  IOwnApplicationCodesResponse,
  IResourceInfo,
  IResourceInfoTreeResponse,
  IResourceTreeNode,
  ResourceTypeConstant,
} from "@haierbusiness-front/common-libs";
import { resourceApi, loginApi } from "@haierbusiness-front/apis";
import {
  loadDataFromLocal,
  removeStorageItem,
  resolveToken,
} from "@haierbusiness-front/utils";
import { computed } from "vue";
// import { useRouter } from "vue-router";

import { storeToRefs } from "pinia";
import { applicationStore } from "@haierbusiness-front/utils/src/store/applicaiton";
import { errorModal, getCurrentRouter, routerParam } from "@haierbusiness-front/utils";
const store = applicationStore();
const { loginUser } = storeToRefs(store);
const router = getCurrentRouter();

// const router = useRouter();

const collapsed = ref<boolean>(false);
const selectedModel = ref<string[]>([]);

const props = defineProps({
  param: Object as PropType<IResourceInfoTreeResponse[]>,
});
const resource = ref<IResourceInfoTreeResponse[]>(props.param ? props.param : []);
const applications = ref<IResourceInfoTreeResponse[]>([]);

let hashUrl = window.location.hash;
if (hashUrl.includes("?")) {
  hashUrl = hashUrl.substring(0, hashUrl.indexOf("?"));
}
if (!hashUrl.endsWith("/")) {
  hashUrl = hashUrl + "/";
}
for (let i of resource.value) {
  let path = i.url ? i.url : "";
  if (i.type === ResourceTypeConstant.MANAGE_APPLICATION.type) {
    path = path.substring(path.indexOf("/#/")).substring(2);
  }
  if (!path.startsWith("#")) {
    path = "#" + path;
  }
  if (!path.endsWith("/")) {
    path = path + "/";
  }

  if (hashUrl.startsWith(path)) {
    selectedModel.value = [(i.id as unknown) as string];
  }
}

const getCurrentMenuItem = (id: number) => {
  for (let i of resource.value) {
    if (i.id === id) {
      return i.children;
    }
  }
  return null;
};

const eMenu = ref();
const currentMenuItem = ref();
currentMenuItem.value = getCurrentMenuItem(Number(selectedModel.value[0]));
const selectMenu = (value: { item: any; key: any; selectedKeys: any }) => {
  currentMenuItem.value = getCurrentMenuItem(value.key);
  if (eMenu.value) {
    eMenu.value.clean();
  }
};
const switchTag = (item: IResourceInfoTreeResponse) => {
  if (item.url) {
    const urlObject = new URL(item.url);
    urlObject.searchParams.append(
      HeaderConstant.TOKEN_KEY.key,
      loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false)
    );
    window.location.href = urlObject.href;
  } else {
    message.error("当前菜单不存在url属性,无法切换");
  }
};
const avatarName = computed(() => {
  if (loginUser?.value?.nickName) {
    const nickName = loginUser.value.nickName;
    if (nickName.length === 2) {
      return nickName;
    } else if (nickName.length > 2 && nickName.length <= 5) {
      return nickName.substring(nickName.length - 2, nickName.length);
    } else {
      return nickName.substring(0, 1).toUpperCase();
    }
  } else {
    return "李白";
  }
});

const logout = () => {
  loginApi
    .haierIamTokenLogout({ token: loginUser?.value?.extended?.iamToken })
    .finally(() => {
      removeStorageItem(HeaderConstant.TOKEN_KEY.key, false);
      window.location.reload();
    });
};

const back = () => {
  router.go(-1);
};

const go = () => {
  router.go(1);
};
const showHeader = computed(() => {
  return router.currentRoute.value.href.includes("/data/report/permission/apply");
});
onMounted(async () => {
  applications.value = await resourceApi.searchApplicationCodes().then((it) => {
    return it;
  });
});
</script>

<template>
  <h-layout-header class="header" v-if="showHeader">
    <h-row style="line-height: 48px">
      <h-col :span="3">
        <div class="logo-div">
          <img :src="logoWhite" class="logo-img" />
        </div>
      </h-col>
      <h-col :span="18"> </h-col>
      <h-col :span="3" style="line-height: 10px;padding-right: 20px;">
        <div style="width: 100%; height: 100%; text-align: right; padding-top: 4px">
          <h-dropdown :trigger="['click']">
            <a @click.prevent>
              <h-avatar
                :title="loginUser?.username"
                size="large"
                style="color: #f56a00; background-color: #fde3cf; user-select: none"
                >{{ avatarName }}</h-avatar
              >
            </a>
            <template #overlay>
              <h-menu>
                <h-menu-item key="0">
                  <h-button @click="logout" type="link">登出</h-button>
                </h-menu-item>
              </h-menu>
            </template>
          </h-dropdown>
        </div>
      </h-col>
    </h-row>
  </h-layout-header>
  <router-view v-slot="{ Component }">
    <keep-alive>
      <component :is="Component" />
    </keep-alive>
  </router-view>
</template>

<style scoped lang="less">
.header {
background-color: rgb(0,66,153);
  height: 48px;
}

.logo-div {
  text-align: center;
  width: 92%;
  height: 48px;

  .logo-img {
    height: 26px;
    object-fit: cover;
  }
}

.trigger {
  font-size: 18px;
  line-height: 44px;
  padding: 3px 24px 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.navigation-button {
  padding-right: 12px;
  cursor: pointer;

  :hover {
    color: #0073e5;
  }
}
</style>
