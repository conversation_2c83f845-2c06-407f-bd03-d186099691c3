import { IPageRequest } from "../../basic";

export class INoticeContractExpirationFilter extends IPageRequest {
    id?:string
    //场景id
    sceneId?:string
    //通知人信息
    sceneContractNotifierDTOList?:{
        nickName:string | null | undefined,
        email:string | null | undefined,
        userName:string | null | undefined,
    }
    //业务类型
    businessType?:string

    //搜索通知人
    userName?:string
    //合同过期通知前置时间天
    expiredNoticeDay?:string

    begin?:string
    end?:string
}


export class INoticeContractExpiration {
    id?: number | null
    creator?:string
    createTime?: string
    updater?: string
    updateTime?: string

    //场景id
    sceneId?:string
    //通知人信息
    sceneContractNotifierDTOList?:{
        nickName:string | null | undefined,
        email:string | null | undefined,
        userName:string | null | undefined,
        id:string | null | undefined,
    }
    //业务类型
    businessType?:string
    //合同过期通知前置时间天
    expiredNoticeDay?:string
}