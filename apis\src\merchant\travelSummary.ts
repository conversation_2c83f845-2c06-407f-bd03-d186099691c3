import { download, get, post, originalPost } from '../request'
import { 
    IDiscountFilter, 
    IDiscount,
    IPageResponse, 
    ITriveFilter,
    Result
 } from '@haierbusiness-front/common-libs'


export const travelSummaryApi = {

    getReport: (params: ITriveFilter): Promise<any> => {
        return get('/data/api/travel_sum/getReport', params)
    },
    getUserAvatar: (params: ITriveFilter): Promise<IPageResponse<Result>> => {
        return get('/data/api/travel_sum/getUserAvatar', params)
    },
    // 是否已读
    readNotify: (params: ITriveFilter): Promise<IPageResponse<Result>> => {
        return get('/data/api/travel_sum/readNotify', params)
    },
}
