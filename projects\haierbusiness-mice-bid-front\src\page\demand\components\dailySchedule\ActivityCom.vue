<script setup lang="ts">
// 拓展活动
import {
  Form as hForm,
  FormItem as hFormItem,
  Select as hSelect,
  SelectOption as hSelectOption,
  Input as hInput,
  InputNumber as hInputNumber,
  DatePicker as hDatePicker,
  RangePicker as hRangePicker,
  Textarea as hTextarea,
  Button as hButton,
  Row as hRow,
  Col as hCol,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  Modal as hModal,
  Toolt<PERSON> as hTooltip,
  Collapse as hCollapse,
  CollapsePanel as hCollapsePanel,
  Upload as hUpload,
  message,
  Upload,
} from 'ant-design-vue';
import { onMounted, ref, reactive, watch, defineProps, defineEmits, toRaw, defineExpose } from 'vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import { ActivitiesArr } from '@haierbusiness-front/common-libs';
import type { UploadProps } from 'ant-design-vue';

import { fileApi, demandApi } from '@haierbusiness-front/apis';

const props = defineProps({
  dateIndex: {
    type: Number,
    default: 0,
  },
  meetingPersonTotal: {
    // 会议人数
    type: Number,
    default: 0,
  },
  activityList: {
    type: Array,
    default: [],
  },
});

const emit = defineEmits(['demandPlanActivityFunc', 'demandPlanRemoveFunc']);

const demandPlanFormRef = ref();
const uploadLoading = ref<boolean>(false);
const isLt50M = ref<boolean>(true);

// 日程安排表单
const formState = reactive<ActivitiesArr>({
  activities: [], // 拓展活动
});

// 拓展活动列表
watch(
  () => props.activityList,
  (newVal) => {
    formState.activities = [...newVal];
  },
  {
    immediate: true,
    deep: true,
  },
);

// 删除
const removeAttendant = (type: String, index: Number) => {
  emit('demandPlanRemoveFunc', { type: type, delIndex: index, index: props.dateIndex });
};

// 提交
const onSubmit = async () => {
  let isVerifyPassed = false;

  await demandPlanFormRef.value
    .validate()
    .then(() => {
      emit('demandPlanActivityFunc', { list: [...formState.activities], index: props.dateIndex });

      isVerifyPassed = true;
    })
    .catch((err) => {
      isVerifyPassed = false;
    });

  return isVerifyPassed;
};
defineExpose({ onSubmit });

// 上传附件 - 删除
const handleRemove: UploadProps['onRemove'] = (file) => {
  formState.activities.forEach((e) => {
    e.paths = [];

    e.fileList.forEach((j) => {
      if (j.name !== file.name) {
        const params = {
          name: j.name,
          url: j.filePath,
        };

        e.paths.push(JSON.stringify(params));
      }
    });
  });
};

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  isLt50M.value = file.size / 1024 / 1024 < 50;

  if (!isLt50M.value) {
    message.error('文件最大不超过50M！');
    return Upload.LIST_IGNORE;
  }

  return isLt50M.value;
};

// 上传附件
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;

      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框

      formState.activities.forEach((e) => {
        e.paths = [];

        e.fileList.forEach((j) => {
          const params = {
            name: j.name,
            url: j.filePath,
          };

          e.paths.push(JSON.stringify(params));
        });
      });
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 价格测算
const calcPrice = async (i: Number) => {
  if (formState.activities[i].demandUnitPrice && formState.activities[i].personNum) {
    const calcParams = {
      calcDate: formState.activities[i].demandDate + ' 00:00:00', // 需求日期
      demandUnitPrice: formState.activities[i].demandUnitPrice, // 人员类型
      personNum: formState.activities[i].personNum, // 人数

      description: formState.activities[i].description, // 	工作范围
    };

    const res = await demandApi.priceCalcActivity({
      ...calcParams,
    });

    console.log('%c [ 拓展活动-自动测算单价 ]-120', 'font-size:13px; background:pink; color:#bf2c9f;', res);

    formState.activities[i].calcUnitPrice = res.calcUnitPrice; //
  }
};

onMounted(async () => {});
</script>

<template>
  <!-- 拓展活动 -->
  <div class="activity_com">
    <h-form ref="demandPlanFormRef" :model="formState" :labelCol="{ style: { width: '84px' } }" hideRequiredMark>
      <div
        class="plan_col_list mb20"
        v-for="(activitiesItem, activitiesIndex) in formState.activities"
        :key="activitiesIndex"
      >
        <div class="plan_col_title">
          {{ '拓展活动' + (activitiesIndex + 1) }}
        </div>
        <div class="plan_col_del" @click="removeAttendant('activity', activitiesIndex)"></div>

        <h-row :gutter="12" class="mt20">
          <h-col :span="8">
            <h-form-item
              label="费用标准："
              :name="['activities', activitiesIndex, 'demandUnitPrice']"
              :rules="{
                required: true,
                message: '请填写费用标准',
                trigger: 'change',
              }"
            >
              <h-input-number
                v-model:value="activitiesItem.demandUnitPrice"
                @blur="calcPrice(activitiesIndex)"
                placeholder="请填写费用标准"
                addon-after="元/人"
                allow-clear
                :min="1"
                :max="999999"
                :precision="2"
                style="width: 100%"
              />
            </h-form-item>
          </h-col>

          <h-col :span="8">
            <h-form-item
              label="参与人数："
              :name="['activities', activitiesIndex, 'personNum']"
              :rules="{
                required: true,
                message: '请填写参与人数',
                trigger: 'change',
              }"
            >
              <h-input-number
                v-model:value="activitiesItem.personNum"
                @blur="calcPrice(activitiesIndex)"
                placeholder="请填写参与人数"
                allow-clear
                :min="1"
                :max="props.meetingPersonTotal || 99999"
                style="width: 100%"
              />
            </h-form-item>
          </h-col>

          <h-col :span="16">
            <h-form-item
              label="活动需求："
              :name="['activities', activitiesIndex, 'description']"
              :rules="{
                required: true,
                message: '请填写活动需求',
                trigger: 'change',
              }"
            >
              <h-textarea
                v-model:value="activitiesItem.description"
                placeholder="请填写活动需求"
                :autoSize="{ minRows: 3, maxRows: 3 }"
                allow-clear
                :maxlength="500"
              />
            </h-form-item>
          </h-col>

          <h-col :span="16">
            <h-form-item
              label="附件："
              :name="['activities', activitiesIndex, 'fileList']"
              :rules="{
                required: false,
                message: '请上传附件',
                trigger: 'blur',
              }"
            >
              <h-upload
                v-model:fileList="activitiesItem.fileList"
                :custom-request="uploadRequest"
                :multiple="false"
                :max-count="10"
                :before-upload="beforeUpload"
                @remove="handleRemove"
              >
                <!-- accept=".rar, .zip, .pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx" -->
                <h-button>
                  <upload-outlined></upload-outlined>
                  上传附件
                </h-button>
              </h-upload>

              <div :class="['support_extend_tip', 'mt8', isLt50M ? '' : 'err_color']">
                <!-- 支持扩展名：.rar, .zip, .pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx -->
                文件最大不超过50M
              </div>
            </h-form-item>
          </h-col>
        </h-row>
      </div>
    </h-form>
  </div>
</template>

<style scoped lang="less">
.activity_com {
  .plan_col_list {
    padding: 20px 24px;
    background: #fff;
    border-radius: 8px;

    position: relative;

    .plan_col_title {
      background: url('@/assets/image/demand/demand_activity.png');
      background-repeat: no-repeat;
      background-size: 18px 18px;
      background-position: left center;

      text-indent: 28px;
      font-weight: 500;
      font-size: 18px;
      line-height: 25px;
      color: #1d2129;
    }

    .plan_col_del {
      position: absolute;
      top: 12px;
      right: 12px;

      width: 18px;
      height: 18px;
      background: url('@/assets/image/demand/demand_del_x.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      cursor: pointer;
    }

    .support_extend_tip {
      color: #86909c;
      line-height: 22px;
    }

    .err_color {
      color: #ff4d4f;
    }
  }
}
</style>
