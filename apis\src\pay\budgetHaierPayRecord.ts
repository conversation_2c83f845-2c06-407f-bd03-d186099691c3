import {
    IBudgetHaierOccupyRequest,
    IBudgetHaierQueryRequest as IBudgetHaierQueryRequest,
    IBudgetHaierQueryResponse as IBudgetHaierQueryResponse,
    IBudgetHaierTypesRequest,
    IBudgetHaierTypesResponse, IHaierApplicationBudgetFeeItemInfo,
    IHaierBudgetFeeItemQueryRequest, IHaierPaymentBudget, IPageResponse,
    IPayData,
    IPayHeader
} from '@haierbusiness-front/common-libs'
import { IPayRequest, IPayResponse, ICoinHaierAccountResponse, ICoinHaierBalanceResponse, ICoinHaierPayRequest } from '@haierbusiness-front/common-libs'
import { get, post } from '../request'
import {IHaierBudgetPayRecordRequest} from "@haierbusiness-front/common-libs/src/pay/model/budgetHaierPayRecordModel";

export const budgetHaierPayRecordApi = {

    /**
     * 查询预算记录
     */
    list: (params: IHaierBudgetPayRecordRequest): Promise<IPageResponse<IHaierPaymentBudget>> => {
        return get('pay/api/haier/budget/pay/record/list', params)
    },

}
