
type keys = 'WAIT' | 'COMPLETE' | 'CANCEL';

/**
 * 审批待办状态 
 * 1: 待审批 2: 已审批 3:已撤回
 */
export const ProcessTodoStateConstant = {
  WAIT: { "type": 1, "name": "待审批" },
  COMPLETE: { "type": 2, "name": "已审批" },
  CANCEL: { "type": 3, "name": "已撤回" },

  ofType: (type?: number): { "type": number, "name": string } | null => {
    for (const key in ProcessTodoStateConstant) {
      const item = ProcessTodoStateConstant[key as keys];
      if (type === item.type) {
        return item;
      }
    }
    return null;
  },

  toArray:() :({ type: number, name: string } | undefined)[] => {
    const types = Object.keys(ProcessTodoStateConstant).map((i: string) => {
      if(i !== 'ofType' && i !== 'toArray' ) {
        return ProcessTodoStateConstant[i as keys]
      }
      return
    })
    const newTypes = types.filter(function (s) {
      return s && s; 
    })
    return newTypes
  }
}