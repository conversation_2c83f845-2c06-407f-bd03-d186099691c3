import { download, downloadPost, get, post } from '../request';
import { ISendCodeCaptcha, Result, TCteateTeam } from '@haierbusiness-front/common-libs';
export const rechargeApi = {
  // 获取账户
  addTeam: (params: TCteateTeam): Promise<TCteateTeam> => {
    return post('team/api/team/order/create', params);
  },
  // 账户充值
  addRecharge: (params: TCteateTeam): Promise<TCteateTeam> => {
    return post('incentive/api/recharge/save', params);
  },
  // 获取详情
  getAndInitTradeUnionAccount: (params: any): Promise<any> => {
    return get('/pay/api/virtual/tradeUnionAccount/getAndInitTradeUnionAccount', params);
  },
  // 查询充值订单
  getRechargeList: (params: any): Promise<any> => {
    return post('/incentive/api/recharge/page', params);
  },
  // 导出充值订单
  exportRechargeList: (params: any): Promise<any> => {
    return downloadPost('/incentive/api/recharge/export', params);
  },
  // 查询充值订单
  getRechargeDetail: (id: any): Promise<any> => {
    return get('/incentive/api/recharge/get?id=' + id);
  },
  // 关闭订单
  close: (id: any): Promise<any> => {
    return get('/incentive/api/recharge/close?id=' + id);
  },
  // 查询激励订单
  getBoostList: (params: any): Promise<any> => {
    return post('/incentive/api/issue/page', params);
  },
  // 交接提交
  submitHandover: (params: any): Promise<any> => {
    return post('/incentive/api/handover/save', params);
  },
   // 交接详情
   handoverDetail: (id: number): Promise<any> => {
    return get('/incentive/api/handover/get?id='+id);
  },
  // 去支付
  gotoPay: (code: string): Promise<any> => {
    return post('/incentive/api/recharge/toPay?orderCode='+code);
  },
  // 查询充值订单
  getRechargeWater: (params: any): Promise<any> => {
    return get('/pay/api/virtual/tradeUnionAccount/accountFlowingWater', params);
  },

  // 上传激励人员名单
  peopleImport: (params: FormData): Promise<any> => {
    return post('/incentive/api/issue/import', params);
  },
  // 下载模板
  downloadTemplate: (): Promise<TCteateTeam> => {
    return downloadPost('/incentive/api/issue/downLoadTemplate');
  },
  // 激励下发保存
  issueSave: (params: FormData): Promise<TCteateTeam> => {
    return post('/incentive/api/issue/save', params);
  },

  /**
   * 发送支付验证码
   */
  sendCodeCaptcha: (params: ISendCodeCaptcha): Promise<string> => {
    return get('/incentive/api/issue/send', params);
  },
  // 导出充值订单
  exportExcitationList: (params: any): Promise<any> => {
    return downloadPost('/incentive/api/issue/export', params);
  },
  // 查询激励订单详情
  getExcitationDetail: (id: any): Promise<any> => {
    return get('/incentive/api/issue/get?id=' + id);
  },
  // 预算充值列表
  exportRechargeAdminList: (params: any): Promise<any> => {
    return downloadPost('/incentive/api/admin/recharge/export', params);
  },
  // 账户流水记录
  getFlowingWaterByAccountPage: (params: any): Promise<any> => {
    return get('/pay/api/virtual/tradeUnionAccount/getFlowingWaterByAccountPage', params);
  },
  // 导出账户流水记录
  exportFlowingWaterByAccount: (params: any): Promise<any> => {
    return download('/pay/api/virtual/tradeUnionAccount/exportFlowingWaterByAccount', params);
  },
  // 管理端查询充值订单
  getAdminRechargeList: (params: any): Promise<any> => {
    return post('/incentive/api/admin/recharge/page', params);
  },
  // 管理端查询充值订单
  getAdminRechargeDetail: (id: any): Promise<any> => {
    return get('/incentive/api/admin/recharge/get?id=' + id);
  },
  //获取直线
  getDirectLine: (params: any): Promise<any> => {
    return get('/system/api/user/one', params);
  },
};
