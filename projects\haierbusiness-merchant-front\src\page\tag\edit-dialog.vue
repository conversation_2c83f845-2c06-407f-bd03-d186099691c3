<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
} from 'ant-design-vue';
import { computed, ref, watch } from 'vue';
import type { Ref } from 'vue';
import { ITag, BusinessTypeEnums } from '@haierbusiness-front/common-libs';
import { max } from 'lodash';

interface Props {
  show: boolean;
  data: ITag | null;
  businessTypeList: BusinessTypeEnums[] | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  businessTypeList: null,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: ITag = {
  tagName: '',
  businessType: '',
  state: 0,
  description: '',
};

const rules = {
  tagName: [{ required: true, message: '请输入标签名称', trigger: 'change' },],
  businessType: [{ required: true, message: '请选择类别', trigger: 'change' }],
};

const tag: Ref<ITag> = ref(props.data ? { ...props.data } : defaultData)
  
  watch(
    () => props.data,
    (newData) => {
      tag.value = newData ? { ...newData } : defaultData
      console.log(tag.value);
      
    },
    { deep: true }
  )

const emit = defineEmits(['cancel', 'ok']);

const visible = computed(() => props.show);

const handleOk = () => {
  confirmLoading.value = true;
  from.value
    .validate()
    .then(() => {
      emit('ok', tag.value, () => {
        confirmLoading.value = false;
      });
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};
</script>

<template>
  <h-modal
    v-model:visible="visible"
    :title="tag.id ? '编辑标签' : '新增标签'"
    :width="600"
    @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
  >
    <h-form ref="from" :model="tag" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules">
      <h-form-item label="标签名称" name="tagName">
        <h-input v-model:value="tag.tagName" :maxlength="200"/>
      </h-form-item>
      <h-form-item label="类别" name="businessType">
        <h-select style="width: 100%" v-model:value="tag.businessType" allow-clear>
          <a-select-option v-for="(item, index) in props.businessTypeList" :key="index" :value="item.key">{{
            item.value
          }}</a-select-option>
        </h-select>
      </h-form-item>
      <h-form-item label="状态" name="state">
        <h-select v-model:value="tag.state">
          <h-select-option :value="1">正常</h-select-option>
          <h-select-option :value="0">禁用</h-select-option>
        </h-select>
      </h-form-item>
      <h-form-item label="标签描述" name="description">
        <h-textarea v-model:value="tag.description" />
      </h-form-item>
    </h-form>
  </h-modal>
</template>


<style lang="less" scoped>
.important {
  color: red;
}
</style>
  