// 酒店星级

type keys = 'ONE' | 'TWO' | 'THREE' | 'FOUR' | 'THREE_FOUR' | 'FIVE' | 'FOUR_FIVE' | 'THREE_FOUR_FIVE';

export const hotelLevelConstant = {
  // 无星级-1
  // 一星级-2
  // 二星级-4
  // 三星级-8
  // 四星级-16
  // 五星级-32
  ONE: { code: 7, desc: '2钻/星及以下' },
  // TWO: { code: 15, desc: '3钻/星及以下' },

  THREE: { code: 8, desc: '3钻/星' },
  FOUR: { code: 16, desc: '4钻/星' },
  // THREE_FOUR: { code: 24, desc: '3钻/星或4钻/星' },
  FIVE: { code: 32, desc: '5钻/星' },
  // FOUR_FIVE: { code: 48, desc: '4钻/星或5钻/星' },
  // THREE_FOUR_FIVE: { code: 56, desc: '3钻/星或4钻/星或5钻/星' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in hotelLevelConstant) {
      const item = hotelLevelConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(hotelLevelConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return hotelLevelConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
