<script setup lang="ts">
import { MiceTypeConstant, DistrictTypeConstant } from '@haierbusiness-front/common-libs';
import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons-vue';
import NotificationImg from '@/assets/image/home/<USER>';
import WarningImg from '@/assets/image/home/<USER>';
import ChangeImg from '@/assets/image/home/<USER>';
import MeetingNameImg from '@/assets/image/home/<USER>';
import MeetingTimeImg from '@/assets/image/home/<USER>';
import MeetingPlaceImg from '@/assets/image/home/<USER>';
import MeetingCountImg from '@/assets/image/home/<USER>';
import MeetingTypeImg from '@/assets/image/home/<USER>';
import { InstructionModal } from '.';
import { computed, ref, onMounted, onUnmounted, watch } from 'vue';
import { usePortalStore } from '../store';
import { message, Modal } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';

import video1 from '@/assets/image/111.mp4';
import video2 from '@/assets/image/222.mp4';
import video3 from '@/assets/image/333.mp4';
import video4 from '@/assets/image/444.mp4';
const videoUrl = ref<number>(1);

const store = usePortalStore();
const meetingTypeOptions = MiceTypeConstant.toArray().map((item) => ({ label: item.desc, value: item.code }));
const districtTypeOptions = computed(() =>
  DistrictTypeConstant.toArray().map((item) => ({ label: item.desc, value: item.code })),
);

const inputRef1 = ref(null);
const inputRef2 = ref(null);

const validate = () => {
  const formInfo = [
    { name: 'miceName', label: '会议名称', rules: [{ required: true }] },
    { name: 'meetingDate', type: 'array', label: '会议日期', rules: [{ required: true }] },
    { name: 'districtType', label: '会议地点', rules: [{ required: true }] },
    { name: 'personTotal', label: '会议人数', rules: [{ required: true }] },
    { name: 'miceType', label: '会议类型', rules: [{ required: true }] },
  ];
  for (const formItem of formInfo) {
    const validateRules = formItem.rules;
    for (const validateRule of validateRules) {
      if (validateRule.required) {
        const name = formItem.name;
        let messageText = '';

        if (formItem.type === 'array' && !store.meetingInfo[formItem.name]?.length) {
          messageText = validateRule.message || `${formItem.label}不能为空`;
        } else if (!(store.meetingInfo[formItem.name] || store.meetingInfo[formItem.name] === 0)) {
          messageText = validateRule.message || `${formItem.label}不能为空`;
        }

        if (messageText) {
          const modal = Modal.error({
            title: messageText,
          });

          setTimeout(() => {
            modal.destroy();

            switch (name) {
              case 'miceName':
                inputRef1.value.focus();
                break;
              case 'personTotal':
                inputRef2.value.focus();
                break;

              default:
                break;
            }
          }, 3000);

          // message.error({
          //   content: () => messageText,
          //   style: {
          //     marginTop: '30vh',
          //   },
          // });

          return false;
        }
      }
    }
  }
  return true;
};

const handleDemandSubmit = () => {
  const validateRes = validate();
  if (validateRes) {
    store.instructionModalOpen = true;
  }
};
const disabledDate = (current: Dayjs) => {
  return current && current < dayjs().endOf('day');
};
const Media = () => {
  const demandSubmit = document.getElementById('demand-submit');
  const handler = () => {
    if (window.innerWidth <= 1099) {
      demandSubmit.style.transform = 'scale(0.5)';
    } else if (window.innerWidth <= 1280) {
      demandSubmit.style.transform = 'scale(0.6)';
    } else if (window.innerWidth <= 1536) {
      demandSubmit.style.transform = 'scale(0.8)';
      demandSubmit.style.marginTop = '26px';
    } else {
      demandSubmit.style.transform = 'scale(1)';
      demandSubmit.style.marginTop = '0px';
    }
  };
  window.addEventListener('resize', handler);
  handler(); // 立即执行一次
  return () => window.removeEventListener('resize', handler); // 返回清理函数
};

const changeVideo = (idx: number) => {
  videoUrl.value = idx;
};

onMounted(() => {
  const cleanup = Media();
  watch(
    () => window.innerWidth,
    () => Media(),
    { immediate: true },
  );
  onUnmounted(cleanup);
});
</script>

<template>
  <div class="banner">
    <video v-if="videoUrl === 1" autoplay loop muted class="banner_video">
      <source :src="video1" type="video/mp4" />
    </video>
    <video v-if="videoUrl === 2" autoplay loop muted class="banner_video">
      <source :src="video2" type="video/mp4" />
    </video>
    <video v-if="videoUrl === 3" autoplay loop muted class="banner_video">
      <source :src="video3" type="video/mp4" />
    </video>
    <video v-if="videoUrl === 4" autoplay loop muted class="banner_video">
      <source :src="video4" type="video/mp4" />
    </video>
    <div class="banner_change">
      <div class="banner_change_btn" v-for="idx in 4" @click="changeVideo(idx)">{{ idx }}</div>
    </div>

    <div class="notice">
      <div class="icon flex-center">
        <img :src="NotificationImg" />
        <span>通知</span>
      </div>
      <span class="notice-text">海易智会系统操作指南</span>
      <span class="arrow" />
    </div>
    <span class="banner-title">海易智会 您身边的会议专家</span>
    <div class="demand-submit" id="demand-submit">
      <div class="demand-submit-top">
        <img :src="WarningImg" />
        <span>2025年年度预算待提报，</span>
        <a>点击立即提报</a>
      </div>
      <div class="demand-submit-center">
        <div class="demand-submit-center-left flex">
          <div class="demand-submit-card flex" :style="{ flex: 283 }">
            <img :src="MeetingNameImg" />
            <div class="card-content">
              <div class="label">会议名称</div>
              <div class="input-wrapper">
                <h-input
                  ref="inputRef1"
                  class="input"
                  v-model:value="store.meetingInfo.miceName"
                  :maxlength="50"
                  placeholder="填写会议名称"
                />
              </div>
            </div>
          </div>
          <div class="demand-submit-card flex" :style="{ flex: 289 }">
            <img :src="MeetingTimeImg" />
            <div class="card-content">
              <div class="label">会议时间</div>
              <div class="input-wrapper flex acenter">
                <h-range-picker
                  class="input"
                  v-model:value="store.meetingInfo.meetingDate"
                  :disabledDate="disabledDate"
                  allow-clear
                >
                  <template #suffixIcon></template>
                </h-range-picker>
              </div>
            </div>
          </div>
          <div class="demand-submit-card flex" :style="{ flex: 253 }">
            <img :src="MeetingPlaceImg" />
            <div class="card-content">
              <div class="label">会议地点</div>
              <div class="input-wrapper">
                <h-select
                  class="input"
                  v-model:value="store.meetingInfo.districtType"
                  :options="districtTypeOptions"
                  placeholder="选择会议地点"
                />
              </div>
            </div>
          </div>
          <div class="demand-submit-card flex" :style="{ flex: 171 }">
            <img :src="MeetingCountImg" />
            <div class="meeting-count card-content">
              <div class="label">会议人数</div>
              <a-input-number
                ref="inputRef2"
                class="input"
                :bordered="false"
                v-model:value="store.meetingInfo.personTotal"
                placeholder="填写人数"
                :min="1"
                :max="999999"
              />
              <!-- <div class="input">
                <div class="meeting-count-value">{{ store.meetingInfo.personTotal }}人</div>
                <div class="meeting-count-buttons">
                  <div @click="store.meetingInfo.personTotal += 1"><CaretUpOutlined :style="{ fontSize: '10px' }" /></div>
                  <div @click="store.meetingInfo.personTotal > 0 && (store.meetingInfo.personTotal -= 1)"><CaretDownOutlined :style="{ fontSize: '10px' }" /></div>
                </div>
              </div> -->
            </div>
          </div>
          <div class="demand-submit-card flex" :style="{ flex: 226 }">
            <img :src="MeetingTypeImg" />
            <div class="card-content">
              <div class="label">会议类型</div>
              <div class="input-wrapper">
                <h-select
                  class="input"
                  v-model:value="store.meetingInfo.miceType"
                  :options="meetingTypeOptions"
                  placeholder="选择会议类型"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="demand-submit-center-right">
          <div class="demand-submit-button" @click="handleDemandSubmit">提报需求</div>
          <h-checkbox v-model:checked="store.meetingInfo.demandType">
            <span class="demand-submit-checkbox-text">顾问代提</span>
          </h-checkbox>
        </div>
      </div>
      <div class="demand-submit-bottom flex-center">
        <div class="button flex-center">
          <img :src="ChangeImg" />
          <span>切换至需求详情页面</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.banner {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  min-height: 600px;
  background: url(@/assets/image/banner.png) no-repeat;
  background-size: cover;
  position: relative;

  .banner_video {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;

    width: 100%;
    height: 100%;
    /* height: auto; */

    object-fit: cover;
  }
  .banner_change {
    position: absolute;
    right: 20px;
    top: 100px;

    display: flex;

    .banner_change_btn {
      padding: 0 12px;
      margin-right: 5px;
      background: #3bc6fc;
      color: #fff;
      cursor: pointer;
    }
  }

  .notice {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px 0 6px;
    width: 316px;
    height: 38px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 19px;
    backdrop-filter: blur(1px);
    margin-top: 117px;
    &:hover {
      .arrow {
        background-position: left 0px bottom 0px;
      }
    }
    .icon {
      width: 76px;
      height: 30px;
      background: linear-gradient(270deg, #3bc6fc 0%, #1868db 100%);
      border-radius: 15px;
      color: #ffffff;
      > img {
        width: 18px;
        margin-right: 6px;
      }
    }
    .notice-text {
      font-weight: 500;
      font-size: 16px;
      color: #1d2129;
      line-height: 22px;
    }
    .arrow {
      display: block;
      width: 22px;
      height: 22px;
      background-image: url(@/assets/image/home/<USER>
      background-size: 44px 44px;
      background-repeat: no-repeat;
      position: relative;
      flex-shrink: 0;
      background-position: right 0px top 0px;
      transition: 0.3s ease-out;
    }
  }
  .banner-title {
    width: 100%;
    margin: 50px 0 36px;
    /* font-family: AlimamaShuHeiTi-Bold; */
    font-size: 62px;
    color: #ffffff;
    line-height: 78px;
    letter-spacing: 1px;
    text-shadow: 0px 4px 8px rgba(0, 0, 0, 0.27);
    font-weight: bold;
    text-align: center;

    z-index: 1;
  }
  .demand-submit {
    width: 1460px;
    /* height: 230px; */
    padding: 20px 30px 16px;
    background: #ffffff;
    box-shadow: 0px 8px 20px 0px rgba(1, 31, 80, 0.5);
    border-radius: 12px;
    backdrop-filter: blur(0px);
    .demand-submit-top {
      display: flex;
      align-items: center;
      > img {
        width: 22px;
        margin-right: 11px;
      }
      > span {
        font-size: 16px;
        color: #4e5969;
        line-height: 22px;
      }
      > a {
        font-weight: 500;
        font-size: 16px;
        color: #1868db;
        line-height: 22px;
        text-decoration-line: underline;
      }
    }
    .demand-submit-center {
      margin-top: 17px;
      display: flex;
      .demand-submit-center-left {
        flex: 1;
        height: 88px;
        background: #ffffff;
        border-radius: 12px;
        border: 1px solid #e5e6eb;
        margin-right: 10px;
        .demand-submit-card {
          align-items: center;
          padding: 0 16px;
          border-right: 1px solid #e5e6eb;
          &:last-child {
            border-right: none;
          }
          > img {
            width: 20px;
            height: 20px;
            margin-right: 16px;
          }
          .label {
            font-size: 16px;
            color: #86909c;
            line-height: 22px;
            margin-bottom: 8px;
          }
          .input-wrapper {
            margin-left: -8px;
            .input {
              width: 100%;
              border: none;
              &:focus,
              &.ant-picker-focused,
              &.ant-input-number-focused {
                box-shadow: none;
              }
              :deep(.ant-select-selector) {
                box-shadow: none;
                border: none !important;
              }
              :deep(.ant-select-arrow) {
                display: none;
              }
            }
          }
          .card-content {
            flex: 1;
          }
          .meeting-count {
            .input {
              :deep(.ant-input-number-input) {
                padding-left: 0;
              }
            }
          }
        }
      }
      .demand-submit-center-right {
        text-align: center;
        .demand-submit-button {
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 168px;
          height: 56px;
          background: linear-gradient(176deg, #3bc6fc 0%, #1868db 100%, #1868db 100%);
          border-radius: 12px;
          font-weight: 500;
          font-size: 20px;
          color: #ffffff;
          margin-bottom: 8px;

          &:hover {
            background: linear-gradient(
              176deg,
              rgba(59, 198, 252, 0.9) 0%,
              rgba(24, 104, 219, 0.9) 100%,
              rgba(24, 104, 219, 0.9) 100%
            );
          }
        }
        .demand-submit-checkbox-text {
          font-weight: 500;
          font-size: 16px;
          color: #1d2129;
          line-height: 22px;
        }
      }
    }
    .demand-submit-bottom {
      margin-top: 16px;
      .button {
        cursor: pointer;
        width: 220px;
        height: 42px;
        background: rgba(24, 104, 219, 0.08);
        border-radius: 6px;
        backdrop-filter: blur(12px);
        > img {
          width: 20px;
          margin-right: 12px;
        }
        > span {
          font-weight: 500;
          font-size: 16px;
          color: #1868db;
          line-height: 22px;
        }
      }
    }
  }
}
</style>
