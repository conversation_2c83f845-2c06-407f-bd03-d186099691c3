// 用车型号

type keys =
  | 'FOUR0'
  | 'FOUR1'
  | 'FOUR2'
  | 'FOUR3'
  | 'FOUR4'
  | 'FOUR5'
  | 'FOUR6'
  | 'FOUR7'
  | 'FOUR8'
  | 'FIVE0'
  | 'FIVE1'
  | 'FIVE2'
  | 'FIVE3'
  | 'FIVE4'
  | 'FIVE5'
  | 'FIVE6'
  | 'FIVE7'
  | 'FIVE8'
  | 'SEVEN0'
  | 'SEVEN1'
  | 'SEVEN2'
  | 'SEVEN3'
  | 'TWELVE'
  | 'NINETEEN0'
  | 'NINETEEN1'
  | 'THIRTY_ONE'
  | 'FORTY_NINE0'
  | 'FORTY_NINE1'
  | 'FIFTY_ONE';

// TODO - 车型、型号：参考老系统枚举，
export const CarBrandTypeConstant = {
  FOUR0: { code: '奥迪A6L', desc: '奥迪A6L', seatNum: 4 },
  FOUR1: { code: '迈腾', desc: '迈腾', seatNum: 4 },
  FOUR2: { code: '帕萨特', desc: '帕萨特', seatNum: 4 },
  FOUR3: { code: '雅阁', desc: '雅阁', seatNum: 4 },
  FOUR4: { code: '凯美瑞', desc: '凯美瑞', seatNum: 4 },
  FOUR5: { code: '奔驰', desc: '奔驰', seatNum: 4 },
  FOUR6: { code: '丰田', desc: '丰田', seatNum: 4 },
  FOUR7: { code: '奥迪', desc: '奥迪', seatNum: 4 },
  FOUR8: { code: '宝马', desc: '宝马', seatNum: 4 },

  FIVE0: { code: '奥迪A6L', desc: '奥迪A6L', seatNum: 5 },
  FIVE1: { code: '迈腾', desc: '迈腾', seatNum: 5 },
  FIVE2: { code: '帕萨特', desc: '帕萨特', seatNum: 5 },
  FIVE3: { code: '雅阁', desc: '雅阁', seatNum: 5 },
  FIVE4: { code: '凯美瑞', desc: '凯美瑞', seatNum: 5 },
  FIVE5: { code: '奔驰', desc: '奔驰', seatNum: 5 },
  FIVE6: { code: '丰田', desc: '丰田', seatNum: 5 },
  FIVE7: { code: '奥迪', desc: '奥迪', seatNum: 5 },
  FIVE8: { code: '宝马', desc: '宝马', seatNum: 5 },

  SEVEN0: { code: '丰田', desc: '丰田', seatNum: 7 },
  SEVEN1: { code: '别克GL8', desc: '别克GL8', seatNum: 7 },
  SEVEN2: { code: '奔驰商务', desc: '奔驰商务', seatNum: 7 },
  SEVEN3: { code: '奥迪', desc: '奥迪', seatNum: 7 },

  TWELVE: { code: '丰田', desc: '丰田', seatNum: 12 },

  NINETEEN0: { code: '丰田', desc: '丰田', seatNum: 19 },
  NINETEEN1: { code: '考斯特', desc: '考斯特', seatNum: 19 },

  THIRTY_ONE: { code: '考斯特', desc: '考斯特', seatNum: 31 },

  FORTY_NINE0: { code: '旅行大巴', desc: '旅行大巴', seatNum: 49 },
  FORTY_NINE1: { code: '宇通', desc: '宇通', seatNum: 49 },

  FIFTY_ONE: { code: '大巴车', desc: '大巴车', seatNum: 51 },

  ofType: (type?: string): { code: string; desc: string } | null => {
    for (const key in CarBrandTypeConstant) {
      const item = CarBrandTypeConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: string; desc: string } | undefined)[] => {
    const types = Object.keys(CarBrandTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return CarBrandTypeConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
