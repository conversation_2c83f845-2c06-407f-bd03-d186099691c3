import { download, get, post } from '../request'
import {
    ServiceExamFilter,
    ServiceExam,
    IPageResponse,
    Result
} from '@haierbusiness-front/common-libs'

export const serviceExamApi = {
    // 获取列表
    list: (params: ServiceExamFilter): Promise<IPageResponse<ServiceExam>> => {
        return get('/mice-bid/api/mice/merchant/service/exam/page/exam', params)
    },
    // 获取列表
    getlist: (params: ServiceExamFilter): Promise<IPageResponse<ServiceExam>> => {
        return get('/mice-bid/api/mice/merchant/service/exam/merchant/page/exam', params)
    },
    //获取详情
    get: (id: number): Promise<ServiceExam> => {
        return get('/mice-bid/api/mice/merchant/service/exam/exam/detail', {
            id
        })
    },
    // 服务商处理详情记录分页查询
    listProcessing: (params: ServiceExamFilter): Promise<IPageResponse<ServiceExam>> => {
        return get('/mice-bid/api/mice/merchant/service/exam/page/record', params)
    },

    //处理详情
    getProcessingDetail: (id: number): Promise<ServiceExam> => {
        return get('/mice-bid/api/mice/merchant/service/exam/dispose/detail', {
            id
        })
    },
    // 后台管理考核处理保存接口
    saveMerchant: (params: ServiceExam): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/service/exam/save/merchant', params)
    },

    // 创建服务商考核接口
    createExam: (params: ServiceExam): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/service/exam/create/exam', params)
    },


    // 服务商考核处理保存接口
    saveService: (params: ServiceExam): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/service/exam/merchant/save/admin', params)
    },
    // 分页获取考核条目
    getExamItem: (params: ServiceExamFilter): Promise<IPageResponse<ServiceExam>> => {
        return get('/mice-bid/api/mice/merchant/exam/item/page', params)
    },
    // 服务商考核汇总接口
    getMerchantSummary: (params: ServiceExamFilter): Promise<IPageResponse<ServiceExam>> => {
        return get('/mice-bid/api/mice/merchant/service/exam/merchant/summary', params)
    },

    // 根据服务商名称获取会议信息
    getMeetingInfo: (params: ServiceExamFilter): Promise<IPageResponse<ServiceExam>> => {
        return get('/mice-bid/api/mice/merchant/service/exam/mice', params)
    },

    // 撤销服务商考核接口
    cancelExam: (params: ServiceExamFilter): Promise<IPageResponse<ServiceExam>> => {
        return post('/mice-bid/api/mice/merchant/service/exam/cancel/exam', params)
    },
    // 获取撤销原因
    getReason: (params: ServiceExamFilter): Promise<IPageResponse<ServiceExam>> => {
        return get('/mice-bid/api/mice/merchant/service/exam/reason', params)
    },

    // 商户询价单-获取询价单号
    getAssessmentCode: (params: ServiceExamFilter): Promise<IPageResponse<ServiceExam>> => {
        return get('/mice-bid/api/mice/merchant/service/exam/merchant/get/assessment/code', params)
    },
}
