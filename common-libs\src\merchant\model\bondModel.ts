import { IPageRequest } from "../../basic";

export class <PERSON><PERSON>ilt<PERSON> extends IPageRequest {
    createStart?: string
    createEnd?: string
    merchantName?: string
    state?: number
    receiveStart?: string
    receiveEnd?: string
    merchantId?: number
    id?: number
    recordNo?: string
    type?: number
    amount?: number
    receiveName?: string
    receiveTime?: [string,string]
}

export class Bond {
    id?: number | null
    creator?: string
    createTime?: string
    updater?: string
    updateTime?: string
    state?: number
    path?: string[]
    confirmRemark?: string
    pageNum?: number
    pageSize?: number
    type?: number
    amount?: number
    recordNo?: string
    merchantName?: string
    gmtCreate?: string
    receiveTime?: string
    balance?: number
    payPath?: string[]
    receiveName?: string
    remark?: string
    message?: string | null
    sapReceiveNo?: string | null
    receiptPath?: string[]
    refundPath?: string[]
}

export interface ExtendedBond extends Bond {
    type?: number;
    state?: number;
    recordNo?: string;
    amount?: number;
    merchantName?: string;
    gmtCreate?: string;
    paymentVoucher?: string[];
    refundApplication?: string;
    payPath?: string[];
    receiveTime?: string;
    balance?: number;
    remark?: string;
    message?: string | null;
    sapReceiveNo?: string | null;
    receiptPath?: string[];
    refundPath?: string[];
}

export class BondDetail {
    recordNo?: string;
    type?: number;
    amount?: number;
    gmtCreate?: string;
    receiveTime?: string;
    state?: number;
    attachment?: string;
    id?: number;
    payPath?: string[];
    receiptPath?: string[];
    refundPath?: string[];
    remark?: string;
    message?: string | null;
    sapReceiveNo?: string | null;
    receiveName?: string;
    attachList?: Array<{
        type: number;
        path: string;
    }>;
    attachmentFiles?: string[]
}

export interface UploadFiles {
    uid?: string;
    name?: string;
    url?: string;
    filePath?: string;
    fileName?: string;
    [key: string]: any;
}

export interface ReceiptRecord {
    id: string;
    sapReceiveNo: string;
    merchantName: string;
    payTime: string;
    amount: number;
    originalData?: {
        budat?: string;
        belnr?: string;
        kunnr?: string;
        vtext?: string;
        txt50?: string;
        zuonr?: string;
        xref1?: string;
        dmbtr?: string;
        name1?: string;
        [key: string]: any;
    };
}

export interface ReceiptSearchParam {
    merchantName: string;
    payTimeStart: any | null;
    payTimeEnd: any | null;
    confirmTime?: null
    beginAndEnd?: null
    code?: string[]
}

export interface DepositForm {
    amount: number | null;
    sapReceiveNo: string;
    merchantName: string;
    fileList: UploadFiles[];
}