<script setup lang="ts">

import {
  Dropdown as hDropdown,
  Tree as hTree,
  InputSearch as hInputSearch,
  Popover as hPopover,
  Spin as hSpin,
  Card as hCard,
  DatePicker as hDatePicker,
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Table as hTable,
  BreadcrumbItem as hBreadcrumbItem,
  Breadcrumb as hBreadcrumb,
  LayoutSider as hLayoutSider,
  LayoutFooter as hLayoutFooter,
  LayoutContent as hLayoutContent,
  LayoutHeader as hLayoutHeader,
  Layout as hLayout,
  MenuItem as hMenuItem,
  MenuItemGroup as hMenuItemGroup,
  SubMenu as hSubMenu,
  Menu as hMenu,
  Divider as hDivider,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Result as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Tabs as hTabs,
  message,
  TableProps
} from 'ant-design-vue';
import {computed, onMounted, ref, watch} from 'vue';
import {
  DeleteOutlined,
  LinkOutlined,
  AddCircleFilled,
  DownOutlined,
  UngroupOutlined,
  FolderOutlined,
  FileOutlined,
  NodeExpandOutlined,
  HomeOutlined,
  ArrowDownOutlined,
  EnterOutlined,
  ArrowUpOutlined,
  UploadOutlined,
  UserOutlined,
  NotificationOutlined,
  AppstoreOutlined,
  HolderOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PlusOutlined,
  SearchOutlined
} from '@ant-design/icons-vue';
import {groupApi, roleApi, resourceApi, userApi, applicationApi} from '@haierbusiness-front/apis';
import {DataType, usePagination, useRequest} from 'vue-request';
import {
  IApplicationInfo,
  IGroupDeleteRequest,
  IGroupInfo,
  IGroupSaveUpdateRequest,
  IPageResponse,
  IResourceInfoTreeResponse,
  IResourceTreeNode,
  IRoleDeleteRequest,
  IRoleInfo,
  IRoleSaveUpdateRequest,
  IUserInfo,
  IUserListRequest,
  IUserSaveUpdateRequest,
  ResourceTypeConstant
} from '@haierbusiness-front/common-libs';
import {useScroll} from '@vueuse/core'
import {DataNode} from 'ant-design-vue/lib/tree';
import {Key} from 'ant-design-vue/lib/table/interface';
import {reject} from 'lodash';

const visibleApplicationSearch = ref()
const applicationSearchName = ref()
const applicationSearchCode = ref()
const applicationMenu = ref()
const startscoll = ref()
const applicationData = ref<IApplicationInfo[]>([])
const selectedRoleKeys = ref()

const onSelectApplication = (item: any) => {
  resourceTreeRun({
    "applicationId": item.key,
    "parentId": 0,
  })
}

const {
  data: currentRoleData,
  run: applicationListApiRun,
  loading: applicationListLoading,
  current,
  pageSize,
  totalPage,
} = usePagination(applicationApi.list, {
  manual: false,
  defaultParams: [
    {
      pageSize: 999
    }
  ],
  onBefore: () => {
    startscoll.value = false
  },
  onSuccess: (data: IPageResponse<IApplicationInfo>) => {
    if (data?.records) {
      applicationData.value.push(...(data?.records))
      /* console.log("selectedRoleKeys.value",selectedRoleKeys.value)
      if (!selectedRoleKeys.value) {
          selectedRoleKeys.value = [currentRoleData.value?.records[0].id]
      } */
    }
    startscoll.value = true
  }
});

const handleApplicationSearch = (
    pag: { current: number; pageSize: number },
    filters?: any,
    sorter?: any,
) => {
  applicationListApiRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
    applicationCode: applicationSearchCode.value,
  });
};

const searchApplicationByCode = () => {
  visibleApplicationSearch.value = false
  applicationData.value = []
  handleApplicationSearch({current: 1, pageSize: 999})
}

const handleApplicationHoverChange = (visible: boolean) => {
  visibleApplicationSearch.value = visible
};

const {arrivedState} = useScroll(applicationMenu)

watch(arrivedState, (newValue, oldValue) => {
  if (startscoll.value && newValue.bottom && current.value < totalPage.value) {
    handleApplicationSearch({current: current.value + 1, pageSize: pageSize.value})
  }
});


// 资源查询
const {
  data: resourceTreeData,
  run: resourceTreeRun,
  loading: resourceTreeLoading,
} = useRequest(
    resourceApi.searchTrees, {}
);


const resourceTree = computed(() => {
  if (resourceTreeData.value) {
    return [
      {
        id: 0,
        name: 'ROOT',
        type: 0,
        children: [...resourceTreeData.value]
      }
    ]
  } else {
    return [
      {
        id: 0,
        name: 'ROOT',
        type: 0,
        children: []
      }
    ]
  }
});
const checkedLinkTreeKeys = ref<{ checked: number[], halfChecked: number[] }>({checked: [], halfChecked: []})
const flatTreeKeys = ref<number[]>([])
const flatTreeKey = (tree: IResourceInfoTreeResponse[]) => {
  for (let i of tree) {
    if (i.id != undefined && i.id != null) {
      if (i.id) {
        flatTreeKeys.value.push(i.id)
      }
      if (i.children && i.children.length > 0) {
        flatTreeKey(i.children)
      }
    }
  }
}
const selectedTreeKeys = ref()
// 资源关联
const visibleLinkResource = ref()
const gotoLinkResource = () => {
  flatTreeKeys.value = []
  flatTreeKey(resourceTree.value)
  visibleLinkResource.value = true
  resourceLinkTreeRun({
    "parentId": 0,
  })
}
const {
  data: resourceLinkTreeData,
  run: resourceLinkTreeRun,
  loading: resourceLinkTreeLoading,
} = useRequest(
    resourceApi.searchTrees, {
      onSuccess: (data: any) => {
        generateKey(data)
        checkedLinkTreeKeys.value.checked = flatTreeKeys.value
      }
    }
);

const generateKey = (resource: IResourceTreeNode[]) => {
  for (let i of resource) {
    i.key = i.id
    if (i.children && i.children.length > 0) {
      generateKey(i.children)
    }
  }
}
const resourceLinkTree = computed(() => {

  if (resourceLinkTreeData.value) {
    return [
      {
        id: 0,
        key: 0,
        name: 'ROOT',
        type: 0,
        children: [...resourceLinkTreeData.value]
      }
    ]
  } else {
    return [
      {
        id: 0,
        key: 0,
        name: 'ROOT',
        type: 0,
        children: []
      }
    ]
  }
});
const selectedLinkTreeKeys = ref()
const linkResource = () => {
  linkResourceRun({
    "applicationId": selectedRoleKeys.value[0],
    "resourceIds": checkedLinkTreeKeys.value.checked,
  });

  visibleLinkResource.value = false
}

const {
  data: linkResourceData,
  run: linkResourceRun,
  loading: linkResourceLoading,
} = useRequest(
    applicationApi.linkResource, {
      onSuccess: () => {
        resourceTreeRun({
          "applicationId": selectedRoleKeys.value[0],
          "parentId": 0,
        })
        message.success("成功")
      }
    }
);
</script>

<template>
  <h-modal v-model:open="visibleLinkResource" :title="'关联资源'" :confirm-loading="resourceLinkTreeLoading"
           style="width: 1200px;height: 1000px;" @ok="linkResource">
    <h-row :align="'middle'">

      <h-col :span="24">
        <h-tree :checkStrictly="true" v-if="resourceLinkTree.length" :defaultExpandAll="true"
                :show-line="{ showLeafIcon: false }" checkable :show-icon="true"
                :tree-data="resourceLinkTree as unknown as DataNode[]" v-model:selectedKeys="selectedLinkTreeKeys"
                v-model:checkedKeys="checkedLinkTreeKeys">
          <template #icon="{ dataRef }">
            <template v-if="dataRef.type === 0"><!-- root -->
              <HomeOutlined/>
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.INTERFACE.type"><!-- 接口 -->
              <NodeExpandOutlined/>
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.PAGE.type"><!-- 页面 -->
              <FileOutlined/>
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.PAGE_MENU.type"><!-- 页面(菜单) -->
              <FileOutlined/>
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.PAGE_GROUP.type"><!-- 页面组 -->
              <FolderOutlined/>
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.WIDGET.type"><!-- 组件 -->
              <UngroupOutlined/>
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.MANAGE_APPLICATION.type"><!-- 应用（管理） -->
              <HolderOutlined/>
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.APPLICATION.type"><!-- 应用 -->
              <AppstoreOutlined/>
            </template>
          </template>
          <template #title="{ dataRef }">
            <span>{{ dataRef.name }}</span>
          </template>
          <template #switcherIcon="{ switcherCls }">
            <DownOutlined :class="switcherCls"/>
          </template>
        </h-tree>
      </h-col>
    </h-row>
  </h-modal>

  <h-row style="height: 100%;width: 100%;">
    <h-col :span="4" style="height:100% ; ">
      <div
          style="height:40px;border-right: 10px solid #f0f2f5;padding: 5px 5px 10px  20px  ;font-size: 18px;font-weight: 600;border-bottom: 1px solid #f0f0f0;background-color: #ffff;">
        应用列表
        <h-popover title="名称检索" placement="bottom" trigger="click" :visible="visibleApplicationSearch"
                   @visibleChange="handleApplicationHoverChange">
          <template #content>
            <h-input-search v-model:value="applicationSearchCode" enter-button allow-clear
                            @search="searchApplicationByCode" autocomplete="off"/>
          </template>
          <h-button type="primary" shape="circle" style="float: right;">
            <template #icon>
              <SearchOutlined/>
            </template>
          </h-button>
        </h-popover>
      </div>
      <div style="height:calc(100% - 40px);border-right: 10px solid #f0f2f5;position: relative; ">
        <div v-if="applicationListLoading"
             style="float: left;position: absolute;height: 100%;width: 100%;text-align: center;padding-top: 100%;">
          <h-spin/>
        </div>
        <h-menu v-if="applicationData" v-model:selectedKeys="selectedRoleKeys" @click="onSelectApplication"
                style="height:100%;width: 100%;border: none;overflow-x: hidden;overflow-y: auto;" mode="inline"
                ref="roleMenu">
          <h-menu-item :key="i.id" v-for="i of applicationData">
            <div style="width: 100%;">{{ i.applicationName }}</div>
          </h-menu-item>
        </h-menu>
      </div>
    </h-col>

    <h-col :span="20" style="height: 100%;">
      <div
          style="height:40px;padding: 5px 20px 10px  20px  ;font-size: 18px;font-weight: 600;border-bottom: 1px solid #f0f0f0;background-color: #ffff;">
        应用资源
        <h-button type="primary" shape="circle" style="float: right;margin-right: 3px;" @click="gotoLinkResource">
          <template #icon>
            <LinkOutlined/>
          </template>
        </h-button>
      </div>
      <div style="height:calc(100% - 40px); overflow-y: auto;padding: 5px 20px 10px  20px  ;background-color: #ffff;">
        <h-tree v-if="resourceTree.length" :defaultExpandAll="true" :show-line="{ showLeafIcon: false }"
                :show-icon="true" :tree-data="resourceTree as unknown as DataNode[]"
                v-model:selectedKeys="selectedTreeKeys">
          <template #icon="{ dataRef }">
            <template v-if="dataRef.type === 0">
              <HomeOutlined/>
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.INTERFACE.type"><!-- 接口 -->
              <NodeExpandOutlined/>
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.PAGE.type"><!-- 页面 -->
              <FileOutlined/>
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.PAGE_MENU.type"><!-- 页面(菜单) -->
              <FileOutlined/>
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.PAGE_GROUP.type"><!-- 页面组 -->
              <FolderOutlined/>
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.WIDGET.type"><!-- 组件 -->
              <UngroupOutlined/>
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.MANAGE_APPLICATION.type"><!-- 应用（管理） -->
              <HolderOutlined/>
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.APPLICATION.type"><!-- 应用 -->
              <AppstoreOutlined/>
            </template>
          </template>
          <template #title="{ dataRef }">
            <span>{{ dataRef.name }}</span>
          </template>
          <template #switcherIcon="{ switcherCls }">
            <DownOutlined :class="switcherCls"/>
          </template>
        </h-tree>
      </div>
    </h-col>

  </h-row>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}
</style>
