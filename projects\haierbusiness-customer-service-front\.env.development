# 打包路径
VITE_BASE_URL = ./
# 测试水印
VITE_TEST_WATERMARK = '测试系统'

#健康检查IP端口
VITE_HEALTH_PORT=https://**************:8043/agentgateway/resource/health.jsp

VITE_BUSINESS_URL=https://businessmanagement.haier.net/

#ws连接地址
# VITE_WEBSOCKET_URL=wss://**************:8043/agentgateway/ccgateway/agent/
VITE_WEBSOCKET_URL=wss://businessmanagement-test.haier.net/ws/agentgateway/ccgateway/agent/
#正式地址测试 
# VITE_WEBSOCKET_URL=wss://businessmanagement.haier.net/ws/agentgateway/ccgateway/agent/
# VITE_WEBSOCKET_URL=wss://lemut-in.haier.net/aiccws/agentgateway/ccgateway/agent/

#坐席签入密码
# VITE_PASSWORD=2wsx@WSX
VITE_PASSWORD=Sl@202408
VITE_CALL=************
 
# 外呼前缀
VITE_OUTCALL_PREFIX=9
# 录音系统 账号密码
VITE_ACCOUNT=shanglv

VITE_PWD=Password,123


