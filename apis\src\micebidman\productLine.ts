import { download, get, post } from '../request'
import {
    IProductLineFilter,
    IProductLine,
    IPageResponse,
    Result
} from '@haierbusiness-front/common-libs'


export const productLineApi = {
    // 产品线-分页查询
    list: (params: IProductLineFilter): Promise<IPageResponse<IProductLine>> => {
        return get('/mice-bid/api/process/define/product/line/page', params)
    },
    // 产品线-详情查询
    get: (id: number): Promise<IProductLine> => {
        return get('/mice-bid/api/process/define/product/line/info', {
            id
        })
    },
    // 产品线-新增
    save: (params: IProductLine): Promise<Result> => {
        return post('/mice-bid/api/process/define/product/line/save', params)
    },
    // 产品线-编辑
    edit: (params: IProductLine): Promise<Result> => {
        return post('/mice-bid/api/process/define/product/line/edit', params)
    },
    // 产品线-删除
    remove: (id: number): Promise<Result> => {
        return post(`/mice-bid/api/process/define/product/line/removeById?id=${id}`)
    },
    // 查询启用的流程列表
    getProcessList: (params: IProductLineFilter): Promise<IPageResponse<IProductLine>> => {
        return get('/mice-bid/api/framework/process/list', params)
    },
    // 产品线-根据流程id查询产品线详情
    queryByProcessId: (params: IProductLineFilter): Promise<IPageResponse<IProductLine>> => {
        return get(`/mice-bid/api/process/define/product/line/queryByProcessId`, params);
    },
}
