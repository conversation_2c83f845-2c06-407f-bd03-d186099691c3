import { MiceBidManBussinessListResult, MerchantContract } from '@haierbusiness-front/common-libs';
import { MerchantBussiness } from '@haierbusiness-front/common-libs/src/merchant/model/serviceProviderModel';
import { download, get, post, originalPost } from '../request';

export const miceBidManServiceExamApi = {
  getBussinessList: (params = {}): Promise<MiceBidManBussinessListResult> => {
    return get('/merchant/getPage', params);
  },
  getBussinessDetail: (params = {}): Promise<MerchantBussiness> => {
    return get('/merchant/getOneById', params);
  },
  addBussiness: (params = {}) => {
    return originalPost('/merchant/insert', params);
  },
  editBussiness: (params = {}) => {
    return originalPost('/merchant/updateById', params);
  },
};
