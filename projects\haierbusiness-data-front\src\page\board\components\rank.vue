<template>
  <div class="compact rank" :style="{ minHeight: vh + 'vh' }">
    <vue3-seamless-scroll :list="props.data" :step="0.5" :limitMoveNum="5" hover :copyNum="10">
      <div class="rank-item" v-for="(item, index) in (props.data as any)" :key="index">
        <div class="rank-item-top">
          <div class="rank-item-top-icon" :style="getColor(index)">
            <span class="arrow"></span>
            {{ index + 1 }}
          </div>
          <div class="rank-item-top-title">{{ item.name }}</div>
        </div>
        <div class="rank-item-progress">
          <div class="progress-bar" :style="getStyle(item, index)"></div>
          <div class="progress-label">
            <template v-if="props.unit == '万元' && item.value == 0"> {{ '<1万' }} </template>
            <template v-else> {{ numFormat(item.value) }}{{ props.unit }} </template>
          </div>
        </div>
      </div>
    </vue3-seamless-scroll>
    <!-- <div class="rank-item" v-for="(item,index) in props.data" :key="index">
            <div class="rank-item-top">
                <div class="rank-item-top-icon" :style="getColor(index)">
                    <span class="arrow"></span>
                    {{ index+1 }}
                </div>
                <div class="rank-item-top-title">{{ item.name }}</div>
            </div>
            <div class="rank-item-progress">
                <div class="progress-bar" :style="getStyle(item,index)"></div>
                <div class="progress-label">{{ numFormat(item.value) }}{{ props.unit }}</div>
            </div>
        </div> -->
  </div>
</template>
<script setup lang="ts">
import { Vue3SeamlessScroll } from 'vue3-seamless-scroll';
import { numFormat } from '../../../utils/numFormat';
import { computed } from 'vue';
const colors = ['#FF3232', '#FF9C00', '#E1C117', '#127FC3', '#127FC3'];
const props = defineProps({
  data: {
    type: Array,
    default: [],
  },
  unit: String,
  base: {
    type: Number,
    default: 6,
  },
  scroll: {
    type: Boolean,
    default: false,
  },
});
const vh = computed(() => {
  if (props.data.length >= 5) return 5 * props.base;
  return props.data.length * props.base;
});
const singleHeight = computed(() => {
  const ratio = props.base / 6;
  return (window.innerHeight / 100) * 5 * ratio;
});
const getStyle = (item, index) => {
  const base = props.data[0].value;
  if (base == 0 || item.value == 0)
    return {
      minWidth: '0.5%',
    };
  return {
    minWidth: (item.value / base) * 70 + '%',
  };
};
const getColor = (index) => {
  return {
    background: colors[index > 4 ? 4 : index],
  };
};
</script>
<style scoped lang="less">
.rank {
  height: 35vh;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  overflow: hidden;

  &-item {
    padding-top: 1vh;

    &-top {
      display: flex;
      align-items: center;
      font-size: 1.2vh;

      &-icon {
        width: 2.4vh;
        height: 1.5vh;
        line-height: 1.5vh;
        text-align: center;
        background: #ff3232;
        border-radius: 2px;
        position: relative;
        font-size: 1vh;

        .arrow {
          position: absolute;
          width: 0.7vh;
          height: 0.7vh;
          transform: rotate(45deg);
          background: inherit;
          top: 50%;
          margin-top: -0.35vh;
          right: -0.3vh;
        }
      }

      &-title {
        margin-left: 8px;
      }
    }

    &-progress {
      display: flex;
      align-items: center;
    }
  }
}

.progress {
  &-bar {
    min-width: 0;
    transition: all 0.2s;
    height: 1vh;
    background: linear-gradient(90deg, #33aaf5 25%, #65ffff 100%);
  }

  &-label {
    flex: 1;
    font-size: 1.2vh;
    margin-left: 8px;
    color: #e2f5fe;
  }
}
</style>
