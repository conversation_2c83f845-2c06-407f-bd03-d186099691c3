<script setup lang="ts">
import { BreadcrumbItem as hBreadcrumbItem, Bread<PERSON>rumb as hBreadcrumb, LayoutSider as hLayoutSider, LayoutFooter as hLayoutFooter, Layout<PERSON>ontent as hLayoutContent, Layout<PERSON>eader as hLayoutHeader, Layout as hLayout, MenuItem as hMenuItem, MenuItemGroup as hMenuItemGroup, SubMenu as hSubMenu, Menu as hMenu, Divider as hDivider, Space as hSpace, Button as hButton, Col as hCol, Result as hResult, Row as hRow, TabPane as hTabPane, Tabs as hTabs, message } from 'ant-design-vue';
import { onMounted, ref } from 'vue';
import { UploadOutlined, UserOutlined, NotificationOutlined, AppstoreOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons-vue';
import EManage from '@haierbusiness-front/components/manange/EManange.vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import '../assets/css/font.css'

const store = applicationStore()
const { resource } = storeToRefs(store)
</script>

<template>
    <div style="height:100vh;min-height:280px">
        <e-manage :param="resource"></e-manage>
    </div>
</template>

<style scoped lang="less">
:root {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}
</style>
