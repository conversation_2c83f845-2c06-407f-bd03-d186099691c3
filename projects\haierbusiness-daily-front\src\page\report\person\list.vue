<script setup lang="ts">
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Input as hInput,
  Table as hTable,
  But<PERSON> as hButton,
  Col as hCol,
  Row as hRow,
  Dropdown as hDropdown,
  Menu as hMenu,
  MenuItem as hMenuItem,
  DatePicker as hDatePicker,
  RangePicker as hRangePicker,
} from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import { computed, onMounted, ref, watch } from "vue";
import { PlusOutlined, SearchOutlined, BarsOutlined, DownOutlined, DownloadOutlined} from '@ant-design/icons-vue';
import { userApi, enterpriseApi, processApi } from "@haierbusiness-front/apis";
import {dailyReportApi, dailyDeptApi} from '@haierbusiness-front/apis/src/daily';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue'
import { usePagination, useRequest } from "vue-request";
import { DailEvaluateDO,UserGroupSystemConstant } from "@haierbusiness-front/common-libs";
import { useEditDialog, useDelete } from "@haierbusiness-front/composables";
import { getCurrentRouter, errorModal, routerParam, checkUserGroup } from "@haierbusiness-front/utils";

import { ColumnType } from 'ant-design-vue/es/table';

const router = getCurrentRouter();

const columns: ColumnType[]  = [
  {
    title: "人员",
    dataIndex: "rateeUsername",
    width: "5%",
    align: 'center'
  },
  {
    title: "部门",
    dataIndex: "rateeDeptName",
    width: "5%",
    align: 'center'
  },
  {
    title: "激励月份",
    dataIndex: "month",
    width: "5%",
    align: 'center'
  },
  {
    title: "激励日期",
    dataIndex: "appraiseTime",
    width: "6%",
    align: 'center'
  },
  {
    title: "类型",
    dataIndex: "typeName",
    width: "5%",
    align: 'center'
  },
  {
    title: "评价维度&要求",
    dataIndex: "evaluateRemark",
    width: "5%",
    align: 'center'
  },
  {
    title: "区位",
    dataIndex: "evaluateLevel",
    width: "3%",
    align: 'center'
  },
  {
    title: "激励金额",
    dataIndex: "evaluateAmount",
    width: "5%",
    align: 'center'
  }
];

const searchParam = ref<DailEvaluateDO>({
  pageNum: 1,
  pageSize: 20,
});

const params = ref<IUserListRequest>({
    pageNum: 1,
    pageSize: 20
})

const reset = () => {
  searchParam.value = {};
  startBeginAndEnd.value = [];
};

const { data, run: processApiRun, loading, current, pageSize } = usePagination(
    dailyReportApi.getPersonReportList, {
      manual: false,
    }
);

const {
  data: exportData,
  run: exportApiRun,
  loading: detailsExportLoading,
} = useRequest(dailyReportApi.exportPersonReport);

const enterpriseSelect = computed(() => {
  return enterprises.value || [];
});

const dataSource = computed(() => data.value?.records || []);
const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: "center" },
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any
) => {
  processApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const exportPersonReport = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any
) => {
  exportApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const startBeginAndEnd = ref<[Dayjs, Dayjs]>()
watch(() => startBeginAndEnd.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.startDate = n[0]
    searchParam.value.endDate = n[1]
  } else {
    searchParam.value.startDate = undefined
    searchParam.value.endDate = undefined
  }
});

const {
  data: typeNameList,
  loading: typeListLoading,
} = usePagination(dailyReportApi.getTypeNameList, {
  manual: false,
});


const {
  data: deptList,
  loading: deptListLoading,
} = usePagination(dailyDeptApi.list, {
  manual: false,
});

const deptSelect = computed(() => {
  let deptArr = [];
  for (let i in deptList.value) {
    deptArr.push(
        {
          value: deptList.value[i].code,
          label: deptList.value[i].name,
        }
    )
  }
  return deptArr;
});

const typeNameSelect = computed(() => {
  return typeNameList.value;
});

const userNameChange = (userInfo: IUserInfo | undefined) => {
    if (!userInfo) {
        searchParam.value.rateeUsercode = ''
        searchParam.value.rateeUsername = ''
        return
    }
    searchParam.value.rateeUsercode = userInfo.username
    searchParam.value.rateeUsername = userInfo.nickName
}

const checkUserPlatFrom = computed(() => {
  return checkUserGroup(UserGroupSystemConstant.DAILY_PLATFORM_MANAGER.groupId);
});

const checkUserAdmin = computed(() => {
  return checkUserGroup(UserGroupSystemConstant.SUPER_MANAGE.groupId);
});

</script>

<template>
  <div
    style="
      background-color: #ffff;
      height: 100%;
      width: 100%;
      padding: 10px 10px 0px 10px;
      overflow: auto;
    "
  >
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :gutter="[12, 24]" :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px" v-if="checkUserAdmin || checkUserPlatFrom">
            <label for="rateeDeptCode">部门：</label>
          </h-col>
          <h-col :span="4" v-if="checkUserAdmin || checkUserPlatFrom">
            <a-select
              v-model:value="searchParam.rateeDeptCode"
              show-search
              placeholder="请选择部门"
              style="width: 200px"
              allowClear
              :options="deptSelect"
            >
            </a-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="rateeUsercode">姓名：</label>
          </h-col>
          <h-col :span="4">
              <user-select :value="searchParam.rateeUsername" style="width: 200px" 
                :params="params" @change="(userInfo: IUserInfo) =>  userNameChange(userInfo)" />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="searchState">激励类型：</label>
          </h-col>
          <h-col :span="4">
            <a-select
              v-model:value="searchParam.type"
              show-search
              placeholder="激励类型"
              style="width: 200px"
              allowClear
              :options="typeNameSelect"
            >
            </a-select>
          </h-col> 
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="year">年份：</label>
          </h-col>
          <h-col :span="4">
            <h-date-picker
                style="margin-bottom: 6px; width: 200px"
                v-model:value="searchParam.year"
                value-format="YYYY"
                picker="year" allowClear
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="searchState">月份：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.month" style="width: 200px" allowClear>
              <h-select-option value="01">1月</h-select-option>
              <h-select-option value="02">2月</h-select-option>
              <h-select-option value="03">3月</h-select-option>
              <h-select-option value="04">4月</h-select-option>
              <h-select-option value="05">5月</h-select-option>
              <h-select-option value="06">6月</h-select-option>
              <h-select-option value="07">7月</h-select-option>
              <h-select-option value="08">8月</h-select-option>
              <h-select-option value="09">9月</h-select-option>
              <h-select-option value="10">10月</h-select-option>
              <h-select-option value="11">11月</h-select-option>
              <h-select-option value="12">12月</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :offset="18" :span="6" style="text-align: right">
            <h-button type="primary" :size="size" @click="exportPersonReport({ current: 1, pageSize: 10 })">
              <template #icon>
                <DownloadOutlined />
              </template>
              导出
            </h-button>
            <h-button style="margin-left: 10px"
              type="primary"
              @click="handleTableChange({ current: 1, pageSize: 10 })"
            >
              <SearchOutlined /> 查询
            </h-button>
            <h-button style="margin-left: 10px" @click="reset">重置</h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :row-key="(record) => record.id"
          :data-source="dataSource"
          :pagination="pagination"
          :size="'small'"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
        <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'evaluateAmount'"> {{ record.evaluateAmount }}/元 </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}
</style>
