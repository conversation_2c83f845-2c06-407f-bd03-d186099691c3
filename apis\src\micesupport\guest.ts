import { download, get, post } from '../request'
import { IMeetingGuestFilter,IMeetingGuest, IPageResponse, Result } from '@haierbusiness-front/common-libs'


export const meetingGuestApi = {
    list: (params: IMeetingGuestFilter): Promise<IPageResponse<IMeetingGuest>> => {
        return get('/mice-support/api/meeting/guest/list', params)
    },
    save: (params: IMeetingGuest): Promise<Result> => {
        return post('/mice-support/api/meeting/guest/add', params)
    },
    //审批
    Approval: (params: IMeetingGuest): Promise<Result> => {
        return post('/mice-support/api/meeting/participant/approve', params)
    },
    //导出
    export: (params: { miceInfoId: number; miceInfoName?: string }): Promise<void> => {
        return download('/mice-support/api/meeting/guest/export', params)
    },
    //导入
    import: (params: IMeetingGuestFilter): Promise<Result> => {
        return post('/mice-support/api/meeting/guest/import', params)
    },
    //上移/下移
    moveUp: (params: IMeetingGuest): Promise<Result> => {
        return post('/mice-support/api/meeting/agenda/move', params)
    },

    edit: (params: IMeetingGuest): Promise<Result> => {
        return post('/mice-support/api/meeting/guest/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post(`/mice-support/api/meeting/guest/delete?id=${id}`)
    },
}