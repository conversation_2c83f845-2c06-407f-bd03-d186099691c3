<script setup lang="ts">
import {
  BreadcrumbItem as hBreadcrumbItem,
  Breadcrumb as hBreadcrumb,
  LayoutSider as hLayoutSider,
  LayoutFooter as hLayoutFooter,
  Layout<PERSON>ontent as hLayoutContent,
  LayoutHeader as hLayoutHeader,
  Layout as hLayout,
  MenuItem as hMenuItem,
  MenuItemGroup as hMenuItemGroup,
  SubMenu as hSubMenu,
  Menu as hMenu,
  Divider as hDivider,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Result as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Tabs as hTabs,
  message,
  RadioGroup as hRadioGroup,
  RadioButton as hRadioButton,
  Table as hTable,
  Modal as hModal,
  Tree as hTree,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Switch as hSwitch,
  Select  as hSelect,
  SelectOption as hSelectOption,
  Image as hImage
} from 'ant-design-vue';
import { onMounted, ref, computed, watch,createVNode  } from 'vue';
import {
  UploadOutlined,
  UserOutlined,
  NotificationOutlined,
  AppstoreOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';
import EManage from '@haierbusiness-front/components/manange/EManange.vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { hotelApi,addressApi } from '@haierbusiness-front/apis';
import { GetMappingHotelListRes,supplierType,levelType } from '@haierbusiness-front/common-libs'
import { getCurrentRouter , errorModal, routerParam ,getEnumOptions } from '@haierbusiness-front/utils';
import { DataType, usePagination, useRequest } from 'vue-request';
import type { TreeProps } from 'ant-design-vue';
const store = applicationStore();
const { resource } = storeToRefs(store);
const tabValue = ref<string>("1")
const modalTabValue = ref<string>("1")
const mappingBoxShow = ref<boolean>(false)
const addBoxShow = ref<boolean>(false)
const levelTypeOptions = computed(()=>{
  return getEnumOptions(levelType,true)
})
const columnsFormng = [
  {
    title: '供应商',
    dataIndex: 'providerCodeName',
    key: 'providerCodeName',
    align:"center",
      ellipsis: true,
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    width:"300px",
  },
  {
    title: '地址',
    dataIndex: 'address',
    key: 'address',
    width:"300px",
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    width:"150px",
    key: 'phone',
  },
  {
    title: '城市',
    dataIndex: 'mappingCityName',
    key: 'mappingCityName',
    align:"center",
    ellipsis: true,
  },
  {
    title: '区域',
    dataIndex: 'mappingRegionName',
    key: 'mappingRegionName',
    align:"center",
    ellipsis: true,
  },
  {
    title: '品牌集团',
    dataIndex: 'groupName',
    key: 'groupName',
    align:"center",
    ellipsis: true,
  },
  {
    title: '品牌',
    dataIndex: 'mappingBrandName',
    key: 'mappingBrandName',
    align:"center",
    ellipsis: true,
  },
  {
    title: '经纬度',
    dataIndex: 'lon',
    key: 'lon',
    width:"250px",
    align:"center",
    ellipsis: true,
  },
  {
    title: '编码',
    dataIndex: 'providerHotelCode',
    key: 'providerHotelCode',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '操作',
    dataIndex: '_operator',
    align: 'center',
    width: '120px',
  },
]



  const columns = [
  {
    title: '酒店名称',
    dataIndex: 'name',
    width:"200px",
    key: 'name',
  },  {
    title: '编码',
    dataIndex: 'code',
    width:"200px",
    key: 'name',
  },
  {
    title: '酒店地址',
    dataIndex: 'address',
    width:"250px",
    key: 'address',
  },
  {
    title: '酒店电话',
    dataIndex: 'phone',
    align:"center",
    ellipsis: true,
    width:"250px",
    key: 'phone',
  },{
    title: '星级',
    dataIndex: 'starLevel',
    align: "center",
    width: "120px",
    key: 'starLevel',
    ellipsis: true,
  },
  {
    title: '城市',
    dataIndex: 'cityName',
    align:"center",
    width:"120px",
    ellipsis: true,
    key: 'cityName',
  },
  {
    title: '区域',
    dataIndex: 'regionName',
    align:"center",
    width:"120px",
    ellipsis: true,
    key: 'regionName',
  },
  {
    title: '品牌',
    dataIndex: 'brandName',
    align:"center",
    width:"120px",
    key: 'brandName',
    ellipsis: true,
  },
  {
    title: '经纬度',
    dataIndex: 'lon',
    align:"center",
    width:"250px",
    ellipsis: true,
    key: 'lon',
  },
  {
    title: '操作',
    dataIndex: '_operator',
    fixed: 'right',
    align: 'center',
    width: '100px',
  },
];
interface DataItem {
  key: number;
  name: string;
  age: number;
  address: string;
  children?: DataItem[];
}

// 酒店分页
const searchParam = ref<any>({  })
const {
  data:dataList,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(hotelApi.getHotelList);

const dataSource = computed(() => dataList.value?.records || []);


const getStarLevelName = (starLevel: string) => {
  if (!starLevel) {
    return "";
  }
  const starLevelResult = levelTypeOptions.value.find((levelType) => levelType.value === starLevel);
  if (starLevelResult) {
    return starLevelResult.label;
  }
  return "";
}
const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const pagination = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: dataList.value?.total,
    current: dataList.value?.pageNum,
    pageSize: dataList.value?.pageSize,
    style: { justifyContent: 'center' },
}));


// 供应商酒店分页
const searchSupplierParam = ref<any>({  })
const {
  data:dataSupplierList,
  run: listSupplierApiRun,
  loading:supplierLoading,
  current:suppliercurrent,
  pageSize:supplierpageSize,
} = usePagination(hotelApi.getProvideHotelList);

const dataSupplierSource = computed(() => dataSupplierList.value?.records || []);

const handleSupplierTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listSupplierApiRun({
    ...searchSupplierParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const supplierpagination = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: dataSupplierList.value?.total,
    current: dataSupplierList.value?.pageNum,
    pageSize: dataSupplierList.value?.pageSize,
    style: { justifyContent: 'center' },
}));


// 映射弹窗标题
const boxTitle = ref<string>("")

// 酒店映射列表
const mappingHotelList = ref<GetMappingHotelListRes[]>([])

// 当前弹窗的国旅酒店code
const hotelCode = ref<string>("")


  // 关联酒店

  const toMappingMng = (row:any) =>{
    boxTitle.value = row.name
    // 获取绑定的供应商酒店
    mappingBoxShow.value = true
    hotelCode.value = row.code
    getMappingHotelList(row.code)
  }

  // 请求国旅酒店映射
  const mappingLoading = ref<boolean>(false)
  const getMappingHotelList =(code:string) =>{
    mappingLoading.value = true
    hotelApi.getMappingHotelList({code:code}).then((res:any)=>{
      mappingHotelList.value = res
      mappingLoading.value = false
    })
    .catch(()=>{
      mappingLoading.value = false
    })
  }


  // 解除供应商映射
  const delMappingHotel =  (row:any) =>{
    hModal.confirm({
      title: '确定要删除此映射吗？',
      icon: createVNode(ExclamationCircleOutlined),
      onOk() {
        return new Promise((resolve, reject) => {
            
          hotelApi.deleteProviderHotelMapping({providerCode:row.providerCode,providerHotelCode:row.providerHotelCode,code:hotelCode.value}).then((res:any)=>{
            message.success('刪除成功')
            getMappingHotelList(hotelCode.value)
            resolve()
          })
          .catch(()=>{
            resolve()
          })
        })
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  }

    const providerHotelCode = ref<string>("")
    const providerCode = ref<string>("")
  // 关联酒店
  const association = (row:any) =>{
    hotelApi.getMappingHotelByProviderCode({providerHotelCode:providerHotelCode.value,providerCode:providerCode.value}).then(res=>{
      hModal.confirm({
      title: res?`当前供应商酒店已经关联了${res.name}，确认关联会覆盖之前的关联，确定关联此酒店吗？`:'确定要关联此酒店吗？',
      icon: createVNode(ExclamationCircleOutlined),
      onOk() {
        return new Promise((resolve, reject) => {
          hotelApi.providerHotelMappingHotel({providerCode:providerCode.value,providerHotelCode:providerHotelCode.value,code:row.code}).then((res:any)=>{
            message.success('关联成功')
            getMappingHotelList(hotelCode.value)
            addBoxShow.value = false
            resolve()
          }).catch(()=>{
            resolve()
          })
        })
      },
      onCancel() {
        console.log('Cancel');
      },
    });

    })
  }

  const addMapping= (record:any) =>{
    console.log(record,"---------------")
    boxTitle.value = record.name
    providerHotelCode.value = record.providerHotelCode
    providerCode.value =  record.providerCode
    addBoxShow.value = true
    listApiRun({
      ...searchParam.value,
      pageNum: 1,
      pageSize:10,
    });
  }

  // 获取国内城市下拉列表
  const hotelList = ref<any>([])
const getDistrictList = ()=>{
  addressApi.getDistrictList({code:'CN',level:'city'}).then(res=>{
    hotelList.value = res.records
  })
}
const filterOption = (input: string, option: any) => {
  if(option.name){
    return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }else{
    return option.brandName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }
};

// 获取区域
const areaList = ref<any>([])
const getAreaList = (value:number)=>{
  addressApi.getDistrictList({code:'CN',level:'district',cityId:value}).then(res=>{
    areaList.value = res.records
  })
}

// 选择完城市 获取区域
const cityChange = (value:number) =>{
  console.log(value)
  if(value){
    getAreaList(value)
  }else{
    areaList.value = []
  }
  searchSupplierParam.value.mappingRegionId=null;
  searchParam.value.regionId=null
}

// 获取品牌下拉列表
const BrandList = ref<any>([])
const getBrandList = ()=>{
  hotelApi.getHotelBrandProviderMapList({type:2}).then(res=>{
    BrandList.value = res.records
  })
}
// 获取集团下拉列表 BrandGroupList
const BrandGroupList = ref<any>([])
const getBrandGroupList = ()=>{
  hotelApi.getHotelBrandProviderMapList({type:1}).then(res=>{
    BrandGroupList.value = res.records
  })
}

const clear = (type:number|string)=>{
  if(type==1){
    searchSupplierParam.value = {}
    listSupplierApiRun({
        ...searchParam.value,
        pageNum: suppliercurrent.value,
        pageSize:supplierpageSize.value,
      });
  }else{
    searchParam.value = {}
    listApiRun({
        ...searchParam.value,
        pageNum: suppliercurrent.value,
        pageSize:supplierpageSize.value,
      });
  }
}

const supplierTypeOptions = computed(()=>{
  return getEnumOptions(supplierType)
})

  onMounted(()=>{
    listSupplierApiRun({
      ...searchSupplierParam.value,
      pageNum: 1,
      pageSize:10,
    });
    getDistrictList()
    getBrandList()
    getBrandGroupList()
  })

</script>

<template>
  <div class="pageBox">
    <div class="headerBox">
      <h-form :labelCol="{span:5, offset: 1}" style="width:100%;">
        <h-row>
          <h-col :span="6">
            <h-form-item label="供应商">
              <h-select ref="select" v-model:value="searchSupplierParam.providerCode" allow-clear>
                <h-select-option
                  v-for="item in supplierTypeOptions"
                  :value="item.value"
                >{{item.label}}</h-select-option>
              </h-select>
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="名称">
              <h-input allow-clear v-model:value="searchSupplierParam.name" placeholder="名称" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="城市">
              <h-select
                ref="city"
                @change="cityChange"
                show-search
                :fieldNames="{label:'name',value:'id'}"
                :options="hotelList"
                :filter-option="filterOption"
                v-model:value="searchSupplierParam.mappingCityId"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="区域">
              <h-select
                ref="city"
                :disabled="!searchSupplierParam.mappingCityId"
                show-search
                :fieldNames="{label:'name',value:'id'}"
                :options="areaList"
                :filter-option="filterOption"
                v-model:value="searchSupplierParam.mappingRegionId"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
        </h-row>
        <h-row>
          <h-col :span="6">
            <h-form-item label="编码">
              <h-input
                allow-clear
                v-model:value="searchSupplierParam.providerHotelCode"
                placeholder="编码"
              />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="地址">
              <h-input allow-clear v-model:value="searchSupplierParam.address" placeholder="地址" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="电话">
              <h-input allow-clear v-model:value="searchSupplierParam.phone" placeholder="电话" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="品牌">
              <h-select
                ref="city"
                show-search
                :fieldNames="{label:'brandName',value:'id'}"
                :options="BrandList"
                :filter-option="filterOption"
                v-model:value="searchSupplierParam.mappingBrandId"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
        </h-row>
        <h-row>
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right:10px;" @click="clear(1)">重置</h-button>
            <h-button
              type="primary"
              @click="handleSupplierTableChange({ current: 1, pageSize: 10 })"
            >
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-form>
      <!-- </h-row> -->
    </div>
    <div class="contentBox">
      <!--         :row-selection="rowSelection" -->
      <h-table
        :columns="columnsFormng"
        :size="'small'"
        :scroll="{ x: 1550,y:'calc(100vh - 440px)' }"
        :data-source="dataSupplierSource"
        :loading="supplierLoading"
        :pagination="supplierpagination"
        @change="handleSupplierTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'name'">
            <div :title="record.name" class="multi-line-ellipsis">
              {{ record.name }}
            </div>
          </template>
          <template v-if="column.dataIndex === 'address'">
            <div :title="record.address" class="multi-line-ellipsis">
              {{ record.address }}
            </div>
          </template>
          <template v-if="column.dataIndex === 'phone'">
            <div :title="record.phone" class="multi-line-ellipsis">
              {{ record.phone }}
            </div>
          </template>
          <template v-if="column.dataIndex === 'lon'">
            <div
                v-if="record.gdLon"
              >经度：{{record.gdLon}} <br> 纬度：{{record.gdLat}}</div>
              <div
                v-else-if="record.gLon"
              >经度：{{record.gLon}} <br> 纬度：{{record.gLat}}</div>
              <div
                v-else-if="record.bdLon"
              >经度：{{record.bdLon}} <br> 纬度：{{record.bdLat}}</div>
          </template>
          <template v-if="column.dataIndex === 'status'">
            <a-tag v-if="record.status==1" color="green">上架</a-tag>
            <a-tag v-if="record.status==0" color="red">下架</a-tag>
          </template>
          <template v-if="column.dataIndex === '_operator'">
            <h-button @click="addMapping(record)" type="link">映射</h-button>
          </template>
        </template>
      </h-table>
    </div>

    <!-- 新增映射 -->
    <h-modal
      width="1400px"
      v-model:open="addBoxShow"
      :title="'新增【'+boxTitle+'】的映射关系'"
      :footer="null"
      @ok="handleOk"
    >
      <h-form :labelCol="{span:5, offset: 1}">
        <h-row>
          <h-col :span="6">
            <h-form-item label="名称">
              <h-input v-model:value="searchParam.name" placeholder="名称" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="城市">
              <h-select
                ref="city"
                @change="cityChange"
                show-search
                :fieldNames="{label:'name',value:'id'}"
                :options="hotelList"
                :filter-option="filterOption"
                v-model:value="searchParam.cityId"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="区域">
              <h-select
                ref="city"
                :disabled="!searchParam.cityId"
                show-search
                :fieldNames="{label:'name',value:'id'}"
                :options="areaList"
                :filter-option="filterOption"
                v-model:value="searchParam.regionId"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="编码">
              <h-input v-model:value="searchParam.code" placeholder="编码" />
            </h-form-item>
          </h-col>
        </h-row>
        <h-row>
          <h-col :span="6">
            <h-form-item label="地址">
              <h-input v-model:value="searchParam.address" placeholder="地址" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="电话">
              <h-input v-model:value="searchParam.phone" placeholder="电话" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="品牌">
              <h-select
                ref="city"
                show-search
                :fieldNames="{label:'brandName',value:'id'}"
                :options="BrandList"
                :filter-option="filterOption"
                v-model:value="searchParam.brandIds"
                style="width: 100%"
                 placeholder="品牌"
                allow-clear
              />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="集团">
              <h-select
                ref="city"
                show-search
                :fieldNames="{label:'brandName',value:'id'}"
                :options="BrandGroupList"
                :filter-option="filterOption"
                mode="multiple"
                :maxTagCount="1"
                v-model:value="searchParam.brandGroupIds"
                placeholder="集团"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
        </h-row>
        <h-row>
          <h-col :span="6">
            <h-form-item label="星级">
              <h-select
                ref="city"
                show-search
                :options="levelTypeOptions"
                mode="multiple"
                :maxTagCount="1"
                v-model:value="searchParam.starLevels"
                placeholder="星级"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
          <!-- <h-col :span="6">
            <h-form-item label="经度">
              <h-input
                allow-clear
                v-model:value="searchParam.lon"
                placeholder="经度"
              />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="纬度">
              <h-input allow-clear v-model:value="searchParam.lat" placeholder="纬度" />
            </h-form-item>
          </h-col> -->
        </h-row>
        <h-row>
          <h-col :span="24" style="text-align: right;margin-bottom: 10px">
            <h-button style="margin-right:10px;" @click="clear(2)">重置</h-button>
            <h-button
              type="primary"
              @click="handleTableChange({ current: 1, pageSize: 10 })"
            >
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-form>
      <div class="modalBox">
        <h-table
          :columns="columns"
          :size="'small'"
          :scroll="{ x: 1550,y:400 }"
          :data-source="dataSource"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'starLevel'">
            <p style="word-break: break-all;">{{ getStarLevelName(record.starLevel) }}</p>
          </template>
            <template v-if="column.dataIndex === 'image'">
              <h-image :width="200" :height="100" :src="record.image" />
            </template>
            <template v-if="column.dataIndex === 'phone'">
              <p style="word-break: break-all;">{{ record.phone }}</p>
            </template>
            <template v-if="column.dataIndex === 'lon'">
              <div
                v-if="record.gdLon"
              >经度：{{record.gdLon}} <br> 纬度：{{record.gdLat}}</div>
              <div
                v-else-if="record.gLon"
              >经度：{{record.gLon}} <br> 纬度：{{record.gLat}}</div>
              <div
                v-else-if="record.bdLon"
              >经度：{{record.bdLon}} <br> 纬度：{{record.bdLat}}</div>
            </template>
            <!-- 映射mappingHotelList -->
            <template v-if="column.dataIndex === 'mappingHotelList'">
              <p v-for="item in record.mappingHotelList">{{ item.providerCodeName }}:{{ item.name }}</p>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button @click="association(record)" type="link">关联</h-button>
            </template>
          </template>
        </h-table>
      </div>
    </h-modal>
  </div>
</template>

<style scoped lang="less">
.pageBox {
  padding: 0 8px;
  //   height: calc(100vh - 100px);
  //   overflow:auto;
  .headerBox {
    width: 100%;
    // height: 60px;
    background: #fff;
    display: flex;
    align-items: center;
    padding: 24px;
  }
  .inputBox {
    display: flex;
    align-items: center;
    // margin-left: 60px;
  }
  .contentBox {
    margin-top: 16px;
    background: #fff;
    height: calc(100vh - 320px);
  }
}
.modalHeaderBox {
  width: 100%;
  // display: flex;
}
</style>
