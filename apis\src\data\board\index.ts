import { get, post } from '../../request'

import { useBoardStore } from '@haierbusiness-front/utils/src/store/board'

import {
    ReportFilter,
    Result
} from '@haierbusiness-front/common-libs';
import dayjs from "dayjs";


const getTypeKey = (url: number | string) => {
    const resultMap: any = {
        "/data/board/booking-hotel": "localHotel",
        "/data/board/ordering-food": "localRestaurant",
        "/data/board/travel/internal": "domesticAirTickets",
        "/data/board/travel/external": "internationalAirfare",
        "/data/board/travel/hotel": "hotel",
        "/data/board/travel/taxi": "taxi",
        "/data/board/travel/train": "trainTicket",
        "/data/board/mice/offsite": "miceOffsite",
        "/data/board/mice/local": "miceLocal",
        "/data/board/travel/index": "travelOverview",
        default: "",
    };
    return resultMap[url] || resultMap.default;
};

const commonParams = {
    moduleType: 2,
    type: getTypeKey(window.location.hash.split('#')[1]),
}

const commonKey = {
    concurrencyControl: true,
    concurrencyControlMode: "DIRTYREAD",
    viewId: "5e8201ff1a314c968e6ef9ab1f396690",

};

const getCommonFilterSynchronism = ({
    defaultFilters = [],
    pattern = "",
    companyColumn = "",
    dateColumn,
    dataSourceKey = "",
    budgetDepartmentKey = "budget_department_code",
    dateParams,
}) => {
    const store = useBoardStore();
    const { date, company, dataSource, budgetDepartment } = store;

    const filters = [...defaultFilters] as any;
    if (budgetDepartmentKey && budgetDepartment) {
        filters.push({
            aggOperator: null,
            column: [budgetDepartmentKey],
            sqlOperator: "IN",
            values: [
                {
                    value: budgetDepartment,
                    valueType: "FRAGMENT",
                },
            ],
        });
    }
    if (company && companyColumn) {
        filters.push({
            aggOperator: null,
            column: [companyColumn],
            sqlOperator: "IN",
            values: [
                {
                    value: company,
                    valueType: "FRAGMENT",
                },
            ],
        });
    }
    if (dateParams && dateParams.length == 2) {
        let [dateStart, dateEnd] = dateParams;
        if (pattern) {
            dateStart = dayjs(dateStart).format(pattern);
            dateEnd = dayjs(dateEnd).format(pattern);
        }
        // //加00:00:00和23:59:59
        // if(dateStart.split(" ").length==1){
        //     dateStart+= " 00:00:00";
        //     dateEnd+= " 23:59:00";
        // }
        filters.push(
            {
                aggOperator: null,
                column: [dateColumn],
                sqlOperator: "GTE",
                values: [
                    {
                        value: dateStart,
                        valueType: "STRING",
                    },
                ],
            },
            {
                aggOperator: null,
                column: [dateColumn],
                sqlOperator: "LTE",
                values: [
                    {
                        value: dateEnd,
                        valueType: "STRING",
                    },
                ],
            }
        );
    }

    if (dataSourceKey && dataSource) {
        filters.push({
            aggOperator: null,
            column: [dataSourceKey],
            sqlOperator: "EQ",
            values: [
                {
                    value: dataSource,
                    valueType: "STRING",
                },
            ],
        });
    }
    return filters;
};
const getCommonFilter = ({
    defaultFilters = [],
    pattern = "",
    companyColumn = "",
    dateColumn,
    dataSourceKey = "",
    budgetDepartmentKey = "budget_department_code",
    areaCodeKey = "field_code",
    plCodeKey = "pl_code",
    ptCodeKey = "pt_code",

}) => {
    const store = useBoardStore();
    const { date, company, dataSource, budgetDepartment, areaCode, plCode, ptCode } = store;
    const filters = [...defaultFilters] as any;
    if (budgetDepartmentKey && budgetDepartment) {
        filters.push({
            aggOperator: null,
            column: [budgetDepartmentKey],
            sqlOperator: "IN",
            values: [
                {
                    value: budgetDepartment,
                    valueType: "FRAGMENT",
                },
            ],
        });
    }
    if (areaCodeKey && areaCode) {
        filters.push({
            aggOperator: null,
            column: [areaCodeKey],
            sqlOperator: "IN",
            values: [
                {
                    value: areaCode,
                    valueType: "FRAGMENT",
                },
            ],
        });
    }

    if (plCodeKey && plCode) {
        filters.push({
            aggOperator: null,
            column: [plCodeKey],
            sqlOperator: "IN",
            values: [
                {
                    value: plCode,
                    valueType: "FRAGMENT",
                },
            ],
        });
    }

    if (ptCodeKey && ptCode) {
        filters.push({
            aggOperator: null,
            column: [ptCodeKey],
            sqlOperator: "IN",
            values: [
                {
                    value: ptCode,
                    valueType: "FRAGMENT",
                },
            ],
        });
    }
    if (company && companyColumn) {
        filters.push({
            aggOperator: null,
            column: [companyColumn],
            sqlOperator: "IN",
            values: [
                {
                    value: company,
                    valueType: "FRAGMENT",
                },
            ],
        });
    }
    if (date && date.length == 2) {
        let [dateStart, dateEnd] = date;
        if (pattern) {
            dateStart = dayjs(dateStart).format(pattern);
            dateEnd = dayjs(dateEnd).format(pattern);
        }
        // //加00:00:00和23:59:59
        // if(dateStart.split(" ").length==1){
        //     dateStart+= " 00:00:00";
        //     dateEnd+= " 23:59:00";
        // }
        filters.push(
            {
                aggOperator: null,
                column: [dateColumn],
                sqlOperator: "GTE",
                values: [
                    {
                        value: dateStart,
                        valueType: "STRING",
                    },
                ],
            },
            {
                aggOperator: null,
                column: [dateColumn],
                sqlOperator: "LTE",
                values: [
                    {
                        value: dateEnd,
                        valueType: "STRING",
                    },
                ],
            }
        );
    }

    if (dataSourceKey && dataSource) {
        filters.push({
            aggOperator: null,
            column: [dataSourceKey],
            sqlOperator: "EQ",
            values: [
                {
                    value: dataSource,
                    valueType: "STRING",
                },
            ],
        });
    }
    return filters;
};

const queryCommonData = (params: any): Promise<Result> => {

    return post("data/api/bi/common/data", {
        concurrencyControl: true,
        concurrencyControlMode: "DIRTYREAD",
        ...params,
    });
};




//查询累计成交
const queryAccumulative = (
    name: string | null,
    from: string | null
): Promise<Result> => {

    let filterArr: any = [
        {
            aggOperator: null,
            column: ["bill_state"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "已核对",
                    valueType: "STRING",
                },
            ],
        },
    ];

    if (name && from) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn: "view_order_create_date",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',

    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        a: '累计成交',
        aggregators: [
            {
                alias: "订单数",
                column: ["order_key"],
                sqlOperator: "COUNT",
            },
            {
                alias: "间夜数",
                column: ["room_nights"],
                sqlOperator: "SUM",
            },
            {
                alias: "成交金额",
                column: ["order_actual_amount"],
                sqlOperator: "SUM",
            },
            {
                alias: "预算部门",
                column: ["budget_department_code"],
                sqlOperator: "COUNT_DISTINCT",
            },
            {
                alias: "结算单位",
                column: ["account_company_code"],
                sqlOperator: "COUNT_DISTINCT",
            },
            // {
            //     "alias": "入住人数",
            //     "column": [
            //         "order_guest_count"
            //     ],
            //     "sqlOperator": "SUM"
            // },
            {
                alias: "政策节省",
                column: ["discount_amount"],
                sqlOperator: "SUM",
            },
            // {
            //     "alias": "人均单价",
            //     "column": [
            //         "avg_person_amount"
            //     ]
            // },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns: [
            {
                alias: "avg_person_amount",
                snippet: " sum(order_actual_amount)/sum(room_nights)",
            },
        ],
    });
};



//查询同期青岛订房业务趋势
const querySynchronismBookingTrend = (
    { dateColumn = "view_order_create_year_month", pattern = "yyyy-MM" },
    name: string | null,
    from: string | null,
    dateParams: any
): Promise<any> => {
    let filterArr: any = [
        {
            aggOperator: null,
            column: ["bill_state"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "已核对",
                    valueType: "STRING",
                },
            ],
        },
    ];
    if (name && from) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }

    const filters = getCommonFilterSynchronism({
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn,
        pattern,
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
        dateParams,
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,

        a: '业务趋势',
        aggregators: [
            {
                alias: "销售金额",
                column: ["order_actual_amount"],
                sqlOperator: "SUM",
            },
            {
                alias: "间夜数",
                column: ["room_nights"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: dateColumn,
                column: [dateColumn],
            },
        ],
        filters,
        orders: [
            {
                column: [dateColumn],
                operator: "ASC",
            },
        ],
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};


//查询订房业务趋势
const queryBookingTrend = (
    { dateColumn = "view_order_create_year_month", pattern = "yyyy-MM" },
    name: string | null,
    from: string | null
): Promise<any> => {
    let filterArr: any = [
        {
            aggOperator: null,
            column: ["bill_state"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "已核对",
                    valueType: "STRING",
                },
            ],
        },
    ];
    if (name && from) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }

    const filters = getCommonFilter({
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn,
        pattern,
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,

        a: '业务趋势',
        aggregators: [
            {
                alias: "销售金额",
                column: ["order_actual_amount"],
                sqlOperator: "SUM",
            },
            {
                alias: "间夜数",
                column: ["room_nights"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: dateColumn,
                column: [dateColumn],
            },
        ],
        filters,
        orders: [
            {
                column: [dateColumn],
                operator: "ASC",
            },
        ],
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
//查询支付类型
const queryPayType = (): Promise<Result> => {
    const filters = getCommonFilter({
        defaultFilters: [
            {
                aggOperator: null,
                column: ["bill_state"],
                sqlOperator: "EQ",
                values: [
                    {
                        value: "已核对",
                        valueType: "STRING",
                    },
                ],
            },
        ] as any,
        companyColumn: "account_company_code",
        dateColumn: "view_order_create_date",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,

        aggregators: [
            {
                alias: "支付类型数量",
                column: ["order_key"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "order_pay_type",
                column: ["order_pay_type"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 10000,
        },
    });
};
//查询挂账支付详情
const queryPayTypeDetail = (
    name: string | null,
    from: string | null
): Promise<any> => {
    let filterArr: any = [
        {
            aggOperator: null,
            column: ["bill_state"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "已核对",
                    valueType: "STRING",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["budget_source"],
            sqlOperator: "NOT_NULL",
            values: [],
        },
    ];
    if (name && from) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn: "view_order_create_date",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,


        aggregators: [
            {
                alias: "支付平台数量",
                column: ["order_key"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "budget_source",
                column: ["budget_source"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 10000,
        },
    });
};
//查询价格分布
const queryPriceDistribution = (
    name: string | null,
    from: string | null
): Promise<any> => {
    let filterArr = [
        {
            aggOperator: null,
            column: ["bill_state"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "已核对",
                    valueType: "STRING",
                },
            ],
        },
    ] as any;
    if (name && from) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn: "view_order_create_date",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,


        viewId: "5e8201ff1a314c968e6ef9ab1f396690",
        aggregators: [
            {
                alias: "间夜数",
                column: ["room_nights"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "价格分布",
                column: ["price_distribution"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
    });
};

//查询星级分布
const queryStarDistribution = (
    name: string | null,
    from: string | null
): Promise<any> => {
    let filterArr = [
        {
            aggOperator: null,
            column: ["bill_state"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "已核对",
                    valueType: "STRING",
                },
            ],
        },
    ] as any;
    if (name && from) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn: "view_order_create_date",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,

        viewId: "5e8201ff1a314c968e6ef9ab1f396690",
        aggregators: [
            {
                alias: "酒店星级名称数量",
                column: [
                    "order_hotel_star_level"
                ],
                sqlOperator: "COUNT"
            }
        ],
        groups: [
            {
                alias: "星级分布",
                column: ["order_hotel_star_level"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
    });
};
//查询结算业务趋势
const querySettleTrend = ({ pattern }): Promise<Result> => {
    const filters = getCommonFilter({
        defaultFilters: [
            {
                aggOperator: null,
                column: ["account_period_year_month"],
                sqlOperator: "NOT_NULL",
                values: [],
            },
            {
                aggOperator: null,
                column: ["bill_state"],
                sqlOperator: "EQ",
                values: [
                    {
                        value: "已核对",
                        valueType: "STRING",
                    },
                ],
            },
        ] as any,
        companyColumn: "account_company_code",
        dateColumn: "view_order_create_year_month",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
        pattern,
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,

        aggregators: [
            {
                alias: "订单数",
                column: ["order_key"],
                sqlOperator: "COUNT",
            },
            {
                alias: "结算金额",
                column: ["account_total_amount"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "account_period_year_month",
                column: ["account_period_year_month"],
            },
        ],
        filters,
        orders: [
            {
                column: ["account_period_year_month"],
                operator: "ASC",
            },
        ],
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
//查询预约酒店排行
const queryHotelRank = (
    name: string | null,
    from: string | null
): Promise<any> => {
    let filterArr: any = [
        {
            aggOperator: null,
            column: ["bill_state"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "已核对",
                    valueType: "STRING",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["order_hotel_name"],
            sqlOperator: "NOT_NULL",
            values: [],
        },
        {
            aggOperator: null,
            column: ["room_nights"],
            sqlOperator: "NOT_NULL",
            values: [],
        },
    ];
    if (name && from) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn: "view_order_create_date",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,

        aggregators: [
            {
                alias: "间夜数",
                column: ["room_nights"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "order_hotel_name",
                column: ["order_hotel_name"],
            },
        ],
        filters,
        orders: [
            {
                column: ["room_nights"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
    });
};
//查询结算排行
const querySettleRank = (
    name: null | string,
    from: null | string
): Promise<Result> => {
    let filterArr: any = [
        {
            aggOperator: null,
            column: ["bill_state"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "已核对",
                    valueType: "STRING",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["account_company_name"],
            sqlOperator: "NOT_NULL",
            values: [],
        },
    ];
    if (name && from) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn: "view_order_create_date",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,

        aggregators: [
            {
                alias: "成交金额",
                column: ["order_actual_amount"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "account_company_name",
                column: ["account_company_name"],
            },
        ],
        filters,
        orders: [
            {
                column: ["order_actual_amount"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
    });
};
//结算单位查询
const querySettleCompany = (params): Promise<Result> => {
    return get("/data/api/bi/account/getbykey", {
        ...params,
        pageNo: 1,
        pageSize: 20,
    });
};
//酒店预算部门排行
const queryHotelBudget = (
    name: string | null,
    from: string | null
): Promise<Result> => {
    let filterArr: any = [
        {
            aggOperator: null,
            column: ["bill_state"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "已核对",
                    valueType: "STRING",
                },
            ],
        },
    ];
    if (name && from) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn: "view_order_create_date",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,

        aggregators: [
            {
                alias: "金额",
                column: ["order_actual_amount"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "预算部门",
                column: ["budget_department_name"],
            },
        ],
        filters,
        orders: [
            {
                column: ["order_actual_amount"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
    });
};
//酒店地图数据
export const queryHotelMapData = (
    name: string | null,
    from: string | null
): Promise<Result> => {
    let filterArr = [
        {
            aggOperator: null,
            column: ["bill_state"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "已核对",
                    valueType: "STRING",
                },
            ],
        },
    ] as any;

    if (name && from) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn: "view_order_create_date",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',

    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,

        aggregators: [
            {
                alias: "间夜数",
                column: ["room_nights"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "区域名称",
                column: ["region_name"],
            },
            {
                alias: "区域code",
                column: ["region_code"],
            },
            {
                alias: "经度",
                column: ["lng"],
            },
            {
                alias: "维度",
                column: ["lat"],
            },
        ],
        filters,
        orders: [
            {
                column: ["room_nights"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
//报表查询
const queryDataList = (params: ReportFilter): Promise<Result> => {
    return queryCommonData({
        ...commonParams,
        ...assembleParams(params),
    });
};
//报表导出
// const reportToExport = (params:ReportFilter):Promise<Result> => {
//     return request.get(`/bi/common/downReport?fileName=${params.fileName}&downloadParams=${JSON.stringify([assembleParams(params)])}&downloadType=EXCEL`);
// }
const reportToExport = (params: ReportFilter) => {
    return {
        url: "/hbweb/data/hb/data/api/bi/common/downReport",
        params: {
            fileName: params.fileName,
            downloadParams: [{ ...assembleParams(params) }],
            downloadType: "EXCEL",
        },
    };
    // return {
    //     url:"/bi/common/downReport",
    //     params:{
    //         fileName:params.fileName,
    //         downloadParams:[{...assembleParams(params)}],
    //         downloadType:"EXCEL"
    //     }
    // }
    // return request.post('/bi/common/downReport',{
    //     fileName:params.fileName,
    //     downloadParams:[{...assembleParams(params)}],
    //     downloadType:"EXCEL"
    // },{
    //     responseType:"blob"
    // });
};

const reportPPTExport = (params: ReportFilter) => {
    return {
        url: "/hbweb/data/hb/data/api/generate/ppt",
        params: params,
    };
    // return {
    //     url:"/bi/common/downReport",
    //     params:{
    //         fileName:params.fileName,
    //         downloadParams:[{...assembleParams(params)}],
    //         downloadType:"EXCEL"
    //     }
    // }
    // return request.post('/bi/common/downReport',{
    //     fileName:params.fileName,
    //     downloadParams:[{...assembleParams(params)}],
    //     downloadType:"EXCEL"
    // },{
    //     responseType:"blob"
    // });
};





//组装报表导出参数
const assembleParams = (params: ReportFilter) => {
    const {
        datartParams,
        pageNo = 1,
        pageSize = 20,
        total,
        pattern = "YYYY-MM-DD",
        fileName,
        ...restFilter
    } = params;
    const {
        viewId,
        aggregators,
        defaultFilters = [],
        orders,
        functionColumns,
        groups,
        moduleType,
        type,
    } = datartParams;
    const filters: any[] = [...defaultFilters];
    //组装datart的filter
    for (let i in restFilter) {
        if (!restFilter[i]) continue;
        if (Array.isArray(restFilter[i]) && restFilter[i].length == 0) continue;
        if (Array.isArray(restFilter[i]) && restFilter[i].length == 2) {
            let [dateStart, dateEnd] = restFilter[i];
            if (pattern) {
                if(i=='ywfssj'){
                    dateStart = dayjs(dateStart).format('YYYY-MM-DD HH:mm:ss');
                    dateEnd = dayjs(dateEnd).format('YYYY-MM-DD HH:mm:ss');
                }else{
                    dateStart = dayjs(dateStart).format(pattern);
                    dateEnd = dayjs(dateEnd).format(pattern);
                }
            }
            filters.push(
                {
                    aggOperator: null,
                    column: [i],
                    sqlOperator: "GTE",
                    values: [
                        {
                            value: dateStart,
                            valueType: "STRING",
                        },
                    ],
                },
                {
                    aggOperator: null,
                    column: [i],
                    sqlOperator: "LTE",
                    values: [
                        {
                            value: dateEnd,
                            valueType: "STRING",
                        },
                    ],
                }
            );
            continue;
        }
        let filterStatus =
            i === "account_company_name" ||
            i === "account_company_code" ||
            i === "budget_department_name" ||
            i === "budget_department_code"||
            i === "area_code";
        filters.push({
            aggOperator: null,
            column: [i],
            sqlOperator: filterStatus
                ? restFilter[i].indexOf(",") > -1
                    ? "IN"
                    : "EQ"
                : "LIKE",
            values: [
                {
                    value: filterStatus ? restFilter[i] : restFilter[i],
                    valueType:
                        filterStatus && restFilter[i].indexOf(",") > -1
                            ? "FRAGMENT"
                            : "STRING",
                },
            ],
        });
    }
    filters.forEach((item) => {
        item.column.forEach((columnItem) => {
            if (
                columnItem == "order_applicant_name" ||
                columnItem == "cxr_xm" ||
                columnItem == "sqr" ||
                columnItem == "sqrxm"
            ) {
                filters.push({
                    aggOperator: null,
                    column: ["filter_flag"],
                    sqlOperator: "EQ",
                    values: [
                        {
                            value: 0,
                            valueType: "STRING",
                        },
                    ],
                });
            }
        });
    });

    console.log(moduleType,
        type, 'module=======>>>>>>>>>>>>>>>>>>.')
    return {
        viewId,
        aggregators,
        filters,
        groups,
        orders,
        moduleType,
        type,
        pageInfo: {
            countTotal: true,
            pageSize,
            pageNo,
        },
        functionColumns,
    };
};
export {
    getCommonFilter,
    getCommonFilterSynchronism,
    queryCommonData,
    queryAccumulative,
    queryBookingTrend,
    querySynchronismBookingTrend,
    queryPayType,
    queryPayTypeDetail,
    queryPriceDistribution,
    queryStarDistribution,
    querySettleTrend,
    queryHotelRank,
    querySettleRank,
    querySettleCompany,
    queryHotelBudget,
    queryDataList,
    reportToExport,
    reportPPTExport,
};
//以下是青岛订餐接口
//累计成交
export const queryOrderingFoodAccumulative = (name, from): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "bill_check_datetime",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
    });
    filters.push({
        aggOperator: null,
        column: ["bill_state"],
        sqlOperator: "EQ",
        values: [
            {
                value: "已核对",
                valueType: "STRING",
            },
        ],
    });
    return queryCommonData({
        ...commonParams,
        a: "累计成交",
        viewId: "a99325ff701b412997cfc345e3db1f9e",

        aggregators: [
            {
                alias: "订单数",
                column: ["order_code"],
                sqlOperator: "COUNT",
            },
            {
                alias: "就餐人数",
                column: ["eating_count"],
                sqlOperator: "SUM",
            },
            {
                alias: "销售金额",
                column: ["order_actual_amount"],
                sqlOperator: "SUM",
            },
            {
                alias: "政策节省",
                column: ["discount_amount"],
                sqlOperator: "SUM",
            },
            {
                alias: "预算部门",
                column: ["budget_department_code"],
                sqlOperator: "COUNT_DISTINCT",
            },
            {
                alias: "结算单位",
                column: ["account_company_code"],
                sqlOperator: "COUNT_DISTINCT",
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
//订餐预算部门排行
export const queryOrderFoodBudget = (
    name: string | null,
    from: string | null
): Promise<Result> => {
    let filterArr: any = [
        {
            aggOperator: null,
            column: ["bill_state"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "已核对",
                    valueType: "STRING",
                },
            ],
        },
    ];
    if (name && from) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn: "bill_check_datetime",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',

    });
    return queryCommonData({
        ...commonParams,
        viewId: "a99325ff701b412997cfc345e3db1f9e",
        aggregators: [
            {
                alias: "金额",
                column: ["order_actual_amount"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "预算部门",
                column: ["budget_department_name"],
            },
        ],
        filters,
        orders: [
            {
                column: ["order_actual_amount"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
    });
};

//同期订餐业务趋势
export const querySynchronismOrderingFoodBusinessTrend = (
    { functionColumns }: any,
    name: string | null,
    from: string | null,
    dateParams: any
): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilterSynchronism({
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "bill_check_datetime",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
        dateParams,
    });
    filters.push({
        aggOperator: null,
        column: ["bill_state"],
        sqlOperator: "EQ",
        values: [
            {
                value: "已核对",
                valueType: "STRING",
            },
        ],
    });
    return queryCommonData({
        ...commonParams,
        viewId: "a99325ff701b412997cfc345e3db1f9e",
        aggregators: [
            {
                alias: "订单数",
                column: ["order_code"],
                sqlOperator: "COUNT",
            },
            {
                alias: "销售金额",
                column: ["order_actual_amount"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "bill_check_datetime_query",
                column: ["bill_check_datetime_query"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns,
    });
};
//订餐业务趋势
export const queryOrderingFoodBusinessTrend = (
    { functionColumns }: any,
    name: string | null,
    from: string | null,
): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "bill_check_datetime",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
    });
    filters.push({
        aggOperator: null,
        column: ["bill_state"],
        sqlOperator: "EQ",
        values: [
            {
                value: "已核对",
                valueType: "STRING",
            },
        ],
    });
    return queryCommonData({
        ...commonParams,
        a: '趋势',
        viewId: "a99325ff701b412997cfc345e3db1f9e",
        aggregators: [
            {
                alias: "订单数",
                column: ["order_code"],
                sqlOperator: "COUNT",
            },
            {
                alias: "销售金额",
                column: ["order_actual_amount"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "bill_check_datetime_query",
                column: ["bill_check_datetime_query"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns,
    });
};
//人均价格分布
export const queryOrderingFoodPriceDistribution = (
    name,
    from
): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "bill_check_datetime",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
    });
    filters.push({
        aggOperator: null,
        column: ["bill_state"],
        sqlOperator: "EQ",
        values: [
            {
                value: "已核对",
                valueType: "STRING",
            },
        ],
    });
    return queryCommonData({
        ...commonParams,
        viewId: "a99325ff701b412997cfc345e3db1f9e",
        aggregators: [
            {
                alias: "订单数",
                column: ["order_code"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "价格类型",
                column: ["price_distribution"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        orders: [],
    });
};
//预订酒店排行
export const queryOrderingFoodHotelRank = (name, from): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "bill_check_datetime",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
    });
    filters.push({
        aggOperator: null,
        column: ["bill_state"],
        sqlOperator: "EQ",
        values: [
            {
                value: "已核对",
                valueType: "STRING",
            },
        ],
    });
    return queryCommonData({
        ...commonParams,
        viewId: "a99325ff701b412997cfc345e3db1f9e",
        aggregators: [
            {
                alias: "销售金额",
                column: ["order_actual_amount"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "酒店名称",
                column: ["order_hotel_name"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
        orders: [
            {
                column: ["order_actual_amount"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
    });
};
//订单支付类型
export const queryOrderingFoodPayType = (name, from): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "bill_check_datetime",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
    });
    filters.push({
        aggOperator: null,
        column: ["bill_state"],
        sqlOperator: "EQ",
        values: [
            {
                value: "已核对",
                valueType: "STRING",
            },
        ],
    });
    return queryCommonData({
        ...commonParams,
        viewId: "a99325ff701b412997cfc345e3db1f9e",
        aggregators: [
            {
                alias: "结算单编码数量",
                column: ["account_code"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "支付类型",
                column: ["order_pay_type"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
//订单支付平台
export const queryOrderingFoodPayPlatform = (name, from): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "bill_check_datetime",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
    });
    filters.push({
        aggOperator: null,
        column: ["budget_source"],
        sqlOperator: "NOT_NULL",
        values: [],
    });
    filters.push({
        aggOperator: null,
        column: ["bill_state"],
        sqlOperator: "EQ",
        values: [
            {
                value: "已核对",
                valueType: "STRING",
            },
        ],
    });
    return queryCommonData({
        ...commonParams,
        viewId: "a99325ff701b412997cfc345e3db1f9e",
        aggregators: [
            {
                alias: "订单数量",
                column: ["order_code"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "预算来源",
                column: ["budget_source"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
//结算单位排行
export const queryOrderingFoodSettleRank = (name, from): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "bill_check_datetime",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
    });
    filters.push({
        aggOperator: null,
        column: ["bill_state"],
        sqlOperator: "EQ",
        values: [
            {
                value: "已核对",
                valueType: "STRING",
            },
        ],
    });
    return queryCommonData({
        ...commonParams,
        viewId: "a99325ff701b412997cfc345e3db1f9e",
        aggregators: [
            {
                alias: "金额",
                column: ["order_actual_amount"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "结算单位名称",
                column: ["account_company_name"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
        orders: [
            {
                column: ["order_actual_amount"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
    });
};
//订餐地图接口
export const queryOrderingFoodMapData = (name, from): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "bill_check_datetime",
        dataSourceKey: 'budget_source',
        budgetDepartmentKey: 'budget_department_code',
    });
    filters.push({
        aggOperator: null,
        column: ["bill_state"],
        sqlOperator: "EQ",
        values: [
            {
                value: "已核对",
                valueType: "STRING",
            },
        ],
    });
    return queryCommonData({
        ...commonParams,
        viewId: "a99325ff701b412997cfc345e3db1f9e",
        aggregators: [
            {
                alias: "订单数量",
                column: ["order_code"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "region_name",
                column: ["region_name"],
            },
            {
                alias: "region_code",
                column: ["region_code"],
            },
            {
                alias: "lng",
                column: ["lng"],
            },
            {
                alias: "lat",
                column: ["lat"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        orders: [
            {
                column: ["order_code"],
                operator: "DESC",
                aggOperator: "COUNT",
            },
        ],
    });
};
