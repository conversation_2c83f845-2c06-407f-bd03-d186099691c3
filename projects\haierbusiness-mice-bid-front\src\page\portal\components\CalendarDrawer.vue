<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { CloseOutlined, DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import CalenderIconImg from '@/assets/image/home/<USER>'
import LikeImg from '@/assets/image/home/<USER>'
import LikeGrayImg from '@/assets/image/home/<USER>'
import { usePortalStore } from '../store';
import { MiceBidCalendar } from '@haierbusiness-front/common-libs';
import { usePagination } from 'vue-request';
import { loading } from 'vxe-pc-ui';

const tableloading = ref(false)

type TableDataType = {
  miceTime: string | number | Date;
  key: string;
  name: string;
  time: string;
  address: string;
  area: string;
  countdown: number;
};

const props = defineProps({
  modelValue: {
    type: Boolean,
  }
});

const emit = defineEmits(['update:modelValue']);

const store = usePortalStore();
const spreadCity = ref(false);
const paginationCurrent = ref(1);
const dataSource = computed(() => store.calendarList || []);

const columns = [
  {
    title: '大会名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true,
  },
  {
    title: '举办时间',
    dataIndex: 'miceTime',
    key: 'miceTime',
    sorter: (a: TableDataType, b: TableDataType) => 
    new Date(a.miceTime).getTime() - new Date(b.miceTime).getTime(),
  },
  {
    title: '举办地点',
    dataIndex: 'area',
    key: 'area',
    width: 180,
  },
  {
    title: '场地面积',
    dataIndex: 'floorSpace',
    key: 'floorSpace',
  },
  {
    title: '距开幕',
    dataIndex: 'countdown',
    key: 'countdown',
    customRender: ({ record }) => {
      const currentDate = new Date();
      const eventDate = new Date(record.miceTime);

      // 清除时间部分，仅比较年月日
      currentDate.setHours(0, 0, 0, 0);
      eventDate.setHours(0, 0, 0, 0);

      const diffDays = Math.floor((eventDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24));
      console.log(diffDays,"diffDays");
      

      return diffDays >= 0 ? `${diffDays}天` : '已结束';
    }
  },
];


const activeCityId = ref<string>('recommand')
const activeCity = (res: string) => {
  tableloading.value = true
  activeCityId.value = res
  console.log(res, "res");

  if (activeCityId.value == 'recommand') {
    const params = {
      pageNum: 1,
      pageSize: 10,
    }
    store.getCalendarList(params)
    tableloading.value = false
  } else {
    const params = {
      city: activeCityId.value,
      pageNum: 1,
      pageSize: 10,
    }
    store.getCalendarList(params)
    tableloading.value = false
  }
}

onMounted(() => {
  activeCity('recommand')
})


const modelOpen = computed({
  get: () => props.modelValue,
  set: v => emit('update:modelValue', v)
});

const showAreaList = computed(() => spreadCity.value ? store.areaList : store.areaList.slice(0, 7))
</script>

<template>
  <h-drawer v-model:open="modelOpen" class="calender-drawer" placement="right" width="750" :closable="false"
    :bodyStyle="{ background: '#F7F8FC', display: 'flex', flexDirection: 'column' }">
    <div class="city flex">
      <span class="label">所在省市：</span>
      <ul>
        <li>
          <span :class="{ selected: activeCityId === 'recommand' }" @click="activeCity('recommand')">
            <img :src="activeCityId === 'recommand' ? LikeImg : LikeGrayImg"
              :style="{ width: '12px', height: '12px', marginTop: '-3px', marginRight: '5px' }" />
            <span>推荐</span>
          </span>
        </li>
        <li v-for="item of showAreaList" :key="item.id" @click="activeCity(item.name)">
          <span :class="{ selected: activeCityId === item.id }">{{ item.name }}</span>
        </li>
      </ul>
      <a @click="spreadCity = !spreadCity">
        <span>{{ spreadCity ? '收起' : '展开' }}</span>
        <DownOutlined v-show="!spreadCity" :style="{ fontSize: '12px', marginLeft: '4px' }" />
        <UpOutlined v-show="spreadCity" :style="{ fontSize: '12px', marginLeft: '4px' }" />
      </a>
    </div>
    <h-table class="table" :dataSource="dataSource" :columns="columns" :pagination="false" :loading="tableloading">
      <template #bodyCell="{ column, record }">
        <!-- <template v-if="column.key === 'countdown'">
          <span :style="{ color: '#FF5533' }">{{ record.countdown }}</span>
          <span>天</span>
        </template> -->
      </template>
    </h-table>
    <!-- <div class="space" /> -->
    <div class="flex-center" v-if="dataSource.length > 0">
      <h-pagination class="pagination" v-model:current="paginationCurrent" :total="dataSource.length" show-less-items />
    </div>
    <template #title>
      <div class="flex acenter">
        <img :src="CalenderIconImg" :style="{ width: '20px', height: '20px', marginRight: '12px' }" />
        <span>大会日历</span>
      </div>
    </template>
    <template #extra>
      <CloseOutlined :style="{ cursor: 'pointer' }" @click="modelOpen = false" />
    </template>
  </h-drawer>
</template>

<style scoped lang="less">
.calender-drawer {
  .city {
    background: #FFFFFF;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
    border-radius: 8px;
    padding: 20px 12px 0 12px;

    .label {
      margin-right: 15px;
    }

    ul {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 0;

      li {
        cursor: pointer;
        width: 68px;
        height: 35px;
        text-align: center;
        color: #4E5969;

        span.selected {
          padding: 5px 7px;
          background: linear-gradient(180deg, #35A1EF 0%, #1868DB 100%);
          box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
          border-radius: 3px;
          color: #fff;
        }
      }
    }
  }

  .table {
    margin-top: 13px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);

    :deep(.ant-table) {
      border-radius: 8px;

      .ant-table-thead {
        tr {
          .ant-table-cell {
            background: #fff;
            color: #86909C;
            font-weight: 400;

            &:first-child {
              border-top-left-radius: 8px;
            }

            &:last-child {
              border-top-right-radius: 8px;
            }

            &::before {
              opacity: 0;
            }
          }
        }
      }
    }
  }

  .pagination {

    :deep(.ant-pagination-prev),
    :deep(.ant-pagination-next),
    :deep(.ant-pagination-item) {
      border: 1px solid rgba(0, 0, 0, 0.15);

      .ant-pagination-item-link,
      a {
        color: rgba(0, 0, 0, 0.65);
      }
    }

    :deep(.ant-pagination-item-active) {
      background: #1868DB;

      a {
        color: #fff;
      }
    }

    :deep(.ant-pagination-disabled) {
      .ant-pagination-item-link {
        color: rgba(0, 0, 0, 0.25);
      }
    }
  }

  .space {
    flex: 1;
  }
}

.flex-center {
  margin-top: 20px;
}
</style>
