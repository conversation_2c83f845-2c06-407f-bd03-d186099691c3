.content {
    height: 9vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url(@/assets/image/bigscreen/bg-accumulative-content.png);
    background-size: 90% 90%;
    background-repeat: no-repeat;
    background-position: center;
}
.num {
    font-size: 25px;
    color: #00f0ff;
    font-weight: 500;
    // width: 70px;
    text-align: center;
    .unit {
        font-size: 14px;
    }
}
.title {
    // width: 70px;
    font-size: 10px;
    text-align: center;
    color: #e2f5fe;
    span {
        margin-right: 5px;
    }
}

@media screen and (min-width: 1700px) {
    .num {
        font-size: 32px;
        text-align: center;
    }
    .title {
        font-size: 12px;
        text-align: center;
    }
}
