<script setup lang="ts">
import {
  Button as hButton,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem,
  Modal,
  message
} from "ant-design-vue";
import { ColumnType } from "ant-design-vue/lib/table/interface";
import { Key } from "ant-design-vue/lib/vc-table/interface";
import {
  PlusOutlined,
  SearchOutlined,
  DownOutlined,
  UpOutlined
} from "@ant-design/icons-vue";
import { parkadeApi } from "@haierbusiness-front/apis";
import {
  BApplyListRecord,
  BApplyPerson,
  BanquetStatusEnum
} from "@haierbusiness-front/common-libs";
import dayjs, { Dayjs } from "dayjs";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { computed, ref, watch, onMounted, createVNode } from "vue";
import { DataType, usePagination, useRequest } from "vue-request";
import { useEditDialog, useDelete } from "@haierbusiness-front/composables";
import {
  getCurrentRouter,
  errorModal,
  routerParam,
  getCurrentRoute
} from "@haierbusiness-front/utils";
import { storeToRefs } from "pinia";
import { applicationStore } from "@haierbusiness-front/utils/src/store/applicaiton";
import type { Rule } from 'ant-design-vue/es/form';
import exportLogModal from "./exportLogModal.vue";

import router from "../../router";
// const router = useRouter()
const store = applicationStore();

const currentRouter = ref();
const route = ref(getCurrentRoute());
const id = route.value?.query?.id;

// 10 详情 20 账单确认 30 维护内接单
const type = route.value?.query?.type;

const detail = ref<any>();

const { loginUser } = storeToRefs(store);
const operationList =ref<any>([]);
const getDetail = (id: number) => {
  parkadeApi.collectInfoById({collectId:id}).then(res => {
    detail.value = res;
  });

  parkadeApi.operationList({collectId:id}).then(res => {
    operationList.value = res;
  });
};
const showMore = ref(false);
const columns: ColumnType[] = [
  {
    title: "订单编号",
    dataIndex: "orderCode",
    align: "center",
    width: "210px",
    fixed: "left",
    ellipsis: true
  },
  {
    title: "车辆系统编号",
    dataIndex: "carSystemCode",
    align: "center",
    width: "200px",
    ellipsis: true
  },
  {
    title: "所属园区",
    dataIndex: "zoneName",
    align: "center",
    width: "200px",
    ellipsis: true
  },
  {
    title: "结算账户",
    dataIndex: "settlementAccount",
    align: "center",
    width: "150px",
    ellipsis: true
  },
  {
    title: "推送金额",
    dataIndex: "pushedAmount",
    align: "center",
    width: "120px",
    ellipsis: true
  },
  {
    title: "充值金额",
    width: "150px",
    dataIndex: "rechargeAmount",
    align: "center",
    ellipsis: true
  },
  {
    title: "到账金额",
    dataIndex: "arrivalAmount",
    width: "150px",
    align: "center",
    ellipsis: true
  },
  {
    title: "账户所属人",
    dataIndex: "accountUser",
    width: "200px",
    align: "center",
    ellipsis: true
  },
  {
    title: "充值时间",
    dataIndex: "rechargeTime",
    align: "center",
    width: "200px",
    ellipsis: true
  },
 
];
// 导出明细
const {
  data:exportBanquetSettleStatementData,
  run:exportBanquetSettleStatement,
  loading:exportBanquetSettleStatementLoading,
} = useRequest(parkadeApi.subOrderExport);

const { data, run: listApiRun, loading, current, pageSize } = usePagination(
  parkadeApi.subOrderList
);
const dataSource = computed(() => data.value?.records || []);
const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: "center" }
}));
const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any
) => {
  listApiRun({
    collectId: id,
    pageNum: pag.current,
    pageSize: pag.pageSize
  });
};

onMounted(async () => {
  currentRouter.value = await router;
  listApiRun({
    collectId: id,
    pageNum: 1,
    pageSize: 10
  });
});

const getInnerPerson = (list?: Array<BApplyPerson>) => {
  if (list && list.length > 0) {
    let resultList = list.filter(item => item.haierUser == true);
    return resultList
      .map(item => `${item.userName}(${item.userCode})`)
      .join(",");
  } else {
    return "";
  }
};

const getOuterPerson = (list?: Array<BApplyPerson>) => {
  if (list && list.length > 0) {
    let resultList = list.filter(item => !item.haierUser);
    return resultList.map(item => item.userName).join(",");
  } else {
    return "";
  }
};

const downloadFile2 = (url: string, name: string) => {
  var xhr = new XMLHttpRequest();
  xhr.open("GET", url, true);
  xhr.responseType = "blob";

  xhr.onload = function() {
    if (xhr.status === 200) {
      var blob = xhr.response;
      var a = document.createElement("a");
      var url = URL.createObjectURL(blob);
      a.href = url;
      a.download = name;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  xhr.send();
};

const downLoadFile = (url: string, name: string) => {
  window.open(url);
  // downloadFile2(url,name)
};

// 

const labelCol = { span: 2 };
const wrapperCol = { span: 8 };
const confirmForm = ref<any>({})

const rules: Record<string, Rule[]> = {
  payType: [{ required: true, message: '请输入内结单号', trigger: 'change' }],
  checked: [{ required: true, message: '请选择账单状态', trigger: 'change' }],
  personList: [{ required: true, message: '请输入备注信息', trigger: 'change' }],
};
const formRef = ref();
const submitLoading = ref(false)
const onSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      submitLoading.value = true
      // 20 确认 30 内接单维护
      if(type == '20') {
        
        const params = { ...confirmForm.value, collectId:id }
        parkadeApi.parkConfirm(params).then(res => {
          message.success("确认成功")
          submitLoading.value = false
          currentRouter.value.push({ path: '/parkade/parkingPayment/billConfirm'})
        }).catch((error) => {
          message.error("确认失败")
          submitLoading.value = false

        })
      }else {

        const params = { ...confirmForm.value, collectId:id }
        parkadeApi.settlement(params).then(res => {
          submitLoading.value = false
          message.success("提交成功")
          currentRouter.value.push({ path: '/parkade/parkingPayment/billConfirm'})
        }).catch((error) => {
          submitLoading.value = false
          message.error("提交失败")

        })


      }
      
    })
    .catch((error:any) => {
      console.log('error', error);
    });
};


watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true
  }
);


const exportLogModalRef = ref();

const showExportLog = () => {
  exportLogModalRef.value.show();
}

</script>

<template>
  <div>
    <h-row justify="space-between" style="padding: 20px;">
      <h-col style="font-size: 18px;">对账单详情</h-col>
      <h-col>
        <h-button type="link" @click="currentRouter.back(-1)">返回</h-button>
      </h-col>
    </h-row>
    <div
      style="background-color: #ffff;height: 100%;width: 100%;padding: 30px 60px;overflow: auto;"
    >
      <h-descriptions  :column="4" title="订单详情" style="margin-bottom: 20px;" bordered>
        <h-descriptions-item label="汇总单号">{{ detail?.collectCode }}</h-descriptions-item>
        <h-descriptions-item label="汇总时间">{{ detail?.collectTime }}</h-descriptions-item>
        <h-descriptions-item label="归账月份">{{ detail?.month }}</h-descriptions-item>
        <h-descriptions-item label="订单时间范围">{{detail?.collectDateStart}} - {{ detail?.collectDateEnd }}</h-descriptions-item>

        <h-descriptions-item label="推送金额汇总">{{ detail?.collectPushedAmount }}元</h-descriptions-item>
        <h-descriptions-item label="实际金额汇总">{{ detail?.collectArrivalAmount }}元</h-descriptions-item>
        <h-descriptions-item label="经办人工号">{{ detail?.userCode }}</h-descriptions-item>
        <h-descriptions-item label="经办人姓名">{{ detail?.userName }}</h-descriptions-item>

        <h-descriptions-item label="所属园区">{{ detail?.zoneName }}</h-descriptions-item>
        <h-descriptions-item label="结算账户">{{ detail?.settlementAccount }}</h-descriptions-item>
        <h-descriptions-item label="订单状态">{{ detail?.collectStatus == 10 ? '未汇总' : detail?.collectStatus == 20 ? '已汇总' : '汇总失败' }}</h-descriptions-item>
        <h-descriptions-item label="结算单号">{{ detail?.settlementCode }}</h-descriptions-item>
        <h-descriptions-item label="取消原因">{{ detail?.cancelReason }}</h-descriptions-item>

      </h-descriptions>

      <h-descriptions  v-if="operationList && operationList.length>0"  :column="4" title="操作记录" style="margin-bottom: 20px;" bordered>
        <template v-for="(item,index) in operationList" :key="index">
          <h-descriptions-item label="业务节点">{{ item?.node }}</h-descriptions-item>
          <h-descriptions-item label="操作时间">{{ item?.operationTime }}</h-descriptions-item>
          <h-descriptions-item label="操作人">{{ item?.user }}</h-descriptions-item>
          <h-descriptions-item label="操作结果">{{ item?.content }}</h-descriptions-item>
        </template>
      </h-descriptions>


      <div class="headerBox">
        <div class="title">子订单信息</div>
        <h-button  type="primary" style="margin-right: 10px;" :loading="exportBanquetSettleStatementLoading" @click="exportBanquetSettleStatement({collectId:id, type:type == 10 ? '30' : type == 20? '40' : '50' })">导出</h-button>
        <h-button  type="primary"  @click="showExportLog">导出记录</h-button>
      </div>
      <h-table
        style="width:100%;"
        :columns="columns"
        :row-key="record => record.id"
        :size="'small'"
        :data-source="dataSource"
        :pagination="pagination"
        :scroll="{ x:1000 }"
        :expand-column-width="100"
        :loading="loading"
        @change="handleTableChange($event as any)"
      >
        <template #bodyCell="{ column, record }">
        
         
          
        </template>
      </h-table>

      <div v-if="type != 10">
        <div class="headerBox">
          <div class="title">账单确认</div>
        </div>

        <h-form
          class="mt-30"
          ref="formRef"
          :model="confirmForm"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          labelAlign="right"
        >
          <h-form-item
            v-if="type == 30"
            label="内结单号"
            name="settlementCode"
            :rules="[{ required: true, message: '请输入内结单号!' }]"
          >
            <h-input v-model:value="confirmForm.settlementCode" placeholder="请输入内结单号" />
          </h-form-item>

      

          <h-form-item v-if="type == 20" label="账单状态" name="result" :rules="[{ required: true, message: '请选择账单状态!' }]">
            <h-radio-group v-model:value="confirmForm.result">
              <h-radio :value="true">账单正确</h-radio>
              <h-radio :value="false">账单错误</h-radio>
            </h-radio-group>
          </h-form-item>

          <h-form-item
            v-if="type == 20"
            label="备注信息"
            name="cancelReason"
            :rules="[{ required: confirmForm.result == false, message: '请输入备注信息!' }]"
          >
            <h-textarea v-model:value="confirmForm.cancelReason" placeholder="请输入备注信息" />
          </h-form-item>

          <a-form-item :wrapper-col="{ span: 14, offset: 4 }">
            <a-button type="primary" :loading="submitLoading" @click="onSubmit">提交</a-button>
            <a-button style="margin-left: 10px" @click="confirmForm={}">重置</a-button>
          </a-form-item>

        </h-form>
      </div>
      


    </div>

    <exportLogModal ref="exportLogModalRef" :businessId="id" :type="type == 10 ? '30' : type == 20? '40' : '50'" />
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}
.headerBox {
  width: 100%;
  display: flex;
  justify-content: space-between;
  height: 80px;
  align-items: center;
  .title {
    white-space: nowrap;
    text-overflow: ellipsis;
    flex: auto;
    color: rgba(0, 0, 0, 0.88);
    font-weight: 600;
    font-size: 16px;
    line-height: 1.5;
  }
}
:deep(.ant-descriptions-item-label) {
  width: 200px;
}
:deep(.ant-descriptions-item-content) {
  width: 300px;
}
</style>
