import { loadEnv } from 'vite'

import vue from '@vitejs/plugin-vue'
import path from 'path';
import postcssPx2remExclude from 'postcss-px2rem-exclude'

const CWD = process.cwd();

export default ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);

  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
    },
    plugins: [vue()],
    build:{
      target:['es2015'],
      sourcemap: true
    },
    server: {
      port: 5186,
      host: "0.0.0.0",
      proxy: {
        // 本地
        // '/hb/trip': {
        //   target: 'http://*************:9222',
        //   // target: 'https://businessmanagement-test.haier.net/hbweb/trip/hb/trip/api',
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(new RegExp(`/hb/trip/api`), ''),
        // },
        "/hb": {
          target: "https://businessmanagement-test.haier.net/hbweb/index/hb",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb/, ""),
        },
        "/upload": {
          target: "https://businessmanagement-test.haier.net/hbweb/upload",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/upload/, ""),
        },
        
      }
    },
    // css: {
    //   postcss: {
    //     plugins: [
    //       postcssPx2remExclude({
    //         remUnit: 192, 
    //         // exclude: /mobile/i // 忽略node_modules目录下的文件
    //       })
    //     ]
    //   }
    // }
  }
}
