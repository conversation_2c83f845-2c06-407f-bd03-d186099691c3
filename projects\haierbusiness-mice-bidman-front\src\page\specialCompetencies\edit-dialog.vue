<script lang="ts" setup>
import { Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
     Input as hInput, Textarea as hTextarea, DatePicker as hDatePicker } from 'ant-design-vue';
import { computed, ref, watch } from "vue";
import type { Ref } from "vue";
import {
  ISpecialCompetencies
} from '@haierbusiness-front/common-libs';

// 使用一次导入的组件避免未使用警告
const useImports = () => {
  return { hSelect, hSelectOption, hModal, hForm, hFormItem, hInput, hTextarea, hDatePicker };
}
useImports();

interface Props {
    show: boolean;
    data: ISpecialCompetencies | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: Partial<ISpecialCompetencies> = {
    orderNo: '',
    meetingName: '',
    specialPermission: '',
    status: '1',
    rejectReason: '',
    operator: '',
    activator: '',
    authTime: '',
    applyTime: '',
    effectiveTime: '',
    id: null,
    description: ''
};

const rules = {
    orderNo: [{ required: true, message: '请输入订单号', trigger: 'blur' }],
    meetingName: [{ required: true, message: '请输入会议名称', trigger: 'blur' }],
    specialPermission: [{ required: true, message: '请输入特殊权限', trigger: 'blur' }],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }],
};

const statusOptions = [
  { label: '启用', value: '1' },
  { label: '禁用', value: '0' },
];

const specialCompetencies = ref<ISpecialCompetencies>(
  props.data ? { ...props.data } as ISpecialCompetencies : { ...defaultData } as ISpecialCompetencies
);

watch(props, (newValue) => {
  if (newValue.data) {
    specialCompetencies.value = { ...newValue.data } as ISpecialCompetencies;
  } else {
    specialCompetencies.value = { ...defaultData } as ISpecialCompetencies;
  }
});

const emit = defineEmits(["cancel", "ok"]);

const visible = computed(() => props.show);

const handleOk = () => {
  confirmLoading.value = true;
  from.value
    .validate()
    .then(() => {
      emit("ok", specialCompetencies.value, () => {
        confirmLoading.value = false;
      });
    })
    .catch(() => {
        confirmLoading.value = false;
    });
};
</script>

<template>
    <h-modal
      :visible="visible"
      :title="specialCompetencies.id ? '编辑特殊权限确认列表' : '新增特殊权限确认列表'"
      :width="600"
      @cancel="$emit('cancel')"
      :confirmLoading="confirmLoading"
      @ok="handleOk"
    >
      <h-form
        ref="from"
        :model="specialCompetencies"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
        :rules="rules"
      >
        <h-form-item label="订单号" name="orderNo">
          <h-input v-model="specialCompetencies.orderNo" placeholder="请输入订单号" />
        </h-form-item>
        
        <h-form-item label="会议名称" name="meetingName">
          <h-input v-model="specialCompetencies.meetingName" placeholder="请输入会议名称" />
        </h-form-item>
        
        <h-form-item label="特殊权限" name="specialPermission">
          <h-input v-model="specialCompetencies.specialPermission" placeholder="请输入特殊权限" />
        </h-form-item>
        
        <h-form-item label="状态" name="status">
          <h-select v-model="specialCompetencies.status" placeholder="请选择状态">
            <h-select-option v-for="option in statusOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </h-select-option>
          </h-select>
        </h-form-item>

        <h-form-item label="驳回原因" name="rejectReason">
          <h-textarea v-model="specialCompetencies.rejectReason" placeholder="请输入驳回原因(非必填)" :rows="2" />
        </h-form-item>
        
        <h-form-item label="经办人" name="operator">
          <h-input v-model="specialCompetencies.operator" placeholder="请输入经办人(非必填)" />
        </h-form-item>
        
        <h-form-item label="开通人" name="activator">
          <h-input v-model="specialCompetencies.activator" placeholder="请输入开通人(非必填)" />
        </h-form-item>
        
        <h-form-item label="授权时间" name="authTime">
          <h-date-picker v-model="specialCompetencies.authTime" style="width: 100%" value-format="YYYY-MM-DD" />
        </h-form-item>
        
        <h-form-item label="申请时间" name="applyTime">
          <h-date-picker v-model="specialCompetencies.applyTime" style="width: 100%" value-format="YYYY-MM-DD" />
        </h-form-item>
        
        <h-form-item label="生效时间" name="effectiveTime">
          <h-date-picker v-model="specialCompetencies.effectiveTime" style="width: 100%" value-format="YYYY-MM-DD" />
        </h-form-item>
      </h-form>
    </h-modal>
</template>


<style lang="less" scoped>
.important {
  color: red;
}
:deep(.ant-form-item-required::before) {
  display: none !important;
}
</style>
  