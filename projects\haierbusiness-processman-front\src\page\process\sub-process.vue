<script lang="ts" setup>
import { Select as hSelect, SelectOption as hSelectOption, Form as hForm, FormItem as hFormItem, Textarea as hTextarea, RadioGroup as hRadioGroup, Radio as hRadio,
     Input as hInput, Steps as hSteps, Step as hStep, <PERSON><PERSON> as hButton, Result as hResult, message, CheckboxGroup as hCheckboxGroup, Checkbox as hCheckbox,
    Row as hRow, Col as hCol, Card as hCard, Cascader as hCascader } from 'ant-design-vue';
import { computed, ref, watch, onActivated, onDeactivated } from "vue";
import type { Ref } from "vue";
import {
    IProcessIno, IStepsInfo, IOperatorsInfo, ProcessNotificationMethodConstant, IUserListRequest, IUserInfo
} from '@haierbusiness-front/common-libs';
import { useRequest, usePagination } from 'vue-request';
import { userApi, enterpriseApi, processApi, departmentApi } from '@haierbusiness-front/apis';
import { debounce, toNumber } from 'lodash';
import { guid, resolveParam, getCurrentRoute, getCurrentRouter } from '@haierbusiness-front/utils';
import Steps from './step.vue'
import { CloseCircleOutlined } from '@ant-design/icons-vue';
import type { FormInstance } from 'ant-design-vue';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue'
import { on } from 'process';

const props = defineProps({
  query: Object
});

const router = getCurrentRouter();
const route = ref(getCurrentRoute());

const formRef = ref<FormInstance>();

const current = ref<number>(0);

const subProcess = ref<IProcessIno>({})

const name = ref('')

const notificationMethod = computed(() => ProcessNotificationMethodConstant.toArray())

const newSubProcess = computed(() => {
    subProcess.value.steps?.forEach((item) => {
        item.guid = guid()
        item.stepOperators?.forEach((operator) => {
            operator.guid = guid()
        })
        item.operators = item.stepOperators ? item.stepOperators : []
    })
    return subProcess.value ? subProcess.value : {} as IProcessIno
})

const pdId = ref<number>(toNumber(route.value.query.pdId))

onActivated(() => {
    route.value = getCurrentRoute()
    pdId.value = toNumber(route.value.query.pdId) || 0
    name.value = route.value.query.name?.toString()
    if (props.query?.record) {
        const data = resolveParam(props.query?.record) as IProcessIno
        subProcess.value = data
        username.value = data.nickName!
        revocable.value = subProcess.value.revocable === 1
        assist.value = subProcess.value.assist === 1
        additionable.value = subProcess.value.additionable === 1
        nodeCall.value = subProcess.value.nodeCall === 1
        autograph.value = subProcess.value.autograph === 1
        voteCall.value = subProcess.value.voteCall === 1
        computedMethod(subProcess.value.todoMethod)
        if(subProcess.value.scope) {
            deptListApiRun(subProcess.value.enterpriseCode?.toString(), subProcess.value.departmentName, 1, 20)
        }
        // handleNewSubProcess()
    }
    getSceneList()
})

onDeactivated(() => {
    current.value = 0
    subProcess.value = {}
    method.value = []
    revocable.value = false
    assist.value = false
    additionable.value = false
    nodeCall.value = false
    autograph.value = false
    // props.query = {}
})

const computedMethod = (num: number | undefined) => {
    if(!num) {
        return
    }

    const list = ProcessNotificationMethodConstant.toNumberArray()
    
    const array: number[] = []
    list.map(item => {
        if((num & item!) != 0) {
            array.push(item!)
        }
    })
    method.value = array
}

watch(() => route.value.query, () =>{
    pdId.value = toNumber(route.value.query.pdId) || 0
    if (props.query?.record) {
        const data = resolveParam(props.query?.record) as IProcessIno
        subProcess.value = data
        username.value = data.nickName!
    }
})

// 查询企业
const {
  data: enterprises,
  run: enterpriseListApiRun
} = useRequest(enterpriseApi.list, {
  manual: false
})


const enterpriseSelect = computed(() => {
  return enterprises.value || []
})

const enterpriseCodeChange = (value: string) => {
  const enterprise = enterpriseSelect.value.find(o => o.code === value)
  if(enterprise) {
    subProcess.value.enterpriseName = enterprise.name
    // 如果作用域是用户 查询企业下用户
    params.value.enterpriseCode = enterprise.code?.toString()
  }
}

const scopeChange = (value: number) => {
    if(value === 2) {
        deptListApiRun(subProcess.value.enterpriseCode?.toString(), undefined, 1, 20)
    }else if(value==3){
        username.value = ''
    }
}

// 查询部门
const {
  data: dept,
  run: deptListApiRun
} = usePagination(departmentApi.departmentList, {
  manual: true,
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
  },
})

const deptCodeChange = (value: string) => {
  const department = dept.value?.records?.find(o => o.code === value)
  if(department) {
    subProcess.value.departmentName = department.name
  }
}

// 查询场景
const sceneList = ref<any>([])
const getSceneList = () =>{
  processApi.sceneList({pdId:route.value.query.pdId,active:1}).then(res=>{
    console.log(res)
    sceneList.value = res
  })
}

const fetchDept = debounce(value => {
    deptListApiRun(subProcess.value.enterpriseCode?.toString(), value, 1, 20)
}, 500);

const userNameChange = (userInfo: IUserInfo | undefined) => {
    if(!userInfo) {
        username.value = ''
        subProcess.value.username = ''
        subProcess.value.nickName = undefined
        return
    }
    username.value = userInfo.nickName ?? ''
    subProcess.value.username = userInfo.username
    subProcess.value.nickName = userInfo.nickName ?? ''
}

const rules = {
  name: [{ required: true, message: "请输入规则名称！" }],
  scope: [{ required: true, message: "请选择作用域！" }],
  enterpriseCode: [{ required: true, message: "请选择企业！" }],
  showStatus: [{ required: true, message: "请选择展示状态！" }],
};

const handleOk = () => {
    currentChange(1)
}

const method = ref<Array<number>>()

const submit = async (steps: IStepsInfo[]) => {
    const subSteps = [] as IStepsInfo[]
    steps.forEach((item, index) => {
        const operators: IStepsInfo[] = []
        item.operators!.forEach((operator) => {
            const newOperator: IOperatorsInfo = {
                role: operator.role,
                roleName: operator.roleName,
                substituteRole:operator.substituteRole,
                substituteRoleName:operator.substituteRoleName
            }
            if(operator.substituteApprover) {
                newOperator.substituteApprover = operator.substituteApprover
                newOperator.substituteApproverName = operator.substituteApproverName
            }
            if(operator.approver) {
                newOperator.approver = operator.approver
                newOperator.approverName = operator.approverName
            }
            operators.push(newOperator)
        })

        const step: IStepsInfo = {
            name: item.name,
            type: item.type,
            seq: index + 1,
            operators
        }
        subSteps.push(step)
    })
    let todoMethod =  0

    if(method.value && method.value.length > 0) {
        method.value.map((item) => {
            todoMethod += item
        })
    }
    const subData: IProcessIno = {
        ...subProcess.value,
        enterpriseCode: subProcess.value.enterpriseCode?.toString(),
        steps: subSteps,
        pdId: pdId.value,
        additionable:  additionable.value ? 1 : 0,
        revocable: revocable.value ? 1 : 0,
        assist: assist.value ? 1 : 0,
        nodeCall: nodeCall.value ? 1 : 0,
        autograph: autograph.value ? 1 : 0,
        voteCall: voteCall.value ? 1 : 0,
        todoMethod: todoMethod
    }

    if (subData.id) {
        subData.pdsId = subData.id
        const data = await processApi.editSub(subData)
        if(data) {
            message.success('操作成功！')
            router.push({ path: "/processman/process/index" })
        }
    } else {
        const data = await processApi.saveSub(subData, undefined)
        if(data) {
            message.success('操作成功！')
            router.push({ path: "/processman/process/index" })
        }
    }
}

const currentChange = (num: number) => {
    current.value += num
}

const username = ref('')

const onChange = async (currentStep: number) => {
    console.log(currentStep)
    try {
        await formRef.value?.validateFields();
        current.value = currentStep
    } catch (errorInfo) {
        console.log('Failed:', errorInfo);
    }
}


const revocable = ref(subProcess.value.revocable === 1)
const assist = ref(subProcess.value.assist === 1)
const additionable = ref(subProcess.value.additionable === 1)
const nodeCall = ref(subProcess.value.nodeCall === 1)
const autograph = ref(subProcess.value.autograph === 1)
const voteCall = ref(subProcess.value.voteCall === 1)

// 选择用户
const params = ref<IUserListRequest>({
    enterpriseCode: subProcess.value.enterpriseCode,
    pageNum: 1,
    pageSize: 20
})

</script>

<template>
    <h-card :title="'主流程&nbsp;-&nbsp;' + name + '&nbsp;-&nbsp;'+'[' + pdId + ']'" :headStyle="{ 'font-weight': 'bold' }" style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
            <div class="header">
                <div class="step">
                    <h-steps :current="current">
                        <h-step @click="onChange(0)">
                            <template #title>规则定义</template>
                        </h-step>
                        <h-step @click="onChange(1)">
                            <template #title>流程步骤</template>
                        </h-step>
                    </h-steps>
                </div>
            </div>
            <div class="content">
                <div class="form" v-if="current === 0">
                    <h-form 
                        ref="formRef"
                        :rules="rules"
                        :model="subProcess"
                        :label-col="{ span: 6 }"
                        :wrapper-col="{ span: 15 }"
                        @finish="handleOk"
                    >
                        <h-form-item label="主流程名称">
                            <span>{{ name }}</span>
                        </h-form-item>
                        <h-form-item label="规则名称" name="name">
                            <h-input v-model:value="subProcess.name" />
                        </h-form-item>
                        <h-form-item label="企业" name="enterpriseCode" >
                            <h-select ref="select" v-model:value="subProcess.enterpriseCode" @change="(value) =>  enterpriseCodeChange(value as string)">
                                <h-select-option v-for="(item, index) in enterpriseSelect" :value="item.code?.toString()" :key="index">{{ item.name }}</h-select-option>
                            </h-select>
                        </h-form-item>
                        <h-form-item label="作用域" name="scope">
                            <h-select v-model:value="subProcess.scope" @change="(value) =>  scopeChange(value as number)">
                                <h-select-option :value="1">企业级</h-select-option>
                                <h-select-option :value="2">部门级</h-select-option>
                                <h-select-option :value="3">个人</h-select-option>
                            </h-select>
                        </h-form-item>
                        <h-form-item label="部门" name="departmentCode" v-if="subProcess.scope === 2">
                            <h-select ref="select" v-model:value="subProcess.departmentCode" :filter-option="false" show-search @change="(value) =>  deptCodeChange(value as string)" @search="fetchDept">
                                <h-select-option v-for="(item, index) in (dept?.records ?? [])" :value="item.code.toString()" :key="index">{{ item.name }}({{ item.code }})</h-select-option>
                            </h-select>
                        </h-form-item>
                        <h-form-item label="个人" name="username" v-if="subProcess.scope === 3">
                            <user-select :value="username" placeholder="请选择" :cache-key="'processUser'"  :params="params" @change="(userInfo: IUserInfo | undefined) =>  userNameChange(userInfo)" />
                        </h-form-item>
                        <h-form-item label="场景" name="sceneCode">
                            <h-select allow-clear ref="select" v-model:value="subProcess.sceneCode" :filter-option="false" show-search @change="(value) =>  deptCodeChange(value as string)" @search="fetchDept">
                                <h-select-option v-for="(item, index) in sceneList" :value="item.code.toString()" :key="index">{{ item.name }}</h-select-option>
                            </h-select>
                        </h-form-item>
                        <h-row>
                            <h-col :span="6" class="setting-item-label">
                                配置项：
                            </h-col>
                            <h-col :span="15">
                                <h-row>
                                    <h-col :span="6">
                                        <h-checkbox v-model:checked="revocable">可撤回</h-checkbox>
                                    </h-col>
                                    <h-col :span="6">
                                        <h-checkbox v-model:checked="assist">可代审</h-checkbox>
                                    </h-col>
                                    <h-col :span="6">
                                        <h-checkbox v-model:checked="additionable">可加审</h-checkbox>
                                    </h-col>
                                    <h-col :span="6">
                                        <h-checkbox v-model:checked="autograph">电子签名</h-checkbox>
                                    </h-col>
                                </h-row>
                            </h-col>
                        </h-row>
                        <h-row style="padding: 15px 0;">
                            <h-col :span="6" class="setting-item-label">
                                回调方式：
                            </h-col>
                            <h-col :span="15">
                                <h-row>
                                    <h-col :span="6">
                                        <h-checkbox v-model:checked="nodeCall">节点回调</h-checkbox>
                                    </h-col>
                                    <h-col :span="6">
                                        <h-checkbox v-model:checked="voteCall">投票回调</h-checkbox>
                                    </h-col>
                                </h-row>
                            </h-col>
                        </h-row>
                        <h-form-item label="待办通知方式" name="method">
                            <h-checkbox-group v-model:value="method" style="width: 100%">
                                <h-checkbox v-for="(item, index) in notificationMethod" :key="index" :value="item!.code">{{ item!.desc }}</h-checkbox>
                            </h-checkbox-group>
                        </h-form-item>
                        <div class="submit-btn">
                            <h-button type="primary" html-type="submit" class="sub-btn">下一步</h-button>
                        </div>
                    </h-form>
                </div>
                <div class="form" v-if="current === 1">
                    <steps :data="newSubProcess.steps ?? []" :enterpriseCode="subProcess.enterpriseCode ?? ''" :assist="assist"  @currentChange="currentChange" @next="submit" />
                </div>
            </div>
        </h-card>
    
</template>

<style lang="less" scoped>

.header {
    width: 100%;
    padding: 12px 16px;
    margin-top: 24px;

    .step {
        max-width: 960px;
        margin: auto;
    }
}

.content {
    width: 100%;
    padding: 12px 16px;
    margin-top: 30px;

    .form {
        max-width: 800px;
        margin: auto;
    }
}

.top {
    background-color: #f5f5f5 !important;
}

.userHeader {
    display: flex;
    width: 100%;
    flex-direction: row;
    color: #262626;

    .no {
        display: flex;
        flex: 1;
    }

    .phone {
        display: flex;
        flex: 1;
    }

    .name {
        display: flex;
        flex: 1;
    }

    .email {
        display: flex;
        flex: 1;
    }
}

.submit-btn {
  width: 100%;
  display: flex;
  justify-content: space-around;
  padding-bottom: 20px;
}

.setting-item-label {
    display: inline-block;
    flex-grow: 0;
    overflow: hidden;
    white-space: nowrap;
    text-align: end;
    vertical-align: middle;
}

</style>
<style>
.userHeaderForSelect {
    background-color: #fafafa !important;
}
</style>