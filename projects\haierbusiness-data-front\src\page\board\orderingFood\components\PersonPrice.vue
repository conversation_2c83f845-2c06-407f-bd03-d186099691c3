<template>
    <div
        v-loading="loading"
        element-loading-background="rgba(0, 0, 0, 0)"
        background="rgba(0,0,0,0)"
        :id="id"
        :style="{ height: props.height + 'vh' }"
    ></div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import * as echarts from "echarts";
import { queryOrderingFoodPriceDistribution } from "@haierbusiness-front/apis/src/data/board";
import { circle2 as cicleOptions, colors } from "../../data";
import { EventBus } from "../../eventBus";
const props = defineProps({
    height: {
        type: Number,
        default: 33,
    },
});
const id = ref("cicle-" + Date.now());
const loading = ref(false);
const payTypeCheck = ref<string>("");

let chartDom, myChart;
onMounted(() => {
    queryData();
    chartDom = document.getElementById(id.value);
    myChart = echarts.init(chartDom as any, "dark");
    myChart.on("click", (param) => {
        if (
            param.from != "price_distribution" &&
            param.name != payTypeCheck.value
        ) {
            payTypeCheck.value = param.name;
            EventBus.emit("refresh", {
                ...param,
                from: "price_distribution",
            });
        } else {
            payTypeCheck.value = "";
            EventBus.emit("refresh");
        }
    });
});
EventBus.on((event, params) => {
    if (event == "refresh") {
        //获取缓存中筛选的模块
        if (!params) queryData();
        //同组件触发
        if (params && params.from != "price_distribution") {
            queryData(params);
        }
        if (params && params.from == "price_distribution") {
            queryData().then(() => {
                myChart.dispatchAction({
                    type: "highlight",
                    seriesIndex: 0,
                    dataIndex: params.dataIndex,
                });
            });
        }
    }
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
    loading.value = true;
    const data = await queryOrderingFoodPriceDistribution(
        params ? params.data.name : null,
        params ? params.from : null
    );
    loading.value = false;
    const rows = [] as Array<{
        name: string;
        value: string | number;
    }>;
    data.rows.forEach((item, index) => {
        rows.push({
            value: item[1],
            name: item[0],
        });
    });
    const { series } = cicleOptions;
    series[0].color = colors;
    series[0].data = rows;
    myChart.clear();
    myChart.setOption(cicleOptions);
};
</script>
<style scoped lang="less"></style>
