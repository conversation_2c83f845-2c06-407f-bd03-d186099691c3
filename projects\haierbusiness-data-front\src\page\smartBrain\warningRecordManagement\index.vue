<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
  Modal
} from "ant-design-vue";
import { ColumnType } from "ant-design-vue/lib/table/interface";
import { Key } from "ant-design-vue/lib/vc-table/interface";
import {
  DownOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined
} from "@ant-design/icons-vue";
import { warningApi } from "@haierbusiness-front/apis";
import {
  IEnterpriseListRequest,
  IEnterprise
} from "@haierbusiness-front/common-libs";
import { errorModal, routerParam } from "@haierbusiness-front/utils";
import dayjs, { Dayjs } from "dayjs";
import { computed, onMounted, ref, watch, h } from "vue";
import { DataType, usePagination, useRequest } from "vue-request";
import { useRouter } from "vue-router";
import { useEditDialog, useDelete } from "@haierbusiness-front/composables";

const router = useRouter();
const columns: ColumnType[] = [
  {
    title: "预警类型",
    dataIndex: "warningTypeName",
    width: "150px",
    align: "left"
  },
  {
    title: "预警时间",
    dataIndex: "warningDate",
    width: "150px",
    align: "left"
  },
  {
    title: "预警内容",
    dataIndex: "messageContent",
    width: "750px",
    align: "left"
  },
  {
    title: "预警接收人",
    dataIndex: "recipientName",
    width: "150px",
    align: "left"
  },
  {
    title: "触发条件",
    dataIndex: "warningTriggerCondition",
    width: "350px",
    align: "left"
  },
  {
    title: "操作",
    dataIndex: "_operator",
    width: "250px",
    fixed: "right",
    align: "center"
  }
];
const searchParam = ref<any>({});
const typeList = ref<any>([]);
const manageParams = ref();
const editDetail = ref<any>({});
// 明细弹窗
const detailVisible = ref<boolean>(false);

const { data, run: listApiRun, loading, current, pageSize } = usePagination(
  warningApi.list,
  {
    defaultParams: [{}],
    manual: false
  }
);

const reset = () => {
  searchParam.value = {};
  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize
  });
};

const dataSource = computed(
  () =>
    data.value?.records?.filter(item => {
      if (item.iconUrl) {
        item.files = [
          {
            name: `${item.productName}`,
            thumbUrl: item.iconUrl
          }
        ];
      }
      return item;
    }) || []
);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: "center" }
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize
  });
};

// 新增表单相关
const {
  visible,
  editData,
  handleCreate,
  handleEdit,
  onDialogClose,
  handleOk
} = useEditDialog<IEnterprise, IEnterprise>(warningApi, "工单", () =>
  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize
  })
);

const { handleDelete } = useDelete(warningApi, () =>
  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize
  })
);

const EditDetailDialogCancel = () => {
  detailVisible.value = false;
  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize
  });
};

const saveWorkOrder = (res: any) => {
  visible.value = false;
  editDetail.value = res;
  detailVisible.value = true;
  listApiRun({
    pageNum: 1,
    pageSize: 10
  });
};

const rangePresets = ref([
  { label: "最近一年", value: [dayjs().add(-365, "d"), dayjs()] },
  { label: "最近半年", value: [dayjs().add(-180, "d"), dayjs()] }
]);

const {
  data: exportListData,
  run: exportListApiRun,
  loading: exportListLoading
} = useRequest(warningApi.exportList);

const showConfirm = (record: any) => {
  Modal.confirm({
    title: "确认要再次发送通知预警吗?",
    icon: h(ExclamationCircleOutlined),
    onOk() {
      warningApi.send({ id: record.id }).then(res => {
        listApiRun({
          pageNum: 1,
          pageSize: 10
        });
      });
    },
    onCancel() {
      console.log("Cancel");
    },
    class: "test"
  });
};

// 请求类型下拉
const getTypeList = () => {
  warningApi.getEarlyWarningTypeList().then(res => {
    typeList.value = res;
  });
};
// 编辑明细
const handleDateilEdit = (row: any) => {
  editDetail.value = row;
  detailVisible.value = true;
};
// 导出明细
onMounted(() => {
  getTypeList();
});
</script>

<template>
  <div
    style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto"
  >
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 30px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col style="text-align: right; padding-right: 10px;width:80px;">
            <label for="titleName">预警日期：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker
              valueFormat="YYYY-MM-DD"
              :presets="rangePresets"
              v-model:value="searchParam.gmtCreate"
            />
          </h-col>
          <h-col style="text-align: right; padding-right: 10px;width:100px;">
            <label for="mobile">预警类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              allowClear
              placeholder="请选择预警类型"
              v-model:value="searchParam.warningType"
              style="width: 100%"
            >
              <h-select-option v-for="item in typeList" :value="item.value">
                {{
                item.name
                }}
              </h-select-option>
            </h-select>
          </h-col>
          <h-col style="text-align: right; padding-right: 10px;width:100px;">
            <label for="mobile">接收人：</label>
          </h-col>
          <h-col :span="4">
            <h-input allowClear v-model:value="searchParam.recipientName" style="width: 100%" />
          </h-col>
          <h-col style="text-align: right; padding-right: 10px;width:100px;">
            <label for="mobile">接收人工号：</label>
          </h-col>
          <h-col :span="4">
            <h-input allowClear v-model:value="searchParam.recipient" style="width: 100%" />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right">
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
            <h-button style="margin-left: 10px" @click="reset">重置</h-button>
            <h-button
              type="primary"
              style="margin-left: 10px"
              :loading="exportListLoading"
              @click="exportListApiRun(searchParam)"
            >
              <UploadOutlined />导出
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :ellipsis="true"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="dataSource"
          :pagination="pagination"
          :scroll="{ y: 550 }"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'recipientName'">
              {{ record.recipientName }}({{ record.recipient }})
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="showConfirm(record)">再次通知</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
:deep(.ant-col) {
  color: #000;
}
</style>
