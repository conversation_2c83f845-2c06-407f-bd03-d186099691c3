import {
    IBudgetConfigRequest,
    Result
} from '@haierbusiness-front/common-libs'
import {download, get, post} from '../request'

export const budgetConfigApi = {

    /**
     * 查询应用信息
     */
     list: (applicationCode: string): Promise<IBudgetConfigRequest> => {
        return get('pay/api/application/pay/type/get-all-info', { applicationCode })
    },

    /**
     * 应用配置预算
     */
    save: (params: IBudgetConfigRequest): Promise<Result> => {
        return post('pay/api/application/pay/type/save', params)
    },
}