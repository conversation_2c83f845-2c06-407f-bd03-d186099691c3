<script lang="ts" setup>
import { computed, createVNode, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';

import {
  Anchor as hAnchor,
  <PERSON><PERSON> as hButton,
  <PERSON> as hSpin,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Checkbox as hCheckbox,
  Form as hForm,
  InputNumber as hInputNumber,
  FormItem as hFormItem,
  Row as hRow,
  Col as hCol,
  Popconfirm as hPopconfirm,
  Upload as hUpload,
  Input as hInput,
  Modal as hModal,
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { getEnumOptions } from '@haierbusiness-front/utils';

import type { AnchorProps, SelectProps } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  DownloadOutlined,
  VerticalAlignBottomOutlined,
  ExclamationCircleFilled,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  SendOutlined,
  PhoneOutlined,
  VerticalAlignTopOutlined,
  DeleteOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { download, tripApi, teamListApi, rechargeApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import relativeTime from 'dayjs/plugin/relativeTime';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';
import { AccountRechargeApproveStatusEnum } from '@haierbusiness-front/common-libs';

import {
  CityResponse,
  CityItem,
  TCteateTeam,
  RRechargeSaveParams,
  RRechargeAccountRecordParams,
  RechargePayStatusEnum,
  RechargeUserPayStatusEnum
} from '@haierbusiness-front/common-libs';
import { fileApi } from '@haierbusiness-front/apis';
import type { TableColumnType } from 'ant-design-vue';
import { useRouter } from 'vue-router';

const route = ref(getCurrentRoute());
const router = useRouter();

const id = route.value?.query?.id;

const goRecharge = () => {
  router.push('/payman/recharge');
};

watch(
  () => id,
  () => {},
);

const rechargeForm = ref<RRechargeAccountRecordParams>({

});

// 下发状态
const payState = computed(() => {
  const list = getEnumOptions(RechargeUserPayStatusEnum, true);
  list.forEach((item) => {
    item.text = item.label;
  });
  return list;
});

const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    align: 'center',
  },
  {
    title: '员工姓名',
    dataIndex: 'ownerName',
    align: 'center',
  },
  {
    title: '员工工号',
    dataIndex: 'ownerCode',
    align: 'center',
  },
  {
    title: '下发金额',
    dataIndex: 'amount',
    align: 'center',
  },
  {
    width: 150,
    title: '下发状态',
    dataIndex: 'rechargeStatus',
    align: 'center',
    filters: payState.value,
    onFilter: (value: string, record: any) => record.rechargeStatus == value,
  },
];
const detail = ref(null);

onMounted(() => {
  getDetail();
});
const formatDateTime = (time: string | undefined) => {
  if (!time) {
    return '';
  }
  return dayjs(time).format('YYYY年MM月DD日 HH:mm:ss');
};
// 根据id获取详情
const getDetail = () => {
  rechargeApi.getExcitationDetail(id).then((res) => {
    res.detailList.forEach((item:any,index:number)=>{
      item.index = index+1
    })
    detail.value = res;
  });
};
const pagination = computed(() => ({
  total: detail.value?.detailList.length || 0, // 总条数
  showTotal: (total:number) => `共 ${total} 条`, // 显示总条数
  pageSize: 10, // 每页显示的条数
  showSizeChanger: true, // 是否可以改变每页显示的条数
  // showQuickJumper: true, // 是否可以快速跳转到指定页
}));
const getStatus=(status:number)=>{
  if(status==10){
    return '待下发'
  }else if(status==20){
    return '下发中'
  }else if(status==30){
    return '下发失败'
  }else if(status==40){
    return '部分成功'
  }else if(status==90){
    return '下发成功'
  }
}

dayjs.extend(relativeTime);

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const labelCol = { span: 3 };
const wrapperCol = { span: 12 };
const spinning = ref<boolean>(false);
</script>

<template>
  <div class="container">
    <div class="row flex">
      <div class="apply-con flex">

        <a-descriptions title="订单详情" class="banner-detail" :column="1">
          <a-descriptions-item label="订单编号">{{ detail?.orderCode }}</a-descriptions-item>
          <a-descriptions-item label="操作人">{{ detail?.applyName }}({{ detail?.applyCode }})</a-descriptions-item>
          <a-descriptions-item label="操作时间">{{ detail?.gmtCreate }}</a-descriptions-item>
          <a-descriptions-item label="总计金额">{{ detail?.amountSum }}</a-descriptions-item>
          <a-descriptions-item label="下发状态">{{
            getStatus(detail?.rechargeStatus)
          }}</a-descriptions-item>
          <a-descriptions-item label="人员明细">
            <a-table :columns="columns" :pagination="pagination" :data-source="detail?.detailList" size="small" bordered>
              <template #bodyCell="{ column, text, index }">
                <!-- <template v-if="column.dataIndex === 'index'">
                  {{ index + 1 }}
                </template> -->
                <template v-if="column.dataIndex === 'rechargeStatus'">
                  {{ RechargeUserPayStatusEnum[text] }}
                </template>
              </template>
            </a-table>
          </a-descriptions-item>
          <a-descriptions-item label="激励留言">{{ detail?.incentiveMessage }}</a-descriptions-item>
        </a-descriptions>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url(../recharge/recharge.less);
.container{
  width: 100%;
  .row{
    width: 100%;
    margin-top: 0;
    .apply-con{
      width: 100%;
      padding-top:20px;
    }
  }
}
.change-title {
  position: absolute;
  right: 0;
  top: -80px;
  width: 300px;

  .ant-col-6,
  .ant-col-18 {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #000000d9;
  }
}

.mt-5 {
  margin-top: 5px;
}

.ml-5 {
  margin-left: 5px;
}

.color-eee {
  color: #a8a7a7;
}

.com-box {
  background: #f5f5f5;
  padding: 20px;
  margin-top: 20px;

  .whole-line {
    height: 40px;
    font-weight: 600;
  }

  .info-box {
    display: flex;
    align-items: flex-start;
    padding: 10px 20px;
    border: 1px solid #ffdb5c;
    background: #fffbd8;
  }

  :deep(.ant-form-item-control-input-content) {
    display: flex;
    align-items: center;
  }
}

.title {
  height: 60px;
}

.download-btn {
  color: #408cff;
  cursor: pointer;
}

:deep(.city-chose-box) {
  width: 200px !important;
}

:deep(.mr-10) {
  margin-right: 10px;
}

.background-eee {
  padding: 10px;
  background: #f4f4f4;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
