<script setup lang="ts">
import {
  Upload as HUpload,
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
} from 'ant-design-vue';
import type { UploadChangeParam, UploadProps } from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { DownOutlined, ExclamationCircleOutlined, PlusOutlined, UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { hotelApi } from '@haierbusiness-front/apis';
import {
  asyncStatus,
  aggregationStatus,
  supplierType
} from '@haierbusiness-front/common-libs';
import { getCurrentRouter , errorModal, routerParam ,getEnumOptions } from '@haierbusiness-front/utils';
import Eloading from '@haierbusiness-front/components/loading/ELoading.vue';
import dayjs, { Dayjs } from 'dayjs';
import { Ref, computed, getCurrentInstance, onMounted, ref, watch,createVNode } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';

const router = getCurrentRouter()
const asyncStatusOptions = computed(()=>{
  return getEnumOptions(asyncStatus,true)
})

const supplierTypeOptions = computed(()=>{
  return getEnumOptions(supplierType)
})

const aggregationStatusOptions = computed(()=>{
  return getEnumOptions(aggregationStatus,true)
})
const columns: ColumnType[] = [
  {
    title: '供应商',
    dataIndex: 'providerCodeName',
    width: '120px',
    align: 'center',
   
  },
  {
    title: '开始时间',
    dataIndex: 'startTime',
    width: '150px',
    align: 'center',
   
  },
  {
    title: '完成时间',
    dataIndex: 'endTime',
    width: '150px',
    align: 'center',
   
  },
  {
    title: '同步状态',
    dataIndex: 'syncStatus',
    width: '120px',
    align: 'center',
   
  },
  {
    title: '同步进度',
    dataIndex: 'synchronizeProgress',
    width: '200px',
    align: 'center',
   
  },
  {
    title: '同步数量',
    dataIndex: 'syncCursor',
    width: '100px',
    align: 'center',
   
  },
  {
    title: '聚合状态',
    dataIndex: 'mappingStatus',
    width: '120px',
    align: 'center',
   
  },
  {
    title: '聚合进度',
    dataIndex: 'qxProgress',
    width: '200px',
    align: 'center',
    
  },
  {
    title: '确认时间',
    dataIndex: 'confirmTime',
    width: '150px',
    align: 'center',
    
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '300px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<any>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(hotelApi.hotelSyncPage, {
  manual: false
});

const {
  data: exportListData,
  run: exportListApiRun,
  loading: exportListLoading,
} = useRequest(hotelApi.hotelSyncPage);

const reset = () => {
  searchParam.value = { }
  listApiRun({
    ...searchParam.value,
    pageNum: current.value,
    pageSize: pageSize.value,
  });
}

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  const obj = JSON.parse(JSON.stringify(searchParam.value))
  if(obj.startTimeRange&&obj.startTimeRange.length){
    obj.startTimeRange[0] = obj.startTimeRange[0] + " 00:00:00"
    obj.startTimeRange[1] = obj.startTimeRange[1] + " 23:59:59"
  }
  listApiRun({
    ...obj,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};


const confirmHotelSync=(row:any)=>{
  hModal.confirm({
      title:"确定要确认完成吗？",
      icon: createVNode(ExclamationCircleOutlined),
      onOk() {
        return new Promise((resolve, reject) => {
          hotelApi.confirmHotelSync({hotelSyncId:row.id}).then((res:any)=>{
            message.success('确认完成成功')
            listApiRun({
              ...searchParam.value,
              pageNum: current.value,
              pageSize: pageSize.value,
            });
            resolve()
          })
          .catch(()=>{
            resolve()
          })
        })
      },
      onCancel() {
        console.log('Cancel');
      },
    });
}


const gotoSynchronizeRecords = (record: any) => {
  console.log(record)
  router.push({ path: "/hotelMaintenance/synchronizeRecords", query: { id: record.id} })
}


const gotoAggregateRecords= (record: any) => {
  // console.log(record)
  // router.push({ path: "/hotelMaintenance/aggregateRecords", query: { providerCode: record.providerCode} })
  router.push({ path: "/hotelMaintenance/aggregateDetails", query: { id: record.id}})

}


const advancedSearchVisible = ref(false)
const gotoAdvancedSearch = () => {
  if (advancedSearchVisible.value) {
    advancedSearchVisible.value = false
  } else {
    advancedSearchVisible.value = true
  }
}

</script>

<template>

  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">

    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="code">供应商：</label>
          </h-col>
          <h-col :span="4">
              <h-select
                ref="select"
                v-model:value="searchParam.providerCode"
                style="width:100%;"
                allow-clear
              >
              <h-select-option v-for="item in supplierTypeOptions" :value="item.value">{{item.label}}</h-select-option>
              </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="recordType">同步状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="recordType" v-model:value="searchParam.syncStatus" style="width: 100%" allow-clear>
              <h-select-option v-for="item in asyncStatusOptions" :value="item.value">{{item.label}}</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="recordType">聚合状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="recordType" v-model:value="searchParam.mappingStatus" style="width: 100%" allow-clear>
              <h-select-option v-for="item in aggregationStatusOptions" :value="item.value">{{item.label}}</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;">
            <label for="startTimeRange">开始时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="searchParam.startTimeRange" value-format="YYYY-MM-DD" style="width: 100%;" />
          </h-col>
        </h-row>
      
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            
            <!-- <h-button type="primary" style="margin-right: 10px" :loading="exportListLoading"
              @click="exportListApiRun(searchParam);">
              <UploadOutlined />
              导出
            </h-button> -->
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ x: 800 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
           
            <template v-if="column.dataIndex === 'synchronizeProgress'">
              <h-progress :size="[300, 20]" :percent="((record.syncCursor / record.syncTotal)*100).toFixed(1)" status="active" />
            </template>
            <template v-if="column.dataIndex === 'qxProgress'">
              <h-progress :size="[300, 20]" :percent="((record.mappingCursor / record.syncTotal)*100).toFixed(1)" status="active" />
            </template>
            <template v-if="column.dataIndex === 'syncStatus'">
              <a-tag v-if="record.syncStatus==20" color="green">{{asyncStatus[record.syncStatus]}}</a-tag>
              <a-tag v-if="record.syncStatus==999" color="red">{{asyncStatus[record.syncStatus]}}</a-tag>
              <a-tag v-if="record.syncStatus==0" color="orange">{{asyncStatus[record.syncStatus]}}</a-tag>
              <a-tag v-if="record.syncStatus==10" color="blue">{{asyncStatus[record.syncStatus]}}</a-tag>
            </template>
            <template v-if="column.dataIndex === 'mappingStatus'">
              <a-tag v-if="record.mappingStatus==30" color="green">{{aggregationStatus[record.mappingStatus]}}</a-tag>
              <a-tag v-if="record.mappingStatus==20" color="purple">{{aggregationStatus[record.mappingStatus]}}</a-tag>
              <a-tag v-if="record.mappingStatus==999" color="red">{{aggregationStatus[record.mappingStatus]}}</a-tag>
              <a-tag v-if="record.mappingStatus==0" color="orange">{{aggregationStatus[record.mappingStatus]}}</a-tag>
              <a-tag v-if="record.mappingStatus==10" color="blue">{{aggregationStatus[record.mappingStatus]}}</a-tag>
              <a-tag v-if="record.mappingStatus==40" color="cyan">{{aggregationStatus[record.mappingStatus]}}</a-tag>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button v-if="record.mappingStatus==20&&!record.confirmTime" @click="confirmHotelSync(record)" type="link">确认完成</h-button>
              <h-button @click="gotoSynchronizeRecords(record)" type="link">同步明细</h-button>
              <h-button @click="gotoAggregateRecords(record)" type="link">聚合记录</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
:deep(.ant-progress-line) {
  width: 82%;
}
:deep(.ant-progress.ant-progress-status-active .ant-progress-bg::before) {
  display: none;
}
</style>
