<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import { banquetApplyApi } from '@haierbusiness-front/apis';
import {
  BApplyListRecord,
  BApplyPerson,
  BanquetStatusEnum
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';

import router from '../../router'
// const router = useRouter()
const store = applicationStore();

const currentRouter = ref()
const route = ref(getCurrentRoute());
const id = route.value?.query?.id;
const detail = ref<BApplyListRecord>();

const { loginUser } = storeToRefs(store);

const getDetail = (id: number) => {

  banquetApplyApi.get(id).then((res) => {
    detail.value = res;
  });
};

const showMore = ref(false)


onMounted(async () => {
  currentRouter.value = await router
})

const getInnerPerson = (list?:Array<BApplyPerson>) => {
  if (list && list.length > 0) {
   let resultList = list.filter(item => item.haierUser == true)
   return resultList.map(item => `${item.userName}(${item.userCode})`).join(',')
  }else {
     return ''
  }

}

const getOuterPerson = (list?:Array<BApplyPerson>) => {
  if (list && list.length > 0) {
    let resultList = list.filter(item => !item.haierUser)
    return resultList.map(item => item.userName).join(',')
  }else {
    return ''
  }
}

const downloadFile2 = (url:string, name:string) => {
      var xhr = new XMLHttpRequest();
      xhr.open('GET', url, true);
      xhr.responseType = 'blob';

      xhr.onload = function() {
        if (xhr.status === 200) {
          var blob = xhr.response;
          var a = document.createElement('a');
          var url = URL.createObjectURL(blob);
          a.href = url;
          a.download = name;
          a.click();
          URL.revokeObjectURL(url);
        }
      };

      xhr.send();
    }

const downLoadFile = (url:string, name:string) => {
  window.open(url)
  // downloadFile2(url,name)
}

watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);




</script>

<template>
  <div>
    <h-row justify="space-between" style="padding: 20px;">
      <h-col style="font-size: 18px;">申请单详情</h-col>
      <h-col><h-button type="link" @click="currentRouter.back(-1)">返回</h-button></h-col>
    </h-row>
    <div style="background-color: #ffff;height: 100%;width: 100%;padding: 30px 60px;overflow: auto;">
      <h-descriptions title="申请单信息" style="margin-bottom: 20px;" bordered>
        <h-descriptions-item label="订单编号">{{ detail?.orderCode }}</h-descriptions-item>
        <h-descriptions-item label="经办人信息">{{ `${detail?.creatorName}(${detail?.creator})` }}</h-descriptions-item>
        <h-descriptions-item label="联系电话">{{ detail?.creatorPhone }}</h-descriptions-item>
        <h-descriptions-item label="订单类型">{{ detail?.sceneType == 1 ? '宴请' : '外卖' }}</h-descriptions-item>
        <h-descriptions-item label="订单状态">{{
          BanquetStatusEnum[detail?.orderStatus] || ''
        }}</h-descriptions-item>
        <h-descriptions-item label="申请时间">{{ detail?.applicationTime }}</h-descriptions-item>
        <h-descriptions-item label="签单人信息">{{`${detail?.signerName}(${detail?.signerCode})`}}</h-descriptions-item>
        <h-descriptions-item label="就餐城市">{{ detail?.mealLocationCity }}</h-descriptions-item>
        <h-descriptions-item label="餐厅名称">{{ detail?.restaurantName }}</h-descriptions-item>
        <h-descriptions-item label="预计就餐时间">{{ detail?.estimatedMealTimeEnd }}</h-descriptions-item>

        <h-descriptions-item label="附件信息" :span="2"><div style="color: #2870ff; cursor: pointer;" @click="downLoadFile(detail?.fileUrl, detail?.fileName)">{{ detail?.fileName }}</div></h-descriptions-item>
        <h-descriptions-item label="申请事由" :span="3">{{ detail?.banquetReason }}</h-descriptions-item>
        <h-descriptions-item label="备注信息" :span="3">{{ detail?.remark }}</h-descriptions-item>
      </h-descriptions>

      <h-descriptions title="预算消费信息" style="margin-bottom: 20px;" bordered>
        <h-descriptions-item label="预算申请金额">{{ `${detail?.budgetAmount}元` }}</h-descriptions-item>
        <h-descriptions-item label="实际消费金额">{{ `${detail?.budgetAmount}元` }}</h-descriptions-item>
        <h-descriptions-item label="预算剩余金额">{{ `${detail?.budgetAmount}元` }}</h-descriptions-item>
        <h-descriptions-item label="释放金额">{{ `${detail?.budgetAmount}元` }}</h-descriptions-item>
        <h-descriptions-item label="预算支付单号">{{`${detail?.signerName}(${detail?.signerCode})`}}</h-descriptions-item>
        <h-descriptions-item label="预算系统">{{ detail?.mealLocationCity }}</h-descriptions-item>
        <h-descriptions-item label="费用科目">{{ detail?.restaurantName }}</h-descriptions-item>
        <h-descriptions-item label="预算部门">{{ detail?.restaurantName }}</h-descriptions-item>
      </h-descriptions>


      <h-descriptions title="就餐人信息" style="margin-bottom: 20px;" bordered>
        <h-descriptions-item label="就餐人数" :span='3'>{{ `${detail?.persons?.length}人` }}</h-descriptions-item>
        <h-descriptions-item label="内部就餐人" :span='3'>{{ getInnerPerson(detail?.persons) }}</h-descriptions-item>
        <h-descriptions-item label="外部就餐人" :span='3'>{{ getOuterPerson(detail?.persons) }}</h-descriptions-item>
      </h-descriptions>

      <h-descriptions title="子订单信息" style="margin-bottom: 20px;" bordered>
        <template #extra>
          <h-button type="link"  @click="showMore = !showMore">
            <template #icon>
              <UpOutlined v-if="showMore"/>

              <DownOutlined v-else />
            </template>
            {{ showMore ? '收起' : '全部订单' }}
          </h-button>
        </template>
        <h-descriptions-item label="预订单号">{{ detail?.orderCode }}</h-descriptions-item>
        <h-descriptions-item label="就餐时间">{{ `${detail?.creatorName}(${detail?.creator})` }}</h-descriptions-item>
        <h-descriptions-item label="餐费金额">{{ detail?.creatorPhone }}</h-descriptions-item>
        <h-descriptions-item label="餐厅名称">{{ detail?.applicationTime }}</h-descriptions-item>
        <h-descriptions-item label="就餐人数">{{`${detail?.signerName}(${detail?.signerCode})`}}</h-descriptions-item>
        <h-descriptions-item label="水票附件查看">{{ detail?.mealLocationCity }}</h-descriptions-item>
      </h-descriptions>

      <h-descriptions title="审批信息" style="margin-bottom: 20px;" bordered>
        <h-descriptions-item label="审批人">{{ detail?.orderCode }}</h-descriptions-item>
        <h-descriptions-item label="审批时间">{{ `${detail?.creatorName}(${detail?.creator})` }}</h-descriptions-item>
        <h-descriptions-item label="审批结果">{{ detail?.creatorPhone }}</h-descriptions-item>
        <h-descriptions-item label="审批意见" :span='3'>{{ detail?.applicationTime }}</h-descriptions-item>
        <h-descriptions-item label="审批人">{{ detail?.mealLocationCity }}</h-descriptions-item>
        <h-descriptions-item label="审批时间">{{ detail?.mealLocationCity }}</h-descriptions-item>
        <h-descriptions-item label="审批结果">{{ detail?.mealLocationCity }}</h-descriptions-item>
        <h-descriptions-item label="审批意见" :span="3">{{ detail?.mealLocationCity }}</h-descriptions-item>
      </h-descriptions>

      
    </div>


  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}
:deep(.ant-descriptions-item-label) {
  width: 200px;
}
:deep(.ant-descriptions-item-content) {
  width: 300px;
}
</style>
