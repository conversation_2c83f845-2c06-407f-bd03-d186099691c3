<script setup lang="ts">
import { SearchOutlined, DownloadOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { userApi } from '@haierbusiness-front/apis';
import {
  Button as hButton,
  Col as hCol,
  Input as hInput,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Form as hForm,
  FormItem as hFormItem,
  Modal as hModal,
  DatePicker as hDatePicker,
  Upload as hUpload,
  Textarea as hTextarea,
  message,
} from 'ant-design-vue';
import { computed, ref } from 'vue';
import { usePagination } from 'vue-request';
import { ColumnType } from 'ant-design-vue/es/table';
type MyColumn = ColumnType & {
  children?: ColumnType[];
};

const { data, run: userApiRun, loading, current, pageSize } = usePagination(userApi.list);
const sorter = (a: any, b: any) => a.applicationForm.localeCompare(b.applicationForm);

/**
 * @表格相关
 * */

const tableProps = computed(() => ({
  rowKey: 'id',
  scroll: { x: 'max-content' },
  dataSource: data.value?.records || [],
  pagination: {
    showSizeChanger: true,
    showQuickJumper: true,
    total: data.value?.total,
    current: data.value?.pageNum,
    pageSize: data.value?.pageSize,
    style: { justifyContent: 'center' },
  },
  columns: [
    {
      title: '序号',
      dataIndex: 'index',
    },
    {
      title: '充值单号',
      dataIndex: 'id',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '类型',
      dataIndex: '类型',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '申请单位',
      dataIndex: '申请单位',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '申请时间',
      dataIndex: '申请时间',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '充值金额',
      dataIndex: '充值金额',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '客户额度',
      dataIndex: '客户额度',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '审核状态',
      dataIndex: '审核状态',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '订单状态',
      dataIndex: '订单状态',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '备注',
      dataIndex: '备注',
      sorter: (a: any, b: any) => sorter(a, b),
    },
  ],
}));

/**
 * @表单相关
 * */
type OrderFormData = {
  applicationForm: string; // 申请单号
  orderDown: string; // 申请单位
  orderPrint: string; // 结算公司
  orderState: string | null; // 订单状态
  payState: string | null; // paymentStatus
  fileList?: any[];
};

// 订单状态
const orderStateOptions = ref([
  { label: '已出库', value: '10' },
  { label: '未出库', value: '20' },
]);

const formData = ref<OrderFormData>({
  applicationForm: '', // 申请单号
  orderDown: '', // 申请单位
  orderPrint: '', // 结算公司
  orderState: null, // 订单状态
  payState: null, // paymentStatus
});

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  userApiRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
    ...formData.value,
  });
};

const handleTableExport = () => {
  message.info('暂无接口');
  console.log(formData.value, ' -- formData');
};

/**
 * 弹窗相关
 * */
const addModal = ref(false);
const formModalData = ref<any>({
  applicationForm: '', // 申请单号
  orderDown: '', // 申请单位
  orderPrint: '', // 结算公司
  orderState: null, // 订单状态
  payState: null, // paymentStatus
  fileList: [],
});
const handleOpenAddModal = () => {
  addModal.value = true;
};
const handleCloseAddModal = () => {
  addModal.value = false;
};
const handleAddModalOk = () => {
  console.log(formData.value, ' -- formData');
  addModal.value = false;
};

const uploadProps = ref({
  beforeUpload: (file: File) => {
    // 只能上传jpg/png文件，且不超过500kb
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只能上传jpg/png文件');
    }
    const isLt5M = file.size / 1024 < 500;
    if (!isLt5M) {
      message.error('文件不能超过500kb');
    }
    return isJpgOrPng && isLt5M;
  },
  fileList: formModalData.value.fileList,
  customRequest: (options: any) => {
    console.log(options, ' -- options');
    const { file, onSuccess, onError } = options;
    const formData = new FormData();
    formData.append('file', file);
    console.log(formData, ' -- formData');
  },
});
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <!-- 新增弹窗 -->
    <h-modal v-model:visible="addModal" title="新增" @cancel="handleCloseAddModal" @ok="handleAddModalOk">
      <h-form :model="formModalData" :label-col="{ style: { width: '80px' } }">
        <h-form-item label="申请日期" name="date" :rules="[{ required: true, message: '请选择申请日期' }]">
          <h-date-picker style="width: 100%" v-model:value="formModalData.date" />
        </h-form-item>
        <h-form-item label="类型" name="type" :rules="[{ required: true, message: '请选择类型' }]">
          <h-select id="type" v-model="formModalData.type" placeholder="请选择" autocomplete="off" style="width: 100%">
            <h-select-option value="10">预充值</h-select-option>
            <h-select-option value="20">综合</h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="申请单位" name="orderDown" :rules="[{ required: true, message: '请输入申请单位' }]">
          <h-input placeholder="请输入" v-model:value="formModalData.orderDown" />
        </h-form-item>
        <h-form-item label="联系电话" name="orderPrint" :rules="[{ required: true, message: '请输入联系电话' }]">
          <h-input placeholder="请输入" v-model:value="formModalData.orderPrint" />
        </h-form-item>
        <h-form-item label="充值金额" name="rechargeAmount" :rules="[{ required: true, message: '请输入充值金额' }]">
          <h-input placeholder="请输入" v-model:value="formModalData.rechargeAmount" />
        </h-form-item>
        <h-form-item label="额度" name="quota" :rules="[{ required: true, message: '请输入额度' }]">
          <h-input placeholder="请输入" v-model:value="formModalData.quota" />
        </h-form-item>
        <h-form-item label="备注" name="remark">
          <h-textarea placeholder="请输入" :show-count="true" :maxlength="100" v-model:value="formModalData.remark" />
        </h-form-item>
        <h-form-item label="见证性资料" name="fileList" style="margin-top: -24px">
          <h-upload v-bind="uploadProps">
            <h-button type="primary"> <upload-outlined /> 点击上传 </h-button>
            <div class="upload-tips">只能上传jpg/png文件，且不超过500kb</div>
          </h-upload>
        </h-form-item>
      </h-form>
    </h-modal>
    <!-- 页面主体 -->
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px" :gutter="[0, 10]">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="applicationForm">充值单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="applicationForm"
              v-model:value="formData.applicationForm"
              placeholder="请输入"
              autocomplete="off"
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="orderState">类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              id="orderState"
              v-model="formData.orderState"
              placeholder="请选择"
              autocomplete="off"
              style="width: 100%"
            >
              <h-select-option value="10">预充值</h-select-option>
              <h-select-option value="20">综合</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="applicationForm">申请单位：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="applicationForm"
              v-model:value="formData.applicationForm"
              placeholder="请输入"
              autocomplete="off"
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="orderState">订单状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              id="orderState"
              v-model="formData.orderState"
              placeholder="请选择"
              autocomplete="off"
              style="width: 100%"
            >
              <h-select-option value="10">待重置</h-select-option>
              <h-select-option value="20">已充值</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="orderState">审核状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              id="orderState"
              v-model="formData.orderState"
              placeholder="请选择"
              autocomplete="off"
              style="width: 100%"
            >
              <h-select-option value="10">待审核</h-select-option>
              <h-select-option value="20">已审核</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined /> 查询
            </h-button>
            <h-button type="primary" @click="handleOpenAddModal()" style="margin-left: 10px">
              <PlusOutlined /> 新增
            </h-button>
            <h-button style="margin-left: 10px" @click="handleTableExport"> <DownloadOutlined /> 导出 </h-button>
            <!-- <h-button style="margin-left: 10px" @click="handleTableReset">重置</h-button> -->
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table v-bind="tableProps" :loading="loading" size="middle" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }"> </template>
        </h-table>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.upload-tips {
  color: #999;
  font-size: 12px;
  margin: 6px 0 0 0px;
}
</style>
