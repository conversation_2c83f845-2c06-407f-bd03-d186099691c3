<template>
  <div class="whole-line block-con">
    <div class="title whole-line">基本信息</div>
    <h-form
      class="mt-30"
      ref="formRef"
      :model="props.creatTripParma"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      :disabled="props.isDetail"
      labelAlign="left"
    >
      <h-form-item ref="name" name="travelUserName">
        <template #label>
          <span
            >出差人</span>
        </template>
        <user-select
          :value="`${chosedPerson.travelUserName || '未知'}/${chosedPerson.travelUserNo || '未知工号'}[${
            chosedPerson.travelUserDeptName || '未知部门'
          }]`"
          placeholder="选择出差人"
          :params="params"
          :disabled="props.isChange || props.isDetail"
          @change="(userInfo: IUserInfo) =>  userNameChange(userInfo)"
          class="whole-line font-size-14"
        />
        <div class="flex primary-color pointer left right" @click="showStandardOpen">差旅标准</div>
      </h-form-item>

      <h-form-item name="persons">
        <template #label>
          <span
            >内部出行人</span>
        </template>
          <user-select2
            ref="userSelectRef"
            :hideCode="chosedPerson.travelUserNo"
            :disabled=" props.isChange || props.isDetail"
            :value="nickName"
            :multiple="true"
            :params="params"
            placeholder="选择内部出行人"
            @change="(userInfo: IUserInfo) => userNameMultipleChange(userInfo)"
          />
      </h-form-item>

      <h-form-item :wrapperCol="{ span: 20,offset:3 }" name="warning" v-if="showPersonWarning()">
        <a-alert  message="多同行人必须所有人确认后才能报销" type="warning" show-icon style="margin-bottom: 10px;" />
      </h-form-item>

      <h-form-item ref="outperson" name="outPersonId">
        <template #label>
          <span
            >外部出行人</span>
        </template>

        <a-select
          v-model:value="props.creatTripParma.outPersonId"
          :disabled="props.isChange || props.isDetail"
          mode="multiple"
          :show-search="false"
          style="width: 100%"
          placeholder="选择外部出行人,外部人员不能通过集团系统报销"
          :options="outPersonList"
          :max-tag-count="8"
          :fieldNames="{ label: 'travelUserName', value: 'travelUserSyId' }"
          @change="handleChange"
          class="font-size-14"
          @focus="remindUser"
        >
          <template #tagRender="{ value: val, label, closable, onClose, option }">
            <a-tooltip>
              <template #title>{{ label }}</template>
              <a-tag class="ant-select-selection-item" :closable="closable" style="margin-right: 3px" @close="onClose">
                {{ `${label}[外部人员]` }}
              </a-tag>
            </a-tooltip>
          </template>
          <template #maxTagPlaceholder="omittedValues">
            <a-tooltip>
              <template #title>
                <div v-for="(item, index) in omittedValues" :key="index">
                  {{ item.label }}
                </div>
              </template>
              <div class="tag">
                <span class="font-size-14 font-color">+ {{ omittedValues.length }}... </span>
              </div>
            </a-tooltip>
          </template>
        </a-select>
        <!-- <users-select
          :value="baseInfo.outPersonId"
          placeholder="请选择外部出行人"
          :params="params"
          @change="(userInfo: Array<string>, chosedList: Array<ITraveler>) =>  outpersonChange(userInfo, chosedList)"
          class="whole-line"
        /> -->
        <div v-if="!props.isDetail && !props.isChange" class="flex primary-color pointer right" @click="outPersonOpen">新增外部出行人</div>
      </h-form-item>

      <h-form-item name="travelReserveFlag">
        <template #label>
          <span
            >预订方式<a-tooltip>
              <template #title>差旅资源集团内统一集采，大资源换大资源，实现差旅价值最大化，无必要原因，差旅费应100%商旅平台预订。非商旅预订：本次出差交通工具及酒店全部自费预订，事后报销。</template>
              <question-circleOutlined class="icon" /> </a-tooltip
          ></span>
        </template>
        <h-radio-group :disabled="props.isChange || props.isDetail" v-model:value="props.creatTripParma.travelReserveFlag">
          <h-radio :value="1">经商旅预订</h-radio>
          <h-radio :value="0">自费垫付</h-radio>
        </h-radio-group>
      </h-form-item>

      <!-- <h-form-item name="unbookedReasonId" v-if="!props.creatTripParma.travelReserveFlag">
        <template #label>
          <span
            >未预订原因<a-tooltip>
              <template #title>没经过商旅预订的原因</template>
              <question-circleOutlined class="icon" /> </a-tooltip
          ></span>
        </template>
        
        <h-select placeholder="选择未预订原因" v-model:value="props.creatTripParma.unbookedReasonId" :options="resonList"  :fieldNames="{ label: 'reasonInfo', value: 'id' }">
        </h-select>
        
      </h-form-item> -->

      <h-form-item name="travelReason">
        <template #label>
          <span
            >出差事由<a-tooltip>
              <template #title>事前明确投入产出，明确项目目标、路径，用于说明出差的必要性和合理性</template>
              <question-circleOutlined class="icon" /> </a-tooltip
          ></span>
        </template>
        <h-textarea
          class="my-textarea"
          v-model:value="props.creatTripParma.travelReason"
          placeholder="请输入出差事由"
          allow-clear
          :show-count="true"
          :auto-size="{ minRows: 1, maxRows: 5 }"
          :maxlength="500"
        />
      </h-form-item>
      <h-form-item name="changeReason" v-if="props.isChange || props.chosedNow == 'changeing'">
        <template #label>
          <span
            >变更原因<a-tooltip>
              <template #title>变更原因</template>
              <question-circleOutlined class="icon" /> </a-tooltip
          ></span>
        </template>
        <h-textarea
          v-model:value="props.creatTripParma.changeReason"
          placeholder="请输入出差单变更原因"
          allow-clear
          :show-count="true"
          :auto-size="{ minRows: 1, maxRows: 5 }"
          :maxlength="200"
        />
      </h-form-item>
    </h-form>
  </div>
</template>

<script lang="ts" setup>
import { createVNode, onMounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import { QuestionCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import {
  Anchor as hAnchor,
  Button as hButton,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  Row as hRow,
  Col as hCol,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal,
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import {
  IUserListRequest,
  ICreatTrip,
  ITraveler,
  IUserInfo,
  ICity,
  IPerson,
  ITripInfo,
  ITripDetailMap,
} from '@haierbusiness-front/common-libs';
import { reasonApi, tripApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import UserSelect2 from '@haierbusiness-front/components/user/UserSelect.vue';

import UserSelect from './UserSelect.vue';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { difference } from 'lodash-es';

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const formRef = ref();
const labelCol = { span: 3 };
const wrapperCol = { span: 20 };


interface Props {
  creatTripParma?: any; // 人员
  isDetail?: boolean;
  isChange?: boolean;
  chosedNow?: string;
}

const baseInfo = ref<ITripInfo>({
  inPerson: [],
  name: undefined, // 出差人名称
  outPerson: [],
  outPersonId: [],
});

// const { creatTripParma, props.isDetail, props.isChange } = defineProps<Props>();
const props = defineProps<Props>();

// 表单验证
const onSubmit = () => {
  return formRef.value.validate();
};

const resonList= ref<Array<object>>([])

// 获取未商旅预定原因
const getResonList = () => {
  reasonApi.list({pageNum: 1,
    reasonType:10,
    pageSize: 50,}).then(res => {
      resonList.value=res.records
    })
}

onMounted(() => {
  getOutPersonList()
  getResonList()
})

const showPersonWarning = () => {
  const innerPersonList = props.creatTripParma.travelerList?.filter(item => item.travelUserType == '0')
  return innerPersonList?.length > 1
}

// 外部出行人信息
const outPersonList = ref<Array<ITraveler>>([]);

// 获取外部出行人
const getOutPersonList = () => {
  const params = {
    size: 999,
    current: 1,
    sfmrss: 0
  }
   tripApi.getOutPersonList(params).then(res => {
     res?.trainExternalPersonnelVos?.forEach(item => {
      item.travelUserSyId = item.ygid
      item.travelUserName = item.lkxm || item.ywxm
      item.travelUserDeptName = item.qybh
      item.travelUserDeptId = item.qybh
     })
     outPersonList.value = res?.trainExternalPersonnelVos || []
   })
}

watch(
  () => props.creatTripParma?.travelerList, 
  (newValue) => {
    if (outPersonList.value.length == 0 && (props.isDetail || props.isChange)) {
      outPersonList.value = props.creatTripParma.travelerList
    }
  },
  {
    deep: true
  }
)

// 已经选择的外部出行人
const chosedOutPersenList = ref<Array<ITraveler>>([]);

const allowBugde = ref<Array<ITripDetailMap>>([]);

const handleChange = (value: Array<string>) => {
  chosedOutPersenList.value = [];
  outPersonList.value.forEach((i) => {
    value.forEach((jj) => {
      if (i.travelUserSyId == jj) {
        i.travelUserType = '1';
        chosedOutPersenList.value = [...chosedOutPersenList.value, i];
      }
    });
  });
  props.creatTripParma.travelerList = [chosedPerson.value, ...chosedOutPersenList.value, ...chosedInPersenList.value];


  // 移除被删掉的外部出行人相关的明细
  props.creatTripParma?.tripList?.map((trip) => {
    let newDetail: Array<ITripDetailMap> = [];
    trip.tripDetailMapList?.map((detail) => {
      let travelerIds: string[] = [];
      props.creatTripParma.travelerList?.map((item) => {
        travelerIds.push(item.travelUserSyId ?? '');
      });
      const differences = difference(detail.personIdList, travelerIds);
      if (differences.length == 0) {
        newDetail.push(detail);
      }
    });
    trip.tripDetailMapList = newDetail;
  });
  // 重新计算费用预算合计
  allowBugde.value = [];
  props.creatTripParma.amountSum = 0;
  // 所有费用
  props.creatTripParma.tripList.forEach((item) => {
    allowBugde.value = [...allowBugde.value, ...(item.tripDetailMapList || [])];
  });
  allowBugde.value.forEach((item) => {
    if(item.insuranceFlag && item.insuranceAmount) {
      props.creatTripParma.amountSum += item.insuranceAmount * item.personIdList?.length
    }
    props.creatTripParma.amountSum += item.budgetAmount ?? 0;
  });
};

const isKnown = ref(false);

const remindUser = () => {
  // 因为外部人是多选不能时时提醒，因此做统一提醒，后续修改直接清空相关的费用项目
  if (!isKnown.value && chosedOutPersenList.value.length > 0) {
    Modal.confirm({
      title: '移除外部出行人会清空与之相关的费用明细，请知悉！',
      icon: createVNode(ExclamationCircleOutlined),
      onOk() {
        isKnown.value = true;
      },
      onCancel() {
        isKnown.value = true;
      },
    });
  }
};

const rules: Record<string, Rule[]> = {
  changeReason: [{ required: true, message: '请输入变更原因', trigger: 'blur' }],
  travelUserName: [{ required: true, message: '请选择出差人', trigger: 'change' }],
  travelReserveFlag: [{ required: true, message: '请选择是否为商旅预订', trigger: 'change' }],
  travelReason: [{ required: true, message: '请输入出差事由', trigger: 'blur' }],
  unbookedReasonId: [{ required: true, message: '请选择未经商旅预订原因', trigger: 'change' }],
};

// end form


const emit = defineEmits(['change', 'outPersonOpen', 'showStandardOpen']);
const outPersonOpen = () => {
  emit('outPersonOpen');
};
const showStandardOpen = () => {
  emit('showStandardOpen');
};

// 用户选择
const params = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});

const chosedPerson = ref<ITraveler>({});
// 根据登陆人初始化数据
chosedPerson.value = props.creatTripParma?.travelerList?.filter((item) => {
  return item.travelUserType == '0';
})[0];

// 出差人
const userNameChange = (userInfo: IUserInfo) => {
  // 如果更换出差人，需要检查行程中是否存在费用明细
  // 如果存在，需要提示用户，用户确认后从费用明细中将已经更换的出差人去掉
  if (chosedPerson.value.travelUserSyId != userInfo.username) {
    Modal.confirm({
      title: '更换出差人会删除与之相关的费用明细，是否继续?',
      icon: createVNode(ExclamationCircleOutlined),
      onOk() {
        props.creatTripParma?.tripList?.map((trip) => {
          let newDetail: Array<ITripDetailMap> = [];
          trip.tripDetailMapList?.map((detail) => {
            if (!detail.personIdList?.includes(chosedPerson.value.travelUserSyId ?? '')) {
              newDetail.push(detail);
            }
          });
          trip.tripDetailMapList = newDetail;
        });
        chosedPerson.value = userInfo;
        chosedPerson.value.travelUserNo = userInfo.username;
        chosedPerson.value.travelUserDeptName = userInfo.enterpriseName;
        chosedPerson.value.travelUserDeptId = userInfo.departmentCode;
        chosedPerson.value.travelUserSyId = userInfo.username;
        chosedPerson.value.travelUserName = userInfo.nickName;
        chosedPerson.value.travelUserType = '0';
        chosedPerson.value.mainFlag = '1';
        chosedPerson.value.id = '';

        props.creatTripParma.travelUserName = userInfo?.nickName ?? undefined;

        props.creatTripParma.travelerList = [chosedPerson.value, ...chosedOutPersenList.value, ...chosedInPersenList.value];
      },
      onCancel() {},
    });
  }

  // 
  // baseInfo.value.name = userInfo?.nickName ?? undefined;
};



// 已经选择的内部同行人
const chosedInPersenList = ref<Array<ITraveler>>([]);
// 多选内部出差人
const nickName = ref<Array<string>>([]);
const userNameMultipleChange = (userInfo: Array<IUserInfo>) => {
  if (!userInfo.length) {
    chosedInPersenList.value = []
  }

  const array: Array<string> = [];
  chosedInPersenList.value = []
  userInfo.forEach((item: any, index: number) => {
    if (item.nickName && item.username) {
      array.push(item.nickName + '/' + item.username);
      chosedInPersenList.value?.push(
        {
          travelUserNo: item.username,
          travelUserDeptName: item.enterpriseName,
          travelUserDeptId: item.departmentCode,
          travelUserSyId: item.username,
          travelUserName: item.nickName,
          travelUserType: '0',
          id: ''
        }
      )
    } else {
      array.push(
        nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[0] +
        '/' +
        nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[1],
      );
      chosedInPersenList.value?.push(
        {
          travelUserSyId: nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[1],
          travelUserNo: nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[1],
          travelUserName: nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[0],
          travelUserType:'0'
        }
      )
    }
  })
  props.creatTripParma.travelerList = [chosedPerson.value, ...chosedOutPersenList.value, ...chosedInPersenList.value];


  // 移除被删掉的内部出行人相关的明细
  props.creatTripParma?.tripList?.map((trip) => {
      let newDetail: Array<ITripDetailMap> = [];
      trip.tripDetailMapList?.map((detail) => {
        let travelerIds: string[] = [];
        props.creatTripParma.travelerList?.map((item) => {
          travelerIds.push(item.travelUserSyId ?? '');
        });
        const differences = difference(detail.personIdList, travelerIds);
        if (differences.length == 0) {
          newDetail.push(detail);
        }
      });
      trip.tripDetailMapList = newDetail;
    });
     // 重新计算费用预算合计
    allowBugde.value = [];
    props.creatTripParma.amountSum = 0;
    // 所有费用
    props.creatTripParma.tripList.forEach((item) => {
      allowBugde.value = [...allowBugde.value, ...(item.tripDetailMapList || [])];
    });
    allowBugde.value.forEach((item) => {
      if(item.insuranceFlag && item.insuranceAmount) {
      props.creatTripParma.amountSum += item.insuranceAmount * item.personIdList?.length
    }
      props.creatTripParma.amountSum += item.budgetAmount ?? 0;
    });
  nickName.value = array;

}
const userSelectRef = ref()

watch(
  () => chosedInPersenList.value, 
  (newValue) => {
    const firstData:any[] = []
    nickName.value = newValue?.map(item => {

      firstData.push(item.travelUserNo)

      return item.travelUserName + '/' + item.travelUserNo
   })
   if (firstData.length > 0) {
      userSelectRef.value.setFirstData(firstData)
    }


})

defineExpose({
  onSubmit,
  chosedPerson,
  chosedInPersenList,
  getOutPersonList
});
</script>

<style scoped>
@import url('./trip.less');

.right {
  position: absolute;
  right: -110px;
  top: 6px;
}
.left {
  right: -70px;
}
.mt-50 {
  margin-top: 50px;
}
.my-textarea :deep(.ant-input){
   padding-right: 20px;
}
</style>