<script setup lang="ts">
import {
  Modal,
  Tag as hTag,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Avatar as hAvatar,
  Popover as hPopover,
  Row as hRow,
  Table as hTable,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import {
  EyeOutlined,
  FormOutlined,
  PlusOutlined,
  SearchOutlined,
  SaveOutlined,
  SubnodeOutlined,
  SafetyOutlined,
  FundOutlined,
  HistoryOutlined,
  FieldTimeOutlined,
} from '@ant-design/icons-vue';
import {
  AnnualPlanStateConstant,
  AnnualPlanTypeStateConstant,
  EvaluateTypeConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
  UserGroupSystemConstant,
} from '@haierbusiness-front/common-libs';
import { checkUserGroup, getCurrentRouter, routerParam } from '@haierbusiness-front/utils';
import { ref, createVNode, PropType, watch, computed } from 'vue';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import {
  IAnnualPlanTypeListRequest,
  IMonthPlanListResponseDTO,
  MonthPlanStateConstant,
} from '@haierbusiness-front/common-libs/src/daily';
import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import VChart from 'vue-echarts';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { BarChart, PieChart, LineChart } from 'echarts/charts';
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
const { loginUser } = storeToRefs(applicationStore(globalPinia));
const router = getCurrentRouter();
use([CanvasRenderer, PieChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent, BarChart, LineChart]);
const prop = defineProps({
  data: Object as PropType<IMonthPlanListResponseDTO>,
  // 模式, 1: 小微模式 . 2: 平台主模式(管理员或平台主)
  type: Number,
});
watch(
  () => prop.data,
  (val: any) => {
    annualListApiRun();
  },
  {
    deep: true,
  },
);
/**
 * 计算当前用户权限的权限bitmap值,当评价模式时使用
 */
const dailyGroupValue = computed(() => {
  let result = 0;
  if (checkUserGroup(UserGroupSystemConstant.DAILY_STRATEGY.groupId)) {
    result += 2;
  }
  if (checkUserGroup(UserGroupSystemConstant.DAILY_RISK_MANAGEMENT.groupId)) {
    result += 4;
  }
  if (checkUserGroup(UserGroupSystemConstant.DAILY_FINANCE.groupId)) {
    result += 8;
  }
  if (checkUserGroup(UserGroupSystemConstant.DAILY_HUMAN_RESOURCES.groupId)) {
    result += 16;
  }
  return result;
});

const gotoDetail = () => {
  router.push({
    path: '/daily/annual-plan/detail',
    query: { code: routerParam(prop?.data?.apCode), month: routerParam(prop?.data?.month) },
  });
};

const gotoAdjust = () => {
  router.push({
    path: '/daily/annual-plan/update',
    query: {
      id: routerParam(annualData.value.id),
      month: routerParam(prop?.data?.month),
      type: 'adjust',
      showTemp: true,
    },
  });
};

const gotoSummarize = () => {
  router.push({
    path: '/daily/annual-plan/update',
    query: {
      id: routerParam(annualData.value.id),
      month: routerParam(prop?.data?.month),
      type: 'summarize',
    },
  });
};

const gotoPlanformEvaluate = () => {
  router.push({
    path: '/daily/annual-plan/update',
    query: {
      id: routerParam(annualData.value.id),
      month: routerParam(prop?.data?.month),
      type: 'planform-evaluate',
      evaluateType: EvaluateTypeConstant.PLATFORM_MONTH.code,
    },
  });
};

const gotoEvaluate = (evaluateFlag: number) => {
  let evaluateType = 0;
  let evaluateGroupFlag = 0;
  if ((evaluateFlag & 2) === 0 && (dailyGroupValue.value & 2) === dailyGroupValue.value) {
    evaluateGroupFlag = 2;
    evaluateType = EvaluateTypeConstant.THREE_STRATEGY.code;
  } else if ((evaluateFlag & 4) === 0 && (dailyGroupValue.value & 4) === dailyGroupValue.value) {
    evaluateGroupFlag = 4;
    evaluateType = EvaluateTypeConstant.THREE_CONTROL.code;
  } else if ((evaluateFlag & 8) === 0 && (dailyGroupValue.value & 8) === dailyGroupValue.value) {
    evaluateGroupFlag = 8;
    evaluateType = EvaluateTypeConstant.THREE_FINANCE.code;
  } else if ((evaluateFlag & 16) === 0 && (dailyGroupValue.value & 16) === dailyGroupValue.value) {
    evaluateGroupFlag = 16;
    evaluateType = EvaluateTypeConstant.THREE_MANPOWER.code;
  }
  router.push({
    path: '/daily/annual-plan/update',
    query: {
      id: routerParam(annualData.value.id),
      month: routerParam(prop?.data?.month),
      type: 'evaluate',
      evaluateType: evaluateType,
      evaluateGroupFlag: evaluateGroupFlag,
    },
  });
};
const dataColor = {
  color: ['rgb(84,112,198)', 'rgb(250,200,88)', 'rgb(145,204,117)'],
};
// 基础数据
const itemCount = computed(() => prop?.data?.itemCount || 0);
const stopItemCount = computed(() => prop?.data?.stopItemCount || 0);
const completeItemCount = computed(() => prop?.data?.completeItemCount || 0);
const inProgress = computed(() => itemCount.value - stopItemCount.value - completeItemCount.value);
const typeItemCountArr = computed(() => {
  if (prop?.data?.typeItemCount) {
    const labelArr = [];
    // 已完成的
    const currentArr = [];
    // 所有的
    const valueArr = [];
    for (let typeItem of prop.data.typeItemCount.split(',')) {
      labelArr.push(typeItem.split(':')?.[0]);
      currentArr.push(parseInt(typeItem.split(':')?.[1].split('/')?.[0]));
      valueArr.push(parseInt(typeItem.split(':')?.[1].split('/')?.[1]));
    }
    return [labelArr, currentArr, valueArr];
  } else {
    return undefined;
  }
});

const annualListLoading = ref(false);
const annualData = ref<IAnnualPlanListResponseDTO>({});

const annualListApiRun = () => {
  annualListLoading.value = true;
  dailyAnnualPlanApi
    .list({
      year: prop?.data?.year,
      deptCode: prop?.data?.deptCode,
    })
    .then((it) => {
      annualData.value = it[0];
    })
    .finally(() => {
      annualListLoading.value = false;
    });
};
annualListApiRun();
const pieEchartsOption = ref({
  tooltip: {
    trigger: 'item',
  },
  ...dataColor,
  legend: {
    top: '25%',
    left: 'right',
    orient: 'verticalAlign',
  },
  series: [
    {
      left: 'left',
      center: ['25%', '50%'],
      name: 'Access From',
      type: 'pie',
      radius: ['50%', '80%'],
      label: {
        show: true,
        position: 'center',
        formatter: '0%',
        fontWeight: 'bold',
        fontSize: 16,
      },
      labelLine: {
        show: false,
      },
      data: [
        {
          value: 0,
          name: '进行中：0',
        },
        { value: 0, name: '已终止：0' },
        { value: 0, name: '已完成：0' },
      ],
    },
  ],
});
const barEchartsOption = ref({
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: (() => {
      const result = [];
      for (let i = 1; i <= dayjs().daysInMonth(); i++) {
        result.push(i);
      }
      return result;
    })(),
  },
  yAxis: {
    type: 'value',
  },
  series: [
    {
      data: (() => {
        const result = [];
        for (let i = 1; i <= dayjs().daysInMonth(); i++) {
          result.push(i + 2);
        }
        return result;
      })(),
      type: 'line',
      areaStyle: {},
    },
    {
      type: 'line',
      areaStyle: {},
      data: (() => {
        const result = [];
        for (let i = 1; i <= dayjs().daysInMonth(); i++) {
          result.push(i);
        }
        return result;
      })(),
    },
  ],
});

const subscriptColorStyle = computed(() => {
  if (MonthPlanStateConstant.WAIT.code === prop?.data?.state) {
    return { 'background-color': 'rgb(210,210,210)' };
  }
  if (MonthPlanStateConstant.RUNNING.code === prop?.data?.state) {
    return { 'background-color': 'rgb(0,115,229)' };
  }
  if (MonthPlanStateConstant.FINISH.code === prop?.data?.state) {
    return { 'background-color': 'rgb(82,196,26)' };
  }
  if (MonthPlanStateConstant.EVALUATE.code === prop?.data?.state) {
    return { 'background-color': 'rgb(250,173,20)' };
  }
  if (MonthPlanStateConstant.PLATFORM_EVALUATE.code === prop?.data?.state) {
    return { 'background-color': 'rgb(250,173,20)' };
  }
  if (MonthPlanStateConstant.COMPLETED.code === prop?.data?.state) {
    return { 'background-color': 'rgb(82,196,26)' };
  }
});
const updateChartsData = () => {
  // 设置pie图
  pieEchartsOption.value.series[0].data = [
    {
      value: inProgress.value,
      name: '进行中：' + inProgress.value,
    },
    { value: stopItemCount.value, name: '已终止：' + stopItemCount.value },
    { value: completeItemCount.value, name: '已完成：' + completeItemCount.value },
  ];
  pieEchartsOption.value.series[0].label.formatter =
    (itemCount.value === 0
      ? 0
      : ((stopItemCount.value + completeItemCount.value) / itemCount.value / 0.01).toFixed(2)) + '%';

  // 设置柱状图
  // barEchartsOption.value.xAxis.data = (typeItemCountArr.value?.[0] || ['', '', '', '']) as string[];
  // barEchartsOption.value.series[0].data = (typeItemCountArr.value?.[2] || [0, 0, 0, 0]) as number[];
};
{
  updateChartsData();
}

watch(() => prop.data, updateChartsData);

const showAction = ref(false);
const onMouseover = () => {
  showAction.value = true;
};
const onMouseout = () => {
  showAction.value = false;
};
const wait = () => {
  message.warning('开发中, 敬请期待!');
};
</script>

<template>
  <div class="card">
    <div class="card-mask" @mouseenter="onMouseover" @mouseleave="onMouseout">
      <div v-if="showAction" style="height: 100%">
        <div style="display: flex; align-items: center; flex-wrap: wrap; justify-content: center; height: 100%">
          <div v-if="type === 1">
            <div>
              <h-space>
                <h-button
                  v-if="
                    (annualData?.state === AnnualPlanStateConstant.WAIT_RUNNING.code ||
                      annualData?.state === AnnualPlanStateConstant.RUNNING.code) &&
                    (data?.state == MonthPlanStateConstant.WAIT.code ||
                      data?.state == MonthPlanStateConstant.RUNNING.code) &&
                    !annualData?.waitAnnualPlan
                  "
                  @click="gotoAdjust()"
                >
                  <template #icon>
                    <SubnodeOutlined />
                  </template>
                  追加
                </h-button>
                <template v-if="data?.state === MonthPlanStateConstant.EVALUATE.code">
                  <h-popover title="评价进度">
                    <template #content>
                      <h-space wrap :size="16" style="margin-bottom: 10px; margin: 5px">
                        <h-avatar size="large" :class="{ 'complete-e': ((data?.evaluateFlag || 0) & 1) != 0 }">
                          小微
                        </h-avatar>
                        <h-avatar size="large" :class="{ 'complete-e': ((data?.evaluateFlag || 0) & 2) != 0 }">
                          战略
                        </h-avatar>
                        <h-avatar size="large" :class="{ 'complete-e': ((data?.evaluateFlag || 0) & 4) != 0 }">
                          风控
                        </h-avatar>
                        <h-avatar size="large" :class="{ 'complete-e': ((data?.evaluateFlag || 0) & 8) != 0 }">
                          财务
                        </h-avatar>
                        <h-avatar size="large" :class="{ 'complete-e': ((data?.evaluateFlag || 0) & 16) != 0 }">
                          人力
                        </h-avatar>
                      </h-space>
                    </template>
                    <h-button style="border-radius: 100%">
                      <template #icon>
                        <HistoryOutlined />
                      </template>
                    </h-button>
                  </h-popover>
                </template>
                <h-button
                  v-if="data?.state === MonthPlanStateConstant.EVALUATE.code && ((data?.evaluateFlag || 0) & 1) !== 1"
                  @click="gotoSummarize"
                >
                  <template #icon>
                    <FundOutlined />
                  </template>
                  总结
                </h-button>
                <h-button @click="gotoDetail" type="primary">
                  <template #icon>
                    <EyeOutlined />
                  </template>
                  查看
                </h-button>
              </h-space>
            </div>
          </div>
          <div v-if="type === 2">
            <div style="margin-top: 10px">
              <h-space>
                <template v-if="data?.state === MonthPlanStateConstant.EVALUATE.code">
                  <h-popover title="评价进度">
                    <template #content>
                      <h-space wrap :size="16" style="margin-bottom: 10px; margin: 5px">
                        <h-avatar size="large" :class="{ 'complete-e': ((data?.evaluateFlag || 0) & 1) != 0 }">
                          小微
                        </h-avatar>
                        <h-avatar size="large" :class="{ 'complete-e': ((data?.evaluateFlag || 0) & 2) != 0 }">
                          战略
                        </h-avatar>
                        <h-avatar size="large" :class="{ 'complete-e': ((data?.evaluateFlag || 0) & 4) != 0 }">
                          风控
                        </h-avatar>
                        <h-avatar size="large" :class="{ 'complete-e': ((data?.evaluateFlag || 0) & 8) != 0 }">
                          财务
                        </h-avatar>
                        <h-avatar size="large" :class="{ 'complete-e': ((data?.evaluateFlag || 0) & 16) != 0 }">
                          人力
                        </h-avatar>
                      </h-space>
                    </template>
                    <h-button style="border-radius: 100%">
                      <template #icon>
                        <HistoryOutlined />
                      </template>
                    </h-button>
                  </h-popover>
                </template>
                <h-button
                  v-if="
                    data?.state === MonthPlanStateConstant.EVALUATE.code &&
                    dailyGroupValue !== 0 &&
                    ((data?.evaluateFlag || 0) & dailyGroupValue) !== dailyGroupValue
                  "
                  @click="gotoEvaluate(data?.evaluateFlag || 0)"
                >
                  <template #icon>
                    <FundOutlined />
                  </template>
                  评价(s)
                </h-button>
                <h-button
                  v-if="
                    data?.state === MonthPlanStateConstant.PLATFORM_EVALUATE.code &&
                    checkUserGroup(UserGroupSystemConstant.DAILY_PLATFORM_MANAGER.groupId)
                  "
                  @click="gotoPlanformEvaluate()"
                >
                  <template #icon>
                    <FundOutlined />
                  </template>
                  评价(p)
                </h-button>
                <h-button @click="gotoDetail" type="primary">
                  <template #icon>
                    <EyeOutlined />
                  </template>
                  查看
                </h-button>
              </h-space>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div :style="subscriptColorStyle" class="subscript">
        <div style="margin-top: 8%">{{ prop?.data?.stateName }}</div>
      </div>
      <div style="height: 50%; width: 90%">
        <v-chart :option="pieEchartsOption" :autoresize="true" />
      </div>
      <div style="height: 75%">
        <!-- <v-chart :option="barEchartsOption" :autoresize="true" style="position: relative; top: -13%" /> -->
        <div style="font-size: 16px; position: relative; top: 40px">
          日清提报天数：{{ prop?.data?.normalDays || 0 }}/{{ prop?.data?.totalDays || 0 }}
          <br />
          <br />
          日清提报人天：{{ prop?.data?.personNormalDays || 0 }}/{{ prop?.data?.personTotalDays || 0 }}
        </div>
      </div>
    </div>
    <div class="footer">
      <div v-if="type === 1">{{ prop?.data?.year }} 年 {{ prop?.data?.month }}月</div>
      <div v-if="type === 2">{{ prop?.data?.deptName }} -- {{ prop?.data?.year }} 年 {{ prop?.data?.month }}月</div>
    </div>
  </div>
</template>

<style scoped lang="less">
.complete-e {
  background-color: rgb(60, 118, 249);
}
.subscript {
  position: absolute;
  width: 23%;
  height: 8%;
  right: 2.5%;
  color: white;
  font-size: 12px;
  text-align: center;
  vertical-align: middle;
  border-bottom-left-radius: 5px;
  border-top-right-radius: 5px;
}

.card {
  box-shadow: 0 0 2px rgba(189, 250, 255, 0.7);
  border: 0.5px solid #eaeaea;
  border-radius: 5px;
  text-align: center;
  height: calc(37vh);
  min-height: 290px;
  width: 95%;
  margin: 10px auto;

  &:hover {
    box-shadow: 0 0 10px rgba(189, 250, 255, 0.7);
    border: 0.5px solid rgba(189, 250, 255, 0.7);
  }

  .content {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    height: 90%;
  }

  .footer {
    padding-top: 5px;
    text-align: center;
    font-weight: 700;
    font-size: 16px;
    border-top: 0.5px solid #eaeaea;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    background-color: rgb(247, 249, 250);
    height: 10%;
  }
}

.card-mask {
  border-radius: 5px;
  cursor: pointer;
  position: absolute;
  height: calc(37vh);
  min-height: 290px;
  width: 95%;
  z-index: 10;
  &:hover {
    background-color: #424242c8;
    transition: all 0.3s;
    animation-name: gradually;
    animation-duration: 1s;
  }
}
</style>
