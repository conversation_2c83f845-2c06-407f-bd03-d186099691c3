  <script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  Switch as hSwitch,
  CheckboxGroup as hCheckboxGroup,
  Checkbox as hCheckbox,
  Image as hImage,
  rangePicker as hrangePicker,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, EditOutlined, ExpandAltOutlined } from '@ant-design/icons-vue';
import { smartBrainApi } from '@haierbusiness-front/apis';
import {
  ISmartBrainFilter,
  ISmartBrain,
  Datum,
  IReportManagerInfoListList,
  deptLabelVo,
  IUserInfo,
  FetchBrainDataParams,
  IndexList,
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import {
  computed,
  ref,
  watch,
  onMounted,
  reactive,
  onBeforeUnmount,
  defineAsyncComponent,
  Suspense,
  onUnmounted,
  markRaw,
} from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { EventBus } from '../../../page/board/eventBus';
import { checkUserGroups } from '@haierbusiness-front/utils/src/authorityUtil';
import { UserGroupSystemConstant } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

const Echarts = defineAsyncComponent(() => import('../components/echarts.vue'));
const Rank = defineAsyncComponent(() => import('../components/rank.vue'));
const Map = defineAsyncComponent(() => import('../components/map.vue'));
const AccumulativeSlim = defineAsyncComponent(() => import('../components/accumulativeSlim.vue'));
const OrderingFoodMap = defineAsyncComponent(() => import('../components/map/orderingFoodMap.vue'));
const BookingHotelMap = defineAsyncComponent(() => import('../components/map/bookingHotelMap.vue'));
const TravelHotel = defineAsyncComponent(() => import('../components/map/travelHotel.vue'));
const TravelTaxiMap = defineAsyncComponent(() => import('../components/map/travelTaxiMap.vue'));
const MiceQDMap = defineAsyncComponent(() => import('../components/map/miceQDMap.vue'));
const TravelAirLineMap = defineAsyncComponent(() => import('../components/map/travelAirLineMap.vue'));
const SupermarketMap = defineAsyncComponent(() => import('../components/map/supermarketMap.vue'));
const TravelAirLineWorldMap = defineAsyncComponent(() => import('../components/map/travelAirLineWorldMap.vue'));
const MiceChinaMap = defineAsyncComponent(() => import('../components/map/miceChinaMap.vue'));
const TravelTrainMap = defineAsyncComponent(() => import('../components/map/travelTrainMap.vue'));
const AmountMap = defineAsyncComponent(() => import('../components/map/amountMap.vue'));
const getContent = (json: string) => {
  if (json.indexOf('OrderingFoodMap') != -1) {
    return OrderingFoodMap;
  } else if (json.indexOf('BookingHotelMap') != -1) {
    return BookingHotelMap;
  } else if (json.indexOf('TravelHotel') != -1) {
    return TravelHotel;
  } else if (json.indexOf('TravelTaxiMap') != -1) {
    return TravelTaxiMap;
  } else if (json.indexOf('MiceQDMap') != -1) {
    return MiceQDMap;
  } else if (json.indexOf('TravelAirLineMap') != -1) {
    return TravelAirLineMap;
  } else if (json.indexOf('SupermarketMap') != -1) {
    return SupermarketMap;
  } else if (json.indexOf('TravelAirLineWorldMap') != -1) {
    return TravelAirLineWorldMap;
  } else if (json.indexOf('MiceChinaMap') != -1) {
    return MiceChinaMap;
  } else if (json.indexOf('TravelTrainMap') != -1) {
    return TravelTrainMap;
  } else if (json.indexOf('AmountMap') != -1) {
    return AmountMap;
  } else {
    return Map;
  }
};
const emit = defineEmits(['showIndexModal']);
const props = defineProps({
  row: {
    type: Object,
    default: {},
  },
  identification:{
    type: String,
    default: "",
  },
  searchForm: {
    type: Object,
    default: {},
  },
  showFull:{
    type: Boolean,
    default: true,
  }
});
import { getCurrentRouter, routerParam } from '@haierbusiness-front/utils';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';

const getunit = (json: any) => {
  return JSON.parse(json)?.unit;
};

// 全屏展示
const fullScreen = (id:string) =>{
    emit('showIndexModal',props.row)
}

const getPopupContainer = (trigger)=>{
    return document.getElementById("content")
  }

// 初始化
onMounted(async () => {
});
onUnmounted(() => {});
</script>

<template>
    <!-- 分析类型筛选 -->
    <div class="indexItemBox" :id="'indexItemBox' + props.row.tid ">
      <div class="indexItemHeader">
        <div class="headerLeft"></div>
        <div class="reportName">
          {{ props.row.reportName }}
          <a-popover :getPopupContainer="getPopupContainer" title="指标详情" trigger="click">
            <template #content>
              <span  style="display: block;margin-top:10px;font-weight: bold;font-size:12px;">1.指标定义</span>
              <div v-if="props.row?.knowCenterVo?.entryName" style="max-width:800px;margin-top:3px;white-space: pre-line;">{{ props.row?.knowCenterVo?.entryName }} : {{ props.row?.knowCenterVo?.entryContent }}</div>
              <span  style="display: block;margin-top:10px;font-weight: bold;font-size:12px;">2.其他描述</span>
              <div style="width: 500px;margin-top:3px;white-space: pre-line;">{{ props.row?.indexReportContent }}</div>
            </template>
            <QuestionCircleOutlined style="color: #7f7f7f" />
          </a-popover>
        </div>
        <div class="fullScreenBox"><ExpandAltOutlined v-if="props.showFull" @click="fullScreen(props.row.tid)" style="color: #7f7f7f;font-size:20px;" /> </div>
      </div>
      <div class="indexItemBottom">
        <Echarts
          ref="echartRef"
          :key="props.row.tid"
          v-if="props.row.echartsJson.indexOf('饼图') != -1 || props.row.echartsJson.indexOf('柱状图') != -1 || props.row.echartsJson.indexOf('多柱线图') != -1"
          :echartsJson="props.row.echartsJson"
          :dataType="props.row.dataSearch"
          :height="24"
          :id="props.row.tid"
          :iden="props.identification"
          :searchForm="props.searchForm"
        />
        <Rank
          ref="echartRef"
          :key="props.row.tid"
          v-if="props.row.echartsJson.indexOf('条形图') != -1"
          :echartsJson="props.row.echartsJson"
          :id="props.row.tid"
          :iden="props.identification"
          :unit="getunit(props.row.echartsJson)"
          :height="24"
          :searchForm="props.searchForm"
        ></Rank>
        <!-- 地图 -->
        <component
          ref="echartRef"
          :key="props.row.tid"
          v-if="props.row.echartsJson.indexOf('地图') != -1"
          :echartsJson="props.row.echartsJson"
          :id="props.row.tid"
          :height="24"
          :is="getContent(props.row.echartsJson)"
          :searchForm="props.searchForm"
          :iden="props.identification"
        >
        </component>
        <AccumulativeSlim
          ref="echartRef"
          :key="props.row.tid"
          v-if="props.row.echartsJson.indexOf('数字看板') != -1"
          :echartsJson="props.row.echartsJson"
          :id="props.row.tid"
          :iden="props.identification"
          :height="24"
          :searchForm="props.searchForm"
        >
        </AccumulativeSlim>
      </div>
    </div>
</template>

<style scoped lang="less">
  .indexItemBox {
    display: flex;
    flex-flow: column;
    background: inherit;
    background-color: rgba(255, 255, 255, 1);
    border: none;
    border-radius: 0px;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    height: 100%;
    padding: 0 6px;
    &:hover{
      .fullScreenBox{
        visibility: visible;
      }
    }
    .fullScreenBox{
      padding:10px;
      margin-bottom: 5px;
      visibility: hidden;
    }
    // margin: 24px 24px 0 0;
    &:nth-child(3n) {
      margin-right: 0;
    }
    .indexItemBottom {
      flex: 1;
      padding: 0 5px 5px 5px;
      overflow: hidden;

    }
    .indexItemHeader {
      width: 100%;
      height: 48px;
      flex-shrink: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .reportName {
        color: #000;
        font-weight: 900;
        margin-left:20px;
        font-size:15px;
      }
    }
  }
</style>
