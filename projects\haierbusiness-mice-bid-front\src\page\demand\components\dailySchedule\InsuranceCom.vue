<script setup lang="ts">
// 保险
import {
  Form as hForm,
  FormItem as hFormItem,
  Select as hSelect,
  SelectOption as hSelectOption,
  Input as hInput,
  InputNumber as hInputNumber,
  DatePicker as hDatePicker,
  RangePicker as hRangePicker,
  Textarea as hTextarea,
  Button as hButton,
  Row as hRow,
  Col as hCol,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  Modal as hModal,
  Checkbox as hCheckbox,
  Tooltip as hTooltip,
  Collapse as hCollapse,
  CollapsePanel as hCollapsePanel,
  Upload as hUpload,
  Spin as aSpin,
  Image as aImage,
  message,
} from 'ant-design-vue';
import { onMounted, ref, reactive, watch, defineProps, defineEmits, toRaw, defineExpose } from 'vue';
import { InsurancesArr, FileTypeConstant } from '@haierbusiness-front/common-libs';
import { demandApi, pascalCaseApi } from '@haierbusiness-front/apis';
import InsuranceDetails from '@haierbusiness-front/components/mice/insuranceDetails/index.vue';

const props = defineProps({
  dateIndex: {
    type: Number,
    default: 0,
  },
  meetingPersonTotal: {
    // 会议人数
    type: Number,
    default: 0,
  },
  insuranceList: {
    type: Array as () => InsurancesArr[],
    default: () => [],
  },
  insuranceProductList: {
    // 保险产品列表（从父组件传递）
    type: Array as () => any[],
    default: () => [],
  },
  insuranceLoading: {
    // 保险产品加载状态（从父组件传递）
    type: Boolean,
    default: false,
  },
  insuranceHasMore: {
    // 是否还有更多保险产品（从父组件传递）
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['demandPlanInsuranceFunc', 'demandPlanRemoveFunc', 'insurancePopupScroll', 'insuranceSearch']);

const demandPlanFormRef = ref();
const insuranceShow = ref(false); // 保险条款
const insuranceChange = ref(false); // 保险切换
const modalType = ref<'product' | 'term'>('product'); // 弹框类型：'product' 或 'term'
const oldInsuranceObj = ref<Partial<InsurancesArr>>({}); // 保险产品数据保存
const insuranceIndex = ref<number | null>(null); // 保险产品数据
const insuranceDetail = ref<any>({}); // 保险产品详情
const insuranceTermFile = ref(''); // 保险条款文件路径
const insuranceTermLoading = ref(false); // 保险条款加载状态
const switchingInsuranceDetail = ref<any>({}); // 切换时的保险详情
const switchingDocuments = ref<any[]>([]); // 切换时的文档列表
const switchingLoading = ref(false); // 切换时的加载状态
const switchingAgreed = ref(false); // 是否已阅读并同意

// 日程安排表单
const formState = reactive<{ insurancesList: InsurancesArr[] }>({
  insurancesList: [], // 保险
});

// 移除本地的保险产品列表状态，改为使用父组件传递的数据

// 保险列表
watch(
  () => props.insuranceList,
  (newVal) => {
    formState.insurancesList = [...newVal];
  },
  {
    immediate: true,
    deep: true,
  },
);

// 删除
const removeInsurance = (type: string, index: number) => {
  emit('demandPlanRemoveFunc', { type: type, delIndex: index, index: props.dateIndex });
};

// 提交
const onSubmit = async () => {
  let isVerifyPassed = false;

  await demandPlanFormRef.value
    .validate()
    .then(() => {
      emit('demandPlanInsuranceFunc', { list: [...formState.insurancesList], index: props.dateIndex });

      isVerifyPassed = true;
    })
    .catch((err) => {
      isVerifyPassed = false;
    });

  return isVerifyPassed;
};
defineExpose({ onSubmit });

// 保险产品
const saveInsurances = (param: InsurancesArr) => {
  oldInsuranceObj.value = { ...param };
};
const changeInsurances = async (index: number) => {
  insuranceIndex.value = index;
  const productId = formState.insurancesList[index].productId;

  if (!productId) {
    message.warning('请先选择保险产品');
    return;
  }

  switchingLoading.value = true;
  switchingInsuranceDetail.value = {};
  switchingDocuments.value = [];
  switchingAgreed.value = false; // 重置同意状态

  try {
    // 获取保险产品详情
    const res = await pascalCaseApi.insuranceGet(productId);

    if (res) {
      switchingInsuranceDetail.value = res;

      // 只筛选出产品条款(type=24)
      if (res.path && res.path.length > 0) {
        switchingDocuments.value = res.path.filter((item) => item.type === 24);
      }

      // 显示切换弹框
      insuranceChange.value = true;
    }
  } catch (error) {
    console.error('获取保险产品详情失败:', error);
    message.error('获取保险产品详情失败');
  } finally {
    switchingLoading.value = false;
  }
};

// 查看保险产品
const viewProduct = async (index: number) => {
  const productId = formState.insurancesList[index].productId;

  if (!productId) {
    message.warning('请先选择保险产品');
    return;
  }

  try {
    const res = await pascalCaseApi.insuranceGet(productId);
    if (res) {
      insuranceDetail.value = res;
      modalType.value = 'product';
      insuranceShow.value = true;
    }
  } catch (error) {
    console.error('获取保险产品详情失败:', error);
    message.error('获取保险产品详情失败');
  }
};

// 查看保险条款
const viewTerm = async (index: number) => {
  const productId = formState.insurancesList[index].productId;

  if (!productId) {
    message.warning('请先选择保险产品');
    return;
  }

  insuranceTermLoading.value = true;
  modalType.value = 'term';
  insuranceShow.value = true;

  try {
    const res = await pascalCaseApi.insuranceGet(productId);
    if (res) {
      insuranceDetail.value = res;

      // 查找type为24的文件作为保险条款
      if (res.path && res.path.length > 0) {
        const termFile = res.path.find((item) => item.type === 24);
        insuranceTermFile.value = termFile ? termFile.path : '';
      } else {
        insuranceTermFile.value = '';
      }
    }
  } catch (error) {
    console.error('获取保险条款失败:', error);
    message.error('获取保险条款失败');
    insuranceTermFile.value = '';
  } finally {
    insuranceTermLoading.value = false;
  }
};

// 保险产品切换
const handleInsChange = async () => {
  insuranceChange.value = false;

  const insurancesChooseList = props.insuranceProductList.filter(
    (e) => e.id === formState.insurancesList[insuranceIndex.value].productId,
  );

  if (insurancesChooseList && insurancesChooseList.length > 0) {
    try {
      // 获取保险产品详情
      const res = await pascalCaseApi.insuranceGet(formState.insurancesList[insuranceIndex.value].productId);

      if (res) {
        insuranceDetail.value = res;

        // 使用接口返回的价格
        formState.insurancesList[insuranceIndex.value].demandUnitPrice =
          res.price || insurancesChooseList[0].demandUnitPrice;
        // 从详情接口获取 merchantId
        formState.insurancesList[insuranceIndex.value].productMerchantId = res.merchantId;
        formState.insurancesList[insuranceIndex.value].insuranceName =
          res.insuranceName || insurancesChooseList[0].insuranceName;

        // 将 type=24 的文档路径拼接成字符串，用逗号分隔
        let insuranceContentPaths = '';
        if (res.path && res.path.length > 0) {
          const termFiles = res.path.filter((item) => item.type === 24);
          insuranceContentPaths = termFiles.map((file) => file.path).join(',');

          // 设置保险条款文件（用于显示）
          const termFile = termFiles[0]; // 取第一个作为主要条款文件
          insuranceTermFile.value = termFile ? termFile.path : '';
        }
        formState.insurancesList[insuranceIndex.value].insuranceContent = insuranceContentPaths;

        if (insuranceIndex.value !== null) {
          calcPrice(insuranceIndex.value);
        }
      }
    } catch (error) {
      console.error('获取保险产品详情失败:', error);
      // 如果获取详情失败，使用原有数据
      if (insuranceIndex.value !== null) {
        formState.insurancesList[insuranceIndex.value].demandUnitPrice = insurancesChooseList[0].demandUnitPrice;
        formState.insurancesList[insuranceIndex.value].productMerchantId =
          insurancesChooseList[0].productMerchantId || null;
        formState.insurancesList[insuranceIndex.value].insuranceName = insurancesChooseList[0].insuranceName;
        formState.insurancesList[insuranceIndex.value].insuranceContent =
          insurancesChooseList[0].insuranceContent || '';

        calcPrice(insuranceIndex.value);
      }
    }
  }
};
// 取消切换
const handleInsClose = () => {
  insuranceChange.value = false;

  // 清空保险产品数据
  formState.insurancesList[insuranceIndex.value].productId = null;
  formState.insurancesList[insuranceIndex.value].demandUnitPrice = null;
  formState.insurancesList[insuranceIndex.value].productMerchantId = null;
  formState.insurancesList[insuranceIndex.value].insuranceName = '';
  formState.insurancesList[insuranceIndex.value].insuranceContent = '';
  formState.insurancesList[insuranceIndex.value].calcUnitPrice = null;

  // 清空切换相关数据
  switchingInsuranceDetail.value = {};
  switchingDocuments.value = [];
  switchingLoading.value = false;
  switchingAgreed.value = false;
};

const closeIns = () => {
  insuranceShow.value = false;
  insuranceDetail.value = {};
  insuranceTermFile.value = '';
  insuranceTermLoading.value = false;

  // 清空切换相关数据
  switchingInsuranceDetail.value = {};
  switchingDocuments.value = [];
  switchingLoading.value = false;
  switchingAgreed.value = false;
};

// 价格测算
const calcPrice = async (i: number) => {
  // 检查基本必要字段
  if (
    !formState.insurancesList[i].demandUnitPrice ||
    !formState.insurancesList[i].personNum ||
    !formState.insurancesList[i].productId
  ) {
    return;
  }

  // 如果缺少 productMerchantId 或 insuranceContent，先获取保险产品详情
  if (!formState.insurancesList[i].productMerchantId || !formState.insurancesList[i].insuranceContent) {
    try {
      const res = await pascalCaseApi.insuranceGet(formState.insurancesList[i].productId);
      if (res) {
        // 补充缺失的字段
        if (!formState.insurancesList[i].productMerchantId) {
          formState.insurancesList[i].productMerchantId = res.merchantId;
        }
        if (!formState.insurancesList[i].insuranceContent) {
          // 将 type=24 的文档路径拼接成字符串，用逗号分隔
          if (res.path && res.path.length > 0) {
            const termFiles = res.path.filter((item) => item.type === 24);
            formState.insurancesList[i].insuranceContent = termFiles.map((file) => file.path).join(',');
          }
        }
        if (!formState.insurancesList[i].insuranceName) {
          formState.insurancesList[i].insuranceName = res.insuranceName || '';
        }
      }
    } catch (error) {
      console.error('获取保险产品详情失败:', error);
      return;
    }
  }

  // 再次检查所有必要字段
  if (
    formState.insurancesList[i].demandUnitPrice &&
    formState.insurancesList[i].personNum &&
    formState.insurancesList[i].productId &&
    formState.insurancesList[i].productMerchantId &&
    formState.insurancesList[i].insuranceName &&
    formState.insurancesList[i].insuranceContent
  ) {
    const calcParams = {
      calcDate: formState.insurancesList[i].demandDate + ' 00:00:00', // 需求日期
      demandUnitPrice: formState.insurancesList[i].demandUnitPrice, // 需求单价
      personNum: formState.insurancesList[i].personNum, // 人数
      productId: formState.insurancesList[i].productId, //
      productMerchantId: formState.insurancesList[i].productMerchantId, //
      insuranceName: formState.insurancesList[i].insuranceName, //
      insuranceContent: formState.insurancesList[i].insuranceContent, //
    };

    const res = await demandApi.priceCalcInsurance({
      ...calcParams,
    });

    console.log('%c [ 保险-自动测算单价 ]-120', 'font-size:13px; background:pink; color:#bf2c9f;', res);

    formState.insurancesList[i].calcUnitPrice = res.calcUnitPrice; //
  }
};

// 打开文档
const openDocument = (url: string) => {
  if (url) {
    window.open(url, '_blank');
  }
};

// 从路径中提取文件名并去除时间戳和.pdf后缀
const getFileNameFromPath = (path: string) => {
  const match = path.match(/obs-swszh1\/(.+)$/);
  let fileName = match ? match[1] : path.split('/').pop() || '文档';
  // 去除文件名前面的时间戳（如 1752564138-）
  fileName = fileName.replace(/^\d+-/, '');
  // 去除.pdf后缀
  fileName = fileName.replace(/\.pdf$/i, '');
  return fileName;
};

// 下拉框滚动到底部 - 转发给父组件处理
const handlePopupScroll = (e: any) => {
  emit('insurancePopupScroll', e);
};

// 搜索防抖 - 转发给父组件处理
const handleSearch = (value: any) => {
  emit('insuranceSearch', value);
};

// 关闭保险详情弹窗
const handleInsuranceDetailsClose = () => {
  insuranceShow.value = false;
  modalType.value = 'product';
  insuranceDetail.value = {};
  insuranceTermFile.value = '';
  insuranceTermLoading.value = false;
};

// 移除 onMounted 中的保险产品列表获取，因为现在由父组件提供
</script>

<template>
  <!-- 保险 -->
  <div class="insurance_com">
    <h-form ref="demandPlanFormRef" :model="formState" :labelCol="{ style: { width: '84px' } }" hideRequiredMark>
      <div
        class="plan_col_list mb20"
        v-for="(insurancesItem, insurancesIndex) in formState.insurancesList"
        :key="insurancesIndex"
      >
        <div class="plan_col_title">
          {{ '保险' + (insurancesIndex + 1) }}
        </div>
        <div class="plan_col_del" @click="removeInsurance('insurance', insurancesIndex)"></div>

        <h-row :gutter="12" class="mt20">
          <h-col :span="8">
            <h-form-item
              label="参保人数："
              :name="['insurancesList', insurancesIndex, 'personNum']"
              :rules="{
                required: true,
                message: '请填写参保人数',
                trigger: 'change',
              }"
            >
              <h-input-number
                v-model:value="insurancesItem.personNum"
                @blur="calcPrice(insurancesIndex)"
                placeholder="请填写参保人数"
                allow-clear
                :min="1"
                :max="props.meetingPersonTotal || 99999"
                style="width: 100%"
              />
            </h-form-item>
          </h-col>

          <h-col :span="8">
            <h-form-item
              label="保险产品："
              :name="['insurancesList', insurancesIndex, 'productId']"
              :rules="{
                required: true,
                message: '请选择保险产品',
                trigger: 'change',
              }"
            >
              <div style="display: flex; gap: 8px; align-items: center">
                <h-select
                  v-model:value="insurancesItem.productId"
                  placeholder="请选择保险产品"
                  allow-clear
                  @focus="saveInsurances(insurancesItem)"
                  @change="changeInsurances(insurancesIndex)"
                  style="flex: 1"
                  :filter-option="false"
                  show-search
                  @search="handleSearch"
                  @popupScroll="handlePopupScroll"
                  :loading="props.insuranceLoading"
                  :not-found-content="props.insuranceLoading ? '加载中...' : '暂无数据'"
                >
                  <h-select-option v-for="item in props.insuranceProductList" :key="item.id" :value="item.id">
                    {{ item.insuranceName }}
                  </h-select-option>
                  <h-select-option v-if="props.insuranceLoading && props.insuranceProductList.length > 0" disabled>
                    <div style="text-align: center; color: #999">加载中...</div>
                  </h-select-option>
                </h-select>
                <h-button type="link" size="small" @click="viewProduct(insurancesIndex)">查看</h-button>
              </div>
            </h-form-item>
          </h-col>

          <h-col :span="8" v-show="insurancesItem.demandUnitPrice">
            <h-form-item label="单价：">
              <div class="ins_term">
                <div class="ins_price mr24">
                  {{ insurancesItem.demandUnitPrice + ' 元/人/天' }}
                </div>
                <!-- <div class="view_term" @click="viewTerm(insurancesIndex)">查看保险条款</div> -->
              </div>
            </h-form-item>
          </h-col>
        </h-row>
      </div>
    </h-form>

    <!-- 保险产品 - 切换 - 弹窗 -->
    <a-modal
      v-model:open="insuranceChange"
      title="提示"
      @ok="handleInsChange"
      @cancel="handleInsClose"
      okText="同意"
      :width="800"
      :confirmLoading="switchingLoading"
      :okButtonProps="{ disabled: !switchingAgreed }"
    >
      <div v-if="switchingLoading" style="display: flex; justify-content: center; align-items: center; height: 200px">
        <a-spin size="large" tip="加载中..." />
      </div>
      <div v-else>
        <div class="switching-tip">
          <p style="margin-bottom: 16px; color: #666; line-height: 1.6">
            <template v-if="switchingDocuments.length > 0">
              温馨提示：添加保险需求前请认真阅读
              <template v-for="(doc, index) in switchingDocuments" :key="index">
                <span
                  v-if="doc.path"
                  class="document-link"
                  @click="openDocument(doc.path)"
                  style="color: #1868db; font-weight: 500; cursor: pointer"
                >
                  《{{ getFileNameFromPath(doc.path) }}》{{ index < switchingDocuments.length - 1 ? '、' : '' }}
                </span>
              </template>
              文件，履行应尽的责任及义务，严格遵守规章制度。
            </template>
            <template v-else> 温馨提示：添加保险需求前，请履行应尽的责任及义务，严格遵守规章制度。 </template>
          </p>
        </div>

        <!-- 同意复选框 -->
        <div class="agreement-checkbox">
          <h-checkbox v-model:checked="switchingAgreed"> 我已阅读并同意 </h-checkbox>
        </div>
      </div>
    </a-modal>

    <!-- 查看保险条款/保险产品 - 弹窗 -->
    <InsuranceDetails
      :open="insuranceShow"
      :modal-type="modalType"
      :insurance-detail="insuranceDetail"
      :insurance-term-file="insuranceTermFile"
      :insurance-term-loading="insuranceTermLoading"
      @update:open="insuranceShow = $event"
      @close="handleInsuranceDetailsClose"
    />
  </div>
</template>

<style scoped lang="less">
.insurance_com {
  .plan_col_list {
    padding: 20px 24px;
    background: #fff;
    border-radius: 8px;

    position: relative;

    .plan_col_title {
      background: url('@/assets/image/demand/demand_insurance.png');
      background-repeat: no-repeat;
      background-size: 18px 18px;
      background-position: left center;

      text-indent: 28px;
      font-weight: 500;
      font-size: 18px;
      line-height: 25px;
      color: #1d2129;
    }

    .plan_col_del {
      position: absolute;
      top: 12px;
      right: 12px;

      width: 18px;
      height: 18px;
      background: url('@/assets/image/demand/demand_del_x.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      cursor: pointer;
    }

    .ins_term {
      display: flex;
      color: #1d2129;
      line-height: 32px;

      .ins_price {
        color: #1868db;
      }

      .view_term {
        color: #1868db;
        cursor: pointer;

        &:hover {
          text-decoration-line: underline;
        }
      }
    }
  }

  .insurance-detail {
    .detail-item {
      margin-bottom: 12px;

      strong {
        display: inline-block;
        margin-bottom: 4px;
      }

      .detail-content {
        margin-left: 0;

        :deep(p) {
          margin: 0;
          padding: 0;
        }

        :deep(span) {
          background-color: transparent !important;
        }
      }

      .file-list {
        margin-top: 8px;

        .file-item {
          margin-bottom: 16px;

          .file-type-label {
            font-weight: 500;
            margin-bottom: 8px;
            color: #333;
          }

          .image-preview {
            :deep(.ant-image) {
              border: 1px solid #e8e8e8;
              border-radius: 4px;
              padding: 4px;
              cursor: pointer;
              transition: all 0.3s;

              &:hover {
                border-color: #1868db;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              }
            }
          }

          .file-link {
            a {
              color: #1868db;
              text-decoration: none;

              &:hover {
                text-decoration: underline;
              }
            }
          }
        }
      }
    }

    .detail-item-horizontal {
      display: flex;
      margin-bottom: 12px;
      align-items: flex-start;

      strong {
        flex-shrink: 0;
        width: 80px;
        margin-right: 12px;
        color: #333;
      }

      .detail-content {
        flex: 1;

        :deep(p) {
          margin: 0 0 8px 0;
          padding: 0;
          line-height: 1.6;
        }

        :deep(p:last-child) {
          margin-bottom: 0;
        }

        :deep(ol),
        :deep(ul) {
          margin: 0;
          padding-left: 20px;
        }

        :deep(li) {
          margin-bottom: 4px;
        }
      }
    }
  }

  // 切换弹框样式
  .switching-tip {
    margin-bottom: 20px;
    padding: 12px;
    background: #f6f8fa;
    border-radius: 6px;
    border-left: 4px solid #1868db;

    .document-link {
      display: inline-block;
      color: #1868db !important;
      cursor: pointer !important;
      text-decoration: none;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
        color: #0d4fb3 !important;
      }
    }
  }

  .switching-documents {
    max-height: 400px;
    overflow-y: auto;

    .document-item {
      margin-bottom: 20px;
      padding: 16px;
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      background: #fafafa;

      .document-title {
        font-weight: 500;
        margin-bottom: 12px;
        color: #333;
        font-size: 14px;
      }

      .document-content {
        .image-preview {
          :deep(.ant-image) {
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 4px;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              border-color: #1868db;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
          }
        }

        .file-link {
          a {
            color: #1868db;
            text-decoration: none;
            font-size: 14px;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }

  .no-documents {
    padding: 40px 0;
    text-align: center;
  }

  .agreement-checkbox {
    margin-top: 20px;
    padding: 16px;
    border-top: 1px solid #e8e8e8;

    :deep(.ant-checkbox-wrapper) {
      font-size: 14px;
      color: #333;
    }
  }
}
</style>
