import { KnowCenterVo } from './smartBrainModel';
import { IPageRequest } from "../../basic";

export class Datum {
    /**
     * 标签id
     */
    id?: number;
    /**
     * 标签名称
     */
    labelName?: string;
    /**
     * 标签类型，0属性分类 1其他标签 2业务分类
     */
    labelType?: number;
    [property: string]: any;
}

export class IndicatorData {
    /**
     * 数据查询接口
     */
    dataSearch?: string;
    /**
     * json数据
     */
    echartsJson?: string;
    /**
     * 指标状态，0表示启用 1使用禁用
     */
    echartsStatus?: number;
    /**
     * 指标id
     */
    id?: number|null;
    /**
     * 图片地址
     */
    imagesUrl?: string;
    /**
     * 指标详情
     */
    indexReportContent?: string;
    /**
     * 词条id
     */
    knowId?: string;
    /**
     * 标签id
     */
    labelId?: string;
    /**
     * 标签名称
     */
    labelName?: string;
    /**
     * 查询条件
     */
    queryCondition?: null;
    /**
     * 指标名称
     */
    reportName?: string;
    [property: string]: any;
}

export class IndexList {
    credibilityVo?: null;
    dataSearch?: null;
    echartsJson?: string;
    echartsStatus?: null;
    id?: number;
    imagesUrl?: string;
    indexReportContent?: string;
    knowCenterVo?: null;
    labelVo?: null;
    queryCondition?: null;
    reportName?: null;
    [property: string]: any;
}

export class ISetting {
    /**
     * 管理配置json，例如大小、宽度、尺寸等信息
     */
    baseManageInfo?: string;
    /**
     * 指标id，多个按逗号隔开传入
     */
    echartsId?: string;
    /**
     * 管理配置id，传入则表示修改，不传表示新增
     */
    id?: number;
    /**
     * 管理配置名称
     */
    managerName?: string;
    /**
     * 需要配置的用户id，如果不传入表示当前用户自己配置
     */
    userId?: string;
    [property: string]: any;
}

export class  IReportManagerInfoListQuery{
    /**
     * 如果传入表示查询某一个配置信息
     */
    id?: string;
    /**
     * 如果传入表示查询某一个用户的配置信息
     */
    userId?: string;
    [property: string]: any;
}


export class ISmartBrain {
    id?: number | null
    creator?:string
    createTime?: string
    updater?: string
    updateTime?: string
}


export class ISmartBrainFilter extends IPageRequest {
    begin?:string
    end?:string
    attributeClassification?:string
    labelIds?:any
    labelIds1?:any
    labelIds2?:any
}

export class IReportManagerInfoListList {
    /**
     * 管理配置基本信息，长宽高尺寸
     */
    baseManageInfo?: null;
    /**
     * 管理名称
     */
    managerName?: string;
    /**
     * 管理配置id
     */
    mid: string;
    reportVo: IndicatorData[];
    [property: string]: any;
}


export class CredibilityVo {
    /**
     * 核对时间
     */
    checkDatetime: string;
    /**
     * 核对详情
     */
    checkDescribe: string;
    /**
     * 核对文件
     */
    checkFileUrl: string;
    /**
     * 核对分数
     */
    checkScore: string;
    /**
     * 核对状态
     */
    checkStatus: null;
    /**
     * 核对标题
     */
    checkTitle: string;
    /**
     * 核对人id
     */
    checkUserId: string;
    /**
     * 核对人姓名
     */
    checkUserName: null;
    /**
     * 可信度id
     */
    id: number;
    /**
     * 指标id
     */
    reportId: string;
    [property: string]: any;
}

export class KnowCenterVo {
    /**
     * 词条分类
     */
    entryCategoryName: string;
    /**
     * 词条内容
     */
    entryContent: string;
    /**
     * 词条名称
     */
    entryName: string;
    /**
     * 词条id
     */
    id: number;
    [property: string]: any;
}

export class LabelVo {
    /**
     * 标签id
     */
    labelId: string;
    /**
     * 标签名称
     */
    labelName: string;
    [property: string]: any;
}

export class deptLabelVo {
    /**
     * 编码
     */
    code?: string;
    /**
     * 名称
     */
    name?: string;
    [property: string]: any;
}
export class deptParmasVo {
    /**
     * 模糊搜索框
     */
    keyword?: string;
    /**
     * 0表示预算部门 1表示结算单位
     */
    type: string;
    [property: string]: any;
}

// 参数接口
export class FetchBrainDataParams {
    /*指标id */
    brainReportIndicatorId?: number|string;
  
    /*结算单位code */
    accountCompanyCodeList?: Record<string, unknown>[];

    /*预算部门code */
    budgetDepartmentCodeList?: Record<string, unknown>[];

    /*预算来源code */
    budgetSourceList?: Record<string, unknown>[];
  
    /*预订时间范围搜索 */
    bookingDate?: Record<string, unknown>[];
  }

  export class IKnowCenterVo {
    /**
     * 分类名称
     */
    categoryName?: string;
    /**
     * 词条名
     */
    entryName?: string;
    /**
     * 分页数
     */
    pageNo?: number;
    /**
     * 每页大小
     */
    pageSize?: number;
    [property: string]: any;
}

export interface IKnowSelectParmasVo {
    /**
     * 模糊查询key
     */
    name?: string;
    [property: string]: any;
}

export interface IKnowSelectResVo {
    /**
     * 分类id
     */
    catId?: string;
    /**
     * 分类名
     */
    catName?: string;
    [property: string]: any;
}
