<template>
    <h-row type="flex" :gutter="[30, 10]" style="margin-left: 0; margin-right: 0; height: 33vh" background="rgba(0,0,0,0)">
        <h-col :span="8" class="content" v-for="(column, index) in columns" :key="index">
            <div>
                <div class="num">
                    <CountTo :start-val="0" :end-val="rows[index]" />
                    <span class="unit" v-if="column.name[0] == '成交金额' ||
                        column.name[0] == '里程'
                        ">w</span>
                    <span class="unit" v-if="column.name[0] == '退单率'">%</span>
                </div>
                <div class="title"><span>▶</span>{{ column.name[0] }}</div>
            </div>
        </h-col>
    </h-row>
</template>
<script setup lang="ts">

import {
    Badge as hBadge,
    Progress as hProgress,
    <PERSON><PERSON> as hButton,
    Col as hCol,
    DatePicker as hDatePicker,
    Form as hForm,
    FormItem as hFormItem,
    Input as hInput,
    Modal as hModal,
    Popconfirm as hPopconfirm,
    Popover as hPopover,
    RangePicker as hRangePicker,
    Row as hRow,
    Select as hSelect,
    SelectOption as hSelectOption,
    Table as hTable,
    Tag as hTag,
    message,
    TableProps
} from 'ant-design-vue';
import CountTo from "@/components/vue-count-to/src";

import { queryTaxiAccumulative as queryAccumulative } from "@haierbusiness-front/apis/src/data/board/travel";
import { ref, onMounted } from "vue";
import { EventBus } from "../../../eventBus";

const columns: any = ref([]);
const rows = ref([]);
const loading = ref(false);
EventBus.on((event, params) => {
    if (event == "refresh") queryData(params);
});

const queryData = async (params?: { data: { name: string }; from: string }) => {
    loading.value = true;
    const data = await queryAccumulative(
        params ? params.data.name : null,
        params ? params.from : null
    );
    columns.value = data.columns ?? [];
    data.columns.forEach((item, index) => {
        if (item.name[0] == "成交金额") {
            let result: any = Number(data.rows[0][index]) / 10000;
            data.rows[0][index] = result.toFixed(0) - 0;
        }
    });
    rows.value = data.rows[0];
    loading.value = false;
};

onMounted(() => {
    queryData()
})
</script>
<style scoped lang="less">
@import url(@/assets/style/board/accumulative.less);
</style>
