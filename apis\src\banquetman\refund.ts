import { downloadPost, get, post } from '../request'
import { 
    IRefundReq, 
    IRefundRes,
    IPageResponse, 
} from '@haierbusiness-front/common-libs'


export const banquetRefundApi = {
    list: (params: IRefundReq): Promise<IPageResponse<IRefundRes>> => {
        return post('banquet/api/banquetRefund/list', params)
    },

    get: (id: number): Promise<IRefundRes> => {
        return get('banquet/api/banquetRefund/info', {
            id
        })
    },
    exportList: (params: IRefundReq): Promise<IPageResponse<IRefundRes>> => {
        return downloadPost('banquet/api/banquetRefund/export', params)
    },
}
