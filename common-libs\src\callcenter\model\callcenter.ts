import { IPageRequest } from "../../basic";

export interface AddressBookListParams {
    /**
     * 模糊查询姓名和电话
     */
    keyWord?: string;
    [property: string]: any;
}

export interface AddressBookListRes {
    /**
     * 电话
     */
    bookMobile?: string;
    /**
     * 姓名
     */
    bookName?: string;
    /**
     * 工号
     */
    bookNum?: string;
    /**
     * 操作
     */
    bookOperation?: number;
    /**
     * 创建时间
     */
    gmtCreate?: number;
    /**
     * 通讯录id
     */
    id?: number;
    /**
     * 状态
     */
    status?: number;
    [property: string]: any;
}

// 响应接口
export interface AddressBookListRes {
    /* */
    data: any;
  
    /* */
    code: string;
  
    /* */
    message: string;
  
    /* */
    success: boolean;
  }
  

  export interface angtListRes {
    /**
     * 坐席id
     */
    agentId?: string;
    /**
     * 分机号
     */
    agentNum?: string;
    /**
     * 主键id。传入表示修改，不传入表示新增
     */
    id?: null;
    /**
     * 工号
     */
    personNum?: string;
    /**
     * 产品id
     */
    productId?: number|null;
    /**
     * 产品名称
     */
    productName?: string;
    [property: string]: any;
}

export interface workOrderQuery {
    /**
     * 业务编码
     */
    businessNum?: any;
    /**
     * 工单id。传入id表示修改，不传入表示修改
     */
    id?: number;
    /**
     * 来电电话
     */
    mobile?: string;
    /**
     * 处理人姓名
     */
    personName?: string;
    /**
     * 处理人工号
     */
    personNum?: string;
    /**
     * 来电人姓名
     */
    userName?: string;
    /**
     * 来电人工号
     */
    userNum?: string;
    /**
     * 工单名称
     */
    workName?: string;
    [property: string]: any;
}