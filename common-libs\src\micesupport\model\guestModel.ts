import { IPageRequest } from '../../basic';
import { UploadFile } from '../../micebid';

export class IMeetingGuestFilter extends IPageRequest {
  miceInfoId?: number;
  begin?: string;
  end?: string;
  miceId?: number;
  groupId?: string;
  createName?: string;
  billType?: number;
  index?: number | null;
}

export class IMeetingGuest {
  index?: number | null;
  id?: number | null;
  miceCode?: number;
  miceInfoId?: number;
  //姓名
  name?: string;
  //所属单位名称
  companyName?: string;
  //嘉宾来源	
  guestSource?: number;
  //嘉宾头衔	
  title?: string;
  //嘉宾简介	
  description?: string;
  //所属单位id	
  companyId?: number;
  //照片类型
  type?: number;
  //照片id	
  photoId?: number;
  //禁忌与偏好	
  specialRequest?: string;
  //备注
  remark?: string;
  //照片
  imageUrl?: string;
  //排序
  sort?: number;
}