import { FileUploadResponse } from '@haierbusiness-front/common-libs'
import { CityResponse, CityItem,CityParams,airportParams,IGetApplicationFormTrainStationByCityReq, IGetApplicationFormTrainStationByCityRes } from '@haierbusiness-front/common-libs'

import { get, post } from '../request'

export const cityApi = {

  // 查询城市地址 三字码 废弃
  // getInitialGroupingCity: (): Promise<CityResponse> => {
  //   return get('/city/common/hb/common/api/district/getInitialGroupingCity')
  // },
  // // 城市模糊检索  /district/listDistrictBySy?name=河
  // listDistrictBySy: (name: string, pageSize: number,  pageNum : number): Promise<CityItem> => {
  //   return get(`/city/common/hb/common/api/district/listDistrictBySy?name=${name}&pageNum=${pageNum}&pagesize=${pageSize}`)
  // },

  // 获取城市列表
  getCityList: (cityParams:CityParams): Promise<CityResponse> => {
    return get(`/city/common/hb/common/api/district/list`, cityParams)
  },
  // 根据id查询行政区划资源树
  getCityTree: (params: any): Promise<CityResponse> => {
    return get('/city/common/hb/common/api/district/trees', params)
  },
  // 根据通用行政区划id置换供应商城市id
  displaceProviderDistrictId: (cityParams:CityParams): Promise<CityResponse> => {
    return post('/city/common/hb/common/api/district/displaceProviderDistrictId',cityParams)
  },

  // 高德地点
  gdArea: (params: CityTreeParams, errorNotify?: ((error: any) => any) | null): Promise<CityResponse> => {
    return get('/gd/place/text', params, undefined, errorNotify, true) as Promise<CityResponse>;
  },
    // 获取出差申请单车站信息
  trainStationGroupingByCityId: (params:IGetApplicationFormTrainStationByCityReq): Promise<Array<IGetApplicationFormTrainStationByCityRes>> => {
    return get(`/city/common/hb/common/api/trainStation/trainStationGroupingByCityId`, params);
  },

  // 根据城市id获取城市机场和临近机场
  getAirportByCityId: (params:airportParams): Promise<any> => {
    return get(`/city/common/hb/common/api/airport/searchCityAirport`, params)
  },

  // 根据城市id获取火车站三字码
  getTrainStationByCityId: (params:airportParams): Promise<any> => {
    return get(`/city/common/hb/common/api/trainStation/searchCityTrainStation`, params)
  },
    
}