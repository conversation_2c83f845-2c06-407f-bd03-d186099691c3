import {
  ICoinHaierAccountResponse,
  ICoinHaierBalanceResponse,
  ICoinHaierPayRequest,
  IPayHeader,
  IPayRequest,
  IPayResponse,
} from '@haierbusiness-front/common-libs';
import { errorHttpMessageHandle, get, post, originalGet } from '../request';

export const coinHaierPayApi = {
  /**
   * 支付
   */
  pay: (params: ICoinHaierPayRequest): Promise<IPayResponse> => {
    return post('pay/api/coin/haier/pay', params);
  },

  /**
   * 查询福利积分账户信息
   */
  searchAccount: (): Promise<ICoinHaierAccountResponse> => {
    return get('pay/api/coin/haier/account', undefined, undefined, (error) => {
      return errorHttpMessageHandle(error);
    });
  },

  /**
   * 查询福利积分余额
   */
  searchBalance: (): Promise<ICoinHaierBalanceResponse> => {
    return get('pay/api/coin/haier/balance');
  },

  /**
   * 查询福利积分余额
   */
  searchBalanceSimple: (): Promise<any> => {
    return originalGet('pay/api/coin/haier/balance');
  },
  /**
   * 发送支付验证码
   */
  sendCaptcha: (params: IPayRequest, header: IPayHeader): Promise<string> => {
    return post('pay/api/coin/haier/sms/captcha', params, {
      'hb-nonce': header.nonce,
      'hb-timestamp': header.timestamp,
      'hb-sign': header.sign,
      'hb-application-code': header.applicationCode,
      'hb-excludes': header.excludes,
    });
  },
};
