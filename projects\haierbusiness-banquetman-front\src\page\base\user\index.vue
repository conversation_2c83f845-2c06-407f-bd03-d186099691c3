<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  InputNumber as hInputNumber,
  message
} from "ant-design-vue";
import { ColumnType } from "ant-design-vue/lib/table/interface";
import { Key } from "ant-design-vue/lib/vc-table/interface";
import {
  PlusOutlined,
  SearchOutlined,
  DownOutlined,
  UpOutlined
} from "@ant-design/icons-vue";
import { banquetApplyApi } from "@haierbusiness-front/apis";
import {
  BApplyListRecord,
  BApplyPerson,
  BanquetStatusEnum
} from "@haierbusiness-front/common-libs";
import dayjs, { Dayjs } from "dayjs";
import { computed, ref, watch, onMounted } from "vue";
import { DataType, usePagination, useRequest } from "vue-request";
import { useEditDialog, useDelete } from "@haierbusiness-front/composables";
import {
  getCurrentRouter,
  errorModal,
  routerParam,
  getCurrentRoute
} from "@haierbusiness-front/utils";
import { storeToRefs } from "pinia";
import { applicationStore } from "@haierbusiness-front/utils/src/store/applicaiton";
import Editor from "@haierbusiness-front/components/editor/Editor.vue";
import { banquetBalanceApi } from "@haierbusiness-front/apis";

const formState = ref({
  accountSubject: "",
  cardNo: "",
  bankName: "",
  contractNo:""
});

const isEdit = ref<boolean>(false);

const uploadUrl = import.meta.env.VITE_UPLOAD_URL;

const detail = ref<any>({});


const onFinish = () => {
  banquetBalanceApi.updateAccount(formState.value).then(res => {
    message.success("保存成功!");
    isEdit.value = false
    getInfo();
  });
};

// 获取承诺需知详情
const getInfo = () => {
  banquetBalanceApi.getbanquetBalanceInfo().then(res => {
    detail.value = res;
  });
};

const editContent = () =>{
  formState.value.accountSubject = detail.value.accountSubject
  formState.value.cardNo = detail.value.cardNo
  formState.value.jointNo = detail.value.jointNo
  formState.value.bankName = detail.value.bankName
  formState.value.contractNo = detail.value.contractNo
  isEdit.value = true
}
const validateInput=(event) =>{
  const value = event.target.value;
      // 只允许输入数字
      if (!/^\d*$/.test(value)) {
        event.target.value = value.replace(/[^0-9]/g, '');
      }
  }
onMounted(() => {
  getInfo();
});
</script>

<template>
  <div>
    <h-row justify="space-between" style="padding: 20px;">
      <h-col style="font-size: 18px;">账户维护</h-col>
    </h-row>
    <div
      v-if="isEdit"
      style="background-color: #ffff;height: 100%;width: 100%;padding: 30px 60px;overflow: auto;min-height:500px;"
    >
      <h-form
        :model="formState"
        name="basic"
        :label-col="{ span: 2 }"
        :wrapper-col="{ span: 22 }"
        autocomplete="off"
        @finish="onFinish"
      >
        <h-form-item label="账户主体" name="accountSubject" :rules="[{ required: true, message: '请输入账户主体' }]">
          <h-input v-model:value.trim="formState.accountSubject" style="width:25%" />
        </h-form-item>
        <h-form-item label="银行卡号" name="cardNo" :rules="[{ required: true, message: '请输入银行卡号' }]">
          <h-input :min="1" type="number" v-model:value.trim="formState.cardNo" step="1" style="width:25%"  />
        </h-form-item>
        <h-form-item label="开户行联行号" name="jointNo" :rules="[{ required: true, message: '请输入开户行联行号' }]">
          <h-input :min="1" type="number"  v-model:value.trim="formState.jointNo"  style="width:25%"  />
        </h-form-item>
        <h-form-item label="所属银行" name="bankName" :rules="[{ required: true, message: '请输入所属银行' }]">
          <h-input v-model:value.trim="formState.bankName" style="width:25%"  />
        </h-form-item>
        <h-form-item label="合同号" name="contractNo" :rules="[{ required: true, message: '请输入合同号' }]">
          <h-input v-model:value.trim="formState.contractNo" style="width:25%"  />
        </h-form-item>
        <h-form-item :wrapper-col="{ offset: 2, span: 16 }">
          <h-button style="margin-right: 40px" @click="isEdit=false">取消</h-button>
          <h-button type="primary" html-type="submit">提交</h-button>
        </h-form-item>
      </h-form>
    </div>
    <div
      v-else
      style="background-color: #ffff;height: 100%;width: 100%;padding: 60px 60px;overflow: auto;min-height:500px;position: relative;"
    >
      <h-form-item label="账户主体" name="content">
        {{detail.accountSubject}}
        </h-form-item>
        <h-form-item label="银行卡号" name="content">
          {{detail.cardNo}}
        </h-form-item>
        <h-form-item label="开户行联行号" name="content">
          {{detail.jointNo}}
        </h-form-item>
        <h-form-item label="所属银行" name="content">
            {{detail.bankName}}
        </h-form-item>
        <h-form-item label="合同号" name="content">
            {{detail.contractNo}}
        </h-form-item>
      <h-button type="primary" class="btn" @click="editContent">修改内容</h-button>
    </div>
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}
.btn {
  position: absolute;
  bottom: 20px;
  left:45%;
}
.img {
  width: 104.5px;
  height: 50px;
}
:deep(.ant-descriptions-item-label) {
  width: 200px;
}
:deep(.ant-descriptions-item-content) {
  width: 300px;
}
input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none; 
  margin: 0; 
}

input[type=number] {
  -moz-appearance: textfield; /* Firefox */
}
</style>
