<template>
  <div v-loading="loading"  @mouseenter.native="scrollIn" @mouseleave.native="scroll=false" class="compact rank" :style="{ height: '100%', width: '100%' }">
    <vue3-seamless-scroll v-model="scroll" :list="dataList" :step="0.5" :limitMoveNum="5" :copyNum="1">
      <div class="rank-item" v-for="(item, index) in (dataList as any)" :key="index">
        <div class="rank-item-top">
          <div class="rank-item-top-icon" :style="getColor(index)">
            <span class="arrow"></span>
            {{ index + 1 }}
          </div>
          <div class="rank-item-top-title">{{ item.name }}</div>
        </div>
        <div class="rank-item-progress">
          <div class="progress-bar" :style="getStyle(item, index)"></div>
          <div class="progress-label">
            <template v-if="props.unit == '万元' && item.value == 0"> {{ '<1万' }} </template>
            <template v-else> {{ item.value}}{{ props.unit }} </template>
          </div>
        </div>
      </div>
    </vue3-seamless-scroll>
  </div>
</template>
<script setup lang="ts">
import { Vue3SeamlessScroll } from 'vue3-seamless-scroll';
import { numFormat } from '../../../utils/numFormat';
import { smartBrainApi } from '@haierbusiness-front/apis';
import { computed, ref, onMounted,onBeforeUnmount } from 'vue';
import { EventBus } from "../../../page/board/eventBus";
const colors = ['#FF3232', '#FF9C00', '#E1C117', '#127FC3', '#127FC3'];
const props = defineProps({
  unit: {
    type: String,
    default: '万元',
  },
  height: {
    type: Number,
    default: 33,
  },
  base: {
    type: Number,
    default: 6,
  },
  echartsJson: {
    type: String,
    default: '',
  },
  id: {
    type: [String, Number],
    default: '',
  },
  searchForm: {
    type: Object,
    default: {},
  },
});
// const setOption =JSON.parse(props.echartsJson)
const loading = ref(false);
const dataList = ref<Array<any>>([]);
const scroll = ref<boolean>(false)
const queryData = async (params?: { data: { name: string }; from: string }) => {
  loading.value = true
  const data = await smartBrainApi.queryCommonData({ brainReportIndicatorId: props.id, ...props.searchForm });
  loading.value = false
  const rows: any = [];
  data.rows.forEach((item:any) => {
    if(props.unit=='万元'){
      var value = (item[1] / 10000).toFixed(item[1] > 10000 ? 0 : 2);
    }else{
      var value = item[1]
    }
    rows.push({
      name: item[0],
      value,
    });
  });
  // rows.sort((a,b)=>b.value-a.value);
  dataList.value = rows;
};
const unsubscribe = EventBus.on((event, params) => {
  if (event == 'refresh') {
     queryData();
  }
});
const vh = computed(() => {
  if (dataList.value.length >= 5) return 5 * props.base;
  return dataList.value.length * props.base;
});
const singleHeight = computed(() => {
  const ratio = props.base / 6;
  return (window.innerHeight / 100) * 5 * ratio;
});
const getStyle = (item:any, index:number) => {
  const base = dataList.value[0].value;
  if (base == 0 || item.value == 0)
    return {
      minWidth: '0.5%',
    };
  return {
    minWidth: (item.value / base) * 70 + '%',
  };
};
const getColor = (index:number) => {
  return {
    background: colors[index > 4 ? 4 : index],
  };
};

const scrollIn = () =>{
  scroll.value = true
}

onMounted(() => {
  queryData();
});
// 在组件销毁前移除事件监听器
onBeforeUnmount(() => {
  unsubscribe()
});
</script>
<style scoped lang="less">
.rank {
  height: 35vh;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  overflow: hidden;

  &-item {
    padding-top: 1vh;

    &-top {
      display: flex;
      align-items: center;
      font-size: 1.2vh;

      &-icon {
        width: 2.4vh;
        height: 1.5vh;
        line-height: 1.5vh;
        text-align: center;
        background: #ff3232;
        border-radius: 2px;
        position: relative;
        font-size: 1vh;

        .arrow {
          position: absolute;
          width: 0.7vh;
          height: 0.7vh;
          transform: rotate(45deg);
          background: inherit;
          top: 50%;
          margin-top: -0.35vh;
          right: -0.3vh;
        }
      }

      &-title {
        margin-left: 8px;
        color: rgba(0, 0, 0, 0.65);
        font-weight: 600;
      }
    }

    &-progress {
      display: flex;
      align-items: center;
    }
  }
}

.progress {
  &-bar {
    min-width: 0;
    transition: all 0.2s;
    height: 1vh;
    background: linear-gradient( 270deg, #00ABF4 0%, #0073E5 100%);
  }

  &-label {
    flex: 1;
    font-size: 1.2vh;
    margin-left: 8px;
    color: rgba(0, 0, 0, 0.65);
  }
}
:deep(.el-loading-mask){
   background-color: rgba(255,255,255,0.5);
}
</style>
