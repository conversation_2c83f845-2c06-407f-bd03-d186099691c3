<script lang="ts" setup>
import {
  Table as hTable,
} from 'ant-design-vue';
import { computed, ref, watch, onMounted } from 'vue';
import { miceBidManOrderLogListApi } from '@haierbusiness-front/apis';
import { usePagination, } from 'vue-request';
import { ColumnType } from 'ant-design-vue/es/table';
import { resolveParam } from '@haierbusiness-front/utils';
import { useRoute, useRouter } from 'vue-router';
const router = useRouter()
const route = useRoute()

const props = defineProps({
  nodeId: {
    type: String,
  },
});

// type：主流程日志传0 分配日志传1
// systemType：用户端1 平台端2 商户端 4
//主流程日志
const orderLogList = ref()
//变更日志
const changeLog = ref()
//授权日志
const authorizationLog = ref()
const loading = ref(false);
const mainProcessLog = async (pageNum, pageSize) => {
  const currentRoute = router.currentRoute.value
  console.log(currentRoute, "currentRoute");
  let systemType:null| number = null
  if (currentRoute.matched[0].meta.title == "会务管理系统") {
    systemType = 2
  } else if (currentRoute.matched[0].meta.title == "会务招标用户端") {
    systemType = 1
  } else if (currentRoute.matched[0].meta.title == "会展商户端"
  ) {
    systemType = 4
  }
  loading.value = true
  const nodeId = resolveParam(props.nodeId).miceId
  try {
    const [order, change, auth] = await Promise.all([
      miceBidManOrderLogListApi.LogList({ mainId: nodeId, type: 0, systemType:systemType, pageNum, pageSize }),
      miceBidManOrderLogListApi.LogList({ mainId: nodeId, type: 2, systemType:systemType, pageNum, pageSize }),
      miceBidManOrderLogListApi.LogList({ mainId: nodeId, type: 3, systemType:systemType, pageNum, pageSize })
    ]);
    orderLogList.value = order;
    changeLog.value = change;
    authorizationLog.value = auth;
    console.log(orderLogList.value, "orderLogList.value");

  } catch (error) {
    console.error('日志获取失败:', error);
    loading.value = false
  } finally {
    loading.value = false
  }
}


const handleTableChange = (
  pag: { current: number; pageSize: number },
) => {
  const params = {
    pageNum: pag.current,
    pageSize: pag.pageSize,
  };
  console.log('最终查询参数:', params);
  mainProcessLog(pag.current, pag.pageSize);
};


onMounted(() => {
  mainProcessLog(1, 5)
})

const columns: ColumnType[] = [
  {
    title: '序号',
    dataIndex: 'index',
    width: '60px',
    align: 'center',
    customRender: ({ index }) => index + 1,

  },
  {
    title: '日期',
    dataIndex: 'gmtCreate',
    width: '160px',
    align: 'center',
    ellipsis: true,
  },

  {
    title: '状态变更',
    dataIndex: 'inquiryState',
    width: '280px',
    align: 'center',
    ellipsis: true,
    customRender: ({ record }) => {
      const from = record?.originalStateDesc ?? ''
      const to = record?.newStateDesc ?? ''
      return [from, to].filter(Boolean).join('→') || ''
    }


  },
  {
    title: '操作人',
    dataIndex: 'operator',
    width: '120px',
    align: 'center',
    ellipsis: true,
    customRender: ({ record }) => {
      const arr = [record?.operator, record?.operatorCode].filter(Boolean)
      return arr.join('/') || ''
    }

  },
  {
    title: '描述',
    dataIndex: 'remark',
    width: '200px',
    ellipsis: true,
    fixed: 'right',
    align: 'center',
  },
];

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: orderLogList.value?.total,
  current: orderLogList.value?.pageNum,
  pageSize: orderLogList.value?.pageSize,
  style: { justifyContent: 'center' },
}));
const changepagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: changeLog.value?.total,
  current: changeLog.value?.pageNum,
  pageSize: changeLog.value?.pageSize,
  style: { justifyContent: 'center' },
}));
const authorizationpagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: authorizationLog.value?.total,
  current: authorizationLog.value?.pageNum,
  pageSize: authorizationLog.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const activeKey = ref('1');


</script>

<template>
  <a-tabs v-model:activeKey="activeKey">
    <a-tab-pane key="1" tab="主日程日志">
      <h-table :columns="columns" :data-source="orderLogList" :pagination="pagination" :scroll="{ y: 550 }"
        :loading="loading" @change="handleTableChange($event as any)">
      </h-table>
    </a-tab-pane>
    <a-tab-pane key="2" tab="变更日志">
      <h-table :columns="columns" :data-source="changeLog" :pagination="changepagination" :scroll="{ y: 550 }"
        :loading="loading" @change="handleTableChange($event as any)">
      </h-table>
    </a-tab-pane>
    <a-tab-pane key="3" tab="授权日志">
      <h-table :columns="columns" :data-source="authorizationLog" :pagination="authorizationpagination"
        :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
      </h-table>
    </a-tab-pane>
  </a-tabs>
</template>

<style lang="less" scoped>
.important {
  color: red;
}
</style>