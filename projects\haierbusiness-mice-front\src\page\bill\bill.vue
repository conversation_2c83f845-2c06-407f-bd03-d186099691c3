<script setup lang="ts">
import { Card as hCard, Upload as hUpload, <PERSON><PERSON> as hButton, <PERSON><PERSON><PERSON> as hDivider, Tabs as hTabs, TabPane as hTabPane, message, Empty,
    Collapse as hCollapse, CollapsePanel as hCollapsePanel, Form as hForm, FormItem as hFormItem, Row as hRow, Col as hCol, Input as hInput,
    DatePicker as hDatePicker, InputNumber as hInputNumber, Table as hTable, Select as hSelect, SelectOption as hSelectOption, Space as hSpace,
    TypographyText as hTypographyText, Tooltip as hTooltip, Spin as hSpin, RangePicker as hRangePicker, Modal } from 'ant-design-vue'
import { UploadOutlined, DeleteOutlined, DownloadOutlined, PlusOutlined } from '@ant-design/icons-vue';
import type { UploadChangeParam, UploadProps } from 'ant-design-vue';
import { IStayBill, IStayBillDetail, SupplementTypeConstant, TeaUseTypeConstant, CarUsageConstant, CarUsageTimeConstant, HeaderConstant, HoldTimeTypeConstant, CateTypeConstant, LedTypeConstant,
    ICbBillData, ICbCateringBill, ICbPlaceBill, ICbVehicleBill, ICbInsuranceBill, ICbPresentBill, ICbOtherBill, IBillSupplements, IBaseBill,
    IBillSaveData, IFileData, IStaySaveData, ICateringSaveData, IPlaceSaveData, 
    IPresentSaveData, IOtherSaveData, IVehicleSaveData, ICbServiceBill } from '@haierbusiness-front/common-libs';
import { ref, computed, onMounted, watch } from 'vue';
import dayjs, { Dayjs } from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import localeData from 'dayjs/plugin/localeData'
import weekday from 'dayjs/plugin/weekday'
import weekOfYear from 'dayjs/plugin/weekOfYear'
import weekYear from 'dayjs/plugin/weekYear'
import {
    guid
} from '@haierbusiness-front/utils';
import { fileApi, billApi } from '@haierbusiness-front/apis';
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil'
import { useRequest } from 'vue-request';
import router from '../../router'
import { ColumnType } from 'ant-design-vue/lib/table';
import _ from 'lodash';
import start from '@/assets/image/miceIcon/start.png';
import end from '@/assets/image/miceIcon/end.png';
import via from '@/assets/image/miceIcon/via.png';


dayjs.extend(customParseFormat)
dayjs.extend(advancedFormat)
dayjs.extend(weekday)
dayjs.extend(localeData)
dayjs.extend(weekOfYear)
dayjs.extend(weekYear)


const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false)
const currentRouter = ref()

type ConferenceType = 'STAY' | 'CATERING' | 'VEHICLE' | 'PLACE' | 'PRESENT' | 'MANPOWER' | 'SERVICE' | 'OTHER' | 'INSURANCE';
const communicationUrl = import.meta.env.VITE_BUSINESS_COMMUNICATION_URL

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

const url = import.meta.env.VITE_BUSINESS_URL;

//#region 会展结算单上传
const fileList = ref<Array<any>>([])

const download = async () => {
    // downloadFile
    if(!data.value?.cbBillResponse?.cbCode) {
        showMessage('请先暂存账单，然后再生成会展结算单！')
        return
    }

    spinning.value = true
    await billApi.downloadFile(code.value, (error)=> {
        spinning.value = false
        showMessage(error.message)
    })
    spinning.value = false

}

const openFileUrl = (currentUrl: string) => {
    if(!currentUrl){
        showMessage('链接错误！')
        return
    }
  window.open(url + currentUrl, '_blank')
}

const fileListUpload = (options:any) => {
    uploadFile(options).then((res: any) => {
        const file = {
            uid: options.file.uid,
            name: options.file.name,
            url: url + res.path,
            relativeUrl: res.path,
        }
        fileList.value = [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const handleFileChange = (info: UploadChangeParam) => {
    if (info.file.status === 'done') {
        const file = {
            uid: info.file.uid,
            name: info.file.name,
            url: url + info.file.response.path,
            relativeUrl: info.file.response.path,
        }
        fileList.value = [file]
    } else if (info.file.status === 'error') {
        showMessage(`${info.file.name} file upload failed.`);
    }
}

const handleChange = (info: UploadChangeParam) => {
    if (info.file.status === 'error') {
        showMessage(`${info.file.name} file upload failed.`);
    }
}

//#endregion

//#region 字段自适应

const xs = 24

const sm = 12

const md = 8

const lg = 6

const input_lg = 8

const input_md = 12

const input_sm = 24


//#endregion


// 订单号
const code = ref('')


//#region 账单总信息

const data = ref<ICbBillData | undefined>({})

const BillsTotal = computed(() => {
    let total = stayTotal.value + cateringTotal.value + placeTotal.value + vehicleTotal.value + insuranceTotal.value
        + presentTotal.value + otherBillTotal.value
    if(cbServiceBill.value) {
        total += cbServiceBill.value.actualPrice ? cbServiceBill.value.actualPrice * 1 : 0
    }
    return round(total) 
})

const BillsDiff = computed(() => {
    let total = stayDiff.value + cateringDiff.value + placeDiff.value + vehicleDiff.value + insuranceDiff.value
        + presentDiff.value + otherBillDiff.value
    if(cbServiceBill.value) {
        const actualPrice = cbServiceBill.value.actualPrice ? (cbServiceBill.value.actualPrice * 1) : 0
        const biddingPrice = cbServiceBill.value.biddingPrice ? (cbServiceBill.value.biddingPrice * 1) : 0
        total += actualPrice - biddingPrice
    }
    return round(total)
})

const spinning = ref(false)

const getBill = async () => {
    spinning.value = true
    const billData = await billApi.getBill(code.value)
    const bill = billData.data
    data.value = bill
    if (bill && bill.cbBillResponse) {
        if(bill.cbBillResponse.cbStayBills && bill.cbBillResponse.cbStayBills.length > 0) {
            bill.cbBillResponse.cbStayBills.map((item, index) => {
                stayActiveKey.value.push('stayBill-' + index)
                stayBillActiveKey.value.push('stayBill-' + 'detail-' + index)
                stayBillActiveKey.value.push('stayBill-' + 'other-' + index)
                stayBillActiveKey.value.push('stayBill-' + 'file-' + index)
                item.detailList && item.detailList.map(detail => {
                    detail.checkInDate && (detail.checkInDate = dayjs(detail.checkInDate));
                    detail.key = guid()
                })
                handleOtherDate(item)
                handleFile(item)
            })
            stayBills.value = bill.cbBillResponse.cbStayBills ?? []
        }
        
        if(bill.cbBillResponse.cbCateringBills && bill.cbBillResponse.cbCateringBills.length > 0) {
            bill.cbBillResponse.cbCateringBills.map((item, index) => {
                cateringActiveKey.value.push('cateringBill-' + index)
                cateringBillActiveKey.value.push('cateringBill-' + 'detail-' + index)
                cateringBillActiveKey.value.push('cateringBill-' + 'other-' + index)
                cateringBillActiveKey.value.push('cateringBill-' + 'file-' + index)

                handleOtherDate(item)
                handleFile(item)
            })
            cateringBills.value = bill.cbBillResponse.cbCateringBills ?? []
        }

        if(bill.cbBillResponse.cbVehicleBills && bill.cbBillResponse.cbVehicleBills.length > 0) {
            bill.cbBillResponse.cbVehicleBills.map((item, index) => {
                vehicleActiveKey.value.push('vehicleBill-' + index)
                vehicleBillActiveKey.value.push('vehicleBill-' + 'detail-' + index)
                vehicleBillActiveKey.value.push('vehicleBill-' + 'other-' + index)
                vehicleBillActiveKey.value.push('vehicleBill-' + 'file-' + index)
                item.detailList && item.detailList.map(detail => {
                    detail.key = guid()
                })
                handleOtherDate(item)
                handleFile(item)
            })
            vehicleBills.value = bill.cbBillResponse.cbVehicleBills ?? []
        }
        
        if(bill.cbBillResponse.cbPlaceBills && bill.cbBillResponse.cbPlaceBills.length > 0) {
            bill.cbBillResponse.cbPlaceBills.map((item, index) => {
                placeActiveKey.value.push('placeBill-' + index)
                placeBillActiveKey.value.push('placeBill-' + 'facility-' + index)
                placeBillActiveKey.value.push('placeBill-' + 'other-' + index)
                placeBillActiveKey.value.push('placeBill-' + 'file-' + index)
                item.facilityDetailList && item.facilityDetailList.map(detail => {
                    detail.key = guid()
                })
                handleOtherDate(item)
                handleFile(item)
            })
            placeBills.value = bill.cbBillResponse.cbPlaceBills ?? []
        }
        insuranceBills.value = bill.cbBillResponse.insuranceBills ?? []
        insuranceActiveKey.value = bill.cbBillResponse.insuranceBills ? ['insuranceBill-0'] : []

        if(bill.cbBillResponse.cbPresentBills && bill.cbBillResponse.cbPresentBills.length > 0) {
            bill.cbBillResponse.cbPresentBills.map((item, index) => {
                presentActiveKey.value.push('presentBill-' + index)
                presentBillActiveKey.value.push('presentBill-' + 'other-' + index)
                presentBillActiveKey.value.push('presentBill-' + 'file-' + index)
                handleOtherDate(item)
                handleFile(item)
            })
            presentBills.value = bill.cbBillResponse.cbPresentBills ?? []
        }

        if(bill.cbBillResponse.cbOtherBills && bill.cbBillResponse.cbOtherBills.length > 0) {
            bill.cbBillResponse.cbOtherBills.map((item, index) => {
                otherActiveKey.value.push('otherBill-' + index)
                otherBillActiveKey.value.push('otherBill-' + 'other-' + index)
                otherBillActiveKey.value.push('otherBill-' + 'file-' + index)
                handleOtherDate(item)
                handleFile(item)
            })
            otherBills.value = bill.cbBillResponse.cbOtherBills ?? []
        }
        
        if(bill.cbBillResponse.cbServiceBills && bill.cbBillResponse.cbServiceBills.length > 0) {
            cbServiceBill.value = bill.cbBillResponse.cbServiceBills[0]
        }

        if(bill.cbBillResponse.settlementBillPath) {
            fileList.value = [{
                name: '会展结算单',
                url: url + bill.cbBillResponse.settlementBillPath,
                relativeUrl: bill.cbBillResponse.settlementBillPath,
                uid: guid()
            }]
        }
        if(bill.cbBillResponse.settlementBillPresentPath) {
            fileList.value = [{
                name: '礼品结算单',
                url: url + bill.cbBillResponse.settlementBillPresentPath,
                relativeUrl: bill.cbBillResponse.settlementBillPresentPath,
                uid: guid()
            }]
        }
        
    }
    spinning.value = false
}

const handleOtherDate = <T extends IBaseBill>(data: T) => {
    data.otherList && data.otherList.map(other => {
        other.key = guid();
        other.projectDate && (other.projectDate = dayjs(other.projectDate, 'YYYY-MM-DD'));
    })
}

const handleFile = <T extends IBaseBill>(data: T) => {
    if(data.invoices && data.invoices.length > 0) {
        let invoices: Array<any> = []
        data.invoices.map((item, index) => {
            const file = {
                name: '附件' + (index + 1),
                url: item,
                uid: guid()
            }
            invoices.push(file)
        })
        data.invoices = invoices
    }
    if(data.memos && data.memos.length > 0) {
        let memos: Array<any> = []
        data.memos.map((item, index) => {
            const file = {
                name: '附件' + (index + 1),
                url: item,
                uid: guid()
            }
            memos.push(file)
        })
        data.memos = memos
    }
    if(data.contracts && data.contracts.length > 0) {
        let contracts: Array<any> = []
        data.contracts.map((item, index) => {
            const file = {
                name: '附件' + (index + 1),
                url: item,
                uid: guid()
            }
            contracts.push(file)
        })
        data.contracts = contracts
    }
    if(data.others && data.others.length > 0) {
        let others: Array<any> = []
        data.others.map((item, index) => {
            const file = {
                name: '附件' + (index + 1),
                url: item,
                uid: guid()
            }
            others.push(file)
        })
        data.others = others
    }
}

onMounted(async() => {
    currentRouter.value = await router
    code.value = currentRouter.value.currentRoute.query?.code ?? ''
    await getBill()
})



//#endregion

// 上传
const uploadFile = async (options: any) => {
    const formData = new FormData()
    formData.append('file', options.file)
    formData.append('fileName', options.file.name)
    const res = await billApi.uploadFile(formData)
    return res
}

// 四舍五入两位小数
const round = (number: number) => {
    return _.round(number, 2)
}

//#region 管理费

const cbServiceBill = ref<ICbServiceBill>()


//#endregion

// #region  住宿

const stayActiveKey = ref<string[]>([]);
const stayBillActiveKey = ref<string[]>([]);

const stayBills = ref<Array<IStayBill>>([]);

const stayBillTypes = computed(() => SupplementTypeConstant.toArray('STAY'))

const stayTotal = ref<number>(0)
const stayDiff = ref<number>(0)


const stayBillDetailColumns: ColumnType[] = [
  {
    title: '房型',
    dataIndex: 'roomType',
  },
  {
    title: '入住日期',
    dataIndex: 'checkInDate',
  },
  {
    title: '单价',
    dataIndex: 'price',
  },
  {
    title: '间夜数',
    dataIndex: 'num',
  },
  {
    title: '操作',
    dataIndex: 'operation',
  },
];

const supplementColumns: ColumnType[] = [
  {
    title: '名称',
    dataIndex: 'name',
    width: 150,
    fixed: 'left'
  },
  {
    title: '日期',
    dataIndex: 'date',
    width: 150
  },
  {
    title: '类别',
    dataIndex: 'type',
    width: 150
  },
  {
    title: '数量',
    dataIndex: 'num',
    width: 150
  },
  {
    title: '单价',
    dataIndex: 'price',
    width: 150
  },
  {
    title: '描述',
    dataIndex: 'desc',
    width: 150
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 100,
    fixed: 'right'
  },
];

const stayInvoicesUpload = (options:any, index: number) => {
    const stayBill = stayBills.value.find(o => o.infoId === index)
    if(!stayBill) {
        showMessage('未找到对应账单！')
        return
    }
    
    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        stayBill.invoices = stayBill.invoices ? [...stayBill.invoices, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const removeStayFile = (uid: string, index: number, fileType: number) => {
    const stayBill = stayBills.value.find(o => o.infoId === index)
    if(!stayBill) {
        showMessage('未找到对应账单！')
        return
    }
    if(fileType === 1) {
        stayBill.invoices = _.filter(stayBill.invoices, function(o){
            return o.uid != uid
        })
    } else if (fileType === 2) {
        stayBill.memos = _.filter(stayBill.memos, function(o){
            return o.uid != uid
        })
    } else if (fileType === 3) {
        stayBill.contracts = _.filter(stayBill.contracts, function(o){
            return o.uid != uid
        })
    } else if (fileType === 4) {
        stayBill.others = _.filter(stayBill.others, function(o){
            return o.uid != uid
        })
    }
}

const stayMemosUpload = (options:any, index: number) => {
    const stayBill = stayBills.value.find(o => o.infoId === index)
    if(!stayBill) {
        showMessage('未找到对应账单！')
        return
    }

    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        stayBill.memos = stayBill.memos ? [...stayBill.memos, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const stayContractsUpload = (options:any, index: number) => {
    const stayBill = stayBills.value.find(o => o.infoId === index)
    if(!stayBill) {
        showMessage('未找到对应账单！')
        return
    }

    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        stayBill.contracts = stayBill.contracts ? [...stayBill.contracts, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const stayOthersUpload = (options:any, index: number) => {
    const stayBill = stayBills.value.find(o => o.infoId === index)
    if(!stayBill) {
        showMessage('未找到对应账单！')
        return
    }
    
    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        stayBill.others = stayBill.others ? [...stayBill.others, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const addStayDetil = (key: number ,roomType: string | undefined) => {
    const stayBill = stayBills.value.find(o => o.infoId === key)
    if (stayBill) {
        !stayBill!.detailList && (stayBill!.detailList = []);

        // 计算详情价格
        let total = 0
        stayBill.detailList.map((item, index) => {
            total += ((item.price ?? 0) * (item.nights ?? 0)) ?? 0
        })

        stayBill._billTotal = total

        stayBill!.detailList!.push({
            key: guid(),
            roomType
        })
    }
}

watch(stayBills, (newValue)=> {
    let allTotal = 0
    let biddingTotal = 0
    if(newValue && newValue.length > 0) {
        newValue.map((item) => {
            let nights = 0
            let otherTotal = 0
            if(item.detailList && item.detailList.length > 0) {
                item.detailList.map((detail) => {
                    detail.price = item.actualPrice
                    nights += (detail.nights ?? 0) * 1
                })
            }
            if(item.otherList && item.otherList.length > 0) {
                item.otherList.map((other) => {
                    otherTotal += ((other.price ?? 0) * (other.num ?? 0))
                })
            }
            item.actualNights = nights ?? 0
            item._otherTotal = round(otherTotal ?? 0)
            item._billTotal = round((item.actualPrice ?? 0) * (item.actualNights ?? 0) ?? 0)
            item._biddingTotal = round((item.biddingPrice ?? 0) * (item.nights ?? 0) ?? 0)
            allTotal += item._billTotal + item._otherTotal
            biddingTotal += item._biddingTotal
        })
        stayTotal.value = round(allTotal)
        stayDiff.value = round(allTotal - biddingTotal)
    }
}, { immediate: true, deep: true })

// #endregion

//#region 餐饮

const cateringActiveKey = ref<Array<string>>([]);

const cateringBillActiveKey = ref<Array<string>>([]);

const cateringBills = ref<Array<ICbCateringBill>>([])

const cateringBillTypes = computed(() => SupplementTypeConstant.toArray('CATERING'))

const cateringTotal = ref<number>(0)
const cateringDiff = ref<number>(0)

watch(cateringBills, (newValue)=> {
    let allTotal = 0
    let biddingTotal = 0
    if(newValue && newValue.length > 0) {
        newValue.map((item) => {
            let otherTotal = 0
            if(item.otherList && item.otherList.length > 0) {
                item.otherList.map((other) => {
                    otherTotal += ((other.price ?? 0) * (other.num ?? 0))
                })
            }
            item._otherTotal = round(otherTotal ?? 0)
            item._billTotal = round((item.actualPrice ?? 0) * (item.actualNum ?? 0) ?? 0)
            allTotal += item._billTotal + item._otherTotal
            item._biddingTotal = round((item.biddingPrice ?? 0) * (item.personNum ?? 0) ?? 0)
            biddingTotal += item._biddingTotal
        })
        cateringTotal.value = round(allTotal)
        cateringDiff.value = round(allTotal - biddingTotal)
    }
}, { immediate: true, deep: true })

const cateringInvoicesUpload = (options:any, index: number) => {
    const cateringBill = cateringBills.value.find(o => o.infoId === index)
    if(!cateringBill) {
        showMessage('未找到对应账单！')
        return
    }
    const formData = new FormData()
    formData.append('file', options.file)
    formData.append('fileName', options.file.name)
    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        cateringBill.invoices = cateringBill.invoices ? [...cateringBill.invoices, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const cateringMemosUpload = (options:any, index: number) => {
    const cateringBill = cateringBills.value.find(o => o.infoId === index)
    if(!cateringBill) {
        showMessage('未找到对应账单！')
        return
    }

    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        cateringBill.memos = cateringBill.memos ? [...cateringBill.memos, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const cateringOthersUpload = (options:any, index: number) => {
    const cateringBill = cateringBills.value.find(o => o.infoId === index)
    if(!cateringBill) {
        showMessage('未找到对应账单！')
        return
    }

    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        cateringBill.others = cateringBill.others ? [...cateringBill.others, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const removeCateringFile = (uid: string, index: number, fileType: number) => {
    const cateringBill = cateringBills.value.find(o => o.infoId === index)
    if(!cateringBill) {
        showMessage('未找到对应账单！')
        return
    }
    if(fileType === 1) {
        cateringBill.invoices = _.filter(cateringBill.invoices, function(o){
            return o.uid != uid
        })
    } else if (fileType === 2) {
        cateringBill.memos = _.filter(cateringBill.memos, function(o){
            return o.uid != uid
        })
    } else if (fileType === 4) {
        cateringBill.others = _.filter(cateringBill.others, function(o){
            return o.uid != uid
        })
    }
}

//#endregion

//#region 场地

const placeActiveKey = ref<Array<string>>([]);

const placeBillActiveKey = ref<Array<string>>([]);

const placeBills = ref<Array<ICbPlaceBill>>([])

const placeBillTypes = computed(() => SupplementTypeConstant.toArray('PLACE'))

const placeTotal = ref<number>(0)
const placeDiff = ref<number>(0)

const placeBillFacilityDetailColumns: ColumnType[] = [
  {
    title: '设备设施项目 ',
    dataIndex: 'project',
    width: 200,
    fixed: 'left'
  },
  {
    title: '使用会议厅',
    dataIndex: 'hall',
    width: 200,
  },
  {
    title: '单价',
    dataIndex: 'unitPrice',
    width: 200
  },
  {
    title: '数量',
    dataIndex: 'num',
    width: 100
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 100,
    fixed: 'right'
  },
];

const placeInvoicesUpload = (options:any, index: number) => {
    const placeBill = placeBills.value.find(o => o.infoId === index)
    if(!placeBill) {
        showMessage('未找到对应账单！')
        return
    }

    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        placeBill.invoices = placeBill.invoices ? [...placeBill.invoices, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const placeMemosUpload = (options:any, index: number) => {
    const placeBill = placeBills.value.find(o => o.infoId === index)
    if(!placeBill) {
        showMessage('未找到对应账单！')
        return
    }

    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        placeBill.memos = placeBill.memos ? [...placeBill.memos, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const placeContractsUpload = (options:any, index: number) => {
    const placeBill = placeBills.value.find(o => o.infoId === index)
    if(!placeBill) {
        showMessage('未找到对应账单！')
        return
    }

    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        placeBill.contracts = placeBill.contracts ? [...placeBill.contracts, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const placeOthersUpload = (options:any, index: number) => {
    const placeBill = placeBills.value.find(o => o.infoId === index)
    if(!placeBill) {
        showMessage('未找到对应账单！')
        return
    }

    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        placeBill.others = placeBill.others ? [...placeBill.others, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const removePlaceFile = (uid: string, index: number, fileType: number) => {
    const placeBill = placeBills.value.find(o => o.infoId === index)
    if(!placeBill) {
        showMessage('未找到对应账单！')
        return
    }
    if(fileType === 1) {
        placeBill.invoices = _.filter(placeBill.invoices, function(o){
            return o.uid != uid
        })
    } else if (fileType === 2) {
        placeBill.memos = _.filter(placeBill.memos, function(o){
            return o.uid != uid
        })
    } else if (fileType === 3) {
        placeBill.contracts = _.filter(placeBill.contracts, function(o){
            return o.uid != uid
        })
    } else if (fileType === 4) {
        placeBill.others = _.filter(placeBill.others, function(o){
            return o.uid != uid
        })
    }
}

const addplaceBillDetilFacility = (key: number) => {
    const placeBill = placeBills.value.find(o => o.infoId === key)
    if (placeBill) {
        !placeBill!.facilityDetailList && (placeBill!.facilityDetailList = []);
        placeBill!.facilityDetailList!.push({
            key: guid()
        })
    }   
}

watch(placeBills, (newValue)=> {
    let allTotal = 0
    let biddingTotal = 0
    if(newValue && newValue.length > 0) {
        newValue.map((item) => {

            // 合计
            let billTotal = 0
            // 竞价合计
            let _biddingTotal = 0

            // 设施实际合计
            let facilityTotal = 0
            // 其他合计
            let otherTotal = 0

            // 设施合计
            let biddingFacilityTotal = 0
            
            if(item.facility && item.facility.length > 0) {
                // 计算竞价设施合计
                biddingFacilityTotal = _.sumBy(item.facility, function(o){
                    return (o.num ?? 0) * (o.price ?? 0)
                })
            }
            
            if(item.facilityDetailList && item.facilityDetailList.length > 0) {
                // 设施
                facilityTotal = _.sumBy(item.facilityDetailList, function(o){
                    return (o.unitPrice ?? 0) * (o.num ?? 0)
                })
            }
            if(item.otherList && item.otherList.length > 0) {
                // 补充
                otherTotal = _.sumBy(item.otherList, function(o){
                    return (o.price ?? 0) * (o.num ?? 0)
                })
            }
            item._facilityTotal = round(facilityTotal)
            item._otherTotal = round(otherTotal)
            billTotal = (item.actualPrice ?? 0) * 1 + item._facilityTotal
            _biddingTotal = round((item.biddingPrice ?? 0) + biddingFacilityTotal)
            if(item.teaBreak === 1) {
                billTotal += (item.billTeaNumber ?? 0) * (item.billTeaUnitPrice ?? 0)
                _biddingTotal += (item.teaNumber ?? 0) * (item.teaBidUnitPrice ?? 0)
            }
            if(item.layout === 1) {
                billTotal += (item.billBuildCost ?? 0)
                _biddingTotal += (item.bidBuildCost ?? 0)
            }

            if(item.isLed === 1) {
                billTotal += (item.billLedPrice ?? 0)
                _biddingTotal += (item.ledBidPrice ?? 0)
            }
            
            item._billTotal = round(billTotal)
            item._biddingTotal = round(_biddingTotal)
            allTotal += billTotal + item._otherTotal
            biddingTotal += _biddingTotal
        })
        placeTotal.value = round(allTotal)
        placeDiff.value = round(allTotal - biddingTotal)
    }
}, { immediate: true, deep: true })

//#endregion

//#region 用车

const vehicleActiveKey = ref<Array<string>>([]);

const vehicleBillActiveKey = ref<Array<string>>([]);

const vehicleBills = ref<Array<ICbVehicleBill>>([])

const vehicleBillTypes = computed(() => SupplementTypeConstant.toArray('VEHICLE'))

const vehicleTotal = ref<number>(0)
const vehicleDiff = ref<number>(0)

const vehicleBillDetailColumns: ColumnType[] = [
  {
    title: '用车时段',
    dataIndex: 'timeFrameList',
    fixed: 'left',
    width: 380
  },
  {
    title: '品牌',
    dataIndex: 'brand',
    width: 150
  },
  {
    title: '型号',
    dataIndex: 'carType',
    width: 150
  },
  {
    title: '座位数',
    dataIndex: 'seats',
    width: 150
  },
  {
    title: '车龄',
    dataIndex: 'carAge',
    width: 150
  },
  {
    title: '车牌号',
    dataIndex: 'carNo',
    width: 150
  },
  {
    title: '驾驶员',
    dataIndex: 'driver',
    width: 150
  },
  {
    title: '公里数',
    dataIndex: 'kmNum',
    width: 150
  },
  {
    title: '公里费用',
    dataIndex: 'kmPrice',
    width: 150
  },
  {
    title: '过路费',
    dataIndex: 'toll',
    width: 150
  },
  {
    title: '实际去向',
    dataIndex: 'practical',
    width: 300
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200
  },
  {
    title: '操作',
    dataIndex: 'operation',
    fixed: 'right',
    width: 150
  },
];

const timeFrameBeginAndEnd = ref<[Dayjs, Dayjs]>()
const ontimeFrameChange = (dates: RangeValue, dateStrings: string[]) => {
  if (dateStrings) {
    vehicleBill.detailList![index].startTimeFrame = dateStrings[0]
    vehicleBill.detailList![index].endTimeFrame = dateStrings[1]
  } else {
    vehicleBill.detailList![index].startTimeFrame = null
    vehicleBill.detailList![index].endTimeFrame = null
  }
};

const vehicleInvoicesUpload = (options:any, index: number) => {
    const vehicleBill = vehicleBills.value.find(o => o.infoId === index)
    if(!vehicleBill) {
        showMessage('未找到对应账单！')
        return
    }

    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        vehicleBill.invoices = vehicleBill.invoices ? [...vehicleBill.invoices, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const vehicleMemosUpload = (options:any, index: number) => {
    const vehicleBill = vehicleBills.value.find(o => o.infoId === index)
    if(!vehicleBill) {
        showMessage('未找到对应账单！')
        return
    }

    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        vehicleBill.memos = vehicleBill.memos ? [...vehicleBill.memos, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const vehicleOthersUpload = (options:any, index: number) => {
    const vehicleBill = vehicleBills.value.find(o => o.infoId === index)
    if(!vehicleBill) {
        showMessage('未找到对应账单！')
        return
    }

    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        vehicleBill.others = vehicleBill.others ? [...vehicleBill.others, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const removeVehicleFile = (uid: string, index: number, fileType: number) => {
    const vehicleBill = vehicleBills.value.find(o => o.infoId === index)
    if(!vehicleBill) {
        showMessage('未找到对应账单！')
        return
    }
    if(fileType === 1) {
        vehicleBill.invoices = _.filter(vehicleBill.invoices, function(o){
            return o.uid != uid
        })
    } else if (fileType === 2) {
        vehicleBill.memos = _.filter(vehicleBill.memos, function(o){
            return o.uid != uid
        })
    } else if (fileType === 4) {
        vehicleBill.others = _.filter(vehicleBill.others, function(o){
            return o.uid != uid
        })
    }
}

// 判断会议类型
const isMiceType = () => {
    var code = data.value.cbBillResponse.code;
    return !code.includes("LC") && !code.includes("LE");
};


const addVehicleBillDetil = (key: number) => {
    const vehicleBill = vehicleBills.value.find(o => o.infoId === key)
    if (vehicleBill){
        !vehicleBill!.detailList && (vehicleBill!.detailList = []);
        vehicleBill!.detailList!.push({
            key: guid()
        })
    }  
}

watch(vehicleBills, (newValue)=> {
    let allTotal = 0
    let biddingTotal = 0
    if(newValue && newValue.length > 0) {
        newValue.map((item) => {
            let otherTotal = 0
            let detailTotal = 0
            if(item.detailList && item.detailList.length > 0) {
                item.detailList.map((detail) => {
                    detailTotal += ((detail.kmNum ?? 0 ) * (detail.kmPrice ?? 0)) + (detail.toll ?? 0) * 1
                })
            }
            if(item.otherList && item.otherList.length > 0) {
                item.otherList.map((other) => {
                    otherTotal += ((other.price ?? 0) * (other.num ?? 0))
                })
            }
            item.actualNum = item.detailList?.length ?? 0
            item._otherTotal = round(otherTotal ?? 0)
            item._billTotal = round(detailTotal)
            item._biddingTotal = round((item.biddingPrice ?? 0) * (item.vehicleNum ?? 0) ?? 0)
            allTotal += item._billTotal + item._otherTotal
            biddingTotal += item._biddingTotal
        })
        vehicleTotal.value = round(allTotal)
        vehicleDiff.value = round(allTotal - biddingTotal)
    }
}, { immediate: true, deep: true })

//#endregion

//#region 保险

const insuranceActiveKey = ref<Array<string>>([]);

const insuranceBills = ref<Array<ICbInsuranceBill>>([])

const insuranceTotal = ref<number>(0)
const insuranceDiff = ref<number>(0)

watch(insuranceBills, (newValue)=> {
    let allTotal = 0
    let biddingTotal = 0
    if(newValue && newValue.length > 0) {
        newValue.map((item) => {
            allTotal += (item.actualPrice ?? 0) * (item.insuredDays ?? 0) ?? 0
            biddingTotal += item.schemeBiddingPrice ?? 0
        })

        insuranceTotal.value = round(allTotal)
        insuranceDiff.value = round(allTotal - biddingTotal)
    }
}, { immediate: true, deep: true })

//#endregion

//#region 礼品

const presentActiveKey = ref<Array<string>>([]);

const presentBillActiveKey = ref<Array<string>>([]);

const presentBills = ref<Array<ICbPresentBill>>([])

const presentBillTypes = computed(() => SupplementTypeConstant.toArray('PRESENT'))

const presentTotal = ref<number>(0)
const presentDiff = ref<number>(0)

const presentInvoicesUpload = (options:any, index: number) => {
    const presentBill = presentBills.value.find(o => o.infoId === index)
    if(!presentBill) {
        showMessage('未找到对应账单！')
        return
    }

    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        presentBill.invoices = presentBill.invoices ? [...presentBill.invoices, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const presentMemosUpload = (options:any, index: number) => {
    const presentBill = presentBills.value.find(o => o.infoId === index)
    if(!presentBill) {
        showMessage('未找到对应账单！')
        return
    }

    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        presentBill.memos = presentBill.memos ? [...presentBill.memos, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const presentOthersUpload = (options:any, index: number) => {
    const presentBill = presentBills.value.find(o => o.infoId === index)
    if(!presentBill) {
        showMessage('未找到对应账单！')
        return
    }

    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        presentBill.others = presentBill.others ? [...presentBill.others, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const removePresentFile = (uid: string, index: number, fileType: number) => {
    const presentBill = presentBills.value.find(o => o.infoId === index)
    if(!presentBill) {
        showMessage('未找到对应账单！')
        return
    }
    if(fileType === 1) {
        presentBill.invoices = _.filter(presentBill.invoices, function(o){
            return o.uid != uid
        })
    } else if (fileType === 2) {
        presentBill.memos = _.filter(presentBill.memos, function(o){
            return o.uid != uid
        })
    } else if (fileType === 4) {
        presentBill.others = _.filter(presentBill.others, function(o){
            return o.uid != uid
        })
    }
}

watch(presentBills, (newValue)=> {
    let allTotal = 0
    let biddingTotal = 0
    if(newValue && newValue.length > 0) {
        newValue.map((item) => {
            let otherTotal = 0
            if(item.otherList && item.otherList.length > 0) {
                item.otherList.map((other) => {
                    otherTotal += ((other.price ?? 0) * (other.num ?? 0))
                })
            }
            item._otherTotal = round(otherTotal ?? 0) 
            item._billTotal = round((item.actualPrice ?? 0) * (item.actualNum ?? 0))
            item._biddingTotal = round((item.biddingPrice ?? 0) * (item.requireNum ?? 0))
            allTotal += item._billTotal + item._otherTotal
            biddingTotal += item._biddingTotal
        })
        presentTotal.value = round(allTotal)
        presentDiff.value = round(allTotal - biddingTotal)
    }
}, { immediate: true, deep: true })

//#endregion

//#region 其他

const otherActiveKey = ref<Array<string>>([]);

const otherBillActiveKey = ref<Array<string>>([]);

const otherBills = ref<Array<ICbOtherBill>>([])

const otherBillTypes = computed(() => SupplementTypeConstant.toArray('OTHER'))

const otherBillTotal = ref<number>(0)
const otherBillDiff = ref<number>(0)

const otherInvoicesUpload = (options:any, index: number) => {
    const otherBill = otherBills.value.find(o => o.infoId === index)
    if(!otherBill) {
        showMessage('未找到对应账单！')
        return
    }

    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        otherBill.invoices = otherBill.invoices ? [...otherBill.invoices, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const otherMemosUpload = (options:any, index: number) => {
    const otherBill = otherBills.value.find(o => o.infoId === index)
    if(!otherBill) {
        showMessage('未找到对应账单！')
        return
    }

    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        otherBill.memos = otherBill.memos ? [...otherBill.memos, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const otherOthersUpload = (options:any, index: number) => {
    const otherBill = otherBills.value.find(o => o.infoId === index)
    if(!otherBill) {
        showMessage('未找到对应账单！')
        return
    }

    uploadFile(options).then((res: any) => {
        const file = {
            ...options.file,
            name: options.file.name,
            url: res.path
        }
        otherBill.others = otherBill.others ? [...otherBill.others, file] : [file]

        options.onProgress(100)
        options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
}

const removeOtherFile = (uid: string, index: number, fileType: number) => {
    const otherBill = otherBills.value.find(o => o.infoId === index)
    if(!otherBill) {
        showMessage('未找到对应账单！')
        return
    }
    if(fileType === 1) {
        otherBill.invoices = _.filter(otherBill.invoices, function(o){
            return o.uid != uid
        })
    } else if (fileType === 2) {
        otherBill.memos = _.filter(otherBill.memos, function(o){
            return o.uid != uid
        })
    } else if (fileType === 4) {
        otherBill.others = _.filter(otherBill.others, function(o){
            return o.uid != uid
        })
    }
}

watch(otherBills, (newValue)=> {
    let allTotal = 0
    let biddingTotal = 0
    if(newValue && newValue.length > 0) {
        newValue.map((item) => {
            let otherTotal = 0
            if(item.otherList && item.otherList.length > 0) {
                item.otherList.map((other) => {
                    otherTotal += ((other.price ?? 0) * (other.num ?? 0))
                })
            }
            item._otherTotal = round(otherTotal)
            item._billTotal = round((item.actualPrice ?? 0) * (item.actualNum ?? 0))
            item._biddingTotal = round((item.biddingPrice ?? 0) * (item.requireNum ?? 0))
            allTotal += item._billTotal + item._otherTotal
            biddingTotal += item._biddingTotal
        })
        otherBillTotal.value = round(allTotal)
        otherBillDiff.value = round(allTotal - biddingTotal)
    }
}, { immediate: true, deep: true })

//#endregion

// #region 增删详情

const deleteDetail = (index: number ,key: string, billType: ConferenceType | 'FACILITY') => {
    if (billType === 'STAY') {
        const stayBill = stayBills.value.find(o => o.infoId === index)
        if (stayBill) {
            stayBill.detailList = stayBill.detailList!.filter(o => o.key != key)
        }
    } else if (billType === 'FACILITY') {
        const placeBill = placeBills.value.find(o => o.infoId === index)
        if (placeBill) {
            placeBill.facilityDetailList = placeBill.facilityDetailList!.filter(o => o.key != key)
        }
    } else if (billType === 'VEHICLE') {
        const vehicleBill = vehicleBills.value.find(o => o.infoId === index)
        if (vehicleBill) {
            vehicleBill.detailList = vehicleBill.detailList!.filter(o => o.key != key)
        }
    }
}

// #endregion

// #region 增删补充项目

const addSupplement = (key: number, billType: ConferenceType) => {
    if (billType === 'STAY') {
        const stayBill = stayBills.value.find(o => o.infoId === key)
        if (stayBill){
            !stayBill.otherList && (stayBill.otherList = []);
            stayBill!.otherList!.push({
                key: guid(),
            })
        }
    } else if (billType === 'CATERING') {
        const cateringBill = cateringBills.value.find(o => o.infoId === key)
        if (cateringBill) {
            !cateringBill.otherList && (cateringBill.otherList = []);
            cateringBill!.otherList!.push({
                key: guid(),
            })
        }
    } else if (billType === 'PLACE') {
        const placeBill = placeBills.value.find(o => o.infoId === key)
        if (placeBill) {
            !placeBill.otherList && (placeBill.otherList = []);
            placeBill!.otherList!.push({
                key: guid(),
            })
        }
    } else if (billType === 'VEHICLE') {
        const vehicleBill = vehicleBills.value.find(o => o.infoId === key)
        if (vehicleBill) {
            !vehicleBill.otherList && (vehicleBill.otherList = []);
            vehicleBill!.otherList!.push({
                key: guid(),
            })
        }
    } else if (billType === 'PRESENT') {
        const presentBill = presentBills.value.find(o => o.infoId === key)
        if (presentBill) {
            !presentBill.otherList && (presentBill.otherList = []);
            presentBill!.otherList!.push({
                key: guid(),
            })
        }
    } else if (billType === 'OTHER') {
        const otherBill = otherBills.value.find(o => o.infoId === key)
        if (otherBill) {
            !otherBill.otherList && (otherBill.otherList = []);
            otherBill!.otherList!.push({
                key: guid(),
            })
        }
    }
    
}

const deleteSupplement = (index: number ,key: string, billType: ConferenceType) => {
    if (billType === 'STAY') {
        const stayBill = stayBills.value.find(o => o.infoId === index)
        if (stayBill) {
            stayBill.otherList = stayBill.otherList!.filter(o => o.key != key)
        }
    } else if (billType === 'CATERING') {
        const cateringBill = cateringBills.value.find(o => o.infoId === index)
        if (cateringBill) {
            cateringBill.otherList = cateringBill.otherList!.filter(o => o.key != key)
        }
    } else if (billType === 'PLACE') {
        const placeBill = placeBills.value.find(o => o.infoId === index)
        if (placeBill) {
            placeBill.otherList = placeBill.otherList!.filter(o => o.key != key)
        }
    } else if (billType === 'VEHICLE') {
        const vehicleBill = vehicleBills.value.find(o => o.infoId === index)
        if (vehicleBill) {
            vehicleBill.otherList = vehicleBill.otherList!.filter(o => o.key != key)
        }
    } else if (billType === 'PRESENT') {
        const presentBill = presentBills.value.find(o => o.infoId === index)
        if (presentBill) {
            presentBill.otherList = presentBill.otherList!.filter(o => o.key != key)
        }
    } else if (billType === 'OTHER') {
        const otherBill = otherBills.value.find(o => o.infoId === index)
        if (otherBill) {
            otherBill.otherList = otherBill.otherList!.filter(o => o.key != key)
        }
    }
}

// #endregion

const computedDiff = (bill: number = 0, bidding: number = 0) => {
    return round(bill - bidding)
}

const checkOther = (others: Array<IBillSupplements>) => {
    for(let o = 0; o < others.length; o++) {
        const other = others[o]
        if(!other.projectName) {
            return '名称不能为空！'
        }
        if(!other.projectDate) {
            return '日期不能为空！'
        }
        if(!other.type && other.type !=0) {
            return '类型不能为空！'
        }
        if(!other.price) {
            return '单价不能为空或0！'
        }
        if(!other.num) {
            return '数量不能为空或0！'
        }
    }
    return ''
}

const getFilePathList = (data: IBaseBill) => {
    let filePathList: Array<IFileData> = []
    if(data.invoices && data.invoices.length > 0) {
        const paths = _.map(data.invoices, function(o) {
            return { 
                path: o.url,
                type: 1
            }
        })
        filePathList = [...filePathList, ...paths]
    }
    if(data.memos && data.memos.length > 0) {
        const paths = _.map(data.memos, function(o) {
            return { 
                path: o.url,
                type: 2
            }
        })
        filePathList = [...filePathList, ...paths]
    }
    if(data.contracts && data.contracts.length > 0) {
        const paths = _.map(data.contracts, function(o) {
            return { 
                path: o.url,
                type: 3
            }
        })
        filePathList = [...filePathList, ...paths]
    }
    if(data.others && data.others.length > 0) {
        const paths = _.map(data.others, function(o) {
            return { 
                path: o.url,
                type: 4
            }
        })
        filePathList = [...filePathList, ...paths]
    }
    return filePathList
}

const showMessage = ((text: string, isSuccess: boolean = false) => {
    if (isSuccess) {
        message.success({
            content:() => text,
            style: {
                marginTop: '50vh',
            }
        })
    } else {
        Modal.error({
            title: text,
        })
        // message.error({
        //     content:() => text,
        //     style: {
        //         marginTop: '50vh',
        //     }
        // })
    }
    
})

const loading = ref(false)

const save = async (isStaging: number) => {
    let cbStayBills: Array<IStaySaveData> =  []
    let cbCateringBills: Array<ICateringSaveData> =  [] 
    let cbPlaceBills: Array<IPlaceSaveData> =  []
    let cbVehicleBills: Array<IVehicleSaveData> =  []
    let cbPresentBills: Array<IPresentSaveData> =  []
    let cbOtherBills: Array<IOtherSaveData> =  []
    let cbServiceBills: Array<ICbServiceBill> =  []

    if(cbServiceBill.value) {
        if(!isStaging && !cbServiceBill.value.actualPrice && cbServiceBill.value.actualPrice != 0) {
            showMessage('请输入运营管理费实际总价！')
            return
        }
        cbServiceBills.push({
            actualPrice: cbServiceBill.value.actualPrice
        })
    }

    if(stayBills.value && stayBills.value.length > 0) {
        for(let i = 0; i < stayBills.value.length; i++) {
            const error = '住宿账单' + (i + 1)
            const stayBill = stayBills.value[i]
            let filePathList: Array<IFileData> = getFilePathList(stayBill as IBaseBill)
            if(!isStaging) {
                if(stayBill.actualPrice === null || stayBill.actualPrice ===undefined) {
                    showMessage(error + '实际单价不能为空！')
                    return
                }
                if(stayBill.actualNights === null || stayBill.actualNights === undefined) {
                    showMessage(error + '实际数量不能为空！')
                    return
                }
                const details = stayBill.detailList;
                const others = stayBill.otherList;
                if(details && details.length > 0) {
                    for(let d = 0; d < details.length; d++) {
                        const detail = details[d]
                        if(!detail.checkInDate) {
                            showMessage(error + '明细流水的入住日期不能为空！')
                            return
                        }
                        if(!detail.nights) {
                            showMessage(error + '明细流水的间夜数不能为空或0！')
                            return
                        }
                    }
                }
                
                if(others && others.length > 0) {
                    const msg = checkOther(others)
                    if(msg) {
                        showMessage(error + '补充项目的' + msg)
                        return
                    }
                }
                if(!stayBill.invoices && stayTotal.value > 0) {
                    showMessage(error + '未上传发票！')
                    return
                }
                if(!stayBill.memos && stayTotal.value > 0) {
                    showMessage(error + '未上传水单！')
                    return
                }
                if(!stayBill.contracts && isMiceType() && stayTotal.value > 0) {
                    showMessage(error + '未上传一手合同！')
                    return
                }
            }

            let detailList: Array<IStayBillDetail> = []

            if(stayBill.detailList && stayBill.detailList.length > 0) {
                stayBill.detailList.map(item => {
                    const detail: IStayBillDetail = {
                        ...item,
                        checkInDate: dayjs(item.checkInDate).format("YYYY-MM-DD HH:mm:ss")
                    }
                    detailList.push(detail)
                })
            }


            const cbStayBill : IStaySaveData = {
                stayInfoId: stayBill.infoId,
                actualPrice: stayBill.actualPrice,
                actualNights: stayBill.actualNights,
                billType: stayBill.billType,
                otherList:stayBill.otherList,
                detailList: detailList,
                filePathList
            }
            cbStayBills.push(cbStayBill)
        }
    }

    if(cateringBills.value && cateringBills.value.length > 0) {
        for(let i = 0; i < cateringBills.value.length; i++) {
            const error = '餐饮账单' + (i + 1)
            const cateringBill = cateringBills.value[i]
            let filePathList: Array<IFileData> = getFilePathList(cateringBill as IBaseBill)
            if(!isStaging) {
                if(cateringBill.actualPrice === null || cateringBill.actualPrice === undefined) {
                    showMessage(error + '实际单价/桌标不能为空！')
                    return
                }
                if(cateringBill.actualNum === null || cateringBill.actualNum === undefined) {
                    showMessage(error + '人数/总桌次不能为空！')
                    return
                }
                const others = cateringBill.otherList;
                if(others && others.length > 0) {
                    const msg = checkOther(others)
                    if(msg) {
                        showMessage(error + '补充项目的' + msg)
                        return
                    }
                }
                if(!cateringBill.invoices && cateringTotal.value > 0) {
                    showMessage(error + '未上传发票！')
                    return
                }
                if(!cateringBill.memos && cateringTotal.value > 0) {
                    showMessage(error + '未上传水单！')
                    return
                }
            }
            
            const cbCateringBill: ICateringSaveData = {
                cateringInfoId: cateringBill.infoId,
                actualPrice: cateringBill.actualPrice,
                actualNum: cateringBill.actualNum,
                billType: cateringBill.billType,
                otherList:cateringBill.otherList,
                filePathList
            }
            cbCateringBills.push(cbCateringBill)
        }
    }

    if(placeBills.value && placeBills.value.length > 0) {
        for(let i = 0; i < placeBills.value.length; i++) {
            const error = '场地账单' + (i + 1)
            const placeBill = placeBills.value[i]
            let filePathList: Array<IFileData> = getFilePathList(placeBill as IBaseBill)
            if(!isStaging) {
                if(placeBill.actualPrice === null || placeBill.actualPrice === undefined) {
                    showMessage(error + '场地实际单价不能为空！')
                    return
                }
                if(placeBill.meetingNum === null || placeBill.meetingNum === undefined) {
                    showMessage(error + '参会人数不能为空！')
                    return
                }
                if(placeBill.teaBreak === 1 && placeBill.teaUse === 1 && placeBill.teaNumber > 0 && placeBill.teaBidUnitPrice > 0) {
                    if(placeBill.billTeaNumber === null || placeBill.billTeaNumber === undefined) {
                        showMessage(error + '茶歇数量不能为空！')
                        return
                    }
                    if(placeBill.billTeaUnitPrice === null || placeBill.billTeaUnitPrice === undefined) {
                        showMessage(error + '茶歇单价不能为空！')
                        return
                    }
                }
                // if(placeBill.layout === 1 && !placeBill.billBuildCost) {
                //     showMessage(error + '搭建场地实际费用不能为空或0！')
                //     return
                // }
                const facilityDetails = placeBill.facilityDetailList ?? []
                const others = placeBill.otherList
                if(placeBill.facility && placeBill.facility.length > 0) {
                    if(!facilityDetails || facilityDetails.length === 0) {
                        showMessage('请添加' + error + '的明细流水！')
                        return
                    }
                }

                for(let d = 0; d < facilityDetails.length; d++) {
                    const detail = facilityDetails[d]
                    if(!detail.project) {
                        showMessage(error + '明细流水的设备设施项目不能为空！')
                        return
                    }
                    if(!detail.hall) {
                        showMessage(error + '明细流水的使用会议厅不能为空！')
                        return
                    }
                    // if(!detail.unitPrice) {
                    //     showMessage(error + '明细流水的单价不能为空或0！')
                    //     return
                    // }
                    if(detail.num === null || detail.num === undefined) {
                        showMessage(error + '明细流水的数量不能为空！')
                        return
                    }
                }
                
                if(others && others.length > 0) {
                    const msg = checkOther(others)
                    if(msg) {
                        showMessage(error + '补充项目的' + msg)
                        return
                    }
                }
                if(!placeBill.invoices && placeTotal.value > 0) {
                    showMessage(error + '未上传发票！')
                    return
                }
                if(!placeBill.memos && placeTotal.value > 0) {
                    showMessage(error + '未上传水单！')
                    return
                }
                if(!placeBill.contracts && isMiceType() && placeTotal.value > 0) {
                    showMessage(error + '未上传一手合同！')
                    return
                }
            }
            
            const cbPlaceBill : IPlaceSaveData = {
                placeInfoId: placeBill.infoId,
                actualPrice: placeBill.actualPrice,
                actualNum: 1,
                billType: placeBill.billType,
                meetingNum: placeBill.meetingNum,
                billTeaNumber: placeBill.billTeaNumber,
                billTeaUnitPrice: placeBill.billTeaUnitPrice,
                billBuildCost: placeBill.billBuildCost,
                billLedPrice: placeBill.billLedPrice,
                otherList:placeBill.otherList,
                facilityDetailList: placeBill.facilityDetailList,
                filePathList
            }
            cbPlaceBills.push(cbPlaceBill)
        }
    }

    if(vehicleBills.value && vehicleBills.value.length > 0) {
        for(let i = 0; i < vehicleBills.value.length; i++) {
            const error = '用车账单' + (i + 1)
            const vehicleBill = vehicleBills.value[i]
            let filePathList: Array<IFileData> = getFilePathList(vehicleBill as IBaseBill)
            if(!isStaging) {
                const details = vehicleBill.detailList;
                const others = vehicleBill.otherList;
                if(!details || details.length === 0) {
                    showMessage('请添加' + error + '的明细流水！')
                    return
                }
                for(let d = 0; d < details.length; d++) {
                    const detail = details[d]
                    // if(!detail.timeFrame) {
                    //     showMessage(error + '明细流水的用车时段不能为空！')
                    //     return
                    // }

                    if(!detail.timeFrameList) {
                        showMessage(error + '明细流水的用车时段不能为空！')
                        return
                    }

                    detail.timeFrameList[0] = dayjs(detail.timeFrameList[0]).format("YYYY-MM-DD HH:mm:ss")
                    detail.timeFrameList[1] = dayjs(detail.timeFrameList[1]).format("YYYY-MM-DD HH:mm:ss")
                    
                    if(!detail.carType) {
                        showMessage(error + '明细流水的车辆类型不能为空！')
                        return
                    }
                    if(!detail.brand) {
                        showMessage(error + '明细流水的品牌不能为空！')
                        return
                    }
                    if(detail.carAge === null || detail.carAge === undefined) {
                        showMessage(error + '明细流水的车龄不能为空！')
                        return
                    }
                    if(!detail.seats) {
                        showMessage(error + '明细流水的座位数不能为空！')
                        return
                    }
                    if(!detail.carNo) {
                        showMessage(error + '明细流水的车牌号不能为空！')
                        return
                    }
                    if(!detail.driver) {
                        showMessage(error + '明细流水的驾驶员不能为空！')
                        return
                    }
                    if(detail.kmNum === null || detail.kmNum === undefined) {
                        showMessage(error + '明细流水的公里数不能为空！')
                        return
                    }
                    if(detail.kmPrice === null || detail.kmPrice === undefined) {
                        showMessage(error + '明细流水的公里费用不能为空！')
                        return
                    }
                    if(detail.toll == null || detail.toll == undefined) {
                        showMessage(error + '明细流水的过路费不能为空！')
                        return
                    }
                    if(!detail.practical) {
                        showMessage(error + '明细流水的实际去向不能为空！')
                        return
                    }
                }
                if(others && others.length > 0) {
                    const msg = checkOther(others)
                    if(msg) {
                        showMessage(error + '补充项目的' + msg)
                        return
                    }
                }
                if(!vehicleBill.invoices && vehicleTotal.value > 0) {
                    showMessage(error + '未上传发票！')
                    return
                }
                if(!vehicleBill.memos && vehicleTotal.value > 0) {
                    showMessage(error + '未上传水单！')
                    return
                }
            }
            
            const cbVehicleBill: IVehicleSaveData = {
                vehicleInfoId: vehicleBill.infoId,
                actualPrice: vehicleBill.actualPrice,
                actualNum: vehicleBill.actualNum,
                billType: vehicleBill.billType,
                otherList:vehicleBill.otherList,
                detailList: vehicleBill.detailList,
                filePathList
            }
            cbVehicleBills.push(cbVehicleBill)
        }
    }

    if(presentBills.value && presentBills.value.length > 0) {
        for(let i = 0; i < presentBills.value.length; i++) {
            const error = '礼品账单' + (i + 1)
            const presentBill = presentBills.value[i]
            let filePathList: Array<IFileData> = getFilePathList(presentBill as IBaseBill)
            if(!isStaging) {
                if(presentBill.actualPrice === null || presentBill.actualPrice === undefined) {
                    showMessage(error + '实际单价不能为空！')
                    return
                }
                if(presentBill.actualNum === null || presentBill.actualNum === undefined) {
                    showMessage(error + '实际人数不能为空！')
                    return
                }
                const others = presentBill.otherList;
                if(others && others.length > 0) {
                    const msg = checkOther(others)
                    if(msg) {
                        showMessage(error + '补充项目的' + msg)
                        return
                    }
                }
                if(!presentBill.invoices && presentTotal.value > 0) {
                    showMessage(error + '未上传发票！')
                    return
                }
                if(!presentBill.memos && presentTotal.value > 0) {
                    showMessage(error + '未上传水单！')
                    return
                }
            }
            
            const cbPresentBill: IPresentSaveData = {
                presentInfoId: presentBill.infoId,
                actualPrice: presentBill.actualPrice,
                actualNum: presentBill.actualNum,
                billType: presentBill.billType,
                otherList:presentBill.otherList,
                filePathList
            }
            cbPresentBills.push(cbPresentBill)
        }
    }

    if(otherBills.value && otherBills.value.length > 0) {
        for(let i = 0; i < otherBills.value.length; i++) {
            const error = '其他账单' + (i + 1)
            const otherBill = otherBills.value[i]
            let filePathList: Array<IFileData> = getFilePathList(otherBill as IBaseBill)
            if(!isStaging) {
                if(otherBill.actualPrice === null || otherBill.actualPrice === undefined) {
                    showMessage(error + '实际单价不能为空！')
                    return
                }
                if(otherBill.actualNum === null || otherBill.actualNum === undefined) {
                    showMessage(error + '实际人数不能为空！')
                    return
                }
                const others = otherBill.otherList;
                if(others && others.length > 0) {
                    const msg = checkOther(others)
                    if(msg) {
                        showMessage(error + '补充项目的' + msg)
                        return
                    }
                }
                if(!otherBill.invoices && otherBillTotal.value > 0) {
                    showMessage(error + '未上传发票！')
                    return
                }
                if(!otherBill.memos && otherBillTotal.value > 0) {
                    showMessage(error + '未上传水单！')
                    return
                }
            }
            
            const cbOtherBill: IOtherSaveData = {
                otherInfoId: otherBill.infoId,
                actualPrice: otherBill.actualPrice,
                actualNum: otherBill.actualNum,
                billType: otherBill.billType,
                otherList:otherBill.otherList,
                filePathList
            }
            cbOtherBills.push(cbOtherBill)
        }
    }

    

    if(!isStaging && fileList.value.length === 0) {
        showMessage('请上传会展结算单！')
        return
    }
    console.log(fileList.value)
    let settlementBillPath = fileList.value.length > 0 ?  fileList.value[0].relativeUrl : ''

    const saveData: IBillSaveData = {
        code: code.value,
        isStaging,
        cbStayBills,
        cbCateringBills,
        cbPlaceBills,
        cbVehicleBills,
        cbPresentBills,
        cbOtherBills,
        cbServiceBills,
        settlementBillPath
    }

    spinning.value = true
    const res = await billApi.saveBill(saveData, (error)=> {
        spinning.value = false
        showMessage(error.message)
    })
    if(res && res.success) {
        showMessage('操作成功！', true)
        await getBill()
        window.parent.postMessage({
            type: 'successToLink',
            message: '成功！'
        }, communicationUrl);
        spinning.value = false
    }

    
}

// 暂存
const Staging = () => {
    save(1)
}

// 直接提交
const onSubmit = () => {
    save(0)
}


// 校验实际单价不可超竞价单价
const checkBiddingPrice = function(rule, value, callback, biddingPrice){
    if (value > biddingPrice) {
        return Promise.reject("不能超过竞标价")
    }
    return Promise.resolve();
}

</script>

<template>
    <h-spin :spinning="spinning">
        <h-card size="small" title="会议账单" class="bill-card" >
            <template #extra>
                <span class="total font-size-12">竞：{{ computedDiff(BillsTotal, BillsDiff) }}元</span>
                <span class="total font-size-12">账：{{ BillsTotal }}元</span>
                <span class="font-size-12">差：<span :class="{'negative': BillsDiff > 0, 'positiv': BillsDiff <= 0 }">{{ BillsDiff }}元</span></span>
            </template>
            <h-tabs type="card" size="large">
                <h-tab-pane key="1" v-if="stayBills && stayBills.length > 0">
                    <template #tab>
                        <div class="tab-title flex">
                            住宿账单
                        </div>
                        <div class="tab-total flex font-size-12">
                            <span >总：{{ stayTotal }}元</span>
                            <span>差：<span :class="{'negative': stayDiff > 0, 'positiv': stayDiff <= 0 }">{{ stayDiff }}元</span></span>
                        </div>
                    </template>
                    
                    <h-collapse v-model:activeKey="stayActiveKey">
                        <h-collapse-panel  :header="'账单' + (index + 1)" style="background-color: #fff;margin-bottom:28px;" 
                            v-for="(stayBill, index) in stayBills" :key="'stayBill-' + index">
                            <template #extra>
                                <span class="total font-size-12">竞：{{ stayBill._biddingTotal ?? 0}}元</span>
                                <span class="total font-size-12">账：{{ (stayBill._billTotal ?? 0) + (stayBill._otherTotal ?? 0) }}元</span>
                                <span class="font-size-12">
                                    差：<span  :class="{'negative': computedDiff((stayBill._billTotal ?? 0) + (stayBill._otherTotal ?? 0), stayBill._biddingTotal) > 0, 'positiv': computedDiff((stayBill._billTotal ?? 0) + (stayBill._otherTotal ?? 0), stayBill._biddingTotal) <= 0 }" >{{ computedDiff((stayBill._billTotal ?? 0) + (stayBill._otherTotal ?? 0), stayBill._biddingTotal) }}元</span>
                                </span>
                            </template>
                            <h-form
                                ref="formRef"
                                style="margin-top: 20px;"
                                :model="stayBill"
                            >
                                <h-row>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg" >
                                        <h-form-item
                                            label="酒店名称"
                                        >
                                            {{ stayBill.supplier }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg" >
                                        <h-form-item
                                            label="开票全称"
                                        >
                                            {{ stayBill.billingFullName }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="星级"
                                        >
                                            {{ stayBill.level }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="位置"
                                        >
                                            {{ stayBill.address }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="房型"
                                        >
                                            {{ stayBill.roomType }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="间夜数"
                                        >
                                            {{ stayBill.nights + '间夜' }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="房间数"
                                        >
                                            {{ stayBill.roomNum }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="日期"
                                        >
                                            {{ dayjs(stayBill.checkInDate).format("YYYY-MM-DD") + '至' + dayjs(stayBill.checkOutDate).format("YYYY-MM-DD") }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="竞价单价"
                                        >
                                            {{ stayBill.biddingPrice }}元
                                        </h-form-item>
                                    </h-col>
                                    
                                </h-row>
                                <h-row>
                                    <h-col :sm="input_sm" :md="input_md" :lg="input_lg">
                                        <h-form-item
                                            label="实际单价"
                                            name="actualPrice"
                                            :rules="[{
                                                required: true,
                                                message: '请输入实际单价',

                                            },{
                                                message: '不能超过竞标价', 
                                                validator: (rule, value, callback) => checkBiddingPrice(rule, value, callback, stayBill.biddingPrice)
                                            }]"
                                        >
                                            <h-input-number :precision="2" v-model:value="stayBill.actualPrice" :min="0" placeholder="实际单价" style="width: 90%" addon-after="元/间夜" />
                                        </h-form-item>
                                    </h-col>
                                    <h-col :sm="input_sm" :md="input_md" :lg="input_lg">
                                        <h-tooltip title="明细流水中间夜数的合计">
                                            <h-form-item
                                                label="实际数量"
                                                :name="['stayBills', index, 'actualNights']"
                                            >
                                                <h-input-number v-model:value="stayBill.actualNights" :min="0" placeholder="实际数量" disabled style="width: 100%" addon-after="间夜" />
                                            </h-form-item>
                                        </h-tooltip>
                                    </h-col>
                                </h-row>
                            </h-form>
                            
                            <h-collapse v-model:activeKey="stayBillActiveKey" style="background: rgb(255, 255, 255)">
                                <h-collapse-panel :key="'stayBill-' + 'detail-' + index" header="明细流水" >
                                    <template #extra>
                                        <span class="total font-size-12">合计：{{ stayBill._billTotal }}元</span>
                                    </template>
                                    <div class="table">
                                        <h-table :columns="stayBillDetailColumns" :data-source="stayBill.detailList" :pagination="false" bordered>
                                            <template #emptyText>
                                                <a-empty :image="simpleImage" description="点击下方按钮添加"/>
                                            </template>
                                            <template #bodyCell="{ column, text, record, index }">
                                                <template v-if="column.dataIndex === 'roomType'">
                                                    {{ text }}
                                                </template>
                                                <template v-if="column.dataIndex === 'price'">
                                                    {{ text }}元
                                                </template>
                                                <template v-if="column.dataIndex === 'checkInDate'">
                                                    <div>
                                                        <h-date-picker v-model:value="stayBill.detailList![index].checkInDate" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'num'">
                                                    <div>
                                                        <h-input-number v-model:value="stayBill.detailList![index].nights" :min="0" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-else-if="column.dataIndex === 'operation'">
                                                    <h-button type="primary" shape="circle" danger @click="deleteDetail(stayBill.infoId!, record.key, 'STAY')">
                                                        <template #icon><DeleteOutlined /></template>
                                                    </h-button>
                                                </template>
                                            </template>
                                        </h-table>
                                        <div class="add-detil">
                                            <h-button type="dashed" block @click="addStayDetil(stayBill.infoId!, stayBill.roomType)">
                                                <template #icon>
                                                    <plus-outlined />
                                                </template>
                                                添加明细
                                            </h-button>
                                        </div>
                                    </div>
                                    
                                </h-collapse-panel>
                                <h-collapse-panel :key="'stayBill-' + 'other-' + index" header="补充项目" >
                                    <template #extra>
                                        <span class="total font-size-12">合计：{{ stayBill._otherTotal }}元</span>
                                    </template>
                                    <div class="table">
                                        <h-table :columns="supplementColumns" :data-source="stayBill.otherList" :pagination="false" bordered :scroll="{ x: 1200 }">
                                            <template #emptyText>
                                                <a-empty :image="simpleImage" description="点击下方按钮添加"/>
                                            </template>
                                            <template #bodyCell="{ column, text, record, index }">
                                                <template v-if="column.dataIndex === 'name'">
                                                    <div>
                                                        <h-input v-model:value="stayBill.otherList![index].projectName" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'type'">
                                                    <h-select
                                                        v-model:value="stayBill.otherList![index].type"
                                                        style="width: 100%"
                                                        allow-clear
                                                    >
                                                        <h-select-option v-for="(item, typeIndex) in stayBillTypes" :key="typeIndex" :value="item?.code">{{ item?.desc }}</h-select-option>
                                                    </h-select>
                                                </template>
                                                <template v-if="column.dataIndex === 'date'">
                                                    <div>
                                                        <h-date-picker v-model:value="stayBill.otherList![index].projectDate" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'desc'">
                                                    <div>
                                                        <h-input v-model:value="stayBill.otherList![index].desc" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'num'">
                                                    <div>
                                                        <h-input-number v-model:value="stayBill.otherList![index].num" :min="0" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'price'">
                                                    <div>
                                                        <h-input-number :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" v-model:value="stayBill.otherList![index].price" :min="0" style="width: 100%" addon-after="元" />
                                                    </div>
                                                </template>
                                                <template v-else-if="column.dataIndex === 'operation'">
                                                    <h-button type="primary" shape="circle" danger @click="deleteSupplement(stayBill.infoId!, record.key, 'STAY')">
                                                        <template #icon><DeleteOutlined /></template>
                                                    </h-button>
                                                </template>
                                            </template>
                                        </h-table>
                                        <div class="add-detil">
                                            <h-button type="dashed" block @click="addSupplement(stayBill.infoId!, 'STAY')">
                                                <template #icon>
                                                    <plus-outlined />
                                                </template>
                                                添加补充项目
                                            </h-button>
                                        </div>
                                    </div>
                                </h-collapse-panel>
                                <h-collapse-panel :key="'stayBill-' + 'file-' + index" header="附件" >
                                    <div class="file-upload flex">
                                        <div class="flex upload-title required">
                                            发票上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="stayBill.invoices"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => stayInvoicesUpload(option, stayBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                发票上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in stayBill.invoices" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removeStayFile(file.uid, stayBill.infoId!, 1)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="file-upload flex">
                                        <div class="flex upload-title required">
                                            水单上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="stayBill.memos"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => stayMemosUpload(option, stayBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                水单上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in stayBill.memos" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removeStayFile(file.uid, stayBill.infoId!, 2)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="file-upload flex" v-if="isMiceType()">
                                        <div class="flex upload-title required">
                                            一手合同上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="stayBill.contracts"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => stayContractsUpload(option, stayBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                一手合同上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in stayBill.contracts" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removeStayFile(file.uid, stayBill.infoId!, 3)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="file-upload flex">
                                        <div class="flex upload-title">
                                            其他上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="stayBill.others"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => stayOthersUpload(option, stayBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                其他上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in stayBill.others" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removeStayFile(file.uid, stayBill.infoId!, 4)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                </h-collapse-panel>
                            </h-collapse>
                        </h-collapse-panel>
                    </h-collapse>
                </h-tab-pane>
                <h-tab-pane key="2" v-if="cateringBills && cateringBills.length > 0">
                    <template #tab>
                        <div class="tab-title flex">
                            餐饮账单
                        </div>
                        <div class="tab-total flex font-size-12">
                            <span >总：{{ cateringTotal }}元</span>
                            <span>差：<span :class="{'negative': cateringDiff > 0, 'positiv': cateringDiff <= 0 }">{{ cateringDiff }}元</span></span>
                        </div>
                    </template>
                    
                    
                    <h-collapse v-model:activeKey="cateringActiveKey">
                        <h-collapse-panel  :header="'账单' + (index + 1)" style="background-color: #fff;margin-bottom:28px;" 
                            v-for="(cateringBill, index) in cateringBills" :key="'cateringBill-' + index">
                            <template #extra>
                                <span class="total font-size-12">竞：{{ cateringBill._biddingTotal }}元</span>
                                <span class="total font-size-12">账：{{ (cateringBill._billTotal ?? 0) + (cateringBill._otherTotal ?? 0)  }}元</span>
                                <span class="font-size-12">
                                    差：<span  :class="{'negative': computedDiff((cateringBill._billTotal ?? 0) + (cateringBill._otherTotal ?? 0), cateringBill._biddingTotal) > 0, 'positiv': computedDiff((cateringBill._billTotal ?? 0) + (cateringBill._otherTotal ?? 0), cateringBill._biddingTotal) <= 0 }" >{{ computedDiff((cateringBill._billTotal ?? 0) + (cateringBill._otherTotal ?? 0), cateringBill._biddingTotal) }}元</span>
                                </span>
                            </template>

                            <h-form
                                ref="formRef"
                                style="margin-top: 20px;"
                                :model="cateringBill"
                            >
                                <h-row>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="酒店名称"
                                        >
                                            {{ cateringBill.supplier }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="餐饮日期"
                                        >
                                            {{ dayjs(cateringBill.eatDate).format("YYYY-MM-DD") }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="餐类"
                                        >
                                            {{ cateringBill.cateType && CateTypeConstant.ofType(cateringBill.cateType)?.desc }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="人数"
                                        >
                                            {{ cateringBill.personNum }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="竞价单价"
                                        >
                                            {{ cateringBill.biddingPrice }}元
                                        </h-form-item>
                                    </h-col>
                                </h-row>
                                <h-row>
                                    <h-col :sm="input_sm" :md="input_md" :lg="input_lg">
                                        <h-form-item
                                            label="实际单价/桌标"
                                            name="actualPrice"
                                            :rules="[{
                                                required: true,
                                                message: '请输入实际单价/桌标',
                                            },{
                                                message: '不能超过竞标价', 
                                                validator: (rule, value, callback) => checkBiddingPrice(rule, value, callback, cateringBill.biddingPrice)
                                            }]"
                                        >
                                            <h-input-number :precision="2" v-model:value="cateringBill.actualPrice" placeholder="实际单价/桌标" style="width: 90%" :min="0" />
                                        </h-form-item>
                                    </h-col>
                                    <h-col :sm="input_sm" :md="input_md" :lg="input_lg">
                                        <h-form-item
                                            label="人数/总桌次"
                                            name="actualNum"
                                            :rules="{
                                                required: true,
                                                message: '请输入人数/总桌次',
                                            }"
                                        >
                                            <h-input-number v-model:value="cateringBill.actualNum" placeholder="人数/总桌次" style="width: 90%" :min="0" />
                                        </h-form-item>
                                    </h-col>
                                </h-row>
                            </h-form>
                            
                            <h-collapse v-model:activeKey="cateringBillActiveKey" style="background: rgb(255, 255, 255)">
                                <h-collapse-panel :key="'cateringBill-' + 'other-' + index" header="补充项目" >
                                    <template #extra>
                                        <span class="total font-size-12">合计：{{ cateringBill._otherTotal }}元</span>
                                    </template>
                                    <div class="table">
                                        <h-table :columns="supplementColumns" :data-source="cateringBill.otherList" :pagination="false" bordered :scroll="{ x: 1200 }">
                                            <template #emptyText>
                                                <a-empty :image="simpleImage" description="点击下方按钮添加"/>
                                            </template>
                                            <template #bodyCell="{ column, text, record, index }">
                                                <template v-if="column.dataIndex === 'name'">
                                                    <div>
                                                        <h-input v-model:value="cateringBill.otherList![index].projectName" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'type'">
                                                    <h-select
                                                        v-model:value="cateringBill.otherList![index].type"
                                                        style="width: 100%"
                                                        allow-clear
                                                    >
                                                        <h-select-option v-for="(item, typeIndex) in cateringBillTypes" :key="typeIndex" :value="item?.code">{{ item?.desc }}</h-select-option>
                                                    </h-select>
                                                </template>
                                                <template v-if="column.dataIndex === 'date'">
                                                    <div>
                                                        <h-date-picker v-model:value="cateringBill.otherList![index].projectDate" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'desc'">
                                                    <div>
                                                        <h-input v-model:value="cateringBill.otherList![index].desc" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'num'">
                                                    <div>
                                                        <h-input-number v-model:value="cateringBill.otherList![index].num" :min="0" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'price'">
                                                    <div>
                                                        <h-input-number :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" v-model:value="cateringBill.otherList![index].price" :min="0" style="width: 100%" addon-after="元" />
                                                    </div>
                                                </template>
                                                <template v-else-if="column.dataIndex === 'operation'">
                                                    <h-button type="primary" shape="circle" danger @click="deleteSupplement(cateringBill.infoId!, record.key, 'CATERING')">
                                                        <template #icon><DeleteOutlined /></template>
                                                    </h-button>
                                                </template>
                                            </template>
                                        </h-table>
                                        <div class="add-detil">
                                            <h-button type="dashed" block @click="addSupplement(cateringBill.infoId!, 'CATERING')">
                                                <template #icon>
                                                    <plus-outlined />
                                                </template>
                                                添加补充项目
                                            </h-button>
                                        </div>
                                    </div>
                                </h-collapse-panel>
                                <h-collapse-panel :key="'cateringBill-' + 'file-' + index" header="附件" >
                                    <div class="file-upload flex">
                                        <div class="flex upload-title required">
                                            发票上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="cateringBill.invoices"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => cateringInvoicesUpload(option, cateringBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                发票上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in cateringBill.invoices" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removeCateringFile(file.uid, cateringBill.infoId!, 1)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="file-upload flex">
                                        <div class="flex upload-title required">
                                            水单上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="cateringBill.memos"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => cateringMemosUpload(option, cateringBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                水单上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in cateringBill.memos" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removeCateringFile(file.uid, cateringBill.infoId!, 2)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="file-upload flex">
                                        <div class="flex upload-title">
                                            其他上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="cateringBill.others"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => cateringOthersUpload(option, cateringBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                其他上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in cateringBill.others" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removeCateringFile(file.uid, cateringBill.infoId!, 4)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                </h-collapse-panel>
                            </h-collapse>
                        </h-collapse-panel>
                    </h-collapse>
                    

                </h-tab-pane>
                <h-tab-pane key="3" v-if="placeBills && placeBills.length > 0">
                    <template #tab>
                        <div class="tab-title flex">
                            会场账单
                        </div>
                        <div class="tab-total flex font-size-12">
                            <span >总：{{ placeTotal }}元</span>
                            <span>差：<span :class="{'negative': placeDiff > 0, 'positiv': placeDiff <= 0 }">{{ placeDiff }}元</span></span>
                        </div>
                    </template>

                    <h-collapse v-model:activeKey="placeActiveKey">
                        <h-collapse-panel  :header="'账单' + (index + 1)" style="background-color: #fff;margin-bottom:28px;" 
                            v-for="(placeBill, index) in placeBills" :key="'placeBill-' + index">
                            <template #extra>
                                <span class="total font-size-12">竞：{{ placeBill._biddingTotal ?? 0}}元</span>
                                <span class="total font-size-12">账：{{ (placeBill._billTotal ?? 0) + (placeBill._otherTotal ?? 0) }}元</span>
                                <span class="font-size-12">
                                    差：<span  :class="{'negative': computedDiff((placeBill._billTotal ?? 0) + (placeBill._otherTotal ?? 0), placeBill._biddingTotal) > 0, 'positiv': computedDiff((placeBill._billTotal ?? 0) + (placeBill._otherTotal ?? 0), placeBill._biddingTotal) <= 0 }" >{{ computedDiff((placeBill._billTotal ?? 0) + (placeBill._otherTotal ?? 0), placeBill._biddingTotal) }}元</span>
                                </span>
                            </template>
                            <h-form
                                ref="formRef"
                                style="margin-top: 20px;"
                                :model="placeBill"
                            >
                                <h-row>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="酒店名称"
                                        >
                                            {{ placeBill.supplier }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="酒店开票全称"
                                        >
                                            {{ placeBill.billingFullName }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="场地"
                                        >
                                            {{ placeBill.area }}
                                        </h-form-item>
                                    </h-col>

                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="可容纳人数"
                                        >
                                            {{ placeBill.capacity }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="场地实际数量"
                                            name="actualNum"
                                        >
                                            1
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="竞价单价"
                                        >
                                            {{ placeBill.biddingPrice }}元
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="场地实际单价"
                                            name="actualPrice"
                                            :rules="[{
                                                required: true,
                                                message: '请输入实际单价',
                                            },{
                                                message: '不能超过竞标价', 
                                                validator: (rule, value, callback) => checkBiddingPrice(rule, value, callback, placeBill.biddingPrice)
                                            }]"
                                        >
                                            <h-input-number :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" v-model:value="placeBill.actualPrice" placeholder="实际单价" style="width: 90%" :min="0" addon-after="元" />
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="参会人数"
                                            name="meetingNum"
                                            :rules="{
                                                required: true,
                                                message: '请输入参会人数',
                                            }"
                                        >
                                            <h-input-number v-model:value="placeBill.meetingNum" placeholder="参会人数" :min="0" style="width: 90%" />
                                        </h-form-item>
                                    </h-col>
                                </h-row>
                                <h-row>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="面积"
                                        >
                                            {{ placeBill.area }}平米
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="层高"
                                        >
                                            {{ placeBill.floor }}米
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="会议日期"
                                        >
                                            {{ dayjs(placeBill.holdDate).format("YYYY-MM-DD") }}{{ HoldTimeTypeConstant.ofType(placeBill.holdTime)?.desc }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="桌型"
                                        >
                                            {{ placeBill.tableType }}
                                        </h-form-item>
                                    </h-col>
                                </h-row>
                                <h-row>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="布展"
                                        >
                                            {{ placeBill.layout === 1 ? '有布展' : '无布展' }}
                                        </h-form-item>
                                    </h-col>
                                    <template v-if="placeBill.layout === 1">
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="会场搭建费用"
                                            >
                                                {{ placeBill.bidBuildCost }}元
                                            </h-form-item>
                                        </h-col>
                                        <h-col :sm="input_sm" :md="input_md" :lg="input_lg" v-if="placeBill.layout === 1">
                                            <h-form-item
                                                label="搭建场地实际费用"
                                                name="billBuildCost"
                                                :rules="[{
                                                    required: true,
                                                    message: '请输入搭建场地实际费用',
                                                },{
                                                    message: '不能超过竞标价', 
                                                    validator: (rule, value, callback) => checkBiddingPrice(rule, value, callback, placeBill.bidBuildCost)
                                                }]"
                                            >
                                                <h-input-number :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" v-model:value="placeBill.billBuildCost" :min="0" placeholder="搭建场地实际费用" style="width: 90%" addon-after="元" />
                                            </h-form-item>
                                        </h-col>
                                        <h-col :sm="24" :md="12">
                                            <h-form-item
                                                label="布展时间"
                                            >
                                                {{ placeBill.layoutTimeLimitBegin}}~{{ placeBill.layoutTimeLimitEnd}}
                                            </h-form-item>
                                        </h-col>
                                    </template>
                                </h-row>
                                <h-row>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="茶歇"
                                        >
                                            {{ placeBill.teaBreak === 1 ? '有茶歇' : '无茶歇' }}
                                        </h-form-item>
                                    </h-col>
                                    <template v-if="placeBill.teaBreak === 1">
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="茶歇使用"
                                            >
                                                {{ TeaUseTypeConstant.ofType(placeBill.teaUse)?.desc }}
                                            </h-form-item>
                                        </h-col>
                                        <template v-if="placeBill.teaUse === 1">
                                            <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                                <h-form-item
                                                    label="茶歇单价"
                                                >
                                                    {{ placeBill.teaBidUnitPrice }}元
                                                </h-form-item>
                                            </h-col>
                                            <h-col :sm="sm" :md="md" :lg="lg" v-if="placeBill.teaBreak === 1 && placeBill.teaUse === 1">
                                                <h-form-item
                                                    label="实际茶歇单价"
                                                    name="billTeaUnitPrice"
                                                    :rules="[{
                                                        required: true,
                                                        message: '请输入茶歇单价',
                                                    },{
                                                        message: '不能超过竞标价', 
                                                        validator: (rule, value, callback) => checkBiddingPrice(rule, value, callback, placeBill.teaBidUnitPrice)
                                                    }]"
                                                >
                                                    <h-input-number :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" v-model:value="placeBill.billTeaUnitPrice" :min="0" placeholder="茶歇单价" style="width: 90%" addon-after="元" />
                                                </h-form-item>
                                            </h-col>
                                            <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                                <h-form-item
                                                    label="使用人数"
                                                >
                                                    {{ placeBill.teaNumber }}
                                                </h-form-item>
                                            </h-col>
                                            <h-col :sm="sm" :md="md" :lg="lg" v-if="placeBill.teaBreak === 1 && placeBill.teaUse === 1">
                                                <h-form-item
                                                    label="实际人数"
                                                    name="billTeaNumber"
                                                    :rules="{
                                                        required: true,
                                                        message: '请输入茶歇数量',
                                                    }"
                                                >
                                                    <h-input-number v-model:value="placeBill.billTeaNumber" :min="0" placeholder="茶歇数量" style="width: 90%" />
                                                </h-form-item>
                                            </h-col>
                                        </template>
                                    </template>
                                </h-row>
                                <h-row>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="是否需要LED"
                                        >
                                            {{ placeBill.isLed === 1? '是' : '否' }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="LED使用"
                                        >
                                            {{ placeBill.ledUse === 1? '收费' : '赠送' }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="LED价格"
                                        >
                                            {{ placeBill.ledBidPrice }}元
                                        </h-form-item>
                                    </h-col>
                                    <h-col :sm="xs" :md="sm" :lg="lg">
                                        <h-form-item
                                            label="实际LED价格"
                                            name="billLedPrice"
                                            :rules="[{
                                                required: true,
                                                message: '请输入实际价格',
                                            },{
                                                message: '不能超过竞标价', 
                                                validator: (rule, value, callback) => checkBiddingPrice(rule, value, callback, placeBill.ledBidPrice)
                                            }]"
                                        >
                                            <h-input-number :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" v-model:value="placeBill.billLedPrice" placeholder="实际价格" style="width: 90%" :min="0" addon-after="元" />
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="LED规格"
                                        >
                                            {{ placeBill.ledModel }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="LED来源"
                                        >
                                            {{ placeBill.ledSource && LedTypeConstant.ofType(placeBill.ledSource)?.desc }}
                                        </h-form-item>
                                    </h-col>
                                </h-row>    
                                <h-row>
                                    <template v-for="(f, fIndex) in placeBill.facility" :key="fIndex">
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="设施名称"
                                            >
                                                {{ f.name }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="数量"
                                            >
                                                {{ f.num }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :md="8" :lg="12">
                                            <h-form-item
                                                label="价格"
                                            >
                                                {{ f.price }}元
                                            </h-form-item>
                                        </h-col>
                                    </template>
                                </h-row>
                            </h-form>
                            
                            <h-collapse v-model:activeKey="placeBillActiveKey" style="background: rgb(255, 255, 255)">
                                <h-collapse-panel :key="'placeBill-' + 'facility-' + index" header="设施明细流水" >
                                    <template #extra>
                                        <span class="total font-size-12">合计：{{ placeBill._facilityTotal }}元</span>
                                    </template>
                                    <div class="table">
                                        <h-table :columns="placeBillFacilityDetailColumns" :data-source="placeBill.facilityDetailList" :pagination="false" bordered :scroll="{ x: 900 }">
                                            <template #emptyText>
                                                <a-empty :image="simpleImage" description="点击下方按钮添加"/>
                                            </template>
                                            <template #bodyCell="{ column, text, record, index }">
                                                <template v-if="column.dataIndex === 'project'">
                                                    <div>
                                                        <h-input v-model:value="placeBill.facilityDetailList![index].project" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'hall'">
                                                    <div>
                                                        <h-input v-model:value="placeBill.facilityDetailList![index].hall" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'unitPrice'">
                                                    <div>
                                                        <h-input-number :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" v-model:value="placeBill.facilityDetailList![index].unitPrice" :min="0" style="width: 100%" addon-after="元" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'num'">
                                                    <div>
                                                        <h-input-number v-model:value="placeBill.facilityDetailList![index].num" :min="0" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'remark'">
                                                    <div>
                                                        <h-input v-model:value="placeBill.facilityDetailList![index].remark" />
                                                    </div>
                                                </template>
                                                <template v-else-if="column.dataIndex === 'operation'">
                                                    <h-button type="primary" shape="circle" danger @click="deleteDetail(placeBill.infoId!, record.key, 'FACILITY')">
                                                        <template #icon><DeleteOutlined /></template>
                                                    </h-button>
                                                </template>
                                            </template>
                                        </h-table>
                                        <div class="add-detil">
                                            <h-button type="dashed" block @click="addplaceBillDetilFacility(placeBill.infoId!)">
                                                <template #icon>
                                                    <plus-outlined />
                                                </template>
                                                添加明细
                                            </h-button>
                                        </div>
                                    </div>
                                    
                                </h-collapse-panel>
                                <h-collapse-panel :key="'placeBill-' + 'other-' + index" header="补充项目" >
                                    <template #extra>
                                        <span class="total font-size-12">合计：{{ placeBill._otherTotal }}元</span>
                                    </template>
                                    <div class="table">
                                        <h-table :columns="supplementColumns" :data-source="placeBill.otherList" :pagination="false" bordered :scroll="{ x: 1200 }">
                                            <template #emptyText>
                                                <a-empty :image="simpleImage" description="点击下方按钮添加"/>
                                            </template>
                                            <template #bodyCell="{ column, text, record, index }">
                                                <template v-if="column.dataIndex === 'name'">
                                                    <div>
                                                        <h-input v-model:value="placeBill.otherList![index].projectName" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'type'">
                                                    <h-select
                                                        v-model:value="placeBill.otherList![index].type"
                                                        style="width: 100%"
                                                        allow-clear
                                                    >
                                                        <h-select-option v-for="(item, typeIndex) in placeBillTypes" :key="typeIndex" :value="item?.code">{{ item?.desc }}</h-select-option>
                                                    </h-select>
                                                </template>
                                                <template v-if="column.dataIndex === 'date'">
                                                    <div>
                                                        <h-date-picker v-model:value="placeBill.otherList![index].projectDate" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'desc'">
                                                    <div>
                                                        <h-input v-model:value="placeBill.otherList![index].desc" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'num'">
                                                    <div>
                                                        <h-input-number v-model:value="placeBill.otherList![index].num" :min="0" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'price'">
                                                    <div>
                                                        <h-input-number :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" v-model:value="placeBill.otherList![index].price" :min="0" style="width: 100%" addon-after="元" />
                                                    </div>
                                                </template>
                                                <template v-else-if="column.dataIndex === 'operation'">
                                                    <h-button type="primary" shape="circle" danger @click="deleteSupplement(placeBill.infoId!, record.key, 'PLACE')">
                                                        <template #icon><DeleteOutlined /></template>
                                                    </h-button>
                                                </template>
                                            </template>
                                        </h-table>
                                        <div class="add-detil">
                                            <h-button type="dashed" block @click="addSupplement(placeBill.infoId!, 'PLACE')">
                                                <template #icon>
                                                    <plus-outlined />
                                                </template>
                                                添加补充项目
                                            </h-button>
                                        </div>
                                    </div>
                                </h-collapse-panel>
                                <h-collapse-panel :key="'placeBill-' + 'file-' + index" header="附件" >
                                    <div class="file-upload flex">
                                        <div class="flex upload-title required">
                                            发票上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="placeBill.invoices"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => placeInvoicesUpload(option, placeBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                发票上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in placeBill.invoices" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removePlaceFile(file.uid, placeBill.infoId!, 1)" />
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="file-upload flex">
                                        <div class="flex upload-title required">
                                            水单上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="placeBill.memos"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => placeMemosUpload(option, placeBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                水单上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in placeBill.memos" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removePlaceFile(file.uid, placeBill.infoId!, 2)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="file-upload flex" v-if="isMiceType()">
                                        <div class="flex upload-title required">
                                            一手合同上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="placeBill.contracts"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => placeContractsUpload(option, placeBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                一手合同上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in placeBill.contracts" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removePlaceFile(file.uid, placeBill.infoId!, 3)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="file-upload flex">
                                        <div class="flex upload-title">
                                            其他上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="placeBill.others"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => placeOthersUpload(option, placeBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                其他上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in placeBill.others" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removePlaceFile(file.uid, placeBill.infoId!, 4)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                </h-collapse-panel>
                            </h-collapse>
                        </h-collapse-panel>
                    </h-collapse>
                    

                </h-tab-pane>
                <h-tab-pane key="4" v-if="vehicleBills && vehicleBills.length > 0">
                    <template #tab>
                        <div class="tab-title flex">
                            用车账单
                        </div>
                        <div class="tab-total flex font-size-12">
                            <span >总：{{ vehicleTotal }}元</span>
                            <span>差：<span :class="{'negative': vehicleDiff > 0, 'positiv': vehicleDiff <= 0 }">{{ vehicleDiff }}元</span></span>
                        </div>
                    </template>
                    
                    <h-collapse v-model:activeKey="vehicleActiveKey">
                        <h-collapse-panel  :header="'账单' + (index + 1)" style="background-color: #fff;margin-bottom:28px;" 
                            v-for="(vehicleBill, index) in vehicleBills" :key="'vehicleBill-' + index">
                            <template #extra>
                                <span class="total font-size-12">竞：{{ vehicleBill._biddingTotal ?? 0}}元</span>
                                <span class="total font-size-12">账：{{ (vehicleBill._billTotal ?? 0) + (vehicleBill._otherTotal ?? 0) }}元</span>
                                <span class="font-size-12">
                                    差：<span  :class="{'negative': computedDiff((vehicleBill._billTotal ?? 0) + (vehicleBill._otherTotal ?? 0), vehicleBill._biddingTotal) > 0, 'positiv': computedDiff((vehicleBill._billTotal ?? 0) + (vehicleBill._otherTotal ?? 0), vehicleBill._biddingTotal) <= 0 }" >{{ computedDiff((vehicleBill._billTotal ?? 0) + (vehicleBill._otherTotal ?? 0), vehicleBill._biddingTotal) }}元</span>
                                </span>
                            </template>
                            <h-form
                                ref="formRef"
                                style="margin-top: 20px;"
                            >
                                <h-row>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="车辆服务商"
                                        >
                                            {{ vehicleBill.supplier }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="用车日期"
                                        >
                                            {{ dayjs(vehicleBill.requireDate).format("YYYY-MM-DD") }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="车型"
                                        >
                                            {{ vehicleBill.vehicleType }}座
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg" >
                                        <h-form-item
                                            label="用车方式"
                                        >
                                            {{ vehicleBill.usageWay && CarUsageConstant.ofType(vehicleBill.usageWay)?.desc  }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg" v-if="vehicleBill.usageWay === 2">
                                        <h-form-item
                                            label="用车时间"
                                        >
                                            {{ vehicleBill.usageTime  &&  CarUsageTimeConstant.ofType(vehicleBill.usageTime)?.desc }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg" v-if="vehicleBill.usageWay === 2">
                                        <h-form-item
                                            label="使用时长"
                                        >
                                            {{ vehicleBill.duration }}小时
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="数量"
                                        >
                                            {{ vehicleBill.vehicleNum }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="竞价单价"
                                        >
                                            {{ vehicleBill.biddingPrice }}元
                                        </h-form-item>
                                    </h-col>
                                    <h-col :span="24">
                                        <h-form-item
                                            label="路线"
                                        >
                                            <div v-if="vehicleBill.routes && vehicleBill.routes.length > 0" class="routes">
                                                <div v-for="(item, index) in vehicleBill.routes" :key="index" :class="{'route': vehicleBill.routes.length != 1}"> 
                                                    <template v-if="vehicleBill.routes.length === 1">
                                                        {{ item }}
                                                    </template>
                                                    <template v-else>
                                                        
                                                        <div class="route-icon">
                                                            <img  v-if="index === 0" :src="start" class="icon" />
                                                            <img  v-else-if="index === (vehicleBill.routes.length - 1)" :src="end" class="icon" />
                                                            <img  v-else :src="via" class="tu" />
                                                        </div>
                                                        <div class="route-name">{{ item }}</div>
                                                        
                                                    </template>
                                                </div>
                                            </div>
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="实际数量"
                                            name="actualNum"
                                        >
                                            <h-input v-model:value="vehicleBill.actualNum" placeholder="实际人数" disabled />
                                        </h-form-item>
                                    </h-col>
                                </h-row>
                            </h-form>
                            
                            <h-collapse v-model:activeKey="vehicleBillActiveKey" style="background: rgb(255, 255, 255)">
                                <h-collapse-panel :key="'vehicleBill-' + 'detail-' + index" header="明细流水" >
                                    <template #extra>
                                        <span class="total font-size-12">合计：{{ vehicleBill._billTotal }}元</span>
                                    </template>
                                    
                                    <div class="table">
                                        <h-table :columns="vehicleBillDetailColumns" :data-source="vehicleBill.detailList" :pagination="false" bordered :scroll="{ x: 1500 }">
                                            <template #emptyText>
                                                <a-empty :image="simpleImage" description="点击下方按钮添加"/>
                                            </template>
                                            <template #bodyCell="{ column, text, record, index }">
                                                <template v-if="column.dataIndex === 'timeFrameList'">
                                                    <div>
                                                        <h-range-picker v-model:value="vehicleBill.detailList![index].timeFrameList" show-time value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%"/>
                                                        <!-- <h-input v-model:value="vehicleBill.detailList![index].timeFrame" style="width: 100%" /> -->
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'carType'">
                                                    <div>
                                                        <h-input v-model:value="vehicleBill.detailList![index].carType" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'brand'">
                                                    <div>
                                                        <h-input v-model:value="vehicleBill.detailList![index].brand" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'carAge'">
                                                    <div>
                                                        <h-input v-model:value="vehicleBill.detailList![index].carAge" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'seats'">
                                                    <div>
                                                        <h-input v-model:value="vehicleBill.detailList![index].seats" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'carNo'">
                                                    <div>
                                                        <h-input v-model:value="vehicleBill.detailList![index].carNo" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'driver'">
                                                    <div>
                                                        <h-input v-model:value="vehicleBill.detailList![index].driver" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'kmNum'">
                                                    <div>
                                                        <h-input-number :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" v-model:value="vehicleBill.detailList![index].kmNum" :min="0" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'kmPrice'">
                                                    <div>
                                                        <h-input-number :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" v-model:value="vehicleBill.detailList![index].kmPrice" :min="0" style="width: 100%" addon-after="元" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'toll'">
                                                    <div>
                                                        <h-input-number :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" v-model:value="vehicleBill.detailList![index].toll" :min="0" style="width: 100%" addon-after="元" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'practical'">
                                                    <div>
                                                        <h-input v-model:value="vehicleBill.detailList![index].practical" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'remark'">
                                                    <div>
                                                        <h-input v-model:value="vehicleBill.detailList![index].remark" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-else-if="column.dataIndex === 'operation'">
                                                    <h-button type="primary" shape="circle" danger @click="deleteDetail(vehicleBill.infoId!, record.key, 'VEHICLE')">
                                                        <template #icon><DeleteOutlined /></template>
                                                    </h-button>
                                                </template>
                                            </template>
                                        </h-table>
                                        <div class="add-detil">
                                            <h-button type="dashed" block @click="addVehicleBillDetil(vehicleBill.infoId!)">
                                                <template #icon>
                                                    <plus-outlined />
                                                </template>
                                                添加明细
                                            </h-button>
                                        </div>
                                    </div>
                                    
                                </h-collapse-panel>
                                <h-collapse-panel :key="'vehicleBill-' + 'other-' + index" header="补充项目" >
                                    <template #extra>
                                        <span class="total font-size-12">合计：{{ vehicleBill._otherTotal }}元</span>
                                    </template>
                                    <div class="table">
                                        <h-table :columns="supplementColumns" :data-source="vehicleBill.otherList" :pagination="false" bordered :scroll="{ x: 1200 }">
                                            <template #emptyText>
                                                <a-empty :image="simpleImage" description="点击下方按钮添加"/>
                                            </template>
                                            <template #bodyCell="{ column, text, record, index }">
                                                <template v-if="column.dataIndex === 'name'">
                                                    <div>
                                                        <h-input v-model:value="vehicleBill.otherList![index].projectName" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'type'">
                                                    <h-select
                                                        v-model:value="vehicleBill.otherList![index].type"
                                                        style="width: 100%"
                                                        allow-clear
                                                    >
                                                        <h-select-option v-for="(item, typeIndex) in vehicleBillTypes" :key="typeIndex" :value="item?.code">{{ item?.desc }}</h-select-option>
                                                    </h-select>
                                                </template>
                                                <template v-if="column.dataIndex === 'date'">
                                                    <div>
                                                        <h-date-picker v-model:value="vehicleBill.otherList![index].projectDate" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'desc'">
                                                    <div>
                                                        <h-input v-model:value="vehicleBill.otherList![index].desc" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'num'">
                                                    <div>
                                                        <h-input-number v-model:value="vehicleBill.otherList![index].num" :min="0" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'price'">
                                                    <div>
                                                        <h-input-number :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" v-model:value="vehicleBill.otherList![index].price" :min="0" style="width: 100%" addon-after="元" />
                                                    </div>
                                                </template>
                                                <template v-else-if="column.dataIndex === 'operation'">
                                                    <h-button type="primary" shape="circle" danger @click="deleteSupplement(vehicleBill.infoId!, record.key, 'VEHICLE')">
                                                        <template #icon><DeleteOutlined /></template>
                                                    </h-button>
                                                </template>
                                            </template>
                                        </h-table>
                                        <div class="add-detil">
                                            <h-button type="dashed" block @click="addSupplement(vehicleBill.infoId!, 'VEHICLE')">
                                                <template #icon>
                                                    <plus-outlined />
                                                </template>
                                                添加补充项目
                                            </h-button>
                                        </div>
                                    </div>
                                </h-collapse-panel>
                                <h-collapse-panel :key="'vehicleBill-' + 'file-' + index" header="附件" >
                                    <div class="file-upload flex">
                                        <div class="flex upload-title required">
                                            发票上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="vehicleBill.invoices"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => vehicleInvoicesUpload(option, vehicleBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                上传发票
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in vehicleBill.invoices" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removeVehicleFile(file.uid, vehicleBill.infoId!, 1)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="file-upload flex">
                                        <div class="flex upload-title required">
                                            水单上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="vehicleBill.memos"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => vehicleMemosUpload(option, vehicleBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                水单上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in vehicleBill.memos" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removeVehicleFile(file.uid, vehicleBill.infoId!, 2)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="file-upload flex">
                                        <div class="flex upload-title">
                                            其他上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="vehicleBill.others"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => vehicleOthersUpload(option, vehicleBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                其他上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in vehicleBill.others" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removeVehicleFile(file.uid, vehicleBill.infoId!, 4)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                </h-collapse-panel>
                            </h-collapse>
                        </h-collapse-panel>
                    </h-collapse>
                    

                </h-tab-pane>
                <h-tab-pane key="5" v-if="insuranceBills && insuranceBills.length > 0">
                    <template #tab>
                        <div class="tab-title flex">
                            保险账单
                        </div>
                        <div class="tab-total flex font-size-12">
                            <span >总：{{ insuranceTotal }}元</span>
                            <span>差：<span :class="{'negative': insuranceDiff > 0, 'positiv': insuranceDiff <= 0 }">{{ insuranceDiff }}元</span></span>
                        </div>
                    </template>
                    
                    <h-form
                        ref="formRef"
                        style="margin-top: 20px;"
                    >
                        <h-collapse v-model:activeKey="insuranceActiveKey">
                            <h-collapse-panel  :header="'账单' + (index + 1)" style="background-color: #fff;margin-bottom:28px;" 
                                v-for="(insuranceBill, index) in insuranceBills" :key="'insuranceBill-' + index">
                                <template #extra>
                                    <span class="total font-size-12">竞：{{ (insuranceBill.schemeBiddingPrice ?? 0)}}</span>
                                    <span class="total font-size-12">账：{{ (insuranceTotal ?? 0) }}</span>
                                    <span class="font-size-12">差：<span :class="{'negative': (insuranceTotal ?? 0) - (insuranceBill.schemeBiddingPrice ?? 0) > 0, 'positiv': (insuranceTotal ?? 0) - (insuranceBill.schemeBiddingPrice ?? 0) <= 0 }">{{ (insuranceTotal ?? 0) - (insuranceBill.schemeBiddingPrice ?? 0) }}</span></span>
                                </template>
                                <h-row>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="保单号"
                                        >
                                            {{ insuranceBill.policyNo }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="保险开始日期"
                                        >
                                            {{ dayjs(insuranceBill.insuranceStartDate).format("YYYY-MM-DD") }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="保险结束日期"
                                        >
                                            {{ dayjs(insuranceBill.insuranceEndDate).format("YYYY-MM-DD") }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="上传被保人数量"
                                        >
                                            {{ insuranceBill.uploadedInsuredNum }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="实际承保人数量"
                                        >
                                            {{ insuranceBill.effectiveInsuredNum }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="单价"
                                        >
                                            {{ insuranceBill.actualPrice }}元
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="人天数"
                                        >
                                            {{ insuranceBill.insuredDays }}
                                        </h-form-item>
                                    </h-col>
                                </h-row>
                            </h-collapse-panel>
                        </h-collapse>
                    </h-form>

                </h-tab-pane>
                <h-tab-pane key="6" v-if="presentBills && presentBills.length > 0">
                    <template #tab>
                        <div class="tab-title flex">
                            礼品账单
                        </div>
                        <div class="tab-total flex font-size-12">
                            <span >总：{{ presentTotal }}元</span>
                            <span>差：<span :class="{'negative': presentDiff > 0, 'positiv': presentDiff <= 0 }">{{ presentDiff }}元</span></span>
                        </div>
                    </template>
                    
                    <h-collapse v-model:activeKey="presentActiveKey">
                        <h-collapse-panel  :header="'账单' + (index + 1)" style="background-color: #fff;margin-bottom:28px;" 
                            v-for="(presentBill, index) in presentBills" :key="'presentBill-' + index">
                            <template #extra>
                                <span class="total font-size-12">竞：{{ presentBill._biddingTotal ?? 0}}元</span>
                                <span class="total font-size-12">账：{{ (presentBill._billTotal ?? 0) + (presentBill._otherTotal ?? 0) }}元</span>
                                <span class="font-size-12">
                                    差：<span  :class="{'negative': computedDiff((presentBill._billTotal ?? 0) + (presentBill._otherTotal ?? 0), presentBill._biddingTotal) > 0, 'positiv': computedDiff((presentBill._billTotal ?? 0) + (presentBill._otherTotal ?? 0), presentBill._biddingTotal) <= 0 }" >{{ computedDiff((presentBill._billTotal ?? 0) + (presentBill._otherTotal ?? 0), presentBill._biddingTotal) }}元</span>
                                </span>
                            </template>
                            <h-form
                                ref="formRef"
                                style="margin-top: 20px;"
                                :model="presentBill"
                            >
                                <h-row>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="其他项目"
                                        >
                                            {{ presentBill.name }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="单位"
                                        >
                                            {{ presentBill.unit }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="竞价单价"
                                        >
                                            {{ presentBill.biddingPrice }}元
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="数量"
                                        >
                                            {{ presentBill.requireNum }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :span="24">
                                        <h-form-item
                                            label="备注"
                                        >
                                            {{ presentBill.remark }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="实际单价"
                                            name="actualPrice"
                                            :rules="[{
                                                required: true,
                                                message: '请输入实际单价',
                                            },{
                                                message: '不能超过竞标价', 
                                                validator: (rule, value, callback) => checkBiddingPrice(rule, value, callback, presentBill.biddingPrice)
                                            }]"
                                        >
                                            <h-input-number :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" v-model:value="presentBill.actualPrice" :min="0" placeholder="实际单价" style="width: 90%" addon-after="元" />
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="实际人数"
                                            name="actualNum"
                                        >
                                            <h-input-number v-model:value="presentBill.actualNum" placeholder="实际人数" :min="0" style="width: 90%" />
                                        </h-form-item>
                                    </h-col>
                                </h-row>
                            </h-form>
                            
                            <h-collapse v-model:activeKey="presentBillActiveKey" style="background: rgb(255, 255, 255)">
                                <h-collapse-panel :key="'presentBill-' + 'other-' + index" header="补充项目" >
                                    <template #extra>
                                        <span class="total font-size-12">合计：{{ presentBill._otherTotal }}元</span>
                                    </template>
                                    <div class="table">
                                        <h-table :columns="supplementColumns" :data-source="presentBill.otherList" :pagination="false" bordered :scroll="{ x: 1200 }">
                                            <template #emptyText>
                                                <a-empty :image="simpleImage" description="点击下方按钮添加"/>
                                            </template>
                                            <template #bodyCell="{ column, text, record, index }">
                                                <template v-if="column.dataIndex === 'name'">
                                                    <div>
                                                        <h-input v-model:value="presentBill.otherList![index].projectName" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'type'">
                                                    <h-select
                                                        v-model:value="presentBill.otherList![index].type"
                                                        style="width: 100%"
                                                        allow-clear
                                                    >
                                                        <h-select-option v-for="(item, typeIndex) in presentBillTypes" :key="typeIndex" :value="item?.code">{{ item?.desc }}</h-select-option>
                                                    </h-select>
                                                </template>
                                                <template v-if="column.dataIndex === 'date'">
                                                    <div>
                                                        <h-date-picker v-model:value="presentBill.otherList![index].projectDate" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'desc'">
                                                    <div>
                                                        <h-input v-model:value="presentBill.otherList![index].desc" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'num'">
                                                    <div>
                                                        <h-input-number v-model:value="presentBill.otherList![index].num" :min="0" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'price'">
                                                    <div>
                                                        <h-input-number :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" v-model:value="presentBill.otherList![index].price" :min="0" style="width: 100%" addon-after="元" />
                                                    </div>
                                                </template>
                                                <template v-else-if="column.dataIndex === 'operation'">
                                                    <h-button type="primary" shape="circle" danger @click="deleteSupplement(presentBill.infoId!, record.key, 'PRESENT')">
                                                        <template #icon><DeleteOutlined /></template>
                                                    </h-button>
                                                </template>
                                            </template>
                                        </h-table>
                                        <div class="add-detil">
                                            <h-button type="dashed" block @click="addSupplement(presentBill.infoId!, 'PRESENT')">
                                                <template #icon>
                                                    <plus-outlined />
                                                </template>
                                                添加补充项目
                                            </h-button>
                                        </div>
                                    </div>
                                </h-collapse-panel>
                                <h-collapse-panel :key="'presentBill-' + 'file-' + index" header="附件" >
                                    <div class="file-upload flex">
                                        <div class="flex upload-title required">
                                            发票上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="presentBill.invoices"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => presentInvoicesUpload(option, presentBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                发票上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in presentBill.invoices" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removePresentFile(file.uid, presentBill.infoId!, 1)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="file-upload flex">
                                        <div class="flex upload-title required">
                                            水单上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="presentBill.memos"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => presentMemosUpload(option, presentBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                水单上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in presentBill.memos" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removePresentFile(file.uid, presentBill.infoId!, 2)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="file-upload flex">
                                        <div class="flex upload-title">
                                            其他上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="presentBill.others"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => presentOthersUpload(option, presentBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                其他上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in presentBill.others" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removePresentFile(file.uid, presentBill.infoId!, 4)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                </h-collapse-panel>
                            </h-collapse>
                        </h-collapse-panel>
                    </h-collapse>
                    

                </h-tab-pane>
                <h-tab-pane key="7" v-if="otherBills && otherBills.length > 0">
                    <template #tab>
                        <div class="tab-title flex">
                            其他账单
                        </div>
                        <div class="tab-total flex font-size-12">
                            <span >总：{{ otherBillTotal }}元</span>
                            <span>差：<span :class="{'negative': otherBillDiff > 0, 'positiv': otherBillDiff <= 0 }">{{ otherBillDiff }}元</span></span>
                        </div>
                    </template>
                    
                    
                    <h-collapse v-model:activeKey="otherActiveKey">
                        <h-collapse-panel  :header="'账单' + (index + 1)" style="background-color: #fff;margin-bottom:28px;" 
                            v-for="(otherBill, index) in otherBills" :key="'otherBill-' + index">
                            <template #extra>
                                <span class="total font-size-12">竞：{{ otherBill._biddingTotal ?? 0}}元</span>
                                <span class="total font-size-12">账：{{ (otherBill._billTotal ?? 0) + (otherBill._otherTotal ?? 0) }}元</span>
                                <span class="font-size-12">差：<span  :class="{'negative': computedDiff((otherBill._billTotal ?? 0) + (otherBill._otherTotal ?? 0), otherBill._biddingTotal) > 0, 'positiv': computedDiff((otherBill._billTotal ?? 0) + (otherBill._otherTotal ?? 0), otherBill._biddingTotal) <= 0 }" >{{ computedDiff((otherBill._billTotal ?? 0) + (otherBill._otherTotal ?? 0), otherBill._biddingTotal) }}元</span></span>
                            </template>

                            <h-form
                                ref="formRef"
                                style="margin-top: 20px;"
                                :model="otherBill"
                            >
                                <h-row>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="其他项目"
                                        >
                                            {{ otherBill.name }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="单位"
                                        >
                                            {{ otherBill.unit }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="竞价单价"
                                        >
                                            {{ otherBill.biddingPrice }}元
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="数量"
                                        >
                                            {{ otherBill.requireNum }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :span="24">
                                        <h-form-item
                                            label="备注"
                                        >
                                            {{ otherBill.remark }}
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="实际单价"
                                            name="actualPrice"
                                            :rules="{
                                                required: true,
                                                message: '请输入实际单价',
                                            }"
                                        >
                                            <h-input-number :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" :min="0" v-model:value="otherBill.actualPrice" placeholder="实际单价" style="width: 90%" addon-after="元" />
                                        </h-form-item>
                                    </h-col>
                                    <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                        <h-form-item
                                            label="实际数量"
                                            name="actualNum"
                                        >
                                            <h-input-number :min="0" v-model:value="otherBill.actualNum" placeholder="实际数量" style="width: 90%"/>
                                        </h-form-item>
                                    </h-col>
                                </h-row>
                            </h-form>
                            
                            <h-collapse v-model:activeKey="otherBillActiveKey" style="background: rgb(255, 255, 255)">
                                <h-collapse-panel :key="'otherBill-' + 'other-' + index" header="补充项目" >
                                    <template #extra>
                                        <span class="total font-size-12">合计：{{ otherBill._otherTotal }}元</span>
                                    </template>
                                    <div class="table">
                                        <h-table :columns="supplementColumns" :data-source="otherBill.otherList" :pagination="false" bordered :scroll="{ x: 1200 }">
                                            <template #emptyText>
                                                <a-empty :image="simpleImage" description="点击下方按钮添加"/>
                                            </template>
                                            <template #bodyCell="{ column, text, record, index }">
                                                <template v-if="column.dataIndex === 'name'">
                                                    <div>
                                                        <h-input v-model:value="otherBill.otherList![index].projectName" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'type'">
                                                    <h-select
                                                        v-model:value="otherBill.otherList![index].type"
                                                        style="width: 100%"
                                                        allow-clear
                                                    >
                                                        <h-select-option v-for="(item, typeIndex) in otherBillTypes" :key="typeIndex" :value="item?.code">{{ item?.desc }}</h-select-option>
                                                    </h-select>
                                                </template>
                                                <template v-if="column.dataIndex === 'date'">
                                                    <div>
                                                        <h-date-picker v-model:value="otherBill.otherList![index].projectDate" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'desc'">
                                                    <div>
                                                        <h-input v-model:value="otherBill.otherList![index].desc" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'num'">
                                                    <div>
                                                        <h-input-number v-model:value="otherBill.otherList![index].num" :min="0" style="width: 100%" />
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'price'">
                                                    <div>
                                                        <h-input-number :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" v-model:value="otherBill.otherList![index].price" :min="0" style="width: 100%" addon-after="元" />
                                                    </div>
                                                </template>
                                                <template v-else-if="column.dataIndex === 'operation'">
                                                    <h-button type="primary" shape="circle" danger @click="deleteSupplement(otherBill.infoId!, record.key, 'OTHER')">
                                                        <template #icon><DeleteOutlined /></template>
                                                    </h-button>
                                                </template>
                                            </template>
                                        </h-table>
                                        <div class="add-detil">
                                            <h-button type="dashed" block @click="addSupplement(otherBill.infoId!, 'OTHER')">
                                                <template #icon>
                                                    <plus-outlined />
                                                </template>
                                                添加补充项目
                                            </h-button>
                                        </div>
                                    </div>
                                </h-collapse-panel>
                                <h-collapse-panel :key="'otherBill-' + 'file-' + index" header="附件" >
                                    <div class="file-upload flex">
                                        <div class="flex upload-title required">
                                            发票上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="otherBill.invoices"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => otherInvoicesUpload(option, otherBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                发票上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in otherBill.invoices" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removeOtherFile(file.uid, otherBill.infoId!, 1)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="file-upload flex">
                                        <div class="flex upload-title required">
                                            水单上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="otherBill.memos"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => otherMemosUpload(option, otherBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                水单上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in otherBill.memos" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removeOtherFile(file.uid, otherBill.infoId!, 2)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="file-upload flex">
                                        <div class="flex upload-title">
                                            其他上传：
                                        </div>
                                        <div class="flex upload-btn">
                                            <div class="flex file">
                                                <h-space :size="10" style="flex-wrap: wrap;">
                                                    <div class="flex">
                                                        <h-upload
                                                            :file-list="otherBill.others"
                                                            
                                                            :show-upload-list="false"
                                                            name="file"
                                                            :custom-request="(option) => otherOthersUpload(option, otherBill.infoId!)"
                                                            @change="handleChange"
                                                            :headers="{
                                                                'Hb-Token': token
                                                            }"
                                                        >
                                                            <h-button >
                                                                <upload-outlined></upload-outlined>
                                                                其他上传
                                                            </h-button>
                                                        </h-upload>
                                                    </div>
                                                    <div class="flex file-hover" v-for="(file, fileIndex) in otherBill.others" :key="fileIndex">
                                                        <div class="flex" @click="openFileUrl(file.url)">
                                                            <h-tooltip class="flex">
                                                                <template #title>{{ file.name }}</template>
                                                                <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                            </h-tooltip>
                                                        </div>
                                                        <div class="flex delete-icon" >
                                                            <DeleteOutlined @click="removeOtherFile(file.uid, otherBill.infoId!, 4)"/>
                                                        </div>
                                                    </div>
                                                </h-space>
                                            </div>
                                        </div>
                                    </div>
                                </h-collapse-panel>
                            </h-collapse>
                        </h-collapse-panel>
                    </h-collapse>
                    

                </h-tab-pane>
            </h-tabs>
            <div v-if="cbServiceBill">
                <h-row class="manage">
                    <h-col :xs="xs" :sm="sm" :md="md" class="align-items">
                        业务运营管理费收取比例：{{ cbServiceBill?.ratio }}%
                    </h-col>
                    <h-col :xs="xs" :sm="sm" :md="md" class="align-items">
                        运营管理费总价：{{ cbServiceBill?.biddingPrice }}元
                    </h-col>
                    <h-col :xs="xs" :sm="sm" :md="md" class="align-items required">
                        实际总价：<h-input-number :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" v-model:value="cbServiceBill!.actualPrice" :min="0"  placeholder="实际总价"  addon-after="元" />
                    </h-col>
                </h-row>
            </div>
            <h-divider size="small" dashed />
            <div class="upload">
                <div class="flex">
                    <span class="font required">会展结算单：</span>
                </div>
                <div class="flex">
                    <h-button type="primary"  @click="download()" style="width: 150px;">
                        <download-outlined />
                        生成会展结算单
                    </h-button>
                    <h-upload
                        style="margin-left: 10px;"
                        v-model:file-list="fileList"
                        :max-count="1"
                        name="file"
                        @change="handleFileChange"
                        :custom-request="(option) => fileListUpload(option)"
                    >
                        <h-button >
                            <upload-outlined></upload-outlined>
                            上传会展结算单
                        </h-button>
                    </h-upload>
                    
                </div>
            </div>
            
            <div class="sub-btn-con">
                <div class="sub-btn">
                    <h-button type="primary" style="width: 200px" @click="Staging" :loading="loading">
                        暂存
                    </h-button>

                    <h-button type="primary" style="width: 200px" @click="onSubmit" :loading="loading">
                        提交
                    </h-button>
                </div>
            </div>
        </h-card>
    </h-spin>
</template>

<style scoped lang="less">
.font-size-12 {
    font-size: 12px;
}

.total {
    padding-right: 30px;
}

.negative {
    color: red;
}

.positiv {
    color: blue;
}

.manage{
    margin-top: 10px;
}

.align-items {
    display: flex;
    align-items: center;
}

.upload {
    display: flex;
    flex-direction: row;

    .font {
        padding-top: 6px;
    }

    
}

.required::before {
    display: inline-block;
    margin-inline-end: 4px;
    color: #ff4d4f;
    font-size: 14px;
    line-height: 1;
    content: "*";
}

.tab-title {
    width: 100%;
    justify-content: center;
    // color: rgba(0, 0, 0, 0.65);
}

.tab-total {
    width: 100%;
    justify-content: space-between;
    flex-direction: row;
    width: 170px;
    color: rgba(0, 0, 0, 0.65);
    font-weight: 500;
}

.add-detil {
    margin-top: 10px;
}

.file-upload {
    flex-direction: row;
    padding-bottom: 20px;

    .upload-title {
        width: 115px;
    }

    .upload-btn {
        width: calc(100% - 115px);
        align-items: center;
        
        .file {
            margin-left: 10px;
            align-items: center;

            .delete-icon {
                padding-left: 20px;
                align-items: center;
            }
            
            .file-hover {
                max-width: 200px;
                padding: 5px 10px;
                cursor: pointer;
                background-color: #f3f3f3;

                .file-name {
                    max-width: 150px;
                }
            }

            .file-hover:hover {
                background-color: #d9d9d9;
            }
        }

        
    }
    
}

.table {
    padding-left: 20px;
    width: calc(100% - 20px);
}

.sub-btn-con {
    margin-top: 20px;
    width: 100%;
    display: flex;
    justify-content: center;

    .sub-btn {
        margin-top: 20px;
        width: 70%;
        display: flex;
        justify-content: space-around;
    }
}



.routes {
    display: flex;
    flex-direction: column;
    width: 100%;
    justify-content: center;
    


    .route {
        display: flex;
        flex-direction: row;
        margin-top: 10px;

        .route-name {
            display: flex;
            align-items: center;
            margin-left: 10px;
        }
        .route-icon {
            display: flex;
            margin-left: 10px;
            width: 20px;
            align-items: center;
            justify-content: center;

            .icon {
                width: 20px;
            }

            .tu {
                width: 10px;
            }
        }
    }

    
}

</style>
<style lang="less">
.bill-card .upload .ant-upload-list-text {
    margin-left: -160px;
}
</style>