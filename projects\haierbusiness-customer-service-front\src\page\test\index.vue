<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
const open = ref(false)
const active = ref(0)

const setActive = (num: number) => {
    active.value = num
}

</script>

<template>
    <div class="con">
        <div class="navigation" :class="{'open': open}">
            <div class="menu-toggle" @click="open = !open">
            </div>
            <ul>
                <li :class="{'active': active == 1}" @click="setActive(1)" style="--list-active-color: #f44336">
                    <a href="#" @click="(e) => e.preventDefault()">
                        <span class="icon"><i class="iconfont icon-shimang"></i></span>
                        <span class="text">示忙</span>
                    </a>
                </li>
                <li :class="{'active': active == 2}" @click="setActive(2)" style="--list-active-color: #ffa117">
                    <a href="#" @click="(e) => e.preventDefault()">
                        <span class="icon"><i class="iconfont icon-waihu"></i></span>
                        <span class="text">外呼</span>
                    </a>
                </li>
                <li :class="{'active': active == 3}" @click="setActive(3)" style="--list-active-color: #0fc70f">
                    <a href="#" @click="(e) => e.preventDefault()">
                        <span class="icon"><i class="iconfont icon-gongdan"></i></span>
                        <span class="text">创建工单</span>
                    </a>
                </li>
                <li :class="{'active': active == 4}" @click="setActive(4)" style="--list-active-color: #2196f3">
                    <a href="#" @click="(e) => e.preventDefault()">
                        <span class="icon"><i class="iconfont icon-jilu"></i></span>
                        <span class="text">通话记录</span>
                    </a>
                </li>
                <li :class="{'active': active == 5}" @click="setActive(5)" style="--list-active-color: #b145e9">
                    <a href="#" @click="(e) => e.preventDefault()">
                        <span class="icon"><i class="iconfont icon-qianchu"></i></span>
                        <span class="text">签出</span>
                    </a>
                </li>
            </ul>
        </div>  
    </div>
    
</template>

<style lang="less" scoped>
.con{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
}

.navigation{
    background-color: #fff;
    position: fixed;
    right: 20px;
    top: 120px;
    width: 75px;
    height: 500px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.5s;
}

.navigation.open {
    width: 180px;
}

.navigation .menu-toggle {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.25);
    padding: 0 20px;
    cursor: pointer;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    transition: all 0.5s;
}

.navigation .menu-toggle::before {
    width: 30px;
    height: 2px;
    content: '';
    background-color: #333;
    transform: translateY(-8px);
    position: absolute;
    transition: all 0.5s;
}

.navigation.open .menu-toggle::before {
    transform: translateY(0px) rotate(-45deg);
}

.navigation .menu-toggle::after {
    width: 30px;
    height: 2px;
    content: '';
    background-color: #333;
    transform: translateY(8px);
    position: absolute;
    transform: translateY(8px);
    box-shadow: 0 -8px 0 #333;
    transition: all 0.5s;
}

.navigation.open .menu-toggle::after {
    transform: translateY(0px) rotate(45deg);
    box-shadow: 0 0 0 #333
}

.navigation ul {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
}

.navigation ul li {
    list-style: none;
    width: 100%;
    height: 60px;
    padding: 0 10px;
    transition: all 0.5s;
}

.navigation ul li.active {
    transform: translateX(-30px);
}

.navigation.open ul li.active {
    transform: translateX(-10px);
}

.navigation ul li a {
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    text-align: center;
}

.navigation ul li a .icon {
    min-width: 55px;
    height: 55px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.75em;
    color: #333;
    border-radius: 10px;
    position: relative;
    transition: all 0.5s;
}

.navigation ul li a .icon .iconfont {
    font-size: 1.25em;
}

.navigation ul li.active a .icon {
    background-color: var(--list-active-color);
    color: #fff;
}

.navigation ul li a .icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--list-active-color);
    z-index: -1;
    filter: blur(5px);
    opacity: 0;
    transition: all 0.5s;
}

.navigation ul li.active a .icon::before {
    opacity: 1;
}

.navigation ul li a .text {
    padding: 0 15px;
    visibility: hidden;
    height: 60px;
    display: flex;
    align-items: center;
    color: #333;
    opacity: 0;
}

.navigation.open ul li a .text {
    opacity: 1;
    visibility: visible;
}

.navigation ul li.active a .text {
    color: var(--list-active-color);
}


</style>