
import { applicationStore } from './store/applicaiton';
import { storeToRefs } from 'pinia';

/**
 * 判断当前用户是否拥有组权限
 * @param condition  AND : 全部拥有返回真 ，OR： 拥有一个则返回真 , 默认OR
 * 
 */
export const checkUserGroups = (groupIds: number[], condition: "AND" | 'OR'): boolean => {
    const userGroups = getUserGroup()
    const len = userGroups.filter(item => groupIds.includes(item!)).length
    if (condition === 'OR') {
        return len > 0
    } else {
        return len >= groupIds.length
    }
}

/**!
 * 判断当前用户是否拥有某个组
 */
export const checkUserGroup = (groupId: number): boolean => {
    return getUserGroup().filter(item => item == Number(groupId)).length > 0
}

/**
 * 获取用户组
 */
export const getUserGroup = (): (number | undefined)[] => {
    const store = applicationStore()
    const { loginUser } = storeToRefs(store)
    if (loginUser.value && loginUser.value.authorities) {
        return loginUser.value.authorities.map(item => item.group)
    } else {
        return []
    }

}
