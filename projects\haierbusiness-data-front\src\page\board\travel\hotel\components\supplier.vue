<template>
    <div background="rgba(0,0,0,0)">
        <div :id="id" :style="{ height: props.height + 'vh' }"></div>
    </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import * as echarts from "echarts";
import { queryHotelSupplier } from "@haierbusiness-front/apis/src/data/board/travel";
import { circle2 as cicleOptions, colors } from "../../../data";
import { EventBus } from "../../../eventBus";
const props = defineProps({
    height: {
        type: Number,
        default: 25,
    },
});
const id = ref("cicle-" + Date.now());
const loading = ref(false);
let chartDom, myChart;
const payTypeCheck = ref<string>("");
onMounted(() => {
    queryData();

    chartDom = document.getElementById(id.value);
    myChart = echarts.init(chartDom as any, "dark");

    myChart.on("click", (param) => {
        if (param.from != "supplier_name" && param.name != payTypeCheck.value) {
            payTypeCheck.value = param.name;
            EventBus.emit("refresh", {
                ...param,
                from: "supplier_name",
            });
        } else {
            payTypeCheck.value = "";
            EventBus.emit("refresh");
        }
    });
});
EventBus.on((event, params) => {
    if (event == "refresh") {
        if (!params) queryData();
        if (params && params.from != "supplier_name") queryData(params);

        if (params && params.from == "supplier_name") {
            queryData().then(() => {
                myChart.dispatchAction({
                    type: "highlight",
                    seriesIndex: 0,
                    dataIndex: params.dataIndex,
                });
            });
        }
    }
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
    loading.value = true;
    const data = await queryHotelSupplier(
        params ? params.data.name : null,
        params ? params.from : null
    );
    loading.value = false;
    const rows = [] as Array<{ value: string | number; name: string }>;
    data.rows.forEach((item, index) => {
        rows.push({
            value: item[1],
            name: item[0],
        });
    });
    const { series } = cicleOptions;
    // series[0].color = colors;
    series[0].data = rows;
    myChart.clear();
    myChart.setOption({
        ...cicleOptions,
        tooltip: { trigger: "item", formatter: "{b} {c} 间夜" },
    });
};
</script>
<style scoped lang="less"></style>
