import { get, post } from '../request'
import {
  BNotificationRes,
  BNotificationReq,
  BHomepagePicRes,
  BNotificationInfoReq,
  BNotificationInfoRes,
  ClientInvokeLoginReq,
  ClientInvokeLoginRes,
  BPoliciesRes,
  BSaveParams,
  BApplyListReq,
  BApplyListRes,
  BApplyDetailReq,
  BApplyListRecord,
  BHomeGetCountRes,
  BRestaurantRes,
  BRestaurantReq
} from '@haierbusiness-front/common-libs'

export const banquetApi = {
  // 通知公告列表
  getNotification: (params: BNotificationReq): Promise<BNotificationRes> => {
    return post('banquet/api/h5/notification/page', params)
  },
  // 通知公告详情
  getNotificationDetail: (params: BNotificationInfoReq): Promise<BNotificationInfoRes> => {
    return get('banquet/api/h5/notification/getNotificationInfo', params)
  },
  // 获取首页图片
  getHomepagePic: (): Promise<Array<BHomepagePicRes>> => {
    return get('banquet/api/h5/notification/getHomepagePic')
  },
  // 获取承诺须知
  getPromise: (): Promise<BHomepagePicRes> => {
    return get('banquet/api/h5/notification/getPromise')
  },
  // 获取隐私承诺
  getPrivacy: (): Promise<BHomepagePicRes> => {
    return get('banquet/api/h5/notification/getPrivacy')
  },
  // 获取温馨提示
  getTips: (): Promise<BHomepagePicRes> => {
    return get('banquet/api/h5/notification/getTips')
  },

  // 获取当前用户受管控信息
  getPolicies: (): Promise<BPoliciesRes> => {
    return get('banquet/api/h5/policies/get')
  },

  // 客户端获取登录信息
  clientInvokeLogin: (params: ClientInvokeLoginReq): Promise<ClientInvokeLoginRes> => {
    return post('banquet/api/h5/mt/clientInvokeLogin', params)
  },

  // 申请单保存
  save: (params: BSaveParams): Promise<string> => {
    return post('banquet/api/h5/applicatioin/save', params)
  },

  // 申请单修改
  update: (params: BSaveParams): Promise<string> => {
    return post('banquet/api/h5/applicatioin/update', params)
  },
  // 申请单列表
  applyList: (params: BApplyListReq): Promise<BApplyListRes> => {
    return post('banquet/api/h5/applicatioin/page', params)
  },
  // 首页获取申请单列表
  homeApplyList: (params: BApplyListReq): Promise<BApplyListRes> => {
    return get('banquet/api/h5/applicatioin/list', params)
  },
  
  // 申请单详情
  getApplyDetail: (params: BApplyDetailReq): Promise<BApplyListRecord> => {
    return get('banquet/api/h5/applicatioin/get', params)
  },

  // 申请单关闭
  applyClose: (params: BApplyDetailReq): Promise<BApplyListRecord> => {
    return get('banquet/api/h5/applicatioin/close', params)
  },
  // 申请单支付
  goPay: (params: BApplyDetailReq): Promise<BApplyListRecord> => {
    return get('banquet/api/h5/applicatioin/pay', params)
  },
  // 获取首页申请数量
  getCount: (): Promise<BHomeGetCountRes> => {
    return get('banquet/api/h5/applicatioin/getCount')
  },
  // 获取美团城市
  getCity: (): Promise<string> => {
    return get('banquet/api/h5/mt/getCity')
  },


  // 根据美团返回餐厅id 获取餐厅详情数据
  getRestaurant: (params: BRestaurantReq): Promise<BRestaurantRes> => {
    return get('banquet/api/common/restaurant/get', params)
  },
  
}