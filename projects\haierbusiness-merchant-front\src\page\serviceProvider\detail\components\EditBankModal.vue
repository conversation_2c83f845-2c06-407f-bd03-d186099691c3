<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { useServiceProviderDetailStore } from '../store';
import { Form } from 'ant-design-vue';

const props = defineProps({
  modelValue: {
    type: <PERSON><PERSON><PERSON>,
  },
  getDataList: Function
});
const emit = defineEmits(['update:modelValue']);

const store = useServiceProviderDetailStore();
const useForm = Form.useForm;

// 创建一个响应式的表单数据对象
const formState = ref({ ...store.bankDetail });


// 监听store中的businessDetail变化，同步到本地formState
watch(() => store.bankDetail, (newVal) => {
  if (newVal) {
    formState.value = { ...newVal };
    console.log(formState.value, "store.bankDetail.id");
  }
}, { deep: true, immediate: true });

const formRules = reactive({
  accountNumber: [
    {
      required: true,
      message: '请输入银行账号',
      trigger: 'change'
    },
  ],
  bankBranchCode: [
    {
      required: true,
      message: '请输入开户行编码',
      trigger: 'change'
    },
  ],
  bankBranchAddress: [
    {
      required: true,
      message: '开户行地址',
      trigger: 'change'
    },
  ],
  accountHolderName: [
    {
      required: true,
      message: '请输入银行户主',
      trigger: 'change'
    },
  ],
  bankCountry: [
    {
      required: true,
      message: '请输入银行所属国家',
      trigger: 'change'
    },
  ],
});
const { resetFields, validate, validateInfos } = useForm(formState, formRules)

const modelOpen = computed({
  get: () => props.modelValue,
  set: v => emit('update:modelValue', v)
});

const handleOk = async () => {

  try {
    await validate();
    await (store.bankDetail.id ? store.editBank : store.addBank)({
      merchantId: Number(store.businessId),
      ...formState.value,
    });
    props.getDataList?.()
    handleCancel()
  } catch (error) {
    console.error('校验未通过', error)
  }
}

const handleCancel = () => {
  resetFields();
  delete store.bankDetail.id;
  modelOpen.value = false;
}

onMounted(() => {
  resetFields();
})
</script>

<template>
  <a-modal :width="600" :title="`${store.bankDetail.id ? '编辑' : '新增'}银行`" v-model:open="modelOpen" class="edit-modal"
    @ok="handleOk"  @cancel="handleCancel">
    <div class="edit-modal-content">
      <a-form class="form" :label-col="{ span: 5 }" :wrapper-col="{ span: 19 }" :rules="formRules" ref>
        <a-form-item label="银行账号" v-bind="validateInfos.accountNumber">
          <a-input v-model:value="formState.accountNumber" placeholder="请输入银行账号" />
        </a-form-item>
        <a-form-item label="开户行编码" v-bind="validateInfos.bankBranchCode">
          <a-input v-model:value="formState.bankBranchCode" placeholder="请输入开户行编码" />
        </a-form-item>
        <a-form-item label="开户行地址" v-bind="validateInfos.bankBranchAddress">
          <a-input v-model:value="formState.bankBranchAddress" placeholder="请输入开户行地址" />
        </a-form-item>
        <a-form-item label="银行户主" v-bind="validateInfos.accountHolderName">
          <a-input v-model:value="formState.accountHolderName" placeholder="请输入银行户主" />
        </a-form-item>
        <a-form-item label="银行所属国家" v-bind="validateInfos.bankCountry">
          <a-input v-model:value="formState.bankCountry" placeholder="请输入银行所属国家" />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<style lang="less" scoped>
.edit-modal {
  .edit-modal-content {
    padding: 10px;
    max-height: 70vh;
    overflow: auto;

    .group-title {
      color: rgba(0, 0, 0, 0.88);
      font-weight: 600;
      line-height: 1.5;
      margin-bottom: 15px;
    }
  }
}

:deep(.ant-form-item-required::before) {
  display: none !important;
}
</style>
