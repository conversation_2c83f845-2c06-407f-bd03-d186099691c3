<script setup lang="ts">
import { ref, watch } from 'vue'
import { IPayData } from '@haierbusiness-front/common-libs/src/pay/model/basicModel';
import { Cell, CellGroup, RadioGroup, Radio } from 'vant';
import 'vant/es/cell/style'
import 'vant/es/cell-group/style'
import 'vant/es/radio-group/style'
import 'vant/es/radio/style'
import zfb from '@/assets/image/composition/zfb.png';
import wx from '@/assets/image/composition/wx.png';
import ysf from '@/assets/image/composition/ysf.png';

interface Props {
    checked: string
}

const props = withDefaults(defineProps<Props>(), {
    checked: ''
})

const checked = ref(props.checked || '')

watch(props, (newValue) => {
    checked.value = props.checked || ''
})

</script>

<template>
<div class="composition-con">
  <radio-group v-model="checked">
    <cell-group inset>
      <cell :border="false" clickable @click="$emit('setChecked', '1')">
        <template #title>
          <div class="type-con">
            <img :width="20" :height="20" :src="zfb" />
            <span class="pay-title">支付宝支付</span>
          </div>
        </template>
        <template #right-icon>
          <radio name="1" />
        </template>
      </cell>
      <!-- <cell :border="false" clickable @click="$emit('setChecked', '2')">
        <template #title>
          <div class="type-con">
            <img :width="20" :height="20" :src="wx" />
            <span class="pay-title">微信支付</span>
          </div>
        </template>
        <template #right-icon>
          <radio name="2" />
        </template>
      </cell> -->
      <!-- <cell :border="false" clickable @click="$emit('setChecked', '3')">
        <template #title>
          <div class="type-con">
            <img :width="20" :height="20" :src="ysf" />
            <span class="pay-title">云闪付支付</span>
          </div>
        </template>
        <template #right-icon>
          <radio name="3" />
        </template>
      </cell> -->
    </cell-group>
  </radio-group>
</div>
</template>

<style scoped lang="less">
.composition-con {
    padding-top: 10px;
}

.type-con {
    display: flex;
    flex-direction: row;
    align-items: center;

    .pay-title {
    display: flex;
    padding-left: 5px;
    }

    .no-img {
    display: flex;
    padding-left: 25px;
    }
}
</style>