<script setup lang="ts">
import {
  Modal,
  Tag as hTag,
  Form as hForm,
  FormItem as hFormItem,
  But<PERSON> as hButton,
  Col as hCol,
  Row as hRow,
  Spin as hSpin,
  Table as hTable,
  Textarea as hTextarea,
  DatePicker as hDatePicker,
  CollapsePanel as hCollapsePanel,
  Collapse as hCollapse,
  FormInstance,
  message,
  TableColumnsType,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import {
  IAnnualPlanSaveOrUpdateRequestDTO,
  AnnualPlanTypeStateConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
  IHaierAccountBillInfo,
} from '@haierbusiness-front/common-libs';
import { getCurrentRouter, resolveParam } from '@haierbusiness-front/utils';
import { ref, createVNode, PropType, computed } from 'vue';

import dayjs, { Dayjs } from 'dayjs';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import {
  EvaluateControlConstant,
  IAnnualPlanDetailRequestDTO,
  IAnnualPlanDetailResponseDTO,
  IAnnualPlanTypeListRequest,
  PlanTypeConstant,
} from '@haierbusiness-front/common-libs/src/daily';
import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
const { loginUser } = storeToRefs(applicationStore(globalPinia));

const router = getCurrentRouter();
const prop = defineProps({
  data: Object as PropType<IAnnualPlanDetailResponseDTO>,
  fold: Boolean as PropType<Boolean>,
});

const collapseActiveKey = ref([1]);
{
  if(prop?.fold){
    collapseActiveKey.value = []
  }
}
</script>

<template>
  <h-collapse v-model:activeKey="collapseActiveKey" :bordered="false" style="background-color: white" :collapsible="'icon'">
    <h-collapse-panel key="1">
      <template #header>
        <div style="display: flex">
          <div style="height: 25px; width: 10px; background-color: rgb(0, 115, 229)" />
          <div style="font-size: 16px; font-weight: 600; margin-left: 10px">年度目标&定位</div>
        </div>
      </template>
      <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%"></div>
      <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px">
        <h-row :gutter="24" style="margin-top: 15px">
          <h-col :span="3" style="text-align: right">年度目标：</h-col>
          <h-col :span="21">
            <h-textarea v-if="data?.target" v-model:value="data.target" :rows="3" readOnly />
          </h-col>
        </h-row>
        <h-row :gutter="24" style="margin-top: 20px">
          <h-col :span="3" style="text-align: right">年度定位：</h-col>
          <h-col :span="21">
            <h-textarea v-if="data?.orientation" v-model:value="data.orientation" :rows="3" readOnly />
          </h-col>
        </h-row>
      </div>
    </h-collapse-panel>
  </h-collapse>
</template>

<style scoped lang="less">
@import '../../../assets/css/main.less';
</style>
