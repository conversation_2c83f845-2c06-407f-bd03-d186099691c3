export interface IRoleInfo {
     id?:number
     name?:string
     description?:string
     gmtCreate?:string
     createBy?:string
     gmtModified?:string
     lastModifiedBy?:string
}

export interface IRoleListRequest {
     groupId?:number
     name?:string
     pageNum?:number
     pageSize?:number
}   

export interface IRoleSaveUpdateRequest {
     id?:number
     name?:string
     description?:string
}

export interface IRoleDeleteRequest {
     id?:number
}

export interface IRoleLinkResourceRequest {
     roleId?:number
     resourceIds:number[]
}