import {
    IBudgetHaierOccupyRequest,
    IBudgetHaierQueryRequest as IBudgetHaierQueryRequest,
    IBudgetHaierQueryResponse as IBudgetHaierQueryResponse,
    IBudgetHaierTypesRequest,
    IBudgetHaierXw,
    IBudgetHaierTypesResponse, IHaierApplicationBudgetFeeItemInfo,
    IHaierBudgetFeeItemQueryRequest,
    IHaierQueryBudgetInfoRequest,
    QueryBudgetInfoRes,
    IPayHeader,
    QueryApproveDetailRes,
    IPayResponse,
    IBudgetHaierHBC2QueryRequest,
    IBudgetHaierHBC2Response
} from '@haierbusiness-front/common-libs'
import { get, post } from '../request'

export const budgetHaierPayApi = {

    /**
     * 查询支持的预算类型
     */
    searchBudgetTypes: (params: IBudgetHaierTypesRequest): Promise<IBudgetHaierTypesResponse[]> => {
        return get('pay/api/haier/budget/pay/query_types', params)
    },

    /**
     * 获取小微用户列表
     */
    queryXwfinBudgeters: (params: IBudgetHaierXw): Promise<IBudgetHaierTypesResponse[]> => {
        return get('pay/api/haier/budget/pay/queryXwfinBudgeters', params)
    },

    /**
     * 查询支持的费用项目
     */
    searchFeeItems: (params: IHaierBudgetFeeItemQueryRequest): Promise<IHaierApplicationBudgetFeeItemInfo[]> => {
        return get('pay/api/haier/budget/pay/query_fee_items', params)
    },
    /**
     * 查询部门预算信息
     */
    queryBudgetInfo: (params: IHaierQueryBudgetInfoRequest): Promise<QueryBudgetInfoRes> => {
        return get('pay/api/haier/budget/dept/hbc/support/queryBudgetInfo', params)
    },
    /**
     * 查询部门预算
     */
    budgetQueryList: (params: IHaierQueryBudgetInfoRequest): Promise<QueryBudgetInfoRes> => {
            return get('pay/api/haier/budget/pay/budgetQueryList', params)
        },
    /**
     * 查询审批详情
     */
    queryApproveDetail: (params:{orderNo:string}): Promise<QueryApproveDetailRes> => {
            return get('pay/api/haier/budget/dept/hbc/support/queryApproveDetail', params)
        },
    /**
     * 查询预算
     */
    searchBudget: (params: IBudgetHaierQueryRequest): Promise<IBudgetHaierQueryResponse> => {
        return get('pay/api/haier/budget/pay/query', params)
    },
    /**
     * kems2查询部门
     */
    searchBudgetPerson: (params: IBudgetHaierQueryRequest): Promise<IBudgetHaierQueryResponse> => {
        return get('pay/api/haier/budget/dept/kems2/support/budgetPerson   ', params)
    },
    /**
     * 占用预算
     */
    occupyBudget: (params: IBudgetHaierOccupyRequest, header: IPayHeader): Promise<IPayResponse> => {
        return post('pay/api/haier/budget/pay/occupy', params,
            {
                "hb-nonce": header.nonce,
                "hb-timestamp": header.timestamp,
                "hb-sign": header.sign,
                "hb-excludes": header.excludes,
                "hb-application-code": header.applicationCode,
            })
    },


    searchHBC2Budget: (params: IBudgetHaierHBC2QueryRequest): Promise<IBudgetHaierHBC2Response[]> => {
        return get('pay/api/haier/budget/pay/budgetQueryList', params)
    },
}
