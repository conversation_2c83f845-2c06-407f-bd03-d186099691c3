<template>
  <div class="news-detail" style="padding-top: 20px; padding-bottom: 40px;">
    <!-- <van-nav-bar  :fixed="true"  title="通知通告" left-arrow style="  z-index: 11;">
      <template #left>
        <van-icon name="arrow-left" color="#000" @click="goBack" />
      </template>
    </van-nav-bar> -->
    <div class="news_title auto_wrap">{{ detail?.title }}</div>
    <div class="mine-user flex mb-20">
        <div class="main-user-img mr-10">{{ detail?.creatorName?.slice(-2) }}</div>
        <div class="main-user-name">
          <div class="user-name mb-5">{{ detail?.creatorName }}</div>
          <div class="user-lable flex">
            <div>{{ detail?.updateTime }}</div>
          </div>
        </div>
        
      </div>
      <div class="news_content" v-html="detail?.content">
      </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import type { Ref } from 'vue';

import { banquetApi } from '@haierbusiness-front/apis';

import {

  BNotificationInfoRes
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { Item } from 'ant-design-vue/es/menu';
const router = getCurrentRouter();

const route = ref(getCurrentRoute());

const id = route.value?.query?.id;


const store = applicationStore();

const { loginUser } = storeToRefs(store);

const detail = ref<BNotificationInfoRes>()
const getDetail = (id: string) => {
  const params = {
    id,
  };

  banquetApi.getNotificationDetail(params).then((res) => {
    detail.value = res;
  });
};

const goBack = () => {
  router.back(-1);
};


watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);

</script>

<style lang="less" scoped>
@import url(../common.less);


:deep(.large-value) {
  min-width: 70%;
}
.news-detail {
  min-height: 100vh;
  padding: 50px 16px 40px;
  display: flex;
  flex-direction: column;
}
.news_title {
  font-family: Open Sans, Open Sans;
  font-weight: bold;
  font-size: 12px;

}
.mine-user {
    display: flex;
    width: 100%;
    margin-top: 20px;
    .main-user-img {
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 40px;
      font-size: 10px;
      color: #fff;
      background: #56d8bf;
      margin-right: 10px;
    }
    .main-user-name {
      display: flex;
      flex-direction: column;
      justify-content: center;
      .user-name {
        font-size: 10px;
        font-weight: bold;
      }
      .user-lable {
        color: rgba(0,0,0,0.7);
        font-size: 8px;
      }
    }
    .main-user-news {
      flex: 1;
      text-align: right;
    }
  }

.auto_wrap {
  word-break: break-all;
}
:deep(.news_content img) {
  width: 100%;
}
</style>