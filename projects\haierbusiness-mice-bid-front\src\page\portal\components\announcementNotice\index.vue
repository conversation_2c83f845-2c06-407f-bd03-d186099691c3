<script setup lang="ts">
import { announcementNoticeApi } from '@haierbusiness-front/apis';
import {
  IAnnouncementNotice
} from '@haierbusiness-front/common-libs';

import { computed, ref, onMounted, nextTick, reactive, h, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import EditDialog from './edit-dialog.vue'
import router from '../../../../router';
// const router = useRouter()

const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router

  // 初始加载数据
  listApiRun({
    pageNum: 1,
    pageSize: 10
  });
})
const confirmLoading = ref(false);
const {
  data,
  run: listApiRun,
  loading,
} = usePagination(announcementNoticeApi.noticeList);
//列表
const noticeList = computed(() => data.value?.records || []);
//可见
const visible = ref(false)
//详情数据
const editData = ref<IAnnouncementNotice>()
//通知详情
const noticeDetails = async (id:number)=>{
  if (!id) return;

  confirmLoading.value = true;
  try {
    const res = await announcementNoticeApi.details(id);
    editData.value = res;
    if(editData.value){
      console.log(editData.value);
      if(res.contentForm == 2){
        window.open(res.informContent, '_blank');
      }else{
        visible.value = true
      }
      
    }
  } catch (error) {
    console.error('获取详情失败:', error);
  } finally {
    confirmLoading.value = false;
  }
}

const onDialogClose = ()=>{
  visible.value = false
}
const handleOk = ()=>{
  visible.value = false
}

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <a-list :data-source="noticeList">
      <template #renderItem="{ item }">
        <a-list-item>
          <div class="notice-title" @click="noticeDetails(item.id)">{{ item.title }}</div>
          <div class="notice-gmtCreate">{{ item.gmtCreate }}</div>
        </a-list-item>
      </template>
    </a-list>
  </div>
  <div v-if="visible">
      <edit-dialog :show="visible" :data="editData" @cancel="onDialogClose" @ok="handleOk">
      </edit-dialog>
    </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}
.notice-title{
  width: 60%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: black;
  font-size: 14px;
  cursor: pointer;
  &:hover{
    color: #1677ff;
  }
}
</style>
