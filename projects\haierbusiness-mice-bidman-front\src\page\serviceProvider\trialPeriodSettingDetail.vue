<template>
  <div class="trial-period-detail">
    <div class="table-container">
      <a-spin :spinning="loading" tip="正在加载数据...">
        <a-table
          :dataSource="tableData"
          :columns="columns"
          bordered
          :pagination="false"
          class="detail-table"
          :locale="{ emptyText: '暂无数据' }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ record.status || '待审批' }}
              </a-tag>
            </template>
            <template v-if="column.key === 'attachmentFiles'">
              <div v-if="record.attachmentFiles && record.attachmentFiles.length > 0">
                <a-button
                  v-for="(file, index) in record.attachmentFiles"
                  :key="index"
                  type="link"
                  size="small"
                  @click="openAttachment(file.path)"
                  style="margin-right: 8px"
                >
                  附件{{ index + 1 }}
                </a-button>
              </div>
              <span v-else>-</span>
            </template>
          </template>
        </a-table>
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { h, ref, onMounted, inject, type Ref } from 'vue';
import { useRoute } from 'vue-router';
import { resolveParam, routerParam } from '@haierbusiness-front/utils';
import { serviceProviderApi } from '@haierbusiness-front/apis';
import { Spin, Table, Tag, Button as hButton } from 'ant-design-vue';
import { TrialStateEnum, TrialStateMap, ApprovalStatusMap, ApprovalStatusEnum } from '@haierbusiness-front/common-libs';

// 获取试用期状态文本
const getTrialStateText = (state: number): string => {
  return TrialStateMap[state as TrialStateEnum] || '-';
};

interface PageRecord {
  id?: string | number;
  title?: string;
  hideBtn?: string;
}

const route = useRoute();
const recordParam = route.query.record as string;
const record: PageRecord | null = recordParam ? resolveParam(recordParam) : null;
console.log('record', record);
const hideBtn = record?.hideBtn || '';
// 获取并设置frameModel
const frameModel = inject<Ref<number>>('frameModel');
if (frameModel) {
  frameModel.value = hideBtn === '1' ? 1 : 0;
}
// 表格数据
const tableData = ref<any[]>([]);
// 加载状态
const loading = ref(false);

// 表格列配置
const columns = [
  {
    title: '操作类型',
    dataIndex: 'operateType',
    width: '200px',
    customRender: ({ text }: { text: string }) => getTrialStateText(Number(text)),
    ellipsis: true,
  },
  {
    title: '理由',
    dataIndex: 'reason',
    key: 'reason',
    align: 'center',
    customRender: ({ text }: { text: any }) => text || '-',
  },
  {
    title: '转正需承接会议',
    dataIndex: 'trialEndMiceNum',
    width: '200px',
    ellipsis: true,
  },
  {
    title: '见证性材料',
    dataIndex: 'attachmentFiles',
    width: '200px',
    customRender: ({ text }: { text: any[] }) => {
      if (!text || !Array.isArray(text) || text.length === 0) return '-';
      return h(
        'div',
        {
          class: 'attachment-list',
        },
        text.map((attachment, index) =>
          h(
            hButton,
            {
              type: 'link',
              size: 'small',
              key: index,
              onClick: () => {
                if (attachment && attachment.path) {
                  window.open(attachment.path, '_blank');
                }
              },
            },
            () => `附件${index + 1}`,
          ),
        ),
      );
    },
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    key: 'createName',
    align: 'center',
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    key: 'gmtCreate',
    align: 'center',
  },
];

// 获取状态颜色
const getStatusColor = (status) => {
  switch (status) {
    case '待审批':
      return 'orange';
    case '已通过':
      return 'green';
    case '已拒绝':
      return 'red';
    default:
      return 'blue';
  }
};

// 打开附件
const openAttachment = (filePath) => {
  if (filePath) {
    window.open(filePath, '_blank');
  }
};

// 数据加载
const loadData = async () => {
  loading.value = true;
  try {
    if (!record?.id) {
      tableData.value = [];
      return;
    }

    const response = await serviceProviderApi.getTrialPeriodSettingDetail(record.id);
    if (response) {
      tableData.value = [response];
    } else {
      tableData.value = [];
    }
  } catch (error) {
    console.error('加载转正设置详情失败:', error);
    tableData.value = [];
  } finally {
    loading.value = false;
  }
};

const initData = () => {
  if (record?.id) {
    loadData();
  } else {
    console.warn('未找到有效的记录ID');
  }
};

const refreshData = () => {
  initData();
};

defineExpose({
  refreshData,
});

onMounted(() => {
  initData();
});
</script>

<style lang="scss" scoped>
.trial-period-detail {
  padding: 20px;
  background: #fff;
  border-radius: 8px;

  .page-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e4e7ed;

    h3 {
      margin: 0;
      color: #303133;
      font-size: 18px;
      font-weight: 600;
    }
  }

  .table-container {
    position: relative;

    .detail-table {
      :deep(.ant-table-thead > tr > th) {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 600;
        text-align: center;
      }

      :deep(.ant-table-tbody > tr > td) {
        text-align: center;
      }

      :deep(.ant-table-tbody > tr:hover > td) {
        background-color: #f5f7fa;
      }

      :deep(.ant-table-placeholder) {
        border: none;
      }
    }
  }
}
</style>
