<template>
  <div style="height: 79.2vh" background="rgba(0,0,0,0)">
    <div class="map" :id="'map' + props.id + props.iden">
      <div class="map-desc">
        <el-tooltip placement="top" effect="light">
          <template #content>
            <p style="color: #fff">
              <b>地图颜色标注：</b><br />
              <template v-for="item in colors">
                <span class="square" :style="{ background: item.color }"></span>：订单数量为{{ item.min }}以上<br />
              </template>
              <span class="square"></span>：无订单
            </p>
          </template>
          <p>
            地图颜色标注：
            <InfoCircleOutlined />
          </p>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { Scene, PolygonLayer, LineLayer, PointLayer, Popup } from '@antv/l7';
import { Mapbox } from '@antv/l7-maps';
import { ref, onMounted } from 'vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import 'element-plus/theme-chalk/dark/css-vars.css';
import { ElTooltip } from 'element-plus';
import { querylocalMapData } from '@haierbusiness-front/apis/src/data/board/travel';
import { smartBrainApi } from '@haierbusiness-front/apis';
import { EventBus } from '../../../../page/board/eventBus';
import qingdao from '@/assets/geojson/370200.json';
const props = defineProps({
  height: {
    type: Number,
    default: 33,
  },
  id: {
    type: [String, Number],
    default: 'cicle-' + Date.now(),
  },
  echartsJson: {
    type: String,
    default: '',
  },
  // 图标类型
  dataType: {
    type: String,
    default: '',
  },
  searchForm: {
    type: Object,
    default: {},
  },
  iden: {
    type: String,
    default: '',
  },
});
const colors = [
  {
    min: 20,
    color: '#012a9e',
  },
  {
    min: 10,
    color: '#0550ee',
  },
  {
    min: 0,
    color: '#29a3ff',
  },
];
const loading = ref(false);
let mapData = []; //地图上的业务数据
let chart;
EventBus.on((event, params) => {
  if (event == 'refresh') {
    if (!params) queryData();
    if (params && params.from != 'county_name') {
      queryData(params);
    }
    if (params && params.from == 'county_name') queryData();
  }
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
  loading.value = true;
  const data = await smartBrainApi.queryCommonData({ brainReportIndicatorId: props.id, ...props.searchForm });
  mapData = data.rows;
  await getMap();
  loading.value = false;
};

onMounted(() => {
  queryData();
});
//获取区域对应的数据
const getDataByCode = (code) => {
  const data = mapData.find((item) => item[1] == code);
  if (!data) return 0;
  return data[4] || 0;
};
//获取区域对应的数据
const getDataByCodeTotal = (code) => {
  const data = mapData.find((item) => item[1] == code);
  if (!data) return 0 + '%';
  let total = 0;
  mapData.forEach((item) => {
    total = total + item[4];
  });
  return ((data[4] / total) * 100).toFixed(2) + '%' || 0 + '%';
};
//生成地址
const getMap = async () => {
  chart && chart.destroy();
  const scene = new Scene({
    id: 'map' + props.id + props.iden,
    logoVisible: false,
    map: new Mapbox({
      pitch: 0,
      style: 'blank',
      maxZoom: 8,
      minZoom: 8,
    }),
  });
  scene.on('loaded', () => {
    addLayer(scene, qingdao);
    chart = scene;
  });
};
const payTypeCheck = ref<any>('');

//添加地图图层
const addLayer = (scene, mapJson) => {
  var labeldata = mapJson.features.map(function (fe) {
    fe.properties.code = fe.properties.adcode;
    fe.properties.value = fe.properties.name;
    fe.properties.longitude = fe.properties.centroid[0];
    fe.properties.latitude = fe.properties.centroid[1];
    return fe.properties;
  });
  //点图层
  const pointLayer = new PointLayer({ zIndex: 5 })
    .source(labeldata, {
      parser: {
        type: 'json',
        x: 'longitude',
        y: 'latitude',
      },
    })
    .size(8)
    .shape('name', 'text')
    .color('#fff');
  scene.addLayer(pointLayer);

  const polygonLayer = new PolygonLayer({ autoFit: true })
    .source(mapJson)
    .color('adcode', (adcode) => {
      const data = getDataByCode(adcode);
      if (!data) return 'rgba(239,243,255,0.05)';
      if (data >= 20) {
        return '#012a9e';
      }
      if (data >= 10) {
        return '#0550ee';
      }
      return '#29a3ff';
    })
    .active(true)
    .shape('fill')
    .style({
      opacity: 1,
    });
  //图层边界
  const lineLayer = new LineLayer({ zIndex: 2 }).source(mapJson).color('rgb(93,112,146)').size(0.6).style({
    opacity: 1,
  });

  //点击事件
  // polygonLayer.on('click', (param) => {
  //   if (param.from != 'county_name' && param.name != payTypeCheck.value) {
  //     EventBus.emit('refresh', {
  //       from: 'county_name',
  //       ...param,
  //       data: {
  //         name: param.feature.properties.name,
  //       },
  //     });
  //     payTypeCheck.value = {
  //       name: param.feature.properties.name,
  //       adcode: param.feature.properties.adcode,
  //       lngLat: param.lngLat,
  //     };
  //   } else {
  //     payTypeCheck.value = '';
  //     EventBus.emit('refresh');
  //   }
  // });
  showPop(scene, mapJson);

  scene.addLayer(polygonLayer);
  scene.addLayer(lineLayer);

  polygonLayer.on('mousemove', (param) => {
    if (param.feature.properties.name != payTypeCheck.value.name) {
      payTypeCheck.value = {
        name: param.feature.properties.name,
        adcode: param.feature.properties.adcode,
        lngLat: param.lngLat,
      };
      // scene.removeLayer(lineLayerActive);
      showPop(scene, mapJson);
    }
  });
};
let lineLayerActive;
const showPop = (scene, mapJson) => {
  if (payTypeCheck.value != '') {
    lineLayerActive = new LineLayer({ zIndex: 3 })
      .source({
        type: 'FeatureCollection',
        features: [],
      })
      .color('#ff6600')
      .size(1)
      .style({
        opacity: 1,
      });
    let mapJsonParam = {
      ...mapJson,
      features: mapJson.features.filter((item) => item.properties.adcode == payTypeCheck.value.adcode),
    };
    lineLayerActive.setData(mapJsonParam);
    scene.addLayer(lineLayerActive);

    const popup = new Popup({
      offsets: [0, 0],
      closeButton: false,
    })
      .setLnglat(payTypeCheck.value.lngLat)
      .setHTML(
        `<span style="color:#fff;">地区: ${payTypeCheck.value.name}</span><br><span style="color:#fff;">订单数量: ${getDataByCode(
          payTypeCheck.value.adcode,
        )}</span><br><span style="color:#fff;">订单占比: ${getDataByCodeTotal(payTypeCheck.value.adcode)}</span>`,
      );

    scene.addPopup(popup);
  }
};
</script>
<style scoped>
.map {
  width: 100%;
  height: 76vh;
  position: relative;
}

.map-desc {
  position: absolute;
  left: 10px;
  top: 10px;
  font-size: 14px;
  z-index: 99;
}

.tooltip-content {
  line-height: 1.5;
}

span.square,
span.circle {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin-right: 2px;
  background: rgba(239, 243, 255, 0.05);
}

span.circle {
  border-radius: 50%;
}
</style>
<style>
.l7-popup-anchor-bottom .l7-popup-tip {
  border-top-color: rgba(0, 11, 45, 0.8) !important;
}

.l7-popup-anchor-bottom .l7-popup-content {
  background: rgba(0, 11, 45, 0.8) !important;
}
</style>
