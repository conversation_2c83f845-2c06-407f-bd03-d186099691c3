

type keys = "INSIDE_PLAN" | "OUTSIDE_PLAN";

/**
 * 年度计划类型
 */
export const MonthPlanItemTypeConstant = {
    INSIDE_PLAN: { "code": 1, "desc": "年度创建" },
    OUTSIDE_PLAN: { "code": 2, "desc": "临时追加" },

    ofCode: (code?: number): { "code": number, "desc": string } | null => {
        for (const key in MonthPlanItemTypeConstant) {
            const item = MonthPlanItemTypeConstant[key as keys];
            if (code === item.code) {
                return item;
            }
        }
        return null;
    }
}