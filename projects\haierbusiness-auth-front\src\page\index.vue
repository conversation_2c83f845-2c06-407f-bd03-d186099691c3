<script setup lang="ts">

import { Spin as hSpin, Space as hSpace, Input as hInput, InputPassword as hInputPassword, Carousel as hCarousel, Button as hButton, Col as hCol, Result as hResult, Row as hRow, TabPane as hTabPane, Tabs as hTabs } from 'ant-design-vue';
import { onMounted, ref } from 'vue';


import { ILoginResult, LoginTypeConstant, HeaderConstant } from '@haierbusiness-front/common-libs';
import { loadDataFromLocal, saveDataToLocal } from '@haierbusiness-front/utils';
import iamCodeLogin from './iamCodeLogin.vue';
import iamTokenLogin from './iamTokenLogin.vue';
import localLogin from './localLogin.vue';
import travelSessionLogin from './travelSessionLogin.vue';
import supermarketTokenLogin from './supermarketTokenLogin.vue';
import wyyTokenLogin from './wyyTokenLogin.vue'
import usernameLogin from './usernameLogin.vue'


const urlSearchParams = ref(new URLSearchParams(window.location.search));
const loginType = ref();
let redirectUrl = urlSearchParams.value.get("redirect_url");

const goToRedirect = () => {
  if (!redirectUrl) {
    redirectUrl = "about:blank";
  }
  const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false);
  let url = redirectUrl;
  const urlObject = new URL(url)
  if (urlObject.search) {
    if (urlObject.hash) {
      url = urlObject.origin + urlObject.pathname + urlObject.search + "&" + HeaderConstant.TOKEN_KEY.key + "=" + token + urlObject.hash
    } else {
      url = redirectUrl + "&" + HeaderConstant.TOKEN_KEY.key + "=" + token
    }
  } else {
    if (urlObject.hash) {
      url = urlObject.origin + urlObject.pathname + "?" + HeaderConstant.TOKEN_KEY.key + "=" + token + urlObject.hash
    } else {
      url = redirectUrl + "?" + HeaderConstant.TOKEN_KEY.key + "=" + token
    }
  }
  console.log("跳转url：",url)
  location.href = url
}

(() => {
  loginType.value = urlSearchParams.value.get("login_type");
  
  const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false);
  if (token) {
    //goToRedirect();
  }
})();

const loginSuccess = (result: ILoginResult) => {
  console.log("登录成功--iam----->>>>", result);
  localStorage.setItem('iam_token', result?.data?.extended?.iamToken);
  localStorage.setItem('refresh_token', result?.data?.extended?.refreshToken);
  goToRedirect()
}

</script>

<template>
  <div v-if="(!loginType) || Number(loginType) === LoginTypeConstant.LOCAL.key">
    <local-login :param="{ urlSearch: urlSearchParams }" @loginSuccess="loginSuccess"></local-login>
  </div>
  <div v-if="Number(loginType) === LoginTypeConstant.IAM_CODE.key">
    <iam-code-login :param="{ urlSearch: urlSearchParams }" @loginSuccess="loginSuccess"></iam-code-login>
  </div>
  <div v-if="Number(loginType) === LoginTypeConstant.IAM_TOKEN.key">
    <iam-token-login :param="{ urlSearch: urlSearchParams }" @loginSuccess="loginSuccess"></iam-token-login>
  </div>
  <div v-if="Number(loginType) === LoginTypeConstant.TRAVEL_SESSION.key">
    <travel-session-login :param="{ urlSearch: urlSearchParams }" @loginSuccess="loginSuccess"></travel-session-login>
  </div>
  <div v-if="Number(loginType) === LoginTypeConstant.SUPERMARKET_TOKEN.key">
    <supermarket-token-login :param="{ urlSearch: urlSearchParams }" @loginSuccess="loginSuccess"></supermarket-token-login>
  </div>
  <div v-if="Number(loginType) === LoginTypeConstant.WYY_TOKEN.key">
    <wyy-token-login :param="{ urlSearch: urlSearchParams }" @loginSuccess="loginSuccess"></wyy-token-login>
  </div>
  <!-- hwork 用户名登录 -->
  <div v-if="Number(loginType) === LoginTypeConstant.USERNAME_TOKEN.key">
    <username-login :param="{ urlSearch: urlSearchParams }" @loginSuccess="loginSuccess"></username-login>
  </div>
</template>

