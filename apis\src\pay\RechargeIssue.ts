import {
    RtoPageParams,
    RtoPageRes
} from '@haierbusiness-front/common-libs'
import { download, get, post,downloadPost } from '../request'

export const RechargeIssueApi = {
    
    /**
     * 充值下发 
     */
    list: (params: RtoPageParams): Promise<RtoPageRes> => {
        return post('incentive/api/admin/issue/page', params)
    },
        
    /**
     * 充值下发详情 
     */
    info: (params: RtoPageParams): Promise<RtoPageRes> => {
        return get('incentive/api/admin/issue/get', params)
    },
    /**
     * 充值下发导出
     */
    exportIssueBill: (params: RtoPageParams): Promise<void> => {
        return downloadPost('incentive/api/admin/issue/export', params)
    },
    

}