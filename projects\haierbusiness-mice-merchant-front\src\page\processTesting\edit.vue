<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Input as HInput,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Switch as hSwitch,
  message,
} from 'ant-design-vue';
import { reactive, ref, watch, onMounted, provide, inject } from 'vue';

const state = reactive({});

// 接收顶层组件的通信
const provideName = inject('provideName');

onMounted(async () => {});
</script>

<template>
  <!-- 流程编辑 -->
  <div>
    <div class="">流程编辑</div>
    <div class="">{{ provideName }}</div>
  </div>
</template>

<style lang="less" scoped></style>
