import { HotelSyncRecordRes,Result } from '@haierbusiness-front/common-libs'
import { download, get, post, filepost, originalGet } from '../request'

export const hotelApi = {
    // 酒店列表
    getHotelList: (params: any): Promise<void> => {
        return get('/hotel-mapping/api/hotel/page', params)
    },
    // 获取国旅酒店映射列表
    getMappingHotelList: (params: any): Promise<void> => {
        return get('/hotel-mapping/api/hotel/getMappingHotelList', params)
    },
    // 解除国旅酒店映射
    deleteProviderHotelMapping: (params: any): Promise<void> => {
        return post('/hotel-mapping/api/hotel/deleteProviderHotelMapping', params)
    },

    //去除疑似
    removeSuspectedCases: (params: any): Promise<void> => {
        return get('/hotel-mapping/api/mapping/removeSuspectedCases', params)
    },

    // 供应商酒店列表
    getProvideHotelList: (params: any): Promise<void> => {
        return get('/hotel-mapping/api/providerHotel/page', params)
    },
    // 关联酒店
    providerHotelMappingHotel: (params: any): Promise<void> => {
        return post('/hotel-mapping/api/hotel/providerHotelMappingHotel', params)
    },

    // 同步管理分页/hotelSync/page
    hotelSyncPage: (params: any): Promise<void> => {
        return get('/hotel-mapping/api/hotelSync/page', params)
    },

    // 查询酒店同步记录 
    hotelSyncRecordPage: (params: HotelSyncRecordRes): Promise<void> => {
        return get('/hotel-mapping/api/hotelSync/hotelSyncRecordPage', params)
    },

    // 查询酒店聚合记录 
    hotelMappingRecordPage: (params: HotelSyncRecordRes): Promise<void> => {
        return get('/hotel-mapping/api/mapping/hotelMappingRecordPage', params)
    },

    // 聚合明细列表
    gethotelMergePageList: (params: HotelSyncRecordRes): Promise<void> => {
        return get('/hotel-mapping/api/mapping/hotelMergePage', params)
    },

    // 根据id 获取酒店聚合记录信息
    getHotelSyncMappingRecord: (params: any): Promise<void> => {
        return get('/hotel-mapping/api/mapping/getHotelSyncMappingRecord', params)
    },

    // 酒店聚合明细映射酒店
    hotelMergeMappingHotel: (params: any): Promise<void> => {
        return post('/hotel-mapping/api/mapping/hotelMergeMappingHotel', params)
    },

    // 聚合提交落地
    hotelMappingRecordSubmit: (params: any): Promise<void> => {
        return get('/hotel-mapping/api/mapping/hotelMappingRecordSubmit', params)
    },
    // 酒店评分记录列表
    getHotelMergeRecordPage: (params: any): Promise<void> => {
        return get('/hotel-mapping/api/mapping/hotelMergeRecordPage', params)
    },

    // 获取品牌下拉列表
    getHotelBrandProviderMapList: (params: any): Promise<void> => {
        return get('/hotel-mapping/api/hotelBrand/page', params)
    },

    // 房型转换规则列表
    roomTransformatRuleList: (params: any): Promise<void> => {
        return get('/hotel-mapping/api/roomTransformatRule/page', params)
    },

    // 根据id 获取同步信息 
    getHotelSync: (params: any): Promise<void> => {
        return get('/hotel-mapping/api/hotelSync/getHotelSync', params)
    },

    // 根据id 获取同步数量信息 
    hotelSyncRecordStatistics: (params: any): Promise<void> => {
        return get('/hotel-mapping/api/hotelSync/hotelSyncRecordStatistics', params)
    },

    // 映射记录
    manualMappingRecordList: (params: any): Promise<void> => {
        return get('/hotel-mapping/api/manualMappingRecord/page', params)
    },

    // 查询供应商是否有关联
    getMappingHotelByProviderCode: (params: any): Promise<void> => {
        return get('/hotel-mapping/api/providerHotel/getMappingHotelByProviderCode', params)
    },

    // 确认同步完成
    confirmHotelSync: (params: any): Promise<void> => {
        return get('/hotel-mapping/api/hotelSync/confirmHotelSync', params)
    },

    // 导出转换规则
    export: (params: any): Promise<Result> => {
        return download('/hotel-mapping/api/manualMappingRecord/export', params)
    }
}   