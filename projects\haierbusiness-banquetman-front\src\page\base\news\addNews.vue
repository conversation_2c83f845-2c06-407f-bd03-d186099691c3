<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem,
  Form as hForm,
  FormItem as hFormItem,
  InputNumber as hInputNumber,
  Input as hInput
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import { banquetNewsApi,banquetApi } from '@haierbusiness-front/apis';
import {
  BApplyListRecord,
  BApplyPerson,
  BanquetStatusEnum
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import Editor from '@haierbusiness-front/components/editor/Editor.vue'
import { message } from 'ant-design-vue';
import type { IDomEditor } from "@wangeditor/editor"

import router from '../../../router'
// const router = useRouter()
const store = applicationStore();

const currentRouter = ref()
const route = ref(getCurrentRoute());
const id = route.value?.query?.id;
const detail = ref<BApplyListRecord>();

const { loginUser } = storeToRefs(store);


const showMore = ref(false)


onMounted(async () => {
  currentRouter.value = await router
  if(id){
    getDetail(id);
  }
})

const getDetail = (id: number) => {

banquetApi.getNotificationDetail({id}).then((res) => {
  formState.value = res;
});
};

const onEditorChange = (editor: IDomEditor) => {
  formState.value.content = editor.getHtml()
}

const uploadUrl = import.meta.env.VITE_UPLOAD_URL

const formState = ref({
  title: '',
  content: '',
  notificationSort:''
})

const onFinish = () => {
  banquetNewsApi.saveNotification(formState.value).then(res => {
    message.success('新增成功!');
    currentRouter.value.push({
      path: `/base/news`,

    })
  })
}

</script>

<template>
  <div>
    <h-row justify="space-between" style="padding: 20px;">
      <h-col style="font-size: 18px;">新增公告</h-col>
      <h-col><h-button type="link" @click="currentRouter.back(-1)">返回</h-button></h-col>
    </h-row>
    <div style="background-color: #ffff;height: 100%;width: 100%;padding: 30px 60px;overflow: auto;">
      <h-form :model="formState" name="basic" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }" autocomplete="off"
        @finish="onFinish" >
        <h-form-item label="通知标题" name="title" :rules="[{ required: true, message: '请输入标题!' }]">
          <h-input v-model:value="formState.title" />
        </h-form-item>
        <h-form-item label="排序" name="notificationSort" :rules="[{ required: fasle, message: '请输入序号' }]">
          <h-input-number :min="1" :precision="0" v-model:value="formState.notificationSort" />
        </h-form-item>
        <h-form-item label="通知内容" name="content" :rules="[{ required: true, message: '请输入内容!' }]">
          <editor height="300px" :modelValue="formState.content" @change="onEditorChange" style="z-index: 20"
            :uploadUrl="uploadUrl" />
        </h-form-item>

        <h-form-item :wrapper-col="{ offset: 8, span: 16 }">
          <h-button style="margin-right: 40px"  @click="currentRouter.back(-1)">取消</h-button>
          <h-button type="primary" html-type="submit">提交</h-button>
        </h-form-item>

      </h-form>

    </div>


  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

:deep(.ant-descriptions-item-label) {
  width: 200px;
}

:deep(.ant-descriptions-item-content) {
  width: 300px;
}
</style>
