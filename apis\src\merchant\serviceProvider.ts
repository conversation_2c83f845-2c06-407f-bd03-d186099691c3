import { MerchantBank, MerchantBusiness, MerchantContract } from '@haierbusiness-front/common-libs';
import { download, get, post, originalPost } from '../request';

export const serviceProviderApi = {
  getBusinessList: (params = {}): Promise<{ records: MerchantBusiness[], total: number }> => {
    return get('merchant/api/merchant/getPage', params);
  },
  getBusinessDetail: (params = {}): Promise<MerchantBusiness> => {
    return get('merchant/api/merchant/getOneById', params);
  },
  getContractList: (params = {}): Promise<MerchantContract[]> => {
    return get('merchant/api/merchantContract/getList', params);
  },
  getBankList: (params = {}): Promise<MerchantBank[]> => {
    return get('merchant/api/merchantBank/getList', params);
  },
  addBusiness: (params = {}) => {
    return originalPost('merchant/api/merchant/insert', params);
  },
  editBusiness: (params = {}) => {
    return originalPost('merchant/api/merchant/updateById', params);
  },
  addContract: (params = {}) => {
    return originalPost('merchant/api/merchantContract/insert', params);
  },
  editContract: (params = {}) => {
    return originalPost('merchant/api/merchantContract/updateById', params);
  },
  deleteContract: (params = {}) => {
    return get('merchant/api/merchantContract/deleteById', params);
  },
  addBank: (params = {}) => {
    return originalPost('merchant/api/merchantBank/insert', params);
  },
  editBank: (params = {}) => {
    return originalPost('merchant/api/merchantBank/updateById', params);
  },
  deleteBank: (params = {}) => {
    return get('merchant/api/merchantBank/deleteById', params);
  },
  setTag: (params = {}) => {
    return originalPost('merchant/api/merchant/setTag', params);
  },
};
