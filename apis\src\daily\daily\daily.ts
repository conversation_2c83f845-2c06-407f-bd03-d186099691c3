import {
    IDailyReportBatchPlatformEvaluateDTO,
    IDailyReportDetailRequestDTO,
    IDailyReportDetailResponseDTO,
    IDailyReportListRequestDTO,
    IDailyReportListResponseDTO,
    IDailyReportPlatformEvaluateDTO,
    IDailyReportSaveRequestDTO
} from '@haierbusiness-front/common-libs'
import { errorHttpMessageHandle, get, post, download } from '../../request'
import { errorModal, isMobile } from '@haierbusiness-front/utils';
import { showFailToast } from 'vant';

const showError = (error: any) => {
    if (error.response) {
        let errorMessage: string = "";
        const httpStatus = error.response.status;
        if (httpStatus === 401) {
            errorMessage = '当前未登录,拒绝访问!' + error.response.data.message
            if (isMobile()) {
                showFailToast(errorMessage)
            } else {
                errorModal(errorMessage)
            }
        } else if (httpStatus === 403) {
            errorMessage = error.response.data.message + "请尝试重新登录！"
            if (isMobile()) {
                showFailToast(errorMessage)
            } else {
                errorModal(errorMessage)
            }
        } else if (httpStatus === 503) {
            errorMessage = "服务无效！"
            if (isMobile()) {
                showFailToast(errorMessage)
            } else {
                errorModal(errorMessage)
            }
        } else {
            errorMessage = error.response.data.message;
            if (isMobile()) {
                showFailToast(errorMessage)
            } else {
                errorModal(errorMessage)
            }
        }
    } else {
        if (error.code === 'parameter_error') {
            let errorMessage = "当前内容填写不完全, 请仔细检查后提交！\n\r" + error.message.replace('参数错误!', '');
            if (isMobile()) {
                showFailToast(errorMessage)
            } else {
                errorModal(errorMessage)
            }
        } else {
            if (isMobile()) {
                showFailToast(error.message)
            } else {
                errorModal(error.message)
            }
        }
    }
}

export const dailyReportApi = {

    list: (params: IDailyReportListRequestDTO): Promise<IDailyReportListResponseDTO[]> => {
        return get('/daily/api/daily-report/list', params)
    },

    detail: (params: IDailyReportDetailRequestDTO): Promise<IDailyReportDetailResponseDTO> => {
        return get('/daily/api/daily-report/detail', params)
    },

    save: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return post('/daily/api/daily-report/save', params, undefined, (err: any) => {
            showError(err)
            return errorHttpMessageHandle(err)
        })
    },

    getMonthPlanReportList: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return get('/daily/api/report/getMonthPlanReportList', params, undefined, (err: any) => {
            showError(err)
            return errorHttpMessageHandle(err)
        })
    },

    getPersonReportList: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return get('/daily/api/report/getPersonReportList', params, undefined, (err: any) => {
            showError(err)
            return errorHttpMessageHandle(err)
        })
    },

    getConferenceList: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return get('/daily/api/report/getConferenceList', params, undefined, (err: any) => {
            showError(err)
            return errorHttpMessageHandle(err)
        })
    },

    getQuantifierlssueList: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return get('/daily/api/report/getQuantifierlssueList', params, undefined, (err: any) => {
            showError(err)
            return errorHttpMessageHandle(err)
        })
    },
    
    getPersonDetailsList: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return get('/daily/api/report/getPersonDetailsList', params, undefined, (err: any) => {
            showError(err)
            return errorHttpMessageHandle(err)
        })
    },
        
    getPersonDetailsDataList: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return get('/daily/api/report/getPersonDetailsList', params, undefined, (err: any) => {
            showError(err)
            return errorHttpMessageHandle(err)
        })
    },

    exportPersonReport: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return download('/daily/api/report/exportPersonReport', params)
    },
    exportMonthPlanReport: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return download('/daily/api/report/exportMonthPlanReport', params)
    },
    exportConferenceReport: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return download('/daily/api/report/exportConferenceReport', params)
    },
    exportQuantifierlssueReport: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return download('/daily/api/report/exportQuantifierlssueReport', params)
    },
    exportPersonDetailsReport: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return download('/daily/api/report/exportPersonDetailsReport', params)
    },


    getTypeNameList: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return get('/daily/api/report/getTypeNameList', params, undefined, (err: any) => {
            showError(err)
            return errorHttpMessageHandle(err)
        })
    },
    getQuantifierStateList: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return get('/daily/api/report/getQuantifierStateList', params, undefined, (err: any) => {
            showError(err)
            return errorHttpMessageHandle(err)
        })
    },

    /**
     * 平台评价
     */
    platformEvaluate: (params: IDailyReportPlatformEvaluateDTO): Promise<void> => {
        return post('/daily/api/daily-report/platform-evaluate', params, undefined, (err: any) => {
            showError(err)
            return errorHttpMessageHandle(err)
        })
    },

    /**
     * 平台默认批量评价
     */
    batchPlatformEvaluate: (params: IDailyReportBatchPlatformEvaluateDTO): Promise<void> => {
        return post('/daily/api/daily-report/batch-platform-evaluate', params, undefined, (err: any) => {
            showError(err)
            return errorHttpMessageHandle(err)
        })
    },
}