<script setup lang="ts">
import { computed, ref } from 'vue';
import { CloseOutlined } from '@ant-design/icons-vue';
import { ConsultantCard } from './components';

interface Props {
  title: string;
  modelValue: boolean;
  items: {
    name: string;
    desc: string;
    avatar: string;
    link?: string;
    isRecommend: boolean;
    performance: { label: string; value: string }[];
    statistics: { label: string; value: string }[];
  }[];
}
const props = defineProps<Props>();

const emit = defineEmits(['update:modelValue', 'search', 'assign']);

const modelOpen = computed({
  get: () => props.modelValue,
  set: (v) => emit('update:modelValue', v),
});

const searchValue = ref<string>('');

const onSearch = () => {
  emit('search', searchValue.value);
};
</script>

<template>
  <!-- 顾问抽屉组件 -->
  <a-drawer
    v-model:open="modelOpen"
    class="meeting-consultant-drawer"
    placement="right"
    width="750"
    :title="props.title"
    :closable="false"
    :bodyStyle="{ background: '#F7F8FC', display: 'flex', flexDirection: 'column', paddingBottom: '80px' }"
  >
    <div class="search-container">
      <a-input-search
        class="search"
        allowClear
        v-model:value="searchValue"
        placeholder="工号/姓名"
        enter-button="搜索"
        :bordered="false"
        @search="onSearch"
      />
    </div>
    <div>
      <consultant-card v-for="(item, index) of items" :key="index" :data="item" @assign="$emit('assign', item)" />
    </div>
    <template #extra>
      <CloseOutlined :style="{ cursor: 'pointer' }" @click="modelOpen = false" />
    </template>
  </a-drawer>
</template>

<style scoped lang="less">
.meeting-consultant-drawer {
  .search-container {
    display: flex;
    justify-content: center;
    align-items: center;
    .search {
      width: 434px;
      background: #ffffff;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.04);
      border-radius: 4px;
      :deep(button.ant-input-search-button) {
        border-radius: 4px !important;
      }
    }
  }
}
</style>
