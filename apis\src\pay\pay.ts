import {
    ICancelRequest,
    IConfirmRequest, IConfirmResponse,
    IFindTypeRequest,
    IPageResponse,
    IPaymentRecord,
    IPaymentRecordListRequest, IPaymentRecordListResponse,
    IRefundRecordListRequest, IRefundRecordListResponse, IRefundRequest, IRefundResponse, IRefundNotifyRequest, ICancelResponse,
    ISearchPaidRecordRequest, IPayLogsRequest, IBusinessLog, IPayBusinessDetailsUrl,
    IRecordListRequest, ICoinFlowingWaterRequest, ICoinListResponse, IAccountResponse
} from '@haierbusiness-front/common-libs'
import { download, get, post } from '../request'

export const payApi = {
    /**
     * 查询支付类型
     */
    payTypes: (params: IFindTypeRequest): Promise<number[]> => {
        return get('pay/api/pay/find_type', params)
    },

    /**
     * 查询支付成功记录
     */
    searchPaidRecord: (params: ISearchPaidRecordRequest): Promise<IPaymentRecord> => {
        return get('pay/api/pay/search_paid_record', params)
    },

    /**
     * 查询支付记录
     */
     searchRecord: (params: ISearchPaidRecordRequest): Promise<IPaymentRecord> => {
        return get('pay/api/pay/search_record', params)
    },

    /**
     * 查询支付业务详情地址
     */
    businessDetailsUrl: (params: IPayBusinessDetailsUrl): Promise<string> => {
        return get('pay/api/pay/business/details', params)
    },

    /**
     * 查询支付记录(只查询自己的)
     */
    paymentRecordList: (params: IPaymentRecordListRequest): Promise<IPageResponse<IPaymentRecordListResponse>> => {
        return get('pay/api/pay/search_records', params)
    },

    /**
     * 查询支付记录(查询所有)
     */
    paymentRecordAllList: (params: IPaymentRecordListRequest): Promise<IPageResponse<IPaymentRecordListResponse>> => {
        return get('pay/api/pay/search_all_records', params)
    },

    /**
     * 导出-查询支付记录(查询所有)
     */
    exportPaymentRecordAllList: (params: IPaymentRecordListRequest): Promise<void> => {
        return download('pay/api/pay/search_all_records/export', params)
    },

    /**
     * 查询云支付记录(查询所有包含支付和退款)
     */
    recordAllList: (params: IRecordListRequest): Promise<IPageResponse<IPaymentRecordListResponse>> => {
        return get('pay/api/pay/personalWelfareAccountRecordPage', params)
    },

    /**
     * 导出-查询支付记录(查询所有)
     */
     exportRecordAllList: (params: IRecordListRequest): Promise<void> => {
        return download('pay/api/pay/personalWelfareAccountRecordList/export', params)
    },

    /**
     * 查询退款记录(只查询自己的)
     */
    refundRecordList: (params: IRefundRecordListRequest): Promise<IPageResponse<IRefundRecordListResponse>> => {
        return get('pay/api/pay/search_refund_records', params)
    },

    /**
     * 查询退款记录(查询所有)
     */
    refundRecordAllList: (params: IRefundRecordListRequest): Promise<IPageResponse<IRefundRecordListResponse>> => {
        return get('pay/api/pay/search_all_refund_records', params)
    },

    /**
     * 导出-查询支付记录(查询所有)
     */
    exportRefundRecordAllList: (params: IRefundRecordListRequest): Promise<void> => {
        return download('pay/api/pay/search_all_refund_records/export', params)
    },

    /**
     * 查询日志
     */
    logs: (params: IPayLogsRequest): Promise<IPageResponse<IBusinessLog>> => {
        return get('pay/api/pay/logs', params)
    },

    /**
     * 退款
     */
    refund: (params: IRefundRequest): Promise<IRefundResponse> => {
        return post('pay/api/pay/refund', params)
    },

    /**
     * 退款再次通知
     */
    refundNotifyAgain: (params: IRefundNotifyRequest): Promise<void> => {
        return post('pay/api/pay/refund/notify/business', params)
    },

    /**
     * 确认
     */
    confirm: (params: IConfirmRequest): Promise<IConfirmResponse> => {
        return post('pay/api/pay/confirm', params)
    },

    /**
     * 取消
     */
    cancel: (params: ICancelRequest): Promise<ICancelResponse> => {
        return post('pay/api/pay/cancel', params)
    },

    /**
     * 查询福利积分流水
     */
     getCoinFlowingWater: (params: ICoinFlowingWaterRequest): Promise<IPageResponse<ICoinListResponse>> => {
        return get('pay/api/coin/haier/getCoinFlowingWater', params)
    },

    getBalanceByUsername: (username: string, searchType: number): Promise<IAccountResponse> => {
        return get('pay/api/coin/haier/getBalanceByUsername', { username, searchType })
    },

    /**
     * 导出-查询支付记录(查询所有)
     */
     exportCoinFlowingWaterList: (params: IRecordListRequest): Promise<void> => {
        return download('pay/api/coin/haier/coinFlowingWater/export', params)
    },

    /**
     * 导出-查询支付记录(查询所有)
     */
     exportVirtualAccountList: (params: IRecordListRequest): Promise<void> => {
        return download('pay/api/virtual/account/export', params)
    },
}