<!-- 行程确认 -->
<script lang="ts" setup>
import { computed, createVNode, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';
import outPersonModal from './components/outPersonModal.vue';
import budgetModal from './components/budgetModal.vue';
import budgetDetailModal from './components/budgetDetailModal.vue';
import travelStandardsModal from './components/travelStandardsModal.vue';

import baseInfo from './components/baseInfo.vue';
import actualBudge from './components/actualBudge.vue';
import fileUpload from './components/fileUpload.vue';
import 'animate.css';

import {
  Anchor as hAnchor,
  Button as hButton,
  Spin as hSpin,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Checkbox as hCheckbox,
  Form as hForm,
  FormItem as hFormItem,
  Row as hRow,
  Col as hCol,
  Popconfirm as hPopconfirm,
  Modal
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
  
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { tripApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import relativeTime from 'dayjs/plugin/relativeTime';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from "@haierbusiness-front/utils";

const store = applicationStore();
const { loginUser } = storeToRefs(store);
const route = ref(getCurrentRoute());



const confirmList = ref<Array<any>>([])

const rules: Record<string, Rule[]> = {
  travelUserNo: [{ required: true, message: '请选择出差人', trigger: 'change' }],
};
const formRef = ref();
const labelCol = { span: 4 };
const wrapperCol = { span: 20 };

const spinning = ref<boolean>(false)

const applyDetail = ref<any>({})


// 查询城市列表
const cityOptions = ref({});

// 选择人员
const changeUser = (item: any, value: string, option: any) => {
  item.travelUserName = option.travelUserName
  item.travelUserNo = option.travelUserNo
  item.reimburFinish = option.reimburFinish
  item.reimburseNum = option.reimburseNum
  changeTravelerList()
}

const travelerList = ref<Array<any>>([]);

const defaultTripList = ref<Array<any>>([]);

const applyNo = route.value?.query?.applyNo;
onMounted(async () => {

  if (applyNo) {
    spinning.value = true
    var res: any = await queryChangeDetailByApplyNo(applyNo);
    // 获取城市数据
    // cityOptions.value = await tripApi.district();

    defaultTripList.value = JSON.parse(JSON.stringify(res?.tripList))

    spinning.value = false

    // 判断一下行程中是否包含当前登录人,如果包含的话默认首要确认当前登录人,否则首先确认主出差人行程
    if (res.travelerList.filter((item: any) => item.travelUserNo == loginUser?.value?.username).length > 0) {
      res.travelUserName = loginUser?.value?.nickName
      res.travelUserNo = loginUser?.value?.username
    } else {
      res.travelUserName = res.travelerList.find((item: any) => item.mainFlag == '1').travelUserName;
      res.travelUserNo = res.travelerList.find((item: any) => item.mainFlag == '1').travelUserNo;
    }

    applyDetail.value = JSON.parse(JSON.stringify(res))

    // res.travelUserName = loginUser?.value?.nickName
    // res.travelUserNo = loginUser?.value?.username

    
    // 出差申请单创建的行程列表

    // 如果是经办人进来确认,可以批量确认 否则只能确认自己的行程
    if (res.operUserNo == loginUser?.value?.username) {
      travelerList.value = res.travelerList.filter((item: any) => item.travelUserType == '0');
      res.reimburseNum = res.travelerList.filter((item: any) => item.travelUserType == '0' && item.mainFlag == '1')[0].reimburseNum
      res.reimburFinish = res.travelerList.filter((item: any) => item.travelUserType == '0' && item.mainFlag == '1')[0].reimburFinish

      if (res.reimburFinish == 20) {
        // 如果确认过行程单,重新回填确认内容
        const confirmRes: any = await getConfirmDetail(res.id, travelerList.value.find(item => item.mainFlag).travelUserNo)
        res.tripList = confirmRes.tripList
        res.endDate = confirmRes.tripList[confirmRes.tripList.length - 1].endDate
        res.beginDate = confirmRes.tripList[0].beginDate
      }

    } else {
      travelerList.value = res.travelerList.filter((item: any) => item.travelUserNo == loginUser?.value?.username);
      res.reimburseNum = travelerList.value[0]?.reimburseNum
      res.reimburFinish = travelerList.value[0]?.reimburFinish

      if (res.reimburFinish == 20) {
        // 如果确认过行程单,重新回填确认内容
        const confirmRes: any = await getConfirmDetail(res.id, travelerList.value[0].travelUserNo)
        res.tripList = confirmRes.tripList
        res.endDate = confirmRes.tripList[confirmRes.tripList.length - 1].endDate
        res.beginDate = confirmRes.tripList[0].beginDate

      }
    }



    
    res.subsidy = res.travelReserveFlag == 1 ? true : false

    res.cityList = []
    res?.tripList?.forEach((item: any, index: number) => {
      if (index == 0) {
        res.cityList.push({
          cityCode: item.beginCityCode,
          city: item.beginCityName,
          syId: item.beginCityCodeSy,
          date: item.beginDate,
        });
      }
      res.cityList.push({
        cityCode: item.endCityCode,
        city: item.endCityName,
        syId: item.endCityCodeSy,
        date: item.endDate,
      });

      item?.tripDetailMapList?.forEach((tripMap: any) => {
        tripMap.personIdList = tripMap.travelApplyTripDetailList.map((tt: any) => tt.travelUserSyId);
      });

    });


    confirmList.value = [res]
    changeTravelerList()
  }

});

// 根据出差申请单号与出差人工号获取 行程确认信息 
const getConfirmDetail = async (applyId: number | string, userCode: string | number) => {
  const params = {
    applyId: applyId,
    userCode: userCode
  }

  const res: any = await tripApi.getRealTrip(params);
  res?.tripList?.forEach((element: any) => {
    element.beginCityCode = element.realBeginCityCode
    element.beginCityName = element.realBeginCityName
    element.beginDate = element.realBeginDate
    element.endCityCode = element.realEndCityCode
    element.endCityName = element.realEndCityName
    element.endDate = element.realEndDate
    element.tripDetailMapList = []
  });
  return res
}

// 根据单号获取详情
const queryChangeDetailByApplyNo = async (code: string) => {
  const res: any = await tripApi.queryChangeDetailByApplyNo(code);
  return res
}
const businessList = import.meta.env.VITE_BUSINESS_INDEX_URL;

const processUrl = import.meta.env.VITE_BUSINESS_PROCESS_URL
// 审批流页面
const goToApproval = () => {
  if (!confirmList.value[0].workFlowId) {
    errorModal('暂无审批流信息!');
    return
  }
  const url = processUrl + `?code=${confirmList.value[0].workFlowId}#/details`
  window.open(url);
}

// 根据当前选择的出差人 展示出可选人员
const changeTravelerList = () => {
  travelerList.value.forEach((item: any) => {
    item.disabled = false;

    confirmList.value.forEach((confirm: any) => {
      if (item.travelUserNo == confirm.travelUserNo || item.reimburseNum > 0) {
        item.disabled = true;
      }
    })
  });
}

// 添加行程单
const addConfirm = async() => {
  changeTravelerList()
  if (confirmList.value.length < travelerList.value?.filter(item => !item.disabled)?.length +1 ) {
    
    let newConfirm = {}
    if(confirmList.value.length > 0) {
      newConfirm = JSON.parse(JSON.stringify(confirmList.value[0]))
    }else {
      newConfirm = JSON.parse(JSON.stringify(applyDetail.value))
    }
    newConfirm.travelUserNo = travelerList.value.find((item: any) => !item.disabled)?.travelUserNo
    newConfirm.travelUserName = travelerList.value.find((item: any) => !item.disabled)?.travelUserName
    newConfirm.reimburFinish = travelerList.value.find((item: any) => !item.disabled)?.reimburFinish
    newConfirm.reimburseNum = travelerList.value.find((item: any) => !item.disabled)?.reimburseNum

    // 如果有确认过的行程进行回显
    if (newConfirm.reimburFinish == 20) {
      const confirmRes: any = await getConfirmDetail(newConfirm.id, newConfirm.travelUserNo)
      newConfirm.tripList = confirmRes.tripList

    } else {
      newConfirm.tripList = defaultTripList.value
    }

    newConfirm.cityList = []
    newConfirm?.tripList?.forEach((item: any, index: number) => {
      if (index == 0) {
        newConfirm.cityList.push({
          cityCode: item.beginCityCode,
          city: item.beginCityName,
          syId: item.beginCityCodeSy,
          date: item.beginDate,
        });
      }
      newConfirm.cityList.push({
        cityCode: item.endCityCode,
        city: item.endCityName,
        syId: item.endCityCodeSy,
        date: item.endDate,
      });

      item?.tripDetailMapList?.forEach((tripMap: any) => {
        tripMap.personIdList = tripMap.travelApplyTripDetailList.map((tt: any) => tt.travelUserSyId);
      });

    });


    confirmList.value = [...confirmList.value, newConfirm]
  }else {
    errorModal('没有可确认的用户!')
  }
}

// 删除某个行程单
const deleteConfirm = (index: number) => {
  confirmList.value.splice(index, 1);
  changeTravelerList()

}

const submitLoading = ref(false)
const isBgDate = (beginDate:any, endDate:any) => {
  return new Date(applyDetail.value.beginDate).getTime() == new Date(beginDate).getTime() && new Date(applyDetail.value.endDate).getTime() == new Date(endDate).getTime()
}
// 提交确认行程
const submitConfirmList = () => {
  if(confirmList?.value?.length < 1) {
    errorModal('请先选择行程!')
    return
  }

  Modal.confirm({
    title: '提示',
    content: '请确认提交的行程信息与实际行程一致，如不一致请修改，否则影响补助发放',
    onOk() {
      
      // 判断最后选择的时间是否超过当前时间
      let temp = false

      spinning.value = true
      const params = []
      confirmList.value.forEach(item => {
        if(new Date(item.endDate).getTime() > new Date().getTime()){
          temp = true  
        }
        params.push({
          applyNo: item.applyNo,
          travelUserNo: item.travelUserNo,
          travelUserName: item.travelUserName,
          subsidy: item.subsidy,
          realBeginDate: item.beginDate,
          realEndDate: item.endDate,
          // 判断是否变更果订单开始时间,结束时间
          consistency: isBgDate(item.beginDate, item.endDate) ? 1 : 0,
          tripList: item.tripList.map((trip: any) => {
            return {
              realBeginDate: trip.beginDate,
              realEndDate: trip.endDate,
              realBeginCityCode: trip.beginCityCode || trip.realBeginCityCode,
              realBeginCityName: trip.beginCityName,
              realEndCityCode: trip.endCityCode || trip.realEndCityCode,
              realEndCityName: trip.endCityName,
              travelUserNo: item.travelUserNo,
              travelUserName: item.travelUserName

            }
          })
        })

      })
      if(temp) {
        hMessage.error('实际行程的结束日期大于当前日期,无法进行行程确认!')
        spinning.value = false
        return
      }

      tripApi.tripFinish(params).then((res: any) => {
        

        // 在用户点击按钮保存的事件内增加消息通知
        window.parent && window.parent.postMessage({
            type: 'Travel:DetailSubmitInEES'
        }, '*')

        hMessage.success('行程确认成功,2秒后自动跳转至申请单列表页!');
        setTimeout(() => {
          spinning.value = false;
          goToOrderList()
        }, 2000);

      }).catch(err => {
        spinning.value = false
      })
    },
    onCancel() {},
  });

  
}

const goToOrderList = () => {
  const url = businessList + '#' + '/card-order/trip';
  window.open(url,"_self");
}

</script>

<template>
  <h-spin size="large" :spinning="spinning">
    <div class="container">
      <div class="row flex">
        <div class="change-title" v-if="confirmList[0]?.applyNo">
          <h-row class="mb-10">
            <h-col :span="6" style="color: #00000073">申请单号:</h-col>
            <h-col :span="18">{{ confirmList[0].applyNo }}</h-col>
          </h-row>
          <h-row class="mb-10">
            <h-col :span="6" style="color: #00000073">申请时间:</h-col>
            <h-col :span="18">{{ confirmList[0].gmtCreate }}</h-col>
          </h-row>

        </div>


        <div class="main-title">
          <span>出差申请单行程确认</span>
        </div>
        <div class="flex confirm-con">
          <!-- 查看审批流 -->
          <div class="approval-btns">
            <h-button class="common-btn-height" v-if="applyDetail?.type != 1 && applyDetail?.travelReserveFlag != 0" size="small" @click="goToApproval">查看审批流</h-button>
            <h-button v-if="confirmList.length < travelerList.length" type="primary" class="common-btn-height mr-10"
              size="small" @click="addConfirm">添加行程单</h-button>

          </div>
        </div>

        <div class="" style="background: #fff;min-height: 400px;" v-if="confirmList.length<1">
          <a-empty description="请先添加行程单"></a-empty>
        </div>


        <div class="apply-con mb-30 flex" v-for="(item, index) in confirmList" :key="index">
          <div v-if="travelerList.length > 1" class="title whole-line mt-30 text-align-center"
            style="position: relative">
            出差行程单{{ index + 1 }}
            <h-popconfirm title="确定要删除这条行程单吗?" ok-text="确定" cancel-text="取消" @confirm="deleteConfirm(index)">
              <h-button danger type="primary" class="common-btn-height confirm-delete-btn"
                size="small">删除行程单</h-button>

            </h-popconfirm>
          </div>

          <div class="title whole-line mt-30">基本信息</div>


          <div class="whole-line block-con">
            <h-form ref="formRef" :model="item" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol"
              labelAlign="left">

              <h-form-item ref="name" name="travelUserNo">
                <template #label>
                  <span>出差人</span>
                </template>

                <h-select :value="`${item.travelUserName}/${item.travelUserNo}`"
                  :fieldNames="{ label: 'travelUserName', value: 'travelUserNo' }" style="width: 100%"
                  placeholder="选择出差人" @change="(val, option) => changeUser(item, val, option)" :options="travelerList">

                </h-select>

              </h-form-item>
            </h-form>

          </div>

          <!-- 行程与费用 -->
          <div class="whole-line">
            <actual-budge :key="index" :cityList="item.cityList" :isDetail="item?.reimburseNum > 1"
              :creatTripParma="item"></actual-budge>
          </div>

        </div>
      </div>

      <a-affix :offset-bottom="0" id="affix-bottom" class="affix-bottom">
        <div class="save-box flex">
          <div class="box-center">
            <div class="save-box-left font-size-14">
            </div>
            <div class="save-box-right flex">

              <div class="save-btns">
                <h-popconfirm title="确定取消编辑并返回列表页吗?" ok-text="确定" cancel-text="取消" @confirm="goToOrderList">
                  <h-button size="small" class="my-button  mr-10">取消</h-button>
                </h-popconfirm>

                <h-button size="small" class="my-button " type="primary"
                  @click="submitConfirmList">提交</h-button>
              </div>
            </div>
          </div>
        </div>
      </a-affix>
    </div>
  </h-spin>
</template>


<style lang="less" scoped>
@import url(./components/trip.less);
</style>