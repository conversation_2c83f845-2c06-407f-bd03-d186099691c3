<template>
  <div>
    <van-field
      :required="props.required"
      readonly
      :is-link="true"
      input-align="right"
      name="picker"
      :label="props.label"
      @click="showSelectBox"
      error-message-align="right"
    >
    <!--       @click-right-icon.stop="onSwitch"
      right-icon="exchange" -->
    <template #input>
      <div>{{ props.value || props.palceholder || '请选择' }}</div>
    </template>
  </van-field>
    <!-- 业务申请人弹窗 -->
    <van-popup v-model:show="showdataList" position="bottom">
      <div class="out-pop" style="height: 100vh">
        <van-sticky>
          <div style="background: #fff; padding-bottom: 10px">
            <van-nav-bar :title="'选择'+props.label"  left-arrow @click-left="closeMainPersonPop">
              <template #left>
                <van-icon name="arrow-left" color="#000" size="24" />
              </template>
              <template #right>
                <div class="color-main"></div>
              </template>
            </van-nav-bar>
            <!-- <van-cell-group inset style="margin: 10px ; background-color: #fff"> -->
              <!-- <van-field
                @update:model-value="searchMainPerson"
                v-model="searchValue"
                style="background: #f6f7f9;padding-left: 20px;"
                :placeholder="props.palceholdeTip"
              /> -->
              <van-search
                v-model="searchValue"
                show-action
                background="#f6f7f9"
               :placeholder="props.palceholdeTip"
               class="searchBox"
               @clear="searchMainPerson"
              >
              <template #action>
                <div @click="searchMainPerson">搜索</div>
              </template>
              </van-search>
            <!-- </van-cell-group> -->
          </div>
        </van-sticky>
        <van-list
          v-model:loading="mainPersonLoading"
          :finished="mainPersonFinished"
          :finished-text="dataList.length ? '没有更多了' : ''"
          @load="onLoadMainPerson"
        >
          <div
            v-if="dataList.length == 0 && mainPersonFinished"
            style="height: 66vh"
            class="flex align-items-center justify-content-center"
          >
            <!-- <img class="img_empty" src="../assets/image/empty.jpg" alt="" /> -->
          </div>
          <template v-else>
            <van-cell v-for="(item, index) in dataList" :key="index" @click="choseMainPerson(item)">
              <div class="flex align-items-center ">
                <div
                  :class="index % 3 == 0 ? 'blue' : 'yellow'"
                  class="mr-10 ml-10 flex align-items-center justify-content-center img-name"
                >
                  {{ item[props.selectKey] ? item[props.selectKey].slice(0,2) : '未知' }}
                </div>
                <div class="main">
                  <div class="user-name color-main">{{ item[props.selectKey] }}({{ item[props.selectValue] || '未知' }})</div>
                </div>
              </div>
            </van-cell>
          </template>
        </van-list>
      </div>
    </van-popup>
    <van-action-sheet v-model:show="selctSheetShow" :actions="actions" @select="onSelect" />
  </div>
</template>

<script setup lang="ts">
import type { Ref } from 'vue';
import { createVNode, onMounted, reactive, ref, watch, watchEffect, toRefs, computed } from 'vue';
import {
  showFailToast,
  Button as VanButton,
  Form as VanForm,
  Field as VanField,
  CellGroup as VanCellGroup,
  showDialog,
  showSuccessToast,
  NoticeBar,
  Popup as VanPopup,
  showToast
} from "vant";
import { userApi, organizationCenterApi } from '@haierbusiness-front/apis';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ITraveler, ICreatTrip,IAbCodeResponse } from '@haierbusiness-front/common-libs';
import { debounce, values } from 'lodash';
import { emit } from 'process';
import { DataType, usePagination, useRequest } from 'vue-request';

const store = applicationStore();

const { loginUser } = storeToRefs(store);

const actions = [
      { name: '选择预算人' },
      { name: '选择预算主体' },
    ];
// props参数

interface Props {
  businessType?:string;
  palceholder?: string; // 人员
  palceholdeTip?: string;
  selectValue?:string;
  selectKey?:string;
  label?: string;
  value?: string;
  keyWord?: string;
  type?:string;
  required?:boolean;
  api?:any;
  bdCode?:string;
  isCanEmpty?:boolean;
  feeItem?:string;
  applicationCode?:string;
  companyCode?:string;
  accountCode?:string;
  ProjectCode?:string;
}
const props = defineProps<Props>();

const emit = defineEmits(['chose','choseMainInfo']);

const onSwitch =()=>{
  console.log(111111,"99999")
  
}


// 业务申请人相关
const searchValue = ref('');
const showMainInfoBox = ref(false);
const showdataList = ref(false);
const mainPersonLoading = ref(false);
const mainPersonFinished = ref(false);
const selctSheetShow = ref(false)
const mainPersonTotal = ref(0);
const dataList = ref<Array<ITraveler>>([]);


const defaultParams: IUserListRequest = {
  pageNum: 0,
  pageSize: 20,
};

const params: Ref<any> = ref(defaultParams);
const closeMainPersonPop = () => {
  showdataList.value = false;
};
const closeMainInfoBox = () => {
  showMainInfoBox.value = false;
};
const onLoadMainPerson = (type:number) => {
  params.value.pageNum++;
  params.value[props.keyWord] = searchValue.value?searchValue.value:"";
  params.value.bdCode = props.bdCode
  params.value.feeItem = props.feeItem
  params.value.applicationCode = props.applicationCode
  params.value.businessType = props.businessType

  params.value.companyCode = props.companyCode
  params.value.accountCode = props.accountCode
  params.value.ProjectCode = props.ProjectCode
  mainPersonLoading.value = false
  if(props.isCanEmpty&&!searchValue.value) return
  props.api(params.value).then((res) => {
    // 判断是否分页
  if(res.records){
        // 加载状态结束
        mainPersonLoading.value = false;
    if(type){
      dataList.value= res.records
    }else{
      dataList.value = [...dataList.value, ...res.records];
    }
    mainPersonTotal.value = res.total ?? 0;
    // 数据全部加载完成
    if (dataList.value.length >= mainPersonTotal.value) {
      mainPersonFinished.value = true;
    }
  }else{
    mainPersonFinished.value = true;
    dataList.value = [...res];
  }
  });
};

const searchMainPerson = debounce((val: string) => {
  params.value[props.keyWord] = val;
  params.value.pageNum = 0;
  params.value.pageSize = 20;
  mainPersonFinished.value = false;
  dataList.value = [];
  onLoadMainPerson(1);
});

// 获取主体信息
const getAbCodeByUser = async (userName:string) => {
  if(!userName) {
    return showToast('请补充预算信息！')
  }
  await searchAbCodeByUserRun({ userName:userName })
  showMainInfoBox.value = true
}


const choseMainPerson = (item: ITraveler) => {
  // 根据传递的type 如果type = hbc2
  if(props.type == 'hbc2'){
    // 选择支付主体
    console.log(item)
    getAbCodeByUser(item.username)
  }
  showdataList.value = false;
  emit('chose', item)
};

// 选择主体信息
const choseMainInfo = (item: any) => {
  // 根据传递的type 如果type = hbc2
  showMainInfoBox.value = false;
  emit('choseMainInfo', item)
};

// 选择弹窗
const showSelectBox = ()=>{
  if(props.type != 'hbc2'){
    if(props?.type=="deptMust"&&!props.bdCode){
        showToast("请先选择预算部门!")
        return
    }
    if(props?.type=="deptFeeItemMust"&&!props.bdCode){
        showToast("请先选择预算部门!")
        return
    }
    if(props?.type=="deptFeeItemMust"&&!props.feeItem){
        showToast("请先选择费用科目!")
        return
    }
    showdataList.value = true
  }else{
    if(!props.value){
      showdataList.value = true
    }else{
      // 选择操作方式
      selctSheetShow.value = true
    }
  }
}
const onSelect = (item:any) =>{
  if(item.name=='选择预算人'){
    showdataList.value = true
  }else{
    showMainInfoBox.value = true
  }
  selctSheetShow.value  = false;
  
}


</script>

<style scoped lang='less'>
.flex {
  display: flex;
}
.flex-column {
  flex-direction: column;
}
.align-items-center {
  align-items: center;
}
.justify-content-center {
  justify-content: center;
}
.justify-content-between {
  justify-content: space-between;
}
.flex-warp {
  flex-wrap: wrap;
}

.mr-10 {
  margin-right: 10px !important;
}
.mr-20 {
  margin-right: 20px;
}
.color-main {
    color: #0073e5;
  }
.out-pop {
  .main {
    flex: 1;
  }
  .img-name {
    width: 30px;
    height: 30px;
    
    color: #fff;
    font-size: 10px;
    border-radius: 30px;
  }
  
  .blue {
    background-color: #0073e5;
  }
  .yellow {
    background-color: #faad14;
  }
  .add-person {
    margin: 16px;
    margin-bottom: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
    font-size: 12px;
  }
  .user-name {
    text-align: left;
    font-size: 12px;
  }
  .phone {
    text-align: left;
    color: #8c8c8c;
  }
}
.searchBox :deep(.van-search__content){
  padding-left: 0;
}
.searchBox :deep(.van-cell){
  padding: 0 10px;
}
</style>