<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Modal as hModal,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
} from "ant-design-vue";
import { ColumnType } from "ant-design-vue/lib/table/interface";
import { Key } from "ant-design-vue/lib/vc-table/interface";
import {
  DownOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
} from "@ant-design/icons-vue";
import { payApi, parkadeApi } from "@haierbusiness-front/apis";
import {
  VirtualAccountTypeConstant,
  HaierBudgetSourceConstant,
  VirtualScopeConstanty,
  ParkadeRecordReq,
  ParkadeExportLogReq,
  PayStatusConstant,
  PayTypeConstant,
  VirtualAccountChangeTypeConstant,
} from "@haierbusiness-front/common-libs";
import { errorModal, routerParam } from "@haierbusiness-front/utils";
import Eloading from "@haierbusiness-front/components/loading/Eloading.vue";
import dayjs, { Dayjs } from "dayjs";
import { computed, onMounted, ref, watch } from "vue";
import { DataType, usePagination, useRequest } from "vue-request";
import { useRouter } from "vue-router";
import { useEditDialog, useDelete } from "@haierbusiness-front/composables";
import EditDialog from "./edit-dialog.vue";
import UserDialog from "./user-dialog.vue";

import exportLogModal from "./exportLogModal.vue";

const router = useRouter();
const columns: ColumnType[] = [
  {
    title: "订单编号",
    dataIndex: "orderCode",
    fixed: "left",
    width: "240px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "车辆系统编号",
    dataIndex: "carSystemCode",
    fixed: "left",
    width: "240px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "充值园区",
    dataIndex: "zoneName",
    width: "240px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "结算账户",
    dataIndex: "settlementAccount",
    width: "240px",
    align: "center",
    ellipsis: true,
  },

  {
    title: "推送金额",
    dataIndex: "pushedAmount",
    width: "240px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "充值金额",
    dataIndex: "rechargeAmount",
    width: "120px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "到账金额",
    dataIndex: "arrivalAmount",
    width: "120px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "账号所属人",
    dataIndex: "accountUser",
    width: "170px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "充值时间",
    dataIndex: "rechargeTime",
    width: "150px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "充值状态",
    dataIndex: "rechargeStatus",
    width: "150px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "汇总状态",
    dataIndex: "collectStatus",
    width: "150px",
    align: "center",
    ellipsis: true,
  },
  
  {
    title: "汇总单号",
    dataIndex: "collectCode",
    width: "200px",
    align: "center",
    ellipsis: true,
  },
 
];
const searchParam = ref<ParkadeRecordReq>({});
const { data, run: listApiRun, loading, current, pageSize } = usePagination(
  parkadeApi.list
);

const reset = () => {
  searchParam.value = {};
};

// 获取园区列表
const zoneList = ref<any>([])
const fieldNames = {
  label: "zoneName",
  value: "zoneCode",
}
const filterOption = (input: string, option: any) => {
  return option.zoneName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
const getZoneList = () => {
  parkadeApi.zoneList().then(res => {
    zoneList.value = res
  })
}


const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: "center" },
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};


const {
  data: exportListData,
  run: exportListApiRun,
  loading: exportListLoading,
} = useRequest(parkadeApi.export);

const type = computed(() => {
  return VirtualAccountChangeTypeConstant.toArray();
});

const advancedSearchVisible = ref(false);



const startBeginAndEnd = ref<[Dayjs, Dayjs]>();
watch(
  () => startBeginAndEnd.value,
  (n: any, o: any) => {
    if (n) {
      searchParam.value.changeBegin = n[0];
      searchParam.value.changeEnd = n[1];
    } else {
      searchParam.value.changeBegin = undefined;
      searchParam.value.changeEnd = undefined;
    }
  }
);

const exportLogModalRef = ref();

const showExportLog = () => {
  exportLogModalRef.value.show();
}

onMounted(() => {
  handleTableChange({ current: 1, pageSize: 10 })
  getZoneList()
});
</script>

<template>
  <div
    style="
      background-color: #ffff;
      height: 100%;
      width: 100%;
      padding: 10px 10px 0px 10px;
      overflow: auto;
    "
  >
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="orderCode">订单编号:</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="orderCode"
              v-model:value="searchParam.orderCode"
              placeholder="订单编号"
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="carSystemCode">车辆系统编号:</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="carSystemCode"
              v-model:value="searchParam.carSystemCode"
              placeholder="车辆系统编号"
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="collectCode">汇总单号:</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="collectCode"
              v-model:value="searchParam.collectCode"
              placeholder="汇总单号"
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="accountUserName">账户所属人:</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="accountUserName"
              v-model:value="searchParam.accountUserName"
              placeholder="账户所属人"
              autocomplete="off"
              allow-clear
            />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="rechargeTimeStart">开始充值时间:</label>
          </h-col>
          <h-col :span="4">
              <h-date-picker v-model:value="searchParam.rechargeTimeStart" format="YYYY-MM-DD HH:mm:ss" :show-time="{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="rechargeTimeEnd">结束充值时间:</label>
          </h-col>
          <h-col :span="4">
            <h-date-picker v-model:value="searchParam.rechargeTimeEnd" format="YYYY-MM-DD HH:mm:ss" :show-time="{ defaultValue: dayjs('23:59:59', 'HH:mm:ss') }" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="changeBusinessCode">充值园区：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              placeholder="充值园区"
              v-model:value="searchParam.zoneCode"
              style="width: 100%"
              allow-clear
              show-search
              :options="zoneList"
              :filter-option="filterOption"
              :fieldNames="fieldNames"
            >
            </h-select>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="settlementAccount">结算账户:</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="settlementAccount"
              v-model:value="searchParam.settlementAccount"
              placeholder="结算账户"
              autocomplete="off"
              allow-clear
            />
          </h-col>

          
          
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="searchState">充值状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              placeholder="充值状态"
              ref="select"
              v-model:value="searchParam.rechargeStatus"
              style="width: 100%"
              allow-clear
            >
              <h-select-option value="1">充值成功</h-select-option>
              <h-select-option value="0">充值失败</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="collectStatus">汇总状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              placeholder="汇总状态"
              ref="select"
              v-model:value="searchParam.collectStatus"
              style="width: 100%"
              allow-clear
            >
              <h-select-option value="10">未汇总</h-select-option>
              <h-select-option value="20">已汇总</h-select-option>
              <h-select-option value="30">汇总失败</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <!-- <template v-if="advancedSearchVisible">
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
            <h-col :span="2" style="text-align: right">
              <label for="startBeginAndEnd">支付发起日期：</label>
            </h-col>
            <h-col :span="4">
              <h-range-picker
                v-model:value="startBeginAndEnd"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </h-col>
          </h-row>
        </template> -->
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
           
            
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button
              style="margin-right: 10px"
              type="primary"
              @click="handleTableChange({ current: 1, pageSize: 10 })"
            >
              <SearchOutlined />
              查询
            </h-button>

            <h-button type="primary"  style="margin-right: 10px" :loading="exportListLoading"
              @click="exportListApiRun(searchParam);">
              <UploadOutlined />
              导出
            </h-button>

            <h-button type="primary" 
              @click="showExportLog">
              导出记录
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="dataSource"
          :pagination="pagination"
          :scroll="{ y: 550 }"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ text, column, record }">
            <template v-if="column.dataIndex === 'source'">
              {{ text == 1 ? "福利积分" : text == 2 ? "代金券" : "附件凭证" }}
            </template>

            <template v-if="column.dataIndex === 'rechargeStatus'">
              <a-tag v-if="text == '1'" color="#87d068">充值成功</a-tag>
              <a-tag v-if="text == '0'" color="#2db7f5">充值失败</a-tag>
            </template>

            <template v-if="column.dataIndex === 'collectStatus'">
              {{ text == 10 ? '未汇总' : text == 20 ? '已汇总': '汇总失败' }}
            </template>
            <template v-if="column.dataIndex === 'type'">
              {{ VirtualAccountChangeTypeConstant.ofType(text)?.desc }}
            </template>

            

           
          </template>
        </h-table>
      </h-col>
    </h-row>
    <!-- 导出记录 -->
    <exportLogModal ref="exportLogModalRef" type="10" />
    
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.masking {
  background-color: #********;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
