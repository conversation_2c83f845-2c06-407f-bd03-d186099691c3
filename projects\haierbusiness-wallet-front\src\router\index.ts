


const modules = import.meta.glob('/src/page/**/*.vue');

import { baseRouterConstructor } from '@haierbusiness-front/utils';

const routes = [{
    path: '/',
    redirect: '/wallet'
},
{
    path: '/wallet',
    component: () => import('../page/wallet/index.vue')
},
{
    path: '/recharge',
    component: () => import('../page/recharge/index.vue')
},
{
    path: '/userbill',
    component: () => import('../page/userbill/index.vue')
},
{
    path: '/bill',
    component: () => import('../page/bill/index.vue')
}

];

const router = baseRouterConstructor("haierbusiness-pay", modules, false, undefined, routes)
export default router;
