import {
    ICbBillData, MiceResult, IBillSaveData
} from '@haierbusiness-front/common-libs'
import { get, originalPost, downloadPost } from '../request'


export const billApi ={
    getBill: (code: string, billType?: number | undefined): Promise<MiceResult<ICbBillData>> => {
        let url = `/businesstravel/haiermice/mice/merconfe/bill/getBill?cbBillRequest.code=${code}`
        if(billType) {
            url += `&cbBillRequest.billType=${billType}`
        }
        return originalPost(url)
    },

    saveBill: (data: IBillSaveData, errorNotify?: ((error: any) => any) | null): Promise<MiceResult<any>> => {
        return originalPost('/businesstravel/haiermice/mice/merconfe/bill/upload', data, undefined, errorNotify)
    },

    uploadFile: (params: FormData): Promise<MiceResult<any>> => {
        return originalPost('/businesstravel/haiermice/mice/file/obs/fileUpload', params, { 'content-type': 'multipart/form-data' })
    },

    downloadFile: (code: string, errorNotify?: ((error: any) => any) | null): Promise<MiceResult<any>> => {
        return downloadPost('/businesstravel/haiermice/mice/settlement/bill/exportExcel', {
            code
        }, undefined, errorNotify)
    },
}