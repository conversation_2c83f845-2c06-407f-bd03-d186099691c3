@import "./scroll-style.less";
// 主题色
@import "@haierbusiness-front/components/theme/theme.css";

@baseWidth: 100vw;
@baseHeight: 100vh;

.base-container {
    height: @baseHeight;
    width: @baseWidth;
}

.float-left {
    float: left;
}

.text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.required::before {
    display: inline-block;
    margin-right: 4px;
    font-family: SimSun, sans-serif;
    font-size: 14px;
    line-height: 1;
    color: #ff4d4f;
    content: '*';
}

.align-right {
    text-align: right;
}

.align-left {
    text-align: left;
}

.lable {
    margin-bottom: 24px;
    margin-right: 15px;
}

.default-input {
    width: 240px
}


.fresh-from-item> :first-child {
    margin: 0;
}