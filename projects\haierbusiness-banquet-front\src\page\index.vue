<script setup lang="ts">
import { onMounted, ref, watch,onUnmounted } from 'vue';
import eFooter from '@haierbusiness-front/components/layout/Footer.vue';
import eHeader from '@haierbusiness-front/components/layout/Header.vue';
import discount from '@/assets/image/discount/discount.png'
import giftTop from '@/assets/image/discount/gift-top.png'
import giftBottom from '@/assets/image/discount/gift-bottom.png'
import { useSocketStore } from '@haierbusiness-front/utils/src/store/socket';

const store = useSocketStore()

const healthUrl = import.meta.env.VITE_HEALTH_PORT
const websocketUrl = import.meta.env.VITE_WEBSOCKET_URL

onMounted(() => {
  const giftTop = document.querySelector('.gift-top-img')
  giftTop?.classList.add('move')
  const giftBottom = document.querySelector('.gift-bottom-img')
  giftBottom?.classList.add('move-bottom')

  giftTop?.addEventListener('transitionend', function() {
    if(giftTop.classList.contains('move')) {
      giftTop.classList.remove('move')
      giftBottom?.classList.remove('move-bottom')
    } else {
      giftTop?.classList.add('move')
      giftBottom?.classList.add('move-bottom')
    }
  })
  // debugger
  // 第一次加载以后   后续就不需要了
  // store.healthCheck(healthUrl)

  var url = websocketUrl + 7100;
  // store.connection(url)

})

onUnmounted(()=>{
  store.closeWs();
})

watch(() => store.msg, messages => {
  if(messages === "order"){
    
  }  
})



</script>

<template>
    <div style="min-height:100vh" class="home">
    <router-view v-slot="{ Component }">
      <keep-alive>
        <component :is="Component" :key="$route.name" v-if="$route.meta.keepAlive" />
      </keep-alive>
      <component :is="Component" :key="$route.name" v-if="!$route.meta.keepAlive" />
    </router-view>
    </div>
</template>

<style scoped lang="less">

.pointer {
  cursor: pointer;
}

.img-cover {
    width: 100%;
    height: 100%;
    z-index: 1;
}

.home {
  font-family: 'HarmonyBold';
  position: relative;
  overflow: hidden;

  .discount-inlet {
    position: fixed;
    top: 200px;
    right: 10px;
    width: 65px;
    height: 168px;
    border-radius: 33px;

    .background-img{
      width: 65px;
      height: 168px;
      border-radius: 33px;
      background: linear-gradient( 180deg, #F97911 0%, #FF5D01 0%, #FFD1A5 100%);
      position: relative;
      overflow: hidden;
      display: flex;
      justify-content: center;

      .font-con {
        padding-left: 1px;
        margin-left: -1px;
        margin-top: 12px;
        width: 24px;
        height: 113px;
        font-size: 22px;
        color: #FEFCF6;
        line-height: 24px;
        transition: all 1s;
        font-weight: bold;
      }

      .img-bottom-con {
        position: absolute;
        width: 65px;
        height: 24.38px;
        top: 150.3px;
      }

      .bottom-con {
        position: relative;
        overflow: hidden;
      }

      .gift-bottom-img{
        transition: all 1s;
      }

      .move-bottom {
        transform: scale(1.2) translateY(4px);
        transition: all 1s;
      }
      
    }

    .img-top-con {
      position: absolute;
      width: 65px;
      height: 34.3px;
      top: 117px;
    }

    .gift-top-img{
      transition: all 1s;
    }
    
    .move {
      transform: scale(1.2);
      transition: all 1s;
    }
  }

  .mr-20 {
    margin-right: 20px;
  }

  .mr-13{
    margin-right: 13px;
  }

  .mr-32{
    margin-right: 13px;
  }

  .mr-26{
    margin-right: 13px;
  }

  .font-10 {
    font-size: 10px;
  }

  .icon {
    width: 22px;
    height: 22px;
  }



  .vertical {
    width: 1px;
  }

  .pl-8 {
    padding-left: 8px
  }

  .flex {
    display: flex;
  }

  .align-items {
    align-items: center;
  }
}

.sub-title {
  color: #3983E5;
  font-size: 14px;
  font-weight: 400;
}

:root {
  // font-family: HarmonyBold, Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}
</style>

<style>
.harmonyBold {
    font-family: 'HarmonyBold';
}

@keyframes discountMove{
  0 {
    transform: perspective(200px) translateZ(0px);
  }
  20% {
    transform: perspective(200px) translateZ(60px);
  }
  40%, 100% {
    transform: perspective(200px) translateZ(0px);
  }
}

</style>
