<template>
    <h-row type="flex" justify="center" :gutter="[30, 30]" style="margin-left: 0; margin-right: 0; height: 15vh"
        background="rgba(0,0,0,0)">
        <h-col :flex="2" class="content" v-for="(column, index) in columns" :key="index">
            <div v-if="column[0] == '服务费'">
                <h-popover>
                    <template #content>
                        <p v-for="(item, i) in column[3]" :key="i">
                            {{ item[0] }}
                            {{
                                (
                                    Number(item[1] / column[1]) * 100 / 10000
                                ).toFixed(2) + "%"
                            }}
                            {{ item[1] + "元" }}
                        </p>
                    </template>
                    <div class="num">
                        <CountTo :start-val="0" :end-val="column[1]" />
                        <span class="unit" v-if="column[2]">{{
                            column[2]
                        }}</span>
                        <!-- <span class="unit" v-if="column.name[0]=='退票率'">%</span> -->
                    </div>
                    <div class="title"><span>▶</span>{{ column[0] }}</div>
                </h-popover>
            </div>
            <div v-else>
                <div class="num">
                    <CountTo :start-val="0" :end-val="column[1]" />
                    <span class="unit" v-if="column[2]">{{ column[2] }}</span>
                    <!-- <span class="unit" v-if="column.name[0]=='退票率'">%</span> -->
                </div>
                <div class="title"><span>▶</span>{{ column[0] }}</div>
            </div>
        </h-col>
    </h-row>
</template>
<script setup lang="ts">

import {
    Badge as hBadge,
    Progress as hProgress,
    Button as hButton,
    Col as hCol,
    DatePicker as hDatePicker,
    Form as hForm,
    FormItem as hFormItem,
    Input as hInput,
    Modal as hModal,
    Popconfirm as hPopconfirm,
    Popover as hPopover,
    RangePicker as hRangePicker,
    Row as hRow,
    Select as hSelect,
    SelectOption as hSelectOption,
    Table as hTable,
    Tag as hTag,
    message,
    TableProps
} from 'ant-design-vue';
import CountTo from "@/components/vue-count-to/src";
import {
    queryOverviewPersonTime,
    queryOverviewAmount,
    queryOverviewTripNum,
    queryTransactionAmount,
    queryOverviewInsureNum,
    queryOverviewAirTicket,
    queryOverviewHotel,
    queryOverviewServiceSum,
    queryhotelServiceSum,
    queryGroundService,
} from "@haierbusiness-front/apis/src/data/board/travel";
import { ref, onMounted } from "vue";
import { EventBus } from "../../../eventBus";


import { findTreesByUrls } from '@haierbusiness-front/utils'
import { storeToRefs } from "pinia";
import globalPinia from "@haierbusiness-front/utils/src/store/store";
import { applicationStore } from "@haierbusiness-front/utils/src/store/applicaiton";
const { resource } = storeToRefs(applicationStore(globalPinia))
const currentModal = findTreesByUrls(resource.value, ['/data/board/travel/index'])
const currentPower: any = findTreesByUrls(currentModal, ["Accumulative"])

const max = 100000; //判断是否加w
const columns: any = ref([]);
const loading = ref(false);
EventBus.on((event) => {
    if (event == "refresh") queryData();
});
onMounted(() => {
    queryData()
})
//获取总数
const getAmount = async () => {
    const res2 = await queryTransactionAmount();
    let amount = 0;
    if (res2.rows[0]) {
        if (res2.rows[0][0]) {
            amount = res2.rows[0][0];
        }
    }
    // let amount = 0;
    // for (let i in queryOverviewAmount) {
    //     const data  = await queryOverviewAmount[i]();
    //     console.log(i, data);
    //     const { rows } = data;
    //     amount += rows[0][0];
    // }
    // if (amount < max) {
    //     return ["成交金额", amount.toFixed(0)];
    // }
    return ["成交金额", (amount / 10000).toFixed(0), "w"];
    // return ["成交金额", (amount / 10000).toFixed(0), "w"];
};
const queryData = async () => {
    loading.value = true;
    const amount = await getAmount();
    const _columns = [amount];
    const res0 = await queryOverviewPersonTime();
    const res1 = await queryOverviewTripNum();
    // const res2 = await queryOverviewInsureNum();
    //  const res2 = await queryTransactionAmount();
    const res3 = await queryOverviewAirTicket();
    const res4 = await queryOverviewHotel();
   // const res5 = await queryOverviewServiceSum();
    //获取酒店服务费
    //const hotelServiceData = await queryhotelServiceSum();
    //将酒店服务费添加到服务费列表
    // if (hotelServiceData.rows[0][0]) {
    //     res5.rows.push(["酒店", hotelServiceData.rows[0][0]]);
    // }
 //   const res6 = await queryGroundService();
    _columns.unshift(getColumnData(res0));
    _columns.push(getColumnData(res1));
    //_columns.push(getServiceData(res5));
    _columns.push(getColumnData(res3));
    _columns.push(getColumnData(res4));
    // _columns.push(getColumnData(res6.data));
    columns.value = _columns;
    // data.columns.forEach((item,index)=>{
    //     if(item.name[0]=="成交金额"||item.name[0]=="政策节省"){
    //         data.rows[0][index] = (data.rows[0][index]/10000).toFixed(0)-0
    //     }
    // })

    const arr = currentPower[0].children.map((item: { name: string }) => item.name)
    columns.value = columns.value.filter((item: { name: string }) => arr.includes(item[0]))
    loading.value = false;
};
function sum(arr) {
    var a = 0;
    for (var i = 0; i < arr.length; i++) {
        a += arr[i];
    }
    return a;
}
const getServiceData = (data) => {
    const { columns, rows } = data;
    let numArr = rows.map((item) => item[1]);
    let num = sum(numArr);
    if (num < max) {
        return [columns[0].name[0], num, "", rows];
    }
    return [columns[0].name[0], (num / 10000).toFixed(0), "w", rows, num];
};
const getColumnData = (data) => {
    const { columns, rows } = data;
    const num = rows[0][0];
    if (num < max) {
        return [columns[0].name[0], num];
    }
    return [columns[0].name[0], (num / 10000).toFixed(0), "w"];
};
</script>
<style scoped lang="less">
@import url(@/assets/style/board/accumulative.less);

.content {
    height: 15vh;
}

.num {
    width: 100%;
    font-size: 4vh;

    .unit {
        font-size: 2vh;
    }
}

.title {
    width: 100%;
    font-size: 2vh;
}
</style>
