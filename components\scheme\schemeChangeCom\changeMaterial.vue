<script setup lang="ts">
// 方案变更-布展物料
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';
import { MaterialTypeConstant } from '@haierbusiness-front/common-libs';

const props = defineProps({
  schemeInfo: {
    type: Object,
    default: {},
  },
  schemeCacheInfo: {
    type: Object,
    default: {},
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  schemeChangeType: {
    // schemeBargainingEdit - 方案议价, schemeBargainingView - 议价查看, schemeChangeEdit - 方案变更, schemeChangeView - 方案变更查看, schemeWinBidView - 方案中标查看
    type: String,
    default: '',
  },
});

const emit = defineEmits(['materialPriceEmit', 'schemeMaterialEmit']);

const oldSchemeList = ref<array>([]);
const newSchemeList = ref<array>([]);

const subtotal = ref<number>(0); // 小计

watch(
  () => [props.schemeInfo, props.schemeCacheInfo],
  () => {
    // console.log(
    //   '%c [ 布展物料 ]-24',
    //   'font-size:13px; background:pink; color:#bf2c9f;',
    //   props.schemeInfo.material,
    //   props.schemeInfo.material?.materialDetails,
    // );

    if (
      (props.schemeInfo.material && props.schemeInfo.material.materialDetails) ||
      (props.schemeCacheInfo.material && props.schemeCacheInfo.material.materialDetails)
    ) {
      oldSchemeList.value = JSON.parse(JSON.stringify(props.schemeInfo))?.material?.materialDetails || [];

      if (props.isSchemeCache && props.schemeCacheInfo) {
        // 缓存 - 反显
        newSchemeList.value = props.schemeCacheInfo?.material?.materialDetails || [];
      } else {
        // 议价、议价查看、 变更查看
        newSchemeList.value = JSON.parse(JSON.stringify(oldSchemeList.value));
      }

      // 小计
      subtotal.value = 0;
      newSchemeList.value.forEach((e) => {
        if (e.schemeUnitPrice && e.schemeMaterialNum) {
          subtotal.value += e.schemeUnitPrice * e.schemeMaterialNum;
        }
      });

      emit('materialPriceEmit', subtotal.value);
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

const schemePlanLabelList = ['物料类型', '规格说明', '数量', '单价'];

const changePrice = (index: number) => {
  if (newSchemeList.value[index].biddingPrice) {
    newSchemeList.value[index].planPrice =
      newSchemeList.value[index].biddingPrice * newSchemeList.value[index].schemeMaterialNum;
  }

  const isAllPriceWrite = newSchemeList.value.every((e) => e.planPrice && e.planPrice > 0);
  subtotal.value = 0;

  if (isAllPriceWrite) {
    newSchemeList.value.forEach((e) => {
      subtotal.value += e.planPrice;
    });

    emit('materialPriceEmit', subtotal.value);
  }
};

// 暂存
const materialTempSave = () => {
  emit('schemeMaterialEmit', {
    schemeMaterial: {
      miceDemandMaterialId: props.schemeCacheInfo.material.id,
      demandTotalPrice: props.schemeCacheInfo.material.demandTotalPrice,
      schemeTotalPrice: props.schemeCacheInfo.material.demandTotalPrice,
      // description: props.schemeCacheInfo.material.description,
      materialDetails: [...newSchemeList.value],
    },
  });
};

// 校验
const materialSub = () => {
  let isVerPassed = true;

  // newSchemeList.value.forEach((e, i) => {
  //   if (e.schemeUnitPrice === null || e.schemeUnitPrice === undefined) {
  //     message.error('请输入' + e.demandDate + '布展物料' + (i + 1) + '单价');

  //     isVerPassed = false;
  //     return;
  //   }
  // });

  if (isVerPassed) {
    materialTempSave();
  }

  return isVerPassed;
};

defineExpose({ materialSub, materialTempSave });

onMounted(async () => {});
</script>

<template>
  <!-- 布展物料 -->
  <div class="scheme_vehicle">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>布展物料</span>
    </div>

    <div class="common_table mt16">
      <!-- 左侧 -->
      <div class="common_table_l">
        <div class="scheme_plan_table" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '布展物料' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ MaterialTypeConstant.ofType(item.type)?.desc || '-' }}
                </template>
                {{ MaterialTypeConstant.ofType(item.type)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.specs || '-' }}
                </template>
                {{ item.specs || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeMaterialNum || '-' }}
                </template>
                {{ item.schemeMaterialNum || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元' : '-' }}
                </template>
                {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元' : '-' }}
              </a-tooltip>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  item.schemeUnitPrice && item.schemeMaterialNum
                    ? '¥' + formatNumberThousands(item.schemeUnitPrice * item.schemeMaterialNum)
                    : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPrice && item.schemeMaterialNum">
                {{ item.schemeMaterialNum + '*' + item.schemeUnitPrice + '(单价)' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_table" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '布展物料' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ MaterialTypeConstant.ofType(item.type)?.desc || '-' }}
                </template>
                {{ MaterialTypeConstant.ofType(item.type)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.specs || '-' }}
                </template>
                {{ item.specs || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeMaterialNum || '-' }}
                </template>
                {{ item.schemeMaterialNum || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元' : '-' }}
                </template>
                {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元' : '-' }}
              </a-tooltip>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  item.schemeUnitPrice && item.schemeMaterialNum
                    ? '¥' + formatNumberThousands(item.schemeUnitPrice * item.schemeMaterialNum)
                    : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPrice && item.schemeMaterialNum">
                {{ item.schemeMaterialNum + '*' + item.schemeUnitPrice + '(单价)' }}
              </div>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_vehicle {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_material.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  :deep(.ant-input-number .ant-input-number-input) {
    height: 24px;
    padding: 0 5px;
    text-align: end;

    width: 84px;
    font-weight: 500;
    font-size: 14px;
    color: #1868db;
    text-align: right;
    border-bottom: 1px solid #4e5969;
  }

  .p0 {
    padding: 0 !important;
  }
}
</style>
