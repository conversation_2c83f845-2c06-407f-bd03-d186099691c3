<template>
    <template v-if="props.layout == 'row'">
        <h-col :span="6" v-for="(item, i) in props.data" :key="i">
            <div class="col-title"> {{ getName(item) }}
            </div>
            <div class="col-chart">
                <component :is="components.get(item)" :type="props.type"></component>
            </div>
        </h-col>
    </template>
    <template v-else>
        <div v-for="(item, i) in props.data" :key="i">
            <template v-if="item == 'Map' || item == 'ChinaMap'">
                <component :is="components.get(item)" :type="props.type"></component>
            </template>
            <template v-else-if="item == 'BusinessTrend'">
                <div class="col-title col-title-l " :class="props.type == '异地会议' ? 'mt-10' : ''">
                    {{ getName(item) }}
                    <div class="col-title-extra">
                        <span class="button" v-for="(tab, index) in tabs" :key="index" @click="active = index">
                            {{ tab }}
                        </span>
                        <img :style="{
                            transform: `translate(${active * 52
                                }px)`,
                        }" class="active" src="@/assets/image/bigscreen/icon-title-btn-acitve.png" alt="" />
                    </div>
                </div>
                <div class="col-chart">
                    <component :is="components.get(item)" :date-type="active" :type="props.type"></component>
                </div>
            </template>

            <template v-else>
                <div class="col-title">{{ getName(item) }}</div>
                <div class="col-chart">
                    <component :is="components.get(item)" :height="33" :type="props.type">
                    </component>
                </div>
            </template>
        </div>
    </template>
</template>
<script setup lang="ts">
import {
    Badge as hBadge,
    Progress as hProgress,
    Button as hButton,
    Col as hCol,
} from 'ant-design-vue';
import { ref, onMounted, markRaw, defineAsyncComponent } from "vue";
const components = markRaw(new Map<string, any>);
//组件Accumulative
components.set(
    'Accumulative',
    defineAsyncComponent(() => import('./accumulative.vue')),
);
components.set(
    'AccumulativeSlim',
    defineAsyncComponent(() => import('./accumulativeSlim.vue')),
);
components.set(
    'TypePercentage',
    defineAsyncComponent(() => import('./typePercentage.vue')),
);
components.set(
    'StarPercentage',
    defineAsyncComponent(() => import('./starPercentage.vue')),
);


components.set(
    'SettleRank',
    defineAsyncComponent(() => import('./settleRank.vue')),
);
components.set(
    'CostPercentage',
    defineAsyncComponent(() => import('./costPercentage.vue')),
);
components.set(
    'InsuranceTrend',
    defineAsyncComponent(() => import('./insuranceTrend.vue')),
);


components.set(
    'BusinessTrend',
    defineAsyncComponent(() => import('./businessTrend.vue')),
);
components.set(
    'Map',
    defineAsyncComponent(() => import('./map.vue')),
);
components.set(
    'ChinaMap',
    defineAsyncComponent(() => import('./mapChina.vue')),
);


components.set(
    'HotelRank',
    defineAsyncComponent(() => import('./hotelRank.vue')),
);
components.set(
    'CityRank',
    defineAsyncComponent(() => import('./cityRank.vue')),
);
components.set(
    'HandlingDepartmentRank',
    defineAsyncComponent(() => import('./handlingDepartmentRank.vue')),
);
components.set(
    'ProcessPercentage',
    defineAsyncComponent(() => import('./processPercentage.vue')),
);

const tabs = ["年", "月", "日"];
const active = ref(1);

const props = defineProps({
    data: Array<string>,
    layout: {
        type: String,
        default: "colums",
    },
    type: {
        type: String,
        default: "青岛会议",
    },
})

// 获取name
const getName = (status: number | string) => {
    const resultMap: any = {
        'Accumulative': "累计成交",
        'AccumulativeSlim': "累计成交",
        'TypePercentage': "会议类型分布",
        'StarPercentage': "酒店星级分布",
        'BusinessTrend': "会议业务趋势",
        'SettleRank': "结算单位排行Top10",
        'CostPercentage': "会议费用分布",
        'InsuranceTrend': "保险业务趋势",
        'HotelRank': "酒店排行Top10",
        'CityRank': "会议城市排行Top10",
        'HandlingDepartmentRank': "经办部门排行Top10",
        'ProcessPercentage': "全流程跟踪",
        default: "",
    };
    return resultMap[status] || resultMap.default;
};

</script>
<style scoped lang="less">
@import url(../../main.less);

.mt-10 {
    margin-top: 10px;
}
</style>