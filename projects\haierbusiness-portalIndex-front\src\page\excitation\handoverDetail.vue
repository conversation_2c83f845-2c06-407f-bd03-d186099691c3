<script lang="ts" setup>
import { computed, createVNode, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';

import {
  Anchor as hAnchor,
  <PERSON><PERSON> as hButton,
  Spin as hSpin,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Checkbox as hCheckbox,
  Form as hForm,
  InputNumber as hInputNumber,
  FormItem as hFormItem,
  Row as hRow,
  Col as hCol,
  Popconfirm as hPopconfirm,
  Upload as hUpload,
  Input as hInput,
  Modal as hModal,
  Card as hCard
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { getEnumOptions } from '@haierbusiness-front/utils';

import type { AnchorProps, SelectProps } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  DownloadOutlined,
  VerticalAlignBottomOutlined,
  ExclamationCircleFilled,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  SendOutlined,
  PhoneOutlined,
  VerticalAlignTopOutlined,
  DeleteOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { download, tripApi, teamListApi, rechargeApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

import relativeTime from 'dayjs/plugin/relativeTime';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';
import { AccountRechargeStatusEnum } from '@haierbusiness-front/common-libs';

import {
  CityResponse,
  CityItem,
  TCteateTeam,
  RRechargeSaveParams,
  RRechargeAccountRecordParams,
  RechargePayStatusEnum,
} from '@haierbusiness-front/common-libs';
import { fileApi } from '@haierbusiness-front/apis';
import type { TableColumnType } from 'ant-design-vue';
import { useRouter } from 'vue-router';

const route = ref(getCurrentRoute());
const router = useRouter();

const id = route.value?.query?.id;

const goRecharge = () => {
  router.push('/payman/recharge');
};

watch(
  () => id,
  () => {},
);

const rechargeForm = ref<RRechargeAccountRecordParams>({
  rechargeIssueDetailList: [
  ],
  payType: 0,
  checked: false,
});

// 充值状态
const payState = computed(() => {
  const list = getEnumOptions(RechargePayStatusEnum, true);
  list.forEach((item) => {
    item.text = item.label;
  });
  return list;
});

const columns = [
  {
    title: '序号',
    dataIndex: 'id',
    align: 'center',
  },
  {
    title: '员工姓名',
    dataIndex: 'ownerName',
    align: 'center',
  },
  {
    title: '员工工号',
    dataIndex: 'ownerCode',
    align: 'center',
  },

  {
    title: '充值金额',
    dataIndex: 'amount',
    align: 'center',
  },
  {
    width: 150,

    title: '充值状态',
    dataIndex: 'rechargeStatus',
    align: 'center',
    filters: payState.value,
    onFilter: (value: string, record: any) => record.rechargeStatus == value,
  },
];
const detail = ref<any>({});

onMounted(() => {
  getDetail();
});
const formatDateTime = (time: string | undefined) => {
  if (!time) {
    return '';
  }
  return dayjs(time).format('YYYY年MM月DD日 HH:mm:ss');
};
// 根据id获取详情
const getDetail = () => {
  rechargeApi.handoverDetail(id).then((res) => {
    console.log('🚀 ~ rechargeApi.getExcitationDetail ~ res:', res);
    detail.value = res;
  });
};

dayjs.extend(relativeTime);

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const labelCol = { span: 3 };
const wrapperCol = { span: 12 };
const spinning = ref<boolean>(false);
</script>

<template>
  <div class="container" style="min-height: 100vh;">
        <h-row justify="center" class="headerTitle" align="middle" style="padding: 40px; font-size: 20px; font-weight: 600">
          交接详情
        </h-row>
        <div class="w-1000">
          <h-card size="large" title="交接人信息">
            <h-row :gutter="24">
              <h-col class="mt-10 phone-w" :span="12">交接人： {{ detail?.handoverName }}/{{ detail?.handoverCode	 }}</h-col>
              <h-col class="mt-10 phone-w" :span="12">交接人部门： {{ detail.handoverDeptName }}</h-col>
              <h-col class="mt-10 phone-w" :span="12">交接金额：{{ detail?.handoverAmount || 0 }}元 </h-col>
              <h-col class="mt-10 phone-w" :span="12">交接人电话：{{detail?.handoverTel}} </h-col>
              <h-col class="mt-10 phone-w" :span="12">交接人邮箱：{{detail?.handoverEmail}} </h-col>
            </h-row>
          </h-card>
          <br />
          <h-card size="large" title="承接人信息">
            <h-row :gutter="24">
              <h-col class="mt-10 phone-w" :span="12">承接人： {{ detail?.undertakeName }}/{{ detail?.undertakeCode	 }}</h-col>
              <h-col class="mt-10 phone-w" :span="12">承接人部门： {{ detail.undertakeDeptName }}</h-col>
              <h-col class="mt-10 phone-w" :span="12">承接人电话：{{detail?.undertakeTel}} </h-col>
              <h-col class="mt-10 phone-w" :span="12">承接人邮箱：{{detail?.undertakeEmail}} </h-col>
              <h-col class="mt-10 phone-w" :span="12">承接人直线：{{ detail?.undertakeSuperiorName }}/{{ detail?.undertakeSuperiorCode	 }} </h-col>
              <h-col class="mt-10 phone-w" :span="12">交接原因{{detail?.handoverReason}} </h-col>
            </h-row>
          </h-card>
        </div>
  </div>
</template>

<style lang="less" scoped>
@import url(./recharge.less);

.change-title {
  position: absolute;
  right: 0;
  top: -80px;
  width: 300px;

  .ant-col-6,
  .ant-col-18 {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #000000d9;
  }
}

.mt-5 {
  margin-top: 5px;
}

.ml-5 {
  margin-left: 5px;
}

.color-eee {
  color: #a8a7a7;
}

.com-box {
  background: #f5f5f5;
  padding: 20px;
  margin-top: 20px;

  .whole-line {
    height: 40px;
    font-weight: 600;
  }

  .info-box {
    display: flex;
    align-items: flex-start;
    padding: 10px 20px;
    border: 1px solid #ffdb5c;
    background: #fffbd8;
  }

  :deep(.ant-form-item-control-input-content) {
    display: flex;
    align-items: center;
  }
}

.title {
  height: 60px;
}

.download-btn {
  color: #408cff;
  cursor: pointer;
}

:deep(.city-chose-box) {
  width: 200px !important;
}

:deep(.mr-10) {
  margin-right: 10px;
}

.background-eee {
  padding: 10px;
  background: #f4f4f4;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.w-1000 {
  width: 1000px;
  margin: 20px auto;
}

.mt-10 {
  margin-top: 10px;
}
@media screen and (max-width: 599px) {
  .w-1000 {
    width: 98%;
    margin: 6px auto;
  }

  .phone-w {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .headerTitle{
    display: none;
  }
  :deep(.ant-card-head){
    min-height: 36px;
  }
  :deep(.ant-card-body){
    padding:12px 24px;
  }
}
</style>
