<template>
    <div
        background="rgba(0,0,0,0)"
        :id="id"
        :style="{ height: props.height + 'vh' }"
    ></div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import * as echarts from "echarts";
import { queryOverviewInsurePercentage } from "@haierbusiness-front/apis/src/data/board/travel";
import { circle2 as cicleOptions, colors } from "../../../data";
import { EventBus } from "../../../eventBus";
const props = defineProps({
    height: {
        type: Number,
        default: 30,
    },
});
const id = ref("cicle-" + Date.now());
const loading = ref(false);
let chartDom, myChart;
onMounted(() => {
    chartDom = document.getElementById(id.value);
    myChart = echarts.init(chartDom as any, "dark");
    // queryData();
});
EventBus.on((event) => {
    if (event == "refresh") queryData();
});
const queryData = async () => {
    loading.value = true;
    const data  = await queryOverviewInsurePercentage();
    console.log("保险销售分布", data);
    loading.value = false;
    const rows: any = [];
    data.rows.forEach((item, index) => {
        rows.push({
            value: item[1],
            name: item[0],
        });
    });
    const { series } = cicleOptions;
    series[0].color = colors;
    series[0].data = rows;
    series[0].radius = 60;
    myChart.setOption({
        ...cicleOptions,
        tooltip: { trigger: "item", formatter: "{b} {c} 单" },
    });
};
</script>
<style scoped lang="less"></style>
