<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  InputNumber as hInputNumber,
  message
} from "ant-design-vue";
import { ColumnType } from "ant-design-vue/lib/table/interface";
import { Key } from "ant-design-vue/lib/vc-table/interface";
import {
  PlusOutlined,
  SearchOutlined,
  DownOutlined,
  UpOutlined
} from "@ant-design/icons-vue";
import { banquetApplyApi } from "@haierbusiness-front/apis";
import {
  BApplyListRecord,
  BApplyPerson,
  BanquetStatusEnum
} from "@haierbusiness-front/common-libs";
import dayjs, { Dayjs } from "dayjs";
import { computed, ref, watch, onMounted } from "vue";
import { DataType, usePagination, useRequest } from "vue-request";
import { useEditDialog, useDelete } from "@haierbusiness-front/composables";
import {
  getCurrentRouter,
  errorModal,
  routerParam,
  getCurrentRoute
} from "@haierbusiness-front/utils";
import { storeToRefs } from "pinia";
import { applicationStore } from "@haierbusiness-front/utils/src/store/applicaiton";
import Editor from "@haierbusiness-front/components/editor/Editor.vue";
import { banquetBalanceApi } from "@haierbusiness-front/apis";
import { useRouter } from 'vue-router';

const router = useRouter();
const formState = ref({
  accountSubject: "",
  cardNo: "",
  bankName: "",
  contractNo:""
});

const uploadUrl = import.meta.env.VITE_UPLOAD_URL;

const onFinish = () => {
  banquetBalanceApi.addAccount(formState.value).then(res => {
    message.success("添加成功!");
    router.push('/base/user');
  });
};

const handleCancel = () => {
  router.push('/base/user');
};

</script>

<template>
  <div>
    <h-row justify="space-between" style="padding: 20px;">
      <h-col style="font-size: 18px;">添加账户</h-col>
    </h-row>
    <div
      style="background-color: #ffff;height: 100%;width: 100%;padding: 30px 60px;overflow: auto;min-height:500px;"
    >
      <h-form
        :model="formState"
        name="basic"
        :label-col="{ span: 2 }"
        :wrapper-col="{ span: 22 }"
        autocomplete="off"
        @finish="onFinish"
      >
        <h-form-item label="账户主体" name="accountSubject" :rules="[{ required: true, message: '请输入账户主体' }]">
          <h-input v-model:value.trim="formState.accountSubject" style="width:25%" />
        </h-form-item>
        <h-form-item label="银行卡号" name="cardNo" :rules="[{ required: true, message: '请输入银行卡号' }]">
          <h-input-number :min="1"  v-model:value.trim="formState.cardNo" style="width:25%"  />
        </h-form-item>
        <h-form-item label="所属银行" name="bankName" :rules="[{ required: true, message: '请输入所属银行' }]">
          <h-input v-model:value.trim="formState.bankName" style="width:25%"  />
        </h-form-item>
        <h-form-item label="合同号" name="contractNo" :rules="[{ required: true, message: '请输入合同号' }]">
          <h-input v-model:value.trim="formState.contractNo" style="width:25%"  />
        </h-form-item>
        <h-form-item :wrapper-col="{ offset: 2, span: 16 }">
          <h-button style="margin-right: 40px" @click="handleCancel">取消</h-button>
          <h-button type="primary" html-type="submit">提交</h-button>
        </h-form-item>
      </h-form>
    </div>
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}
.img {
  width: 104.5px;
  height: 50px;
}
:deep(.ant-descriptions-item-label) {
  width: 200px;
}
:deep(.ant-descriptions-item-content) {
  width: 300px;
}
</style> 