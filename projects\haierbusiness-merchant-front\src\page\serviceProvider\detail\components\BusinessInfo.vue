<script setup lang="ts">
import { useServiceProviderDetailStore } from '../store';

const store = useServiceProviderDetailStore();
</script>

<template>
  <a-descriptions :labelStyle="{ width: '120px', justifyContent: 'flex-end' }">
    <a-descriptions-item label="商户企业名称">{{ store.businessDetail.name }}</a-descriptions-item>
    <a-descriptions-item label="统一信用代码">{{ store.businessDetail.unifiedSocialCreditCode }}</a-descriptions-item>
    <a-descriptions-item label="国内结算V码">{{ store.businessDetail.code }}</a-descriptions-item>
    <a-descriptions-item label="海外结算码">-</a-descriptions-item>
    <a-descriptions-item label="企业CODE">{{ store.businessDetail.enterpriseCode }}</a-descriptions-item>
  </a-descriptions>
</template>