import {IBaseDO} from "../../../basic/model/baseModel";
import {IPageRequest} from "../../../basic";

export class IDailyPersonalSaveRequestDTO {

    /**
     * 用户工号
     */
    usercode?:string

    /**
     * 部门名称
     */
    name?:string

    /**
     * 同集团组织架构code
     */
    deptCode?:string

    /**
     * 部门名称
     */
    deptName?:string

    phone?:string

    mail?:string
}


export class IDailyPersonalUpdateRequestDTO {

     id?:number

    /**
     * 用户工号
     */
     usercode?:string

    /**
     * 部门名称
     */
     name?:string

    /**
     * 同集团组织架构code
     */
     deptCode?:string

    /**
     * 部门名称
     */
     deptName?:string

     phone?:string

     mail?:string
}

export class IDailyPersonalDeleteRequestDTO {
    id?: number
}

export class IDailyPersonalListRequestDTO extends IPageRequest{
    /**
     * 用户工号
     */
    usercode?:string

    /**
     * 部门名称
     */
    name?:string

    /**
     * 同集团组织架构code
     */
    deptCode?:string

    /**
     * 部门名称
     */
    deptName?:string

    mail?:string

    phone?:string
}


export class IDailyPersonalResponse extends IBaseDO {
     id?:number

    /**
     * 工号
     */
     usercode?:string

    /**
     * 姓名
     */
     name?:string

     deptCode?:string

     deptName?:string

     mail?:string

     phone?:string

}

