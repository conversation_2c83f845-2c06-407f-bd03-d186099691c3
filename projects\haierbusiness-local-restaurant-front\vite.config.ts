import { loadEnv } from 'vite'

import vue from '@vitejs/plugin-vue'
import path from 'path';
import postcssPx2remExclude from 'postcss-px2rem-exclude'

const CWD = process.cwd();

export default ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);

  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
    },
    plugins: [vue()],
    build:{
      target:['es2015'],
      sourcemap: true
    },
    server: {
      port: 5200,
      proxy: {
        
        '/hb/businesstravel': {
          target: 'https://businesstravel-test.haier.net',
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`/hb/businesstravel`), ''),
        },
        "/hb": {
          target: "https://businessmanagement-test.haier.net/hbweb/index/hb",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb/, ""),
        },
        "/upload": {
          target: "https://businessmanagement-test.haier.net/hbweb/upload",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/upload/, ""),
        },
        
      }
    },
    css: {
      postcss: {
        plugins: [
          postcssPx2remExclude({
            remUnit: 75, 
            exclude: /node_module/i,
            // /node_module|components/i
          })
        ]
      }
    }
  }
}
