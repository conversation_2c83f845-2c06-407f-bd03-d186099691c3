import {
    Result,
    IApplicationRequest ,
    IPageResponse,
    IApplicationResponse,
    IAssignUserMenuType,
    IApplicationAccount
} from '@haierbusiness-front/common-libs'
import { get, post } from '../request'
export const applicationListApi ={
    list: (params: IApplicationRequest ): Promise<IPageResponse<IApplicationResponse>> => {
        return get("/portal/api/admin-api/banner/apply-menu/page", params)
    },
    get: (id: number): Promise<IApplicationResponse> => {
        return get("/portal/api/admin-api/banner/apply-menu/get", {
            id
        })
    },
    save: (params: IApplicationRequest ): Promise<Result> => {
        return post('/portal/api/admin-api/banner/apply-menu/create', params)
    },
    edit: (params: IApplicationRequest): Promise<Result> => {
        return post('/portal/api/admin-api/banner/apply-menu/update', params)
    },
    remove: (id: number): Promise<Result> => {
        return get('/portal/api/admin-api/banner/apply-menu/delete', { id })
    },

    allList: (client: string): Promise<Array<IApplicationAccount>> => {
        return get('/portal/api/app-api/banner/apply-menu/list', { client })
    },

    userApps: (userId: string, client: string): Promise<Array<IApplicationAccount>> => {
        return get('/portal/api/app-api/banner/user-menu/list', { userId, client })
    },

    assignUserMenu: (userMenu: IAssignUserMenuType): Promise<Result> => {
        return post('/portal/api/app-api/banner/user-menu/assign-user-menu', userMenu)
    },

    onTimeChange : (dateRange: string[]) => {
        const times:string[] = []
        times.push(dateRange[0] + ' 00:00:00')
        times.push(dateRange[1] + ' 23:59:59')
        return times
        // searchKey.createTime = times
    }
 
}