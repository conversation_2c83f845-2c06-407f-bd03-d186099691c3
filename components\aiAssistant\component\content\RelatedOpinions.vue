<template>
  <div class="related-opinions">
    <!-- 新增题头 -->
    <div class="header">相关问题</div>

    <!-- 水平布局容器 -->
    <div class="opinions-container">
      <div
          v-for="(opinion, index) in opinions"
          :key="index"
          class="opinion-item"
      >
        <a-button
            type="link"
            @click="askAgain(opinion)"
            class="opinion-button"
        >
          {{ opinion }}
        </a-button>
      </div>
    </div>
  </div>
</template>


<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

const props = defineProps<{
  opinions: string[]
}>()

const emit = defineEmits<{
  (e: 'ask-again', value: string): void
}>()

const askAgain = (value: string) => {
  emit('ask-again', value)
}
</script>

<style scoped lang="less">
.related-opinions {
  margin-top: 8px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 6px;

  // 题头样式
  .header {
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
    font-size: 14px; // 新增字体大小
  }

  // 水平布局容器
  .opinions-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    // 推荐组合（基础版）
    .opinion-item {
      // 添加投影
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

      // 渐变背景
      background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);

      // 微交互动画
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }

  }
}

</style>
