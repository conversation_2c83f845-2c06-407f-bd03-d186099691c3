<script setup lang="ts">
import {
  BreadcrumbItem as hBreadcrumbItem,
  Bread<PERSON>rumb as hBreadcrumb,
  LayoutSider as hLayoutSider,
  LayoutFooter as hLayoutFooter,
  LayoutContent as hLayoutContent,
  LayoutHeader as hLayoutHeader,
  Layout as hLayout,
  MenuItem as hMenuItem,
  MenuItemGroup as hMenuItemGroup,
  SubMenu as hSubMenu,
  Menu as hMenu,
  Divider as hDivider,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Result as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Tabs as hTabs,
  message,
} from 'ant-design-vue';
import { onMounted, ref, watch } from 'vue';
import {
  HolderOutlined,
  UserOutlined,
  NotificationOutlined,
  AppstoreOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons-vue';
import { ResourceTypeConstant } from '@haierbusiness-front/common-libs';
import { useRouter } from 'vue-router';
import AntIcon from '../IconSelector/AntIcon.vue';

const router = useRouter()
const props = defineProps({
  item: Object,
});

const emits = defineEmits(['select-key', 'open-key']);
const selectedKey = (param: any) => {
  emits('select-key', param);
  if (props?.item?.type === ResourceTypeConstant.PAGE_GROUP.type) {
    emits('open-key', [props?.item]);
  }
};

const openKey = (param: any) => {
  emits('open-key', [...param, props?.item]);
};

if (props?.item?.type === ResourceTypeConstant.PAGE_MENU.type) {
  let hashUrl = window.location.hash;
  if (hashUrl.includes('?')) {
    hashUrl = hashUrl.substring(0, hashUrl.indexOf('?'));
  }
  if (!hashUrl.endsWith('/')) {
    hashUrl = hashUrl + '/';
  }
  let path: string = props.item.url;
  if (!path.startsWith('#')) {
    path = '#' + path;
  }
  if (!path.endsWith('/')) {
    path = path + '/';
  }
  const replacePath = path.indexOf('?') > 0 ? path.substring(0, path.indexOf('?')) : path;
  if (hashUrl.startsWith(replacePath)) {
    emits('select-key', props?.item);
  }
}
watch(() => router.currentRoute.value.path, () => {
  //获取所有路由
  if (props?.item?.type === ResourceTypeConstant.PAGE_MENU.type) {
    let hashUrl = window.location.hash;
    if (hashUrl.includes('?')) {
      hashUrl = hashUrl.substring(0, hashUrl.indexOf('?'));
    }
    if (!hashUrl.endsWith('/')) {
      hashUrl = hashUrl + '/';
    }
    let path: string = props.item.url;
    if (!path.startsWith('#')) {
      path = '#' + path;
    }
    if (!path.endsWith('/')) {
      path = path + '/';
    }
    const replacePath = path.indexOf('?') > 0 ? path.substring(0, path.indexOf('?')) : path;
    if (hashUrl.startsWith(replacePath)) {
      emits('select-key', props?.item);
    }
  }
}, { immediate: true })

</script>
<template>
  <h-sub-menu v-if="item?.type === ResourceTypeConstant.PAGE_GROUP.type" :key="item?.id">
    <template #icon>
      <AntIcon :size="16" v-if="item?.menuIcon" :name="item?.menuIcon" />
      <AntIcon :size="16" v-else :name="'HolderOutlined'" />
    </template>
    <template #title>{{ item.name }}</template>
    <e-menu-item v-for="i of item?.children" :item="i" @select-key="selectedKey" @open-key="openKey"></e-menu-item>
  </h-sub-menu>
  <h-menu-item v-if="item?.type === ResourceTypeConstant.PAGE_MENU.type" :key="item?.id">
    <AntIcon :size="16" v-if="item?.menuIcon" :name="item?.menuIcon" />
    <AntIcon :size="16" v-else :name="'HolderOutlined'" />
    <router-link :to="item?.url" :target="item?.url.indexOf('board') > -1 ? '_blank' : ''">{{ item.name }}</router-link>
  </h-menu-item>
</template>
