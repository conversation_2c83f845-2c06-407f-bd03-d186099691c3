<script lang="ts" setup>
import { Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
     Input as hInput, CheckboxGroup as hCheckboxGroup, Checkbox as hCheckbox, Row as hRow, Col as hCol, DatePicker as hDatePicker } from 'ant-design-vue';
import { computed, ref, watch, onMounted } from "vue";
import type { Ref } from "vue";
import {
    IPaymentVirtualAccount, VirtualAccountTypeConstant, VirtualScopeConstanty, IApplicationInfo
} from '@haierbusiness-front/common-libs';
import { useRequest } from 'vue-request';
import { enterpriseApi, applicationApi, virtualPayApi } from '@haierbusiness-front/apis';
import dayjs from 'dayjs';

interface Props {
    show: boolean;
    data: IPaymentVirtualAccount | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

// 授权应用
const applicationList = ref<IApplicationInfo[] | undefined>([]);

onMounted(() => {
  if (account.value.id) {
    virtualPayApi.accountTypes(account.value.accountNo!).then(data => {
      if(data && data.length > 0) {
        appInfoList.value = data.map(item => {
          return item.applicationCode!
        })
      }
    })
  }

  applicationApi.list({ type: 1 }).then(it => {
      if (it && it.records && it.records.length > 0) {
          applicationList.value = it.records;
      }
  })
})

const from = ref();
const confirmLoading = ref(false);

const defaultData: IPaymentVirtualAccount = {
    accountNo: '',
    accountName: '',
    enterpriseCode: '',
    enterpriseName: '',
    amount: '',
    expireDate: ''
};

const {
  data: enterprises,
  run: userListApiRun
} = useRequest(enterpriseApi.list, {
  manual: false
});

const enterpriseSelect = computed(() => {
  return enterprises.value || []
})

const enterpriseCodeChange = (value: string) => {
  const enterprise = enterpriseSelect.value.find(o => o.id?.toString() === value)
  if(enterprise) {
    account.value.enterpriseName = enterprise.name
  }
}

// 类型
const accountType = computed(() => VirtualAccountTypeConstant.toArray())

// 作用域
const scope = computed(() => VirtualScopeConstanty.toArray())


const rules = {
  accountNo: [{ required: true, message: "请输入账户！" }],
  accountName: [{ required: true, message: "请输入名称！" }],
  enterpriseCode: [{ required: true, message: "请选择企业！" }],
  type: [{ required: true, message: "请选择类型！" }],
  scope: [{ required: true, message: "请选择作用域！" }],
  amount: [{ required: true, message: "请输入金额！" }],
  state: [{ required: true, message: "请选择状态！" }],
  // appInfoList: [{ required: true, message: "请选择授权应用！" }],
};

const account: Ref<IPaymentVirtualAccount> = ref(
    ({ ...props.data } as IPaymentVirtualAccount) || defaultData
);

const appInfoList = ref<Array<string>>()

watch(props, (newValue) => {
    account.value = ({ ...newValue.data } as IPaymentVirtualAccount) || defaultData;
});

const emit = defineEmits(["cancel", "ok"]);

const visible = computed(() => props.show);

const handleOk = () => {
    confirmLoading.value = true;
    from.value.validate()
    .then(() => {
      const appInfos = appInfoList.value!.map(item => {
        return {
          applicationCode: item,
          scope: 1
        }
      })

      const data = {
        ...account.value,
        expireDate: dayjs(account.value.expireDate).format("YYYY-MM-DD HH:mm:ss"),
        appInfoList: appInfos
      }
      emit("ok", data, () => {
        confirmLoading.value = false;
      });
    })
    .catch(() => {
        confirmLoading.value = false;
    });
};
</script>

<template>
    <h-modal
      v-model:visible="visible"
      :title="account.id ? '编辑账号' : '新增账号'"
      :width="600"
      @cancel="$emit('cancel')"
      :confirmLoading="confirmLoading"
      @ok="handleOk"
    >
      <h-form
        ref="from"
        :model="account"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
        :rules="rules"
      >
        <h-form-item label="账户" name="accountNo">
          <h-input v-model:value="account.accountNo" />
        </h-form-item>
        <h-form-item label="名称" name="accountName">
          <h-input v-model:value="account.accountName" />
        </h-form-item>
        <h-form-item label="企业" name="enterpriseCode">
            <h-select ref="select" v-model:value="account.enterpriseCode" @change="(value) =>  enterpriseCodeChange(value as string)" style="width: 100%">
                <h-select-option :disabled="account.id" v-for="(item, index) in enterpriseSelect" :key="index" :value="item.id?.toString()">{{ item.name }}</h-select-option>
            </h-select>
        </h-form-item>
        <h-form-item label="类型" name="type">
            <h-select ref="select" v-model:value="account.type" style="width: 100%" allow-clear>
                <h-select-option v-for="(item, index) in accountType" :key="index" :value="item?.code">{{ item?.desc }}</h-select-option>
            </h-select>
        </h-form-item>
        <h-form-item label="作用域" name="scope">
            <h-select ref="select" v-model:value="account.scope" style="width: 100%" allow-clear>
                <h-select-option v-for="(item, index) in scope" :key="index" :value="item?.code">{{ item?.desc }}</h-select-option>
            </h-select>
        </h-form-item>
        <!-- <h-form-item label="金额" name="amount">
          <h-input v-model:value="account.amount" />
        </h-form-item> -->
        <h-form-item label="过期时间" name="expireDate" >
          <h-date-picker v-model:value="account.expireDate" style="width: 100%;"/>
        </h-form-item>
        <h-form-item label="状态" name="state">
          <h-select v-model:value="account.state" allow-clear>
            <h-select-option :value="0">无效</h-select-option>
            <h-select-option :value="1">有效</h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="授权应用" name="appInfoList">
          <h-checkbox-group v-model:value="appInfoList" style="width: 100%">
            <h-row>
              <h-col :span="8" v-for="(item, index) in applicationList" :key="index">
                <h-checkbox :value="item.applicationCode">{{ item.applicationName }}</h-checkbox>
              </h-col>
            </h-row>
          </h-checkbox-group>
        </h-form-item>
      </h-form>
    </h-modal>
</template>


<style lang="less" scoped>
.important {
  color: red;
}
</style>
  