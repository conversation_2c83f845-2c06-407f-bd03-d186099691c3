<template>
  <div class="wallet">
    
    <van-nav-bar title="钱包二维码"  />

    <div class="pb-10"></div>
    <div class="wallet-nav wallet-code">
      <div class="wallet-mark" v-if="codeSuccess || loseEfficacy" @click="qrCodeRefash2">
        <div class="wallet-mark-text">
          <van-icon name="replay" />
          <div>{{ loseEfficacy ? '二维码已失效请刷新' : '支付成功,点击刷新' }}</div>
        </div>
      </div>
      <div class="wallet-mark" v-if="prepay">
        <div class="wallet-mark-text wallet-mark-loading">
          <van-loading color="#ffffff" size="60px" class="loading" />
          <div>支付中请勿刷新</div>
        </div>
      </div>
      <div class="wallet-code1">
        <vue-barcode v-if="barcodeCode" :height="70" :fontSize="16" :value="barcodeCode">不支持vue-bercode</vue-barcode>
      </div>
      <div>
        <div id="qrcode" class="wallet-qrcode"></div>
        <div class="wallet-refash" v-if="!prepay" @click="qrCodeRefash" :class="time !== 0 ? 'countdown' : ''">
          <van-icon name="replay" />刷新二维码
          <span v-if="time !== 0">({{ time }}s)</span>
        </div>
      </div>
      <div class="wallet-time">{{ currentTime }}</div>
    </div>

    <div class="wallet-nav">
      <van-cell icon="gold-coin-o" title="账户余额">
        <span style="color: red">{{ amount }}元</span>
      </van-cell>
    </div>
    <div class="wallet-nav">
      <van-row class="user-links">
        <van-col span="12" @click="goRecharge">
          <van-icon name="pending-payment" class="mb-10" />
          账户充值
        </van-col>
        <van-col span="12" @click="goUserBill">
          <van-icon name="records" class="mb-10" />
          账户流水
        </van-col>
      </van-row>
    </div>

    <!-- codeSuccess -->
    <!-- <van-action-sheet v-model:show="show" cancel-text="取消" close-on-click-action>
      <van-cell-group>
        <van-cell
          icon="gift-o"
          size="large"
          label="使用代金券充值"
          title="代金券"
          is-link
        />
        <van-cell
          @click="goRecharge"
          icon="points"
          size="large"
          label="使用福利积分充值"
          title="福利积分"
          is-link
        />
      </van-cell-group>
    </van-action-sheet> -->
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import QRCode from 'qrcodejs2-fix';
import { ref, onMounted, onUnmounted } from 'vue';
import { showFailToast } from 'vant';
import { payPhoneApi } from '@haierbusiness-front/apis';
// vue3
import VueBarcode from 'vue3-barcode';
import { getCurrentRouter } from '@haierbusiness-front/utils';
import axios from 'axios'

const appId = import.meta.env.VITE_APP_ID
const sdkUrl = import.meta.env.VITE_SDK_URL

const ihaierAuth = async () => {
  const ihaierSDK = await window.IHaierRuntime.getRuntime({
    appId: appId, //您在准备阶段获取的appId，不填默认临时appId，
    authUrl: sdkUrl, //鉴权服务地址，不填默认http://127.0.0.1:13025/ihaier-runtime-auth-server
  });

  console.log(ihaierSDK, 'ihaierSDK=>>>>>>>>>>>>>>>>>>>');
  ihaierSDK
    .scanCode()
    .then(async (res) => {
      console.log(res, 'res======>>>>>>>>>>>>>>>');
      const response = await payPhoneApi.getVoucherInfo({
        // checkOffCode: res,
        checkOffCode: res.data.data,
      });
      showToast('扫码成功');
      var voucherArray = res.data.data.split(':');
      expansion.value.voucherAccount = response.cardNum;
      expansion.value.voucherAmount = response.faceValue;
      expansion.value.voucherPassword = voucherArray[1];
    })
    .catch((err) => {
      console.log(`getSystemInfo fail: ${JSON.stringify(err)}`);
    });
}

const router = getCurrentRouter();
const route = router.currentRoute.value;

const prepay = ref(false); //预支付状态
const currentTime = ref('');
const time = ref(0); //主动刷新计时
const passivityTime = ref(180); //被动刷新计时
const qrCodetimer = ref<any>(null); //被动计时器
const dayTimer = ref<any>(null); //日期计时器
const loseEfficacy = ref(false); //二维码是否失效
const qrCodeUrl = ref<any>(null);
const getData = async () => {
  source.value.cancel()
  const res = await payPhoneApi.generatePaymentCode();
  barcodeCode.value = res.paymentCode;
  // 二维码刷新
  initQrCode(res.paymentCode);
  // 查询付款状态
  searchCompleteStatus(res.paymentCode);
};
const codeSuccess = ref(false);
const paymentUserCodePay = async (paymentCode: string) => {
  try {
    const res = await payPhoneApi.paymentCodePay({ paymentCode });
    if (res.code != '') {
      codeSuccess.value = true;
      prepay.value = false;
      getPersonalWelfareAccount();
    }
  } catch (error) {
    prepay.value = false;
  }
};
const errorNotify = (error: any) => {
  if(error.code === 'ERR_NETWORK') {
    searchCompleteStatus(barcodeCode.value)
  } else if (error.code = 'ERR_CANCELED') {

  } else {
    console.log(error)
    showFailToast(error?.response?.data?.message)
  }
}

const source = ref(axios.CancelToken.source())

const searchCompleteStatus = (paymentCode: string) => {
  // 确保同时只存在一个查询请求
  source.value.cancel()
  source.value = axios.CancelToken.source()
  payPhoneApi
    .searchPaymentCodePayStatus({
      paymentCode,
    }, errorNotify, source.value.token)
    .then((response) => {
      if (route.path === '/wallet' && response) {
        if (response.state === 0) {
          //0 取消(当获取状态为取消时重新获取付款码) 
          loseEfficacy.value = true
          // if (passivityTime.value) {
          //   // getData();
          // } 
        } else if (response.state === 10) {
          // 10：保存(当获取状态为保存时什么也不做) 
          searchCompleteStatus(paymentCode);
        } else if (response.state === 20) {
          // 20 预支付(判断验证类型 比如验证码验证 密码验证)
          prepay.value = true;
          paymentUserCodePay(paymentCode);
        }
      }
    });
};

const goRecharge = () => {
  if (!accountNo.value) {
    showFailToast('账户查询中，请稍后！')
    return
  }
  router.push({
    path: '/recharge',
    query: {
      accountNo: accountNo.value,
    },
  });
};

const goUserBill = () => {
  router.push({
    path: '/userbill',
  });
};
const barcodeCode = ref('');

const qrcode = ref('');

const qr = ref('');
const qrCodeRefash = () => {
  if (time.value > 0) {
    return;
  }

  time.value = 15;
  passivityTime.value = 180;
  codeSuccess.value = false;
  loseEfficacy.value = false;
  let timer: any = setInterval(() => {
    time.value = time.value - 1;
    if (time.value == 0) {
      clearInterval(timer);
      timer = null;
    }
  }, 1000);

  // if (qrCodetimer.value) {
  //   clearInterval(qrCodetimer.value);
  //   qrCodetimer.value = null;
  // }

  // changeqrCodeStatus();
  // qr.value = "";
  // document.getElementById("qrcode").innerHTML = "";

  getData();
};

const qrCodeRefash2 = () => {
  time.value = 0;
  passivityTime.value = 180;
  if (qrCodetimer.value) {
    // changeqrCodeStatus();
  }

  qrCodeRefash();
};
const initQrCode = (value) => {
  console.log('二维码刷新了');
  qr.value = '';
  document.getElementById('qrcode').innerHTML = '';
  qr.value = new QRCode(document.getElementById('qrcode'), {
    text: value,
    width: 150,
    height: 150,
    colorDark: '#000000',
    colorLight: '#ffffff',
  });
};
// const changeqrCodeStatus = () => {
//   qrCodetimer.value = setInterval(() => {
//     passivityTime.value = passivityTime.value - 1;
//     if (passivityTime.value == 0) {
//       loseEfficacy.value = true;
//       clearInterval(qrCodetimer.value);
//       qrCodetimer.value = null;
//     }
//   }, 1000);
// };
const onClickLeft = () => {
  console.log(1111);
};
onMounted(() => {
  currentTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
  dayTimer.value = setInterval(() => (currentTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss')), 1000);
  getPersonalWelfareAccount().then(() => {
    getData()
  });
  // changeqrCodeStatus();
});
onUnmounted(() => {
  source.value.cancel()

  if (dayTimer.value) {
    clearInterval(dayTimer.value);
    dayTimer.value = null;
  }
  if (qrCodetimer.value) {
    clearInterval(qrCodetimer.value);
    qrCodetimer.value = null;
  }
});
const amount = ref(0);
const accountNo = ref(0);
const getPersonalWelfareAccount = async () => {
  const res = await payPhoneApi.getPersonalWelfareAccount();
  amount.value = res.amount;
  accountNo.value = res.accountNo;
};
</script>

<style lang="scss" scoped>



.wallet-qrcode {
  width: 150px;
  height: 150px;
}

.wallet {
  background: #f3f3f3;
  height: 100vh;
  overflow: hidden;
  &-qrcode {
    display: flex;
    justify-content: center;
    width: 100%;
  }
  &-nav {
    width: 96%;
    margin: 0 auto 10px;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
  }
  &-bg {
    background: url('../../assets/image/blurry-gradient-haikei.png');
    background-size: 100% 100%;
    height: 200px;
    padding-top: 20px;
    // border-bottom-left-radius: 20%;
    // border-bottom-right-radius: 20%;
  }
  &-code {
    background: #fff;
    padding-bottom: 25px;
    // height: 55vh;
  }

  &-code1 {
    display: flex;
    justify-content: center;
    margin-top: 25px;
    margin-bottom: 20px;
  }
  &-mark {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;

    &-text {
      color: #fff;
      i {
        display: flex;
        justify-content: center;
        font-size: 60px;
        margin-bottom: 20px;
      }
    }
    &-loading {
      display: flex;
      flex-direction: column;
      align-items: center;

      .loading {
        margin-bottom: 20px;
      }
    }
  }
  &-refash {
    font-size: 12px;
    text-align: center;
    margin-top: 10px;
    color: #1989fa;

    i {
      color: #1989fa;
      font-size: 14px;
      margin-right: 5px;
    }
  }
  .countdown {
    color: #969799;

    i {
      color: #969799;
    }
  }
  &-time {
    display: flex;
    justify-content: center;
    margin-top: 10px;
  }
  &-user {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
    color: #fff;
    img {
      width: 70px;
      height: 70px;
      margin-right: 10px;
    }
  }
}
.user {
  &-poster {
    width: 100%;
    height: 53vw;
    display: block;
  }

  &-group {
    margin-bottom: 15px;
  }

  &-links {
    padding: 15px 0;
    font-size: 12px;
    text-align: center;
    background-color: #fff;

    .van-icon {
      display: block;
      font-size: 24px;
    }
  }
}
.mb-10 {
  margin-bottom: 10px;
}
.pb-10 {
  padding-bottom: 10px;
}
</style>
