import { IBudgetHaierOccupyRequest, IBudgetHaierQueryRequest as IBudgetHaierQueryRequest, IBudgetHaierQueryResponse as IBudgetHaierQueryResponse, IBudgetHaierTypesRequest, IBudgetHaierTypesResponse, IHaierAccountCompany, IHaierBudgetDepartment, IHaierBudgetDeptBccAccountsRequest, IHaierBudgetDeptBccBudgetItemRequest, IHaierBudgetDeptBccBudgetItemResponse, IHaierBudgetDeptBccDcItemRequest, IHaierBudgetDeptBccDcProjectRequest, IHaierBudgetDeptBccDepartmentsRequest, IHaierBudgetDeptBccProjectRequest, IHaierBudgetDeptBccWBSRequest, IHaierDcProjectItem, IHaierProject, IHaierWbs, IPageResponse, IPayData, IPayHeader } from '@haierbusiness-front/common-libs'
import { IPayRequest, IPayResponse, ICoinHaierAccountResponse, ICoinHaierBalanceResponse, ICoinHaierPayRequest } from '@haierbusiness-front/common-libs'
import { get, post } from '../request'

export const budgetHaierPayBccSupportApi = {

    /**
     * 查询预算部门
     */
    searchDepartments: (params: IHaierBudgetDeptBccDepartmentsRequest): Promise<IPageResponse<IHaierBudgetDepartment>> => {
        return get('pay/api/haier/budget/dept/bcc/support/departments', params)
    },

    /**
     * 查询结算单位
     */
    searchAccounts: (params: IHaierBudgetDeptBccAccountsRequest): Promise<IPageResponse<IHaierAccountCompany>> => {
        return get('pay/api/haier/budget/dept/bcc/support/accounts', params)
    },

    /**
     * 查询预算立项
     */
    searchBudgetItems: (params: IHaierBudgetDeptBccBudgetItemRequest): Promise<IHaierBudgetDeptBccBudgetItemResponse[]> => {
        return get('pay/api/haier/budget/dept/bcc/support/items', params)
    },

     /**
     * 查询研发项目
     */
     searchProjects: (params: IHaierBudgetDeptBccProjectRequest): Promise<IPageResponse<IHaierProject>> => {
        return get('pay/api/haier/budget/dept/bcc/support/projects', params)
    },

     /**
     * 查询WBS
     */
     searchWbs: (params: IHaierBudgetDeptBccWBSRequest): Promise<IPageResponse<IHaierWbs>> => {
        return get('pay/api/haier/budget/dept/bcc/support/wbs', params)
    },

     /**
     * 查询地产项目
     */
     searchDcProjects: (params: IHaierBudgetDeptBccDcProjectRequest): Promise<IPageResponse<IHaierDcProjectItem>> => {
        return get('pay/api/haier/budget/dept/bcc/support/dc/projects', params)
    },

     /**
     * 查询地产分期
     */
     searchDcItems: (params: IHaierBudgetDeptBccDcItemRequest): Promise<IPageResponse<IHaierDcProjectItem>> => {
        return get('pay/api/haier/budget/dept/bcc/support/dc/items', params)
    },
}
