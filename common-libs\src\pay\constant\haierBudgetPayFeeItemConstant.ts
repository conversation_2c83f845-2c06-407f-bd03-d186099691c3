
type keys = 'LP' | 'ZGHD' | 'BMZF' | 'NDJDFLSR' | 'ZGHDAQ' | 'HW' | 'CPFBJTJ' | 'GNCL' | 'GWCL' | 'ZGHDAQ' | 'SNJT' | 'YQ'

/**
 * 费用项目类型
 *   { "value": "6666020102", "label": "礼品费" },
 *   { "value": "6666010305", "label": "职工活动费" },
 *   { "value": "6666010310", "label": "部门走访费" },
 *   { "value": "6666010304", "label": "年度、季度福利费、生日福利" },
 *   { "value": "6666010399", "label": "职工活动经费-安全" },
 *
 *   { "value": "66660210", "label": "会务费" },
 *   { "value": "6666040210", "label": "产品发布及推介费" },
 *
 *   { "value": "6666020201", "label": "国内差旅费" },
 *   { "value": "6666020202", "label": "国外差旅费" },
 *   { "value": "6666020401", "label": "市内交通费" },
 *   { "value": "6666020101", "label": "宴请费" },
 */
export const HaierBudgetPayFeeItemConstant = {
  //LP: { "code": "6666020102","bcc_code": "6666020102","gems_code": "6666020102","xw_code": "abc", "name": "礼品费" },
  LP: { "code": "6666020102", "name": "礼品费" },
  ZGHD: { "code": "6666010305", "name": "职工活动费" },
  BMZF: { "code": "6666010310", "name": "部门走访费" },
  NDJDFLSR: { "code": "6666010304", "name": "年度、季度福利费、生日福利" },
  ZGHDAQ: { "code": "6666010399", "name": "职工活动经费-安全" },

  HW: { "code": "66660210", "name": "会务费" },
  CPFBJTJ: { "code": "6666040210", "name": "产品发布及推介费" },

  GNCL: { "code": "6666020201", "name": "国内差旅费" },
  GWCL: { "code": "6666020202", "name": "国外差旅费" },
  SNJT: { "code": "6666020401", "name": "市内交通费" },
  YQ: { "code": "6666020101", "name": "宴请费" },
  GNGCYQ:{ "code": "6666040706", "name": "国内工程宴请费" },

  ofCode: (code?: string): { "code": string, "name": string } | null => {
    for (const key in HaierBudgetPayFeeItemConstant) {
      const item = HaierBudgetPayFeeItemConstant[key as keys];
      if (code === item.code) {
        return item;
      }
    }
    return null;
  }
}