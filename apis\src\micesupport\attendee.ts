import { download, get, post } from '../request'
import { 
    IMeetingAttendeeFilter, 
    IMeetingAttendee,
    IPageResponse, 
    IMeetingAttendeeFile,
    Result, 
    IMeetingDetails} from '@haierbusiness-front/common-libs'


export const meetingAttendeeApi = {
    list: (params: IMeetingAttendeeFilter): Promise<IPageResponse<IMeetingAttendee>> => {
        return get('/mice-support/api/meeting/participant/list', params)
    },
    //标签列表
    groupList: (params: IMeetingAttendeeFilter): Promise<IPageResponse<IMeetingAttendee>> => {
        return get('/mice-support/api/meeting/participant/group/list', params)
    },
    //会议详情
    details: (id: number): Promise<IMeetingDetails> => {
        return get('/mice-support/api/meeting/info/detail', {
            id
        })
    },

    //会议参会人详情
    attendeeDetails: (id: number): Promise<IMeetingDetails> => {
        return get('/mice-support/api/meeting/participant/detail', {
            id
        })
    },

    save: (params: IMeetingAttendee): Promise<Result> => {
        return post('/mice-support/api/meeting/participant/add', params)
    },
    //添加标签
    groupSave: (params: {miceInfoId:number,tagName:string}[]): Promise<Result> => {
        return post('/mice-support/api/meeting/participant/group/add', params)
    },

    //审批
    Approval: (params: IMeetingAttendee): Promise<Result> => {
        return post('/mice-support/api/meeting/participant/approve', params)
    },
    //导出
    export: (params: { miceInfoId: number; miceInfoName?: string }): Promise<void> => {
        return download('/mice-support/api/meeting/participant/export', params)
    },
    //导入
    import: (params: IMeetingAttendeeFile): Promise<Result> => {
        return post('/mice-support/api/meeting/participant/import', params)
    },
    //编辑参会人
    edit: (params: IMeetingAttendee): Promise<Result> => {
        return post('/mice-support/api/meeting/participant/update', params)
    },
    //编辑会议
    meetingEdit: (params: IMeetingDetails): Promise<Result> => {
        return post('/mice-support/api/meeting/info/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post(`/mice-support/api/meeting/participant/delete?id=${id}`)
    },
}