<script setup lang="ts">
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Input as hInput,
  Table as hTable,
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Dropdown as hDropdown,
  Menu as hMenu,
  MenuItem as hMenuItem,
  DatePicker as hDatePicker,
  RangePicker as hRangePicker,
} from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import { computed, onMounted, ref ,watch  } from 'vue';
import { PlusOutlined, SearchOutlined, BarsOutlined, DownOutlined, DownloadOutlined } from '@ant-design/icons-vue';
import { userApi, enterpriseApi, processApi } from '@haierbusiness-front/apis';
import { dailyReportApi, dailyDeptApi } from '@haierbusiness-front/apis/src/daily';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue'
import { usePagination, useRequest } from "vue-request";
import { IMonthPlantDTO, UserGroupSystemConstant} from "@haierbusiness-front/common-libs";
import { useEditDialog, useDelete } from "@haierbusiness-front/composables";
import { getCurrentRouter, errorModal, routerParam, checkUserGroup } from "@haierbusiness-front/utils";

const router = getCurrentRouter();

let columns = ref([]);


function flattenDateList(item) {
  return item.dateList.reduce((obj, dayData) => {
    Object.entries(dayData).forEach(([date, value]) => {
      obj[date] = value;
    });
    return obj;
  }, {});
}

const searchParam = ref<IMonthPlantDTO>({
});

const reset = () => {
  searchParam.value = {};
  startBeginAndEnd.value = []
};

const {
  data,
  run: processApiRun,
  loading,
} = usePagination(dailyReportApi.getPersonDetailsList, {
  manual: false,
});

const {
  data: quantifierStateList,
  loading: quantifierStateLoading,
} = usePagination(dailyReportApi.getQuantifierStateList, {
  manual: false,
});


const {
  data: deptList,
  loading: deptListLoading,
} = usePagination(dailyDeptApi.list, {
  manual: false,
});

const deptSelect = computed(() => {
  let deptArr = [];
  for (let i in deptList.value) {
    deptArr.push(
        {
          value: deptList.value[i].code,
          label: deptList.value[i].name,
        }
    )
  }
  return deptArr;
});

const params = ref<IUserListRequest>({
    pageNum: 1,
    pageSize: 20
})

const quantifierState = computed(() => {
  return quantifierStateList.value;
});

const checkUserPlatFrom = computed(() => {
  return checkUserGroup(UserGroupSystemConstant.DAILY_PLATFORM_MANAGER.groupId);
});

const checkUserAdmin = computed(() => {
  return checkUserGroup(UserGroupSystemConstant.SUPER_MANAGE.groupId);
});


const {
  data: exportData,
  run: exportApiRun,
  loading: detailsExportLoading,
} = useRequest(dailyReportApi.exportPersonDetailsReport);


const dataSource = computed(() => data.value?.records || []);


const list = ref([]);

const columnList = computed(() => {
  columns.value = [];
  columns.value.push(
    // 固定列定义
    {
      title: '录入人员',
      dataIndex: 'quantifierUsername',
      width: 100,
      height: 30,
      align: 'center',
      fixed: 'left'
    },
    {
      title: '应提报总数',
      dataIndex: 'needCount',
      width: 100,
      align: 'center',
      fixed: 'left'
    },
    {
      title: '已提报总数',
      dataIndex: 'issueCount',
      width: 100,
      align: 'center',
      fixed: 'left'
    },
    {
      title: '提报率',
      dataIndex: 'submitRate',
      width: 150,
      align: 'center',
      fixed: 'left'
    }
  )
  
  let uniqueDates = Array.from(new Set(
    dataList.value.map((item) => ({
      ...item,
      ...flattenDateList(item),
    }))
    .flatMap((record) => Object.keys(record))
    // 对比columns列表过滤掉已存在的字段
    .filter((key) => !columns.value.some((col) => col.dataIndex === key || key== 'dateList'))
    .map((date) => (
    { 
      title: date,
      dataIndex: date,
      width: 100,
      align: 'center',
      isDate: true, 
      customCell: (record, index, column) => {
        if (record[column.dataIndex] == 10) {
          return {
            style: {
              'background-color': '#FFC8B4'
            }
          }
        }else if(record[column.dataIndex] == 40){
          return {
            style: {
              'background-color': '#C7EDCC'
            }
          }
        }else if(record[column.dataIndex] == 21 
          || record[column.dataIndex] == 22 
          || record[column.dataIndex] == 23 
          || record[column.dataIndex] == 30){
          return {
            style: {
              'background-color': '#FFFF77'
            }
          }
        }
      },
    }))
  ));

  // 去重
  let arr = uniqueDates.filter((item, index, self) =>
    index === self.findIndex((t) => t.title === item.title)
  )

  for(let i = 0; i < arr.length; i++) {
    columns.value.push(
      arr[i]
    )
  }
  return columns.value;
});

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChange = () => {
  listApiRun();
};

const exportPersonDetailsReport = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  exportApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const userNameChange = (userInfo: IUserInfo | undefined) => {
    if (!userInfo) {
      searchParam.value.quantifierUsercode = ''
      searchParam.value.quantifierUsername = ''
      return
    }
    searchParam.value.quantifierUsername = userInfo.nickName
    searchParam.value.quantifierUsercode = userInfo.username
}

const datas = ref([]);
const dataList = ref([]);
const listApiRun = async () => {
  loading.value = true;
  dailyReportApi
    .getPersonDetailsDataList(searchParam.value)
    .then((it) => {      
      dataList.value = it;

      datas.value = dataList.value?.map((item) => ({
        ...item,
        ...flattenDateList(item),
      }));

    })
    .finally(() => {
      loading.value = false;
    });
};
listApiRun();

const startBeginAndEnd = ref<[Dayjs, Dayjs]>()
const rangePresets = ref([
  {
    label: '本月',
    value: [dayjs().startOf('months').subtract(0,'months'), dayjs().add(-1, 'd')],
  },
  {
    label: '上月',
    value: [dayjs().month(dayjs().month() - 1).startOf('month'), dayjs().month(dayjs().month() - 1).endOf('month')],
  },
  { label: '近7天', value: [dayjs().add(-8, 'd'), dayjs().add(-1, 'd')] },
  { label: '近14天', value: [dayjs().add(-15, 'd'), dayjs().add(-1, 'd')] },
  { label: '近30天', value: [dayjs().add(-31, 'd'), dayjs().add(-1, 'd')] },
  { label: '近90天', value: [dayjs().add(-91, 'd'), dayjs().add(-1, 'd')] },
]);

const onRangeChange = (dates: RangeValue, dateStrings: string[]) => {
  if (dateStrings) {
    searchParam.value.startDate = dateStrings[0]
    searchParam.value.endDate = dateStrings[1]
  } else {
    searchParam.value.startDate = undefined
    searchParam.value.endDate = undefined
  }
};

const tableHeight = ref('400px');

</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <h-row :align="'middle'" >
      <h-col :span="22" style="margin-bottom: 10px">
        <h-row :gutter="[12, 22]" :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px" v-if="checkUserAdmin || checkUserPlatFrom">
            <label for="deptCode">部门：</label>
          </h-col>
          <h-col :span="4" v-if="checkUserAdmin || checkUserPlatFrom">          
            <a-select 
              v-model:value="searchParam.deptCode"
              show-search
              placeholder="请选择部门"
              style="width: 200px"
              allowClear
              :options="deptSelect"
            >
            </a-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="quantifierUsercode">人员：</label>
          </h-col>
          <h-col :span="4">
            <user-select :value="searchParam.quantifierUsername" style="width: 200px"
              :params="params" @change="(userInfo: IUserInfo) =>  userNameChange(userInfo)"/>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="startBeginAndEnd">日期：</label>
          </h-col>
          <h-col :span="9">
            <h-range-picker
              format="YYYY-MM-DD HH:mm:ss"
              v-model:value="startBeginAndEnd"
              :presets="rangePresets"
              @change="onRangeChange"
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="submitRate">提报率</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.submitRate" style="width: 200px" allowClear>
              <h-select-option :value="0">未达成</h-select-option>
              <h-select-option :value="1">已达成</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :offset="18" :span="6" style="text-align: right">
            <h-button type="primary" @click="exportPersonDetailsReport({ current: 1, pageSize: 10 })">
              <template #icon>
                <DownloadOutlined />
              </template>
              导出
            </h-button>
            <h-button style="margin-left: 10px" type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined /> 查询
            </h-button>
            <h-button style="margin-left: 10px" @click="reset">重置</h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columnList"
          :row-key="(record) => record.id"
          :data-source="datas"
          :size="'small'"
          :loading="loading"
          :scroll="{ x: 1000, y: 380 }"
          @change="handleTableChange($event as any)"
          bordered
          :pagination="false"
          class="my-custom-table" :height="tableHeight"
        >
          <template v-slot:bodyCell="{ column, text, record, index }">
            <template v-if="column.isDate">
              <div :style="{padding: '8px' }">
                <template v-if="record[column.dataIndex] == 10">X</template>
                <template v-if="record[column.dataIndex] == 40">√</template>
                <template v-if="record[column.dataIndex] == 21">休假</template>
                <template v-if="record[column.dataIndex] == 22">离职</template>
                <template v-if="record[column.dataIndex] == 23">其他</template>
                <template v-if="record[column.dataIndex] == 30">录入中</template>
              </div>
            </template>
            <template v-else>{{text}}</template>
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>
</template>

<style lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.my-custom-table .ant-table-row {
  height: 10px; /* 你想要的行高 */
  line-height: 10px; /* 确保内容垂直居中 */
}

</style>
