<script setup lang="ts">
import {theme ,
  ConfigProvider as hConfigProvider,
} from 'ant-design-vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
dayjs.locale('zh-cn');
</script>

<template>
  <h-config-provider :locale="zhCN" :theme="{
        token: {
          borderRadius: 2,
        },
      }">
    <router-view/>
  </h-config-provider>
</template>

<style scoped>


</style>
