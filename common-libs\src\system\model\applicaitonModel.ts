import { IPageRequest } from "../../basic";

export interface IApplicationInfoRequest extends IPageRequest  {
    applicationCode?: string;
    state?: string;
    type?: number
}

export interface IApplicationInfo {
     id?:number;
     applicationCode?:string;
     applicationName?:string;
     secret?:string;
     description?:string;
     state?:boolean;
     haierApplicationCode?:string;
     loginType?:string;
     gmtCreate?:string;
     gmtModified?:string;
     createBy?:string;
     lastModifiedBy?:string;
     deleted?:boolean;
     type?: number
     iconUrl?: string
}

export interface IApplicationLoginUrlRequest {
    applicationCode?: string;
}

export interface IApplicationInfoSaveRequest {
    applicationCode?:string;
    applicationName?:string;
    description?:string;
    haierApplicationCode?:string;
    loginType?:string;
    type?: number
}

export interface IApplicationInfoUpdateRequest {
    id?:number;
    applicationCode?:string;
    applicationName?:string;
     /**
     * true 需要重置秘钥
     */
    resetSecret?:boolean;
    description?:string;
    state?:boolean;
    haierApplicationCode?:string;
    loginType?:string;
    type?: number
}

export interface IApplicationInfoDeleteRequest {
    id?:number;
}
export interface IApplicationLinkResourceRequest {
    applicationId?:number
    resourceIds:number[]
}