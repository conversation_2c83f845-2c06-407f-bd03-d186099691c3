<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Cascader as hCascader,
  message
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons-vue';
import { banquetApplyApi, download, banquetApi, cityApi } from '@haierbusiness-front/apis';
import {
  IApplyFilter,
  IApply,
  BanquetStatusEnum,
  BanquetApproveStatusEnum
} from '@haierbusiness-front/common-libs';
import { getEnumOptions } from '@haierbusiness-front/utils';

import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import router from '../../router'
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
const route = ref(getCurrentRoute());
// const router = useRouter()
import type { ShowSearchType } from 'ant-design-vue/es/cascader';

const currentRouter = ref()

onMounted(async () => {
    currentRouter.value = await router
    getCityList()
    if(route.value?.query?.orderCode){
      searchParam.value.orderCode = route.value?.query?.orderCode
    }
    handleTableChange({ current: 1, pageSize: 10 })

})

// 订单状态
const orderState = computed(() => {
  return getEnumOptions(BanquetStatusEnum, true);
});

const approveState = computed(() => {
  return getEnumOptions(BanquetApproveStatusEnum, true);
});


const getorderStatus = (status:any)=>{
  var label = null
  orderState.value.forEach(item => {
    if(item.value==status){
      label = item.label
    }
  });
  return label
}

const getapproveStatus = (status:any)=>{
  var label = null
  approveState.value.forEach(item => {
    if(item.value==status){
      label = item.label
    }else{
      if(!status&&status!=0){
        label="未提交"
      }
    }
  });
  return label
}


const columns: ColumnType[] = [
  {
    title: '申请单号',
    dataIndex: 'orderCode',
    width: '240px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '申请类型',
    dataIndex: 'sceneType',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '经办人工号',
    dataIndex: 'creator',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '经办人姓名',
    dataIndex: 'creatorName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '预算人工号',
    dataIndex: 'budgeterCode',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '预算人姓名',
    dataIndex: 'budgeterName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  // {
  //   title: '联系电话',
  //   dataIndex: 'creatorPhone',
  //   width: '150px',
  //   align: 'center',
  //   ellipsis: true,
  // },
  {
    title: '申请时间',
    dataIndex: 'applicationTime',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '申请金额/元',
    dataIndex: 'budgetAmount',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '实际使用金额/元',
    dataIndex: 'actualPaymentAmount',
    width: '130px',
    align: 'center',
    ellipsis: true,
  },
  // {
  //   title: '实际下发金额/元',
  //   dataIndex: 'budgetIssuance',
  //   width: '130px',
  //   align: 'center',
  //   ellipsis: true,
  // },
  {
    title: '预算剩余金额/元',
    dataIndex: 'residualAmount',
    width: '130px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '签单人工号',
    dataIndex: 'signerCode',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '签单人姓名',
    dataIndex: 'signerName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '有效日期',
    dataIndex: 'estimatedMealTime',
    width: '300px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '用餐城市-省',
    dataIndex: 'mealLocationProvince',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '用餐城市-市',
    dataIndex: 'mealLocationCity',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '餐厅名称',
    dataIndex: 'restaurantName',
    width: '300px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '支付单号',
    dataIndex: 'payCode',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '费用科目',
    dataIndex: 'feeItemName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '预算部门',
    dataIndex: 'budgetDepartmentName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '使用状态',
    dataIndex: 'payStatus',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '审批状态',
    dataIndex: 'approveStatus',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<IApplyFilter>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(banquetApplyApi.list);

const {
  data:exportListData,
  run:exportListApiRun,
  loading:exportListLoading,
} = useRequest(banquetApplyApi.exportList);

const reset = () => {
  cityCodeList.value = []
  searchParam.value = {}
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const goToDetail = (id: number) => {
  currentRouter.value.push({
    path: '/apply/detail',
    query: {
      id: id
    }
  })
}

// 获取美团城市列表
const cityDict = ref<Array<any>>([])
const getCityList = () => {
  // banquetApi.getCity().then(res => {
  //   cityDict.value = res
  // })

  const params = {
    id: 1,
    providerCode: 'MT',
    subdistrict: 2
  }
  cityApi.getCityTree(params).then(res => {
    cityDict.value = res.children
  })
}

const filter: ShowSearchType['filter'] = (inputValue, path) => {
  return path.some(option => option.chineseName.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
};

// 根据id 获取城市 code
const getCityCodeById = (id?:string | number) => {
  if (!id) {
    return ''
  }
  let code = ''
  cityDict.value?.forEach(province => {
    province?.children?.forEach(city => {
      if (id == city.id) {
        if (city?.providerMapList && city?.providerMapList.length > 0) {
          code = city?.providerMapList[0]?.districtId
        }
      }
      
    });
  });
  return code
}
// 重新推送订单
const toRePush = (orderCode:string) =>{
  banquetApplyApi.rePush({orderCode}).then(res=>{
    message.success('重新推送成功');
    listApiRun({
    ...searchParam.value,
    pageNum: current.value,
    pageSize: pageSize.value,
    });
  })
}

const cityCodeList = ref([])
watch(() => cityCodeList.value, (n: any, o: any) => {
  if (n && n.length > 0) {
    searchParam.value.cityCode = [getCityCodeById(n[1] || n[0])]
  } else {
    searchParam.value.cityCode = undefined
  }
});

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="orderCode">申请单号:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="申请单号" v-model:value="searchParam.orderCode"  style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="applicationTimes">申请时间:</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="searchParam.applicationTimes" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="estimatedMealTimeEnd">有效日期:</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="searchParam.estimatedMealTimeEnd" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="sceneType">申请类型:</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value="searchParam.sceneType"
              allow-clear
              style="width: 100%"
              placeholder="申请类型"
            >
              <h-select-option :value="1">宴请</h-select-option>
              <h-select-option :value="2">外卖</h-select-option>
            </h-select>
          </h-col>

        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="creatorName">经办人工号/姓名:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="经办人工号/姓名" v-model:value="searchParam.creatorName"  style="width: 100%" allow-clear />
          </h-col>
          
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="signerName">签单人工号/姓名:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="签单人工号/姓名" v-model:value="searchParam.signerName"  style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="budgeterName">预算人工号/姓名:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="预算人工号/姓名" v-model:value="searchParam.budgeterName"  style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="budgetDepartmentName">预算部门:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="预算部门" v-model:value="searchParam.budgetDepartmentName"  style="width: 100%" allow-clear />
          </h-col>

        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="cityCode">用餐城市:</label>
          </h-col>
          <h-col :span="4">
            <h-cascader v-model:value="cityCodeList" show-search  :fieldNames="{label: 'name', value: 'id', children:'children'}" :options="cityDict" placeholder="城市" style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="restaurantName">餐厅名称:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="餐厅名称" v-model:value="searchParam.restaurantName"  style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="payCode">支付单号:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="支付单号" v-model:value="searchParam.payCode"  style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="feeItemName">费用科目:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="费用科目" v-model:value="searchParam.feeItemName"  style="width: 100%" allow-clear />
          </h-col>

        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="orderStatus">订单状态:</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value="searchParam.orderStatus"
              allow-clear
              style="width: 100%"
              placeholder="订单状态"
            >
            <h-select-option value="">全部</h-select-option>
              <h-select-option v-show="item.label!='待提交'" v-for="(item, index) in orderState" :key="index" :value="item.value">{{
                item.label
              }}</h-select-option>
              
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="payStatus">使用状态:</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value="searchParam.payStatus"
              allow-clear
              style="width: 100%"
              placeholder="使用状态"
            >
              <h-select-option   :value="true">已使用</h-select-option>
              <h-select-option   :value="false">未使用</h-select-option>
            </h-select>
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="approveStatus">审批状态:</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value="searchParam.approveStatus"
              allow-clear
              style="width: 100%"
              placeholder="审批状态"
            >
            
            <h-select-option value="">全部</h-select-option>
              <h-select-option v-for="(item, index) in approveState" :key="index" :value="item.value">{{
                item.label
              }}</h-select-option>
            </h-select>
          </h-col>

          
        </h-row>


        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button  style="margin-right: 10px" type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>

            <!-- <h-button type="primary" :loading="exportListLoading" @click="exportListApiRun(searchParam);">
              导出
            </h-button> -->
          </h-col>
        </h-row>


        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'estimatedMealTime'">
              {{ record.estimatedMealTimeStart }} - {{ record.estimatedMealTimeEnd }}
            </template>
            <template v-if="column.dataIndex === 'sceneType'">
              {{ record.sceneType == 1 ? '宴请' :'外卖' }}
            </template>
            <template v-if="column.dataIndex === 'orderStatus'">
              <!-- 订单状态 -->
             {{ getorderStatus(record.orderStatus) }}
            </template>
            <template v-if="column.dataIndex === 'payStatus'">
              <!-- 订单状态 -->
             {{ record.payStatus?'已使用':'未使用'}}
            </template>
            <template v-if="column.dataIndex === 'approveStatus'">
              <!-- 审批状态 -->
              {{getapproveStatus(record.approveStatus)}}
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="goToDetail(record.id)">查看详情</h-button>
              <a-popconfirm title="确定要重新推送该订单吗?" @confirm="toRePush(record.orderCode)">
                <h-button v-if="record.mtApplyCode==-1"  type="link">重新推送</h-button>
              </a-popconfirm>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    

  </div>
</template>

<style scoped lang="less">

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

</style>
