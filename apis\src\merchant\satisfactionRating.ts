import { download, get, post } from '../request'
import { 
    ISatisfactionRatingFilter, 
    ISatisfactionRating,
    IPageResponse, 
    Result } from '@haierbusiness-front/common-libs'


export const satisfactionRatingApi = {
    list: (params: ISatisfactionRatingFilter): Promise<IPageResponse<ISatisfactionRating>> => {
        return get('merchant/api/satisfactionRating/list', params)
    },

    get: (id: number): Promise<ISatisfactionRating> => {
        return get('merchant/api/satisfactionRating/get', {
            id
        })
    },

    save: (params: ISatisfactionRating): Promise<Result> => {
        return post('/mice-bid/api/evaluate/create', params)
    },

    edit: (params: ISatisfactionRating): Promise<Result> => {
        return post('merchant/api/satisfactionRating/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('merchant/api/satisfactionRating/delete', { id })
    },
}
