<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Input as hInput,
  Cascader as hCascader,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Popover as hPopover,
  message 
} from 'ant-design-vue';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';

import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, VerticalAlignBottomOutlined,ExclamationCircleOutlined  } from '@ant-design/icons-vue';
import { statementAccountApi, download, banquetApi, cityApi } from '@haierbusiness-front/apis';
import {
  SyncSettleParams,
  IApply,
  BanquetStatusEnum,
  BanquetApproveStatusEnum
} from '@haierbusiness-front/common-libs';
import { getEnumOptions } from '@haierbusiness-front/utils';

import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted,createVNode  } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import router from '../../router'
// const router = useRouter()
import type { ShowSearchType } from 'ant-design-vue/es/cascader';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
const route = ref(getCurrentRoute());
const currentRouter = ref()

onMounted(async () => {
    currentRouter.value = await router
    if(route.value?.query?.settleCode){
      searchParam.value.statementCode = route.value?.query?.settleCode
    }
    handleTableChange({ current: 1, pageSize: 10 })

})

const syncSettleLoading = ref<boolean>(false)
const openModalShow = ref<boolean>(false)

const openModal = () =>{
  openModalShow.value = true
}

// 订单状态
const orderState = computed(() => {
  return getEnumOptions(BanquetStatusEnum, true);
});

const approveState = computed(() => {
  return getEnumOptions(BanquetApproveStatusEnum, true);
});

const rules = {
  settleMonth: [
    {
      required: true,
      message: '请选择账单所属年月',
      trigger: 'change',
    },
  ],
  sceneType: [
    {
      required: true,
      message: '请选择账单类型',
      trigger: 'change',
    },
  ],
  settleDates: [
    {
      required: true,
      message: '请选择账单日期',
      trigger: 'change',
    },
  ],
}

const columns: ColumnType[] = [
  {
    title: '对账单号',
    dataIndex: 'statementCode',
    width: '240px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '账单开始日期',
    dataIndex: 'settleStartDate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '账单结束日期',
    dataIndex: 'settleEndDate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '账单所属年月',
    dataIndex: 'settleMonth',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '账单类型',
    dataIndex: 'sceneType',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },

  {
    title: '本地订单总金额',
    dataIndex: 'effectiveSettlementAmount',
    width: '140px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '美团订单总金额',
    dataIndex: 'mtOriginAmount',
    width: '140px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: "本地企业支付总金额",
    dataIndex: "entPayAmount",
    align: "center",
    width: "140px",
    ellipsis: true
  },
  {
    title: "美团企业支付总金额",
    dataIndex: "mtSettlementAmount",
    align: "center",
    width: "140px",
    ellipsis: true
  },
  {
    title: "差异金额/元",
    dataIndex: "diffAmount",
    align: "center",
    width: "120px",
    ellipsis: true
  },
  // {
  //   title: '服务费/元',
  //   dataIndex: 'realtimeServiceFee',
  //   width: '120px',
  //   align: 'center',
  //   ellipsis: true,
  // },
  // {
  //   title: '差异金额/元',
  //   dataIndex: 'diffAmount',
  //   width: '120px',
  //   align: 'center',
  //   ellipsis: true,
  // },

  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '350px',
    fixed: 'right',
    align: 'center'
  },
];

const searchParam = ref<SyncSettleParams>({})
const syncSettleForm = ref<SyncSettleParams>({})

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(statementAccountApi.list);

const {
  data:exportListData,
  run:exportListApiRun,
  loading:exportListLoading,
} = useRequest(statementAccountApi.exportList);

const {
  data:exportInvoiceData,
  run:exportInvoice,
  loading:exportInvoiceLoading,
} = useRequest(statementAccountApi.exportInvoice);

const reset = () => {
  searchParam.value = {}
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize: 10,
  })
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const goToDetail = (id: number) => {
  currentRouter.value.push({
    path: '/statementAccount/detail',
    query: {
      id: id
    }
  })
}
const formRef = ref();
// 账单同步 
const syncSettle =()=>{
  formRef.value
    .validate()
    .then(() => {
      syncSettleLoading.value = true
      statementAccountApi.syncSettle(syncSettleForm.value).then((res:any)=>{
        listApiRun({
        ...searchParam.value,
        pageNum: 1,
        pageSize: 10,
      });
      message.success('账单同步成功')
      openModalShow.value = false
      syncSettleLoading.value = false
      })
  .catch(()=>{
    syncSettleLoading.value = false
  })
    })
    .catch((error:any) => {
      console.log('error', error);
    });

}

const monthChange = (val:any)=>{
  console.log(val)
  if(val){
    const { start, end } = getMonthStartAndEnd(new Date(val).getFullYear(), new Date(val).getMonth()+1);
    syncSettleForm.value.settleDates = [start,end]
    // console.log(start,end,"-----")
  }else{
    syncSettleForm.value.settleDates = []
  }
}

//账单取消
const showConfirm = (id:string) => {
  hModal.confirm({
    title: '确定要取消此对账单吗？',
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {
      statementAccountApi.settleCancel({id}).then((res:any)=>{
        message.success('取消成功')
        listApiRun({
        ...searchParam.value,
        pageNum: 1,
        pageSize: 10,
        }
        );
      })
    },
    onCancel() {
      console.log('Cancel');
    }
  });
}; 

const getMonthStartAndEnd =(year:any, month:any)=>{
  // 月份从0开始，所以需要减1
  const date = new Date(year, month - 1, 1);
  const startTime = new Date(date);
  let endTime = new Date(year, month, 0);
  // console.log(month,dayjs().month() + 1,year,dayjs().year(),endTime,dayjs().subtract(1, 'day'),"*****")
  if(dayjs().month() + 1 == month&&year==dayjs().year()){
    endTime = dayjs().subtract(1, 'day')
  }
  return {
    start:dayjs(startTime).format('YYYY-MM-DD'),
    end:dayjs(endTime).format('YYYY-MM-DD')
  };
}

const disabledDate = (current: Dayjs) => {
  // Can not select days before today and today
  return current && current > dayjs().subtract(1, 'day')
};

</script>

<template>
  <div
    style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;"
  >
    <h-row :align="'middle'">
      <!-- <a-card style="width: 100%;margin-bottom:10px;">
        <h-col :span="24" style="margin-bottom: 10px;">
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="orderCode">账单所属年月:</label>
            </h-col>
            <h-col :span="4">
              <h-date-picker
                v-model:value="syncSettleForm.settleMonth"
                picker="month"
                value-format="YYYY-MM-DD hh:mm:ss"
                style="width: 100%"
                @change="monthChange"
                allow-clear
              />
            </h-col>
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="creatorName">账单类型:</label>
            </h-col>
            <h-col :span="4">
              <h-select
                ref="select"
                v-model:value="syncSettleForm.sceneType"
                allow-clear
                style="width: 100%"
                placeholder="账单类型"
              >
                <h-select-option :value="1">宴请</h-select-option>
                <h-select-option :value="2">外卖</h-select-option>
              </h-select>
            </h-col>

            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="creator">账单日期:</label>
            </h-col>
            <h-col :span="4">
              <h-range-picker
                v-model:value="syncSettleForm.settleDates"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                allow-clear
              />
            </h-col>
            <h-col :span="6" style="text-align: right;">
              <h-button
                type="primary"
                :loading="syncSettleLoading"
                @click="syncSettle();"
              >账单同步</h-button>
            </h-col>
          </h-row>
        </h-col>
      </a-card> -->
      <!-- <a-card style="width: 100%;margin-bottom:10px;"> -->
        <h-col :span="24" style="margin-bottom: 10px;">
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="orderCode">对账单号:</label>
            </h-col>
            <h-col :span="4">
              <h-input
                placeholder="对账单号"
                v-model:value="searchParam.statementCode"
                style="width: 100%"
                allow-clear
              />
            </h-col>
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="creatorName">账单所属年月:</label>
            </h-col>
            <h-col :span="4">
              <h-date-picker
                v-model:value="searchParam.settleMonth"
                picker="month"
                value-format="YYYY-MM-DD hh:mm:ss"
                style="width: 100%"
                allow-clear
              />
            </h-col>

            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="creator">账单日期:</label>
            </h-col>
            <h-col :span="4">
              <h-range-picker
                v-model:value="searchParam.settleDates"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                allow-clear
              />
            </h-col>

            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="signerName">账单类型:</label>
            </h-col>
            <h-col :span="4">
              <h-select
                ref="select"
                v-model:value="searchParam.sceneType"
                allow-clear
                style="width: 100%"
                placeholder="账单类型"
              >
                <h-select-option :value="1">宴请</h-select-option>
                <h-select-option :value="2">外卖</h-select-option>
              </h-select>
            </h-col>
          </h-row>
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="signerName">订单状态:</label>
            </h-col>
            <h-col :span="4">
              <h-select
                ref="select"
                v-model:value="searchParam.orderStatus"
                allow-clear
                style="width: 100%"
                placeholder="订单状态"
              >
                <h-select-option :value="1">待确认</h-select-option>
                <h-select-option :value="2">已确认</h-select-option>
                <h-select-option :value="3">已取消</h-select-option>
              </h-select>
            </h-col>
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="signerName">差异金额是否为0:</label>
            </h-col>
            <h-col :span="4">
              <h-select
                ref="select"
                v-model:value="searchParam.sameAmount"
                allow-clear
                style="width: 100%"
                placeholder="订单状态"
              >
                <h-select-option :value="1">是</h-select-option>
                <h-select-option :value="0">否</h-select-option>
              </h-select>
            </h-col>
          </h-row>
          <h-row :align="'middle'" style="padding: 10px 10px 10px 10px;">
            <h-col :span="24" style="text-align: right;">
              <h-button style="margin-right: 10px" @click="reset">重置</h-button>
              <h-button
                style="margin-right: 10px"
                type="primary"
                @click="openModal"
              >
                账单同步
              </h-button>

              <h-button
                style="margin-right: 10px"
                type="primary"
                @click="handleTableChange({ current: 1, pageSize: 10 })"
              >
                <SearchOutlined />查询
              </h-button>

              <h-button
                type="primary"
                :loading="exportListLoading"
                @click="exportListApiRun(searchParam);"
              >导出</h-button>
            </h-col>
          </h-row>
        </h-col>
      <!-- </a-card> -->
      <h-col :span="24">
        <h-table
          :columns="columns"
          :row-key="record => record.id"
          :size="'small'"
          :data-source="dataSource"
          :pagination="pagination"
          :scroll="{ y: 550 }"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
        <!-- <template #headerCell="{ column }">
            <template
              v-if="column.dataIndex === 'effectiveSettlementAmount'"
            >
            有效结算
            <a-tooltip placement="top">
              <template #title>
                <span>（企业+服务费）</span>
              </template>
              <ExclamationCircleOutlined />
            </a-tooltip>
              
            </template>
            
           
          </template> -->

          <template #bodyCell="{ column, record }">
            <template
              v-if="column.dataIndex === 'sceneType'"
            >{{ record.sceneType == 1 ? '宴请' :'外卖' }}
            </template>
            <template
              v-if="column.dataIndex === 'orderStatus'"
            >{{ record.orderStatus == 1 ? '待确认' :record.orderStatus == 2?'已确认':'已取消' }}
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button v-if="record.orderStatus != 1" type="link" @click="goToDetail(record.id)">查看详情</h-button>
              <h-button v-if="record.orderStatus == 1" type="link" @click="goToDetail(record.id)">账单确认</h-button>
              <h-button v-if="record.orderStatus == 1" type="link" @click="showConfirm(record.id)">取消</h-button>
              <h-button v-if="record.orderStatus != 3" type="link" @click="exportInvoice({id:record.id})">导出开票信息</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
      <h-modal v-model:open="openModalShow" :confirmLoading="syncSettleLoading" title="账单同步" @ok="syncSettle">
        <h-form ref="formRef" :model="syncSettleForm"  :rules="rules"  :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }" >
            <h-form-item name="settleMonth" label="账单所属年月">
              <h-date-picker
                v-model:value="syncSettleForm.settleMonth"
                picker="month"
                value-format="YYYY-MM-DD hh:mm:ss"
                style="width: 100%"
                :disabled-date="disabledDate"
                @change="monthChange"
                allow-clear
              />
              </h-form-item>
              <h-form-item name="sceneType" label="账单类型">
                <h-select
                  ref="select"
                  v-model:value="syncSettleForm.sceneType"
                  allow-clear
                  style="width: 100%"
                  placeholder="账单类型"
                >
                  <h-select-option :value="1">宴请</h-select-option>
                  <h-select-option :value="2">外卖</h-select-option>
                </h-select>
              </h-form-item>

              <h-form-item name="settleDates" label="账单日期">
                <h-range-picker
                  v-model:value="syncSettleForm.settleDates"
                  :disabled-date="disabledDate"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  allow-clear
                />
              </h-form-item>
          </h-form>
      </h-modal>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

:deep(.ant-card .ant-card-body) {
  padding: 0;
}
</style>
