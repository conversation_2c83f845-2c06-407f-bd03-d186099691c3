<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import {
  DownOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
} from '@ant-design/icons-vue';
import { workOrderApi,callCenterApi } from '@haierbusiness-front/apis';
import { IEnterpriseListRequest, IEnterprise } from '@haierbusiness-front/common-libs';
import { errorModal, routerParam } from '@haierbusiness-front/utils';
import dayjs, { Dayjs } from 'dayjs';
import { computed, onMounted, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useRouter } from 'vue-router';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables';
import EditDialog from '../components/dialog/edit-dialog.vue';
import EditDetailDialog from '../components/dialog/workOrderDetail.vue';

const router = useRouter();
const columns: ColumnType[] = [
  {
    title: '工单编号',
    dataIndex: 'workTableNum',
    align: 'left',
  },
  {
    title: '工单标题',
    dataIndex: 'workName',
    align: 'left',
    width: '200px',
  },
  {
    title: '工单类型',
    dataIndex: 'workOrderType',
    align: 'left',
  },
  {
    title: '来电电话',
    dataIndex: 'mobile',
    align: 'left',
  },
  {
    title: '业务单号',
    dataIndex: 'businessNum',
    align: 'left',
    width: '200px',
  },
  {
    title: '创建人工号',
    dataIndex: 'createBy',
    align: 'left',
  },
  {
    title: '创建人姓名',
    dataIndex: 'createName',
    align: 'left',
  },
  {
    title: '产品线',
    dataIndex: 'productName',
    align: 'left',
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    align: 'left',
  },
  {
    title: '状态',
    dataIndex: 'status',
    align: 'left',
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '250px',
    fixed: 'right',
    align: 'center',
  },
];
const searchParam = ref<IEnterpriseListRequest>({});

const manageParams = ref();
const editDetail = ref<any>({})
// 明细弹窗
const detailVisible = ref<boolean>(false);
const productList = ref<any>([]);
const isLook = ref<boolean>(false)

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(workOrderApi.list, {
  defaultParams: [{}],
  manual: false,
});

const reset = () => {
  searchParam.value = {};
  handleTableChange({ current: 1, pageSize: 10 })
};

const dataSource = computed(
  () =>
    data.value?.records?.filter((item) => {
      if (item.iconUrl) {
        item.files = [
          {
            name: `${item.productName}`,
            thumbUrl: item.iconUrl,
          },
        ];
      }
      return item;
    }) || [],
);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

// 新增表单相关
const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } = useEditDialog<
  IEnterprise,
  IEnterprise
>(workOrderApi, '工单', () =>
  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize,
  }),
);

const { handleDelete } = useDelete(workOrderApi, () =>
  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize,
  }),
);

const EditDetailDialogCancel = (val) =>{
    detailVisible.value = false
    if(!val){
      listApiRun({
        ...searchParam.value,
        pageNum: data.value?.pageNum,
        pageSize: data.value?.pageSize,
      })
    }
}

const Cancel = () =>{
    detailVisible.value = false
}

const saveWorkOrder = (res:any) =>{
  visible.value = false
  editDetail.value = res
  detailVisible.value = true
  listApiRun({
    pageNum:1,
    pageSize:10,
  })
}

// 创建工单
const createdWorkOrder = () =>{
  isLook.value = false
  editDetail.value = {
    mobile: '',
    workName: '',
    userName:"",
    userNum:"",
    personName: '',
    personNum: '',
    businessNum: [""],
    detailsList:[]
  }
  detailVisible.value = true
}

// 修改日期
const changeDate = (val: any) => {
  if (val) {
    searchParam.value.startTime = val[0] + " 00:0:00";
    searchParam.value.endTime = val[1] + " 23:59:59";
  } else {
    searchParam.value.startTime = null;
    searchParam.value.endTime = null;
  }
};

// 编辑明细
const handleDateilEdit = (row:any,val:boolean) =>{
  isLook.value = val
  editDetail.value = row
  detailVisible.value = true
}
// 获取产品线的数据
const getProductList = () => {
  callCenterApi.getProductList().then((res: any) => {
    productList.value = res;
  });
};
// 初始化
onMounted(async () => {
  getProductList();
});

</script>

<template>
  <!-- <Eloading :loading="confirmLoading"></Eloading>
  <Eloading :loading="confirmAccountLoading"></Eloading>
  <Eloading :loading="revokeConfirmLoading"></Eloading>
  <Eloading :loading="cancelLoading"></Eloading> -->
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 30px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col style="text-align: right; padding-right: 10px;width:80px;">
            <label for="gdNum">工单编号：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="gdNum"
              v-model:value="searchParam.gdNum"
              placeholder=""
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col style="text-align: right; padding-right: 10px;width:93px;">
            <label for="titleName">工单标题：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="titleName"
              v-model:value="searchParam.titleName"
              placeholder=""
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col style="text-align: right; padding-right: 10px;width:90px;">
            <label for="mobile">电话：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="mobile"
              v-model:value="searchParam.mobile"
              placeholder=""
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col style="text-align: right; padding-right: 10px;width:100px;">
            <label for="personNum">创建人：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="personNum"
              v-model:value="searchParam.personNum"
              placeholder=""
              autocomplete="off"
              allow-clear
            />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 20px 10px 0px 10px">
          <h-col style="text-align: right; padding-right: 10px;width:80px;">
            <label for="orderStatus">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select  v-model:value="searchParam.orderStatus" allowClear  style="width:100%;">
              <h-select-option :value="10">处理中</h-select-option>
              <h-select-option :value="20">已闭环</h-select-option>
            </h-select>
          </h-col>
          <h-col style="text-align: right; padding-right: 10px;width:93px;">
            <label for="customerNum">创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker
              valueFormat="YYYY-MM-DD"
              :presets="rangePresets"
              @change="changeDate"
              v-model:value="searchParam.Date"
            />
          </h-col>
          <h-col style="text-align: right; padding-right: 10px;width:90px;">
            <label for="mobile">产品线：</label>
          </h-col>
          <h-col :span="4">
            <h-select allowClear v-model:value="searchParam.productId" style="width: 100%">
              <h-select-option v-for="item in productList" :value="item.id">{{ item.name }}</h-select-option>
            </h-select>
          </h-col>
        </h-row>
         <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right">
            <h-button style="margin-right: 10px" @click="createdWorkOrder">创建工单</h-button>
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row> 
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :ellipsis="true"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="dataSource"
          :pagination="pagination"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'status'">
              <a-tag color="#2db7f5" v-if="record.status == 10">处理中</a-tag>
              <a-tag color="#87d068" v-if="record.status == 20">已闭环</a-tag>
            </template>
            <template v-if="column.dataIndex === 'workOrderType'">
              <div>{{record.workOrderType==1?'投诉':'咨询'}}</div>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <!-- <h-button v-if="record.status != 20" type="link" @click="handleEdit(record)">编辑</h-button> -->
              <h-button v-if="record.status != 20" type="link" @click="handleDateilEdit(record,false)">编辑工单</h-button>
              <!-- <h-button v-if="record.status != 20" type="link" @click="handleDelete(record.id)">删除</h-button> -->
              <h-button  type="link" @click="handleDateilEdit(record,true)">查看工单</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>

  <!-- <edit-dialog v-if="visible" :show="visible" :data="editData" @cancel="onDialogClose" @ok="saveWorkOrder"> </edit-dialog> -->
  <EditDetailDialog @cancel="EditDetailDialogCancel" @close="Cancel" v-if="detailVisible" :show="detailVisible" :id="editDetail.id" :isLook="isLook"> </EditDetailDialog>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
