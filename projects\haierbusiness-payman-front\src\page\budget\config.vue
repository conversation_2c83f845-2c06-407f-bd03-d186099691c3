<script setup lang="ts">
import { Popconfirm } from 'ant-design-vue';
import { Textarea as hTextarea, <PERSON><PERSON> as hModal, Popconfirm as hPopconfirm, Select as hSelect, SelectOption as hSelectOption, Checkbox as hCheckbox, CheckboxGroup as hCheckboxGroup, Radio as hRadio, RadioGroup as hRadioGroup, Switch as hSwitch, Input as hInput, Form as hForm, FormItem as hFormItem, Dropdown as hDropdown, InputSearch as InputSearch, Avatar as hAvatar, ListItemMeta as hListItemMeta, ListItem as hListItem, List as hList, Tree as hTree, BreadcrumbItem as hBreadcrumbItem, Breadcrumb as hBreadcrumb, LayoutSider as hLayoutSider, LayoutFooter as hLayoutFooter, LayoutContent as hLayoutContent, LayoutHeader as hLayoutHeader, Layout as hLayout, MenuItem as hMenuItem, MenuItemGroup as hMenuItemGroup, SubMenu as hSubMenu, <PERSON><PERSON> as hMenu, <PERSON><PERSON><PERSON> as hDivider, Space as hSpace, <PERSON><PERSON> as hButton, <PERSON> as hCol, <PERSON>sult as hResult, Row as hRow, TabPane as hTabPane, Tabs as hTabs, message, TreeProps, InputProps } from 'ant-design-vue';
import { UnwrapRef, onMounted, reactive, ref, toRaw, watchEffect } from 'vue';
import { ExclamationCircleOutlined, ArrowDownOutlined, ArrowUpOutlined, EnterOutlined, DeleteOutlined, FileOutlined, UngroupOutlined, FolderOutlined, HomeOutlined, NodeExpandOutlined, DownOutlined, FrownOutlined , UploadOutlined, UserOutlined, NotificationOutlined, AppstoreOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons-vue';
import { resourceApi, applicationApi, budgetConfigApi } from '@haierbusiness-front/apis';
import { IApplicationInfo, IResourceInfoTreeResponse, IBudgetConfigRequest, HaierBudgetTypeConstant, paymentMethodConstant,
  IFeeItemList, IBudgetSettingList, IPayTypeList } from '@haierbusiness-front/common-libs'
import lodash from 'lodash'
import { CheckInfo } from 'ant-design-vue/lib/vc-tree/props';
import { Key } from 'ant-design-vue/lib/_util/type';

const resource = ref(paymentMethodConstant)

const selectedApplicationKeys = ref()
const selectApplicationCode = ref()
const selectApplicationName = ref()
const applicationList = ref<IApplicationInfo[] | undefined>([]);
applicationApi.list({ type: 1 }).then(it => {
  if (it && it.records && it.records.length > 0) {
    applicationList.value = it.records;
    onSelectApplication({ key: applicationList.value[0].id })
    selectedApplicationKeys.value = [it.records[0].id]
    getlist(it.records[0].applicationCode!)
  }
})

const getlist = (applicationCode: string) => {
  budgetConfigApi.list(applicationCode).then((data) => {
    if(data && data.payTypeList && data.payTypeList.length > 0) {
      const keys: Array<any> = []
      data.payTypeList.map(item => {
         keys.push(item.payTypeId);
        (item.budgetSettingList && item.budgetSettingList.length > 0) &&(item.budgetSettingList.map(o => {
          const json: IBudgetSettingList = {
            budgetType: o.budgetType,
            settingJson: o.settingJson
          }
          jsonList.value = [...jsonList.value, json]
          keys.push(o.budgetType);
          (o.feeItemList && o.feeItemList.length > 0) && (o.feeItemList.map(n => {
            keys.push(o.budgetType + ':' + n.itemCode)
          }))
        }));
      })
      checkedKeys.value = keys
    } else {
      checkedKeys.value = []
    }
  })
}

// 选择应用
const onSelectApplication = (obj: any) => {
  if (applicationList.value) {
    for (let i of applicationList.value) {
      if (obj.key === i.id) {
        selectApplicationCode.value = i.applicationCode
        selectApplicationName.value = i.applicationName
        submitData.value.applicationCode = i.applicationCode
        getlist(i.applicationCode!)
      }
    }
  }
}

const submitData = ref<IBudgetConfigRequest>({})

// 支付方式
const array = [1, 2, 3, 4, 5]

// 预算编码数组
const budgetArray = ['DEPT_BCC_1', 'DEPT_BCC_2', 'DEPT_GEMS_1', 'DEPT_KEMS_1', 'DEPT_RRS_1']

const checkedKeys = ref<Array<any>>()
const selectedTreeKeys = ref<Array<any>>([])
const visible = ref<boolean>(false)
const saveTitle = ref("资源配置")

watchEffect(() => {
  if(selectedTreeKeys.value && selectedTreeKeys.value.length > 0 ) {
    const currentKey = selectedTreeKeys.value[0]

    if(budgetArray.includes(currentKey)) {
      // 点击的是预算
      visible.value = true
      saveTitle.value = HaierBudgetTypeConstant.ofCode(currentKey)?.name || ''
      const json = jsonList.value?.find(o => currentKey === o.budgetType)?.settingJson
      treeItemForm.value = {
        budgetType: currentKey,
        settingJson: json
      }
    } else {
      visible.value = false
      saveTitle.value = '资源配置'
    }
  }
})

const onSelect: TreeProps['onSelect'] = (selectedKeys, info) => {
    const key = info.node.key
    if(checkedKeys.value?.some((o: string | number) => o === key)) {
      // 取消选中已勾选的条目 
        if (!info.selected){
          const keys: Array<any> = [key]
          // 如果该节点有子集  取消子集选中状态
          if(info.node.children && info.node.children.length > 0){
            
            info.node?.children?.map((item: any) => {
              if(item.children && item.children.length > 0) {
                item.children.map((n: any) => {
                  keys.push(n.key)
                })
              }
              keys.push(item.key)
            })
          }
          
          checkedKeys.value = checkedKeys.value.filter((item: string | number) => {
            return !keys.includes(item)
          })
        }
    } else {
        checkedKeys.value = checkedKeys.value ? [...checkedKeys.value!, key!] : [key]
        selectedTreeKeys.value = [key]

        // 选中子节点  需要将父节点一起勾选
        if(info.node.parent) {
          // 存在父级
          // 判断父级是否已经勾选
          if (!checkedKeys.value.some(o => o === info.node.parent!.key)) {
            // 如果没被选中，将父级添加进去    父级的父级也需要加入
            if(info.node.parent.parent) {
              // 父级存在父级
              !checkedKeys.value.some(o => o === info.node.parent!.key) && (checkedKeys.value = [info.node.parent.parent.key, info.node.parent.key, ...checkedKeys.value])
            } 
            else
              checkedKeys.value = [info.node.parent.key, ...checkedKeys.value]
          }
        }

    }
}

const onCheck = (selectedKeys: { checked: Key[], halfChecked: Key[] }, info: any) => {
    const index = selectedKeys.checked.length

    if (index) {
        selectedTreeKeys.value = [selectedKeys.checked[index - 1]] as string[]
    } else {
        selectedTreeKeys.value = []
    }
    checkedKeys.value = selectedKeys.checked

    if(!info.checked) {
      selectedTreeKeys.value = []
    }

    
    // 点击子集自动勾选父级
    if (info.checked && info.node.parent) {
      // 存在父级
      // 判断父级是否已经选中
      if (!selectedKeys.checked.some(o => o === info.node.parent.key)) {
        // 如果没被选中，将父级添加进去    父级的父级也需要加入
        if(info.node.parent.parent) {
          // 父级存在父级
          !selectedKeys.checked.some(o => o === info.node.parent.key) && (checkedKeys.value = [info.node.parent.parent.key, info.node.parent.key, ...selectedKeys.checked])
        } else
          checkedKeys.value = [info.node.parent.key, ...selectedKeys.checked]
      }
    }

    // 取消父级自动取消子集所有勾选状态
    if(!info.checked && info.node.children && info.node.children.length > 0) {
      const keys: Array<any> = [info.node.key]
      info.node.children.map((item: any) => {
        if(item.children && item.children.length > 0) {
          item.children.map((n: any) => {
            keys.push(n.key)
          })
        }
        keys.push(item.key)
      })
      checkedKeys.value = selectedKeys.checked.filter(o => !keys.includes(o))
    }
    

    // setSubmitData(selectedKeys.checked)
}

const submit = () => {
  const feeItems = checkedKeys.value?.filter(o => o.toString().indexOf(':') > -1) // 第三级
  const payTypes = checkedKeys.value?.filter(o => array.includes(o)) // 第一级
  const budgetSettings= checkedKeys.value?.filter(o => !array.includes(o) && o.toString().indexOf(':') === -1) // 第二级
  if (payTypes && payTypes.length > 0) {
    const payTypeList: Array<IPayTypeList> = []
    paymentMethodConstant.map(item => {
      
      if(payTypes.includes(item.key)) {
        // 勾选
        const budgetSettingList: Array<IBudgetSettingList> = [];
        (item.children) && item.children.map(o => {
          const feeItemList: Array<IFeeItemList> = [];
          if(o.children && budgetSettings && budgetSettings.includes(o.key)) {
            o.children.map(n => {
              if(feeItems && feeItems.includes(n.key)) {
                const code = n.key.replace(o.key + ':','')
                const feeItem = {
                  itemCode: code,
                  itemName: n.title
                }
                feeItemList.push(feeItem)
              }
            })

            const json = jsonList.value.find(j => j.budgetType === o.key) 

            const budgetSetting: IBudgetSettingList = {
              budgetType: o.key,
              settingJson: json ? json.settingJson : '',
              feeItemList: feeItemList
            }
            budgetSettingList.push(budgetSetting)
          }
        });

        const data: IPayTypeList = {
          payTypeId: item.key,
          payTypeName: item.title,
          budgetSettingList
        }
        payTypeList.push(data)
      }
    })
    const subData: IBudgetConfigRequest = {
      applicationCode:  selectApplicationCode.value,
      payTypeList
    }

    budgetConfigApi.save(subData).then(() => {
      message.success('配置成功')
    })
  } else {
    message.error('请勾选资源树！')
  }
}

// 用于展示与修改的
const treeItemForm = ref<IBudgetSettingList>({})
// 选中的
const selectedTreeItem = ref<IResourceInfoTreeResponse>({})


const labelCol = { span: 6 }
const wrapperCol = { span: 16 }

const jsonList = ref<Array<IBudgetSettingList>>([])

const settingJsonChange: InputProps['onChange'] = (e) => {
  const settingJson = e.target.value
  if(jsonList.value?.some(o => o.budgetType === treeItemForm.value.budgetType)) {
    const budget = jsonList.value.find(o => o.budgetType === treeItemForm.value.budgetType)
    budget && (budget.settingJson = settingJson)
  } else {
    jsonList.value = [...jsonList.value, { budgetType: treeItemForm.value.budgetType, settingJson }]
  }
}

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;">
    <h-row style="height:100%">
      <h-col :span="3" style="height:100%">
        <div style="height:100% ">
          <div
            style="height:40px;border-right: 10px solid #f0f2f5;padding: 5px 20px 10px  20px  ;font-size: 18px;font-weight: 600;border-bottom: 1px solid #f0f0f0;">
            应用
          </div>
          <div style="height:calc(100% - 40px);border-right: 10px solid #f0f2f5; overflow-y: auto;">
            <h-menu v-if="applicationList" v-model:selectedKeys="selectedApplicationKeys" @click="onSelectApplication"
              style="height:100%;width: 99%;border: none;overflow-x: hidden;overflow-y: auto;" mode="inline">
              <h-menu-item :key="i.id" v-for="i of applicationList">
                {{ i.applicationName }}
              </h-menu-item>
            </h-menu>
          </div>
        </div>
      </h-col>
      <h-col :span="13" style="height:100%">
        <div
          style="height:40px;border-right: 10px solid #f0f2f5;padding: 5px 20px 10px  20px  ;font-size: 18px;font-weight: 600;border-bottom: 1px solid #f0f0f0;">
          <h-row>
            <h-col :span="4">资源树</h-col>
            <h-col :span="20" class="hint">
              <h-button type="primary" size="small" @click="submit">保存</h-button>
            </h-col>
          </h-row>
        </div>
        <div style="height:calc(100% - 40px);border-right: 10px solid #f0f2f5; overflow-y: auto;padding: 15px;">
          <h-tree v-if="resource.length" checkable :show-line="{ showLeafIcon: false }" :show-icon="true" :tree-data="resource"
             @select="onSelect" @check="(key, info) => onCheck(key as any, info)" :checkedKeys="checkedKeys" v-model:selectedKeys="selectedTreeKeys" :check-strictly="true">
            <template #title="{ dataRef }">
              <span>{{ dataRef.title }}</span>
            </template>
            <template #switcherIcon="{ switcherCls }">
              <down-outlined :class="switcherCls" />
            </template>
          </h-tree>
        </div>
      </h-col>
      <h-col :span="8" style="height:100%">
        <div
          style="height:40px;border-right: 10px solid #f0f2f5;padding: 5px 20px 10px  20px  ;font-size: 18px;font-weight: 600;border-bottom: 1px solid #f0f0f0;">
          {{ saveTitle }}
        </div>
        <div style="height:calc(100% - 40px);border-right: 10px solid #f0f2f5; overflow-y: auto;padding: 40px;">
          <h-form :model="treeItemForm" :label-col="labelCol" :wrapper-col="wrapperCol">
            <template v-if="visible">
              <h-form-item label="Json配置">
                <h-textarea v-model:value="treeItemForm.settingJson" @change="settingJsonChange" />
              </h-form-item>
            </template>
          </h-form>
        </div>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.hint {
  padding-top: 6px;
  font-size: 10px;
  font-weight: 400;
  color: red;
  text-align: right;

  .hint-icon {
    font-size: 13px;
    font-weight: 700;
  }

}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}
</style>
