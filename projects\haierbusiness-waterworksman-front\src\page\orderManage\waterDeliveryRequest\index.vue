<script setup lang="ts">
import { SearchOutlined, DownloadOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { userApi } from '@haierbusiness-front/apis';
import {
  Button as hButton,
  Col as hCol,
  Input as hInput,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  DatePicker as hDatePicker,
  message,
} from 'ant-design-vue';
import { computed, ref } from 'vue';
import { usePagination } from 'vue-request';
import { ColumnType } from 'ant-design-vue/es/table';
import InfoModal from './add-modal.vue';

type MyColumn = ColumnType & {
  children?: ColumnType[];
};

const columns: MyColumn[] = [
  {
    title: '序号',
    dataIndex: 'index',
    width: '5%',
  },
  {
    title: '送水单号',
    dataIndex: 'applicationForm',
    width: '10%',
  },
  {
    title: '类型',
    dataIndex: '类型',
    width: '10%',
  },
  {
    title: '申请单位',
    dataIndex: 'orderDown',
    width: '10%',
  },
  {
    title: '送水区域',
    dataIndex: '送水区域',
    width: '10%',
  },
  {
    title: '送水地址',
    dataIndex: '送水地址',
    width: '10%',
  },
  {
    title: '申请时间',
    dataIndex: '申请时间',
    width: '10%',
  },
  {
    title: '送水时间',
    dataIndex: '送水时间',
    width: '10%',
  },
  {
    title: '联系电话',
    dataIndex: '联系电话',
    width: '10%',
  },
  {
    title: '类型及数量',
    dataIndex: '类型及数量',
    children: [
      {
        title: '5加仑',
        dataIndex: '5加仑',
        width: '10%',
      },
      {
        title: '330ml瓶装水',
        dataIndex: '330ml瓶装水',
        width: '10%',
      },
      {
        title: '2加仑',
        dataIndex: '2加仑',
        width: '10%',
      },
    ],
  },
  {
    title: '申请送水金额',
    dataIndex: '申请送水金额',
    width: '10%',
  },
  {
    title: '订单状态',
    dataIndex: '订单状态',
    width: '10%',
  },
  {
    title: '支付状态',
    dataIndex: '支付状态',
    width: '10%',
  },
  {
    title: '备注',
    dataIndex: '备注',
    width: '10%',
  },
  {
    title: '操作',
    dataIndex: 'operator',
    width: '15%',
    fixed: 'right',
  },
];

const { data, run: userApiRun, loading, current, pageSize } = usePagination(userApi.list);

type OrderFormData = {
  applicationForm: string; // 申请单号
  orderDown: string; // 申请单位
  orderPrint: string; // 结算公司
  orderState: string | null; // 订单状态
  payState: string | null; // paymentStatus
};

const formData = ref<OrderFormData>({
  applicationForm: '', // 申请单号
  orderDown: '', // 申请单位
  orderPrint: '', // 结算公司
  orderState: null, // 订单状态
  payState: null, // paymentStatus
});

const dataSource = computed(() => data.value?.records || []);
const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  userApiRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
    ...formData.value,
  });
};

const handleTableExport = () => {
  message.info('暂无接口');
  console.log(formData.value, ' -- formData');
};

const handleTableReset = () => {
  formData.value = {
    applicationForm: '',
    orderDown: '',
    orderPrint: '',
    orderState: null,
    payState: null,
  };
  // handleTableChange({ current: 1, pageSize: 10 });
};

/**
 * @弹窗相关
 * */
const addModalVisible = ref(false);
const addModalRecord = ref<any>(null);

const handleOpenAddModal = () => {
  addModalVisible.value = true;
};
const handleCancelAddModal = () => {
  addModalVisible.value = false;
  message.info('取消');
};
const handleOkAddModal = () => {
  addModalVisible.value = false;
  message.info('确定');
};
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <!-- 弹窗 -->
    <info-modal
      v-model:visible="addModalVisible"
      :record="addModalRecord"
      @onCancel="handleCancelAddModal"
      @onOk="handleOkAddModal"
    ></info-modal>
    <!-- 页面主体 -->
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px" :gutter="[0, 10]">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="applicationForm">送水单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="applicationForm"
              v-model:value="formData.applicationForm"
              placeholder="请输入"
              autocomplete="off"
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="orderDown">类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              id="payState"
              v-model="formData.payState"
              placeholder="请选择"
              autocomplete="off"
              style="width: 100%"
            >
              <h-select-option value="10">预算用户</h-select-option>
              <h-select-option value="20">VIP</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="orderPrint">申请单位：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="orderPrint" v-model:value="formData.orderPrint" placeholder="请输入" autocomplete="off" />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="orderPrint">送水日期：</label>
          </h-col>
          <h-col :span="4">
            <h-date-picker
              style="width: 100%"
              id="orderPrint"
              v-model:value="formData.orderPrint"
              placeholder="请输入"
              value-format="YYYY-MM-DD"
              autocomplete="off"
            />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="orderState">订单状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              id="orderState"
              v-model="formData.orderState"
              placeholder="请选择"
              autocomplete="off"
              style="width: 100%"
            >
              <h-select-option value="10">待付款</h-select-option>
              <h-select-option value="20">待配送</h-select-option>
              <h-select-option value="30">已配送</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="payState">支付状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              id="payState"
              v-model="formData.payState"
              placeholder="请选择"
              autocomplete="off"
              style="width: 100%"
            >
              <h-select-option value="10">待支付</h-select-option>
              <h-select-option value="20">已支付</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined /> 查询
            </h-button>
            <h-button type="primary" @click="handleOpenAddModal()" style="margin-left: 10px">
              <PlusOutlined /> 新增
            </h-button>
            <h-button style="margin-left: 10px" @click="handleTableExport"> <DownloadOutlined /> 导出 </h-button>

            <!-- <h-button style="margin-left: 10px" @click="handleTableReset">重置</h-button> -->
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          size="middle"
          :columns="columns"
          :scroll="{ x: 2000 }"
          :row-key="(record) => record.id"
          :data-source="dataSource"
          :pagination="pagination"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'enterprise' && record.enterpriseCode">
              {{ record.enterpriseName }}({{ record.enterpriseCode }})
            </template>
            <template v-if="column.dataIndex === 'state'">
              {{ record._stateName }}
            </template>
            <template v-if="column.dataIndex === 'operator'">
              <h-button type="link"> 修改 </h-button>
              <h-button type="link"> 转库存 </h-button>
              <h-button type="link"> 撤销 </h-button>
              <h-button type="link"> 支付 </h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}
</style>
