<script setup lang="ts">
import { computed, onMounted, PropType, reactive, ref, watch, h } from 'vue';
import {
  Table as hTable,
  Divider as hDivider,
  Select as hSelect,
  SelectOption as hSelectOption,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  RadioButton as hRadioButton,
  Modal as hModal,
  Input as hInput,
  InputSearch as hInputSearch,
  Loading as hLoading,
  Space as hSpace,
  Button as hButton,
  Row as hRow,
  Col as hCol,
  Tabs as hTabs,
  TabPane as hTabPane,
  Image as hImage,
  Tooltip as hTooltip,
  message,
  TableProps,
  InputGroup as hInputGroup,
} from 'ant-design-vue';
import { SearchOutlined, SwapOutlined } from '@ant-design/icons-vue';
import { IPayData } from '@haierbusiness-front/common-libs/src/pay/model/basicModel';
import { budgetHaierPayApi } from '@haierbusiness-front/apis/src/pay/budgetHaierPay';
import {
  HaierBudgetSourceConstant,
  IUserInfo,
  IUserListRequest,
  PaySourceConstant,
  QueryBudgetInfoRes,
  IAbCodeResponse,
  IBudgetHaierHBC2Response,
  IAbCodeDeptResponse
} from '@haierbusiness-front/common-libs';
import { DataType, usePagination, useRequest } from 'vue-request';
import { userApi, organizationCenterApi } from '@haierbusiness-front/apis';
import { Key, TablePaginationConfig } from 'ant-design-vue/lib/table/interface';
import { isMobile } from '@haierbusiness-front/utils';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue'

const props = defineProps<{ budgetType?: string; param?: IPayData }>();

const emit = defineEmits<{
  (e: 'payComplete', isPayComplete: boolean): void;
}>();

// 选人组件
const userName = ref()
const nickName = ref()
// 预算人
const budgeterCode = ref()
const budgeterName = ref()

// 费用项目
const feeItemName = ref();
const feeItem = ref();


// 本月可用预算
const leftAmt = ref()

// 部门
const companyName = ref()
const companyCode = ref()

const unitCode = ref()
const unitName = ref()


// 预算部门
const budgetDepartmentName = ref()
const budgetDepartmentCode = ref()


const clear = () => {
  feeItemName.value = ''
  feeItem.value = ''
  companyName.value = ''
  companyCode.value = ''
  leftAmt.value = ''
  budgetDepartmentName.value = ''
  budgetDepartmentCode.value = ''
}
// 用户选择
const params = ref<IUserListRequest>({
  enterpriseCode: 'haier',
  pageNum: 1,
  pageSize: 20
})


const userNameChange = (userInfo: IUserInfo | undefined) => {
  clear()
  if (!userInfo) {
    nickName.value = ''
    userName.value = ''
    budgeterCode.value = ''
    budgeterName.value = ''
    return
  }
  userName.value = userInfo.username
  nickName.value = userInfo.nickName
  budgeterCode.value = userInfo.username
  budgeterName.value = userInfo.nickName

  const params = {
    applicationCode: props.param?.applicationCode,
    haierBudgetType: props.budgetType,
    businessType: props.param?.businessType,
    budgeterCode: budgeterCode.value,
    feeItem: '6666020101'
  }

  budgetHaierPayApi
    .searchBudget(params).then(res => {
      feeItemName.value = res.feeItemName
      feeItem.value = res.feeItem
      companyName.value = res.companyName
      companyCode.value = res.companyCode
      unitCode.value = res.unitCode
      unitName.value = res.unitName
      leftAmt.value = res.leftAmt
      budgetDepartmentName.value = res.entityName
      budgetDepartmentCode.value = res.entityCode
    }).catch(err => {
    })
}
const payLoading = ref<boolean>(false)
const payComplete = () => {
  emit('payComplete', true);
};
const pay = () => {
  if (!budgeterCode.value) {
    message.error('请先选择预算人!');
    return;
  }
  if (!leftAmt.value) {
    message.error('预算不足,请重新选择预算人!');
    return;
  }
  if (!feeItemName.value) {
    message.error('暂无费用项目,请重新选择预算人!');
    return;
  }
  payLoading.value= true
  const params = {
    applicationCode: props.param?.applicationCode,
    haierBudgetType: props.budgetType,
    businessType: props.param?.businessType,
    budgeterCode: budgeterCode.value,
    budgeterName: budgeterName.value,

    unitCode: unitCode.value,
    unitName: unitName.value,
    accountCompanyName: companyName.value,

    accountCompanyCode: companyCode.value,
    budgetDepartmentCode: budgetDepartmentCode.value,
    feeItem: feeItem.value,
    amount: Number(props.param?.amount),
    orderCode: props.param?.orderCode,
    providerCode: props.param?.providerCode,
    startApproveFlag: props.param?.startApproveFlag,

    payTypes: props.param?.payTypes,
    username: props.param?.username,
    orderDetailsUrl: props.param?.orderDetailsUrl,
    notifyUrl: props.param?.notifyUrl,
    callbackUrl: props.param?.callbackUrl,
    description: props.param?.description,
    payload: props.param?.payload,
    processId: props.param?.processId,
    enterpriseCode: props.param?.enterpriseCode,

    feeItemCode: props.param?.feeItemCode,
    feeItemName: feeItemName.value,

  }
  const headers = {
    applicationCode: props.param?.applicationCode,
    excludes:
      'paySource,haierBudgetType,budgeterCode,budgeterName,budgetDepartmentCode,budgetDepartmentName,accountCompanyCode,accountCompanyName,unitCode,unitName,projectCode,feeItem,feeItemName,financialCode,financialName,budgetManager,isQueryDept,accountCode,performCode,performName,costCenter,costCenterName,budgetSystemCode,paymentMethod,username,payload,startApproveFlag,feeItemCode,feeItemName',
    nonce: props.param?.hbNonce,
    timestamp: props.param?.hbTimestamp,
    sign: props.param?.sign,
  }
  budgetHaierPayApi
    .occupyBudget(
      params,
      headers
    ).then(res => {
      payLoading.value= false

      payComplete();
    }).catch(err => {
      payLoading.value= false
    })
}




</script>
<template>

  <h-row :align="'middle'">

    <h-col span="2" class="line-top" style="font-size: 12px"> 预算人： </h-col>
    <h-col span="10" class="line-top" style="text-align: left">
      <h-row>
        <h-col span="24">
          <div class="row">
            <user-select class="budget-input" :value="nickName" :params="params" :size="'large'"
              @change="(userInfo: IUserInfo | undefined) => userNameChange(userInfo)" />

          </div>
        </h-col>

      </h-row>
    </h-col>

    <h-col span="2" class="line-top" style="font-size: 12px"> 费用项目： </h-col>
    <h-col span="10" class="line-top" style="text-align: left">
      <h-input v-model:value="feeItemName" :disabled="true" placeholder="" :size="'large'"
        :title="feeItemName" class="budget-input-readonly" style="width: 100%;" />
    </h-col>


    <h-col span="2" class="line-top" style="font-size: 12px"> 预算部门： </h-col>
    <h-col span="10" class="line-top" style="text-align: left">
      <div class="row">
        <h-input :disabled="true" v-model:value="budgetDepartmentName" placeholder="" :size="'large'"
          class="budget-input-readonly" style="width: 100%;" />
      </div>
    </h-col>
    <h-col span="2" class="line-top" style="font-size: 12px"> 本月可用预算： </h-col>
    <h-col span="10" class="line-top" style="text-align: left">
      <h-input v-model:value="leftAmt" :size="'large'" class="budget-input-readonly" :disabled="true"
        style="width:100%;" />
    </h-col>


    <h-col span="2" class="line-top" style="font-size: 12px"> 结算单位： </h-col>
    <h-col span="10" class="line-top" style="text-align: left">
      <h-input v-model:value="companyName" :size="'large'" :title="companyName" class="budget-input-readonly"
        :disabled="true" style="width:100%;" />
    </h-col>



  </h-row>
  <h-row>
    <h-divider></h-divider>
  </h-row>
  <h-row style="line-height: 14vh" :align="'middle'">
    <h-col span="2" offset="11">
      <h-button type="primary" style="width: 100%" @click="pay" :loading="payLoading" size="large">&nbsp;提&nbsp;&nbsp;交
        &nbsp;
      </h-button>
    </h-col>
  </h-row>
</template>

<style scoped lang="less">
.row {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.line-top {
  margin-top: 4vh;
}

.budget-input {
  width: 12vw;
}

.budget-input-readonly {
  width: 12vw;
  background-color: rgb(245, 245, 245);
  color: #2e2e2e;
}
</style>
