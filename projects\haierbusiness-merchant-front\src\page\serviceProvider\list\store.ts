import { defineStore } from "pinia"
import { enterpriseApi, tagApi } from '@haierbusiness-front/apis';
import { serviceProviderApi } from '@haierbusiness-front/apis/src/merchant/serviceProvider';

import { MerchantBusiness, IEnterprise, ITag, BusinessTypeEnums, SetTag } from '@haierbusiness-front/common-libs';
import { message } from "ant-design-vue";

interface State {
  searchValues: {
    name: string,
  },
  businessDetail: MerchantBusiness,
  businessList: MerchantBusiness[],
  businessListTotal: number,
  enterpriseList: IEnterprise[],
  businessTypeList: BusinessTypeEnums[]
  tagList: ITag[],
  setTagList: SetTag[],
}
export const defaultBusinessDetail = {
  name: '',
  code: '',
  enterpriseCode: undefined,
  unifiedSocialCreditCode: '',
}
export const useServiceProviderListStore = defineStore('serviceProviderList', {
  state: (): State => ({
    searchValues: {
      name: '',
    },
    businessDetail: defaultBusinessDetail,
    businessList: [],
    businessListTotal: 0,
    enterpriseList: [],
    businessTypeList: [],
    tagList: [],
    setTagList: [],
  }),
  getters: {
    businessTypeOptions: state => state.businessTypeList.map(item => ({ label: item.value, value: item.key }))
  },
  actions: {
    async getBusinessList(params = {}) {
      try {
        const res = await serviceProviderApi.getBusinessList(params);
        this.businessList = res.records;
        this.businessListTotal = res.total;
      } catch (e) {
        console.log(e)
      }
    },
    async getBusinessDetail(params = {}) {
      try {
        const res = await serviceProviderApi.getBusinessDetail(params);
        this.businessDetail = res;
      } catch (e) {
        console.log(e)
      }
    },
    async getEnterpriseList(params = {}) {
      try {
        const res = await enterpriseApi.list(params);
        this.enterpriseList = res;
      } catch (e) {
        console.log(e)
      }
    },
    async addBusiness(params = {}) {
      try {
        const res = await serviceProviderApi.addBusiness(params);
        message.success('新增成功');
      } catch (e) {
        console.log(e)
      }
    },
    async editBusiness(params = {}) {
      try {
        const res = await serviceProviderApi.editBusiness(params);
        message.success('编辑成功');
      } catch (e) {
        console.log(e)
      }
    },
    async getBusinessTypeList(params = {}) {
      try {
        const res = await tagApi.getBusinessTypeList(params);
        this.businessTypeList = res;
      } catch (e) {
        console.log(e)
      }
    },
    async getTagList(params = {}) {
      try {
        const res = await tagApi.page(params);
        this.tagList = res.records!;
      } catch (e) {
        console.log(e)
      }
    },
    async getSetTagList(params = {}) {
      try {
        const res = await tagApi.getSetTagList(params);
        this.setTagList = res;
      } catch (e) {
        console.log(e)
      }
    },
    async setTag(params = {}) {
      try {
        const res = await serviceProviderApi.setTag(params);
        console.log(params);
        if(params.isSetTag == 1){
          message.success('配置成功');
        }else{
          message.success('删除成功');
        }
        
      } catch (e) {
        console.log(e)
      }
    },
  },
})