import { defineStore } from "pinia"
import { cityApi, portalApi, productLineApi } from '@haierbusiness-front/apis';
import { MiceBidCreateOrderParams, MiceBidConsultant, MiceBidCalendar } from '@haierbusiness-front/common-libs';
import { Dayjs } from "dayjs";

interface State {
  calendarDrawerOpen: boolean,
  consultantModalOpen: boolean,
  instructionModalOpen: boolean,
  areaList: { id: string, name: string }[],
  consultantList: MiceBidConsultant[],
  calendarList: MiceBidCalendar[],
  meetingInfo: {
    miceName: string,
    meetingDate: [Dayjs?, Dayjs?],
    demandType: boolean,
    districtType: number | null,
    personTotal: number | null,
    miceType: number | null,
    intentionConsultantUserCode?: string,
  },
}
export const usePortalStore = defineStore('portal', {
  state: (): State => ({
    calendarDrawerOpen: false,
    consultantModalOpen: false,
    instructionModalOpen: false,
    areaList: [],
    consultantList: [],
    calendarList: [],
    meetingInfo: {
      miceName: '',
      meetingDate: [],
      demandType: false,
      districtType: null,
      personTotal: null,
      miceType: null,
      intentionConsultantUserCode: void 0,
    },
  }),
  actions: {
    async getCityList() {
      const params = {
        id: 1,
        providerCode: 'MT',
        subdistrict: 1
      };
      try {
        const res = await cityApi.getCityTree(params);
        this.areaList = res.children
      } catch (e) {
        console.log(e)
      }
    },
    async createOrder(data: MiceBidCreateOrderParams) {
      try {
        const res = await portalApi.createOrder(data);
        return res
      } catch (e) {
        console.log(e)
      }
    },
    async getCounsellorList(params = {}) {
      try {
        const res = await productLineApi.queryByProcessId(params);
        this.consultantList = res?.counsellorList!
      } catch (e) {
        console.log(e)
      }
    },
    async getCalendarList(params = {}) {
      try {
        const res = await portalApi.getCalendarList(params);
        this.calendarList = res.records!
      } catch (e) {
        console.log(e)
      }
    },
  },
})