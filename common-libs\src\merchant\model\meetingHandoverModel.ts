import { IPageRequest } from "../../basic";

export class IMeetingHandoverFilter extends IPageRequest {
    begin?: string
    end?: string
    handoverCode?: string
    mainCode?: string

}


export class IMeetingHandover {
    id?: number | null
    creator?: string
    createTime?: string
    updater?: string
    updateTime?: string
    mainCode?: string
    miceName?: string
    startDate?: string
    finishDate?: string
    city?: string
}