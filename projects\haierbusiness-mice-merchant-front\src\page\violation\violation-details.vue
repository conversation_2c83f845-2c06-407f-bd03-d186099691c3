<script setup lang="ts">
import {
  Modal as hModal,
  Row as hRow,
  Col as hCol,
  Button as aButton,
  Input as aInput,
  Upload as aUpload,
  Form as aForm,
  FormItem as aFormItem,
  Table as aTable,
  message,
} from 'ant-design-vue';
import { ViolationRecord } from '@haierbusiness-front/common-libs';
import { UploadOutlined } from '@ant-design/icons-vue';
import { fileApi, serviceExamApi } from '@haierbusiness-front/apis';
import { ref, reactive, computed, onMounted, h, watch } from 'vue';
import { getFileNameFromPath } from '@haierbusiness-front/utils';
import {
  ProcessForm,
  ServiceExam,
  ProcessingRecord,
  ExamineStateEnum,
  PunishmentStatusEnum,
  PunishmentStatusMap,
  FileTypeConstant,
} from '@haierbusiness-front/common-libs';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import ProcessingDetail from './processing-detail.vue';
const props = defineProps<{
  visible: boolean;
  data: ViolationRecord;
}>();
console.log(props.data, 'props.data');

const emit = defineEmits<{
  (e: 'cancel'): void;
  (e: 'handle'): void;
  (e: 'submit', data: { description: string; path: string[] }): void;
  (e: 'ok'): void;
}>();

const showProcessing = ref(false);
// 处理详情数据
const handleDetails = ref<any[]>([]);
const loading = ref(false);
const pageSize = ref(10);
const current = ref(1);
const total = ref(0);

const handleCancel = () => {
  emit('cancel');
  showProcessing.value = false;
  // 清空表单数据
  processForm.description = '';
  processForm.path = [];
};

// 修改v-model指令和添加提交loading状态
const submitLoading = ref<boolean>(false);

const handleSubmit = () => {
  // 表单校验
  if (!processForm.description) {
    message.warning('请输入处理说明');
    return;
  }

  if (!processForm.path || processForm.path.length === 0) {
    message.warning('请上传付款凭证');
    return;
  }

  // 调用保存接口
  submitLoading.value = true;
  const serviceExamData: ServiceExam = {
    mdrId: props.data.id,
    illustrate: processForm.description,
    path: processForm.path,
    pathType: FileTypeConstant.PAY_PROVE.code,
  };

  serviceExamApi
    .saveService(serviceExamData)
    .then((response) => {
      message.success('提交成功');
      emit('submit', {
        description: processForm.description,
        path: processForm.path,
      });
      showProcessing.value = false;
      emit('cancel'); // 关闭弹框
      emit('ok'); // 触发ok事件以刷新父组件数据
    })
    .catch((error) => {
      console.error('保存失败:', error);
      message.error('提交失败，请重试');
    })
    .finally(() => {
      submitLoading.value = false;
    });
};

// @ts-ignore
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const uploadLoading = ref<boolean>(false);
const processForm = reactive<ProcessForm>({
  description: '',
  path: [],
});

const uploadRequest = (options: any) => {
  uploadLoading.value = true;
  submitLoading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      processForm.path.push(baseUrl + it.path);
      options.onProgress(100);
      options.onSuccess(it, options.file);
    })
    .finally(() => {
      uploadLoading.value = false;
      submitLoading.value = false;
    });
};
const processingDetailVisible = ref(false);
const currentProcessingRecord = ref<ProcessingRecord | null>(null);
const handleProcessingDetailClose = () => {
  processingDetailVisible.value = false;
  currentProcessingRecord.value = null;
};
const showProcessingDetail = async (record: ProcessingRecord) => {
  try {
    const details = await serviceExamApi.getProcessingDetail(record.id);
    if (details) {
      const processingDetails: ProcessingRecord = {
        id: details.id || 0,
        operateType: (details as ProcessingRecord).operateType || 0,
        illustrate: (details as ProcessingRecord).illustrate || '',
        applyTime: (details as ProcessingRecord).applyTime || '',
        result: (details as ProcessingRecord).result || 0,
        disposeIdea: (details as ProcessingRecord).disposeIdea || '',
        disposeTime: (details as ProcessingRecord).disposeTime || '',
        path: (details as ProcessingRecord).path || [],
      };
      currentProcessingRecord.value = processingDetails;
      processingDetailVisible.value = true;
    }
  } catch (error) {
    console.error('Failed to fetch processing details:', error);
  }
};
// 获取处理详情列表
const fetchProcessingList = () => {
  if (!props.data || !props.data.id) return;

  loading.value = true;
  serviceExamApi
    .listProcessing({
      // @ts-ignore - 接口定义可能不完整
      pageNum: current.value,
      pageSize: pageSize.value,
      id: props.data.id,
    })
    .then((res) => {
      if (res && res.records) {
        console.log(res, 'res');

        total.value = res.total || 0;
        handleDetails.value = res.records;
      }
    })
    .catch((error) => {
      console.error('获取处理详情失败:', error);
    })
    .finally(() => {
      loading.value = false;
    });
};

// 分页变化
const handleTableChange = (pag: any) => {
  if (pag.current) {
    current.value = pag.current;
  }
  if (pag.pageSize) {
    pageSize.value = pag.pageSize;
  }
  fetchProcessingList();
};

// 监听详情页面变化
onMounted(() => {
  console.log(props, 'props.visible');

  if (props.visible && props.data && (props.data.state === 30 || props.data.state === 40)) {
    fetchProcessingList();
  }
});

// 监听弹框显示状态变化
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible && props.data && (props.data.state === 30 || props.data.state === 40)) {
      fetchProcessingList();
    }
  },
);

const columns: ColumnType<ProcessingRecord>[] = [
  {
    title: '序号',
    dataIndex: 'id',
    width: 80,
    align: 'center',
  },
  {
    title: '操作类型',
    dataIndex: 'operateType',
    width: 120,
    align: 'center',
    customRender: ({ text }) => {
      return text === 10 ? '违规处理' : text;
    },
  },
  {
    title: '说明',
    dataIndex: 'illustrate',
    width: 200,
    align: 'center',
  },
  {
    title: '申请时间',
    dataIndex: 'applyTime',
    width: 160,
    align: 'center',
  },
  {
    title: '结果',
    dataIndex: 'result',
    width: 120,
    align: 'center',
    customRender: ({ text }) => {
      if (text === 1) return '通过';
      if (text === 2) return '驳回';
      return text;
    },
  },
  {
    title: '处理意见',
    dataIndex: 'disposeIdea',
    width: 200,
    align: 'center',
  },
  {
    title: '处理时间',
    dataIndex: 'disposeTime',
    width: 160,
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 100,
    fixed: 'right',
    align: 'center',
    customRender: ({ record }: { record: ProcessingRecord }) => {
      return h(
        aButton,
        {
          type: 'link',
          onClick: () => showProcessingDetail(record),
        },
        '查看',
      );
    },
  },
];

// 计算属性，根据state确定显示状态
const showButtonText = computed(() => {
  // 所有状态都只显示"提交"按钮
  return '提交';
});

const shouldShowProcessing = computed(() => {
  // 修改逻辑：state为10、40或50时直接显示处理表单，不需要点击马上处理按钮
  return props.data?.state === 50 || props.data?.state === 10 || props.data?.state === 40;
});

const shouldShowHandleDetails = computed(() => {
  // examineState为20时显示处理详情表格
  return props.data?.state === 30 || props.data?.state === 40;
});

const shouldShowFooter = computed(() => {
  // examineState为10或40时显示底部按钮
  return props.data?.state === 10 || props.data?.state === 40;
});

// 生成见证性材料文件名
const getWitnessFileName = (filePath: string, index: number) => {
  // 获取文件扩展名
  const fileExtension = filePath.split('.').pop() || 'file';
  // 生成文件名：见证性材料 + 序号 + 扩展名
  return `见证性材料${index + 1}.${fileExtension}`;
};



// 计算属性定义分页对象
const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: total.value,
  current: current.value,
  pageSize: pageSize.value,
  style: { justifyContent: 'center' },
}));
</script>

<template>
  <div class="violation-details-container">
    <h-modal :visible="visible" title="违规详情" @cancel="handleCancel" width="800px" :footer="null">
      <div class="violation-details" v-if="data">
        <h2 class="title">海尔会务会展系统关于{{ data?.title }}未提报方案违规通知</h2>
        <h-row class="info-row">
          <h-col :span="8">
            <span class="label">违规单号：</span>
            <span class="value">{{ data?.examineCode }}</span>
          </h-col>
          <h-col :span="8">
            <span class="label">关联单号：</span>
            <span class="value">{{ data?.mainCode }}</span>
          </h-col>
          <h-col :span="8">
            <span class="label">通知时间：</span>
            <span class="value">{{ data?.gmtCreate }}</span>
          </h-col>
        </h-row>
        <h-row class="info-row">
          <h-col :span="8">
            <span class="label">处理状态：</span>
            <span class="value">{{ data?.state ? PunishmentStatusMap[data.state as PunishmentStatusEnum] : '-' }}</span>
          </h-col>
          <h-col :span="8">
            <span class="label">处理截止时间：</span>
            <span class="value">{{ data?.violationDisposeEndTime }}</span>
          </h-col>
        </h-row>

        <div class="violation-details-section">
          <h3 class="section-title">违规细节</h3>
          <div class="detail-item">
            <span class="label">考核条目名称：</span>
            <span class="value">{{ data?.entry }}</span>
          </div>
          <div class="detail-item">
            <span class="label">考核条目细节：</span>
            <span class="value">{{ data?.details }}</span>
          </div>
          <div class="detail-item">
            <span class="label">违规类型：</span>
            <span class="value">{{ data?.type === 1 ? '违规' : data?.type === 2 ? '整改' : '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">违规措施：</span>
            <span class="value">
              {{
                data?.score > 0 ? `加${Math.abs(data.score)}分` : data?.score < 0 ? `扣${Math.abs(data.score)}分` : ''
              }}
              {{ data?.score && data?.fine ? '，' : '' }}
              {{
                data?.fine > 0 ? `奖励${Math.abs(data.fine)}元` : data?.fine < 0 ? `罚款${Math.abs(data.fine)}元` : ''
              }}
              {{ !data?.score && !data?.fine ? '-' : '' }}
            </span>
          </div>
        </div>

        <div class="violation-details-section">
          <h3 class="section-title">违规描述</h3>
          <div class="detail-item">
            <span class="label">描述：</span>
            <span class="value">{{ data?.violationDesc }}</span>
          </div>
          <div class="detail-item">
            <span class="label">违规时间：</span>
            <span class="value">{{ data?.violationTime }}</span>
          </div>
          <div class="detail-item">
            <span class="label">见证性材料：</span>
            <span class="value">
              <template v-if="data?.path && data.path.length > 0">
                <a
                  v-for="(file, index) in data.path"
                  :key="index"
                  :href="file"
                  target="_blank"
                  :download="getWitnessFileName(file, index)"
                  class="file-link"
                >
                  {{ getFileNameFromPath(file) }}
                  <span v-if="index < data.path.length - 1">, </span>
                </a>
              </template>
              <span v-else>-</span>
            </span>
          </div>
        </div>
      </div>

      <!-- 处理详情表格，当examineState为20时显示 -->
      <div class="violation-handling" v-if="shouldShowHandleDetails">
        <h3 class="section-title">处理详情</h3>
        <a-table
          :columns="columns"
          :data-source="handleDetails"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          :scroll="{ y: 550 }"
          size="small"
        />
      </div>
      <!-- 违规处理表单，当examineState为50或examineState为10且点击了马上处理时显示 -->
      <div class="violation-handling" v-if="shouldShowProcessing">
        <h3 class="section-title">违规处理</h3>
        <a-form :model="processForm" :label-col="{ span: 2 }" :wrapper-col="{ span: 20 }">
          <a-form-item label="说明">
            <aInput.TextArea v-model:value="processForm.description" :rows="4" placeholder="请输入处理说明" />
          </a-form-item>
          <a-form-item label="付款凭证">
            <a-upload
              v-model="processForm.path"
              :custom-request="uploadRequest"
              :max-count="1"
              accept=".pdf, .doc, .docx, .jpg, .png, .jpeg"
            >
              <a-button :loading="uploadLoading">
                <upload-outlined></upload-outlined>
                上传
              </a-button>
            </a-upload>
          </a-form-item>
        </a-form>
      </div>
      <!-- 底部按钮，根据条件显示 -->
      <div class="footer" v-if="shouldShowFooter">
        <a-button type="primary" @click="handleSubmit()" :loading="submitLoading">
          {{ showButtonText }}
        </a-button>
      </div>
    </h-modal>

    <processing-detail
      :visible="processingDetailVisible"
      :data="currentProcessingRecord"
      @cancel="handleProcessingDetailClose"
    />
  </div>
</template>

<style scoped lang="less">
.violation-details-container {
  background-color: #ffff;
  width: 100%;
  padding: 10px 10px 0px 10px;
  overflow: auto;
}

.violation-details {
  .title {
    text-align: center;
    font-weight: bold;
    margin-bottom: 24px;
  }

  .info-row {
    margin-bottom: 16px;
  }

  .label {
    font-weight: bold;
    margin-right: 8px;
  }

  .value {
    color: #666;
  }

  .violation-details-section {
    margin-top: 24px;
    border-top: 1px solid #f0f0f0;
    padding-top: 16px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 16px;
    }

    .detail-item {
      margin-bottom: 12px;
      line-height: 1.5;
    }
  }

  .file-link {
    color: #1890ff;
    text-decoration: none;
    margin-right: 4px;
  }
}

.footer {
  text-align: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.violation-handling {
  margin-top: 24px;
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 16px;
    text-align: left;
  }

  :deep(.ant-form-item) {
    margin-bottom: 24px;
  }

  :deep(.ant-form-item-label) {
    text-align: left;
    padding-right: 8px;
  }

  :deep(.ant-form-item-control) {
    text-align: left;
  }

  :deep(.ant-input) {
    width: 100%;
  }

  :deep(.ant-upload) {
    width: 100%;
  }
}
</style>
