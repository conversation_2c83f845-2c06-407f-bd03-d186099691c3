<template>
    <div :style="{ height: height + 'vh' }" background="rgba(0,0,0,0)">
        <bar-line from="ChangeAndRefund" :height="height" v-if="loaded" :legend="legend" :x-axis="xAxis" :y-axis="yAxis"
            :series="series" :tooltip="tooltip" />
    </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted } from "vue";

import BarLine from "../../../components/barLine.vue";
import { queryTrainChangeAndRefund } from "@haierbusiness-front/apis/src/data/board/travel";
import { EventBus } from "../../../eventBus";
const props = defineProps({
    dateType: Number,
    gngj: {
        type: [String, Number],
        default: "1",
    },
    height: {
        type: Number,
        default: 30,
    },
});
const loaded = ref(false);
const loading = ref(false);
const legend: any = [];
const xAxis = ref([]);
const yAxis = [
    {
        type: "value",
        name: "元",
        min: 0,
    },
    {
        type: "value",
        name: "百分比",
        min: 0,
        max: 100,
    },
];
const tooltip = {
    trigger: "axis",
    formatter:
        "{b}<br />{a0}：{c0}<br />{a1}：{c1}<br />{a2}：{c2}%<br />{a3}：{c3}%",
};
const series = ref([]);
onMounted(() => {
    queryData();
});

EventBus.on((event, params) => {
    console.log(params);
    if (event == "refresh") {
        if (!params) queryData();
        if (params && params.from != "date") queryData(params);
    }
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
    // console.log("refresh");
    loading.value = true;
    const data = await queryTrainChangeAndRefund(
        params ? params.data.name : null,
        params ? params.from : null
    );
    data.columns.forEach((item, index) => {
        if ((index = 0)) return;
        legend.push(item.name[0]);
    });
    loading.value = false;
    const barData1: any = [];
    const barData2: any = [];
    const lineData1: any = [];
    const lineData2: any = [];
    const xData: any = [];
    data.rows.forEach((item, index) => {
        xData.push(item[0]);
        barData1.push(item[1] || 0);
        barData2.push(item[2] || 0);
        // barData1.push((item[1]||0)/10000);
        // barData2.push((item[2]||0)/10000);
        const item3 = (item[3] || 0) * 100;
        const item4 = (item[4] || 0) * 100;
        lineData1.push(item3.toFixed(2));
        lineData2.push(item4.toFixed(2));
    });
    xAxis.value = xData;
    series.value = [
        {
            name: "改签费",
            type: "bar",
            data: barData1,
        },
        {
            name: "退票费",
            type: "bar",
            data: barData2,
        },
        {
            name: "改期率",
            type: "line",
            yAxisIndex: 1,
            smooth: true,
            symbol: "none",
            data: lineData1,
        },
        {
            name: "退票率",
            type: "line",
            yAxisIndex: 1,
            smooth: true,
            symbol: "none",
            data: lineData2,
        },
    ] as any;
    loaded.value = true;
};
watch(() => props.dateType, queryData);
</script>
