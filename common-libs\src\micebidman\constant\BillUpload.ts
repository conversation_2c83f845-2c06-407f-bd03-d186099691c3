// 导入 UploadFile 类型定义
import { UploadFile } from '../../micebid';


// 酒店合同附件相关类型定义
export interface HotelItem {
  id: string;
  hotelName: string;
  contractFiles: UploadFile[];
}

// 文件上传相关类型
export interface FileUploadInfo {
  hotelId: string;
  hotelName: string;
  file: UploadFile;
}

// 账单附件组件 Props 类型
export interface BillAttachmentProps {
  orderId?: string;
}

// 账单附件组件 Emits 类型
export interface BillAttachmentEmits {
  'update:fileList': [files: FileUploadInfo[]];
}

// 发票项目接口
export interface InvoiceItem {
  id: string;
  sequenceNumber: number;
  attachmentName: string;
  attachmentFile?: UploadFile;
  localCurrency: number | null;
  exchangeRate: number | null;
  waterBillAmount: number;
  relatedAmountTotal: string;
  relatedBill: string;
  exchangeRateScreenshot?: UploadFile; // 汇率截图
}

// 水单项目接口
export interface WaterBillItem {
  id: string;
  sequenceNumber: number;
  attachmentName: string;
  attachmentFile?: UploadFile;
  localCurrency: number | null;
  exchangeRate: number | null;
  waterBillAmount: number;
  relatedAmountTotal: string;
  relatedBill: string;
  exchangeRateScreenshot?: UploadFile; // 汇率截图
}

// 住宿详单项目接口
export interface AccommodationDetailItem {
  id: string;
  sequenceNumber: number;
  attachmentName: string;
  attachmentFile?: UploadFile;
  date: string;
  checkInCount: number;
  detailCount: number;
  verificationResult: string;
  operation: string;
}

// 会议照片项目接口
export interface MeetingPhotoItem {
  id: string;
  sequenceNumber: number;
  attachmentName: string;
  attachmentFile?: UploadFile;
  type: string;
  relatedAccount: string;
  operation: string;
}

// 其他附件项目接口
export interface OtherAttachmentItem {
  id: string;
  sequenceNumber: number;
  attachmentName: string;
  attachmentFile?: UploadFile;
  description: string;
  relatedAccount: string;
}

// 支持的文件类型
export const SUPPORTED_FILE_TYPES = [
  'application/pdf',
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/msword'
] as const;

// 文件大小限制 (MB)
export const FILE_SIZE_LIMIT = 10;

// 文件上传接受的格式
export const UPLOAD_ACCEPT = '.pdf,.jpg,.jpeg,.png,.doc,.docx';
