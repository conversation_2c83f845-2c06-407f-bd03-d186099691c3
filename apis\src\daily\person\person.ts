import {get, post} from '../../request'
import {
    IDailyPersonalDeleteRequestDTO, IDailyPersonalListRequestDTO, IDailyPersonalResponse,
    IDailyPersonalSaveRequestDTO,
    IDailyPersonalUpdateRequestDTO,
    IPageResponse
} from "@haierbusiness-front/common-libs";


export const dailyPersonApi = {

    list: (params: IDailyPersonalListRequestDTO): Promise<IPageResponse<IDailyPersonalResponse>> => {
        return get('/daily/api/daily-person/list', params)
    },

    save: (params: IDailyPersonalSaveRequestDTO): Promise<void> => {
        return post('/daily/api/daily-person/save', params)
    },

    update: (params: IDailyPersonalUpdateRequestDTO): Promise<void> => {
        return post('/daily/api/daily-person/update', params)
    },

    delete: (params: IDailyPersonalDeleteRequestDTO): Promise<void> => {
        return post('/daily/api/daily-person/delete', params)
    },
}