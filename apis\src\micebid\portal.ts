import { Result, MiceBidCreateOrderParams, IPageResponse, MiceBidConsultant, MiceBidCalendar } from '@haierbusiness-front/common-libs'
import { get, originalPost } from '../request'


export const portalApi ={
  createOrder: (data: MiceBidCreateOrderParams): Promise<Result> => {
    return originalPost(`/mice-bid/api/mice/main/user/create`, data)
  },
  getCounsellorList: (data = {}): Promise<IPageResponse<MiceBidConsultant>> => {
    return get(`/mice-bid/api/counsellor/user/page`, data)
  },
  getCalendarList: (data = {}): Promise<IPageResponse<MiceBidCalendar>> => {
    return get(`/mice-bid/api/mice-calendar/page`, data)
  },
}