import { download, get, post } from '../request'
import { IEnterpriseListRequest, IPageResponse, IEnterprise, Result } from '@haierbusiness-front/common-libs'



export const enterpriseApi = {
    /**
     * 查询所有企业
     */
    searchAllEnterprises: (params: IEnterpriseListRequest): Promise<IPageResponse<IEnterprise>> => {
        return get('system/api/enterprise/listPage', params)
    },

    /**
     * 查询所有企业
     */
    list: (params: IEnterpriseListRequest): Promise<Array<IEnterprise>> => {
        return get('system/api/enterprise/list ', params)
    },

    save: (params: IEnterprise): Promise<Result> => {
        return post('system/api/enterprise/save', params)
    },

    edit: (params: IEnterprise): Promise<Result> => {
        return post('system/api/enterprise/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('system/api/enterprise/delete', { id })
    },
}