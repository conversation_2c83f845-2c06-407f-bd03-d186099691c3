<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0,user-scalable=no, maximum-scale=1, minimum-scale=1" />
    <title>海尔商务门户</title>
  </head>
  <body>
    <!-- <script id="robotScript" src="https://nesp.haier.net/webchatbot/static/js/entrance.js?width=1000&amp;height=650&amp;iconSrc=https://nesp.haier.net/upload/material/13/20221215/6134AF0F056B49FA9573985F25D1881D.png&amp;iwidth=65&amp;iheight=65&amp;iright=10&amp;ibottom=200&amp;openUrl=https%3A%2F%2Fnesp.haier.net%2Fwebchatbot%2FsingleSign.html%3FredirectUrl%3Dhttps%253A%252F%252Fnesp.haier.net%252Fwebchatbot%252Fchat.html%253FsysNum%253D1659675658214%2526sourceId%253D179%2526lang%253Dzh_CN"></script> -->

    <script>
          let javaScript = document.createElement('script')
        
          if(navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)) {
            // javaScript.src =
            // 'https://r.haier.net/assets/daily/dts-fe/online-popup/2.0.1/index.js'
            // document.body.appendChild(javaScript)
            // window.__Konph = {
            //   'popup-js': {
            //     id: 'helpCenterBtn',
            //     // env: 'test' , //测试
            //     env: 'prod', //线上
            //     'one-url-name': 'popup',
            //     'apply-code': 'S03274',
            //     'scene-code': '230525979889',
            //     map: encodeURI('{"帮助中心":["in","bzzx"]}'),
            //     type: 'help_center',
            //     screenshots: false,
            //     mode: 'phone',
            //     show: true,
            //     'app-id': 'cli_a4c36fae15f89013', //正式
            //     // 'app-id': 'cli_a4cbbae78be8d00c', //测试
            //     'auth-url': '',
            //     'delay-time': 3,
            //     'url-list': [],
            //   },
            // }
          } else {
            javaScript.id= 'robotScript'
            javaScript.src =
            'https://nesp.haier.net/webchatbot/static/js/entrance.js?width=1000&height=650&iconSrc=https://nesp.haier.net/upload/material/13/20221215/6134AF0F056B49FA9573985F25D1881D.png&iwidth=65&iheight=65&iright=10&ibottom=200&openUrl=https%3A%2F%2Fnesp.haier.net%2Fwebchatbot%2FsingleSign.html%3FredirectUrl%3Dhttps%253A%252F%252Fnesp.haier.net%252Fwebchatbot%252Fchat.html%253FsysNum%253D1659675658214%2526sourceId%253D179%2526lang%253Dzh_CN'
            document.body.appendChild(javaScript)
          }

      
    </script>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
  <style>
    /* @font-face {
        font-family: 'SourceHanSansCN';
        src: url('./public/font/HarmonyOS_Sans_SC_Bold.ttf');
    } */
  </style>
</html>
