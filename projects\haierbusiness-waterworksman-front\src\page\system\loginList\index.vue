<script setup lang="ts">
import { SearchOutlined, DownloadOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { userApi } from '@haierbusiness-front/apis';
import {
  Button as hButton,
  Col as hCol,
  Input as hInput,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Form as hForm,
  FormItem as hFormItem,
  Modal as hModal,
  DatePicker as hDatePicker,
  Upload as hUpload,
  Textarea as hTextarea,
  message,
} from 'ant-design-vue';
import { computed, ref } from 'vue';
import { usePagination } from 'vue-request';
import { ColumnType } from 'ant-design-vue/es/table';
type MyColumn = ColumnType & {
  children?: ColumnType[];
};

const { data, run: userApiRun, loading, current, pageSize } = usePagination(userApi.list);
const sorter = (a: any, b: any) => a.applicationForm.localeCompare(b.applicationForm);

/**
 * @表格相关
 * */

const tableProps = computed(() => ({
  rowKey: 'id',
  scroll: { x: 'max-content' },
  dataSource: data.value?.records || [],
  pagination: {
    showSizeChanger: true,
    showQuickJumper: true,
    total: data.value?.total,
    current: data.value?.pageNum,
    pageSize: data.value?.pageSize,
    style: { justifyContent: 'center' },
  },
  columns: [
    {
      title: '序号',
      dataIndex: 'index',
    },
    {
      title: '用户编码',
      dataIndex: 'id',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '用户名称',
      dataIndex: '用户名称',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '申请单位',
      dataIndex: '申请单位',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '申请时间',
      dataIndex: '申请时间',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '联系电话',
      dataIndex: '联系电话',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '合同编号',
      dataIndex: '合同编号',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '邮箱',
      dataIndex: '邮箱',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '角色',
      dataIndex: '角色',
      sorter: (a: any, b: any) => sorter(a, b),
    },
    {
      title: '状态',
      dataIndex: '状态',
      sorter: (a: any, b: any) => sorter(a, b),
    },
  ],
}));

/**
 * @表单相关
 * */
type OrderFormData = {
  applicationForm: string; // 申请单号
  orderDown: string; // 申请单位
  orderPrint: string; // 结算公司
  orderState: string | null; // 订单状态
  payState: string | null; // paymentStatus
};

// 订单状态
const orderStateOptions = ref([
  { label: '已出库', value: '10' },
  { label: '未出库', value: '20' },
]);

const formData = ref<OrderFormData>({
  applicationForm: '', // 申请单号
  orderDown: '', // 申请单位
  orderPrint: '', // 结算公司
  orderState: null, // 订单状态
  payState: null, // paymentStatus
});

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  userApiRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
    ...formData.value,
  });
};

const handleTableExport = () => {
  message.info('暂无接口');
  console.log(formData.value, ' -- formData');
};

/**
 * 弹窗相关
 * */
const addModal = ref(false);
const formModalRef = ref();
const formModalData = ref<OrderFormData>({
  applicationForm: '', // 申请单号
  orderDown: '', // 申请单位
  orderPrint: '', // 结算公司
  orderState: null, // 订单状态
  payState: null, // paymentStatus
});
const handleOpenAddModal = () => {
  addModal.value = true;
  formModalRef.value.resetFields();
};
const handleCloseAddModal = () => {
  addModal.value = false;
  formModalRef.value.resetFields();
};
const handleAddModalOk = () => {
  formModalRef.value.validateFields().then((values: any) => {
    console.log(values, ' -- values');
    addModal.value = false;
  });
};
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <!-- 新增弹窗 -->
    <h-modal v-model:visible="addModal" title="新增" @cancel="handleCloseAddModal" @ok="handleAddModalOk">
      <h-form ref="formModalRef" style="margin-top: 16px;" :model="formModalData" :label-col="{ style: { width: '110px' } }">
        <h-form-item
          label="用户名称"
          prop="applicationForm"
          :rules="[{ required: true, message: '请输入供应商编码' }]"
        >
          <h-input placeholder="请输入" v-model:value="formModalData.applicationForm" />
        </h-form-item>
        <h-form-item
          label="申请单位"
          prop="applicationForm"
          :rules="[{ required: true, message: '请输入供应商名称' }]"
        >
          <h-input placeholder="请输入" v-model:value="formModalData.applicationForm" />
        </h-form-item>
        <h-form-item label="联系电话" prop="applicationForm" :rules="[{ required: true, message: '请输入银行账号' }]">
          <h-input placeholder="请输入" v-model:value="formModalData.applicationForm" />
        </h-form-item>
        <h-form-item label="邮箱" prop="applicationForm" :rules="[{ required: true, message: '请输入开户行' }]">
          <h-input placeholder="请输入" v-model:value="formModalData.applicationForm" />
        </h-form-item>
        <h-form-item label="状态" prop="applicationForm" :rules="[{ required: true, message: '请选择状态' }]">
          <h-select placeholder="请选择" v-model:value="formModalData.applicationForm" style="width: 100%">
            <h-select-option value="10">有效</h-select-option>
            <h-select-option value="20">无效</h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="备注" prop="applicationForm">
          <h-textarea placeholder="请输入" v-model:value="formModalData.applicationForm" show-count :maxlength="100" />
        </h-form-item>
      </h-form>
    </h-modal>
    <!-- 页面主体 -->
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px" :gutter="[0, 10]">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="applicationForm">用户名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="applicationForm"
              v-model:value="formData.applicationForm"
              placeholder="请输入"
              autocomplete="off"
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="orderState">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              id="orderState"
              v-model="formData.orderState"
              placeholder="请选择"
              autocomplete="off"
              style="width: 100%"
            >
              <h-select-option value="10">有效</h-select-option>
              <h-select-option value="20">无效</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined /> 查询
            </h-button>
            <h-button type="primary" @click="handleOpenAddModal()" style="margin-left: 10px">
              <PlusOutlined /> 新增
            </h-button>
            <h-button style="margin-left: 10px" @click="handleTableExport"> <DownloadOutlined /> 导出 </h-button>
            <!-- <h-button style="margin-left: 10px" @click="handleTableReset">重置</h-button> -->
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table v-bind="tableProps" :loading="loading" size="middle" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }"> </template>
        </h-table>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}
</style>
