<script setup lang="ts">
import { loginApi } from '@haierbusiness-front/apis';
import { PropType, ref } from 'vue';

import { message } from 'ant-design-vue';
import { ILoginResult, IIamCodeLogin } from '@haierbusiness-front/common-libs';

import { isIhaier2App } from '@haierbusiness-front/utils';

const props = defineProps({
  param: Object as PropType<IIamCodeLogin>,
});

const emit = defineEmits<{
  (e: 'loginSuccess', result: ILoginResult): void;
}>();

const loginSuccess = (result: ILoginResult) => {
  emit('loginSuccess', result);
};

const loading = ref(false);
if (isIhaier2App()) {
  loading.value = true;
  (window as any).__USERCENTER__.configUserCenter({
    clientId: import.meta.env.VITE_IAM_CLIENT_ID, //账号中心cliendtId
    ssoUrl: import.meta.env.VITE_IAM_SSO_URL, //账号中心统一登录页
    appId: import.meta.env.VITE_FS_APP_ID, //开放平台创建应用获取（open.feishu.cn）
    tokenUrl: import.meta.env.VITE_TOKEN_URL, //集成了账号中心提供的后端服务的地址
  });
  (window as any).__USERCENTER__.login().then((it: any) => {
    console.log('iam token----------->>>>>', it);
    localStorage.setItem('iam_token', it?.token);

    loginApi
      .haierIamTokenLogin({
        token: it.token,
      })
      .then((hitl) => {
        loginSuccess({ data: hitl });
      })
      .finally(() => {
        loading.value = false;
      });
  }).catch((iamError: any) => {
    message.error(iamError ?? '账号中心登录报错！');
  });
} else {
  (() => {
    const code = props.param?.urlSearch.get('code');
    const applicationCode = props.param?.urlSearch.get('application_code');
    const iamNotify = props.param?.urlSearch.get('iam_notify');
    if (!applicationCode) {
      message.error('applicationCode不能为空!');
      return;
    }
    // 如不是回调则跳转到登录
    if (!iamNotify) {
      const loginType = props.param?.urlSearch.get('login_type');
      const redirectUrl = props.param?.urlSearch.get('redirect_url');
      loginApi
        .getLoginUrl({
          applicationCode: applicationCode,
          loginType: Number(loginType),
          redirectUrl: redirectUrl,
        })
        .then((it) => {
          if (it.loginUrl) {
            window.location.href = it.loginUrl;
          } else {
            message.error('无法获取到海尔统一登录参数URL!');
          }
        })
        .finally(() => {
          loading.value = false;
        });
      return;
    }

    if (!code) {
      message.error('code不能为空!');
      return;
    }

    loading.value = true;
    loginApi
      .haierIamCodeLogin({
        code: code,
        applicationCode: applicationCode,
      })
      .then((it) => {
        loginSuccess({ data: it });
      })
      .finally(() => {
        loading.value = false;
      });
  })();
}
</script>

<template></template>
