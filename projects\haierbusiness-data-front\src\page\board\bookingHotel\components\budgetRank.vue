<template>
  <div :style="{ height: height + 'vh', overflow: 'hidden' }" background="rgba(0,0,0,0);">
    <Rank :data="rankData" unit="万元" />
  </div>
</template>
<script setup lang="ts">
import Rank from '../../components/rank.vue';
import { queryHotelBudget } from '@haierbusiness-front/apis/src/data/board';
import { ref, onMounted } from 'vue';
import { EventBus } from '../../eventBus';
const props = defineProps({
  height: {
    type: Number,
    default: 25,
  },
});
const rankData = ref([]);
const loading = ref(false);
onMounted(() => {
  queryData();
});
EventBus.on((event, params) => {
  if (event == 'refresh') queryData(params);
});
const queryData = async (params?: any) => {
  loading.value = true;
  const data = await queryHotelBudget(params ? params.data.name : null, params ? params.from : null);
  loading.value = false;
  const rows: any = [];
  data.rows.forEach((item) => {
    const value = (item[1] / 10000).toFixed(item[1] > 10000 ? 0 : 2);
    rows.push({
      name: item[0],
      value: value, //item[1]
    });
  });
  // rows.sort((a,b)=>b.value-a.value);
  rankData.value = rows;
};
</script>
