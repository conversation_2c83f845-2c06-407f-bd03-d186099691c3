<template>
    <div style="height: 33vh" background="rgba(0,0,0,0)">
        <h-row type="flex" :gutter="[30, 10]" style="margin-left: 0; margin-right: 0; height: 30vh">
            <h-row type="flex" :gutter="[30, 10]" style="margin-left: 0; margin-right: 0; height: 30vh">
                <template v-for="(column, index) in  columns" :key="index">
                    <template
                        v-if="(props.type == '青岛会议' && column.name[0] == '降费率') || (props.type == '青岛会议' && column.name[0] == '降费金额') || (props.type == '异地会议' && column.name[0] == '降费率')"></template>
                    <template v-else> <h-col :span="8" class="content">
                            <div>
                                <div class="num">
                                    <CountTo :start-val="0" :end-val="rows[index]" />
                                    <span class="unit" v-if="column.name[0] == '中标金额'">w</span>
                                    <span class="unit" v-if="column.name[0] == '降费金额'">w</span>
                                    <span class="unit" v-if="column.name[0] == '降费率'">%</span>
                                </div>
                                <div class="title"><span>▶</span>{{ column.name[0] }}</div>
                            </div>
                        </h-col>
                    </template>
                </template>
            </h-row>
        </h-row>
    </div>
</template>
<script setup lang="ts">

import {
    Badge as hBadge,
    Progress as hProgress,
    Button as hButton,
    Col as hCol,
    DatePicker as hDatePicker,
    Form as hForm,
    FormItem as hFormItem,
    Input as hInput,
    Modal as hModal,
    Popconfirm as hPopconfirm,
    Popover as hPopover,
    RangePicker as hRangePicker,
    Row as hRow,
    Select as hSelect,
    SelectOption as hSelectOption,
    Table as hTable,
    Tag as hTag,
    message,
    TableProps
} from 'ant-design-vue';
import CountTo from "@/components/vue-count-to/src";
import { queryLocalAccumulative } from "@haierbusiness-front/apis/src/data/board/mice";
import { ref, onMounted } from "vue";
import { EventBus } from "../../eventBus";
import { remove } from "lodash";


const props = defineProps({
    type: {
        type: String,
        default: "青岛会议",
    },
});

const columns = ref(
    [] as Array<{
        name: string;
        value: string | number;
    }>
);
const rows = ref([]);
const loading = ref(false);
onMounted(() => {
    queryData();
})
EventBus.on((event, params) => {
    if (event == "refresh") queryData(params ? params : null);
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
    loading.value = true;
    const data = await queryLocalAccumulative(
        {
            type: props.type,
        },
        params ? params.data.name : null,
        params ? params.from : null
    );
    loading.value = false;
    columns.value = data.columns ?? [];
    data.columns.forEach((item, index) => {
        if (item.name[0] == "中标金额" || item.name[0] == "降费金额") {
            data.rows[0][index] =
                ((data.rows[0][index] / 10000).toFixed(0) as any) - 0;
        }
        if (item.name[0] == "降费率") {
            data.rows[0][index] =
                ((data.rows[0][index] * 100).toFixed(0) as any) - 0;
        }
    });

    rows.value = data.rows[0];
};
</script>
<style scoped lang="less">
@import url(@/assets/style/board/accumulative.less);
</style>
