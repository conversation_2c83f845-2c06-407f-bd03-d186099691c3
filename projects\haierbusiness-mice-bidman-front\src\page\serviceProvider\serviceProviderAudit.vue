<!-- 新增服务商考核弹框 -->
<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  DatePicker as hDatePicker,
  Upload as hUpload,
  message,
  Table as hTable,
  Button as hButton,
  Card as hCard,
  Modal as hModal,
} from 'ant-design-vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import { computed, ref, watch, onMounted, nextTick } from 'vue';
import type { Ref } from 'vue';
import {
  ServiceExam,
  AssessmentItem,
  UploadFile,
  ServiceExamFilter,
  TablePagination,
  FileTypeConstant,
} from '@haierbusiness-front/common-libs';
import { fileApi, serviceExamApi, miceBidManServiceProviderApi } from '@haierbusiness-front/apis';
import { useRouter, useRoute } from 'vue-router';
import dayjs, { Dayjs } from 'dayjs';
import { usePagination } from 'vue-request';
import type { TablePaginationConfig } from 'ant-design-vue';
import type { ColumnType } from 'ant-design-vue/lib/table/interface';

const router = useRouter();
const route = useRoute();
const from = ref();
const confirmLoading = ref(false);
const assessmentDialogVisible = ref(false);
const searchKeyword = ref('');

// 时间设置
const violationTime = ref<Dayjs>();
const violationDisposeEndTime = ref<Dayjs>();

// 监听时间变化
watch(
  () => violationTime.value,
  (newVal) => {
    if (newVal) {
      const formattedDate = newVal.format('YYYY-MM-DD') + ' 00:00:00';
      serviceExam.value.violationTime = formattedDate as any;
    } else {
      serviceExam.value.violationTime = null;
    }
  },
);

watch(
  () => violationDisposeEndTime.value,
  (newVal) => {
    if (newVal) {
      // 日期后拼接 23:59:00
      const formattedDate = newVal.format('YYYY-MM-DD') + ' 23:59:00';
      serviceExam.value.violationDisposeEndTime = formattedDate as any;
    } else {
      serviceExam.value.violationDisposeEndTime = null;
    }
  },
);

// 添加分页相关代码
const searchParam = ref<ServiceExamFilter & { keyword?: string }>({});
const {
  data: assessmentItemsData,
  run: listAssessmentItems,
  loading: assessmentItemsLoading,
} = usePagination((params: TablePagination) =>
  serviceExamApi.getExamItem({
    ...searchParam.value,
    pageNum: params.current || 1,
    pageSize: params.pageSize || 5,
    state: 10,
  }),
);

const assessmentItems = computed(() => assessmentItemsData.value?.records || []);
const assessmentItemsPagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: assessmentItemsData.value?.total || 0,
  current: assessmentItemsData.value?.pageNum || 1,
  pageSize: assessmentItemsData.value?.pageSize || 5,
}));

const defaultData: ServiceExam = {
  id: undefined,
  merchantName: '',
  mainCode: '',
  miceName: '',
  entry: '',
  details: '',
  type: '',
  score: 0,
  fine: 0,
  status: 0,
  attachment: '',
  violationTime: null,
  violationDisposeEndTime: null,
  violationDesc: '',
  evidenceMaterial: '',
  merchantExamItemId: null,
};

const serviceExam = ref<ServiceExam>({ ...defaultData });
const selectedExamType = ref<string | number>(''); // 存储考核条目的examType

// 监听 serviceExam 的变化
watch(
  serviceExam,
  (newVal) => {
    console.log('serviceExam changed:', newVal);
  },
  { deep: true },
);

const fileList = ref<UploadFile[]>([]);
const uploadLoading = ref<boolean>(false);
const serviceProviders = ref<{ label: string; value: string }[]>([]);
const orderNumbers = ref<{ label: string; value: string }[]>([]);
const meetingList = ref<{ miceId: number; mainCode: string; name: string }[]>([]);
const loading = ref<boolean>(false);

// 将状态同步到控制台，方便调试
watch(
  [orderNumbers, serviceProviders, meetingList],
  (newVals) => {
    console.log('反应式变量更新:', {
      orderNumbers: newVals[0].length,
      orderNumbersData: JSON.stringify(newVals[0]),
      serviceProviders: newVals[1].length,
      meetingList: newVals[2].length,
    });
  },
  { deep: true },
);

// 获取服务商列表
const getBussinessList = async () => {
  loading.value = true;
  try {
    const response = await miceBidManServiceProviderApi.getBussinessList();
    if (response?.records && Array.isArray(response.records)) {
      serviceProviders.value = response.records.map((item) => ({
        label: item.name || '',
        value: item.name || '',
      }));
    }
  } catch (error) {
    console.error('获取服务商列表失败:', error);
    message.error('获取服务商列表失败');
  } finally {
    loading.value = false;
  }
};

// 根据服务商ID获取会议信息
const getMeetingInfoByMerchantId = async (merchantId: string | number) => {
  if (!merchantId) return;

  loading.value = true;
  try {
    const response = await serviceExamApi.getMeetingInfo({ id: Number(merchantId) });

    // 根据返回结果判断处理方式 - 如果本身就是数组，直接使用
    if (Array.isArray(response)) {
      meetingList.value = response as { miceId: number; mainCode: string; name: string }[];
      orderNumbers.value = meetingList.value.map((item) => ({
        label: item.mainCode || '',
        value: item.mainCode || '',
      }));
    }
    // 如果是对象且包含data字段是数组
    else if (
      response &&
      typeof response === 'object' &&
      (response as any).data &&
      Array.isArray((response as any).data)
    ) {
      meetingList.value = (response as any).data as { miceId: number; mainCode: string; name: string }[];
      orderNumbers.value = meetingList.value.map((item) => ({
        label: item.mainCode || '',
        value: item.mainCode || '',
      }));
    }
    // 如果是对象且包含records字段是数组
    else if (response && typeof response === 'object' && response.records && Array.isArray(response.records)) {
      meetingList.value = response.records as { miceId: number; mainCode: string; name: string }[];
      orderNumbers.value = meetingList.value.map((item) => ({
        label: item.mainCode || '',
        value: item.mainCode || '',
      }));
    } else {
      console.warn('未找到会议数据或格式不正确, response:', response);
      meetingList.value = [];
      orderNumbers.value = [];
    }
  } catch (error) {
    console.error('获取会议信息失败:', error);
    message.error('获取会议信息失败');
  } finally {
    loading.value = false;
  }
};

// 移除 watch 监听器，避免与 handleOrderChange 冲突
// 现在统一由 handleOrderChange 处理会议名称填充

// 上传附件
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      const file: UploadFile = {
        uid: options.file.uid,
        name: options.file.name,
        status: 'done',
        filePath: baseUrl + it.path,
        fileName: options.file.name,
      };
      fileList.value = [file];
      options.onProgress(100);
      options.onSuccess(it, options.file);
    })
    .catch((error) => {
      message.error('文件上传失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 移除文件
const handleRemove = (file: UploadFile) => {
  const index = fileList.value.indexOf(file);
  if (index !== -1) {
    fileList.value.splice(index, 1);
  }
};

const handleSubmit = () => {
  confirmLoading.value = true;

  // 手动校验必填字段
  if (!serviceExam.value.merchantExamItemId) {
    message.error('请选择考核条目');
    confirmLoading.value = false;
    return;
  }

  if (!serviceExam.value.entry) {
    message.error('考核条目不能为空');
    confirmLoading.value = false;
    return;
  }

  if (!serviceExam.value.violationTime) {
    message.error('违规时间不能为空');
    confirmLoading.value = false;
    return;
  }

  if (!serviceExam.value.details) {
    message.error('考核明细不能为空');
    confirmLoading.value = false;
    return;
  }

  if (!serviceExam.value.violationDesc) {
    message.error('违规描述不能为空');
    confirmLoading.value = false;
    return;
  }

  // 校验文件上传
  if (fileList.value.length === 0) {
    message.error('请上传见证性材料');
    confirmLoading.value = false;
    return;
  }

  from.value
    .validate()
    .then(() => {
      // 获取当前选中订单对应的miceId
      let miceId = null;
      if (serviceExam.value.mainCode && meetingList.value.length > 0) {
        const selectedMeeting = meetingList.value.find((item) => item.mainCode === serviceExam.value.mainCode);
        if (selectedMeeting) {
          miceId = selectedMeeting.miceId;
        }
      }

      const formData: ServiceExam = {
        merchantId: Number(route.query.id),
        // 服务商信息
        merchantName: serviceExam.value.merchantName,
        mainCode: serviceExam.value.mainCode,
        miceName: serviceExam.value.miceName,

        // 考核信息
        entry: serviceExam.value.entry,
        details: serviceExam.value.details,
        type: selectedExamType.value,
        score: serviceExam.value.score,
        fine: serviceExam.value.fine,
        merchantExamItemId: serviceExam.value.merchantExamItemId,

        // 违规信息
        violationTime: serviceExam.value.violationTime,
        violationDisposeEndTime: serviceExam.value.violationDisposeEndTime,
        violationDesc: serviceExam.value.violationDesc,
        miceId: miceId,
        // 见证性材料
        path: fileList.value.map((file) => file.filePath).filter((path): path is string => path !== undefined),
        pathType: FileTypeConstant.WITNESS_MATERIALS.code,
      };

      // 调用保存API，传递额外的miceId参数
      serviceExamApi
        .createExam({ ...formData })
        .then(() => {
          message.success('保存成功');
          router.push('/bidman/serviceProvider');
        })
        .catch((error) => {
          console.error('保存失败:', error);
          message.error('保存失败，请重试');
        })
        .finally(() => {
          confirmLoading.value = false;
        });
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};

const showAssessmentDialog = () => {
  assessmentDialogVisible.value = true;
};

const handleAssessmentDialogCancel = () => {
  assessmentDialogVisible.value = false;
};

const handleSelectAssessment = async (record: AssessmentItem) => {
  const currentMiceName = serviceExam.value.miceName;
  const currentMerchantName = serviceExam.value.merchantName;
  const currentMainCode = serviceExam.value.mainCode;

  // 将examType转换为数字进行比较
  const typeNumber = Number(record.examType);
  const examType = typeNumber === 1 ? '违规' : '整改';
  const examTypeValue = typeNumber === 1 ? 1 : 2;

  // 保存examType到单独的ref中
  selectedExamType.value = record.examType;

  const updatedExam = {
    ...serviceExam.value,
    details: record.detail,
    type: examType,
    typeValue: examTypeValue,
    score: record.score,
    fine: record.money,
    merchantExamItemId: Number(record.id),
    entry: record.name,
    miceName: currentMiceName,
    merchantName: currentMerchantName,
    mainCode: currentMainCode,
  };

  serviceExam.value = updatedExam;

  await nextTick();
  assessmentDialogVisible.value = false;
};

const handleSearch = () => {
  searchParam.value = {
    ...searchParam.value,
    name: searchKeyword.value,
  };
  listAssessmentItems({
    current: 1,
    pageSize: 5,
    total: 0,
  });
};

const handleTableChange = (pagination: TablePaginationConfig) => {
  listAssessmentItems({
    current: pagination.current || 1,
    pageSize: pagination.pageSize || 5,
    total: pagination.total || 0,
  });
};

const columns: ColumnType[] = [
  {
    title: '考核条目',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '考核明细',
    dataIndex: 'detail',
    key: 'detail',
  },
  {
    title: '分数',
    dataIndex: 'score',
    key: 'score',
  },
  {
    title: '金额',
    dataIndex: 'money',
    key: 'money',
  },
  {
    title: '操作',
    key: 'action',
    slots: { customRender: 'action' },
  },
];

// 获取服务商详情
const getServiceProviderDetail = async (id: string) => {
  loading.value = true;
  try {
    const data = await miceBidManServiceProviderApi.getBussinessDetail({ id });
    console.log('服务商详情数据:', data);

    if (data) {
      // 设置服务商信息
      serviceExam.value.merchantName = data.name || '';

      // 如果获取到了服务商ID，自动获取相关会议信息
      if (data.id) {
        getMeetingInfoByMerchantId(data.id);
      }
    }
  } catch (error) {
    console.error('获取服务商详情失败:', error);
    message.error('获取服务商详情失败');
  } finally {
    loading.value = false;
  }
};

// 添加初始化加载
onMounted(() => {
  // 从URL获取服务商ID
  const id = route.query.id as string;
  if (id) {
    getServiceProviderDetail(id);
  }

  getBussinessList();
  listAssessmentItems({
    current: 1,
    pageSize: 5,
    total: 0,
  });
});

// 处理服务商选择变化
const handleMerchantChange = (value: any) => {
  if (value) {
    orderNumbers.value = [];
    serviceExam.value.mainCode = '';
    serviceExam.value.miceName = '';
    getMeetingInfoByMerchantId(value);
  }
};

// 处理订单号选择变化
const handleOrderChange = (value: any) => {
  console.log('订单号选择变化:', value, '当前会议列表:', meetingList.value);

  // 直接在这里处理会议名称填充，避免依赖 watch
  if (value && meetingList.value.length > 0) {
    const selectedMeeting = meetingList.value.find((item) => item.mainCode === value);
    if (selectedMeeting) {
      console.log('找到匹配的会议:', selectedMeeting);
      serviceExam.value.miceName = (selectedMeeting as any).miceName;
    } else {
      console.warn('未找到匹配的会议信息，当前订单号:', value, '可用会议:', meetingList.value);
      serviceExam.value.miceName = '';
    }
  } else {
    serviceExam.value.miceName = '';
  }
};

// 服务商下拉框模糊搜索
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
</script>

<template>
  <div class="create-page">
    <div class="page-header">
      <h1>新增服务商考核</h1>
    </div>
    <h-card>
      <h-form ref="from" :model="serviceExam" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
        <h-form-item label="服务商" name="merchantName">
          <h-select
            v-model:value="serviceExam.merchantName"
            placeholder="请选择服务商"
            :options="serviceProviders"
            @change="handleMerchantChange"
            show-search
            :filter-option="filterOption"
            disabled
          />
        </h-form-item>

        <h-form-item label="订单号" name="mainCode">
          <h-select
            v-model:value="serviceExam.mainCode"
            placeholder="请选择订单号"
            :options="orderNumbers"
            @change="handleOrderChange"
          />
        </h-form-item>

        <h-form-item label="会议名称" name="miceName">
          <h-input v-model:value="serviceExam.miceName" disabled />
        </h-form-item>

        <h-form-item label="考核条目" name="entry">
          <div style="display: flex; gap: 8px">
            <h-input v-model:value="serviceExam.entry" placeholder="请选择考核条目" @click="showAssessmentDialog" />
          </div>
        </h-form-item>

        <h-form-item label="考核明细" name="details">
          <h-input v-model:value="serviceExam.details" disabled />
        </h-form-item>

        <h-form-item label="考核类型" name="type">
          <h-input v-model:value="serviceExam.type" disabled />
        </h-form-item>

        <h-form-item label="考核分数" name="score">
          <h-input v-model:value="serviceExam.score" disabled />
        </h-form-item>

        <h-form-item label="违规金额" name="fine">
          <h-input v-model:value="serviceExam.fine" disabled />
        </h-form-item>

        <h-form-item label="违规时间" name="violationTime">
          <h-date-picker v-model:value="violationTime" />
        </h-form-item>

        <h-form-item label="处理截止时间" name="violationDisposeEndTime" v-if="serviceExam.type === '违规'">
          <h-date-picker v-model:value="violationDisposeEndTime" />
        </h-form-item>

        <h-form-item label="违规描述" name="violationDesc">
          <h-textarea
            v-model:value="serviceExam.violationDesc"
            :rows="4"
            :maxlength="200"
            show-count
            placeholder="请输入违规描述"
          />
        </h-form-item>

        <h-form-item label="见证性材料" name="evidenceMaterial">
          <h-upload
            :file-list="fileList"
            :custom-request="uploadRequest"
            :multiple="false"
            :max-count="1"
            @remove="handleRemove"
            accept=".pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx"
            :show-upload-list="true"
          >
            <h-button>
              <upload-outlined-icon></upload-outlined-icon>
              上传文件
            </h-button>
          </h-upload>
        </h-form-item>

        <h-form-item :wrapper-col="{ offset: 4, span: 18 }">
          <h-button type="primary" :loading="confirmLoading" @click="handleSubmit">保存</h-button>
        </h-form-item>
      </h-form>
    </h-card>

    <!-- 考核条目选择弹窗 -->
    <h-modal
      :visible="assessmentDialogVisible"
      title="服务商考核"
      :width="800"
      :footer="null"
      @cancel="handleAssessmentDialogCancel"
    >
      <div style="margin-bottom: 16px">
        <h-form layout="inline">
          <h-form-item label="考核条目">
            <h-input v-model:value.trim="searchKeyword" placeholder="请输入考核条目" />
          </h-form-item>
          <h-form-item>
            <h-button type="primary" @click="handleSearch">查询</h-button>
          </h-form-item>
        </h-form>
      </div>
      <h-table
        :columns="columns"
        :data-source="assessmentItems"
        :pagination="assessmentItemsPagination"
        :loading="assessmentItemsLoading"
        @change="handleTableChange"
      >
        <template #action="{ record }">
          <h-button type="link" @click="handleSelectAssessment(record)">选择</h-button>
        </template>
      </h-table>
    </h-modal>
  </div>
</template>

<style lang="less" scoped>
.create-page {
  padding: 24px;
  background: #fff;
  min-height: 100vh;

  .page-header {
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 16px;

    h1 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }
  }
}

.important {
  color: red;
}

.support_extend_tip {
  color: #86909c;
  line-height: 22px;
  margin-top: 8px;
}

/* 设置输入框的宽度 */
:deep(.ant-form-item-control-input) {
  max-width: 360px;
}

/* 确保时间选择器的宽度一致 */
:deep(.ant-picker) {
  width: 100%;
}
</style>
