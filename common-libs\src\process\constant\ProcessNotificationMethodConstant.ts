type keys = 'HAIER_TODO' | 'MAIL' | 'MESSAGE';

export const ProcessNotificationMethodConstant = {
    HAIER_TODO: { "code": 1, "desc": "haier待办通知" },
    MAIL: { "code": 2, "desc": "邮件审批通知" },
    MESSAGE: { "code": 4, "desc": "短信审批通知" },
    ROBOT: { "code": 8, "desc": "机器人推送消息" },
  
    ofType: (type?: number): { "code": number, "desc": string } | null => {
      for (const key in ProcessNotificationMethodConstant) {
        const item = ProcessNotificationMethodConstant[key as keys];
        if (type === item.code) {
          return item;
        }
      }
      return null;
    },

    toArray:() :({ code: number, desc: string } | undefined)[] => {
      const types = Object.keys(ProcessNotificationMethodConstant).map((i: string) => {
        if(i !== 'ofType' && i !== 'toArray' && i !== 'toNumberArray' ) {
          return ProcessNotificationMethodConstant[i as keys]
        }
        return
      })
      const newTypes = types.filter(function (s) {
        return s && s; 
      })
      return newTypes
    },
    toNumberArray:() :(number | undefined)[] => {
      const types = Object.keys(ProcessNotificationMethodConstant).map((i: string) => {
        if(i !== 'ofType' && i !== 'toArray' ) {
          return ProcessNotificationMethodConstant[i as keys].code
        }
        return
      })
      const newTypes = types.filter(function (s) {
        return s && s; 
      })
      console.log(newTypes,'newTypes')
      return newTypes
    }
  }