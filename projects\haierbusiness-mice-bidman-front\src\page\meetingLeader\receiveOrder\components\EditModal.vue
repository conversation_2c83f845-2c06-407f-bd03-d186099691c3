<script setup lang="ts">
import { computed, reactive } from 'vue';
import { useReceiveOrderStore } from '../store';
import { Form } from 'ant-design-vue';
import type { IDomEditor } from '@wangeditor/editor';
import Editor from '@haierbusiness-front/components/editor/Editor.vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
  },
  getDataList: Function,
});
const emit = defineEmits(['update:modelValue']);

const store = useReceiveOrderStore();
const useForm = Form.useForm;

const formRules = reactive({
  title: [
    {
      required: true,
      message: '标签名称必填',
    },
  ],
  effectScope: [
    {
      required: true,
      message: '通知作用范围必填',
    },
  ],
  contentForm: [
    {
      required: true,
      message: '内容形式必填',
    },
  ],
  sort: [
    {
      required: true,
      message: '排序必填',
    },
  ],
  state: [
    {
      required: true,
      message: '状态必填',
    },
  ],
  isWindow: [
    {
      required: true,
      message: '是否弹框通知必填',
    },
  ],
});

const { resetFields, validate, validateInfos } = useForm(store.noticeDetail, formRules);

const modelOpen = computed({
  get: () => props.modelValue,
  set: (v) => emit('update:modelValue', v),
});

const onEditorChange = (editor: IDomEditor) => {
  store.noticeDetail.informContent = editor.getHtml();
};

const handleOk = async () => {
  try {
    await validate();
    await (store.noticeDetail.id ? store.editNotice : store.addNotice)(store.noticeDetail);
    props.getDataList?.();
    handleCancel();
  } catch (error) {
    console.error('校验未通过', error);
  }
};

const handleCancel = () => {
  resetFields();
  delete store.noticeDetail.id;
  modelOpen.value = false;
};
</script>

<template>
  <a-modal
    :width="1000"
    :title="`${store.noticeDetail.id ? '编辑' : '新增'}通知`"
    v-model:open="modelOpen"
    class="edit-modal"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="edit-modal-content">
      <a-form class="form" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="标签名称" v-bind="validateInfos.title">
          <a-input v-model:value="store.noticeDetail.title" placeholder="请输入标签名称" />
        </a-form-item>
        <a-form-item label="通知作用范围" v-bind="validateInfos.effectScope">
          <a-select
            :options="store.effectScopeOptions"
            v-model:value="store.noticeDetail.effectScope"
            placeholder="请选择通知作用范围"
            allow-clear
          />
        </a-form-item>
        <a-form-item label="内容形式" v-bind="validateInfos.contentForm">
          <a-radio-group :options="store.contentFormOptions" v-model:value="store.noticeDetail.contentForm" />
        </a-form-item>
        <a-form-item label="公告内容">
          <editor
            height="300px"
            :modelValue="store.noticeDetail.informContent"
            @change="onEditorChange"
            style="z-index: 20"
            uploadUrl="/upload"
          />
        </a-form-item>
        <a-form-item label="排序" v-bind="validateInfos.sort">
          <a-input-number :style="{ width: '100%' }" v-model:value="store.noticeDetail.sort" placeholder="请输入排序" />
        </a-form-item>
        <a-form-item label="状态" v-bind="validateInfos.state">
          <a-select v-model:value="store.noticeDetail.state" :options="store.stateOptions" placeholder="请选择状态" />
        </a-form-item>
        <a-form-item label="是否弹框通知" v-bind="validateInfos.isWindow">
          <a-checkbox v-model:checked="store.noticeDetail.isWindow" />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<style lang="less" scoped>
.edit-modal {
  .edit-modal-content {
    padding: 10px;
    max-height: 70vh;
    overflow: auto;
    .group-title {
      color: rgba(0, 0, 0, 0.88);
      font-weight: 600;
      line-height: 1.5;
      margin-bottom: 15px;
    }
  }
}
</style>
