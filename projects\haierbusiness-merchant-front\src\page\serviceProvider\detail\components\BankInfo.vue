<script setup lang="ts">
import { MerchantBank } from '@haierbusiness-front/common-libs';
import { useServiceProviderDetailStore } from '../store';
import { Modal } from 'ant-design-vue';

const store = useServiceProviderDetailStore();
const columns = [
  {
    title: '银行账号',
    dataIndex: 'accountNumber',
    key: 'accountNumber',
  },
  {
    title: '开户行编码',
    dataIndex: 'bankBranchCode',
    key: 'bankBranchCode',
  },
  {
    title: '开户行地址',
    dataIndex: 'bankBranchAddress',
    key: 'bankBranchAddress',
  },
  {
    title: '银行户主',
    dataIndex: 'accountHolderName',
    key: 'accountHolderName',
  },
  {
    title: '银行所属国家',
    dataIndex: 'bankCountry',
    key: 'bankCountry',
  },
  {
    title: '操作',
    dataIndex: 'options',
    key: 'options',
  },
];
const handleEdit = (record: MerchantBank) => {
  const recordKeys = Object.keys(record);
  for (const recordKey of recordKeys) {
    store.bankDetail[recordKey] = record[recordKey]
  }
  store.editBankModalOpen = true;
}
const handleDelete = (record: MerchantBank) => {
  Modal.confirm({
    title: '是否确定删除?',
    async onOk() {
      await store.deleteBank({ id: record.id });
      store.getBankList({ merchantId: store.businessId })
    },
  })
}
</script>

<template>
  <a-table :dataSource="store.bankList" :columns="columns" bordered :pagination="false">
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'options'">
        <a-space>
          <a @click="handleEdit(record)">编辑</a>
          <a @click="handleDelete(record)">删除</a>
        </a-space>
      </template>
    </template>
  </a-table>
</template>
