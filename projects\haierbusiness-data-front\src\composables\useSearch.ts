import { onMounted, reactive, ref } from "vue";
import type { Ref } from "vue";
import type { PaginationProps } from 'ant-design-vue';
import { reportApi } from '@haierbusiness-front/apis';
import { checkUserGroups } from '@haierbusiness-front/utils/src/authorityUtil'
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil'
import { message, message as globalMessage } from 'ant-design-vue';
import { HeaderConstant, UserGroupSystemConstant } from '@haierbusiness-front/common-libs';


// apply
const BASE_URL: string = import.meta.env.VITE_API_BASE_URL;
export const useSearch = <T, K>(
    api: any,
    searchKey: any,
    dataType?: string
) => {
    const pagination = reactive<PaginationProps>({
        current: 1,
        total: 0,
        pageSize: 10,
        disabled: false,
        showTotal(total) {
            return `共${total}条`;
        },
    });

    const data = <Ref<Array<T>>>ref([]);
    const from = ref();
    const loading = ref(false);
    const downloading = ref(false);

    const onFilterChange = () => {
        pagination.current = 1;
        fetchData();
    };
    const power: any = ref([]);

    function combine_ids(ids: any) {
        if (ids.length == 1) return ids[0]
        return ids.length ? "'" + ids.join("','") + "'" : "";
    }


    const fetchData = async () => {
        loading.value = true;
        let params;
        //普通用户无权限判断
        // if (
        //     power.value.length == 0 &&
        //     dataType &&
        //     !checkUserGroups([UserGroupSystemConstant.SUPER_MANAGE.groupId, UserGroupSystemConstant.REPORT_CONTROL.groupId], 'OR')
        // ) {
        //     data.value = [];
        //     pagination.total = 0;
        //     pagination.disabled = true;
        //     loading.value = false;
        //     return false;
        // }


        //普通用户有权限判断
        // if (
        //     power.value.length > 0 &&
        //     !checkUserGroups([UserGroupSystemConstant.SUPER_MANAGE.groupId, UserGroupSystemConstant.REPORT_CONTROL.groupId], 'OR')
        // ) {
        //     let account_company_nameList = power.value.map((item: any) => item.account_company_code);
        //     let arr: string[] = [];
        //     account_company_nameList.forEach((item: any) => {
        //         if (item) arr = arr.concat(JSON.parse(item));
        //     });
        //     let budgetDepartmentCode = power.value.map((item: any) => item.budgetDepartmentCode);
        //     let arrBudget: string[] = [];
        //     budgetDepartmentCode.forEach((item: any) => {
        //         if (item) arrBudget = arrBudget.concat(JSON.parse(item));
        //     });
        //     if (dataType == "bookingHotel") {
        //         params = {
        //             pageNo: pagination.current,
        //             pageSize: pagination.pageSize,
        //             total: pagination.total,
        //             ...searchKey,
        //             account_company_name: searchKey.account_company_name
        //                 ? searchKey.account_company_name
        //                 : arr && arr.length > 0 ? combine_ids(arr) : null,
        //             budget_department_code: searchKey.budget_department_code
        //                 ? searchKey.budget_department_code
        //                 : arrBudget && arrBudget.length > 0 ? combine_ids(arrBudget) : null,
        //         };
        //     }
        //     if (
        //         dataType == "travel-train" ||
        //         dataType == "travel-internal" ||
        //         dataType == "travel-external" ||
        //         dataType == "travel-hotel" ||
        //         dataType == "travel-taxi" ||
        //         dataType == "travel-ground"
        //     ) {
        //         params = {
        //             pageNo: pagination.current,
        //             pageSize: pagination.pageSize,
        //             total: pagination.total,
        //             ...searchKey,
        //             account_company_code: searchKey.account_company_code
        //                 ? searchKey.account_company_code
        //                 : arr && arr.length > 0 ? combine_ids(arr) : null,
        //             budget_department_code: searchKey.budget_department_code
        //                 ? searchKey.budget_department_code
        //                 : arrBudget && arrBudget.length > 0 ? combine_ids(arrBudget) : null,
        //         };
        //     }
        //     if (dataType == "travel-orderFood") {
        //         params = {
        //             pageNo: pagination.current,
        //             pageSize: pagination.pageSize,
        //             total: pagination.total,
        //             ...searchKey,
        //             account_company_code: searchKey.account_company_code
        //                 ? searchKey.account_company_code
        //                 : arr && arr.length > 0 ? combine_ids(arr) : null,

        //             budget_department_code: searchKey.budget_department_code
        //                 ? searchKey.budget_department_code
        //                 : arrBudget && arrBudget.length > 0 ? combine_ids(arrBudget) : null,
        //         };
        //     }
        // } else {
        params = {
            pageNo: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            ...searchKey,
        };
        // }
        api.list(params)
            .then((res: any) => {
                data.value = res?.records;
                pagination.total = res?.total;
            })
            .finally(() => {
                loading.value = false;
            });
    };
    const powerCompany: any = ref([]);
    const powerDepartment: any = ref([]);

    //获取个人权限数据
    // const getPower = async () => {
    //     let filter = {
    //         createTime: [],
    //         moduleType: 1,
    //         businessType:
    //             dataType == "travel-train" ||
    //                 dataType == "travel-internal" ||
    //                 dataType == "travel-external" ||
    //                 dataType == "travel-hotel" ||
    //                 dataType == "travel-taxi" ||
    //                 dataType == "travel-ground"
    //                 ? "travel"
    //                 : dataType == 'bookingHotel' || dataType == 'travel-orderFood' ? "mealService" : "meetingAffairs"
    //     };
    //     const res = await reportApi.postByIdAndVerificatSignature(filter);
    //     power.value = res;
    //     fetchData();
    //     if (res) {
    //         let tmpCodeArray: any = ref([]);
    //         let tmpCodeArray1: any = ref([]);

    //         for (let i = 0; i < res.length; i++) {
    //             let account_company_codeArray = JSON.parse(res[i].account_company_code);
    //             let account_company_nameArray = JSON.parse(res[i].account_company_name);
    //             let budgetDepartmentCode = JSON.parse(res[i].budgetDepartmentCode);
    //             let budgetDepartmentName = JSON.parse(res[i].budgetDepartmentName);
    //             if (account_company_codeArray && account_company_codeArray.length > 0) {
    //                 for (let j = 0; j < account_company_codeArray.length; j++) {
    //                     if (!tmpCodeArray.value.includes(account_company_codeArray[j])) {
    //                         tmpCodeArray.value.push(account_company_codeArray[j]);
    //                         powerCompany.value.push({
    //                             name: account_company_nameArray[j],
    //                             code: account_company_codeArray[j],
    //                         });
    //                     }
    //                 }
    //             }

    //             if (budgetDepartmentCode && budgetDepartmentCode.length > 0) {
    //                 for (let e = 0; e < budgetDepartmentCode.length; e++) {
    //                     if (!tmpCodeArray1.value.includes(budgetDepartmentCode[e])) {
    //                         tmpCodeArray1.value.push(budgetDepartmentCode[e]);
    //                         powerDepartment.value.push({
    //                             name: budgetDepartmentName[e],
    //                             code: budgetDepartmentCode[e],
    //                         });
    //                     }
    //                 }
    //             }
    //         }
    //     }
    // };
    onMounted(() => {
        //是否有权限
        // if (dataType) {
        //     getPower();
        // } else {
        fetchData();
        // }
    });

    const onPageChange = (currentPagination: PaginationProps) => {
        pagination.pageSize = currentPagination.pageSize;
        pagination.current = currentPagination.current;
        fetchData();
    };

    const handleReset = () => {
        from.value && from.value.resetFields();
        fetchData();
    };

    const onTimeChange = (dateRange: string[]) => {
        const times: string[] = [];
        if (dateRange?.length == 2) {
            times.push(dateRange[0] + " 00:00:00");
            times.push(dateRange[1] + " 23:59:59");
        }
        return times;
        // searchKey.createTime = times
    };

    const download = () => {
        downloading.value = true;
        let param = {} as any;
        //普通用户有权限判断
        // if (
        //     power.value.length > 0 &&
        //     !checkUserGroups([UserGroupSystemConstant.SUPER_MANAGE.groupId, UserGroupSystemConstant.REPORT_CONTROL.groupId], 'OR')
        // ) {
        //     let account_company_nameList = power.value.map((item: any) => item.account_company_code);
        //     let arr: string[] = [];
        //     account_company_nameList.forEach((item: any) => {
        //         if (item) arr = arr.concat(JSON.parse(item));
        //     });
        //     let budgetDepartmentCode = power.value.map((item: any) => item.budgetDepartmentCode);
        //     let arrBudget: string[] = [];
        //     budgetDepartmentCode.forEach((item: any) => {
        //         if (item) arrBudget = arrBudget.concat(JSON.parse(item));
        //     });
        //     if (dataType == "bookingHotel") {
        //         param = {
        //             ...searchKey,
        //             account_company_name: searchKey.account_company_name
        //                 ? searchKey.account_company_name
        //                 : arr && arr.length > 0 ? combine_ids(arr) : null,
        //             budget_department_code: searchKey.budget_department_code
        //                 ? searchKey.budget_department_code
        //                 : arrBudget && arrBudget.length > 0 ? combine_ids(arrBudget) : null,
        //         };
        //     }
        //     if (
        //         dataType == "travel-train" ||
        //         dataType == "travel-internal" ||
        //         dataType == "travel-external" ||
        //         dataType == "travel-hotel" ||
        //         dataType == "travel-taxi" ||
        //         dataType == "travel-ground"
        //     ) {
        //         param = {
        //             ...searchKey,
        //             account_company_code: searchKey.account_company_code
        //                 ? searchKey.account_company_code
        //                 : arr && arr.length > 0 ? combine_ids(arr) : null,
        //             budget_department_code: searchKey.budget_department_code
        //                 ? searchKey.budget_department_code
        //                 : arrBudget && arrBudget.length > 0 ? combine_ids(arrBudget) : null,
        //         };
        //     }
        //     if (dataType == "travel-orderFood") {
        //         param = {
        //             ...searchKey,
        //             account_company_code: searchKey.account_company_code
        //                 ? searchKey.account_company_code
        //                 : arr && arr.length > 0 ? combine_ids(arr) : null,
        //             budget_department_code: searchKey.budget_department_code
        //                 ? searchKey.budget_department_code
        //                 : arrBudget && arrBudget.length > 0 ? combine_ids(arrBudget) : null,
        //         };
        //     }
        // } else {
        param = {
            ...searchKey,
        };
        // }
        const { url, params } = api.download(param);
        reportApi.exportList({ ...params, downloadParams: params['downloadParams'] })
        setTimeout(() => {
            downloading.value = false;
        }, 5000);
    };
    return {
        data,
        pagination,
        loading,
        fetchData,
        onPageChange,
        from,
        handleReset,
        onTimeChange,
        onFilterChange,
        downloading,
        download,
        powerCompany,
        powerDepartment,
    };
};
