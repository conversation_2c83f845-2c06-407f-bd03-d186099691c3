<script setup lang="ts">
import { computed, onMounted, reactive, ref, watchEffect } from 'vue';
import { useServiceProviderListStore } from '../store';

// 不变的数据
const columns = [
  {
    title: '业务类型',
    dataIndex: 'businessType',
    key: 'businessType',
  },
  {
    title: '标签名称',
    dataIndex: 'tagName',
    key: 'tagName',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
  {
    title: '操作',
    dataIndex: 'options',
    key: 'options',
  },
];
const props = defineProps({
  modelValue: {
    type: Boolean,
  },
});
const emit = defineEmits(['update:modelValue']);
const businessType = ref('')
const store = useServiceProviderListStore();

const modelOpen = computed({
  get: () => props.modelValue,
  set: v => emit('update:modelValue', v)
});

const handleOk = async () => {
  handleCancel()
}

const handleCancel = () => {
  delete store.businessDetail.id;
  modelOpen.value = false
}

const handleConfigTag = async (record: { id: number }, isSetTag: 0 | 1) => {
  await store.setTag({ businessType: businessType.value, merchantId: store.businessDetail.id, isSetTag: isSetTag, tagId: record.id })
  store.getSetTagList({ businessType: businessType.value, merchantId: store.businessDetail.id })
}

onMounted(async () => {
  await store.getBusinessTypeList();
  if (store.businessTypeList?.length) {
    businessType.value = store.businessTypeList[0].key;
  }
})

watchEffect(() => {
  if (props.modelValue && businessType.value) {
    store.getTagList({ businessType: businessType.value,state:1 });
    store.businessDetail.id && store.getSetTagList({ businessType: businessType.value, merchantId: store.businessDetail.id });
  }
})
</script>

<template>
  <a-modal :width="600" title="标签配置" v-model:open="modelOpen" class="tag-modal" @ok="handleOk" @cancel="handleCancel">
    <div class="tag-modal-content">
      <a-form layout="inline">
        <a-form-item label="业务类型">
          <a-select :style="{ width: '150px' }" v-model:value="businessType"
            :options="store.businessTypeOptions"></a-select>
        </a-form-item>
      </a-form>
      <div class="tag-container">
        <span>已配置标签：</span>
        <template v-if="!!store.setTagList?.length">
          <a-tag v-for="tagItem of store.setTagList" closable @close.prevent="handleConfigTag(tagItem, 0)">{{
            tagItem.tagName }}</a-tag>
        </template>
        <template v-else>
          无
        </template>
      </div>
      <a-table :dataSource="store.tagList" :columns="columns" :pagination="false">
        <template #bodyCell="{ column, record }">
          <a-space v-if="column.key === 'options'">
            <a @click="handleConfigTag(record, 1)">配置</a>
          </a-space>
          <div v-if="column.key === 'businessType'">
            {{store.businessTypeOptions.find(item => item.value === record[column.key])?.label}}
          </div>
        </template>
      </a-table>
    </div>
  </a-modal>
</template>

<style lang="less" scoped>
.tag-modal {
  .tag-modal-content {
    padding: 10px;
    max-height: 70vh;
    overflow: auto;

    .tag-container {
      padding: 15px 0;
    }
  }
}
</style>
