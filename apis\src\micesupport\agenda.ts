import { download, get, post } from '../request';
import {
  IAgendaFilter,
  IMeetingAgenda,
  IMeetingAgendaDetails,
  IPageResponse,
  Result,
} from '@haierbusiness-front/common-libs';

export const meetingAgendaApi = {
  list: (params: IAgendaFilter): Promise<IPageResponse<IMeetingAgenda>> => {
    return get('/mice-support/api/meeting/agenda/list', params);
  },

  details: (id: number): Promise<IMeetingAgendaDetails> => {
    return get('/mice-support/api/meeting/agenda/detail', {
      id,
    });
  },

  save: (params: IMeetingAgenda): Promise<Result> => {
    return post('/mice-support/api/meeting/agenda/add', params);
  },
  //审批
  Approval: (params: IMeetingAgenda): Promise<Result> => {
    return post('/mice-support/api/meeting/participant/approve', params);
  },
  //导出
  export: (params: { miceInfoId: number; miceInfoName?: string }): Promise<void> => {
    return download('/mice-support/api/meeting/agenda/export', params);
  },
  //导入
  import: (params: IAgendaFilter): Promise<Result> => {
    return post('/mice-support/api/meeting/agenda/import', params);
  },

  //上移/下移
  moveUp: (params: IMeetingAgenda): Promise<Result> => {
    return post('/mice-support/api/meeting/agenda/move', params);
  },

  edit: (params: IMeetingAgenda): Promise<Result> => {
    return post('/mice-support/api/meeting/agenda/update', params);
  },

  remove: (id: number): Promise<Result> => {
    return post(`/mice-support/api/meeting/agenda/delete?id=${id}`);
  },
};
