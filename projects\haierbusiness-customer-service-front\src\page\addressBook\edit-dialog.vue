<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  Switch as hSwitch,
  CheckboxGroup as hCheckboxGroup,
  Checkbox as hCheckbox,
  Image as hImage,
  message,
} from 'ant-design-vue';
import {
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { computed, ref, watch } from 'vue';
import { fileApi } from '@haierbusiness-front/apis';
import type { Ref } from 'vue';
import { AddressBookListRes, Datum } from '@haierbusiness-front/common-libs';
// import { de } from 'element-plus/es/locale';
// import { Codemirror } from 'vue-codemirror';
// import { javascript } from '@codemirror/lang-javascript';
// import { oneDark } from '@codemirror/theme-one-dark';
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil';
import { HeaderConstant } from '@haierbusiness-front/common-libs';
// import Echarts from "./echarts.vue"
import type { UploadChangeParam, UploadFile } from 'ant-design-vue';
// const extensions = [javascript(), oneDark];
const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false);

interface Props {
  show: boolean;
  data: AddressBookListRes;
  labelList: Array<Datum>;
  knowCenterOptions: any;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: AddressBookListRes = {
  bookNum: '',
  bookName: '',
  bookMobile: '',
  status: 10,
};

const rules: Record<string, Rule[]> = {
  bookNum: [{ required: false, message: '请输入工号' }],
  bookName: [{ required: true, message: '请输入姓名' }],
  bookMobile: [{ required: true, message: '请输入坐席话机号' }],
  status: [{ required: true, message: '请选择状态' }],
};

const indexData: Ref<AddressBookListRes> = ref(props.data ? props.data : defaultData);

watch(props, (newValue) => {
  indexData.value = ({ ...newValue.data } as AddressBookListRes) || defaultData;
});

const emit = defineEmits(['cancel', 'ok']);

const visible = computed(() => props.show);

const handleOk = () => {
  from.value
    .validate()
    .then(() => {
      confirmLoading.value = true;
      emit('ok', indexData.value, () => {
        confirmLoading.value = false;
      });
      confirmLoading.value = false;
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};
</script>

<template>
  <h-modal
    v-model:visible="visible"
    :title="indexData.id ? '编辑通讯录' : '新增通讯录'"
    :width="600"
    @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
  >
    <h-form ref="from" :model="indexData" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules">
      <h-form-item label="工号" name="bookNum">
        <h-input v-model:value.trim="indexData.bookNum" style="width: 100%" />
      </h-form-item>
      <h-form-item label="姓名" name="bookName">
        <h-input v-model:value="indexData.bookName" style="width: 100%" />
      </h-form-item>
      <h-form-item label="手机/座机号" name="bookMobile">
        <a-input v-model:value="indexData.bookMobile" style="width: 100%" />
      </h-form-item>
      <!-- <h-form-item label="状态" name="status">
        <h-select v-model:value="indexData.status" style="width: 100%">
          <h-select-option :value="10">启用</h-select-option>
          <h-select-option :value="20">禁用</h-select-option>
        </h-select>
      </h-form-item> -->
    </h-form>
  </h-modal>
</template>

<style lang="less" scoped>
.important {
  color: red;
}
.imgItem {
  position: relative;
  display: inline-block;
  margin-right: 4px;
  .deleteIcon {
    position: absolute;
    right: 0px;
    z-index: 9999;
  }
}
</style>
