import { download, get, post } from '../request'
import { 
    IInSiteMessageFilter, 
    IInSiteMessage,
    IPageResponse, 
    Result } from '@haierbusiness-front/common-libs'


export const inSiteMessageApi = {
    list: (params: IInSiteMessageFilter): Promise<IPageResponse<IInSiteMessage>> => {
        return get('common/api/web/site/pullMessage', params)
    },
    //只包含自己的通知
    oneSelfList: (params: IInSiteMessageFilter): Promise<IPageResponse<IInSiteMessage>> => {
        return get('common/api/web/site/pullMeMessage', params)
    },

    state: (id: number): Promise<IInSiteMessage> => {
        return get('common/api/web/site/readMessage', {
            id
        })
    },

    save: (params: IInSiteMessage): Promise<Result> => {
        return post('merchant/api/inSiteMessage/save', params)
    },

    edit: (params: IInSiteMessage): Promise<Result> => {
        return post('merchant/api/inSiteMessage/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return get(`common/api/web/site/deleteById?id=${id}`)
    },
}
