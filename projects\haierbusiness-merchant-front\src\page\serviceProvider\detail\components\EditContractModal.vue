<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue';
import { useServiceProviderDetailStore } from '../store';
import { Form, message } from 'ant-design-vue';
import { UploadOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { MerchantContract } from '@haierbusiness-front/common-libs';

const props = defineProps({
  modelValue: {
    type: Boolean,
  },
  contractDetail: {
    type: Object,
    required: true,
  },
  getContractList: {
    type: Function,
    default: () => {},
  },
});

const formState = ref({ ...props.contractDetail });
const emit = defineEmits(['update:modelValue', 'update:contractDetail']);
// 添加一个唯一key，用于强制重新创建上传组件
const uploadKey = ref(0);

// 重置上传组件的key
const resetUploadKey = () => {
  uploadKey.value = Date.now();
};

// 清空已上传的文件
const resetUploadFile = () => {
  formState.value.contractUrl = '';
  if (props.contractDetail) {
    props.contractDetail.contractUrl = '';
  }
  resetUploadKey();
};

// 将数字状态转换为布尔值
const convertStateToBool = (val: any) => {
  // 针对服务商系统：0表示开（有效/true），1表示关（失效/false）
  if (val === 0) return true;
  if (val === 1) return false;
  return Boolean(val);
};

watch(
  () => props.contractDetail,
  (val) => {
    if (val) {
      // 确保 state 字段是布尔值
      if (val.state !== undefined) {
        val.state = convertStateToBool(val.state);
      }
    }
  },
  { immediate: true },
);

const useContractEdit = () => {
  const store = useServiceProviderDetailStore();
  const contractDetail = props.contractDetail;
  console.log(contractDetail, 'contractDetail');

  const addContract = async (params = {}) => {
    try {
      await store.addContract(params);
      return true;
    } catch (e) {
      console.log(e);
      return false;
    }
  };

  const editContract = async (params = {}) => {
    try {
      await store.editContract(params);
      return true;
    } catch (e) {
      console.log(e);
      return false;
    }
  };

  const getContractList = async (params = {}) => {
    await store.getContractList(params);
  };

  const setContractDetail = (detail: MerchantContract) => {
    Object.assign(contractDetail, detail);
  };

  return {
    contractDetail,
    addContract,
    editContract,
    getContractList,
    setContractDetail,
    businessId: computed(() => store.businessId),
  };
};

const {
  contractDetail,
  addContract,
  editContract,
  getContractList: storeGetContractList,
  businessId,
} = useContractEdit();

const useForm = Form.useForm;
const formRules = reactive({
  contractNo: [
    {
      required: true,
      message: '请输入合同流水号',
    },
  ],
  contractCode: [
    {
      required: true,
      message: '请输入合同编号',
    },
  ],
  contractDate: [
    {
      required: true,
      message: '请选择合同有效期',
    },
  ],
  signDate: [
    {
      required: true,
      message: '请选择签订日期',
    },
  ],
  contractUrl: [
    {
      required: true,
      message: '请选择签订日期',
    },
  ],
});
const { resetFields, validate, validateInfos } = useForm(contractDetail, formRules);

const modelOpen = computed({
  get: () => props.modelValue,
  set: (v) => emit('update:modelValue', v),
});

const resetFormData = () => {
  // 只在新增模式下完全重置表单
  if (!props.contractDetail?.id) {
    formState.value = {
      contractNo: '',
      contractCode: '',
      contractDate: null,
      signDate: null,
      state: false,
      contractUrl: '', // 确保清空文件
    };

    // 强制清空contractDetail中的附件字段
    if (props.contractDetail) {
      props.contractDetail.contractUrl = '';
    }

    // 重置上传组件
    resetUploadKey();
  }
};

const handleOk = async () => {
  try {
    const requiredFields = ['contractNo', 'contractCode', 'contractDate', 'signDate'] as const;
    type FieldKey = (typeof requiredFields)[number];

    const fieldNames: Record<FieldKey, string> = {
      contractNo: '合同流水号',
      contractCode: '合同编号',
      contractDate: '合同有效期',
      signDate: '签订日期',
    };

    const missingFields = requiredFields.filter((field) => {
      if (field === 'contractDate') {
        return !formState.value[field] || !formState.value[field][0] || !formState.value[field][1];
      }
      return !formState.value[field];
    });

    if (missingFields.length > 0) {
      const missingFieldNames = missingFields.map((field) => fieldNames[field]).join('、');
      console.log('缺少必填字段：', missingFields);
      message.error(`请填写${missingFieldNames}`);
      return;
    }

    // 验证已通过，准备提交数据
    console.log('表单验证通过！');

    const { contractDate, signDate, state, ...restParams } = formState.value;
    const submitData = {
      ...restParams,
      merchantId: businessId.value,
      contractStart: contractDate && contractDate[0]?.format('YYYY-MM-DD HH:mm:ss'),
      contractEnd: contractDate && contractDate[1]?.format('YYYY-MM-DD HH:mm:ss'),
      signDate: signDate?.format('YYYY-MM-DD HH:mm:ss'),
      state: state ? 0 : 1, // 确保提交到后端的是数字类型：true对应0（开/有效），false对应1（关/失效）
    };

    console.log('提交的数据：', submitData);
    console.log('数据：', contractDetail);

    const result = await (props.contractDetail.id ? editContract : addContract)(submitData);
    if (result) {
      // 调用父组件的刷新方法
      if (props.getContractList && typeof props.getContractList === 'function') {
        props.getContractList({ merchantId: businessId.value });
      } else {
        // 兜底方案：调用store中的方法
        storeGetContractList({ merchantId: businessId.value });
      }

      handleCancel();
    }
  } catch (error) {
    console.error('校验未通过', error);
    console.log('校验未通过的表单值：', formState.value);
    message.error('请完善所有必填项信息');
  }
};

const handleCancel = () => {
  resetFields();

  // 只在取消操作时清空附件
  resetFormData();

  // 通知父组件关闭弹窗
  modelOpen.value = false;
};

// 文件上传成功处理函数
const handleUploadSuccess = (fileUrl: string) => {
  console.log('文件上传成功：', fileUrl);
  formState.value.contractUrl = fileUrl;
  if (contractDetail) {
    contractDetail.contractUrl = fileUrl;
  }
};

// 恢复一个简化版的文件名提取函数
const getFileNameFromUrl = (url: string) => {
  if (!url) return '';
  const urlParts = url.split('/');
  return urlParts[urlParts.length - 1] || '';
};

let modalTitle: any = '';
// 监听modelValue变化，处理弹框打开和关闭
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      // 弹框打开时，初始化表单状态，保留合同附件URL
      const newFormState = { ...props.contractDetail };

      // 保留原有contractUrl值用于回显
      formState.value = newFormState;

      // 编辑模式下，确保重置上传组件的key，强制组件重新渲染以显示现有文件
      if (props.contractDetail?.id && props.contractDetail?.contractUrl) {
        resetUploadKey();
      }

      console.log('弹框打开，初始化表单数据：', formState.value);
    }
  },
  { immediate: true },
);

// 监听store中的businessDetail变化，同步到本地formState
watch(
  () => props.contractDetail,
  (newVal) => {
    if (newVal) {
      modalTitle = computed(() => `${props.contractDetail?.id ? '编辑' : '新增'}合同`);
      console.log('接收到合同详情数据:', newVal);

      // 确保state字段转换为布尔值
      const formStateData = { ...newVal };
      if (formStateData.state !== undefined) {
        formStateData.state = convertStateToBool(formStateData.state);
      }

      formState.value = formStateData;
      console.log('处理后的表单数据:', formState.value);
    } else {
      console.log(props.contractDetail, 'props.contractDetail66666');
    }
  },
  { deep: true, immediate: true },
);
</script>

<template>
  <a-modal
    :width="600"
    :title="modalTitle"
    v-model:open="modelOpen"
    class="edit-modal"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="edit-modal-content">
      <a-form class="form" :label-col="{ span: 5 }" :wrapper-col="{ span: 19 }">
        <a-form-item label="合同流水号" v-bind="validateInfos.contractNo">
          <a-input v-model:value="formState.contractNo" placeholder="请输入合同流水号" :maxlength="200" />
        </a-form-item>
        <a-form-item label="合同编号" v-bind="validateInfos.contractCode">
          <a-input v-model:value="formState.contractCode" placeholder="请输入合同编号" :maxlength="200" />
        </a-form-item>
        <a-form-item label="合同有效期" v-bind="validateInfos.contractDate">
          <a-range-picker v-model:value="formState.contractDate" s />
        </a-form-item>
        <a-form-item label="合同状态" v-bind="validateInfos.state">
          <a-switch v-model:checked="formState.state" />
        </a-form-item>
        <a-form-item label="签订日期" v-bind="validateInfos.signDate">
          <a-date-picker v-model:value="formState.signDate" />
        </a-form-item>
        <a-form-item label="合同附件" v-bind="validateInfos.contractUrl">
          <custom-upload
            :value="formState.contractUrl"
            @update:value="(val: any) => formState.contractUrl = val"
            :key="uploadKey"
          >
            <a-button>
              <upload-outlined />
              上传附件
            </a-button>
          </custom-upload>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<style lang="less" scoped>
.edit-modal {
  .edit-modal-content {
    padding: 10px;
    max-height: 70vh;
    overflow: auto;

    .group-title {
      color: rgba(0, 0, 0, 0.88);
      font-weight: 600;
      line-height: 1.5;
      margin-bottom: 15px;
    }
  }
}

:deep(.ant-form-item-required::before) {
  display: none !important;
}

.uploaded-file {
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  a {
    color: #1868db;
    max-width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
