import { downloadPost, get, post } from '../request'
import { 
    IRefundReq, 
    IRefundRes,
    IPageResponse, 
    Update_1Params,
    ExportInvoiceParams,
    IrechargemngParams
} from '@haierbusiness-front/common-libs'


export const rechargemngApi = {
    list: (params: IrechargemngParams): Promise<IPageResponse<IRefundRes>> => {
        return post('banquet/api/banquetRechargeRecord/list', params)
    },
    flowList: (params: IRefundReq): Promise<IPageResponse<IRefundRes>> => {
        return post('banquet/api/banquetSettleConsumeFlowDO/list', params)
    },
    exportFlowList: (params: IRefundReq): Promise<IPageResponse<IRefundRes>> => {
        return downloadPost('banquet/api/banquetSettleConsumeFlowDO/export', params)
    },
    get: (id: number): Promise<IRefundRes> => {
        return get('banquet/api/banquetRechargeRecord/get', {
            id
        })
    },
    exportList: (params: IRefundReq): Promise<IPageResponse<IRefundRes>> => {
        return downloadPost('banquet/api/banquetRechargeRecord/export', params)
    },
    // 修改费率
    changeServiceRatemng: (params: Update_1Params): Promise<IPageResponse<IRefundRes>> => {
        return post('banquet/api/banquetBalance/update', params)
    },
    // 新增归账
    add: (params: IrechargemngParams): Promise<IPageResponse<IRefundRes>> => {
        return post('banquet/api/banquetBalance/recharge', params)
    },
    // 获取
    getInfo: (): Promise<IPageResponse<IRefundRes>> => {
        return post('banquet/api/banquetBalance/info')
    },
    // 获取
    getMtInfo: (): Promise<IPageResponse<IRefundRes>> => {
        return get ('banquet/api/banquetBalance/queryAccount')
    },
    // 下载充值模版
    exportBanquetBalance: (params: IRefundReq): Promise<IPageResponse<IRefundRes>> => {
        return downloadPost('banquet/api/banquetBalance/export', params)
    },
    // 撤回充值
    getRevoke:(params: any): Promise<IPageResponse<IRefundRes>> => {
        return get ('banquet/api/banquetRechargeRecord/revoke',params)
    },
}
