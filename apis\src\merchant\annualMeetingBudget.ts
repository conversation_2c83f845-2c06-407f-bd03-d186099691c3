import { download, get, post } from '../request'
import { 
    IAnnualMeetingBudgetFilter, 
    IAnnualMeetingBudget,
    IPageResponse, 
    Result } from '@haierbusiness-front/common-libs'


export const annualMeetingBudgetApi = {
    list: (params: IAnnualMeetingBudgetFilter): Promise<IPageResponse<IAnnualMeetingBudget>> => {
        return get('/mice-bid/api/mice/year/budget/list', params)
    },

    get: (id: number): Promise<IAnnualMeetingBudget> => {
        return get('/mice-bid/api/mice/year/budget/detail', {
            id
        })
    },

    save: (params: IAnnualMeetingBudget): Promise<Result> => {
        return post('/mice-bid/api/mice/year/budget/add', params)
    },

    edit: (params: IAnnualMeetingBudget): Promise<Result> => {
        return post('/mice-bid/api/mice/year/budget/edit', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('merchant/api/annualMeetingBudget/delete', { id })
    },
}
