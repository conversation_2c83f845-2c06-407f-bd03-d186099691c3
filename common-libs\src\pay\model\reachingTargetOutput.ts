// 参数接口
export interface RtoPageParams {
    /* */
    pageNum?: number;
  
    /* */
    pageSize?: number;
  
    /* */
    payCode?: string;
  
    /* */
    serialCode?: string;
  
    /* */
    orderCode?: string;
  
    /* */
    amount?: number;
  
    /* */
    statusList?: Record<string, unknown>[];
  
    /* */
    applyCodeOrName?: string;
  
    /* */
    applyBeginDate?: Record<string, unknown>;
  
    /* */
    applyEndDate?: Record<string, unknown>;
  
    /*结算状态：0 待完善 10 待汇总 20 已汇总 30 取消汇总 40 已确认 50 已结算 */
    balanceStatus?: number;
  
    /* */
    needPage?: boolean;
  }


  // 响应接口
export interface RtoPageRes {
    /* */
    data: {
      /* */
      pageNum: number;
  
      /* */
      pageSize: number;
  
      /* */
      total: number;
  
      /* */
      totalPage: number;
  
      /* */
      records: {
        /* */
        pageNum: number;
  
        /* */
        pageSize: number;
  
        /* */
        id: number;
  
        /*支付单号 */
        payCode: string;
  
        /*申请人 */
        applyName: string;
  
        /*订单号 */
        orderCode: string;
  
        /*费用预算总计 */
        budgetAmount: number;
  
        /*创建人 */
        createBy: string;
  
        /*创建人名称 */
        createName: string;
  
        /*创建时间 */
        gmtCreate: Record<string, unknown>;
  
        /*附件名称 */
        fileName: string;
  
        /*附件地址 */
        fileUrl: string;
  
        /*订单状态10待提交20已提交30支付中40已关闭90已完成 */
        status: number;
  
        /*申请时间 */
        applyTime: Record<string, unknown>;
  
        /*充值事由 */
        reason: string;
  
        /*审批失败原因 */
        processFailInfo: string;
  
        /*结算状态：0 待完善 10 待汇总 20 已汇总 30 取消汇总 40 已确认 50 已结算 */
        balanceStatus: number;
  
        /* */
        billResponse: {
          /*id */
          id: number;
  
          /*商品名称 */
          productName: string;
  
          /*税收分类编码 */
          taxCode: string;
  
          /*税率 */
          rate: number;
  
          /*数量 */
          num: number;
  
          /*商品单价 */
          unitPrice: number;
  
          /*规格 */
          standards: string;
  
          /*售卖单位 */
          saleUnit: string;
  
          /*商品总价 */
          amountSum: number;
        };
  
        /* */
        needPage: boolean;
      }[];
    };
  
    /* */
    code: string;
  
    /* */
    message: string;
  
    /* */
    success: boolean;
  }