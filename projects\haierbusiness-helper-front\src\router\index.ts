import { RouteRecordRaw } from 'vue-router';

const modules = import.meta.glob('/src/page/**/*.vue');

import { baseRouterConstructor } from '@haierbusiness-front/utils';

const whiteList = ['/travel/infoDetail', '/travel/lifeDetail', '/travel/bannerDetail', '/travel/adDetail']
let flag = true
const currentUrl = location.hash
const index = currentUrl.lastIndexOf('?')
const url = currentUrl.substring(1, index)

let routes:RouteRecordRaw[] = []

if(whiteList.indexOf(url)> -1) {
    flag = false
    routes = [
        
    ];
} 

const router = baseRouterConstructor("haierbusiness-helper", modules, flag, undefined, routes)

export default router;
