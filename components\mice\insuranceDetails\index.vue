<script setup lang="ts">
import { Modal as aModal, Image as aImage, Spin as aSpin, Button as hButton } from 'ant-design-vue';
import { defineProps, defineEmits, PropType, computed } from 'vue';
import { getFileNameFromPath } from '@haierbusiness-front/utils';

const props = defineProps({
  // 是否显示弹窗
  open: {
    type: Boolean,
    required: true,
  },
  // 弹窗类型：product-保险产品；term-保险条款
  modalType: {
    type: String as PropType<'product' | 'term'>,
    default: 'product',
  },
  // 保险产品详情对象
  insuranceDetail: {
    type: Object as PropType<Record<string, any>>, // 具体结构由业务方决定
    default: () => ({}),
  },
  // 保险条款文件地址
  insuranceTermFile: {
    type: String,
    default: '',
  },
  // 条款加载状态
  insuranceTermLoading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:open', 'close']);

// 判断是否为图片文件
const isImageFile = (url: string) => {
  if (!url) return false;
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];
  const lowerUrl = url.toLowerCase();
  return imageExtensions.some((ext) => lowerUrl.includes(ext));
};

// 获取文件类型标签
type FileType = number | string;
const getFileTypeLabel = (type: FileType) => {
  const typeMap: Record<string, string> = {
    24: '保险条款',
    25: '保险产品介绍',
    26: '产品特色',
    30: '免除责任条款',
  };
  return typeMap[type as string] || `文件类型${type}`;
};

// 打开文档
const openDocument = (url: string) => {
  if (url) {
    window.open(url, '_blank');
  }
};



const closeIns = () => {
  emit('update:open', false);
  emit('close');
};

// 按文件类型分组
const groupedFilesByType = computed(() => {
  if (!props.insuranceDetail.path || !Array.isArray(props.insuranceDetail.path)) {
    return {};
  }

  const grouped: Record<string, any[]> = {};

  props.insuranceDetail.path.forEach((file: any) => {
    if (file.path) {
      // 只处理有路径的文件
      const type = file.type;
      if (!grouped[type]) {
        grouped[type] = [];
      }
      grouped[type].push(file);
    }
  });

  return grouped;
});
</script>

<template>
  <a-modal
    :open="props.open"
    :title="props.modalType === 'product' ? '保险产品' : '保险条款'"
    :width="props.modalType === 'term' ? '900px' : '600px'"
    :footer="null"
    @cancel="closeIns"
  >
    <!-- 保险产品详情 -->
    <div v-if="props.modalType === 'product'" style="max-height: 600px; overflow-y: auto">
      <div v-if="props.insuranceDetail" class="insurance-detail">
        <div class="detail-item-horizontal">
          <span class="label">保险名称：</span>
          <span class="value">{{ props.insuranceDetail.insuranceName || '—' }}</span>
        </div>

        <div class="detail-item-horizontal">
          <span class="label">保费：</span>
          <span class="value">{{ props.insuranceDetail.price ? `${props.insuranceDetail.price} 元/人/天` : '—' }}</span>
        </div>

        <div class="detail-item-horizontal">
          <span class="label">年龄要求：</span>
          <span class="value">{{ props.insuranceDetail.ageRequire || '—' }}</span>
        </div>

        <div class="detail-item-horizontal">
          <span class="label">等待期：</span>
          <span class="value">{{ props.insuranceDetail.waitingPeriod || '—' }}</span>
        </div>

        <div class="detail-item-horizontal" v-if="props.insuranceDetail.notice">
          <span class="label">投保须知：</span>
          <div class="value detail-content">
            <div v-for="(line, index) in (props.insuranceDetail.notice || '').split('\n')" :key="index">
              {{ line }}
            </div>
          </div>
        </div>

        <div class="detail-item-horizontal" v-if="props.insuranceDetail.claimProcess">
          <span class="label">理赔流程：</span>
          <div class="value detail-content">
            <div v-for="(line, index) in (props.insuranceDetail.claimProcess || '').split('\n')" :key="index">
              {{ line }}
            </div>
          </div>
        </div>

        <div class="detail-item-horizontal" v-if="props.insuranceDetail.hintMessage">
          <span class="label">温馨提示：</span>
          <div class="value detail-content">
            <div v-for="(line, index) in (props.insuranceDetail.hintMessage || '').split('\n')" :key="index">
              {{ line }}
            </div>
          </div>
        </div>

        <div class="detail-item-horizontal" v-if="props.insuranceDetail.question">
          <span class="label">常见问题：</span>
          <div class="value detail-content">
            <div v-for="(line, index) in (props.insuranceDetail.question || '').split('\n')" :key="index">
              {{ line }}
            </div>
          </div>
        </div>

        <!-- 展示不同类型的文件 -->
        <template
          v-for="(groupedFiles, type) in groupedFilesByType"
          :key="type"
          v-if="props.insuranceDetail.path && props.insuranceDetail.path.length > 0"
        >
          <div class="detail-item-horizontal">
            <span class="label">{{ getFileTypeLabel(type) }}：</span>
            <div class="value">
              <template v-for="(file, index) in groupedFiles" :key="`${type}-${index}`">
                <div
                  v-if="isImageFile(file.path)"
                  class="image-preview"
                  style="display: inline-block; margin-right: 12px; margin-bottom: 8px"
                >
                  <a-image
                    :width="120"
                    :src="file.path"
                    :alt="getFileTypeLabel(file.type)"
                    :preview="{ maskClosable: true }"
                  />
                </div>
                <div v-else class="file-link" style="display: inline-block; margin-right: 12px">
                  <a href="javascript:void(0)" @click="openDocument(file.path)">{{ getFileNameFromPath(file.path) }}</a>
                </div>
              </template>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 保险条款 -->
    <div v-else style="height: 600px; overflow: auto">
      <div
        v-if="props.insuranceTermLoading"
        style="display: flex; justify-content: center; align-items: center; height: 100%"
      >
        <a-spin size="large" tip="加载中..." />
      </div>
      <div v-else-if="props.insuranceTermFile" style="height: 100%">
        <!-- 图片文件 -->
        <div
          v-if="isImageFile(props.insuranceTermFile)"
          style="height: 100%; display: flex; justify-content: center; align-items: center; background: #f5f5f5"
        >
          <a-image
            :src="props.insuranceTermFile"
            alt="保险条款"
            style="max-width: 100%; max-height: 100%"
            :preview="{ maskClosable: true }"
          />
        </div>
        <!-- 非图片文件，如 PDF，用 iframe -->
        <iframe
          v-else
          :src="props.insuranceTermFile"
          style="width: 100%; height: 100%; border: none"
          frameborder="0"
        ></iframe>
      </div>
      <div v-else style="display: flex; justify-content: center; align-items: center; height: 100%">
        <p>暂无保险条款文件</p>
      </div>
    </div>

    <template #footer>
      <h-button type="primary" @click="closeIns">关闭</h-button>
    </template>
  </a-modal>
</template>

<style scoped lang="less">
.insurance-detail {
  .detail-item-horizontal {
    display: flex;
    margin-bottom: 12px;
    align-items: flex-start;

    .label {
      flex-shrink: 0;
      width: 120px;
      margin-right: 12px;
      color: #333;
    }

    .value {
      flex: 1;
      color: #666;
    }

    .detail-content {
      div {
        line-height: 1.6;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .image-preview {
      :deep(.ant-image) {
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        padding: 4px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: #1868db;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }

    .file-link {
      a {
        color: #1868db;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}
</style>
