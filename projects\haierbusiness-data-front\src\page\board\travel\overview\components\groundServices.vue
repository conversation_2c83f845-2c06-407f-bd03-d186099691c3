<template>
    <div :style="{ height: height + 'vh' }" background="rgba(0,0,0,0)">
        <bar-line :from="props.from ? props.from : ''" :height="height" v-if="loaded" :legend="legend" :x-axis="xAxis"
            :y-axis="yAxis" :series="series" />
    </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import BarLine from "../../../components/barLine.vue";
import { queryGroundServices } from "@haierbusiness-front/apis/src/data/board/travel";
import { EventBus } from "../../../eventBus";
const props = defineProps({
    dateType: Number,
    height: {
        type: Number,
        default: 30,
    },
    from: {
        type: String,
        default: "",
    },
});
const loaded = ref(false);
const loading = ref(false);
const legend = ["销售金额", "人次"];
const xAxis = ref([]);
const yAxis = [
    {
        type: "value",
        name: "万元",
        splitNumber: 5,
        axisLabel: {
            formatter(value) {
                return value / 10000;
            },
        },
    },
    {
        type: "value",
        name: "人次",
        splitNumber: 5,
    },
];
const series = ref([]);
onMounted(()=>{
    queryData();
})
EventBus.on((event) => {
    if (event == "refresh") queryData();
});
const getFunctionColumns = () => {
    if (props.dateType == 0) {
        return [
            {
                alias: "dd_ydsj_group",
                snippet: "AGG_DATE_YEAR([dd_ydsj])",
            },
        ];
    }
    if (props.dateType == 1) {
        return [
            {
                alias: "dd_ydsj_group",
                snippet: "AGG_DATE_MONTH([dd_ydsj])",
            },
        ];
    }
    return [
        {
            alias: "dd_ydsj_group",
            snippet: "AGG_DATE_DAY([dd_ydsj])",
        },
    ];
};
const queryData = async () => {
    loading.value = true;
    const functionColumns = getFunctionColumns();
    const data = await queryGroundServices({
        functionColumns,
    });
    loading.value = false;
    const barData: any = [];
    const lineData: any = [];
    const xData: any = [];
    data.rows.forEach((item, index) => {
        xData.push(item[0]);
        barData.push(item[2] || 0);
        // barData.push((item[2]/10000).toFixed(0));
        lineData.push(item[1] || 0);
    });
    xAxis.value = xData;
    series.value = [
        {
            name: "销售金额",
            type: "bar",
            color: "rgba(0,240,255,0.4)",
            itemStyle: {
                borderColor: "#00F0FF",
            },
            data: barData,
        },
        {
            name: "人次",
            type: "line",
            yAxisIndex: 1,
            color: "#FFD700",
            smooth: true,
            symbol: "none",
            data: lineData,
        },
    ] as any;
    loaded.value = true;
};
watch(() => props.dateType, queryData);
</script>
