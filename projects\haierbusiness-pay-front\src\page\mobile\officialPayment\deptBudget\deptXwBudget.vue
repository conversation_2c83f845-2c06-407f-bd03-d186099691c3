<script setup lang="ts">
import {
  showFailToast,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
  Form as <PERSON><PERSON><PERSON>,
  Field as <PERSON><PERSON><PERSON>,
  CellGroup as VanCellGroup,
  showDialog,
  showSuccessToast,
  NoticeBar,
  Popup as VanPopup,
  showToast,
  showLoadingToast
} from "vant";
import "vant/es/notice-bar/style";
import {
  payApi,
  compositionPayApi,
  virtualPayApi,
  budgetHaierPayApi,
  organizationCenterApi
} from "@haierbusiness-front/apis";
import {
  IPayData,
  IQueryVirtualAccountsResponse,
  PaySourceConstant,
  HaierBudgetSourceConstant,
  IloginUser,
  IBudgetHaierTypesResponse,
  IAbCodeResponse,
  ITraveler,

} from "@haierbusiness-front/common-libs";
import { computed, PropType, ref,watch } from "vue";
import {
  removeStorageItem,
  isMobile,
  
} from "@haierbusiness-front/utils";
import { useRequest } from "vue-request";
import userSelectM from "@/components/userSelectM.vue";
import deptSelectM from "@/components/deptSelectM.vue";
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { resolveToken } from '@haierbusiness-front/utils';

const loginUser = ref(resolveToken() || {})


interface Props {
  applicationCode: IPayData;
  budgetType: string;
  param:IPayData;
}

const props = withDefaults(defineProps<Props>(), {});
const budgetBelong = ref(0);

watch(budgetBelong,(New, Old)=>{
	console.log(`新值:${New} ——— 老值:${Old}`)
  clear()
  budgeterName.value = null
})

// 预算系统
const budgetSystem = ref()
const orgSource = ref()

// 项目名称
const projectCode = ref()
const projectName = ref()
const show = ref(false)

// 法人
const legalPerson = ref()
const legalPersonName = ref()
const legalPersonId = ref()
// 成本中心
const costCenter = ref()
const costCenterName = ref()
// 执行主体
const performCode = ref()
const performName = ref()
// 预算人
const budgeterCode = ref()
const budgeterName = ref()
// 预算主体
const budgetGroupCode = ref()
const budgetGroupName = ref()

// 财务组织编码
const financialCode = ref()
const financialName = ref()

// 预算管理岗
const budgetManager = ref()
const budgetManagerName = ref()

// 月度账户id
const accountCode = ref()

// 预算金额
const leftAmt = ref()
// 查询的列表
const searchDataList = ref([]);
// 支付loading
const payLoading = ref<any>(false)

const emit = defineEmits(["setIsPayComplete", "payComplete","isPayLoading"]);

// 按人查询选中的组织/部门
const userSelectedRowKeys = ref<any>([])
const userSelectedRowRecord = ref<any>({})
const deptSelectedRowKeys = ref<any>([])
const deptSelectedRowRecord = ref<any>({})
const budgets = ref<any>()
// 出账法人下拉值 
const legalPersonOptions = ref<any>([])
// 费用科目
const {
  data: searchFeeItemsData,
  run: searchFeeItemsRun,
  loading: searchFeeItemsLoading
} = useRequest(budgetHaierPayApi.searchFeeItems, {
  manual: false,
  defaultParams: [
    {
      applicationCode: props?.applicationCode,
      budgetSysCode: HaierBudgetSourceConstant.GEMS.code,
      businessType:props.param?.businessType,
    }
  ]
});

// 费用科目选项
const feeItemOptions = computed(() => {
  if (searchFeeItemsData.value) {
    return searchFeeItemsData.value.map(it => {
      return { value: it.itemCode, text: it.itemName };
    });
  } else {
    return [];
  }
});

// 费用科目弹窗
const feeItem = ref<any>();
const feeItemName = ref<any>("");
const showPicker = ref<boolean>(false);


// 选择预算人
const selectBudgeter = (item: ITraveler) => {
  console.log(item)
  budgeterCode.value = item.username;
  budgeterName.value = item.nickName;
};

// 选择预算部门
const selectDept = (item: ITraveler) => {
  console.log(item)
  deptSelectedRowRecord.value = item
    if (deptSelectedRowRecord.value.budgetCode) {
      // 有数据，且选中
      // 清除之前的值
      clear()
      // 赋值A码和B码
      budgetGroupCode.value = deptSelectedRowRecord.value.budgetCode
      budgetGroupName.value = deptSelectedRowRecord.value.financialName
      performCode.value = deptSelectedRowRecord.value.performCode
      performName.value = deptSelectedRowRecord.value.financialName
      financialCode.value = deptSelectedRowRecord.value.financialCode
      financialName.value = deptSelectedRowRecord.value.financialName
      budgetManager.value = deptSelectedRowRecord.value.masterCode
      budgetManagerName.value = deptSelectedRowRecord.value.masterName
      orgSource.value = deptSelectedRowRecord.value.systemCode

      onSearch();
      deptSelectedRowKeys.value = [];
      deptSelectedRowRecord.value = {};
    } else {
      showToast('预算主体为空，查询预算失败！')
      clear()
      return
    }
};


const selectMainInfo = (item:any) => {
  selectCode.value = item.performGroupCode

  userSelectedRowRecord.value = item
  if(userSelectedRowRecord.value && (userSelectedRowRecord.value.id || userSelectedRowRecord.value.id === 0)) {
    if (userSelectedRowRecord.value.budgetGroupCode) {
      // 有数据，且选中
      // 清除之前的值
      clear()
      // 赋值A码和B码
      budgetGroupCode.value = userSelectedRowRecord.value.budgetGroupCode
      budgetGroupName.value = userSelectedRowRecord.value.budgetGroupName
      performCode.value = userSelectedRowRecord.value.performGroupCode
      performName.value = userSelectedRowRecord.value.performGroupName
      financialCode.value = userSelectedRowRecord.value.financialCode
      financialName.value = userSelectedRowRecord.value.financialName
      budgetManager.value = userSelectedRowRecord.value.budgetMasterCode
      budgetManagerName.value = userSelectedRowRecord.value.budgetMasterName
      orgSource.value = userSelectedRowRecord.value.systemCode
      onSearch();
      userSelectedRowKeys.value = [];
      userSelectedRowRecord.value = {};
    } else {
      showToast('预算主体为空，查询预算失败！')
      clear()
      return
    }
  } else{
    clear()
  }
};

const clear = () => {
  // 清除预算主体
  budgetGroupCode.value = null
  budgetGroupName.value = null
  // 清除法人
  legalPerson.value = null
  legalPersonName.value = null
  legalPersonId.value = null
  // 清除成本中心
  costCenter.value = null
  costCenterName.value = null
  // 清除执行主体
  performCode.value = null
  performName.value = null
  // 清除预算系统
  budgetSystem.value = null
  orgSource.value = null
  // 清除项目名称
  projectCode.value = null
  projectName.value = null
  // 清除费用科目
  feeItem.value = null
  feeItemName.value = null
  // 清除预算金额
  leftAmt.value = null
  // 清除管理岗
  financialCode.value = null
  financialName.value = null
  // 清除财务组织
  budgetManager.value = null
  budgetManagerName.value = null
  // budgeterName.value = null
  legalPersonOptions.value = []
}

// 清除其他项目
const clearOther = () => {
  // 清除选中的组织
  userSelectedRowKeys.value = []
  userSelectedRowRecord.value = {}

  // 清除选中的预算
  SubjectSelectedRowKeys.value = []
  SubjectSelectedRowRecord.value = []

  // 清除预算信息
  budgets.value = []
  // 清除出账法人
  legalPersonOptions.value = []
}

const onSearch = () => {
  const toast = showLoadingToast({
    duration: 0,
    forbidClick: true,
    message: '请求中...',
  });
  budgetHaierPayApi
    .searchHBC2Budget({
      applicationCode: props?.applicationCode,
      budgeterCode: budgeterCode.value,
      budgetDepartmentCode: budgetGroupCode.value,
      isQueryDept: budgetBelong.value,
      haierBudgetType: props.budgetType,
      businessType: props.param?.businessType,
    })
    .then((res) => { 
      toast.close()
      // 费用科目
      res.map((item, index) => {
        item.id = index + 1
        item.name = item.systemCode
        item.subname = `费用科目名称:${item.feeItemName}  费用科目编码:${item.feeItem} 预算金额:${item.leftAmt} 项目名称:${item.projectName} 项目编码:${item.projectCode} 预算金额:${item.leftAmt}` 
      })
      budgets.value = res
      // 是否是多个
      if(res.length > 0) {
        const data = res[0]
        leftAmt.value = data.leftAmt
        feeItem.value = data.feeItem
        feeItemName.value = data.feeItemName
        budgetSystem.value = data.systemCode
        accountCode.value = data.accountCode
        projectName.value = data.projectName
        projectCode.value = data.projectCode
        getBillAndCostCenters(performCode.value, data.feeItem ?? '')
      } else {
        showToast('未查询到预算！')
      }
    })
    .finally(() => {
      // userLoading.value = false;
      // toast.close()
    })
};


// 获取出账法人和成本中心
const getBillAndCostCenters = async (performCode: string, costItemCode: string) => {
  if(!performCode) {
    showToast('未查询到执行主体！')
    return
  }
  if(!costItemCode) {
    showToast('未查询到费用科目！')
    return
  }
  const toast1 = showLoadingToast({
    duration: 0,
    forbidClick: true,
    message: '请求中...',
  });
  legalPersonOptions.value = []
  const data = await organizationCenterApi.getBillAndCostCenters(performCode, costItemCode)
  toast1.close()
  if(data && data.length > 0) {
    data.map((item, index) => {
      legalPersonOptions.value.push({
        id: index + 1,
        legalPerson: item.legalPerson ?? '',
        legalPersonName: item.legalPersonName ?? '',
        costCenter: item.costCenter ?? '',
        costCenterName: item.costCenterName ?? '',
        value:item.legalPerson ?? '',
        text: item.legalPersonName ?? '',
      })
      legalPerson.value = data[0].legalPerson
      legalPersonName.value = data[0].legalPersonName
      legalPersonId.value = 1
      costCenter.value = data[0].costCenter
      costCenterName.value = data[0].costCenterName
    })
    
  }
}

// 选出账法人
const onConfirm = ({ selectedOptions }) => {
      legalPerson.value = selectedOptions[0]?.legalPerson
      legalPersonName.value = selectedOptions[0]?.legalPersonName
      costCenter.value = selectedOptions[0]?.costCenter
      costCenterName.value = selectedOptions[0]?.costCenterName
    showPicker.value = false;
};

const onSelect = (item) =>{
  leftAmt.value = item.leftAmt
  feeItem.value = item.feeItem
  feeItemName.value = item.feeItemName
  budgetSystem.value = item.systemCode
  accountCode.value = item.accountCode
  projectName.value = item.projectName
  projectCode.value = item.projectCode
  getBillAndCostCenters(performCode.value, item.feeItem ?? '')
}

const payComplete = () => {
  emit('payComplete', true);
};

// 选择预算主体
const showMainInfoBox = ref(false)
const closeMainInfoBox = () => {
  showMainInfoBox.value = false;
};
const selectCode = ref('')
// 获取主体信息
const getAbCodeByUser = async (userName:string) => {
  if(!userName) {
    return showToast('请补充预算信息！')
  }
  await searchAbCodeByUserRun({ userName:userName, systemCode:'XW' })

  selectCode.value = ""
  showMainInfoBox.value = true
}

// 按人查询A码和B码
const {
  data: userAbCodeData,
  run: searchAbCodeByUserRun,
  loading: searchUserLoading
} = useRequest(organizationCenterApi.getAbCode);

const userAbCode = computed(() => {
  if(userAbCodeData.value && userAbCodeData.value.length > 0) {
    let list = [] as IAbCodeResponse[]
    userAbCodeData.value.map((item, index) => {
      const data: IAbCodeResponse = {
        ...item,
        id: index
      }
      list = [...list, data]
    })
    return list
  } else {
    return [] as IAbCodeResponse[]
  }
})
const chose = () => {
  console.log('chose-------->>>')
  selectBudgeter(loginUser.value)
  getAbCodeByUser(loginUser.value?.username)
}

const pay = () => {
  if (!feeItem.value) {
    showToast('请选择预算主体!');
    return;
  }
  if (budgetBelong.value === 0 && !budgeterCode.value) {
    showToast('请输入预算人!');
    return;
  }
  // if(!budgetManager.value) {
  //   showToast('未查询到预算管理岗,请联系组织中心!');
  //   return;
  // }
  emit('isPayLoading',true)
  // if (!leftAmt.value) {
  //   showToast("获取可用余额失败!")
  //   return;
  // }
  // if (leftAmt.value <= 0) {
  //   showToast("可用余额不足!")
  //   return;
  // }
  payLoading.value = true;
  budgetHaierPayApi
    .occupyBudget(
      {
        haierBudgetType: props.budgetType,
        budgeterCode: budgeterCode.value,
        budgeterName: budgeterName.value,
        budgetDepartmentCode: budgetGroupCode.value,
        budgetDepartmentName: budgetGroupName.value,
        accountCompanyCode: legalPerson.value,
        accountCompanyName: legalPersonName.value,
        projectCode: projectCode.value,
        feeItem: feeItem.value,
        feeItemName: feeItemName.value,
        financialCode: financialCode.value,
        financialName: financialName.value,
        budgetManager: budgetManager.value,
        isQueryDept: budgetBelong.value,
        accountCode: accountCode.value,
        performCode:performCode.value,
        performName: performName.value,
        costCenter:costCenter.value,
        costCenterName:costCenterName.value,
        budgetSystemCode: budgetSystem.value,
        orgSource: orgSource.value,

        // - 通用参数
        orderCode: props.param?.orderCode,
        applicationCode: props.param?.applicationCode,
        payTypes: props.param?.payTypes,
        username: props.param?.username,
        providerCode: props.param?.providerCode,
        amount: Number(props.param?.amount),
        orderDetailsUrl: props.param?.orderDetailsUrl,
        notifyUrl: props.param?.notifyUrl,
        callbackUrl: props.param?.callbackUrl,
        description: props.param?.description,
        payload: props.param?.payload,
        businessType: props.param?.businessType,
        processId: props.param?.processId,
        startApproveFlag: props.param?.startApproveFlag,
        enterpriseCode: props.param?.enterpriseCode,
        paySource: isMobile() ? PaySourceConstant.MOBILE.type : PaySourceConstant.PC.type,
        paymentMethod: 2
      },
      {
        applicationCode: props.param?.applicationCode,
        excludes:
          'paySource,haierBudgetType,budgeterCode,orgSource,budgeterName,budgetDepartmentCode,budgetDepartmentName,accountCompanyCode,accountCompanyName,projectCode,feeItem,feeItemName,financialCode,financialName,budgetManager,isQueryDept,accountCode,performCode,performName,costCenter,costCenterName,budgetSystemCode,paymentMethod,payload,startApproveFlag',
        nonce: props.param?.hbNonce,
        timestamp: props.param?.hbTimestamp,
        sign: props.param?.sign,
      },
    )
    .then((it) => {
      payComplete();
      emit('isPayLoading',false)
    })
    .finally(() => {
      emit('isPayLoading',false)
    });
};


defineExpose({ pay,chose });
</script>

<template>
  <div class="contentBox">
    <van-form>
        <van-tabs v-model:active="budgetBelong">
          <van-tab title="个人">
            <!-- <van-field
            v-model="budgeterName"
            input-align="right"
            :disabled="true"
            name="预算主体"
            label="预算人"
            :rules="[{ required: true, message: '请填写预算主体' }]"
          /> -->
            <userSelectM label="预算人" type="xw" :value="budgeterName" @chose="selectBudgeter" @choseMainInfo="selectMainInfo" />
          </van-tab>
          <!-- <van-tab v-if="props.param?.applicationCode!='haierbusiness-localhotel'&&props.param?.applicationCode!='haierbusiness-banquet'" title="部门">
            <deptSelectM label="预算信息" :value="financialName" @chose="selectDept" />
          </van-tab> -->
        </van-tabs>

        <!-- 选择主体弹窗 -->
        <van-popup v-model:show="showMainInfoBox" position="bottom">
          <div class="out-pop" style="height: 100vh">
            <van-sticky>
              <div style="background: #fff; padding-bottom: 10px">
                <van-nav-bar :title="'选择主体信息'"  left-arrow @click-left="closeMainInfoBox">
                  <template #left>
                    <van-icon name="arrow-left" color="#000" size="24" />
                  </template>
                  <template #right>
                    <div class="color-main"></div>
                  </template>
                </van-nav-bar>
              </div>
            </van-sticky>
            <van-list>
              <div
                v-if="userAbCode.length == 0"
                style="height: 66vh"
                class="flex align-items-center justify-content-center"
              >
              <van-empty description="暂无主体信息" />
              </div>
              <template v-else>
                <van-radio-group  v-model="selectCode">
                <van-cell v-for="(item, index) in userAbCode" :key="index" @click="selectMainInfo(item)">
                  <template #icon>
                    <van-radio
                      :name="item.performGroupCode"
                      style="width:30px;"
                    />
                  </template>
                  <div class="flex ">
                      <div class="user-name color-main">
                        <p>执行主体：{{ item.performGroupCode}} </p>
                        <p>预算主体：{{ item.budgetGroupCode }} </p>
                        <p>来源系统： {{  item.systemCode}}</p>
                  </div>
                  </div>
                </van-cell>
                <div v-if="type=='hbc2'" style="margin-top:20px;font-size:12px;color:brown;padding:10px 20px;">集团平台部门请选择ORG，智家选择BCC，其他领域根据实际情况选择对应组织</div>
              </van-radio-group >
              </template>
            </van-list>
          </div>
        </van-popup>

        <van-field
            v-model="budgetGroupName"
            input-align="right"
            :disabled="true"
            name="预算主体"
            label="预算主体"
            is-link
            @click="showMainInfoBox = true"
            :rules="[{ required: true, message: '请填写预算主体' }]"
          />
          <van-field
            v-model="legalPersonName"
            is-link
            readonly
            input-align="right"
            label-class="czfr"
            :label-width="60"
            name="出账法人"
            label="出账法人"
            placeholder="请选择出账法人"
            @click="showPicker = true"
          />
          <van-field
            v-model="leftAmt"
            :disabled="true"
            input-align="right"
            name="预算金额"
            label="预算金额"
            :rules="[{ required: true, message: '请填写预算金额' }]"
          />
          <!-- 隐藏其他预算信息 -->
          <div v-show="false">
            <van-field
              v-model="budgetSystem"
              input-align="right"
              :disabled="true"
              name="预算系统"
              label="预算系统"
              @click-right-icon="show=true"
              :right-icon="budgetSystem&&budgets&&budgets.length>1?'exchange':''"
              :rules="[{ required: true, message: '请填写预算系统' }]"
            />
            <van-field
              v-model="projectName"
              v-if="projectName"
              input-align="right"
              :disabled="true"
              name="项目名称"
              label="项目名称"
            />
            <van-field
              v-model="performName"
              input-align="right"
              :disabled="true"
              name="执行主体"
              label="执行主体"
              :rules="[{ required: true, message: '请填写执行主体' }]"
            />
          
            <van-field
              v-model="feeItemName"
              :disabled="true"
              input-align="right"
              name="feeItem"
              label="费用科目"
            />
            
            <van-field
              v-model="costCenterName"
              input-align="right"
              :disabled="true"
              name="成本中心"
              label="成本中心"
              :rules="[{ required: true, message: '请填写成本中心' }]"
            />
        </div>
    </van-form>
  </div>
  <!-- 出账弹窗  -->
  <van-popup v-model:show="showPicker" position="bottom">
    <van-picker
      :columns="legalPersonOptions"
      @confirm="onConfirm"
      @cancel="showPicker = false"
      :columns-field-names="customFieldName"
    />
  </van-popup>
  <!-- 切换预算系统主题 -->
  <van-action-sheet
    v-model:show="show"
    title="切换预算系统主体"
    :actions="budgets"
    cancel-text="取消"
    close-on-click-action
    @select="onSelect"
  />
</template>

<style scoped lang="less">
:deep(.van-tabs__line ){
  width: 125px;
  height: 4px;
  background: linear-gradient( 180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.1) 100%), #2681FF;
}
:deep(.van-tabs__content){
  padding-top:10px;
}
:deep(.czfr){
  flex: none;
}
</style>

<style>
:root:root {
  --van-button-primary-background: #0073e5;
  --van-radio-checked-icon-color: #0073e5;
  --van-password-input-background: #f2f2f2;
}

.color-main {
    color: #0073e5;
  }

.out-pop {
  .main {
    flex: 1;
  }
  .img-name {
    width: 30px;
    height: 30px;
    
    color: #fff;
    font-size: 10px;
    border-radius: 30px;
  }
  
  .blue {
    background-color: #0073e5;
  }
  .yellow {
    background-color: #faad14;
  }
  .add-person {
    margin: 16px;
    margin-bottom: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
    font-size: 12px;
  }
  .user-name {
    text-align: left;
    font-size: 12px;
  }
  .phone {
    text-align: left;
    color: #8c8c8c;
  }
}
</style>