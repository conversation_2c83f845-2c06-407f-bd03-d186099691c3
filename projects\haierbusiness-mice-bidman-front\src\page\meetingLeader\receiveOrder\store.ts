import { defineStore } from "pinia"
import { miceBidManNoticeApi } from '@haierbusiness-front/apis';
import { MiceBidManNotice } from '@haierbusiness-front/common-libs';
import { message } from "ant-design-vue";

interface State {
  searchValues: MiceBidManNotice,
  noticeDetail: MiceBidManNotice,
  noticeList: MiceBidManNotice[],
  noticeListTotal: number,
}
export const defaultNoticeDetail = {
  id: void 0,
  title: '',
  effectScope: void 0,
  contentForm: void 0,
  informContent: void 0,
  sort: 0,
  state: void 0,
  isWindow: false,
}

export const defaultSearchValues = {
  title: '',
  effectScope: void 0,
  state: void 0,
  createName: '',
}
export const useReceiveOrderStore = defineStore('receiveOrder', {
  state: (): State => ({
    searchValues: defaultSearchValues,
    noticeDetail: defaultNoticeDetail,
    noticeList: [],
    noticeListTotal: 0,
  }),
  getters: {
    effectScopeOptions: () => [{ label: '正常', value: 10 }, { label: '隐藏', value: 11 }],
    stateOptions: () => [{ label: '服务商', value: 10 }, { label: '用户', value: 11 }, { label: '顾问', value: 12 }],
    contentFormOptions: () => [{ label: '文本', value: 1 }, { label: '链接', value: 2 }],
  },
  actions: {
    async getNoticeList(params = {}) {
      try {
        const res = await miceBidManNoticeApi.getList(params);
        this.noticeList = res.records!;
        this.noticeListTotal = res.total!;
      } catch (e) {
        console.log(e)
      }
    },
    async getNoticeDetail(params = {}) {
      try {
        const res = await miceBidManNoticeApi.getDetail(params);
        this.noticeDetail = res;
      } catch (e) {
        console.log(e)
      }
    },
    async addNotice(params = {}) {
      try {
        const res = await miceBidManNoticeApi.add(params);
        message.success('新增成功');
      } catch (e) {
        console.log(e)
      }
    },
    async editNotice(params = {}) {
      try {
        const res = await miceBidManNoticeApi.edit(params);
        message.success('编辑成功');
      } catch (e) {
        console.log(e)
      }
    },
    async deleteNotice(params = {}) {
      try {
        const res = await miceBidManNoticeApi.delete(params);
        message.success('删除成功');
      } catch (e) {
        console.log(e)
      }
    },
  },
})