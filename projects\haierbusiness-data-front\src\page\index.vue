<script setup lang="ts">
import {onMounted, ref} from 'vue';
import EManage from '@haierbusiness-front/components/manange/EManange.vue';
import {storeToRefs} from 'pinia';
import {applicationStore} from '@haierbusiness-front/utils/src/store/applicaiton';
import layout from './layout.vue';
import {onBeforeRouteUpdate, useRoute, useRouter} from 'vue-router';
import aiAssistant from '@haierbusiness-front/components/aiAssistant/AiAssistant.vue';
import {resolveParam} from "@haierbusiness-front/utils";

const store = applicationStore()
const {resource} = storeToRefs(store)
const {loginUser} = storeToRefs(store);
const showLayout = ref(false)
const showEManage = ref(false)
let router = useRouter()

onBeforeRouteUpdate((to) => {
  if (to.path.indexOf('board') > -1 || to.path.includes('/data/report/permission/apply') || to.path.includes('/data/report/permission/detail')) {
    showLayout.value = true
    showEManage.value = false
  } else {
    showEManage.value = true
    showLayout.value = false
  }
  console.log('onBeforeRouteUpdate', to.path);
});
onMounted(async () => {
  if (location.href.indexOf('board') > -1 || location.href.includes('/data/report/permission/apply') || location.href.includes('/data/report/permission/detail')) {
    showLayout.value = true
  } else {
    showEManage.value = true
  }
})
const route = useRoute();
const aidev = resolveParam(route.query.aidev as string);
</script>

<template>
  <ai-assistant v-if="aidev == 'true'"></ai-assistant>
  <div style="height:100vh;min-height:280px">
    <layout v-if="showLayout"></layout>
    <e-manage v-if="showEManage" :param="resource"></e-manage>
  </div>
</template>

<style scoped lang="less">
:root {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}
</style>
