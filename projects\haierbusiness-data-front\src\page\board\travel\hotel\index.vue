<template>
  <div class="bg">
    <div class="main">
      <div class="title">差旅酒店业务总览图</div>
      <Picker />
      <div class="content">
        <h-row type="flex" :gutter="[20, 20]" v-for="(item, i) in data" :key="i">
          <template v-if="item.layout == 'row'">
            <common
              v-for="(each, index) in item.list"
              :key="index"
              :data="each"
              :layout="item.layout"
            >
            </common>
          </template>
          <template v-else>
            <h-col
              v-for="(each, index) in item.list"
              :key="index"
              :span="index == 1 ? 12 : 6"
            >
              <common :data="each" :layout="item.layout"></common>
            </h-col>
          </template>
        </h-row>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
} from "ant-design-vue";
// import VScaleScreen from 'v-scale-screen';
import "element-plus/theme-chalk/dark/css-vars.css";
import Picker from "../../components/picker.vue";
import { ref, onMounted } from "vue";
import dayjs from "dayjs";

import common from "./components/common.vue";
import { boardApi } from "@haierbusiness-front/apis";
const data = ref<any>([]);
onMounted(async () => {
  const res = await boardApi.getBulletinBoardConfig({ url: "/data/board/travel/hotel" });
  if (res.bulletinBoardJson) {
    data.value = JSON.parse(res.bulletinBoardJson);
  }
});

function startRefresh() {
  var now = new Date(); // 获取当前时间
  var nextRefresh = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 6, 0, 0); // 设置下一个刷新时间为今天的6点
  if (now.getHours() >= 6) {
    nextRefresh.setDate(now.getDate() + 1); // 如果当前时间已经过了6点，则下一个刷新时间设定为明天的6点
  }

  var timeToRefresh = nextRefresh.getTime() - now.getTime(); // 计算距离下一个刷新时间还有多长时间（单位：毫秒）

  setTimeout(function () {
    refreshData(); // 刷新数据
    setInterval(refreshData, 12 * 60 * 60 * 1000); // 设置每隔12小时刷新一次数据
  }, timeToRefresh);
}
onMounted(() => {
  startRefresh();
});
</script>
<style scoped lang="less">
@import url(../../main.less);
</style>
