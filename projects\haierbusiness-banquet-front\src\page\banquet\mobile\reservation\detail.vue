<template>
  <div class="order-detail" style="padding-top: 20px; padding-bottom: 40px;">
    
    <van-cell-group inset style="background-color: rgba(0,0,0,0);" class="mb-10">
      <van-cell title="订单编号" value-class="large-value" :value="detail?.orderBookingCode" class="order-title-cell" />
      <van-cell title="签单人信息" :value="`${detail?.signerName}(${detail?.signerCode})`" />
      <van-cell title="关联申请单" value-class="large-value" :value="detail?.orderCode" />
      <van-cell title="订单类型" :value="BanquetApplicationTypeEnum[detail?.sceneType]" />
      <van-cell title="就餐城市" :value="detail?.mealLocationCity" />
      <van-cell title="餐厅名称" :value="detail?.restaurantName" />
      <van-cell title="餐厅电话" :value="detail?.restaurantPhone" />
      <van-cell title="餐厅地址" :value="detail?.restaurantLocation" />
      
    </van-cell-group>

    <van-cell-group inset style="background-color: rgba(0,0,0,0);" v-if="detail?.sceneType == 1">
      <van-cell title="签到人" :value="`${detail?.checkinPersonName}(${detail?.checkinPersonCode})`" />
      <van-cell title="签到时间" :value="detail?.checkinTime" />
      <van-cell title="签到地址" :value="detail?.checkinLocation" />
      <!-- <van-cell title="实际支付金额" :value="`${detail?.eatingTime}元`" /> -->
      <van-cell title="支付人信息" :value="`${detail?.payerPersonName}(${detail?.payerCode})`" />
      <van-cell title="支付时间" :value="detail?.payTime" />
      <van-cell title="实际支付金额" value-class="large-value">
        <template #value>
          <div>{{ detail?.actualPaymentAmount }}</div>
          <div>{{ `(企业:${detail?.entPayAmount || 0}元;个人:${detail?.staffPayAmount || 0}元)` }}</div>
        </template> 
      </van-cell>
    </van-cell-group>

    <van-cell-group inset style="background-color: rgba(0,0,0,0);" v-else>
      <van-cell title="收货人信息" :value="`${detail?.checkinPersonName}(${detail?.checkinPersonCode})`" />
      <van-cell title="配送时间" :value="detail?.checkinTime" />
      <van-cell title="配送地址" :value="detail?.checkinLocation" />
      <!-- <van-cell title="实际支付金额" :value="`${detail?.eatingTime}元`" /> -->
      <van-cell title="支付人信息" :value="`${detail?.payerPersonName}(${detail?.payerCode})`" />
      <van-cell title="支付时间" :value="detail?.payTime" />
      <van-cell title="实际支付金额" value-class="large-value">
        <template #value>
          <div>{{ detail?.actualPaymentAmount }}</div>
          <div>{{ `(企业:${detail?.entPayAmount || 0}元;个人:${detail?.staffPayAmount || 0}元)` }}</div>
        </template> 
      </van-cell>
    </van-cell-group>



  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import type { Ref } from 'vue';

import { banquetReservationApi } from '@haierbusiness-front/apis';

import {
  RHotelParams,
  RpayType,
  ROrderPayMentStateEnum,
  ROrderPayMentStateEnumColor,
  ROrderApprovalStateEnum,
  ROrderApprovalStateEnumColor,
  ROrderStateEnum,
  ROrderStateEnumColor,
  IReservationRes,
  BanquetApplicationTypeEnum
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { Item } from 'ant-design-vue/es/menu';
const router = getCurrentRouter();

const route = ref(getCurrentRoute());

const id = route.value?.query?.id;

const store = applicationStore();

const { loginUser } = storeToRefs(store);

const detail = ref<IReservationRes>();

const getDetail = (id: number) => {

  banquetReservationApi.get(id).then((res) => {
    detail.value = res;
  });
};

const goBack = () => {
  router.back(-1);
};


watch(
  () => id,
  (val: number) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);
</script>

<style lang="less" scoped>
@import url(../common.less);


:deep(.large-value) {
  min-width: 70%;
}
</style>