<script setup lang="ts">
import { showFailToast, But<PERSON> as <PERSON><PERSON><PERSON><PERSON>, showDialog } from 'vant';
import 'vant/es/toast/style'
import 'vant/es/button/style'
import 'vant/es/dialog/style'

import finishIcon from '@/assets/image/finish.png';
import runningIcon from '@/assets/image/running.png';
import logo from '@/assets/image/logo.png';
import { Tag as hTag, Avatar as hAvatar, Step as hStep, Steps as hSteps, Divider as hDivider, Space as hSpace, But<PERSON> as hButton, Col as hCol, Result as hResult, Row as hRow, TabPane as hTabPane, Tabs as hTabs, message } from 'ant-design-vue';
import { CheckCircleOutlined, MinusCircleOutlined, StopOutlined, DownOutlined, ExclamationCircleOutlined, PlusOutlined, UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { processApi } from '@haierbusiness-front/apis';
import { computed, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { ProcessStepStateConstant, ProcessStateConstant, IProcessRecordStep, ProcessStepOperatorStateConstant } from '@haierbusiness-front/common-libs';
import DetailsPc from './detailsPc.vue';
import DetailsMobile from './detailsMobile.vue';
import { isMobile } from '@haierbusiness-front/utils';

</script>

<template>
  <details-mobile v-if="isMobile()"></details-mobile>
  <details-pc v-else></details-pc>
</template>

