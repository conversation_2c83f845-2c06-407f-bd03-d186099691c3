<template>
  <div class="mine">
    <!-- <van-nav-bar  :fixed="true"  title="个人中心" left-arrow style="  z-index: 11;">
      <template #left>
        <van-icon name="arrow-left" color="#000" @click="goBack" />
      </template>
    </van-nav-bar> -->

    <!-- 背景色块 -->
    <!-- <div class="mine-top-bg"></div> -->

    <div class="mine-content">
      <!-- 用户 -->
      <div class="mine-user flex mb-10">
        <div class="main-user-img mr-10">{{ loginUser?.nickName?.slice(-2) }}</div>
        <div class="main-user-name">
          <div class="user-name mb-5">{{ loginUser?.nickName }}</div>
          <div class="user-lable flex">
            <div>{{ loginUser?.username }}</div>
            <van-divider vertical class="middle" />
            <div>{{ loginUser?.departmentName }}</div>
          </div>
        </div>
        <div class="main-user-news">
          <van-icon @click="goNewsList" size="20" :name="bellringIcon"  />
        </div>
      </div>
      <!-- 申请单 -->
       <div class="mine-common-box flex mb-10">
        <div class="apply-title mb-5 flex justify-content-between">
          <div class="left">我的申请单</div>
          <div class="right" @click="goApplyList('')">查看全部</div>
        </div>
        <div class="mine-common-content flex">
          
          
          <div class="mine-content-item flex" @click="goApplyList('15')">
            <van-icon :name="submitIcon" size="24" :badge="homeCount?.submittedCount || ''"/>
            <div class="mt-5 font-size-10">待占预算</div>
          </div>

          <div class="mine-content-item flex" @click="goApplyList('20')">
            <van-icon :name="dhxIcon" size="24" :badge="homeCount?.processCount || ''" />
            <div class="mt-5 font-size-10">审批中</div>
          </div>

          

          <div class="mine-content-item flex" @click="goApplyList('30')">
            <van-icon :name="complateIcon" size="24" :badge="homeCount?.effectiveCount || ''"/>
            <div class="mt-5 font-size-10">已生效</div>
          </div>

          <div class="mine-content-item flex" @click="goApplyList('25')">
            <van-icon :name="applyIcon" size="24" :badge="homeCount?.pendingCount || ''" />
            <div class="mt-5 font-size-10">待生效</div>
          </div>

          <!-- <div class="mine-content-item flex" @click="goApplyList('40')">
            <van-icon :name="closeIcon" size="24" :badge="homeCount?.closeCount || ''"/>
            <div class="mt-5 font-size-10">已关闭</div>
          </div> -->
        </div>


        
       </div>
       <!-- 预订单 -->
       <div class="mine-common-box flex mb-10">
        <div class="apply-title mb-5 flex justify-content-between">
          <div class="left">预订单</div>
          <div class="right" @click="goReservationList('')">查看全部</div>
        </div>
        <div class="mine-common-content flex" style="justify-content: flex-start;">
          <div class="mine-content-item flex" style="max-width: 25% !important;" @click="goReservationList(1)">
            <van-icon :name="dhxIcon" size="24"  />
            <div class="mt-5 font-size-10">待核销</div>
          </div>

          <div class="mine-content-item flex" style="max-width: 25% !important;" @click="goReservationList(2)">
            <van-icon :name="wcIcon" size="24"  />
            <div class="mt-5 font-size-10">已完成</div>
          </div>

          <div class="mine-content-item flex" style="max-width: 25% !important;" @click="goReservationList(3)">
            <van-icon :name="tkIcon" size="24"  />
            <div class="mt-5 font-size-10">已取消</div>
          </div>
        </div>

       </div>

       <!-- 退款 -->
       <div class="mine-common-box flex mb-10">
        <div class="apply-title mb-5 flex justify-content-between">
          <div class="left">退款</div>
        </div>
        <div class="mine-common-content flex" style="justify-content: flex-start;">
          <div class="mine-content-item flex" style="max-width: 25% !important;" @click="goRefundList">
            <van-icon :name="refundIcon" size="24" />
            <div class="mt-5 font-size-10">退款单</div>
          </div>
        </div>

       </div>

       <!-- 消费记录 -->
       <!-- <div class="mine-common-box flex mb-10">
        <div class="apply-title mb-5 flex justify-content-between">
          <div class="left">消费记录</div>
        </div>
        <div class="mine-common-content flex" style="justify-content: flex-start;">
          <div class="mine-content-item flex" style="max-width: 33% !important;" @click="goToMt">
            <van-icon :name="refundIcon" size="24" />
            <div class="mt-5">消费记录</div>
          </div>
        </div>

       </div> -->

       <!-- 隐私协议 -->
       

       <div class="mine-common-box flex mb-10" style="height: auto;">
        
        <van-cell-group >
          <van-cell @click="showCnxz = true" is-link style="padding-left: 0;padding-right: 0;">
            <template #title>
              <div class="flex align-items-center">
                <van-icon :name="promiseIcon" size="24" class="mr-10" />
                <span>承诺须知</span>
              </div>
            </template>
          </van-cell>
          <van-cell @click="showYsxy = true" is-link style="padding-left: 0;padding-right: 0;">
            <template #title>
              <div class="flex align-items-center">
                <van-icon :name="lockIcon" size="24" class="mr-10" />
                <span>隐私协议</span>
              </div>
              
            </template>
          </van-cell>
        </van-cell-group>
       </div>
    </div>

 

   
    <!-- 底部 -->
    <van-tabbar v-model="active" route class="tabbar-bottom">
      <van-tabbar-item replace to="/banquet/home">
        <span>首页</span>
        <template #icon="props">
          <img :src="props.active ? bookIcon.active : bookIcon.inactive" />
        </template>
      </van-tabbar-item>

      <van-tabbar-item replace to="/banquet/mine">
        <span>我的</span>
        <template #icon="props">
          <img :src="props.active ? orderIcon.active : orderIcon.inactive" />
        </template>
      </van-tabbar-item>
    </van-tabbar>

    <!-- 承诺须知 -->
    <van-action-sheet :closeable="false" v-model:show="showCnxz" title="承诺须知">
      <div class="action-main flex flex-column align-items-center" style="overflow: hidden;">
        <div style="overflow: auto; max-height: 300px;" class=" action-content mb-20 width100" v-html="promiseVal?.content"></div>
        <van-button style="width: 200px;" @click="showCnxz = false" round type="primary">我已知晓</van-button>
      </div>
    </van-action-sheet>

    <!-- 隐私协议 -->
    <van-action-sheet :closeable="false" v-model:show="showYsxy" title="隐私协议">
      <div class="action-main flex flex-column align-items-center">
        <div style="overflow: auto;max-height: 300px;" class=" action-content mb-20 width100" v-html="privacyVal?.content"></div>
        <van-button style="width: 200px;" @click="showYsxy = false" round type="primary">我已知晓</van-button>
      </div>
    </van-action-sheet>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch, reactive, computed } from 'vue';
import type { Ref } from 'vue';
import { BHomepagePicRes,BHomeGetCountRes} from '@haierbusiness-front/common-libs';
import { banquetApi } from '@haierbusiness-front/apis';
import { showSuccessToast, showFailToast, showToast } from 'vant';

import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute, getEnumOptions } from '@haierbusiness-front/utils';
// import { useAppStore } from "@/store"


const store = applicationStore();

const { loginUser } = storeToRefs(store);


const router = getCurrentRouter();

const route = ref(getCurrentRoute());

const active = ref(0);

const bellringIcon = new URL('@/assets/image/banquet/mine/bellring_on.png', import.meta.url).href

const applyIcon = new URL('@/assets/image/banquet/mine/apply.png', import.meta.url).href
const submitIcon = new URL('@/assets/image/banquet/mine/submitIcon.png', import.meta.url).href
const closeIcon = new URL('@/assets/image/banquet/mine/close.png', import.meta.url).href
const complateIcon = new URL('@/assets/image/banquet/mine/complate.png', import.meta.url).href
const foodIcon = new URL('@/assets/image/banquet/mine/food.png', import.meta.url).href
const lockIcon = new URL('@/assets/image/banquet/mine/lock.png', import.meta.url).href
const promiseIcon = new URL('@/assets/image/banquet/mine/promise.png', import.meta.url).href
const refundIcon = new URL('@/assets/image/banquet/mine/refund.png', import.meta.url).href
const takeawayIcon = new URL('@/assets/image/banquet/mine/takeaway.png', import.meta.url).href
const order2Icon = new URL('@/assets/image/banquet/mine/order.png', import.meta.url).href
const dhxIcon = new URL('@/assets/image/banquet/mine/dhx.png', import.meta.url).href
const tkIcon = new URL('@/assets/image/banquet/mine/tk.png', import.meta.url).href
const wcIcon = new URL('@/assets/image/banquet/mine/wc.png', import.meta.url).href

const bookIcon = {
  active: new URL('@/assets/image/banquet/home/<USER>', import.meta.url).href,
  inactive: new URL('@/assets/image/banquet/home/<USER>', import.meta.url).href,
};
const orderIcon = {
  active: new URL('@/assets/image/banquet/home/<USER>', import.meta.url).href,
  inactive: new URL('@/assets/image/banquet/home/<USER>', import.meta.url).href,
};


const goBack = () => {
  router.back(-1);
};


// 获取首页数量
const homeCount = ref<BHomeGetCountRes>()
const getHomeCount= () => {
 
  banquetApi.getCount().then(res => {
    homeCount.value = res
  })
}

// 跳转申请单列表
const goApplyList = (val:number | string) => {
  router.push('/banquet/apply/list?status='+val)
};

// 跳转预订单列表
const goReservationList = (val:number | string) => {
  
  router.push('/banquet/reservation/list?status='+val)
};

// 退款单列表
const goRefundList = () => {
  router.push('/banquet/refund/list')
};

// 通知通告
const goNewsList = () => {
  router.push('/banquet/news/list')

};

const goToMt = () => {
  
  const params = {
    type: 'STAFF_ORDER_LIST',
    bizParam: {}
  }
  banquetApi.clientInvokeLogin(params).then(res => {
    setTimeout(() => {
    }, 3000);
    window.location.href = res
  })
}


const showCnxz = ref<boolean>(false)
const showYsxy = ref<boolean>(false)
// 获取承诺须知
const promiseVal = ref<BHomepagePicRes>()
const getPromise= () => {
  banquetApi.getPromise().then(res => {
    promiseVal.value = res
  })
}

// 获取隐私承诺
const privacyVal = ref<BHomepagePicRes>()
const getPrivacy= () => {
 
  banquetApi.getPrivacy().then(res => {
    privacyVal.value = res
    // privacyVal.value.content = JSON.parse(privacyVal.value?.content)
  })
}

onMounted(() => {
  getHomeCount()

  getPrivacy()
  getPromise()
})

</script>

<style scoped lang="less">
@import url(./common.less);
.mine {
  background: #FAFBFD;
  position: relative;
  background-image: url('@/assets/image/banquet/home/<USER>');
  background-size: contain;
  background-repeat: no-repeat;
}
.action-main {
  padding: 10px ;
  :deep(img) {
    max-width: 100% !important;
    height: auto !important;
  }
}
.tabbar-bottom {
  z-index: 11;
}
.mine-top-bg {
  position: absolute;
  z-index: 5;
  width: 100%;
  height: 150px;
  background: linear-gradient( 360deg, #C9E7FF 0%, #2681FF 100%);
  border-radius: 0px 0px 30px 30px;
}
.mine-content {
  position: relative;
  z-index: 10;
  width: 100vw;
  min-height: 100vh;
  padding: 30px 10px 40px;
  .mine-user {
    display: flex;
    .main-user-img {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 40px;
      font-size: 13px;
      color: #fff;
      background: #56d8bf;
      margin-right: 10px;
    }
    .main-user-name {
      display: flex;
      flex-direction: column;
      justify-content: center;
      .user-name {
        font-size: 12px;
        font-weight: bold;
      }
      .user-lable {
        color: rgba(0,0,0,0.7);
        font-size: 10px;
      }
    }
    .main-user-news {
      flex: 1;
      text-align: right;
    }
  }
  .mine-common-box {
    height: 90px;
    background: #FFFFFF;
    box-shadow: 0px 8px 8px 0px rgba(157,178,232,0.1);
    border-radius: 10px 10px 10px 10px;
    padding: 12px 12px 6px 12px;
    flex-direction: column;
    .apply-title {
      .left {
        font-family: 'Open Sans-Bold';
        font-weight: bold;
        font-size: 12px;
      }
      .right{
        font-size: 10px;
        font-weight: 400;
        font-family: Open Sans, Open Sans;
        color: #2681FF;
      }
    }
    

    .mine-common-content {
      flex: 1;
      align-items: center;
      justify-content: space-around;
      .mine-content-item {
        flex-direction: column;
        align-items: center;
        justify-content: center;
        flex: 1;
      }
    }
  }
  
}
</style>

