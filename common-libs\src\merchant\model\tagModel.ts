import { IPageRequest } from "../../basic";

export class ITagFilter extends IPageRequest {
    businessType?:string
    tagName?:string
    state?:number
}


export class ITag {
    /* */
    id?: number;

    /* */
    businessType?: string;

    /* */
    tagName?: string;

    /* */
    state?: number;

    /* */
    description?: string;

    /* */
    deleted?: number;
}

export interface BusinessTypeEnums {
    key: string,
    value: string
}

export interface SetTag {
    /* */
    id: number;

    /* */
    businessType: string;

    /* */
    tagName: string;

    /* */
    state: number;

    /* */
    description: number;
}