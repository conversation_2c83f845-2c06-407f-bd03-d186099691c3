<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Input as hInput,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  Alert,
  Tooltip
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { DownOutlined,  UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { payApi } from '@haierbusiness-front/apis';
import {
  PayNotifyStateConstant,
  PayTypeChildConstant,
  ICoinFlowingWaterRequest,
  PayStatusConstant,
  PayTypeConstant
} from '@haierbusiness-front/common-libs';
import { getCurrentRouter , errorModal, routerParam } from '@haierbusiness-front/utils';
import dayjs, { Dayjs } from 'dayjs';
import { Ref, computed, getCurrentInstance, onMounted, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { toNumber } from "lodash"

const router = getCurrentRouter()

const searchType = ref<number>()

onMounted(async () => {
    const currentId = router.currentRoute.value.query?.searchType
    searchType.value = toNumber(currentId)
    searchParam.value.searchType = toNumber(currentId)
})

const columns: ColumnType[] = [
  {
    title: '支付(退款)单号',
    dataIndex: 'mainOrderCode',
    fixed: 'left',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '业务单号',
    dataIndex: 'businessCode',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '福利积分外部单号',
    dataIndex: 'outerOrder',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '类型',
    dataIndex: 'transactionType',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '支付账户',
    dataIndex: 'transactionFrom',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '收款方',
    dataIndex: 'transactionTo',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '交易金额',
    dataIndex: 'amount',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '业务应用Code',
    dataIndex: 'applicationCode',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '发起时间',
    dataIndex: 'transactionTime',
    width: '180px',
    align: 'center',
    ellipsis: true
  }
];
const searchParam = ref<ICoinFlowingWaterRequest>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(payApi.getCoinFlowingWater, {
  manual: true
});

const {
  data: balance,
  run: balancelistApiRun,
} = useRequest(payApi.getBalanceByUsername, {
  manual: true
});

const {
  data: exportListData,
  run: exportListApiRun,
  loading: exportListLoading,
} = useRequest(payApi.exportCoinFlowingWaterList);

const reset = () => {
  startBeginAndEnd.value = undefined
  searchParam.value = {
    searchType: searchType.value
  }
}

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  if(searchParam.value.id) {
    listApiRun({
      ...searchParam.value,
      pageNum: pag.current,
      pageSize: pag.pageSize,
    });
    balancelistApiRun(searchParam.value.id, searchParam.value.searchType!)
  }
};


const advancedSearchVisible = ref(false)
const gotoAdvancedSearch = () => {
  if (advancedSearchVisible.value) {
    advancedSearchVisible.value = false
  } else {
    advancedSearchVisible.value = true
  }
}

const startBeginAndEnd = ref<[Dayjs, Dayjs]>()
watch(() => startBeginAndEnd.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.timeBegin = dayjs(n[0]).format('YYYY-MM-DD')
    searchParam.value.timeEnd = dayjs(n[1]).format('YYYY-MM-DD')
  } else {
    searchParam.value.timeBegin = undefined
    searchParam.value.timeEnd = undefined
  }
})

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="id"><span style="color:#ff4d4f;">* &nbsp;</span>工号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="id" v-model:value="searchParam.id" placeholder="请输入工号" autocomplete="off"
              allow-clear />
          </h-col>
          <!-- <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchType">账户类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="searchType" v-model:value="searchParam.searchType" style="width: 100%" allow-clear>
              <h-select-option :value="1">超市福利积分</h-select-option>
              <h-select-option :value="2">机票福利积分</h-select-option>
            </h-select>
          </h-col> -->
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="type">类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="type" v-model:value="searchParam.type" style="width: 100%" allow-clear>
              <h-select-option :value="0">所有</h-select-option>
              <h-select-option :value="1">充值</h-select-option>
              <h-select-option :value="2">消费</h-select-option>
              <h-select-option :value="3">退款</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;">
            <label for="startBeginAndEnd">发起日期：</label>
          </h-col>
          <h-col :span="5">
            <h-range-picker v-model:value="startBeginAndEnd" value-format="YYYY-MM-DD" style="width: 100%;" />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button type="primary" style="margin-right: 10px" :loading="exportListLoading"
              @click="exportListApiRun(searchParam);" :disabled="(!searchParam.searchType || !searchParam.id)">
              <UploadOutlined />
              导出
            </h-button>
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <template v-if="!searchParam.id">
              <a-tooltip title="请填写工号后再点击查询" color="#1677ff" >
                <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })" :disabled="(!searchParam.searchType || !searchParam.id)">
                  <SearchOutlined />
                  查询
                </h-button>
              </a-tooltip>
            </template>
            <template v-else>
              <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })" :disabled="(!searchParam.searchType || !searchParam.id)">
                <SearchOutlined />
                查询
              </h-button>
            </template>
            
            

          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ x: 1550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #title>
            <a-alert :message="'余额：' + ((balance && balance.balance) ? '¥' + balance.balance : 0)" type="info" show-icon />
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'transactionType'">
              {{ record.transactionType === '1' ? '充值' : '' }}
              {{ record.transactionType === '2' ? '消费' : '' }}
              {{ record.transactionType === '3' ? '退款' : '' }}
            </template>
            <template v-if="column.dataIndex === 'amount'">
              {{ record.transactionType === '2' ? '-' + record.amount : record.amount}}
            </template>
            <template v-if="column.dataIndex === '_operator'">
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
