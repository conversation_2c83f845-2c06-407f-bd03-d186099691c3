import {
    ICompositionSearchCompleteStatusRequest,
    ICompositionSearchCompleteStatusResponse,
    ICompositionSearchStatusRequest,
    ICompositionSearchStatusResponse,
    IPayHeader,
    IPayRefreshResponse,
    IPayRequest,
    IPayResponse
} from '@haierbusiness-front/common-libs'
import {get, post} from '../request'

export const compositionPayApi = {
    /**
     * c扫b支付
     */
    csb: (params: IPayRequest, header: IPayHeader): Promise<IPayResponse> => {
        return post('pay/api/composition_pay/csb', params,
            {
                "hb-nonce": header.nonce,
                "hb-timestamp": header.timestamp,
                "hb-sign": header.sign,
                "hb-application-code": header.applicationCode,
                "hb-excludes": header.excludes,
            }
        )
    },

    /**
     * H5聚合支付
     */
    h5: (params: IPayRequest, header: IPayHeader): Promise<IPayResponse> => {
        return post('pay/api/composition_pay/h5', params,
            {
                "hb-nonce": header.nonce,
                "hb-timestamp": header.timestamp,
                "hb-sign": header.sign,
                "hb-application-code": header.applicationCode,
                "hb-excludes": header.excludes,
            }
        )
    },

    /**
     * c扫b刷新二维码
     */
    csbRefresh: (params: IPayRefreshResponse): Promise<IPayResponse> => {
        return post('pay/api/composition_pay/refresh', params)
    },

    /**
     * 查询支付状态
     */
    searchStatus: (params: ICompositionSearchStatusRequest): Promise<ICompositionSearchStatusResponse> => {
        return get('pay/api/composition_pay/search_complete_status', params)
    },

    /**
     * 查询支付完成状态 - 长轮询
     */
    searchCompleteStatus: (params: ICompositionSearchCompleteStatusRequest): Promise<ICompositionSearchCompleteStatusResponse> => {
        return get('pay/api/composition_pay/search_complete_status', params)
    },
}