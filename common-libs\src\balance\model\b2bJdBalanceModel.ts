import { IPageRequest } from "../../basic";

export interface IJdB2bListRequest extends IPageRequest{
    /**
     * 汇总单号
     */
    accountCode?:string;

    /**
     * 结算状态
     */
    state?:number;

    
    /**
     * 商户通单号
     */
    cvpCode?:string;

    /**
     * 预算释放通知商户通状态
     * 0: 未通知 1: 通知成功 2: 通知异常
     */
    notifiedReleaseCvp?:number;

    /**
     * 推送异常
     * 1: 全部  2:未读
     */
    pushErrorState?:number;


    /**
     * 预算系统编码GEMS/BCC等
     */
    budgetSysCode?: string;

    /**
     * 供应商
     */
     providerCode?:string;

    /**
     * 账单所属年月yyyy-MM
     */
     periodYearMonth?:string;

    /**
     * 汇总开始日期
     */
     begin?:string;

    /**
     * 汇总结束日期
     */
     end?:string;

    /**
     * 预算部门
     */
    departmentCode?: string;

    departmentName?: string;

    /**
     * 结算单位
     */
    accountCompanyCode?: string;

    accountCompanyName?: string;

    /**
     * 客户编码
     */
    customCode?: string;

    customName?: string;

    /**
     * WBS
     */
    wbsCode?: string;

    wbsName?: string;

    /**
     * 研发项目
     */
    projectCode?: string;

    projectName?: string;

    /**
     * 地产项目
     */
    dcProjectCode?: string;

    dcProjectName?: string;

    /**
     * 地产分期
     */
    dcItemCode?: string;

    dcItemName?: string;

    /**
     * 费用项目
     */
    feeItem?: string;
    feeItemName?: string;

    /**
     * 支付单号
     */
     detailPaymentCode?: string;

    /**
     * 业务单号
     */
     detailBusinessCode?: string;

    /**
     * 退款支付单号
     */
     detailRefundPaymentCode?: string;

    /**
     * 退款业务单号
     */
     detailRefundBusinessCode?: string;

     /**
      * 预算单号
      */
     detailBudgetCode?: string;
}

export interface IJdB2bAccountRequest {
    /**
     * 供应商
     */
     providerCode?:string;

    /**
     * 账单所属年月yyyy-MM
     */
     periodYearMonth?:string;

    /**
     * 汇总开始日期
     */
     begin?:string;

    /**
     * 汇总结束日期
     */
     end?:string;
}

export interface IJdB2bConfirmRequest {
    /**
     * 需要结算的汇总单
     */
    accounts?:IJdB2bConfirmAccountsRequest[];
}

export interface IJdB2bRevokeConfirmRequest {
    accountCodes?:(string| undefined)[];
}

export interface IJdB2bConfirmAccountsRequest {
    /**
     * 汇总单号
     */
    accountCode?:string;
}


export interface IJdB2bDetailsRequest extends IPageRequest{
    /**
     * 汇总单号
     */
    accountCode?:string;

    /**
     * 支付单号
     */
     paymentCode?:string;

    /**
     * 业务单号
     */
     businessCode?:string;

    /**
     * 退款支付单号
     */
     refundPaymentCode?:string;

    /**
     * 退款业务单号
     */
     refundBusinessCode?:string;

    /**
     * 预算系统单号
     */
     budgetCode?:string;

    /**
     * 产品唯一id
     */
     productId?:string;

    /**
     * 产品名称
     */
     productName?:string;

    /**
     * 1: 已确认
     * 0: 未确认
     */
     confirmed?:string;
}



export interface IJdB2bConfirmBalanceOrderBudgetRequest {
    accountCode?:string;
}

export interface IJdB2bCancelRequest {
    accountCodes?:(string| undefined)[];
}

export interface IJdB2bMarkReadRequest {
    accountCodes?:(string| undefined)[];
}