<!-- 会议交接 -->
<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Input as hInput,
  Divider as hDivider,
  message,
  Modal as hModal,
} from 'ant-design-vue';
import { meetingHandoverApi } from '@haierbusiness-front/apis';
import { ref, watch, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { IUserInfo, IUserListRequest } from '@haierbusiness-front/common-libs';
import userSelect from '@haierbusiness-front/components/user/UserSelect.vue';
import { useRouter } from 'vue-router';

const { loginUser } = storeToRefs(applicationStore());
const router = useRouter();

const TextArea = hInput.TextArea;

// 审批流程相关
const businessProcess = import.meta.env.VITE_BUSINESS_PROCESS_URL;
const approvalModalShow = ref(false);
const approveCode = ref<string>(''); // 审批流程Code

// 经办人信息
const handlerInfo = ref({
  name: '',
  phone: '',
  email: '',
  jobNumber: '',
});

// 会议信息
const meetingInfo = ref({
  codes: [] as Array<{ value: string; label: string }>,
  code: undefined as string | undefined,
  id: '',
  name: '',
  time: '',
  city: '',
});

// 承接人信息
const transferInfo = ref({
  user: '',
  userCode: '',
  userPhone: '',
  userEmail: '',
  userComments: '',
  reason: '',
  jobNumber: '',
});

// 用户选择相关参数
const userSelectParams = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});

// 用户选择回调
const userNameChange = (userInfo: IUserInfo) => {
  if (!userInfo) {
    // 处理清空操作
    transferInfo.value.user = '';
    transferInfo.value.userCode = '';
    transferInfo.value.userPhone = '';
    transferInfo.value.userEmail = '';
    transferInfo.value.userComments = '';
    transferInfo.value.jobNumber = '';
    return;
  }

  transferInfo.value.user = userInfo.nickName || '';
  transferInfo.value.userCode = userInfo.username || '';
  transferInfo.value.userPhone = userInfo.phone || '';
  transferInfo.value.userEmail = userInfo.email || '';
  transferInfo.value.jobNumber = userInfo.username || '';
  fetchUserLineInfo(userInfo.username || '');
};

// 获取承接人直线信息
const fetchUserLineInfo = async (handoverCode: string) => {
  if (!handoverCode) return;

  try {
    const response = await meetingHandoverApi.getMeetUser({
      handoverCode,
    });
    console.log('response', response);

    if (response) {
      const responseData = response as any;
      transferInfo.value.userComments = responseData || '';
    }
  } catch (error) {
    console.error('获取承接人直线信息失败', error);
  }
};

// 初始化获取当前用户信息
onMounted(async () => {
  try {
    if (loginUser.value) {
      handlerInfo.value.name = loginUser.value.nickName || '';
      handlerInfo.value.phone = loginUser.value.phone || '';
      handlerInfo.value.email = loginUser.value.email || '';
      handlerInfo.value.jobNumber = loginUser.value.username || '';
    }
    // fetchMeetingCodes()
  } catch (error) {
    console.error('获取当前用户信息失败', error);
  }
});

// 重置表单
const reset = () => {
  meetingInfo.value.code = undefined;
  meetingInfo.value.name = '';
  meetingInfo.value.time = '';
  meetingInfo.value.city = '';

  transferInfo.value.user = '';
  transferInfo.value.userCode = '';
  transferInfo.value.userPhone = '';
  transferInfo.value.userEmail = '';
  transferInfo.value.userComments = '';
  transferInfo.value.reason = '';
};

// 根据经办人查询会议单号
// const fetchMeetingCodes = async () => {
//   if (!handlerInfo.value.name) return
//   try {
//     const response = await meetingHandoverApi.get({
//       operatorName: handlerInfo.value.name
//     })
//     if (response && Array.isArray(response)) {
//       meetingInfo.value.codes = response.map(item => ({
//         value: item.mainCode || '',
//         label: item.mainCode || ''
//       }))
//     } else {
//       meetingInfo.value.codes = []
//     }
//   } catch (error) {
//     console.error('获取会议单号失败', error)
//     meetingInfo.value.codes = []
//   }
// }

// 根据会议单号获取会议信息
const fetchMiceInfo = async () => {
  if (!meetingInfo.value.code) {
    resetMeetingInfo();
    return;
  }

  try {
    const response = await meetingHandoverApi.getMiceInfo({
      mainCode: meetingInfo.value.code,
    });
    if (response && typeof response === 'object') {
      const responseAny = response as any;
      meetingInfo.value.name = responseAny.miceName || '';
      meetingInfo.value.time =
        responseAny.startDate && responseAny.finishDate ? `${responseAny.startDate} 至 ${responseAny.finishDate}` : '';
      meetingInfo.value.city = responseAny.city || '';
      meetingInfo.value.id = responseAny.id || '';
    } else {
      resetMeetingInfo();
    }
  } catch (error) {
    console.error('获取会议信息失败', error);
    resetMeetingInfo();
  }
};

// 重置会议信息
const resetMeetingInfo = () => {
  meetingInfo.value.name = '';
  meetingInfo.value.time = '';
  meetingInfo.value.city = '';
  meetingInfo.value.id = '';
};

// 监听会议单号变化，获取会议信息
watch(
  () => meetingInfo.value.code,
  () => {
    fetchMiceInfo();
  },
);

// 弹框提示语配置
const confirmModal = {
  title: '确认提交',
  content: '一次性交接所有会议',
  okText: '确定',
  cancelText: '取消',
};

// 提交会议交接
const submitHandover = async () => {
  if (!transferInfo.value.user) {
    message.warning('请填写完整信息');
    return;
  }

  try {
    const meetingCodeId = parseInt(meetingInfo.value.id);
    const params = {
      connectBeforeName: handlerInfo.value.name,
      connectBeforePhone: handlerInfo.value.phone,
      connectBeforeCode: handlerInfo.value.jobNumber,
      connectBeforeEmail: handlerInfo.value.email,
      handoverAfterName: transferInfo.value.user,
      handoverAfterPhone: transferInfo.value.userPhone,
      handoverAfterCode: transferInfo.value.userCode,
      handoverAfterEmail: transferInfo.value.userEmail,
      handoverLineName: transferInfo.value.userComments,
      handoverReason: transferInfo.value.reason,
    };
    console.log(meetingCodeId, 'meetingCodeId');
    console.log(params, 'params');

    const result = await meetingHandoverApi.save(params as any);
    console.log('提交结果:', result);
    if (result === null) {
      // 提交成功，重置表单
      message.success('提交成功');
      reset();
    } else if (typeof result === 'string' && result) {
      // 如果返回的是审批流程code
      message.success('提交成功');
      approveCode.value = result;
      approvalModalShow.value = true;
    } else {
      message.error('提交失败: ' + (result?.message || '未知错误'));
    }
  } catch (error: any) {
    console.error('提交会议交接失败', error);
    message.error('提交失败: ' + (error.message || '未知错误'));
  }
};
</script>

<template>
  <div class="meeting-handover-container">
    <h2 class="meeting-handover-title">会议交接</h2>

    <!-- 功能说明提示 -->
    <div class="function-notice">
      <span class="notice-text">{{ confirmModal.content }}</span>
    </div>

    <div class="meeting-handover-form">
      <!-- 会议信息区块 -->
      <h-row class="section-title">
        <h-col :span="24" class="text-left">
          <h3>交接人信息</h3>
        </h-col>
      </h-row>

      <!-- 第一行：经办人、联系电话、邮箱 -->
      <h-row :align="'middle'" class="form-row">
        <h-col :span="2" class="label-col">
          <label>经办人：</label>
        </h-col>
        <h-col :span="4">
          <h-input v-model:value="handlerInfo.name" placeholder="联系电话" disabled />
        </h-col>

        <h-col :span="2" class="label-col">
          <label>联系电话：</label>
        </h-col>
        <h-col :span="4">
          <h-input v-model:value="handlerInfo.phone" placeholder="联系电话" disabled />
        </h-col>

        <h-col :span="2" class="label-col">
          <label>邮箱：</label>
        </h-col>
        <h-col :span="4">
          <h-input v-model:value="handlerInfo.email" placeholder="邮箱" disabled />
        </h-col>
        <h-col :span="2" class="label-col">
          <label>工号：</label>
        </h-col>
        <h-col :span="4">
          <h-input v-model:value="handlerInfo.jobNumber" placeholder="工号" disabled />
        </h-col>
      </h-row>

      <!-- 承接人信息区块 -->
      <h-row class="section-title section-space">
        <h-col :span="24" class="text-left">
          <h3>承接人信息</h3>
        </h-col>
      </h-row>

      <!-- 第一行：承接人、工号、联系电话、邮箱 -->
      <h-row :align="'middle'" class="form-row">
        <h-col :span="2" class="label-col">
          <label>承接人：</label>
        </h-col>
        <h-col :span="4">
          <user-select
            :value="transferInfo.user"
            :params="userSelectParams"
            placeholder="请选择承接人姓名"
            @change="userNameChange"
            allowClear
          />
        </h-col>

        <h-col :span="2" class="label-col">
          <label>工号：</label>
        </h-col>
        <h-col :span="4">
          <h-input v-model:value="transferInfo.userCode" placeholder="请输入工号" disabled />
        </h-col>

        <h-col :span="2" class="label-col">
          <label>联系电话：</label>
        </h-col>
        <h-col :span="4">
          <h-input v-model:value="transferInfo.userPhone" placeholder="请输入联系电话" disabled />
        </h-col>

        <h-col :span="2" class="label-col">
          <label>邮箱：</label>
        </h-col>
        <h-col :span="4">
          <h-input v-model:value="transferInfo.userEmail" placeholder="请输入邮箱" disabled />
        </h-col>
      </h-row>

      <!-- 承接人直线 -->
      <h-row :align="'middle'" class="form-row">
        <h-col :span="2" class="label-col">
          <label>承接人直线：</label>
        </h-col>
        <h-col :span="4">
          <h-input v-model:value="transferInfo.userComments" placeholder="请输入承接人直线" disabled />
        </h-col>
      </h-row>

      <!-- 承接人原因 -->
      <h-row :align="'middle'" class="form-row">
        <h-col :span="2" class="label-col">
          <label>承接原因：</label>
        </h-col>
        <h-col :span="22">
          <TextArea v-model:value="transferInfo.reason" :rows="4" placeholder="请输入承接原因" />
        </h-col>
      </h-row>

      <!-- 提交按钮(居中) -->
      <h-row class="form-row">
        <h-col :span="24" class="button-col">
          <h-button type="primary" @click="submitHandover">提交</h-button>
          <h-button class="reset-button" @click="reset">重置</h-button>
        </h-col>
      </h-row>
    </div>

    <!-- 审批流程模态框 -->
    <h-modal
      v-model:open="approvalModalShow"
      title="已提交如下人员审批"
      width="80%"
      :keyboard="false"
      :maskClosable="false"
      :closable="false"
    >
      <div>
        <iframe width="100%" :src="businessProcess + '?code=' + approveCode + '#/detailsPcSt'" frameborder="0"></iframe>
      </div>
      <template #footer>
        <h-button
          @click="
            approvalModalShow = false;
            message.success('交接成功');
            reset();
          "
          >确定</h-button
        >
      </template>
    </h-modal>
  </div>
</template>

<style scoped lang="less">
.meeting-handover-container {
  background-color: #f1f2f6;
  height: 100%;
  width: 100%;
  padding: 10px 30px 0px 30px;
  overflow: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.meeting-handover-title {
  text-align: center;
  margin-bottom: 20px;
}

.function-notice {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  padding: 12px 16px;
  margin-bottom: 24px;
  text-align: center;
  width: 90%;
  max-width: 1200px;
}

.notice-text {
  color: #1890ff;
  font-size: 14px;
  font-weight: 500;
}

.meeting-handover-form {
  background-color: #fff;
  padding: 30px;
  border-radius: 4px;
  margin-bottom: 20px;
  width: 90%;
  max-width: 1200px;
}

.section-title {
  margin-bottom: 10px;
}

.section-space {
  margin-top: 20px;
}

.text-left {
  text-align: left;
}

.form-row {
  margin-bottom: 20px;
}

.label-col {
  text-align: right;
  padding-right: 10px;
}

.button-col {
  text-align: center;
}

.reset-button {
  margin-left: 10px;
}

.handler-info {
  height: 32px;
  line-height: 32px;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  padding: 0 11px;
  color: rgba(0, 0, 0, 0.65);
}
</style>
