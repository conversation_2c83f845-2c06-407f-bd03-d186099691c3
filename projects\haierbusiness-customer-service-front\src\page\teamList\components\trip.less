.save-box-right {
  align-items: center;
}
.flex {
  display: flex;
}

.shu {
  width: 1px;
  height: 20px;
  background-color: #c6c6c6;
}
.icon {
  padding-left: 5px;
}
.color-blue {
  color: #0852df;
}
.pointer {
  cursor: pointer;
}
.save-box {
  align-items: center;
  justify-content: space-between;
  background: #fff;
  border-top: 1px solid #eee;
  padding: 10px;
}
.whole-line {
  width: 100%;
}

.pl-8 {
  padding-left: 8px;
}

.pr-8 {
  padding-right: 8px;
}

.mb-100 {
  margin-bottom: 100px;
}
.mt-20 {
  margin-top: 20px;
}
.mt-10 {
  margin-top: 10px;
}
.mb-10 {
  margin-bottom: 10px;
}
.mr-10 {
  margin-right: 10px;
}
.ml-10 {
  margin-left: 10px;
}
.mr-20 {
  margin-right: 20px;
}
.input {
  margin-top: 10px;
}

.block-con {
  margin-top: 50px;
}

.primary-color {
  color: #0073E5;
  font-size: 14px;
}
.color-main {
  color: #0073E5;
}

.eee-color {
  color: #eee;
}
.font-size-12 {
  font-size: 12px;
}
.font-size-14 {
  font-size: 14px;
}

.font-size-24 {
  font-size: 24px;
}
.file-text {
  margin-bottom: 10px;
  margin-top: 20px;
  color: #888;
  font-size: 14px;
}
.my-selsect {
  width: 160px;
}

.container {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  background-color: #f5f5f5;
  padding-bottom: 85px;

  .row {
    width: 1170px;
    margin-top: 120px;
    flex-direction: column;
    position: relative;
    .apply-con {
      position: relative;
      width: 1170px;
      min-height: calc(100vh - 420px);
      background-color: #ffffff;
      padding: 0px 100px 0 60px;

      flex-direction: column;

      .title {
        font-size: 20px;
      }

      .sub-title {
        padding-top: 24px;
        font-size: 14px;
        color: #666666;
        justify-content: space-between;
        align-items: center;
      }

      .budget {
        margin-top: 24px;
        background: rgba(150, 150, 150, 0.06);
        border: 1px solid rgba(100, 100, 100, 0.2);
        padding: 18px 20px;
        align-items: center;
        justify-content: space-between;

        .detail {
          padding-left: 20px;
        }
      }

      .travel {
        margin-top: 4px;
        display: flex;
        width: 100%;

        .add-btn {
          border-color: #0073E5;
          width: 105px;
        }

        .pict {
          align-items: center;
          height: 24px;
          align-items: end;

          .dashed-line {
            margin-left: 3px;
            border-top: 2px dashed #0073E5;
            height: 50%;
            min-width: 100px;
            width: 100%;
          }
        }
        

        .block {
          flex-direction: column;
        }
      }
      .travel-list {
        .travel-item {
          .travel-item-head {
            justify-content: space-between;
            align-items: center;
            .head-left {
              align-items: center;
              .head-left-num {
                width: 70px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #3a83e5;
                color: #fff;
                font-size: 12px;
                border-radius: 5px;
              }
              .primary-color {
                color: #fff;
              }
            }
            .head-right {
              align-items: center;
            }
          }
        }
      }
    }

    .anchor-con {
      width: 80px;
      min-height: calc(100vh - 420px);
      padding-left: 20px;
      position: absolute;
      right: -80px;
    }
  }
}

.editable-cell {
  position: relative;
  .editable-cell-input-wrapper,
  .editable-cell-text-wrapper {
    padding-right: 24px;
  }

  .editable-cell-text-wrapper {
    padding: 5px 24px 5px 5px;
  }

  .editable-cell-icon,
  .editable-cell-icon-check {
    position: absolute;
    right: 0;
    width: 20px;
    cursor: pointer;
  }

  .editable-cell-icon {
    margin-top: 4px;
    display: none;
  }

  .editable-cell-icon-check {
    line-height: 28px;
  }

  .editable-cell-icon:hover,
  .editable-cell-icon-check:hover {
    color: #108ee9;
  }

  .editable-add-btn {
    margin-bottom: 8px;
  }
}
.editable-cell:hover .editable-cell-icon {
  display: inline-block;
}
.add-btn2 {
  border-color: #3983e5;
  
}
.button-padding {
  padding-left: 12px !important;
  padding-right: 12px !important;
}
.flex-center {
  display: flex;
  align-items: center;
}
.product-option {
  display: flex;
  align-items: center;
  justify-content: start;
}
.product-icon {
  width: 20px;
  height: 20px;
  margin-right: 6px;
}
.product-text {
 
}
.ant-form-item-label > label > span {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;

}

.ant-form-item-required > span{
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
}

.font-color{
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
}
.font-color-black{
  color: #000000d9;
  font-family: PingFangSC-Medium;
  font-weight: 500;
}

.change-title {
  position: absolute;
  right: 0;
  top: -80px;
  width: 300px;
  
  .ant-col-6 , .ant-col-18, .ant-col-12{
    font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #000000d9;
  }
}
.change-now {
  width: 200px;
  .ant-col-12{
    font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #000000d9;
  cursor: pointer;
  }
  .active {
    color: rgb(22, 119, 255);
  }
}

.row {
  width: 1170px;
  margin-top: 160px;
  flex-direction: row;

  .apply-con {
    width: 1170px;
    min-height: calc(100vh - 420px);
    background-color: #ffffff;
    padding: 0px 100px 0 60px;
        flex-direction: column;

    .title {
      font-size: 20px;
    }

    .sub-title {
      padding-top: 24px;
      font-size: 14px;
      color: #666666;
      justify-content: space-between;
      align-items: center;
    }

    .budget {
      margin-top: 24px;
      background: rgba(150, 150, 150, 0.06);
      border: 1px solid rgba(100, 100, 100, 0.2);
      padding: 18px 20px;
      align-items: center;
      justify-content: space-between;

      .detail {
        padding-left: 20px;
      }
    }

    .travel {
      margin-top: 24px;
      display: flex;
      width: 100%;

      .add-btn {
        border-color: #3983e5;
        width: 105px;
      }

      .pict {
        align-items: center;
        height: 24px;
        align-items: end;

        .dashed-line {
          margin-left: 3px;
          border-top: 2px dashed #3983e5;
          height: 50%;
          min-width: 100px;
          width: 100%;
        }
      }
      .dashed-line {
      }

      .block {
        flex-direction: column;
      }
    }
    .travel-list {
      .travel-item {
        .travel-item-head {
          justify-content: space-between;
          align-items: center;
          .head-left {
            align-items: center;
            .head-left-num {
              width: 70px;
              height: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #3a83e5;
              color: #fff;
              font-size: 12px;
              border-radius: 5px;
            }
            .primary-color {
              color: #fff;

            }
          }
          .head-right {
            align-items: center;
          }
        }
      }
    }
  }

  .anchor-con {
    width: 80px;
    min-height: calc(100vh - 420px);
    padding-left: 20px;
  }
}

.affix-bottom {
  width: 100%;
  height: 56px;
  margin-top: 24px;
}
.save-box {
  height: 100%;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-top: 1px solid #eee;
  width: 100%;
  .box-center {
    width: 1170px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // .ant-form-item-explain-error {
    //   font-size: 12px;
    // }
      .save-box-left {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
      }
    .ant-form-item-explain-connected {

    }
  }

  // :deep(.ant-form-item-control) {
  //   display: flex !important;
  //   flex-direction: row;
  // }
  // :deep(.ant-form-item-explain-error) {
  //   font-size: 12px !important;
  // }
  // :deep(.ant-form-item-explain) {
  //   display: flex;
  //   align-items: center;
  //   position: relative;
  //   bottom: -1px;
  // }
}

.ant-checkbox-inner {
  transform:scale(0.8)
}
.error-text {
  color: red;
  font-family: '';
}
.ant-table-cell, .ant-select-selection-item, .city ,.date, .ant-select-selection-search-input, .ant-select-selection-placeholder {
  font-size: 14px !important;
}

.ant-picker-input {
  input {
    font-size: 14px !important;

  }
}
.ant-form-item-control-input-content {
  input {
    font-size: 14px !important;

  }
}
.tag {
  padding: 1px 10px;
  border-radius: 11px;
  height: 100%;
  display: flex;
  align-items: center;
  line-height: normal;
}
.mr-5 {
  margin-right: 5px;
}
.ant-input, .ant-form-item-explain-error, .ant-radio-wrapper >span {
  font-size: 14px !important;
}
.auto-text {
  font-family: "";
}
.font-color-grey {
  color: rgba(0, 0, 0, 0.45);
}
.border-radios {
  border-radius: 12px !important;
}
.main-title {
  position: absolute;
    left: 0;
    display: flex;
    flex-direction: column;
    top: -80px;
    span {
      margin-top: 10px;
      font-family: PingFangSC-SNaNpxibold;
      font-weight: 600;
      font-size: 20px;
      color: #000000d9;
      line-height: 28px;
    }
    img {
      width: 195px;
      height: 28px;
    }
}
.my-button {
  width: 65px !important;
  height: 32px !important;
}
.my-table-empty {
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  span {
    color: #00000073;
  }
}
.ant-table-cell {
  padding: 0!important;
  height: 36px !important;
}
.my-button {
  
}
.mt-30 {
  margin-top: 30px;
}
.reject {
    margin-top: 80px;
    box-sizing: border-box;
    .ant-row {
      margin:0;
    }
  .ant-col-2, .ant-col-20 {
    font-size: 16px;
    color: red;
  }
  .ant-col-2{
    margin-right: 5px;
   }
   .ant-col-20 {
    color: #000;

    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
   }
}
.reject-bg {
  position: absolute;
  right: 0;
  top: 0;
  width: 100px;
  height: 100px;
  background: url("../../../assets/image/trip/reject.png") 100% 100% no-repeat;
  background-size: cover;
}
.approval-btns {
 
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
  position: absolute;
  top: 20px;
  right: 110px;
}
.common-btn-height {
  height: 32px !important;
}
