<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  Upload as hUpload,
  Image as hImage,
  Button as hButton,
  message,
  InputNumber as hInputNumber,
} from 'ant-design-vue';
import { computed, ref, watch, h } from 'vue';
import type { Ref } from 'vue';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { IConsultant, UploadFiles, IUserInfo, IUserListRequest } from '@haierbusiness-front/common-libs';
import { FileTypeConstant } from '@haierbusiness-front/common-libs/src/micebid/constant/PriceInquiryEnumConstant';
import { fileApi } from '@haierbusiness-front/apis';
import userSelect from '@haierbusiness-front/components/user/UserSelect.vue';

interface Props {
  show: boolean;
  data: IConsultant | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const from = ref();
const confirmLoading = ref(false);
const imageUrl = ref<string>('');
const loading = ref<boolean>(false);

// 文件列表
const fileList = ref<UploadFiles[]>([]);

const defaultData: IConsultant = {
  nickName: '',
  username: '',
  id: null,
  description: '',
  path: '',
  gender: undefined,
  phone: '',
  seniority: 0,
  type: FileTypeConstant.PICTURE.code, // 修改为数字类型
};

const rules = {
  nickName: [{ required: true, message: '请输入顾问姓名', trigger: 'blur' }],
  description: [{ required: true, message: '请输入顾问介绍', max: 200, trigger: 'blur' }],
  seniority: [{ required: true, message: '请输入顾问工作年限', trigger: 'blur' }],
  path: [{ required: true, message: '请上传顾问照片', trigger: 'blur' }],
};

const consultant: Ref<IConsultant> = ref(({ ...props.data } as IConsultant) || defaultData);

// 基础URL
const baseUrl = import.meta.env?.VITE_BUSINESS_URL || '';

watch(
  props,
  (newValue) => {
    consultant.value = ({ ...newValue.data } as IConsultant) || defaultData;

    // 处理图片路径
    const imagePath = consultant.value.path || '';
    imageUrl.value = imagePath;

    // 如果有路径，初始化文件列表
    if (imagePath) {
      // 构建文件对象
      fileList.value = [
        {
          uid: '-1',
          name: '照片',
          status: 'done',
          url: imagePath,
          filePath: imagePath,
          thumbUrl: imagePath, // 添加缩略图URL
        },
      ];
    } else {
      fileList.value = [];
    }
  },
  { immediate: true },
);

const emit = defineEmits(['cancel', 'ok']);

const visible = computed(() => props.show);

const handleOk = () => {
  confirmLoading.value = true;

  // 设置超时机制，防止loading一直不结束
  const timeoutId = setTimeout(() => {
    confirmLoading.value = false;
  }, 3000); // 10秒超时

  from.value
    .validate()
    .then(() => {
      // 将图片地址赋值给consultant
      if (fileList.value && fileList.value.length > 0) {
        consultant.value.path = fileList.value[0].filePath || '';
      } else {
        consultant.value.path = '';
      }
      // 确保type字段存在
      if (!consultant.value.type) {
        consultant.value.type = FileTypeConstant.PICTURE.code;
      }

      emit('ok', consultant.value, (success = true) => {
        clearTimeout(timeoutId); // 清除超时定时器
        confirmLoading.value = false;
        // 如果父组件明确返回失败，可以在这里处理
        if (!success) {
          // 可以添加额外的错误处理逻辑
        }
      });
    })
    .catch(() => {
      clearTimeout(timeoutId); // 清除超时定时器
      confirmLoading.value = false;
    });
};

// 用户选择相关
const userSelectParams = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});

// 用户选择回调
const userNameChange = (userInfo: IUserInfo) => {
  consultant.value.nickName = userInfo?.nickName;
  consultant.value.username = userInfo?.username;

  // 如果gender是null，保持null值
  consultant.value.gender = userInfo?.gender === null ? null : userInfo?.gender === 1;

  consultant.value.phone = userInfo?.phone;
};

// 上传文件相关
const uploadRequest = (options: any) => {
  loading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((response) => {
      loading.value = false;
      if (response && response.path) {
        const fullPath = baseUrl + response.path;
        options.file.filePath = fullPath;
        options.file.fileName = options.file.name;
        options.file.url = fullPath; // 设置URL以便预览
        options.file.thumbUrl = fullPath; // 设置缩略图URL
        options.onSuccess(response, options.file);
        // 更新图片预览
        imageUrl.value = fullPath;
        // 立即更新consultant.value.path，解决表单验证问题
        consultant.value.path = fullPath;
      } else {
        options.onError('上传失败');
        message.error('上传失败，请重试');
      }
    })
    .catch((error) => {
      loading.value = false;
      options.onError('上传失败');
      console.error('上传失败:', error);
      message.error('文件上传失败，请重试');
    });
};

// 移除文件
const handleRemove = (file: UploadFiles) => {
  const index = fileList.value.indexOf(file);
  if (index !== -1) {
    fileList.value.splice(index, 1);
    imageUrl.value = '';
    // 清空consultant.value.path
    consultant.value.path = '';
  }
};

// 图片上传相关
const beforeUpload = (file: File) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('请上传JPG或PNG格式图片!');
    return false;
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('图片大小不能超过2MB!');
    return false;
  }

  // 检查图片尺寸 - 先注释掉尺寸校验guw
  return true;
  /* 暂时注释掉尺寸校验
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = URL.createObjectURL(file);
    img.onload = () => {
      // 这里我们可以允许一定的误差
      if (Math.abs(img.width - 240) <= 20 && Math.abs(img.height - 300) <= 20) {
        resolve(true);
      } else {
        message.error(`请上传尺寸为240*300的图片，当前尺寸为${img.width}*${img.height}`);
        reject(false);
      }
    };
  });
  */
};

const handlePreview = (file: UploadFiles) => {
  // 预览图片
  if (file.url || file.filePath) {
    const previewUrl = file.url || file.filePath;
    const image = new Image();
    image.src = previewUrl || '';
    const newWindow = window.open('');
    if (newWindow) {
      newWindow.document.write(`
        <html>
          <head>
            <title>图片预览</title>
            <style>
              body {
                margin: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                background-color: #000;
              }
              img {
                max-width: 100%;
                max-height: 100%;
              }
            </style>
          </head>
          <body>
            <img src="${previewUrl}" />
          </body>
        </html>
      `);
    }
  }
};
</script>

<template>
  <h-modal
    v-model:visible="visible"
    :title="consultant.id ? '编辑顾问管理' : '新增顾问管理'"
    :width="600"
    @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
  >
    <h-form ref="from" :model="consultant" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules">
      <h-form-item label="顾问姓名" name="nickName">
        <user-select
          :value="consultant.nickName"
          :params="userSelectParams"
          placeholder="请选择顾问姓名"
          @change="(userInfo: IUserInfo) => userNameChange(userInfo)"
        />
      </h-form-item>

      <h-form-item label="工号" name="username">
        <h-input v-model:value="consultant.username" placeholder="请输入工号" :disabled="true" />
      </h-form-item>

      <h-form-item label="工作年限" name="seniority">
        <h-input-number
          v-model:value="consultant.seniority"
          placeholder="请输入工作年限"
          style="width: 100%"
          :min="0"
        />
      </h-form-item>

      <h-form-item label="顾问介绍" name="description">
        <h-textarea
          v-model:value="consultant.description"
          placeholder="请输入顾问介绍"
          :auto-size="{ minRows: 3, maxRows: 5 }"
          :maxlength="200"
          show-count
        />
      </h-form-item>

      <h-form-item label="照片" name="path">
        <h-upload
          v-model:fileList="fileList"
          :custom-request="uploadRequest"
          :multiple="false"
          :max-count="1"
          @remove="handleRemove"
          :before-upload="beforeUpload"
          accept=".jpg, .png, .jpeg"
          :show-upload-list="true"
          list-type="picture-card"
          @preview="handlePreview"
        >
          <div v-if="fileList.length < 1">
            <plus-outlined />
            <div style="margin-top: 8px">上传照片</div>
          </div>
        </h-upload>
        <div style="color: #999; font-size: 12px; margin-top: 8px">建议上传图片尺寸为240*300 大小不超过2MB</div>

        <div v-if="imageUrl" style="margin-top: 16px">
          <h4>效果展示：</h4>
          <div style="position: relative; width: 240px; height: 300px; background-color: #f0f0f0">
            <img :src="imageUrl" style="width: 100%; height: 100%" />
            <div
              style="
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background-color: rgba(0, 0, 0, 0.6);
                color: white;
                padding: 8px;
                text-align: center;
              "
            >
              <div style="font-size: 18px; font-weight: bold">{{ consultant.nickName || '顾问姓名' }}</div>
              <div style="font-size: 14px">
                {{
                  consultant.description
                    ? consultant.description.length > 10
                      ? consultant.description.substring(0, 10) + '...'
                      : consultant.description
                    : '10年行业专家顾问'
                }}
              </div>
            </div>
          </div>
        </div>
      </h-form-item>
    </h-form>
  </h-modal>
</template>


<style lang="less" scoped>
.important {
  color: red;
}

:deep(.ant-upload-select) {
  width: 104px;
  height: 104px;
}

:deep(.ant-upload-list-item) {
  margin-top: 8px;
}
:deep(.ant-form-item-required::before) {
  display: none !important;
}
</style>