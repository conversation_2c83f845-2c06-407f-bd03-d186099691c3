<template>
  <div class="wallet">
    <van-nav-bar title="充值" left-text="返回" left-arrow @click-left="onClickLeft" />
    <van-notice-bar
      left-icon="volume-o"
      text="此功能仅限海尔员工使用,离职/待岗等情况账户自动关闭。"
    />
    <van-tabs v-model:active="activeName">
      <van-tab title="使用代金券充值" name="voucher"> <Voucher></Voucher></van-tab>
      <van-tab title="福利积分充值" name="integral">
        <Integral></Integral>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import Integral from "./components/integral.vue";
import Voucher from "./components/voucher.vue";
import { getCurrentRouter } from "@haierbusiness-front/utils";
const router = getCurrentRouter();

const activeName = ref<string>("voucher");
const onClickLeft = () => {
  router.push({
    path: "/wallet",
  });
};
</script>
<style lang="scss" scoped>
.wallet {
  background: #f3f3f3;
  height: calc(100vh - 110px);
}
</style>
