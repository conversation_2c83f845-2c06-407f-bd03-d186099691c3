<template>
  <div class="w-1000">
    <h-card size="large" title="申请审批详情">
      <h-row :gutter="24">
        <h-col class="mt-10 phone-w" :span="12">使用人账号： {{ detail.employeeId }}</h-col>
        <h-col class="mt-10 phone-w" :span="12"
          >使用人名称： {{ detail.employeeName }}</h-col
        >
        <h-col class="mt-10 phone-w" :span="12"
          >审批流： {{ detail.approvalFlowType==0?'经办人':'使用人' }}</h-col
        >
        <h-col class="mt-10 phone-w" :span="12"
          >经办人： {{ detail.nickName }}</h-col
        >
        <h-col class="mt-10 phone-w" :span="12"
          >结算单位：
          <div v-for="item in accountCompanyName" :key="item">
            {{ item }}
          </div>
        </h-col>
        <h-col class="mt-10 phone-w" :span="12"
          >预算部门：
          <div v-for="item in budgetDepartmentName" :key="item">
            {{ item }}
          </div>
        </h-col>

        <h-col class="mt-10 phone-w" :span="12"
          >领域：
          <div v-for="item in fieldName" :key="item">
            {{ item }}
          </div>
        </h-col>

        <h-col class="mt-10 phone-w" :span="12"
          >平台：
          <div v-for="item in ptName" :key="item">
            {{ item }}
          </div>
        </h-col>

        <h-col class="mt-10 phone-w" :span="12"
          >产业线：
          <div v-for="item in plName" :key="item">
            {{ item }}
          </div>
        </h-col>
        <h-col class="mt-10 phone-w" :span="12"
          >业务类型： {{detail.businessType=="travel" ? "商旅" : detail.businessType=="mealService" ?  "餐务": ""  }}<a v-if="!detail.businessType" @click="showModal(record)">指标</a>
          <!-- {{
                      detail.businessType
                          ? businessType.find(
                                (item) => item.key == detail.businessType
                            ).text
                          : ""
                  }} -->
        </h-col>
        <h-col class="mt-10 phone-w" :span="12"
          >数据类型： {{detail.moduleType==1 ? "报表" : detail.moduleType==2 ?  "看板": ""  }}
          {{detail.moduleType == 4?'指标':"" }}
          <!-- {{
                      detail.businessType
                          ? businessType.find(
                                (item) => item.key == detail.businessType
                            ).text
                          : ""
                  }} -->
        </h-col>
        <!-- 
              <h-col class="mt-10 phone-w" :span="12"
                  >状态：{{
                      detail.permissionStatus == "VALID" ? "正常" : "停用"
                  }}</h-col
              > -->
        <h-col class="mt-10 phone-w" :span="12"
          >申请原因：{{ detail.approveReason }}
        </h-col>
        <h-col class="mt-10 phone-w" :span="12"
          >审核状态：{{ getApproveStatus(detail.approveStatus!) }}
        </h-col>
        <h-col class="mt-10 phone-w" :span="12"
          >审核意见：{{ detail.approveRemark }}
        </h-col>

        <!-- <h-col class="mt-10 phone-w" :span="12"
        >业务数据开始日期： {{ detail.businessDataTimeStart }}
      </h-col>
      <h-col class="mt-10 phone-w" :span="12"
        >业务数据结束日期： {{ detail.businessDataTimeEnd }}
      </h-col> -->

        <h-col class="mt-10 phone-w" :span="12"
          >权限有效开始日期：{{
            dayjs(detail.permissionValidTimeStart).format("YYYY-MM-DD HH:mm:ss")
          }}
        </h-col>
        <h-col class="mt-10 phone-w" :span="12"
          >权限有效结束日期：{{
            dayjs(detail.permissionValidTimeEnd).format("YYYY-MM-DD HH:mm:ss")
          }}
        </h-col>
        <h-col class="mt-10 phone-w" :span="12"
          >创建时间：{{ dayjs(detail.createTime).format("YYYY-MM-DD HH:mm:ss") }}
        </h-col>
        <!-- <h-col class="mt-10 phone-w" :span="24"
        >业务类型权限：
        <span style="margin-right: 10px;" v-for="(item, i) in permissionType" :key="i">{{ item }}</span>
      </h-col> -->
      </h-row>
    </h-card>
    <br />
  </div>
    <h-modal
    v-model:visible="modalShow"
    :width="1200"
    title="申请指标"
    :footer="null"
  >
    <h-card>
      <div class="reportBox" style="padding: 8px;height:70vh;overflow:auto;">
        <div v-for="(item, index) in detail.reportLabelCenterVos" :key="item.id" class="indexItemBox">
            <ReportItem :reportInfo="item"></ReportItem>
        </div>
      </div>
    </h-card>
  </h-modal>
</template>
<script setup lang="ts">
import {
  Badge as hBadge,
  Card as hCard,
  Progress as hProgress,
  Button as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
} from 'ant-design-vue';
import { businessType } from "../columns";
import { computed, ref, watch, onMounted } from "vue";
import { applyApi, reportApi } from "@haierbusiness-front/apis";
import ReportItem from '../../smartBrain/components/reportItem.vue';
import { ApplyCompanyType, ApplyType } from "@haierbusiness-front/common-libs";

import { useRoute, useRouter } from "vue-router";
import dayjs from "dayjs";
const modalShow = ref<boolean>(false)
const getQuery = () => {
  let hashUrl = window.location.hash;
  let url = hashUrl.substring(hashUrl.indexOf("?") + 1, hashUrl.length);
  let arr = url.replace(/^\#/, "").split("&");
  let params: any = {};
  for (let i = 0; i < arr.length; i++) {
    let data = arr[i].split("=");
    if (data.length === 2) {
      params[data[0]] = data[1];
    }
  }
  return params;
};
const route = useRoute();
const detail = ref({} as ApplyType);
const accountCompanyName = ref([]);
const budgetDepartmentName = ref([]);
const fieldName = ref([]);
const ptName = ref([]);
const plName = ref([]);


const permissionType = ref([] as Array<{ alias: string }>);
const getDetail = async () => {
  const data = await applyApi.getPermissionApprove(getQuery());
  detail.value = data;
  permissionType.value = JSON.parse(data.permissionString).map((item: any) => item.alias);
  accountCompanyName.value = JSON.parse(data.accountCompanyName);
  budgetDepartmentName.value = JSON.parse(data.budgetDepartmentName);
  fieldName.value = JSON.parse(data.fieldName);
  ptName.value = JSON.parse(data.ptName);
  plName.value = JSON.parse(data.plName);

};

// 获取退款状态
const getApproveStatus = (status: number | string) => {
  const resultMap: any = {
    0: "取消",
    10: "审批中",
    20: "审批通过",
    30: "审批驳回",
    40: "审批撤回",
    default: "",
  };
  return resultMap[status] || resultMap.default;
};
const showModal=(row:any)=>{
  modalShow.value = true
}
onMounted(() => {
  getDetail();
});
</script>
<style lang="less" scoped>
.w-1000 {
  width: 1000px;
  margin: 20px auto;
}

.mt-10 {
  margin-top: 10px;
}

@media screen and (max-width: 599px) {
  .w-1000 {
    width: 98%;
    margin: 20px auto;
  }

  .phone-w {
    flex: 0 0 100%;
    max-width: 100%;
  }
}
.reportBox{
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .indexItemBox{
    width:49%;
  }
}
</style>

