<script lang="ts" setup>
import {
  Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
  Input as hInput, CheckboxGroup as hCheckboxGroup, Checkbox as hCheckbox, Row as hRow, Col as hCol, DatePicker as hDatePicker, message,
  Upload as hUpload, RadioGroup as hRadioGroup, Radio as hRadio, InputNumber as hInputNumber, <PERSON>ton as hButton, Switch as hSwitch
} from 'ant-design-vue';
import { computed, ref, watch, onMounted } from "vue";
import { useRoute, useRouter } from 'vue-router'
import type { Ref } from "vue";
import {
  IAdvertisementPrividerAccount, HeaderConstant
} from '@haierbusiness-front/common-libs';
import { useRequest } from 'vue-request';
import { providerAdvertisementApi, advertisementListApi, fileApi } from '@haierbusiness-front/apis';
import type { UploadChangeParam, UploadProps } from 'ant-design-vue';
import PlusOutlined from '@ant-design/icons-vue/PlusOutlined';
import LoadingOutlined from '@ant-design/icons-vue/LoadingOutlined';
import { UploadOutlined } from '@ant-design/icons-vue'
import Editor from '@haierbusiness-front/components/editor/Editor.vue'
import type { IDomEditor } from "@wangeditor/editor"
import { toNumber } from "lodash-es"
import router from '../../router'
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil'

const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false)
const baseUrl = import.meta.env.VITE_BUSINESS_URL

const currentRouter = ref()


const from = ref();
const confirmLoading = ref(false);
const id = ref<number>()

onMounted(async () => {
    currentRouter.value = await router
    const currentId = currentRouter.value.currentRoute.query?.id
    id.value = toNumber(currentId)
    if (id.value) {
      await get(id.value)
    }
    else {
      account.value = { showStatus: 1 }
      imageUrl.value = ''
    }
})

watch(() => currentRouter.value?.currentRoute.query?.id, (newValue, oldValue) => {
  id.value = toNumber(newValue)
  if (id.value) {
    get(id.value)
  } 
  else {
    account.value = { showStatus: 1 }
    imageUrl.value = ''
  }
})

const get = async (id: number) => {
  const data = await advertisementListApi.get(id)
  if(data && data.id) {
    account.value = data
    imageUrl.value = data.imgUrl || ''
  } 
}

const rules = {
  title: [{ required: true, message: "请输入标题！" }],
  author: [{ required: true, message: "请输入作者！" }],
  // content: [{ required: true, message: "请输入描述！" }],
  showStatus: [{ required: true, message: "请选择展示状态！" }],
};

const account = ref<IAdvertisementPrividerAccount>({});

const fileList = ref<Array<any>>([]);
const loading = ref<boolean>(false);
const imageUrl = ref<string>('');

const upload = async (options:any) => {
  loading.value = true;
  const formData = new FormData()
  formData.append('file', options.file)
  const res = await fileApi.upload(formData)
  const file = {
      ...options.file,
      name: options.file.name,
      url: baseUrl + res.path
  }
  loading.value = false;
  fileList.value = [...fileList.value, file]
  imageUrl.value = baseUrl + res.path
  account.value.imgUrl = baseUrl + res.path
  
  options.onProgress(100)
  options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
}

const beforeUpload = (file: any) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg'
  if (!isJpgOrPng) {
    message.error('只能上传格式为png/jpg/jpeg的文件！')
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('Image must smaller than 2MB!');
  }
  return isJpgOrPng && isLt2M;
};
const handleOk = () => {
  if (!account.value.jumpLinkPc && !account.value.content) {
    message.error('跳转链接和详情页不能同时为空！')
    return
  }
  confirmLoading.value = true;
  from.value.validate()
    .then(() => {
      const data = {
        ...account.value,
      }

      if (data.id) {
        providerAdvertisementApi.edit(data).then(res => {
          message.success(`编辑成功!`);
          currentRouter.value.push({ path: "/portal/providerAdvert/list"})
        })
      } else {
        providerAdvertisementApi.save(data).then(res => {
          message.success(`新增成功!`);
          currentRouter.value.push({ path: "/portal/providerAdvert/list"})
        })
      }


      confirmLoading.value = false;
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};

// if (props.data) {
//   imageUrl.value = props.data.imgUrl ? props.data.imgUrl : ''
// }

const onEditorChange = (editor: IDomEditor) => {
  account.value.content = editor.getHtml()
}

const uploadUrl = import.meta.env.VITE_UPLOAD_URL

</script>

<script lang="ts">
  export default {
    name: "providerAdvertEdit",
  };
</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-form ref="from" :model="account" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules" @finish="handleOk">
        <h-form-item label="标题" name="title">
          <h-input v-model:value="account.title" />
        </h-form-item>
        <h-form-item label="作者" name="author">
          <h-input v-model:value="account.author" />
        </h-form-item>
        <h-form-item label="图片" name="imgUrl" class="upload">
          <h-upload
              list-type="picture-card"
              class="avatar-uploader"
              :file-list="fileList"
              :max-count="1"
              :show-upload-list="false"
              name="avatar"
              :custom-request="upload"
              :before-upload="beforeUpload"
              :headers="{
                  'Hb-Token': token
              }"
          >
            <img v-if="imageUrl" :src="imageUrl" class="imgShow" />
            <div v-else>
              <loading-outlined v-if="loading"></loading-outlined>
              <plus-outlined v-else></plus-outlined>
              <div>上传图片</div>
            </div>
          </h-upload>
          请上传图片的尺寸为<span class="important">327*230</span> 大小不超过<span class="important">2MB</span> 格式为<span class="important">png/jpg/jpeg</span>的文件
          <!-- <div class="preview">
              <h-button type="link" @click="onPreview">预览</h-button>
          </div> -->
        </h-form-item>
        <h-form-item label="跳转链接" name="jumpLinkPc">
          <h-input v-model:value="account.jumpLinkPc" />
        </h-form-item>
        <h-form-item label="详情页" name="content">
          <editor height="300px" :modelValue="account.content" @change="onEditorChange" style="z-index: 20"
            :uploadUrl="uploadUrl" />
        </h-form-item>
        <h-form-item label="是否置顶" name="isTopping">
          <h-switch v-model:checked="account.isTopping" :checked-value="1" :un-checked-value="0" />
        </h-form-item>
        <h-form-item label="展示状态" name="showStatus">
          <h-radio-group v-model:value="account.showStatus" name="radioGroup">
              <h-radio :value="1">显示</h-radio>
              <h-radio :value="0">隐藏</h-radio>
          </h-radio-group>
        </h-form-item>
        
        <div class="submit-btn">
          <h-button type="primary" shape="round" html-type="submit" class="sub-btn">提交</h-button>
        </div>
    </h-form>
  </div>
    
</template>


<style lang="less" scoped>

.submit-btn {
  width: 100%;
  display: flex;
  justify-content: center;
  padding-bottom: 20px;
}

.sub-btn {
  width: 200px;
  
}

.important {
  color: red;
}

.imgShow {
  width: 100%;
}

.preview {
  margin-top: 10px;
}

.avatar-uploader {
  :deep(.bwdv-upload.bwdv-upload-select-picture-card) {
    width: 327px !important;
    height: 230px !important;
    overflow: hidden;
    border-radius: 8px;
  }
}
</style>

<style>
.upload .ant-form-item-label >label::before {
    display: inline-block;
    margin-inline-end: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun,sans-serif;
    line-height: 1;
    content: "*";
}
</style>
  