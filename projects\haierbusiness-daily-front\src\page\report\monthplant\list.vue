<script setup lang="ts">
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Input as hInput,
  Table as hTable,
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Dropdown as hDropdown,
  Menu as hMenu,
  MenuItem as hMenuItem,
  DatePicker as hDatePicker,
} from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import { computed, onMounted, ref } from 'vue';
import { PlusOutlined, SearchOutlined, BarsOutlined, DownOutlined, DownloadOutlined } from '@ant-design/icons-vue';
import { userApi, enterpriseApi, processApi } from '@haierbusiness-front/apis';
import { dailyReportApi, dailyDeptApi } from '@haierbusiness-front/apis/src/daily';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue'
import { usePagination, useRequest } from "vue-request";
import { IMonthPlantDTO, UserGroupSystemConstant} from "@haierbusiness-front/common-libs";
import { useEditDialog, useDelete } from "@haierbusiness-front/composables";
import { getCurrentRouter, errorModal, routerParam, checkUserGroup } from "@haierbusiness-front/utils";

const router = getCurrentRouter();

const columns: ColumnType[] = [
  {
    title: '项目名称',
    dataIndex: 'apiName',
    width: '8%',
    align: 'center',
  },
  {
    title: '目标类型',
    dataIndex: 'apTypeName',
    width: '5%',
    align: 'center',
  },
  {
    title: '部门名称',
    dataIndex: 'deptName',
    width: '5%',
    align: 'center',
  },
  {
    title: '年份',
    dataIndex: 'year',
    width: '3%',
    align: 'center',
  },
  {
    title: '月份',
    dataIndex: 'month',
    width: '3%',
    align: 'center',
  },
  {
    title: '责任人',
    dataIndex: 'principalUsername',
    width: '3%',
    align: 'center',
  },
  {
    title: '月度目标及达成路径',
    dataIndex: 'planValue',
    width: '6%',
    align: 'center',
  },
  {
    title: '完成效果',
    dataIndex: 'completePlanValue',
    width: '4%',
    align: 'center',
  },
  {
    title: '完成状态',
    dataIndex: 'stateName',
    width: '4%',
    align: 'center',
  },
  {
    title: '完成率',
    dataIndex: 'completeRate',
    width: '3%',
    align: 'center',
  },
  {
    title: '完成计划日期',
    dataIndex: 'completePlanTime',
    width: '5%',
    align: 'center',
  },
];


const searchParam = ref<IMonthPlantDTO>({
});

const reset = () => {
  searchParam.value = {};
  startBeginAndEnd.value = [];
};

const {
  data,
  run: processApiRun,
  loading,
  current,
  pageSize,
} = usePagination(dailyReportApi.getMonthPlanReportList, {
  manual: false,
});

const {
  data: typeNameList,
  loading: typeListLoading,
} = usePagination(dailyReportApi.getTypeNameList, {
  manual: false,
});


const {
  data: deptList,
  loading: deptListLoading,
} = usePagination(dailyDeptApi.list, {
  manual: false,
});

const deptSelect = computed(() => {
  let deptArr = [];
  for (let i in deptList.value) {
    deptArr.push(
        {
          value: deptList.value[i].code,
          label: deptList.value[i].name,
        }
    )
  }
  return deptArr;
});

const params = ref<IUserListRequest>({
    pageNum: 1,
    pageSize: 20
})

const typeNameSelect = computed(() => {
  return typeNameList.value;
});

const checkUserPlatFrom = computed(() => {
  return checkUserGroup(UserGroupSystemConstant.DAILY_PLATFORM_MANAGER.groupId);
});

const checkUserAdmin = computed(() => {
  return checkUserGroup(UserGroupSystemConstant.SUPER_MANAGE.groupId);
});


const {
  data: exportData,
  run: exportApiRun,
  loading: detailsExportLoading,
} = useRequest(dailyReportApi.exportMonthPlanReport);


const dataSource = computed(() => data.value?.records || []);
const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  processApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const exportMonthPlanReport = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  exportApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const userNameChange = (userInfo: IUserInfo | undefined) => {
    if (!userInfo) {
        searchParam.value.principalUsercode = ''
        searchParam.value.principalUsername = ''
        return
    }
    searchParam.value.principalUsercode = userInfo.username
    searchParam.value.principalUsername = userInfo.nickName
}

</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :gutter="[12, 24]" :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px" v-if="checkUserAdmin || checkUserPlatFrom">
            <label for="deptCode">部门：</label>
          </h-col>
          <h-col :span="4" v-if="checkUserAdmin || checkUserPlatFrom">          
            <a-select
              v-model:value="searchParam.deptCode"
              show-search
              placeholder="请选择部门"
              style="width: 200px"
              allowClear
              :options="deptSelect"
            >
            </a-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="apiName">项目名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="apiName" v-model:value="searchParam.apiName" placeholder="" style="width: 200px" autocomplete="off" allowClear />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="principalUsercode">责任人：</label>
          </h-col>
          <h-col :span="4">
              <user-select :value="searchParam.principalUsername" style="width: 200px" :cache-key="'processSubUser'" 
                :params="params" @change="(userInfo: IUserInfo) =>  userNameChange(userInfo)" />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="state">完成状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.state" style="width: 100%" allowClear>
              <h-select-option :value="10">待生效</h-select-option>
              <h-select-option :value="20">进行中</h-select-option>
              <h-select-option :value="30">已完成</h-select-option>
              <h-select-option :value="35">已终止</h-select-option>
              <h-select-option :value="40">未完成</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px; width: 200px">
            <label for="year">年份：</label>
          </h-col>
          <h-col :span="4">
            <h-date-picker
              style="margin-bottom: 6px; width: 200px"
              v-model:value="searchParam.year"
              value-format="YYYY"
              picker="year" 
              allowClear
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="searchState">月份：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.month" style="width: 200px" allowClear>
              <h-select-option :value="1">1月</h-select-option>
              <h-select-option :value="2">2月</h-select-option>
              <h-select-option :value="3">3月</h-select-option>
              <h-select-option :value="4">4月</h-select-option>
              <h-select-option :value="5">5月</h-select-option>
              <h-select-option :value="6">6月</h-select-option>
              <h-select-option :value="7">7月</h-select-option>
              <h-select-option :value="8">8月</h-select-option>
              <h-select-option :value="9">9月</h-select-option>
              <h-select-option :value="10">10月</h-select-option>
              <h-select-option :value="11">11月</h-select-option>
              <h-select-option :value="12">12月</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="apTypeName">目标类型：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="apTypeName" style="width: 200px" v-model:value="searchParam.apTypeName" placeholder="" autocomplete="off" allowClear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :offset="18" :span="6" style="text-align: right">
            <h-button type="primary" @click="exportMonthPlanReport({ current: 1, pageSize: 10 })">
              <template #icon>
                <DownloadOutlined />
              </template>
              导出
            </h-button>
            <h-button style="margin-left: 10px" type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined /> 查询
            </h-button>
            <h-button style="margin-left: 10px" @click="reset">重置</h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :row-key="(record) => record.id"
          :data-source="dataSource"
          :pagination="pagination"
          :size="'small'"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'planValue'">
              <template v-if="record.planType == 1">
                <template v-if="record.planValue == 0">
                  0 /{{ record.planUnit }}
                </template>
                <template v-else>
                  {{ record.planValue }}/{{ record.planUnit }}
                </template>
              </template>
              <template v-else>
                {{ record.planDesc }}
              </template>
            </template>
            <template v-if="column.dataIndex === 'completePlanValue'">
              <template v-if="record.completePlanValue">
                {{ record.completePlanValue }}/{{ record.planUnit }}
              </template>
              <template v-else>
                {{ record.completePlanDesc }}
              </template>
            </template>
            <template v-if="column.dataIndex === 'completeRate' && record.completeRate != null"> {{ (record.completeRate*100).toFixed(2) }}% </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}
</style>
