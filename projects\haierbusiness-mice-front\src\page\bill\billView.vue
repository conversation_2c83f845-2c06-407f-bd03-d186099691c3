<script setup lang="ts">
import { Card as hCard, Upload as hUpload, <PERSON><PERSON> as hButton, <PERSON><PERSON><PERSON> as hDivider, Tabs as hTabs, TabPane as hTabPane, message,
    Collapse as hCollapse, CollapsePanel as hCollapsePanel, Form as hForm, FormItem as hFormItem, Row as hRow, Col as hCol, Input as hInput,
    DatePicker as hDatePicker, InputNumber as hInputNumber, Table as hTable, Select as hSelect, SelectOption as hSelectOption, Space as hSpace,
    TypographyText as hTypographyText, Tooltip as hTooltip, Spin as hSpin, Modal as hModal, Modal,
    Image as hImage } from 'ant-design-vue'
import { IStayBill, SupplementTypeConstant, TeaUseTypeConstant, BillFileTypeConstant, CarUsageConstant, CarUsageTimeConstant, HoldTimeTypeConstant, ConferenceTypeConstant, CateTypeConstant, LedTypeConstant,
    ICbBillData, ICbCateringBill, ICbPlaceBill, ICbVehicleBill, ICbInsuranceBill, ICbPresentBill, ICbOtherBill, IBillSupplements, IBaseBill,
    IFileCheckList, ICbServiceBill } from '@haierbusiness-front/common-libs';
import { ref, computed, onMounted, watch } from 'vue';
import dayjs, { Dayjs } from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import localeData from 'dayjs/plugin/localeData'
import weekday from 'dayjs/plugin/weekday'
import weekOfYear from 'dayjs/plugin/weekOfYear'
import weekYear from 'dayjs/plugin/weekYear'
import {
    guid
} from '@haierbusiness-front/utils';
import { billApi } from '@haierbusiness-front/apis';
import router from '../../router'
import { ColumnType } from 'ant-design-vue/lib/table';
import _ from 'lodash';
import { useResizeObserver } from '@vueuse/core'
import start from '@/assets/image/miceIcon/start.png';
import end from '@/assets/image/miceIcon/end.png';
import via from '@/assets/image/miceIcon/via.png';
import { CheckCircleTwoTone } from '@ant-design/icons-vue';


dayjs.extend(customParseFormat)
dayjs.extend(advancedFormat)
dayjs.extend(weekday)
dayjs.extend(localeData)
dayjs.extend(weekOfYear)
dayjs.extend(weekYear)

const currentRouter = ref()

type ConferenceType = 'STAY' | 'CATERING' | 'VEHICLE' | 'PLACE' | 'PRESENT' | 'MANPOWER' | 'SERVICE' | 'OTHER' | 'INSURANCE';

const url = import.meta.env.VITE_BUSINESS_URL;
const communicationUrl = import.meta.env.VITE_BUSINESS_COMMUNICATION_URL
const currrentUrl = import.meta.env.VITE_CURRENT_COMMUNICATION_URL

// 会展结算单上传
const fileList = ref<Array<any>>([])

// 弹出框内容url 
const modalPicUrl = ref('')
const modalPdfUrl = ref('')
// 结算单查看
const isSettlementBillCheck = ref(false)
const isSettlementBillPresentCheck = ref(false)

const isImageType = (url: string) => {
    var imageTypes = /(\jpg|\jpeg|\png|\gif|\webp)$/i;

    return imageTypes.test(url);
}

const openSettlementBillUrl = (currentUrl: string) => {
    isSettlementBillCheck.value = true
    window.open(url + currentUrl, '_blank')
}

const getSettlementBillCheck = () => {
   return isSettlementBillCheck.value;
}

const openSettlementBillPresentUrl = (currentUrl: string) => {
    isSettlementBillPresentCheck.value = true
    window.open(url + currentUrl, '_blank')
}

const getSettlementBillPresentCheck = () => {
   return isSettlementBillPresentCheck.value;
}

const top = ref(0)

const openFileUrl = (e: MouseEvent,currentUrl: string) => {
    const distanceFromTop = e.y < 600 ? 0 : e.y - 600
    top.value = distanceFromTop

    if(!currentUrl){
        showMessage('链接错误！')
        return
    }
    const completeUrl = url + currentUrl

    let flieArr = currentUrl.split('.')
    let suffix = flieArr[flieArr.length - 1]

    if(isImageType(suffix)) {
        modalPicUrl.value = currentUrl
        modalOpen()
    } else if (suffix === 'pdf') {
        modalPdfUrl.value = currentUrl
        modalOpen()
    } else {
        checkFile(currentUrl)
        window.open(completeUrl, '_blank')
    }
}

const checkFile = (currentUrl: string) => {
    const fileCheck = fileCheckList.value.find(o => o.url === currentUrl)
    if(fileCheck) {
        fileCheck.isCheck = true
    } 
}

const getCheck = (currentUrl: string) => {
    const fileCheck = fileCheckList.value.find(o => o.url === currentUrl)
    return fileCheck.isCheck;
}

//#region 字段自适应

const xs = 24

const sm = 12

const md = 8

const lg = 6

const input_lg = 8

const input_md = 12

const input_sm = 24


//#endregion


// 订单号
const code = ref('')


//#region 账单总信息

const data = ref<ICbBillData | undefined>({})

const BillsTotal = computed(() => {
    let total = stayTotal.value + cateringTotal.value + placeTotal.value + vehicleTotal.value + insuranceTotal.value
        + presentTotal.value + otherBillTotal.value
    if(cbServiceBill.value) {
        total += cbServiceBill.value.actualPrice ? cbServiceBill.value.actualPrice * 1 : 0
    }
    return round(total)
})

const BillsDiff = computed(() => {
    let total = stayDiff.value + cateringDiff.value + placeDiff.value + vehicleDiff.value + insuranceDiff.value
        + presentDiff.value + otherBillDiff.value
    if(cbServiceBill.value) {
        const actualPrice = cbServiceBill.value.actualPrice ? (cbServiceBill.value.actualPrice * 1) : 0
        const biddingPrice = cbServiceBill.value.biddingPrice ? (cbServiceBill.value.biddingPrice * 1) : 0
        total += actualPrice - biddingPrice
    }
    return round(total)
})

const spinning = ref(false)

const getBill = async () => {
    spinning.value = true
    const billData = await billApi.getBill(code.value)
    const bill = billData.data
    data.value = bill
    if (bill && bill.cbBillResponse) {
        if(bill.cbBillResponse.cbStayBills && bill.cbBillResponse.cbStayBills.length > 0) {
            bill.cbBillResponse.cbStayBills.map((item, index) => {
                stayActiveKey.value.push('stayBill-' + index)
                stayBillActiveKey.value.push('stayBill-' + 'detail-' + index)
                stayBillActiveKey.value.push('stayBill-' + 'other-' + index)
                stayBillActiveKey.value.push('stayBill-' + 'file-' + index)
                item.detailList && item.detailList.map(detail => {
                    detail.checkInDate && (detail.checkInDate = dayjs(detail.checkInDate));
                    detail.key = guid()
                })
                handleOtherDate(item)
                handleFile(item, 'STAY', index)
            })
            stayBills.value = bill.cbBillResponse.cbStayBills ?? []
        }
        
        if(bill.cbBillResponse.cbCateringBills && bill.cbBillResponse.cbCateringBills.length > 0) {
            bill.cbBillResponse.cbCateringBills.map((item, index) => {
                cateringActiveKey.value.push('cateringBill-' + index)
                cateringBillActiveKey.value.push('cateringBill-' + 'other-' + index)
                cateringBillActiveKey.value.push('cateringBill-' + 'file-' + index)
                handleOtherDate(item)
                handleFile(item, 'CATERING', index)
            })
            cateringBills.value = bill.cbBillResponse.cbCateringBills ?? []
        }

        if(bill.cbBillResponse.cbVehicleBills && bill.cbBillResponse.cbVehicleBills.length > 0) {
            bill.cbBillResponse.cbVehicleBills.map((item, index) => {
                vehicleActiveKey.value.push('vehicleBill-' + index)
                vehicleBillActiveKey.value.push('vehicleBill-' + 'detail-' + index)
                vehicleBillActiveKey.value.push('vehicleBill-' + 'other-' + index)
                vehicleBillActiveKey.value.push('vehicleBill-' + 'file-' + index)
                item.detailList && item.detailList.map(detail => {
                    detail.key = guid()
                })
                handleOtherDate(item)
                handleFile(item, 'VEHICLE', index)
            })
            vehicleBills.value = bill.cbBillResponse.cbVehicleBills ?? []
        }
        
        if(bill.cbBillResponse.cbPlaceBills && bill.cbBillResponse.cbPlaceBills.length > 0) {
            bill.cbBillResponse.cbPlaceBills.map((item, index) => {
                placeActiveKey.value.push('placeBill-' + index)
                placeBillActiveKey.value.push('placeBill-' + 'facility-' + index)
                placeBillActiveKey.value.push('placeBill-' + 'other-' + index)
                placeBillActiveKey.value.push('placeBill-' + 'file-' + index)
                item.facilityDetailList && item.facilityDetailList.map(detail => {
                    detail.key = guid()
                })
                handleOtherDate(item)
                handleFile(item, 'PLACE', index)
            })
            placeBills.value = bill.cbBillResponse.cbPlaceBills ?? []
        }
        insuranceBills.value = bill.cbBillResponse.insuranceBills ?? []
        insuranceActiveKey.value = bill.cbBillResponse.insuranceBills ? ['insuranceBill-0'] : []

        if(bill.cbBillResponse.cbPresentBills && bill.cbBillResponse.cbPresentBills.length > 0) {
            bill.cbBillResponse.cbPresentBills.map((item, index) => {
                presentActiveKey.value.push('presentBill-' + index)
                presentBillActiveKey.value.push('presentBill-' + 'other-' + index)
                presentBillActiveKey.value.push('presentBill-' + 'file-' + index)
                handleOtherDate(item)
                handleFile(item, 'PRESENT', index)
            })
            presentBills.value = bill.cbBillResponse.cbPresentBills ?? []
        } else {
            isSettlementBillPresentCheck.value = true
        }

        if(bill.cbBillResponse.cbOtherBills && bill.cbBillResponse.cbOtherBills.length > 0) {
            bill.cbBillResponse.cbOtherBills.map((item, index) => {
                otherActiveKey.value.push('otherBill-' + index)
                otherBillActiveKey.value.push('otherBill-' + 'other-' + index)
                otherBillActiveKey.value.push('otherBill-' + 'file-' + index)
                handleOtherDate(item)
                handleFile(item, 'OTHER', index)
            })
            otherBills.value = bill.cbBillResponse.cbOtherBills ?? []
        }

        if(bill.cbBillResponse.cbServiceBills && bill.cbBillResponse.cbServiceBills.length > 0) {
            cbServiceBill.value = bill.cbBillResponse.cbServiceBills[0]
        }
    }
    spinning.value = false
}

// 判断会议类型
const isMiceType = () => {
    var code = data.value.cbBillResponse.code;
    return !code.includes("LC") && !code.includes("LE");
};

const handleOtherDate = <T extends IBaseBill>(data: T) => {
    data.otherList && data.otherList.map(other => {
        other.key = guid();
        other.projectDate && (other.projectDate = dayjs(other.projectDate, 'YYYY-MM-DD'));
    })
}

const handleFile = <T extends IBaseBill>(data: T, type: string = 'STAY', billIndex: number) => {
    if(data.invoices && data.invoices.length > 0) {
        let invoices: Array<any> = []
        data.invoices.map((item, index) => {
            addFileToCheckList(type, billIndex, item, index, 'INVOICE')
            const file = {
                name: '附件' + (index + 1),
                url: item,
                uid: guid()
            }
            invoices.push(file)
        })
        data.invoices = invoices
    }
    if(data.memos && data.memos.length > 0) {
        let memos: Array<any> = []
        data.memos.map((item, index) => {
            addFileToCheckList(type, billIndex, item, index, 'MEMO')
            const file = {
                name: '附件' + (index + 1),
                url: item,
                uid: guid()
            }
            memos.push(file)
        })
        data.memos = memos
    }
    if(data.contracts && data.contracts.length > 0) {
        let contracts: Array<any> = []
        data.contracts.map((item, index) => {
            addFileToCheckList(type, billIndex, item, index, 'CONTRACT')
            const file = {
                name: '附件' + (index + 1),
                url: item,
                uid: guid()
            }
            contracts.push(file)
        })
        data.contracts = contracts
    }
    if(data.others && data.others.length > 0) {
        let others: Array<any> = []
        data.others.map((item, index) => {
            addFileToCheckList(type, billIndex, item, index, 'OTHER')
            const file = {
                name: '附件' + (index + 1),
                url: item,
                uid: guid()
            }
            others.push(file)
        })
        data.others = others
    }
}

onMounted(async() => {
    currentRouter.value = await router
    code.value = currentRouter.value.currentRoute.query?.code ?? ''
    await getBill()
})

//#endregion

const round = (number: number) => {
    return _.round(number, 2)
}

//#region 管理费

const cbServiceBill = ref<ICbServiceBill>()


//#endregion

// #region  住宿

const stayActiveKey = ref<Array<string>>([]);
const stayBillActiveKey = ref<Array<string>>([]);

const stayBills = ref<Array<IStayBill>>([]);

const stayTotal = ref<number>(0)
const stayDiff = ref<number>(0)


const stayBillDetailColumns: ColumnType[] = [
  {
    title: '房型',
    dataIndex: 'roomType',
  },
  {
    title: '入住日期',
    dataIndex: 'checkInDate',
  },
  {
    title: '单价',
    dataIndex: 'price',
  },
  {
    title: '间夜数',
    dataIndex: 'nights',
  }
];

const supplementColumns: ColumnType[] = [
  {
    title: '名称',
    dataIndex: 'projectName',
    width: 150,
    fixed: 'left'
  },
  {
    title: '日期',
    dataIndex: 'projectDate',
    width: 150
  },
  {
    title: '类别',
    dataIndex: 'type',
    width: 150
  },
  {
    title: '数量',
    dataIndex: 'num',
    width: 150
  },
  {
    title: '单价',
    dataIndex: 'price',
    width: 150
  },
  {
    title: '描述',
    dataIndex: 'desc',
    width: 150
  },
];

watch(() => stayBills.value, (newValue)=> {
    let allTotal = 0
    let biddingTotal = 0
    if(newValue && newValue.length > 0) {
        newValue.map((item) => {
            let nights = 0
            let otherTotal = 0
            if(item.detailList && item.detailList.length > 0) {
                item.detailList.map((detail) => {
                    detail.price = item.actualPrice
                    nights += (detail.nights ?? 0) * 1
                })
            }
            if(item.otherList && item.otherList.length > 0) {
                item.otherList.map((other) => {
                    otherTotal += ((other.price ?? 0) * (other.num ?? 0))
                })
            }
            item.actualNights = nights ?? 0
            item._otherTotal = round(otherTotal)
            item._billTotal = round((item.actualPrice ?? 0) * (item.actualNights ?? 0))
            item._biddingTotal = round((item.biddingPrice ?? 0) * (item.nights ?? 0))
            allTotal += item._billTotal + item._otherTotal
            biddingTotal += item._biddingTotal
        })
        stayTotal.value = round(allTotal)
        stayDiff.value = round(allTotal - biddingTotal)
    }
}, { immediate: true, deep: true })

// #endregion

//#region 餐饮

const cateringActiveKey = ref<Array<string>>([]);

const cateringBillActiveKey = ref<Array<string>>([]);

const cateringBills = ref<Array<ICbCateringBill>>([])

const cateringTotal = ref<number>(0)
const cateringDiff = ref<number>(0)

watch(() => cateringBills.value, (newValue)=> {
    let allTotal = 0
    let biddingTotal = 0
    if(newValue && newValue.length > 0) {
        newValue.map((item) => {
            let otherTotal = 0
            if(item.otherList && item.otherList.length > 0) {
                item.otherList.map((other) => {
                    otherTotal += ((other.price ?? 0) * (other.num ?? 0))
                })
            }
            item._otherTotal = round(otherTotal)
            item._billTotal = round((item.actualPrice ?? 0) * (item.actualNum ?? 0))
            allTotal += item._billTotal + item._otherTotal
            item._biddingTotal = round((item.biddingPrice ?? 0) * (item.personNum ?? 0))
            biddingTotal += item._biddingTotal
        })
        cateringTotal.value = round(allTotal)
        cateringDiff.value = round(allTotal - biddingTotal)
    }
}, { immediate: true, deep: true })

//#endregion

//#region 场地

const placeActiveKey = ref<Array<string>>([]);

const placeBillActiveKey = ref<Array<string>>([]);

const placeBills = ref<Array<ICbPlaceBill>>([])

const placeTotal = ref<number>(0)
const placeDiff = ref<number>(0)

const placeBillFacilityDetailColumns: ColumnType[] = [
  {
    title: '设备设施项目 ',
    dataIndex: 'project',
    width: 200,
    fixed: 'left'
  },
  {
    title: '使用会议厅',
    dataIndex: 'hall',
    width: 200,
  },
  {
    title: '单价',
    dataIndex: 'unitPrice',
    width: 100
  },
  {
    title: '数量',
    dataIndex: 'num',
    width: 100
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
  }
];

watch(() => placeBills.value, (newValue)=> {
    let allTotal = 0
    let biddingTotal = 0
    if(newValue && newValue.length > 0) {
        newValue.map((item) => {
            // 合计
            let billTotal = 0
            // 竞价合计
            let _biddingTotal = 0
            // 设施实际合计
            let facilityTotal = 0
            // 其他合计
            let otherTotal = 0

            // 设施合计
            let biddingFacilityTotal = 0
            
            if(item.facility && item.facility.length > 0) {
                // 计算竞价设施合计
                biddingFacilityTotal = _.sumBy(item.facility, function(o){
                    return (o.num ?? 0) * (o.price ?? 0)
                })
            }
            
            if(item.facilityDetailList && item.facilityDetailList.length > 0) {
                // 设施
                facilityTotal = _.sumBy(item.facilityDetailList, function(o){
                    return (o.unitPrice ?? 0) * (o.num ?? 0)
                })
            }
            if(item.otherList && item.otherList.length > 0) {
                // 补充
                otherTotal = _.sumBy(item.otherList, function(o){
                    return (o.price ?? 0) * (o.num ?? 0)
                })
            }
            item._facilityTotal = round(facilityTotal)
            item._otherTotal = round(otherTotal)
            billTotal = (item.actualPrice ?? 0) * 1 + item._facilityTotal
            _biddingTotal = (item.biddingPrice ?? 0) + biddingFacilityTotal
            if(item.teaBreak === 1) {
                billTotal += (item.billTeaNumber ?? 0) * (item.billTeaUnitPrice ?? 0)
                _biddingTotal += (item.teaNumber ?? 0) * (item.teaBidUnitPrice ?? 0)
            }
            if(item.layout === 1) {
                billTotal += (item.billBuildCost ?? 0)
                _biddingTotal += (item.bidBuildCost ?? 0)
            }
            
            if(item.isLed === 1) {
                billTotal += (item.billLedPrice ?? 0)
                _biddingTotal += (item.ledBidPrice ?? 0)
            }

            item._billTotal = round(billTotal)
            item._biddingTotal = round(_biddingTotal)
            allTotal += billTotal + item._otherTotal
            biddingTotal += _biddingTotal
        })
        placeTotal.value = round(allTotal)
        placeDiff.value = round(allTotal - biddingTotal)
    }
}, { immediate: true, deep: true })

//#endregion

//#region 用车

const vehicleActiveKey = ref<Array<string>>([]);

const vehicleBillActiveKey = ref<Array<string>>([]);

const vehicleBills = ref<Array<ICbVehicleBill>>([])

const vehicleTotal = ref<number>(0)
const vehicleDiff = ref<number>(0)

const vehicleBillDetailColumns: ColumnType[] = [
  {
    title: '用车时段',
    dataIndex: 'timeFrameList',
    fixed: 'left',
    width: 300
  },
  {
    title: '车辆类型',
    dataIndex: 'carType',
    width: 150
  },
  {
    title: '品牌',
    dataIndex: 'brand',
    width: 150
  },
  {
    title: '车龄',
    dataIndex: 'carAge',
    width: 150
  },
  {
    title: '座位数',
    dataIndex: 'seats',
    width: 150
  },
  {
    title: '车牌号',
    dataIndex: 'carNo',
    width: 150
  },
  {
    title: '驾驶员',
    dataIndex: 'driver',
    width: 150
  },
  {
    title: '公里数',
    dataIndex: 'kmNum',
    width: 150
  },
  {
    title: '公里费用',
    dataIndex: 'kmPrice',
    width: 150
  },
  {
    title: '过路费',
    dataIndex: 'toll',
    width: 150
  },
  {
    title: '实际去向',
    dataIndex: 'practical',
    width: 300
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200
  }
];

watch(() => vehicleBills.value, (newValue)=> {
    let allTotal = 0
    let biddingTotal = 0
    if(newValue && newValue.length > 0) {
        newValue.map((item) => {
            let otherTotal = 0
            let detailTotal = 0
            if(item.detailList && item.detailList.length > 0) {
                item.detailList.map((detail) => {
                    detailTotal += ((detail.kmNum ?? 0 ) * (detail.kmPrice ?? 0)) + (detail.toll ?? 0) * 1
                })
            }
            if(item.otherList && item.otherList.length > 0) {
                item.otherList.map((other) => {
                    otherTotal += ((other.price ?? 0) * (other.num ?? 0))
                })
            }
            item.actualNum = item.detailList?.length ?? 0
            item._otherTotal = round(otherTotal)
            item._billTotal = round(detailTotal)
            item._biddingTotal = round((item.biddingPrice ?? 0) * (item.vehicleNum ?? 0))
            allTotal += item._billTotal + item._otherTotal
            biddingTotal += item._biddingTotal
        })
        vehicleTotal.value = round(allTotal)
        vehicleDiff.value = round(allTotal - biddingTotal)
    }
}, { immediate: true, deep: true })

//#endregion

//#region 保险

const insuranceActiveKey = ref<Array<string>>([]);

const insuranceBills = ref<Array<ICbInsuranceBill>>([])

const insuranceTotal = ref<number>(0)
const insuranceDiff = ref<number>(0)

watch(() => insuranceBills.value, (newValue)=> {
    let allTotal = 0
    let biddingTotal = 0
    if(newValue && newValue.length > 0) {
        newValue.map((item) => {
            allTotal += (item.actualPrice ?? 0) * (item.insuredDays ?? 0) ?? 0
            biddingTotal += item.schemeBiddingPrice ?? 0
        })

        insuranceTotal.value = round(allTotal)
        insuranceDiff.value = round(allTotal - biddingTotal)
    }
}, { immediate: true, deep: true })

//#endregion

//#region 礼品

const presentActiveKey = ref<Array<string>>([]);

const presentBillActiveKey = ref<Array<string>>([]);

const presentBills = ref<Array<ICbPresentBill>>([])

const presentTotal = ref<number>(0)
const presentDiff = ref<number>(0)

watch(() => presentBills.value, (newValue)=> {
    let allTotal = 0
    let biddingTotal = 0
    if(newValue && newValue.length > 0) {
        newValue.map((item) => {
            let otherTotal = 0
            if(item.otherList && item.otherList.length > 0) {
                item.otherList.map((other) => {
                    otherTotal += ((other.price ?? 0) * (other.num ?? 0))
                })
            }
            item._otherTotal = round(otherTotal)
            item._billTotal = round((item.actualPrice ?? 0) * (item.actualNum ?? 0))
            item._biddingTotal = round((item.biddingPrice ?? 0) * (item.requireNum ?? 0))
            allTotal += item._billTotal + item._otherTotal
            biddingTotal += item._biddingTotal
        })
        presentTotal.value = round(allTotal)
        presentDiff.value = round(allTotal - biddingTotal)
    }
}, { immediate: true, deep: true })

//#endregion

//#region 其他

const otherActiveKey = ref<Array<string>>([]);

const otherBillActiveKey = ref<Array<string>>([]);

const otherBills = ref<Array<ICbOtherBill>>([])

const otherBillTotal = ref<number>(0)
const otherBillDiff = ref<number>(0)

watch(() => otherBills.value, (newValue)=> {
    let allTotal = 0
    let biddingTotal = 0
    if(newValue && newValue.length > 0) {
        newValue.map((item) => {
            let otherTotal = 0
            if(item.otherList && item.otherList.length > 0) {
                item.otherList.map((other) => {
                    otherTotal += ((other.price ?? 0) * (other.num ?? 0))
                })
            }
            item._otherTotal = round(otherTotal)
            item._billTotal = round((item.actualPrice ?? 0) * (item.actualNum ?? 0))
            item._biddingTotal = round((item.biddingPrice ?? 0) * (item.requireNum ?? 0))
            allTotal += item._billTotal + item._otherTotal
            biddingTotal += item._biddingTotal
        })
        otherBillTotal.value = round(allTotal)
        otherBillDiff.value = round(allTotal - biddingTotal)
    }
}, { immediate: true, deep: true })

//#endregion

const computedDiff = (bill: number = 0, bidding: number = 0) => {
    return round(bill - bidding)
}

const showMessage = ((text: string, isSuccess: boolean = false) => {
    if (isSuccess) {
        message.success({
            content:() => text,
            style: {
                marginTop: '50vh',
            }
        })
    } else {
        Modal.error({
            title: text,
        })
        // message.error({
        //     content:() => text,
        //     style: {
        //         marginTop: '50vh',
        //     }
        // })
    }
    
})

const fileCheckList = ref<Array<IFileCheckList>>([] as Array<IFileCheckList>)

const addFileToCheckList = (type: string, billIndex: number, url:string, index: number, fileType?: 'INVOICE' | 'MEMO' | 'CONTRACT' | 'OTHER') => {
    const fileCheck: IFileCheckList = {
        type,
        billIndex: billIndex + 1,
        fileType,
        index: index + 1,
        url,
        isCheck: false
    }
    fileCheckList.value.push(fileCheck)
}

const open = ref<boolean>(false)
const time = ref<number>(0)
const timer = ref()
const countdown = ref(import.meta.env.VITE_COUNT_DOWN)

const clearTimer = () => {
    clearInterval(timer.value); // 清除定时器
    countdown.value = import.meta.env.VITE_COUNT_DOWN
    timer.value = null
}

const modalOpen = () => {
    open.value = true
    // timer.value = setInterval(() => {
    //     if (countdown.value > 0) {
    //         countdown.value--;
    //     }
    // }, 500)
}

const handleClose = () => {
    const url = modalPicUrl.value ? modalPicUrl.value : modalPdfUrl.value
    checkFile(url)  
    // clearTimer()
    modalPicUrl.value = ''
    modalPdfUrl.value = ''
    open.value = false
}

const card = ref()

watch(() => card.value, (newValue) => {
    if(newValue) {
        useResizeObserver(card.value, (el) => {
            const entry = el[0]
            const { width, height } = entry.contentRect
            console.log('高度', height);
            window.parent.postMessage({ height }, communicationUrl);
        })

    }
}, { deep: true })

const handleMessage = (event: any) => {
    if (event.origin === communicationUrl) {
        // 处理消息
        if(event.data === 'checkFile' || event.data === 'process_checkFile') {
            event.source.postMessage({
                type: 'checkSuccess',
                originType: event.data,
                message: '成功！'
            }, event.origin);
            // if (!isSettlementBillCheck.value) {
                // event.source.postMessage({
                //     type: 'checkError',
                //     originType: event.data,
                //     message: '请查看会展结算单附件，并确保已经仔细查看所有附件！'
                // }, event.origin);
            // } else if (!isSettlementBillPresentCheck.value) {
                // event.source.postMessage({
                //     type: 'checkError',
                //     originType: event.data,
                //     message: '请查看礼品结算单附件，并确保已经仔细查看所有附件！'
                // }, event.origin);
            // } else {
                // const fileCheck = fileCheckList.value.find(o => o.isCheck === false)
                // if (fileCheck) {
                //     const message = '请查看' + ConferenceTypeConstant[fileCheck.type! as ConferenceType].desc + '账单'+ fileCheck.billIndex + '的' + BillFileTypeConstant[fileCheck.fileType!].desc + '附件' + fileCheck.index + '，并确保已经仔细查看所有附件！'
                //     event.source.postMessage({
                //         type: 'checkError',
                //         originType: event.data,
                //         message: message
                //     }, event.origin);
                // } else {
                //     event.source.postMessage({
                //         type: 'checkSuccess',
                //         originType: event.data,
                //         message: '成功！'
                //     }, event.origin);
                // }
            // }
        }
        // 发送回执给发送方
        // event.source.postMessage('Message received', event.origin);
    }
}

window.addEventListener('message', handleMessage, false)

const formattedTimeRange = (vehicleBill, index) => {
   // 确保detailList存在且索引有效
   if (vehicleBill.detailList && vehicleBill.detailList[index]) {
    const startTime = new Date(vehicleBill.detailList[index].startTimeFrame);
    const endTime = new Date(vehicleBill.detailList[index].endTimeFrame);
    
    
    
    return formattedDate(startTime) + ' - ' + formattedDate(endTime);
  } else {
    // 如果数据不存在，返回适当的信息
    return "时间信息缺失";
  }
};

const formattedDate = (date) => {
  const year = date.getFullYear();
  const month = `${date.getMonth() + 1}`.padStart(2, '0'); // 月份是从0开始的，所以需要+1
  const day = `${date.getDate()}`.padStart(2, '0');
  const hours = `${date.getHours()}`.padStart(2, '0');
  const minutes = `${date.getMinutes()}`.padStart(2, '0');
  const seconds = `${date.getSeconds()}`.padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

</script>

<template>

    <div ref="card">
        <h-spin :spinning="spinning" >
            <h-card size="small" title="会议账单"  >
                <template #extra>
                    <span class="total font-size-12">竞：{{ computedDiff(BillsTotal, BillsDiff) }}</span>
                    <span class="total font-size-12">账：{{ BillsTotal }}</span>
                    <span class="font-size-12">差：<span :class="{'negative': BillsDiff > 0, 'positiv': BillsDiff <= 0 }">{{ BillsDiff }}</span></span>
                </template>
                <div class="upload" >
                    <div class="flex">
                        <span class="font">会展结算单：</span>
                    </div>
                    <div class="flex">
                        <div class="flex file-hover" v-if="data?.cbBillResponse?.settlementBillPath">
                            <div class="flex" @click="openSettlementBillUrl(data?.cbBillResponse?.settlementBillPath ?? '')">
                                <h-tooltip class="flex">
                                    <template #title>附件</template>
                                    <h-typography-text class="file-name" ellipsis content="附件" />
                                    <a-space>
                                        <check-circle-two-tone v-if="getSettlementBillCheck()" two-tone-color="#52c41a" />
                                    </a-space>
                                </h-tooltip>
                            </div>
                            <div class="flex" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="upload" style="margin-top: 10px;" v-if="presentBills.length > 0">
                    <div class="flex">
                        <span class="font">礼品结算单：</span>
                    </div>
                    <div class="flex">
                        <div class="flex file-hover" v-if="data?.cbBillResponse?.settlementBillPresentPath">
                            <div class="flex" @click="openSettlementBillPresentUrl(data?.cbBillResponse?.settlementBillPresentPath ?? '')">
                                <h-tooltip class="flex">
                                    <template #title>附件</template>
                                    <h-typography-text class="file-name" ellipsis content="附件" />
                                    <a-space>
                                        <check-circle-two-tone v-if="getSettlementBillPresentCheck()" two-tone-color="#52c41a" />
                                    </a-space>
                                </h-tooltip>
                            </div>
                            <div class="flex" >
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="cbServiceBill">
                    <h-row class="manage">
                        <h-col :xs="xs" :sm="sm" :md="md" class="align-items">
                            业务运营管理费收取比例：{{ cbServiceBill?.ratio }}%
                        </h-col>
                        <h-col :xs="xs" :sm="sm" :md="md" class="align-items">
                            运营管理费总价：{{ cbServiceBill?.biddingPrice }}元
                        </h-col>
                        <h-col :xs="xs" :sm="sm" :md="md" class="align-items">
                            实际总价：{{ cbServiceBill?.actualPrice }}元
                        </h-col>
                    </h-row>
                </div>
                <h-divider size="small" dashed />
                <h-tabs type="card" size="large">
                    <h-tab-pane key="1" v-if="stayBills && stayBills.length > 0">
                        <template #tab>
                            <div class="tab-title flex">
                                住宿账单
                            </div>
                            <div class="tab-total flex font-size-12">
                                <span >总：{{ stayTotal }}元</span>
                                <span>差：<span :class="{'negative': stayDiff > 0, 'positiv': stayDiff <= 0 }">{{ stayDiff }}元</span></span>
                            </div>
                        </template>
                        
                        <h-collapse v-model:activeKey="stayActiveKey">
                            <h-collapse-panel  :header="'账单' + (index + 1)" style="background-color: #fff;margin-bottom:28px;" 
                                v-for="(stayBill, index) in stayBills" :key="'stayBill-' + index">
                                <template #extra>
                                    <span class="total font-size-12">竞：{{ stayBill._biddingTotal ?? 0}}元</span>
                                    <span class="total font-size-12">账：{{ (stayBill._billTotal ?? 0) + (stayBill._otherTotal ?? 0) }}元</span>
                                    <span class="font-size-12">
                                        差：<span  :class="{'negative': computedDiff((stayBill._billTotal ?? 0) + (stayBill._otherTotal ?? 0), stayBill._biddingTotal) > 0, 'positiv': computedDiff((stayBill._billTotal ?? 0) + (stayBill._otherTotal ?? 0), stayBill._biddingTotal) <= 0 }" >{{ computedDiff((stayBill._billTotal ?? 0) + (stayBill._otherTotal ?? 0), stayBill._biddingTotal) }}元</span>
                                    </span>
                                </template>
                                <h-form
                                    ref="formRef"
                                    style="margin-top: 20px;"
                                    :model="stayBill"
                                >
                                    <h-row>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg" >
                                            <h-form-item
                                                label="酒店名称"
                                            >
                                                {{ stayBill.supplier }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg" >
                                            <h-form-item
                                                label="开票全称"
                                            >
                                                {{ stayBill.billingFullName }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="星级"
                                            >
                                                {{ stayBill.level }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="位置"
                                            >
                                                {{ stayBill.address }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="房型"
                                            >
                                                {{ stayBill.roomType }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="间夜数"
                                            >
                                                {{ stayBill.nights + '间夜' }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="房间数"
                                            >
                                                {{ stayBill.roomNum }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="日期"
                                            >
                                                {{ dayjs(stayBill.checkInDate).format("YYYY-MM-DD") + '至' + dayjs(stayBill.checkOutDate).format("YYYY-MM-DD") }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="竞价单价"
                                            >
                                                {{ stayBill.biddingPrice }}元
                                            </h-form-item>
                                        </h-col>
                                        
                                    </h-row>
                                    <h-row>
                                        <h-col :sm="input_sm" :md="input_md" :lg="input_lg">
                                            <h-form-item
                                                label="实际单价"
                                                name="actualPrice"
                                            >
                                            {{ stayBill.actualPrice }}元/间夜
                                            </h-form-item>
                                        </h-col>
                                        <h-col :sm="input_sm" :md="input_md" :lg="input_lg">
                                            <h-form-item
                                                label="实际数量"
                                            >
                                                {{ stayBill.actualNights }}间夜
                                            </h-form-item>
                                        </h-col>
                                    </h-row>
                                </h-form>
                                
                                <h-collapse v-model:activeKey="stayBillActiveKey" style="background: rgb(255, 255, 255)">
                                    <h-collapse-panel :key="'stayBill-' + 'detail-' + index" header="明细流水" >
                                        <template #extra>
                                            <span class="total font-size-12">合计：{{ stayBill._billTotal }}元</span>
                                        </template>
                                        <div class="table">
                                            <h-table :columns="stayBillDetailColumns" :data-source="stayBill.detailList" :pagination="false" bordered>
                                                <template #bodyCell="{ column, text, record, index }">
                                                    <template v-if="column.dataIndex === 'checkInDate'">
                                                        {{ dayjs(stayBill.detailList![index].checkInDate).format("YYYY-MM-DD") }}
                                                    </template>
                                                </template>
                                            </h-table>
                                        </div>
                                        
                                    </h-collapse-panel>
                                    <h-collapse-panel :key="'stayBill-' + 'other-' + index" header="补充项目" >
                                        <template #extra>
                                            <span class="total font-size-12">合计：{{ stayBill._otherTotal }}元</span>
                                        </template>
                                        <div class="table">
                                            <h-table :columns="supplementColumns" :data-source="stayBill.otherList" :pagination="false" bordered :scroll="{ x: 1200 }">
                                                <template #bodyCell="{ column, text, record, index }">
                                                    <template v-if="column.dataIndex === 'type'">
                                                        {{ SupplementTypeConstant.ofType(stayBill.otherList![index].type)?.desc }}
                                                    </template>
                                                    <template v-if="column.dataIndex === 'projectDate'">
                                                        {{ dayjs(stayBill.otherList![index].projectDate).format("YYYY-MM-DD") }}
                                                    </template>
                                                </template>
                                            </h-table>
                                        </div>
                                    </h-collapse-panel>
                                    <h-collapse-panel :key="'stayBill-' + 'file-' + index" header="附件" >
                                        <div class="file-upload flex">
                                            <div class="flex upload-title">
                                                发票：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                            
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in stayBill.invoices" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />&nbsp;&nbsp;
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="file-upload flex">
                                            <div class="flex upload-title">
                                                水单：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in stayBill.memos" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="file-upload flex" v-if="isMiceType()">
                                            <div class="flex upload-title">
                                                一手合同：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in stayBill.contracts" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="file-upload flex">
                                            <div class="flex upload-title">
                                                其他上传：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in stayBill.others" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                    </h-collapse-panel>
                                </h-collapse>
                            </h-collapse-panel>
                        </h-collapse>
                        
                        

                    </h-tab-pane>
                    <h-tab-pane key="2" v-if="cateringBills && cateringBills.length > 0">
                        <template #tab>
                            <div class="tab-title flex">
                                餐饮账单
                            </div>
                            <div class="tab-total flex font-size-12">
                                <span >总：{{ cateringTotal }}元</span>
                                <span>差：<span :class="{'negative': cateringDiff > 0, 'positiv': cateringDiff <= 0 }">{{ cateringDiff }}元</span></span>
                            </div>
                        </template>
                        
                        
                        <h-collapse v-model:activeKey="cateringActiveKey">
                            <h-collapse-panel  :header="'账单' + (index + 1)" style="background-color: #fff;margin-bottom:28px;" 
                                v-for="(cateringBill, index) in cateringBills" :key="'cateringBill-' + index">
                                <template #extra>
                                    <span class="total font-size-12">竞：{{ cateringBill._biddingTotal }}元</span>
                                    <span class="total font-size-12">账：{{ (cateringBill._billTotal ?? 0) + (cateringBill._otherTotal ?? 0)  }}元</span>
                                    <span class="font-size-12">
                                        差：<span  :class="{'negative': computedDiff((cateringBill._billTotal ?? 0) + (cateringBill._otherTotal ?? 0), cateringBill._biddingTotal) > 0, 'positiv': computedDiff((cateringBill._billTotal ?? 0) + (cateringBill._otherTotal ?? 0), cateringBill._biddingTotal) <= 0 }" >{{ computedDiff((cateringBill._billTotal ?? 0) + (cateringBill._otherTotal ?? 0), cateringBill._biddingTotal) }}元</span>
                                    </span>
                                </template>

                                <h-form
                                    ref="formRef"
                                    style="margin-top: 20px;"
                                    :model="cateringBill"
                                >
                                    <h-row>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="酒店名称"
                                            >
                                                {{ cateringBill.supplier }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="餐饮日期"
                                            >
                                                {{ dayjs(cateringBill.eatDate).format("YYYY-MM-DD") }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="餐类"
                                            >
                                                {{ cateringBill.cateType && CateTypeConstant.ofType(cateringBill.cateType).desc }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="人数"
                                            >
                                                {{ cateringBill.personNum }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="竞价单价"
                                            >
                                                {{ cateringBill.biddingPrice }}元
                                            </h-form-item>
                                        </h-col>
                                    </h-row>
                                    <h-row>
                                        <h-col :sm="input_sm" :md="input_md" :lg="input_lg">
                                            <h-form-item
                                                label="实际单价/桌标"
                                                name="actualPrice"
                                            >
                                                {{ cateringBill.actualPrice }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :sm="input_sm" :md="input_md" :lg="input_lg">
                                            <h-form-item
                                                label="人数/总桌次"
                                                name="actualNum"
                                            >
                                                {{ cateringBill.actualNum }}
                                            </h-form-item>
                                        </h-col>
                                    </h-row>
                                </h-form>
                                
                                <h-collapse v-model:activeKey="cateringBillActiveKey" style="background: rgb(255, 255, 255)">
                                    <h-collapse-panel :key="'cateringBill-' + 'other-' + index" header="补充项目" >
                                        <template #extra>
                                            <span class="total font-size-12">合计：{{ cateringBill._otherTotal }}元</span>
                                        </template>
                                        <div class="table">
                                            <h-table :columns="supplementColumns" :data-source="cateringBill.otherList" :pagination="false" bordered :scroll="{ x: 1200 }">
                                                <template #bodyCell="{ column, text, record, index }">
                                                    <template v-if="column.dataIndex === 'type'">
                                                        {{ SupplementTypeConstant.ofType(cateringBill.otherList![index].type)?.desc }}
                                                    </template>
                                                    <template v-if="column.dataIndex === 'projectDate'">
                                                        {{ dayjs(cateringBill.otherList![index].projectDate).format("YYYY-MM-DD") }}
                                                    </template>
                                                </template>
                                            </h-table>
                                        </div>
                                    </h-collapse-panel>
                                    <h-collapse-panel :key="'cateringBill-' + 'file-' + index" header="附件" >
                                        <div class="file-upload flex">
                                            <div class="flex upload-title">
                                                发票：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in cateringBill.invoices" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="file-upload flex">
                                            <div class="flex upload-title">
                                                水单：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in cateringBill.memos" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="file-upload flex">
                                            <div class="flex upload-title">
                                                其他：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in cateringBill.others" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                    </h-collapse-panel>
                                </h-collapse>
                            </h-collapse-panel>
                        </h-collapse>
                        

                    </h-tab-pane>
                    <h-tab-pane key="3" v-if="placeBills && placeBills.length > 0">
                        <template #tab>
                            <div class="tab-title flex">
                                会场账单
                            </div>
                            <div class="tab-total flex font-size-12">
                                <span >总：{{ placeTotal }}元</span>
                                <span>差：<span :class="{'negative': placeDiff > 0, 'positiv': placeDiff <= 0 }">{{ placeDiff }}元</span></span>
                            </div>
                        </template>

                        <h-collapse v-model:activeKey="placeActiveKey">
                            <h-collapse-panel  :header="'账单' + (index + 1)" style="background-color: #fff;margin-bottom:28px;" 
                                v-for="(placeBill, index) in placeBills" :key="'placeBill-' + index">
                                <template #extra>
                                    <span class="total font-size-12">竞：{{ placeBill._biddingTotal ?? 0}}元</span>
                                    <span class="total font-size-12">账：{{ (placeBill._billTotal ?? 0) + (placeBill._otherTotal ?? 0) }}元</span>
                                    <span class="font-size-12">
                                        差：<span  :class="{'negative': computedDiff((placeBill._billTotal ?? 0) + (placeBill._otherTotal ?? 0), placeBill._biddingTotal) > 0, 'positiv': computedDiff((placeBill._billTotal ?? 0) + (placeBill._otherTotal ?? 0), placeBill._biddingTotal) <= 0 }" >{{ computedDiff((placeBill._billTotal ?? 0) + (placeBill._otherTotal ?? 0), placeBill._biddingTotal) }}元</span>
                                    </span>
                                </template>
                                <h-form
                                    ref="formRef"
                                    style="margin-top: 20px;"
                                    :model="placeBill"
                                >
                                    <h-row>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="酒店名称"
                                            >
                                                {{ placeBill.supplier }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="酒店开票全称"
                                            >
                                                {{ placeBill.billingFullName }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="场地"
                                            >
                                                {{ placeBill.area }}
                                            </h-form-item>
                                        </h-col>

                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="可容纳人数"
                                            >
                                                {{ placeBill.capacity }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="场地实际数量"
                                                name="actualNum"
                                            >
                                                1
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="竞价单价"
                                            >
                                                {{ placeBill.biddingPrice }}元
                                            </h-form-item>
                                        </h-col>
                                        <h-col :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="场地实际单价"
                                                name="actualPrice"
                                            >
                                                {{ placeBill.actualPrice }}元
                                            </h-form-item>
                                        </h-col>
                                        <h-col :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="参会人数"
                                                name="meetingNum"
                                            >
                                                {{ placeBill.meetingNum }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="面积"
                                            >
                                                {{ placeBill.area }}平米
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="层高"
                                            >
                                                {{ placeBill.floor }}米
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="会议日期"
                                            >
                                                {{ dayjs(placeBill.holdDate).format("YYYY-MM-DD") }}{{ HoldTimeTypeConstant.ofType(placeBill.holdTime)?.desc }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="桌型"
                                            >
                                                {{ placeBill.tableType }}
                                            </h-form-item>
                                        </h-col>
                                    </h-row>
                                    <h-row>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="布展"
                                            >
                                                {{ placeBill.layout === 1 ? '有布展' : '无布展' }}
                                            </h-form-item>
                                        </h-col>
                                        <template v-if="placeBill.layout === 1">
                                            <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                                <h-form-item
                                                    label="会场搭建费用"
                                                >
                                                    {{ placeBill.bidBuildCost }}元
                                                </h-form-item>
                                            </h-col>
                                            <h-col :sm="input_sm" :md="input_md" :lg="input_lg" v-if="placeBill.layout === 1">
                                                <h-form-item
                                                    label="搭建场地实际费用"
                                                    name="billBuildCost"
                                                >
                                                    {{ placeBill.billBuildCost }}
                                                </h-form-item>
                                            </h-col>
                                            <h-col :sm="24" :md="12">
                                                <h-form-item
                                                    label="布展时间"
                                                >
                                                    {{ placeBill.layoutTimeLimitBegin}}~{{ placeBill.layoutTimeLimitEnd}}
                                                </h-form-item>
                                            </h-col>
                                        </template>
                                    </h-row>
                                    <h-row>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="茶歇"
                                            >
                                                {{ placeBill.teaBreak === 1 ? '有茶歇' : '无茶歇' }}
                                            </h-form-item>
                                        </h-col>
                                        <template v-if="placeBill.teaBreak === 1">
                                            <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                                <h-form-item
                                                    label="茶歇使用"
                                                >
                                                    {{ TeaUseTypeConstant.ofType(placeBill.teaUse)?.desc }}
                                                </h-form-item>
                                            </h-col>
                                            <template v-if="placeBill.teaUse === 1">
                                                <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                                    <h-form-item
                                                        label="茶歇单价"
                                                    >
                                                        {{ placeBill.teaBidUnitPrice }}元
                                                    </h-form-item>
                                                </h-col>
                                                <h-col :sm="sm" :md="md" :lg="lg" v-if="placeBill.teaBreak === 1 && placeBill.teaUse === 1">
                                                    <h-form-item
                                                        label="实际茶歇单价"
                                                        name="billTeaUnitPrice"
                                                        :rules="{
                                                            required: true,
                                                            message: '请输入茶歇单价',
                                                        }"
                                                    >
                                                        {{ placeBill.billTeaUnitPrice }} 元
                                                    </h-form-item>
                                                </h-col>
                                                <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                                    <h-form-item
                                                        label="使用人数"
                                                    >
                                                        {{ placeBill.teaNumber }}
                                                    </h-form-item>
                                                </h-col>
                                                <h-col :sm="sm" :md="md" :lg="lg" v-if="placeBill.teaBreak === 1 && placeBill.teaUse === 1">
                                                    <h-form-item
                                                        label="实际人数"
                                                        name="billTeaNumber"
                                                    >
                                                        {{ placeBill.billTeaNumber }}
                                                    </h-form-item>
                                                </h-col>
                                            </template>
                                        </template>
                                    </h-row>
                                    <h-row>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="是否需要LED"
                                            >
                                                {{ placeBill.isLed === 1? '是' : '否' }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="LED使用"
                                            >
                                                {{ placeBill.ledUse === 1? '收费' : '赠送' }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="LED价格"
                                            >
                                                {{ placeBill.ledBidPrice }}元
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="实际LED价格"
                                            >
                                                {{ placeBill.billLedPrice }}元
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="LED规格"
                                            >
                                                {{ placeBill.ledModel }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="LED来源"
                                            >
                                                {{ placeBill.ledSource && LedTypeConstant.ofType(placeBill.ledSource)?.desc }}
                                            </h-form-item>
                                        </h-col>
                                    </h-row>
                                    <h-row>
                                        <template v-for="(f, fIndex) in placeBill.facility" :key="fIndex">
                                            <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                                <h-form-item
                                                    label="设施名称"
                                                >
                                                    {{ f.name }}
                                                </h-form-item>
                                            </h-col>
                                            <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                                <h-form-item
                                                    label="数量"
                                                >
                                                    {{ f.num }}
                                                </h-form-item>
                                            </h-col>
                                            <h-col :md="8" :lg="12">
                                                <h-form-item
                                                    label="价格"
                                                >
                                                    {{ f.price }}元
                                                </h-form-item>
                                            </h-col>
                                        </template>
                                    </h-row>
                                </h-form>
                                
                                <h-collapse v-model:activeKey="placeBillActiveKey" style="background: rgb(255, 255, 255)">
                                    <h-collapse-panel :key="'placeBill-' + 'facility-' + index" header="设施明细流水" >
                                        <template #extra>
                                            <span class="total font-size-12">合计：{{ placeBill._facilityTotal }}元</span>
                                        </template>
                                        <div class="table">
                                            <h-table :columns="placeBillFacilityDetailColumns" :data-source="placeBill.facilityDetailList" :pagination="false" bordered :scroll="{ x: 900 }">
                                                <template #bodyCell="{ column, text, record, index }">
                                                    
                                                </template>
                                            </h-table>
                                        </div>
                                        
                                    </h-collapse-panel>
                                    <h-collapse-panel :key="'placeBill-' + 'other-' + index" header="补充项目" >
                                        <template #extra>
                                            <span class="total font-size-12">合计：{{ placeBill._otherTotal }}元</span>
                                        </template>
                                        <div class="table">
                                            <h-table :columns="supplementColumns" :data-source="placeBill.otherList" :pagination="false" bordered :scroll="{ x: 1200 }">
                                                <template #bodyCell="{ column, text, record, index }">
                                                    <template v-if="column.dataIndex === 'type'">
                                                        {{ SupplementTypeConstant.ofType(placeBill.otherList![index].type)?.desc }}
                                                    </template>
                                                    <template v-if="column.dataIndex === 'projectDate'">
                                                        {{ dayjs(placeBill.otherList![index].projectDate).format("YYYY-MM-DD") }}
                                                    </template>
                                                </template>
                                            </h-table>
                                        </div>
                                    </h-collapse-panel>
                                    <h-collapse-panel :key="'placeBill-' + 'file-' + index" header="附件" >
                                        <div class="file-upload flex">
                                            <div class="flex upload-title">
                                                发票：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in placeBill.invoices" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="file-upload flex">
                                            <div class="flex upload-title">
                                                水单：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in placeBill.memos" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="file-upload flex" v-if="isMiceType()">
                                            <div class="flex upload-title">
                                                一手合同：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in placeBill.contracts" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>    
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="file-upload flex">
                                            <div class="flex upload-title">
                                                其他上传：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in placeBill.others" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                    </h-collapse-panel>
                                </h-collapse>
                            </h-collapse-panel>
                        </h-collapse>
                        

                    </h-tab-pane>
                    <h-tab-pane key="4" v-if="vehicleBills && vehicleBills.length > 0">
                        <template #tab>
                            <div class="tab-title flex">
                                用车账单
                            </div>
                            <div class="tab-total flex font-size-12">
                                <span >总：{{ vehicleTotal }}元</span>
                                <span>差：<span :class="{'negative': vehicleDiff > 0, 'positiv': vehicleDiff <= 0 }">{{ vehicleDiff }}元</span></span>
                            </div>
                        </template>
                        
                        <h-collapse v-model:activeKey="vehicleActiveKey">
                            <h-collapse-panel  :header="'账单' + (index + 1)" style="background-color: #fff;margin-bottom:28px;" 
                                v-for="(vehicleBill, index) in vehicleBills" :key="'vehicleBill-' + index">
                                <template #extra>
                                    <span class="total font-size-12">竞：{{ vehicleBill._biddingTotal ?? 0}}元</span>
                                    <span class="total font-size-12">账：{{ (vehicleBill._billTotal ?? 0) + (vehicleBill._otherTotal ?? 0) }}元</span>
                                    <span class="font-size-12">
                                        差：<span  :class="{'negative': computedDiff((vehicleBill._billTotal ?? 0) + (vehicleBill._otherTotal ?? 0), vehicleBill._biddingTotal) > 0, 'positiv': computedDiff((vehicleBill._billTotal ?? 0) + (vehicleBill._otherTotal ?? 0), vehicleBill._biddingTotal) <= 0 }" >{{ computedDiff((vehicleBill._billTotal ?? 0) + (vehicleBill._otherTotal ?? 0), vehicleBill._biddingTotal) }}元</span>
                                    </span>
                                </template>
                                <h-form
                                    ref="formRef"
                                    style="margin-top: 20px;"
                                >
                                    <h-row>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="车辆服务商"
                                            >
                                                {{ vehicleBill.supplier }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="用车日期"
                                            >
                                                {{ dayjs(vehicleBill.requireDate).format("YYYY-MM-DD") }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="车型"
                                            >
                                                {{ vehicleBill.vehicleType }}座
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="用车方式"
                                            >
                                            {{ vehicleBill.usageWay && CarUsageConstant.ofType(vehicleBill.usageWay)?.desc  }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg" v-if="vehicleBill.usageWay === 2">
                                            <h-form-item
                                                label="用车时间"
                                            >
                                            {{ vehicleBill.usageTime  &&  CarUsageTimeConstant.ofType(vehicleBill.usageTime)?.desc }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg" v-if="vehicleBill.usageWay === 2">
                                            <h-form-item
                                                label="使用时长"
                                            >
                                                {{ vehicleBill.duration }}小时
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="数量"
                                            >
                                                {{ vehicleBill.vehicleNum }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="竞价单价"
                                            >
                                                {{ vehicleBill.biddingPrice }}元
                                            </h-form-item>
                                        </h-col>
                                        <h-col :span="24">
                                            <h-form-item
                                                label="路线"
                                            >
                                                <div v-if="vehicleBill.routes && vehicleBill.routes.length > 0" class="routes">
                                                    <div v-for="(item, index) in vehicleBill.routes" :key="index" :class="{'route': vehicleBill.routes.length != 1}"> 
                                                        <template v-if="vehicleBill.routes.length === 1">
                                                            {{ item }}
                                                        </template>
                                                        <template v-else>
                                                            
                                                            <div class="route-icon">
                                                                <img  v-if="index === 0" :src="start" class="icon" />
                                                                <img  v-else-if="index === (vehicleBill.routes.length - 1)" :src="end" class="icon" />
                                                                <img  v-else :src="via" class="tu" />
                                                            </div>
                                                            <div class="route-name">{{ item }}</div>
                                                            
                                                        </template>
                                                    </div>
                                                </div>
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="实际数量"
                                                name="actualNum"
                                            >
                                                {{ vehicleBill.actualNum }}
                                            </h-form-item>
                                        </h-col>
                                    </h-row>
                                </h-form>
                                
                                <h-collapse v-model:activeKey="vehicleBillActiveKey" style="background: rgb(255, 255, 255)">
                                    <h-collapse-panel :key="'vehicleBill-' + 'detail-' + index" header="明细流水" >
                                        <template #extra>
                                            <span class="total font-size-12">合计：{{ vehicleBill._billTotal }}元</span>
                                        </template>
                                        
                                        <div class="table">
                                            <h-table :columns="vehicleBillDetailColumns" :data-source="vehicleBill.detailList" :pagination="false" bordered :scroll="{ x: 1500 }">
                                                <template #bodyCell="{ column, text, record, index }">
                                                    <template v-if="column.dataIndex === 'timeFrameList'">
                                                        {{formattedTimeRange(vehicleBill, index)}}
                                                    </template>
                                                </template>
                                            </h-table>
                                        </div>
                                        
                                    </h-collapse-panel>
                                    <h-collapse-panel :key="'vehicleBill-' + 'other-' + index" header="补充项目" >
                                        <template #extra>
                                            <span class="total font-size-12">合计：{{ vehicleBill._otherTotal }}元</span>
                                        </template>
                                        <div class="table">
                                            <h-table :columns="supplementColumns" :data-source="vehicleBill.otherList" :pagination="false" bordered :scroll="{ x: 1200 }">
                                                <template #bodyCell="{ column, text, record, index }">
                                                    <template v-if="column.dataIndex === 'type'">
                                                        {{ SupplementTypeConstant.ofType(vehicleBill.otherList![index].type)?.desc }}
                                                    </template>
                                                    <template v-if="column.dataIndex === 'projectDate'">
                                                        {{ dayjs(vehicleBill.otherList![index].projectDate).format("YYYY-MM-DD") }}
                                                    </template>
                                                </template>
                                            </h-table>
                                        </div>
                                    </h-collapse-panel>
                                    <h-collapse-panel :key="'vehicleBill-' + 'file-' + index" header="附件" >
                                        <div class="file-upload flex">
                                            <div class="flex upload-title">
                                                发票：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in vehicleBill.invoices" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="file-upload flex">
                                            <div class="flex upload-title">
                                                水单：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in vehicleBill.memos" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="file-upload flex">
                                            <div class="flex upload-title">
                                                其他上传：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in vehicleBill.others" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                    </h-collapse-panel>
                                </h-collapse>
                            </h-collapse-panel>
                        </h-collapse>
                        

                    </h-tab-pane>
                    <h-tab-pane key="5" v-if="insuranceBills && insuranceBills.length > 0">
                        <template #tab>
                            <div class="tab-title flex">
                                保险账单
                            </div>
                            <div class="tab-total flex font-size-12">
                                <span >总：{{ insuranceTotal }}元</span>
                                <span>差：<span :class="{'negative': insuranceDiff > 0, 'positiv': insuranceDiff <= 0 }">{{ insuranceDiff }}元</span></span>
                            </div>
                        </template>
                        
                        <h-form
                            ref="formRef"
                            style="margin-top: 20px;"
                        >
                            <h-collapse v-model:activeKey="insuranceActiveKey">
                                <h-collapse-panel  :header="'账单' + (index + 1)" style="background-color: #fff;margin-bottom:28px;" 
                                    v-for="(insuranceBill, index) in insuranceBills" :key="'insuranceBill-' + index">
                                    <template #extra>
                                        <span class="total font-size-12">竞：{{ (insuranceBill.schemeBiddingPrice ?? 0)}}</span>
                                        <span class="total font-size-12">账：{{ (insuranceTotal ?? 0) }}</span>
                                        <span class="font-size-12">差：<span :class="{'negative': (insuranceTotal ?? 0) - (insuranceBill.schemeBiddingPrice ?? 0) > 0, 'positiv': (insuranceTotal ?? 0) - (insuranceBill.schemeBiddingPrice ?? 0) <= 0 }">{{ (insuranceTotal ?? 0) - (insuranceBill.schemeBiddingPrice ?? 0) }}</span></span>
                                    </template>
                                    <h-row>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="保单号"
                                            >
                                                {{ insuranceBill.policyNo }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="保险开始日期"
                                            >
                                                {{ dayjs(insuranceBill.insuranceStartDate).format("YYYY-MM-DD") }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="保险结束日期"
                                            >
                                                {{ dayjs(insuranceBill.insuranceEndDate).format("YYYY-MM-DD") }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="上传被保人数量"
                                            >
                                                {{ insuranceBill.uploadedInsuredNum }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="实际承保人数量"
                                            >
                                                {{ insuranceBill.effectiveInsuredNum }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="单价"
                                            >
                                                {{ insuranceBill.actualPrice }}元
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="人天数"
                                            >
                                                {{ insuranceBill.insuredDays }}
                                            </h-form-item>
                                        </h-col>
                                    </h-row>
                                </h-collapse-panel>
                            </h-collapse>
                        </h-form>

                    </h-tab-pane>
                    <h-tab-pane key="6" v-if="presentBills && presentBills.length > 0">
                        <template #tab>
                            <div class="tab-title flex">
                                礼品账单
                            </div>
                            <div class="tab-total flex font-size-12">
                                <span >总：{{ presentTotal }}元</span>
                                <span>差：<span :class="{'negative': presentDiff > 0, 'positiv': presentDiff <= 0 }">{{ presentDiff }}元</span></span>
                            </div>
                        </template>
                        
                        <h-collapse v-model:activeKey="presentActiveKey">
                            <h-collapse-panel  :header="'账单' + (index + 1)" style="background-color: #fff;margin-bottom:28px;" 
                                v-for="(presentBill, index) in presentBills" :key="'presentBill-' + index">
                                <template #extra>
                                    <span class="total font-size-12">竞：{{ presentBill._biddingTotal ?? 0}}元</span>
                                    <span class="total font-size-12">账：{{ (presentBill._billTotal ?? 0) + (presentBill._otherTotal ?? 0) }}元</span>
                                    <span class="font-size-12">
                                        差：<span  :class="{'negative': computedDiff((presentBill._billTotal ?? 0) + (presentBill._otherTotal ?? 0), presentBill._biddingTotal) > 0, 'positiv': computedDiff((presentBill._billTotal ?? 0) + (presentBill._otherTotal ?? 0), presentBill._biddingTotal) <= 0 }" >{{ computedDiff((presentBill._billTotal ?? 0) + (presentBill._otherTotal ?? 0), presentBill._biddingTotal) }}元</span>
                                    </span>
                                </template>
                                <h-form
                                    ref="formRef"
                                    style="margin-top: 20px;"
                                    :model="presentBill"
                                >
                                    <h-row>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="其他项目"
                                            >
                                                {{ presentBill.name }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="单位"
                                            >
                                                {{ presentBill.unit }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="竞价单价"
                                            >
                                                {{ presentBill.biddingPrice }}元
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="数量"
                                            >
                                                {{ presentBill.requireNum }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :span="24">
                                            <h-form-item
                                                label="备注"
                                            >
                                                {{ presentBill.remark }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="实际单价"
                                                name="actualPrice"
                                            >
                                                {{ presentBill.actualPrice }}元
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="实际人数"
                                                name="actualNum"
                                            >
                                                {{ presentBill.actualNum }}
                                            </h-form-item>
                                        </h-col>
                                    </h-row>
                                </h-form>
                                
                                <h-collapse v-model:activeKey="presentBillActiveKey" style="background: rgb(255, 255, 255)">
                                    <h-collapse-panel :key="'presentBill-' + 'other-' + index" header="补充项目" >
                                        <template #extra>
                                            <span class="total font-size-12">合计：{{ presentBill._otherTotal }}元</span>
                                        </template>
                                        <div class="table">
                                            <h-table :columns="supplementColumns" :data-source="presentBill.otherList" :pagination="false" bordered :scroll="{ x: 1200 }">
                                                <template #bodyCell="{ column, text, record, index }">
                                                    <template v-if="column.dataIndex === 'type'">
                                                        {{ SupplementTypeConstant.ofType(presentBill.otherList![index].type)?.desc }}
                                                    </template>
                                                    <template v-if="column.dataIndex === 'projectDate'">
                                                        {{ dayjs(presentBill.otherList![index].projectDate).format("YYYY-MM-DD") }}
                                                    </template>
                                                </template>
                                            </h-table>
                                        </div>
                                    </h-collapse-panel>
                                    <h-collapse-panel :key="'presentBill-' + 'file-' + index" header="附件" >
                                        <div class="file-upload flex">
                                            <div class="flex upload-title">
                                                发票：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in presentBill.invoices" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="file-upload flex">
                                            <div class="flex upload-title">
                                                水单上传：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in presentBill.memos" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="file-upload flex">
                                            <div class="flex upload-title">
                                                其他上传：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in presentBill.others" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                    </h-collapse-panel>
                                </h-collapse>
                            </h-collapse-panel>
                        </h-collapse>
                        

                    </h-tab-pane>
                    <h-tab-pane key="7" v-if="otherBills && otherBills.length > 0">
                        <template #tab>
                            <div class="tab-title flex">
                                其他账单
                            </div>
                            <div class="tab-total flex font-size-12">
                                <span >总：{{ otherBillTotal }}元</span>
                                <span>差：<span :class="{'negative': otherBillDiff > 0, 'positiv': otherBillDiff <= 0 }">{{ otherBillDiff }}元</span></span>
                            </div>
                        </template>
                        
                        
                        <h-collapse v-model:activeKey="otherActiveKey">
                            <h-collapse-panel  :header="'账单' + (index + 1)" style="background-color: #fff;margin-bottom:28px;" 
                                v-for="(otherBill, index) in otherBills" :key="'otherBill-' + index">
                                <template #extra>
                                    <span class="total font-size-12">竞：{{ otherBill._biddingTotal ?? 0}}元</span>
                                    <span class="total font-size-12">账：{{ (otherBill._billTotal ?? 0) + (otherBill._otherTotal ?? 0) }}元</span>
                                    <span class="font-size-12">差：<span  :class="{'negative': computedDiff((otherBill._billTotal ?? 0) + (otherBill._otherTotal ?? 0), otherBill._biddingTotal) > 0, 'positiv': computedDiff((otherBill._billTotal ?? 0) + (otherBill._otherTotal ?? 0), otherBill._biddingTotal) <= 0 }" >{{ computedDiff((otherBill._billTotal ?? 0) + (otherBill._otherTotal ?? 0), otherBill._biddingTotal) }}元</span></span>
                                </template>

                                <h-form
                                    ref="formRef"
                                    style="margin-top: 20px;"
                                    :model="otherBill"
                                >
                                    <h-row>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="其他项目"
                                            >
                                                {{ otherBill.name }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="单位"
                                            >
                                                {{ otherBill.unit }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="竞价单价"
                                            >
                                                {{ otherBill.biddingPrice }}元
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="数量"
                                            >
                                                {{ otherBill.requireNum }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :span="24">
                                            <h-form-item
                                                label="备注"
                                            >
                                                {{ otherBill.remark }}
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="实际单价"
                                                name="actualPrice"
                                            >
                                                {{ otherBill.actualPrice }} 元
                                            </h-form-item>
                                        </h-col>
                                        <h-col :xs="xs" :sm="sm" :md="md" :lg="lg">
                                            <h-form-item
                                                label="实际数量"
                                                name="actualNum"
                                            >
                                                {{ otherBill.actualNum }}
                                            </h-form-item>
                                        </h-col>
                                    </h-row>
                                </h-form>
                                
                                <h-collapse v-model:activeKey="otherBillActiveKey" style="background: rgb(255, 255, 255)">
                                    <h-collapse-panel :key="'otherBill-' + 'other-' + index" header="补充项目" >
                                        <template #extra>
                                            <span class="total font-size-12">合计：{{ otherBill._otherTotal }}元</span>
                                        </template>
                                        <div class="table">
                                            <h-table :columns="supplementColumns" :data-source="otherBill.otherList" :pagination="false" bordered :scroll="{ x: 1200 }">
                                                <template #bodyCell="{ column, text, record, index }">
                                                    <template v-if="column.dataIndex === 'type'">
                                                        {{ SupplementTypeConstant.ofType(otherBill.otherList![index].type)?.desc }}
                                                    </template>
                                                    <template v-if="column.dataIndex === 'projectDate'">
                                                        {{ dayjs(otherBill.otherList![index].projectDate).format("YYYY-MM-DD") }}
                                                    </template>
                                                </template>
                                            </h-table>
                                        </div>
                                    </h-collapse-panel>
                                    <h-collapse-panel :key="'otherBill-' + 'file-' + index" header="附件" >
                                        <div class="file-upload flex">
                                            <div class="flex upload-title">
                                                发票：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in otherBill.invoices" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="file-upload flex">
                                            <div class="flex upload-title">
                                                水单：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in otherBill.memos" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="file-upload flex">
                                            <div class="flex upload-title">
                                                其他上传：
                                            </div>
                                            <div class="flex upload-btn">
                                                <div class="flex file">
                                                    <h-space :size="10" style="flex-wrap: wrap;">
                                                        <div class="flex">
                                                        </div>
                                                        <div class="flex file-hover" v-for="(file, fileIndex) in otherBill.others" :key="fileIndex">
                                                            <div class="flex" @click="(e) => openFileUrl(e, file.url)">
                                                                <h-tooltip class="flex">
                                                                    <template #title>{{ file.name }}</template>
                                                                    <h-typography-text class="file-name" ellipsis :content="file.name" />
                                                                    <a-space>
                                                                        <check-circle-two-tone v-if="getCheck(file.url)" two-tone-color="#52c41a" />
                                                                    </a-space>
                                                                </h-tooltip>
                                                            </div>
                                                            <div class="flex" >
                                                            </div>
                                                        </div>
                                                    </h-space>
                                                </div>
                                            </div>
                                        </div>
                                    </h-collapse-panel>
                                </h-collapse>
                            </h-collapse-panel>
                        </h-collapse>
                    </h-tab-pane>
                </h-tabs>
            </h-card>
        </h-spin>
    </div>
    

    <h-modal :open="open"  :style="{'top': top + 'px'}" title="附件查看" @ok="handleClose" @cancel="handleClose" :width="1200" :bodyStyle="{ 'height' : '500px' }" :wrap-class-name="{'full-modal': (modalPdfUrl ? true : false)}">
        <div style="width: 100%;display: flex;justify-content: center; height: 100%; overflow: hidden; align-items: center;">
            <div style="width: 100%; height: 500px; overflow: auto;" v-if="modalPicUrl">
                <h-image :src="url + modalPicUrl" :preview="false" style="height: 100%; overflow: auto;" />
            </div>
            <iframe :src="url + modalPdfUrl" frameborder="0" class="pdf-iframe"  id="details_iframe"  v-if="modalPdfUrl" style="height: 100%; " />
        </div>  
    </h-modal>
</template>

<style scoped lang="less">

.font-size-12 {
    font-size: 12px;
}

.total {
    padding-right: 30px;
}

.negative {
    color: red;
}

.positiv {
    color: blue;
}

.manage{
    margin-top: 10px;
}

.align-items {
    display: flex;
    align-items: center;
}

.upload {
    display: flex;
    flex-direction: row;

    .font {
        padding-top: 6px;
    }
}

.tab-title {
    width: 100%;
    justify-content: center;
    // color: rgba(0, 0, 0, 0.65);
}

.tab-total {
    width: 100%;
    justify-content: space-between;
    flex-direction: row;
    width: 170px;
    color: rgba(0, 0, 0, 0.65);
    font-weight: 500;
}

.add-detil {
    margin-top: 10px;
}

.file-upload {
    flex-direction: row;
    padding-bottom: 20px;

    .upload-title {
        width: 100px;
    }

    .upload-btn {
        width: calc(100% - 100px);
        align-items: center;
        
        .file {
            margin-left: 10px;
            align-items: center;

            .delete-icon {
                padding-left: 20px;
                align-items: center;
            }
            
            
        }

        
    }
    
}

.file-hover {
    max-width: 200px;
    padding: 5px 10px;
    cursor: pointer;
    background-color: #f3f3f3;

    .file-name {
        max-width: 150px;
    }
}

.file-hover:hover {
    background-color: #d9d9d9;
}

.table {
    padding-left: 20px;
    width: calc(100% - 20px);
}

.sub-btn {
    margin-top: 20px;
    width: 100%;
    display: flex;
    justify-content: center;
}

.pdf-iframe {
    width: 100%;
    height: calc(80vh - 116px);
}

.routes {
    display: flex;
    flex-direction: column;
    width: 100%;
    justify-content: center;
    


    .route {
        display: flex;
        flex-direction: row;
        margin-top: 10px;

        .route-name {
            display: flex;
            align-items: center;
            margin-left: 10px;
        }
        .route-icon {
            display: flex;
            margin-left: 10px;
            width: 20px;
            align-items: center;
            justify-content: center;

            .icon {
                width: 20px;
            }

            .tu {
                width: 10px;
            }
        }
    }

    
}

</style>
<style lang="less">
.full-modal {
  .ant-modal {
    max-width: 100%;
    padding-bottom: 0;
  }
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: 600px;
  }
  .ant-modal-body {
    flex: 1;
  }
}

</style>