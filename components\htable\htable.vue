<script setup lang="ts">
import { onMounted, reactive, computed, defineProps, defineEmits } from 'vue';
import { download, get, post, originalPost } from '@haierbusiness-front/apis/src/request.ts';

const props = defineProps({
  searchParam: {
    // 搜索项
    type: Object,
    default: {},
  },
  url: {
    // 接口地址
    type: String,
    default: {},
  },
  tableColumns: {
    // 表头配置
    type: Array,
    default: [],
  },
  tableAction: {
    // 自定义操作
    type: Array,
    default: [],
  },
});

const emit = defineEmits(['tableEmit']);

const state = reactive({
  loading: false,
  tableSource: [],
  pagination: {
    total: 0,
    current: 0,
    pageSize: 10,
  },
});

async function getList() {
  state.loading = true;
  const res = await get(props.url, { ...props.searchParam });
  console.log('%c [ res ]-37', 'font-size:13px; background:pink; color:#bf2c9f;', res);

  state.pagination = {
    total: res.total,
    current: res.pageNum,
    pageSize: 10,
  };

  state.tableSource = res.records || [];

  state.loading = false;
}

// 自定义操作
function actionClick(obj) {
  emit('tableEmit', { ...obj });
}

onMounted(async () => {
  await getList();
});
</script>

<template>
  <!-- table组件 -->
  <div class="h_table">
    <a-table
      :columns="tableColumns"
      :data-source="state.tableSource"
      :row-key="(record) => record.id"
      :pagination="state.pagination"
      :loading="state.loading"
      @change="handleTableChange($event as any)"
      :size="'small'"
      :scroll="{ x: 1500 }"
    >
      <!-- 表头 -->
      <template #headerCell="{ column }"> </template>

      <!-- 内容 -->
      <template #bodyCell="{ column, record, text, index }">
        <!-- 操作 -->
        <template v-if="column.dataIndex === '_operator'">
          <span v-for="(act, index) in tableAction" :key="act.dataIndex || index">
            <a-divider v-if="index > 0 && tableAction.length > 1" type="vertical" />
            <a @click="actionClick(act)">{{ act.name }} </a>
          </span>
        </template>
      </template>
    </a-table>
  </div>
</template>

<style scoped lang="less">
.h_table {
}
</style>
