type keys = "INVALID" | "WAIT" | "VALID";

/**
 * 评价控制
 */
export const EvaluateStateConstant = {
    INVALID: {"code": 0, "desc": "无效"},
    WAIT: {"code": 1, "desc": "待生效"},
    VALID: {"code": 2, "desc": "有效"},

    ofCode: (code?: number): { "code": number, "desc": string } | null => {
        for (const key in EvaluateStateConstant) {
            const item = EvaluateStateConstant[key as keys];
            if (code === item.code) {
                return item;
            }
        }
        return null;
    }
}