import {get} from '../request'
import {IAiChartContextDO} from "@haierbusiness-front/common-libs/src/ai";

export const agentsApi = {

    contextList: (): Promise<IAiChartContextDO[]> => {
        return get('/ai/api/agent/hpp/context/list')
    },

    contextListDetails: (param: { chatId?: string }): Promise<IAiChartContextDO[]> => {
        return get('/ai/api/agent/hpp/context/list/details', param)
    },
    executeSql(param: { pageSize: number; sqlContentId: string; pageNum: number }):any {
        return get('/ai/api/agent/hpp/execute/sql', param)
    }
}

