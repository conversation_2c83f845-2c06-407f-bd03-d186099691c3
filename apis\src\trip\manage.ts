import { get, post } from '../request'
import {
  Create_3Res,
  ICreatTrip,
  PageRes,
  IOutPerson,
  DataListRes,
  BudgeQueryRes,
  QueryCityListRes,
  MemberBudgetParams,
  MemberBudgetRes
} from '@haierbusiness-front/common-libs'
export const manageApi = {
  // 产品分页查询
  list: (params: object): Promise<DataListRes> => {
    return post(`trip/api/product/manage/page`, params);
  },
  // 删除产品
  remove: (id: number): Promise<DataListRes> => {
    return post(`trip/api/product/manage/delete`, {id});
  },
  // 修改产品
  edit: (params: object): Promise<DataListRes> => {
    return post(`trip/api/product/manage/update`, params);
  },
  // 新增产品
  save: (params: object): Promise<DataListRes> => {
    return post(`trip/api/product/manage/create`, params);
  },
}