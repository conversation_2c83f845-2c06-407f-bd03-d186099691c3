<script setup lang="ts">
import {computed, onMounted, PropType, ref} from "vue";
import {
  SelectOption as hSelectOption,
  Divider as hDivider,
  Select as hSelect,
  Modal as hModal,
  Input as hInput,
  Spin as hSpin,
  Space as hSpace,
  <PERSON><PERSON> as hButton,
  Row as hRow,
  Col as hCol,
  Tabs as hTabs,
  TabPane as hTabPane,
  Image as hImage,
  message,
} from "ant-design-vue";
import {IPayData} from "@haierbusiness-front/common-libs/src/pay/model/basicModel";
import {coinHaierPayApi, virtualPayApi} from "@haierbusiness-front/apis";
import {useRequest} from "vue-request";
import {
  IQueryVirtualAccountsResponse, PaySourceConstant,
  VirtualAccountTypeConstant,
} from "@haierbusiness-front/common-libs";
import {isMobile, resolveToken} from "@haierbusiness-front/utils";

const props = defineProps({
  param: Object as PropType<IPayData>,
  accounts: Object as PropType<IQueryVirtualAccountsResponse[]>,
});

const emit = defineEmits<{
  (e: "payComplete", isPayComplete: boolean): void;
}>();

const payComplete = () => {
  emit("payComplete", true);
};
const loginUser = ref(resolveToken() || {});

const virtualAccountsData = computed(() => props?.accounts || []);
const changeVirtualAccount = () => {
  const selectAccount = virtualAccountsData.value.filter(
      (it) => it.accountNo === virtualAccountCode.value
  );

  virtualAccount.value = selectAccount[0];
};
const virtualAccountCode = ref();
const virtualAccount = ref<IQueryVirtualAccountsResponse>({});

if (virtualAccountsData.value.length > 0) {
  virtualAccountCode.value = virtualAccountsData.value[0].accountNo;
  changeVirtualAccount();
}

const {data: payData, run: payRun, loading: payLoading} = useRequest(
    virtualPayApi.pay,
    {
      onSuccess: () => {
        visiblePay.value = false;
        payComplete();
      },
    }
);

/**
 * 验证码
 */
const captcha = ref();

const countdownTime = 60;
const visiblePay = ref(false);
const noCoin = ref(false);
const reSendLoading = ref(false);
const goPayLoading = ref(false);

/**
 * 验证码倒计时
 */
const countdown = ref(countdownTime);

/**
 * 开始验证码倒计时
 */
const startCountdown = () => {
  const sh = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      countdown.value = countdownTime;
      clearInterval(sh);
    }
  }, 1000);
};

const sendSms = (success: () => void) => {
  virtualPayApi
      .sendCodeCaptcha(
          {
            accountNo: virtualAccountCode.value,
            orderCode: props.param?.orderCode,
          }
      )
      .then((it) => {
        success();
      })
      .finally(() => {
        reSendLoading.value = false;
        goPayLoading.value = false;
      });
};
const reSend = () => {
  reSendLoading.value = true;
  countdown.value = countdownTime - 1;
  sendSms(() => {
    reSendLoading.value = false;
    startCountdown();
  });
};

const payConfirm = () => {
  if (virtualAccount.value.smsCaptchaFlag == 2) {
    goPayLoading.value = true;
    if (countdown.value === countdownTime) {
      sendSms(() => {
        goPayLoading.value = false;
        countdown.value = countdownTime - 1;
        visiblePay.value = true;
        startCountdown();
      });
    } else {
      goPayLoading.value = false;
      visiblePay.value = true;
    }
  } else {
    pay();
  }
};
const pay = () => {
  payRun(
      {
        captcha: captcha.value,
        accountNo: virtualAccountCode.value,
        // - 通用参数
        orderCode: props.param?.orderCode,
        applicationCode: props.param?.applicationCode,
        payTypes: props.param?.payTypes,
        username: props.param?.username,
        providerCode: props.param?.providerCode,
        amount: Number(props.param?.amount),
        notifyUrl: props.param?.notifyUrl,
        orderDetailsUrl: props.param?.orderDetailsUrl,
        callbackUrl: props.param?.callbackUrl,
        description: props.param?.description,
        payload: props.param?.payload,
        paySource: isMobile() ? PaySourceConstant.MOBILE.type : PaySourceConstant.PC.type,
        paymentMethod: 2,
        startApproveFlag: props.param?.startApproveFlag
      },
      {
        applicationCode: props.param?.applicationCode,
        excludes: "paySource,accountNo,captcha,paymentMethod",
        nonce: props.param?.hbNonce,
        timestamp: props.param?.hbTimestamp,
        sign: props.param?.sign,
      }
  );
  
};
</script>
<template>
  <h-modal v-model:open="visiblePay" title="请输入验证码" @ok="pay" okText="确认支付">
    <p>验证码已发送至{{ loginUser.phone }}</p>
    <p>
      <h-row>
        <h-col span="16">
          <h-input v-model:value="captcha" placeholder="验证码"/>
        </h-col>
        <h-col span="7" offset="1">
          <div v-if="countdown != countdownTime" style="margin-top: 4px">
            {{ countdown }}S 后重新发送
          </div>
          <div v-else>
            &nbsp;&nbsp;&nbsp;<hButton
              type="primary"
              :loading="reSendLoading"
              @click="reSend"
          >
            重新发送
          </hButton
          >
          </div>
        </h-col>
      </h-row>
    </p>
  </h-modal>
  <h-row style="margin-top: 4vh; text-align: right" :align="'middle'">
    <h-col span="2" style="font-size: 12px"><span style="color: red">*</span>账户：</h-col>
    <h-col span="6" style="text-align: left">
      <h-select
          v-model:value="virtualAccountCode"
          :size="'large'"
          @change="changeVirtualAccount"
          class="virtual-input"
      >
        <h-select-option
            v-for="opt in virtualAccountsData"
            :key="opt.accountNo"
            :value="opt.accountNo"
        >{{ opt.accountNo }}/{{ opt.accountName }}
        </h-select-option
        >
      </h-select>
    </h-col>
    <h-col span="2" style="font-size: 12px"> 户名：</h-col>
    <h-col span="6" style="text-align: left">
      <h-input
          placeholder=""
          :size="'large'"
          :value="virtualAccount.accountName"
          class="virtual-input-readonly"
      />
    </h-col>
    <h-col span="2" style="font-size: 12px"> 类型：</h-col>
    <h-col span="6" style="text-align: left">
      <h-input
          placeholder=""
          :size="'large'"
          :value="virtualAccount._typeName"
          class="virtual-input-readonly"
      />
    </h-col>
  </h-row>
  <h-row style="margin-top: 4vh; text-align: right;" :align="'middle'">
    <h-col span="2" style="font-size: 12px"> 支付人：</h-col>
    <h-col span="6" style="text-align: left">
      <h-input
          placeholder=""
          :size="'large'"
          :value="props.param?.username || loginUser.username"
          class="virtual-input-readonly"
      />
    </h-col>
    <h-col span="2" style="font-size: 12px"> 所属企业：</h-col>
    <h-col span="6" style="text-align: left">
      <h-input
          placeholder=""
          :size="'large'"
          :value="virtualAccount.enterpriseCode"
          class="virtual-input-readonly"
      />
    </h-col>
    <h-col span="2" style="font-size: 12px"> 企业名称：</h-col>
    <h-col span="6" style="text-align: left">
      <h-input
          placeholder=""
          :size="'large'"
          :value="virtualAccount.enterpriseName"
          class="virtual-input-readonly"
      />
    </h-col>
  </h-row>
  <h-row style="margin-top: 4vh; text-align: right" :align="'middle'">
    <h-col span="2" style="font-size: 12px"> 余额：</h-col>
    <h-col span="6" style="text-align: left">
      <h-input
          v-if="virtualAccount.type === VirtualAccountTypeConstant.GZ.code"
          placeholder=""
          :size="'large'"
          :value="'----------------'"
          class="virtual-input-readonly"
      />
      <h-input
          v-else
          placeholder=""
          :size="'large'"
          :value="virtualAccount.amount"
          class="virtual-input-readonly"
      />
    </h-col>
  </h-row>
  <h-row style="line-height: 14vh" :align="'middle'">
    <h-col span="2" offset="11">
      <h-button
          type="primary"
          style="width: 100%"
          @click="payConfirm"
          :loading="payLoading"
          size="large"
      >&nbsp;支&nbsp;&nbsp;付&nbsp;
      </h-button
      >
    </h-col>
  </h-row>
</template>

<style scoped lang="less">
.virtual-input-readonly {
  width: 12vw;
  background-color: rgb(245, 245, 245);
  color: #2e2e2e;
}

.virtual-input {
  width: 12vw;
}
</style>

