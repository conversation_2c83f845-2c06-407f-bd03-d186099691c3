<script setup>
defineProps(['id', 'title', 'desc', 'contentStyle']);
</script>

<template>
  <div :id="id" class="common-box">
    <div class="common-box-header">
      <div class="title">{{ title }}</div>
      <div class="desc">{{ desc }}</div>
    </div>
    <div class="common-box-content" :style="contentStyle">
      <slot />
    </div>
  </div>
</template>

<style scoped lang="less">
.common-box {
  margin-top: 72px;
  .common-box-header {
    .title {
      font-weight: 800;
      font-size: 42px;
      color: #1d2129;
      line-height: 59px;
    }
    .desc {
      margin-top: 8px;
      font-size: 18px;
      color: #86909c;
      line-height: 25px;
      font-weight: 400;
    }
  }
}
</style>
