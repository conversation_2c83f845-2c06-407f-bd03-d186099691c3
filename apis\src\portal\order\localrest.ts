import {
    LocalrestFilter, LocalrestType, OrderListResult
} from '@haierbusiness-front/common-libs'
import { get, originalPost } from '../../request'

export const localrestApi = {
    list: (params: LocalrestFilter): Promise<OrderListResult<LocalrestType>> => {
        return originalPost(`/businesstravel/api/localhotel/v1/restaurant/order/list.json?pageNum=${params.pageNum}&pageSize=${params.pageSize}`, params)
    }
}