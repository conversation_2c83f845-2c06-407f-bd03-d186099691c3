import { get, post } from '../../request'
import {
    Result
} from '@haierbusiness-front/common-libs';
import { getCommonFilter, getCommonFilterSynchronism } from "./index";

const getTypeKey = (url: number | string) => {
    const resultMap: any = {
        "/data/board/booking-hotel": "localHotel",
        "/data/board/ordering-food": "localRestaurant",
        "/data/board/travel/internal": "domesticAirTickets",
        "/data/board/travel/external": "internationalAirfare",
        "/data/board/travel/hotel": "hotel",
        "/data/board/travel/taxi": "taxi",
        "/data/board/travel/train": "trainTicket",
        "/data/board/mice/offsite": "miceOffsite",
        "/data/board/mice/local": "miceLocal",
        "/data/board/travel/index": "travelOverview",
        default: "",
    };
    return resultMap[url] || resultMap.default;
};

const commonParams = {
    moduleType: 2,
    type: getTypeKey(window.location.hash.split('#')[1]),
}
const commonKey = {
    concurrencyControl: true,
    concurrencyControlMode: "DIRTYREAD",
    viewId: "ab679edb60894c8b95a65fe5fed786d3"
};
const queryCommonData = (params): Promise<Result> => {
    return post("data/api/bi/common/data", {
        concurrencyControl: true,
        concurrencyControlMode: "DIRTYREAD",
        ...params,
    });
};
//查询累计成交-人次
const queryAccumulativePerson = (): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: [
            {
                aggOperator: null,
                column: ["gngj"],
                sqlOperator: "EQ",
                values: [
                    {
                        value: "0",
                        valueType: "STRING",
                    },
                ],
            },
        ] as any,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        aggregators: [
            {
                alias: "人次",
                column: ["dd_ydsj"],
                sqlOperator: "COUNT",
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
//gngj: (0:国际 1:国内)
const queryAccumulativeOther = ({ gngj }, name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["gngj"],
            sqlOperator: "EQ",
            values: [
                {
                    value: gngj,
                    valueType: "STRING",
                },
            ],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    const aggregators = () => {
        if (!gngj || gngj == 1) {
            return [
                {
                    alias: "人次",
                    column: ["person_time_number"],
                    sqlOperator: "SUM",
                },
                {
                    alias: "成交金额",
                    column: ["fyje_number"],
                },
                {
                    alias: "政策节省",
                    column: ["zcjs_number"],
                },
                {
                    alias: "平均折扣",
                    column: ["zkl_number"],
                },

                // {
                //     alias: "国家地区",
                //     column: ["address_number"],
                // },
                // {
                //     alias: "退票率",
                //     column: ["ticket_return_rate"],
                // },
                // {
                //     alias: "改期率",
                //     column: ["ticket_change_rate"],
                // },
                {
                    alias: "投保率",
                    column: ["insure_rate"],
                },
                {
                    alias: "预算部门",
                    column: ["budget_people_emp_number"],
                },
                {
                    alias: "结算单位",
                    column: ["account_company_code_number"],
                },
            ];
        }
        return [
            // {
            //     alias: "投保率",
            //     column: ["insure_rate"],
            // },
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
            {
                alias: "成交金额",
                column: ["fyje_number"],
            },
            {
                alias: "国家地区",
                column: ["ddgjmc_number"],
            },
            {
                alias: "退票率",
                column: ["ticket_return_rate"],
            },
            {
                alias: "改期率",
                column: ["ticket_change_rate"],
            },
            {
                alias: "预算部门",
                column: ["budget_people_emp_number"],
            },
            {
                alias: "结算单位",
                column: ["account_company_code_number"],
            },
        ];
    };
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        viewId: "ab679edb60894c8b95a65fe5fed786d3",
        aggregators: aggregators(),
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns: [
            {
                alias: "ticket_change_rate",
                snippet:
                    "ifnull(sum(ticket_change_number)/sum(normal_order_number),0)",
            },
            {
                alias: "ticket_return_rate",
                snippet:
                    "ifnull(sum(ticket_return_number)/sum(normal_order_number),0)",
            },
            {
                alias: "budget_people_emp_number",
                snippet: "count(distinct budget_department_code)",
            },
            {
                alias: "ddgjmc_number",
                snippet: "count(distinct ddgjmc)",
            },
            {
                alias: "account_company_code_number",
                snippet: "count(distinct account_company_code)",
            },
            {
                alias: "insure_rate",
                snippet:
                    "ifnull(sum(if(bxje >0,1, if(bxje < 0,-1,0)))/COUNT(distinct  ytkno),0)",
            },
            {
                alias: "fyje_number",
                snippet: "ifnull(sum(fyje),0)",
            },
            {
                alias: "zcjs_number",
                snippet: "sum(if(sftp=1,0,zcjs))",
            },
            {
                alias: "zkl_number",
                snippet: "SUM(if(sftp=1,0,zkl))/SUM(if(sftp=1,0,1))",
            },
        ],
        groups: [],
    });
};



//查询国内国际机票业务趋势 同期
const querySynchronismAirTicketTrend = (
    { gngj, functionColumns }: any,
    name: string | null,
    from: string | null,
    dateParams: any,
): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["gngj"],
            sqlOperator: "EQ",
            values: [
                {
                    value: gngj,
                    valueType: "STRING",
                },
            ],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilterSynchronism({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
        dateParams
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        viewId: "ab679edb60894c8b95a65fe5fed786d3",
        aggregators: [
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
            {
                alias: "SUM(fyje)",
                column: ["fyje"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "日期",
                column: ["dd_ydsj_query"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns,
    });
};
//查询国内国际机票业务趋势
const queryAirTicketTrend = (
    { gngj, functionColumns }: any,
    name: string | null,
    from: string | null
): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["gngj"],
            sqlOperator: "EQ",
            values: [
                {
                    value: gngj,
                    valueType: "STRING",
                },
            ],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        viewId: "ab679edb60894c8b95a65fe5fed786d3",
        aggregators: [
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
            {
                alias: "SUM(fyje)",
                column: ["fyje"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "日期",
                column: ["dd_ydsj_query"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns,
    });
};
//查询国内机票折扣占比
const queryDiscountPercentage = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    filters.push({
        aggOperator: null,
        column: ["gngj"],
        sqlOperator: "EQ",
        values: [
            {
                value: "1",
                valueType: "STRING",
            },
        ],
    });
    filters.push({
        aggOperator: null,
        column: ["sftp"],
        sqlOperator: "EQ",
        values: [
            {
                value: "0",
                valueType: "STRING",
            },
        ],
    });
    filters.push({
        aggOperator: null,
        column: ["ddlx"],
        sqlOperator: "EQ",
        values: [
            {
                value: "01001",
                valueType: "STRING",
            },
        ],
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        aggregators: [
            {
                alias: "折扣人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "折扣区间",
                column: ["zklmc"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
//查询非最低价占比
const queryNonMinimumPrice = (name?: string | null, from?: string | null): Promise<Result> => {
    let filtersArr: any =
        [
            {
                aggOperator: null,
                column: [
                    "gngj"
                ],
                sqlOperator: "EQ",
                values: [{
                    value: "1",
                    valueType: "STRING"
                }]
            },
            {
                aggOperator: null,
                column: [
                    "ddlx"
                ],
                sqlOperator: "EQ",
                values: [{
                    value: "01001",
                    valueType: "STRING"
                }]
            },
            {
                aggOperator: null,
                column: [
                    "sftp"
                ],
                sqlOperator: "EQ",
                values: [{
                    value: "0",
                    valueType: "STRING"
                }]
            },
            {
                aggOperator: null,
                column: ["sfzdj"],
                sqlOperator: "NOT_NULL",
                values: [],
            },
        ]
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    filters.push({
        aggOperator: null,
        column: ["gngj"],
        sqlOperator: "EQ",
        values: [
            {
                value: "1",
                valueType: "STRING",
            },
        ],
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        viewId: "ab679edb60894c8b95a65fe5fed786d3",
        a: '非最低价',
        aggregators: [
            {
                alias: "是否最低价",
                column: [
                    "sfzdj"
                ],
                sqlOperator: "COUNT"
            }, {
                alias: "损失金额",
                column: [
                    "damages_amount"
                ],
                sqlOperator: "SUM"
            }
        ],
        groups: [
            {
                alias: "sfzdj_groups",
                column: [
                    "sfzdj"
                ]
            }
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};
//查询国内机票预计时间占比
const queryTimeProportion = (name?: string | null, from?: string | null): Promise<Result> => {
    let filtersArr: any =
        [
            {
                aggOperator: null,
                column: [
                    "gngj"
                ],
                sqlOperator: "EQ",
                values: [{
                    value: "1",
                    valueType: "STRING"
                }]
            },
            {
                aggOperator: null,
                column: [
                    "tqydtsmc"
                ],
                sqlOperator: "NOT_NULL",
                values: []
            },{
                aggOperator: null,
                column: [
                    "ddlx"
                ],
                sqlOperator: "EQ",
                values: [{
                    value: "01001",
                    valueType: "STRING"
                }]
            },{
                aggOperator: null,
                column: [
                    "sftp"
                ],
                sqlOperator: "EQ",
                values: [{
                    value: "0",
                    valueType: "STRING"
                }]
            }
        ]
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    filters.push({
        aggOperator: null,
        column: ["gngj"],
        sqlOperator: "EQ",
        values: [
            {
                value: "1",
                valueType: "STRING",
            },
        ],
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        a: '时间占比区间',
        aggregators: [
            {
                alias: "时间占比区间",
                column: [
                    "tqydtsmc"
                ],
                sqlOperator: "COUNT"
            }, {
                alias: "平均折扣",
                column: [ "zkl" ]
            }, {
                alias: "损失金额",
                column: [
                    "damages_amount"
                ],
                sqlOperator: "SUM"
            }
        ],
        groups: [
            {
                alias: "提前预订天数名称",
                column: [
                    "tqydtsmc"
                ]
            }, {
                alias: "提前预订时间占比排序字段",
                column: [
                    "tqydorder"
                ]
            }
        ],
        orders: [{
            column: [
                "tqydorder"
            ],
            operator: "ASC",
        }],  functionColumns: [
            {
                alias: "zkl",
                snippet: "ROUND(avg(zkl), 2)",
            }
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
//查询航线排行
const queryAirlineRank = ({ gngj }, name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["gngj"],
            sqlOperator: "EQ",
            values: [
                {
                    value: gngj,
                    valueType: "STRING",
                },
            ],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    //国际航班特有
    if (gngj == "0") {
        filters.push({
            aggOperator: null,
            column: ["ddgjmc"],
            sqlOperator: "NE",
            values: [
                {
                    value: "中国",
                    valueType: "STRING",
                },
            ],
        });
    }
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        viewId: "ab679edb60894c8b95a65fe5fed786d3",
        aggregators: [
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "行程",
                column: ["xc"],
            },
        ],
        orders: [
            {
                column: ["person_time_number"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 20,
        },
    });
};
//查询航空公司排行
const queryAirlineCompanyRank = ({ gngj }, name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["gngj"],
            sqlOperator: "EQ",
            values: [
                {
                    value: gngj,
                    valueType: "STRING",
                },
            ],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        viewId: "ab679edb60894c8b95a65fe5fed786d3",
        aggregators: [
            {
                alias: "成交金额",
                column: ["fyje"],
                sqlOperator: "SUM",
            },
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "hkgsmc",
                column: ["hkgsmc"],
            },
        ],
        orders: [
            {
                column: ["fyje"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 9999,
        },
    });
};
//查询结算单位排行
const querySettleRank = ({ gngj }, name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["gngj"],
            sqlOperator: "EQ",
            values: [
                {
                    value: gngj,
                    valueType: "STRING",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["account_company_name"],
            sqlOperator: "NOT_NULL",
            values: [],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        viewId: "ab679edb60894c8b95a65fe5fed786d3",
        aggregators: [
            {
                alias: "SUM(fyje)",
                column: ["fyje"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "account_company_name",
                column: ["account_company_name"],
            },
        ],
        orders: [
            {
                column: ["fyje"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
    });
};
//查询出发城市排行
const queryDepartureRank = ({ gngj }, name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["gngj"],
            sqlOperator: "EQ",
            values: [
                {
                    value: gngj,
                    valueType: "STRING",
                },
            ],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        aggregators: [
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "pnr_cfcs_mc",
                column: ["pnr_cfcs_mc"],
            },
        ],
        orders: [
            {
                column: ["person_time_number"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 20,
        },
    });
};
//查询退改情况
const queryChangeAndRefund = (
    { gngj, functionColumns },
    name,
    from
): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["gngj"],
            sqlOperator: "EQ",
            values: [
                {
                    value: gngj,
                    valueType: "STRING",
                },
            ],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        viewId: "ab679edb60894c8b95a65fe5fed786d3",
        aggregators: [
            {
                alias: "退票费",
                column: ["tp_tpsxf"],
                sqlOperator: "SUM",
            },
            {
                alias: "改签费",
                column: ["gqsxf_compute"],
            },
            {
                alias: "改签率",
                column: ["ticket_change_rate"],
            },
            {
                alias: "退票率",
                column: ["ticket_return_rate"],
            },
        ],
        groups: [
            {
                alias: "dd_ydsj_group",
                column: ["dd_ydsj_group"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns: [
            {
                alias: "ticket_change_rate",
                snippet:
                    "ifnull(sum(ticket_change_number) / sum(normal_order_number), 0)",
            },
            {
                alias: "ticket_return_rate",
                snippet:
                    "ifnull(sum(ticket_return_number) / sum(normal_order_number), 0)",
            },
            {
                alias: "gqsxf_compute",
                snippet: "sum(if(ddlx='01003',fyje,0))",
            },
            ...functionColumns,
        ],
    });
};
//国际机票查询供应商占比
const querySupplierPercentage = ({ gngj }, name: string | null, from: string | null): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: [
            {
                aggOperator: null,
                column: ["gngj"],
                sqlOperator: "EQ",
                values: [
                    {
                        value: gngj,
                        valueType: "STRING",
                    },
                ],
            }
        ] as any,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        aggregators: [
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "供应商",
                column: ["supplier_name"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
//查询国内机票节省金额趋势 同期
const querySynchronismAirlineSavingsTrend = (
    { functionColumns }: any,
    name: string | null,
    from: string | null,
    dateParams: any
): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["gngj"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "1",
                    valueType: "STRING",
                },
            ],
        },
    ];

    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilterSynchronism({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
        dateParams
    });
    functionColumns.push({
        alias: "zcjs_compute",
        snippet: "sum(if(sftp=1,0,zcjs))",
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        aggregators: [
            {
                alias: "政策节省金额",
                column: ["zcjs_compute"],
            },
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "政策节省",
                column: ["dd_ydsj_group"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns,
    });
};
//查询国内机票节省金额趋势
const queryAirlineSavingsTrend = (
    { functionColumns },
    name,
    from
): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["gngj"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "1",
                    valueType: "STRING",
                },
            ],
        },
    ];

    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    functionColumns.push({
        alias: "zcjs_compute",
        snippet: "sum(if(sftp=1,0,zcjs))",
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        aggregators: [
            {
                alias: "政策节省金额",
                column: ["zcjs_compute"],
            },
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "政策节省",
                column: ["dd_ydsj_group"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns,
    });
};
//查询支付类型
const queryAirlinePayType = ({ gngj }, name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["ddlx"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "01001",
                    valueType: "STRING",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["gngj"],
            sqlOperator: "EQ",
            values: [
                {
                    value: gngj,
                    valueType: "STRING",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["pay_type"],
            sqlOperator: "NOT_NULL",
            values: [],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        viewId: "ab679edb60894c8b95a65fe5fed786d3",
        aggregators: [
            {
                alias: "数量",
                column: ["pay_type"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "支付类型",
                column: ["pay_type"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};
//查询支付平台
const queryAirlinePayPlatform = ({ gngj }, name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["ddlx"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "01001",
                    valueType: "STRING",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["gngj"],
            sqlOperator: "EQ",
            values: [
                {
                    value: gngj,
                    valueType: "STRING",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["budget_source"],
            sqlOperator: "NOT_NULL",
            values: [],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        viewId: "ab679edb60894c8b95a65fe5fed786d3",
        aggregators: [
            {
                alias: "预算类型数量",
                column: ["budget_source"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "预算类型",
                column: ["budget_source"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};
//国际机票查询订单状态
const queryAirlineOrderStatus = (): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        viewId: "a3430b0ef3c84b5cb1a5b3a2da434349",
        aggregators: [
            {
                alias: "订单状态数量",
                column: ["ddztmc"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "订单状态名称",
                column: ["ddztmc"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};
export const queryAirlineMapData = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["gngj"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "1",
                    valueType: "STRING",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["ddlx"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "01001",
                    valueType: "STRING",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["sftp"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "0",
                    valueType: "STRING",
                },
            ],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        a: "中国地图",
        viewId: "ab679edb60894c8b95a65fe5fed786d3",
        aggregators: [
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "出发城市名称",
                column: ["pnr_cfcs_mc"],
            },
            {
                alias: "到达城市名称",
                column: ["pnr_ddcs_mc"],
            },
            {
                alias: "出发城市经度",
                column: ["cfcsjd"],
            },
            {
                alias: "出发城市维度",
                column: ["cfcswd"],
            },
            {
                alias: "到达城市经度",
                column: ["ddcsjd"],
            },
            {
                alias: "到达城市维度",
                column: ["ddcswd"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
        orders: [
            {
                column: ["person_time_number"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
    });
};

export const queryAbroadMapData = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["gngj"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "0",
                    valueType: "STRING",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["ddlx"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "01001",
                    valueType: "STRING",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["sftp"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "0",
                    valueType: "STRING",
                },
            ],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        a: "国际机票",
        viewId: "ab679edb60894c8b95a65fe5fed786d3",
        aggregators: [
            {
                alias: "订单数",
                column: ["normal_order_number"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "出发国家名称",
                column: ["cfgjmc"],
            },
            {
                alias: "到达国家名称",
                column: ["ddgjmc"],
            },
            {
                alias: "出发城市经度",
                column: ["cfcsjd"],
            },
            {
                alias: "出发城市维度",
                column: ["cfcswd"],
            },
            {
                alias: "到达城市经度",
                column: ["ddcsjd"],
            },
            {
                alias: "到达城市维度",
                column: ["ddcswd"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
        orders: [
            {
                column: ["normal_order_number"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
    });
};
export const queryAirlineDestinationRank = ({ gngj }): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: [
            {
                aggOperator: null,
                column: ["gngj"],
                sqlOperator: "EQ",
                values: [
                    {
                        value: gngj,
                        valueType: "STRING",
                    },
                ],
            },
            {
                aggOperator: null,
                column: ["ddgjmc"],
                sqlOperator: "NE",
                values: [
                    {
                        value: "中国",
                        valueType: "STRING",
                    },
                ],
            },
        ] as any,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...commonKey,
        aggregators: [
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "国家/地区",
                column: ["ddgjmc"],
            },
        ],
        orders: [
            {
                column: ["person_time_number"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 20,
        },
    });
};
export {
    queryAccumulativePerson,
    queryAccumulativeOther,
    queryAirTicketTrend,
    querySynchronismAirTicketTrend,
    queryDiscountPercentage,
    queryNonMinimumPrice,
    queryTimeProportion,
    queryAirlineRank,
    queryAirlineCompanyRank,
    querySettleRank,
    queryDepartureRank,
    queryChangeAndRefund,
    querySupplierPercentage,
    queryAirlineSavingsTrend,
    querySynchronismAirlineSavingsTrend,
    queryAirlinePayType,
    queryAirlinePayPlatform,
    queryAirlineOrderStatus,
};

//以下是火车票业务
const trainCommonKey = {
    ...commonKey,
    viewId: "8b0b509fdd954c93af797232767dd71f",
};
export const queryTrainAccumulative = (
    name: string | null,
    from: string | null
): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });

    if (name) {
        filters.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }

    return queryCommonData({
        ...commonParams,
        ...trainCommonKey,
        aggregators: [
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
            {
                alias: "成交金额",
                column: ["fyje_number"],
            },
            // {
            //     "alias": "行程",
            //     "column": [
            //         "xc_number"
            //     ]
            // },
            // {
            //     "alias": "退票率",
            //     "column": [
            //         "ticket_return_rate"
            //     ]
            // }, {
            //     "alias": "改期率",
            //     "column": [
            //         "ticket_change_rate"
            //     ]
            // },
            {
                alias: "投保率",
                column: ["insure_rate"],
            },
            {
                alias: "预算部门",
                column: ["budget_people_emp_number"],
            },
            {
                alias: "结算单位",
                column: ["account_company_code_number"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns: [
            {
                alias: "ticket_change_rate",
                snippet:
                    "ifnull(sum(ticket_change_number)/sum(normal_order_number),0)",
            },
            {
                alias: "ticket_return_rate",
                snippet:
                    "ifnull(sum(ticket_return_number)/sum(normal_order_number),0)",
            },
            {
                alias: "budget_people_emp_number",
                snippet: "count(distinct budget_department_code)",
            },
            {
                alias: "account_company_code_number",
                snippet: "count(distinct account_company_code)",
            },
            {
                alias: "insure_rate",
                snippet:
                    "ifnull(sum(if(sftp=1,0,insure_number))/sum(if(sftp=1,0,normal_order_number)),0)",
            },
            {
                alias: "fyje_number",
                snippet: "ifnull(sum(fyje),0)",
            },
        ],
    });
};
//差旅火车票 趋势
export const queryTrainTicketTrend = (
    { functionColumns }: any,
    name: string | null,
    from: string | null
): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });

    if (name) {
        filters.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }

    return queryCommonData({
        ...commonParams,
        ...trainCommonKey,
        aggregators: [
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
            {
                alias: "全部",
                column: ["fyje"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "dd_ydsj_group",
                column: ["dd_ydsj_group"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns,
    });
};
//差旅火车票 同期趋势
export const querySynchronismTrainTicketTrend = (
    { functionColumns }: any,
    name: string | null,
    from: string | null,
    dateParams: any
): Promise<Result> => {
    const filters = getCommonFilterSynchronism({
        dataSourceKey: 'budget_source',
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
        dateParams
    });

    if (name) {
        filters.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }

    return queryCommonData({
        ...commonParams,
        ...trainCommonKey,
        aggregators: [
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
            {
                alias: "全部",
                column: ["fyje"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "dd_ydsj_group",
                column: ["dd_ydsj_group"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns,
    });
};
export const querySeatPercentage = (
    name: string | null,
    from: string | null
): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });

    if (name) {
        filters.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    //排除掉改签信息
    filters.push({
        aggOperator: null,
        column: ["sftp"],
        sqlOperator: "EQ",
        values: [
            {
                value: "0",
                valueType: "STRING",
            },
        ],
    });
    filters.push({
        aggOperator: null,
        column: ["sfgq"],
        sqlOperator: "EQ",
        values: [
            {
                value: "0",
                valueType: "STRING",
            },
        ],
    });
    return queryCommonData({
        ...commonParams,
        ...trainCommonKey,
        viewId: "8b0b509fdd954c93af797232767dd71f",
        aggregators: [
            {
                alias: "座位类型",
                column: ["zwlxmc"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "zwlxmc",
                column: ["zwlxmc"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
//行程排行
export const queryJourneyRank = (
    name: string | null,
    from: string | null
): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });

    if (name) {
        filters.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }

    return queryCommonData({
        ...commonParams,
        ...trainCommonKey,
        viewId: "8b0b509fdd954c93af797232767dd71f",
        aggregators: [
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "行程",
                column: ["xc"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 20,
        },
        orders: [
            {
                column: ["person_time_number"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
    });
};
//查询火车票结算单位排行
export const queryTrainSettleRank = (
    name: string | null,
    from: string | null
): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });

    if (name) {
        filters.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    return queryCommonData({
        ...commonParams,
        ...trainCommonKey,
        aggregators: [
            {
                alias: "金额",
                column: ["fyje"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "结算单位名称",
                column: ["account_company_name"],
            },
        ],
        orders: [
            {
                column: ["fyje"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
    });
};
export const queryTrainChangeAndRefund = (
    name: string | null,
    from: string | null
): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });

    if (name) {
        filters.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    return queryCommonData({
        ...commonParams,
        ...trainCommonKey,
        aggregators: [
            {
                alias: "改签费",
                column: ["gqsxf"],
                sqlOperator: "SUM",
            },
            {
                alias: "退票费",
                column: ["tp_tpsxf"],
                sqlOperator: "SUM",
            },
            {
                alias: "退票率",
                column: ["ticket_change_rate"],
            },
            {
                alias: "改期率",
                column: ["ticket_return_rate"],
            },
        ],
        groups: [
            {
                alias: "dd_ydsj_group",
                column: ["dd_ydsj_group"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns: [
            {
                alias: "ticket_change_rate",
                snippet: "sum(ticket_change_number)/sum(normal_order_number)",
            },
            {
                alias: "ticket_return_rate",
                snippet: "sum(ticket_return_number)/sum(normal_order_number)",
            },
            {
                alias: "dd_ydsj_group",
                snippet: "AGG_DATE_MONTH([dd_ydsj])",
            },
        ],
    });
};
export const queryTrainPayType = (): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: [
            {
                aggOperator: null,
                column: ["ddlx"],
                sqlOperator: "EQ",
                values: [
                    {
                        value: "06001",
                        valueType: "STRING",
                    },
                ],
            },
        ] as any,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...trainCommonKey,
        aggregators: [
            {
                alias: "支付类型",
                column: ["pay_type"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "支付类型",
                column: ["pay_type"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};
export const queryTrainPayPlatform = (
    name: string | null,
    from: string | null
): Promise<Result> => {
    const filtersArr: any = [
        {
            aggOperator: null,
            column: ["ddlx"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "06001",
                    valueType: "STRING",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["budget_source"],
            sqlOperator: "NOT_NULL",
            values: [],
        },
    ];

    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...trainCommonKey,
        aggregators: [
            {
                alias: "预算类型数量",
                column: ["budget_source"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "预算类型",
                column: ["budget_source"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
export const queryTrainMapData = (
    name: string | null,
    from: string | null
): Promise<Result> => {
    let filtersArr = [
        {
            aggOperator: null,
            column: ["cfcsjd"],
            sqlOperator: "NOT_NULL",
            values: [],
        },
        {
            aggOperator: null,
            column: ["sfgq"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "0",
                    valueType: "STRING",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["sftp"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "0",
                    valueType: "STRING",
                },
            ],
        },
    ] as any;

    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...trainCommonKey,
        viewId: "8b0b509fdd954c93af797232767dd71f",
        aggregators: [
            {
                alias: "订单数量",
                column: ["cfcsmc"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "出发城市名称",
                column: ["cfcsmc"],
            },
            {
                alias: "到达城市名称",
                column: ["ddcsmc"],
            },
            {
                alias: "出发城市经度",
                column: ["cfcsjd"],
            },
            {
                alias: "出发城市维度",
                column: ["cfcswd"],
            },
            {
                alias: "到达城市经度",
                column: ["ddcsjd"],
            },
            {
                alias: "到达城市维度",
                column: ["ddcswd"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 200,
        },
        orders: [
            {
                column: ["person_time_number"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
    });
};
//以下是用车业务接口
const taxiCommonKey = {
    ...commonKey,
    viewId: "3683b29f5389486c82ac52c22a7ef981",
};
export const queryTaxiAccumulative = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...taxiCommonKey,
        a: "累计成交",
        aggregators: [
            {
                alias: "订单数",
                column: ["normal_order_number"],
                sqlOperator: "SUM",
            },
            {
                alias: "成交金额",
                column: ["jg_ysje"],
                sqlOperator: "SUM",
            },
            {
                alias: "用车城市",
                column: ["cfcsmc"],
                sqlOperator: "COUNT_DISTINCT",
            },
            // {
            //     "alias": "里程",
            //     "column": [
            //         "bdlc"
            //     ],
            //     "sqlOperator": "SUM"
            // },
            // {
            //     "alias": "车型",
            //     "column": [
            //         "cxzmc"
            //     ],
            //     "sqlOperator": "COUNT_DISTINCT"
            // },
            // {
            //     "alias": "退单率",
            //     "column": [
            //         "order_return_rate"
            //     ]
            // },
            {
                alias: "预算部门",
                column: ["budget_department_code"],
                sqlOperator: "COUNT_DISTINCT",
            },
            {
                alias: "结算单位",
                column: ["account_company_code"],
                sqlOperator: "COUNT_DISTINCT",
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns: [
            {
                alias: "order_return_rate",
                snippet: "sum(retreat_number)/sum(normal_order_number)",
            },
        ],
    });
};



export const querySynchronismTaxiTrend = (
    { functionColumns }: any,
    name: string | null,
    from: string | null,
    dateParams: any,
): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilterSynchronism({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
        dateParams,
    });
    return queryCommonData({
        ...commonParams,
        ...taxiCommonKey,
        aggregators: [
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
            {
                alias: "成交金额",
                column: ["jg_ysje"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "dd_ydsj_group",
                column: ["dd_ydsj_group"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns,
    });
};
export const queryTaxiTrend = (
    { functionColumns }: any,
    name: string | null,
    from: string | null,
): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...taxiCommonKey,
        aggregators: [
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
            {
                alias: "成交金额",
                column: ["jg_ysje"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "dd_ydsj_group",
                column: ["dd_ydsj_group"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns,
    });
};
export const queryTaxiPercentage = (name: string | null, from: string | null): Promise<Result> => {
    // params
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["ddlx"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "10001",
                    valueType: "STRING",
                },
            ],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...taxiCommonKey,
        a: "用车服务类型",
        aggregators: [
            {
                alias: "服务类型数量",
                column: ["cxzmc"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "cxzmc",
                column: ["cxzmc"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
    });
};
export const queryTaxiCityRank = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [
        {
            column: ["cfcsid"],
            sqlOperator: "NOT_NULL",
            values: [],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...taxiCommonKey,
        aggregators: [
            {
                alias: "数量",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "cfcsmc",
                column: ["cfcsmc"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 20,
        },
        orders: [
            {
                column: ["person_time_number"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
    });
};
export const queryTaxiSettleRank = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...taxiCommonKey,
        aggregators: [
            {
                alias: "金额",
                column: ["jg_ysje"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "account_company_name",
                column: ["account_company_name"],
            },
        ],
        orders: [
            {
                column: ["jg_ysje"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
    });
};
export const queryTaxiUsageTime = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["ddlx"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "10001",
                    valueType: "STRING",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["ydsj_hour"],
            sqlOperator: "NOT_NULL",
            values: [],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...taxiCommonKey,
        aggregators: [
            {
                alias: "实际上车时间-小时",
                column: ["ydsj_hour"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "实际上车时间小时",
                column: ["ydsj_hour"],
            },
        ],
        orders: [
            {
                column: ["ydsj_hour"],
                operator: "ASC",
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
export const queryTaxiBudgetType = (name: string | null, from: string | null): Promise<Result> => {
    const filterArr = [
        {
            aggOperator: null,
            column: ["ddlx"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "10001",
                    valueType: "NUMERIC",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["budget_source"],
            sqlOperator: "NOT_NULL",
            values: [],
        },
    ] as any;
    if (name) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...taxiCommonKey,
        a: "预算类型",
        aggregators: [
            {
                alias: "预算类型数量",
                column: ["budget_source"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "预算类型",
                column: ["budget_source"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
export const queryTaxiType = (name: string | null, from: string | null): Promise<Result> => {
    const filterArr = [
        {
            aggOperator: null,
            column: ["ddlx"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "10001",
                    valueType: "STRING",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["yskmmc"],
            sqlOperator: "NOT_NULL",
            values: [],
        },
    ] as any;
    if (name) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...taxiCommonKey,
        aggregators: [
            {
                alias: "费用项目数量",
                column: ["yskmmc"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "费用项目",
                column: ["yskmmc"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
export const queryTaxiMapData = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["ddlx"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "10001",
                    valueType: "NUMERIC",
                },
            ],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...taxiCommonKey,
        aggregators: [
            {
                alias: "订单数量",
                column: ["normal_order_number"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "省份名称",
                column: ["province_name"],
            },
            {
                alias: "省份code",
                column: ["province_area_code"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
export const queryTaxiCityData = (provinceCode): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: [
            {
                aggOperator: null,
                column: ["province_area_code"],
                sqlOperator: "EQ",
                values: [
                    {
                        value: provinceCode,
                        valueType: "STRING",
                    },
                ],
            },
            {
                aggOperator: null,
                column: ["ddlx"],
                sqlOperator: "EQ",
                values: [
                    {
                        value: "10001",
                        valueType: "STRING",
                    },
                ],
            },
        ] as any,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...taxiCommonKey,
        aggregators: [
            {
                alias: "订单数量",
                column: ["normal_order_number"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "城市名称",
                column: ["cfcsmc"],
            },
            {
                alias: "城市code",
                column: ["city_area_code"],
            },
            {
                alias: "经纬度",
                column: ["jwd"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 1000,
        },
    });
};
//以下是差旅酒店业务接口
const hotelCommonKey = {
    ...commonKey,
    viewId: "8cac337d644c4850a5a64e09b278dd1c",
};
export const queryHotelAccumulative = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...hotelCommonKey,
        aggregators: [
            {
                alias: "订单数",
                column: ["order_number"],
            },
            {
                alias: "间夜数",
                column: ["jys_sum"],
            },
            {
                alias: "成交金额",
                column: ["fyje_number"],
            },
            // {
            //     alias: "入住人数",
            //     column: ["rzrs_sum"],
            // },
            {
                alias: "政策节省",
                column: ["zcjs_sum"],
            },
            {
                alias: "酒店均价",
                column: ["jdjj_avg"],
            },
            {
                alias: "预算部门",
                column: ["budget_people_emp_number"],
            },
            {
                alias: "结算单位",
                column: ["account_company_code_number"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns: [
            {
                alias: "jdjj_avg",
                snippet: "ifnull(sum(fyje)/sum(jys),0)",
            },
            {
                alias: "budget_people_emp_number",
                snippet: "count(distinct budget_department_code)",
            },
            {
                alias: "jys_sum",
                snippet: "ifnull(sum(jys),0)  ",
            },
            {
                alias: "rzrs_sum",
                snippet: "ifnull(sum(if(sftf=1,0,rzrs)),0)  ",
            },
            {
                alias: "zcjs_sum",
                snippet: "ifnull(sum(zcjs),0)  ",
            },
            {
                alias: "account_company_code_number",
                snippet: "count(distinct account_company_code)",
            },
            {
                alias: "order_number",
                snippet: "SUM(normal_order_number)",
            },
            {
                alias: "fyje_number",
                snippet: "ifnull(sum(fyje),0)",
            },
        ],
    });
};
export const querySynchronismHotelTrend = (
    { functionColumns }: any,
    name: string | null,
    from: string | null,
    dateParams: any
): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilterSynchronism({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
        dateParams,
    });
    return queryCommonData({
        ...commonParams,
        ...hotelCommonKey,
        aggregators: [
            {
                alias: "间夜数",
                column: ["jys"],
                sqlOperator: "SUM",
            },
            {
                alias: "成交金额",
                column: ["fyje"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "dd_ydsj_group",
                column: ["dd_ydsj_group"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns,
    });
};
export const queryHotelTrend = (
    { functionColumns },
    name,
    from
): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...hotelCommonKey,
        aggregators: [
            {
                alias: "间夜数",
                column: ["jys"],
                sqlOperator: "SUM",
            },
            {
                alias: "成交金额",
                column: ["fyje"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "dd_ydsj_group",
                column: ["dd_ydsj_group"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns,
    });
};
export const queryPolicySavings = (
    name: string,
    from: string
): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: [
                "supplier_name"
            ],
            sqlOperator: "IN",
            values: [
                {
                    value: "锦江",
                    valueType: "STRING"
                },
                {
                    value: "如家",
                    valueType: "STRING"
                },
                {
                    value: "华住",
                    valueType: "STRING"
                }
            ]
        }
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...hotelCommonKey,
        aggregators: [
            {
                "alias": "政策节省",
                "column": [
                    "zcjs"
                ],
                "sqlOperator": "SUM"
            }
        ],
        groups: [{
            "alias": "供应商名称",
            "column": [
                "supplier_name"
            ]
        }],
        filters,
        orders: [
            {
                column: [
                    "zcjs"
                ],
                operator: "ASC",
                aggOperator: "SUM"
            }
        ],
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns: [
            {
                alias: "fyje_number",
                snippet: "ifnull(sum(fyje),0)",
            },
        ],
    });
};
export const queryPricePercentage = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...hotelCommonKey,
        viewId: "8cac337d644c4850a5a64e09b278dd1c",
        aggregators: [
            {
                alias: "间夜数",
                column: ["jys"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "价格分布",
                column: ["price_distribution"],
            },
        ],
        filters,
        orders: [
            {
                column: ["price_distribution"],
                operator: "DESC",
            },
        ],
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
    });
};
export const queryHotelCityRank = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...hotelCommonKey,
        aggregators: [
            {
                alias: "SUM(jys)",
                column: ["jys"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "rzcs",
                column: ["rzcs"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 20,
        },
        orders: [
            {
                column: ["jys"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
    });
};
export const queryHotelSettleRank = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [{
        aggOperator: null,
        column: ["account_company_name"],
        sqlOperator: "NOT_NULL",
        values: [],
    }];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...hotelCommonKey,
        aggregators: [
            {
                alias: "成交金额",
                column: ["fyje"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "account_company_name",
                column: ["account_company_name"],
            },
        ],
        orders: [
            {
                column: ["fyje"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
    });
};
export const queryHotelSupplier = (name: string | null, from: string | null): Promise<Result> => {
    let filterArr: any = [];
    if (name) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...hotelCommonKey,
        aggregators: [
            {
                alias: "间夜数量",
                column: ["jys"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "供应商名称",
                column: ["supplier_name"],
            },
        ],
        orders: [
            {
                column: ["jys"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
//差旅酒店星级分布
export const queryStarType = (name: string | null, from: string | null): Promise<Result> => {
    let filterArr: any = [];
    if (name) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...hotelCommonKey,
        a: '星级分布',
        aggregators: [
            {
                alias: "间夜数",
                column: [
                    "jys"
                ],
                sqlOperator: "SUM"
            }
        ],
        groups: [
            {
                alias: "酒店星级名称",
                column: [
                    "jdxjmc"
                ]
            }
        ],
        orders: [],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
//差旅酒店供应商类型
export const queryHotelSupplierType = (name: string | null, from: string | null): Promise<Result> => {
    let filterArr: any = [{
        aggOperator: null,
        column: ["supplier_type"],
        sqlOperator: "NOT_NULL",
        values: [],
    }];
    if (name) {
        filterArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filterArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...hotelCommonKey,
        a: '供应商类型',
        aggregators: [
            {
                alias: "间夜",
                column: [
                    "jys"
                ],
                sqlOperator: "SUM"
            }
        ],
        groups: [
            {
                alias: "供应商类型",
                column: [
                    "supplier_type"
                ]
            }
        ],
        orders: [],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
export const queryHotelPayType = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["ddlx"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "03001",
                    valueType: "STRING",
                },
            ],
        },  
        {
            aggOperator: null,
            column: ["pay_type"],
            sqlOperator: "NOT_NULL",
            values: [],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...hotelCommonKey,
        aggregators: [
            {
                alias: "支付类型数量",
                column: ["pay_type"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "支付类型",
                column: ["pay_type"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
export const queryHotelPayPlatform = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["ddlx"],
            sqlOperator: "EQ",
            values: [
                {
                    value: "03001",
                    valueType: "STRING",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["budget_source"],
            sqlOperator: "NOT_NULL",
            values: [],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...hotelCommonKey,
        aggregators: [
            {
                alias: "预算类型数量",
                column: ["budget_source"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "预算类型",
                column: ["budget_source"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};
//查询省份数据
export const queryHotelMapData = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    filters.push({
        aggOperator: null,
        column: ["province_name"],
        sqlOperator: "NOT_NULL",
        values: [],
    });
    filters.push({
        aggOperator: null,
        column: ["sftf"],
        sqlOperator: "EQ",
        values: [
            {
                value: "0",
                valueType: "STRING",
            },
        ],
    });
    return queryCommonData({
        ...commonParams,
        ...hotelCommonKey,
        aggregators: [
            {
                alias: "间夜",
                column: ["jys"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "省份名称",
                column: ["province_name"],
            },
            {
                alias: "城市code",
                column: ["province_code"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};
//根据省份查询城市数据
export const queryHotelCityData = (provinceCode): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: [
            {
                aggOperator: null,
                column: ["province_code"],
                sqlOperator: "EQ",
                values: [
                    {
                        value: provinceCode,
                        valueType: "STRING",
                    },
                ],
            },
            {
                aggOperator: null,
                column: ["sftf"],
                sqlOperator: "EQ",
                values: [
                    {
                        value: "0",
                        valueType: "STRING",
                    },
                ],
            },
        ] as any,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        ...hotelCommonKey,
        aggregators: [
            {
                alias: "间夜数",
                column: ["jys"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "城市名称",
                column: ["rzcs"],
            },
            {
                alias: "城市code",
                column: ["city_area_code"],
            },
            {
                alias: "经纬度",
                column: ["jwd"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};
//以下是商旅业务总览接口
export const queryOverviewAmount = {
    trip: (): Promise<Result> => {
        return queryCommonData({
            ...commonParams,
            viewId: "ab679edb60894c8b95a65fe5fed786d3",
            aggregators: [
                {
                    alias: "成交金额",
                    column: ["fyje_number"],
                },
            ],
            filters: getCommonFilter({
                dataSourceKey: 'budget_source',
                companyColumn: "account_company_code",
                dateColumn: "dd_ydsj",
            }),
            pageInfo: {
                countTotal: false,
                pageSize: 100,
            },
            functionColumns: [
                {
                    alias: "fyje_number",
                    snippet: "ifnull(sum(fyje),0)",
                },
            ],
        });
    },
    hotel: (): Promise<Result> => {
        return queryCommonData({
            ...commonParams,
            viewId: "8cac337d644c4850a5a64e09b278dd1c",
            aggregators: [
                {
                    alias: "成交金额",
                    column: ["fyje_number"],
                },
            ],
            filters: getCommonFilter({
                dataSourceKey: 'budget_source',
                companyColumn: "account_company_code",
                dateColumn: "dd_ydsj",
            }),
            pageInfo: {
                countTotal: false,
                pageSize: 100,
            },
            functionColumns: [
                {
                    alias: "fyje_number",
                    snippet: "ifnull(sum(fyje),0)",
                },
            ],
        });
    },
    train: (): Promise<Result> => {
        return queryCommonData({
            ...commonParams,
            viewId: "8b0b509fdd954c93af797232767dd71f",
            aggregators: [
                {
                    alias: "成交金额",
                    column: ["fyje_number"],
                },
            ],
            filters: getCommonFilter({
                dataSourceKey: 'budget_source',
                companyColumn: "account_company_code",
                dateColumn: "dd_ydsj",
            }),
            pageInfo: {
                countTotal: false,
                pageSize: 100,
            },
            functionColumns: [
                {
                    alias: "fyje_number",
                    snippet: "ifnull(sum(fyje),0)",
                },
            ],
        });
    },
    taxi: (): Promise<Result> => {
        return queryCommonData({
            ...commonParams,
            viewId: "3683b29f5389486c82ac52c22a7ef981",
            aggregators: [
                {
                    alias: "成交金额",
                    column: ["jg_ysje_number"],
                },
            ],
            filters: getCommonFilter({
                dataSourceKey: 'budget_source',
                companyColumn: "account_company_code",
                dateColumn: "dd_ydsj",
            }),
            pageInfo: {
                countTotal: false,
                pageSize: 100,
            },
            functionColumns: [
                {
                    alias: "jg_ysje_number",
                    snippet: "ifnull(sum(jg_ysje),0)",
                },
            ],
        });
    },
};
export const queryOverviewTripNum = (): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        companyColumn: "account_company_code",
        dateColumn: "sqsj_date_key",
        defaultFilters: [
            {
                aggOperator: null,
                column: ["single_point"],
                sqlOperator: "EQ",
                values: [
                    {
                        value: "事前",
                        valueType: "STRING",
                    },
                ],
            },
        ] as any,
    });
    return queryCommonData({
        ...commonParams,
        viewId: "11378ac7792e4eed81b0d991cdc8fc30",
        a: '出差申请单数',
        aggregators: [
            {
                alias: "出差申请单数",
                column: ["sqdid"],
                sqlOperator: "COUNT",
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};
//成交金额
export const queryTransactionAmount = (): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: [] as any,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        viewId: "0c2669be59a14fac9aada79882fdfde0",
        aggregators: [
            {
                alias: "成交金额",
                column: ["fyje_sum"]
            },
        ],
        filters,
        functionColumns: [
            {
                alias: "fyje_sum",
                snippet:
                    "ifnull(sum(fyje) , 0)",
            } 
        ],
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};
//保险销售数
export const queryOverviewInsureNum = (): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        companyColumn: "account_company_code",
        dateColumn: "ydsj",
    });
    return queryCommonData({
        ...commonParams,
        viewId: "49dcf22d438349868b7452d6548b4e4b",
        aggregators: [
            {
                alias: "保险销售",
                column: ["jg_xsj"],
                sqlOperator: "SUM",
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};
//机票政策节省
export const queryOverviewAirTicket = (): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: [
            {
                aggOperator: null,
                column: ["gngj"],
                sqlOperator: "EQ",
                values: [
                    {
                        value: "1",
                        valueType: "STRING",
                    },
                ],
            },
        ] as any,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        viewId: "ab679edb60894c8b95a65fe5fed786d3",
        aggregators: [
            {
                alias: "机票政策节省",
                column: ["zcjs_number"],
            },
        ],
        filters,
        functionColumns: [
            {
                alias: "zcjs_number",
                snippet: "IFNULL(sum(if(sftp=1,0,zcjs)),0)",
            },
        ],
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};
//酒店政策节省
export const queryOverviewHotel = (): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        viewId: "8cac337d644c4850a5a64e09b278dd1c",
        aggregators: [
            {
                alias: "酒店政策节省",
                column: ["zcjs_sum"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
        functionColumns: [
            {
                alias: "zcjs_sum",
                snippet: "ifnull(sum(zcjs),0)  ",
            },
        ],
    });
};
//总览顶部服务费总数
export const queryOverviewServiceSum = (): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: [
            {
                aggOperator: null,
                column: ["cpmc"],
                sqlOperator: "NE",
                values: [
                    {
                        value: "用车",
                        valueType: "STRING",
                    },
                ],
            }, {
                aggOperator: null,
                column: ["cpmc"],
                sqlOperator: "NE",
                values: [
                    {
                        value: "地面服务",
                        valueType: "STRING",
                    },
                ],
            }, {
                aggOperator: null,
                column: ["cpmc"],
                sqlOperator: "NE",
                values: [
                    {
                        value: "酒店",
                        valueType: "STRING",
                    },
                ],
            }
        ] as any,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        a: "总览服务费",
        viewId: "0c2669be59a14fac9aada79882fdfde0",
        aggregators: [
            {
                alias: "服务费总和",
                column: ["ptfwf"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "产品名称",
                column: ["cpmc"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};
//总览顶部获取酒店服务费
export const queryhotelServiceSum = (): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: [] as any,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        a: "酒店服务费",
        viewId: "0eeb8c921fb14520b396e5643e5bbe3f",
        aggregators: [
            {
                alias: "酒店服务费",
                column: ["ptfwf"],
                sqlOperator: "SUM",
            },
        ],
        groups: [],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};

//总览顶部地面服务
export const queryGroundService = (): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        viewId: "73c7c793091c47838ef1684357fd2225",
        aggregators: [
            {
                alias: "地面服务",
                column: ["fyje"],
                sqlOperator: "SUM",
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
        functionColumns: [
            {
                alias: "jphf_number",
                snippet: "ifnull(sum(jphf),0)",
            },
        ],
    });
};

//保险销售分布
export const queryOverviewInsurePercentage = (): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        companyColumn: "account_company_code",
        dateColumn: "ydsj",
    });
    return queryCommonData({
        ...commonParams,
        viewId: "49dcf22d438349868b7452d6548b4e4b",
        aggregators: [
            {
                alias: "保险数量",
                column: ["bxsl"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "保险名称",
                column: ["bxlxmc"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};
//出差申请单
export const queryOverviewTravelStatus = ({
    functionColumns,
}): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        companyColumn: "account_company_code",
        dateColumn: "sqsj",
    });
    return queryCommonData({
        ...commonParams,
        viewId: "24a86f9f99184e8db5fa7502ee78dc84",
        aggregators: [
            {
                alias: "审批完成",
                column: ["approval_completed"],
                sqlOperator: "SUM",
            },
            {
                alias: "已确认",
                column: ["confirmed"],
                sqlOperator: "SUM",
            },
            {
                alias: "审批中",
                column: ["in_approval"],
                sqlOperator: "SUM",
            },
            {
                alias: "已报销",
                column: ["reimbursed"],
                sqlOperator: "SUM",
            },
            {
                alias: "已拒绝",
                column: ["rejected"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "申请时间",
                column: ["sqsj_group"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
        functionColumns,
    });
};
//出差预算单 改为金额
export const queryOverviewTravelBudget = ({
    functionColumns,
}): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: [] as any,
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    filters.push(
        {
            aggOperator: null,
            column: ["budget_source"],
            sqlOperator: "NOT_NULL",
            values: [],
        }
    )
    return queryCommonData({
        ...commonParams,
        a: "领域消费分布",
        viewId: "0c2669be59a14fac9aada79882fdfde0",
        aggregators: [
            {
                alias: "已使用金额",
                column: ["fyje"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "预算类型",
                column: ["budget_source"],
            },
        ],
        orders: [
            {
                column: ["fyje"],
                operator: "DESC",
                aggOperator: "SUM",
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
        functionColumns,
    });
};

//地面服务
export const queryGroundServices = ({ functionColumns }): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        a: "地面服务",
        viewId: "73c7c793091c47838ef1684357fd2225",
        aggregators: [
            {
                alias: "人次",
                column: ["person_time_number"],
                sqlOperator: "SUM",
            },
            {
                alias: "金额",
                column: ["fyje"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "dd_ydsj_group",
                column: ["dd_ydsj_group"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
        functionColumns,
    });
};

//总览费用类型占比
export const queryExpenseType = (): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        a: '费用类型占比',
        viewId: "0c2669be59a14fac9aada79882fdfde0",
        aggregators: [
            {
                alias: "费用总计",
                column: ["fyje"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "业务类型",
                column: ["cpmc"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};

//总览服务费饼图
export const queryOverviewServiceCharge = (): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        viewId: "0c2669be59a14fac9aada79882fdfde0",
        aggregators: [
            {
                alias: "平台服务费",
                column: ["ptfwf"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "产品名称",
                column: ["cpmc"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};
//总览顶部服务人次
export const queryOverviewPersonTime = (): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        companyColumn: "account_company_code",
        dateColumn: "dd_ydsj",
    });
    return queryCommonData({
        ...commonParams,
        viewId: "0c2669be59a14fac9aada79882fdfde0",
        aggregators: [
            {
                alias: "服务人次",
                column: ["person_time_number_sum"]
            },
        ],
        filters,
        functionColumns: [
            {
                alias: "person_time_number_sum",
                snippet:
                    "ifnull(sum(person_time_number) , 0)",
            } 
        ],
        pageInfo: {
            countTotal: false,
            pageSize: 100,
        },
    });
};

//会务异地接口
export const queryOffsiteMapData = (): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: [
            {
                aggOperator: null,
                column: ["status"],
                sqlOperator: "IN",
                values: [
                    {
                        value: "会议完成",
                        valueType: "STRING",
                    },
                    {
                        value: "会议执行中",
                        valueType: "STRING",
                    },
                ],
            },
            {
                aggOperator: null,
                column: ["type"],
                sqlOperator: "IN",
                values: [
                    {
                        value: "异地会议",
                        valueType: "STRING",
                    },
                ],
            },
        ] as any,
        companyColumn: "account_company_code",
        dateColumn: "gmt_create",
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        ceshi: ****************,
        viewId: "99186c758c30414fae907c2e14c69626",
        aggregators: [
            {
                alias: "订单数量",
                column: ["code"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "province_name",
                column: ["province_name"],
            },
            {
                alias: "province_code",
                column: ["province_code"],
            },
            {
                alias: "province_lng",
                column: ["province_lng"],
            },
            {
                alias: "province_lat",
                column: ["province_lat"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        orders: [],
    });
};
const offsiteCommonKey = {
    ...commonKey,
    viewId: "99186c758c30414fae907c2e14c69626",
};

export const queryOffsiteCityData = (
    code: string | number
): Promise<Result> => {
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: [
            {
                aggOperator: null,
                column: ["province_code"],
                sqlOperator: "EQ",
                values: [
                    {
                        value: code,
                        valueType: "STRING",
                    },
                ],
            },
            {
                aggOperator: null,
                column: ["status"],
                sqlOperator: "IN",
                values: [
                    {
                        value: "会议完成",
                        valueType: "STRING",
                    },
                    {
                        value: "会议执行中",
                        valueType: "STRING",
                    },
                ],
            },
            {
                aggOperator: null,
                column: ["type"],
                sqlOperator: "IN",
                values: [
                    {
                        value: "异地会议",
                        valueType: "STRING",
                    },
                ],
            },
        ] as any,
        companyColumn: "account_company_code",
        dateColumn: "gmt_create",
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        ...offsiteCommonKey,
        aggregators: [
            {
                alias: "订单数量",
                column: ["code"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "城市名称",
                column: ["city_name"],
            },
            {
                alias: "城市code",
                column: ["city_code"],
            },
            {
                alias: "经纬度",
                column: ["city_jwd"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 1000,
        },
    });
};

export const querylocalMapData = (name: string | null, from: string | null): Promise<Result> => {
    let filtersArr: any = [
        {
            aggOperator: null,
            column: ["status"],
            sqlOperator: "IN",
            values: [
                {
                    value: "会议完成",
                    valueType: "STRING",
                },
                {
                    value: "会议执行中",
                    valueType: "STRING",
                },
            ],
        },
        {
            aggOperator: null,
            column: ["type"],
            sqlOperator: "IN",
            values: [
                {
                    value: "青岛会议",
                    valueType: "STRING",
                },
            ],
        },
    ];
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "gmt_create",
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        ...offsiteCommonKey,
        aggregators: [
            {
                alias: "订单数量",
                column: ["code"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "区名",
                column: ["county_name"],
            },
            {
                alias: "区code",
                column: ["county_code"],
            },
            {
                alias: "区经度",
                column: ["county_lng"],
            },
            {
                alias: "区维度",
                column: ["county_lat"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 1000,
        },
    });
};
