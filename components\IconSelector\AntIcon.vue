<template>
    <component :is="getIconComponent(props.name)" :icon="undefined" :style="{ fontSize: '20px', width: iconSize, height: iconSize }" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import * as Icons from "@ant-design/icons-vue";

defineOptions({ name: 'AntIcon' });

const getIconComponent = (iconName: string) => {
  return Icons[iconName as keyof typeof Icons];
}

const props = withDefaults(defineProps<Props>(), {
  name: '',
  color: '',
  size: 20,
});

interface Props {
  name: string;
  color?: string;
  size?: string | number;
}

// 判断传入的值，是否带有单位，如果没有，就默认用px单位
const getUnitValue = (value: string | number): string | number => {
  return /(px|em|rem|%)$/.test(value.toString()) ? value : `${value}px`;
};

const iconSize = computed<string | number>(() => {
  return getUnitValue(props.size);
});
</script>

<style scoped lang="less">
.svg-icon {
  width: auto;
  height: auto;
  // fill: currentColor;
  vertical-align: middle;
  flex-shrink: 0;
}
</style>