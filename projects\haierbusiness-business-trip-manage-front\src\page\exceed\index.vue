<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { DownOutlined, ExclamationCircleOutlined, PlusOutlined, UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { enterpriseApi, manageApi } from '@haierbusiness-front/apis';
import {
  IEnterpriseListRequest,
  IEnterprise
} from '@haierbusiness-front/common-libs';
import { errorModal, routerParam } from '@haierbusiness-front/utils';
import dayjs, { Dayjs } from 'dayjs';
import { computed, onMounted, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useRouter } from "vue-router";
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import EditDialog from './edit-dialog.vue'

const router = useRouter()
const columns: ColumnType[] = [
  {
    title: '产品名称',
    dataIndex: 'productName',
    align: 'center',
  },
  {
    title: '产品编码',
    dataIndex: 'productCode',
    align: 'center',
  },
  {
    title: '产品图标',
    dataIndex: 'iconUrl',
    align: 'center',
  },
  {
    title: '服务费(元)',
    dataIndex: 'serviceAmount',
    align: 'center',
  },
 
  {
    title: '保险费(元)',
    dataIndex: 'insuranceAmount',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '180px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<IEnterpriseListRequest>({ })

const manageParams = ref()

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(manageApi.list, {
  defaultParams: [{}],
  manual: false
});

const reset = () => {
  searchParam.value = {}
}

const dataSource = computed(() => (data.value?.records?.filter(item => {
  if (item.iconUrl) {
     item.files = [{
    name: `${item.productName}`,
    thumbUrl: item.iconUrl
  }]
  }
  return item
}) || []));

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
  /* stateAccountApiRun(
    searchParam.value
  )
  cvpErrorAccountApiRun(
    searchParam.value
  )
  pushCvpErrorAccountApiRun(
    searchParam.value
  ) */
};


// 新增表单相关
const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } =
  useEditDialog<IEnterprise, IEnterprise>(manageApi, "产品", () => listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize,
  }))

  const { handleDelete } = useDelete(manageApi, () => listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize,
  }))


</script>

<template>
  <!-- <Eloading :loading="confirmLoading"></Eloading>
  <Eloading :loading="confirmAccountLoading"></Eloading>
  <Eloading :loading="revokeConfirmLoading"></Eloading>
  <Eloading :loading="cancelLoading"></Eloading> -->
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">

    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="productCode">产品编码：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="productCode" v-model:value="searchParam.productCode" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="productName">产品名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="productName" v-model:value="searchParam.productName" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: left;">
            <h-button type="primary" @click="handleCreate">
              <PlusOutlined /> 新增产品
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            
            <template v-if="column.dataIndex === 'iconUrl'">
              <a-image
              v-if="record.iconUrl"
                :width="30"
                :src="record.iconUrl"
              />
              <div v-else>--</div>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link"  @click="handleEdit(record)">编辑</h-button>
              <h-button type="link"  @click="handleDelete(record.id)">删除</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

  </div>

  <div v-if="visible">
    <edit-dialog
        :show="visible"
        :data="editData"
        @cancel="onDialogClose"
        @ok="handleOk"
    >
    </edit-dialog>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
