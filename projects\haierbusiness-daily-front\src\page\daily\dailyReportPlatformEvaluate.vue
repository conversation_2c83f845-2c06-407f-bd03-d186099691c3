<script setup lang="ts">
import {
  Modal,
  Tag as hTag,
  Form as hForm,
  Input as hInput,
  Popover as hPopover,
  InputNumber as hInputNumber,
  Popconfirm as hPopconfirm,
  FormItem as hFormItem,
  Select as hSelect,
  SelectOption as hSelectOption,
  <PERSON>ton as hButton,
  Col as hCol,
  Row as hRow,
  Spin as hSpin,
  Table as hTable,
  Textarea as hTextarea,
  DatePicker as hDatePicker,
  CollapsePanel as hCollapsePanel,
  Collapse as hCollapse,
  FormInstance,
  message,
  TableColumnsType,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { PlusOutlined, SearchOutlined, ExpandAltOutlined, MinusCircleOutlined } from '@ant-design/icons-vue';
import {
  IAnnualPlanSaveOrUpdateRequestDTO,
  AnnualPlanTypeStateConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
  IHaierAccountBillInfo,
IUserInfo,
} from '@haierbusiness-front/common-libs';
import { getCurrentRouter, resolveParam } from '@haierbusiness-front/utils';
import { ref, createVNode, PropType, computed } from 'vue';

import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';
import dayjs, { Dayjs } from 'dayjs';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import {
  EvaluateControlConstant,
  IAnnualPlanDetailRequestDTO,
  IAnnualPlanDetailResponseDTO,
  IAnnualPlanTypeListRequest,
  IDailyPersonalResponse,
  IDailyReportConferenceRequestDTO,
  IDailyReportListResponseDTO,
  IDailyReportProjectRequestDTO,
  IEvaluateDO,
  IMonthPlanDetailResponseDTO,
  PlanTypeConstant,
} from '@haierbusiness-front/common-libs/src/daily';
import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
const { loginUser } = storeToRefs(applicationStore(globalPinia));

const router = getCurrentRouter();
const prop = defineProps({
  data: Object as PropType<IEvaluateDO>,
  dailyReport: Object as PropType<IDailyReportListResponseDTO>,
  monthData: Object as PropType<IMonthPlanDetailResponseDTO>,
  fold: Boolean as PropType<Boolean>,
  dailyPerson: Object as PropType<IDailyPersonalResponse[]>,
});

const collapseActiveKey = ref([1]);
{
  if (prop?.fold) {
    collapseActiveKey.value = [];
  }
}
</script>

<template>
  <h-collapse
    v-model:activeKey="collapseActiveKey"
    :bordered="false"
    style="background-color: white"
    :collapsible="'icon'"
  >
    <h-collapse-panel key="1">
      <template #header>
        <div style="display: flex">
          <!--  <div style="height: 25px; width: 10px; background-color: rgb(0, 115, 229)" /> -->
          <div style="font-size: 16px; font-weight: 600; margin-left: 10px">平台评价</div>
        </div>
      </template>
      <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%"></div>
      <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px">
        <h-row :gutter="24" style="margin-top: 15px">
          <h-col :span="18" :offset="2">
            <h-form-item label="&nbsp;&nbsp;&nbsp;平台评价" name="evaluateAmount">
              <h-input-number
                :precision="2"
                :decimalPlaces="2"
                v-model:value="data!!.evaluateAmount"
                style="margin: -5px 0; width: 200px"
                type="number"
                allowClear
                placeholder="平台评价"
              />&nbsp;&nbsp;&nbsp;/&nbsp;&nbsp;&nbsp;元
            </h-form-item>
            <h-form-item label="维度&要求" name="evaluateRemark">
              <h-textarea v-model:value="data!!.evaluateRemark" style="width: 800px" :rows="4" allowClear />
            </h-form-item>
          </h-col>
        </h-row>
      </div>
    </h-collapse-panel>
  </h-collapse>
</template>

<style scoped lang="less">
@import '../../assets/css/main.less';
</style>
