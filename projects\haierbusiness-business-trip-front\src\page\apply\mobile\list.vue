<template>
  <div class="order-list" style="padding-bottom: 50px; min-height: 100vh;" >

    <van-sticky>
      <van-search class="list-search" shape="round" @search="reSearch" v-model="defaultParams.applyNo" @clear="reSearch" @click-left-icon="reSearch" :clearable="true" show-action placeholder="搜索我的订单">
        <template #action>
          <van-dropdown-menu :close-on-click-outside="false" ref="menuRef">
            <van-dropdown-item ref="itemRef">
              <template #title>
                <van-icon name="filter-o" />
                筛选
              </template>

              <van-cell-group title="按出差日期选择">
                <van-cell>
                  <template #value>
                    <!-- 快速选择时间 -->
                    <van-radio-group v-model="defaultParams.timeType">
                      <van-row justify="space-between">
                        <van-radio name="week" class="my-radio">
                          <template #icon="{ checked }">
                            <van-button class="btn-com" size="small" round :type="checked ? 'primary' : 'default'"
                              >最近一周</van-button
                            >
                          </template>
                        </van-radio>

                        <van-radio name="month" class="my-radio">
                          <template #icon="{ checked }">
                            <van-button class="btn-com" size="small" round :type="checked ? 'primary' : 'default'"
                              >最近一月</van-button
                            >
                          </template>
                        </van-radio>

                        <van-radio name="year" class="my-radio">
                          <template #icon="{ checked }">
                            <van-button class="btn-com" size="small" round :type="checked ? 'primary' : 'default'"
                              >最近一年</van-button
                            >
                          </template>
                        </van-radio>
                      </van-row>
                    </van-radio-group>

                    <!-- 具体时间选择 -->
                    <van-row class="mt-10" style="margin-top: 10px;" justify="space-between">
                      <van-col :span="10">
                        <van-field
                          class="input-border"
                          readonly
                          @click="openTimePicker('begin')"
                          v-model="defaultParams.beginDate"
                          placeholder="选择开始时间"
                        />
                      </van-col>
                      <van-col :span="4">
                        <van-divider :style="{ height: '100%', borderColor: '#000', padding: '0 16px', margin: '0' }" />
                      </van-col>
                      <van-col :span="10">
                        <van-field
                          class="input-border"
                          readonly
                          @click="openTimePicker('end')"
                          v-model="defaultParams.endDate"
                          placeholder="选择结束时间"
                        />
                      </van-col>
                    </van-row>
                  </template>
                </van-cell>
              </van-cell-group>
              
              <van-cell-group title="按出差人查询">
                <van-search
                  shape="round"
                  v-model="defaultParams.travelerKeyword"
                  :clearable="true"
                  left-icon=""
                  placeholder="出差人名称/工号"
                />
              </van-cell-group>

              <van-row justify="space-around" style="padding: 15px 0">
                <van-button class="list-search-btn" size="small" type="default" round @click="reSet"> 清空 </van-button>
                <van-button class="list-search-btn" size="small" type="primary" round @click="reSearch">
                  确认
                </van-button>
                <van-button @click.stop="goHistoryList" class="list-search-btn" size="small" type="primary" round @click="reSearch">
                  历史订单
                </van-button>
              </van-row>
            </van-dropdown-item>
          </van-dropdown-menu>
        </template>
      </van-search>

      <van-tabs v-model:active="defaultParams.status">
        <van-tab title="全部" name=""></van-tab>
        <van-tab v-for="(item, index) in documentState" :name="item.value" :title="item.label" :key="index"> </van-tab>
      </van-tabs>

    </van-sticky>

    <van-list
      v-model:loading="orderLoading"
      :finished="orderFinished"
      :finished-text="orderList.length ? '没有更多了' : ''"
      @load="loadorderList"
      class="mt-10"
    >
      <van-cell-group
        inset
        class="mt-10"
        v-for="(item, index) in orderList"
        :key="index"
        @click="goToDetail(item?.id)"
      >
        <van-cell >
          <template #title>
            <div class="weight600 font-size-12">{{ item?.applyNo }}</div>
          </template>
          <template #label>
            <div>
              <div v-if="item.beginDate && item.endDate">{{ item.beginDate }} ~ {{ item.endDate }}</div>
            </div>
          </template>

          <template #value>
            <van-tag v-if="item.status != '90' && item.travelReserveFlag" style="margin-right:3px" plain size="medium" :color="TripApprovalStatusToTagColor[item.auditStatus] || ''">{{ TripApprovalStatus[item.auditStatus] || '' }}</van-tag>
            <van-tag plain size="medium" :color="TripDocumentStatusToTagColor[item.status] || ''">{{ TripDocumentStatus[item.status] || '' }}</van-tag>
          </template>
        </van-cell>

        <van-cell title="经办人" :value="item.operUserName">
          
        </van-cell>

        <van-cell title="出差人">
          <template #value>
            <div>{{getMainPerson(item.travelerList)}}</div>
          </template>
        </van-cell>
        <van-cell title="同行人" value-class="large-value">
          <template #value>
            <div >{{getOutPerson(item.travelerList)}}</div>
          </template>
        </van-cell>
        <van-cell title="出差事由" :value="item.travelReason">
          
        </van-cell>
        <van-cell title="行程信息">
          <template #label>
            <div style="text-align: right">{{ tripListToStr(item.tripList) }}</div>
          </template>
        </van-cell>

        <van-cell title="预算费用" value-class="large-value">
          <template #value>
            <div >
              <span>{{ `¥ ${item.amountSum}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') }}</span>
              <span style="color: #3983e5">(总)</span>
              <!-- <span>{{ `¥ ${item.amountSum - item.realAmountSum}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') }}</span>
              <span style="color: #52c41a">(可用)</span> -->
            </div>
          </template>
        </van-cell>

       

        <van-cell>
          <template #value>
            <div>
              <van-button class="mr-5" v-if="item.status == '10'" type="primary" size="small" @click.stop="goToApply(item.id)">编辑</van-button>
              <van-button class="mr-5"  v-if="item.reimbursementButton" type="primary" size="small" @click.stop="showReimburseDialog(item)">去报销</van-button>
              <van-button class="mr-5" v-if="!item.changeApplyStatus ? item.confirmButton : (item.confirmButton && item.changeApplyStatus == '30')" type="primary" size="small" @click.stop="goToConfirm(item.applyNo)">行程确认</van-button>
              <van-button class="mr-5" type="primary" v-if="item.status != '10' && item.travelReserveFlag && item.type != 1 && item?.workFlowId" size="small" :loading="approvalIndex == index" @click.stop="goToApproval(item?.workFlowId, index)">审批详情</van-button>

              <van-button  class="mr-5" type="warning" v-if="item.status == '30' && item.type != 1 && item.changeApplyStatus != '20'" size="small" @click.stop="goToChange(item)">变更</van-button>
              <van-button  class="mr-5" type="danger"  v-if="(item.status == '10' || item.status == '20' || item.status == '30') && item.type != 1 && item.changeApplyStatus != '20' &&  item.auditStatus!='20' && loginUser?.username == item.createBy" size="small" @click.stop="cancelApply(item.id)">作废</van-button>
              <van-button type="danger" v-if="item.status == '20' && item.auditStatus == '20' && item.type != 1" size="small" @click.stop="recallApply(item.id)">撤回</van-button>
            </div>
          </template>
        </van-cell>
      </van-cell-group>
    </van-list>

    <van-empty v-if="!orderLoading && orderList.length == 0" description="暂无数据" />


    <!-- 时间选择 -->
    <van-popup @click.stop v-model:show="showTimePicker" :close-on-click-overlay="false" position="bottom">
      <van-date-picker
        title="选择日期"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="confirmTime"
        @cancel="showTimePicker = false"
      />
    </van-popup>

    <!-- 选择报销人 -->
    <van-popup @click.stop v-model:show="reimburseDialog" :close-on-click-overlay="false" position="bottom">
      <van-picker
        title="选择报销人"
        :columns="ReimburseTravelerList"
        :columns-field-names="{
          text: 'travelUserName',
          value: 'travelUserNo',
        }"
        @confirm="confimFuc"
        @cancel="closeReimburseDialog"
      />
    </van-popup>
    <van-floating-bubble   axis="xy" magnetic="x" v-model:offset="offsetHeight"  icon="edit" @click="goToAdd" />
  </div>
</template>

<script lang="ts" setup>
import { ref, watch,onActivated, computed, onMounted } from 'vue';

import {
  IUserListRequest,
  ROrderPayMentStateEnum,
  ROrderPayMentStateEnumColor,
  ROrderApprovalStateEnum,
  ROrderApprovalStateEnumColor,
  ROrderStateEnum,
  ROrderStateEnumColor,
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { tripApi,loginApi } from '@haierbusiness-front/apis';
import { removeStorageItem } from '@haierbusiness-front/utils';
import {
  HeaderConstant
} from '@haierbusiness-front/common-libs'
import {
  TripApprovalStatus,
  TripDocumentStatus,
  TripChangeApprovalStatus,
  TripChangeApprovalStatusToTagColor,
  TripChangeStatus,
  TripBudgeStatus,
  ICreatTrip,
  ITraveler,
  TripApprovalStatusToTagColor,
  TripDocumentStatusToTagColor,
  TripChangeStatusToTagColor,
  TripBudgeStatusToTagColor,
  IDataListItem,
  ICity,
  CityItem,
} from '@haierbusiness-front/common-libs';
import type { Ref } from 'vue';
import { getEnumOptions } from '@haierbusiness-front/utils';

import dayjs from 'dayjs';

import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { showConfirmDialog, showSuccessToast,showDialog, showFailToast } from 'vant';
const store = applicationStore();

const { loginUser } = storeToRefs(store);

const router = getCurrentRouter();
const route = ref();


// 添加悬浮窗方便跳转创建页
const offsetHeight = {x: document.body.clientWidth * 300 / 375, y: 500}
const goToAdd = () => {
  router.push('/mobile/apply')
}

// 审批状态
const approvalState = computed(() => {
  return getEnumOptions(TripApprovalStatus, true);
});

// 单据状态
const documentState = computed(() => {
  return getEnumOptions(TripDocumentStatus, true);
});

// 变更状态
const changeState = computed(() => {
  return getEnumOptions(TripChangeStatus, true);
});

// 预算状态

const budgeState = computed(() => {
  return getEnumOptions(TripBudgeStatus, true);
});

const getMainPerson = (list: Array<ITraveler>) => {
  return list.filter((item) => item.mainFlag === '1')[0]?.travelUserName;
};

let listTemp = [];
const getOutPerson = (list: Array<ITraveler>) => {
  listTemp = [];
  list.forEach((item) => {
    if (item.mainFlag !== '1') {
      if (item.travelUserType != '0') {
        listTemp.push(`${item.travelUserName}[外部]`);

      }else {
        listTemp.push(item.travelUserName);

      }
    }
  });
  return listTemp.join(',');
};
const reimburseDialog = ref(false)

// 可报销的人员列表
const ReimburseTravelerList = ref<Array<any>>([])
// 选择的报销人
const reimburseUserCode = ref<any>('')
// 选中的申请单数据
const activeApplyData = ref<any>({})

const showReimburseDialog = (data:any) => {
  activeApplyData.value = data

  // 如果是经办人可以给所有人报销
  // 否则只能给自己报销
  if(loginUser?.value?.username == data.operUserNo) {
    ReimburseTravelerList.value = data?.travelerList?.filter((item:any) => item.travelUserType == 0 && item.reimburseNum <= 1)
    if ( ReimburseTravelerList.value.length == 0) {
      showFailToast('暂无可报销人员!')
      return
    }
    reimburseUserCode.value = null
    reimburseDialog.value = true
  }else {
    reimburseUserCode.value = loginUser?.value?.username
    goToEES()
  }

}

const eesUrl = ref('')
// 去报销功能
const eesGingle = import.meta.env.VITE_BUSINESS_EES_SINGLE;

const closeReimburseDialog = () => {
  reimburseUserCode.value = ''
  reimburseDialog.value = false
  ReimburseTravelerList.value = []
}
const confimFuc = ({ selectedOptions }) => {
  reimburseUserCode.value = selectedOptions[0].travelUserNo
  goToEES()
}

const goToEES = () => {
  const token = loginUser?.value?.extended?.iamToken


  loginApi.checkIamToken({token}).then((res:any) => {
    console.log('验证iam_token是否有效----->>', res)
    if (res.data) {
      eesUrl.value = `${eesGingle}?travelCode=${activeApplyData.value.applyNo}&accountCode=${reimburseUserCode.value}&creatorCode=${loginUser.value?.username}&accessToken=${token}&fromOrigin=SLMOBILE`
      // 清除上次本地缓存的数据
      // window.open(eesUrl.value)
      console.log('去报销----->>', eesUrl.value)
      location.href = eesUrl.value
      localStorage.removeItem('reimburseData')
    }else {

      showDialog({
        title: '提示',
        message: '当前集团统一认证系统已过期，请重新登录！',
      }).then(() => {
        // on close
        console.log('ok');
        // 将这次跳转去报销的数据本地缓存起来
        localStorage.setItem('reimburseCode', JSON.stringify(reimburseUserCode.value))
        localStorage.setItem('reimburseData', JSON.stringify(activeApplyData.value))
        logout()
      });

    }
  })
  
}

const logout = () => {
  loginApi.haierIamTokenLogout({ token: loginUser?.value?.extended?.iamToken }).finally(() => {
    removeStorageItem(HeaderConstant.TOKEN_KEY.key, false)
    window.location.reload()
  })
}

const tripListToStr = ((list?: Array<any>) => {
  if(!list || list.length == 0) {
    return
  }
  let cityNames:string[] = []
  list.forEach((item,index) => {
    if(index == 0) {
      cityNames.push(item.beginCityName)
      cityNames.push(item.endCityName)
    }else {
      cityNames.push(item.endCityName)
    }
  })

  return cityNames.join('-')
})

// 订单列表
const orderLoading = ref<boolean>(false);
const orderFinished = ref<boolean>(false);
const orderList = ref<Array<IDataListItem>>([]);
const orderTotal = ref<number>(0);
const defaultParams = ref<ICreatTrip>({
  timeType: '',
  applyNo: '',
  ownerIsOwn: loginUser.value?.username, //联系人工号
  pageNum: 0,
  pageSize: 20,
  status: '', // 订单状态
  payType: null, // 支付方式
  beginDate: '',
  endDate: '',
  travelerKeyword: ''
});


const businessTravel = import.meta.env.VITE_BUSINESS_TRIP_URL;


// 编辑
const goToApply = (id: string) => {
  const url = businessTravel + '#' + '/mobile/apply?id=' + id;
  location.href = url
};

const goToConfirm = (applyNo: string | number) => {
  router.push(`/mobile/confirmTrip?applyNo=${applyNo}`)
}
// 商旅平台地址
const slUrl = import.meta.env.VITE_BUSINESS_TRIP_SINGLE
// 跳转商旅平台h5历史单据列表
const goHistoryList = () => {
  // const url = slUrl + 'fcapp/redirectOrder?djlx=9900110'
  const url = slUrl + 'fcopen/fcsso?skipType=9900103&djlx=99001&scene=sqlb'
  location.href = url
}

// 变更
const goToChange = (data:any) => {
  showConfirmDialog({
    title: '确认要变更吗?',
    message:
      '确认要变更此条数据吗?',
  })
    .then(() => {
      router.push('/mobile/update?applyNo=' + data.applyNo)
    })
    .catch(() => {
    });
 
}

// 作废
const cancelApply = (id: number) => {
  showConfirmDialog({
    title: '确认要作废吗?',
    message:
      '确认要作废此条数据吗?作废后无法恢复!',
  })
    .then(() => {
      tripApi.cancelApply({ id }).then((res) => {
        reSearch();
      });
    })
    .catch(() => {
    });
    
};

// 撤回
const recallApply = (id: number) => {
  showConfirmDialog({
    title: '确认要撤回吗?',
    message:
      '确认要撤回此条数据吗?撤回后无法恢复!',
  })
    .then(() => {
      tripApi.recallApply({ id }).then((res) => {
        reSearch();
      });
    })
    .catch(() => {
    });
    
};

// 审批
const businessList = import.meta.env.VITE_BUSINESS_URL;

const approvalIndex = ref<number>()
const goToApproval = (processCode: number | string, index:number) => {
  
  if (!processCode) {
    showFailToast('暂无审批记录!');
    return
  }
  approvalIndex.value = index
  window.location.href = `${businessList}hbweb/process/?code=${processCode}#/details`
  
  setTimeout(() => {
    approvalIndex.value = -1
  }, 2000);
  // router.push({ path: '/banquet/apply/approval', query: { processCode: processCode } });
};


const loadorderList = () => {
  defaultParams.value.pageNum++;
  tripApi.getApplyPage(defaultParams.value).then((res) => {
    // 加载状态结束
    orderLoading.value = false;
    orderList.value = [...orderList.value, ...res.records];
    orderTotal.value = res.total ?? 0;
    // 数据全部加载完成
    if (orderList.value.length >= orderTotal.value) {
      orderFinished.value = true;
    }
  });
};

onActivated(() => {
  route.value = getCurrentRoute()
  const type = route.value?.query?.reload
  if ( type == '1') {
    console.log('reload', type)
    reSet()
    reSearch();
  }
})

watch(
  () => defaultParams.value.timeType,
  (val: string | undefined) => {
    console.log('9999', val);
    switch (val) {
      case 'week':
        defaultParams.value.endDate = dayjs().format('YYYY-MM-DD');
        defaultParams.value.beginDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD');
        break;

      case 'month':
        defaultParams.value.endDate= dayjs().format('YYYY-MM-DD');
        defaultParams.value.beginDate= dayjs().subtract(1, 'month').format('YYYY-MM-DD');
        break;
      case 'year':
        defaultParams.value.endDate= dayjs().format('YYYY-MM-DD');
        defaultParams.value.beginDate = dayjs().subtract(1, 'year').format('YYYY-MM-DD');
        break;

      default:
        break;
    }
  },
);

watch(
  () => defaultParams.value.status,
  () => {
    reSearch();
  },
);

// 清空
const reSet = () => {
  defaultParams.value = {
    travelerKeyword: '',
    timeType: '',
    ownerIsOwn: loginUser.value?.username, //联系人工号
    pageNum: 0,
    pageSize: 20,
    applyNo:'',
    status: '', // 订单状态
    payType: null, // 支付方式
    beginDate: '',
    endDate: '',
  };
};

// 搜索
const menuRef = ref(null);
const reSearch = () => {
  orderLoading.value=true
  orderFinished.value = false
  defaultParams.value.pageNum = 0;
  orderList.value = []
  loadorderList();
  menuRef.value.close();
};

// 时间选择相关
const showTimePicker = ref<boolean>(false);

const minDate = ref(new Date(2022,0,1));
const maxDate = ref(new Date(2026, 0, 1));
const choseTimeType = ref('');
const currentDate = ref<Array<string>>([]);

const openTimePicker = (type: string) => {
  choseTimeType.value = type;
  currentDate.value = [];
  if (defaultParams.value.beginDate) {
    const minDateArr = defaultParams.value.beginDate.split('-');
    minDate.value = new Date(parseInt(minDateArr[0]), parseInt(minDateArr[1]) - 1, parseInt(minDateArr[2]));
  }
  if (defaultParams.value.endDate) {
    const maxDateArr = defaultParams.value.endDate.split('-');
    maxDate.value = new Date(parseInt(maxDateArr[0]), parseInt(maxDateArr[1]) - 1, parseInt(maxDateArr[2]));
  }

  if (type == 'begin') {
    minDate.value = new Date(2022,0,1);
    currentDate.value = defaultParams.value.beginDate?.split('-');
  } else {
    maxDate.value = new Date(2026, 0, 1);
    currentDate.value = defaultParams.value.endDate?.split('-');
  }

  showTimePicker.value = true;
};

const confirmTime = ({ selectedValues }: any) => {
  if (choseTimeType.value == 'begin') {
    defaultParams.value.beginDate = selectedValues.join('-');
  } else {
    defaultParams.value.endDate = selectedValues.join('-');
  }

  if (defaultParams.value.beginDate && defaultParams.value.endDate) {
    defaultParams.value.timeType = '';
  }

  showTimePicker.value = false;
};

const goToDetail = (id: string) => {
  router.push({ path: '/mobile/detail', query: { id: id } });
};


onMounted(() => {
  if(localStorage.getItem('reimburseData')) {
    activeApplyData.value = JSON.parse(localStorage.getItem('reimburseData'));
    reimburseUserCode.value = JSON.parse(localStorage.getItem('reimburseCode'));
    goToEES()
  }
});
</script>

<style lang='less' scoped>
@import url(./components/mobile.less);
:deep(.van-dropdown-menu__bar) {
  //  background: var(--van-dropdown-menu-background);
  box-shadow: none;
}
.list-search {
  :deep(.van-search__action) {
    // padding: 0;
  }
}
.list-search-btn {
  width: 110px;
}
:deep(.large-value) {
  min-width: 70%;
}
.my-radio {
  :deep(.van-radio__icon) {
    height: auto !important;
  }
}
.btn-com {
  width: 70px;
}
.input-border {
  border: 1px solid #dcdee0;
  border-radius: 20px;
  padding: 2px 10px;
}

.order-list {
  // margin-top: 36px;
  background-color: #f3f3f3;
}
.mt-10 {
  margin-top: 10px;
}
.weight600 {
  font-weight: 600;
}
</style>