import NProgress from 'nprogress';
import 'nprogress/nprogress.css'
import { createRouter, createWebHashHistory } from 'vue-router';

NProgress.configure({
    speed: 200,
    minimum: 0.02,
    trickleSpeed: 200,
    showSpinner: false
});
const routes = [{
    path: '/',
    redirect: '/index'
},
{
    path: '/index',
    component: () => import('../page/index.vue')
}
];

const router = createRouter({
    routes,
    history: createWebHashHistory(),
    scrollBehavior() {
        return { top: 0 };
    }
});

router.beforeEach((to, from, next) => {
    NProgress.start();
    next()
})

router.afterEach(() => {
    NProgress.done(true);
})
export default router;
