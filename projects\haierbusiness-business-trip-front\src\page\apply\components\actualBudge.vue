
<script lang="ts" setup>
import { tripApi, reasonApi,cityApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { ConfigProvider, message, Modal } from 'ant-design-vue';
import icon12306 from './icon12306.vue';
import { computed, onMounted, reactive, ref, watch, watchEffect, toRefs, createVNode } from 'vue';
import type { Ref, UnwrapRef } from 'vue';
import { CityResponse, CityItem } from '@haierbusiness-front/common-libs';

import {
  QuestionCircleOutlined,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  SearchOutlined,
  InfoCircleOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
  ExclamationCircleOutlined,
} from '@ant-design/icons-vue';
import {
  Anchor as hAnchor,
  <PERSON><PERSON> as hButton,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  Popconfirm as hPopconfirm,
  SelectOption as hSelectOption,
  InputNumber as hInputNumber,
  Form as hForm,
  FormItem as hFormItem,
  Tag as hTag,
  Tooltip as hTooltip,
  Cascader as hCascader,
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import {
  IUserListRequest,
  IUserInfo,
  ICity,
  ICreatTrip,
  IPerson,
  ITripInfo,
  ITripList,
  ITraveler,
  IProduct,
  ITripDetailMap,
  MemberBudgetParams,
} from '@haierbusiness-front/common-libs';
import { cloneDeep, difference } from 'lodash-es';
import type { CascaderProps } from 'ant-design-vue';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';


const cityName = ref<string>('');
// 根据id递归获取城市名
const getCityName = (code: string, options) => {
  options.forEach((item) => {
    if (item.districts && item.districts.length) {
      getCityName(code, item.districts);
    } else {
      if (code === item.adcode) {
        cityName.value = item.name;
      }
    }
  });
  return cityName.value;
};

const labelCol = { span: 4 };
const wrapperCol = { span: 20 };

// 起点
const beginTime = ref<string>('');

// 终点
const endTime = ref<string>('');

interface Props {
  creatTripParma?: any;
  isDetail?: boolean;
  isChange?: boolean;
  cityList?: Array<ICity>;
}
const props = defineProps<Props>();


const resonList = ref<Array<object>>([]);

onMounted(async () => {


});

const cityList = ref([]);



const formRef = ref();


// 表单验证
const onSubmit = () => {
  let temp = true;
  let tripList = props?.creatTripParma?.tripList;
  // 未添加行程
  if (!tripList || tripList?.length < 1) {
    temp = false;
    hMessage.warning('请先添加行程再进行后续操作!');
  } 

  return temp;
};

defineExpose({
  cityList,
  getCityName,
  onSubmit,
});
// const tripList = ref<Array<ITripList>>([]);


let cityItem: ITripList = {
  beginCityName: '',
  beginCityCodeSy: '',
  endCityCodeSy: '',
  endCityName: '',
  beginDate: '',
  endDate: '',
  tripDetailMapList: [],
};
const newCity = ref<string>();
const newTimeRange = ref<Array<string>>();
const newTime = ref<string>();



const addCity = () => {
  let leg: number = cityList.value.length;
  if (leg > 0) {
    if (!newEndCity.value || !endTime.value) {
      hMessage.warning('请先完善行程信息!');
      return;
    }
    if (newEndCity.value.citycode == newBeginCity.value.citycode) {
      Modal.confirm({
        title: '警告',
        icon: createVNode(ExclamationCircleOutlined),
        content: createVNode('div', { style: 'color:origin;' }, '出发城市与目的城市一致,您确定吗?'),
        onOk() {},
      });
    }
    if (newEndCity.value.citycode == cityList.value[leg - 1].cityCode) {
      Modal.confirm({
        title: '提醒',
        icon: createVNode(ExclamationCircleOutlined),
        content: createVNode('div', { style: 'color:origin;' }, '出发城市与目的城市一致!'),
        onOk() {},
      });
    }
    cityItem = {
      endCityCodeSy: newEndCity.value.syId,
      beginCityCodeSy: cityList.value[leg - 1].syId,
      beginCityCode: cityList.value[leg - 1].cityCode,
      endCityCode: newEndCity.value.citycode,
      beginCityName: cityList.value[leg - 1].city,
      endCityName: newEndCity.value.name,
      beginDate: cityList.value[leg - 1].date,
      endDate: endTime.value,
      tripDetailMapList: [],
      detailMap: true,
    };
    props?.creatTripParma?.tripList.push(cityItem);
    cityList.value.push({
      cityCode: newEndCity.value.citycode,
      city: newEndCity.value.name,
      syId: newEndCity.value.syId,
      date: endTime.value,
      active: false,
    });
    newEndCity.value = {
      name: '',
      citycode: '',
    };
    endTime.value = '';
    showNextAddCity.value = false;
  } else {
    // 一次添加两个
    if (!newBeginCity.value.citycode || !beginTime.value) {
      return;
    }
    if (!newEndCity.value.citycode || !endTime.value) {
      return;
    }

    if (newEndCity.value.citycode == newBeginCity.value.citycode) {
      Modal.confirm({
        title: '提醒',
        icon: createVNode(ExclamationCircleOutlined),
        content: createVNode('div', { style: 'color:origin;' }, '出发城市与目的城市一致!'),
        onOk() {},
      });
    }

    cityList.value.push({
      cityCode: newBeginCity.value.citycode,
      city: newBeginCity.value.name,
      date: beginTime.value,
      syId: newBeginCity.value.syId,
      active: false,
    });
    cityItem = {
      endCityCodeSy: newEndCity.value.syId,
      beginCityCodeSy: newBeginCity.value.syId,
      beginCityCode: newBeginCity.value.citycode,
      endCityCode: newEndCity.value.citycode,
      beginCityName: newBeginCity.value.name,
      endCityName: newEndCity.value.name,
      beginDate: beginTime.value,
      endDate: endTime.value,
      tripDetailMapList: [],
      detailMap: true,
    };
    if (!props.creatTripParma.tripList) {
      props.creatTripParma.tripList = [];
      props.creatTripParma.tripList.push(cityItem);
    } else {
      props.creatTripParma.tripList.push(cityItem);
    }
    cityList.value.push({
      cityCode: newEndCity.value.citycode,
      city: newEndCity.value.name,
      date: endTime.value,
      syId: newEndCity.value.syId,
      active: false,
    });

    // startCityCodeStrs.value = newBeginCity.value;
    newBeginCity.value = {
      name: '',
      citycode: '',
      syId: '',
    };
    beginTime.value = '';
    newEndCity.value = {
      name: '',
      citycode: '',
      syId: '',
    };
    endTime.value = '';
  }
  isChosedTimeRange.value = false;
  isChosedCity.value = false;
  // 总行程的出发地、目的地、出发时间、到达时间、syid
  props.creatTripParma.beginDate = cityList.value[0].date;
  props.creatTripParma.beginCityCodeSy = cityList.value[0].syId;
  props.creatTripParma.beginCityCode = cityList.value[0].cityCode;
  props.creatTripParma.beginCityName = cityList.value[0].city;

  props.creatTripParma.endCityCode = cityList.value[cityList.value.length - 1].cityCode;
  props.creatTripParma.endDate = cityList.value[cityList.value.length - 1].date;
  props.creatTripParma.endCityCodeSy = cityList.value[cityList.value.length - 1].syId;
  props.creatTripParma.endCityName = cityList.value[cityList.value.length - 1].city;
};

const isChosedTimeRange = ref<boolean>(false);
const isChosedCity = ref<boolean>(false);

// 时间选择完毕后自动保存行程
const rangeChange = (date: Dayjs | string, dateString: string) => {
  if (dateString) {
    isChosedTimeRange.value = true;
  }
  if (isChosedCity.value && isChosedTimeRange.value) {
    addCity();
  }
};

const newEndCity = ref<CityItem>({
  name: '',
  citycode: '',
  syId: '',
});
const newBeginCity = ref<CityItem>({
  name: '',
  citycode: '',
  syId: '',
});
const chosedEditCity = (city: CityItem, index: number, i: number) => {
  if (!city.citycode) {
    return;
  }
  cityListArr.value[i][index].city = city.name;
  cityListArr.value[i][index].cityCode = city.citycode;
  cityListArr.value[i][index].syId = city.syId;
  saveDetailCity(cityListArr.value[i][index], index, i);
};
const chosedCity = (city: CityItem) => {
  newEndCity.value = city;
  addEndCity();
};
const chosedBeginCity = (city: CityItem) => {
  newBeginCity.value = city;
  addBeginCity();
};
const addBeginCity = () => {
  if (newBeginCity.value) {
    isChosedCity.value = true;
  }
  if (isChosedCity.value && isChosedTimeRange.value) {
    addCity();
  }
};
const addEndCity = () => {
  if (newEndCity.value) {
    isChosedCity.value = true;
  }
  if (isChosedCity.value && isChosedTimeRange.value) {
    addCity();
  }
};

// 获取最后行程时间作为禁选时间
const disabledDate = (current: Dayjs) => {
  return current && current < dayjs(dayjs(props?.creatTripParma.endDate).format()) || current && current >= dayjs();
};

// 开始时间作为禁选时间
const disabledDate3 = (current: Dayjs) => {
  return current && current < dayjs(dayjs(beginTime.value).format()) || current && current > dayjs();
};

// 今天时间作为禁选时间
const disabledDate55 = (current: Dayjs) => {
  return current && current > dayjs();
};

// 开始时间作为禁选时间
const disabledDate4 = (current: Dayjs) => {
  if (disabledDate11.value && disabledDate111.value) {
    return (
      (current && current < dayjs(dayjs(disabledDate11.value).format())) ||
      (current && current > dayjs(dayjs(disabledDate111.value).format())) || current && current > dayjs()
    );
  } else if (disabledDate11.value) {
    return current && current < dayjs(dayjs(disabledDate11.value).format()) || current && current > dayjs();
  } else if (disabledDate111.value) {
    return current && current > dayjs(dayjs(disabledDate111.value).format()) || current && current > dayjs();
  }
  return current;
};


const saveEditCity = (city: ICity) => {
  if (!city.cityCode || !city.date) {
    return;
  }

  city.active = false;
};

// 转换时间格式  2.1
const formateTime = (time: string) => {
  return `${new Date(time).getFullYear()}/${new Date(time).getMonth() + 1}/${new Date(time).getDate()}`;
};




const allowBugde = ref<Array<ITripDetailMap>>([]);




const showFirstAddCity = ref<boolean>(false);
const showNextAddCity = ref<boolean>(false);

const showAddCity = () => {
  if (cityList.value?.length > 0) {
    if (showNextAddCity.value) {
      message.warning('请先完善当前城市信息!');
      return;
    }
    showNextAddCity.value = true;
  } else {
    if (showFirstAddCity.value) {
      message.warning('请先完善当前城市信息!');
      return;
    }
    showFirstAddCity.value = true;
  }
};

let i = 0;
const tripItem = ref<ITripList>({});


// 删除行程
const delTrip = (index: number) => {

  // 如果恰好只有一个行程 全部删除
  if(cityList.value?.length == 2) {
    cityList.value = [];
    props.creatTripParma.cityList= []

    props.creatTripParma.tripList = [];
    props.creatTripParma['beginDate'] =''
    props.creatTripParma['endDate'] =''

    props.creatTripParma['beginCityCode'] =''
    props.creatTripParma['beginCityCodeSy'] =''
    props.creatTripParma['beginCityName'] =''
    props.creatTripParma['endCityCode'] =''
    props.creatTripParma['endCityCodeSy'] =''
    props.creatTripParma['endCityName'] =''
    return
  }

  // 删除中间城市 清空后边城市的行程
  if (index + 1 !== props.creatTripParma.tripList?.length) {
    props.creatTripParma.tripList[index + 1].tripDetailMapList = [];
  }

  cityList.value.splice(index + 1, 1);
  props?.creatTripParma?.tripList.splice(index, 1);

  // 删除中间城市导致 起始地-终点错误
  cityList.value.forEach((i: ICity, ii: number) => {
    
    if (ii > 0) {
      props.creatTripParma.tripList[ii - 1].beginCityName = cityList.value[ii - 1].city;
      props.creatTripParma.tripList[ii - 1].endCityName = cityList.value[ii].city;
      props.creatTripParma.tripList[ii - 1].beginCityCode = cityList.value[ii - 1].cityCode;
      props.creatTripParma.tripList[ii - 1].endCityCode = cityList.value[ii].cityCode;
      props.creatTripParma.tripList[ii - 1].beginCityCodeSy = cityList.value[ii - 1].syId;
      props.creatTripParma.tripList[ii - 1].endCityCodeSy = cityList.value[ii].syId;
      if (ii > 1) {
        props.creatTripParma.tripList[ii - 1].beginDate = cityList.value[ii - 1].date;
      } else {
        props.creatTripParma.tripList[ii - 1].beginDate = cityList.value[ii - 1].date;
      }
    }
  });

  if (cityList.value.length > 1) {
    props.creatTripParma['beginDate'] = cityList.value[0].date;
    props.creatTripParma['endDate'] = cityList.value[cityList.value.length - 1]?.date;
  } else {
    props.creatTripParma['beginDate'] = cityList.value[0].date;
    props.creatTripParma['endDate'] = cityList.value[cityList.value.length - 1].date;
  }

};

const disabledDate1 = ref<string>('');
const disabledDate11 = ref<string>('');
const disabledDate111 = ref<string>('');

// 编辑时控制不可选择时间
const editCity = (city: ICity, index: number, i: number) => {
  cityListArr.value.forEach((list) => {
    list.forEach((ll) => {
      ll.active = false;
    });
  });
  if (props.isDetail) {
    return;
  }
  // 记录修改第一个不可选择时间
  if (index == 0 && i == 0) {
    if (cityList.value.length > 1) {
      disabledDate11.value = '';
      disabledDate111.value = cityList.value[1].date;
    }
  } else if (i * 4 + index + 1 == cityList.value.length) {
    // 记录最后一个不可选择时间
    disabledDate11.value = cityList.value[cityList.value.length - 2].date;
    disabledDate111.value = '';
  } else {
    disabledDate11.value = cityList.value[index - 1].date;
    disabledDate111.value = cityList.value[index + 1].date;
  }

  city.active = true;
};

// 修改出发地目的地
const saveDetailCity = (city: ICity, index: number, i: number) => {
  if (!city.date || !city.cityCode) {
    return;
  }
  // 数组总长度
  let legAll = i * 4 + index + 1;

  if (index == 0 && i == 0 && cityList.value.length > 1) {
    props.creatTripParma['beginDate'] = city.date;
    props.creatTripParma['beginCityName'] = city.city;
    props.creatTripParma['beginCityCode'] = city.cityCode;
    props.creatTripParma['beginCityCodeSy'] = city.syId;
    props.creatTripParma.tripList[0].beginCityName = city.city;
    props.creatTripParma.tripList[0].beginCityCode = city.cityCode;
    props.creatTripParma.tripList[0].beginCityCodeSy = city.syId;
    props.creatTripParma.tripList[0]['beginDate'] = city.date;
  } else if (legAll == cityList.value.length) {
    props.creatTripParma['endDate'] = city.date;
    props.creatTripParma['endCityName'] = city.city;
    props.creatTripParma['endCityCode'] = city.cityCode;
    props.creatTripParma['endCityCodeSy'] = city.syId;

    props.creatTripParma.tripList[legAll - 2]['endCityName'] = city.city;
    props.creatTripParma.tripList[legAll - 2]['endCityCodeSy'] = city.syId;
    props.creatTripParma.tripList[legAll - 2]['endDate'] = city.date;
    props.creatTripParma.tripList[legAll - 2]['endCityCode'] = city.cityCode;
  } else {
    props.creatTripParma.tripList[legAll - 1]['beginCityName'] = city.city;
    props.creatTripParma.tripList[legAll - 1]['beginDate'] = city.date;
    props.creatTripParma.tripList[legAll - 1]['beginCityCode'] = city.cityCode;
    props.creatTripParma.tripList[legAll - 1]['beginCityCodeSy'] = city.syId;

    props.creatTripParma.tripList[legAll - 2]['endCityName'] = city.city;
    props.creatTripParma.tripList[legAll - 2]['endDate'] = city.date;
    props.creatTripParma.tripList[legAll - 2]['endCityCode'] = city.cityCode;
    props.creatTripParma.tripList[legAll - 2]['endCityCodeSy'] = city.syId;
  }

  city.active = false;
};



interface budge {
  workNo: string;
  budgetCode: string;
  performCode: string;
}
const budgeParams = ref<budge>({
  workNo: '',
  budgetCode: '',
  performCode: '',
});


// [[{}],[{}]]

const cityListArr = ref<Array<Array<ICity>>>([]);

const value = ref<string[]>([]);


watch(
  props,
  (newVal, oldVal) => {
    cityList.value = newVal.cityList;
  },
  {
    immediate: true,
    deep: true,

  },
)
watch(
  cityList,
  (newVal, oldVal) => {
    cityListArr.value = [];
    let groups = Math.ceil(newVal.length / 4);

    let cityListTemp = [];

    newVal.forEach((item, index) => {
      // 一组

      if (groups == 1) {
        cityListTemp = [...cityListTemp, item];
      } else {
        cityListTemp = [...cityListTemp, item];
        if ((index + 1) % 4 == 0) {
          cityListArr.value = [...cityListArr.value, cityListTemp];
          cityListTemp = [];
        }
      }

      if (index + 1 == newVal.length) {
        if (groups == 1 && index == 3) {
          cityListArr.value = [...cityListArr.value, cityListTemp, []];
        } else {
          cityListArr.value = [...cityListArr.value, cityListTemp];
        }
      }
    });

    // 详情页摘掉最后一个空对象
    if (props.isDetail && cityListArr.value.length > 1) {
      if (
        cityListArr.value[cityListArr.value.length - 1] &&
        cityListArr.value[cityListArr.value.length - 1].length == 0
      )
        cityListArr.value.pop();
    }
  },
  {
    deep: true,
    immediate: true,
  },
  
);



// 表格结束
</script>
<template>
  <div class="whole-line" style="margin-top: 20px">
    <div class="title whole-line">实际行程确认</div>

    <h-form
      labelAlign="left"
      ref="formRef"
      class="mt-30"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      :disabled="props.isDetail"
    >

      <h-form-item ref="plan" plan="name" required>
        <template #label>
          <span
            >实际行程<h-tooltip>
              <template #title>按照出差任务选择始发、目的地以及中转地，务必包含全部途径地及停留时间。事前做好差旅行为计划，提前规划行程（国内机票建议提前5天预订出票），在满足出行需求的前提下，确保成本最优；</template>
              <question-circleOutlined class="icon" /> </h-tooltip
          ></span>
        </template>

        <div class="travel">
          <div class="flex city-list" :class="i % 2 == 0 ? '' : 'reverse'" v-for="(list, i) in cityListArr" :key="i">
            <div
              class="flex block list-item"
              @blur="city.active = false"
              v-for="(city, index) in list"
              :key="index"
            >
              <div class="pict flex">
                <environment-filled class="primary-color font-size-24" />
                <div
                  v-if="!(props.isDetail && i + 1 == cityListArr.length && index + 1 == list.length && i % 2 == 0)"
                  class="dashed-line"
                ></div>
              </div>
              <!-- 鼠标双击变为可编辑 -->
              <template v-if="city.active">
                <div class="city mt-10">
                  <city-chose
                    :showInternational="false"
                    :value="city.city"
                    :bordered="false"
                    :index="index"
                    :i="i"
                    @chosedCity="chosedEditCity"
                  ></city-chose>
                </div>
                <div class="date mt-10 flex-center">
                  <div v-if="!(i == 0 && index == 0)">{{ formateTime(disabledDate11) }} -</div>
                  <h-date-picker
                    @blur="saveEditCity(city)"
                    :disabled-date="disabledDate4"
                    @change="saveDetailCity(city, index, i)"
                    size="small"
                    valueFormat="YYYY-MM-DD"
                    :bordered="false"
                    v-model:value="city.date"
                  />
                </div>
              </template>

              <template v-else>
                <div class="city mt-10">
                  {{ city.city }}
                  <EditOutlined @click="editCity(city, index, i)" style="color: rgba(0, 0, 0, 0.45)" />
                </div>
                <div class="date mt-10" v-if="i == 0 && index == 0">
                  {{ formateTime(city.date) }}
                  <EditOutlined @click="editCity(city, index, i)" style="color: rgba(0, 0, 0, 0.45)" />

                </div>
                <div class="date mt-10" style="min-width: 120px" v-else>
                  {{
                    index == 0 ? formateTime(cityListArr[i - 1][3].date) : formateTime(cityListArr[i][index - 1].date)
                  }}
                  - {{ formateTime(city.date) }}
                  <EditOutlined  @click="editCity(city, index, i)" style="color: rgba(0, 0, 0, 0.45)" />

                </div>
              </template>
            </div>
            <div
              v-if="i + 1 != cityListArr.length"
              :class="i % 2 == 0 ? 'inflection-point-right' : 'inflection-point-left'"
            ></div>
            <!-- 新增途径点城市 后面的 -->
            <template v-if="!props.isDetail && i + 1 == cityListArr.length && showNextAddCity">
              <div class="flex block list-item">
                <div class="pict flex">
                  <!-- <environment-filled class="eee-color font-size-24" /> -->
                  <environment-two-tone class="font-size-24" />
                  <div class="dashed-line"></div>
                </div>
                <div class="city mt-10">
                  <city-chose
                    :showInternational="false"
                    :value="newEndCity.name"
                    :bordered="false"
                    @chosedCity="(city:CityItem) => {chosedCity(city)}"
                  ></city-chose>
                </div>
                <div class="date mt-10 flex-center">
                  <div>{{ formateTime(props?.creatTripParma.endDate) }} -</div>

                  <h-date-picker
                    style="padding-left: 0;padding-right: 0;"
                    @change="rangeChange"
                    :disabled-date="disabledDate"
                    size="small"
                    valueFormat="YYYY-MM-DD"
                    :bordered="false"
                    v-model:value="endTime"
                  />
                </div>
              </div>
            </template>
            <div v-if="!(i % 2 == 0) && !props.isDetail && i + 1 == cityListArr.length" class="dashed-line2"></div>

            <h-button
              type="dashed"
              size="small"
              style="padding: 0 8px !important"

              class="flex-center border-radios button-padding add-btn2 primary-color"
              @click="showAddCity"
              v-if="!props.isDetail && i + 1 == cityListArr.length"
            >
              <template #icon>
                <plus-outlined class="font-size-12" />
              </template>
              <span class="font-size-12">添加行程</span>
            </h-button>
          </div>

          <!-- 第一次添加新增两个城市 -->
          <template v-if="!cityList?.length  && showFirstAddCity">
            <!-- 新增途径点城市 第一个 -->
            <div class="flex block">
              <div class="pict flex">
                <environment-two-tone class="font-size-24" />
                <div class="dashed-line"></div>
              </div>
              <div class="city mt-10">
                <city-chose
                  :showInternational="false"
                  :value="newBeginCity?.name"
                  :bordered="false"
                  @chosedCity="(city:CityItem) => {chosedBeginCity(city)}"
                ></city-chose>
              </div>
              <div class="date mt-10">
                <h-date-picker size="small" :disabled-date="disabledDate55" valueFormat="YYYY-MM-DD" :bordered="false" v-model:value="beginTime" />
              </div>
            </div>
            <!-- 新增途径点城市 后面的 -->
            <div class="flex block">
              <div class="pict flex">
                <!-- <environment-filled class="eee-color font-size-24" /> -->
                <environment-two-tone class="font-size-24" />
                <div class="dashed-line"></div>
              </div>
              <div class="city mt-10">
                <city-chose
                :showInternational="false"
                  :value="newEndCity.name"
                  :bordered="false"
                  @chosedCity="(city:CityItem) => {chosedCity(city)}"
                ></city-chose>
              </div>
              <div class="date mt-10">
                <h-date-picker
                  @change="rangeChange"
                  :disabled-date="disabledDate3"
                  size="small"
                  valueFormat="YYYY-MM-DD"
                  :bordered="false"
                  v-model:value="endTime"
                />
              </div>
            </div>
          </template>
          <h-button
            type="dashed"
            size="small"
            class="flex-center border-radios ml-10 button-padding add-btn2 primary-color"
            @click="showAddCity"
            v-if="!cityList?.length"
          >
            <template #icon>
              <plus-outlined class="font-size-12" />
            </template>
            <span class="font-size-12">添加行程</span>
          </h-button>
        </div>
        <div class="travel-list">
          <!-- 行程费用 -->
          <div class="travel-item" v-for="(trip, index) in props.creatTripParma.tripList" :key="index">
            <div class="travel-item-head mt-20 flex">
              <div class="head-left flex">
                <div class="head-left-num mr-10">第 {{ index + 1 }} 行程</div>
                <div class="flex font-size-14">
                  {{ trip.beginCityName }} - {{ trip.endCityName }}
                  <div class="shu ml-10 mr-10"></div>
                  {{ formateTime(trip.beginDate) }} - {{ formateTime(trip.endDate) }}
                </div>
              </div>
              <div class="head-right flex">
              
                <h-popconfirm
                  title="删除行程无法恢复,确定删除吗?"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="delTrip(index)"
                >
                  <div class="add-btn primary-color pl-8 pr-8 pointer">
                    <span class="font-size-14">删除行程</span>
                  </div>
                </h-popconfirm>
              </div>
            </div>
           
          </div>
        </div>
      </h-form-item>

      <h-form-item ref="plan" plan="name" required>
        <template #label>
          <span
            >享受出差补助<h-tooltip>
              <template #title>是否享受出差补助</template>
              <question-circleOutlined class="icon" /> </h-tooltip
          ></span>
        </template>

        <h-radio-group v-model:value="props.creatTripParma.subsidy" :disabled="props.creatTripParma.travelReserveFlag == 0"  >
          <h-radio :value="true">是</h-radio>
          <h-radio :value="false">否</h-radio>
        </h-radio-group>

      </h-form-item>

    </h-form>
  </div>
</template>

<style lang="less" scoped>
@import url('./trip.less');

.my-tag {
  margin: 0px;
  position: absolute;
  right: 0;
  padding: 0 4px;
  font-size: 10px;
}
:deep(.ant-table-cell) {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.font-size-16 {
  font-size: 16px;
}
:deep(.ant-select-selection-item) {
  background: rgba(0, 0, 0, 0) !important;
  border: none !important;
}
.travel-item {
  :deep(.ant-select-selection-item) {
    background: rgba(0, 0, 0, 0) !important;
    border: none !important;
    font-size: 14px;
    padding: 0 !important;
  }
  :deep(.ant-input-number-input, .ant-input-borderless, .ant-checkbox-wrapper) {
    font-size: 14px;
  }
  :deep(.ant-checkbox-inner) {
    transform: scale(0.8);
  }
}

.travel {
  flex-wrap: wrap;
}
.reverse {
  flex-direction: row-reverse;
}
.list-item {
  width: 236px;
  min-height: 100px;
}
.list-item > .city {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.list-item > .date {
}
.list-item-max {
  flex: 1;
  min-width: 260px;
  max-width: 260px;
  position: relative;
}
.city-list {
  width: 100%;
  position: relative;
  margin-bottom: 16px;
}
.text-hidden {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
  padding: 0 10px;
}
.inflection-point-right {
  border: 2px dashed #0073e5;
  width: 24px;
  min-height: 118px;
  border-left: none;
  position: absolute;
  top: 12px;
  right: -24px;
  border-radius: 0 8px 8px 0;
}
.inflection-point-left {
  border: 2px dashed #0073e5;
  min-height: 118px;
  border-right: none;
  position: absolute;
  top: 12px;
  width: 60px;
  left: -60px;
  border-radius: 8px 0 0 8px;
}
.dashed-line2 {
  margin-top: 12px;
  margin-left: 3px;
  border-top: 2px dashed #0073e5;
  height: 50%;
  max-width: 40px;
  min-width: 10px;
}
.chose-position {
  // position: absolute;
  // left: -8px;
  // top: 14px;
}
:deep(.ant-select-selection-item) {
}
:deep(.city-chose-input) {
  padding: 0 0 0 8px !important;
}
.budget-tooltip {
  :deep(.ant-tooltip-content) {
    width: 400px !important;
  }
  :deep(.ant-tooltip-inner, .ant-spin-container) {
    width: 400px !important;
  }
  :deep(.ant-popover-title) {
    width: 400px !important;
  }
}
</style>