<script setup lang="ts">
import {
  Modal,
  Tag as hTag,
  Form as hForm,
  Input as hInput,
  Popover as hPopover,
  Popconfirm as hPopconfirm,
  FormItem as hFormItem,
  Select as hSelect,
  SelectOption as hSelectOption,
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Spin as hSpin,
  Table as hTable,
  Textarea as hTextarea,
  DatePicker as hDatePicker,
  CollapsePanel as hCollapsePanel,
  Collapse as hCollapse,
  FormInstance,
  message,
  TableColumnsType,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { PlusOutlined, SearchOutlined, ExpandAltOutlined, MinusCircleOutlined } from '@ant-design/icons-vue';
import {
  IAnnualPlanSaveOrUpdateRequestDTO,
  AnnualPlanTypeStateConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
  IHaierAccountBillInfo,
  UserGroupSystemConstant,
} from '@haierbusiness-front/common-libs';
import { checkUserGroup, getCurrentRouter, resolveParam } from '@haierbusiness-front/utils';
import { ref, createVNode, PropType, computed } from 'vue';

import dayjs, { Dayjs } from 'dayjs';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import {
  DailyReportStateConstant,
  EvaluateControlConstant,
  IAnnualPlanDetailRequestDTO,
  IAnnualPlanDetailResponseDTO,
  IAnnualPlanTypeListRequest,
  IDailyPersonalResponse,
  IDailyReportListResponseDTO,
  IDailyReportProjectRequestDTO,
  IMonthPlanDetailResponseDTO,
  PlanTypeConstant,
} from '@haierbusiness-front/common-libs/src/daily';
import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
const { loginUser } = storeToRefs(applicationStore(globalPinia));

const router = getCurrentRouter();
const prop = defineProps({
  data: Object as PropType<IDailyReportProjectRequestDTO[]>,
  dailyReport: Object as PropType<IDailyReportListResponseDTO>,
  monthData: Object as PropType<IMonthPlanDetailResponseDTO>,
  fold: Boolean as PropType<Boolean>,
  dailyPerson: Object as PropType<IDailyPersonalResponse[]>,
});

const columns: ColumnType[] = [
  {
    title: '月度项目',
    dataIndex: 'monthPlanItem',
    width: '120px',
    align: 'center',
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '月度目标',
    dataIndex: 'monthPlanTarget',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '责任人',
    dataIndex: 'monthPlanPrincipalUsercode',
    width: '70px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '录入人',
    dataIndex: 'principalUsername',
    width: '70px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '日推进完成效果',
    dataIndex: 'content',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '40px',
    fixed: 'right',
    align: 'center',
  },
];

const collapseActiveKey = ref([1]);
{
  if (prop?.fold) {
    collapseActiveKey.value = [];
  }
}

const allowChange = (record: IDailyReportProjectRequestDTO) => {
  return (
    checkUserGroup(UserGroupSystemConstant.DAILY_MICRO.groupId) ||
    (record.principalUsercode === loginUser.value?.username) 
  ) && !isReadonly();
};

const isReadonly = () => {
  return (
    DailyReportStateConstant.RUNNING.code === prop?.dailyReport?.state ||
    DailyReportStateConstant.FINISH.code === prop?.dailyReport?.state
  );
};

const removeDomain = (item: any) => {
  let index = prop?.data?.indexOf(item);
  if (index != undefined && index !== -1) {
    prop?.data?.splice(index, 1);
  }
};

const addDomain = () => {
  prop?.data?.push({
    apiCode: prop.monthData?.apCode,
    principalUsercode: loginUser.value?.username,
  });
};

const monthPlanPrincipalUsercode = (record: IDailyReportProjectRequestDTO) => {
  const mpiCode = prop.data!![getIndex(record)].mpiCode;
  const mp = prop?.monthData?.monthPlanItems?.filter((it) => {
    return it.code === mpiCode;
  })[0];
  if (mp) {
    return mp.principalUsername;
  }
};
const getIndex = (record: any) => {
  return prop.data?.indexOf(record) || 0;
};
const changeMonthItem = (record: IDailyReportProjectRequestDTO) => {
  record.apiCode = prop.data!![getIndex(record)].mpiCode;
};

const monthPlanTarget = computed(() => {
  return (record: IDailyReportProjectRequestDTO) => {
    const mpiCode = prop.data!![getIndex(record)].mpiCode;
    const mp = prop?.monthData?.monthPlanItems?.filter((it) => {
      return it.code === mpiCode;
    })[0];
    if (mp?.planType === PlanTypeConstant.QUANTIFY.code) {
      return mp.planValue + '/' + mp.planUnit;
    } else {
      return mp?.planDesc;
    }
  };
});
const monthPlanOptions = computed(() => {
  const result = prop.monthData?.monthPlanItems?.filter((it) => {
    return it.month === prop?.dailyReport?.month;
  });
  return result;
});

const filterOption = (input: string, option: any) => {
  return option.planName.indexOf(input) >= 0;
};

</script>

<template>
  <h-collapse
    v-model:activeKey="collapseActiveKey"
    :bordered="false"
    style="background-color: white"
    :collapsible="'icon'"
  >
    <h-collapse-panel key="1">
      <template #header>
        <div style="display: flex">
          <!--  <div style="height: 25px; width: 10px; background-color: rgb(0, 115, 229)" /> -->
          <div style="font-size: 16px; font-weight: 600; margin-left: 10px">月度预算项目日清</div>
        </div>
      </template>
      <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%"></div>
      <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px">
        <h-row :gutter="24" style="margin-top: 15px">
          <h-col :span="24">
            <h-table
              style="margin-left: 5px"
              :size="'small'"
              :bordered="true"
              :columns="columns"
              :data-source="prop?.data"
              :pagination="false"
            >
              <template #emptyText> 请点击下方按钮新增 </template>
              <template #bodyCell="{ record, column, text }">
                <template v-if="['monthPlanItem'].includes(column.dataIndex as any)">
                  <h-select
                    show-search
                    :filter-option="filterOption"
                    style="width: 100%"
                    v-model:value="data!![getIndex(record)].mpiCode"
                    :options="monthPlanOptions"
                    :field-names="{ label: 'planName', value: 'code' }"
                    @change="changeMonthItem(data!![getIndex(record)])"
                    placeholder="选择项目"
                  >
                  </h-select>
                </template>
                <template v-if="['monthPlanTarget'].includes(column.dataIndex as any)">
                  <div>
                    <h-input :value="monthPlanTarget(record)" style="margin: -5px 0" :disabled="true">
                      <template #addonAfter>
                        <h-popover trigger="click">
                          <template #content>
                            <h-textarea
                              :disabled="true"
                              :value="monthPlanTarget(record)"
                              style="width: 500px"
                              :rows="4"
                            />
                          </template>
                          <ExpandAltOutlined />
                        </h-popover>
                      </template>
                    </h-input>
                  </div>
                </template>
                <template v-if="['monthPlanPrincipalUsercode'].includes(column.dataIndex as any)">
                  {{ monthPlanPrincipalUsercode(record) }}
                </template>
                <template v-if="['principalUsername'].includes(column.dataIndex as any)">
                  {{ data!![getIndex(record)].principalUsername || loginUser?.nickName }}
                </template>
                <template v-if="['content'].includes(column.dataIndex as any)">
                  <div>
                    <h-input :disabled="!allowChange(record)" v-model:value="data!![getIndex(record)].content" style="margin: -5px 0" allowClear>
                      <template #addonAfter>
                        <h-popover trigger="click">
                          <template #content>
                            <h-textarea
                              v-model:value="data!![getIndex(record)].content"
                              style="width: 500px"
                              :rows="4"
                              allowClear
                            />
                          </template>
                          <ExpandAltOutlined />
                        </h-popover>
                      </template>
                    </h-input>
                  </div>
                </template>
                <template  v-if="['_operator'].includes(column.dataIndex as any)">
                  <h-popconfirm title="确认删除?" v-if="allowChange(record)"  @confirm="removeDomain(record)">
                    <h-button type="link" danger>
                      <template #icon>
                        <MinusCircleOutlined :style="{ fontSize: '20px' }" />
                      </template>
                    </h-button>
                  </h-popconfirm>
                </template>
              </template>
            </h-table>
          </h-col>
          <h-col :span="24" style="margin: 10px">
            <h-button v-if="!isReadonly()" type="dashed" style="width: calc(100% - 10px)" @click="addDomain">
              <PlusOutlined />
              添加
            </h-button>
          </h-col>
        </h-row>
      </div>
    </h-collapse-panel>
  </h-collapse>
</template>

<style scoped lang="less">
@import '../../assets/css/main.less';
</style>
