<script lang="ts" setup>
import { computed, ref, watch, onMounted } from "vue";
import {
  IAdvertisementAccount
} from '@haierbusiness-front/common-libs';
import { Modal as hModal } from 'ant-design-vue';
import type { Ref } from "vue";

interface Props {
    show: boolean;
    data: IAdvertisementAccount | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
})

const defaultData: IAdvertisementAccount = {
    title: '',
    content: '',
    imgUrl: ''
};

const advertisement: Ref<IAdvertisementAccount> = ref(
    ({ ...props.data } as IAdvertisementAccount) || defaultData
);


const visible = computed(() => props.show);

</script>

<template>
    <h-modal
      v-model:visible="visible"
      title="预览"
      :width="1000"
      @cancel="$emit('close')"
      @ok="$emit('close')"
    >
        <div class="banner-con">
            <div class="banner">
                <img :src="advertisement.imgUrl" class="img">
            </div>
        </div>
    </h-modal>
</template>

<style lang="less" scoped>
.banner-con {
    display: flex;
    width: 100%;
    justify-content: center;

    .banner {
        width: 833px;
        height: 400px;
        position: relative;
        border-radius: 8px;
        

        .img {
          width: 100%;
          height: 100%;
          border-radius: 8px;
        }

        .desc {
            position: absolute;
            width: 600px;
            // height: 120px;
            left: 20px;
            bottom: 20px;
            background: rgba(0, 0, 0, 0.35);
            padding: 21px 17px 26px 26px;
            color: #FFF;

            .desc-con {
                width: 100%;
                height: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;

                .banner-title {
                    font-size: 20px;
                    font-weight: 500;
                    line-height: 20px;
                }

                .banner-desc {
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 21px;
                    margin-top: 17px;
                }
            }
            

            
        }
    }
}

</style>