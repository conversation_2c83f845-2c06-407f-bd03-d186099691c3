<template>
    <div style="height: 33vh" background="rgba(0,0,0,0)">
        <div :id="circleId" style="height: 20vh"></div>
        <div class="tips">
            <div class="tips-main" style="width: 24vh">
                <div class="tip" v-for="(row, index) in payTypeDetailRows" :key="row.name">
                    <span class="tip-dot" :style="{ borderColor: colors[index] }"></span>
                    <span class="tip-percent">{{
                        ((row.value / payTypeDetailTotal) * 100).toFixed(0)
                    }}%</span>
                    <span class="tip-title">{{ row.name }}</span>
                </div>
            </div>
        </div>

        <!-- <h-row>
            <h-col :span="8">
                <div :id="pieId" style="height:20vh"></div>    
            </h-col>
            <h-col :span="16">
                <div :id="circleId" style="height:20vh"></div>    
            </h-col>
        </h-row>
        <h-row>
            <h-col :span="8" flex class="tips">
                <div>
                    <div class="tip" v-for="(row,index) in payTypeRows" :key="row.name">
                        <span class="tip-dot" :style="{borderColor:colors[index]}"></span>
                        <span class="tip-percent">{{ (row.value/payTypeTotal*100).toFixed(0) }}%</span>
                        <span class="tip-title">{{ row.name }}</span>
                    </div>
                </div>
            </h-col>
            <h-col :span="16" class="tips">
                <div class="tips-main" style="width:24vh">
                    <div class="tip" v-for="(row,index) in payTypeDetailRows" :key="row.name">
                        <span class="tip-dot" :style="{borderColor:colors[index]}"></span>
                        <span class="tip-percent">{{ (row.value/payTypeDetailTotal*100).toFixed(0) }}%</span>
                        <span class="tip-title">{{ row.name }}</span>
                    </div>
                </div>
            </h-col>
        </h-row> -->
    </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { pie, circle, colors } from "../../../data";
import * as echarts from "echarts";
import { queryTrainPayType, queryTrainPayPlatform } from "@haierbusiness-front/apis/src/data/board/travel";
import { EventBus } from "../../../eventBus";
import { useRouter, useRoute } from "vue-router";
const route = useRoute();
const props = defineProps({
    gngj: {
        type: [String, Number],
        default: "1",
    },
});
const loading = ref(false);
const pieId = ref("pie-" + Date.now());
const circleId = ref("circle-" + Date.now());
const payTypeRows = ref([]);
const payTypeTotal = ref(0);
const payTypeDetailRows: any = ref([]);
const payTypeDetailTotal = ref(0);
let pieChartDom, pieChart, circleChartDom, circleChart;

const payTypeCheck = ref<string>("");

onMounted(() => {
    // pieChartDom = document.getElementById(pieId.value);
    // pieChart = echarts.init(pieChartDom as any,"dark");
    circleChartDom = document.getElementById(circleId.value);
    circleChart = echarts.init(circleChartDom as any, "dark");
    // queryPie();
    queryCircle();
    //饼状图点击事件
    circleChart.on("click", (param) => {
        if (param.from != "budget_source" && param.name != payTypeCheck.value) {
            EventBus.emit("refresh", {
                ...param,
                from: "budget_source",
            });
        } else {
            payTypeCheck.value = "";
            EventBus.emit("refresh");
        }
    });
});
EventBus.on((event, params) => {
    if (event == "refresh") {
        if (!params) queryCircle();
        if (params && params.from != "budget_source") {
            queryCircle(params);
        }
        if (params && params.from == "budget_source") {
            payTypeCheck.value = params.data.name;
            queryCircle().then(() => {
                circleChart.dispatchAction({
                    type: "highlight",
                    seriesIndex: 0,
                    dataIndex: params.dataIndex,
                });
            });
        }
    }
});
//左侧饼图
const queryPie = async () => {
    const data = await queryTrainPayType();
    const payTypeData: any = [];
    let total = 0;
    data.rows?.forEach((item) => {
        total += item[1];
        payTypeData.push({
            name: item[0],
            value: item[1],
        });
    });
    payTypeTotal.value = total;
    payTypeRows.value = payTypeData;
    const { series } = pie;
    series[0].color = colors;
    series[0].data = payTypeData;
    pieChart.setOption(pie);
};
const queryCircle = async (params?: { data: { name: string }; from: string }) => {
    loading.value = true;
    const data = await queryTrainPayPlatform(
        params ? params.data.name : null,
        params ? params.from : null
    );
    loading.value = false;
    const payTypeDetailData: any = [];
    let total = 0;
    data.rows.forEach((item) => {
        total += item[1];
        payTypeDetailData.push({
            name: item[0],
            value: item[1],
        });
    });
    payTypeDetailTotal.value = total;
    payTypeDetailRows.value = payTypeDetailData;
    const { series } = circle;
    series[0].color = colors;
    series[0].data = payTypeDetailData;
    series[0].radius = ["50%", "70%"];
    circleChart.clear();
    circleChart.setOption(circle);
    return payTypeDetailData;
};
</script>
<style scoped lang="less">
.tips {
    display: flex;
    justify-content: center;
}

.tips-main {
    display: flex;
    flex-wrap: wrap;
    width: 20vh;
}

.tip {
    width: 12vh;

    &-dot {
        display: inline-block;
        width: 1vh;
        height: 1vh;
        border: 3px solid #ffd700;
        border-radius: 50%;
    }

    &-percent {
        font-size: 1.5vh;
        margin: 0 5px 0 7px;
    }

    &-title {
        font-size: 1vh;
    }
}
</style>
