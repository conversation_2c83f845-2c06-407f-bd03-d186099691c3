<template>
    <Rank style="height: 25vh" background="rgba(0,0,0,0)" :data="rankData" unit="万元" />
</template>
<script setup lang="ts">
import Rank from "../../../components/rank.vue";
import { queryHotelSettleRank } from "@haierbusiness-front/apis/src/data/board/travel";
import { ref, onMounted } from "vue";
import { EventBus } from "../../../eventBus";
const rankData = ref([] as Array<{ name: string; value: number | string }>);
const loading = ref(false);
EventBus.on((event, params) => {
    if (event == "refresh") queryData(params ? params : null);
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
    loading.value = true;
    const data = await queryHotelSettleRank(
        params ? params.data.name : null,
        params ? params.from : null
    );
    loading.value = false;
    const rows = [] as Array<{ name: string; value: number | string }>;
    data.rows.forEach((item) => {
        rows.push({
            name: item[0],
            value: (item[1] / 10000).toFixed(0),
        });
    });
    // rows.sort((a,b)=>b.value-a.value);
    rankData.value = rows;
};

onMounted(() => {
    queryData()
})
</script>
