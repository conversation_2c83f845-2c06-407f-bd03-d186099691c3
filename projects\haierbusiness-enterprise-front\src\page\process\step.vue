<script lang="ts" setup>
import { Select as hSelect, SelectOption as hSelectOption, Row as hRow, Col as hCol, Form as hForm, FormItem as hFormItem,
     Input as hInput, Button as hButton, Space as hSpace, Card as hCard } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { computed, ref, watch } from "vue";
import {
    IStepsInfo, ProcessRoleConstant
} from '@haierbusiness-front/common-libs';

const stepInfo = ref<{ steps: Array<IStepsInfo> }>({ steps: []})

const addStep = () => {
    stepInfo.value.steps.push({})
}

const roles = computed(() => ProcessRoleConstant.toArray())

</script>

<template>
    <h-form
        ref="formRef"
        :model="stepInfo"
        style="margin-top: 20px;"
    >
        <h-row style="margin-top: 20px;" v-for="(step, index) in stepInfo.steps" :key="index">
            <h-col :span="24" >
                <h-card size="middle" :title="'步骤' + (index + 1)" style="width: 100%" hoverable>
                    <h-form-item
                        label="步骤名称"
                        :name="['steps', index, 'name']"
                        :rules="{
                            required: true,
                            message: '请输入步骤名称',
                        }"
                    >
                        <h-input v-model:value="step.name" placeholder="步骤名称" />
                    </h-form-item>
                    <h-form-item
                        :name="['steps', index, 'type']"
                        :rules="{
                            required: true,
                            message: '请输入步骤名称',
                        }"
                    >
                        <template #label>
                            <span>流转条件</span>
                        </template>
                        <h-select v-model:value="step.type" allow-clear>
                            <h-select-option :value="1">全部通过则通过,一人驳回则驳回</h-select-option>
                            <h-select-option :value="2">一人通过则通过, 一人驳回则驳回</h-select-option>
                        </h-select>
                    </h-form-item>
                    <h-form-item
                        label="角色"
                        :name="['steps', index, 'roles']"
                    >
                        <h-select
                            v-model:value="step.roles"
                            placeholder="请选择角色"
                            style="width: 100%"
                            allow-clear
                        >
                            <h-select-option v-for="(item, index) in roles" :key="index" :value="item?.code">{{ item?.desc }}</h-select-option>
                        </h-select>
                    </h-form-item>
                </h-card>
            </h-col>

        </h-row>
        <h-form-item>
            <h-button type="dashed" block @click="addStep" style="margin-top: 20px;">
                <PlusOutlined />
                添加步骤
            </h-button>
        </h-form-item>
    </h-form>


</template>

<style lang="less" scoped>

</style>