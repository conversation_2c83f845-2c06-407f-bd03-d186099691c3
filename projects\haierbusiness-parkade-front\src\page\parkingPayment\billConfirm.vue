<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
} from "ant-design-vue";
import type { Rule } from 'ant-design-vue/es/form';

import { ColumnType } from "ant-design-vue/lib/table/interface";
import { Key } from "ant-design-vue/lib/vc-table/interface";
import {
  DownOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
} from "@ant-design/icons-vue";
import { payApi, parkadeApi } from "@haierbusiness-front/apis";
import {
  VirtualAccountTypeConstant,
  HaierBudgetSourceConstant,
  VirtualScopeConstanty,
  IVirtualChangeListRequest,
  PayStatusConstant,
  PayTypeConstant,
  VirtualAccountChangeTypeConstant,
} from "@haierbusiness-front/common-libs";
import { errorModal, routerParam } from "@haierbusiness-front/utils";
import Eloading from "@haierbusiness-front/components/loading/Eloading.vue";
import dayjs, { Dayjs } from "dayjs";
import { computed, onMounted, ref, watch } from "vue";
import { DataType, usePagination, useRequest } from "vue-request";
import { useRouter } from "vue-router";
import { useEditDialog, useDelete } from "@haierbusiness-front/composables";
import EditDialog from "./edit-dialog.vue";
import UserDialog from "./user-dialog.vue";
import exportLogModal from "./exportLogModal.vue";

const router = useRouter();
const columns: ColumnType[] = [
  {
    title: "汇总单号",
    dataIndex: "collectCode",
    fixed: "left",
    width: "240px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "汇总时间",
    dataIndex: "collectTime",
    width: "240px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "所属园区",
    dataIndex: "zoneName",
    width: "240px",
    align: "center",
    ellipsis: true,
  },

  {
    title: "归账年月",
    dataIndex: "month",
    width: "240px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "结算账户",
    dataIndex: "settlementAccount",
    width: "120px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "汇总时间范围",
    dataIndex: "collectDateStart",
    width: "240px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "推送金额汇总",
    dataIndex: "collectPushedAmount",
    width: "170px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "实际金额汇总",
    dataIndex: "collectArrivalAmount",
    width: "150px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "经办人工号",
    dataIndex: "userCode",
    width: "150px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "经办人姓名",
    dataIndex: "userName",
    width: "150px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "订单状态",
    dataIndex: "collectStatus",
    width: "150px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "结算单号",
    dataIndex: "settlementCode",
    width: "200px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "取消原因",
    dataIndex: "cancelReason",
    width: "220px",
    align: "center",
    ellipsis: true,
  },

  {
    title: "操作",
    dataIndex: "_operator",
    width: "220px",
    fixed: "right",
    align: "center",
  },
];
const searchParam = ref<IVirtualChangeListRequest>({});
const { data, run: listApiRun, loading, current, pageSize } = usePagination(
  parkadeApi.confirmList
);

const reset = () => {
  searchParam.value = {};
};

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: "center" },
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const {
  data: exportListData,
  run: exportListApiRun,
  loading: exportListLoading,
} = useRequest(parkadeApi.exportParkCollectList);



// 获取园区列表
const zoneList = ref<any>([])
const fieldNames = {
  label: "zoneName",
  value: "zoneCode",
}
const filterOption = (input: string, option: any) => {
  return option.zoneName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
const getZoneList = () => {
  parkadeApi.zoneList().then(res => {
    zoneList.value = res
  })
}

const changeZone = (value: any, option:any) => {
  billSummaryForm.value.settlementAccount = option.settlementAccount
}


const startBeginAndEnd = ref<[Dayjs, Dayjs]>();
watch(
  () => startBeginAndEnd.value,
  (n: any, o: any) => {
    if (n) {
      searchParam.value.changeBegin = n[0];
      searchParam.value.changeEnd = n[1];
    } else {
      searchParam.value.changeBegin = undefined;
      searchParam.value.changeEnd = undefined;
    }
  }
);


// 账单汇总----
const billSummaryDialog = ref(false)

const rules: Record<string, Rule[]> = {
  month: [{ required: true,message:'请选择归账月份', trigger: 'change' }],
  time: [{ required: true,message:'请选择订单时间', trigger: 'change' }],
  zoneCode: [{ required: true,message:'请选择所属园区', trigger: 'change' }],
  settlementAccount: [{ required: true,message:'请选择结算账户	', trigger: 'change' }],

};

const layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};
const billSummaryForm = ref<any>({
  "month": "",
  "collectDateStart": "",
  "collectDateEnd": "",
  "zoneName": "",
  "zoneCode": "",
  "settlementAccount": ""
})
const showBillSummary = () => {
  billSummaryForm.value = {}
  billSummaryDialog.value = true
}

const formRef = ref();
const onSubmitLoading = ref(false)
const onSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      onSubmitLoading.value = true
      parkadeApi.parkCollect(billSummaryForm.value).then(res => {
        message.success("汇总成功")
        billSummaryDialog.value = false
        onSubmitLoading.value = false

      }).catch(() => {
        onSubmitLoading.value = false
        message.error("汇总失败")
      })

    })
    .catch((error:any) => {

    });
};

const cancle = () => {
  billSummaryForm.value = {}

  billSummaryDialog.value = false
}

const exportLogModalRef = ref();

const showExportLog = () => {
  exportLogModalRef.value.show();
}

watch(
  () => billSummaryForm.value.time,
  (n: any, o: any) => {
    if (n) {
      billSummaryForm.value.collectDateStart = n[0];
      billSummaryForm.value.collectDateEnd = n[1];
    } else {
      billSummaryForm.value.collectDateStart = undefined;
      billSummaryForm.value.collectDateEnd = undefined;
    }
  }
);

const goToDetail = (record:any, type:any) => {
  router.push({
    path: "/parkade/parkingPayment/detail",
    query: {
      id: record.id,
      type
    }
  });
}

const handleMonthChange = (val:any) => {
  if(val) {
    billSummaryForm.value.time = [dayjs(val).startOf('month').format('YYYY-MM-DD'),dayjs(val).endOf('month').format('YYYY-MM-DD')]
  }else {
    billSummaryForm.value.time = []
  }
}

onMounted(() => { 
  handleTableChange({ current: 1, pageSize: 10 })
  getZoneList()
}); 

</script>

<template>
  <div
    style="
      background-color: #ffff;
      height: 100%;
      width: 100%;
      padding: 10px 10px 0px 10px;
      overflow: auto;
    "
  >
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="collectCode">汇总单号:</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="collectCode"
              v-model:value="searchParam.collectCode"
              placeholder="汇总单号"
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="userCode">经办人工号:</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="userCode"
              v-model:value="searchParam.userCode"
              placeholder="经办人工号"
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="userName">经办人姓名:</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="userName"
              v-model:value="searchParam.userName"
              placeholder="经办人姓名"
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="settlementCode">结算单号:</label>
          </h-col>
          <h-col :span="4">
            <h-input
            
              id="settlementCode"
              v-model:value="searchParam.settlementCode"
              placeholder="结算单号"
              autocomplete="off"
              allow-clear
            />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="collectTimeStart">汇总时间:</label>
          </h-col>
          <h-col :span="4">
              <h-date-picker v-model:value="searchParam.collectTimeStart" format="YYYY-MM-DD HH:mm:ss" :show-time="{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="collectTimeEnd">截止汇总时间:</label>
          </h-col>
          <h-col :span="4">
            <h-date-picker v-model:value="searchParam.collectTimeEnd" format="YYYY-MM-DD HH:mm:ss" :show-time="{ defaultValue: dayjs('23:59:59', 'HH:mm:ss') }" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
          </h-col>


          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="month">归账月份：</label>
          </h-col>
          <h-col :span="4">
            <h-date-picker v-model:value="searchParam.month" value-format="YYYY-MM" style="width: 100%" picker="month" />
          </h-col>
          
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="searchState">所属园区:</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value="searchParam.zoneCode"
              style="width: 100%"
              allow-clear
              show-search
              placeholder="请选择所属园区"
              :options="zoneList"
              :filter-option="filterOption"
              :fieldNames="fieldNames"
            >
            </h-select>
          </h-col>
          
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="settlementAccount">结算账户:</label>
          </h-col>
          <h-col :span="4">
            <h-input
            
              id="settlementAccount"
              v-model:value="searchParam.settlementAccount"
              placeholder="结算账户"
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="collectStatus">订单状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value="searchParam.collectStatus"
              style="width: 100%"
              allow-clear
              placeholder="订单状态"
            >
              <h-select-option value="10">待确认</h-select-option>
              <h-select-option value="20">已确认 </h-select-option>
              <h-select-option value="30">已取消</h-select-option>
              <h-select-option value="40">已完成</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="12" >
            <h-button
              type="primary"
              @click="showBillSummary"
            >
              账单汇总
            </h-button>
          </h-col>
          <h-col :span="12" style="text-align: right">
           
            
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button
              type="primary"
              style="margin-right: 10px"
              @click="handleTableChange({ current: 1, pageSize: 10 })"
            >
              <SearchOutlined />
              查询
            </h-button>

            <h-button type="primary" style="margin-right: 10px" :loading="exportListLoading"
              @click="exportListApiRun(searchParam);">
              <UploadOutlined />
              导出
            </h-button>

            <h-button type="primary"
              @click="showExportLog">
              导出记录
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="dataSource"
          :pagination="pagination"
          :scroll="{ y: 550 }"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ text, column, record }">
            <template v-if="column.dataIndex === 'collectDateStart'">
              {{ record.collectDateStart && record.collectDateEnd ? `${record.collectDateStart} - ${record.collectDateEnd}` : '' }}
            </template>
          
            <template v-if="column.dataIndex === 'collectStatus'">
              {{ text === 10 ? '待确认' 
                  : text === 20
                  ? '已确认'
                  : text === 30
                  ? '已取消'
                  : text === 40
                  ? '已完成'
                  : '-'
              }}
            </template>

            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="goToDetail(record, 10)">查看详情</h-button>
              <h-button type="link" v-if="record.buttonType == 20" @click="goToDetail(record, 20)">账单确认</h-button>
              <h-button type="link" v-if="record.buttonType == 30" @click="goToDetail(record, 30)">维护内接单</h-button>

            </template>
         
          </template>
        </h-table>
      </h-col>
    </h-row>

    <!-- 导出记录 -->
    <exportLogModal ref="exportLogModalRef" type="20" />

    <h-modal v-model:open="billSummaryDialog" :width="600" title="账单汇总" :footer="null">
      <h-form
        ref="formRef"
        :model="billSummaryForm"
        :rules="rules"
        v-bind="layout"
      >
        <h-form-item has-feedback label="归账月份" name="month">
          <h-date-picker v-model:value="billSummaryForm.month" @change="handleMonthChange"  value-format="YYYY-MM" style="width: 100%" picker="month" />
        </h-form-item>
        <h-form-item has-feedback label="订单时间" name="time">
          <h-range-picker v-model:value="billSummaryForm.time" value-format="YYYY-MM-DD" style="width: 100%" />
        </h-form-item>
        <h-form-item has-feedback label="所属园区" name="zoneCode">
          <h-select
              ref="select"
              v-model:value="billSummaryForm.zoneCode"
              style="width: 100%"
              show-search
              placeholder="请选择所属园区"
              :options="zoneList"
              :filter-option="filterOption"
              :fieldNames="fieldNames"
              @change="changeZone"

            >
            </h-select>
        </h-form-item>

        <h-form-item has-feedback label="结算账户" name="settlementAccount">
          <h-input
            v-model:value="billSummaryForm.settlementAccount"
            placeholder="结算账户"
            autocomplete="off"
            disabled
          />
        </h-form-item>

        
        <h-form-item :wrapper-col="{ span: 14, offset: 8 }">
          <h-button type="primary" :loading="onSubmitLoading" @click="onSubmit">提交</h-button>
          <h-button style="margin-left: 10px" @click="cancle">取消</h-button>
        </h-form-item>
      </h-form>
    </h-modal>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
