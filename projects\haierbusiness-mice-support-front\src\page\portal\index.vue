<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import {
  CommonBox,
  Banner,
  Process,
  Consultant,
  Service,
  Resource,
  CalendarDrawer,
  InstructionModal,
  ConsultantModal,
} from './components';
import CalendarImg from '@/assets/image/home/<USER>';
import MiceBidAnchor from '@/page/components/MiceBidAnchor.vue';
import { usePortalStore } from './store';
import { useRoute,useRouter } from 'vue-router';
import { resolveParam } from '@haierbusiness-front/utils';
import router from '../../router';
const gorouter = useRouter();

// const globalStore = applicationStore();
// const { loginUser } = storeToRefs(globalStore);
const route = useRoute();
const store = usePortalStore();
const anchorLink = ref('');

const anchorItems = [
  {
    key: '1',
    href: '#hotelProcess',
    title: '服务流程',
  },
  {
    key: '2',
    href: '#hotelConsultant',
    title: '会议顾问',
  },
  {
    key: '3',
    href: '#hotelService',
    title: '配套服务',
  },
  {
    key: '4',
    href: '#hotelResource',
    title: '直签资源',
  },
];

// 流程id
const processId = ref<string>('1');
const contentRef = ref(null);

const handleResize = () => {
  if (!contentRef.value) return;
  const el = contentRef.value;
  if(window.innerWidth<=1099){
    el.style.transform = 'scale(0.5)'
    el.style.marginTop = '-600px'
    el.style.marginLeft = '-150px'
    }else if(window.innerWidth<=1280){
      el.style.transform = 'scale(0.6)'
      el.style.marginTop = '-500px'
      el.style.marginLeft = '-80px'
    }else if(window.innerWidth<=1536){
      el.style.transform = 'scale(0.8)'
      el.style.margin = '0 auto'
      el.style.marginTop = '-237px'

    }else{
      el.style.transform = 'scale(1)'
      el.style.margin = '0 auto'
      el.style.marginTop = '0px'
    }

};



onMounted(() => {
  window.addEventListener('resize', handleResize);
  handleResize(); // 初始化执行
  // 地址栏取参
  const record = resolveParam(route.query.record);
  processId.value = record?.processId || '87'; // TODO 流程id
  // 流程节点id
  localStorage.setItem('processId', processId.value);

  store.getCityList();
  store.getCounsellorList({ id: processId.value });
});
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

const go = ()=>{
  gorouter.push('/support/meeting/index')
}
</script>

<template>
  <!-- 商户云首页 -->
  <div class="container">
    <banner />
    <h-button @click="go">跳转</h-button>
    <div class="content" ref="contentRef">
      <common-box
        id="hotelProcess"
        title="海易智会服务流程"
        desc="一对一服务，全程一单到底服务"
        :contentStyle="{ marginTop: '48px' }"
      >
        <process />
      </common-box>
      <common-box
        id="hotelConsultant"
        title="专业会议顾问"
        desc="行业内专业会议顾问，为您的每一次会议保驾"
        :contentStyle="{ marginTop: '48px' }"
      >
        <consultant />
      </common-box>
      <common-box
        id="hotelService"
        title="配套服务"
        desc="海易智会除了给您提供标准会议服务外，还提供了旅游、团建等多种服务"
        :contentStyle="{ marginTop: '48px' }"
      >
        <service />
      </common-box>
      <common-box
        id="hotelResource"
        title="直签资源"
        desc="海尔商旅会务平台直签了全国800家酒店，可享受更低的企业折扣价格"
        :contentStyle="{ marginTop: '-30px' }"
      >
        <resource />
      </common-box>
    </div>
    <div class="left-float" v-show="!!anchorLink">
      <mice-bid-anchor :items="anchorItems" :offsetTop="88" @change="(link: string) => anchorLink = link" />
    </div>
    <div class="right-float">
      <img :src="CalendarImg" class="calendar" @click="store.calendarDrawerOpen = true" />
    </div>
    <calendar-drawer v-model="store.calendarDrawerOpen" />
    <consultant-modal v-model="store.consultantModalOpen" />
    <instruction-modal v-model="store.instructionModalOpen" />
  </div>
</template>

<style scoped lang="less">
:root {
  font-size: 14px;
  line-height: 1;
  font-weight: 400;
  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}
.container {
  margin-top: -78px;
  .content {
    width: 1400px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .left-float {
    background: #f6f9fc;
    padding: 26px 20px 26px 10px;
    border-radius: 5px;
    position: fixed;
    top: 50%;
    left: 10px;
    transform: translate(0, -50%);
    .anchor {
      .anchor-link {
        margin-bottom: 36px;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  .right-float {
    position: fixed;
    /* bottom: calc(50% - 100px); */
    bottom: 200px;
    right: 10px;
    transform: translate(0, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    img.calendar {
      cursor: pointer;
      width: 31.5px;
      border-radius: 10px;
    }
  }
}
</style>
