<script setup lang="ts">
import {
  Modal,
  Tag as hTag,
  Form as hForm,
  Input as hInput,
  Popover as hPopover,
  Popconfirm as hPopconfirm,
  FormItem as hFormItem,
  Select as hSelect,
  SelectOption as hSelectOption,
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Spin as hSpin,
  Table as hTable,
  Textarea as hTextarea,
  DatePicker as hDatePicker,
  CollapsePanel as hCollapsePanel,
  Collapse as hCollapse,
  FormInstance,
  message,
  TableColumnsType,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { PlusOutlined, SearchOutlined, ExpandAltOutlined, MinusCircleOutlined } from '@ant-design/icons-vue';
import {
  IAnnualPlanSaveOrUpdateRequestDTO,
  AnnualPlanTypeStateConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
  IHaierAccountBillInfo,
IUserInfo,
UserGroupSystemConstant,
} from '@haierbusiness-front/common-libs';
import { checkUserGroup, getCurrentRouter, resolveParam } from '@haierbusiness-front/utils';
import { ref, createVNode, PropType, computed, watch } from 'vue';

import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';
import dayjs, { Dayjs } from 'dayjs';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import {
DailyReportStateConstant,
  EvaluateControlConstant,
  IAnnualPlanDetailRequestDTO,
  IAnnualPlanDetailResponseDTO,
  IAnnualPlanTypeListRequest,
  IDailyPersonalResponse,
  IDailyReportConferenceRequestDTO,
  IDailyReportListResponseDTO,
  IDailyReportProjectRequestDTO,
  IMonthPlanDetailResponseDTO,
  PlanTypeConstant,
} from '@haierbusiness-front/common-libs/src/daily';
import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
const { loginUser } = storeToRefs(applicationStore(globalPinia));

const router = getCurrentRouter();
const prop = defineProps({
  data: Object as PropType<IDailyReportConferenceRequestDTO[]>,
  dailyReport: Object as PropType<IDailyReportListResponseDTO>,
  monthData: Object as PropType<IMonthPlanDetailResponseDTO>,
  fold: Boolean as PropType<Boolean>,
  dailyPerson: Object as PropType<IDailyPersonalResponse[]>,
});

const columns: ColumnType[] = [
  {
    title: '日清会决议内容',
    dataIndex: 'content',
    width: '220px',
    align: 'center',
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '责任人',
    dataIndex: 'principalUsercode',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '时间节点',
    dataIndex: 'executionTime',
    width: '70px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '40px',
    fixed: 'right',
    align: 'center',
  },
];

const collapseActiveKey = ref([1]);
{
  if (prop?.fold) {
    collapseActiveKey.value = [];
  }
}
const isReadonly = () => {
  return (
    DailyReportStateConstant.RUNNING.code === prop?.dailyReport?.state ||
    DailyReportStateConstant.FINISH.code === prop?.dailyReport?.state
  ) || !checkUserGroup(UserGroupSystemConstant.DAILY_MICRO.groupId);
};

const removeDomain = (item: any) => {
  let index = prop?.data?.indexOf(item);
  if (index != undefined && index !== -1) {
    prop?.data?.splice(index, 1);
  }
};

const addDomain = () => {
  prop?.data?.push({});
};

const getIndex = (record: any) => {
  return prop.data?.indexOf(record) || 0;
};
const userNameChange = (userInfo: IUserInfo, record: IDailyReportConferenceRequestDTO) => {
  record.principalUsercode = userInfo.username;
  record.principalUsername = userInfo.nickName;
};
const currentData = computed(() => {
  return dayjs();
});
</script>

<template>
  <h-collapse
    v-model:activeKey="collapseActiveKey"
    :bordered="false"
    style="background-color: white"
    :collapsible="'icon'"
  >
    <h-collapse-panel key="1">
      <template #header>
        <div style="display: flex">
          <!--  <div style="height: 25px; width: 10px; background-color: rgb(0, 115, 229)" /> -->
          <div style="font-size: 16px; font-weight: 600; margin-left: 10px">日清会</div>
        </div>
      </template>
      <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%"></div>
      <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px">
        <h-row :gutter="24" style="margin-top: 15px">
          <h-col :span="24">
            <h-table
              style="margin-left: 5px"
              :size="'small'"
              :bordered="true"
              :columns="columns"
              :data-source="prop?.data"
              :pagination="false"
            >
              <template #emptyText> 请点击下方按钮新增 </template>
              <template #bodyCell="{ record, column, text }">
                <template v-if="['content'].includes(column.dataIndex as any)">
                  <div>
                    <h-input v-model:value="data!![getIndex(record)].content" allowClear>
                      <template #addonAfter>
                        <h-popover trigger="click">
                          <template #content>
                            <h-textarea
                              v-model:value="data!![getIndex(record)].content"
                              style="width: 500px"
                              :rows="4"
                              allowClear
                            />
                          </template>
                          <ExpandAltOutlined />
                        </h-popover>
                      </template>
                    </h-input>
                  </div>
                </template>
                <template v-if="['principalUsercode'].includes(column.dataIndex as any)">
                  <user-select
                    style="width: 100%"
                    :value="data!![getIndex(record)].principalUsername"
                    placeholder="责任人"
                    :params="{
                      pageNum: 1,
                      pageSize: 20,
                    }"
                    @change="(userInfo: IUserInfo) =>  userNameChange(userInfo,  data!![getIndex(record)])"
                  ></user-select>
                </template>
                <template v-if="['executionTime'].includes(column.dataIndex as any)">
                  <h-date-picker
                    :defaultPickerValue="currentData"
                    v-model:value="data!![getIndex(record)].executionTime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                  />
                </template>
                <template v-if="['_operator'].includes(column.dataIndex as any)">
                  <h-popconfirm title="确认删除?" v-if="!isReadonly()" @confirm="removeDomain(record)">
                    <h-button type="link" danger>
                      <template #icon>
                        <MinusCircleOutlined :style="{ fontSize: '20px' }" />
                      </template>
                    </h-button>
                  </h-popconfirm>
                </template>
              </template>
            </h-table>
          </h-col>
          <h-col :span="24" style="margin: 10px">
            <h-button v-if="!isReadonly()" type="dashed" style="width: calc(100% - 10px)" @click="addDomain">
              <PlusOutlined />
              添加
            </h-button>
          </h-col>
        </h-row>
      </div>
    </h-collapse-panel>
  </h-collapse>
</template>

<style scoped lang="less">
@import '../../assets/css/main.less';
</style>
