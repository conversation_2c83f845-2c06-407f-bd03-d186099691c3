<script lang="ts" setup>
import {
  Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
  Input as hInput, CheckboxGroup as hCheckboxGroup, Checkbox as hCheckbox, Row as hRow, Col as hCol, DatePicker as hDatePicker, message,
  Upload as hUpload, RadioGroup as hRadioGroup, Radio as hRadio, InputNumber as hInputNumber,
  Textarea as hTextarea, Button as hButton
} from 'ant-design-vue';
import { computed, ref, watch, onMounted } from "vue";
import type { Ref } from "vue";
import {
  ILifeAccount, VirtualAccountTypeConstant, VirtualScopeConstanty, IApplicationInfo
} from '@haierbusiness-front/common-libs';
import { useRequest } from 'vue-request';
import { enterpriseApi, applicationApi, virtualPayApi, advertisementListApi } from '@haierbusiness-front/apis';
import type { UploadChangeParam, UploadProps } from 'ant-design-vue';
import PlusOutlined from '@ant-design/icons-vue/PlusOutlined';
import LoadingOutlined from '@ant-design/icons-vue/LoadingOutlined';
import { UploadOutlined } from '@ant-design/icons-vue'
import type { Rule } from "ant-design-vue/es/form";
interface Props {
  show: boolean;
  data: ILifeAccount | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: ILifeAccount = {
  imgUrl: '',
  jumpLinkApp: '',
  jumpLinkPc: '',
  adSubject: ''
};

const checkDisUrlTip = (url: string) => {
  const reg = /^((https|http|ftp|rtsp|mms)?:\/\/)[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/;
  if (reg.test(url)) {
    return true;
  } else {
    return false;
  }
}
const {
  data: enterprises,
  run: userListApiRun
} = useRequest(advertisementListApi.queryAdvertisement, {
  manual: false
});
const validateUrl = (_rule: Rule, value: string, name: string) => {
  if (value !== "") {
    if (checkDisUrlTip(value)) {
      return Promise.resolve();
    } else {
      return Promise.reject('必须以http或https开头的网页链接！');
    }
  } else {
    return Promise.reject(`请填写${name}！`);
  }
}
const rules = {
  imgUrl: [
    { required: true, message: '请上传Life图片！' }
  ],
  jumpLinkApp: [
    { required: true, validator: (_rule: Rule, value: string) => validateUrl(_rule, value, '移动端链接'), trigger: 'change' }
  ],
  jumpLinkPc: [
    { required: true, validator: (_rule: Rule, value: string) => validateUrl(_rule, value, 'PC端链接'), trigger: 'change' }
  ],
  showOrder: [
    { required: true, message: '请填写展示顺序！' }
  ],
  showStatus: [
    { required: true, message: '请选择展示状态！' }
  ],
  adSubject: [
    { required: true, message: '请填写园区生活主题！' }
  ],
};

const life: Ref<ILifeAccount> = ref(
  ({ ...props.data } as ILifeAccount) || defaultData
);

watch(props, (newValue) => {
  life.value = ({ ...newValue.data } as ILifeAccount) || defaultData;
});

const emit = defineEmits(["cancel", "ok"]);
function getBase64(img: any, callback: (base64Url: string) => void) {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result as string));
  reader.readAsDataURL(img);
}
const visible = computed(() => props.show);
const fileList = ref([]);
const loading = ref<boolean>(false);
const imageUrl = ref<string>('');
const handleChange = (info: UploadChangeParam) => {
  if (info.file.status === 'uploading') {
    loading.value = true;
    return;
  }
  if (info.file.status === 'done') {
    // Get this url from response in real world.
    console.log('上传成功', info)
    getBase64(info.file.originFileObj, (base64Url: string) => {
      imageUrl.value = base64Url;
      loading.value = false;
    });
    life.value.imgUrl = info.file.response.content.url

  }
  if (info.file.status === 'error') {
    loading.value = false;
    message.error('upload error');
  }
};

const beforeUpload = (file: any) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('只允许上传图片类型');
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('Image must smaller than 2MB!');
  }
  return isJpgOrPng && isLt2M;
};
const handleOk = () => {
  confirmLoading.value = true;
  from.value.validate()
    .then(() => {
      const data = {
        ...life.value,
      }
      emit("ok", data, () => {
        confirmLoading.value = false;
      });
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};
const handleRemove = () => {
  fileList.value = []
  imageUrl.value = ''
}
if (props.data) {
  imageUrl.value = props.data.imgUrl ? props.data.imgUrl : ''
}
</script>

<template>
  <h-modal v-model:visible="visible" :title="life.id ? '编辑轮播图' : '新增轮播图'" :width="600" @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading" @ok="handleOk">
    <h-form ref="from" :model="life" :label-col="{ span: 5 }" :wrapper-col="{ span: 20 }" :rules="rules">
      <h-form-item label="Banner主题" name="adSubject">
        <h-input v-model:value="life.adSubject" />
      </h-form-item>
      <h-form-item label="Banner图片" name="imgUrl">
        <h-row>
          <h-col :span="24">
            <h-upload v-model:file-list="fileList" accept="image/png,image/jpeg,image/jpg" list-type="picture-card"
              :max-count="1" class="avatar-uploader" :show-upload-list="false" action="/upload" @change="handleChange"
              :before-upload="beforeUpload" @remove="handleRemove">
              <img v-if="imageUrl" :src="imageUrl" alt="life" class="imgShow" />
              <div v-else>
                <loading-outlined v-if="loading"></loading-outlined>
                <plus-outlined v-else></plus-outlined>
                <div>上传图片</div>
              </div>
            </h-upload>
          </h-col>
          <h-col :span="24">
            请上传图片的尺寸为<span class="important">2496*691</span> 大小不超过<span class="important">2MB</span> 格式为<span
              class="important">png/jpg/jpeg</span>的文件
          </h-col>
        </h-row>

      </h-form-item>
      <h-form-item label="PC端链接" name="jumpLinkPc">
        <h-textarea v-model:value="life.jumpLinkPc" />
      </h-form-item>
      <h-form-item label="移动端链接" name="jumpLinkApp">
        <h-textarea v-model:value="life.jumpLinkApp" />
      </h-form-item>
      <h-form-item label="展示顺序" name="showOrder">
        <h-input-number v-model:value="life.showOrder" :min="0" />
      </h-form-item>
      <h-form-item label="展示状态" name="showStatus">
        <h-radio-group v-model:value="life.showStatus">
          <h-radio :value=1>展示</h-radio>
          <h-radio :value=0>隐藏</h-radio>
        </h-radio-group>
      </h-form-item>
    </h-form>
  </h-modal>
</template>


<style lang="less" scoped>
.important {
  color: red;
}

.imgShow {
  width: 100%;
}

.avatar-uploader {
  :deep(.bwdv-upload.bwdv-upload-select-picture-card) {
    width: 416px !important;
    height: 115px !important;
    overflow: hidden;
    border-radius: 8px;
  }
}
</style>
  