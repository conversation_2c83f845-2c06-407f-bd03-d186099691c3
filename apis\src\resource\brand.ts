import { brandReq,providerBrandReq} from '@haierbusiness-front/common-libs'
import { download, get, post,filepost,originalGet} from '../request'

export const brandApi = {
    // 国旅品牌
    getHotelBrandList: (params:brandReq): Promise<void> => {
        return get('/hotel-mapping/api/hotelBrand/page', params)
    },
    // 供应商品牌列表
    getHotelBrandProviderMapList: (params:providerBrandReq): Promise<void> => {
        return get('/hotel-mapping/api/hotelBrandProviderMap/page', params)
    },
    // 关联 供应商行政区划信息映射国旅行政区划
    providerBrandMappingHotelBrand: (params:any): Promise<void> => {
        return post('/hotel-mapping/api/hotelBrandProviderMap/providerBrandMappingHotelBrand', params)
    },
    // 解除供应商映射
    deleteMappingHotelBrand: (params:any): Promise<void> => {
        return post('/hotel-mapping/api/hotelBrandProviderMap/deleteMappingHotelBrand', params)
    },
    // 更新地址字段
    update: (params:any): Promise<void> => {
        return post('/hotel-mapping/api/hotelBrand/update', params)
    },
}