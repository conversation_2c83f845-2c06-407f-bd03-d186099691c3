import { IDepartmentTreeRes,IDepartmentTreeReq,IPageResponse } from '@haierbusiness-front/common-libs'
import { get, post } from '../request'

export const departmentApi = {

  /**
   * 查询部门树
   */
  departmentTree: (params: IDepartmentTreeReq): Promise<IDepartmentTreeRes[]> => {
      return get('system/api/department/tree', params)
  },


  /**
   * 查询部门树
   */
   departmentList: (enterpriseCode: string | undefined, name: string | undefined, pageNum: number, pageSize: number): Promise<IPageResponse<IDepartmentTreeRes>> => {
      return get('system/api/department/page', {enterpriseCode, name, pageNum, pageSize})
   },

}