import { download, get, post } from '../request'
import { 
    IAnnualBudgetFilter, 
    IAnnualBudget,
    IPageResponse, 
    Result } from '@haierbusiness-front/common-libs'


export const annualBudgetApi = {
    list: (params: IAnnualBudgetFilter): Promise<IPageResponse<IAnnualBudget>> => {
        return get('/mice-bid/api/mice/year/budget/admin/list', params)
    },

    details: (id: number): Promise<IAnnualBudget> => {
        return get('/mice-bid/api/mice/year/budget/detail', {
            id
        })
    },

    save: (params: IAnnualBudget): Promise<Result> => {
        return post('/mice-bid/api/mice/year/budget/add', params)
    },

    edit: (params: IAnnualBudget): Promise<Result> => {
        return post('/mice-bid/api/mice/year/budget/edit', params)
    },

    remove: (id: number): Promise<Result> => {
        return post(`/mice-bid/api/mice/year/budget/delete?id=${id}`)
    },
}
