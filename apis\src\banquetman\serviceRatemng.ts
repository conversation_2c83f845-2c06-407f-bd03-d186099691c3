import { downloadPost, get, post } from '../request'
import { 
    IRefundReq, 
    IRefundRes,
    IPageResponse, 
    Update_1Params,
    ExportInvoiceParams,
    IPserviceRatemng
} from '@haierbusiness-front/common-libs'


export const serviceRatemngApi = {
    list: (params: IPserviceRatemng): Promise<IPageResponse<IRefundRes>> => {
        return post('banquet/api/banquetServiceSettleStatement/list', params)
    },
    detailList: (params: IRefundReq): Promise<IPageResponse<IRefundRes>> => {
        return post('banquet/api/banquetSettleDetail/list', params)
    },
    get: (id: number): Promise<IRefundRes> => {
        return get('banquet/api/banquetServiceSettleStatement/get', {
            id
        })
    },
    exportList: (params: IRefundReq): Promise<IPageResponse<IRefundRes>> => {
        return downloadPost('banquet/api/banquetServiceSettleStatement/export', params)
    },
    // 修改费率
    changeServiceRatemng: (params: Update_1Params): Promise<IPageResponse<IRefundRes>> => {
        return post('banquet/api/banquetServiceSettle/update', params)
    },
    // 新增归账
    add: (params: Update_1Params): Promise<IPageResponse<IRefundRes>> => {
        return post('banquet/api/banquetServiceSettleStatement/add', params)
    },
    // 获取
    getInfo: (): Promise<IPageResponse<IRefundRes>> => {
        return get('banquet/api/banquetServiceSettle/get')
    },
}
