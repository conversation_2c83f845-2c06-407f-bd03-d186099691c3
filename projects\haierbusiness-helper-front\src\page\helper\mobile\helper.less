.mr-5 {
  margin-right: 5px !important;
}

.mt-5 {
  margin-top: 5px !important;
}

.mt-10 {
  margin-top: 10px;
}

.ml-5 {
  margin-left: 5px !important;
}

.mb-5 {
  margin-bottom: 5px !important;
}

.mr-10 {
  margin-right: 10px !important;
}

.mt-20 {
  margin-top: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.ml-10 {
  margin-left: 10px !important;
}

.mb-10 {
  margin-bottom: 10px !important;
}

.mb-30 {
  margin-bottom: 30px !important;
}

.strong {
  font-weight: 600;
  font-size: 14px;
}

.mr-20 {
  margin-right: 20px;
}

.container {
  padding: 10px;
  width: 100vw;
  min-height: 100vh;
  overflow-x: hidden;
  background-color: #f6f7f9;
  display: flex;
  flex-direction: column;

  .banner-img {
    height: 100px;
    width: 100%;
    background: url("@/assets/image/helper/mobile-Banner.jpg") 100% 100% no-repeat;
    border-radius: 10px;
    background-size: 100% 100%;
  }

  .my-radio {
    :deep(.van-radio__icon) {
      height: auto !important;
    }

    .btn-com {
      width: 100px;
      height: 28px;
      border: none;

      &.active {
        border: 1px solid #0073E5;
        color: #0073E5;
        background-color: #F0F7FF;
      }
    }
  }
}

.trip-item {
  .item-address {
    font-weight: 500;
    font-size: 20px;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 5px;
    text-align: center;

  }

  .item-time {
    font-size: 12px;
    color: #595959;
  }
}

.my-tab {
  height: 38px;
  display: flex;
  width: 100%;
  // justify-content: space-between;
  overflow-x: scroll;
  flex-wrap: nowrap;
  overflow-y: hidden;
  align-items: center;

  .tab-item {
    flex: 0 0 auto;
    align-items: center;
    justify-content: center;
    flex-wrap: nowrap;
    margin-right: 12px;

    &.active {
      color: #0073E5;
    }
  }

  /* 隐藏滚动条 */
  &::-webkit-scrollbar {
    display: none;
    /* 隐藏滚动条 */
  }

  /* 兼容其他浏览器 */
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.order-box {
  display: flex;
  justify-content: space-around;
  height: 40px;
  align-items: center;
}

.order-btn-box {
  display: flex;
  align-items: center;

  .order-btn-text {
    &.active {
      color: #2b73e5;
    }
  }

  .order-btn {
    display: flex;
    flex-direction: column;
    margin-left: 4px;

    .order-btn-top {
      width: 8px;
      height: 8px;
      background: url('@/assets/image/helper/default-top.png');
      background-size: 100% 100%;

      &.active {
        background-image: url('@/assets/image/helper/asc.png');
      }
    }

    .order-btn-bottom {
      width: 8px;
      height: 8px;
      background: url('@/assets/image/helper/default-bottom.png');
      background-size: 100% 100%;

      &.active {
        background-image: url('@/assets/image/helper/desc.png');
      }

    }
  }
}

.list-box,
.list-empty {
  flex: 1;
}

.list-box {
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  .list-item {
    background-color: #fff;
    margin-bottom: 10px;
    padding: 12px;
    position: relative;

    .list-item-text {
      position: absolute;
      bottom: 16px;
      left: 12px;
    }
    .list-item-title {
      color: rgba(89, 89, 89, 1);
      font-size: 12px;
    }

    .city-font {
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      font-weight: 500;
    }

    .list-item-btns {
      
      display: flex;
      justify-content: flex-end;
    }
  }
}

.list-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  background: url("@/assets/image/helper/mobile-empty.png") no-repeat;
  background-position: center;
  background-color: #fff;
  background-size: 100%;
  .empty {
    padding-top: 80%;
  }
}

.addbtn-box {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 16px ;
  background-color: #fff;
  .add-btn {
    width: 100%;
  }
}

.list-btn-big {
  min-width: 60px;
  background: rgba(240, 247, 254, 1);
  color: #0073E5;
  border: none
}

:deep(.icon-chart) {
  display: flex;
  width: 12px;
  height: 12px;
  background-image: url('@/assets/image/helper/chart.png');
  background-size: 100% 100%;
}
.trip-box {
  max-height: 130px;
    overflow: auto;
}
.helper-detail {
  min-height: 100vh;
  width: 100vw;
  display: flex;
  background-color: #f6f7f9;
  flex-direction: column;

  .helper-detail-top {
    width: 100%;
    color: #fff;
    height: 32px;
    background-color: rgba(255, 129, 51, 1);
    display: flex;
    align-items: center;
    padding: 0 15px;
  }

  .helper-detail-contetnt {
    flex: 1;

    .detail-city-box {
      background-color: #fff;

      position: relative;
      height: 68px;
      width: 100%;
      align-items: center;
      padding: 0 10px;
      justify-content: space-between;

      .detail-city-text {
        width: 76%;
        .city-name {
          font-weight: 500;
          font-size: 16px;
          color: #262626;
        }

        .city-address {
          font-weight: 400;
          font-size: 12px;
          color: #8C8C8C;
        }
      }

      .detail-city-position {
        margin-right: 10px;
        flex-direction: column;
        align-items: center;
        font-weight: 400;
        font-size: 12px;
        color: #0073E5;
        position: relative;
        z-index: 100;
      }
    }
  }
}

.flex-center {
  display: flex;
  align-items: center;
}

.transparent-bg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0.3;
  background-image: conic-gradient(from 1.5708rad at 50% 50%, rgba(68, 248, 255, 0.14) 1%, rgba(255, 255, 255, 0.23) 33%, rgba(32, 238, 255, 0.35) 57%, #DDC9FF 96%);
  filter: blur(44px);
}

:deep(.large-value) {
  min-width: 70%;
}

.plan-list {
  height: 120px;
  overflow: auto;
}