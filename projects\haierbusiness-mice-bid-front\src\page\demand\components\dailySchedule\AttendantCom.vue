<script setup lang="ts">
// 服务人员
import {
  Form as hForm,
  FormItem as hFormItem,
  Select as hSelect,
  SelectOption as hSelectOption,
  Input as hInput,
  InputNumber as hInputNumber,
  DatePicker as hDatePicker,
  RangePicker as hRangePicker,
  Textarea as hTextarea,
  Button as hButton,
  Row as hRow,
  Col as hCol,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  Modal as hModal,
  Toolt<PERSON> as hTooltip,
  Collapse as hCollapse,
  CollapsePanel as hCollapsePanel,
  message,
} from 'ant-design-vue';
import { onMounted, ref, reactive, watch, defineProps, defineEmits, toRaw, defineExpose } from 'vue';
import { AttendantTypeConstant, AttendantsArr } from '@haierbusiness-front/common-libs';
import { demandApi } from '@haierbusiness-front/apis';

const props = defineProps({
  dateIndex: {
    type: Number,
    default: 0,
  },
  attendantList: {
    type: Array,
    default: [],
  },
});

const emit = defineEmits(['demandPlanAttendantFunc', 'demandPlanRemoveFunc']);

const demandPlanFormRef = ref();

// 日程安排表单
const formState = reactive<AttendantsArr>({
  attendants: [], // 服务人员
});

// 服务人员列表
watch(
  () => props.attendantList,
  (newVal) => {
    formState.attendants = [...newVal];
  },
  {
    immediate: true,
    deep: true,
  },
);

// 删除
const removeAttendant = (type: String, index: Number) => {
  emit('demandPlanRemoveFunc', { type: type, delIndex: index, index: props.dateIndex });
};

// 提交
const onSubmit = async () => {
  let isVerifyPassed = false;

  await demandPlanFormRef.value
    .validate()
    .then(() => {
      emit('demandPlanAttendantFunc', { list: [...formState.attendants], index: props.dateIndex });
      isVerifyPassed = true;
    })
    .catch((err) => {
      isVerifyPassed = false;
    });

  return isVerifyPassed;
};
defineExpose({ onSubmit });

// 价格测算
const calcPrice = async (i: Number) => {
  if (formState.attendants[i].type >= 0 && formState.attendants[i].personNum) {
    const calcParams = {
      calcDate: formState.attendants[i].demandDate + ' 00:00:00', // 需求日期
      type: formState.attendants[i].type, // 人员类型
      personNum: formState.attendants[i].personNum, // 人数

      duty: formState.attendants[i].duty, // 	工作范围
    };

    const res = await demandApi.priceCalcAttendant({
      ...calcParams,
    });

    console.log('%c [ 服务人员-自动测算单价 ]-120', 'font-size:13px; background:pink; color:#bf2c9f;', res);

    formState.attendants[i].calcUnitPrice = res.calcUnitPrice; //
  }
};

onMounted(async () => {});
</script>

<template>
  <!-- 服务人员 -->
  <div class="attendant_com">
    <h-form ref="demandPlanFormRef" :model="formState" :labelCol="{ style: { width: '84px' } }" hideRequiredMark>
      <div
        class="plan_col_list mb20"
        v-for="(attendantsItem, attendantsIndex) in formState.attendants"
        :key="attendantsIndex"
      >
        <div class="plan_col_title">
          {{ '服务人员' + (attendantsIndex + 1) }}
        </div>
        <div class="plan_col_del" @click="removeAttendant('attendant', attendantsIndex)"></div>

        <h-row :gutter="12" class="mt20">
          <h-col :span="8">
            <h-form-item
              label="人员类型："
              :name="['attendants', attendantsIndex, 'type']"
              :rules="{
                required: true,
                message: '请选择人员类型',
                trigger: 'change',
              }"
            >
              <h-select
                v-model:value="attendantsItem.type"
                @change="calcPrice(attendantsIndex)"
                placeholder="请选择人员类型"
                allow-clear
              >
                <h-select-option v-for="item in AttendantTypeConstant.toArray()" :key="item.code" :value="item.code">
                  {{ item.desc }}
                </h-select-option>
              </h-select>
            </h-form-item>
          </h-col>

          <h-col :span="8">
            <h-form-item
              label="人数："
              :name="['attendants', attendantsIndex, 'personNum']"
              :rules="{
                required: true,
                message: '请填写人数',
                trigger: 'change',
              }"
            >
              <h-input-number
                v-model:value="attendantsItem.personNum"
                @blur="calcPrice(attendantsIndex)"
                placeholder="请填写人数"
                allow-clear
                :min="1"
                :max="999999"
                style="width: 100%"
              />
            </h-form-item>
          </h-col>

          <h-col :span="16">
            <h-form-item
              label="工作范围："
              :name="['attendants', attendantsIndex, 'duty']"
              :rules="{
                required: true,
                message: '请填写工作范围',
                trigger: 'change',
              }"
            >
              <h-textarea
                v-model:value="attendantsItem.duty"
                placeholder="请填写工作范围"
                :autoSize="{ minRows: 3, maxRows: 3 }"
                allow-clear
                :maxlength="500"
              />
            </h-form-item>
          </h-col>
        </h-row>
      </div>
    </h-form>
  </div>
</template>

<style scoped lang="less">
.attendant_com {
  .plan_col_list {
    padding: 20px 24px;
    background: #fff;
    border-radius: 8px;

    position: relative;

    .plan_col_title {
      background: url('@/assets/image/demand/demand_attendant.png');
      background-repeat: no-repeat;
      background-size: 18px 18px;
      background-position: left center;

      text-indent: 28px;
      font-weight: 500;
      font-size: 18px;
      line-height: 25px;
      color: #1d2129;
    }

    .plan_col_del {
      position: absolute;
      top: 12px;
      right: 12px;

      width: 18px;
      height: 18px;
      background: url('@/assets/image/demand/demand_del_x.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      cursor: pointer;
    }
  }
}
</style>
