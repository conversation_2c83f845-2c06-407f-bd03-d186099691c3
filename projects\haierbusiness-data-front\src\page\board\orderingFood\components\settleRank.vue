<template>
    <Rank
        background="rgba(0,0,0,0)"
        :data="rankData"
        unit="万元"
    />
</template>
<script setup lang="ts">
import Rank from "../../components/rank.vue";
import { queryOrderingFoodSettleRank } from "@haierbusiness-front/apis/src/data/board";
import { onMounted, ref } from "vue";
import { EventBus } from "../../eventBus";
const rankData = ref(
    [] as Array<{
        name: string;
        value: string | number;
    }>
);
const loading = ref(false);
onMounted(()=>{
    queryData();
})
EventBus.on((event, params) => {
    if (event == "refresh") queryData(params ? params : null);
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
    loading.value = true;
    const data = await queryOrderingFoodSettleRank(
        params ? params.data.name : null,
        params ? params.from : null
    );
    loading.value = false;
    const rows = [] as Array<{
        name: string;
        value: string | number;
    }>;
    data.rows.forEach((item) => {
        const value = (item[1] / 10000).toFixed(item[1] > 10000 ? 0 : 2);
        rows.push({
            name: item[0],
            value,
        });
    });
    // rows.sort((a,b)=>b.value-a.value);
    rankData.value = rows;
};
</script>
