
type keys = 'TOBEREPORTED' | 'REPORTED' | 'NOTREPORT'

export const InteractState = {
  // 会议类型定义
  TOBEREPORTED: { code: 10, desc: '待提报' },
  REPORTED: { code: 20, desc: '已提报' },
  NOTREPORT: { code: 30, desc: '放弃提报' },

  // 工具方法
  ofType: (type?: number): { code: number, desc: string } | null => {
    for (const key in InteractState) {
      const item = InteractState[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number, desc: string } | undefined)[] => {
    const types = Object.keys(InteractState).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return InteractState[i as keys]
      }
      return
    })
    const newTypes = types.filter(function (s) {
      return s && s;
    })
    return newTypes
  },
};