import { downloadPost, get, post } from '../request'
import { 
    IApplyFilter, 
    IApply,
    IPageResponse, 
    Result } from '@haierbusiness-front/common-libs'


export const banquetApplyApi = {
    list: (params: IApplyFilter): Promise<IPageResponse<IApply>> => {
        return post('banquet/api/admin/applicatioin/page', params)
    },
    listReport:(params: IApplyFilter): Promise<IPageResponse<IApply>> => {
        return post('banquet/api/admin/report/page', params)
    },
    
    get: (id: number): Promise<IApply> => {
        return get('banquet/api/admin/applicatioin/get', {
            id
        })
    },
    exportList: (params: IApplyFilter): Promise<IApply> => {
        return downloadPost('banquet/api/admin/applicatioin/export', params)
    },
     /**
     * 查询审批详情
     */
    details: (params: any): Promise<any> => {
        return get('process/api/control/details', params)
    },
    /**
     * 重新推送订单
     */
    rePush: (params: any): Promise<any> => {
        return get('banquet/api/admin/applicatioin/manualPush', params)
    },
}
