<template>
  <div class="wallet">
    <van-nav-bar
      :title="soucePath == '/bill' ? '福利积分流水' : '账户流水'"
      left-text="返回"
      left-arrow
      @click-left="onClickLeft"
    />
    <van-dropdown-menu>
      <van-dropdown-item v-model="value1" :options="option1" @change="onDropChange" />
      <van-dropdown-item :title="dateTitle" ref="itemRef">
        <van-calendar
          title="选择日期"
          :poppable="false"
          @confirm="onCalendarConfirm"
          type="range"
          :show-confirm="false"
          :min-date="new Date(2010, 0, 1)"
          :max-date="new Date(2099, 0, 1)"
          :style="{
            height: '500px',
          }"
        />
      </van-dropdown-item>
    </van-dropdown-menu>

    <div class="wallet-total">
      消费:￥<span style="color: #333333">{{ totalData.consumption }}</span> &nbsp;&nbsp;&nbsp;&nbsp;充值:￥<span
        style="color: #36b876"
        >{{ totalData.recharge }}</span
      >
      &nbsp;&nbsp;&nbsp;&nbsp;退款:￥<span style="color: #ff4d4f">{{ totalData.refund }}</span>
    </div>
    <van-list
      v-model:loading="loading"
      :finished="finished"
      :offset="100"
      finished-text="没有更多了"
      @load="onLoad"
      :immediate-check="false"
      class="wallet-con"
    >
      <van-cell-group inset v-for="item in list" :key="item.id" class="wallet-group">
        <van-cell :title="item.remark ? item.remark : item.enterpriseName" class="bill-item">
          <div
            :class="
              item.type == 3 ? 'wallet-money' : item.type == 1 ? 'wallet-consumption-money' : 'wallet-refund-money'
            "
          >
            <b>{{ (item.type == 1 ? '' : '+') + item.amount }}元</b>
          </div>
          <template #label>
            <div class="billNo">流水号:{{ item.code || '' }}</div>
            <div>
              {{ item.gmtCreate }}&nbsp;&nbsp;&nbsp;&nbsp;{{
                item.type == 3 ? '充值' : item.type == 1 ? '消费' : '退款'
              }}
            </div>
          </template>
          <template #icon>
            <div class="wallet-img">
              <img v-if="item.type == 3" src="@/assets/image/recharge.png" style="width: 100%" />
              <img v-else-if="item.type == 1" src="@/assets/image/consumption.png" style="width: 100%" />
              <img v-else src="@/assets/image/refund.png" style="width: 100%" />
            </div>
          </template>
        </van-cell>
      </van-cell-group>
    </van-list>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { payPhoneApi } from '@haierbusiness-front/apis';
import { isMobile } from '@haierbusiness-front/utils';
import dayjs from 'dayjs';
import { getCurrentRouter } from '@haierbusiness-front/utils';
import { showFailToast } from 'vant';

const router = getCurrentRouter();
const route = router.currentRoute.value;

const totalData = ref({});
const personalWelfareAccountFlowingWaterTotal = async () => {
  const res = await payPhoneApi.personalWelfareAccountFlowingWaterTotal({
    type: pageConfig.value.type ? pageConfig.value.type : 0,
    changeBegin: pageConfig.value.changeBegin,
    changeEnd: pageConfig.value.changeEnd,
  });
  totalData.value = res;
};
const personalWelfareAccountList = async () => {
  const res = await payPhoneApi.personalWelfareAccountList({
    ...pageConfig.value,
  });

  list.value = res.records;
  if (list.value.length == res.total) {
    finished.value = true;
  }
};

const soucePath = ref('');
onMounted(() => {
  if (!isMobile()) {
    showFailToast('为了更好的体验，请在移动端ihaier上打开此链接！')
  }
  soucePath.value = route.path;
  personalWelfareAccountList();
  personalWelfareAccountFlowingWaterTotal();
});
const activeName = ref<string>('voucher');
const onClickLeft = () => {
  router.go(-1);
};

const date = ref('2023/01/01-2023/01/31');
const itemRef = ref(null);

const onCalendarConfirm = async (e) => {
  dateTitle.value = `${dayjs(e[0]).format('YYYY/MM/DD')}-${dayjs(e[1]).format('YYYY/MM/DD')}`;
  pageConfig.value.pageNum = 1;
  pageConfig.value.changeBegin = dayjs(e[0]).format('YYYY-MM-DD');
  pageConfig.value.changeEnd = dayjs(e[1]).format('YYYY-MM-DD');
  list.value = [];
  personalWelfareAccountFlowingWaterTotal();
  finished.value = false;
  personalWelfareAccountList();
  // const res = await payPhoneApi.personalWelfareAccountList({
  //   ...pageConfig.value,
  // });

  // list.value = res.records;
  itemRef.value.toggle();
};

const value1 = ref(0);
const option1 = [
  { text: '全部账单', value: 0 },
  { text: '充值', value: 3 },
  { text: '消费', value: 1 },
  { text: '退款', value: 2 },
];

const list = ref([]);
const loading = ref(false);
const finished = ref(false);

const dateTitle = ref(
  `${dayjs().startOf('month').format('YYYY/MM/DD')}-${dayjs().endOf('month').format('YYYY/MM/DD')}`,
);
const pageConfig = ref({
  pageSize: 10,
  pageNum: 1,
  type: null,
  changeBegin: dayjs().startOf('month').format('YYYY-MM-DD'),
  changeEnd: dayjs().endOf('month').format('YYYY-MM-DD'),
});

const onDropChange = async (e) => {
  pageConfig.value.pageNum = 1;
  pageConfig.value.type = e != 0 ? e : null;
  list.value = [];
  personalWelfareAccountFlowingWaterTotal();

  finished.value = false;
  personalWelfareAccountList();
};

const onLoad = async () => {
  pageConfig.value.pageNum = pageConfig.value.pageNum + 1;
  const res = await payPhoneApi.personalWelfareAccountList({ ...pageConfig.value });

  if (res.records) {
    list.value = list.value.length > 0 ? list.value.concat(res.records) : res.records;
  }
  // 加载状态结束
  loading.value = false;

  // 数据全部加载完成
  console.log(list.value.length, res.total, '1111');
  if (list.value.length == res.total) {
    finished.value = true;
  }
};
</script>
<style lang="scss" scoped>
.wallet {
  background: #f3f3f3;
  // min-height: 100vh;
  
  &-total {
    display: flex;
    justify-content: end;
    align-items: center;
    height: 50px;
    padding: 0 15px;
    font-size: 12px;
    color: #666;
  }
  &-con {
    height: calc(100vh - 144px);
    overflow: auto;
  }
  &-group {
    margin-bottom: 10px;
    .bill-item {
      position: relative;
      padding-top: 16px;
      .billNo {
        position: absolute;
        top: 0px;
        right: 5px;
        opacity: 0.9;
        font-size: 10px;
      }
    }
  }

  &-money {
    color: #36b876;
    font-size: 14px;
  }

  &-consumption-money {
    color: #333333;
    font-size: 14px;
  }
  &-refund-money {
    color: #ff4d4f;
    font-size: 14px;
  }
  &-img {
    // border-radius: 100%;
    overflow: hidden;
    width: 36px;
    height: 36px;
    margin-right: 12px;
  }
}
.p-15 {
  padding: 15px;
}

:deep(.van-cell) {
  align-items: center;
}

:deep(.van-cell__title) {
  flex: 3;
}
</style>
