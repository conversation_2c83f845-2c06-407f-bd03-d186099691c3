<template>
  <div>
    <div v-for="(item, i) in props.data" :key="i">
      <template v-if="item == 'ChinaMap'">
        <component :is="components.get(item)"></component>
      </template>
      <template v-else-if="item == 'BusinessTrend'">
        <div class="col-title col-title-l mt-10">
          {{ getName(item) }}
          <div class="col-title-extra">
            <span
              class="button"
              v-for="(tab, index) in tabs"
              :key="index"
              @click="active = index"
            >
              {{ tab }}
            </span>
            <img
              :style="{
                transform: `translate(${active * 52}px)`,
              }"
              class="active"
              src="@/assets/image/bigscreen/icon-title-btn-acitve.png"
              alt=""
            />
          </div>
        </div>
        <div class="col-chart">
          <component :is="components.get(item)" :date-type="active"></component>
        </div>
      </template>

      <template v-else>
        <div class="col-title">{{ getName(item) }}</div>
        <div class="col-chart">
          <component :is="components.get(item)" :height="33"> </component>
        </div>
      </template>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, markRaw, defineAsyncComponent } from "vue";



const components = markRaw(new Map<string, any>);
//组件Accumulative
components.set(
    'Accumulative',
    defineAsyncComponent(() => import('./accumulative.vue')),
);
components.set(
    'AccumulativeSlim',
    defineAsyncComponent(() => import('./accumulativeSlim.vue')),
);
components.set(
    'SeatPercentage',
    defineAsyncComponent(() => import('./seatPercentage.vue')),
);
components.set(
    'JourneyRank',
    defineAsyncComponent(() => import('./journeyRank.vue')),
);

components.set(
    'PayType',
    defineAsyncComponent(() => import('./payType.vue')),
);

components.set(
    'ChangeAndRefund',
    defineAsyncComponent(() => import('./changeAndRefund.vue')),
);
components.set(
    'SettleRank',
    defineAsyncComponent(() => import('./settleRank.vue')),
);
components.set(
    'BusinessTrend',
    defineAsyncComponent(() => import('./businessTrend.vue')),
);
components.set(
    'ChinaMap',
    defineAsyncComponent(() => import('./chinaMap.vue')),
);


const tabs = ["年", "月", "日"];
const active = ref(1);

const props = defineProps({
    data: Array<string>,
})

// 获取name
const getName = (status: number | string) => {
    const resultMap: any = {
        'SeatPercentage': "坐席等级分布",
        'JourneyRank': "行程排行Top20",
        'Accumulative': "累计成交",
        'AccumulativeSlim': "累计成交",
        'PayPlatform': "支付平台",
        'HotelPrice': "酒店价格分布",
        'BudgetRank': "预算部门排行Top10",
        'HotelRank': "酒店销售排行Top10",
        'SettleRank': "结算单位排行Top10",
        'Percentage': "用车服务类型分布",
        'CityRank': '用车服务城市排行Top20',
        'PayType': "支付类型与平台",
        'BusinessTrend': "火车票业务趋势",
        'ChangeAndRefund': '退改情况',
        default: "",
    };
    return resultMap[status] || resultMap.default;
};
</script>
<style scoped lang="less">
@import url(../../../main.less);

.mt-10 {
  margin-top: 10px;
}
</style>
