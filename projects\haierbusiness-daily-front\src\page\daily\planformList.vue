<script setup lang="ts">
import {
  Modal,
  TabPane as hTabPane,
  Tabs as hTabs,
  Tag as hTag,
  Select as hSelect,
  But<PERSON> as hButton,
  Col as hCol,
  FloatButton as hFloatButton,
  DatePicker as hDatePicker,
  Row as hRow,
  Table as hTable,
} from 'ant-design-vue';
import dayjs, {Dayjs} from 'dayjs';
import {ColumnType} from 'ant-design-vue/lib/table/interface';
import {PlusOutlined, SearchOutlined} from '@ant-design/icons-vue';
import {
  AnnualPlanTypeStateConstant,
  BalanceStatusConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
  UserGroupSystemConstant,
} from '@haierbusiness-front/common-libs';
import {checkUserGroups, getCurrentRouter} from '@haierbusiness-front/utils';
import {ref, computed, watch, provide, inject} from 'vue';
import {dailyTypeApi} from '@haierbusiness-front/apis/src/daily/type/type';
import {
  IAnnualPlanTypeListRequest,
  IDailyDeptListRequestDTO,
  IDailyDeptResponse,
  IDailyReportListRequestDTO,
  IDailyReportListResponseDTO,
  IMonthPlanListRequestDTO,
  IMonthPlanListResponseDTO,
} from '@haierbusiness-front/common-libs/src/daily';
import planformDayCard from './planformDayCard.vue';
import {dailyAnnualPlanApi} from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import {storeToRefs} from 'pinia';
import {applicationStore} from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
import {dailyReportApi} from '@haierbusiness-front/apis';

const router = getCurrentRouter();
const {loginUser} = storeToRefs(applicationStore(globalPinia));
const prop = defineProps({
  query: Object,
});

watch([() => prop.query?.refresh], () => {
  if (prop.query?.refresh) {
    listApiRun();
  }
});

watch(
    () => prop.query,
    (val: any) => {
      searchListParam.value = {
          year: String(currentDay.value?.format('YYYY')),
          month: String(currentDay.value?.format('MM')),
          day: String(currentDay.value?.format('DD')),
        };
      listData.value = []
      listApiRun()
    },

    {
      deep: true,
    },
);
const currentDay = ref<Dayjs>(dayjs());
const searchListParam = ref<IDailyReportListRequestDTO>({
  year: String(currentDay.value?.format('YYYY')),
  month: String(currentDay.value?.format('MM')),
  day: String(currentDay.value?.format('DD')),
});
const listLoading = ref(false);
const listData = ref<IDailyReportListResponseDTO[]>([]);
const listApiRun = () => {
  listLoading.value = true;
  dailyReportApi
      .list(searchListParam.value)
      .then((it) => {
        listData.value = it;
      })
      .finally(() => {
        listLoading.value = false;
      });
};
listApiRun();

const changeDay = (current: any) => {
  (searchListParam.value.year = String(current.format('YYYY'))),
      (searchListParam.value.month = String(current.format('MM'))),
      (searchListParam.value.day = String(current.format('DD'))),
      listApiRun();
};
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <h-row :align="'middle'">
      <h-col
          :span="4"
          style="text-align: center; margin-bottom: 23px; font-size: 16px; border-bottom: 1px solid rgb(221, 221, 221)"
      >
        <h-date-picker style="margin-bottom: 6px" v-model:value="currentDay" @change="changeDay"/>
      </h-col>
      <h-col
          :span="16"
          style="text-align: center; margin-bottom: 23px; font-size: 16px; border-bottom: 1px solid rgb(221, 221, 221)"
      >
        <div style="font-weight: 700; height: 30px; margin-top: 8px"></div>
      </h-col>
      <h-col
          :span="4"
          style="text-align: center; margin-bottom: 23px; font-size: 16px; border-bottom: 1px solid rgb(221, 221, 221)"
      >
        <div style="font-weight: 700; height: 30px; margin-top: 8px"></div>
      </h-col>
    </h-row>
    <h-row :align="'middle'">
      <h-col v-if="listData && listData.length > 0" :span="6" v-for="(domain, index) in listData">
        <planform-day-card :data="domain" ></planform-day-card>
      </h-col>
      <div
          v-else
          style="
          display: flex;
          align-items: center;
          justify-content: center;
          height: 400px;
          width: 100%;
          color: rgb(163, 163, 163);
          font-size: 18px;
          font-weight: 500;
        "
      >
        当前日清不存在
      </div>
    </h-row>
  </div>
</template>

<style scoped lang="less"></style>
