<template>
  <div class="page-container">
    <div class="content-wrapper">
      <!-- 发票上传信息区域 -->
      <div class="scheme_info">
        <div class="interact_mice_title">
          <div class="interact_mice_name_img mr8"></div>
          <div class="interact_mice_name mr24">
            {{ demandInfo.miceName || '' }}
          </div>
        </div>
        <div class="interact_mice_num mt12">
          <span class="mr10">会议编号：{{ demandInfo.mainCode }}</span>
          <img @click="getCopy(demandInfo.mainCode)" src="@/assets/image/scheme/copy_blue.png" width="16" />
        </div>

        <a-row class="interact_mice_info mt24">
          <a-col :span="12">
            <span class="mice_info_title mice_info_person_img">会议人数：</span>
            <span class="mice_info_value">
              {{ demandInfo.personTotal ? demandInfo.personTotal + '人' : '-' }}
            </span>
          </a-col>
          <a-col :span="12">
            <span class="mice_info_title mice_info_type_img">会议类型：</span>
            <span class="mice_info_value">
              {{ demandInfo.miceType ? MiceTypeConstant.ofType(demandInfo.miceType)?.desc || '' : '-' }}
            </span>
          </a-col>

          <a-col :span="12" class="mt12">
            <span class="mice_info_title mice_info_time_img">需求开始时间：</span>
            <span class="mice_info_value">
              {{ demandInfo.startDate || '' }}
            </span>
          </a-col>
          <a-col :span="12" class="mt12">
            <span class="mice_info_title mice_info_time_img">需求结束时间：</span>
            <span class="mice_info_value">
              {{ demandInfo.endDate || '' }}
            </span>
          </a-col>
        </a-row>
      </div>

      <!-- 平台发票上传表格 -->
      <div class="invoice-content">
        <div class="invoice-section">
          <h3 class="section-title">平台发票上传</h3>

          <!-- 发票列表表格 -->
          <div class="info-table-wrapper invoice-table">
            <div class="table-header">
              <div class="col-date">发票日期</div>
              <div class="col-number">发票号</div>
              <div class="col-amount">发票金额</div>
              <div class="col-operation">操作</div>
            </div>
            <div class="table-body">
              <div v-for="(item, index) in invoiceList" :key="item.key" class="table-row">
                <!-- 发票日期 -->
                <div class="col-date">
                  <h-date-picker
                    v-model:value="item.invoiceDate"
                    placeholder="请选择日期"
                    size="small"
                    class="borderless-input"
                    :bordered="false"
                    format="YYYY.M.D"
                  />
                </div>
                <!-- 发票号 -->
                <div class="col-number">
                  <h-input
                    v-model:value="item.invoiceNumber"
                    placeholder="请输入发票号"
                    size="small"
                    class="borderless-input"
                    :bordered="false"
                  />
                </div>
                <!-- 发票金额 -->
                <div class="col-amount">
                  <h-input-number
                    v-model:value="item.invoiceAmount"
                    :min="0"
                    :precision="2"
                    placeholder="请输入金额"
                    size="small"
                    class="borderless-input"
                    :bordered="false"
                  />
                </div>
                <!-- 操作 -->
                <div class="col-operation">
                  <h-button
                    type="link"
                    danger
                    @click="deleteInvoice(index)"
                    :icon="h(DeleteOutlined)"
                    size="small"
                  >
                  </h-button>
                </div>
              </div>

              <!-- 添加按钮行 -->
              <div v-if="invoiceList.length === 0" class="table-row add-row">
                <div class="add-button-full-width" @click="addInvoice">
                  <div class="demand_add">
                    <div class="demand_add_img mr8"></div>
                    <span>添加发票</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 固定底部按钮区域 -->
    <div class="footer-container">
      <h-button @click="handleCancel" style="margin-right: 10px">
        取消
      </h-button>
      <h-button type="primary" @click="handleSubmit">
        提交
      </h-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, h } from 'vue';
import dayjs, { Dayjs } from 'dayjs';
import {
  Button as hButton,
  Input as hInput,
  InputNumber as hInputNumber,
  DatePicker as hDatePicker,
  message,
  Row as aRow,
  Col as aCol
} from 'ant-design-vue';
import { DeleteOutlined } from '@ant-design/icons-vue';
import { useRouter, useRoute } from 'vue-router';
import { DemandSubmitObj, ProcessNode, MiceTypeConstant } from '@haierbusiness-front/common-libs';
import { miceBidManOrderListApi } from '@haierbusiness-front/apis';

// 发票项目接口
interface InvoiceItem {
  key: string;
  invoiceDate: Dayjs | null;
  invoiceNumber: string;
  invoiceAmount: number | null;
}

const router = useRouter();
const route = useRoute();

// 复制文本到剪贴板
const getCopy = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    message.success('复制成功！');
  } catch (err) {
    message.success('复制失败');
  }
};

// 需求信息数据
const demandInfo = ref({
  miceName: '',
  mainCode: '',
  personTotal: null,
  miceType: null,
  startDate: '',
  endDate: ''
});

// 发票列表数据
const invoiceList = ref<InvoiceItem[]>([]);

// 添加发票
const addInvoice = () => {
  // 限制最多只能添加一个发票
  if (invoiceList.value.length >= 1) {
    message.warning('最多只能添加一个发票');
    return;
  }
  
  const newKey = (invoiceList.value.length + 1).toString();
  invoiceList.value.push({
    key: newKey,
    invoiceDate: null,
    invoiceNumber: '',
    invoiceAmount: null,
  });
  message.success('已添加新发票行');
};

// 删除发票
const deleteInvoice = (index: number) => {
  invoiceList.value.splice(index, 1);
  message.success('发票已删除');
};

// 获取格式化的提交数据
const getSubmitData = () => {
  return {
    demandInfo: {
      miceName: demandInfo.value.miceName,
      mainCode: demandInfo.value.mainCode,
      personTotal: demandInfo.value.personTotal,
      miceType: demandInfo.value.miceType,
      startDate: demandInfo.value.startDate,
      endDate: demandInfo.value.endDate,
    },
    invoiceList: invoiceList.value.map((item, index) => ({
      序号: index + 1,
      发票日期: item.invoiceDate ? item.invoiceDate.format('YYYY-MM-DD') : '',
      发票号: item.invoiceNumber,
      发票金额: item.invoiceAmount,
      原始数据: {
        key: item.key,
        invoiceDate: item.invoiceDate,
        invoiceNumber: item.invoiceNumber,
        invoiceAmount: item.invoiceAmount,
      }
    })),
    统计信息: {
      发票总数: invoiceList.value.length,
      发票总金额: invoiceList.value.reduce((sum, item) => sum + (item.invoiceAmount || 0), 0),
      已填写完整的发票数: invoiceList.value.filter(item =>
        item.invoiceDate && item.invoiceNumber && item.invoiceAmount
      ).length,
    }
  };
};

// 获取提交数据 - 后端期望的对象格式
const getApiSubmitData = () => {
  // 获取路由参数中的 miceId
  const { miceId } = route.query;

  return {
    mainCode: demandInfo.value.mainCode,           // 订单号
    miceId: miceId ? parseInt(miceId as string) : 0, // 主表id (转换为整数)
    invoiceList: invoiceList.value.map(item => ({
      invoiceDate: item.invoiceDate ? item.invoiceDate.format('YYYY-MM-DD') : '', // 发票日期
      invoiceNumber: item.invoiceNumber || '',       // 发票号
      invoiceAmount: item.invoiceAmount || 0         // 发票金额
    }))
  };
};

// 处理取消操作
const handleCancel = () => {
  router.back();
};

// 处理提交操作
const handleSubmit = async () => {
  // 验证数据
  if (invoiceList.value.length === 0) {
    message.warning('请至少添加一条发票记录');
    return;
  }

  // 验证每条记录的必填字段
  const invalidItems = invoiceList.value.filter(item =>
    !item.invoiceDate || !item.invoiceNumber || !item.invoiceAmount
  );

  if (invalidItems.length > 0) {
    message.warning('请完善所有发票信息（日期、发票号、金额都为必填项）');
    return;
  }

  // 获取提交数据
  const submitData = getApiSubmitData();

  // 打印提交的数据结构
  console.log('=== 提交给后端的数据 ===');
  console.log('mainCode (订单号):', submitData.mainCode);
  console.log('miceId (主表id):', submitData.miceId);
  console.log('invoiceList (发票列表):');
  submitData.invoiceList.forEach((invoice, index) => {
    console.log(`发票 ${index + 1}:`, {
      invoiceDate: invoice.invoiceDate,     // 发票日期
      invoiceNumber: invoice.invoiceNumber, // 发票号
      invoiceAmount: invoice.invoiceAmount  // 发票金额
    });
  });

  try {
    // 调用发票提交API
    const response = await miceBidManOrderListApi.invoiceSubmit(submitData);

    if (response.success) {
      message.success(`提交成功！共提交 ${submitData.invoiceList.length} 条发票记录`);
      // 可以选择返回上一页或刷新数据
      // router.back();
    } else {
      message.error(response.message || '提交失败，请重试');
    }
  } catch (error) {
    console.error('提交发票失败:', error);
    message.error('提交失败，请检查网络连接后重试');
  }
};

onMounted(() => {
  // 组件挂载后的初始化逻辑
  console.log('Invoice uploader mounted');

  // 获取路由参数
  const { mainCode, miceId, record } = route.query;

  // 如果有 record 参数，解析完整的订单数据
  if (record) {
    try {
      const orderData = JSON.parse(decodeURIComponent(record as string));
      console.log('获取到的完整订单数据:', orderData);

      // 使用订单数据初始化 demandInfo
      demandInfo.value = {
        miceName: orderData.miceName || '',
        mainCode: orderData.mainCode || '',
        personTotal: orderData.personTotal || null,
        miceType: orderData.miceType || null,
        startDate: orderData.startDate || '',
        endDate: orderData.endDate || ''
      };
    } catch (error) {
      console.error('解析订单数据失败:', error);
      // 如果解析失败，使用基本参数
      if (mainCode) {
        demandInfo.value.mainCode = mainCode as string;
      }
    }
  } else {
    // 如果没有 record，使用基本参数
    if (mainCode) {
      demandInfo.value.mainCode = mainCode as string;
    }
  }

  console.log('初始化后的需求信息:', demandInfo.value);
});
</script>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  position: relative;
}

.content-wrapper {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  margin-bottom: 60px;
  /* 给底部按钮留出空间 */
}

.invoice-content {
  margin-top: 24px;
}

.invoice-section {
  background: #fff;
  border-radius: 6px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #1d2129;
  margin: 0 0 20px 0;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;
}

.invoice-table-container {
  margin-bottom: 16px;
}

.add-invoice-container {
  display: flex;
  justify-content: flex-start;
}

.add-invoice-btn {
  width: 200px;
  height: 40px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: all 0.3s;
}

.add-invoice-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

/* 表格样式 - 模仿 billUploadschemeInvoice.vue */
.info-table-wrapper {
  width: 100%;
  border: none;
  // border-bottom: 1px solid #d9d9d9;
  border-radius: 0;
  margin-bottom: 0;

  &.invoice-table {
    width: 100%;
  }

  .table-header {
    display: flex;
    background-color: #fafafa;
    font-weight: 500;
    font-size: 14px;
    color: #333;

    > div {
      padding: 12px 8px;
      text-align: center;
      border-right: 1px solid #d9d9d9;

      &:last-child {
        border-right: none;
      }
    }

    .col-date {
      width: 200px;
    }
    .col-number {
      width: 200px;
    }
    .col-amount {
      width: 200px;
    }
    .col-operation {
      width: 100px;
    }
  }

  .table-body {
    .table-row {
      display: flex;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      &.add-row {
        border-bottom: none;

        .add-button-full-width {
          width: 100%;
          padding: 12px 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 60px;
          cursor: pointer;
          border-bottom: none;

          &:hover {
            background-color: #f5f5f5;
          }

          .demand_add {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #1890ff;
            font-size: 14px;

            .demand_add_img {
              width: 16px;
              height: 16px;
              background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMVY4TTE1IDhIOE04IDE1VjhNMSA4SDgiIHN0cm9rZT0iIzE4OTBGRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+') no-repeat center;
              background-size: contain;
            }
          }
        }
      }

      > div {
        padding: 12px 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 60px;
        border-right: 1px solid #f0f0f0;

        &:last-child {
          border-right: none;
        }
      }

      .col-date {
        width: 200px;
      }
      .col-number {
        width: 200px;
      }
      .col-amount {
        width: 200px;
      }
      .col-operation {
        width: 100px;
      }
    }
  }
}

/* 无边框输入框样式 */
.borderless-input {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;

  &:focus,
  &:hover {
    border: none !important;
    box-shadow: none !important;
  }

  .ant-input {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
  }

  .ant-picker-input > input {
    border: none !important;
    box-shadow: none !important;
  }
}

.mr8 {
  margin-right: 8px;
}

/* 全局样式覆盖 */
:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-picker) {
  width: 100%;
}

/* 表格内输入框样式 */
:deep(.ant-table-tbody .ant-input) {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 4px 8px;
}

:deep(.ant-table-tbody .ant-input:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 表格行高度调整 */
:deep(.ant-table-tbody > tr > td) {
  padding: 8px 12px;
}

.footer-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 24px;
  border-top: 1px solid #f0f0f0;
  background-color: #fff;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

/* 发票上传信息区域样式 */
.scheme_info {
  padding: 24px 32px;
  width: 100%;
  height: 40%;
  background: url('@/assets/image/scheme/mice_bgc.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border-radius: 6px;

  .interact_mice_title {
    display: flex;
    align-items: center;

    .interact_mice_name_img {
      width: 28px;
      height: 28px;
      background-image: url('@/assets/image/scheme/mice_name.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .interact_mice_name {
      font-family: PingFangSCSemibold, PingFangSCSemibold;
      font-weight: normal;
      font-size: 20px;
      color: #1d2129;
      line-height: 28px;
    }

    .interact_mice_type {
      width: 108px;
      height: 28px;
      line-height: 28px;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #ffffff;
      background: #1868db;
      border-radius: 4px;
    }
  }

  .interact_mice_num {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #86909c;
    line-height: 20px;

    img {
      cursor: pointer;
    }
  }

  .interact_mice_info {
    width: 50%;
    font-size: 14px;
    color: #86909c;
    line-height: 20px;

    .mice_info_title {
      display: inline-block;
      text-indent: 26px;
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center left;
    }

    .mice_info_person_img {
      background-image: url('@/assets/image/scheme/mice_person.png');
    }

    .mice_info_type_img {
      background-image: url('@/assets/image/scheme/mice_type.png');
    }

    .mice_info_time_img {
      background-image: url('@/assets/image/scheme/mice_time.png');
    }

    .mice_info_value {
      color: #1d2129;
    }
  }
}

/* 通用样式类 */
.mt12 {
  margin-top: 12px;
}

.mt24 {
  margin-top: 24px;
}

.mr10 {
  margin-right: 10px;
}

.mr24 {
  margin-right: 24px;
}
</style>
