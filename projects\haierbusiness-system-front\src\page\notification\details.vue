<script setup lang="ts">
import {
  DatePicker as hDatePicker,
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Table as hTable,
  BreadcrumbItem as hBreadcrumbItem,
  Breadcrumb as hBreadcrumb,
  LayoutSider as hLayoutSider,
  LayoutFooter as hLayoutFooter,
  LayoutContent as hLayoutContent,
  LayoutHeader as hLayoutHeader,
  Layout as hLayout,
  MenuItem as hMenuItem,
  MenuItemGroup as hMenuItemGroup,
  SubMenu as hSubMenu,
  Menu as hMenu,
  Divider as hDivider,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Result as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Tabs as hTabs,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  Textarea as hTextarea,
  Image as hImage,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem,
  message,
  TableProps
} from "ant-design-vue";
import { computed, onMounted, ref, inject, watch } from "vue";
import {
  UploadOutlined,
  UserOutlined,
  NotificationOutlined,
  AppstoreOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PlusOutlined,
  SearchOutlined
} from "@ant-design/icons-vue";
import { notificationApi } from "@haierbusiness-front/apis";
import { usePagination, useRequest } from "vue-request";
import {
  GetByIdRes,
  sendStatusConstant,
  approvalStatusConstant
} from "@haierbusiness-front/common-libs";
const processUrl = import.meta.env.VITE_PROCESS_URL
import Card from "../card/index.vue"
import { getCurrentRoute, routerParam } from "@haierbusiness-front/utils";
const route = getCurrentRoute();
const details = ref<GetByIdRes>({});
const frameModel = inject<any>("frameModel");
const roleList = ref<any>([]);
const readFlag = ref<string>("");
const searchForm = ref<any>({})
const columns = [
  {
    title: "员工姓名",
    dataIndex: "nickName"
  },
  {
    title: "员工工号",
    dataIndex: "userCode"
  },
  {
    title: "员工角色",
    dataIndex: "userRoleName"
  },
  {
    title: "发送状态",
    dataIndex: "sendFlag"
  },
  {
    title: "阅读状态",
    dataIndex: "readFlag"
  }
];
const visible = ref<boolean>(false);
const { data, run: listRun, loading, current, pageSize } = usePagination(
  notificationApi.getReadDetail
);
const dataSource = computed(() => data.value?.records || []);
const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: "center" }
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any
) => {
  listRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
    notificationId: currentRoute.value.query.id,
    ...searchForm.value
  });
};

watch(readFlag, () => {
  listRun({
    pageNum: 1,
    pageSize: 10,
    notificationId: currentRoute.value.query.id,
    ...searchForm.value
  });
});

// 获取详情
const getInfo = (id: string) => {
  notificationApi.getInfoById(id).then(res => {
    details.value = res;
  });
};
// 查看阅读情况
const openVisible = () => {
  visible.value = true;
  listRun({
    pageNum: 1,
    pageSize: 10,
    notificationId: currentRoute.value.query.id,
    ...searchForm.value
  });
};
// 获取所有接收角色
const getAllRole = () => {
  notificationApi.getAllRole().then(res => {
    roleList.value = res;
  });
};
const currentRoute = ref<any>(null);

const exportDetails = () => {
  notificationApi.exportReadDetail({
    notificationId: currentRoute.value.query.id,
    ...searchForm.value
  });
};
const rest = () =>{
  searchForm.value = {}
  listRun({
    pageNum: 1,
    pageSize: 10,
    notificationId: currentRoute.value.query.id,
    ...searchForm.value
  });
}
onMounted(async () => {
  currentRoute.value = await route;

  if (route?.query?.onlydetails) {
    frameModel.value = 1;
  }
  getInfo(currentRoute.value.query.id);
  getAllRole();
});
</script>

<template>
  <div
    style="background-color: #ffff;height: 100%;width: 100%;padding: 60px 60px 0px 60px;overflow: auto;"
  >
    <h-descriptions :column="2" title="应用机器人消息通知">
      <h-descriptions-item label="通知标题">{{details.title}}</h-descriptions-item>
      <h-descriptions-item label="发送状态">{{sendStatusConstant[details.sendStatus]}}</h-descriptions-item>
      <h-descriptions-item label="经办人">{{details.operatorName}}({{details.operator}})</h-descriptions-item>
      <h-descriptions-item
        label="接收角色"
      >{{roleList.find(obj => obj.roleCode ===details.receiverRole)?.roleName}}</h-descriptions-item>
      <h-descriptions-item label="预计发送时间">{{details.scheduledTime}}</h-descriptions-item>
      <h-descriptions-item label="应发/实发/已读">
        {{details.readDesc}}
        <h-button style="margin-left:10px;" @click="openVisible" size="small">查看明细</h-button>
      </h-descriptions-item>
      <h-descriptions-item label="实际发送时间">{{details.actualSendTime}}</h-descriptions-item>
      <h-descriptions-item label="审批状态">{{approvalStatusConstant[details.approvalStatus]}}</h-descriptions-item>
      <h-descriptions-item label="通知模版" :span="2">{{details.templateName}}</h-descriptions-item>
      <h-descriptions-item label="正文内容" :span="2">{{details.content}}</h-descriptions-item>
      <h-descriptions-item label="跳转链接" :span="2">
        <a :href="details.linkUrl"  target="_blank">{{details.linkUrl}}</a>
      </h-descriptions-item>
      <h-descriptions-item label="图片内容" :span="2">
        <!-- <img :src="'data:image/png;base64,'+details.imageBase64" alt=""> -->
        <h-image
          v-if="details.imageBase64"
          :width="200"
          :src="'data:image/png;base64,'+details.imageBase64"
        />
      </h-descriptions-item>
      <h-descriptions-item label="卡片预览" :span="2">
        <Card style="margin-left:24px;" :addForm="details" />
      </h-descriptions-item>
    </h-descriptions>
      <a-card v-if="frameModel!=1" title="审批流程" style="width: 100%">
          <iframe class="iframe" v-if="details.processCode" :key="processUrl + `?code=${details.processCode}#/detailsPcSt`" :src="processUrl + `?code=${details.processCode}#/detailsPcSt`" frameborder="0"></iframe>
      </a-card>
    <h-modal
      v-model:open="visible"
      title
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      :footer="false"
      width="1000px"
    >
      <h-row :align="'middle'" style="padding: 24px 10px 24px 10px;">
        <!-- <h-col :span="22"> -->
          <!-- <a-radio-group v-model:value="readFlag" style="margin-bottom: 16px">
            <a-radio-button value>全部</a-radio-button>
            <a-radio-button value="1">已读</a-radio-button>
            <a-radio-button value="0">未读</a-radio-button>
          </a-radio-group> -->
        <!-- </h-col> -->
        <h-col :span="3" style="text-align: right;padding-right: 10px;">
            <label for="sendFlag">发送状态：</label>
          </h-col>
          <h-col :span="5">
            <h-select
              ref="select"
              v-model:value.prop="searchForm.sendFlag"
              style="width: 100%"
              allow-clear
            >
              <h-select-option value="0">发送失败</h-select-option>
              <h-select-option value="1">发送成功</h-select-option>
              <h-select-option value="2">未发送</h-select-option>
            </h-select>
          </h-col>
        <h-col :span="3" style="text-align: right;padding-right: 10px;">
            <label for="readFlag">阅读状态：</label>
          </h-col>
          <h-col :span="5">
            <h-select
              ref="select"
              v-model:value.prop="searchForm.readFlag"
              style="width: 100%"
              allow-clear
            >
              <h-select-option value="0">未读</h-select-option>
              <h-select-option value="1">已读</h-select-option>
            </h-select>
          </h-col>
        <h-col :span="8" style="text-align: right;">
          <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
            <h-button @click="rest" style="margin-left: 10px">重置</h-button>
          <h-button  style="margin-left: 10px" @click="exportDetails">导出</h-button>
        </h-col>
      </h-row>
      <h-table
        :columns="columns"
        :row-key="record => record.id"
        :data-source="dataSource"
        :pagination="pagination"
        :loading="loading"
        @change="handleTableChange($event as any)"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'sendFlag'">{{ record.sendFlag==1?'发送成功':record.sendFlag==0?'发送失败':'未发送' }}</template>
          <template v-if="column.dataIndex === 'readFlag'">{{ record.readFlag==1?'已读':'未读' }}</template>
        </template>
      </h-table>
    </h-modal>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.title {
  width: 100%;
  text-align: center;
  font-size: 28px;
  margin: 20px 0;
  font-weight: 600;
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}
.iframe{
  width: 100%;
}
.contentBox {
  word-break: break-all;
}
:deep(.ant-card-bordered .ant-card-cover) {
      padding: 16px 16px 0 16px;
}
:deep(.ant-card){
    border-radius: 8px;
}
:deep(.ant-card-head) {
    background-color: #e0e9ff;
    color: #1456f0;
    border-radius: 8px 8px 0 0;
    font-size: 16px;
    line-height: 24px;
    padding: 0px 12px;
}
:deep(.ant-card-head-title){
 white-space: normal !important;
}
.imgBox {
  width: 100%;
  img {
    width: 100%;
  }
}
</style>
