<template>
  <div
    style="height: 78.2vh"
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0)"
    background="rgba(0,0,0,0)"
  >
    <div id="map-food">
      <div class="map-desc">
        <el-tooltip placement="top" effect="light">
          <template #content>
            <p style="color: #fff">
              <b>地图颜色标注：</b><br />
              <template v-for="item in colors">
                <span class="square" :style="{ background: item.color }"></span>：订单数量为{{ item.min }}以上<br />
              </template>
              <span class="square"></span>：无订单
            </p>
          </template>
          <p>
            地图颜色标注：
            <InfoCircleOutlined />
          </p>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { Scene, PolygonLayer, LineLayer, PointLayer, Popup } from '@antv/l7';
import { Mapbox } from '@antv/l7-maps';
import { ref, onMounted } from 'vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { queryOrderingFoodMapData } from '@haierbusiness-front/apis/src/data/board';
import { EventBus } from '../../eventBus';
import qingdao from '@/assets/geojson/370200.json';
import { ElTooltip } from 'element-plus';

const colors = [
  {
    min: 1000,
    color: '#012a9e',
  },
  {
    min: 500,
    color: '#0550ee',
  },
  {
    min: 0,
    color: '#29a3ff',
  },
];
const loading = ref(false);
let mapData = []; //地图上的业务数据
let chart;
EventBus.on((event, params) => {
  if (event == 'refresh') {
    if (!params) queryData();
    if (params && params.from != 'region_name') {
      queryData(params);
    }
    if (params && params.from == 'region_name') queryData();
  }
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
  loading.value = true;
  const data = await queryOrderingFoodMapData(params ? params.data.name : null, params ? params.from : null);
  console.log('订餐地图数据', data);
  mapData = data.rows;
  await getMap();
  loading.value = false;
};
//获取区域对应的数据
const getDataByCode = (code) => {
  const data = mapData.find((item) => item[1] == code);
  if (!data) return 0;
  return data[4] || 0;
};

//获取区域对应的数据
const getDataByCodeTotal = (code) => {
  const data = mapData.find((item) => item[1] == code);
  if (!data) return 0 + '%';
  let total = 0;
  mapData.forEach((item) => {
    total = total + item[4];
  });
  return ((data[4] / total) * 100).toFixed(2) + '%' || 0 + '%';
};
//生成地址
const getMap = async () => {
  chart && chart.destroy();
  const scene = new Scene({
    id: 'map-food',
    logoVisible: false,
    map: new Mapbox({
      pitch: 0,
      style: 'blank',
      maxZoom: 8,
      minZoom: 6,
    }),
  });
  scene.on('loaded', () => {
    addLayer(scene, qingdao);
    chart = scene;
  });
};
const payTypeCheck = ref<any>('');
//添加地图图层
const addLayer = (scene, mapJson) => {
  var labeldata = mapJson.features.map(function (fe) {
    fe.properties.code = fe.properties.adcode;
    fe.properties.value = fe.properties.name;
    fe.properties.longitude = fe.properties.centroid[0];
    fe.properties.latitude = fe.properties.centroid[1];
    return fe.properties;
  });
  //点图层
  const pointLayer = new PointLayer({ zIndex: 5 })
    .source(labeldata, {
      parser: {
        type: 'json',
        x: 'longitude',
        y: 'latitude',
      },
    })
    .size(8)
    .shape('name', 'text')
    .color('#fff');
  scene.addLayer(pointLayer);

  const polygonLayer = new PolygonLayer({ autoFit: true })
    .source(mapJson)
    .color('adcode', (adcode) => {
      const data = getDataByCode(adcode);
      if (!data) return 'rgba(239,243,255,0.05)';
      if (data >= 1000) {
        return '#012a9e';
      }
      if (data >= 500) {
        return '#0550ee';
      }
      return '#29a3ff';
    })
    .active(true)
    .shape('fill')
    .style({
      opacity: 1,
    });
  //图层边界
  const lineLayer = new LineLayer({ zIndex: 2 }).source(mapJson).color('rgb(93,112,146)').size(0.6).style({
    opacity: 1,
  });

  polygonLayer.on('mousemove', (param) => {
    if (param.feature.properties.name != payTypeCheck.value.name) {
      payTypeCheck.value = {
        name: param.feature.properties.name,
        adcode: param.feature.properties.adcode,
        lngLat: param.lngLat,
      };
      scene.removeLayer(lineLayerActive);
      showPop(scene, mapJson);
    }
  });

  //点击事件
  polygonLayer.on('click', (param) => {
    if (param.from != 'region_name' && param.name != payTypeCheck.value) {
      EventBus.emit('refresh', {
        from: 'region_name',
        ...param,
        data: {
          name: param.feature.properties.name,
        },
      });
      payTypeCheck.value = {
        name: param.feature.properties.name,
        adcode: param.feature.properties.adcode,
        lngLat: param.lngLat,
      };
    } else {
      payTypeCheck.value = '';
      EventBus.emit('refresh');
    }
  });
  showPop(scene, mapJson);

  scene.addLayer(polygonLayer);
  scene.addLayer(lineLayer);
};
let lineLayerActive;
const showPop = (scene, mapJson) => {
  if (payTypeCheck.value != '') {
    lineLayerActive = new LineLayer({ zIndex: 3 })
      .source({
        type: 'FeatureCollection',
        features: [],
      })
      .color('#ff6600')
      .size(1)
      .style({
        opacity: 1,
      });
    let mapJsonParam = {
      ...mapJson,
      features: mapJson.features.filter((item) => item.properties.adcode == payTypeCheck.value.adcode),
    };
    lineLayerActive.setData(mapJsonParam);
    scene.addLayer(lineLayerActive);

    const popup = new Popup({
      offsets: [0, 0],
      closeButton: false,
    })
      .setLnglat(payTypeCheck.value.lngLat)
      .setHTML(
        `<span>地区: ${payTypeCheck.value.name}</span><br><span>订单数量: ${getDataByCode(
          payTypeCheck.value.adcode,
        )}<br><span>订单占比: ${getDataByCodeTotal(payTypeCheck.value.adcode)}</span>`,
      );

    scene.addPopup(popup);
  }
};

onMounted(() => {
  queryData();
});
//添加地图上的点
// const addPoint = async () => {
//     const data = await queryHotelCityData(code);
//     const { rows } = data;
//     if (!rows) return;
//     const _data:any = [];
//     rows?.forEach((item) => {
//         if (!item[2]) return;
//         let jwd = item[2] || "";
//         jwd = jwd.split("/");
//         _data.push({
//             name: item[0],
//             code: item[1],
//             value: item[3],
//             longitude: jwd[0] - 0,
//             latitude: jwd[1] - 0,
//         });
//     });
//     // //点图层
//     const pointLayer = new PointLayer()
//         .source(_data, {
//             parser: {
//                 type: "json",
//                 x: "longitude",
//                 y: "latitude",
//             },
//         })
//         .shape("circle")
//         .size("value", [10, 50])
//         .color("color", (color) => {
//             // if(color=='red')return '#E8684A'
//             // if(color=='yellow')return '#F6BD16'
//             // if(color=='blue')return '#5B8FF9'
//             return "#edae5d";
//         })
//         .style({
//             opacity: 0.5,
//             strokeWidth: 1,
//         });
//     chart.addLayer(pointLayer);
//     const popup = new Popup({
//         offsets: [0, 0],
//         closeButton: false,
//     });
//     pointLayer.on("mousemove", (e) => {
//         const { feature } = e;
//         popup.setLnglat(e.lngLat).setHTML(
//             `<div style="text-align:left;padding:10px 10px 5px;min-width:195px;max-width:200px">
//                         <p style="font-size:14px">${feature.name}<span style="color:#6ed1ff;font-size:12px"></span></p>
//                         <p style="font-size:12px">订单数：${feature.value}</p>
//                     </div>`
//         );
//         chart.addPopup(popup);
//     });
// };
</script>
<style scoped>
#map-food {
  width: 100%;
  height: 76vh;
  position: relative;
}

.map-desc {
  position: absolute;
  left: 10px;
  top: 10px;
  font-size: 14px;
  z-index: 99;
}

.tooltip-content {
  line-height: 1.5;
}

span.square,
span.circle {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin-right: 2px;
  background: rgba(239, 243, 255, 0.05);
}

span.circle {
  border-radius: 50%;
}
</style>
<style>
.l7-popup-anchor-bottom .l7-popup-tip {
  border-top-color: rgba(0, 11, 45, 0.8) !important;
}

.l7-popup-anchor-bottom .l7-popup-content {
  background: rgba(0, 11, 45, 0.8) !important;
}
</style>
