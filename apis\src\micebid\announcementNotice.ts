import { download, get, post } from '../request'
import {
    IAnnouncementNoticeFilter,
    IAnnouncementNotice,
    IPageResponse,
    Result
} from '@haierbusiness-front/common-libs'


export const announcementNoticeApi = {
    list: (params: IAnnouncementNoticeFilter): Promise<IPageResponse<IAnnouncementNotice>> => {
        return get('/mice-bid/api/allin/from/page', params)
    },

    //当前登陆人
    noticeList: (params: IAnnouncementNoticeFilter): Promise<IPageResponse<IAnnouncementNotice>> => {
        return get('/mice-bid/api/allin/from/inform', params)
    },

    details: (id: number): Promise<IAnnouncementNotice> => {
        return get('/mice-bid/api/allin/from/detail', {
            id
        })
    },

    read: (params: {}): Promise<IAnnouncementNotice> => {
        return post('/mice-bid/api/allin/from/confirm', params)
    },

    save: (params: IAnnouncementNotice): Promise<Result> => {
        return post('/mice-bid/api/allin/from/add', params)
    },

    edit: (params: IAnnouncementNotice): Promise<Result> => {
        return post('/mice-bid/api/allin/from/update', params)
    },
    // 公告通知删除
    remove: (id: number): Promise<Result> => {
        return post(`/mice-bid/api/allin/from/delete?id=${id}`, {})
    },
}
