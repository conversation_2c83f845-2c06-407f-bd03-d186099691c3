<script setup lang="ts">
import {
  Modal,
  Tag as hTag,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Table as hTable,
  RadioButton as hRadioButton,
  RadioGroup as hRadioGroup,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import {
  EyeOutlined,
  FormOutlined,
  SolutionOutlined,
  PlusOutlined,
  SyncOutlined,
  SearchOutlined,
  SaveOutlined,
  SubnodeOutlined,
  SafetyOutlined,
  RetweetOutlined,
  FundOutlined,
} from '@ant-design/icons-vue';
import {
  AnnualPlanStateConstant,
  AnnualPlanTypeStateConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
} from '@haierbusiness-front/common-libs';
import { getCurrentRouter, routerParam } from '@haierbusiness-front/utils';
import { ref, createVNode, PropType, watch, computed } from 'vue';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import {
  IAnnualPlanTypeListRequest,
  IDailyPersonalResponse,
  IDailyReportListResponseDTO,
  IMonthPlanDetailResponseDTO,
} from '@haierbusiness-front/common-libs/src/daily';
import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import VChart from 'vue-echarts';

import { message } from 'ant-design-vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { BarChart, PieChart, LineChart } from 'echarts/charts';
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components';
import dayjs, { Dayjs } from 'dayjs';
const router = getCurrentRouter();
use([CanvasRenderer, PieChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent, BarChart, LineChart]);
const prop = defineProps({
  data: Object as PropType<IDailyReportListResponseDTO>,
});
// wait ：待生效 master ：主记录
const modelData = ref('master');

// 修改modelData
const changeModel = () => {
  if (modelData.value === 'master') {
    modelData.value = 'wait';
  } else {
    modelData.value = 'master';
  }
  updateChartsData();
};

const dataColor = {
  color: ['rgb(230,232,234)', 'rgb(145,205,118)', 'rgb(238,102,102)'],
};
// 基础数据
// 总数
const itemCount = computed(() => prop.data?.itemCount || 0);
// 已录入
const entered = computed(() => prop.data?.completeItemCount || 0);
// 待录入
const wait = computed(() => itemCount.value - entered.value - needNot.value || 0);
// 无需录入
const needNot = computed(() => prop.data?.nonItemCount || 0);

const pieEchartsOption = ref({
  tooltip: {
    trigger: 'item',
  },
  ...dataColor,
  legend: {
    top: '0%',
    left: 'right',
    orient: 'verticalAlign',
  },
  series: [
    {
      left: 'center',
      center: ['25%', '50%'],
      name: 'Access From',
      type: 'pie',
      radius: ['50%', '80%'],
      label: {
        show: true,
        position: 'center',
        formatter: '0%',
        fontWeight: 'bold',
        fontSize: 12,
      },
      labelLine: {
        show: false,
      },
      data: [
        {
          value: 0,
          name: '无需录入',
        },
        { value: 0, name: '已录入' },
        { value: 0, name: '未录入' },
      ],
    },
  ],
});

const updateChartsData = () => {
  // 设置pie图
  pieEchartsOption.value.series[0].data = [
    {
      value: needNot.value,
      name: needNot.value + '',
    },
    { value: entered.value, name: entered.value + ' ' },
    { value: wait.value, name: wait.value + '  ' },
  ];
  pieEchartsOption.value.series[0].label.formatter =
    ((needNot.value + entered.value) / itemCount.value / 0.01).toFixed(0) + '%';
};
{
  updateChartsData();
}

const gotoDailyReport = () => {
  router.push({
    path: '/daily/daily/report',
    query: {  data: routerParam(prop.data) },
  });
};
</script>

<template>
  <div class="card">
    <div class="card-mask" @click="gotoDailyReport"></div>
    <v-chart :option="pieEchartsOption" :autoresize="true" />
  </div>
</template>

<style scoped lang="less">
.subscript {
  position: absolute;
  width: 23%;
  height: 8%;
  right: 2.5%;
  color: white;
  font-size: 12px;
  text-align: center;
  vertical-align: middle;
  border-bottom-left-radius: 5px;
  border-top-right-radius: 5px;
}

.card {
  overflow-y: hidden;
  overflow-x: hidden;
  text-align: center;
  height: calc(7vh);
  min-height: 80px;

  .content {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    height: 90%;
  }

  .footer {
    padding-top: 2px;
    text-align: center;
    font-weight: 700;
    font-size: 16px;
    border-top: 0.5px solid #eaeaea;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    background-color: rgb(247, 249, 250);
    height: 10%;
  }
}

.card-mask {
  border-radius: 5px;
  cursor: pointer;
  position: absolute;
  height: calc(7vh);
  min-height: 80px;
  width: 95%;
  z-index: 10;
}
</style>
