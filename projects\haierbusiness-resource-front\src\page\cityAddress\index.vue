<script setup lang="ts">
import {
  BreadcrumbItem as hBreadcrumbItem,
  Breadcrumb as hBreadcrumb,
  LayoutSider as hLayoutSider,
  LayoutFooter as hLayoutFooter,
  Layout<PERSON>ontent as hLayoutContent,
  Layout<PERSON>eader as hLayoutHeader,
  Layout as hLayout,
  MenuItem as hMenuItem,
  MenuItemGroup as hMenuItemGroup,
  SubMenu as hSubMenu,
  Menu as hMenu,
  Divider as hDivider,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Result as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Tabs as hTabs,
  message,
  RadioGroup as hRadioGroup,
  RadioButton as hRadioButton,
  Table as hTable,
  Modal as hModal,
  Tree as hTree,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Switch as hSwitch,
  Select  as hSelect,
  SelectOption as hSelectOption 
} from 'ant-design-vue';
import { onMounted, ref, computed, watch,createVNode } from 'vue';
import {
  UploadOutlined,
  UserOutlined,
  NotificationOutlined,
  AppstoreOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ExclamationCircleOutlined,
  SearchOutlined,
  BarsOutlined,
  ClusterOutlined
} from '@ant-design/icons-vue';
import EManage from '@haierbusiness-front/components/manange/EManange.vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { addressRes,DistrictProviderMapPageReq,addressSupplierType,addressLevel,addressType } from '@haierbusiness-front/common-libs';
import { addressApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import type { TreeProps } from 'ant-design-vue';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute, getEnumOptions } from "@haierbusiness-front/utils";
const addressSupplierTypeOptions = computed(()=>{
  return getEnumOptions(addressSupplierType)
})
const addressLevelOptions = computed(()=>{
  return getEnumOptions(addressLevel)
})
const addressTypeOptions = computed(()=>{
  return getEnumOptions(addressType,true)
})

const store = applicationStore();
const { resource } = storeToRefs(store);
const tabValue = ref<string>("1")
const modalTabValue = ref<string>("1")
const mappingBoxShow = ref<boolean>(false)
const addBoxShow = ref<boolean>(false)
const treeData = ref<addressRes[]>([])
const providerTreeData = ref<DistrictProviderMapPageReq[]>([])
const openBoxTitle = ref<string>("")
const districtId = ref<string>("") //国旅酒店地址id
const confirmLoading = ref<boolean>(false)
const editBoxShow = ref<boolean>(false)
const rowData = ref<addressRes>({})
const mappingType = ref<string>('XC')
const columnsFormng:any = [
  {
    title: '供应商',
    dataIndex: 'providerCodeName',
    key: 'providerCodeName',
    ellipsis: true,
  },
  {
    title: '名称',
    dataIndex: 'districtName',
    key: 'districtName',
  },
  {
    title: '级别',
    dataIndex: 'districtLevel',
    key: 'districtLevel',
  },
  {
    title: '目前关联',
    dataIndex: 'mainName',
    key: 'mainName',
  },
  {
    title: '路径',
    dataIndex: 'pathKey',
    key: 'pathKey',
  },
  {
    title: '操作',
    dataIndex: '_operator',
    align: 'center',
    width: '60px',
  },
]

const columnsp:any = [
  {
    title: '供应商',
    dataIndex: 'providerCodeName',
    key: 'providerCodeName',
    ellipsis: true,
  },
  {
    title: '名称',
    dataIndex: 'districtName',
    key: 'districtName',
  },
  {
    title: '路径',
    dataIndex: 'pathKey',
    key: 'pathKey',
  },
  {
    title: '操作',
    dataIndex: '_operator',
    align: 'center',
    width: '60px',
  },
]

  const columns:any = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true,
    align:"center",
  },
  {
    title: '级别',
    dataIndex: 'level',
    key: 'level',
    align:"center",
  },
  {
    title: '父级',
    dataIndex: 'parentName',
    key: 'parentName',
    align:"center",
  },
  {
    title: '区号',
    dataIndex: 'areaCode',
    key: 'areaCode',
    align:"center",
  },
  {
    title: '国家代码',
    dataIndex: 'code',
    key: 'code',
    align:"center",
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    align:"center",
  },
  {
    title: '携程映射',
    dataIndex: 'providerMapList',
    key: 'providerMapList',
    align:"center",
    width: '280px',
    customFilterDropdown: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    align: 'center',
    width: '180px',
  },
];

// 获取行政区划列表
const searchParam = ref<addressRes>({
  providerCodes:["XC", "AMAP", "QT", "MT", "YD", "RJ", "JJ", "HZ", "TXFC", "VETECH"]
})
const {
  data:dataList,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(addressApi.getDistrictList);

const dataSource = computed(() => dataList.value?.records || []);

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const pagination = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: dataList.value?.total,
    current: dataList.value?.pageNum,
    pageSize: dataList.value?.pageSize,
    style: { justifyContent: 'center' },
}));


// 获取供应商行政区划
const searchProviderParam = ref<DistrictProviderMapPageReq>({})
const {
  data:dataListProvider,
  run: listApiRunProvider,
  loading:loadingProvider,
  current:currentProvider,
  pageSize:pageSizeProvider,
} = usePagination(addressApi.getProviderDistrictList);

const dataSourceProvider = computed(() => dataListProvider.value?.records || []);

const handleProviderTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRunProvider({
    ...searchProviderParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const providerTableReset = () =>{
  searchProviderParam.value = {}
  listApiRunProvider({
    ...searchProviderParam.value,
    pageNum: currentProvider.value,
    pageSize: pageSizeProvider.value,
  });
}

const paginationProvider = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: dataListProvider.value?.total,
    current: dataListProvider.value?.pageNum,
    pageSize: dataListProvider.value?.pageSize,
    style: { justifyContent: 'center' },
}));

const mappingData = ref<any>([])

const toMappingMng = (row:addressRes) =>{
  openBoxTitle.value = row.name
  districtId.value = row.id
  mappingBoxShow.value = true
  // 获取已关联映射列表
  getMappingList(row.id)
}

const mappingListLoading = ref<boolean>(false)
// 获取已关联映射列表
const getMappingList= (mainId:string|number)=>{
  mappingListLoading.value = true
  addressApi.getProviderDistrictList({mainId}).then((res:any)=>{
    mappingData.value = res.records
    mappingListLoading.value = false
  })
}

const addMapping= () =>{
  addBoxShow.value = true
  // 请求供应商数据
  listApiRunProvider({
    ...searchProviderParam.value,
    pageNum: 1,
    pageSize:10,
  });
  getProviderDistrictList(1,10)
}

// 请求国旅地址映射
const mappingLoading = ref<boolean>(false)
const getMappingHotelList =(code:string) =>{
  mappingLoading.value = true
  addressApi.getMappingHotelList({code:code}).then((res:any)=>{
    mappingHotelList.value = res
    mappingLoading.value = false
  })
  .catch(()=>{
    mappingLoading.value = false
  })
}
// 解除供应商映射
const delMappingAdress =  (row:any) =>{
  hModal.confirm({
    title: '确定要删除此映射吗？',
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {
      return new Promise((resolve, reject) => {
        addressApi.districtProviderRelieveMap({providerDistrictId:row.id}).then((res:any)=>{
          message.success('刪除成功')
          getMappingList(districtId.value)
          resolve()
        })
      }).catch(()=>{
        resolve()
      })
    },
    onCancel() {
      console.log('Cancel');
    },
  });
}

// 关联地址
const association = (row:any) =>{
  hModal.confirm({
    title: '如果已有关联地址，会覆盖关联,确定要关联此地址吗？',
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {
      return new Promise((resolve, reject) => {
        console.log(row,"-----------row")
        addressApi.districtProviderMapDistrict({providerDistrictId:row.id,districtId:districtId.value}).then((res:any)=>{
          message.success('关联成功')
          listApiRun({
            ...searchParam.value,
            pageNum: 1,
            pageSize: 10,
          });
          getDistrictList(1,10)
          getMappingList(districtId.value)
          addBoxShow.value = false
          resolve()
        })
      })
    },
    onCancel() {
      console.log('Cancel');
    },
  });
}


// 国旅tree相关
const childrenLoading = ref<boolean>(false)
const expandedRow = ref<string[]>([])
const onLoadData: TreeProps['loadData'] = treeNode => {
  console.log(treeNode.id)
  return new Promise<void>(resolve => {
    childrenLoading.value = true
    addressApi.getDistrictList({parentId:treeNode.dataRef.id,providerCodes:["XC", "AMAP", "QT", "MT", "YD", "RJ", "JJ", "HZ", "TXFC", "VETECH"]}).then(res=>{
      treeNode.dataRef.children = []
      res.records.forEach(item=>{
        item.children = []
      })
      treeNode.dataRef.children = res.records
      // treeData.value = [...treeData.value];
      resolve()
      childrenLoading.value = false
    })
  });
}
// 展开tree
const onExpand = (expanded:boolean, record:addressRes) =>{
    // 当节点展开时，如果没有加载子节点，则进行加载
    if (expanded) {
      onLoadData({ dataRef: record });
    }
    if (expanded) {
        expandedRow.value.push(record.id)
      } else {
        var expandedRowKeys = expandedRow.value.filter(RowKey => RowKey !== record.id)
        expandedRow.value = expandedRowKeys
      }
}
const treeListData = ref<any>({})
//获取tree一级列表
const getDistrictList = (pageNum:number|string,pageSize:number|string)=>{
  expandedRow.value = []
  addressApi.getDistrictList({level:'country',pageNum: pageNum?pageNum:1,pageSize:pageSize?pageSize:10,providerCodes:["XC", "AMAP", "QT", "MT", "YD", "RJ", "JJ", "HZ", "TXFC", "VETECH"]}).then((res:any)=>{
    treeListData.value = res
    res.records.forEach((item:any)=>{
      item.children = []
    })
    treeData.value = res.records
    })
}
const treePagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: treeListData.value?.total,
  current: treeListData.value?.pageNum,
  pageSize: treeListData.value?.pageSize,
  style: { justifyContent: 'center' },
}));
const handleTreeTableChange = (
pag: { current: number; pageSize: number }
) => {
  getDistrictList(pag.current,pag.pageSize)
};

// 供应商tree相关
const providerChildrenLoading = ref<boolean>(false)
const onProviderLoadData: TreeProps['loadData'] = treeNode => {
  return new Promise<void>(resolve => {
    console.log(treeNode,"-------")
    providerChildrenLoading.value = true
    addressApi.getProviderDistrictList({parentPathKey:treeNode.dataRef.pathKey}).then(res=>{
      treeNode.dataRef.children = []
      res.records.forEach((item:any)=>{
        item.children = []
      })
      treeNode.dataRef.children = res.records
      providerTreeData.value = [...providerTreeData.value];
      resolve()
      providerChildrenLoading.value = false
    })
  });
}
// 展开tree
const onExpandProvider = (expanded:boolean, record:addressRes) =>{
    // 当节点展开时，如果没有加载子节点，则进行加载
    if (expanded) {
      onProviderLoadData({ dataRef: record });
    }
}
const treeProviderListData = ref<any>({})
//获取tree一级列表
const getProviderDistrictList = (pageNum:number|string,pageSize:number|string)=>{
  addressApi.getProviderDistrictList({level:'country',pageNum: pageNum?pageNum:1,pageSize:pageSize?pageSize:10}).then((res:any)=>{
    treeProviderListData.value = res
    res.records.forEach((item:any)=>{
      item.children = []
    })
    providerTreeData.value = res.records
    })
}
const treeProviderPagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: treeProviderListData.value?.total,
  current: treeProviderListData.value?.pageNum,
  pageSize: treeProviderListData.value?.pageSize,
  style: { justifyContent: 'center' },
}));
const handleProviderTreeTableChange = (
pag: { current: number; pageSize: number }
) => {
  getProviderDistrictList(pag.current,pag.pageSize)
};

const glReset = () =>{
  searchParam.value = {providerCodes:["XC", "AMAP", "QT", "MT", "YD", "RJ", "JJ", "HZ", "TXFC", "VETECH"]}
  searchGlList()
}

const searchGlList= () =>{
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize: 10,
  });
  getDistrictList(1,10)
}

// 编辑国旅酒店
const editRow = (row:addressRes) =>{
  console.log(row.type)
  rowData.value = row
  editBoxShow.value = true
}

// 提交编辑
const from = ref();
const handleOk = ()=>{
  console.log(expandedRow.value,"****************")
  from.value
    .validate()
    .then(() => {
      confirmLoading.value = true;
      addressApi.update(rowData.value).then(res=>{
        message.success('更新成功')
        editBoxShow.value = false
        confirmLoading.value = false;
        listApiRun({
          ...searchParam.value,
          pageNum: 1,
          pageSize: 10,
        });
        getDistrictList(1,10)
      })
    })
    .catch(() => {
      confirmLoading.value = false;
    });
}

  
onMounted(()=>{
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize: 10,
  });
  getDistrictList(1,10)
})
</script>

<template>
  <div class="pageBox">
    <div class="headerBox">
      <h-row>
        <h-col :span="4">
          <h-radio-group v-model:value="tabValue" button-style="solid">
            <h-radio-button value="1"><BarsOutlined /> 平铺</h-radio-button>
            <h-radio-button value="2"><ClusterOutlined /> 树形</h-radio-button>
          </h-radio-group>
        </h-col>
        <h-col :span="6">
          <h-form v-if="tabValue=='1'" :labelCol="{span:6, offset: 0}" >
          <h-form-item label="名称">
            <h-input
              allow-clear
              v-model:value="searchParam.name"
              placeholder="请输入名称"
            />
          </h-form-item>
          </h-form>
        </h-col>
        <h-col :span="14" style="text-align: right;">
          <h-button style="margin-right: 10px" @click="glReset">重置</h-button>
          <h-button type="primary" @click="searchGlList()">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </h-button>
        </h-col>
      </h-row>
    </div>
    <div v-if="tabValue=='1'" class="contentBox">
      <!-- :row-selection="rowSelection" -->
      <h-table
        :columns="columns"
        :size="'small'"
        :scroll="{ x: 1550 }"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange($event as any)"
      >
        <template #headerCell="{ column }">
          <template v-if="column.key === 'providerMapList'">
            <span style="color: #1890ff">{{addressSupplierType[mappingType]}}映射</span>
          </template>
        </template>
        <template
          #customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
        >
          <div style="padding: 8px">
            <h-select ref="select" v-model:value="mappingType" style="width: 160px" allow-clear>
              <h-select-option
                v-for="item in addressSupplierTypeOptions"
                :value="item.value"
              >{{item.label}}</h-select-option>
            </h-select>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'type'">{{ addressType[record.type] }}</template>
          <template v-if="column.dataIndex === 'level'">{{ addressLevel[record.level] }}</template>
          <template v-if="column.dataIndex === 'providerMapList'">
            <p
              v-show="item.providerCode == mappingType"
              v-for="item in record.providerMapList"
            >{{ item.districtName }}</p>
          </template>
          <template v-if="column.dataIndex === '_operator'">
            <h-button @click="editRow(record)" type="link">编辑</h-button>
            <h-button @click="toMappingMng(record)" type="link">映射管理</h-button>
          </template>
        </template>
      </h-table>
    </div>
    <!-- checkable -->
    <div v-else class="contentBox">
      <h-table
        :columns="columns"
        :dataSource="treeData"
        :size="'small'"
        :expandedRowKeys="expandedRow"
        :loadData="onLoadData"
        @expand="onExpand"
        :loading="loading"
        :pagination="treePagination"
        @change="handleTableChange($event as any)"
        rowKey="id"
      >
        <template #headerCell="{ column }">
          <template v-if="column.key === 'providerMapList'">
            <span style="color: #1890ff">{{addressSupplierType[mappingType]}}映射</span>
          </template>
        </template>
        <template
          #customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
        >
          <div style="padding: 8px">
            <h-select ref="select" v-model:value="mappingType" style="width: 160px" allow-clear>
              <h-select-option
                v-for="item in addressSupplierTypeOptions"
                :value="item.value"
              >{{item.label}}</h-select-option>
            </h-select>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'type'">{{ addressType[record.type] }}</template>
          <template v-if="column.dataIndex === 'level'">{{ addressLevel[record.level] }}</template>
          <template v-if="column.dataIndex === 'providerMapList'">
            <p
              v-show="item.providerCode == mappingType"
              v-for="item in record.providerMapList"
            >{{ item.districtName }}</p>
          </template>
          <template v-if="column.dataIndex === '_operator'">
            <h-button @click="editRow(record)" type="link">编辑</h-button>
            <h-button @click="toMappingMng(record)" type="link">映射管理</h-button>
          </template>
        </template>
        <!-- <template #expandedRowRender="{record, index}">
          <h-table
            :columns="columns"
            :showHeader="false"
            :dataSource="record.children"
            :loadData="onLoadData"
            @expand="onExpand"
            :pagination="false"
            :loading="childrenLoading"
            rowKey="id"
          >
            <template #headerCell="{ column }">
              <template v-if="column.key === 'providerMapList'">
                <span style="color: #1890ff">{{addressSupplierType[mappingType]}}映射</span>
              </template>
            </template>
            <template
              #customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
            >
              <div style="padding: 8px">
                <h-select ref="select" v-model:value="mappingType" style="width: 160px" allow-clear>
                  <h-select-option
                    v-for="item in addressSupplierTypeOptions"
                    :value="item.value"
                  >{{item.label}}</h-select-option>
                </h-select>
              </div>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'providerMapList'">
                <p
                  v-show="item.providerCode == mappingType"
                  v-for="item in record.providerMapList"
                >{{ item.districtName }}</p>
              </template>
              <template v-if="column.dataIndex === '_operator'">
                <h-button @click="editRow(record)" type="link">编辑</h-button>
                <h-button @click="toMappingMng(record)" type="link">映射管理</h-button>
              </template>
            </template>
          </h-table>
        </template> -->
      </h-table>
    </div>

    <!-- 维护映射关系 -->
    <h-modal
      width="600px"
      v-model:open="mappingBoxShow"
      :title="`维护【${openBoxTitle}】映射关系`"
      :footer="null"
    >
      <h-table
        :columns="columnsp"
        :size="'small'"
        :loading="mappingListLoading"
        :data-source="mappingData"
        :pagination="false"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === '_operator'">
            <h-button @click="delMappingAdress(record)" type="link">删除</h-button>
          </template>
        </template>
      </h-table>
      <h-button @click="addMapping" block style="margin-top:16px;">+ 新增映射</h-button>
    </h-modal>
    <!-- 新增映射 -->
    <h-modal
      width="1400px"
      v-model:open="addBoxShow"
      :title="`新增【${openBoxTitle}】映射关系`"
      :footer="null"
    >
      <h-row>
        <h-col :span="4">
          <h-radio-group v-model:value="modalTabValue" button-style="solid">
            <h-radio-button value="1"><BarsOutlined /> 平铺</h-radio-button>
            <h-radio-button value="2"><ClusterOutlined /> 树形</h-radio-button>
          </h-radio-group>
        </h-col>
        <h-col v-if="modalTabValue=='1'" :span="20">
          <h-form>
            <h-row>
              <h-col :span="5">
                <h-form-item label="是否未映射">
                  <h-switch
                    v-model:checked="searchProviderParam.districtMappingFlag"
                    checked-children="开"
                    un-checked-children="关"
                  />
                </h-form-item>
              </h-col>
              <h-col :span="6">
                <h-form-item label="供应商">
                  <h-select
                    ref="select"
                    v-model:value="searchProviderParam.providerCode"
                    style="width: 160px"
                    allow-clear
                  >
                    <h-select-option
                      v-for="item in addressSupplierTypeOptions"
                      :value="item.value"
                    >{{item.label}}</h-select-option>
                  </h-select>
                </h-form-item>
              </h-col>
              <h-col :span="6">
                <h-form-item label="名称">
                  <h-input
                    allow-clear
                    style="width: 160px"
                    v-model:value="searchProviderParam.name"
                    placeholder="请输入名称"
                  />
                </h-form-item>
              </h-col>
              <h-col :span="6">
                <h-form-item label="地址级别">
                  <h-select
                    ref="select"
                    v-model:value="searchProviderParam.level"
                    style="width: 160px"
                    allow-clear
                  >
                    <h-select-option
                      v-for="item in addressLevelOptions"
                      :value="item.value"
                    >{{item.label}}</h-select-option>
                  </h-select>
                </h-form-item>
              </h-col>
            </h-row>
          </h-form>
          <h-row>
            <h-col :span="24" style="text-align: right;margin-bottom: 10px">
              <h-button style="margin-right: 10px" @click="providerTableReset">重置</h-button>
              <h-button
                type="primary"
                @click="handleProviderTableChange({ current: 1, pageSize: 10 })"
              >
                <template #icon>
                  <SearchOutlined />
                </template>
                查询
              </h-button>
            </h-col>
          </h-row>
        </h-col>
      </h-row>
      <div v-if="modalTabValue=='1'" class="modalBox">
        <h-table
          :columns="columnsFormng"
          :size="'small'"
          :loading="loadingProvider"
          :data-source="dataSourceProvider"
          @change="handleProviderTableChange($event as any)"
          :pagination="paginationProvider"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'districtLevel'">{{ addressLevel[record.districtLevel] }}</template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button @click="association(record)" type="link">关联</h-button>
            </template>
          </template>
        </h-table>
      </div>
      <div class="modalBox" v-else>
        <h-table
          :columns="columnsFormng"
          :dataSource="providerTreeData"
          :size="'small'"
          :loadData="onProviderLoadData"
          @expand="onExpandProvider"
          :loading="providerChildrenLoading"
          :pagination="treeProviderPagination"
          @change="handleProviderTreeTableChange($event as any)"
          rowKey="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'districtLevel'">{{ addressLevel[record.districtLevel] }}</template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button @click="association(record)" type="link">关联</h-button>
            </template>
          </template>
          <!-- <template #expandedRowRender="{record, index}">
            <h-table
              :columns="columnsFormng"
              :showHeader="false"
              :dataSource="record.children"
              :loadData="onProviderLoadData"
              @expand="onExpandProvider"
              :pagination="false"
              rowKey="id"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === '_operator'">
                  <h-button @click="association(record)" type="link">关联</h-button>
                </template>
              </template>
            </h-table>
          </template> -->
        </h-table>
      </div>
    </h-modal>
    <!-- 编辑弹窗 -->
    <h-modal
      v-model:visible="editBoxShow"
      :title="'编辑地址信息'"
      :width="600"
      :confirmLoading="confirmLoading"
      forceRender
      @ok="handleOk"
    >
      <h-form
        v-if="editBoxShow"
        ref="from"
        :model="rowData"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
        :rules="rules"
      >
        <h-form-item label="名称" name="name">
          <h-input v-model:value="rowData.name" style="width: 100%" />
        </h-form-item>
        <h-form-item label="英文名称" name="enName">
          <h-input v-model:value="rowData.enName" style="width: 100%" />
        </h-form-item>
        <h-form-item label="区号" name="areaCode">
          <h-input v-model:value="rowData.areaCode" style="width: 100%" />
        </h-form-item>
        <h-form-item label="类型" name="type">
          <h-select v-model:value="rowData.type" style="width: 100%">
            <h-select-option
              v-for="item in addressTypeOptions"
              :value="Number(item.value)"
            >{{ item.label }}</h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="地址级别" name="level">
          <h-select v-model:value="rowData.level" style="width: 100%">
            <h-select-option
              v-for="item in addressLevelOptions"
              :value="item.value"
            >{{ item.label }}</h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="经度" name="lng">
          <h-input v-model:value="rowData.lng" style="width: 100%" />
        </h-form-item>
        <h-form-item label="纬度" name="lat">
          <h-input v-model:value="rowData.lat" style="width: 100%" />
        </h-form-item>
        <h-form-item label="是否国际城市" name="internationalFlag">
          <h-select v-model:value="rowData.internationalFlag" style="width: 100%">
            <h-select-option :value="0">否</h-select-option>
            <h-select-option :value="1">是</h-select-option>
          </h-select>
        </h-form-item>
      </h-form>
    </h-modal>
  </div>
</template>

<style scoped lang="less">
.pageBox {
  padding: 0 8px;
  .headerBox {
    width: 100%;
    height: 60px;
    background: #fff;
    // display: flex;
    // align-items: center;
    padding: 12px;
  }
  .inputBox {
    display: flex;
    align-items: center;
    margin-left: 60px;
  }
  .searchBtn {
    float: right;
  }
  .contentBox {
    margin-top: 16px;
    background: #fff;
    height: calc(100vh - 200px);
  }
}
.modalHeaderBox {
  width: 100%;
  // display: flex;
}
:deep(.ant-tree-node-content-wrapper) {
  .btnBox {
    display: none;
  }
  &:hover {
    .btnBox {
      display: inline-block;
    }
  }
}
</style>
