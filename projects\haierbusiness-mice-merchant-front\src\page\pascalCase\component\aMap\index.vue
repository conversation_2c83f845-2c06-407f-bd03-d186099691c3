<script setup>
import { onMounted, onUnmounted } from "vue";
import AMapLoader from "@amap/amap-jsapi-loader";

let map = null;

onMounted(() => {
  window._AMapSecurityConfig = {
    securityJsCode: "610db7cc7881574494e34fd00b13ab97",
  };
});

const createByMap = (lon,lat) =>{
  AMapLoader.load({
    key: "25569e43d6c6bcfa4d39a1b920d8d2d1", // 申请好的Web端开发者Key，首次调用 load 时必填
    version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    plugins: ["AMap.Scale"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
  })
    .then((AMap) => {
      map = new AMap.Map("container", {
        // 设置地图容器id
        viewMode: "3D", // 是否为3D地图模式
        zoom: 11, // 初始化地图级别
        center: [lon, lat], // 初始化地图中心点位置
      });
    })
    .catch((e) => {
      console.log(e);
    });
}

const addMarker = (lon,lat) =>{
  //创建一个 Marker 实例：
  const marker = new AMap.Marker({
    position: new AMap.LngLat(lon, lat), //经纬度对象，也可以是经纬度构成的一维数组[116.39, 39.9]
  });
  //将创建的点标记添加到已有的地图实例：
  map.clearMap()
  map.add(marker);
  // https://restapi.amap.com/v5/place/text?key=25569e43d6c6bcfa4d39a1b920d8d2d1&keywords=北京大学&types=141201&region=北京市
}
    // const fetchData = async () => {
    //   try {
    //     const response = await fetch('https://restapi.amap.com/v5/place/text?key=25569e43d6c6bcfa4d39a1b920d8d2d1&keywords=北京大学&types=141201&region=北京市');
    //     if (!response.ok) {
    //       throw new Error('Network response was not ok');
    //     }
    //     const res = await response.json();
    //     console.log(res,"66666666666666666666666666666")
    //   } catch (error) {
    //     console.error('Fetch error:', error);
    //   }
    // }

const setZoomAndCenter = (lon,lat) =>{
  map.setZoomAndCenter(14, [lon, lat]);
}


defineExpose({
  addMarker,
  createByMap,
  setZoomAndCenter
});
onUnmounted(() => {
  map?.destroy();
});
</script>

<template>
  <div  id="container"></div>
</template>

<style scoped>
#container {
  width: 100%;
  height: 100%;
}
</style>
