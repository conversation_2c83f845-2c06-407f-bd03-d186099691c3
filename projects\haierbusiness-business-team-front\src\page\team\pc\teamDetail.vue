<script lang="ts" setup>
import { computed,createVNode, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';


import {
  Anchor as hAnchor,
  <PERSON><PERSON> as hButton,
  Spin as hSpin,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Checkbox as hCheckbox,
  Form as hForm,
  FormItem as hFormItem,
  Row as hRow,
  Col as hCol,
  Popconfirm as hPopconfirm,
  Upload as hUpload,
  Input as hInput,
  Modal
} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  DownloadOutlined,
  VerticalAlignBottomOutlined,
  ExclamationCircleFilled,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  SendOutlined,
  PhoneOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { download, tripApi, teamListApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import {  ExclamationCircleOutlined } from '@ant-design/icons-vue';

import relativeTime from 'dayjs/plugin/relativeTime';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';

import { CityResponse, CityItem, TCteateTeam } from '@haierbusiness-front/common-libs';
import { fileApi } from '@haierbusiness-front/apis';
import type { TableColumnType } from 'ant-design-vue';
import { useRouter } from 'vue-router';

const route = ref(getCurrentRoute());
const router = useRouter();

const baseUrl = import.meta.env.VITE_BUSINESS_URL;


const id = route.value?.query?.id;

dayjs.extend(relativeTime);

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const labelCol = { span: 6 };
const wrapperCol = { span: 18 };
// 下载模板
const downloadFile = (filePath: string, name: string) => {
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = filePath;
  link.setAttribute('download', name);
  document.body.appendChild(link);
  link.click();
};
const data = [
  {
    key: '1',
    name: 'John Brown',
    money: '￥300,000.00',
    address: '￥1,256,000.00',
  },
  {
    key: '2',
    name: 'Jim Green',
    money: '￥1,256,000.00',
    address: '￥1,256,000.00',
  },
  {
    key: '3',
    name: 'Joe Black',
    money: '￥120,000.00',
    address: '￥1,256,000.00',
  },
];

const columns = [
  {
    title: '票号',
    dataIndex: 'name',
  },
  {
    title: '乘机人',
    className: 'column-money',
    dataIndex: 'money',
  },
  {
    title: '起飞时间',
    dataIndex: 'address',
  },

  {
    title: '航班号',
    dataIndex: 'address',
  },

  {
    title: '订单号',
    dataIndex: 'address',
  },

  {
    title: '团队价(含税)',
    dataIndex: 'address',
    fixed: 'right',
    width: 150,
  },

  {
    title: '非团队价(含税)',
    dataIndex: 'address',
    fixed: 'right',
    width: 150,
  },
];

// 价格录入页面
const goToPrice = () => {
  router.push({ path: "/teamPrice", query: { id:id  }  } )
}

// 接收订单
const receive = () => {
  Modal.confirm({
      title: '确认要接收此团队订单吗?',
      icon: createVNode(ExclamationCircleOutlined),
      onOk() {
        teamListApi.receive(detail.value).then(res => {
          getDetail(id)
        })
      },
      onCancel() {
      },
    });
 
}

const detail = ref<TCteateTeam>({})

const getDetail = (id: string) => {
  
  teamListApi.teamDetail(id).then(res => {
    detail.value = res
  })
};

const ticketColumns = [
  {
    title: '票号',
    dataIndex: 'ticketNum',
  },
  {
    title: '乘机人',
    className: 'column-money',
    dataIndex: 'passenger',
  },
  {
    title: '起飞时间',
    dataIndex: 'departureTime',
  },

  {
    title: '航班号',
    dataIndex: 'flightNum',
  },

  {
    title: '订单号',
    dataIndex: 'orderNum',
  },

  {
    title: '团队价(含税)',
    dataIndex: 'teamPrice',
    fixed: 'right',
    width: 150,
  },

  {
    title: '非团队价(含税)',
    dataIndex: 'nonTeamPrice',
    fixed: 'right',
    width: 150,
  },
];

const hotelColumns = [
  // 序号列
  {
    title: '序号',
    dataIndex: 'serialNumber', // 这个dataIndex仅作为标识，不对应实际数据字段
    width: '80px', // 可以自定义宽度
    scopedSlots: { customRender: 'bodyCell' }, // 对应模板中的具名插槽
  },
  {
    title: '酒店名称',
    dataIndex: 'hotelName',
  },

  {
    title: '房型',
    dataIndex: 'roomType',
  },

  {
    title: '入住人',
    dataIndex: 'occupant',
  },

  {
    title: '预订入住日期',
    dataIndex: 'checkInTime',
  },

  {
    title: '预订离店日期',
    dataIndex: 'checkOutTime',
  },

  {
    title: '入住天数',
    dataIndex: 'liveDays',
  },

  {
    title: '订单号',
    dataIndex: 'orderNum',
  },

  {
    title: '团队价(含税)',
    dataIndex: 'teamPrice',
    fixed: 'right',
    width: 150,
  },

  {
    title: '非团队价(含税)',
    dataIndex: 'nonTeamPrice',
    fixed: 'right',
    width: 150,
  },
];


watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);

</script>


<template>
  <div class="container">
    <div class="row flex">
      <div class="change-title" v-if="detail.destineNo">
        <h-row class="mb-10">
          <h-col :span="6" style="color: #00000073">需求单号:</h-col>
          <h-col :span="18">{{ detail.destineNo }}</h-col>
        </h-row>
        <h-row class="mb-10">
          <h-col :span="6" style="color: #00000073">申请时间:</h-col>
          <h-col :span="18">{{ detail.gmtCreate }}</h-col>
        </h-row>
      </div>

      <div class="main-title">
        <!-- <img src="../../../assets/image/trip/title.png" alt="" /> -->
        <span>团队订票订房需求-订单预订</span>
      </div>
      <div class="apply-con flex">
        <h-form
          class="mt-30"
          ref="formRef"
          :model="detail"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          labelAlign="left"
        >
          <!-- 基本信息 -->

          <div class="title whole-line" id="base-info">需求</div>
          
          <h-row>
            <h-col :span="12">
              <h-form-item name="beginCityName">
                <template #label>
                  <span
                    >经办人<a-tooltip>
                      <template #title>需求经办人</template>
                      <question-circleOutlined class="icon" /> </a-tooltip
                  ></span>
                </template>
                <div>{{ detail?.createName }}/{{ detail?.createBy }}</div>
              </h-form-item>
            </h-col>

            <h-col :span="12">
              <h-form-item name="beginCityName">
                <template #label>
                  <span
                    >联系人<a-tooltip>
                      <template #title>订单联系人</template>
                      <question-circleOutlined class="icon" /> </a-tooltip
                  ></span>
                </template>
                <div>
                  {{ detail?.contactUserName }}/{{ detail?.contactUserCode }}( <span><PhoneOutlined /></span> {{ detail?.contactUserPhone }})
                </div>
              </h-form-item>
            </h-col>

            <h-col :span="12">
              <h-form-item name="beginCityName">
                <template #label>
                  <span
                    >目的城市<a-tooltip>
                      <template #title>行程目的城市</template>
                      <question-circleOutlined class="icon" /> </a-tooltip
                  ></span>
                </template>
                <div>
                  {{ detail?.beginCityName }} <span>-</span> {{ detail?.endCityName }}
                </div>
              </h-form-item>
            </h-col>

            <h-col :span="12">
              <h-form-item name="beginCityName">
                <template #label>
                  <span
                    >行程时间<a-tooltip>
                      <template #title>购票行程时间</template>
                      <question-circleOutlined class="icon" /> </a-tooltip
                  ></span>
                </template>
                <div>
                  {{ detail?.beginDate }}<span>-</span>  {{ detail?.endDate }}
                </div>
              </h-form-item>
            </h-col>

            <h-col :span="12">
              <h-form-item name="beginCityName">
                <template #label>
                  <span
                    >出差类型<a-tooltip>
                      <template #title>团队出差类型</template>
                      <question-circleOutlined class="icon" /> </a-tooltip
                  ></span>
                </template>
                <div>{{detail?.evectionType== 0 ?'因公' :'因私' }}</div>
              </h-form-item>
            </h-col>

            <h-col :span="12">
              <h-form-item name="beginCityName">
                <template #label>
                  <span
                    >出行人<a-tooltip>
                      <template #title>订单出行人</template>
                      <question-circleOutlined class="icon" /> </a-tooltip
                  ></span>
                </template>
                <a v-if="detail?.travelerFileName" :href="detail.travelerFileUrl" class="color-main pointer"><DownloadOutlined />  {{ detail?.travelerFileName}}</a>
              </h-form-item>
            </h-col>
          </h-row>

          <a-divider />

          <!-- 产品类型 -->
          <h-form-item :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }" name="beginCityName" v-if="detail?.destineInfo">
            <template #label>
              <span
                >产品类型<a-tooltip>
                  <template #title>行程产品类型</template>
                  <question-circleOutlined class="icon" /> </a-tooltip
              ></span>
            </template>
              <!-- 机票 -->
              <h-row class="background-eee"  v-if="detail?.destineInfo.indexOf('0') > -1 || detail?.destineInfo.indexOf('1') > -1">
                <img
                  class="product-icon"
                  src="../../../assets/image/trip/plane.png"
                  alt=""
                />
                <span>{{ !detail?.teamDestinePlaneTicket?.planeTicketType ? '国内机票': '国际机票'}}</span>
                <a-divider type="vertical" />
                <span>{{detail?.teamDestinePlaneTicket?.voyageType ? '往返' : '单程'}}</span>
                <a-divider type="vertical" />
                <span>{{detail?.teamDestinePlaneTicket?.travelPriod ? '下午' : '上午'}}出行</span>
                <a-divider type="vertical" />
                <span>{{detail?.teamDestinePlaneTicket?.travelerNum || 0}}人出行(成人)</span>
                <a-divider type="vertical" />
                <div style="word-break: break-all;">{{detail?.teamDestinePlaneTicket?.otherInfo || '无其他需求'}}</div>

              </h-row>
              
             
              <!-- 酒店 -->
              <h-row class="background-eee" v-if="detail.destineInfo.indexOf('2') > -1">
                <img
                  class="product-icon"
                  src="../../../assets/image/trip/hotel.png"
                  alt=""
                />
                <span>酒店</span>
                <a-divider type="vertical" />
                <span>{{detail?.teamDestineHotel?.kingRoomNum || 0}} 间大床房</span>
                <a-divider type="vertical" />
                <span> {{detail?.teamDestineHotel?.doubleRoomNum || 0}} 间双床房</span>
                <a-divider type="vertical" />
                <div style="word-break: break-all;">{{detail?.teamDestineHotel?.otherInfo || '无其他需求'}}</div>

              </h-row>
          </h-form-item>

          <!-- 订单预订 -->
          <div class="title whole-line" id="base-info">订单预订</div>
          <!-- 国内机票明细 -->
          <h-form-item v-if="detail?.destineInfo?.indexOf('0') > -1 || detail?.destineInfo?.indexOf('1') > -1" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }" name="beginCityName">
            <template #label>
              <span
                >{{ !detail?.teamDestinePlaneTicket?.planeTicketType ? '国内机票' : '国际机票'}}<a-tooltip>
                  <template #title>{{ !detail?.teamDestinePlaneTicket?.planeTicketType ? '国内机票' : '国际机票'}}</template>
                  <question-circleOutlined class="icon" /> </a-tooltip
              ></span>
            </template>
              <h-row >
                <h-col :span="7">
                  <h-form-item name="beginCityName">
                    <template #label>
                      <span
                        >团队价合计(含税)</span>
                    </template>
                    <div>¥ {{ detail?.teamDestinePlaneTicket?.teamPriceTotal || 0 }}</div>
                  </h-form-item>
                </h-col>

                <h-col :span="7">
                  <h-form-item name="beginCityName">
                    <template #label>
                      <span
                        >非团队价合计(含税)</span>
                    </template>
                    <div>¥ {{ detail?.teamDestinePlaneTicket?.nonTeamAmount || 0 }}</div>
                  </h-form-item>
                </h-col>

                <h-col :span="6">
                  <h-form-item name="beginCityName">
                    <template #label>
                      <span
                        >节省费用</span>
                    </template>
                    <div>¥ {{ detail?.teamDestinePlaneTicket?.saveAmount || 0 }}</div>
                  </h-form-item>
                </h-col>

                <h-col :span="4">
                  <h-form-item name="beginCityName">
                    <template #label>
                      <span
                        >是否预订</span>
                    </template>
                    <div>{{detail?.teamDestinePlaneTicket?.ticketFlag ? '已预订' : '未预订'}}</div>
                  </h-form-item>
                </h-col>
              </h-row>
              

              <a-table :columns="ticketColumns" :data-source="detail?.teamDestinePlaneTicket?.planeTicketDetailList" size="small" bordered :pagination="false" :scroll="{ x: 1300, y: 400 }">
                <template #bodyCell="{ column, text }">
                  <template v-if="column.dataIndex === 'name'">
                    <a>{{ text }}</a>
                  </template>
                </template>
              </a-table>

              <h-row class="mt-10" >
                <h-col :span="3">
                  <span class="mr-10">导入明细:</span>
                </h-col>
                <h-col :span="20">
                  <a class="color-main pointer mr-20" :href="baseUrl + detail?.teamDestinePlaneTicket?.filePath" :download="detail?.teamDestinePlaneTicket?.fileName">{{ detail?.teamDestinePlaneTicket?.fileName}}</a>
                </h-col>
                

              </h-row>

              <h-row class="mt-10">
                <h-col :span="3">
                  <span class="mr-10">附件信息:</span>
                </h-col>
                <h-col :span="20">
                  <a class="color-main pointer mr-20" :href="item.filePath" v-for="item,index in detail?.teamDestinePlaneTicket?.fileMapList" :key="index">{{item.fileName}}</a>
                </h-col>
                
              </h-row>
              <h-row class="mt-10" v-if="!detail?.teamDestinePlaneTicket?.ticketFlag">
                <h-col :span="3">
                  <span>备注:</span>
                </h-col>
                <h-col :span="20">
                  <span style="word-break: break-all;">{{ detail?.teamDestinePlaneTicket?.remarks }}</span>
                </h-col>
              </h-row>
          </h-form-item>



          <!-- 酒店明细 -->
          <h-form-item v-if="detail?.destineInfo?.indexOf('2') > -1" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }" name="beginCityName">
            <template #label>
              <span
                >酒店<a-tooltip>
                  <template #title>酒店</template>
                  <question-circleOutlined class="icon" /> </a-tooltip
              ></span>
            </template>
              <h-row >
                <h-col :span="7">
                  <h-form-item name="beginCityName">
                    <template #label>
                      <span
                        >团队价合计(含税)</span>
                    </template>
                    <div>¥ {{detail?.teamDestineHotel?.teamPriceTotal || 0}}</div>
                  </h-form-item>
                </h-col>

                <h-col :span="7">
                  <h-form-item name="beginCityName">
                    <template #label>
                      <span
                        >非团队价合计(含税)</span>
                    </template>
                    <div>¥ {{detail?.teamDestineHotel?.nonTeamAmount || 0}}</div>
                  </h-form-item>
                </h-col>

                <h-col :span="6">
                  <h-form-item name="beginCityName">
                    <template #label>
                      <span
                        >节省费用</span>
                    </template>
                    <div>¥ {{detail?.teamDestineHotel?.saveAmount || 0}}</div>
                  </h-form-item>
                </h-col>

                <h-col :span="4">
                  <h-form-item name="beginCityName">
                    <template #label>
                      <span
                        >是否预订</span>
                    </template>
                    <div>{{detail?.teamDestineHotel?.ticketFlag ? '已预订' : '未预订'}}</div>
                  </h-form-item>
                </h-col>
              </h-row>

             

              <a-table size="small" :columns="hotelColumns" :data-source="detail?.teamDestineHotel?.hotelDetailList" bordered :pagination="false" :scroll="{ x: 1300, y: 400 }">
                
                <template #bodyCell="{ column, text, record, index }">
                <template v-if="column.dataIndex === 'serialNumber'">
                  <a>{{ index + 1 }}</a>
                </template>

              </template>
              </a-table>

              <h-row class="mt-10">
                <h-col :span="3">
                  <span class="mr-10">导入明细:</span>
                </h-col>
                <h-col :span="20">
                  <a class="color-main pointer mr-20" :href="baseUrl + detail?.teamDestineHotel?.filePath" :download="detail?.teamDestineHotel?.fileName">{{ detail?.teamDestineHotel?.fileName}}</a>
                </h-col>
              </h-row>

              <h-row class="mt-10">
                <h-col :span="3">
                  <span class="mr-10">附件信息:</span>
                </h-col>
                <h-col :span="20">
                  <a class="color-main pointer mr-20" :href="item.filePath" :download="item.fileName" v-for="item,index in detail?.teamDestineHotel?.fileMapList" :key="index">{{item.fileName}}</a>
                </h-col>
              </h-row>

              <h-row class="mt-10" v-if="!detail?.teamDestineHotel?.ticketFlag">
                <h-col :span="3">
                  <span>备注:</span>
                </h-col>
                <h-col :span="20">
                  <span style="word-break: break-all;">{{ detail?.teamDestineHotel?.remarks }}</span>
                </h-col>
              </h-row>
            
          </h-form-item>


        </h-form>
      </div>
    </div>
   
  </div>
</template>

<style lang="less" scoped>
@import url(./components/trip.less);

.change-title {
  position: absolute;
  right: 0;
  top: -80px;
  width: 300px;

  .ant-col-6,
  .ant-col-18 {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #000000d9;
  }
}
.mt-5 {
  margin-top: 5px;
}

.ml-5 {
  margin-left: 5px;
}
.color-eee {
  color: #a8a7a7;
}

.com-box {
  background: #f5f5f5;
  padding: 20px;
  margin-top: 20px;

  .whole-line {
    height: 40px;
    font-weight: 600;
  }
  .info-box {
    display: flex;
    align-items: flex-start;
    padding: 10px 20px;
    border: 1px solid #ffdb5c;
    background: #fffbd8;
  }
  :deep(.ant-form-item-control-input-content) {
    display: flex;
    align-items: center;
  }
}
.title {
  height: 60px;
}
.download-btn {
  color: #408cff;
  cursor: pointer;
}
:deep(.city-chose-box) {
  width: 200px !important;
}
:deep(.mr-10) {
  margin-right: 10px;
}

.background-eee {
  padding: 10px;
    background: #f4f4f4;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}
</style>