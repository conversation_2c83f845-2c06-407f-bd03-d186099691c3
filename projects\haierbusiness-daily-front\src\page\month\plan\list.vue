<script setup lang="ts">
import {
  Modal,
  TabPane as hTabPane,
  Tabs as hTabs,
  Tag as hTag,
  Select as hSelect,
  But<PERSON> as hButton,
  Col as hCol,
  FloatButton as hFloatButton,
  DatePicker as hDatePicker,
  Row as hRow,
  Table as hTable,
} from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import {
  AnnualPlanTypeStateConstant,
  BalanceStatusConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
  UserGroupSystemConstant,
} from '@haierbusiness-front/common-libs';
import { checkUserGroups, getCurrentRouter } from '@haierbusiness-front/utils';
import { ref, computed, watch, provide, inject } from 'vue';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import {
  IAnnualPlanTypeListRequest,
  IDailyDeptListRequestDTO,
  IDailyDeptResponse,
  IDailyPersonalResponse,
  IMonthPlanListRequestDTO,
  IMonthPlanListResponseDTO,
} from '@haierbusiness-front/common-libs/src/daily';
import planCard from './planCard.vue';
import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
import { dailyDeptApi, dailyMonthPlanApi } from '@haierbusiness-front/apis';
import { getDailyPersonUser } from '../../../utils/dailyPersonUtil';

const router = getCurrentRouter();
const { loginUser } = storeToRefs(applicationStore(globalPinia));
const prop = defineProps({
  query: Object,
});

watch(
  [() => prop.query],
  () => {
    if (prop.query) {
      listApiRun();
    }
  },
  { deep: true },
);
// 模式, 1: 小微模式 . 2: 平台主模式(管理员或平台主)
const listType = ref(parseInt(prop.query?.listType) || 1);
watch(
  [() => prop.query?.listType],
  () => {
    listType.value = parseInt(prop.query?.listType);
  },
  { deep: true },
);
const columns: ColumnType[] = [
  {
    title: '年度',
    dataIndex: 'year',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '类型',
    dataIndex: 'name',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '220px',
    fixed: 'right',
    align: 'center',
  },
];

const searchListParam = ref<IMonthPlanListRequestDTO>({
  year: dayjs().format('YYYY'),
  month: listType.value === 2 ? String(parseInt(dayjs().format('MM'))) : undefined
} as any);

const changeMonthActive = ref(
  (() => {
    return parseInt(dayjs().format('MM'));
  })(),
);

const listLoading = ref(false);
const listData = ref<IMonthPlanListResponseDTO[]>([]);
const listDataComputed = computed(() => {
  return listData.value;
});
const listApiRun = () => {
  listLoading.value = true;
  dailyMonthPlanApi
    .list({
      ...searchListParam.value,
    })
    .then((it) => {
      listData.value = it;
    })
    .finally(() => {
      listLoading.value = false;
    });
};

const dailyPersonUser = ref<IDailyPersonalResponse>();
const refreshDailyPersonUser = () => {
  return getDailyPersonUser(loginUser?.value?.username!!).then(
    (it) => (
      (dailyPersonUser.value = it),
      (searchListParam.value.deptCode = listType.value === 1 ? dailyPersonUser?.value?.deptCode : undefined),
      (searchListParam.value.deptName = listType.value === 1 ? dailyPersonUser?.value?.deptName : undefined)
    ),
  );
};
{
  refreshDailyPersonUser().then(() => {
    listApiRun();
  });
}

const changeYear = () => {
  listApiRun();
};
const changeMonth = () => {
  if (!changeMonthActive.value) {
    searchListParam.value.month = undefined;
  } else {
    (searchListParam.value.month as any) = String(changeMonthActive.value);
  }
  listApiRun();
};
const deptLoading = ref(false);
const searchDeptParam = ref<IDailyDeptListRequestDTO>({});
const deptData = ref<IDailyDeptResponse[]>();
const dailyDeptApiRun = () => {
  deptLoading.value = true;
  dailyDeptApi
    .list(searchDeptParam.value)
    .then((it) => {
      deptData.value = it;
    })
    .finally(() => {
      deptLoading.value = false;
    });
};
const changeDept = () => {
  listApiRun();
};
dailyDeptApiRun();
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <h-row :align="'middle'">
      <template v-if="listType === 1">
        <h-col
          :span="4"
          style="text-align: center; margin-bottom: 23px; font-size: 16px; border-bottom: 1px solid rgb(221, 221, 221)"
        >
          <h-date-picker
            style="margin-bottom: 6px"
            v-model:value="(searchListParam.year as any)"
            value-format="YYYY"
            picker="year"
            @change="changeYear"
          />
        </h-col>
        <h-col
          :span="16"
          style="text-align: center; margin-bottom: 23px; font-size: 16px; border-bottom: 1px solid rgb(221, 221, 221)"
        >
          <div style="font-weight: 700; height: 30px; margin-top: 8px">
            {{ loginUser?.departmentName }}
          </div>
        </h-col>
        <h-col
          :span="4"
          style="text-align: center; margin-bottom: 23px; font-size: 16px; border-bottom: 1px solid rgb(221, 221, 221)"
        >
          <div style="font-weight: 700; height: 30px; margin-top: 8px"></div>
        </h-col>
      </template>
      <template v-if="listType === 2">
        <h-col
          :span="3"
          style="text-align: center; margin-bottom: 20px; font-size: 16px; border-bottom: 1px solid rgb(221, 221, 221)"
        >
          <h-select
            placeholder="请选择部门"
            :options="deptData"
            :field-names="{ label: 'name', value: 'code' }"
            style="margin-bottom: 6px; width: 140px; margin-bottom: 20px"
            v-model:value="searchListParam.deptCode"
            allow-clear
            @change="changeDept"
          />
        </h-col>
        <h-col
          :span="3"
          style="text-align: center; margin-bottom: 19px; font-size: 16px; border-bottom: 1px solid rgb(221, 221, 221)"
        >
          <h-date-picker
            style="margin-bottom: 6px; width: 140px; margin-bottom: 20px"
            v-model:value="searchListParam.year"
            value-format="YYYY"
            picker="year"
            @change="changeYear"
          />
        </h-col>
        <h-col
          :span="18"
          style="text-align: center; margin-bottom: 23px; font-size: 16px; border-bottom: 1px solid rgb(221, 221, 221)"
        >
          <h-tabs v-model:activeKey="changeMonthActive" type="card" @change="changeMonth">
            <h-tab-pane v-for="i in 12" :key="i">
              <template #tab>
                <div style="width: 30px">{{ i }}月</div>
              </template>
            </h-tab-pane>
            <h-tab-pane>
              <template #tab>
                <div style="width: 30px" :key="'-1'">全年</div>
              </template>
            </h-tab-pane>
          </h-tabs>
        </h-col>
      </template>
    </h-row>
    <h-row :align="'middle'">
      <h-col
        v-if="listDataComputed && listDataComputed.length > 0"
        :span="6"
        v-for="(domain, index) in listDataComputed"
      >
        <plan-card :data="domain" :type="listType"></plan-card>
      </h-col>
      <div
        v-else
        style="
          display: flex;
          align-items: center;
          justify-content: center;
          height: 400px;
          width: 100%;
          color: rgb(163, 163, 163);
          font-size: 18px;
          font-weight: 500;
        "
      >
        当前年度计划不存在
      </div>
    </h-row>
  </div>
</template>

<style scoped lang="less"></style>
