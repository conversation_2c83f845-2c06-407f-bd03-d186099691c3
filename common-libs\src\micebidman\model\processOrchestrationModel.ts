import { IPageRequest } from "../../basic";

export class IProcessOrch<PERSON>ration<PERSON>ilter extends IPageRequest {
    begin?: string
    end?: string
}

// 扩展过滤器接口，添加需要的字段
export interface ExtendedProcessOrchestrationFilter extends IProcessOrchestrationFilter {
  name?: string;
  state?: number;
  createName?: string;
  createTimeBegin?: string;
  createTimeEnd?: string;
  processNo?: string;
  item?: string;
  lastModifiedName?: string;
  updateTimeBegin?: string;
  updateTimeEnd?: string;
  processType?: string;
  packageFlag?: string;
  businessTypeDisplay?: string;
}

export class IProcessOrchestration {
    id?: number | null
    creator?: string
    createTime?: string
    updater?: string
    updateTime?: string
    name?: string
    description?: string
    prefix?: string
    items?: string[]
    modifyContent?: string
    imgUrl?: string
    needPlateFormFee?: boolean
    processNotice?: string
}

// 为显示卡片定义接口
export interface ProcessCardItem extends IProcessOrchestration {
  currentVerId?: string;
  gmtCreate?: string;
  createName?: string;
  state?: number;
  pdProductLineId?: string;
  item?: string;
  lastModifiedName?: string;
}

// 定义角色规则接口
export interface IRoleRule {
    ruleMetaKey: string;
    ruleMetaName: string;
}
// 匹配规则元数据接口
export interface IRuleMetadata {
    ruleMetaKey: string
    ruleMetaName: string
    ruleType?: string
    description?: string
}

// 匹配规则列表响应接口
export interface IRuleMetadataResponse {
    records: IRuleMetadata[]
    total: number
    size: number
    current: number
    pages: number
}

// 流程节点接口
export interface IProcessNode {
    key?: string;
    title: string;
    name: string;
    nodes?: IProcessNode[];
    isChecked: boolean;
    operator?: string;
    description?: string;
    instructions?: string;
    distributionMode?: string;
    needAgreement?: boolean;
    isCollapsed?: boolean;
}

// 流程对象1接口
export interface IProcessObj1 {
    id: number;
    code: string;
    prefix: string;
    title: string;
    name: string;
    nodes: IProcessNode[];
    isChecked: boolean;
    operator: string;
    description: string;
    instructions: string;
    distributionMode: string;
    needAgreement: boolean;
    isCollapsed?: boolean;
}

// 流程状态类型接口
export interface IProcessStateType {
    processObj1: IProcessObj1;
    processObj2: IProcessNode[];
    processObj3: IProcessNode[];
    processObj4: IProcessNode[];
    searchParam: Record<string, any>;
    tableColumns: any[];
    tableAction: any[];
}

// 节点编辑表单接口
export interface INodeEditForm {
    name: string;
    operator: string;
    description: string;
    instructions: string;
    distributionMode: string;
    needAgreement: boolean;
}

// 操作人选项接口
export interface IOperatorOption {
    value: string;
    label: string;
}

// 分单模式选项接口
export interface IDistributionModeOption {
    value: string;
    label: string;
}

// 扩展流程节点接口，添加isExpanded属性
export interface IExtendedProcessNode extends IProcessNode {
    isExpanded?: boolean;
    code?: string;
}

// 扩展流程对象1接口，添加isExpanded属性
export interface IExtendedProcessObj1 extends IProcessObj1 {
    isExpanded?: boolean;
}

// 扩展流程状态类型接口，支持多级节点
export interface IExtendedProcessStateType extends Omit<IProcessStateType, 'processObj1' | 'processObj2' | 'processObj3' | 'processObj4'> {
    processObj1: IExtendedProcessObj1;
    processObj2: IExtendedProcessNode[];
    processObj3: IExtendedProcessNode[];
    processObj4: IExtendedProcessNode[];
    processLevels: Record<number, IExtendedProcessNode[]>;
    maxLevel: number;
}

// 节点配置接口，用于提交给后端的数据结构
export interface INodeConfig {
    type: string;
    metaKey: string;
    configParam: string;
    reverseDisplayParam: string;
}

// 节点触发器接口
export interface INodeTrigger {
    triggerType: string;
    triggerMetaKey: string;
    triggerMetaName: string;
    triggerWaitingTime: number;
    triggerMethodParamJson: string;
}

// 提交节点接口
export interface ISubmitNode {
    metaKey: string;
    nodeName: string;
    seq: number;
    configs: INodeConfig[];
}

// 处理API返回的数据结构接口
export interface IProcessNodeData {
    key: string;
    name: string;
    configs: Record<string, any>;
    nextNodes: Record<string, { nextNode: string | null }>;
}

// 新的节点流程状态接口
export interface IFlowNode {
    id: string;
    code: string;
    name: string;
    title: string;
    isSelected: boolean;
    hasExpanded: boolean; // 是否已经展开过下一个节点
    isEdited: boolean; // 是否已经被编辑过
}

// 节点列的数据结构接口
export interface INodeColumn {
    id: string;
    nodes: IFlowNode[];
    sourceNodeIndex: number; // 来源节点的索引
    isCollapsed?: boolean; // 是否收起状态（只显示选中节点）
    isMainFlow?: boolean; // 是否为主流程列（第一次展开的列可以继续扩展，后续新增的列不能扩展）
}

export interface IProcess { 
    id: number; 
    name: string; 
    pdProductLineId: number
}