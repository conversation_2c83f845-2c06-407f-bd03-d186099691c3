import { IPageResponse, IJdB2bListRequest,IHaierAccountBillInfo, IJdB2bAccountRequest, IJdB2bDetailsRequest, IHabDetailInfo, IJdB2bConfirmRequest, IJdB2bRevokeConfirmRequest, IJdB2bConfirmBalanceOrderBudgetRequest, IJdB2bCancelRequest, IJdB2bMarkReadRequest } from '@haierbusiness-front/common-libs'
import { download, get, post } from '../request'

export const b2bJdBalanceApi = {


     /**
     * 进行汇总
     */
     account: (params: IJdB2bAccountRequest): Promise<void> => {
        return post('balance/api/b2b/jd/account', params)
    },

    /**
     * 确认汇总
     */
    confirm: (params: IJdB2bConfirmRequest): Promise<void> => {
        return post('balance/api/b2b/jd/confirm', params)
    },

     /**
     * 确认并释放全部预算,全部确认成功则通知商户通
     */
     confirmBalanceOrderBudget: (params: IJdB2bConfirmBalanceOrderBudgetRequest): Promise<IHabDetailInfo[]> => {
        return post('balance/api/b2b/jd/revoke/confirm/budget', params)
    },

    /**
     * 撤回已确认状态
     */
    revokeConfirm: (params: IJdB2bRevokeConfirmRequest): Promise<void> => {
        return post('balance/api/b2b/jd/revoke/confirm', params)
    },

    /**
     * 取消汇总
     */
    cancel: (params: IJdB2bCancelRequest): Promise<void> => {
        return post('balance/api/b2b/jd/cancel', params)
    },
    
    /**
     * 获取结算列表
     */
    list: (params: IJdB2bListRequest): Promise<IPageResponse<IHaierAccountBillInfo>> => {
        return get('balance/api/b2b/jd/list', params)
    },

    /**
     * 导出结算列表
     */
    exportList: (params: IJdB2bListRequest): Promise<void> => {
        return download('balance/api/b2b/jd/list/export', params)
    },

    /**
     * 获取结算状态数量
     */
    stateAccount: (params: IJdB2bListRequest): Promise<any> => {
        return get('balance/api/b2b/jd/state/account', params)
    },

    /**
     * 获取预算释放失败,cvp通知失败数量
     */
    cvpErrorAccount: (params: IJdB2bListRequest): Promise<number> => {
        return get('balance/api/b2b/jd/cvp/error/account', params)
    },

    /**
     * 获取结算详情
     */
    details: (params: IJdB2bDetailsRequest): Promise<IPageResponse<IHabDetailInfo>> => {
        return get('balance/api/b2b/jd/details', params)
    },

    /**
     * 导出结算详情
     */
    detailsExport: (params: IJdB2bDetailsRequest): Promise<void> => {
        return download('balance/api/b2b/jd/details/export', params)
    },

    /**
     * 已读CVP错误消息
     */
    markRead: (params: IJdB2bMarkReadRequest): Promise<void> => {
        return post('balance/api/b2b/jd/mark/read', params)
    },

    /**
     * 获取推送CVP失败且未读的汇总单
     */
    pushCvpErrorAccount: (params: IJdB2bListRequest): Promise<number> => {
        return get('balance/api/b2b/jd/push/cvp/error/account', params)
    },
}