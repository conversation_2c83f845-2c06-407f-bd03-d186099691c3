<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  Input as hInput,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, EditOutlined, QuestionCircleOutlined, DeleteOutlined, MailOutlined } from '@ant-design/icons-vue';
import { knowCenterApi } from '@haierbusiness-front/apis';
import { ISmartBrainFilter, ISmartBrain, IKnowSelectResVo } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables';
import EditDialog from './/edit-dialog.vue';
import Echarts from '../components/echarts.vue';
import Rank from '../components/rank.vue';
import router from '../../../router';

import konwImg from "../../../assets/image/smartBrain/konw.png"
// const router = useRouter()

const currentRouter = ref();

const plainOptions = ref<IKnowSelectResVo[]>([]);

const searchParam = ref<ISmartBrainFilter>({});


// 新增弹窗
const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } = useEditDialog<
  ISmartBrain,
  ISmartBrain
>(knowCenterApi, '词条', () =>
  listApiRun({
    ...searchParam.value,
    pageNo: data.value?.pageNum || 1,
    pageSize: data.value?.pageSize || 9999,
  }),
);

const { data, run: listApiRun, loading, current, pageSize } = usePagination(knowCenterApi.getKnowCenterList);

const reset = () => {
  searchParam.value = {};
  handleTableChange({ current: 1, pageSize: 9999 });
};


const dataSource = computed(() => data.value?.list || []);

// 删除
const { handleDelete } = useDelete(knowCenterApi, () =>
  listApiRun({
    ...searchParam.value,
    pageNo: data.value?.pageNum,
    pageSize: data.value?.pageSize,
  }),
);

watch(current, () => {
  console.log(current.value)
  handleTableChange({ current: current.value, pageSize: 9999 });
});

// 请求标签list列表
const getCategoryList = () => {
  knowCenterApi.getCategoryList().then((res: Array<IKnowSelectResVo>) => {
    plainOptions.value.push(...res);
  });
};

// 查询
const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  listApiRun({
    ...searchParam.value,
    pageNo: pag.current,
    pageNum:pag.current,
    pageSize: pag.pageSize,
  });
};

// 初始化
onMounted(async () => {
  currentRouter.value = await router;
  getCategoryList();
  handleTableChange({ current: 1, pageSize: 9999 });
  console.log(data)
});
</script>

<template>
  <div style="height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <a-card>
      <h-row :align="'middle'">
        <h-col :span="24" style="margin-bottom: 10px">
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
            <h-col :span="1.5" style="text-align: right; padding-right: 10px">
              <label for="createTime">词条名称：</label>
            </h-col>
            <h-col :span="4">
                <h-input allowClear v-model:value="searchParam.entryName" style="width: 100%" />
            </h-col>
            <h-col :span="2" style="text-align: right; padding-right: 10px">
              <label for="createTime">分类：</label>
            </h-col>
            <h-col :span="4">
              <h-select allowClear @change="changeType" style="width: 100%" v-model:value="searchParam.categoryName">
                <h-select-option v-for="item in plainOptions" :value="item.catName">{{ item.catName }}</h-select-option>
              </h-select>
            </h-col>
            <h-col :span="12" style="text-align: right">
              <h-button type="primary" style="margin-right: 10px" @click="handleCreate()">
                <PlusOutlined /> 新增
              </h-button>
              <h-button style="margin-right: 10px" @click="reset">重置</h-button>
              <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 9999 })">
                <SearchOutlined />查询
              </h-button>
            </h-col>
          </h-row>
        </h-col>
      </h-row>
    </a-card>
    <!-- 商务知识中心列表 -->
    <div v-loading="loading" class="businessListBox">
      <!-- 知识中心item -->
      <div v-for="(item, index) in dataSource" :key="item.id" class="businessItemBox">
        <a-card hoverable class="cardItem">
          <template #actions>
            <edit-outlined @click="handleEdit(item)" key="edit" />
            <DeleteOutlined @click="handleDelete(item.id)" />
          </template>
          <a-card-meta style="height:130px;overflow:auto;" :title="item.entryName" :description="item.entryContent">
            <template #avatar>
              <!-- <img style="width:50px;height:50px;" :src="konwImg" alt=""> -->
            </template>
          </a-card-meta>
        </a-card>
      </div>
    </div>
    <!-- <div class="pagination">
        <a-pagination v-model:current="current" v-model:pageSize="pageSize" :total="data?.total" show-less-items />
    </div> -->
    <div v-if="visible">
      <edit-dialog :labelList="plainOptions" :show="visible" :data="editData" @cancel="onDialogClose" @ok="handleOk">
      </edit-dialog>
    </div>
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.businessListBox {
  min-height:calc(100vh - 300px);
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  width: 100%;
  .businessItemBox{
    margin-right:0.65%;
    // float: left;
    width: 24.5%;
    .cardItem{
      width:100%;
      height: 220px;
      margin-top:16px;
    }
    &:nth-child(4n){
      margin-right:0;
    }
  }
}
.pagination{
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top:20px;
}
:deep(.el-loading-mask){
  background-color: rgba(255,255,255,0.5);
}
:deep(.ant-card-meta-description){
  height: 100px;
  overflow: auto;
}
</style>
