import { IPageRequest } from "../../basic";

export interface IRefundReq extends IPageRequest {
  /* */
  pageNum?: number;

  /* */
  pageSize?: number;

  /* */
  id?: number;

  /* */
  refundOrderCode?: string;

  /* */
  applicationOrderCode?: string;

  /* */
  orderBookingCode?: string;

  /* */
  refundAmount?: number;

  /* */
  restaurantName?: string;

  /* */
  signerCode?: string;

  /* */
  signerName?: string;

  /* */
  dealTime?: Record<string, unknown>;

  /*处理时间0开始1结束 */
  dealTimes?: Record<string, unknown>[];

  /* */
  creator?: string;

  /* */
  createTime?: Record<string, unknown>;

  /* */
  needPage?: boolean;
}

export interface IRefundRes {
  /* */
  主键: number;

  /* */
  退款单编号: string;

  /* */
  申请单号: string;

  /* */
  预订单号: string;

  /* */
  签单人工号: string;

  /* */
  签单人名称: string;

  /* */
  处理时间: Record<string, unknown>;

  /* */
  退款餐厅: string;

  /* */
  退款金额: number;

  /* */
  创建人: string;

  /* */
  创建时间: Record<string, unknown>;
}

// 参数接口
export interface Update_1Params {
  /* */
  pageNum?: number;

  /* */
  pageSize?: number;

  /*主键id */
  id?: number;

  /*汇总单号 */
  statementCode?: string;

  /*账单所属年月 */
  settleMonth?: Record<string, unknown>;

  /*账单开始日期 */
  settleStartDate?: Record<string, unknown>;

  /*账单结束日期 */
  settleEndDate?: Record<string, unknown>;

  /*账单类型1宴请2外卖 */
  sceneType?: number;

  /*有效结算金额 */
  effectiveSettlementAmount?: number;

  /*美团结算金额 */
  mtSettlementAmount?: number;

  /*差异金额 */
  diffAmount?: number;

  /*订单状态1待确认2已确认3已取消 */
  orderStatus?: number;

  /*确认人工号 */
  confirmPersonCode?: string;

  /*确认人名称 */
  confirmPersonName?: string;

  /*创建人 */
  creator?: string;

  /*创建人名称 */
  creatorName?: string;

  /*创建时间 */
  createTime?: Record<string, unknown>;

  /*创建人电话 */
  creatorPhone?: string;

  /*更新时间 */
  updateTime?: Record<string, unknown>;

  /*更新人 */
  updater?: string;

  /*备注 */
  remark?: string;

  /* */
  needPage?: boolean;
}

// 参数接口
export interface ExportInvoiceParams {
  /* */
  pageNum?: number;

  /* */
  pageSize?: number;

  /*主键id */
  id?: number;

  /*汇总单号 */
  statementCode?: string;

  /*账单所属年月 */
  settleMonth?: Record<string, unknown>;

  /*账单开始日期 */
  settleStartDate?: Record<string, unknown>;

  /*账单结束日期 */
  settleEndDate?: Record<string, unknown>;

  /*账单类型1宴请2外卖 */
  sceneType?: number;

  /*有效结算金额 */
  effectiveSettlementAmount?: number;

  /*美团结算金额 */
  mtSettlementAmount?: number;

  /*差异金额 */
  diffAmount?: number;

  /*订单状态1待确认2已确认3已取消 */
  orderStatus?: number;

  /*确认人工号 */
  confirmPersonCode?: string;

  /*确认人名称 */
  confirmPersonName?: string;

  /*创建人 */
  creator?: string;

  /*创建人名称 */
  creatorName?: string;

  /*创建时间 */
  createTime?: Record<string, unknown>;

  /*创建人电话 */
  creatorPhone?: string;

  /*更新时间 */
  updateTime?: Record<string, unknown>;

  /*更新人 */
  updater?: string;

  /*备注 */
  remark?: string;

  /* */
  needPage?: boolean;
}
