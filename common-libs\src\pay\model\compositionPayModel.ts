

export interface ICompositionSearchCompleteStatusRequest {
    /**
    * 应用程序code
    * 每个应用程序对应了多种支付方式
    */
    applicationCode?: string;

    /**
     * 业务自己进行控制需要显示哪些支付类型
     * 如果为空则获取应用程序对应的全部支付方式
     */
    payTypes?: number[];

    /**
     * 支付用户
     * 用户对应多种支付方式
     */
    username?: string;

    /**
     * 企业编码
     * 企业对应多种支付方式
     */
    enterpriseCode?: string;

    /**
     * 订单号
     */
    orderCode?: string;

    /**
     * 订单供应商编码 PS: wyy
     */
    providerCode?: string;

    /**
     * 订单金额 保留两位小数
     * 20.02
     */
    amount?: number;

    /**
     * 支付回调地址
     */
    notifyUrl?: string;

    /**
    * 支付结束跳转地址
    */
    callbackUrl?: string;

    /**
     * 订单描述
     */
    description?: string;
}

export interface ICompositionSearchCompleteStatusResponse {
    /**
    * 是否获取到响应结果
    */
    effective?: boolean;

    /**
     * 状态
     */
    state?: number;
}


export interface ICompositionSearchStatusRequest {
    /**
    * 商户订单编号
    */
    storeOrderId?: string;

    /**
     * 支付中心流水号
     */
    tradeId?: string;
}

export interface ICompositionSearchStatusResponse {
    /**
    * 描述
    */
    desc?: string;

    /**
     * 状态
     */
    state?: number;
}
