<script lang="ts" setup>
import { computed, onMounted, reactive, ref, toRaw } from 'vue';
import type { UnwrapRef } from 'vue';
import type { Rule } from 'ant-design-vue/es/form';
import {
  IOutPerson,
  QueryCertificatesRes,
  QueryServiceGuaranteeRes,
  QueryTripNationalityRes,
} from '@haierbusiness-front/common-libs';
import { tripApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import dayjs, { Dayjs } from 'dayjs';

import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Row as hRow,
  Col as hCol,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Button as hButton,
  Space as hSpace,
  Card as hCard,
  Divider as hDivider,
  Modal as hModal,
  Spin as hSpin,
  Cascader as hCascader,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  DatePicker as hDatePicker,
  RangePicker as hRangePicker,
  message,
} from 'ant-design-vue';
import nationalityChose from '@haierbusiness-front/components/nationalityChose/index.vue';

const visible = ref<boolean>(false);
const show = () => {
  visible.value = true;
};
defineExpose({
  show,
});

// 查询城市列表
const {
  data: cityOptions,
  run: cityOptionsRun,
  loading: cityOptionsLoading,
} = useRequest(tripApi.district, {
  defaultParams: [],
  onSuccess: () => {
    // detailsApiRun({ todoId: todoId.value, code: code.value })
  },
  manual: false,
});

const formRef = ref();
const labelCol = { span: 8 };
const wrapperCol = { span: 13 };
const disabledDate = (current: Dayjs) => {
  // Can not select days before today and today
  return current && current > dayjs().endOf('day');
};
const familyNameToUpperCase = (val: string) => {
  if(formState.value.familyName) {
    formState.value.familyName = formState.value.familyName.toUpperCase();
  }
};
const realNameToUpperCase = (val: string) => {
  if(formState.value.realName) {
    formState.value.realName = formState.value.realName.toUpperCase();
  }
};
const formState = ref<IOutPerson>({
  /*姓名 */
  name: '',
  /*英文名 */
  englishName: '',
  realName: '',
  familyName: '',
  /*国籍/地区 */
  natureName: '中国',
  natureCode: 'CN',
  /*性别1男2女 */
  sex: '',
  /*出生日期 */
  birthday: '',

  /*乘机人类型 */
  travelType: undefined,

  /*联系电话 */
  telephone: '',

  /*邮箱 */
  email: '',

  /*电话号码 */
  phone: '',

  /*服务保障等级 */
  promiseLevel: '普通保障级别',

  /*证件类型 */
  cardType: undefined,

  /*证件号 */
  cardNo: '',
  // dateRange
  dateRange: [],

  /*有效期始 */
  effectBeginDate: '',

  /*有效期止 */
  effectEndDate: '',
});
const requiredEng = computed(() => formState.value.natureName != '中国');
const requiredCn = computed(() => formState.value.natureName == '中国');

const rules: Record<string, Rule[]> = {
  // name: [{ required: true, message: '请填写姓名', trigger: 'blur' }],
  // englishName: [
  //   { required: true, message: '请填写英文名称', trigger: 'blur' },
  //   { pattern: /^[A-Za-z\s]+$/, message: '英文名称只能包含字母和空格', trigger: 'blur' },
  // ],
  familyName: [
    { message: '请填写英文姓', trigger: 'blur' },
    { pattern: /^[A-Za-z]+$/, message: '英文姓只能是英文', trigger: 'blur' },
  ],
  realName: [
    { message: '请填写英文名', trigger: 'blur' },
    { pattern: /^[A-Za-z]+$/, message: '英文名只能是英文', trigger: 'blur' },
  ],
  // nature: [{ required: true, message: '请选择国籍', trigger: 'change' }],
  natureCode: [{ required: true, message: '请选择国籍', trigger: 'change' }],
  sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
  // birthday: [{ required: true, message: '请选择出生日期', trigger: 'change' }],
  // travelType: [{ required: true, message: '请选择乘机人类型', trigger: 'change' }],
  // telephone: [{ required: true, message: '请填写联系电话', trigger: 'blur' }],
  // email: [{ required: true, message: '请填写邮箱', trigger: 'blur' }],
  phone: [{ required: true, message: '请填写电话号码', trigger: 'blur' }],
  cardType: [{ required: true, message: '请选择证件类型', trigger: 'change' }],
  cardNo: [{ required: true, message: '请填写证件号码', trigger: 'blur' }],
  qfgj: [{ required: true, message: '请选择签发地', trigger: 'change' }],
  qfrqStr: [{ required: true, message: '请填写签发日期', trigger: 'blur' }],
};
const emit = defineEmits(['getOutPersonList']);

const onSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      spinning.value = true;
      if(formState.value?.dateRange && formState.value?.dateRange.length > 0) {
        formState.value.effectBeginDate = formState.value?.dateRange[0] || '';
        formState.value.effectEndDate = formState.value?.dateRange[1] || '';
      }
      
      formState.value.englishName = formState.value.realName && formState.value.familyName ? formState.value.familyName + '/' + formState.value.realName : '';
      tripApi
        .addOutPerson(formState.value)
        .then((res) => {
          message.success('新增成功');
          visible.value = false;
          formState.value = {
            /*姓名 */
            name: '',
            /*英文名 */
            englishName: '',
            realName: '',
            familyName: '',
            /*国籍/地区 */
            natureName: '中国',
            natureCode: 'CN',
            /*性别1男2女 */
            sex: '',
            /*出生日期 */
            birthday: '',

            /*乘机人类型 */
            travelType: undefined,

            /*联系电话 */
            telephone: '',

            /*邮箱 */
            email: '',

            /*电话号码 */
            phone: '',

            /*服务保障等级 */
            promiseLevel: '普通保障级别',

            /*证件类型 */
            cardType: undefined,

            /*证件号 */
            cardNo: '',
            // dateRange
            dateRange: [],

            /*有效期始 */
            effectBeginDate: '',

            /*有效期止 */
            effectEndDate: '',
          };
          spinning.value = false;

          emit('getOutPersonList');
        })
        .catch((err) => {
          spinning.value = false;
        });
    })
    .catch((error) => {
      console.log('error', error);
    });
};
const resetForm = () => {
  formRef.value.resetFields();
  visible.value = false;
};

const spinning = ref<boolean>(false);

// 选择国籍

const chosedNationality = (city: CityItem) => {
  formState.value.natureName = city.mc;
  formState.value.natureCode = city.by3;
};

const chosedHzAddress = (city: CityItem) => {
  formState.value.qfgj = city.mc;
  formState.value.qfgjezm = city.by3;
};

const {
  data: outPersonData,
  run: outPersonRun,
  loading: outPersonLoading,
} = useRequest(tripApi.addOutPerson, {
  defaultParams: [formState.value],
});

// 查询证件类型
const certificates = ref<Array<QueryCertificatesRes>>([]);
const queryCertificates = () => {
  tripApi.queryCertificates().then((res) => {
    certificates.value = res;
  });
};
// 查询服务等级
const serviceGuarantee = ref<Array<QueryServiceGuaranteeRes>>([]);
const queryServiceGuarantee = () => {
  tripApi.queryServiceGuarantee().then((res) => {
    serviceGuarantee.value = res;
  });
};

// 查询国籍信息

const tripNationality = ref<QueryTripNationalityRes>();
const queryTripNationality = () => {
  tripApi.queryTripNationality().then((res) => {
    tripNationality.value = res;
  });
};

onMounted(() => {
  queryServiceGuarantee();
  queryTripNationality();
  queryCertificates();
});

const contryOptions = ref([
  {
    value: 'china',
    label: '中国',
    children: [
      {
        value: 'shandong',
        label: '山东',
        children: [
          {
            value: 'qingdao',
            label: '青岛',
          },
        ],
      },
    ],
  },
  {
    value: 'out',
    label: '国外',
    children: [
      {
        value: 'xby',
        label: '西班牙',
        children: [
          {
            value: 'bsln',
            label: '巴萨罗那',
          },
        ],
      },
    ],
  },
]);
</script>

<template>
  <h-modal :maskClosable="false" v-model:open="visible" title="新增外部联系人" :footer="null" width="1000px">
    <h-spin size="large" :spinning="spinning">
      <h-form
        style="width: 100%"
        ref="formRef"
        name="form"
        layout="inline"
        :model="formState"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <h-card size="small" title="基本信息" class="mb-30" style="width: 100%">
          <h-row>
            <h-col :span="12"
              ><h-form-item
                :required="requiredCn"
                :labelCol="{ span: 8, offset: 0 }"
                label="姓名"
                name="name"
                id="name"
              >
                <h-input placeholder="请输入姓名" v-model:value="formState.name" /> </h-form-item
            ></h-col>
            <h-col :span="8"
              ><h-form-item
                :labelCol="{ span: 8, offset: 0 }"
                :wrapperCol="{span: 16, offset: 0 }"
                label="英文姓名"
                :required="requiredEng"
                name="familyName"
                id="familyName"
              >
                <h-input placeholder="请输入英文姓" v-model:value="formState.familyName" @change="familyNameToUpperCase" /> </h-form-item
            ></h-col>
            <h-col :span="4"
              ><h-form-item
                :labelCol="{ span: 1, offset: 0 }"
                :wrapperCol="{span: 23, offset: 0 }"
                :required="requiredEng"
                name="realName"
                id="realName"
              >
                <h-input placeholder="请输入英文名" v-model:value="formState.realName" @change="realNameToUpperCase" /> </h-form-item
            ></h-col>
          </h-row>

          <h-row>
            <h-col :span="12"
              ><h-form-item :labelCol="{ span: 8, offset: 0 }" label="国籍/地区" name="natureCode" id="natureCode">
                <nationality-chose
                  width="100%"
                  :value="formState.natureName"
                  @chosedCity="chosedNationality"
                ></nationality-chose>
              </h-form-item>
            </h-col>
            <h-col :span="12"
              ><h-form-item :labelCol="{ span: 8, offset: 0 }" label="性别" name="sex" id="sex">
                <h-radio-group v-model:value="formState.sex">
                  <h-radio value="M">男</h-radio>
                  <h-radio value="F">女</h-radio>
                </h-radio-group>
              </h-form-item></h-col
            >
          </h-row>

          <h-row>
            <h-col :span="12"
              ><h-form-item label="出生日期" name="birthday" id="birthday">
                <h-date-picker
                  style="width: 100%"
                  :disabled-date="disabledDate"
                  valueFormat="YYYY-MM-DD"
                  v-model:value="formState.birthday"
                /> </h-form-item
            ></h-col>
            <h-col :span="12"
              ><h-form-item label="乘机人类型" name="travelType" id="travelType">
                <a-select placeholder="请选择乘机人类型" v-model:value="formState.travelType" style="width: 100%">
                  <a-select-option value="1">成人</a-select-option>
                  <a-select-option value="2">儿童</a-select-option>
                  <a-select-option value="3">婴儿</a-select-option>
                </a-select>
              </h-form-item></h-col
            >
          </h-row>

          <h-row>
            <h-col :span="12"
              ><h-form-item label="联系电话" name="phone" id="phone">
                <h-input placeholder="请输入联系电话" v-model:value="formState.phone" /> </h-form-item
            ></h-col>
            <h-col :span="12"
              ><h-form-item label="邮箱" name="email" id="email">
                <h-input placeholder="请输入邮箱" v-model:value="formState.email" /> </h-form-item
            ></h-col>
          </h-row>

          <h-row>
            <h-col :span="12">
              <h-form-item label="电话号码" name="telephone" id="telephone">
                <h-input placeholder="请输入电话号码" v-model:value="formState.telephone" />
              </h-form-item>
            </h-col>
            <h-col :span="12">
              <h-form-item label="服务保障级别" name="promiseLevel" id="promiseLevel">
                <h-select placeholder="请选择服务保障级别" v-model:value="formState.promiseLevel">
                  <h-select-option
                    v-for="(item, index) in serviceGuarantee"
                    :key="index"
                    :value="item.serviceGuarantee"
                    >{{ item.serviceGuarantee }}</h-select-option
                  >
                </h-select>
              </h-form-item></h-col
            >
          </h-row>
        </h-card>

        <h-card size="small" title="证件信息" class="mb-30" style="width: 100%">
          <h-row>
            <h-col :span="6">
              <h-form-item
                ref="cardType"
                label=""
                :labelCol="{ span: 0, offset: 0 }"
                :wrapperCol="{ span: 24, offset: 0 }"
                name="cardType"
                id="cardType"
              >
                <h-select placeholder="请选择证件类型" v-model:value="formState.cardType">
                  <h-select-option v-for="(item, index) in certificates" :key="index" :value="item.certificatesCode">{{
                    item.certificates
                  }}</h-select-option>
                </h-select>
              </h-form-item>
            </h-col>
            <h-col :span="8"
              ><h-form-item
                ref="cardNo"
                label=""
                :labelCol="{ span: 0, offset: 0 }"
                :wrapperCol="{ span: 24, offset: 0 }"
                name="cardNo"
                id="cardNo"
              >
                <h-input v-model:value="formState.cardNo" placeholder="证件号码" /> </h-form-item
            ></h-col>

            <h-col :span="10"
              ><h-form-item
                ref="dateRange"
                label=""
                :labelCol="{ span: 0, offset: 0 }"
                :wrapperCol="{ span: 24, offset: 0 }"
                name="dateRange"
                id="dateRange"
              >
                <h-range-picker valueFormat="YYYY-MM-DD" v-model:value="formState.dateRange" /> </h-form-item
            ></h-col>
          </h-row>
          <h-row>
            <!-- 护照 -->
            <template v-if="formState.cardType == '1003402'">
              <h-col :span="6"
                ><h-form-item
                  ref="qfgj"
                  label=""
                  :labelCol="{ span: 0, offset: 0 }"
                  :wrapperCol="{ span: 24, offset: 0 }"
                  name="qfgj"
                  id="qfgj"
                >
                  <nationality-chose
                    width="100%"
                    :value="formState.qfgj"
                    @chosedCity="chosedHzAddress"
                  ></nationality-chose> </h-form-item
              ></h-col>
              <h-col :span="8"
                ><h-form-item
                  ref="qfrqStr"
                  label=""
                  :labelCol="{ span: 0, offset: 0 }"
                  :wrapperCol="{ span: 24, offset: 0 }"
                  name="qfrqStr"
                  id="qfrqStr"
                >
                  <h-date-picker
                    valueFormat="YYYY-MM-DD"
                    v-model:value="formState.qfrqStr"
                    placeholder="签发时间"
                  /> </h-form-item
              ></h-col>
            </template>
          </h-row>
        </h-card>
        <h-row style="width: 100%">
          <h-col :offset="6" :span="12">
            <h-form-item :wrapper-col="{ offset: 8, span: 16 }">
              <h-button type="primary" @click="onSubmit">确定</h-button>
              <h-button style="margin-left: 10px" @click="resetForm">取消</h-button>
            </h-form-item>
          </h-col>
        </h-row>
      </h-form>
    </h-spin>
  </h-modal>
</template>

<style scoped>
@import url('./trip.less');

.mb-30 {
  margin-bottom: 30px;
}

.ant-row {
  margin-bottom: 20px;
}

.ant-form-item-required,
.ant-form-item-label > label {
  font-size: 14px !important;
  color: #000000a6 !important;
}
</style>