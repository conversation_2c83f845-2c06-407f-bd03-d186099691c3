<template>
  <div style="height: 33vh; position: relative" background="rgba(0,0,0,0)" v-loading="loading">
    <el-select
      v-if="props.dateType == 1"
      v-model="panel"
      class="bigscreen"
      placeholder="选择同期对比"
      filterable
      remote
      multiple
      collapse-tags
      clearable
      remote-show-suffix
      no-data-text="暂无数据"
      @change="pageChange"
    >
      <el-option v-for="item in pageList" :label="item.label" :value="item.value" />
    </el-select>
    <bar-line from="date" v-if="loaded" :legend="legend" :x-axis="xAxis" :y-axis="yAxis" :series="series" />
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import BarLine from '../../components/barLine.vue';
import { useBoardStore } from '@haierbusiness-front/utils/src/store/board';
import { queryBookingTrend, querySynchronismBookingTrend } from '@haierbusiness-front/apis/src/data/board';
import dayjs from 'dayjs';
import { EventBus } from '../../eventBus';
import { numberToChinese } from '../../../../utils/numberToChinese';

const panel = ref([]);
const pageList = ref([] as Array<{ label: string; value: string }>);
const chartArr = ref([] as any);

const pageChange = (value: any) => {
  const store = useBoardStore();
  loading.value = true;
  const { date } = store;
  legend = ['销售金额', '间夜数'];
  chartArr.value = [
    {
      name: '销售金额',
      type: 'bar',
      color: 'rgba(0,240,255,0.4)',
      itemStyle: {
        borderColor: '#00F0FF',
      },
      data: barData,
      selectedMode: 'single', //鼠标点击是否突出该区域
    },
    {
      name: '间夜数',
      type: 'line',
      yAxisIndex: 1,
      color: 'rgba(255,215,0,1)',
      smooth: true,
      symbol: 'none',
      data: lineData,
    },
  ] as any;
  let requestLength = 0;

  if (panel.value.length > 0) {
    panel.value.forEach(async (item) => {
      let arr = date.map((each) => {
        return each.split('-')[0] - item + '-' + each.split('-')[1] + '-' + each.split('-')[2];
      });
      const { dateColumn, pattern } = getDateType();
      const res = await querySynchronismBookingTrend(
        {
          dateColumn,
          pattern,
        },
        paramsData.value && paramsData.value.data ? paramsData.value.data.name : null,
        paramsData.value && paramsData.value.from ? paramsData.value.from : null,
        arr,
      );

      chartArr.value.push({
        name: '同期' + numberToChinese(item) + '年销售金额',
        type: 'bar',

        data: res.rows.map((e) => e[1]),
        selectedMode: 'single', //鼠标点击是否突出该区域
      });
      legend.push('同期' + numberToChinese(item) + '年销售金额');
      chartArr.value.push({
        name: '同期' + numberToChinese(item) + '年间夜数',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        symbol: 'none',
        data: res.rows.map((e) => e[2]),
      });
      legend.push('同期' + numberToChinese(item) + '年间夜数');
      requestLength = requestLength + 1;
      if (requestLength == panel.value.length) {
        series.value = chartArr.value;
        loading.value = false;
      }
    });
  } else {
    series.value = chartArr.value;
    loading.value = false;
  }
};

onMounted(() => {
  let thisYear = dayjs().subtract(1, 'year').format('YYYY'); // 获取当前年份
  let startYear = 2019; // 起始年份
  for (let i = startYear; i <= thisYear; i++) {
    pageList.value.push({
      label: '同期' + numberToChinese(thisYear - i + 1) + '年',
      value: thisYear - i + 1,
    });
  }
});
const props = defineProps({
  dateType: Number,
});
const loaded = ref(false);
const loading = ref(false);
let legend = ['销售金额', '间夜数'];
const xAxis = ref([]);
const yAxis = [
  {
    type: 'value',
    name: '万元',
    splitNumber: 5,
    axisLabel: {
      formatter(value: any) {
        return value / 10000;
      },
    },
  },
  {
    type: 'value',
    name: '间夜数',
    splitNumber: 5,
  },
];
const series = ref([]);
onMounted(() => {
  queryData();
});

const paramsData: any = ref({});
EventBus.on((event, params) => {
  if (event == 'refresh') {
    panel.value = [];
    if (!params) queryData();
    if (params && params.from != 'date') {
      paramsData.value = params;
      queryData();
    }
  }
});
const getDateType = () => {
  if (props.dateType == 0) {
    return {
      dateColumn: 'view_order_create_year',
      pattern: 'YYYY',
    };
  }
  if (props.dateType == 1) {
    return {
      dateColumn: 'view_order_create_year_month',
      pattern: 'YYYY-MM',
    };
  }
  return {
    dateColumn: 'view_order_create_date',
    pattern: 'YYYY-MM-DD',
  };
};
let barData: any = [];
let lineData: any = [];
let xData: any = [];
const queryData = async () => {
  const { dateColumn, pattern } = getDateType();
  const data = await queryBookingTrend(
    {
      dateColumn,
      pattern,
    },
    paramsData.value && paramsData.value.data ? paramsData.value.data.name : null,
    paramsData.value && paramsData.value.from ? paramsData.value.from : null,
  );

  barData = [];
  lineData = [];
  xData = [];

  data.rows.forEach((item: any) => {
    xData.push(item[0]);
    barData.push(item[1] || 0);
    lineData.push(item[2] || 0);
  });
  xAxis.value = xData;

  series.value = [
    {
      name: '销售金额',
      type: 'bar',
      color: 'rgba(0,240,255,0.4)',
      itemStyle: {
        borderColor: '#00F0FF',
      },
      data: barData,
      selectedMode: 'single', //鼠标点击是否突出该区域
    },
    {
      name: '间夜数',
      type: 'line',
      yAxisIndex: 1,
      color: '#FFD700',
      smooth: true,
      symbol: 'none',
      data: lineData,
    },
  ] as any;
  loaded.value = true;
};
watch(
  () => props.dateType,
  () => {
    queryData();
    panel.value = [];
  },
);
</script>
<style lang="less" scoped>
.bigscreen {
  position: absolute;
  bottom: 34.5vh;
  right: 25%;
  z-index: 999;
  width: 180px;
}

@media screen and (max-width: 1500px) {
  .bigscreen {
    position: absolute;
    bottom: 35.2vh;
    right: 27%;
    z-index: 999;
    width: 180px;
  }
}
</style>
