<script setup lang="ts">
import {onMounted, PropType, ref} from 'vue'
import {Col as hCol, Row as hRow, Spin as hSpin} from 'ant-design-vue';
import zfb from '@/assets/image/composition/zfb.png';
import wx from '@/assets/image/composition/wx.png';
import ysf from '@/assets/image/composition/ysf.png';
import {IPayData} from '@haierbusiness-front/common-libs/src/pay/model/basicModel';
import {compositionPayApi} from '@haierbusiness-front/apis';
import reload from '@/assets/image/reload.svg';
import {isMobile} from "@haierbusiness-front/utils";
import {PaySourceConstant} from "@haierbusiness-front/common-libs";

const props = defineProps({
  param: Object as PropType<IPayData>
});


const emit = defineEmits<{
  (e: 'payComplete', isPayComplete: boolean): void
}>()

const qr = ref();
const payCode = ref();
const isReload = ref();
const reloadTime = ref(0);

const payComplete = () => {
  emit('payComplete', true)
}

/**
 * 开始刷新倒计时
 */
const startCountdown = () => {
  const sh = setInterval(() => {
    reloadTime.value--;
    if (reloadTime.value <= 0) {
      clearInterval(sh);
    }
  }, 1000);
}

// 刷新二维码
const payQRRefresh = () => {
  if (reloadTime.value <= 0) {
    qr.value = null
    compositionPayApi.csbRefresh(
        {
          code: payCode.value,
          businessCode: props.param?.orderCode,
        }
    ).then(it => {
      payCode.value = it.code
      qr.value = it.url
      reloadTime.value = 5;
      startCountdown();
    })
  }
}

// 加载聚合支付 c扫b 二维码
compositionPayApi.csb(
    {
      orderCode: props.param?.orderCode,
      applicationCode: props.param?.applicationCode,
      payTypes: props.param?.payTypes,
      username: props.param?.username,
      providerCode: props.param?.providerCode,
      amount: Number(props.param?.amount),
      orderDetailsUrl: props.param?.orderDetailsUrl,
      notifyUrl: props.param?.notifyUrl,
      callbackUrl: props.param?.callbackUrl,
      description: props.param?.description,
      payload: props.param?.payload,
      paySource: isMobile() ? PaySourceConstant.MOBILE.type : PaySourceConstant.PC.type,
      paymentMethod: 2
    },
    {
      applicationCode: props.param?.applicationCode,
      excludes: "paySource,paymentMethod",
      nonce: props.param?.hbNonce,
      timestamp: props.param?.hbTimestamp,
      sign: props.param?.sign,
    }
).then(it => {
  payCode.value = it.code
  qr.value = it.url
})

const searchCompleteStatus = () => {
  compositionPayApi.searchCompleteStatus({
    orderCode: props.param?.orderCode,
  }).then(it => {
    if (!it.effective) {
      searchCompleteStatus()
    } else {
      payComplete()
    }
  })
}

onMounted(() => {
  searchCompleteStatus()
})

</script>
<template>
  <h-row>
    <h-col :span="6" offset="9">
      <div class="qr-container" @click="payQRRefresh" @mouseenter="isReload = true" @mouseleave="isReload = false">
        <h-spin size="large" v-if="!qr" style="margin-top: 10vh;"/>
        <img v-if="qr" :src="qr" style="width:100%; height: 100%;"/>
        <div v-if="qr" class="masking">
          <div v-if="isReload" style="margin-top: 12.5vh;">
            <img v-if="reloadTime <= 0" :src="reload" style="width: 2vw;"/>
            <div v-else class="readtime-countdown">{{ reloadTime }}s 后刷新</div>
          </div>
        </div>
      </div>
    </h-col>
  </h-row>

  <h-row class="pay-typs">
    <h-col :span="24">支持收款方式</h-col>
  </h-row>
  <h-row>
    <h-col :span="2" offset="9">
      <img :width="50" :src="zfb"/>
    </h-col>
    <h-col :span="2">
      <img :width="50" :src="wx"/>
    </h-col>
    <h-col :span="2">
      <img :width="50" :src="ysf"/>
    </h-col>
  </h-row>
</template>

<style scoped lang="less">
.pay-typs {
  font-size: 1.6vh;
  margin-top: 2vh;
  margin-bottom: 1.8vh;
}

.qr-container {
  height: 30vh;

  .masking {
    position: absolute;
    top: 0;
    left: 0;
    float: left;
    height: 100%;
    width: 100%;

    .readtime-countdown {
      padding-top: 1.5vh;
      color: rgb(255, 255, 255);
      font-size: 1.7vh;
      text-shadow: 1px 0.5px 1px rgb(157, 176, 177);
    }
  }

  .masking:hover {
    background-color: rgba(77, 80, 80, 0.603);
  }
}
</style>
