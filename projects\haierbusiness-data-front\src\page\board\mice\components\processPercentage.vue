<template>
    <div
        :style="{ height: props.height + 'vh' }"
        background="rgba(0,0,0,0)"
    >
        <div :id="id" :style="{ height: props.height + 'vh' }"></div>
    </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import * as echarts from "echarts";
import { queryLocalProcessPercentage } from "@haierbusiness-front/apis/src/data/board/mice";
import { circle2 as cicleOptions, colors } from "../../data";
import { EventBus } from "../../eventBus";
const props = defineProps({
    height: {
        type: Number,
        default: 33,
    },
    type: {
        type: String,
        default: "青岛会议",
    },
});
const id = ref("cicle-type-" + Date.now());
const loading = ref(false);
let chartDom, myChart;
onMounted(() => {
    queryData();

    chartDom = document.getElementById(id.value);
    myChart = echarts.init(chartDom as any, "dark");
});
EventBus.on((event, params) => {
    if (event == "refresh") queryData(params ? params : null);
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
    loading.value = true;
    const data  = await queryLocalProcessPercentage(
        {
            type: props.type,
        },
        params ? params.data.name : null,
        params ? params.from : null
    );
    loading.value = false;
    const rows = [] as Array<{
        name: string;
        value: string | number;
    }>;
    data.rows.forEach((item, index) => {
        rows.push({
            value: item[1],
            name: item[0],
        });
    });
    const { series } = cicleOptions;
    series[0].color = colors;
    series[0].data = rows;
    myChart.setOption(cicleOptions);
};
</script>
<style scoped lang="less"></style>
