<script setup lang="ts">
import { onMounted, ref, watch,onUnmounted } from 'vue';
import { CloseOutlined } from '@ant-design/icons-vue';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';
import {
  IUserListRequest,
  IUserInfo,
} from '@haierbusiness-front/common-libs';

const emit = defineEmits(['change'])

interface Props {
    travelType?: number
    leftNum?: string
    list?:any
}

const props = withDefaults(defineProps<Props>(), {
    travelType: 1,
    list: []
});

// 单程，返程
const travelType = ref(props.travelType)

const personList = ref(props.list)

const travelerChange = (vals: string[]) => {
    emit('change', vals)
}


watch(props, (newValue) => {
    travelType.value = newValue.travelType
})

const value = ref([])


// 申请人 
// 用户选择
const params = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});
// 申请人
const nickName = ref<Array<string>>([]);

const formState = ref({
    persons: []
})
const userNameChange = (userInfo: Array<IUserInfo>) => {
  if (!userInfo.length) {
    formState.value.persons = []
  }
  console.log(99999, userInfo)
  emit('change', userInfo)
  const array: Array<string> = [];
  formState.value.persons = []
  userInfo.forEach((item: any, index: number) => {
    if (item.nickName && item.username) {
      array.push(item.nickName + '/' + item.username);
      formState.value.persons?.push(
        {
          policyObjectCode: item.username,
          policyObjectName: item.nickName
        }
      )
    } else {
      array.push(
        nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[0] +
        '/' +
        nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[1],
      );
      formState.value.persons?.push(
        {
          policyObjectCode: nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[1],
          policyObjectName: nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[0]
        }
      )
    }
  })
  nickName.value = array;

}



</script>

<template>
    <div class="traveler-select-component">
        <div class="ticket-item" :class="{ 'international-left-width': travelType === 3 }">
            <div class="item-labels">出行人员</div>
            <div class="item-num">
                <user-select ref="userSelectRef" :maxTagCount="2" :value="nickName"  :multiple="true" :params="params"
                @change="(userInfo: IUserInfo) => userNameChange(userInfo)" />
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
@import url('./common.less');

.tag {
    padding: 1px 10px;
    border-radius: 11px;
    color: rgba(0,0,0,0.65);
    background-color:rgba(31,35,41,0.1);
    height: 100%;
    display: flex;
    align-items: center;
    line-height: normal;
}

.close {
    margin-left: 5px;
    font-size:12px;
}

</style>

<style>
.traveler-select-component .ant-select-selector {
    
  border:none !important;
  box-shadow: none !important;
  height: 22px;
  padding: 0px !important;
  
}

.traveler-select-component .ant-select-sm {
    width: 100% !important;
}

.traveler-select-component .ant-select-selection-placeholder {
  font-size: 16px !important;
  color: rgba(0,0,0,0.35) !important;
  
  margin-left: -7px;
  /* padding-inline-end: 25px !important; */
}

.traveler-select-component .ant-select-selection-item {
  font-size: 14px !important;
  
  color: rgba(0,0,0,0.65);
}

.traveler-select-component .ant-select-selection-overflow-item-rest .ant-select-selection-item{
    background: #ffffff;
    border: none;
    height: 22px;
}


.traveler-select-component .ant-select-item-option-content {
  
}
</style>
