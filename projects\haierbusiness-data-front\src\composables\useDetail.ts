import type { Detailable } from "@/api/types";
import { useRoute, useRouter } from 'vue-router'
import {onMounted, reactive, ref, watch} from "vue";
import type { Ref } from 'vue'
import { isFinite } from "lodash-es";
import { message } from 'ant-design-vue';

export const useDetail = <T>(api: Detailable, id: number, modelLabel = "",) => {
    const currentDetail = ref<T>()

    const fetchDetail = (id: number) => {
        if (isFinite(id)) {
            api.detail(id).then(res => {
                if (res.code == 0) {
                    currentDetail.value = res.data
                } else {
                    message.error(res.msg)
                }
            })
        } else {
            message.error(`未找到${modelLabel}信息！`)
        }
    }

    onMounted( () => fetchDetail(id))

    return {
        currentDetail,
        fetchDetail
    }
}