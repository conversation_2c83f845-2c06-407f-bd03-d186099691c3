<template>
  <div class="order-list" style=" min-height: 100vh;">
    <van-sticky :offset-top="0">
      <!-- <van-nav-bar   title="宴请订餐" left-arrow style="  z-index: 11;">
        <template #left>
          <van-icon name="arrow-left" color="#000" @click="goBack" />
        </template>
</van-nav-bar> -->



      <van-search class="list-search" shape="round" @search="reSearch" v-model="defaultParams.orderBookingCode"
        @clear="reSearch" @click-left-icon="reSearch" :clearable="true" show-action placeholder="搜索我的订单">

        <template #action>
          <div class="mr-10" @click="reSearch">查询</div>

        </template>
        <!-- <template #action>
          <van-dropdown-menu :close-on-click-outside="false" ref="menuRef">
            <van-dropdown-item ref="itemRef">
              <template #title>
                <van-icon name="filter-o" />
                筛选
              </template>

              <van-cell-group title="按时间选择">
                <van-cell>
                  <template #value>
                    <van-radio-group v-model="defaultParams.timeType">
                      <van-row justify="space-between">
                        <van-radio name="week" class="my-radio">
                          <template #icon="{ checked }">
                            <van-button class="btn-com" size="small" round
                              :type="checked ? 'primary' : 'default'">最近一周</van-button>
                          </template>
                        </van-radio>

                        <van-radio name="month" class="my-radio">
                          <template #icon="{ checked }">
                            <van-button class="btn-com" size="small" round
                              :type="checked ? 'primary' : 'default'">最近一月</van-button>
                          </template>
                        </van-radio>

                        <van-radio name="year" class="my-radio">
                          <template #icon="{ checked }">
                            <van-button class="btn-com" size="small" round
                              :type="checked ? 'primary' : 'default'">最近一年</van-button>
                          </template>
                        </van-radio>
                      </van-row>
                    </van-radio-group>

                    <van-row class="mt-10" justify="space-between">
                      <van-col :span="10">
                        <van-field class="input-border" readonly @click="openTimePicker('begin')"
                          v-model="defaultParams.mealTimes[0]" placeholder="选择开始时间" />
                      </van-col>
                      <van-col :span="4">
                        <van-divider :style="{ height: '100%', borderColor: '#000', padding: '0 16px', margin: '0' }" />
                      </van-col>
                      <van-col :span="10">
                        <van-field class="input-border" readonly @click="openTimePicker('end')"
                          v-model="defaultParams.mealTimes[1]" placeholder="选择结束时间" />
                      </van-col>
                    </van-row>
                  </template>
                </van-cell>
              </van-cell-group>
             
              <van-cell-group title="按商家查询">
                <van-search shape="round" v-model="defaultParams.restaurantName" :clearable="true" left-icon=""
                  placeholder="搜索商家名称" />
              </van-cell-group>

              <van-row justify="space-around" style="padding: 15px 0">
                <van-button class="list-search-btn" size="small" type="default" round @click="reSet"> 清空 </van-button>
                <van-button class="list-search-btn" size="small" type="primary" round @click="reSearch">
                  确认
                </van-button>
              </van-row>
            </van-dropdown-item>
          </van-dropdown-menu>
        </template> -->
      </van-search>
      <van-tabs v-model:active="defaultParams.orderStatus">
        <van-tab v-for="(item, index) in tabList" :name="item.value" :title="item.label" :key="index"> </van-tab>
      </van-tabs>
    </van-sticky>

    <van-list v-model:loading="orderLoading" :finished="orderFinished" :finished-text="orderList.length ? '没有更多了' : ''"
      @load="loadorderList" class="van-list-box">
      <div class="order-item mb-8 flex" v-for="(item, index) in orderList" :key="index">
        <div class="order-item-right flex">

          <div class="order-content cell-group-shadow ">
            <van-cell-group style="background-color: rgba(0,0,0,0);">
              <van-cell>
                <template #title>
                  <div class="flex align-items-center justify-content-between ">
                    <div class="flex align-items-center mr-10">
                      <img v-if="item?.sceneType" class="icon-size mr-5"
                        :src="item?.sceneType == 1 ? yqIcon : item?.sceneType == 2 ? wmIcon : ''" />
                      <span class="font-size-10">{{ item?.orderBookingCode }}</span>
                    </div>

                  </div>
                </template>
              </van-cell>

              <van-cell>
                <template #title>
                  <div class="flex  flex-column" style="position:relative">
                    <div>签单人:{{ `${item?.signerName}(${item?.signerCode})` }}</div>
                    <div>餐厅名称:{{ item?.restaurantName }}</div>
                    <div>实际消费金额:{{ `${item.actualPaymentAmount || 0}元` }}</div>
                    <div>下单时间:{{ item?.payTime }}</div>
                    <div>就餐时间:{{ item?.mealTime }}</div>
                    <div style="position: absolute; top: 2px; right: 0;" :style="{ color: statusToColor(item?.orderStatus, item?.payStatus) }" >
                      {{statusToText(item?.orderStatus, item?.payStatus)}}
                    </div>
                  </div>
                </template>
              </van-cell>

              <van-cell class="my-cell">
                <template #value>
                  <div class="apply-btns">
                    <van-button  size="small" :loading="payIndex == index" v-if="item?.orderStatus == 0 && item?.payStatus == 20"  round type="primary" plain
                      @click.stop="goToMt(index,1)">去核销</van-button>

                    <van-button  size="small" :loading="refundIndex == index"  v-if="item?.orderStatus == 0 && item?.payStatus == 20"  round type="primary" plain
                      @click.stop="goToMt(index,2)">去退款</van-button>

                    <van-button v-if="item?.sceneType == 2" :loading="mtIndex == index"  size="small" round type="primary" plain
                      @click.stop="goToWmDetail(item?.mtBookingCode, index)">外卖详情</van-button>

                    <van-button  size="small" round type="primary" plain
                      @click.stop="goToDetail(item?.id)">详情</van-button>
                  </div>
                </template>
              </van-cell>


            </van-cell-group>
          </div>
        </div>
      </div>

    </van-list>

    <van-empty v-if="!orderLoading && orderList.length == 0" description="暂无数据" />


    <!-- 时间选择 -->
    <van-popup @click.stop v-model:show="showTimePicker" :close-on-click-overlay="false" position="bottom">
      <van-date-picker title="选择日期" v-model="currentDate" :min-date="minDate" :max-date="maxDate" @confirm="confirmTime"
        @cancel="showTimePicker = false" />
    </van-popup>


  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue';

import {
  IUserListRequest,
  ROrderPayMentStateEnum,
  ROrderPayMentStateEnumColor,
  ROrderApprovalStateEnum,
  ROrderApprovalStateEnumColor,
  ROrderStateEnum,
  ROrderStateEnumColor,
  BanquetStatusEnumMobile,
  BanquetStateTagColorMap,
  BanquetReservationStateTagColorMap,
  BanquetReservationStatusEnumMobile
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { banquetReservationApi, banquetApi } from '@haierbusiness-front/apis';
import { ROrderParams, BanquetApplicationTypeEnum, IReservationRes } from '@haierbusiness-front/common-libs';
import type { Ref } from 'vue';

import dayjs from 'dayjs';

import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { showConfirmDialog, showSuccessToast } from 'vant';
const store = applicationStore();

const { loginUser } = storeToRefs(store);

const router = getCurrentRouter();
const route = ref();

const wmIcon = new URL('@/assets/image/banquet/order/order-wm.png', import.meta.url).href
const yqIcon = new URL('@/assets/image/banquet/order/order-yq.png', import.meta.url).href


const payIndex = ref<number>(-1)
const refundIndex = ref<number>(-1)

// tab
const tabList = [
  {
    label: '全部订单',
    value: '',
  },
  {
    label: '待核销',
    value: 1,
  },
  {
    label: '已完成',
    value: 2,
  },
  {
    label: '已取消',
    value: 4,
  },

  // {
  //   label: '外卖单',
  //   value: 2,
  // },

];

// 订单列表
const orderLoading = ref<boolean>(false);
const orderFinished = ref<boolean>(false);
const orderList = ref<Array<IReservationRes>>([])
const orderTotal = ref<number>(0);
const defaultParams = ref<ROrderParams>({
  timeType: '',
  orderBookingCode: '',
  pageNum: 0,
  pageSize: 20,
  sceneType: '', // 订单状态
  mealTimes: [],
  orderStatus: '', // 订单状态
  signerCode:loginUser.value?.username
});

const goBack = () => {
  router.back(-1);
};
// 订单状态 0 未核销 10已核销 20部分核销
// 支付状态 20：已支付，31：部分退款，32：全额退款

const statusToColor = (orderStatus?:string|number, payStatus?:string|number) => {
  
  
  if (orderStatus == '0' && payStatus == '20') {
    return 'green'
  }else if (orderStatus == '0' && payStatus == '31') {
    return 'green'
  }else if (orderStatus == '0' && payStatus == '32') {
    return 'red'
  }else if (orderStatus == '10' && payStatus == '20') {
    return 'blue'

  }else if (orderStatus == '10' && payStatus == '31') {
    return 'blue'

  }else if (orderStatus == '10' && payStatus == '32') {
    return 'red'

  }else if (orderStatus == '20' && payStatus == '20') {
    return 'cyan'

  }else if (orderStatus == '20' && payStatus == '31') {
    return 'cyan'

  }else if (orderStatus == '20' && payStatus == '32') {
    return 'red'

  }else {
    if (!orderStatus) {
      return 'green'
    }
    return '#eee'
  }
}

// 订单状态 0 未核销 10已核销 20部分核销
// 支付状态 20：已支付，31：部分退款，32：全额退款

const statusToText = (orderStatus?:string|number, payStatus?:string|number) => {
  if (orderStatus == '0' && payStatus == '20') {
    return '已支付未核销'
  }else if (orderStatus == '0' && payStatus == '31') {
    return '未核销'
  }else if (orderStatus == '0' && payStatus == '32') {
    return '全额退款'
  }else if (orderStatus == '10' && payStatus == '20') {
    return '已核销'
  }else if (orderStatus == '10' && payStatus == '31') {
    return '已核销'
  }else if (orderStatus == '10' && payStatus == '32') {
    return '全额退款'
  }else if (orderStatus == '20' && payStatus == '20') {
    return '部分核销'
  }else if (orderStatus == '20' && payStatus == '31') {
    return '部分核销'
  }else if (orderStatus == '20' && payStatus == '32') {
    return '全额退款'
  }else if (!orderStatus && payStatus == '32') {
    return '全额退款'
  }else if (!orderStatus && payStatus == '20') {
    return '已支付'
  }else if (!orderStatus && payStatus == '31') {
    return '部分退款'
  }else {
    if (!orderStatus) {
      return '已支付'
    }
    return ''
  }
}


const goToMt = (i: number, type:number) => {
  if (type ==2 ) {
    refundIndex.value = i
  }else {
    payIndex.value = i
  }

  const params = {
    type: 'STAFF_ORDER_LIST',
    bizParam: {
       
      }
  }
  banquetApi.clientInvokeLogin(params).then(res => {
    setTimeout(() => {
      payIndex.value = -1
      refundIndex.value = -1

    }, 3000);
    window.location.href = res
  }).catch(err => {
    setTimeout(() => {
      payIndex.value = -1
      refundIndex.value = -1

    }, 3000);
  })
}



const loadorderList = () => {
  defaultParams.value.pageNum++;
  banquetReservationApi.list(defaultParams.value).then((res) => {
    // 加载状态结束
    orderLoading.value = false;
    orderList.value = [...orderList.value, ...res.records];
    orderTotal.value = res.total ?? 0;
    // 数据全部加载完成
    if (orderList.value.length >= orderTotal.value) {
      orderFinished.value = true;
    }
  });
};
const routeStatus = ref<number | string>()
onMounted(() => {
  route.value = getCurrentRoute()
  routeStatus.value = route.value?.query?.status ? Number(route.value?.query?.status) : ''
})

watch(
  () => defaultParams.value.timeType,
  (val: string | undefined) => {
    switch (val) {
      case 'week':
        defaultParams.value.mealTimes[1] = dayjs().format('YYYY-MM-DD');
        defaultParams.value.mealTimes[0] = dayjs().subtract(7, 'day').format('YYYY-MM-DD');
        break;

      case 'month':
        defaultParams.value.mealTimes[1] = dayjs().format('YYYY-MM-DD');
        defaultParams.value.mealTimes[0] = dayjs().subtract(1, 'month').format('YYYY-MM-DD');
        break;
      case 'year':
        defaultParams.value.mealTimes[1] = dayjs().format('YYYY-MM-DD');
        defaultParams.value.mealTimes[0] = dayjs().subtract(1, 'year').format('YYYY-MM-DD');
        break;

      default:
        break;
    }
  },
);

watch(
  () => routeStatus.value,
  (newValue, oldValue) => {
    defaultParams.value.orderStatus = newValue
  },
);

watch(
  () => defaultParams.value.orderStatus,
  (newValue, oldValue) => {

    reSearch();

  },

)

// 清空
const reSet = () => {
  defaultParams.value = {
    restaurantName: '',
    timeType: '',
    pageNum: 0,
    pageSize: 20,
    orderBookingCode: '',
    sceneType: '', // 订单状态
    mealTimes: []
  };
};

// 搜索
const menuRef = ref(null);
const reSearch = () => {
  orderLoading.value = true
  orderFinished.value = false
  defaultParams.value.pageNum = 0;
  orderList.value = []
  loadorderList();
  // menuRef.value.close();
};

// 时间选择相关
const showTimePicker = ref<boolean>(false);

const minDate = ref(new Date(2024, 0, 1));
const maxDate = ref(new Date(2026, 0, 1));
const choseTimeType = ref('');
const currentDate = ref<Array<string>>([]);

const openTimePicker = (type: string) => {
  choseTimeType.value = type;
  currentDate.value = [];
  if (defaultParams.value.mealTimes[0]) {
    const minDateArr = defaultParams.value.mealTimes[0].split('-');
    minDate.value = new Date(parseInt(minDateArr[0]), parseInt(minDateArr[1]) - 1, parseInt(minDateArr[2]));
  }
  if (defaultParams.value.mealTimes[1]) {
    const maxDateArr = defaultParams.value.mealTimes[1].split('-');
    maxDate.value = new Date(parseInt(maxDateArr[0]), parseInt(maxDateArr[1]) - 1, parseInt(maxDateArr[2]));
  }

  if (type == 'begin') {
    minDate.value = new Date(2024, 0, 1);
    currentDate.value = defaultParams.value.mealTimes[0]?.split('-');
  } else {
    maxDate.value = new Date(2034, 0, 1);
    currentDate.value = defaultParams.value.mealTimes[1]?.split('-');
  }

  showTimePicker.value = true;
};

const confirmTime = ({ selectedValues }: any) => {
  if (choseTimeType.value == 'begin') {
    defaultParams.value.mealTimes[0] = selectedValues.join('-');
  } else {
    defaultParams.value.mealTimes[1] = selectedValues.join('-');
  }

  if (defaultParams.value.mealTimes[0] && defaultParams.value.mealTimes[1]) {
    defaultParams.value.timeType = '';
  }

  showTimePicker.value = false;
};

const goToDetail = (id: string) => {
  router.push({ path: '/banquet/reservation/detail', query: { id: id } });
};
const mtIndex = ref(-1)
const goToWmDetail = (id: any, index: number) => {
  mtIndex.value = index
  const params = {
    type: "WM_ORDER_DEATIL",
    bizParam: {
      sqtBizOrderId: id
    }
  }
  banquetApi.clientInvokeLogin(params).then(res => {
    window.location.href = res
    setTimeout(() => {
      mtIndex.value = -1
    }, 3000);
  }).catch(err => {
    setTimeout(() => {
      mtIndex.value = -1
    }, 3000);
  })
}


</script>

<style lang='less' scoped>
@import url(../common.less);

:deep(.van-dropdown-menu__bar) {
  //  background: var(--van-dropdown-menu-background);
  box-shadow: none;
}

.list-search {
  :deep(.van-search__action) {
    // padding: 0;
  }
}

.list-search-btn {
  width: 110px;
}

.my-radio {
  :deep(.van-radio__icon) {
    height: auto !important;
  }
}

.btn-com {
  width: 70px;
}

.input-border {
  border: 1px solid #dcdee0;
  border-radius: 20px;
  padding: 2px 10px;
}

.van-list-box {
  padding: 8px 10px;
}

.order-item {
  // height: 260px;

  .order-item-left {
    width: 10px;
    height: 100%;
    background: url('@/assets/image/banquet/order/step.png') no-repeat;
    background-size: cover;

  }

  .order-item-right {
    flex: 1;
    flex-direction: column;

    .order-item-title-left {
      color: rgba(0, 0, 0, 0.5);
    }


  }
}

:deep(.van-search__action:active) {
  background-color: rgba(0, 0, 0, 0)
}

.icon-size {
  height: 16px;
}

.apply-btns {
  display: flex;
  justify-content: flex-end;

  :deep(.van-button) {
    margin-right: 4px;
  }

  :nth-last-child(1) {
    margin-right: 0;
  }
}

:deep(.van-cell__title) {
  color: rgba(20, 21, 3, 0.6);
}

:deep(.van-cell__value) {
  color: rgba(20, 21, 3, 0.8);
}

.mb-8 {
  margin-bottom: 8px;
}
.my-cell{
  padding-top: 5px ;
  padding-bottom: 5px;
}
</style>