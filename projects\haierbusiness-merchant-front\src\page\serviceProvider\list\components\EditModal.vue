<script setup lang="ts">
import { computed, onMounted, reactive, nextTick, watch, ref } from 'vue';
import { useServiceProviderListStore } from '../store';
import { Form } from 'ant-design-vue';
import { defaultBusinessDetail } from '../store';
import { log } from 'node:console';

const props = defineProps({
  modelValue: {
    type: Boolean,
  },
  getDataList: Function,
});
const emit = defineEmits(['update:modelValue']);

const store = useServiceProviderListStore();
const useForm = Form.useForm;

// 创建一个响应式的表单数据对象
const formState = ref({ ...store.businessDetail });

// 监听store中的businessDetail变化，同步到本地formState
watch(
  () => store.businessDetail,
  (newVal) => {
    if (newVal) {
      formState.value = { ...newVal };
    }
  },
  { deep: true, immediate: true },
);

const formRules = reactive({
  name: [
    {
      required: true,
      message: '服务商名称必填',
    },
  ],
  code: [
    {
      required: true,
      message: '服务商编码必填',
    },
  ],
  enterpriseId: [
    {
      required: true,
      message: '所属企业必填',
    },
  ],
  enterpriseCode: [
    {
      required: true,
      message: '企业编码必填',
    },
  ],
  unifiedSocialCreditCode: [
    {
      required: true,
      message: '统一社会信用代码必填',
    },
  ],
});

const { resetFields, validate, validateInfos } = useForm(formState, formRules, {
  validateOnRuleChange: false, // 规则变化时不自动验证
});

const modelOpen = computed({
  get: () => props.modelValue,
  set: (v) => emit('update:modelValue', v),
});

// 监听弹窗打开状态，当打开时重置表单状态
watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      // 无论是编辑还是新增，都确保表单数据与store同步
      nextTick(() => {
        formState.value = { ...store.businessDetail };
        // 编辑模式下不重置表单，保留已有数据
        if (!store.businessDetail.id) {
          resetFields();
        }
      });
    }
  },
  { immediate: true },
);

const enterpriseOptions = computed(() => {
  let list = [];

  // 处理不同格式的数据
  if (Array.isArray(store.enterpriseList)) {
    list = store.enterpriseList;
  } else if (
    store.enterpriseList &&
    typeof store.enterpriseList === 'object' &&
    'records' in store.enterpriseList &&
    Array.isArray(store.enterpriseList.records)
  ) {
    list = store.enterpriseList.records;
  } else {
    list = [];
  }

  return list.filter((item) => item.code).map((item) => ({ label: item.name, value: item.code }));
});

const handleOk = async () => {
  try {
    await validate();

    // 获取企业列表，与enterpriseOptions使用相同的处理逻辑
    let enterpriseList = [];
    if (Array.isArray(store.enterpriseList)) {
      enterpriseList = store.enterpriseList;
    } else if (
      store.enterpriseList &&
      typeof store.enterpriseList === 'object' &&
      'records' in store.enterpriseList &&
      Array.isArray(store.enterpriseList.records)
    ) {
      enterpriseList = store.enterpriseList.records;
    }

    const params = {
      ...formState.value,
      enterpriseId: enterpriseList.find((item) => item.code === formState.value.enterpriseCode)?.id,
    };
    await (formState.value.id ? store.editBusiness : store.addBusiness)(params);
    props.getDataList?.();
    handleCancel();
  } catch (error) {
    console.error('校验未通过', error);
  }
};

const handleCancel = () => {
  resetFields();
  store.businessDetail = { ...defaultBusinessDetail };
  modelOpen.value = false;
};

onMounted(() => {
  store.getEnterpriseList();
});

const handleMerchantSelect = (value: any) => {
  console.log(value);
  formState.value.enterpriseCode = value;

  // 获取企业列表，与enterpriseOptions使用相同的处理逻辑
  let enterpriseList = [];
  if (Array.isArray(store.enterpriseList)) {
    enterpriseList = store.enterpriseList;
  } else if (
    store.enterpriseList &&
    typeof store.enterpriseList === 'object' &&
    'records' in store.enterpriseList &&
    Array.isArray(store.enterpriseList.records)
  ) {
    enterpriseList = store.enterpriseList.records;
  }

  // 根据选择的企业编码找到对应的企业名称
  const selectedEnterprise = enterpriseList.find((item) => item.code === value);
  if (selectedEnterprise) {
    formState.value.enterpriseName = selectedEnterprise.name;
  }
};
</script>

<template>
  <a-modal
    :width="600"
    :title="`${formState.id ? '编辑' : '新增'}服务商`"
    v-model:open="modelOpen"
    class="edit-modal"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="edit-modal-content">
      <a-form class="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="服务商名称" v-bind="validateInfos.name">
          <a-input v-model:value="formState.name" placeholder="请输入服务商名称" />
        </a-form-item>
        <a-form-item label="服务商编码" v-bind="validateInfos.code">
          <a-input v-model:value="formState.code" placeholder="请输入服务商编码" />
        </a-form-item>
        <a-form-item label="所属企业" v-bind="validateInfos.enterpriseId">
          <a-select
            :style="{ width: '100%' }"
            v-model:value="formState.enterpriseName"
            :options="enterpriseOptions"
            placeholder="请选择所属企业"
            show-search
            optionFilterProp="label"
            @select="handleMerchantSelect"
          />
        </a-form-item>
        <a-form-item label="企业编码" v-bind="validateInfos.code">
          <a-input v-model:value="formState.enterpriseCode" placeholder="请输入企业编码" disabled />
        </a-form-item>
        <a-form-item label="统一社会信用代码" v-bind="validateInfos.unifiedSocialCreditCode">
          <a-input v-model:value="formState.unifiedSocialCreditCode" placeholder="请输入统一社会信用代码" />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<style lang="less" scoped>
.edit-modal {
  .edit-modal-content {
    padding: 10px;
    max-height: 70vh;
    overflow: auto;

    .group-title {
      color: rgba(0, 0, 0, 0.88);
      font-weight: 600;
      line-height: 1.5;
      margin-bottom: 15px;
    }
  }
}
:deep(.ant-form-item-required::before) {
  display: none !important;
}
</style>
