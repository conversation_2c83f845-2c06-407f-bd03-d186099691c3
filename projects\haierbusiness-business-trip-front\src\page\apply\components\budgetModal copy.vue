
<script lang="ts" setup>
import { Dayjs } from 'dayjs';
import { reactive, ref, toRaw } from 'vue';
import type { UnwrapRef } from 'vue';
import type { Rule } from 'ant-design-vue/es/form';
import {
  Anchor as hAnchor,
  <PERSON>ton as hButton,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Form as hForm,
  Modal as hModal,
  Row as hRow,
  Col as hCol,
  FormItem as hFormItem,
    Cascader as hCascader,
  Input as hInput
} from 'ant-design-vue';
import { IBudgetType, IBudgetForm} from '@haierbusiness-front/common-libs';

const visible = ref<boolean>(false);

const prop = defineProps({
  budgetModalOpen: Boolean,
})

const show = () => {
  visible.value = true;
};
defineExpose({
  show,
});
const formRef = ref();
const formState: UnwrapRef<IBudgetForm> = reactive({
  budgetType: '', // 预算归属
  settlementUnit: '', // 结算单位
  cost: '', // 费用
  money: '18,222', // 预算可用金额
  cos: '', // 卡奥斯项目
  rd:'', // 研发项目
});


const budgetTypeOptions = ref<Array<IBudgetType>>([
  {
    id: 1,
    name: '个人'
  },
  {
    id: 2,
    name: '部门'
  },
])

const rules: Record<string, Rule[]> = {

  budgetType: [{ required: true, message: '请选择结算单位', trigger: 'change' }],
  date1: [{ required: true, message: 'Please pick a date', trigger: 'change', type: 'object' }],
  type: [
    {
      type: 'array',
      required: true,
      message: 'Please select at least one activity type',
      trigger: 'change',
    },
  ],
  resource: [{ required: true, message: 'Please select activity resource', trigger: 'change' }],
  desc: [{ required: true, message: 'Please input activity form', trigger: 'blur' }],
};
const onSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      console.log('values', formState, toRaw(formState));
    })
    .catch((error) => {
      console.log('error', error);
    });
};
const resetForm = () => {
  formRef.value.resetFields();
};

const owner = ref([
  {
    value: 'persional',
    label: '个人',
    children: [
      {
        value: '1101',
        label: '小李',
      },
    ],
  },
  {
    value: 'deptment',
    label: '部门',
    children: [
      {
        value: '11',
        label: '研发部',
        children: [
          {
            value: '1102',
            label: '小王',
          },
        ],
      },
    ],
  },
]);

const labelCol = { span: 5 };
const wrapperCol = { span: 13 }
</script>

<template>
  <h-modal :maskClosable="false" v-model:open="visible" title="选择预算" width="800px">
    <h-form class="mt-30" ref="formRef"  :model="formState" :wrapper-col="wrapperCol" :label-col="labelCol" :rules="rules">
      <h-row>
        <h-col :span="8">
          <h-form-item label="预算归属" :labelCol="{ span: 10, offset: 0 }" name="budgetType">
            <h-cascader
                v-model:value="formState.budgetType"
                :options="owner"
                placeholder="预算归属"
                change-on-select
              >
              
            </h-cascader>
          </h-form-item>
        </h-col>
        <h-col :span="8">
          <h-form-item label="结算单位" :labelCol="{ span: 10, offset: 0 }" name="settlementUnit">
            <h-select show-search  v-model:value="formState.settlementUnit" :filter-option="filterBudgetType" :fieldNames="{label: 'name', value: 'id'}" class="my-selsect" :options="budgetTypeOptions"  placeholder="选择城市" />
          </h-form-item>
        </h-col>
        <h-col :span="8" v-if="true">
          <h-form-item label="卡奥斯项目" :labelCol="{ span: 10, offset: 0 }" name="cos">
            <h-select show-search  v-model:value="formState.cos" :filter-option="filterBudgetType" :fieldNames="{label: 'name', value: 'id'}" class="my-selsect" :options="budgetTypeOptions"  placeholder="选择城市" />
          </h-form-item>
        </h-col>
      </h-row>

      <h-row>
        <h-col :span="8">
          <h-form-item label="费用项目" :labelCol="{ span: 10, offset: 0 }" name="cost">
            <h-select show-search  v-model:value="formState.cost" :filter-option="filterBudgetType" :fieldNames="{label: 'name', value: 'id'}" class="my-selsect" :options="budgetTypeOptions"  placeholder="选择城市" />
          </h-form-item>
        </h-col>
        <h-col :span="8">
          <h-form-item label="预算可用金额" :labelCol="{ span: 10, offset: 0 }" name="money">
            <h-input v-model:value="formState.money" :bordered="false" disabled  placeholder="" />
          </h-form-item>
        </h-col>
        <h-col :span="8">
          <h-form-item label="研发项目" :labelCol="{ span: 10, offset: 0 }" name="rd">
            <h-select show-search  v-model:value="formState.rd" :filter-option="filterBudgetType" :fieldNames="{label: 'name', value: 'id'}" class="my-selsect" :options="budgetTypeOptions"  placeholder="选择城市" />
          </h-form-item>
        </h-col>
      </h-row>
    </h-form>
  </h-modal>
</template>

<style scoped>
.mb-30 {
  margin-bottom: 30px;
}
.ant-row {
  margin-bottom: 20px;
}

</style>