<template>
    <div id="map-airline" v-loading="loading" background="rgba(0,0,0,0)"></div>
</template>
<script lang="ts" setup>
    import { Scene, LineLayer } from '@antv/l7';
    import { GaodeMap } from '@antv/l7-maps';
    import { ref } from "vue";
    import { queryAirlineMapData } from "@haierbusiness-front/apis/src/data/board/travel";
    import { EventBus } from "../../../eventBus";
    import chinaData from "@/assets/geojson/china.json";
    const loading = ref(false);
    let scene;
    EventBus.on((event)=>{
        if(event=="refresh"){
            queryData();
        }
    })
    const queryData = async ()=>{
        loading.value = true;
        const data  = await queryAirlineMapData();
        console.log("data",data);
        const { rows } = data;
        const sourceData = [];
        rows.forEach(item=>{
            if(item[2]&&item[3]&&item[4]&&item[5]){
                sourceData.push({
                    a:item[0],
                    b:item[1],
                    x:item[2],
                    y:item[3],
                    x1:item[4],
                    y1:item[5],
                    value:item[6]
                })
            }
        })
        init(sourceData);
        loading.value = false;
    }
    const init = (sourceData) => {
        scene && scene.destroy();
        scene = new Scene({
            id: 'map-airline',
            logoVisible:false,
            map: new GaodeMap({
                style: 'dark',
                center: [ 107.77791556935472, 32.443286920228644 ],
                zoom: 3
            })
        });
        const layer = new LineLayer({})
        .source(sourceData, {
            parser: {
                type: 'json',
                x:"x",
                y:"y",
                x1:"x1",
                y1:"y1"
            }
        })
        .size("value",[1,10])
        .shape('arc')
        .color('#8C1EB2')
        .style({
            opacity: 0.8,
            blur: 0.99,
        })
        .animate({
            duration: 4,
            interval: 0.2,
            trailLength:1,
        });
        scene.addLayer(layer);
    }
</script>
<style scoped>
    #map-airline {
        height: 73vh;
        position: relative;
    }
</style>