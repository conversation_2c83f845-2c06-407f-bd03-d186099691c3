import {
    IControlProcessExecuteRequest,
    IControlProcessResponse,
    IProcessBusinessDetailsUrl,
    IProcessDetailsRequest,
    IProcessRecordList,
    IPageResponse,
    IProcessRecordListRequest,
    AddSceneParams,
    IControlLogRequest,
    IControlLogList,
    IProcessListRequest,
    IProcessIno,
    IRule,
    Result,
    IDefinedProcess
} from '@haierbusiness-front/common-libs'
import { get, post } from '../request'

export const processApi = {
    /**
      * 查看审批流定义
      */
    definedDetail: (pdId: number): Promise<IDefinedProcess> => {
        return get('process/api/defined/details', { pdId })
    },
    /**
     * 
     * 批量获取子流程
     */
    detailsBatch: (pdIds:string[]): Promise<IDefinedProcess> => {
        return get('process/api/defined/detailsBatch',{pdIds})
    },
    /**
      * 查看审批流定义列表不分页
      */
    listDefined: (): Promise<IDefinedProcess> => {
        return get('process/api/defined/listDefined')
    },
    /**
     * 查询审批详情
     */
    details: (params: IProcessDetailsRequest): Promise<IControlProcessResponse> => {
        return get('process/api/control/details', params)
    },

    /**
     * 审批
     */
    execute: (params: IControlProcessExecuteRequest): Promise<IControlProcessResponse> => {
        return post('process/api/control/execute', params)
    },

    /**
     * 审批
     */
    revoke: (code: string): Promise<IControlProcessResponse> => {
        return post('process/api/control/execute/revoke', { code })
    },

    /**
     * 查询审批业务详情地址
     */
    businessDetailsUrl: (params: IProcessBusinessDetailsUrl): Promise<string> => {
        return get('process/api/control/business/details', params)
    },

    /**
     * 查询待办已办
     */
    list: (params: IProcessRecordListRequest): Promise<IPageResponse<IProcessRecordList>> => {
        return get('process/api/control/list', params)
    },

     /**
     * 新增场景
     */
    addScene: (params: AddSceneParams): Promise<IPageResponse<IProcessRecordList>> => {
        return post('process/api/defined/add/scene', params)
    },
    /**
     * 编辑场景
     */
    updateScene: (params: AddSceneParams): Promise<IPageResponse<IProcessRecordList>> => {
        return post('process/api/defined/update/scene', params)
    },
     /**
     * 场景列表
     */
    sceneList: (params: AddSceneParams): Promise<IPageResponse<IProcessRecordList>> => {
        return post('process/api/defined/list/scene', params)
    },
    /**
     * 场景列表
     */
    reSend: (params: any): Promise<IPageResponse<any>> => {
        return post('process/api/message/reSend', params)
    },
    /**
     * 查询日志
     */
    logs: (params: IControlLogRequest): Promise<IPageResponse<IControlLogList>> => {
        return get('process/api/control/logs', params)
    },

    pages: (params: IProcessListRequest): Promise<IPageResponse<IRule>> => {
        return get('process/api/defined/page', params)
    },

    save: (params: IRule): Promise<Result> => {
        return post('process/api/defined/save', params)
    },

    edit: (params: IRule): Promise<Result> => {
        return post('process/api/defined/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('process/api/defined/delete', { id })
    },

    saveSub: (params: IProcessIno, error: ((error: any) => any) | undefined): Promise<Result> => {
        return post('process/api/defined/save/subset', params, undefined, error, true)
    },

    editSub: (params: IProcessIno): Promise<Result> => {
        return post('process/api/defined/update/subset', params)
    },

    removeSub: (id: number): Promise<Result> => {
        return post('process/api/defined/delete/subset', { id })
    },
    /**
     * 新增待审 /defined/addAgent
     */
    addAgent: (params: any): Promise<IDefinedProcess> => {
        return get('process/api/defined/addAgent',params)
    },
    /**
     * 新增代审 /defined/updateAgentById
     */
    updateAgentById: (params: any): Promise<IDefinedProcess> => {
        return get('process/api/defined/updateAgentById',params)
    },
    /**
     * 代审列表 
     */
    searchAgentList: (params: any): Promise<IPageResponse<IProcessRecordList>> => {
        return get('process/api/defined/searchAgent', params)
    },
    // 删除代审
    deleteAgentById: (id: number): Promise<Result> => {
        return get('process/api/defined/deleteAgentById', { id })
    },
}
