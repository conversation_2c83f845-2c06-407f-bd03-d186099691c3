import { download, get, post, filepost, originalGet } from '../request'

export const waterworkCartApi = {
    list: (params: any): Promise<void> => {
        return get('waterworks/api/tshoppingCart/page', params)
    },
    listAll: (params: any): Promise<void> => {
        return get('waterworks/api/tshoppingCart/list', params)
    },
    add: (params: any): Promise<void> => {
        return post('waterworks/api/tshoppingCart/save', params)
    },
    update: (params: object): Promise<void> => {
        return post(`waterworks/api/tshoppingCart/update`, params);
    },
    export: (params: any): Promise<void> => {
        return download('waterworks/api/tshoppingCart/export', params)
    },
    delete: (ids: string): Promise<void> => {
        return post(`waterworks/api/tshoppingCart/delete/${ids}`);
    },
}