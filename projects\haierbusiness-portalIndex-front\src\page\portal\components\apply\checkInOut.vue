<script setup lang="ts">
import { onMounted, ref, watch, computed } from 'vue';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';

const emit = defineEmits(['setHotelInDate','setHotelLeaveDate'])

interface Props {
    hotelInDate?: string;
    hotelLeaveDate?:string ;
}
const props = withDefaults(defineProps<Props>(), {
    hotelInDate: '',
    hotelLeaveDate: ''
});

watch(props, (newValue) => {
    hotelInDate.value = newValue.hotelInDate
    hotelLeaveDate.value = newValue.hotelLeaveDate
})

const hotelInDate = ref(props.hotelInDate)
const hotelLeaveDate = ref(props.hotelLeaveDate)

const leaveDateChange = (val) => {
    emit('setHotelLeaveDate', val)
}
const inDateChange = (val) => {
    emit('setHotelInDate', val)
}


const num = computed(() => {
    if(props.hotelInDate && props.hotelLeaveDate) {
        return dayjs(props.hotelLeaveDate).diff(dayjs(props.hotelInDate), 'day')
    }
    return 0
})

const disabledInDate = (current: Dayjs) => {
  if(props.hotelLeaveDate) {
    return (current && current < dayjs().endOf('day')) || (current && current > dayjs(props.hotelLeaveDate).endOf('day'))

  }else {
    return current && current < dayjs().endOf('day');
  }
};

const disabledOutDate = (current: Dayjs) => {
  if(props.hotelInDate) {
    return current && current < dayjs(props.hotelInDate).endOf('day')

  }else {
    return current && current < dayjs().endOf('day');
  }
};

</script>

<template>
    <div class="apply-date-picker-component">
        <div class="ticket-item ticket-item-date">
            <div class="left-time">
                <div class="item-labels">入住日期</div>
                <a-date-picker v-model:value="hotelInDate" :disabled-date="disabledInDate" @change="inDateChange" valueFormat="YYYY-MM-DD" :bordered="false" class="item-time" placeholder="请选择">
                    <template #suffixIcon>  
                    </template>
                </a-date-picker>
            </div>
            <div class="night">-&nbsp;<span class="num">共{{ num }}晚</span>&nbsp;-</div>
            <div class="right-time">
                <div class="item-labels item-labels-right">离店日期</div>
                <a-date-picker v-model:value="hotelLeaveDate" :disabled-date="disabledOutDate" @change="leaveDateChange" valueFormat="YYYY-MM-DD" :bordered="false" placeholder="请选择">
                    <template #suffixIcon>  
                    </template>
                </a-date-picker>
            </div>   
        </div>
    </div>
</template>

<style lang="less" scoped>
@import url('./common.less');

.pointer {
    cursor: pointer;
}

.ticket-item-date{
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
    .night{
        font-weight: 400;
        font-size: 14px;
        width: 84px;
        color: rgba(0,0,0,0.15);
        display: flex;
        justify-content: center;
    }
}

.left-time, .right-time {
    width: 150px;
}

.num {
    color:rgba(0,0,0,0.85);
}

.item-labels-right {
    text-align: right;
    margin-right: 16px;
}

.right-time {
    :deep(.ant-picker){
        .ant-picker-input{
            input{
                text-align: right;
            }
            input::placeholder{
                text-align: right;
            }
        }
    }
}




</style>

<style>

</style>
