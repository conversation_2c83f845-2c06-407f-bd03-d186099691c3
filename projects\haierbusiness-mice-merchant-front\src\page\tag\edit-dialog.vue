<script lang="ts" setup>
import { Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
     Input as hInput, Textarea as hTextarea } from 'ant-design-vue';
import { computed, ref, watch } from "vue";
import type { Ref } from "vue";
import {
  ITag
} from '@haierbusiness-front/common-libs';

interface Props {
    show: boolean;
    data: ITag | null;
}

const props = withDefaults(defineProps<Props>(), {
show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: ITag = {
    tagName: '',
    businessType: '',
    state: 0,
    description: ''
};

const rules = {

};

const tag: Ref<ITag> = ref(
({ ...props.data } as ITag) || defaultData
);

watch(props, (newValue) => {
    tag.value = ({ ...newValue.data } as ITag) || defaultData;
});

const emit = defineEmits(["cancel", "ok"]);

const visible = computed(() => props.show);

const handleOk = () => {
confirmLoading.value = true;
from.value
    .validate()
    .then(() => {
    emit("ok", tag.value, () => {
        confirmLoading.value = false;
    });
    })
    .catch(() => {
        confirmLoading.value = false;
    });
};
</script>

<template>
    <h-modal
      v-model:visible="visible"
      :title="tag.id ? '编辑标签' : '新增标签'"
      :width="600"
      @cancel="$emit('cancel')"
      :confirmLoading="confirmLoading"
      @ok="handleOk"
    >
      <h-form
        ref="from"
        :model="tag"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
        :rules="rules"
      >
      <h-form-item label="标签名称" name="merName">
        <h-input v-model:value="tag.tagName" />
      </h-form-item>
      <h-form-item label="类别" name="businessType">
        <h-select style="width: 100%" v-model:value="tag.businessType" allow-clear>
              <h-select-option value="1" >订餐</h-select-option>
              <h-select-option value="2" >订房</h-select-option>
              <h-select-option value="3" >会展</h-select-option>
            </h-select>
      </h-form-item>
      <h-form-item label="状态" name="state">
        <h-select v-model:value="tag.state">
            <h-select-option :value='1'>是</h-select-option>
            <h-select-option :value='0'>否</h-select-option>
        </h-select>
      </h-form-item>
      <h-form-item label="描述" name="description">
        <h-textarea v-model:value="tag.description" />
      </h-form-item>
      </h-form>
    </h-modal>
</template>


<style lang="less" scoped>
.important {
color: red;
}
</style>
  