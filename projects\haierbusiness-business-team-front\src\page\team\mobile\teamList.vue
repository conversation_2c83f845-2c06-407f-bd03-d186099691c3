<template>
  <div class="mobile-box">
    <!-- <van-nav-bar title="团队订单列表" left-arrow fixed>
      <template #left>
        <van-icon name="arrow-left" color="#000" size="24" />
      </template>
    </van-nav-bar> -->

    <van-tabs v-model:active="params.destineStatus">
      <van-tab name="" title="全部"></van-tab>
      <van-tab v-for="item,index in teamListState" :name="item.value" :title="item.label" :key="index +1">
      </van-tab>
    </van-tabs>
    
    <van-list class="my-list" v-model:loading="teamLoading" :finished="teamFinished" :finished-text="teamList.length ? '没有更多了' : ''" @load="loadteamList">
      <van-cell-group inset class="mt-10"  v-for="(item, index) in teamList" :key="index" @click="goToDetail(item.id)">
        <van-cell   >
          <template #title>
            <div class="weight600 font-size-12">{{ item.destineNo }}</div>
          </template>
          <template #value>
            <div class=" font-size-12">{{ item.gmtCreate }}</div>
          </template>
          <!-- <template #label>
            <div class=" font-size-12" style="text-align: right;">{{ item.gmtCreate }}</div>
          </template> -->
        </van-cell>

        <van-cell class="cell-item">
          <template #title>
            <van-row >
              <van-col :span="8">出发日期:</van-col>
              <van-col :span="16" class="font-size-10">{{ formatTime(item.beginDate) }}</van-col>
            </van-row>
            <van-row >
              <van-col :span="8">到达日期:</van-col>
              <van-col :span="16" class="font-size-10">{{ formatTime(item.endDate) }}</van-col>
            </van-row>
            <van-row>
              <van-col :span="8">产品类型:</van-col>
              <!-- destineInfo -->
              <van-col :span="16">{{ toLabel(item.destineInfo) }}</van-col>
            </van-row>
            <van-row>
              <van-col :span="8">起止城市:</van-col>
              <van-col :span="16">{{ item.beginCityName }} - {{ item.endCityName }}</van-col>
            </van-row>
            
          </template>
          <template #value>
            <div>
              <van-tag :color="teamListStateTagColorMap[item.destineStatus]" size="medium">{{ TeamListStatusEnum[item.destineStatus] || '' }}</van-tag>
            </div>
          </template>
        </van-cell>

        <van-cell>
          <template #value>
            <div>
              <van-button  size="small" v-if="item.destineStatus == '10'" @click.stop="goToSubmit(item.id)">继续提交</van-button>
              <van-button class="ml-10" type="primary" size="small">详情</van-button>
              <van-button class="ml-10"  type="danger" size="small" v-if="item.destineStatus != '40' && item.destineStatus != '30' && item.destineStatus != '10' && item.destineStatus != '90'" @click.stop="recallTeam(item.id)">撤回</van-button>
            </div>
          </template>
        </van-cell>
      </van-cell-group>
      <div class="empty-box" v-if="!teamLoading && teamList.length < 1">
        暂无数据
      </div>
    </van-list>

    <van-sticky :offset-bottom="0" position="bottom">
      <div style=" padding: 10px 16px ; background-color: #fff;" >
        <van-button block round type="primary" @click="goToAdd">创建团队申请单</van-button>

      </div>
    </van-sticky>

  </div>
</template>

<script setup lang='ts' >
import {
  IUserListRequest,
  TCteateTeam,
  IUserInfo,
  ICity,
  ITripInfo,
  ICreatTrip,
  ITraveler,
  TGetListParams,
  TeamListStatusEnum,
  teamListStateTagColorMap
} from '@haierbusiness-front/common-libs';
import { getEnumOptions } from '@haierbusiness-front/utils';

import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { debounce, values } from 'lodash';
import { teamApi } from '@haierbusiness-front/apis';
import { createVNode, onMounted, reactive, ref,computed, watch, watchEffect, toRefs } from 'vue';

import type { Ref } from 'vue';
import { Item } from 'ant-design-vue/es/menu';
import { spawn } from 'child_process';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { debug } from 'console';
import { showConfirmDialog } from 'vant';


const router = getCurrentRouter()
const store = applicationStore();

const { loginUser } = storeToRefs(store);

const formatTime = (time: string|undefined) => {
  if(!time) {
    return '-'
  }
  const timeArr = time.split('-')
  return `${timeArr[0]}年${timeArr[1]}月${timeArr[2]}日`
}


// 订单列表
const teamLoading = ref<boolean>(false);
const teamFinished = ref<boolean>(false);
const teamList = ref<Array<TCteateTeam>>([])
const teamTotal = ref<number>(0);
const defaultParams: TGetListParams = {
  pageNum: 0,
  pageSize: 10,
  destineStatus:""
};
const params: Ref<TGetListParams> = ref(defaultParams);

const loadteamList = () => {
  params.value.pageNum++;
  teamApi.pageList(params.value).then((res) => {
    // 加载状态结束
    teamLoading.value = false;
    teamList.value = [...teamList.value, ...res.records];
    teamTotal.value = res.total ?? 0;
    // 数据全部加载完成
    if (teamList.value.length >= teamTotal.value) {
      teamFinished.value = true;
    }
  });
};

const goToDetail = (id?: string) => {
  router.push({ path: "/mobile/teamDetail", query: { id:id  }  } )
}

const goToAdd = () => {
  router.push({ path: "/mobile/addTeam"} )
}



const productList = ref([
  {
    label: '国内机票',
    plain: true,
    value: 0,
    disabled: false,
  },
  {
    label: '国际机票',
    plain: true,
    value: 1,
    disabled: false,
  },
  {
    label: '酒店',
    plain: true,
    value: 2,
  },
]);

// 审批状态
const teamListState = computed(() => {
  return getEnumOptions(TeamListStatusEnum, true);
});

const goToSubmit = (id: string| undefined) => {
  router.push({ path: "/mobile/addTeam", query: { id:id  }  } )
}


const recallTeam = (id: string| undefined) => {
  const param:TCteateTeam = {
    id
  }
  showConfirmDialog({
    title: '您确认吗?',
    message: '确认要撤销这条团队票申请吗?',
  }).then(() => {
    teamApi.recallTeam(param).then((res) => {
      reload()
    });
  }).catch(() => {

  })
  
}

const reload = () => {
  params.value.pageNum = 0
    params.value.pageSize = 10
    teamList.value = []
    teamFinished.value = false
    loadteamList()
}


const toLabel = (str: string) => {
  if (!str) {
    return '-'
  }
  const strList = str.split(',')
  const name:Array<string> = []
  strList.forEach(item => {
    productList.value.forEach(ele => {
      if (item == ele.value) {
        name.push(ele.label)
      }
    });
  })
  return name.join('、')
}

watch(
  () => params.value.destineStatus,
  (val) => {
    reload()
  },
  { deep: true },
)

</script>

<style lang="less" scoped>
@import url(./components/mobile.less);
.cell-item {
  :deep(.van-cell__value) {
    width: 20%;
    flex: none;
  }
}
.mobile-box {
}
.my-list {
  min-height: 80vh;
}
.empty-box {
  height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}
</style>