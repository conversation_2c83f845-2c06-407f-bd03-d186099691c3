<script lang="ts" setup>
import { Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
     Input as hInput, Textarea as hTextarea } from 'ant-design-vue';
import { computed, ref, watch } from "vue";
import type { Ref } from "vue";
import {
  VipType
} from '@haierbusiness-front/common-libs';

interface Props {
    show: boolean;
    data: VipType | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: VipType = {
   id: null,
    employeeId: "",
    employeeName: "",
};

const rules = {
    employeeId: [{ required: true, message: "请填写员工号" }],
    employeeName: [{ required: true, message: "请填写员工姓名" }],
};

const vip: Ref<VipType> = ref(
({ ...props.data } as VipType) || defaultData
);

watch(props, (newValue) => {
    vip.value = ({ ...newValue.data } as VipType) || defaultData;
});

const emit = defineEmits(["cancel", "ok"]);

const visible = computed(() => props.show);

const handleOk = () => {
confirmLoading.value = true;
from.value
    .validate()
    .then(() => {
    emit("ok", vip.value, () => {
        confirmLoading.value = false;
    });
    })
    .catch(() => {
        confirmLoading.value = false;
    });
};
</script>

<template>
    <h-modal
      v-model:visible="visible"
      :title="vip.id ? '编辑员工' : '新增员工'"
      :width="600"
      @cancel="$emit('cancel')"
      :confirmLoading="confirmLoading"
      @ok="handleOk"
    >
      <h-form
        ref="from"
        :model="vip"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
        :rules="rules"
      >
        <h-form-item label="员工号" name="employeeId">
          <h-input v-model:value="vip.employeeId" />
        </h-form-item>
        <h-form-item label="员工姓名" name="employeeName">
          <h-input v-model:value="vip.employeeName" />
        </h-form-item>
      </h-form>
    </h-modal>
</template>


<style lang="less" scoped>
.important {
color: red;
}
</style>
  