import { download, get, post, filepost, originalGet } from '../request'

export const waterworkProdApi = {
    list: (params: any): Promise<void> => {
        return get('waterworks/api/tproduct/page', params)
    },
    listAll: (params: any): Promise<void> => {
        return get('waterworks/api/tproduct/list', params)
    },
    add: (params: any): Promise<void> => {
        return post('waterworks/api/tproduct/save', params)
    },
    update: (params: object): Promise<void> => {
        return post(`waterworks/api/tproduct/update`, params);
    },
    export: (params: any): Promise<void> => {
        return download('waterworks/api/tproduct/export', params)
    },
    delete: (ids: string): Promise<void> => {
        return post(`waterworks/api/tproduct/delete/${ids}`);
    },
}