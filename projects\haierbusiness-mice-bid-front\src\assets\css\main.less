@import  "./scroll-style.less";

@baseWidth: 100vw;
@baseHeight: 100vh;

.base-container {
    height: @baseHeight;
    width: @baseWidth;
}

.float-left {
    float: left;
}

.text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.wid1280{
    width: 1280px;
    margin: 0 auto;
}

// 按钮 - 默认颜色
.ant-btn-primary {
    background-color: #1868db;
}

// 需求提报
.demand_pad24{
    padding: 24px;
    background: #ffffff;
    border-radius: 12px;
}
.demand_title {
    font-weight: 500;
    font-size: 20px;
    color: #1d2129;
    line-height: 28px;
    position: relative;

    .demand_border {
        position: absolute;
        top: 4px;
        left: -24px;

        width: 4px;
        height: 20px;
        background: #1868db;
    }
}

.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item:hover {
    background: #e6f0ff;
}