<script setup lang="ts">
import {
  Dropdown as hDropdown,
  Watermark as hW<PERSON>mark,
  Avatar as hAvatar,
  BreadcrumbItem as hBreadcrumbItem,
  <PERSON>readcrumb as hBreadcrumb,
  LayoutSider as hLayoutSider,
  LayoutFooter as hLayoutFooter,
  Layout<PERSON>ontent as hLayoutContent,
  LayoutHeader as hLayoutHeader,
  Layout as hLayout,
  MenuItem as hMenuItem,
  MenuItemGroup as hMenuItemGroup,
  SubMenu as hSubMenu,
  Menu as hMenu,
  Divider as hDivider,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Result as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Tabs as hTabs,
  Modal as hModal,
  Table as hTable,
  message,
} from 'ant-design-vue';
import { PropType, onMounted, ref, toRef, getCurrentInstance } from 'vue';
import {
  RightOutlined,
  LeftOutlined,
  UploadOutlined,
  UserOutlined,
  NotificationOutlined,
  AppstoreOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BellOutlined
} from '@ant-design/icons-vue';
import EMenu from './EMenu.vue';

import logoWhite from '@/assets/image/logo-white.png';
import notice from './notice.vue';
import {
  HeaderConstant,
  IInSiteMessageFilter,
  IOwnApplicationCodesResponse,
  IResourceInfo,
  IResourceInfoTreeResponse,
  IResourceTreeNode,
  ResourceTypeConstant,
} from '@haierbusiness-front/common-libs';
import { resourceApi, loginApi, inSiteMessageApi } from '@haierbusiness-front/apis';
import { loadDataFromLocal, removeStorageItem, resolveToken } from '@haierbusiness-front/utils';
import { computed, inject, provide } from 'vue';
import { useRouter, RouteLocationNormalizedLoaded } from 'vue-router';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { watch } from 'vue';
import { FrameModelType } from './FrameModelType';
import { themeColor } from '@haierbusiness-front/utils';

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const router = useRouter();

const collapsed = ref<boolean>(false);
const selectedModel = ref<string[]>([]);

const isdev = import.meta.env.MODE

const props = defineProps({
  param: Object as PropType<IResourceInfoTreeResponse[]>,
});
// 父子传值
const frameModel = ref<FrameModelType>(0);
provide('frameModel', frameModel);
const resource = ref<IResourceInfoTreeResponse[]>(props.param ? props.param : []);
const applications = ref<IResourceInfoTreeResponse[]>([]);

let hashUrl = window.location.hash;
if (hashUrl.includes('?')) {
  hashUrl = hashUrl.substring(0, hashUrl.indexOf('?'));
}
if (!hashUrl.endsWith('/')) {
  hashUrl = hashUrl + '/';
}
for (let i of resource.value) {
  let path = i.url ? i.url : '';
  if (i.type === ResourceTypeConstant.MANAGE_APPLICATION.type) {
    path = path.substring(path.indexOf('/#/')).substring(2);
  }
  if (!path.startsWith('#')) {
    path = '#' + path;
  }
  if (!path.endsWith('/')) {
    path = path + '/';
  }

  if (hashUrl.startsWith(path)) {
    selectedModel.value = [i.id as unknown as string];
  }
}

const getCurrentMenuItem = (id: number) => {
  for (let i of resource.value) {
    if (i.id === id) {
      return i.children;
    }
  }
  return null;
};

const eMenu = ref();
const currentMenuItem = ref();
currentMenuItem.value = getCurrentMenuItem(Number(selectedModel.value[0]));
const selectMenu = (value: { item: any; key: any; selectedKeys: any }) => {
  currentMenuItem.value = getCurrentMenuItem(value.key);
  if (eMenu.value) {
    eMenu.value.clean();
  }
};
const switchTag = (item: IResourceInfoTreeResponse) => {
  if (item.url) {
    const urlObject = new URL(item.url);
    urlObject.searchParams.append(HeaderConstant.TOKEN_KEY.key, loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false));
    window.location.href = urlObject.href;
  } else {
    message.error('当前菜单不存在url属性,无法切换');
  }
};
const avatarName = computed(() => {
  if (loginUser?.value?.nickName) {
    const nickName = loginUser.value.nickName;
    if (nickName.length === 2) {
      return nickName;
    } else if (nickName.length > 2 && nickName.length <= 5) {
      return nickName.substring(nickName.length - 2, nickName.length);
    } else {
      return nickName.substring(0, 1).toUpperCase();
    }
  } else {
    return '岳飞';
  }
});

const logout = () => {
  loginApi.haierIamTokenLogout({ token: loginUser?.value?.extended?.iamToken }).finally(() => {
    removeStorageItem(HeaderConstant.TOKEN_KEY.key, false);
    // 登出后清空session
    sessionStorage.clear();
    window.location.reload();
  });
};

const back = () => {
  router.go(-1);
};

const go = () => {
  router.go(1);
};

onMounted(async () => {
  console.log(isdev)
  // todo 正式环境不显示站内信
  if (isdev !== 'production') {
    letterList()
  }
  applications.value = await resourceApi.searchApplicationCodes().then((it) => {
    return it;
  });
});

// 表头tab切换组件相关
const tabsValue = ref<{ title: string; content: string; key: string; closable?: boolean }[]>([]);

const tabsValueActiveKey = ref('');
const changeTab = (key: string, path: string) => {
  const target = router.getRoutes().filter((it) => {
    return it.name === key;
  });
  router.push(path);
};

// 是否关闭上一个tab
const isCloseLastTab = ref<boolean>(false)
provide('isCloseLastTab', isCloseLastTab);

watch(
  [() => router.currentRoute.value],
  (val, oldVal) => {
    addTabValue(val[0]);
    tabsValueActiveKey.value = val[0].name as string;

    if (isCloseLastTab.value) {
      onRemoveTab(oldVal[0].name as string);
      isCloseLastTab.value = false;
    }
  },
  { deep: true },
);
const addTabValue = (val: RouteLocationNormalizedLoaded) => {
  if (val.path !== '/') {
    if (val) {
      const exist = tabsValue.value.find((it) => {
        return it.key === val.name;
      });
      if (!exist) {
        tabsValue.value.push({ title: val.meta.title as string, content: val.fullPath, key: val.name as string });
      } else {
        if (val.fullPath !== exist.content) {
          let index = tabsValue.value.indexOf(exist);
          if (index != undefined && index !== -1) {
            tabsValue.value.splice(index, 1);
            tabsValue.value.push({ title: val.meta.title as string, content: val.fullPath, key: val.name as string });
          }
        }
      }
    }
  }
}

const onRemoveTab = (targetKey: any) => {
  let lastIndex = 0;
  tabsValue.value.forEach((pane, i) => {
    if (pane.key === targetKey) {
      lastIndex = i - 1;
      if (lastIndex < 0) {
        lastIndex = i;
        if (tabsValue.value.length - 1 <= i) {
          lastIndex = -1;
        }
      }
    }
  });
  if (lastIndex !== -1) {
    tabsValue.value = tabsValue.value.filter((pane) => pane.key !== targetKey);
    if (tabsValue.value.length && tabsValueActiveKey.value === targetKey) {
      if (lastIndex >= 0) {
        changeTab(tabsValue.value[lastIndex].key, tabsValue.value[lastIndex].content);
        tabsValueActiveKey.value = tabsValue.value[lastIndex].key;
      } else {
        changeTab(tabsValue.value[lastIndex].key, tabsValue.value[lastIndex].content);
        tabsValueActiveKey.value = tabsValue.value[0].key;
      }
    }
  } else {
    tabsValueActiveKey.value = '';
    tabsValue.value = [];
    router.push('/');
  }
};

{
  addTabValue(router.currentRoute.value);
  tabsValueActiveKey.value = router.currentRoute.value.name as string;
}
const watermark = import.meta.env.VITE_TEST_WATERMARK;
console.log(watermark);

const loading = ref(false);
const letter = ref()
const letterLenght = ref()

// 消息详情弹框相关
const messageDetailVisible = ref(false);
const currentMessageDetail = ref<any>(null);

//获取站内信列表
const letterList = async () => {
  try {
    loading.value = true;
    // 实际接口调用（先注释掉）
    const details = await inSiteMessageApi.oneSelfList({ pageNum: 1, pageSize: 10, isRead: 1 });
    letterLenght.value = details?.total
    letter.value = details.records?.splice(0,5);
  } catch (error) {
    console.error('获取详情失败:', error);
    message.error('获取详情失败，请重试');
  } finally {
    loading.value = false;
  }
}

// 格式化时间显示
const formatTime = (timeStr: string) => {
  if (!timeStr) return '';
  const time = new Date(timeStr);
  const now = new Date();
  const diff = now.getTime() - time.getTime();
  const minutes = Math.floor(diff / (1000 * 60));

  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (minutes < 1440) return `${Math.floor(minutes / 60)}小时前`;
  return `${Math.floor(minutes / 1440)}天前`;
}

// 标记全部已读
const markAllAsRead = async () => {
  try {
    // 获取所有未读消息的ID
    const unreadMessages = letter.value?.filter(item => item.isRead === 1) || [];

    if (unreadMessages.length === 0) {
      message.info('当前没有未读消息');
      return;
    }

    // 批量调用state接口标记为已读
    const promises = unreadMessages.map(item =>
      inSiteMessageApi.state(item.id)
    );

    await Promise.all(promises);
    message.success('已标记全部已读');
    letterList(); // 重新获取列表
  } catch (error) {
    console.error('标记已读失败:', error);
    message.error('操作失败，请重试');
  }
}

// 查看更多
const viewMore = () => {
  const baseUrl = import.meta.env?.VITE_BUSINESS_URL || '';
  console.log(baseUrl);
  window.location.href = `${baseUrl}/hbweb/system/#/inSiteMessage/index`;
}

// 点击单条消息
const handleMessageClick = async (item: any) => {
  try {
    // 如果是未读消息，先标记为已读
    if (item.isRead === 1) {
      await inSiteMessageApi.state(item.id);
      letterList(); // 重新获取列表
    }

    // 显示消息详情弹框
    currentMessageDetail.value = item;
    messageDetailVisible.value = true;
  } catch (error) {
    console.error('处理消息点击失败:', error);
    message.error('操作失败，请重试');
  }
}

// 关闭消息详情弹框
const closeMessageDetail = () => {
  messageDetailVisible.value = false;
  currentMessageDetail.value = null;
}

// 格式化表格数据
const getDetailTableData = (item: any) => {
  if (!item) return [];

  return [
    { label: '来自', value: item.applicationCode || '会展系统' },
    { label: '内容', value: item.content || item.subject },
    { label: '相关链接', value: item.url || '-' },
    { label: '发送时间', value: item.gmtCreate ? new Date(item.gmtCreate).toLocaleString() : '-' },
    { label: '已读时间', value: item.readTime ? new Date(item.readTime).toLocaleString() : '-' }
  ];
}
</script>

<template>
  <h-watermark :content="watermark" :gap="[200, 200]" style="height: 100%; width: 100%">
    <h-layout v-if="parseInt(frameModel as any) === 2" class="h-manage" style="height: 100%; min-height: 100px">
      <h-layout-header class="h-header">
        <h-row style="line-height: 48px">
          <h-col :span="3">
            <div class="logo-div">
              <img :src="logoWhite" class="logo-img" />
            </div>
          </h-col>
          <h-col :span="18"> </h-col>
          <h-col :span="3" style="line-height: 10px; padding-right: 20px">
            <div style="width: 100%; height: 100%; text-align: right; padding-top: 4px">
              <h-dropdown :trigger="['click']">
                <a @click.prevent>
                  <h-avatar :title="loginUser?.username" size="large"
                    style="color: #f56a00; background-color: #fde3cf; user-select: none">{{ avatarName }}</h-avatar>
                </a>
                <template #overlay>
                  <h-menu>
                    <h-menu-item key="0">
                      <h-button @click="logout" type="link">登出</h-button>
                    </h-menu-item>
                  </h-menu>
                </template>
              </h-dropdown>
            </div>
          </h-col>
        </h-row>
      </h-layout-header>
      <router-view v-slot="{ Component }">
        <keep-alive>
          <component :is="Component" :key="$route.name" v-if="$route.meta.keepAlive" />
        </keep-alive>
        <component :is="Component" :key="$route.name" v-if="!$route.meta.keepAlive" />
      </router-view>
    </h-layout>

    <h-layout v-if="parseInt(frameModel as any) === 1" class="h-manage" style="height: 100%; min-height: 100px">
      <router-view v-slot="{ Component }">
        <keep-alive>
          <component :is="Component" :key="$route.name" v-if="$route.meta.keepAlive" />
        </keep-alive>
        <component :is="Component" :key="$route.name" v-if="!$route.meta.keepAlive" />
      </router-view>
    </h-layout>

    <h-layout v-if="parseInt(frameModel as any) === 0" class="h-manage" style="height: 100%; min-height: 100px">
      <h-layout-header class="h-header">
        <h-row style="line-height: 48px">
          <div style="width: 200px; display: flex">
            <div class="logo-div">
              <img :src="logoWhite" class="logo-img" />
            </div>
          </div>
          <div style="width: calc(100% - 200px); display: flex">
            <h-row style="width: 100%">
              <h-col :span="21">
                <h-menu v-model:selectedKeys="selectedModel" :theme="themeColor.isDark" mode="horizontal"
                  style="height: 100%" @select="selectMenu">
                  <h-menu-item v-for="i of applications" :key="i?.id">
                    <div @click="switchTag(i)" type="link">{{ i.name }}</div>
                  </h-menu-item>
                </h-menu>
              </h-col>
              <h-col :span="3" style="line-height: 10px">
                <div
                  style="width: 100%; height: 100%; text-align: right; padding-top: 4px;display: flex;justify-content: center;">
                  <div v-if="isdev !== 'production'" style="width: 50px; height: 100%;text-align: center;" class="letter-div">
                    <span class="Hint">{{ letterLenght || 0}}</span>
                    <a-dropdown :trigger="['click']">
                        <!-- <notification-outlined style="font-Size: 28px;color: #fff;margin-top: 7px;" @click.prevent /> -->
                      <!-- <BellOutlined style="font-Size: 28px;color: #fff;margin-top: 5px;" @click.prevent></BellOutlined> -->
                      <notice class="notice-svg"/>
                      <template #overlay>
                        <h-menu class="letter">
                          <div class="letter-header">站内信</div>
                          <template v-if="letter && letter.length > 0">
                            <h-menu-item v-for="item in letter" :key="item.id" class="letter-item"
                              @click="handleMessageClick(item)">
                              <div class="letter-content">
                                <div class="letter-left">
                                  <div class="letter-subject">{{ item.subject || '用户邮代货提交了新订单，请...' }}</div>
                                  <div class="letter-time">
                                    <span class="time-icon">⏰</span>
                                    {{ formatTime(item.gmtCreate) }}
                                  </div>
                                </div>
                                <div class="letter-right">
                                  <div class="letter-status" v-if="item.isRead === 1">
                                    <span class="unread-dot"></span>
                                  </div>
                                  <RightOutlined class="arrow-icon" />
                                </div>
                              </div>
                            </h-menu-item>
                          </template>
                          <template v-else>
                            <h-menu-item class="letter-empty">
                              <div class="empty-content">暂无站内信</div>
                            </h-menu-item>
                          </template>
                          <h-menu-item class="letterutton">
                            <h-button @click="markAllAsRead" type="link">全部已读</h-button>
                            <h-button @click="viewMore" type="link">查看更多</h-button>
                          </h-menu-item>
                        </h-menu>
                      </template>
                    </a-dropdown>
                  </div>
                  <h-dropdown :trigger="['click']">
                    <a @click.prevent>
                      <h-avatar :title="loginUser?.username" size="large"
                        style="color: #f56a00; background-color: #fde3cf; user-select: none">{{ avatarName }}
                      </h-avatar>
                    </a>
                    <template #overlay>
                      <h-menu>
                        <h-menu-item key="0">
                          <h-button @click="logout" type="link">登出</h-button>
                        </h-menu-item>
                      </h-menu>
                    </template>
                  </h-dropdown>
                </div>
              </h-col>
            </h-row>
          </div>
        </h-row>
      </h-layout-header>
      <h-layout style="height: 100%">
        <h-layout-sider v-model:collapsed="collapsed" :trigger="null" collapsible class="h-layout" width="250">
          <e-menu :list="currentMenuItem" ref="eMenu"></e-menu>
        </h-layout-sider>
        <h-layout>
          <h-layout-header style="background: #fff; padding: 0; height: 42px; line-height: 42px; width: 100%">
            <h-row style="width: 100%; height: 100%">
              <div style="display: flex; width: 66px; align-items: center; height: 100%">
                <menu-unfold-outlined v-if="collapsed" class="trigger" @click="() => (collapsed = !collapsed)" />
                <menu-fold-outlined v-else class="trigger" @click="() => (collapsed = !collapsed)" />
              </div>
              <div style="display: flex; width: 90px; align-items: center; height: 100%">
                <span class="navigation-button" @click="back">
                  <LeftOutlined />
                </span>
                <span class="navigation-button" @click="go">
                  <RightOutlined />
                </span>
              </div>
              <div style="display: flex; width: calc(100% - 156px); align-items: center; height: 100%">
                <h-tabs v-if="tabsValue.length > 0" style="margin-top: 16px; width: 100%"
                  v-model:activeKey="tabsValueActiveKey" hide-add type="editable-card" @edit="onRemoveTab">
                  <h-tab-pane v-if="tabsValue.length > 0" v-for="t in tabsValue" :key="t.key">
                    <template #tab>
                      <div style="width: 140px; display: inline-block" @click="changeTab(t.key, t.content)">
                        {{ t.title }}
                      </div>
                    </template>
                  </h-tab-pane>
                </h-tabs>
              </div>
            </h-row>
          </h-layout-header>
          <h-layout-content :style="{ margin: '10px', minHeight: '280px', overflow: 'auto', overflowX: 'hidden' }">
            <router-view v-slot="{ Component }">
              <keep-alive>
                <component :is="Component" :key="$route.name" v-if="$route.meta.keepAlive" />
              </keep-alive>
              <component :is="Component" :key="$route.name" v-if="!$route.meta.keepAlive" />
            </router-view>
          </h-layout-content>
        </h-layout>
      </h-layout>
    </h-layout>

    <!-- 消息详情弹框 -->
    <h-modal v-model:open="messageDetailVisible" title="消息详情" width="600px" :footer="null" @cancel="closeMessageDetail">
      <div v-if="currentMessageDetail" class="message-detail">
        <h-table :dataSource="getDetailTableData(currentMessageDetail)" :columns="[
          { title: '项目', dataIndex: 'label', key: 'label', width: '120px' },
          { title: '内容', dataIndex: 'value', key: 'value' }
        ]" :pagination="false" :showHeader="false" size="small" bordered />
      </div>
    </h-modal>
  </h-watermark>
</template>

<style lang="less">
.h-manage {
  .ant-layout-sider {
    position: relative;
    min-width: 0;
    background: var(--nav-bar-bgc) !important;
    transition: all 0.2s, background 0s;
  }

  .h-layout {
    height: 100%;
    overflow-y: auto;
  }

  .ant-menu-dark {
    background: var(--nav-bar-bgc) !important;
  }

  .ant-menu-sub {
    background: var(--subnav-bar-bgc) !important;
  }

  .h-header {
    background: var(--nav-bar-bgc) !important;
    height: 48px;
  }

  .logo-div {
    text-align: center;
    width: 92%;
    height: 48px;

    .logo-img {
      height: 26px;
      object-fit: cover;
    }
  }

  .trigger {
    font-size: 18px;
    line-height: 44px;
    padding: 3px 24px 0 24px;
    cursor: pointer;
    transition: color 0.3s;
  }

  .navigation-button {
    font-size: 21px;
    padding-right: 12px;
    cursor: pointer;

    :hover {
      color: #0073e5;
    }
  }
}

.letter {
  top: 7px !important;
  width: 300px;
  padding: 0px;

  .letter-header {
    padding: 4px 16px;
    font-weight: 600;
    font-size: 16px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fafafa;
  }

  .letter-item {
    padding: 8px 16px !important;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;

    &:hover {
      background-color: #f5f5f5;
    }

    .letter-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      .letter-left {
        flex: 1;

        .letter-subject {
          font-size: 14px;
          color: #333;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-bottom: 4px;
          line-height: 1.4;
        }

        .letter-time {
          font-size: 12px;
          color: #999;
          display: flex;
          align-items: center;

          .time-icon {
            margin-right: 4px;
            font-size: 10px;
          }
        }
      }

      .letter-right {
        display: flex;
        align-items: center;

        .letter-status {
          margin-right: 8px;

          .unread-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            background-color: #ff4d4f;
            border-radius: 50%;
          }
        }

        .arrow-icon {
          font-size: 14px;
          color: #999;
        }
      }
    }
  }

  .letter-empty {
    padding: 20px 16px !important;
    text-align: center;

    .empty-content {
      color: #999;
      font-size: 14px;
    }
  }
}

.letterutton {
  padding: 0px !important;
  border-top: 1px solid #f0f0f0;

  button {
    width: 50%;
    border: none;
    border-radius: 0px;
    font-size: 14px;

    &:first-child {
      border-right: 1px solid #f0f0f0;
    }

    &:hover {
      color: #0073e5;
      background-color: #f0f8ff;
    }
  }
}

.message-detail {
  .modal-footer {
    text-align: right;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }

  :deep(.ant-table-tbody > tr > td) {
    padding: 12px 16px;
    vertical-align: top;

    &:first-child {
      background-color: #fafafa;
      font-weight: 500;
      color: #333;
    }

    &:last-child {
      word-break: break-all;
      white-space: pre-wrap;
    }
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background-color: #f5f7fa !important;
  }
}

.letter-div {
  position: relative;
  margin-right: 20px;

  .notice-svg{
    margin-top: 5px;
  }

  .Hint {
    display: block;
    width: 19px;
    height: 19px;
    background-color: #F46160;
    color: #fff;
    font-size: 12px;
    text-align: center;
    line-height: 18px;
    border-radius: 50%;
    position: absolute;
    top: 0px;
    left: 27px;
  }
}
</style>
