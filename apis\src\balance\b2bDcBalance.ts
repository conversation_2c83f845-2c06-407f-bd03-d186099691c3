import { IPageResponse, IDcB2bListRequest,IHaierAccountBillInfo, IDcB2bAccountRequest, IDcB2bDetailsRequest, IHabDetailInfo, IDcB2bConfirmRequest, IDcB2bRevokeConfirmRequest, IDcB2bConfirmBalanceOrderBudgetRequest, IDcB2bCancelRequest, IDcB2bMarkReadRequest, IPayInfo } from '@haierbusiness-front/common-libs'
import { download, get, post } from '../request'

export const b2bDcBalanceApi = {


     /**
     * 进行汇总
     */
     account: (params: IDcB2bAccountRequest): Promise<void> => {
        return post('balance/api/b2b/restaurant/account', params)
    },

    /**
     * 确认汇总
     */
    confirm: (params: IDcB2bConfirmRequest): Promise<void> => {
        return post('balance/api/b2b/restaurant/confirm', params)
    },

     /**
     * 确认并释放全部预算,全部确认成功则通知商户通
     */
     confirmBalanceOrderBudget: (params: IDcB2bConfirmBalanceOrderBudgetRequest): Promise<IHabDetailInfo[]> => {
        return post('balance/api/b2b/restaurant/revoke/confirm/budget', params)
    },

    /**
     * 撤回已确认状态
     */
    revokeConfirm: (params: IDcB2bRevokeConfirmRequest): Promise<void> => {
        return post('balance/api/b2b/restaurant/revoke/confirm', params)
    },

    /**
     * 取消汇总
     */
    cancel: (params: IDcB2bCancelRequest): Promise<void> => {
        return post('balance/api/b2b/restaurant/cancel', params)
    },
    
    /**
     * 获取结算列表
     */
    list: (params: IDcB2bListRequest): Promise<IPageResponse<IHaierAccountBillInfo>> => {
        return get('balance/api/b2b/restaurant/list', params)
    },

    /**
     * 导出结算列表
     */
    exportList: (params: IDcB2bListRequest): Promise<void> => {
        return download('balance/api/b2b/restaurant/list/export', params)
    },

    /**
     * 获取结算状态数量
     */
    stateAccount: (params: IDcB2bListRequest): Promise<any> => {
        return get('balance/api/b2b/restaurant/state/account', params)
    },

    /**
     * 获取预算释放失败,cvp通知失败数量
     */
    cvpErrorAccount: (params: IDcB2bListRequest): Promise<number> => {
        return get('balance/api/b2b/restaurant/cvp/error/account', params)
    },

    /**
     * 获取结算详情
     */
    details: (params: IDcB2bDetailsRequest): Promise<IPageResponse<IHabDetailInfo>> => {
        return get('balance/api/b2b/restaurant/details', params)
    },

    /**
     * 导出结算详情
     */
    detailsExport: (params: IDcB2bDetailsRequest): Promise<void> => {
        return download('balance/api/b2b/restaurant/details/export', params)
    },

    /**
     * 已读CVP错误消息
     */
    markRead: (params: IDcB2bMarkReadRequest): Promise<void> => {
        return post('balance/api/b2b/restaurant/mark/read', params)
    },

    /**
     * 获取推送CVP失败且未读的汇总单
     */
    pushCvpErrorAccount: (params: IDcB2bListRequest): Promise<number> => {
        return get('balance/api/b2b/restaurant/push/cvp/error/account', params)
    },

    /**
     * 获取付款银行账户
     */
     getPaymentBankAccount: (vCode: string): Promise<Array<IPayInfo>> => {
        return get('balance/api/b2b/restaurant/searchBankByMDM', { vCode })
    },
}