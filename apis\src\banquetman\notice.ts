import { downloadPost, get, post } from '../request'
import { 
    IRefundReq, 
    IRefundRes,
    IPageResponse, 
    Update_1Params,
    ExportInvoiceParams,
    IrechargemngParams
} from '@haierbusiness-front/common-libs'


export const noticeApi = {
    list: (params: IrechargemngParams): Promise<IPageResponse<IRefundRes>> => {
        return post('banquet/api/banquetNoticePolicy/list', params)
    },
    updateNotice: (params: any): Promise<any> => {
        return post('banquet/api/banquetNoticePolicy/update', params)
      },
    get: (id: number): Promise<IRefundRes> => {
        return get('banquet/api/banquetNoticePolicy/get', {
            id
        })
    },
    exportList: (params: IRefundReq): Promise<IPageResponse<IRefundRes>> => {
        return downloadPost('banquet/api/banquetNoticePolicy/export', params)
    },
}
