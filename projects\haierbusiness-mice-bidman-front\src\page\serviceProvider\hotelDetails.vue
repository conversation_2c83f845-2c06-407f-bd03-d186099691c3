<script setup lang="ts">
import { Button as hButton, Col as hCol, Row as hRow, Table as hTable, Image as hImage } from 'ant-design-vue';
import { SketchOutlined, WhatsAppOutlined, CheckOutlined, EyeTwoTone, InfoCircleOutlined } from '@ant-design/icons-vue';
import { computed, onMounted, ref } from 'vue';
import { hotelListApi, priceInquiryApi } from '@haierbusiness-front/apis';
import { getCurrentRoute } from '@haierbusiness-front/utils';
import aMap from './components/aMap/index.vue';
import aMapLayout from './components/aMapLayout/index.vue';
import { api as viewerApi } from 'v-viewer';
import { IPriceInquiry, PriceTypeConstant } from '@haierbusiness-front/common-libs';
import { spawn } from 'child_process';
import path from 'path';
import { useRouter } from 'vue-router';
const router = useRouter();

const route = ref(getCurrentRoute());
const mapRef1 = ref();
const mapRef2 = ref();
const lowestPrice = ref<any>(null);
const open = ref<boolean>(false);
const inquiryInfo = ref<any>({});
const roomInfo = ref<any>({});
const lookRoomInfo = (row: any) => {
  roomInfo.value = row;
  open.value = true;
};

// 酒店信息
const hotel = ref<any>({ hotelDetail: {} });
const hotelLoading = ref<boolean>(true);
const getHotelInfo = async () => {
  const res = await hotelListApi.hotelInfo({ code: route.value.query.code });
  if (res.starLevel == 40) {
    res.starLevel = 5;
  } else if (res.starLevel == 30) {
    res.starLevel = 4;
  } else if (res.starLevel == 20) {
    res.starLevel = 3;
  } else if (res.starLevel == 10) {
    res.starLevel = 2;
  } else {
    res.starLevel = 0;
  }

  hotel.value = res;
  let stringLonLat = getLonLat(res);
  if (stringLonLat.indexOf(',') > 0) {
    let lonlatArray = stringLonLat.split(',');
    let lon = parseFloat(lonlatArray[0]);
    let lat = parseFloat(lonlatArray[1]);
    mapRef1.value.createByMap(lon, lat);
    mapRef2.value.createByMap(lon, lat);
    setTimeout(() => {
      mapRef1.value.addMarker(lon, lat);
      mapRef1.value.setZoomAndCenter(lon, lat);
      mapRef2.value.addMarker(lon, lat);
      mapRef2.value.setZoomAndCenter(lon, lat);
    }, 1000);
  }

  setInterval(() => {
    hotelLoading.value = false;
  }, 3000);
};

const getInquiryInfo = async () => {
  const inquiryCode = route.value.query.inquiryCode;
  if (inquiryCode) {
    const res = await priceInquiryApi.get({ inquiryCode: inquiryCode as string });
    console.log(res, 'res');
    inquiryInfo.value = res;
    console.log('inquiryInfo', res);
  }
};

const getScoreTitle = (score: number) => {
  if (score == 5) {
    return '完美';
  } else if (score == 4.9) {
    return '超棒';
  } else if (score > 4.5 && score <= 4.8) {
    return '很棒';
  } else if (score > 4.0 && score <= 4.5) {
    return '很好';
  } else if (score > 3.0 && score <= 4.0) {
    return '不错';
  } else if (score >= 0 && score <= 3.0) {
    return '一般';
  } else {
    return '一般';
  }
};
const getLonLat = (item: any) => {
  if (item.gdLon && item.gdLat) {
    return item.gdLon + ',' + item.gdLat;
  }
  if (item.gLon && item.gLat) {
    return item.gLon + ',' + item.gLat;
  }
  if (item.bdLon && item.bdLat) {
    return item.bdLon + ',' + item.bdLat;
  }
  return '';
};

const getArrayValue = (value: any) => {
  if (value) {
    return value.split(',');
  } else {
    return null;
  }
};

// 查看图片
const lookImgUrl = (value: any): void => {
  if (!value || !Array.isArray(value) || value.length === 0) {
    return;
  }

  const imgList: string[] = [];
  value.forEach((v: any) => {
    if (v && v.pictureUrl) {
      imgList.push(v.pictureUrl);
    }
  });

  console.log('Image list:', imgList);

  if (imgList.length === 0) {
    console.log('No valid image URLs found');
    return;
  }

  viewerApi({
    images: imgList,
  });
};

const handleMap = () => {
  const dom = document.getElementById(`transportationLocation`);
  dom && dom.scrollIntoView({ behavior: 'instant', block: 'end', inline: 'end' });
};
onMounted(() => {
  getHotelInfo();
  getInquiryInfo();
  fetchPriceInquiryDetail();
});
//住宿数据
const loading = ref(false);
const priceInquiryData = ref<IPriceInquiry | null>(null);
//是否开启淡旺季
const enableQuarterSetting = ref(true);
//存在淡旺季时间
const resourceHotelQuarters = ref();
//显示时间
const enablejudgment = ref(true);
//是否青岛
const isLocal = ref<Boolean | undefined>(false);
//桌型
const tableType: any = ref([]);
//价格弹框
const priceVibile = ref(false);
//获取价格
const fetchPriceInquiryDetail = async () => {
  const priceInquiryCode = route.value.query.inquiryCode;
  try {
    loading.value = true;
    if (priceInquiryCode) {
      const response = await priceInquiryApi.get({ inquiryCode: priceInquiryCode });
      priceInquiryData.value = response;
      tableType.value = response.resourceHotelPlaces;
      console.log(tableType.value, 'tableType');
      if (response) {
        enableQuarterSetting.value = Boolean(response.enableQuarter);
        resourceHotelQuarters.value = response.resourceHotelQuarters;
        if (resourceHotelQuarters.value.length > 0) {
          enablejudgment.value = true;
        } else {
          enablejudgment.value = false;
        }
        isLocal.value = response.isLocal;
        console.log(isLocal.value, 'isLocal');
        console.log(enablejudgment.value, 'enablejudgment');
      }
    } else {
      // console.log('priceInquiryCode为空，不调用API');
    }
  } catch (error) {
    // console.error('获取价格询价详情失败:', error);
  } finally {
    loading.value = false;
  }
};
//会场处理
const venueList = computed(() => {
  if (!tableType.value) {
    return;
  }
  let venueList: any = [];
  tableType.value.forEach((item: any) => {
    venueList.push(item);
  });
  if (venueList.length > 0) {
  }
  return venueList;
});
//桌型处理
const tableTypeList = computed(() => (index: any) => {
  if (!tableType.value) {
    return;
  }
  let tableList: any = [];
  tableType.value[index]?.tableResults.map((item: any, index: any) => {
    tableList.push({
      id: index,
      maxNum: item.maxNum,
      tableType: item.tableType,
      tableUrls: item.tableUrls[0]?.path,
    });
  });
  console.log(tableList, 'tableList');

  return tableList;
});
const modifyTitle = (num: number) => {
  let imgTitle = '';
  switch (num) {
    case 1: // U型式
      imgTitle = 'U型式';
      break;
    case 2: // 宴会会式/董事会式
      imgTitle = '宴会式/董事会式';
      break;
    case 3: // 剧院式
      imgTitle = '剧院式';
      break;
    case 4: // 海岛式
      imgTitle = '海岛式';
      break;
    case 5: // 鸡尾酒/酒会式
      imgTitle = '鸡尾酒/酒会式';
      break;
    case 6: // 课桌式
      imgTitle = '课桌式';
      break;
    case 7: // 鱼骨式
      imgTitle = '鱼骨式';
      break;
  }
  return imgTitle;
};
//套房
const suitePriceData = computed(() => {
  if (!priceInquiryData.value?.resourceHotelRooms) {
    return [];
  }
  let priceObj: Record<string, any> = [];
  let date = ['淡季', '旺季'];
  let priceList: any = priceInquiryData.value.resourceHotelRooms[2];
  if (enablejudgment.value) {
    priceObj = [
      {
        id: 1,
        roomDate: '淡季',
        offSeasonPrice: null, //淡季门市价
        lightSeasonTier: null, //淡季协议价
      },
      {
        id: 2,
        roomDate: '旺季',
        offSeasonPrice: null, //旺季门市价
        lightSeasonTier: null, //旺季协议价
      },
    ];

    // 直接查找价格项 - 门市价 (作为淡季门市价)
    const offSeason = priceList.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_MARKET_PRICE.code,
    );
    if (offSeason) {
      priceObj[0].offSeasonPrice = offSeason.price;
    }
    // 设置旺季门市价
    const peakListPriceItem = priceList.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_MARKET_PRICE.code,
    );
    if (peakListPriceItem) {
      priceObj[1].offSeasonPrice = peakListPriceItem.price;
    }

    // 淡季协议价
    const lightSeasonAgreementUnder50 = priceList.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_AGREEMENT_PRICE_UNDER_50.code,
    );
    if (lightSeasonAgreementUnder50) {
      priceObj[0].lightSeasonTier = lightSeasonAgreementUnder50.price;
    }

    // 旺季协议价,50人以下
    const peakSeasonAgreementUnder50 = priceList.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_AGREEMENT_PRICE_UNDER_50.code,
    );
    if (peakSeasonAgreementUnder50) {
      priceObj[1].lightSeasonTier = peakSeasonAgreementUnder50.price;
    }
  } else {
    priceObj = [
      {
        id: 1,
        listPrice: null, //门市价
        lightSeasonTier: null, //协议价
      },
    ];
    //普通门市价
    const listPriceItem = priceList.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.LIST_PRICE.code,
    );
    console.log(listPriceItem, "listPriceItem");

    if (listPriceItem) {
      priceObj[0].listPrice = listPriceItem?.price;
    }
    //普通协议价
    const negotiatedPrice = priceList.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.AGREEMENT_PRICE.code,
    );
    if (negotiatedPrice) {
      // console.log('room.priceResults', room.priceResults);
      priceObj[0].lightSeasonTier = negotiatedPrice.price;
    }
  }

  return priceObj;
});
const suiteColumns = computed(() => {
  //判断是否有淡旺季时间
  if (enablejudgment.value) {
    const columns = [
      {
        title: '淡旺季',
        dataIndex: 'roomDate',
        align: 'center' as const,
        key: 'roomDate',
      },
      {
        title: '门市价(元)',
        dataIndex: 'offSeasonPrice',
        align: 'center' as const,
        key: 'offSeasonPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
      {
        title: '协议价(元)',
        dataIndex: 'lightSeasonTier',
        align: 'center' as const,
        key: 'lightSeasonTier',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
    ];
    return columns;
  } else {
    // 如果enableQuarterSetting为false，只显示简化版的价格列
    const columns = [
      {
        title: '门市价(元)',
        dataIndex: 'listPrice',
        align: 'center' as const,
        key: 'listPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
      {
        title: '协议价(元)',
        dataIndex: 'lightSeasonTier',
        align: 'center' as const,
        key: 'lightSeasonTier',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
    ];
    return columns;
  }
});
//大床房
const bigPriceData = computed(() => {
  if (!priceInquiryData.value?.resourceHotelRooms) {
    return [];
  }
  let priceObj: Record<string, any> = [];
  let date = ['淡季', '旺季'];
  let priceList: any = priceInquiryData.value.resourceHotelRooms[0];
  console.log(priceList, 'priceList');
  if (enablejudgment.value) {
    priceObj = [
      {
        id: 1,
        roomDate: '淡季',
        offSeasonPrice: null, //淡季门市价
        lightSeasonTier: null, //淡季协议价
      },
      {
        id: 2,
        roomDate: '旺季',
        offSeasonPrice: null, //旺季门市价
        lightSeasonTier: null, //旺季协议价
      },
    ];

    // 直接查找价格项 - 门市价 (作为淡季门市价)
    const offSeason = priceList.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_MARKET_PRICE.code,
    );
    if (offSeason) {
      priceObj[0].offSeasonPrice = offSeason.price;
    }
    // 设置旺季门市价
    const peakListPriceItem = priceList.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_MARKET_PRICE.code,
    );
    if (peakListPriceItem) {
      priceObj[1].offSeasonPrice = peakListPriceItem.price;
    }

    // 淡季协议价
    const lightSeasonAgreementUnder50 = priceList.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_AGREEMENT_PRICE_UNDER_50.code,
    );
    if (lightSeasonAgreementUnder50) {
      priceObj[0].lightSeasonTier = lightSeasonAgreementUnder50.price;
    }

    // 旺季协议价,50人以下
    const peakSeasonAgreementUnder50 = priceList.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_AGREEMENT_PRICE_UNDER_50.code,
    );
    if (peakSeasonAgreementUnder50) {
      priceObj[1].lightSeasonTier = peakSeasonAgreementUnder50.price;
    }
  } else {
    priceObj = [
      {
        id: 1,
        listPrice: null, //门市价
        lightSeasonTier: null, //协议价
      },
    ];
    //普通门市价
    const listPriceItem = priceList.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.LIST_PRICE.code,
    );
    if (listPriceItem) {
      priceObj[0].listPrice = listPriceItem.price;
    }
    //普通协议价
    const negotiatedPrice = priceList.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.AGREEMENT_PRICE.code,
    );
    if (negotiatedPrice) {
      // console.log('room.priceResults', room.priceResults);
      priceObj[0].lightSeasonTier = negotiatedPrice.price;
    }
  }

  return priceObj;
});
const bigColumns = computed(() => {
  //判断是否有淡旺季时间
  if (enablejudgment.value) {
    const columns = [
      {
        title: '淡旺季',
        dataIndex: 'roomDate',
        align: 'center' as const,
        key: 'roomDate',
      },
      {
        title: '门市价(元)',
        dataIndex: 'offSeasonPrice',
        align: 'center' as const,
        key: 'offSeasonPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
      {
        title: '协议价(元)',
        dataIndex: 'lightSeasonTier',
        align: 'center' as const,
        key: 'lightSeasonTier',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
    ];
    return columns;
  } else {
    // 如果enableQuarterSetting为false，只显示简化版的价格列
    const columns = [
      {
        title: '门市价(元)',
        dataIndex: 'listPrice',
        align: 'center' as const,
        key: 'listPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
      {
        title: '协议价(元)',
        dataIndex: 'lightSeasonTier',
        align: 'center' as const,
        key: 'lightSeasonTier',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
    ];
    return columns;
  }
});
//双床房
const doublePriceData = computed(() => {
  if (!priceInquiryData.value?.resourceHotelRooms) {
    return [];
  }
  let priceObj: Record<string, any> = [];
  let date = ['淡季', '旺季'];
  let priceList: any = priceInquiryData.value.resourceHotelRooms[1];
  console.log(priceList, 'priceList');
  if (enablejudgment.value) {
    priceObj = [
      {
        id: 1,
        roomDate: '淡季',
        offSeasonPrice: null, //淡季门市价
        lightSeasonTier: null, //淡季协议价
      },
      {
        id: 2,
        roomDate: '旺季',
        offSeasonPrice: null, //旺季门市价
        lightSeasonTier: null, //旺季协议价
      },
    ];

    // 直接查找价格项 - 门市价 (作为淡季门市价)
    const offSeason = priceList.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_MARKET_PRICE.code,
    );
    if (offSeason) {
      priceObj[0].offSeasonPrice = offSeason.price;
    }
    // 设置旺季门市价
    const peakListPriceItem = priceList.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_MARKET_PRICE.code,
    );
    if (peakListPriceItem) {
      priceObj[1].offSeasonPrice = peakListPriceItem.price;
    }

    // 淡季协议价
    const lightSeasonAgreementUnder50 = priceList.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_AGREEMENT_PRICE_UNDER_50.code,
    );
    if (lightSeasonAgreementUnder50) {
      priceObj[0].lightSeasonTier = lightSeasonAgreementUnder50.price;
    }

    // 旺季协议价,50人以下
    const peakSeasonAgreementUnder50 = priceList.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_AGREEMENT_PRICE_UNDER_50.code,
    );
    if (peakSeasonAgreementUnder50) {
      priceObj[1].lightSeasonTier = peakSeasonAgreementUnder50.price;
    }
  } else {
    priceObj = [
      {
        id: 1,
        listPrice: null, //门市价
        lightSeasonTier: null, //协议价
      },
    ];
    //普通门市价
    const listPriceItem = priceList.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.LIST_PRICE.code,
    );
    if (listPriceItem) {
      priceObj[0].listPrice = listPriceItem.price;
    }
    //普通协议价
    const negotiatedPrice = priceList.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.AGREEMENT_PRICE.code,
    );
    if (negotiatedPrice) {
      // console.log('room.priceResults', room.priceResults);
      priceObj[0].lightSeasonTier = negotiatedPrice.price;
    }
  }

  return priceObj;
});
const doubleColumns = computed(() => {
  //判断是否有淡旺季时间
  if (enablejudgment.value) {
    const columns = [
      {
        title: '淡旺季',
        dataIndex: 'roomDate',
        align: 'center' as const,
        key: 'roomDate',
      },
      {
        title: '门市价(元)',
        dataIndex: 'offSeasonPrice',
        align: 'center' as const,
        key: 'offSeasonPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
      {
        title: '协议价(元)',
        dataIndex: 'lightSeasonTier',
        align: 'center' as const,
        key: 'lightSeasonTier',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
    ];
    return columns;
  } else {
    // 如果enableQuarterSetting为false，只显示简化版的价格列
    const columns = [
      {
        title: '门市价(元)',
        dataIndex: 'listPrice',
        align: 'center' as const,
        key: 'listPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
      {
        title: '协议价(元)',
        dataIndex: 'lightSeasonTier',
        align: 'center' as const,
        key: 'lightSeasonTier',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
    ];
    return columns;
  }
});
//价格预览
const priceJudgment = () => {
  priceVibile.value = true;
};
const handleOk = () => {
  priceVibile.value = false;
};

//会场价格表格
const venueTableColumns = computed(() => {
  const baseColumns = [
    { title: '会场名称', dataIndex: 'placeName', align: 'center' as const, width: '60px', key: 'placeName' },
  ];
  // 如果enableQuarterSetting为true，显示完整的淡旺季价格列
  if (enablejudgment.value && isLocal.value) {
    const columns = [
      ...baseColumns,
      {
        title: '淡季全天门市价(元)',
        dataIndex: 'lightSeasonFullDayListPrice',
        align: 'center' as const,
        width: '60px',
        key: 'lightSeasonFullDayListPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
      {
        title: '淡季全天协议价(元)',
        dataIndex: 'lightSeasonFullDayAgreementPrice',
        align: 'center' as const,
        width: '60px',
        key: 'lightSeasonFullDayAgreementPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
      {
        title: '淡季半天门市价(元)',
        dataIndex: 'lightSeasonHalfDayListPrice',
        align: 'center' as const,
        width: '60px',
        key: 'lightSeasonHalfDayListPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
      {
        title: '淡季半天协议价(元)',
        dataIndex: 'lightSeasonHalfDayAgreementPrice',
        align: 'center' as const,
        width: '60px',
        key: 'lightSeasonHalfDayAgreementPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
      {
        title: '旺季全天门市价(元)',
        dataIndex: 'peakSeasonFullDayListPrice',
        align: 'center' as const,
        width: '60px',
        key: 'peakSeasonFullDayListPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
      {
        title: '旺季全天协议价(元)',
        dataIndex: 'peakSeasonFullDayAgreementPrice',
        align: 'center' as const,
        width: '60px',
        key: 'peakSeasonFullDayAgreementPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
      {
        title: '旺季半天门市价(元)',
        dataIndex: 'peakSeasonHalfDayListPrice',
        align: 'center' as const,
        width: '60px',
        key: 'peakSeasonHalfDayListPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
      {
        title: '旺季半天协议价(元)',
        dataIndex: 'peakSeasonHalfDayAgreementPrice',
        align: 'center' as const,
        width: '60px',
        key: 'peakSeasonHalfDayAgreementPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
    ];

    return columns;
  } else if (!enablejudgment.value && isLocal.value) {
    // 如果enableQuarterSetting为false，只显示简化版的价格列
    const columns = [
      ...baseColumns,
      {
        title: '全天门市价(元)',
        dataIndex: 'OrdinaryRegularfulldaymarketprice',
        align: 'center' as const,
        width: '60px',
        key: 'OrdinaryRegularfulldaymarketprice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
      {
        title: '全天协议价(元)',
        dataIndex: 'OrdinaryRegularfulldayAgreementprice',
        align: 'center' as const,
        width: '60px',
        key: 'OrdinaryRegularfulldayAgreementprice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
      {
        title: '半天门市价(元)',
        dataIndex: 'Ordinaryhalfdaymarketprice',
        align: 'center' as const,
        width: '60px',
        key: 'Ordinaryhalfdaymarketprice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
      {
        title: '半天协议价(元)',
        dataIndex: 'OrdinaryhalfdayAgreementprice',
        align: 'center' as const,
        width: '60px',
        key: 'OrdinaryhalfdayAgreementprice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text + '元';
        },
      },
    ];
    return columns;
  } else if (!isLocal.value) {
    const columns: any[] | undefined = [];
    return columns;
  }
});

// 处理会场价格数据
const processedVenueData = computed(() => {
  if (!(priceInquiryData.value as any)?.resourceHotelPlaces) {
    return [];
  }
  const venues = (priceInquiryData.value as any).resourceHotelPlaces || [];
  let venueObj: Record<string, any> = [];
  venues.map((venue: any, index: number) => {
    venueObj.push({
      id: venue.id,
      placeName: venue.placeName,
      fullDayMarketPrice: null,
      halfDayMarketPrice: null,
      lightSeasonFullDayListPrice: null,
      lightSeasonFullDayAgreementPrice: null,
      lightSeasonHalfDayListPrice: null,
      lightSeasonHalfDayAgreementPrice: null,
      peakSeasonFullDayListPrice: null,
      peakSeasonFullDayAgreementPrice: null,
      peakSeasonHalfDayListPrice: null,
      peakSeasonHalfDayAgreementPrice: null,
      Ordinaryhalfdaymarketprice: null,
      OrdinaryRegularfulldaymarketprice: null,
      OrdinaryhalfdayAgreementprice: null,
      OrdinaryRegularfulldayAgreementprice: null,
    });
    if (venue.priceResults && Array.isArray(venue.priceResults) && venue.priceResults.length > 0) {
      // 淡季全天门市价
      const lightSeasonFullDayMarketPrice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_FULL_DAY_MARKET_PRICE.code,
      );
      if (lightSeasonFullDayMarketPrice) {
        venueObj[index].lightSeasonFullDayListPrice = lightSeasonFullDayMarketPrice.price;
      }

      // 淡季全天协议价
      const lightSeasonFullDayAgreementPrice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_FULL_DAY_AGREEMENT_PRICE.code,
      );
      if (lightSeasonFullDayAgreementPrice) {
        venueObj[index].lightSeasonFullDayAgreementPrice = lightSeasonFullDayAgreementPrice.price;
      }

      // 淡季半天门市价
      const lightSeasonHalfDayMarketPrice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_HALF_DAY_MARKET_PRICE.code,
      );
      if (lightSeasonHalfDayMarketPrice) {
        venueObj[index].lightSeasonHalfDayListPrice = lightSeasonHalfDayMarketPrice.price;
      }

      // 淡季半天协议价
      const lightSeasonHalfDayAgreementPrice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_HALF_DAY_AGREEMENT_PRICE.code,
      );
      if (lightSeasonHalfDayAgreementPrice) {
        venueObj[index].lightSeasonHalfDayAgreementPrice = lightSeasonHalfDayAgreementPrice.price;
      }

      // 旺季全天门市价
      const peakSeasonFullDayMarketPrice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_FULL_DAY_MARKET_PRICE.code,
      );
      if (peakSeasonFullDayMarketPrice) {
        venueObj[index].peakSeasonFullDayListPrice = peakSeasonFullDayMarketPrice.price;
      }

      // 旺季全天协议价
      const peakSeasonFullDayAgreementPrice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_FULL_DAY_AGREEMENT_PRICE.code,
      );
      if (peakSeasonFullDayAgreementPrice) {
        venueObj[index].peakSeasonFullDayAgreementPrice = peakSeasonFullDayAgreementPrice.price;
      }

      // 旺季半天门市价
      const peakSeasonHalfDayMarketPrice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_HALF_DAY_MARKET_PRICE.code,
      );
      if (peakSeasonHalfDayMarketPrice) {
        venueObj[index].peakSeasonHalfDayListPrice = peakSeasonHalfDayMarketPrice.price;
      }

      // 旺季半天协议价
      const peakSeasonHalfDayAgreementPrice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_HALF_DAY_AGREEMENT_PRICE.code,
      );
      if (peakSeasonHalfDayAgreementPrice) {
        venueObj[index].peakSeasonHalfDayAgreementPrice = peakSeasonHalfDayAgreementPrice.price;
      }

      //普通半天门市价
      const Ordinaryhalfdaymarketprice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.LIST_PRICE_FULL_DAY.code,
      );
      if (Ordinaryhalfdaymarketprice) {
        venueObj[index].Ordinaryhalfdaymarketprice = Ordinaryhalfdaymarketprice.price;
      }
      //普通全天门市价
      const OrdinaryRegularfulldaymarketprice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.LIST_PRICE_HALF_DAY.code,
      );
      if (OrdinaryRegularfulldaymarketprice) {
        venueObj[index].OrdinaryRegularfulldaymarketprice = OrdinaryRegularfulldaymarketprice.price;
      }
      //普通半天协议价
      const OrdinaryhalfdayAgreementprice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.AGREEMENT_PRICE_FULL_DAY.code,
      );
      if (OrdinaryhalfdayAgreementprice) {
        venueObj[index].OrdinaryhalfdayAgreementprice = OrdinaryhalfdayAgreementprice.price;
      }
      //普通半天协议价
      const OrdinaryRegularfulldayAgreementprice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.AGREEMENT_PRICE_HALF_DAY.code,
      );
      if (OrdinaryRegularfulldayAgreementprice) {
        venueObj[index].OrdinaryRegularfulldayAgreementprice = OrdinaryRegularfulldayAgreementprice.price;
      }
    }
  });
  return venueObj;
});

//产品编辑
const productVisible = () => {
  router.push({
    path: '/bidman/serviceProvider/productEdit',
    query: {
      code: route.value.query.inquiryCode,
    },
  });
};
</script>

<template>
  <div class="homeBox">
    <div class="contentBox">
      <div class="hotelInfoBox">
        <div class="topBox">
          <div class="top_left">
            <div class="hotelName">
              {{ hotel.name
              }}<SketchOutlined v-for="v in hotel.starLevel" :style="{ fontSize: '14px', color: '#ff6600' }">
              </SketchOutlined>
            </div>
            <div class="address">【{{ hotel.regionName }}】{{ hotel.address }} <a @click="handleMap">查看地图</a></div>
          </div>
          <div class="topRight fr">
            <div v-if="lowestPrice">
              <em>¥</em> <strong id="szdj">{{ lowestPrice }}</strong> <span>起</span>
            </div>
          </div>
        </div>
        <div class="bottomBox">
          <div class="bottomLeft">
            <div class="leftImgBox">
              <img v-if="hotel?.hotelPictures"
                v-lazy="hotel?.hotelPictures && hotel?.hotelPictures.length && hotel?.hotelPictures[0].pictureUrl"
                alt="" />
              <div @click="lookImgUrl(hotel?.hotelPictures)" v-if="hotel?.hotelPictures"
                class="detail-headalbum_focus_des">
                查看所有{{ hotel?.hotelPictures.length }}图片
              </div>
            </div>
            <div class="rightImgBox">
              <div v-for="(item, index) in hotel?.hotelPictures" class="imgBox">
                <img v-lazy="item.pictureUrl" alt="" />
              </div>
            </div>
          </div>
          <div class="bottomReft">
            <div>
              <p class="score" v-if="hotel.hotelDetail.commentScoreTotal">
                <span>{{ hotel.hotelDetail.commentScoreTotal }} </span>分
              </p>
              <p class="score" v-else><span>暂无评分</span></p>
              <span v-if="hotel.hotelDetail.commentScoreTotal" class="scoreTitle">{{
                getScoreTitle(hotel.hotelDetail.commentScoreTotal)
              }}</span>
              <a-tooltip placement="topLeft" :title="hotel.hotelDetail.bestCommentSentence">
                <div class="bestCommentSentence ellipsis">{{ hotel.hotelDetail.bestCommentSentence }}</div>
              </a-tooltip>
              <a-popover>
                <template #content>
                  <div style="width: 300px" v-html="hotel.hotelDetail.description"></div>
                </template>
                <div class="description ellipsis">{{ hotel.hotelDetail.description }}</div>
              </a-popover>
              <a-tooltip placement="topLeft" :title="hotel.phone">
                <div class="phone ellipsis">
                  <WhatsAppOutlined /> {{ hotel.phone }}
                </div>
              </a-tooltip>
              <a-tooltip placement="topLeft" :title="hotel.hotelDetail.arrivalDeparturePolicy">
                <div class="arrivalDeparturePolicy ellipsis">{{ hotel.hotelDetail.arrivalDeparturePolicy }}</div>
              </a-tooltip>
            </div>
            <div class="mapBox">
              <aMap :key="1" ref="mapRef1"></aMap>
              <div @click="handleMap" class="lookMap">查看地图</div>
            </div>
          </div>
        </div>
      </div>
      <div class="hotelRoomContent">
        <div class="accommodation infoBox">
          <div class="title">住宿产品</div>
          <ul style="width: 100%;margin-bottom: 0;" v-if="isLocal">
            <li>
              <div class="title" style="padding-bottom: 0;">套房</div>
              <div class="flex">
                <p>价格时间模式：</p>
                <p v-if="enablejudgment">按淡旺季设置</p>
                <p v-else>无淡旺季设置</p>
              </div>
              <a-table :columns="suiteColumns" :data-source="suitePriceData" bordered :pagination="false">
                <template #bodyCell="{ column, text }">
                  <template v-if="column.dataIndex === 'name'">
                    <a>{{ text }}</a>
                  </template>
                </template>
              </a-table>
            </li>
            <li>
              <div class="title" style="padding-bottom: 0;">大床房</div>
              <div class="flex">
                <p>价格时间模式：</p>
                <p v-if="enablejudgment">按淡旺季设置</p>
                <p v-else>无淡旺季设置</p>
              </div>
              <a-table :columns="bigColumns" :data-source="bigPriceData" bordered :pagination="false">
                <template #bodyCell="{ column, text }">
                  <template v-if="column.dataIndex === 'name'">
                    <a>{{ text }}</a>
                  </template>
                </template>
              </a-table>
            </li>
            <li>
              <div class="title" style="padding-bottom: 0;">双床房</div>
              <div class="flex">
                <p>价格时间模式：</p>
                <p v-if="enablejudgment">按淡旺季设置</p>
                <p v-else>无淡旺季设置</p>
              </div>
              <a-table :columns="doubleColumns" :data-source="doublePriceData" bordered :pagination="false">
                <template #bodyCell="{ column, text }">
                  <template v-if="column.dataIndex === 'name'">
                    <a>{{ text }}</a>
                  </template>
                </template>
              </a-table>
            </li>
          </ul>
        </div>
        <div class="hotelInformation infoBox" id="hotelInformation">
          <div class="Venue-Modify">
            <div class="venus-title">
              会场产品
            </div>
            <h-button v-if="isLocal" type="link" @click="priceJudgment">价格预览
              <EyeTwoTone />
            </h-button>
          </div>
          <div class="Venue" v-for="(venue, label) in venueList" :key="label" style="margin-bottom: 30px;">
            <div class="Venue-top">
              <h3 class="title" style="padding-bottom: 0;">{{ venue.placeName }}</h3>

            </div>
            <h-row>
              <h-col :span="4" class="line-style">会场规模：<span>大型</span></h-col>
              <h-col :span="4" class="line-style">最大容纳人数：<span>{{ venue.maxNum }}</span></h-col>
              <h-col :span="4" class="line-style">面积(㎡)：<span>{{ venue.area }}</span></h-col>
              <h-col :span="4" class="line-style">层高(m)：<span>{{ venue.height }}</span></h-col>
              <h-col :span="4" class="line-style">长(m)：<span>{{ venue.length }}</span></h-col>
              <h-col :span="4" class="line-style">宽(m)：<span>{{ venue.wide }}</span></h-col>
            </h-row>
            <h-row style="margin-top: 15px">
              <h-col :span="4" class="line-style">楼层：<span>{{ venue.floor }}</span></h-col>
              <h-col :span="4" class="line-style">是否有柱：<span>{{ venue.isPillar == true ? '是' : '否' }}</span></h-col>
              <h-col :span="4" style="display: flex" class="line-style">价格时间模式：
                <p v-if="enablejudgment">按淡旺季设置</p>
                <p v-else>无淡旺季设置</p>
              </h-col>
            </h-row>
            <div class="tableType" v-if="tableType[label].tableResults.length > 0">
              <h3 class="title">支持桌型</h3>
              <table>
                <tr class="table-top">
                  <td>桌型</td>
                  <td>最大可容纳人数</td>
                  <td>桌型图片</td>
                  <td>创建时间</td>
                  <td>更新时间</td>
                </tr>
                <tr v-for="(item, index) in tableTypeList(label)" :key="item.id">
                  <td>
                    {{ modifyTitle(item.tableType) }}
                  </td>
                  <td>
                    {{ item.maxNum }}人
                  </td>
                  <td>
                    <a-image v-if="item.tableUrls" :width="150" :src="item.tableUrls" alt=" " />
                  </td>
                  <td>{{ priceInquiryData?.createTime }}</td>
                  <td>-</td>
                </tr>
              </table>
            </div>
          </div>
        </div>
        <div class="hotelInformation infoBox" id="hotelInformation">
          <div class="title">酒店信息</div>
          <ul class="info clearfix" id="hotelInfo">
            <li class="jdxjIconFont" v-if="hotel.starLevel && hotel.startLevel != 0">
              <div class="left">酒店星级</div>
              <div class="right" style="color: #f60">
                <SketchOutlined v-for="v in hotel.starLevel" :style="{ fontSize: '14px', color: '#ff6600' }">
                </SketchOutlined>
              </div>
            </li>
            <li v-if="hotel.openDate || hotel.decorationDate">
              <div class="left">基本信息</div>
              <div class="right" v-if="hotel.openDate">{{ hotel.decorationDate }}开业&nbsp;&nbsp;</div>
              <div class="right">{{ hotel.decorationDate }}装修</div>
            </li>
            <li v-if="hotel.phone">
              <div class="left">联系方式</div>
              <div class="right" id="jddh" jddh="0532-66968888">{{ hotel.phone }}</div>
            </li>
            <li v-if="hotel.address">
              <div class="left">酒店地址</div>
              <div class="right">{{ hotel.address }}</div>
            </li>
            <li v-if="hotel.hotelDetail.description">
              <div class="left">酒店简介</div>
              <div v-html="hotel.hotelDetail.description" class="right"></div>
            </li>
          </ul>
        </div>
        <div class="hotelInformation infoBox" id="hotelPolicies">
          <div class="title">酒店政策</div>
          <ul class="info clearfix" id="hotelInfo">
            <li v-if="hotel.hotelDetail.arrivalDeparturePolicy" class="jdxjIconFont">
              <div class="left">入离时间</div>
              <div class="right" style="color: #f60">
                {{ hotel.hotelDetail.arrivalDeparturePolicy }}
              </div>
            </li>
            <li v-if="hotel.hotelDetail.mealPolicy">
              <div class="left">早餐信息</div>
              <div class="right" v-html="hotel.hotelDetail.mealPolicy"></div>
            </li>
            <li v-if="hotel.hotelDetail.childAndAddBedPolicy">
              <div class="left">儿童和加床政策</div>
              <div class="right" v-html="hotel.hotelDetail.childAndAddBedPolicy"></div>
            </li>

            <li v-if="hotel.hotelDetail.ageLimitPolicy">
              <div class="left">年龄限制政策</div>
              <div class="right">{{ hotel.hotelDetail.ageLimitPolicy }}</div>
            </li>
            <li v-if="hotel.hotelDetail.checkLimitInfoDescription">
              <div class="left">入住提示</div>
              <div class="right">{{ hotel.hotelDetail.checkLimitInfoDescription }}</div>
            </li>
            <li v-if="hotel.hotelDetail.paymentMethod">
              <div class="left">前台可用支付方式</div>
              <div class="right">
                {{ hotel.hotelDetail.paymentMethod }}
              </div>
            </li>
            <li v-if="hotel.hotelDetail.importantNotice">
              <div class="left">重要通知(提示)</div>
              <div class="right" v-html="hotel.hotelDetail.importantNotice"></div>
            </li>
          </ul>
        </div>
        <div class="serviceFacilities infoBox" id="serviceFacilities">
          <div class="title">服务设施</div>
          <div class="serviceFacilitiesBox">
            <div v-for="item in hotel?.hotelFacilities" class="serviceFacilitiesItem">
              <div class="type">{{ item.name }}</div>
              <div class="facilitiesItem">
                <div class="facilitiesItemBox" v-for="v in getArrayValue(item.value)">
                  <CheckOutlined :style="{ marginRight: '3px' }" />{{ v }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="transportationLocation infoBox" id="transportationLocation">
          <div class="title">交通位置</div>
          <div class="mapBox">
            <aMapLayout :key="2" ref="mapRef2"></aMapLayout>
          </div>
        </div>
      </div>
      <div class="footer">
        <p>
          <InfoCircleOutlined style="color: orange" />发布后当前修改方案方可生效
        </p>
        <p>
          <h-button style="margin-right: 10px" @click="productVisible">产品编辑</h-button>
        </p>
      </div>
      <!-- 房型详情 -->
      <a-modal :footer="null" width="800px" v-model:open="open" title="房型详情">
        <a-carousel autoplay arrows dots-class="slick-dots slick-thumb">
          <template #customPaging="props">
            <a>
              <img v-lazy="roomInfo.roomImageUrlList[props.i]" />
            </a>
          </template>
          <div v-for="item in roomInfo.roomImageUrlList" :key="item">
            <img v-lazy="item" />
          </div>
        </a-carousel>
        <div class="roomInfoBox">
          <p class="label">
            面积：<span>{{ roomInfo.roomArea }}</span> 楼层：<span>{{ roomInfo.floor }}</span> 床型:
            <span>{{ roomInfo.bedTypeName }}</span>
          </p>
          <!-- <p class="label">
          推荐入住人数: <span>{{ roomInfo.bedType }}</span>
        </p> -->
          <div v-if="roomInfo.roomFaclitiesList" class="jcssBox">
            <div class="label title">基础设施：</div>
            <div class="label">
              <span v-for="v in roomInfo.roomFaclitiesList">
                {{ v }}
              </span>
            </div>
          </div>
        </div>
      </a-modal>
      <a-modal v-model:open="priceVibile" title="会场价格预览" @ok="handleOk" width="60%">
        <h-row class="price-table-section Modify-Table">
          <h-col :span="24">
            <h-table :columns="venueTableColumns" :dataSource="processedVenueData" :pagination="false" bordered
              :scroll="{ x: 1000 }" style="margin-top: 20px">
            </h-table>
          </h-col>
        </h-row>
      </a-modal>
    </div>
  </div>
</template>

<style lang="less" scoped>
.homeBox {
  width: 100%;
  background: #f5f7fa;
}

.contentBox {
  padding-bottom: 24px;
  width: 1280px;
  height: 100%;
  margin: 0 auto;
  position: relative;

  .breadcrumb {
    padding: 20px 0;
  }
}

.hotelInfoBox {
  background-color: #fff;
  padding: 16px 24px;
  margin-bottom: 8px;

  .topBox {
    display: flex;
    justify-content: space-between;

    .top_left {
      .hotelName {
        font-size: 20px;
        color: #0f294d;
        display: inline;
        margin-right: 8px;
        font-weight: bolder;
        width: 100%;
      }

      .address {
        width: 100%;
        font-size: 14px;
        color: #455873;
        letter-spacing: 0;
        text-align: left;
        line-height: 14px;
        margin-top: 14px;
        display: inline-block;

        a {
          cursor: pointer;
          color: #008ad2 !important;
        }
      }
    }

    .topRight {
      float: right;
      display: flex;
      align-items: center;

      em {
        font-size: 24px;
        color: #ff5522;
      }

      strong {
        font-weight: bold;
        font-size: 24px;
        color: #ff5522;
      }
    }
  }

  .bottomBox {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;

    .bottomLeft {
      display: flex;

      .leftImgBox {
        width: 475px;
        height: 302px;
        display: inline-block;
        cursor: pointer;
        position: relative;

        img {
          width: 100%;
          height: 100%;
        }

        .detail-headalbum_focus_des {
          position: absolute;
          right: 16px;
          bottom: 14px;
          color: #fff;
          font-size: 14px;
          font-weight: 700;
        }

        // background: green;
      }

      .rightImgBox {
        width: 400px;
        height: 302px;
        display: flex;
        flex-wrap: wrap;
        overflow: hidden;

        .imgBox {
          width: 190px;
          height: 147px;
          margin: 0 0 8px 8px;

          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }

    .bottomReft {
      display: flex;
      flex-flow: column;
      justify-content: space-between;

      .score {
        background-color: #4978ce;
        padding: 3px 8px;
        border-radius: 2px;
        font-size: 22px;
        line-height: 22px;
        color: hsla(0, 0%, 100%, 0.6);
        cursor: pointer;
        white-space: nowrap;
        display: inline-block;
        font-size: 14px;

        span {
          font-size: 16px;
          font-weight: 700;
          color: #fff;
          margin-right: 3px;
        }
      }

      .scoreTitle {
        color: #4978ce;
        font-size: 20px;
        margin-left: 8px;
        font-weight: 700;
        position: relative;
        top: 1px;
      }

      .bestCommentSentence {
        margin-bottom: 8px;
        font-size: 14px;
        color: #06aebd;
        font-weight: 400;
        white-space: nowrap;
      }

      .description {
        margin-bottom: 8px;
        font-size: 14px;
        color: #06aebd;
        font-weight: 400;
        white-space: nowrap;
        width: 350px;
      }

      .phone {
        margin-bottom: 8px;
        font-size: 14px;
        color: #06aebd;
        font-weight: 400;
        white-space: nowrap;
      }

      .arrivalDeparturePolicy {
        margin-bottom: 8px;
        font-size: 14px;
        color: #eb17c7;
        font-weight: 400;
        white-space: nowrap;
      }

      .mapBox {
        width: 350px;
        height: 160px;
        position: relative;

        .lookMap {
          position: absolute;
          right: 0;
          bottom: 0;
          outline: 0;
          border: none;
          border-radius: 1px;
          cursor: pointer;
          width: 100px;
          height: 30px;
          color: #fff;
          text-align: center;
          line-height: 30px;
          background: #008ad2;
          font-size: 14px;
        }
      }
    }
  }
}

.roomInfoBox {
  margin-top: 10px;

  .jcssBox {
    display: flex;

    .title {
      width: 60px;
      flex-shrink: 0;
    }
  }

  .label {
    margin-bottom: 4px;
    color: #999999;
    font-size: 12px;

    span {
      margin-left: 3px;
      margin-right: 6px;
      color: #000;
    }
  }

  p {
    margin: 0;
    padding: 0;
  }
}

.hotelRoomContent {
  position: relative;

  .infoBox {
    margin-top: 10px;
    padding: 20px 30px 30px;
    border-radius: 4px;
    box-shadow: 0 0 10px #f5f4f4;
    background: #ffffff;

    .title {
      font-size: 18px;
      color: #0f294d;
      line-height: 24px;
      font-weight: 500;
      padding-bottom: 16px;
    }
  }

  .transportationLocation {
    .mapBox {
      width: 100%;
      height: 600px;
    }
  }

  .serviceFacilities {
    .serviceFacilitiesBox {
      padding: 20px;

      .serviceFacilitiesItem {
        display: flex;
        padding-bottom: 20px;

        .type {
          width: 150px;
          font-size: 14px;
          color: #0f294d;
          letter-spacing: 0;
          line-height: 18px;
          font-weight: bold;
          flex-shrink: 0;
        }

        .facilitiesItem {
          display: flex;
          flex-wrap: wrap;

          .facilitiesItemBox {
            font-size: 14px;
            color: #0f294d;
            letter-spacing: 0;
            line-height: 18px;
            margin-right: 10px;
            width: 140px;
            margin-bottom: 10px;
          }
        }
      }
    }
  }

  .hotelInformation {
    ul li {
      display: flex;
      padding: 8px 0;
    }

    .left {
      font-size: 14px;
      color: #0f294d;
      letter-spacing: 0;
      line-height: 18px;
      font-weight: bold;
      flex-shrink: 0;
      margin-right: 10px;
      padding-left: 20px;
      width: 160px;
    }

    .right {
      font-size: 14px;
      color: #0f294d;
      letter-spacing: 0;
      line-height: 18px;
    }
  }
}

.ellipsis {
  white-space: nowrap;
  /* 确保文本在一行内显示 */
  overflow: hidden;
  /* 隐藏超出容器的文本 */
  text-overflow: ellipsis;
  /* 使用省略符号表示被截断的文本 */
}

:deep(.slick-list) {
  height: 360px;
}

:deep(.slick-dots) {
  position: relative;
  height: auto;
}

:deep(.slick-slide img) {
  border: 5px solid #fff;
  display: block;
  margin: auto;
  width: 400px;
  height: 340px;
}

:deep(.slick-arrow) {
  display: none !important;
}

:deep(.slick-thumb) {
  bottom: 0px;
}

:deep(.slick-thumb li) {
  width: 60px;
  height: 45px;
}

:deep(.slick-thumb li img) {
  width: 100%;
  height: 100%;
  filter: grayscale(100%);
  display: block;
}

:deep .slick-thumb li.slick-active img {
  filter: grayscale(0%);
}

:deep(.ant-table-cell) {
  padding: 6px 16px !important;
}

:deep(.ant-empty-description) {
  color: #969595;
}

img {
  object-fit: cover;
}

.accommodation {
  background-color: #fff;
  justify-content: center;
  align-items: center;
  padding-top: 20px;
  padding-left: 30px;

  :deep(.ant-table-cell) {
    color: #333 !important;
    font-weight: 500 !important;
  }

  ul {
    display: flex;
  }

  li {
    height: 100%;
    width: 33%;
    margin: 0 5px;
    padding: 15px 15px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.08);

    &:nth-child(1) {
      margin-left: 0px;
    }

    .flex {
      margin-top: 20px;

      p:nth-child(1) {
        color: #999999;
      }

      p:nth-child(2) {
        color: #0F294D;
        margin-left: 10px;
      }
    }
  }
}

.Venue {
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #E5E6EB;
  padding: 15px 15px;

  .Venue-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    p {
      color: rgba(22, 119, 255, 1);
      font-size: 14px;
      margin-right: 20px;
    }
  }

  .line-style {
    color: #999999;

    span {
      color: #0F294D;
    }

    p {
      color: #0F294D;
    }
  }
}

.tableType {
  width: 100%;
  margin-top: 20px;
  border-radius: 5px;

  table {
    border-collapse: collapse;
    width: 100%;
  }

  .table-top {
    font-weight: 500;
    font-size: 14px;
    color: #333333;

    td {
      width: 160px;
      height: 35px;
      background-color: #FAFAFA;
    }
  }

  th,
  td {
    border: 1px solid #F2F2F2;
    padding: 10px;
    /* 所有边框 */
    text-align: left;
  }

}

.footer {
  height: 50px;
  line-height: 20px;
  border-radius: 2px;
  background-color: rgba(255, 255, 255, 1);
  color: rgba(16, 16, 16, 1);
  font-size: 14px;
  text-align: center;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.4);
  background-color: #fff;
  width: calc(100% - 250px);
  position: fixed;
  bottom: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 999;

  p {
    margin-bottom: 0;
  }
}

:deep(.ant-image-img) {
  height: auto;
  vertical-align: middle;
}
.Venue-Modify{
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.venus-title {
  font-size: 18px;
  color: #0f294d;
  line-height: 24px;
  font-weight: 500;
}
</style>
