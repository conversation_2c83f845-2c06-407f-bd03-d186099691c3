<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  But<PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { DownOutlined, ExclamationCircleOutlined, PlusOutlined,UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { payApi,virtualPayApi } from '@haierbusiness-front/apis';
import {
    VirtualAccountTypeConstant,
    HaierBudgetSourceConstant,
    VirtualScopeConstanty,
    IVirtualChangeListRequest,
    PayStatusConstant,
    PayTypeConstant,
    VirtualAccountChangeTypeConstant
} from '@haierbusiness-front/common-libs';
import { errorModal, routerParam } from '@haierbusiness-front/utils';
import Eloading from '@haierbusiness-front/components/loading/ELoading.vue';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useRouter } from "vue-router";
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import EditDialog from './edit-dialog.vue'
import UserDialog from './user-dialog.vue'

const router = useRouter()
const columns: ColumnType[] = [
  {
    title: '账户',
    dataIndex: 'accountNo',
    fixed: 'left',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '企业编码',
    dataIndex: 'enterpriseCode',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '企业名称',
    dataIndex: 'enterpriseName',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '变动金额',
    dataIndex: 'amount',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '剩余金额',
    dataIndex: 'balance',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '类别',
    dataIndex: 'type',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '变动来源应用',
    dataIndex: 'applicationCode',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '费用变动说明',
    dataIndex: 'remark',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '变动凭证单号',
    dataIndex: 'changeCode',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '变动凭证业务单号',
    dataIndex: 'changeBusinessCode',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '180px',
    fixed: 'right',
    align: 'center'
  },
]
const searchParam = ref<IVirtualChangeListRequest>({ needPage: true })
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(virtualPayApi.changeList);

const reset = () => {
  startBeginAndEnd.value = undefined
  searchParam.value = { needPage: true }
}

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const type = computed(() => {
    return VirtualAccountChangeTypeConstant.toArray()
})

const advancedSearchVisible = ref(false)
const gotoAdvancedSearch = () => {
  if (advancedSearchVisible.value) {
    advancedSearchVisible.value = false
  } else {
    advancedSearchVisible.value = true
  }
}

const startBeginAndEnd = ref<[Dayjs, Dayjs]>()
watch(() => startBeginAndEnd.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.changeBegin = n[0]
    searchParam.value.changeEnd = n[1]
  } else {
    searchParam.value.changeBegin = undefined
    searchParam.value.changeEnd = undefined
  }
});

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">

    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="accountNo">账户：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="accountNo" v-model:value="searchParam.accountNo" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="applicationCode">应用Code：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="applicationCode" v-model:value="searchParam.applicationCode" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="applicationCode">订单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="applicationCode" v-model:value="searchParam.code" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="enterpriseCode">企业编码：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="enterpriseCode" v-model:value="searchParam.enterpriseCode" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
          
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
                <label for="searchState">类别：</label>
            </h-col>
            <h-col :span="4">
                <h-select ref="select" v-model:value="searchParam.type" style="width: 100%" allow-clear>
                <h-select-option :value="item?.code" v-for="(item, index) in type" :key="index">{{ item?.desc }}</h-select-option>
                </h-select>
            </h-col>
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
                <label for="enterpriseCode">费用变动说明：</label>
            </h-col>
            <h-col :span="4">
                <h-input id="enterpriseCode" v-model:value="searchParam.remark" placeholder="" autocomplete="off"
                allow-clear />
            </h-col>
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
                <label for="enterpriseCode">变动凭证单号：</label>
            </h-col>
            <h-col :span="4">
                <h-input id="enterpriseCode" v-model:value="searchParam.changeCode" placeholder="" autocomplete="off"
                allow-clear />
            </h-col>
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
                <label for="enterpriseCode">变动业务单号：</label>
            </h-col>
            <h-col :span="4">
                <h-input id="enterpriseCode" v-model:value="searchParam.changeBusinessCode" placeholder="" autocomplete="off"
                allow-clear />
            </h-col>
        </h-row>
        <template v-if="advancedSearchVisible">
            <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
                <h-col :span="2" style="text-align: right;">
                    <label for="startBeginAndEnd">支付发起日期：</label>
                    </h-col>
                    <h-col :span="4">
                    <h-range-picker v-model:value="startBeginAndEnd" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" />
                </h-col>
            </h-row>
        </template>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button type="link" @click="gotoAdvancedSearch()">
              <UpOutlined v-if="advancedSearchVisible" />
              <DownOutlined v-else />
              高级搜索
            </h-button>
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table  :columns="columns" :row-key="record => record.id" :size="'small'"
          :data-source="dataSource" :pagination="pagination" :scroll="{ y: 550 }" :loading="loading"
          @change="handleTableChange($event as any)">
          <template #bodyCell="{ text ,column, record }">
            <template v-if="column.dataIndex === 'type'">
                {{ VirtualAccountChangeTypeConstant.ofType(text)?.desc }}
            </template>
            <template v-if="column.dataIndex === '_operator'">
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #********;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
