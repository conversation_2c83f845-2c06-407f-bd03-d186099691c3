<template>
    <div :style="{height:props.height+'px'}">
        <bar-line v-if="loaded" :height="props.height" :legend="legend" :x-axis="xAxis" :y-axis="yAxis" :series="series" />
    </div>
</template>
<script setup lang="ts">
    import { ref } from "vue";
    import BarLine from "../../components/barLine.vue"
    import { querySettleTrend } from "@haierbusiness-front/apis/src/data/board";
    import { EventBus } from "../../eventBus";
    const props = defineProps({
        height:{
            type:Number,
            default:250
        }
    })
    const loaded = ref(false);
    const loading = ref(false);
    const legend = ["订单数","结算金额"];
    const xAxis = ref([]);
    const yAxis = [{
            type: "value",
            name: "订单数",
        },
        {
            type: "value",
            name: "结算金额(万)",
        },
    ];
    const series = ref([]);
    // onMounted(()=>{
    //     queryData();
    // })
    EventBus.on((event)=>{
        if(event=="refresh")queryData();
    })
    const queryData = async ()=>{
        loading.value = true;
        const { data } = await querySettleTrend({});
        loading.value = false;
        const barData = [];
        const lineData = [];
        const xData = [];
        data.rows.forEach((item,index)=>{
            xData.push(item[0]);
            barData.push(item[1]||0);
            lineData.push(item[2]/10000||0);
        })
        xAxis.value = xData;
        series.value = [
            {
                name: "订单数",
                type: "bar",
                color: "rgba(0,240,255,0.4)",
                itemStyle: {
                    borderColor: "#00F0FF",
                },
                data: barData
            },{
                name: "结算金额",
                type: "line",
                yAxisIndex: 1,
                color: "#FFD700",
                smooth: true,
                symbol: "none",
                data: lineData
            }
        ] as any;
        loaded.value = true;
    }
</script>