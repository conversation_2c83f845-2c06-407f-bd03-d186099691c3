<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Cascader as hCascader
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons-vue';
import { banquetRefundApi, download, banquetApi, cityApi } from '@haierbusiness-front/apis';
import {
  List_4Params,
  IApply,
  BanquetStatusEnum,
  BanquetApproveStatusEnum,
  BanquetPayTypeNum,
  statementStatusNum
} from '@haierbusiness-front/common-libs';
import { getEnumOptions } from '@haierbusiness-front/utils';

import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import router from '../../router'
// const router = useRouter()
import type { ShowSearchType } from 'ant-design-vue/es/cascader';
const route = ref(getCurrentRoute());
const currentRouter = ref()

onMounted(async () => {
    currentRouter.value = await router
    if(route.value?.query?.refundOrderCode){
      searchParam.value.refundOrderCode = route.value?.query?.refundOrderCode
    }
    if(route.value?.query?.orderBookingCode){
      searchParam.value.orderBookingCode = route.value?.query?.orderBookingCode
    }
    getCityList()
    handleTableChange({ current: 1, pageSize: 10 })

})

// 订单状态
const orderState = computed(() => {
  return getEnumOptions(BanquetStatusEnum, true);
});

const approveState = computed(() => {
  return getEnumOptions(BanquetApproveStatusEnum, true);
});


// 支付类型
const payTypeOptions = computed(() => {
  return getEnumOptions(BanquetPayTypeNum, true);
});

// 汇总状态
const statementStatusOptions = computed(() => {
  return getEnumOptions(statementStatusNum, true);
});


const columns: ColumnType[] = [
  {
    title: '退款单号',
    dataIndex: 'refundOrderCode',
    width: '240px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '预订单号',
    dataIndex: 'orderBookingCode',
    width: '240px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '签单人工号',
    dataIndex: 'signerCode',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '签单人姓名',
    dataIndex: 'signerName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '支付类型',
    dataIndex: 'payType',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '处理时间',
    dataIndex: 'dealTime',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '退款餐厅',
    dataIndex: 'restaurantName',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '退款金额/元',
    dataIndex: 'refundAmount',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '汇总状态',
    dataIndex: 'statementStatus',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  // {
  //   title: '操作',
  //   dataIndex: '_operator',
  //   width: '200px',
  //   fixed: 'right',
  //   align: 'center'
  // },
];
const searchParam = ref<List_4Params>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(banquetRefundApi.list);

const {
  data:exportListData,
  run:exportListApiRun,
  loading:exportListLoading,
} = useRequest(banquetRefundApi.exportList);

const reset = () => {
  cityCodeList.value = []
  searchParam.value = {}
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize:10
  });
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const goToDetail = (id: number) => {
  currentRouter.value.push({
    path: '/apply/detail',
    query: {
      id: id
    }
  })
}

// 获取美团城市列表
const cityDict = ref<Array<any>>([])
const getCityList = () => {
  // banquetApi.getCity().then(res => {
  //   cityDict.value = res
  // })

  const params = {
    id: 1,
    providerCode: 'MT',
    subdistrict: 2
  }
  cityApi.getCityTree(params).then(res => {
    cityDict.value = res.children
  })
}

const filter: ShowSearchType['filter'] = (inputValue, path) => {
  return path.some(option => option.chineseName.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
};

// 根据id 获取城市 code
const getCityCodeById = (id?:string | number) => {
  if (!id) {
    return ''
  }
  let code = ''
  cityDict.value?.forEach(province => {
    province?.children?.forEach(city => {
      if (id == city.id) {
        if (city?.providerMapList && city?.providerMapList.length > 0) {
          code = city?.providerMapList[0]?.districtId
        }
      }
      
    });
  });
  return code
}


const cityCodeList = ref([])
watch(() => cityCodeList.value, (n: any, o: any) => {
  if (n && n.length > 0) {
    searchParam.value.cityCode = [getCityCodeById(n[1] || n[0])]
  } else {
    searchParam.value.cityCode = undefined
  }
});

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="refundOrderCode">退款单号:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="退款单号" v-model:value="searchParam.refundOrderCode"  style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="orderBookingCode">预订单号:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="预订单号" v-model:value="searchParam.orderBookingCode"  style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="restaurantName">退款餐厅:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="退款餐厅" v-model:value="searchParam.restaurantName"  style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="signerName">签单人姓名:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="签单人姓名" v-model:value="searchParam.signerName"  style="width: 100%" allow-clear />
          </h-col>
        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="signerCode">签单人工号:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="签单人工号" v-model:value="searchParam.signerCode"  style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="sceneType">支付类型:</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value="searchParam.payType"
              allow-clear
              style="width: 100%"
              placeholder="支付类型"
            >
              <h-select-option  v-for="(item, index) in payTypeOptions" :key="index" :value="item.value">{{
                item.label
              }}</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="sceneType">汇总状态:</label>
          </h-col>
          <h-col :span="4">
            <h-select
              ref="select"
              v-model:value="searchParam.statementStatus"
              allow-clear
              style="width: 100%"
              placeholder="汇总状态"
            >
              <h-select-option  v-for="(item, index) in statementStatusOptions" :key="index" :value="item.value">{{
                item.label
              }}</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="applicationTimes">处理时间:</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="searchParam.dealTimes" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>
        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button  style="margin-right: 10px" type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
<!-- 
            <h-button type="primary" :loading="exportListLoading" @click="exportListApiRun(searchParam);">
              导出
            </h-button> -->
          </h-col>
        </h-row>


        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id"  :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">

            <template v-if="column.dataIndex === 'sceneType'">
              {{ record.sceneType == 1 ? '宴请' :'外卖' }}
            </template>
            <template v-if="column.dataIndex === 'payType'">
              {{BanquetPayTypeNum[record.payType] }}
            </template>
            <template v-if="column.dataIndex === 'statementStatus'">
              {{statementStatusNum[record.statementStatus] }}
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="goToDetail(record.id)">查看详情</h-button>

            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    

  </div>
</template>

<style scoped lang="less">

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

</style>
