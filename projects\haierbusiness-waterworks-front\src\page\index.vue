<script setup lang="ts">
import { onMounted, ref, computed } from 'vue';
import eFooter from '@haierbusiness-front/components/layout/Footer.vue';
import eHeader from '@haierbusiness-front/components/layout/Header.vue';
// import discount from '@/assets/image/discount/discount.png'
// import giftTop from '@/assets/image/discount/gift-top.png'
// import giftBottom from '@/assets/image/discount/gift-bottom.png'

onMounted(() => {
  const giftTop = document.querySelector('.gift-top-img')
  giftTop?.classList.add('move')
  const giftBottom = document.querySelector('.gift-bottom-img')
  giftBottom?.classList.add('move-bottom')

  giftTop?.addEventListener('transitionend', function() {
    if(giftTop.classList.contains('move')) {
      giftTop.classList.remove('move')
      giftBottom?.classList.remove('move-bottom')
    } else {
      giftTop?.classList.add('move')
      giftBottom?.classList.add('move-bottom')
    }
  })
})

</script>

<template>
    <div style="min-height:100vh" class="home">
      <e-header></e-header>
      <router-view></router-view>
      <e-footer class="footer"></e-footer>
    </div>
</template>

<style scoped lang="less">

.pointer {
  cursor: pointer;
}

.img-cover {
    width: 100%;
    height: 100%;
    z-index: 1;
}

.home {
  // width:1200px;
  // background: #f5f7fa;
  margin:0 auto;
}

.sub-title {
  color: #3983E5;
  font-size: 14px;
  font-weight: 400;
}


:root {
  // font-family: HarmonyBold, Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}
</style>

<style>

@keyframes discountMove{
  0 {
    transform: perspective(200px) translateZ(0px);
  }
  20% {
    transform: perspective(200px) translateZ(60px);
  }
  40%, 100% {
    transform: perspective(200px) translateZ(0px);
  }
}

</style>
