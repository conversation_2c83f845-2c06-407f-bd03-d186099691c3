<script setup lang="ts">
import { onMounted, ref, watch,onUnmounted } from 'vue';
import type { CascaderProps } from 'ant-design-vue';
import cityChose from '@haierbusiness-front/components/hotelCityChose/index.vue';
import { CityResponse, CityItem, TCteateTeam, TTicket } from '@haierbusiness-front/common-libs';

const emit = defineEmits(['chosedHotelCity','changeLevel'])


interface Props {
    chosedCityName?: string;
    level?:string | number;
    form?: any;
}
const props = withDefaults(defineProps<Props>(), {
    chosedCityName: '',
    form:{}
});

watch(props, (newValue) => {
    chosedCityName.value = newValue.chosedCityName
    level.value = newValue.level
})

const chosedCityName = ref(props.chosedCityName)
watch(props, (newValue) => {
    form.value = newValue.form
})
const form = ref(props.form)

const level = ref(props.level)


const changeLevel = (val: number) => {
    emit('changeLevel', val)
}
const chosedHotelCity = (city: CityItem) => {
    emit('chosedHotelCity', city)
};

const eliminateSearchProvinceIds = [7,16,17]


</script>


<template>
    <div class="apply-destination-level-component">
        <div class="ticket-item-city" >
            <div class="ticket-item-ai">
                <div class="item-labels">目的地</div>
                
                <city-chose
                  class="mr-10 city-box-index"
                  placeholder="目的地"
                  width="100%"
                  :defaultValue="form"
                  :value="chosedCityName"
                  :showInternational="false"
                  :bordered="false"
                  @chosedCity="chosedHotelCity"
                  :eliminateSearchProvinceIds="eliminateSearchProvinceIds"
                ></city-chose>


            </div>
            <div  class="ticket-item-ai">
                <div class="item-labels">酒店级别</div>
                <div class="item-num">
                    <a-select v-model="level" @change="changeLevel" class="level" size="small" placeholder="请选择酒店级别">
                        <a-select-option value="5">五星/豪华</a-select-option>
                        <a-select-option value="4">四星/高档</a-select-option>
                        <a-select-option value="3">三星/舒适</a-select-option>
                        <a-select-option value="2">二星及以下/经济</a-select-option>
                    </a-select>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
@import url('./common.less');

.level {
    width:100%; 
    height: 22px;
}

</style>

<style>
.apply-destination-level-component .ant-select-selector {
  border:none !important;
  box-shadow: none !important;
  height: 22px !important;
  padding: 0px !important;
  
}

.apply-destination-level-component .ant-select-selection-placeholder {
    font-size: 16px !important;
    color: rgba(0,0,0,0.35) !important;
    
    padding-inline-end: 25px !important;
    display: flex;
    align-items: center;
}

.apply-destination-level-component .ant-select-selection-item {
    display: flex;
    align-items: center;
    font-size: 16px !important;
    
    color: rgba(0,0,0,0.85);
}

.apply-destination-level-component .ant-select-item-option-content {
  
}

.apply-destination-level-component .ant-cascader {
    width: 100%;
}

.ant-cascader-menus {
    
}

.apply-destination-level-component .ant-select-selector {
  border:none !important;
  box-shadow: none !important;
  height: 22px !important;
  padding: 0px !important;
  
}

.apply-destination-level-component .ant-select-selection-placeholder {
  font-size: 16px !important;
  color: rgba(0,0,0,0.35) !important;
  
  padding-inline-end: 25px !important;
}

.apply-destination-level-component .ant-select-selection-item {
  font-size: 16px !important;
  
  color: rgba(0,0,0,0.85);
}

.apply-destination-level-component .ant-select-item-option-content {
  
}
</style>
