import { create<PERSON><PERSON>, defineStore } from 'pinia'
import { ref, computed, onMounted } from 'vue'
import { waterworkCartApi as api } from '@haierbusiness-front/apis'

export const useCartStore = defineStore('cart', () => {
  const goodsList = ref<any[]>([])
  const isLoading = ref(false)
  const cartCount = computed(() => goodsList.value.reduce((total, item) => total + item.amount, 0))
  const orderList = ref<any[]>([]) // 订单列表
  async function addToCart(item: any) {
    // goodsList.value.push(item)
    console.log(item, 999)
    // 调用接口
    const res = await api.add({
      userCode: '01506579',
      amount: 1,
      productId: item.id,
      status: 1
    })
    // 更新数据
    fetchGoodsList()
  }

  async function deleteFromCart(id: any) {
    await api.delete(id)
    fetchGoodsList()
  }

  function setOrderList(list: any[]) {
    orderList.value = list.map((item) => {
      return {
        ...item,
        waterCount: item.amount,
      }
    })
  }

  async function changeAmount(item:any) {
    // 调用接口
    const res = await api.update({
      userCode: '01506579',
      amount: item.amount,
      productId: item.id,
      status: 1
    })
    // 更新数据
    fetchGoodsList()
  }

  // 获取购物车数据
  async function fetchGoodsList() {
    isLoading.value = true
    try {
      const res = await api.listAll({ userCode: '01506579' })
      // console.log(res, 888)
      goodsList.value = res || []
    } finally {
      isLoading.value = false
    }
  }

  onMounted(() => {
    fetchGoodsList()
  })

  return {
    goodsList, // 列表
    isLoading, // 是否加载中
    cartCount, // 数量
    orderList, // 订单列表
    addToCart, // 添加项
    deleteFromCart, //移除
    setOrderList, // 设置订单列表
  }
})
