<script setup lang="ts">
import { Tag as hTag, Card as hCard, <PERSON>lapse as hCollapse, CollapsePanel as hCollapsePanel, DatePicker as hDatePicker, Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem, Input as hInput, Table as hTable, BreadcrumbItem as hBreadcrumbItem, Breadcrumb as hBreadcrumb, LayoutSider as hLayoutSider, LayoutFooter as hLayoutFooter, LayoutContent as hLayoutContent, LayoutHeader as hLayoutHeader, Layout as hLayout, MenuItem as hMenuItem, MenuItemGroup as hMenuItemGroup, SubMenu as hSubMenu, Menu as hMenu, Divider as hDivider, Space as hSpace, Button as hButton, Col as hCol, Result as hResult, Row as hRow, TabPane as hTabPane, Tabs as hTabs, message, TableProps } from 'ant-design-vue';
import { computed, onMounted, ref, watch } from 'vue';
import { UploadOutlined, UserOutlined, NotificationOutlined, AppstoreOutlined, MenuFoldOutlined, MenuUnfoldOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { b2bWyyBalanceApi, budgetHaierPayRecordApi, payApi, virtualPayApi } from '@haierbusiness-front/apis';
import { usePagination, useRequest } from 'vue-request';
import { VirtualAccountChangeTypeConstant,VirtualAppScopeConstant,VirtualScopeConstanty,VirtualAccountTypeConstant,RefundStatusConstant, BudgetNotifiedReleaseCvpStateConstant, BalanceStatusConstant, IHaierAccountBillInfo, IUserSaveUpdateRequest, IWyyB2bAccountRequest, IWyyB2bDetailsRequest, IPaymentRecordListResponse, PayTypeConstant, PayStatusConstant, HaierBudgetTypeConstant, HaierBudgetSourceConstant, IPaymentVirtualAccount } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { resolveParam, copyValue } from '@haierbusiness-front/utils';
import { ColumnType } from 'ant-design-vue/lib/table';

const columns: ColumnType[] = [

  {
    title: '单号',
    dataIndex: 'code',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: '60px',
    align: 'center'
  },
  {
    title: '金额',
    dataIndex: 'amount',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '变动后余额',
    dataIndex: 'balance',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '变动来源应用',
    dataIndex: 'applicationCode',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '费用变动说明',
    dataIndex: 'remark',
    width: '300px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '变动凭证号',
    dataIndex: 'changeCode',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '业务单号',
    dataIndex: 'changeBusinessCode',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '附件',
    dataIndex: 'attachmentFileId',
    width: '80px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  }
];



const props = defineProps({
  query: Object
});
const recordData = computed(() => <IPaymentVirtualAccount>resolveParam(props.query?.record));
const {
  data,
  run: logsApiRun,
  loading,
} = usePagination(virtualPayApi.changeList, {
  defaultParams: [
    {
      accountNo: recordData.value.accountNo
    }
  ],
  manual: false
});

// 查询权限
const {
  data: accountTypesApiData,
  run: accountTypesApiRun,
  loading: accountTypesApiLoading
} = useRequest(virtualPayApi.accountTypes, {
  defaultParams: [
     recordData.value?.accountNo || ''
  ],
  manual: false
});

const accountTypesDataSource = computed(() => accountTypesApiData.value || []);

// 查询退款信息
const {
  data: accountUsersApiData,
  run: accountUsersAllApiRun,
  loading: accountUsersAllApiLoading
} = useRequest(virtualPayApi.accountUsers, {
  defaultParams: [
  recordData.value?.accountNo || ''
  ],
  manual: false
});

const accountUsersDataSource = computed(() => accountUsersApiData.value || []);

const {
  data: detailsExportData,
  run: detailsExportApiRun,
  loading: detailsExportLoading,
} = useRequest(b2bWyyBalanceApi.detailsExport);

const dataSource = computed(() => data.value?.records || []);
const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

watch(() => props.query, (n: any, o: any) => {
  accountUsersAllApiRun(recordData.value.accountNo||'')
  accountUsersAllApiRun(recordData.value.accountNo||'')
  logsApiRun({ accountNo: recordData.value.accountNo })
});

const gotoAttachment = (url: string) => {
  window.open(url)
}
const collapseAactiveKey = ref(["1", "2", "3"])
</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-collapse v-model:activeKey="collapseAactiveKey" style="font-size: 14px;font-weight: 600;" ghost>
      <h-collapse-panel key="1">
        <template #header>
          <div style="height: 22px;width: 5px;border-radius: 2px;background-color: #0073e5;margin-right: 10px;"></div>
          <span style="font-weight: 600;font-size: 15px;">账户详情</span>
        </template>
        <div style="padding: 0 20px 0px 20px;line-height: 28px;">
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">账户：</h-col>
            <h-col span="5">{{ recordData.accountNo }}</h-col>
            <h-col span="3" style="text-align: right;">名称：</h-col>
            <h-col span="5">{{ recordData.accountName }}</h-col>
            <h-col span="3" style="text-align: right;">当前余额：</h-col>
            <h-col span="5">{{ recordData.amount }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">状态：</h-col>
            <h-col span="5">
              <h-tag v-if="recordData.state === 0" color="default">
                无效
              </h-tag>
              <h-tag v-if="recordData.state === 1" color="success"> 
                可用
              </h-tag>
            </h-col>
            <h-col span="3" style="text-align: right;">企业：</h-col>
            <h-col span="5">{{ recordData.enterpriseCode }}/{{ recordData.enterpriseName }}</h-col>
            <h-col span="3" style="text-align: right;">过期时间：</h-col>
            <h-col span="5">{{ recordData.expireDate }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">类型：</h-col>
            <h-col span="5">{{ VirtualAccountTypeConstant.ofType(recordData.type)?.desc }}</h-col>
            <h-col span="3" style="text-align: right;">作用域：</h-col>
            <h-col span="5">{{ VirtualScopeConstanty.ofType(recordData.scope)?.desc }}</h-col>
          </h-row>
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">最后修改人：</h-col>
            <h-col span="5">{{ recordData.lastModifiedBy }}</h-col>
            <h-col span="3" style="text-align: right;">最后修改时间：</h-col>
            <h-col span="5">{{ recordData.gmtModified }}</h-col>
          </h-row>
        </div>
      </h-collapse-panel>
      <h-collapse-panel v-if="accountTypesDataSource && accountTypesDataSource.length > 0" key="2">
        <template #header>
          <div style="height: 22px;width: 5px;border-radius: 2px;background-color: #0073e5;margin-right: 10px;"></div>
          <span style="font-weight: 600;font-size: 15px;">账户权限信息</span>
        </template>
       
        <div style="padding: 0 20px 0px 20px;line-height: 28px;" v-for="i in accountTypesDataSource" :key="i?.id">
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">可用应用：</h-col>
            <h-col span="5">{{ i.applicationCode }}</h-col>
            <h-col span="3" style="text-align: right;">作用域：</h-col>
            <h-col span="5">{{ VirtualAppScopeConstant.ofType(i.scope)?.desc }}</h-col>
            <h-col span="3" style="text-align: right;">备注：</h-col>
            <h-col span="5">{{ i.scope }}</h-col>
          </h-row>
        </div>
      </h-collapse-panel>
      <h-collapse-panel v-if="accountUsersDataSource && accountUsersDataSource.length > 0 && recordData.scope === VirtualScopeConstanty.PERSON.code" key="3">
        <template #header>
          <div style="height: 22px;width: 5px;border-radius: 2px;background-color: #0073e5;margin-right: 10px;"></div>
          <span style="font-weight: 600;font-size: 15px;">账户用户</span>
        </template>
        <div style="padding: 0 20px 0px 20px;line-height: 28px;" v-for="i in accountUsersDataSource" :key="i?.id">
          <h-row :align="'middle'" style="margin-bottom: 8px;">
            <h-col span="3" style="text-align: right;">授权用户：</h-col>
            <h-col span="5">{{ i.userCode }}</h-col>
            <h-col span="3" style="text-align: right;">授权应用：</h-col>
            <h-col span="5">{{ i.applicationCode }}</h-col>
          </h-row>
        </div>
      </h-collapse-panel>
    </h-collapse>

    <h-row :align="'middle'">
      <h-col :span="24">
        <h-card :bordered="false" style="width: 100%">
          <template #title>
            <div
              style="height: 22px;width: 5px;border-radius: 2px;background-color: #0073e5;margin-right: 10px;margin-left: 24px;display:  inline-block;vertical-align:middle">
            </div>
            <span style="font-weight: 600;font-size: 15px">变更日志</span>
          </template>
          <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
            :scroll="{ y: 550 }" :pagination="pagination" :loading="loading">
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'type'">
                {{ VirtualAccountChangeTypeConstant.ofType(record.type)?.desc }}
              </template>
            </template>
          </h-table>
        </h-card>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}
</style>
