<template>
  <div style="
      background-color: #ffff;
      height: 100%;
      width: 100%;
      padding: 10px 10px 0px 10px;
      overflow: auto;
    ">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="cxr_xm">乘机人：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="cxr_xm" v-model:value="searchKey.cxr_xm" placeholder="" autocomplete="off" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="budget_people_name">预算人姓名：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="budget_people_name" v-model:value="searchKey.budget_people_name" placeholder=""
              autocomplete="off" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="ddbh">订单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="ddbh" v-model:value="searchKey.ddbh" placeholder="" autocomplete="off" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="dd_ydsj">预订时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="searchKey.dd_ydsj" value-format="YYYY-MM-DD" style="width: 100%" />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="djbh">出差申请单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="djbh" v-model:value="searchKey.djbh" placeholder="" autocomplete="off" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="yddbh">原订单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="yddbh" v-model:value="searchKey.yddbh" placeholder="" autocomplete="off" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="account_company_code">结算单位：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择结算单位" v-model:value="searchKey.account_company_code" show-search
              :filter-option="filterOption" allow-clear @search="handleSearch" :options="settleCompany"
              style="width: 100%" :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="budget_department_code ">预算部门：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择预算部门" v-model:value="searchKey.budget_department_code" show-search allow-clear
              :filter-option="filterOption" @search="handleBudgetSearch" :options="settleDepartment" style="width: 100%"
              :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>
        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="budget_source">预算类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择预算类型" v-model:value="searchKey.budget_source" :options="budgetTypeList" allow-clear
              style="width: 100%" :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="ddlxmc">订单类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择订单类型" v-model:value="searchKey.ddlxmc" :options="orderTypeOptions" allow-clear
              style="width: 100%" :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="ywfssj">业务发生时间：</label>
          </h-col>
          <h-col :span="5">
            <h-range-picker v-model:value="searchKey.ywfssj" :show-time="{ format: 'HH:mm:ss' }"
              format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            <!--valueFormat="YYYY-MM-DD HH:mm:ss" -->
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 0px">
            <label for="yskm">费用项目编码：</label>
          </h-col>
          <h-col :span="3">
            <h-input id="yskm" v-model:value="searchKey.yskm" placeholder="" autocomplete="off" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="area_code ">领域：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择领域" v-model:value="searchKey.area_code" show-search allow-clear
              :filter-option="filterOption" @search="handleAreaSearch" :options="areaList" style="width: 100%"
              :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="pt_code">平台：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择平台" v-model:value="searchKey.pt_code" show-search :filter-option="filterOption" allow-clear
              @search="handlePtSearch" :options="platformList" style="width: 100%"
              :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="pl_code">产业线：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择产业线" v-model:value="searchKey.pl_code" show-search :filter-option="filterOption" allow-clear
              @search="handlePlSearch" :options="industryList" style="width: 100%"
              :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>

          <h-col v-if="sjlyInnersShow" :span="2" style="text-align: right; padding-right: 10px">
            <label for="pl_code">内外部数据：</label>
          </h-col>
          <h-col v-if="sjlyInnersShow" :span="4">
            <h-select v-model:value="searchKey.sjly_inner" show-search allow-clear
                      :options="preData.sjlyInners" style="width: 100%"
                      :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>

        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button style="margin-right: 10px" @click="handleReset">重置</h-button>
            <h-button style="margin-right: 10px" type="primary" @click="onFilterChange">
              <SearchOutlined />
              查询
            </h-button>
            <h-button type="primary" style="margin-right: 10px" v-if="!pagination.disabled" :loading="downloading"
              @click="download">
              <UploadOutlined />
              导出
            </h-button>
          </h-col>
        </h-row>

        <!-- <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: left;">
            <h-button type="primary" @click="handleCreate">
              <PlusOutlined /> 新增员工
            </h-button>
          </h-col>
        </h-row> -->
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="(record) => record.id" :size="'small'" :data-source="data"
          :pagination="pagination" :scroll="{ y: 550, x: 6000 }" :loading="loading" @change="onPageChange">
          <template #emptyText v-if="pagination.disabled">
            <div>暂无权限，<a @click="goApplyDetail">去申请</a></div>
          </template>
          <template #customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
            <!-- 日期选择器 -->
            <div v-if="column.title.indexOf('时间') != -1 || column.title.indexOf('日期') != -1" style="padding: 8px">
              <h-range-picker v-model:value="searchKey[column.key]" value-format="YYYY-MM-DD"
                style="width: 218px; margin-bottom: 8px;" />
              <div style="display: block">
                <a-button type="primary" size="small" style="width: 90px; margin-right: 8px" @click="onFilterChange">
                  <template #icon>
                    <SearchOutlined />
                  </template>
                  搜索
                </a-button>
                <a-button size="small" style="width: 90px" @click="handleReset">
                  重置
                </a-button>
              </div>
            </div>
            <!-- 文本搜索框 -->
            <div v-else style="padding: 8px">
              <h-input ref="searchInput" :placeholder="`搜索${column.title}`"
                v-model:value="searchKey[column.key.replace('_filter', '')]" style="width: 188px; margin-bottom: 8px;"
                allow-clear @pressEnter="onFilterChange" />
              <div style="display: block">
                <a-button type="primary" size="small" style="width: 90px; margin-right: 8px" @click="onFilterChange">
                  <template #icon>
                    <SearchOutlined />
                  </template>
                  搜索
                </a-button>
                <a-button size="small" style="width: 90px" @click="handleReset">
                  重置
                </a-button>
              </div>
            </div>
          </template>
          <template #customFilterIcon="{ filtered }">
            <SearchOutlined :style="{ color: filtered ? '#108ee9' : undefined }" />
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>

  <!-- <div v-if="visible">
  <edit-dialog
        :show="visible"
        :data="editData"
        @cancel="onDialogClose"
        @ok="handleOk"
    >
    </edit-dialog> 
  </div> -->
</template>

<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  Button as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
} from 'ant-design-vue';
import { reactive, ref, computed, onMounted } from "vue";
import { PlusOutlined, SearchOutlined, UploadOutlined } from '@ant-design/icons-vue';
import moment from 'moment';
import { reset } from "@haierbusiness-front/utils/src/commonUtil";
import { checkUserGroups } from "@haierbusiness-front/utils/src/authorityUtil";
import { useSearch } from "../../../composables/useSearch";
import {
  ReportFilter,
  ReportType,
  ApplyCompanyType,
  UserGroupSystemConstant,
} from "@haierbusiness-front/common-libs";
import { reportApi } from "@haierbusiness-front/apis";
import { internalColumns, aggregatorsToColumn, budgetTypeOptions } from "../columns";
import { getCurrentRouter, routerParam } from "@haierbusiness-front/utils";
const router = getCurrentRouter();
const yesterday = moment().subtract(1, 'days');
const year = moment().subtract(1, 'year');
const pattern = 'YYYY-MM-DD';
const columns = aggregatorsToColumn(
  checkUserGroups(
    [
      UserGroupSystemConstant.SUPER_MANAGE.groupId,
      UserGroupSystemConstant.REPORT_CONTROL.groupId,
    ],
    "OR"
  )
    ? internalColumns
    : internalColumns.filter(
      (item) =>
        item.alias != "政策节省" &&
        item.alias != "舱位码" &&
        item.alias != "供应商" &&
        item.alias != "服务费" &&
        item.alias != "因公因私"
    )
);

const orderTypeOptions = [
  {
    name: "国内机票退票单",
    code: "国内机票退票单",
  },
  {
    name: "国内机票正常单",
    code: "国内机票正常单",
  },
  {
    name: "国内机票改签单",
    code: "国内机票改签单",
  },
];

const preData = {
  sjlyInners: reactive([{
    code: '1',
    name: '内部数据'
  }, {
    code: '2',
    name: '外部数据'
  }]),
}

const searchKey = reactive<ReportFilter>({
  ddbh: null,
  cxr_xm: null,
  sjly_inner: null,
  dd_ydsj: [moment(year).format(pattern), moment(yesterday).format(pattern)] as string[],
  account_company_code: null,
  datartParams: {
    moduleType: 1,
    type: "domesticAirTickets",
    viewId: "ab679edb60894c8b95a65fe5fed786d4",
    aggregators: checkUserGroups(
      [
        UserGroupSystemConstant.SUPER_MANAGE.groupId,
        UserGroupSystemConstant.REPORT_CONTROL.groupId,
      ],
      "OR"
    )
      ? internalColumns
      : internalColumns.filter(
        (item) =>
          item.alias != "政策节省" &&
          item.alias != "舱位码" &&
          item.alias != "供应商" &&
          item.alias != "服务费" &&
          item.alias != "因公因私"
      ),
    defaultFilters: [
      {
        aggOperator: null,
        column: ["gngj"],
        sqlOperator: "EQ",
        values: [
          {
            value: "1",
            valueType: "STRING",
          },
        ],
      },{
        aggOperator: null,
        column: ["sjly_inner"],
        sqlOperator: "EQ",
        values: [{
          value: "1",
          valueType: "STRING",
        }]
      }
    ],
    orders: [{
      "column": [
        "dd_ydsj"
      ],
      "operator": "DESC"
    }],
    functionColumns: [
      {
        alias: "cxr_xm_filter",
        snippet: "if(filter_flag=1,'***',cxr_xm)",
      },
      {
        alias: "cxr_gh_filter",
        snippet: "if(filter_flag=1,'***',cxr_gh)",
      },
      {
        alias: "pnr_cfcs_mc_filter",
        snippet: "if(filter_flag=1,'***',pnr_cfcs_mc)",
      },
      {
        alias: "pnr_ddcs_mc_filter",
        snippet: "if(filter_flag=1,'***',pnr_ddcs_mc)",
      },
      {
        alias: "pnr_hbh_filter",
        snippet: "if(filter_flag=1,'***',pnr_hbh)",
      },
      {
        alias: "sqrgh_filter",
        snippet: "if(filter_flag=1,'********',sqrgh)",
      },
      {
        alias: "sqr_filter",
        snippet: "if(filter_flag=1,'***',sqr)",
      },
      {
        alias: "jp_ddsj_filter",
        snippet: "if(filter_flag=1,'**********',jp_ddsj)",
      },
      {
        alias: "jp_cfsj_filter",
        snippet: "if(filter_flag=1,'**********',jp_cfsj)",
      },
      {
        alias: "sftp_state",
        snippet: "if(sftp=1,'已退票','未退票')",
      },
      {
        alias: "sfgq_state",
        snippet: "if(sfgq=1,'已被改签','未被改签')",
      },
    ],
  },
  fileName: "国内机票",
});

const goApplyDetail = () => {
  router.push("/data/report/permission/apply");
};

const handleReset = () => {
  reset(searchKey, ["datartParams", 'fileName']);
  onFilterChange();
};

const {
  data,
  fetchData,
  pagination,
  loading,
  onPageChange,
  onTimeChange,
  downloading,
  download,
  onFilterChange,
  powerCompany,
  powerDepartment,
} = useSearch<ReportType, ReportFilter>(reportApi, searchKey, "travel-internal");
const onCreateTimeChange = (dateRange: any) => {
  searchKey.order_create_datetime = onTimeChange(dateRange);
};
const settleCompany = ref([] as Array<ApplyCompanyType>);
const querySettleCompany = async (keyword: string) => {
  //查询结算单位
  const res = await reportApi.querySettleCompany(keyword);
  settleCompany.value = res;
};
const handleSearch = (val: string) => {
  //管理员搜索
  getPowerByApprove(val, 2);
};
const filterOption = (input: string, option: any) => {
  return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const handleBudgetSearch = (val: string) => {
  //管理员搜索
  getPowerByApprove(val, 1);
};
const settleDepartment = ref([]);
const querySettleDepartment = async (keyword: string) => {
  //查询部门
  const data = await reportApi.querySettleDepartment(keyword);
  if (data && data.length > 0) {
    settleDepartment.value = data;
  }
};

// const handleAreaSearch = (val: string) => {
//   //管理员搜索
//   getAreaList({ type: "domesticAirTickets", moduleType: 1, keyword: "" });
// };

// const areaList = ref([]);
// const getAreaList = async (params) => {
//   //查询领域
//   const data = await reportApi.queryAreaList(params);
//   if (data && data.length > 0) {
//     areaList.value = data;
//   }
// };

const budgetTypeList = ref([]);

// 查询领域、平台、产业线
const areaList = ref([]);
const platformList = ref([]);
const industryList = ref([]);

const sjlyInnersShow = ref(false);

// 根据类型查询不同权限类型 permissionType 1: 预算部门 2: 结算单位 3:领域 4:平台 5:产业线
const getPowerByApprove = async (name: string, permissionType: number) => {
  const data = await reportApi.getPowerByApprove(name, permissionType, 1, 'domesticAirTickets');
  switch (permissionType) {
    case 1:
      settleDepartment.value = data;
      break;
    case 2:
      settleCompany.value = data;
      break;
    case 3:
      areaList.value = data;
      break;
    case 4:
      platformList.value = data;
      break;
    case 5:
      industryList.value = data;
      break;
    case 6:
      budgetTypeList.value = data;
      break;
    case 99: {
      if (data && data.length > 1) {
        sjlyInnersShow.value = true;
        searchKey.sjly_inner = '1'
        searchKey.datartParams.defaultFilters = [{
          aggOperator: null,
          column: ["gngj"],
          sqlOperator: "EQ",
          values: [
            {
              value: "1",
              valueType: "STRING",
            },
          ],
        }]
      }
    }
      break;
    default:
      break;
  }
};

const handleAreaSearch = (val: string) => {
  getPowerByApprove(val, 3);
};

const handlePtSearch = (val: string) => {
  getPowerByApprove(val, 4);
};

const handlePlSearch = (val: string) => {
  getPowerByApprove(val, 5);
};

onMounted(() => {
  // getAreaList({ type: "domesticAirTickets", moduleType: 1, keyword: "" });
  getPowerByApprove("", 1);
  getPowerByApprove("", 2);
  getPowerByApprove("", 3);
  getPowerByApprove("", 4);
  getPowerByApprove("", 5);
  getPowerByApprove("", 6);
  getPowerByApprove("", 99);
});
</script>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
