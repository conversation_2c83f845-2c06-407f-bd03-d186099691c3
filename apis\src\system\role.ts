import { IPageResponse, IRoleDeleteRequest, IRoleInfo, IRoleLinkResourceRequest, IRoleListRequest, IRoleSaveUpdateRequest } from '@haierbusiness-front/common-libs'
import { get, post } from '../request'

export const roleApi = {

    /**
     * 获取角色列表
     */
    list: (params: IRoleListRequest): Promise<IPageResponse<IRoleInfo>> => {
        return get<IPageResponse<IRoleInfo>>('system/api/role/list', params)
    },


    /**
     * 新增角色
     */
    save: (params: IRoleSaveUpdateRequest): Promise<IRoleInfo> => {
        return post('system/api/role/save', params)
    },

    /**
     * 删除角色
     */
    delete: (params: IRoleDeleteRequest): Promise<void> => {
        return post('system/api/role/delete', params)
    },

     /**
     * 关联资源
     */
     linkResource: (params: IRoleLinkResourceRequest): Promise<void> => {
        return post('system/api/role/link/resource', params)
    },
}