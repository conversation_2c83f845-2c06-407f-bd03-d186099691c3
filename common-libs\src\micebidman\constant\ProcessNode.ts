// BillFileTypeConstant

type keys =
  | 'DEMAND_SUBMIT'
  | 'DEMAND_RECEIVE'
  | 'DEMAND_PRE_INTERACT'
  | 'DEMAND_CONFIRM'
  | 'DEMAND_APPROVAL'
  | 'DEMAND_PUSH'
  | 'DEMAND_RE_APPROVAL'
  | 'SCHEME_SUBMIT'
  | 'SCHEME_APPROVAL'
  | 'SCHEME_RE_APPROVAL'
  | 'SCHEME_CONFIRM'
  | 'BID_PUSH'
  | 'BIDDING'
  | 'BID_RESULT_CONFIRM'
  | 'COST_APPROVAL'
  | 'MICE_EXECUTION'
  | 'MICE_COMPLETED'
  | 'BILL_CONFIRM'
  | 'BILL_APPROVAL'
  | 'BILL_RE_APPROVAL'
  | 'PAYMENT_CONFIRM'
  | 'PLATFORM_INVOICE_ENTRY'
  | 'VENDOR_INVOICE_ENTRY'
  | 'INVOICE_CONFIRM'
  | 'PLATFORM_REFUND_RECEIPT_UPLOAD'
  | 'PLATFORM_INVOICE_CONFIRM'
  | 'SETTLEMENT_PENDING'
  | 'SETTLEMENT_RECORDED'
  | 'END'
  | 'MICE_PENDING' | 'PLATFORM_PAY_RECEIPT_UPLOAD' | 'REFUND_CONFIRM'

export const ProcessNode = {
  // 需求相关
  DEMAND_SUBMIT: { code: 'DEMAND_SUBMIT', desc: '需求提报', color: '#1868DB', role: 'operatorCode' },
  DEMAND_RECEIVE: { code: 'DEMAND_RECEIVE', desc: '需求接单', color: '#1868DB', role: '会务负责人' },
  DEMAND_PRE_INTERACT: {
    code: 'DEMAND_PRE_INTERACT',
    desc: '需求事先交互',
    color: '#1868DB',
    role: 'consultantUserCode',
  },
  DEMAND_CONFIRM: { code: 'DEMAND_CONFIRM', desc: '需求确认', color: '#1868DB', role: 'operatorCode' },
  DEMAND_APPROVAL: { code: 'DEMAND_APPROVAL', desc: '需求审批', color: '#1868DB', role: '审批人' },
  DEMAND_PUSH: { code: 'DEMAND_PUSH', desc: '需求发布', color: '#1868DB', role: 'consultantUserCode' },
  DEMAND_RE_APPROVAL: { code: 'DEMAND_RE_APPROVAL', desc: '需求发布复核', color: '#1868DB', role: '' },

  // 方案相关
  SCHEME_SUBMIT: { code: 'SCHEME_SUBMIT', desc: '方案提报', color: '#FAAD14', role: '服务商' },
  SCHEME_APPROVAL: { code: 'SCHEME_APPROVAL', desc: '方案审核', color: '#FAAD14', role: 'consultantUserCode' },
  SCHEME_RE_APPROVAL: { code: 'SCHEME_RE_APPROVAL', desc: '方案复审', color: '#FAAD14', role: '审批人' },
  SCHEME_CONFIRM: { code: 'SCHEME_CONFIRM', desc: '方案确认', color: '#FAAD14', role: 'operatorCode' },

  // 竞价相关
  BID_PUSH: { code: 'BID_PUSH', desc: '竞价推送', color: '#4E00CC', role: 'consultantUserCode' },
  BIDDING: { code: 'BIDDING', desc: '竞价中', color: '#4E00CC', role: '服务商' },
  BID_RESULT_CONFIRM: { code: 'BID_RESULT_CONFIRM', desc: '费用支付', color: '#4E00CC', role: 'operatorCode' },

  // 费用和执行
  COST_APPROVAL: { code: 'COST_APPROVAL', desc: '费用审批', color: '#4E00CC', role: '审批人' },
  MICE_PENDING: { code: 'MICE_PENDING', desc: '会议待执行', color: '#FF6A00', role: 'operatorCode' },
  MICE_EXECUTION: { code: 'MICE_EXECUTION', desc: '会议执行中', color: '#FF6A00', role: 'operatorCode' },
  MICE_COMPLETED: {
    code: 'MICE_COMPLETED',
    desc: '会议完成',
    // desc: '账单上传',
    color: '#FF6A00', role: '服务商'
  },

  // 账单相关
  BILL_CONFIRM: { code: 'BILL_CONFIRM', desc: '账单确认', color: '#FF6A00', role: 'operatorCode' },
  BILL_APPROVAL: { code: 'BILL_APPROVAL', desc: '账单审批', color: '#FF6A00', role: '审批人' },
  BILL_RE_APPROVAL: { code: 'BILL_RE_APPROVAL', desc: '账单复审', color: '#FF6A00', role: 'consultantUserCode' },

  // 支付和发票
  PAYMENT_CONFIRM: { code: 'PAYMENT_CONFIRM', desc: '财务收款确认', color: '#FF6A00', role: '财务人员' },
  PLATFORM_INVOICE_ENTRY: {
    code: 'PLATFORM_INVOICE_ENTRY',
    desc: '平台收款发票录入',
    color: '#52C41A',
    role: '财务人员',
  },
  VENDOR_INVOICE_ENTRY: { code: 'VENDOR_INVOICE_ENTRY', desc: '服务商发票录入', color: '#52C41A', role: '服务商' },//上传发票
  INVOICE_CONFIRM: { code: 'INVOICE_CONFIRM', desc: '发票确认', color: '#52C41A', role: 'operatorCode' },
  PLATFORM_REFUND_RECEIPT_UPLOAD: {
    code: 'PLATFORM_REFUND_RECEIPT_UPLOAD',
    desc: '平台上传退款凭证',
    color: '#52C41A',
    role: '财务人员',
  },
  REFUND_CONFIRM: {
    code: 'REFUND_CONFIRM',
    desc: '用户退款确认',
    color: '#52C41A',
    role: '财务人员',
  },
  PLATFORM_PAY_RECEIPT_UPLOAD: {
    code: 'PLATFORM_PAY_RECEIPT_UPLOAD',
    desc: '上传平台支付凭证',
    color: '#52C41A',
    role: '财务人员',
  },
  PLATFORM_INVOICE_CONFIRM: { code: 'PLATFORM_INVOICE_CONFIRM', desc: '平台发票确认', color: '#52C41A', role: '' },

  // 结算相关
  SETTLEMENT_PENDING: {
    code: 'SETTLEMENT_PENDING',
    desc: '结算中（已推送）已推送账单/最终节点',
    color: '#52C41A',
    role: '',
  },
  SETTLEMENT_RECORDED: { code: 'SETTLEMENT_RECORDED', desc: '结算中（已记账）', color: '#52C41A', role: '' },
  END: { code: 'END', desc: '流程结束', role: '' },

  // 保持原有的工具方法
  ofType: (type?: string): { code: string; desc: string } | null => {
    for (const key in ProcessNode) {
      const item = ProcessNode[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: string; desc: string } | undefined)[] => {
    const types = Object.keys(ProcessNode).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return ProcessNode[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
