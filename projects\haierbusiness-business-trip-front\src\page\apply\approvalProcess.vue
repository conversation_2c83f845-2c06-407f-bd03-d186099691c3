<script lang="ts" setup>
import {
  Card as hCard,
  MenuI<PERSON> as hMenuItem,
  <PERSON>u as hMenu,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  Row as hRow,
  TypographyParagraph as hTypographyParagraph,
  Badge as hBadge,
  Modal,
  message,
  TableProps,
  Empty as hEmpty,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem
} from 'ant-design-vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue'
import { computed, ref, watch, onMounted, onActivated, onDeactivated, createVNode } from "vue";
import { processApi } from "@haierbusiness-front/apis";
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from "@haierbusiness-front/utils";
import {
    IProcessIno, IOperatorsInfo, ProcessNotificationMethodConstant
} from '@haierbusiness-front/common-libs';
import { toNumber } from 'lodash';
import G6, { TreeGraph } from "@antv/g6";

const router = getCurrentRouter();
const route = ref(getCurrentRoute());
const definedSubsets = ref<Array<IProcessIno> | undefined>([]);
const currentMenu = ref([0])
const emptyImg = hEmpty.PRESENTED_IMAGE_SIMPLE
interface Props {
  processCode?: string;
}
const props = defineProps<Props>();


const getDetail = async () => {
  const res = await processApi.details({
    code: props.processCode
  });
  definedSubsets.value = [res.processRecord]
  serializeData(definedSubsets.value ?? [])
  currentData.value = currentList.value && currentList.value.length > 0 ? currentList.value[0] : {}
  console.log(currentData.value)
  if(currentData.value.id) {
    tree.value = initg61();
  } else {
    show.value = false
  }
};

const currentData = ref() 
const currentList = ref([] as any)
const tree = ref<TreeGraph>()
const title = ref('')
const method = ref<Array<number>>()

const serializeData = (list: IProcessIno[]) => {
  list.map(item => {
    const newList = item.steps?.map(step => {
      const newData = {
        id: step.psName!,
        label: step.name!,
        newType: step.type!,
        stepOperators: step.operators!,
        pdId: step.pdId!,
        pdsId: step.pdsId!
      }
      return newData
    })

    const todoMethod = computedMethod(item.todoMethod)

    if (newList && newList.length > 0) {
      let current = {
        id: '结束',
        label: '结束',
        newType: 0,
        stepOperators: [] as any,
        pdId: item.pdId,
        pdsId: item.id,
        typename: "system",
        no: newList.length
      }
      for(let i = newList.length; i > 0; i--) {
        const node = {
          ...newList[i-1],
          children: [current] as any,
          typename: "none",
          no: i
        }
        current = node
      }
      let begin = {
        id: '开始',
        label: '开始',
        newType: 0,
        stepOperators: [] as any,
        pdId: item.pdId,
        pdsId: item.id,
        children: [current],
        typename: "system",
        no: 0,
        additionable: item.additionable,
        revocable: item.revocable,
        assist: item.assist,
        nodeCall: item.nodeCall,
        voteCall: item.voteCall,
        enterpriseName: item.enterpriseName,
        scope: item.scope,
        nickName: item.nickName,
        departmentName: item.departmentName,
        todoMethod
      }
      currentList.value.push(begin)
    }
  })
}

const computedMethod = (num: number | undefined)=> {
    if(!num) {
        return
    }

    const list = ProcessNotificationMethodConstant.toNumberArray()
    console.log(list)
    // method
    const array: number[] = []
    list.map(item => {
        if((num & item!) != 0) {
            array.push(item!)
        }
    })
    return array
}

onMounted(() => {
  getDetail();
});

onActivated(() => {
  route.value = getCurrentRoute();
  title.value = route.value?.query?.name
  getDetail();
})

onDeactivated(() => {
  currentList.value = []
  tree.value?.clear();
  tree.value?.destroy();
  show.value = true
  currentMenu.value = [0]
})

const initg61 = () => {
  const data = currentData.value;
  //       insertCss(`
  //   #contextMenu {
  //     position: absolute;
  //     list-style-type: none;
  //     padding: 10px 8px;
  //     left: -150px;
  //     background-color: rgba(255, 255, 255, 0.9);
  //     border: 1px solid #e2e2e2;
  //     border-radius: 4px;
  //     font-size: 12px;
  //     color: #545454;
  //   }
  //   #contextMenu li {
  //     cursor: pointer;
  // 		list-style-type:none;
  //     list-style: none;
  //     margin-left: 0px;
  //     text-align:left
  //   }
  //   #contextMenu li:hover {
  //     color: #aaa;
  //   }
  // `);
//   const contextMenu = new G6.Menu({
//     getContent(evt) {
//       // let header;
//       // if (evt.target && evt.target.isCanvas && evt.target.isCanvas()) {
//       //   header = 'Canvas ContextMenu';
//       // } else if (evt.item) {
//       //   const itemType = evt.item.getType();
//       //   header = `${itemType.toUpperCase()} ContextMenu`;
//       // }
//       const { _cfg } = evt.item;
//       return `
// <ul>
//   <li style="display:${
//     _cfg.model.typename === "system" ? "none" : ""
//   }" id="contextMenu_addChildNode" class="contextMenu_normallabel" >新增子节点 </li>

//   <li id="contextMenu_addBroNode" class="contextMenu_normallabel" >新增同级节点 </li>
//   <li id="contextMenu_editNode" class="contextMenu_normallabel" > 编辑 </li>
//   <li id="contextMenu_detailNode" class="contextMenu_normallabel" > 详情 </li>
//   <li id="contextMenu_syncNode" class="contextMenu_normallabel" > 同步配置 </li>
//   <li id="contextMenu_enableNode" class="contextMenu_normallabel"> 启用 </li>
//   <li id="contextMenu_unenabledNode" class="contextMenu_normallabel" > 禁用 </li>
//   <li id="contextMenu_deleteNode" class="contextMenu_deletelabel"> 删除 </li>
// </ul>
// `;
//     },
//     handleMenuClick: (target, item) => {},
//     // offsetX and offsetY include the padding of the parent container
//     // 需要加上父级容器的 padding-left 16 与自身偏移量 10
//     offsetX: 16 + 10,
//     // 需要加上父级容器的 padding-top 24 、画布兄弟元素高度、与自身偏移量 10
//     offsetY: 0,
//     // the types of items that allow the menu show up
//     // 在哪些类型的元素上响应
//     itemTypes: ["node"],
//     // itemTypes: ['node', 'edge', 'canvas'],
//   });

  const tooltip = new G6.Tooltip({
    offsetX: 10,
    offsetY: 10,
    // v4.2.1 起支持配置 trigger，click 代表点击后出现 tooltip。默认为 mouseenter
    trigger: 'click',
    // the types of items that allow the tooltip show up
    // 允许出现 tooltip 的 item 类型
    itemTypes: ['node'],
    shouldBegin: (e) => {
      if (e.item.getModel().id === '开始' || e.item.getModel().id === '结束') return false;
      return true;
    },
    // custom the tooltip's content
    // 自定义 tooltip 内容
    getContent: (e) => {
      const outDiv = document.createElement('div');
      outDiv.style.width = 'fit-content';
      const a = e?.item
      const b = e?.item?.getModel()
      //outDiv.style.padding = '0px 0px 20px 0px';
      // outDiv.innerHTML = `
      //   <h3>节点信息</h3>
      //   <ul>
      //     <li>流转条件: ${e.item.getType()}</li>
      //   </ul>
      //   <ul>
      //     <li>Label: ${e.item.getModel().label || e.item.getModel().id}</li>
      //   </ul>`;
      // newType
      let name = ''
      if(e?.item?.getModel().stepOperators) {
        (e.item.getModel().stepOperators as Array<IOperatorsInfo>).forEach(item => {
          if(name) {
            name += '、' 
          }
          name += item.approverName ? item.approverName : item.roleName
        }) 
      }
      outDiv.innerHTML = `
      <div style="min-width: 300px;">
        <div style="font-weight:bold; font-size: 16px;">节点信息</div>
          <div style="margin-top: 5px;display: flex;flex-direction: row;">
            <div style="font-weight:bold;display: flex;width: 95px;">流转条件：</div>
            <div style="display: flex;">${e?.item?.getModel().newType === 1 ? '全部通过则通过,一人驳回则驳回' : '一人通过则通过, 一人驳回则驳回' }</div>
          </div>
          <div style="margin-top: 5px;display: flex;flex-direction: row;">
            <div style="font-weight:bold;display: flex;width: 95px;">执行角色/人：</div>
            <div style="display: flex;">${name}</div>
          </div>
      </div>`
        
      return outDiv;
    },
  });

  G6.registerNode(
    "card-node",
    {
      draw: function drawShape(cfg, group) {    
        const r = 2;
        const color = "#0073E5";
        const w = 200;
        const h = cfg.size[1];
        if (cfg.id ==='开始' || cfg.id === '结束') {
          const shape = group.addShape("rect", {
            attrs: {
              x: -w / 2,
              y: -h / 2,
              width: w, //200,
              height: h, // 60
              stroke: color,
              radius: h / 2,
              fill: "#fff",
            },
            name: "main-box",
            // draggable: true,
            zIndex: 1,
          });
          
          group.addShape("text", {
            attrs: {
              textBaseline: "middle",
              // x: -w / 2 + 40,
              // y: -h / 2 + 36,
              text: cfg.id,
              fill: "#8C97B2",
              textAlign: "center",
              color: "#8C97B2",
            },
            name: `description`,
          });

          cfg.children &&
            group.addShape("marker", {
              attrs: {
                x: 0,
                y: h / 2,
                r: 6,
                cursor: "pointer",
                //也可以使用path  https://g6.antv.vision/zh/docs/manual/middle/elements/shape/shape-and-properties/#%E6%A0%87%E8%AE%B0%E5%9B%BE%E5%BD%A2-marker
                symbol: cfg.collapsed ? G6.Marker.expand : G6.Marker.collapse,
                stroke: "#0073E5",
                lineWidth: 1,
                size: 2 * h,
                fill: "#fff",
              },
              name: "collapse-icon",
            });

          return shape;

        } else {
          const shape = group.addShape("rect", {
            attrs: {
              x: -w / 2,
              y: -h / 2,
              width: w, //200,
              height: h, // 60
              stroke: color,
              radius: r,
              fill: "#fff",
            },
            name: "main-box",
            // draggable: true,
            zIndex: 1,
          });

          group.addShape("rect", {
            attrs: {
              x: -w / 2,
              y: -h / 2,
              width: w, //200,
              height: h / 2, // 60
              fill: "#0073E5",
              radius: [r, r, 0, 0],
            },
            name: "title-box",
            // draggable: true,
          });

          // title text
          group.addShape("text", {
            attrs: {
              textBaseline: "top",
              // x: -w / 2 + 8,
              y: -h / 2 + 12,
              text: cfg.id ==='开始' || cfg.id === '结束' ?'' : '步骤' + cfg.no,
              fill: "#fff",
              textAlign: "center",
            },
            name: "title",
          });
          
          group.addShape("text", {
            attrs: {
              textBaseline: "hanging",
              // x: -w / 2 + 40,
              y: -h / 2 + 36,
              // text: cfg?.stepOperators[0]?.approverName,
              // text: JSON.stringify(cfg.stepOperators),
              text: `${cfg.id}: ${cfg?.stepOperators[0]?.approverName}(${cfg?.stepOperators[0]?.approverCode})`,

              fill: "#8C97B2",
              textAlign: "center",
              color: "#8C97B2",
            },
            name: `description`,
          });

          cfg.children &&
            group.addShape("marker", {
              attrs: {
                x: 0,
                y: h / 2,
                r: 6,
                cursor: "pointer",
                //也可以使用path  https://g6.antv.vision/zh/docs/manual/middle/elements/shape/shape-and-properties/#%E6%A0%87%E8%AE%B0%E5%9B%BE%E5%BD%A2-marker
                symbol: cfg.collapsed ? G6.Marker.expand : G6.Marker.collapse,
                stroke: "#0073E5",
                lineWidth: 1,
                size: 2 * h,
                fill: "#fff",
              },
              name: "collapse-icon",
            });

          return shape;
        }
        
      },
      update: (cfg, item) => {
        const group = item.getContainer();
        const icon = group.find((e) => e.get("name") === "collapse-icon");
        icon.attr("symbol", cfg.collapsed ? G6.Marker.expand : G6.Marker.collapse);
      },
      // update: undefined,
      //此方式相当于重写hover select 等状态事件
      // setState(name, value, item) {
      //   if (name === 'collapsed') {
      //     const marker = item.get('group').find((ele) => ele.get('name') === 'collapse-icon');
      //     const icon = value ? G6.Marker.expand : G6.Marker.collapse;
      //     marker.attr('symbol', icon);
      //   }
      // },
    },
    "rect"
  );

  G6.registerEdge("flow-line", {
    draw(cfg, group) {
      const startPoint = cfg.startPoint;
      const endPoint = cfg.endPoint;

      const { style } = cfg;
      const shape = group.addShape("path", {
        attrs: {
          stroke: style.stroke,
          endArrow: style.endArrow,
          path: [
            ["M", startPoint.x, startPoint.y],
            ["L", startPoint.x, (startPoint.y + endPoint.y) / 2],
            ["L", endPoint.x, (startPoint.y + endPoint.y) / 2],
            ["L", endPoint.x, endPoint.y],
          ],
        },
      });

      return shape;
    },
  });

  const defaultStateStyles = {
    hover: {
      stroke: "#8C97B2",
      lineWidth: 2,
      lineDash: [4, 3],
    },
    selected: {
      stroke: "#8C97B2",
      lineWidth: 0,
      shadowColor: "#0073E5",
      lineDash: [8, 3],
      shadowBlur: 10,
    },
  };

  const defaultNodeStyle = {
    fill: "#0073E5",
    stroke: "#0073E5",
    radius: 2,
  };

  const defaultEdgeStyle = {
    stroke: "#0073E5",
    endArrow: {
      path: 'M 0,0 L 10,5 L 10,-5 Z',
      fill: "#0073E5",
      d: -30,
    },
  };

  const defaultLayout = {
    type: "compactBox",
    direction: 'TB',
    //  type: 'mindmap',
    // direction: "V",
    getId: function getId(d) {
      return d.id;
    },
    getHeight: function getHeight() {
      return 16;
    },
    getWidth: function getWidth() {
      return 16;
    },
    getVGap: function getVGap() {
      return 40;
    },
    getHGap: function getHGap() {
      return 70;
    },
  };

  const defaultLabelCfg = {
    style: {
      fill: "#fff",
      fontSize: 12,
      textAlign: "center",
    },
  };
  const container = document.getElementById("container");
  const width = container.scrollWidth;
  const height = container.scrollHeight || 500;

  // const minimap = new G6.Minimap({
  //   size: [150, 100]
  // })
  G6.registerBehavior("activate-node", {
    getDefaultCfg() {
      return {
        multiple: true,
      };
    },
    getEvents() {
      return {
        "collapse-expand": "onNodeClick",
      };
    },
    onNodeClick(e) {
      const data = e.item.get("model");
      let flag = !(data.collapsed ? true : false);
      console.log(data);
      graph.updateItem(e.item, {
        flag,
      });
      data.collapsed = true;
      return true;
    },
  });
  const graph = new G6.TreeGraph({
    container: "container",
    width,
    height,
    linkCenter: true,
    // fitView: true,
    // layout: {
    //   direction: 'H',
    // },
    // back
    // plugins: [minimap, contextMenu],
    plugins: [tooltip],
    modes: {
      default: [
        {
          type: "drag-node",
          // enableDebounce:true,
          // enableDelegate: true,   //拖动节点过程中是否启用 delegate，即在拖动过程中是否使用方框代替元素的直接移动，效果区别见下面两个动图。默认值为  false
          // onlyChangeComboSize: true,
          shouldBegin: (e) => {
            // e.item._cfg.group.zIndex = e.item._cfg.group.zIndex += 1;
            // // 不允许拖拽 id 为 'node1' 的节点
            // if (e.item && e.item.getModel().id === "root") {
            //   return false;
            // } else {
            //   return true;
            // }
            return false
          },
        },
        //当节点被点击时也会触发折叠与展开
        // {
        //   type: 'collapse-expand',

        //   onChange: function onChange(event, collapsed) {
        //     // console.log(event)
        //     // if(event._cfg.children.length===0) return
        //     // const data = event.get('model');
        //     // graph.updateItem(event, {
        //     //   collapsed,
        //     // });
        //     // data.collapsed = collapsed;
        //     return false;
        //   },
        // },
        "drag-canvas",
        "zoom-canvas",
        "drag-group",
      ],
      // 'activate-relations'当鼠标移到某节点时，突出显示该节点以及与其直接关联的节点和连线；
    },
    defaultNode: {
      type: "card-node",
      size: [140, 60],
      style: defaultNodeStyle,
      color: "#fff",
      labelCfg: defaultLabelCfg,
    },
    defaultEdge: {
      type: "flow-line",
      style: defaultEdgeStyle,
    },
    nodeStateStyles: defaultStateStyles,
    edgeStateStyles: defaultStateStyles,
    layout: defaultLayout,
  });
  //监听节点展开折叠事件
  //       graph.on('itemcollapsed', (e) => {
  //   // 当前被操作的节点 item
  //   console.log(e.item);
  //   // 当前操作是收起（`true`）还是展开（`false`）
  //   console.log(e.collapsed);
  // });

  graph.data(data);
  graph.render();
  graph.fitView(0, { onlyOutOfViewPort: true, direction: 'y' });
  

  graph.on("node:mouseenter", (evt) => {
    const { item } = evt;
    graph.setItemState(item, "hover", true);
  });
  // graph.on("dragnodeend", (evt) => {
  //   graph.getNodes().forEach((node) => {
  //     if (node === evt.items[0]) {
  //       node.toFront();
  //       // node.getKeyShape().toFront()
  //     }
  //   });
  //   // evt.item[0].toFront();
  //   if (evt.targetItem) {
  //     const { model } = evt.targetItem._cfg;
  //     // evt.targetItem._cfg.group.cfg.zIndex = 9
  //   }
  // });
  let dragData = {};
  function togetNodeItem2(val, id) {
    val.forEach((ele) => {
      if (ele.id === id) {
        dragData = ele;
      }
      if (ele.children && ele.children.length) {
        togetNodeItem2(ele.children, id);
      }
    });
  }
  graph.on("node:mouseleave", (evt) => {
    const { item } = evt;
    graph.setItemState(item, "hover", false);
  });
  // graph.on("node:drag", (evt) => {
  //   const { item } = evt;
  //   let { x, y } = item._cfg.model;
  //   togetNodeItem2(data.children, item._cfg.model.id);
  //   if (dragData.children && dragData.children.length) {
  //     dragData.children.forEach((ele) => {
  //       let cfg = {
  //         x: x - ele.x,
  //         y: y - 80,
  //       };
  //       console.log(cfg);
  //       let childnode = graph.findById(ele.id);
  //       childnode.updatePosition(cfg);
  //     });
  //   }
  // });
  graph.on("node:click", (evt) => {
    console.log(evt);
    // graph.setItemState(edge, "highlight.light", false);
    // evt.item.setState('selected', true)
    //  console.log(evt.item.hasState('selected'))
    const { item, target } = evt;
    //展开与折叠子节点
    if (evt.target.get("name") === "collapse-icon") {
      // evt.item.getModel().collapsed = !evt.item.getModel().collapsed;
      // graph.setItemState(evt.item, 'collapsed', evt.item.getModel().collapsed);
      graph.updateItem(item, {
        collapsed: !item.getModel().collapsed,
      });
      graph.layout();
    }
    graph.setItemState(item, "selected", true);
    let nodes = graph.getNodes();
    let nodelen = nodes.length;
    if (nodelen) {
      for (let i = 0; i < nodelen; i++) {
        if (nodes[i] !== item) {
          graph.setItemState(nodes[i], "selected", false);
        }
      }
    }
    //  graph.setItemState(item, "highlight.light", true);
    // 增加元素
    // const targetType = target.get('type')
    // const name = target.get('name')
    // if (targetType === 'marker') {
    //   const model = item.getModel()
    //   if (name === 'add-item') {
    //     if (!model.children) {
    //       model.children = []
    //     }
    //     const id = `n-${Math.random()}`
    //     model.children.push({
    //       id,
    //       label: id.substr(0, 8),
    //       leftIcon: {
    //         style: {
    //           fill: '#e6fffb',
    //           stroke: '#e6fffb'
    //         },
    //         img:
    //           'https://gw.alipayobjects.com/mdn/rms_f8c6a0/afts/img/A*Q_FQT6nwEC8AAAAAAAAAAABkARQnAQ'
    //       }
    //     })
    //     graph.updateChild(model, model.id)
    //   } else if (name === 'remove-item') {
    //     graph.removeChild(model.id)
    //   }
    // }
  });
  const nodeContainerGroup = graph.get("nodeGroup"); // 获得存储节点图形分组的组
  const nodeGroups = nodeContainerGroup.get("children"); // 获得所有节点的图形分组
  for (let i = 0; i < nodeGroups.length; i++) {
    nodeGroups[i].set("zIndex", 10); // 把第 0 个节点的图形分组 zIndex 设置为 10
  }
  // nodeContainerGroup.sort() // 排序
  if (typeof window !== "undefined") {
    window.onresize = () => {
      if (!graph || graph.get("destroyed")) return;
      if (!container || !container.scrollWidth || !container.scrollHeight) return;
      graph.changeSize(container.scrollWidth, container.scrollHeight);
    };
  }
  return graph
};

const show = ref(true)

const handleSub = (pdsId: number | undefined) => {
  if(pdsId) {
    currentData.value = currentList.value.find((o: any) => o.pdsId === pdsId) ?? {}
    tree.value?.destroy()
    tree.value = initg61()
    // tree.value?.changeData(currentData.value, false)
  }
}

const goEdit = (pdId: number | undefined, pdsId: number | undefined) => {
  if(pdsId) {
    const subProcess = definedSubsets.value?.find(o => o.id === pdsId)
    router.push({ path: "/processman/process/editsubprocess", query: { record: routerParam(subProcess), pdId, name: title.value } })
  }
  else
    router.push({ path: "/processman/process/editsubprocess", query: { pdId: toNumber(route.value.query.pdId), name: title.value } })
}

const handleDelete = (pdsId: number) => {
  Modal.confirm({
      title: '确认要删除吗？',
      icon: createVNode(ExclamationCircleFilled),
      onOk: () => {
        processApi.removeSub(pdsId).then(() => {
            message.success('删除成功！')
            currentList.value = []
            tree.value?.destroy()
            getDetail()
        })
      },
      onCancel: async () => {

      },
  })
}

</script>

<template>
  <h-row style="height: 500px; width: 100%">
    
    <h-col :span="24" style="height: 500px">
      <h-row style="height: 100%; width: 100%; overflow: auto; display: flex; flex-direction: column;">
       
        <div
          style="
            width: 100%;
            padding: 5px 5px 10px 20px;
            font-size: 18px;
            /* font-weight: 600; */
            border-bottom: 1px solid #f0f0f0;
            background-color: #ffff;
            height: calc(100% );
            display: flex;
            position: relative;
          "
        >
          <template v-if="show">
            <div id="container" />
            <!-- <h-card hoverable style="position: absolute;  background-color: #ffff; top: 50px; left: 100px; width: 300px;">
              <h-descriptions title="企业/作用域" :column="4">
                <h-descriptions-item label="企业" :span="4">
                    {{ currentData?.enterpriseName }}
                  </h-descriptions-item>
                  <h-descriptions-item label="作用域" :span="4">
                    {{ currentData?.scope === 1 ? '企业级' : currentData?.scope === 2 ? '部门级' : '个人' }}
                  </h-descriptions-item>
                  <h-descriptions-item label="部门" :span="4" v-if="currentData?.scope === 2">
                    {{ currentData?.departmentName }}
                  </h-descriptions-item>
                  <h-descriptions-item label="用户" :span="4" v-if="currentData?.scope === 3">
                    {{ currentData?.nickName }}
                  </h-descriptions-item>
              </h-descriptions>
              <h-descriptions title="待办通知方式" :column="3">
                <h-descriptions-item label="ihaier" :span="1">
                    <h-badge v-if="currentData?.todoMethod && currentData.todoMethod.indexOf(1) > -1" status="success" />
                    <h-badge v-else status="error" />
                  </h-descriptions-item>
                  <h-descriptions-item label="邮件" :span="1">
                    <h-badge v-if="currentData?.todoMethod && currentData.todoMethod.indexOf(2) > -1" status="success" />
                    <h-badge v-else status="error" />
                  </h-descriptions-item>
                  <h-descriptions-item label="短信" :span="1">
                    <h-badge v-if="currentData?.todoMethod && currentData.todoMethod.indexOf(4) > -1" status="success" />
                    <h-badge v-else status="error" />
                  </h-descriptions-item>
              </h-descriptions>
              <h-descriptions title="回调方式" :column="4">
                  <h-descriptions-item label="节点回调" :span="2">
                    <h-badge v-if="currentData?.nodeCall" status="success" />
                    <h-badge v-else status="error" />
                  </h-descriptions-item>
                  <h-descriptions-item label="投票回调" :span="2">
                    <h-badge v-if="currentData?.voteCall" status="success" />
                    <h-badge v-else status="error" />
                  </h-descriptions-item>
              </h-descriptions>
              <h-descriptions title="配置" :column="4">
                <h-descriptions-item label="是否可撤回" :span="2">
                    <h-badge v-if="currentData?.revocable" status="success" />
                    <h-badge v-else status="error" />
                  </h-descriptions-item>
                  <h-descriptions-item label="是否可代审" :span="2">
                    <h-badge v-if="currentData?.assist" status="success" />
                    <h-badge v-else status="error" />
                  </h-descriptions-item>
                  <h-descriptions-item label="是否可加审" :span="2">
                    <h-badge v-if="currentData?.additionable" status="success" />
                    <h-badge v-else status="error" />
                  </h-descriptions-item>
                  <h-descriptions-item label="电子签名" :span="2">
                    <h-badge v-if="currentData?.nodeCall" status="success" />
                    <h-badge v-else status="error" />
                  </h-descriptions-item>
              </h-descriptions>
              
            </h-card> -->
          </template>
          <template v-else>
            <div style="width: 100%; height: 100%;display: flex; align-items: center; justify-content: center;">
              <h-empty :image="emptyImg" />
            </div>
          </template>
          
        </div>
      </h-row>
    </h-col>
  </h-row>
</template>

<style lang="less" scoped>
#container {
  width: 100%;
  height: 100%;
  border: 1px solid #ebecef;
  /* background: linear-gradient(to right,#ccc 1px,transparent 1px),
     linear-gradient(to bottom,#ccc 1px,transparent 1px);
     background-repeat: repeat;
     background-size: 10px 10px; */
  background: #fff;
  background-image: linear-gradient(#fff 14px, transparent 0),
    linear-gradient(90deg, #ebecef 2px, transparent 0);
  background-size: 16px 16px, 16px 16px;
}
.g6-component-contextmenu {
  padding: 0;
  background-color: rgba(255, 255, 255, 1);
}
ul {
  list-style: none;
  padding: 0 10px;
  margin: 0;
}
li {
  height: 30px;
  line-height: 30px;
  text-align: left;
  cursor: pointer;
}
.contextMenu_normallabel {
  color: #21252e;
}
.contextMenu_normallabel:hover {
  color: #0073E5;
}
.contextMenu_deletelabel {
  color: #e60000;
  border-top: 1px solid #ebecef;
}

.card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;

  .cell {
    display: flex;
    width: 50%;
    padding-bottom: 10px;
  }
}
</style>
