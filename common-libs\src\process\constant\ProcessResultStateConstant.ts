type keys = 'PASS' | 'REVOKE' | 'REFUSE' | 'OTHER' | 'CLOSED';

export const ProcessResultStateConstant = {
    PASS: { "type": 1, "name": "通过" },
    REVOKE: { "type": 2, "name": "撤销" },
    REFUSE: { "type": 3, "name": "驳回/拒绝" },
    OTHER: { "type": 4, "name": "其它" },
    CLOSED: { "type": 7, "name": "已关闭" },

    ofType: (type?: number): { "type": number, "name": string } | null => {
        for (const key in ProcessResultStateConstant) {
          const item = ProcessResultStateConstant[key as keys];
          if (type === item.type) {
            return item;
          }
        }
        return null;
      },
  
      toArray:() :({ type: number, name: string } | undefined)[] => {
          const types = Object.keys(ProcessResultStateConstant).map((i: string) => {
            if(i !== 'ofType' && i !== 'toArray' ) {
              return ProcessResultStateConstant[i as keys]
            }
            return
          })
          const newTypes = types.filter(function (s) {
            return s && s; 
          })
          return newTypes
        }
}