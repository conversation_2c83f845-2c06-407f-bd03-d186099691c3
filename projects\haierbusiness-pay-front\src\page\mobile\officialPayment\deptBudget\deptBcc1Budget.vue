<script setup lang="ts">
import {
  showF<PERSON>Toast,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
  Form as <PERSON><PERSON><PERSON>,
  Field as <PERSON><PERSON><PERSON>,
  CellGroup as VanCellGroup,
  showDialog,
  showSuccessToast,
  NoticeBar,
  Popup as VanPopup,
  showToast
} from "vant";
import "vant/es/notice-bar/style";
import {
  payApi,
  compositionPay<PERSON>pi,
  virtualPayApi,
  budgetHaierPayApi,
  budgetHaierPayBccSupportApi
} from "@haierbusiness-front/apis";
import {
  HaierBudgetSourceConstant,
  IHaierAccountCompany,
  IHaierBudgetDepartment,
  IHaierBudgetDeptBccAccountsRequest,
  IHaierBudgetDeptBccBudgetItemRequest,
  IHaierBudgetDeptBccBudgetItemResponse,
  IHaierBudgetDeptBccDcProjectRequest,
  IHaierBudgetDeptBccDepartmentsRequest,
  IHaierBudgetDeptBccProjectRequest,
  IHaierBudgetDeptBccWBSRequest,
  IHaierDcProjectItem,
  IHaierProject,
  IHaierWbs,
  PaySourceConstant
} from '@haierbusiness-front/common-libs';
import { computed, PropType, ref } from "vue";
import { removeStorageItem,ITraveler,isMobile } from "@haierbusiness-front/utils";
import deptSelectM from "@/components/deptSelectM.vue";
import moreTypeSelectM from "@/components/moreTypeSelectM.vue";
import { DataType, usePagination, useRequest } from 'vue-request';

interface Props {
  applicationCode: IPayData;
  budgetType:string,
  param:any
}

const props = withDefaults(defineProps<Props>(), {});

// 表单的值 
const budgetDepartmentCode = ref();
const budgetDepartmentName = ref();

const accountCompanyCode = ref();
const accountCompanyName = ref();



const itemCode = ref();
const itemName = ref();
// 非必填
const projectCode = ref();
const projectName = ref();

const wbsCode = ref();
const wbsName = ref();


const dcProjectCode = ref();
const dcProjectName = ref();

const dcItemCode = ref();
const dcItemName = ref();

const saleType = ref() //预算类型

const leftAmt = ref();

const userLoading = ref(false);
const departmentsSelectedRowRecord = ref<any>()
const accountsSelectedRowRecord = ref<any>()


const emit = defineEmits(["setIsPayComplete","payComplete","isPayLoading"]);

const {
  data: payData,
  run: payRun,
  loading: payLoading,
} = useRequest(
    budgetHaierPayApi.occupyBudget, {
      onSuccess: () => {
        payComplete()
        emit('isPayLoading',false)
      },
      onError:()=>{
        emit('isPayLoading',false)
      }
    }
);

// 费用科目
const {
  data: searchFeeItemsData,
  run: searchFeeItemsRun,
  loading: searchFeeItemsLoading,
} = useRequest(
    budgetHaierPayApi.searchFeeItems, {
      manual: false,
      defaultParams: [
        {
          applicationCode: props?.applicationCode,
          budgetSysCode: HaierBudgetSourceConstant.GEMS.code,
          businessType:props.param?.businessType,
        }
      ]
    }
);
// 费用科目选项
const feeItemOptions = computed(() => {
  if (searchFeeItemsData.value) {
    return searchFeeItemsData.value.map(it => {
      return {"value": it.itemCode, "text": it.itemName}
    })
  } else {
    return []
  }
});

// 费用科目弹窗
const feeItem = ref<any>();
const feeItemName = ref<any>('');
const showPicker = ref<boolean>(false);

// 选择费用科目
const onConfirm = ({ selectedOptions }) => {
    feeItemName.value = selectedOptions[0]?.text
    feeItem.value = selectedOptions[0]?.value
    showPicker.value = false;
    // 如果已经选择了预算人 执行搜索
    // if(budgeterCode.value){
    //   onSearch()
    // }
};
// 选择预算部门
const selectDept = (item:ITraveler) => {
  departmentsSelectedRowRecord.value = item
  budgetDepartmentCode.value = item.code
  budgetDepartmentName.value = item.name
};

// 选择预算立项 
const selectBudgetItems = (item:ITraveler) => {
  itemCode.value = item.itemCode
  itemName.value = item.itemName
};

// 选择研发项目
const selectProjectsData = (item:ITraveler) => {
  console.log(item)
  projectCode.value = item.code
  projectName.value = item.name
};

// 选择wbs
const selectWbs  = (item:ITraveler) => {
  console.log(item)
  wbsCode.value = item.code
  wbsName.value = item.name
};

// 选择地产项目
const selectDcProjectsData  = (item:ITraveler) => {
  console.log(item)
  dcProjectCode.value = item.code
  dcProjectName.value = item.name
  searchDcItemsRun({
      pageNum: 1,
      pageSize: 10,
      projectCode: dcProjectCode.value
    });
};

const selectDcItem = (item:ITraveler) => {
  dcItemCode.value = item.code
  dcItemName.value = item.name
};


// 研发项目 
const {
  data: searchProjectsData,
  run: searchProjectsRun,
  loading: searchProjectsLoading,
  current: searchProjectsCurrent,
  pageSize: searchProjectsPageSize,
  totalPage: searchProjectsTotalPage,
} = usePagination(
    budgetHaierPayBccSupportApi.searchProjects
);
const searchProjectsParams = ref<IHaierBudgetDeptBccProjectRequest>({})

// WBS编码
const {
  data: searchWbsData,
  run: searchWbsRun,
  loading: searchWbsLoading,
  current: searchWbsCurrent,
  pageSize: searchWbsPageSize,
  totalPage: searchWbsTotalPage,
} = usePagination(
    budgetHaierPayBccSupportApi.searchWbs
);
const searchWbsParams = ref<IHaierBudgetDeptBccWBSRequest>({})

// 地产项目
const {
  data: searchDcProjectsData,
  run: searchDcProjectsRun,
  loading: searchDcProjectsLoading,
  current: searchDcProjectsCurrent,
  pageSize: searchDcProjectsPageSize,
  totalPage: searchDcProjectsTotalPage,
} = usePagination(
    budgetHaierPayBccSupportApi.searchDcProjects
);
const searchDcProjectsParams = ref<IHaierBudgetDeptBccDcProjectRequest>({})

// 地产分期
const {
  data: searchDcItemsData,
  run: searchDcItemsRun,
  loading: searchDcItemsLoading,
  current: searchDcItemsCurrent,
  pageSize: searchDcItemsPageSize,
  totalPage: searchDcItemsTotalPage,
} = usePagination(
    budgetHaierPayBccSupportApi.searchDcItems
);

// 选择结算单位
const selectAccountCompanyName = (item:ITraveler) => {
  accountsSelectedRowRecord.value = item
  accountCompanyCode.value = item.code
  accountCompanyName.value = item.name
  // 查询 关联内容 
    searchProjectsRun({
      pageNum: 1,
      pageSize: 10,
      ...searchProjectsParams.value,
      companyCode: accountCompanyCode.value
    });
    searchWbsRun({
      pageNum: 1,
      pageSize: 10,
      ...searchWbsParams.value,
      companyCode: accountCompanyCode.value
    });

    searchDcProjectsRun({
      pageNum: 1,
      pageSize: 10,
      ...searchDcProjectsParams.value,
      accountCode: accountCompanyCode.value
    });
};

const payComplete = () => {
  emit('payComplete', true);
};

// 点击支付
const pay = () => {
  // 必填项
  if (!budgetDepartmentCode.value) {
    showToast("请先选择预算部门!")
    return;
  }
  if (!accountCompanyCode.value) {
    showToast("请先选择结算单位!")
    return;
  }
  if (!feeItem.value) {
    showToast("请先选择费用科目!")
    return;
  }
  if (!itemCode.value) {
    showToast("请先选择预算立项!")
    return;
  }

  // 特殊情况必填项
  if (departmentsSelectedRowRecord.value?.funcCode === 'E' && accountsSelectedRowRecord.value?.funcCode === 'E' && !projectCode.value) {
    showToast("请先选择研发项目!")
    return;
  }

  if (accountCompanyCode.value == 3200 && !wbsCode.value) {
    showToast("请先选择WBS编码!")
    return;
  }
  // 地产项目以及地产分期不在前台做校验,预算占用时后台校验
  emit('isPayLoading',true)
  // 调用占用预算
  payRun(
      {
        haierBudgetType: props.budgetType,
        budgetDepartmentCode: budgetDepartmentCode.value,
        budgetDepartmentName: budgetDepartmentName.value,
        accountCompanyCode: accountCompanyCode.value,
        accountCompanyName: accountCompanyName.value,
        feeItem: feeItem.value,
        feeItemName: feeItemName.value,
        itemCode: itemCode.value,
        itemName: itemName.value,
        // 非必填
        projectCode: projectCode.value ? projectCode.value : null,
        projectName: projectName.value,
        wbsCode: wbsCode.value ? wbsCode.value : null,
        wbsName: wbsName.value,
        dcProjectCode: dcProjectCode.value ? dcProjectCode.value : null,
        dcProjectName: dcProjectName.value,
        dcItemCode: dcItemCode.value ? dcItemCode.value : null,
        dcItemName: dcItemName.value,
        saleType: saleType.value,

        // - 通用参数
        orderCode: props.param?.orderCode,
        applicationCode: props.param?.applicationCode,
        businessType:props.param?.businessType,
        payTypes: props.param?.payTypes,
        username: props.param?.username,
        providerCode: props.param?.providerCode,
        amount: Number(props.param?.amount),
        orderDetailsUrl: props.param?.orderDetailsUrl,
        notifyUrl: props.param?.notifyUrl,
        callbackUrl: props.param?.callbackUrl,
        description: props.param?.description,
        payload: props.param?.payload,
        paySource: isMobile() ? PaySourceConstant.MOBILE.type : PaySourceConstant.PC.type,
        paymentMethod: 2
      },
      {
        applicationCode: props.param?.applicationCode,
        excludes: "paySource,haierBudgetType,feeItem,feeItemName,budgeterCode,budgeterName,budgetDepartmentCode,budgetDepartmentName,accountCompanyCode,accountCompanyName,itemCode,itemName,projectCode,projectName,wbsCode,wbsName,dcProjectCode,dcProjectName,dcItemCode,dcItemName,saleType,paymentMethod",
        nonce: props.param?.hbNonce,
        timestamp: props.param?.hbTimestamp,
        sign: props.param?.sign,
      }
  )
};

defineExpose({pay})

</script>

<template>
  <div class="contentBox">
    <van-form>
      <moreTypeSelectM label="预算部门" :required="true" :api="budgetHaierPayBccSupportApi.searchDepartments" :isCanEmpty="true" :palceholdeTip="'请输入部门名称或者编码'" :keyWord="'name'" :selectKey="'name'" :selectValue="'code'"  :value="budgetDepartmentName" @chose="selectDept" />
      <moreTypeSelectM label="结算单位" :required="true" :api="budgetHaierPayBccSupportApi.searchAccounts" :palceholdeTip="'请输入结算单位名称或者编码'" :keyWord="'accountName'" :selectKey="'name'"  :selectValue="'code'" :type="'deptMust'" :bdCode="budgetDepartmentCode"  :value="accountCompanyName" @chose="selectAccountCompanyName" />
      <van-field
        v-model="feeItemName"
        required
        is-link
        readonly
        input-align="right"
        name="feeItem"
        label="费用科目"
        placeholder="点击选择费用科目"
        @click="showPicker = true"
      />
      <moreTypeSelectM label="预算立项" :api="budgetHaierPayBccSupportApi.searchBudgetItems" :palceholdeTip="'请输入立项名称或者编码'" :type="'deptFeeItemMust'" :keyWord="'accountName'" :selectKey="'itemName'"  :selectValue="'itemCode'" :bdCode="budgetDepartmentCode" :feeItem="feeItem" :applicationCode="props.applicationCode" :businessType="props.param.businessType"  :required="true" :value="itemName" @chose="selectBudgetItems" />
      <moreTypeSelectM v-if="searchProjectsData&&searchProjectsData.records.length"  label="研发项目" :api="budgetHaierPayBccSupportApi.searchProjects" :palceholdeTip="'请输入研发项目或者编码'" :keyWord="'name'" :selectKey="'name'"  :selectValue="'code'" :companyCode="accountCompanyCode" :value="projectName" @chose="selectProjectsData" />

      <moreTypeSelectM v-if="searchWbsData&&searchWbsData.records.length" label="WBS编码"  :api="budgetHaierPayBccSupportApi.searchWbs" :palceholdeTip="'请输入WBS编码'" :keyWord="'name'" :selectKey="'name'"  :selectValue="'code'" :companyCode="accountCompanyCode" :value="wbsName" @chose="selectWbs" />
      <moreTypeSelectM v-if="searchDcProjectsData&&searchDcProjectsData.records.length" :api="budgetHaierPayBccSupportApi.searchDcProjects" :palceholdeTip="'请输入地产项目或者编码'"  :keyWord="'name'" :selectKey="'name'"  :selectValue="'code'" :accountCode="accountCompanyCode" label="地产项目" :value="dcProjectName" @chose="selectDcProjectsData" />
      <moreTypeSelectM v-if="searchDcItemsData&&searchDcItemsData.records.length" :api="budgetHaierPayBccSupportApi.searchDcItems" :palceholdeTip="'请输入地产分期或者编码'"  :keyWord="'name'" :selectKey="'name'"  :selectValue="'code'" :ProjectCode="dcProjectCode" label="地产分期" :value="dcItemName" @chose="selectDcItem" />
    </van-form>
  </div>
  <!-- 费用科目弹窗  -->
  <van-popup v-model:show="showPicker" position="bottom">
    <van-picker
      :columns="feeItemOptions"
      @confirm="onConfirm"
      @cancel="showPicker = false"
      :columns-field-names="customFieldName"
    />
  </van-popup>
</template>

<style scoped lang="less">
</style>

<style>
:root:root {
  --van-button-primary-background: #0073e5;
  --van-radio-checked-icon-color: #0073e5;
  --van-password-input-background: #f2f2f2;
}
</style>