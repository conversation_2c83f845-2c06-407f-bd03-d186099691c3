import {
    IDailyReportSaveRequestDTO
} from '@haierbusiness-front/common-libs'
import { errorHttpMessageHandle, get, post, download } from '../../request'
import { errorModal, isMobile } from '@haierbusiness-front/utils';
import { showFailToast } from 'vant';

const showError = (error: any) => {
    if (error.response) {
        let errorMessage: string = "";
        const httpStatus = error.response.status;
        if (httpStatus === 401) {
            errorMessage = '当前未登录,拒绝访问!' + error.response.data.message
            if (isMobile()) {
                showFailToast(errorMessage)
            } else {
                errorModal(errorMessage)
            }
        } else if (httpStatus === 403) {
            errorMessage = error.response.data.message + "请尝试重新登录！"
            if (isMobile()) {
                showFailToast(errorMessage)
            } else {
                errorModal(errorMessage)
            }
        } else if (httpStatus === 503) {
            errorMessage = "服务无效！"
            if (isMobile()) {
                showFailToast(errorMessage)
            } else {
                errorModal(errorMessage)
            }
        } else {
            errorMessage = error.response.data.message;
            if (isMobile()) {
                showFailToast(errorMessage)
            } else {
                errorModal(errorMessage)
            }
        }
    } else {
        if (error.code === 'parameter_error') {
            let errorMessage = "当前内容填写不完全, 请仔细检查后提交！\n\r" + error.message.replace('参数错误!', '');
            if (isMobile()) {
                showFailToast(errorMessage)
            } else {
                errorModal(errorMessage)
            }
        } else {
            if (isMobile()) {
                showFailToast(error.message)
            } else {
                errorModal(error.message)
            }
        }
    }
}

export const dailyReportListApi = {
    // 项目完成效果
    getMonthPlanReportList: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return get('/daily/api/report/getMonthPlanReportList', params, undefined, (err: any) => {
            showError(err)
            return errorHttpMessageHandle(err)
        })
    },

    // sst报表
    getPersonReportList: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return get('/daily/api/report/getPersonReportList', params, undefined, (err: any) => {
            showError(err)
            return errorHttpMessageHandle(err)
        })
    },

    // 会议决议明细报表
    getConferenceList: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return get('/daily/api/report/getConferenceList', params, undefined, (err: any) => {
            showError(err)
            return errorHttpMessageHandle(err)
        })
    },

    // 日量化问题报表
    getQuantifierlssueList: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return get('/daily/api/report/getQuantifierlssueList', params, undefined, (err: any) => {
            showError(err)
            return errorHttpMessageHandle(err)
        })
    },
    
    // 日量化问题报表
    getPersonDetailsList: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return get('/daily/api/report/getPersonDetailsList', params, undefined, (err: any) => {
            showError(err)
            return errorHttpMessageHandle(err)
        })
    },

    // 导出SST报表
    exportPersonReport: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return download('/daily/api/report/exportPersonReport', params)
    },
    // 导出项目完成效果
    exportMonthPlanReport: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return download('/daily/api/report/exportMonthPlanReport', params)
    },
    // 导出会议决议报表
    exportConferenceReport: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return download('/daily/api/report/exportConferenceReport', params)
    },
    // 日量化问题表导出
    exportQuantifierlssueReport: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return download('/daily/api/report/exportQuantifierlssueReport', params)
    },

    // 获取激励类型枚举列表
    getTypeNameList: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return get('/daily/api/report/getTypeNameList', params, undefined, (err: any) => {
            showError(err)
            return errorHttpMessageHandle(err)
        })
    },

    // 获取量化人员状态枚举列表
    getQuantifierStateList: (params: IDailyReportSaveRequestDTO): Promise<void> => {
        return get('/daily/api/report/getQuantifierStateList', params, undefined, (err: any) => {
            showError(err)
            return errorHttpMessageHandle(err)
        })
    },

}