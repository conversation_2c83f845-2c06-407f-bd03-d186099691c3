<template>
    <div :style="{ height: props.height + 'vh' }" background="rgba(0,0,0,0)">
        <bar-line from="ydsj_hour" :height="height" v-if="loaded" :legend="legend" :x-axis="xAxis" :y-axis="yAxis"
            :series="series" :tooltip="tooltip" />
    </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import BarLine from "../../../components/barLine.vue";
import { queryTaxiUsageTime } from "@haierbusiness-front/apis/src/data/board/travel";
import { EventBus } from "../../../eventBus";
const props = defineProps({
    dateType: Number,
    height: {
        type: Number,
        default: 30,
    },
});
const loaded = ref(false);
const loading = ref(false);
const legend: any = [];
const xAxis = ref([]);
const yAxis = [
    {
        type: "value",
        name: "单",
        min: 0,
    },
];
const tooltip = {
    trigger: "axis",
};
const series = ref([]);
onMounted(() => {
    queryData();
})
EventBus.on((event, params) => {
    if (event == "refresh") {
        if (!params) queryData();
        if (params) queryData(params);
    }
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
    loading.value = true;
    const data = await queryTaxiUsageTime(
        params ? params.data.name : null,
        params ? params.from : null
    );
    data.columns.forEach((item, index) => {
        if ((index = 0)) return;
        legend.push(item.name[0]);
    });
    loading.value = false;
    const lineData1: any = [];
    const xData: any = [];
    data.rows.forEach((item, index) => {
        xData.push(item[0] + "时");
        lineData1.push(item[1] || 0);
    });
    xAxis.value = xData;
    series.value = [
        {
            name: "订单数",
            type: "line",
            smooth: true,
            areaStyle: {
                color: "rgba(0,240,255,0.4)",
            },
            data: lineData1,
        },
    ] as any;
    loaded.value = true;
};
watch(() => props.dateType, queryData);
</script>
