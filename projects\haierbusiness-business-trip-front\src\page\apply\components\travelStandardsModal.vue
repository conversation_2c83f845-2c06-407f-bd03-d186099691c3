<script lang="ts" setup>
import { Dayjs } from 'dayjs';
import { onMounted, reactive, ref, toRaw } from 'vue';
import type { UnwrapRef } from 'vue';
import type { Rule } from 'ant-design-vue/es/form';
import {
  Anchor as hAnchor,
  <PERSON>ton as hButton,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Form as hForm,
  Modal as hModal,
  Row as hRow,
  Col as hCol,
  FormItem as hFormItem,
  Cascader as hCascader,
  Input as hInput,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem,
  Empty as hEmpty,

} from 'ant-design-vue';
import { IBudgetType, IBudgetForm, TrainStandardRes } from '@haierbusiness-front/common-libs';
import { tripApi } from '@haierbusiness-front/apis';

const visible = ref<boolean>(false);
const names = ref<any>([]);

const show = (codeList:any, nameList:any) => {
  console.log('查询差旅标准dialog ------->>>' , codeList, nameList)
  names.value = JSON.parse(JSON.stringify(nameList))
  visible.value = true;
  queryTrainStandard({userCodes:codeList})
};
defineExpose({
  show,
});

const standardList = ref<Array<Array<TrainStandardRes>>>([])

const queryTrainStandard = (params:any) => {
  tripApi.queryTrainStandard(params).then((res:any) => {
    standardList.value = res || []
  })
}
onMounted(() => {
  // queryTrainStandard()
})
</script>

<template>
  <h-modal v-model:open="visible" title="差旅标准" width="800px"  :footer="null">
    <div style="height: 600px; overflow:auto;" v-if="standardList?.length > 0">
      <div v-for="item, index in standardList" :key="index" >
        <div style="text-align:center; padding:20px; font-size: 14px; font-weight: 500;">{{names[index]}}的差旅标准</div>

        <h-descriptions v-if="item && item.length > 0" bordered :column="1" >
            <h-descriptions-item :label="item2?.productName" v-for="item2, index2 in item" :key="index2">
              <div v-html="item2?.describes"></div>
            </h-descriptions-item>
        </h-descriptions>

        <h-empty v-else/>
      </div>
    </div>
    <h-empty v-else />
  </h-modal>
</template>

<style scoped>
.mb-30 {
  margin-bottom: 30px;
}

.ant-row {
  margin-bottom: 20px;
}

.mt-50 {
  margin-top: 50px;
}

:deep(.ant-descriptions-item-label) {
  width: 120px;
}
</style>