// 参数接口
export interface IPserviceRatemng {
  /* */
  pageNum?: number;

  /* */
  pageSize?: number;

  /* */
  id?: number;

  /* */
  statementCode?: string;

  /* */
  handerCode?: string;

  /* */
  handerName?: string;

  /* */
  handTime?: Record<string, unknown>;

  /*操作时间0开始1结束 */
  handTimes?: Record<string, unknown>[];

  /* */
  settleAmount?: number;

  /* */
  settleMonth?: Record<string, unknown>;

  /* */
  orderStatus?: number;

  /* */
  waterTicketInformation?: string;

  /* */
  remark?: string;

  /* */
  approverCode?: string;

  /* */
  approverName?: string;

  /* */
  approveTime?: Record<string, unknown>;

  /* */
  approveComment?: string;

  /* */
  creator?: string;

  /* */
  creatorName?: string;

  /* */
  createTime?: Record<string, unknown>;

  /* */
  creatorPhone?: string;

  /* */
  updateTime?: Record<string, unknown>;

  /* */
  updater?: string;

  settleMonth?:string;

  settleAmount?:string;

  /* */
  needPage?: boolean;
}

// 响应接口
export interface List_3Res {
  /* */
  pageNum: number;

  /* */
  pageSize: number;

  /* */
  total: number;

  /* */
  totalPage: number;

  /* */
  records: {
    /*主键ID */
    id: number;

    /*账单编号 */
    statementCode: string;

    /*经办人工号 */
    handerCode: string;

    /*经办人名称 */
    handerName: string;

    /*操作时间 */
    handTime: Record<string, unknown>;

    /*归账金额 */
    settleAmount: number;

    /*归账月份 */
    settleMonth: Record<string, unknown>;

    /*订单状态1待审批2已审批3已取消 */
    orderStatus: number;

    /*归账凭证 */
    waterTicketInformation: string;

    /*备注 */
    remark: string;

    /*审批人工号 */
    approverCode: string;

    /*审批人名称 */
    approverName: string;

    /*审批时间 */
    approveTime: Record<string, unknown>;

    /*审批意见 */
    approveComment: string;

    /*创建人 */
    creator: string;

    /*创建人名称 */
    creatorName: string;

    /*创建时间 */
    createTime: Record<string, unknown>;

    /*创建人电话 */
    creatorPhone: string;

    /*更新时间 */
    updateTime: Record<string, unknown>;

    /*更新人 */
    updater: string;
  }[];
}
