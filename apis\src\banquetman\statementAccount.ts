import { downloadPost, get, post } from '../request'
import { 
    IRefundReq, 
    IRefundRes,
    IPageResponse, 
    Update_1Params,
    ExportInvoiceParams
} from '@haierbusiness-front/common-libs'


export const statementAccountApi = {
    list: (params: IRefundReq): Promise<IPageResponse<IRefundRes>> => {
        return post('banquet/api/banquetSettleStatement/list', params)
    },
    detailList: (params: IRefundReq): Promise<IPageResponse<IRefundRes>> => {
        return post('banquet/api/banquetSettleDetail/list', params)
    },
    get: (id: number): Promise<IRefundRes> => {
        return get('banquet/api/banquetRefund/info', {
            id
        })
    },
    exportList: (params: IRefundReq): Promise<IPageResponse<IRefundRes>> => {
        return downloadPost('banquet/api/banquetSettleStatement/export', params)
    },
    // 导出开票信息
    exportInvoice: (params: ExportInvoiceParams): Promise<IPageResponse<IRefundRes>> => {
        return downloadPost('banquet/api/banquetSettleStatement/exportInvoice', params)
    },
    // 导出对账明细
    exportBanquetSettleStatement: (params: ExportInvoiceParams): Promise<IPageResponse<IRefundRes>> => {
        return downloadPost('banquet/api/banquetSettleDetail/export', params)
    },
    // 账单确认
    orderSure: (params: Update_1Params): Promise<IPageResponse<IRefundRes>> => {
        return post('banquet/api/banquetSettleStatement/update', params)
    },
    // 账单同步
    syncSettle: (params: Update_1Params): Promise<IPageResponse<IRefundRes>> => {
        return post('banquet/api/banquetSettleStatement/syncSettle', params)
    },
    // 获取详情
    getInfo: (id: number): Promise<IPageResponse<IRefundRes>> => {
        return get('banquet/api/banquetSettleStatement/get', {
            id
        })
    },
    // 账单取消 
    settleCancel: (params: any): Promise<IPageResponse<IRefundRes>> => {
        return post('banquet/api/banquetSettleStatement/cancel', params)
    },
}
