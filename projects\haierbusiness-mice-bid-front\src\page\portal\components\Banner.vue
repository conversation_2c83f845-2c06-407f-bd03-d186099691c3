<script setup lang="ts">
import { MiceTypeConstant, DistrictTypeConstant, ProcessNode, MiceBidManOrderList } from '@haierbusiness-front/common-libs';
import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons-vue';
import NotificationImg from '@/assets/image/home/<USER>';
import WarningImg from '@/assets/image/home/<USER>';
import ChangeImg from '@/assets/image/home/<USER>';
import MeetingNameImg from '@/assets/image/home/<USER>';
import MeetingTimeImg from '@/assets/image/home/<USER>';
import MeetingPlaceImg from '@/assets/image/home/<USER>';
import MeetingCountImg from '@/assets/image/home/<USER>';
import MeetingTypeImg from '@/assets/image/home/<USER>';
import demandImg from '@/assets/image/demand/demand_material.png'
import { InstructionModal, AnnouncementNotice } from '.';
import {
  routerParam,
} from '@haierbusiness-front/utils';
import { computed, ref, onMounted, onUnmounted, watch, defineEmits } from 'vue';
import { usePortalStore } from '../store';
import { CloseOutlined } from '@ant-design/icons-vue';
import { message, Modal, Tooltip } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import { processOrchestrationApi, miceBidManOrderListApi } from '@haierbusiness-front/apis';
import { useRoute, useRouter } from 'vue-router';
import { resolveParam, meetingProcessOrchestration } from '@haierbusiness-front/utils';

import video1 from '@/assets/image/111.mp4';
import video2 from '@/assets/image/222.mp4';
import video3 from '@/assets/image/333.mp4';
import video4 from '@/assets/image/444.mp4';

const emit = defineEmits(['meetingProcessDetails']);

const videoUrl = ref<number>(1);
const route = useRoute();
const router = useRouter();

// 定义props
interface Props {
  hasPermission: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  hasPermission: true,
  type: {
    type: String,
    default: 'user',
    // manage - 后台-订单列表
    // user - 用户端-订单列表
  },
});

const store = usePortalStore();
const meetingTypeOptions = MiceTypeConstant.toArray().map((item) => ({ label: item.desc, value: item.code }));
const districtTypeOptions = computed(() =>
  DistrictTypeConstant.toArray().map((item) => ({ label: item.desc, value: item.code })),
);

const isQingdao = ref<string>(''); // 是否固定会议城市仅青岛
const meetingMinDaysNum = ref<number>(0); // 会议最短召开日期(单位: 天)
const supportInternational = ref<Boolean>(false); // 是否支持国际会议提报

const inputRef1 = ref(null);
const inputRef2 = ref(null);

const validate = () => {
  const formInfo = [
    { name: 'miceName', label: '会议名称', rules: [{ required: true }] },
    { name: 'meetingDate', type: 'array', label: '会议日期', rules: [{ required: true }] },
    { name: 'districtType', label: '会议地点', rules: [{ required: true }] },
    { name: 'personTotal', label: '会议人数', rules: [{ required: true }] },
    { name: 'miceType', label: '会议类型', rules: [{ required: true }] },
  ];
  for (const formItem of formInfo) {
    const validateRules = formItem.rules;
    for (const validateRule of validateRules) {
      if (validateRule.required) {
        const name = formItem.name;
        let messageText = '';

        if (formItem.type === 'array' && !store.meetingInfo[formItem.name]?.length) {
          messageText = validateRule.message || `${formItem.label}不能为空`;
        } else if (!(store.meetingInfo[formItem.name] || store.meetingInfo[formItem.name] === 0)) {
          messageText = validateRule.message || `${formItem.label}不能为空`;
        }

        if (messageText) {
          const modal = Modal.error({
            title: messageText,
          });

          setTimeout(() => {
            modal.destroy();

            switch (name) {
              case 'miceName':
                inputRef1.value.focus();
                break;
              case 'personTotal':
                inputRef2.value.focus();
                break;

              default:
                break;
            }
          }, 3000);

          // message.error({
          //   content: () => messageText,
          //   style: {
          //     marginTop: '30vh',
          //   },
          // });

          return false;
        }
      }
    }
  }
  return true;
};

const handleDemandSubmit = () => {
  // 检查权限
  if (!props.hasPermission) {
    return;
  }

  const validateRes = validate();
  if (validateRes) {
    store.instructionModalOpen = true;
  }
};

const disabledDate = (current: Dayjs) => {
  // 对接流程编排
  return (
    current &&
    current <
    dayjs()
      .add(meetingMinDaysNum.value - 1 || 0, 'day')
      .endOf('day')
  );
};
const Media = () => {
  const demandSubmit = document.getElementById('demand-submit');
  const handler = () => {
    if (window.innerWidth <= 1099) {
      demandSubmit.style.transform = 'scale(0.5)';
    } else if (window.innerWidth <= 1280) {
      demandSubmit.style.transform = 'scale(0.6)';
    } else if (window.innerWidth <= 1536) {
      demandSubmit.style.transform = 'scale(0.8)';
      demandSubmit.style.marginTop = '26px';
    } else {
      demandSubmit.style.transform = 'scale(1)';
      demandSubmit.style.marginTop = '0px';
    }
  };
  window.addEventListener('resize', handler);
  handler(); // 立即执行一次
  return () => window.removeEventListener('resize', handler); // 返回清理函数
};

const changeVideo = (idx: number) => {
  videoUrl.value = idx;
};

// 流程详情
const getProcessDetails = async (processId = localStorage.getItem('processId') || '') => {
  // 流程ID
  if (!processId) {
    message.error('流程ID不存在！');
    return;
  }

  const res = await miceBidManOrderListApi.processDetails({
    id: processId,
  });

  // 是否固定会议城市仅青岛
  isQingdao.value = meetingProcessOrchestration(
    'DEMAND_SUBMIT',
    res.nodes || [],
    'demandSubmitFixedCityValueConfigDefine',
  );

  // 会议最短召开日期(单位: 天)
  const overDay = meetingProcessOrchestration(
    'DEMAND_SUBMIT',
    res.nodes || [],
    'demandSubmitMinimumMiceStartDateConfigDefine',
  );
  meetingMinDaysNum.value = overDay ? Number(overDay) : 0;

  // 是否支持国际会议提报
  supportInternational.value = res.supportInternational;

  store.meetingInfo.districtType = isQingdao.value === '1' || !supportInternational.value ? 0 : null;

  emit('meetingProcessDetails', {
    isQingdao: isQingdao.value,
    meetingMinDaysNum: meetingMinDaysNum.value,
    processNotice: res.processNotice || '',
  });
};

//登陆人会议列表
const meetingDetails = ref()

//获取登陆人会议列表
const meetingList = async (pageSize = 10) => {
  const ruselt = await miceBidManOrderListApi.active();
  meetingDetails.value = ruselt
}

onMounted(async () => {
  const processId = route.query.processId || '';
  // 流程详情
  await getProcessDetails(processId);

  const cleanup = Media();
  watch(
    () => window.innerWidth,
    () => Media(),
    { immediate: true },
  );
  onUnmounted(cleanup);
  meetingList()
});

const isDetails = ref(false)
const change = () => {
  isDetails.value = !isDetails.value
}

//公告通知非弹框
const open = ref<boolean>(false);

const afterOpenChange = (bool: boolean) => {
  console.log('open', bool);
};

const showDrawer = () => {
  open.value = true;
};
//需求产看详情
const btnJump = async (item: MiceBidManOrderList, path?: string, type?: string) => {
  if(!item){
    item = meetingDetails.value[0]
  }
  console.log(item,"item");
  let miceId = item.id;
  let miceDemandId = '';
  let processNode = item.processNode;
  if (item.state == 1410) processNode = 'DEMAND_PUSH';
  let status = route.query.status;
  if (item.demandId) {
    miceDemandId = item.demandId;
  }
  let pathCru = path;
  const localUrl = window.location.href;

  let record = {
    miceId: item.miceId,
    orderSource: props.type,
    orderState: item.state, // 订单驳回,110 - 查详情
    processNode: processNode,
    orderType: type === '2' ? 'detail' : '',
    finalReverseReason: [110, 120, 310, 320, 620, 910].includes(item.state) ? item.finalReverseReason : '',

    pdMainId: item.pdMainId,
    pdVerId: item.pdVerId,
  };

  if (processNode === 'DEMAND_PRE_INTERACT') {
    // 需求事先交互 - 显示价格预算
    record.previewCalc = 'previewCalc';
    record.priceSource = 'manage';
    record.demandTotalPrice = item.demandTotalPrice;
  }
  if (type === '2') {
    // 查看详情

    //方案确认,方案复审
    if (['SCHEME_APPROVAL', 'SCHEME_CONFIRM', 'SCHEME_RE_APPROVAL'].includes(processNode)) {
      record.miceDetails = item;
      if (props.type === 'manage') pathCru = '/bidman/scheme/confirm/view';
      else {
        const businessMiceBid = import.meta.env.VITE_MICE_BID_URL + '#';

        // 跳转需求确认页面
        const url =
          (localUrl.includes('/localhost') ? 'http://localhost:5182/#' : businessMiceBid) +
          '/bidman/scheme/confirm/view?record=' +
          routerParam(record);

        window.location.href = url;
        return;
      }
    } else if (['DEMAND_SUBMIT'].includes(processNode)) {
      message.error('需求未提报！');
      return;
    } else if (['BID_PUSH', 'BIDDING', 'BID_RESULT_CONFIRM'].includes(processNode)) {
      if (props.type === 'manage') pathCru = '/bidman/scheme/confirm/view';
      else {
        const businessMiceBid = import.meta.env.VITE_MICE_BID_URL + '#';

        // 跳转需求确认页面
        const url =
          (localUrl.includes('/localhost') ? 'http://localhost:5182/#' : businessMiceBid) +
          '/bidman/scheme/confirm/view?record=' +
          routerParam(record);

        window.location.href = url;
        return;
      }
    } else {
      const businessMiceBid = import.meta.env.VITE_MICE_BID_URL + '#';
      const businessMiceBidMan = import.meta.env.VITE_MICE_BIDMAN_URL + '#';

      let baseUrl = '';
      let url = '';

      if (props.type === 'manage') {
        baseUrl = businessMiceBidMan;
        pathCru = '/bidman/orderList/meetingDetail';
      } else {
        baseUrl = businessMiceBid;
        pathCru = '/miceOrder/meetingDetail';
      }

      const localHref = localUrl.includes('/localhost:5183') ? 'http://localhost:5182/#' : 'http://localhost:5161/#';

      // 跳转需求确认页面
      const openUrl =
        (localUrl.includes('/localhost') ? localHref : baseUrl) + pathCru + '?record=' + routerParam(record);

      window.location.href = openUrl;
      return;
    }
  }

  router.push({
    path: pathCru,
    query: {
      record: routerParam({ ...record, orderType: type === '2' ? 'detail' : '' }),
      miceId: miceId,
      miceDemandId: miceDemandId,
      status: status,
    },
  });
};
const currentMeeting = ref()
const handleChange = (from:number,to:number) => {
  console.log(from,to,"from,to");
  currentMeeting.value = meetingDetails.value[to]
};
</script>

<template>
  <div class="banner">
    <video v-if="videoUrl === 1" autoplay loop muted class="banner_video">
      <source :src="video1" type="video/mp4" />
    </video>
    <video v-if="videoUrl === 2" autoplay loop muted class="banner_video">
      <source :src="video2" type="video/mp4" />
    </video>
    <video v-if="videoUrl === 3" autoplay loop muted class="banner_video">
      <source :src="video3" type="video/mp4" />
    </video>
    <video v-if="videoUrl === 4" autoplay loop muted class="banner_video">
      <source :src="video4" type="video/mp4" />
    </video>
    <div class="banner_change">
      <div class="banner_change_btn" v-for="idx in 4" @click="changeVideo(idx)">{{ idx }}</div>
    </div>

    <div class="notice" @click="showDrawer">
      <div class="icon flex-center">
        <img :src="NotificationImg" />
        <span>通知</span>
      </div>
      <span class="notice-text">海易智会系统操作指南</span>
      <span class="arrow" />
    </div>
    <span class="banner-title">海易智会 您身边的会议专家</span>
    <div class="demand-submit" id="demand-submit">
      <div class="demand-submit-top">
        <img :src="WarningImg" />
        <span>2025年年度预算待提报，</span>
        <a>点击立即提报</a>
      </div>
      <div class="demand-submit-center" v-show="!isDetails">
        <div class="demand-submit-center-left flex">
          <div class="demand-submit-card flex" :style="{ flex: 283 }">
            <img :src="MeetingNameImg" />
            <div class="card-content">
              <div class="label">会议名称</div>
              <div class="input-wrapper">
                <h-input ref="inputRef1" class="input" v-model:value="store.meetingInfo.miceName" :maxlength="50"
                  placeholder="填写会议名称" />
              </div>
            </div>
          </div>
          <div class="demand-submit-card flex" :style="{ flex: 289 }">
            <img :src="MeetingTimeImg" />
            <div class="card-content">
              <div class="label">会议时间</div>
              <div class="input-wrapper flex acenter">
                <h-range-picker class="input" v-model:value="store.meetingInfo.meetingDate" :disabledDate="disabledDate"
                  allow-clear>
                  <template #suffixIcon></template>
                </h-range-picker>
              </div>
            </div>
          </div>
          <div class="demand-submit-card flex" :style="{ flex: 253 }">
            <img :src="MeetingPlaceImg" />
            <div class="card-content">
              <div class="label">会议地点</div>
              <div class="input-wrapper">
                <h-select class="input" v-model:value="store.meetingInfo.districtType" :options="districtTypeOptions"
                  :disabled="isQingdao === '1' || !supportInternational" placeholder="选择会议地点" />
              </div>
            </div>
          </div>
          <div class="demand-submit-card flex" :style="{ flex: 171 }">
            <img :src="MeetingCountImg" />
            <div class="meeting-count card-content">
              <div class="label">会议人数</div>
              <a-input-number ref="inputRef2" class="input" :bordered="false"
                v-model:value="store.meetingInfo.personTotal" placeholder="填写人数" :min="1" :max="999999" />
              <!-- <div class="input">
                <div class="meeting-count-value">{{ store.meetingInfo.personTotal }}人</div>
                <div class="meeting-count-buttons">
                  <div @click="store.meetingInfo.personTotal += 1"><CaretUpOutlined :style="{ fontSize: '10px' }" /></div>
                  <div @click="store.meetingInfo.personTotal > 0 && (store.meetingInfo.personTotal -= 1)"><CaretDownOutlined :style="{ fontSize: '10px' }" /></div>
                </div>
              </div> -->
            </div>
          </div>
          <div class="demand-submit-card flex" :style="{ flex: 226 }">
            <img :src="MeetingTypeImg" />
            <div class="card-content">
              <div class="label">会议类型</div>
              <div class="input-wrapper">
                <h-select class="input" v-model:value="store.meetingInfo.miceType" :options="meetingTypeOptions"
                  placeholder="选择会议类型" />
              </div>
            </div>
          </div>
        </div>
        <div class="demand-submit-center-right">
          <h-tooltip :title="props.hasPermission ? '' : '很抱歉，您没有当前流程的使用权限！'" placement="top">
            <div class="demand-submit-button" :class="{ disabled: !props.hasPermission }" @click="handleDemandSubmit">
              提报需求
            </div>
          </h-tooltip>
          <h-checkbox v-model:checked="store.meetingInfo.demandType">
            <span class="demand-submit-checkbox-text">顾问代提</span>
          </h-checkbox>
        </div>
      </div>
      <div class="demand-submit-details" v-show="isDetails">
        <div class="detaisl-left">
          <a-carousel arrows dot-position="left" dots="false" :before-change="handleChange">
            <template #nextArrow>
              <div class="custom-slick-arrow" style="left: 10px; z-index: 1">
                <img src="@/assets/image/icon/middle.png" alt="">
              </div>
            </template>
            <template #prevArrow>
              <div class="custom-slick-arrow" style="left: 10px;top: 18px;">
                <img src="@/assets/image/icon/top.png" alt="">
              </div>
            </template>
            <div class="details-content" v-for="(item, index) in meetingDetails">
              <div class="content-top">
                <p class="p-left">{{ item.miceName }}</p>
                <p>{{ item.mainCode }}</p>
              </div>
              <ul class="content-middle">
                <li>
                  <img src="@/assets/image/home/<USER>" alt="">
                  会议时间：{{ item.startDate }} - {{ item.endDate }}
                </li>
                <li>
                  <img src="@/assets/image/demand/demand_attendant.png" alt="">
                  会议人数：{{ item.personTotal }}
                </li>
                <li>
                  <img src="@/assets/image/home/<USER>" alt="">
                  会议预算：￥{{ item.winTheBidTotalPrices }}
                </li>
              </ul>
              <div class="state">
                {{ ProcessNode.ofType(item.processNode).desc }}
              </div>
            </div>
          </a-carousel>
        </div>

        <div class="demand-submit-center-right">
          <h-tooltip placement="top">
            <div class="demand-submit-button" :class="{ disabled: !props.hasPermission }"
              @click="btnJump(currentMeeting, '', '2')">
              查看详情
            </div>
          </h-tooltip>
        </div>
      </div>
      <div class="demand-submit-bottom flex-center">
        <div class="button flex-center" @click="change">
          <img :src="ChangeImg" />
          <span>切换至需求详情页面</span>
        </div>
      </div>
    </div>
    <a-drawer v-model:open="open" class="calender-drawer" placement="right" width="750" :closable="false"
      :bodyStyle="{ background: '#F7F8FC', display: 'flex', flexDirection: 'column', padding: '0px' }"
      @after-open-change="afterOpenChange">
      <AnnouncementNotice></AnnouncementNotice>
      <template #title>
        <div class="flex acenter">
          <img :src="demandImg" :style="{ width: '20px', height: '20px', marginRight: '12px' }" />
          <span style="font-weight: bold;">公告通知</span>
        </div>
      </template>
      <template #extra>
        <CloseOutlined :style="{ cursor: 'pointer' }" @click="open = false" />
      </template>
    </a-drawer>
  </div>
</template>

<style scoped lang="less">
* {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-style: normal;
}

.banner {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  min-height: 600px;
  background: url(@/assets/image/banner.png) no-repeat;
  background-size: cover;
  position: relative;

  .banner_video {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;

    width: 100%;
    height: 100%;
    /* height: auto; */

    object-fit: cover;
  }

  .banner_change {
    position: absolute;
    right: 20px;
    top: 100px;

    display: flex;

    .banner_change_btn {
      padding: 0 12px;
      margin-right: 5px;
      background: #3bc6fc;
      color: #fff;
      cursor: pointer;
    }
  }

  .notice {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px 0 6px;
    width: 316px;
    height: 38px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 19px;
    backdrop-filter: blur(1px);
    margin-top: 117px;

    &:hover {
      .arrow {
        background-position: left 0px bottom 0px;
      }
    }

    .icon {
      width: 76px;
      height: 30px;
      background: linear-gradient(270deg, #3bc6fc 0%, #1868db 100%);
      border-radius: 15px;
      color: #ffffff;

      >img {
        width: 18px;
        margin-right: 6px;
      }
    }

    .notice-text {
      font-weight: 500;
      font-size: 16px;
      color: #1d2129;
      line-height: 22px;
    }

    .arrow {
      display: block;
      width: 22px;
      height: 22px;
      background-image: url(@/assets/image/home/<USER>
      background-size: 44px 44px;
      background-repeat: no-repeat;
      position: relative;
      flex-shrink: 0;
      background-position: right 0px top 0px;
      transition: 0.3s ease-out;
    }
  }

  .banner-title {
    width: 100%;
    margin: 50px 0 36px;
    /* font-family: AlimamaShuHeiTi-Bold; */
    font-size: 62px;
    color: #ffffff;
    line-height: 78px;
    letter-spacing: 1px;
    text-shadow: 0px 4px 8px rgba(0, 0, 0, 0.27);
    font-weight: bold;
    text-align: center;

    z-index: 1;
  }

  .demand-submit {
    width: 1460px;
    height: 221px;
    padding: 20px 30px 16px;
    background: #ffffff;
    box-shadow: 0px 8px 20px 0px rgba(1, 31, 80, 0.5);
    border-radius: 12px;
    backdrop-filter: blur(0px);

    .demand-submit-top {
      display: flex;
      align-items: center;

      >img {
        width: 22px;
        margin-right: 11px;
      }

      >span {
        font-size: 16px;
        color: #4e5969;
        line-height: 22px;
      }

      >a {
        font-weight: 500;
        font-size: 16px;
        color: #1868db;
        line-height: 22px;
        text-decoration-line: underline;
      }
    }

    .demand-submit-center {
      margin-top: 17px;
      display: flex;

      .demand-submit-center-left {
        flex: 1;
        height: 88px;
        background: #ffffff;
        border-radius: 12px;
        border: 1px solid #e5e6eb;
        margin-right: 10px;

        .demand-submit-card {
          align-items: center;
          padding: 0 16px;
          border-right: 1px solid #e5e6eb;

          &:last-child {
            border-right: none;
          }

          >img {
            width: 20px;
            height: 20px;
            margin-right: 16px;
          }

          .label {
            font-size: 16px;
            color: #86909c;
            line-height: 22px;
            margin-bottom: 8px;
          }

          .input-wrapper {
            margin-left: -8px;

            .input {
              width: 100%;
              border: none;

              &:focus,
              &.ant-picker-focused,
              &.ant-input-number-focused {
                box-shadow: none;
              }

              :deep(.ant-select-selector) {
                box-shadow: none;
                border: none !important;
              }

              :deep(.ant-select-arrow) {
                display: none;
              }
            }
          }

          .card-content {
            flex: 1;
          }

          .meeting-count {
            .input {
              :deep(.ant-input-number-input) {
                padding-left: 0;
              }
            }
          }
        }
      }

      .demand-submit-center-right {
        text-align: center;

        .demand-submit-button {
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 168px;
          height: 56px;
          background: linear-gradient(176deg, #3bc6fc 0%, #1868db 100%, #1868db 100%);
          border-radius: 12px;
          font-weight: 500;
          font-size: 20px;
          color: #ffffff;
          margin-bottom: 8px;

          &:hover {
            background: linear-gradient(176deg,
                rgba(59, 198, 252, 0.9) 0%,
                rgba(24, 104, 219, 0.9) 100%,
                rgba(24, 104, 219, 0.9) 100%);
          }

          &.disabled {
            cursor: not-allowed;
            background: #d9d9d9;
            color: #999;

            &:hover {
              background: #d9d9d9;
            }
          }
        }

        .demand-submit-checkbox-text {
          font-weight: 500;
          font-size: 16px;
          color: #1d2129;
          line-height: 22px;
        }
      }
    }

    .demand-submit-bottom {
      margin-top: 16px;

      .button {
        cursor: pointer;
        width: 220px;
        height: 42px;
        background: rgba(24, 104, 219, 0.08);
        border-radius: 6px;
        backdrop-filter: blur(12px);

        >img {
          width: 20px;
          margin-right: 12px;
        }

        >span {
          font-weight: 500;
          font-size: 16px;
          color: #1868db;
          line-height: 22px;
        }
      }
    }

    .demand-submit-details {
      position: relative;
      display: flex;
      align-items: center;
      margin-top: 17px;
      height: 98px;

      .detaisl-left {
        width: 1198px;
        height: 100%;
        margin-right: 34px;

        :deep(.ant-carousel) {
          height: 100%;
        }
      }

      h3 {
        margin-bottom: 0px;
      }

      .custom-slick-arrow {
        display: flex !important;
        justify-content: center;
        align-items: center;

        img {
          width: 22px;
          height: 22px;
        }

      }

      .details-content {
        display: block;
        height: 90px;
        padding-left: 66px;
        position: relative;

        .content-top {
          display: flex;
          align-items: center;
          margin: 14px auto;

          p {
            margin-bottom: 0;

            &:nth-child(1) {
              font-weight: bold;
              font-size: 20px;
              color: #1D2129;
            }

            &:nth-child(2) {
              margin-left: 10px;
              font-weight: 400;
              font-size: 14px;
              color: #86909c;
            }
          }
        }

        .content-middle {
          display: flex;
          align-items: center;
          margin-right: 30px;
          margin-bottom: 0px;

          li {
            margin-right: 78px;
            font-weight: 400;
            font-size: 16px;
            color: #1D2129;
          }

          img {
            display: inline-block;
            width: 20px;
            height: 20px;
            vertical-align: middle;
          }
        }

        .state {
          height: 30px;
          line-height: 30px;
          color: #fff;
          background: #1868DB;
          border-radius: 4px;
          position: absolute;
          top: 5px;
          right: 5px;
          padding: 0 10px;
        }
      }

      .demand-submit-center-right {
        text-align: center;

        .demand-submit-button {
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 168px;
          height: 56px;
          background: linear-gradient(176deg, #3bc6fc 0%, #1868db 100%, #1868db 100%);
          border-radius: 12px;
          font-weight: 500;
          font-size: 20px;
          color: #ffffff;
          margin-bottom: 8px;

          &:hover {
            background: linear-gradient(176deg,
                rgba(59, 198, 252, 0.9) 0%,
                rgba(24, 104, 219, 0.9) 100%,
                rgba(24, 104, 219, 0.9) 100%);
          }

          &.disabled {
            cursor: not-allowed;
            background: #d9d9d9;
            color: #999;

            &:hover {
              background: #d9d9d9;
            }
          }
        }

        .demand-submit-checkbox-text {
          font-weight: 500;
          font-size: 16px;
          color: #1d2129;
          line-height: 22px;
        }
      }
    }
  }
}

:deep(.slick-track) {
  height: 90px !important;
}

:deep(.slick-slider) {
  height: 90px !important;
}

:deep(.slick-slide) {
  height: 90px !important;
}

:deep(.slick-list) {
  height: 90px !important;
}

:deep(.slick-slide) {
  text-align: center;
  height: 100%;
  background: #F7F8FC;
  overflow: hidden;
}

:deep(.slick-prev::before) {
  content: " "
}

:deep(.slick-next::before) {
  content: " "
}

:deep(.slick-prev:hover) {
  background-color: #E1E2EA;
}

:deep(.slick-next:hover) {
  background-color: #E1E2EA;
}

:deep(.slick-arrow) {
  top: 56px;
  width: 28px;
  height: 36px;
  font-size: 25px;
  background-color: #E1E2EA;
  z-index: 1;
}

:deep(.ant-carousel .slick-dots) {
  display: none !important;
}

:deep(.ant-drawer-body) {
  padding: 0 !important;
}
</style>
