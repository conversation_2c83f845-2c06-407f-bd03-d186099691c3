<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script
      crossorigin="anonymous"
      src="https://r.haier.net/assets/prod/dts-fe/kitchengod/0.2.55/index.js?scode=S03256"
    ></script>
    <script type="text/javascript">
      window._AMapSecurityConfig = {
        securityJsCode: '610db7cc7881574494e34fd00b13ab97',
      };
    </script>
    <script
      type="text/javascript"
      src="https://webapi.amap.com/maps?v=1.4.15&key=25569e43d6c6bcfa4d39a1b920d8d2d1&plugin=AMap.Geocoder"
    ></script>
    <script src="/snap.svg-min.js"></script>
    <title>海尔商务门户</title>
  </head>
  <body>
    <!-- <script id="robotScript" src="https://nesp.haier.net/webchatbot/static/js/entrance.js?width=1000&amp;height=650&amp;iconSrc=https://nesp.haier.net/upload/material/13/20221215/6134AF0F056B49FA9573985F25D1881D.png&amp;iwidth=65&amp;iheight=65&amp;iright=10&amp;ibottom=200&amp;openUrl=https%3A%2F%2Fnesp.haier.net%2Fwebchatbot%2FsingleSign.html%3FredirectUrl%3Dhttps%253A%252F%252Fnesp.haier.net%252Fwebchatbot%252Fchat.html%253FsysNum%253D1659675658214%2526sourceId%253D179%2526lang%253Dzh_CN"></script> -->

    <script>
      let javaScript = document.createElement('script');

      if (
        navigator.userAgent.match(
          /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i,
        )
      ) {
        javaScript.src = 'https://r.haier.net/assets/overlay/dts-fe/online-popup/index.js';
        document.body.appendChild(javaScript);
        window.__Konph = {
          'popup-js': {
            'dom-id': 'helpCenterBtn',
            'one-url-name': 'popup/prod' /*测试：popup/test*/ /*生产：popup/prod*/,
            'apply-code': 'S03256' /*替换系统码*/,
            'scene-code': '240430264084' /*替换场景码*/,
            'user-code': '',
            map: encodeURI('{"帮助中心":["in","bzzx"]}'),
            mode: 'mobile' /*按需接入*/,
            'delay-time': 3,
            'is-draggable': '1',
            'url-list': [],
            'tip-name': '帮助',
          },
        };
      } else {
        javaScript.id = 'robotScript';
        javaScript.src =
          'https://nesp.haier.net/webchatbot/static/js/entrance.js?width=1000&height=650&iconSrc=https://nesp.haier.net/upload/material/13/20221215/6134AF0F056B49FA9573985F25D1881D.png&iwidth=65&iheight=65&iright=10&ibottom=150&openUrl=https%3A%2F%2Fnesp.haier.net%2Fwebchatbot%2FsingleSign.html%3FredirectUrl%3Dhttps%253A%252F%252Fnesp.haier.net%252Fwebchatbot%252Fchat.html%253FsysNum%253D1659675658214%2526sourceId%253D179%2526lang%253Dzh_CN';
        document.body.appendChild(javaScript);
      }
    </script>
    <div id="app"></div>
    <div id="helpCenterBtn"></div>
    <!--生产-->
    <!-- <script src="https://r.haier.net/assets/overlay/dts-fe/online-popup/index.js"></script> -->

    <!--测试-->
    <!-- <script src="https://r.haier.net/assets/daily/dts-fe/online-popup/3.0.1/index.js"></script> -->
    <script type="module" src="/src/main.ts"></script>
    <!-- <script src="//r.haier.net/assets/overlay/dts-fe/common-assets/vconsole/3.10.1/vconsole.min.js"></script>
    <script>
      // VConsole 默认会挂载到 `window.VConsole` 上
      var vConsole = new window.VConsole();
    </script> -->
  </body>
  <style>
    /* @font-face {
        font-family: 'SourceHanSansCN';
        src: url('./public/font/HarmonyOS_Sans_SC_Bold.ttf');
    } */
  </style>
</html>
