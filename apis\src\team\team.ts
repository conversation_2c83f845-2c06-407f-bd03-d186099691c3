import { get, post,downloadPost } from '../request'
import {
  Result,
  TCteateTeam,
  THotelDeatil,
  TTicket,
  THotel,
  TTicketDetail
} from '@haierbusiness-front/common-libs'
export const teamApi = {

  // 新增团队申请单
  addTeam: (params: TCteateTeam): Promise<TCteateTeam> => {
    return post('team/api/team/order/create', params )
  },

  // 提交团队申请单
  submitTeam: (params: TCteateTeam): Promise<Result> => {
    return post('team/api/team/order/submit', params )
  },

  // 修改团队申请单
  updateTeam: (params: TCteateTeam): Promise<TCteateTeam> => {
    return post('team/api/team/order/update', params )
  },

  // 分页列表
  pageList: (params: TCteateTeam): Promise<TCteateTeam> => {
    return post('team/api/team/order/pageList', params )
  },

  // 获取详情
  teamDetail: (id: string): Promise<TCteateTeam> => {
    return get('team/api/team/order/detail?id='+ id )
  },

  // 撤回
  recallTeam: (params: TCteateTeam): Promise<TCteateTeam> => {
    return post('team/api/team/order/recall', params )
  },

  // 上传出行人

  travelerImport: (params: FormData): Promise<TCteateTeam> => {
    return post('team/api/team/order/travelerImport', params )
  },

  
}

// 管理端接口
export const teamListApi = {
  // 获取详情
  teamDetail: (id: string): Promise<TCteateTeam> => {
    return get('team/api/team/order/admin/detail?id='+ id )
  },

   // 修改团队申请单
   updateTeam: (params: TCteateTeam): Promise<TCteateTeam> => {
    return post('team/api/team/order/admin/update', params )
  },
  // 保存
  saveDetail: (params: TCteateTeam): Promise<TCteateTeam> => {
    return post('team/api/team/order/admin/saveDetail', params )
  },
  // 提交
  submit: (params: TCteateTeam): Promise<TCteateTeam> => {
    return post('team/api/team/order/admin/submit', params )
  },

  // 管理端列表查询
  list: (params: TCteateTeam): Promise<TCteateTeam> => {
    return post(`team/api/team/order/admin/adminPageList`, params);
  },

  // 订单接收
  receive: (params: TCteateTeam): Promise<TCteateTeam> => {
    return post(`team/api/team/order/admin/receive`, params);
  },

  // 酒店明细导入
  hotelImport: (params: FormData): Promise<THotel> => {
    return post(`team/api/team/order/admin/hotelImport`, params,  { 'content-type': 'multipart/form-data' });
  },

  // 机票明细导入
  ticketImport: (params: FormData): Promise<TTicket> => {
    return post(`team/api/team/order/admin/ticketImport`, params,  { 'content-type': 'multipart/form-data' });
  },

  // 列表导出
  exportList: (params: any): Promise<void> => {
    return downloadPost('team/api//team/order/admin/adminPageExport', params)
},
}
