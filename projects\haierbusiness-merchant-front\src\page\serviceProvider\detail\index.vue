<!-- 服务商详情页 -->
<script setup lang="ts">
import {
  Button as hButton,
  Card as hCard,
  Row as hRow,
  Col as hCol,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem,
  Divider as hDivider,
  Table as hTable,
  Modal as hModal,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Upload as hUpload,
  message,
  Modal,
} from 'ant-design-vue';
import { ref, onMounted, h, UnwrapRef, reactive, toRaw, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { serviceProviderApi } from '@haierbusiness-front/apis/src/merchant/serviceProvider';
import {
  ServiceProviderStateEnum,
  ServiceProviderStateMap,
  TrialStateEnum,
  TrialStateMap,
  ContractStateEnum,
  ContractStateMap,
  UploadFile,
  MerchantContract
} from '@haierbusiness-front/common-libs';
import { fileApi, serviceProviderDetailApi } from '@haierbusiness-front/apis';
import { EditContractModal, EditBankModal } from './components';
import { useServiceProviderDetailStore } from './store';
import dayjs, { Dayjs } from 'dayjs';

const route = useRoute();
const router = useRouter();
const loading = ref(false);
const detailData = ref<any>({});
const showContractModal = ref(false);
const currentContractUrl = ref('');
const contractList = ref<any[]>([]);
const bankList = ref<any[]>([]);
const store = useServiceProviderDetailStore();

store.businessId = String(route.query.id)
// 获取详情数据
const getDetail = async () => {
  const id = Number(route.query.id);
  if (!id) return;

  loading.value = true;
  try {
    const res = await serviceProviderApi.getBusinessDetail({ id: id });
    detailData.value = res;
  } catch (error) {
    console.error('获取详情失败:', error);
  } finally {
    loading.value = false;
  }
};

// 获取合同列表
const getContractList = async (params = {}) => {
  try {
    const id = Number(route.query.id);
    if (!id) return;

    const res = await serviceProviderApi.getContractList({ ...params, merchantId: id });
    contractList.value = res;
  } catch (e) {
    console.error('获取合同列表失败:', e);
  }
};

// 获取银行列表
const getBankList = async (params = {}) => {
  try {
    const id = Number(route.query.id);
    if (!id) return;

    const res = await serviceProviderApi.getBankList({ ...params, merchantId: id });
    console.log(res, '2');
    bankList.value = res;
  } catch (e) {
    console.error('获取银行列表失败:', e);
  }
};

// 添加分页配置
const pagination = {
  pageSize: 5,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
  pageSizeOptions: ['5', '10', '20', '50'],
};

// 合同列表列定义
const contractColumns = [
  {
    title: '合同编号',
    dataIndex: 'contractCode',
    width: '200px',
  },
  {
    title: '合同有效期',
    dataIndex: 'contractDate',
    width: '200px',
    customRender: ({ record }: { record: any }) => {
      if (!record.contractStart && !record.contractEnd) return '-';
      return `${record.contractStart || ''} ~ ${record.contractEnd || ''}`;
    }
  },
  {
    title: '合同状态',
    dataIndex: 'state',
    width: '120px',
    customRender: ({ text }: { text: number }) => {
      if (text === undefined || text === null) return '-';
      return ContractStateMap[text as ContractStateEnum] || '-';
    }
  },
  {
    title: '签订日期',
    dataIndex: 'signDate',
    width: '200px',
  },
  {
    title: '合同附件',
    dataIndex: 'contractUrl',
    width: '200px',
    customRender: ({ text }: { text: string }) => {
      if (!text) return '-';
      return h(hButton, {
        type: 'link',
        size: 'small',
        onClick: () => {
          currentContractUrl.value = text;
          showContractModal.value = true;
        }
      }, () => '查看');
    }
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center'
  },
];

// 银行信息列定义
const bankColumns = [
  {
    title: '银行户主',
    dataIndex: 'accountHolderName',
    width: '200px',
  },

  {
    title: '银行账号',
    dataIndex: 'accountNumber',
    width: '200px',
  },

  {
    title: '开户行地址',
    dataIndex: 'bankBranchAddress',
    width: '200px',
  },
  {
    title: '银行所属国家',
    dataIndex: 'bankCountry',
    width: '200px',
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center'
  },
];

// 获取状态文本
const getStateText = (state: number | null | undefined) => {
  return state ? ServiceProviderStateMap[state as ServiceProviderStateEnum] || '-' : '-';
};

// 获取试用状态文本
const getTrialStateText = (state: number | null | undefined) => {
  if (state === TrialStateEnum.FORMAL) return TrialStateMap[TrialStateEnum.FORMAL];
  if (state === TrialStateEnum.TRIAL) return TrialStateMap[TrialStateEnum.TRIAL];
  return '-';
};

onMounted(() => {
  getDetail();
  getContractList();
  getBankList();
  const baseUrl = import.meta.env.VITE_BUSINESS_URL;
  console.log(baseUrl);

});

// 关闭合同预览弹框
const handleCloseContractModal = () => {
  showContractModal.value = false;
  currentContractUrl.value = '';
};

// 查看附件
const handleViewAttachment = (attachment: string) => {
  if (attachment) {
    // 如果是URL直接打开，也可以做成弹框查看
    window.open(attachment, '_blank');
  }
};

// 详情查看方法
const handleDetail = (record: any) => {
  // 查看详情逻辑
  if (record.contractUrl) {
    currentContractUrl.value = record.contractUrl;
    showContractModal.value = true;
  } else {
    message.info('暂无详情可查看');
  }
};

const handleDelete = (record: MerchantContract) => {
  Modal.confirm({
    title: '是否确定删除?',
    async onOk() {
      await store.deleteContract({ id: record.id })
      getContractList()
    },
  })
}

const bankHandleDelete = (record: MerchantContract) => {
  Modal.confirm({
    title: '是否确定删除?',
    async onOk() {
      await store.deleteBank({ id: record.id })
      getBankList()
    },
  })
}

const contractDetail = ref<MerchantContract>({})
const editContractModalOpen = ref(false)
const editBankModalModalOpen = ref(false)
const handleContract = () => {
  contractDetail.value = {
    contractNo: '',
    contractCode: '',
    contractDate: [],
    signDate: void 0,
    state: true,
    contractUrl: '',
  },
    editContractModalOpen.value = true;
}
const handleBank = () => {
  store.bankDetail = {
    accountNumber: '',
    bankBranchCode: '',
    bankBranchAddress: '',
    accountHolderName: '',
    bankCountry: '',
  },
    editBankModalModalOpen.value = true;
}

const handleEdit = (record: any) => {
  console.log(record.contractUrl, "record");
  // 创建新对象而不是直接赋值record
  contractDetail.value = {
    id: record.id,
    merchantId: record.merchantId,
    contractNo: record.contractNo || '',
    contractCode: record.contractCode || '',
    // 如果有开始和结束日期，使用dayjs处理
    contractDate: record.contractStart && record.contractEnd ?
      [dayjs(record.contractStart), dayjs(record.contractEnd)] : [],
    // 签订日期使用dayjs处理
    signDate: record.signDate ? dayjs(record.signDate) : undefined,
    // 状态：直接传递原始状态值（0表示开/有效，1表示关/失效）
    state: record.state,
    // 确保合同附件URL正确传递
    contractUrl: record.contractUrl || ''
  };

  console.log('编辑时传递的数据:', contractDetail.value);
  editContractModalOpen.value = true;
}

const bankHandleEdit = (record: any) => {
  console.log(record, "record");
  store.bankDetail = {
    id: record.id,
    accountNumber: record.accountNumber || '',
    bankBranchCode: record.bankBranchCode || '',
    bankBranchAddress: record.bankBranchAddress || '',
    accountHolderName: record.accountHolderName || '',
    bankCountry: record.bankCountry || '',
  }
  editBankModalModalOpen.value = true;
  console.log(store.bankDetail, "store.bankDetail");
}

</script>

<template>
  <div class="detail-container">

    <h-card :loading="loading">
      <!-- 基本信息 -->
      <div class="info-section">
        <h3><span></span>基本信息</h3>
        <h-row :gutter="[16, 16]">
          <h-col :span="6">
            <div class="info-item">
              <span class="label">商户企业名称：</span>
              <span class="value">{{ detailData.name }}</span>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="info-item">
              <span class="label">统一社会信用代码：</span>
              <span class="value">{{ detailData.unifiedSocialCreditCode }}</span>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="info-item">
              <span class="label">商户企业编码：</span>
              <span class="value">{{ detailData.code }}</span>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="info-item">
              <span class="label">商户海外Z码：</span>
              <span class="value">{{ detailData.zcode }}</span>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="info-item">
              <span class="label">企业CODE：</span>
              <span class="value">{{ detailData.enterpriseCode }}</span>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="info-item">
              <span class="label">引入时间：</span>
              <span class="value">{{ detailData.gmtCreate }}</span>
            </div>
          </h-col>
        </h-row>
      </div>
    </h-card>

    <!-- 合同信息 -->
    <h-card :loading="loading">
      <div class="contract">
        <h3><span></span>合同信息</h3>
        <h-button type="primary" @click="handleContract()">
          <PlusOutlined /> 新增
        </h-button>
      </div>

      <h-table :columns="contractColumns" :data-source="contractList" :pagination="pagination" size="small">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === '_operator'">
            <div class="operator-buttons">
              <h-button type="link" @click="handleEdit(record)">编辑</h-button>
              <h-button type="link" @click="handleDelete(record)">删除</h-button>
            </div>
          </template>
        </template>
      </h-table>
    </h-card>
    <!-- 银行信息 -->
    <h-card :loading="loading">
      <div class="contract">
        <h3><span></span>供应商银行信息</h3>
        <h-button type="primary" @click="handleBank()">
          <PlusOutlined /> 新增
        </h-button>
      </div>
      <h-table :columns="bankColumns" :data-source="bankList" :pagination="pagination" size="small">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === '_operator'">
            <div class="operator-buttons">
              <h-button type="link" @click="bankHandleEdit(record)">编辑</h-button>
              <h-button type="link" @click="bankHandleDelete(record)">删除</h-button>
            </div>
          </template>
        </template>
      </h-table>
    </h-card>

    <!-- 合同附件预览弹框 -->
    <h-modal :visible="showContractModal" title="合同附件预览" width="800px" @cancel="handleCloseContractModal"
      :footer="null">
      <iframe :src="currentContractUrl" style="width:100%; height:500px;" frameborder="0"></iframe>
    </h-modal>
  </div>

  <EditContractModal v-model="editContractModalOpen" :contractDetail="contractDetail"
    :getContractList="getContractList">
  </EditContractModal>
  <EditBankModal v-model="editBankModalModalOpen" :getDataList="getBankList" :bankDetail="store.bankDetail">
  </EditBankModal>

</template>

<style lang="less" scoped>
.detail-container {
  padding: 24px;
  background: #fff;
  min-height: 100vh;

  .ant-card-bordered {
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0px 3px 12px 0px rgba(1, 12, 51, 0.07);
  }

  .page-header {
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 16px;

    h1 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }
  }

  .info-section {
    margin-bottom: 24px;

    h3 {
      margin-bottom: 16px;
      font-size: 18px;
      font-weight: 500;
    }
  }

  .info-item {
    display: flex;
    align-items: flex-start;

    .label {
      color: rgba(0, 0, 0, 0.65);
      margin-right: 8px;
      min-width: 90px;
    }

    .value {
      color: rgba(0, 0, 0, 0.85);
      flex: 1;
      word-break: break-all;
    }
  }

  :deep(.ant-descriptions) {
    margin-bottom: 24px;
  }

  h3 {
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;

    span {
      display: inline-block;
      width: 4px;
      height: 20px;
      margin-right: 3px;
      background: #1868DB;
    }
  }
}

.contract {
  display: flex;
  justify-content: space-between;

  button {
    width: 60px !important;
  }
}

:deep(.css-tb4uf8) {
  width: 100% !important;
}

.ant-picker {
  width: 100% !important;
}
</style>
