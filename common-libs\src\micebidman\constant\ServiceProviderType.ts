// 商户类型枚举
export enum MerchantTypeEnum {
  HOTEL = 1,
  TRAVEL = 2,
  INSURANCE = 3,
  GIFT = 4,
  CAR = 5,
}

// 商户类型描述映射
export const MerchantTypeMap = {
  [MerchantTypeEnum.HOTEL]: '酒店',
  [MerchantTypeEnum.TRAVEL]: '旅行社',
  [MerchantTypeEnum.INSURANCE]: '保险',
  [MerchantTypeEnum.GIFT]: '礼品',
  [MerchantTypeEnum.CAR]: '用车',
} as const;

// 联系人职位类型枚举
export enum ContactPositionEnum {
  GENERAL_MANAGER = 1,
  CONTACT = 2
}

// 联系人职位类型描述映射
export const ContactPositionMap = {
  [ContactPositionEnum.GENERAL_MANAGER]: '总经理',
  [ContactPositionEnum.CONTACT]: '对接人'
} as const;

// 是否缴纳保证金枚举
export enum EarnestMoneyEnum {
  YES = 1,
  NO = 0
}

// 是否缴纳保证金描述映射
export const EarnestMoneyMap = {
  [EarnestMoneyEnum.YES]: '是',
  [EarnestMoneyEnum.NO]: '否'
} as const;

// 试用状态枚举
export enum TrialStateEnum {
  TRIAL = 0, // 试用期
  FORMAL = 1  // 转正
}
export const TrialStateMap = {
  [TrialStateEnum.TRIAL]: '试用期',
  [TrialStateEnum.FORMAL]: '正式',
} as const;

// 试用状态描述映射
export const MiceBidManTrialStateMap = {
  [TrialStateEnum.TRIAL]: '试用期',
  [TrialStateEnum.FORMAL]: '转正'
} as const;



// 将枚举转换为选项数组形式
export const getMerchantTypeOptions = () => {
  return Object.entries(MerchantTypeEnum)
    .filter(([key]) => isNaN(Number(key))) // 过滤掉数字键（反向映射）
    .map(([_, value]) => ({
      label: MerchantTypeMap[value as MerchantTypeEnum],
      value: value as MerchantTypeEnum
    }));
};

// 将是否缴纳保证金枚举转换为选项数组形式
export const getEarnestMoneyOptions = () => {
  return Object.keys(EarnestMoneyEnum)
    .filter(key => !isNaN(Number(key))) // 过滤数字键
    .map(key => ({
      label: EarnestMoneyMap[key as unknown as EarnestMoneyEnum],
      value: Number(key)
    }));
}; 