<script setup lang="ts">
import {
  Button as hButton,
  Upload as hUpload,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem,
  Form as hForm,
  Modal as hModal,
  FormItem as hFormItem,
  Input as hInput,
  message
} from "ant-design-vue";
import { ColumnType } from "ant-design-vue/lib/table/interface";
import { Key } from "ant-design-vue/lib/vc-table/interface";
import {
  PlusOutlined,
  SearchOutlined,
  DownOutlined,
  UpOutlined
} from "@ant-design/icons-vue";
import { banquetApplyApi } from "@haierbusiness-front/apis";
import {
  BApplyListRecord,
  BApplyPerson,
  BanquetStatusEnum
} from "@haierbusiness-front/common-libs";
import dayjs, { Dayjs } from "dayjs";
import { computed, ref, watch, onMounted } from "vue";
import { DataType, usePagination, useRequest } from "vue-request";
import { useEditDialog, useDelete } from "@haierbusiness-front/composables";
import {
  getCurrentRouter,
  errorModal,
  routerParam,
  getCurrentRoute
} from "@haierbusiness-front/utils";
import { storeToRefs } from "pinia";
import { applicationStore } from "@haierbusiness-front/utils/src/store/applicaiton";
import Editor from "@haierbusiness-front/components/editor/Editor.vue";
import { UploadOutlined } from '@ant-design/icons-vue';
import type { UploadChangeParam } from 'ant-design-vue';
import { imgApi } from "@haierbusiness-front/apis";
import { fileApi } from '@haierbusiness-front/apis';
import type { UploadProps } from 'ant-design-vue';

const fileList = ref<UploadProps['fileList']>([]);
const uploadLoading = ref<boolean>(false);
const changeImgShow = ref<boolean>(false)
const formState = ref({
  id: "",
  content: "",
  type: "HOMEPAGE_PIC",
  notificationSort:null
});

const imgData = ref<any>([])

const columns: ColumnType[] = [
  {
    title: '显示顺序',
    dataIndex: 'notificationSort',
    width:"300px",
    align: 'center',
    ellipsis: true,
  },
  {
    title: '图片',
    dataIndex: 'img',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '250px',
    fixed: 'right',
    align: 'center'
  },
];

const loading = ref<boolean>(false)

const getDataList = ()=>{
  loading.value = true
  imgApi.getHomepagePic().then((res:any)=>{
    imgData.value = res
    loading.value = false
  })
  .catch(()=>{
    loading.value = false
  })
}



const handleRemove: UploadProps['onRemove'] = file => {
  const index = fileList.value.indexOf(file);
  const newFileList = fileList.value.slice();
  newFileList.splice(index, 1);
  fileList.value = newFileList;
};

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  if (!(file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg')) {
   
   message.warning('只能上传 JPG, JPEG, PNG哦！');
   return hUpload.LIST_IGNORE;
 }
  return true;
}

const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const upload = (options: any) => {
  uploadLoading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);
  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path
      options.file.fileName = options.file.name
      options.file.url = baseUrl + it.path
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

const onFinish = () =>{
  if(fileList.value&&fileList.value.length){
    formState.value.content = JSON.stringify(fileList.value)
    imgApi.saveOrUpdatePic(formState.value).then(res => {
    message.success("保存成功!");
    changeImgShow.value = false
    getDataList()
    });
  }else{
    message.warning('请上传文件');
  }
}


const getImgUrl = (content:string) =>{
  if(content){
    return JSON.parse(content)&&JSON.parse(content)[0].filePath
  }else{
    return null
  }
}

// 

const EditorImg = (row:any) =>{
  if(row.content){
    fileList.value = JSON.parse(row.content)
  }else{
    fileList.value = []
  }
  formState.value.notificationSort = row.notificationSort
  changeImgShow.value = true
}

// 删除图片
const delImg =  (row:any) =>{
  formState.value.content= ""
  formState.value.notificationSort = row.notificationSort
  imgApi.saveOrUpdatePic(formState.value).then(res => {
    message.success("刪除成功");
    changeImgShow.value = false
    getDataList()
    });

}

onMounted(() => {
  getDataList()
});
</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'" style="padding: 10px 10px 10px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" type="primary"
              @click="getDataList()">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
    <div
      style="background-color: #ffff;"
    >
    <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="imgData"
          :pagination="false" :scroll="{ y: 900 }" :loading="loading">
          <template #bodyCell="{ column, record }">

            <template v-if="column.dataIndex === 'img'">
              <img v-if="record.content" style="width:360px;height:120px;" :src="getImgUrl(record.content)" alt="">
            </template>

            <template v-if="column.dataIndex === '_operator'">
              <h-button  type="link"  @click="EditorImg(record)">修改图片</h-button>
              <h-button type="link"  @click="delImg(record)">删除图片</h-button>
            </template>
          </template>
        </h-table>

      <h-modal v-model:open="changeImgShow" title="修改图片" @ok="onFinish">
        <h-form
        style="width:400px;"
        layout="vertical"
        name="basic"
        :label-col="{ span: 2 }"
        :wrapper-col="{ span: 22 }"
        autocomplete="off"
        >
          <h-form-item label="">
            <h-upload style="width:50%;" v-model:fileList="fileList"  list-type="picture" :custom-request="upload" :max-count="1" :accept="'.png,.jpg,.jpeg'" :before-upload="beforeUpload" @remove="handleRemove">
              <h-button>
                <upload-outlined></upload-outlined>
                上传图片 
              </h-button>
              <span class="text" style="font-size:14px; color: #999;margin-left:10px;">建议尺寸（宽360px，高120px）</span>
              <div class="text" style="font-size:14px; color: red;">仅支持png、jpg格式文件</div>
            </h-upload>
          </h-form-item>
        </h-form>
      </h-modal>
    </div>
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}
.btn {
  position: absolute;
  bottom: 20px;
  left:45%;
}
.img {
  width: 104.5px;
  height: 50px;
}
:deep(.ant-descriptions-item-label) {
  width: 200px;
}
:deep(.ant-descriptions-item-content) {
  width: 300px;
}
</style>
