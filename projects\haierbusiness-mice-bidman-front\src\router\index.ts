import { RouteRecordRaw } from 'vue-router';
import { baseRouterConstructor } from '@haierbusiness-front/utils';

const modules = import.meta.glob('/src/page/**/*.vue');



const whiteList = ['/bidman/priceInquiry/inquiryOrderForm']
let flag = true
const currentUrl = location.hash
const index = currentUrl.lastIndexOf('?')
const url = currentUrl.substring(1, index)

let routes:RouteRecordRaw[] = []

if(whiteList.indexOf(url)> -1) {
    flag = false
    routes = [
        {
            path: '/bidman/priceInquiry/inquiryOrderForm',
            component: () => import("../page/priceInquiry/inquiryOrderForm.vue")
        }
    ];
} 

const router = baseRouterConstructor('haierbusiness-mice-bidman', modules, flag, undefined, routes)
export default router;
