<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  DatePicker as hDatePicker,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Cascader as hCascader,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  Card as hCard,
  TreeSelect as hTreeSelect,
  message

} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons-vue';
import { serviceRatemngApi, banquetApi,download,cityApi } from '@haierbusiness-front/apis';
import {
  IPserviceRatemng,
  IApply,
  BanquetStatusEnum,
  BanquetApproveStatusEnum
} from '@haierbusiness-front/common-libs';
import { getEnumOptions,getCurrentRoute } from '@haierbusiness-front/utils';

import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'

import {useRoute} from 'vue-router'
import router from '../../router'
const route = ref(getCurrentRoute());
// const route = useRoute()

const currentRouter = ref()
const serviceModalShow = ref<boolean>(false)
const serviceRef = ref()
const serviceForm = ref<any>({})
const detail = ref<any>({})

const saveRules = {
  haierServiceRate: [
    {
      required: true,
      message: '请输入海尔服务费率',
      trigger: 'blur',
    },
  ],
  mtServiceRate: [
    {
      required: true,
      message: '请输入美团服务费率',
      trigger: 'blur',
    },
  ],
}

// 订单状态
const orderState = computed(() => {
  return getEnumOptions(BanquetStatusEnum, true);
});

const approveState = computed(() => {
  return getEnumOptions(BanquetApproveStatusEnum, true);
});


const columns: ColumnType[] = [
  {
    title: '账单编号',
    dataIndex: 'statementCode',
    width: '240px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '经办人工号',
    dataIndex: 'handerCode',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '经办人姓名',
    dataIndex: 'handerName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'handTime',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '归账金额/元',
    dataIndex: 'settleAmount',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '归账月份',
    dataIndex: 'settleMonth',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<IPserviceRatemng>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(serviceRatemngApi.list);

const {
  data:exportListData,
  run:exportListApiRun,
  loading:exportListLoading,
} = useRequest(serviceRatemngApi.exportList);

const reset = () => {
  searchParam.value = {}
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize: 10,
  });
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const goToDetail = (id: number) => {
  currentRouter.value.push({
    path: '/serviceRatemng/detail',
    query: {
      id: id
    }
  })
}

// 点击修改费率
const changeService = () =>{
  serviceModalShow.value = true
}

// 修改费率
const saveServiceForm = ()=>{
  serviceRef.value.validate()
    .then(() => {
      serviceRatemngApi.changeServiceRatemng(serviceForm.value).then(res=>{
        message.success('修改成功')
        serviceModalShow.value = false
        getInfo()
      })
    })
    .catch(error => {
      console.log('error', error);
    });
}

// 费用归账
const addList =()=>{
  currentRouter.value.push({
    path: '/serviceRatemng/add'
  })
}

const getInfo = () =>{
  serviceRatemngApi.getInfo().then(res=>{
    detail.value = res
  })
}

onMounted(async () => {
    currentRouter.value = await router
    handleTableChange({ current: 1, pageSize: 10 })
    getInfo()
})

</script>

<template>
  <div v-if="route.matched.length<=2" style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="statementCode">账单编号:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="充值编号" v-model:value="searchParam.statementCode"  style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="handerCode">经办人工号:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="经办人" v-model:value="searchParam.handerCode"  style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="handerName">经办人姓名:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="经办人" v-model:value="searchParam.handerName"  style="width: 100%" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="handTimes">操作时间:</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="searchParam.handTimes" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>
        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="orderStatus">订单状态:</label>
          </h-col>
          <h-col :span="4">
            <h-select
                ref="select"
                v-model:value="searchParam.orderStatus"
                allow-clear
                style="width: 100%"
                placeholder="订单状态"
              >
                <h-select-option :value="1">待审批</h-select-option>
                <h-select-option :value="2">已审批</h-select-option>
                <h-select-option :value="3">已取消</h-select-option>
              </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="settleMonth">归账月份:</label>
          </h-col>
          <h-col :span="4">
            <h-date-picker
                v-model:value="searchParam.settleMonth"
                picker="month"
                value-format="YYYY-MM-DD hh:mm:ss"
                style="width: 100%"
                allow-clear
              />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button  style="margin-right: 10px" type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>

            <h-button type="primary" :loading="exportListLoading" @click="exportListApiRun(searchParam);">
              导出
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="16" style="text-align: left;">
            <h-button type="primary" :loading="exportListLoading" @click="addList">
              费用归账
            </h-button>
          </h-col>
          <h-col :span="8" style="text-align: right;">
            <div class="flBox">
                <div class="left">
                  <div>
                      海尔: <span>{{detail?.haierServiceRate}}%</span>
                  </div>
                  <div>
                      美团: <span>{{detail?.mtServiceRate}}%</span>
                  </div>
                </div>
                <div class="right">
                  <h-button type="primary" :loading="exportListLoading" @click="changeService">
                  修改费率
                </h-button>
                </div>
            </div>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <div class="tableHeader"> 
          <div class="ygBox">已归账金额：<span>{{detail?.settledAmount}}元</span></div>
          <div class="dgBox">待归账金额：<span>{{detail?.unsettleAmount}}元</span></div>
        </div>
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">

            <template v-if="column.dataIndex === 'sceneType'">
              {{ record.sceneType == 1 ? '宴请' :'外卖' }}
            </template>
            <template
              v-if="column.dataIndex === 'orderStatus'"
            >{{ record.orderStatus == 1 ? '待审批' :record.orderStatus == 2?'已审批':'已取消' }}
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="goToDetail(record.id)">查看详情</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    <!-- 维护服务费率 -->
    <h-modal v-model:open="serviceModalShow" title="服务费率维护
    " @ok="saveServiceForm">
        <h-form ref="serviceRef" :model="serviceForm" :label-col="{ span: 5 }" :wrapper-col="{ span: 19 }" autocomplete="off"  :rules="saveRules">
          <h-form-item name="haierServiceRate" label="海尔">
            <a-input-number v-model:value="serviceForm.haierServiceRate" placeholder="海尔服务费率" style="width: 80%; ">
              <template #addonAfter>
                <div>%</div>
              </template>
            </a-input-number>
          </h-form-item>
          <h-form-item name="mtServiceRate" label="美团">
            <a-input-number v-model:value="serviceForm.mtServiceRate" placeholder="美团服务费率" style="width: 80%; ">
              <template #addonAfter>
                <div>%</div>
              </template>
            </a-input-number>
          </h-form-item>
        </h-form>

    </h-modal>
  </div>
  <router-view v-if="route.matched.length>2"></router-view>
</template>

<style scoped lang="less">

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.tableHeader{
  display: flex;
  margin-bottom: 10px;
  span{
    font-size:18px;
    font-weight: bolder;
    color: rgb(70, 4, 239);
  }
}
.dgBox{
  margin-left:20px;
}
.flBox{
  width:100%;
  display:flex;
  justify-content:space-between;
  background: #fff;;
  align-items: center;
  div{
    margin-left:40px;
    span{
    font-size:18px;
    font-weight: bolder;
    color: rgb(70, 4, 239);
  }
  }
  .left{
    display:flex;
  }
}
</style>
