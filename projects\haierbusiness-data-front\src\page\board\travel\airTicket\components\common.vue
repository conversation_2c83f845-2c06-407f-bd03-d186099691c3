<template>
  <template v-if="props.type == 'row'">
    <h-col :span="6" v-for="(item, i) in props.data" :key="i">
      <div class="col-title">{{ getName(item) }}</div>
      <div class="col-chart">
        <component :is="components.get(item)" :gngj="props.gngj"></component>
      </div>
    </h-col>
  </template>
  <template v-else>
    <div v-for="(item, i) in props.data" :key="i">
      <template v-if="item == 'ChinaMap' || item == 'Map'">
        <component :is="components.get(item)" :gngj="props.gngj"></component>
      </template>
      <template v-else-if="item == 'BusinessTrend' || item == 'SaveTrend' || item == 'TimeProportion'">
        <div class="col-title col-title-l">
          {{ getName(item) }}
          <div class="col-title-extra" v-if="item != 'TimeProportion'">
            <span class="button" v-for="(tab, index) in tabs" :key="index" @click="onTabs(item, index)">
              {{ tab }}
            </span>
            <img
              :style="
                item == 'BusinessTrend'
                  ? {
                      transform: `translate(${active * 52}px)`,
                    }
                  : item == 'SaveTrend'
                  ? {
                      transform: `translate(${active2 * 52}px)`,
                    }
                  : {
                      transform: `translate(${active3 * 52}px)`,
                    }
              "
              class="active"
              src="@/assets/image/bigscreen/icon-title-btn-acitve.png"
              alt=""
            />
          </div>
        </div>
        <div class="col-chart">
          <component
            :is="components.get(item)"
            :gngj="props.gngj"
            :date-type="item == 'BusinessTrend' ? active : item == 'SaveTrend' ? active2 : active3"
          ></component>
        </div>
      </template>

      <template v-else>
        <div class="col-title">{{ getName(item) }}</div>
        <div class="col-chart">
          <component :is="components.get(item)" :gngj="props.gngj" :height="33"> </component>
        </div>
      </template>
    </div>
  </template>
</template>
<script setup lang="ts">
import { ref, onMounted, markRaw, defineAsyncComponent } from 'vue';

import { Badge as hBadge, Progress as hProgress, Button as hButton, Col as hCol } from 'ant-design-vue';

const components = markRaw(new Map<string, any>());
//组件Accumulative
components.set(
  'Accumulative',
  defineAsyncComponent(() => import('./accumulative.vue')),
);
components.set(
  'AccumulativeSlim',
  defineAsyncComponent(() => import('./accumulativeSlim.vue')),
);

components.set(
  'DiscountPercentage',
  defineAsyncComponent(() => import('./discountPercentage.vue')),
);
components.set(
  'AirlineRank',
  defineAsyncComponent(() => import('./airlineRank.vue')),
);
components.set(
  'DepartureRank',
  defineAsyncComponent(() => import('./departureRank.vue')),
);

components.set(
  'NonMinimumPrice',
  defineAsyncComponent(() => import('./nonMinimumPrice.vue')),
);

components.set(
  'DestinationRank',
  defineAsyncComponent(() => import('./destinationRank.vue')),
);
components.set(
  'SupplierPercentage',
  defineAsyncComponent(() => import('./supplierPercentage.vue')),
);

components.set(
  'PayType',
  defineAsyncComponent(() => import('./payType.vue')),
);
components.set(
  'ChangeAndRefund',
  defineAsyncComponent(() => import('./changeAndRefund.vue')),
);

components.set(
  'SettleRank',
  defineAsyncComponent(() => import('./settleRank.vue')),
);
components.set(
  'AirlineCompanyRank',
  defineAsyncComponent(() => import('./airlineCompanyRank.vue')),
);

components.set(
  'ChinaMap',
  defineAsyncComponent(() => import('./chinaMap.vue')),
);
components.set(
  'Map',
  defineAsyncComponent(() => import('./map.vue')),
);
components.set(
  'BusinessTrend',
  defineAsyncComponent(() => import('./businessTrend.vue')),
);
components.set(
  'SaveTrend',
  defineAsyncComponent(() => import('./saveTrend.vue')),
);
components.set(
  'TimeProportion',
  defineAsyncComponent(() => import('./timeProportion.vue')),
);

const tabs = ['年', '月', '日'];
const active = ref(1);
const active2 = ref(1);
const active3 = ref(1);
const onTabs = (item: string, index: number) => {
  if (item == 'BusinessTrend') active.value = index;
  if (item == 'SaveTrend') active2.value = index;
  if (item == 'TimeProportion') active3.value = index;
};
const props = defineProps({
  data: Array<string>,
  gngj: {
    type: [String, Number],
    default: '1',
  },
  type: {
    type: String,
    default: 'colums',
  },
});

// 获取name
const getName = (status: number | string) => {
  const resultMap: any = {
    AccumulativeSlim: '累计成交',
    Accumulative: '累计成交',
    DiscountPercentage: '折扣占比',
    AirlineRank: '行程排行Top20',
    DepartureRank: '出发城市排行Top20',
    NonMinimumPrice: '非最低价占比',

    PayType: '支付类型与平台',
    ChangeAndRefund: '退改情况',
    SettleRank: '结算单位排行Top10',
    AirlineCompanyRank: '航空公司排行',

    DestinationRank: '目的地排行Top20',
    SupplierPercentage: '供应商占比',

    BusinessTrend: props.gngj == 1 ? '国内机票业务趋势' : '国际机票业务趋势',
    SaveTrend: '节省金额趋势',
    TimeProportion: '预订时间占比',
    default: '',
  };
  return resultMap[status] || resultMap.default;
};
</script>
<style scoped lang="less">
@import url(../../../main.less);

.mt-10 {
  margin-top: 10px;
}

.supplier {
  display: flex;
  justify-content: center;
}
</style>
