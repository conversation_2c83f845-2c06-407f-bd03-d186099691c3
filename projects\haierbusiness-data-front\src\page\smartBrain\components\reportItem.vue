<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Popover as hPopover,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  QuestionCircleOutlined,
  DeleteOutlined,
  MenuOutlined,
  MinusCircleOutlined,
} from '@ant-design/icons-vue';
import { computed, ref, watch, onMounted, defineEmits } from 'vue';
const props = defineProps({
  reportInfo: {
    type: Object,
    default: {},
  },
  showRemove: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['remove']);
const confirmRemove = () => {
  emit('remove',props.reportInfo);
};
// 初始化
onMounted(async () => {});
</script>

<template>
  <!-- 指标item -->
  <div class="indexItem">
    <div class="indexItemHeader">
      <div class="headerLeft">
        <a-popconfirm title="确认取消申请这个指标吗？" @confirm="confirmRemove" @cancel="cancel">
          <MinusCircleOutlined v-if="props.showRemove" class="icon" />
        </a-popconfirm>
      </div>
      <div class="reportName">
        {{ props.reportInfo.reportName }}
        <h-popover title="指标详情" trigger="click">
          <template #content>
            <span style="display: block;margin-top:10px;font-weight: bold;font-size:12px;">1.指标定义</span>
            <div style="width: 700px;margin-top:3px;">
              {{ props.reportInfo.knowCenterVo.entryName }} : {{ props.reportInfo.knowCenterVo.entryContent }}
            </div>
            <span style="display: block;margin-top:10px;font-weight: bold;font-size:12px;">2.其他描述</span>
            <div style="width: 500px;margin-top:3px;">{{ props.reportInfo.indexReportContent }}</div>
          </template>
          <QuestionCircleOutlined style="color: #7f7f7f" />
        </h-popover>
      </div>
      <div class="headerRight">
        <h-tag v-for="(v, index) in props.reportInfo.labelVo" v-show="index < 2" color="#1677ff">{{
          v.labelName
        }}</h-tag>
        <h-popover title="更多标签">
          <template #content>
            <h-tag v-for="(v, index) in props.reportInfo.labelVo" v-show="index > 2" color="#1677ff">{{
              v.labelName
            }}</h-tag>
          </template>
          <MenuOutlined v-if="props.reportInfo.labelVo.length >= 3" style="color: #1677ff" />
        </h-popover>
      </div>
    </div>
    <div class="indexItemBottom">
      <!-- <Echarts v-if="props.reportInfo.dataSearch==0" :queryCondition="props.reportInfo.queryCondition" :echartsJson="props.reportInfo.echartsJson" :dataType="props.reportInfo.dataSearch" :height="24" :id="index" />
          <Rank  v-if="props.reportInfo.dataSearch==2" :queryCondition="props.reportInfo.queryCondition" :echartsJson="props.reportInfo.echartsJson" :height="24"></Rank> -->
      <img style="width: 100%; height: 100%" :src="props.reportInfo.imagesUrl" alt="" />
    </div>
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.indexItem {
  box-shadow: 0 1px 1px hsl(0deg 0% 0% / 0.075), 0 2px 2px hsl(0deg 0% 0% / 0.075), 0 4px 4px hsl(0deg 0% 0% / 0.075),
    0 8px 8px hsl(0deg 0% 0% / 0.075), 0 16px 16px hsl(0deg 0% 0% / 0.075);
  display: flex;
  flex-flow: column;
  width: 100%;
  min-height: 30vh;
  height: 30vh;
  background: inherit;
  background-color: rgba(255, 255, 255, 1);
  border: none;
  border-radius: 0px;
  margin: 24px 24px 0 0;
  &:nth-child(3n) {
    margin-right: 0;
  }
  .indexItemBottom {
    flex: 1;
    height: calc(30vh - 48px);
  }
  .indexItemHeader {
    position: relative;
    height: 48px;
    background: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    border-bottom: 1px solid #ccc;
    .reportName {
      width: 100%;
      font-weight: 900;
      font-size: 14px;
      color: #000;
      text-align: center;
    }
    .headerLeft {
      // width: 10%;
      position: relative;
      cursor: pointer;
      .icon {
        font-size: 16px;
        color: #f63333;
        margin-left: 6px;
      }
    }
    .headerRight {
      // width: 35%;
      text-align: right;
      position: absolute;
      right: 20px;
    }
  }
}
.pagination {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.5);
}
.ant-popover-inner-content {
  span {
    font-weight: 600;
    margin-top: 10px;
    display: block;
    font-size: 12px;
  }
  div {
    font-size: 12px;
  }
}
</style>
