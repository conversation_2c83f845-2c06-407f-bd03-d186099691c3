<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  message,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { productLineApi } from '@haierbusiness-front/apis';
import {
  IProductLineFilter,
  IProductLine
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted, h, reactive, nextTick } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import router from '../../router'
import ColumnFilter from '@haierbusiness-front/components/mice/search/ColumnFilter.vue';
// const router = useRouter()

const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router
  // 初始化时加载数据
  listApiRun({
    pageNum: 1,
    pageSize: 10,
    ...searchParam.value
  })
})

// 表格过滤搜索相关状态
const filterInputs = reactive({
  name: '',
  adminList: '',
  counsellorList: '',
  processList: '',
  createName: '',
  gmtCreate: undefined,
});

const columns: ColumnType[] = [
  {
    title: '序号',
    dataIndex: 'index',
    width: '80px',
    align: 'center',
    customRender: ({ index }) => index + 1 + (current.value - 1) * pageSize.value,

  },
  {
    title: '产品线名称',
    dataIndex: 'name',
    width: '220px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '产品线管理员',
    dataIndex: 'adminList',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      if (!text || text.length === 0) return '-';
      return text.map((item: { nickName: string }) => item.nickName).join('、');
    },
  },
  {
    title: '会议顾问',
    dataIndex: 'counsellorList',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      if (!text || text.length === 0) return '-';
      return text.map((item: { nickName: string }) => item.nickName).join('、');
    },
  },
  {
    title: '包含流程数量',
    dataIndex: 'processList',
    width: '120px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      if (!text) return 0;
      return text.length || 0;
    },
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<IProductLineFilter>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(productLineApi.list);

const reset = () => {
  searchParam.value = {}
  console.log('重置后的搜索参数:', searchParam.value);

  // 使用nextTick确保DOM更新后再调用接口
  nextTick(() => {
    // 重置后立即查询
    const params = {
      pageNum: 1,
      pageSize: 10
    };
    console.log('重置后调用接口参数:', params);
    listApiRun(params);
  });
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
) => {
  // 确保使用最新的搜索参数和过滤参数，优先使用filterInputs中的非空值
  const params = {
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  };

  console.log('最终查询参数:', params);
  listApiRun(params);
};
const edit = (id?: number) => {
  currentRouter.value.push({ path: "/bidman/productLine/edit", query: { id: id } })
}


// 删除
const { handleDelete } = useDelete(productLineApi, () => listApiRun({
  ...searchParam.value,
  pageNum: data.value?.pageNum,
  pageSize: data.value?.pageSize,
}))

const beginAndEnd = ref<[Dayjs, Dayjs]>()
watch(() => beginAndEnd.value, (n: [Dayjs, Dayjs] | undefined) => {
  if (n) {
    searchParam.value.begin = n[0].format('YYYY-MM-DD HH:mm:ss')
    searchParam.value.end = n[1].format('YYYY-MM-DD HH:mm:ss')
  } else {
    searchParam.value.begin = undefined
    searchParam.value.end = undefined
  }
});

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="name">产品线名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.name" placeholder="请输入产品线名称" style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;" class="Modify">
            <label for="adminName">产品线管理员：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.adminName" placeholder="请输入产品线管理员" style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="counsellorName">会议顾问：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.counsellorName" placeholder="请输入会议顾问" style="width: 100%" allow-clear />
          </h-col>
          <!-- <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="counsellorName">会议顾问：</label>
          </h-col>
          <h-col :span="3">
            <h-input v-model:value="searchParam.counsellorName" placeholder="请输入会议顾问" style="width: 100%" allow-clear />
          </h-col> -->
        </h-row>
        <!-- <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="name">产品线名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.name" placeholder="请输入产品线名称" style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;" class="Modify">
            <label for="adminName">产品线管理员：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.adminName" placeholder="请输入产品线管理员" style="width: 100%" allow-clear />
          </h-col>
        </h-row> -->

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
            <h-button type="primary" @click="edit()">
              <PlusOutlined /> 新增
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="(record) => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="edit(record.id)">编辑</h-button>

              <h-button type="link" @click="handleDelete(record.id)">删除</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>


  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}
.Modify{
  display: block;
  flex: 0 0 10.333333%;
  max-width: 10.333333%;
}
</style>
