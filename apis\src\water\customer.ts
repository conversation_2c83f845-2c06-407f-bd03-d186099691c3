import { download, get, post, filepost, originalGet } from '../request'

export const waterworkCustomerApi = {
    list: (params: any): Promise<void> => {
        return get('waterworks/api/tcustomer/page', params)
    },
    listAll: (params: any): Promise<void> => {
        return get('waterworks/api/tcustomer/list', params)
    },
    add: (params: any): Promise<void> => {
        return post('waterworks/api/tcustomer/save', params)
    },
    update: (params: object): Promise<void> => {
        return post(`waterworks/api/tcustomer/update`, params);
    },
    export: (params: any): Promise<void> => {
        return download('waterworks/api/tcustomer/export', params)
    },
    delete: (ids: string): Promise<void> => {
        return post(`waterworks/api/tcustomer/delete/${ids}`);
    },
}