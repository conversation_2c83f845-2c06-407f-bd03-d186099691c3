<template>
  <div class="mobile-box">
    <!-- <van-nav-bar title="团队订票/订房申请" left-arrow>
      <template #left>
        <van-icon name="arrow-left" color="#000" size="24" />
      </template>
    </van-nav-bar> -->

    <van-form @submit="onSubmit">
      <!-- 基本信息 -->
      <van-cell-group inset title="基本信息">
        <van-field label="出发地" @click.stop="showCityPop('begin', teamForm.beginCityCode)" readonly required
          :rules="[{ required: true, message: '请选择出发地' }]" is-link name="beginCityCode" placeholder="请选择"
          input-align="right" v-model="teamForm.beginCityName"></van-field>
        <van-field v-model="teamForm.endCityName" required :rules="[{ required: true, message: '请选择目的地' }]" label="目的地"
          @click.stop="showCityPop('end', teamForm.endCityCode)" readonly is-link name="endCityCode" placeholder="请选择"
          input-align="right"></van-field>
      </van-cell-group>
      <van-cell-group inset class="mt-10">
        <van-row class="width100">
          <van-col :span="11">
            <van-field readonly v-model="teamForm.beginDate" :rules="[{ required: true, message: '请选择出发时间' }]"
              @click="openTimePicker('begin')" placeholder="点击选择出发时间" name="beginDate" label-align="left"
              input-align="left">
            </van-field>
          </van-col>
          <van-col :span="2" class="flex align-items-center justify-content-center">
            <van-icon name="arrow" />
          </van-col>
          <van-col :span="11">
            <van-field readonly v-model="teamForm.endDate" @click="openTimePicker('end')" placeholder="点击选择到达时间"
              :rules="[{ required: true, message: '请选择到达时间' }]" name="endDate" label-align="left" input-align="left">
            </van-field>
          </van-col>
        </van-row>
      </van-cell-group>

      <van-cell-group inset class="mt-10">
        <van-field required name="evectionType" label="出差类型" label-align="left" input-align="right">
          <template #input>
            <van-radio-group v-model="teamForm.evectionType" direction="horizontal">
              <van-radio :name="1">因私</van-radio>
              <van-radio :name="0">因公</van-radio>
            </van-radio-group>
          </template>
        </van-field>

        <!-- <van-field name="travelerFileUrl" label="出行人信息" label-align="left" input-align="right">
          <template #input>
            <van-uploader :after-read="afterRead" :preview-image="false" v-model="fileList" :max-count="1"
              accept=".xls, .xlsx">
              <van-button size="small" icon="plus" type="primary">上传文件</van-button>
              <div class="color-eee font-size-12">请上传xls,xlsx格式文件</div>
            </van-uploader>
            <div class="width100" @click.stop="downloadFile(teamForm.travelerFileUrl)" v-if="teamForm.travelerFileUrl">
              <span>{{ teamForm.travelerFileName }}</span>
              <van-icon @click.stop="delFile" class="ml-20" name="cross" style="color: red" />
            </div>
          </template>
        </van-field> -->
        
      </van-cell-group>

      <!-- 产品信息 -->
      <van-cell-group inset title="产品信息">
        <van-field label="产品类型" label-align="top" required :rules="[{ required: true, message: '请选择产品类型' }]"
          v-model="teamForm.destineInfo">
          <template #input>
            <div class="width100 flex justify-content-between">
              <van-button round type="primary" :plain="item.plain" @click.stop="item.plain = !item.plain" size="small"
                style="min-width: 100px" v-for="(item, index) in productList" :disabled="item.disabled" :key="index">{{
                item.label }}</van-button>
            </div>
          </template>
        </van-field>
      </van-cell-group>

      <!-- 机票信息 -->
      <van-cell-group inset class="mt-10"
        v-if="teamForm?.destineInfoArr?.indexOf(0) > -1 || teamForm?.destineInfoArr?.indexOf(1) > -1">
        <van-cell class="text-left">
          <van-icon name="info-o" style="color: rgb(222 133 17); font-size: 16px" />
          <span v-if="teamForm?.destineInfoArr?.indexOf(0) > -1">
            10位成人(含)以上可申请团队，国内团队机票请至少在航班起飞72小时前确认出票。请注意：团队机票出票后可能无法退改，具体以航司申请确认为主，我们会在工作日9:00-17:30回复您！ </span>
          <span v-else>
            10位成人(含)以上可申请团队，国际团队机票请提至少提前15个工作日提交申请，请注意：团队机票出票后可能无法退改，具体以航司申请确认为主，我们会在工作日9:00-17:30回复您！ </span>
        </van-cell>
        <van-field required :rules="[{ required: true, message: '请选择航程类型' }]"
          v-model="teamForm.teamDestinePlaneTicket.voyageText" is-link readonly label="航程类型" placeholder="请选择"
          label-align="left" input-align="right" @click="openHcPicker">
          <template #input>
            {{ teamForm?.teamDestinePlaneTicket?.voyageType ? '往返' : '单程' }}
          </template>
        </van-field>

        <van-field required :rules="[{ required: true, message: '请输入出行人数' }]" label-width="110px" label="出行人数(成人)"
          placeholder="请输入" type="digit" v-model="teamForm.teamDestinePlaneTicket.travelerNum" label-align="left"
          input-align="right">
        </van-field>

        <van-field required :rules="[{ required: true, message: '请选择出行时段' }]"
          v-model="teamForm.teamDestinePlaneTicket.travelPriodText" is-link readonly label="出行时段" placeholder="请选择"
          @click="openSdPicker" label-align="left" input-align="right">
          <template #input>
            {{ teamForm?.teamDestinePlaneTicket?.travelPriod ? '下午' : '上午' }}
          </template>
        </van-field>

        <van-field required :rules="[{ required: true, message: '请输入需求' }]" label="其他需求"
          v-model="teamForm.teamDestinePlaneTicket.otherInfo" label-align="top" placeholder="请输入需求">
        </van-field>
      </van-cell-group>

      <!-- 酒店 -->
      <van-cell-group inset class="mt-10" v-if="teamForm?.destineInfoArr?.indexOf(2) > -1">
        <van-cell class="text-left">
          <van-icon name="info-o" style="color: rgb(222 133 17); font-size: 16px" />
          10间/夜以上(含)可申请团队价或连续长期入驻的可申请长包房价格。团队房/长包房客户提前7个工作日提交申请。我们会在工作日9:00-17:30回复，具体结果以申请为准。
        </van-cell>

        <van-field required :rules="[{ required: true, message: '请输入房间数量' }]" label-width="100px" label="房间数(大床)"
          placeholder="请输入" type="digit" v-model="teamForm.teamDestineHotel.kingRoomNum" label-align="left"
          input-align="right">
        </van-field>

        <van-field required :rules="[{ required: true, message: '请输入房间数量' }]" label-width="100px" label="房间数(双床)"
          placeholder="请输入" type="digit" v-model="teamForm.teamDestineHotel.doubleRoomNum" label-align="left"
          input-align="right">
        </van-field>

        <van-field required :rules="[{ required: true, message: '请输入需求' }]" label="其他需求"
          v-model="teamForm.teamDestineHotel.otherInfo" label-align="top" placeholder="请输入需求">
        </van-field>
      </van-cell-group>

      <!-- 联系人信息 -->
      <van-cell-group inset title="联系人信息">
        <van-field label="姓名" label-align="left" input-align="right" :rules="[{ required: true, message: '请选择联系人' }]"
          @click="showMainPersonList = true" readonly is-link placeholder="请选择联系人" v-model="teamForm.contactUserName">
        </van-field>
        <van-field label="电话" v-model="teamForm.contactUserPhone" :rules="[{ required: true, message: '请输入电话' }]"
          label-align="left" input-align="right" placeholder="联系电话">
        </van-field>
        <van-field label="邮箱" v-model="teamForm.contactUserMail" label-align="left" input-align="right"
          placeholder="邮箱">
        </van-field>
      </van-cell-group>

      <van-row style="margin: 20px 16px; padding-bottom: 30px" justify="space-between" flex>
        <van-col :span="11">
          <van-button block type="default" @click="save"> 保存 </van-button>
        </van-col>

        <van-col :span="11">
          <van-button block type="primary" :loading="submitLoading" native-type="submit"> 提交 </van-button>
        </van-col>
      </van-row>
    </van-form>
    <!-- 选择航程类型 -->
    <van-popup v-model:show="showHcPicker" position="bottom">
      <van-picker title="选择航程类型" :columns="hcColumns" @confirm="confirmHc" @cancel="showHcPicker = false" />
    </van-popup>

    <!-- 选择出行时段 -->
    <van-popup v-model:show="showSdPicker" position="bottom">
      <van-picker title="选择出行时段" :columns="sdColumns" @confirm="confirmSd" @cancel="showSdPicker = false" />
    </van-popup>

    <!-- 选择联系人 -->
    <van-popup v-model:show="showMainPersonList" position="bottom">
      <div class="out-pop" style="height: 100vh">
        <van-sticky>
          <div style="background: #fff;">
            <van-nav-bar title="选择联系人" left-arrow @click-left="closeMainPersonPop">
              <template #left>
                <van-icon name="arrow-left" color="#000" size="24" />
              </template>
              <template #right>
                <div class="color-main"></div>
              </template>
            </van-nav-bar>

               <van-search
                v-model="searchValue"
                placeholder="输入工号、姓名"
                autocomplete="off"
                @search="searchMainPerson"
              />
              <!-- <van-field @update:model-value="searchMainPerson" v-model="searchValue" style="background: #f6f7f9"
                placeholder="输入工号、姓名" /> -->
          </div>
        </van-sticky>
        <van-list v-model:loading="mainPersonLoading" :finished="mainPersonFinished"
          :finished-text="mainPersonList.length ? '没有更多了' : ''" @load="onLoadMainPerson">
          <div v-if="mainPersonList.length == 0 && mainPersonFinished" style="height: 66vh"
            class="flex align-items-center justify-content-center">
            <img class="img_empty" src="../../../assets/image/trip/empty.jpg" alt="" />
          </div>
          <template v-else>
            <van-cell v-for="(item, index) in mainPersonList" :key="index" @click="choseMainPerson(item)">
              <div class="flex align-items-center out-person-checkbox">
                <div :class="index % 3 == 0 ? 'blue' : 'yellow'"
                  class="mr-20 ml-10 flex align-items-center justify-content-center img-name">
                  {{ item?.nickName ? item?.nickName.slice(-2) : '未知' }}
                </div>
                <div class="main">
                  <div class="user-name color-main">{{ item.nickName }}({{ item.username }})</div>
                  <div class="phone">{{ item.enterpriseName || '未知' }}</div>
                </div>
              </div>
            </van-cell>
          </template>
        </van-list>
      </div>
    </van-popup>

    <!-- 城市选择 -->
    <van-popup v-model:show="showCityPicker"   position="bottom">
      <van-sticky :offset-top="0">
        <van-nav-bar title="城市选择可搜索" >
          <template #right>
            <van-icon @click="closeCityChosePop" name="cross" size="18" />
          </template>
        </van-nav-bar>
        <van-search autocomplete="off" :show-action="searchCityList.length > 0" @cancel="onCityClear" @search="onCitySearch" @clear="onCityClear" v-model="cityName" placeholder="请输入城市" />
      </van-sticky>
      
      <van-list
        v-if="searchCityList.length > 0"
        v-model:loading="cityLoading"
        :finished="cityFinished"
        finished-text="没有更多了"
        @load="onLoadCity"
      >
        <van-cell @click="chosedCityItem(item)" v-for="item in searchCityList" :key="item" :title="item.name" :value="`${item.continentsName}/${item.countryName}/${item.provinceName}`" />
      </van-list>

      <van-cascader v-else v-model="chosedCity" :show-header="false" :options="cityDict"
        :field-names="{ text: 'name', value: 'id', children: 'children' }" 
        @finish="finishCityChose" @change="onChange" />

    </van-popup>

    <!-- 时间选择 -->
    <van-popup v-model:show="showTimePicker" position="bottom">
      <van-date-picker title="选择日期" v-model="currentDate" :min-date="minDate" :max-date="maxDate" @confirm="confirmTime"
        @cancel="showTimePicker = false" />
    </van-popup>
  </div>
</template>

<script setup lang='ts'>
import {
  IUserListRequest,
  TCteateTeam,
  IUserInfo,
  ICity,
  ITripInfo,
  ICreatTrip,
  ITraveler,
  TTicket,
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { debounce, values } from 'lodash';
import { cityApi } from '@haierbusiness-front/apis';
import { userApi, tripApi, fileApi, download, teamApi, teamListApi } from '@haierbusiness-front/apis';
import { createVNode, onMounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';

import type { Ref } from 'vue';
import { Item } from 'ant-design-vue/es/menu';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { showSuccessToast, showFailToast, CascaderOption, showLoadingToast } from 'vant';

const router = getCurrentRouter()
const route = ref(getCurrentRoute());



const id = route.value?.query?.id;

const getDetail = (id: string) => {
  if (!id) {
    return
  }

  teamListApi.teamDetail(id).then(res => {
    teamForm.value = res
    teamForm.value?.destineInfo?.split(',')?.forEach(item =>
      productList.value[item].plain = false
    )

    if (!teamForm.value?.teamDestineHotel) {
      teamForm.value.teamDestineHotel = {}
    }

    if (!teamForm.value?.teamDestinePlaneTicket) {
      teamForm.value.teamDestinePlaneTicket = {}
    }
    if (!teamForm.value?.teamDestinePlaneTicket?.voyageType) {
      teamForm.value.teamDestinePlaneTicket.voyageType = 0
    }
    if (!teamForm.value?.teamDestinePlaneTicket?.travelPriod) {
      teamForm.value.teamDestinePlaneTicket.travelPriod = 0
    }
    teamForm.value.teamDestinePlaneTicket.voyageText = teamForm.value?.teamDestinePlaneTicket?.voyageType ? '往返' : '单程'
    teamForm.value.teamDestinePlaneTicket.travelPriodText = teamForm.value?.teamDestinePlaneTicket?.travelPriod ? '下午' : '上午'



    fileList.value = [{
      name: teamForm.value?.travelerFileName,
      url: teamForm.value?.travelerFileUrl,

    }]
  })
};

const store = applicationStore();



watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);

const { loginUser } = storeToRefs(store);

const productList = ref([
  {
    label: '国内机票',
    plain: true,
    value: 0,
    disabled: false,
  },
  {
    label: '国际机票',
    plain: true,
    value: 1,
    disabled: false,
  },
  {
    label: '酒店',
    plain: true,
    value: 2,
  },
]);
// 创建申请单请求参数
const teamForm = ref<TCteateTeam>({
  beginCityCode: '',
  beginCityName: '',

  endCityCode: '',
  endCityName: '',
  destineNo: '',
  gmtCreate: '',

  // 出差类型0因公1因私
  evectionType: 0,

  // 附件地址
  travelerFileUrl: '',

  // 产品类型
  destineInfoArr: [],
  teamDestinePlaneTicket: {
    //机票类型0国内1国际
    planeTicketType: 0,
    //航程类型0单程1往返
    voyageType: 0,
    voyageText: '单程',
    travelerNum: 1,
    //出行时间段0上午1下午
    travelPriod: 0,
    travelPriodText: '上午',
    //出差类型0因公1因私
    evectionType: undefined,
    //其他需求
    otherInfo: '',
  },

  teamDestineHotel: {},

  contactDeptName: loginUser.value?.departmentName || loginUser.value?.enterpriseName,
  contactUserCode: loginUser.value?.username, //联系人工号
  contactUserName: loginUser.value?.nickName, //联系人名称
  contactUserPhone: loginUser.value?.phone, //联系人电话
  contactUserMail: loginUser.value?.email, //联系人邮箱
});

// 文件上传
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const fileList = ref([]);

const afterRead = (options: any) => {

  const formData = new FormData();
  formData.append('file', options.file);
  teamApi
    .travelerImport(formData)
    .then((it) => {
      teamForm.value.travelerFileUrl = baseUrl + it.path;
      teamForm.value.travelerFileName = options.file.name;
    })
    .catch(() => {
      delFile()
    })
    .finally(() => {
      // uploadLoading.value = false;
    });
};

const downloadFile = (url: string) => {
  window.open(url);
};

const delFile = () => {
  teamForm.value.travelerFileUrl = '';
  teamForm.value.travelerFileName = '';
  fileList.value = [];
};







// 出行人相关
const searchValue = ref('');
const showMainPersonList = ref(false);
const mainPersonLoading = ref(false);
const mainPersonFinished = ref(false);
const chosedPerson = ref<ITraveler>({});
const mainPersonTotal = ref(0);

const mainPersonList = ref<Array<ITraveler>>([]);

const defaultParams: IUserListRequest = {
  pageNum: 0,
  pageSize: 20,
};

const params: Ref<IUserListRequest> = ref(defaultParams);

const onLoadMainPerson = () => {
  params.value.pageNum++;
  userApi.list(params.value).then((res) => {
    // 加载状态结束
    mainPersonLoading.value = false;
    mainPersonList.value = [...mainPersonList.value, ...res.records];
    mainPersonTotal.value = res.total ?? 0;
    // 数据全部加载完成
    if (mainPersonList.value.length >= mainPersonTotal.value) {
      mainPersonFinished.value = true;
    }
  });
};

const searchMainPerson = debounce((val: string) => {
  params.value.keyWord = val;
  params.value.pageNum = 0;
  params.value.pageSize = 20;
  mainPersonFinished.value = false;
  mainPersonList.value = [];
  onLoadMainPerson();
});

const closeMainPersonPop = () => {
  showMainPersonList.value = false;
};

const choseMainPerson = (item: ITraveler) => {
  teamForm.value.contactUserCode = item.username;
  teamForm.value.contactDeptName = item.enterpriseName || '';
  teamForm.value.contactUserPhone = item.phone;
  teamForm.value.contactUserName = item.nickName;
  teamForm.value.contactUserMail = item.email;

  showMainPersonList.value = false;
};

// 城市选择相关
const showCityPicker = ref<boolean>(false);
const cityDict = ref<Array<ICity>>([
  {
    name: '国际',
    id: '2112',
    children: [
      {
        key: 0,
        name: '国内热门',
        id: '1',
        children: []

      },

      {
        key: 1,
        name: '亚洲',
        id: '2',
        children: []
      },
      {
        key: 2,
        name: '美洲',
        id: '3',
        children: []
      },

      {
        key: 3,
        name: '欧洲',
        id: '4',
        children: []

      },
      {
        key: 4,
        name: '大洋洲',
        id: '5',
        children: []

      },
      {
        key: 5,
        name: '非洲',
        id: '6',
        children: []

      },

      {
        key: 6,
        name: '国际/中国港澳台热门',
        id: '7',
        children: []

      },

    ]
  },
  {
    name: '国内',
    id: '21122',
    children: []
  },
]);

// 城市选择相关
const cityName = ref('')
const cityLoading= ref<boolean>(false)
const cityFinished= ref<boolean>(false)

// 城市类型flag 0国内 1国际
const internationalFlag = ref<number>()

const cityPageNum = ref<number>(1)
const cityPageSize = ref<number>(10)
const searchCityList = ref([])

const onLoadCity = () => {

  const params = {
    name: cityName.value,
    pageSize: cityPageSize.value,
    pageNum: cityPageNum.value,
    level: 'city',
    internationalFlag: internationalFlag.value
  }
  cityApi.getCityList(params).then(res => {
    searchCityList.value = [...searchCityList.value, ...res.records]
    cityLoading.value = false;
    if (searchCityList.value.length >= res.total) {
      cityFinished.value = true
    } else {
      cityFinished.value = false
      cityPageNum.value++
    }
  })
}

const closeCityChosePop = () => {
  showCityPicker.value = false;
  cityName.value = ''
  onCityClear()
}

const onCitySearch = (val: string) => {

  searchCityList.value = []
  cityPageNum.value = 1
  onLoadCity()
}
const onCityClear = () => {
  cityPageNum.value = 1
  searchCityList.value = []
}  
const chosedCity = ref();
const cityChoseType = ref<string>('');

const showCityPop = (type: string, code: string | number) => {
  chosedCity.value = Number(code);

  cityChoseType.value = type;
  showCityPicker.value = true;
};

const chosedCityItem = (item) => {
  if (cityChoseType.value == 'begin') {
    teamForm.value.beginCityCode = item.id;
    teamForm.value.beginCityName = item.name;
  } else {
    teamForm.value.endCityCode = item.id;
    teamForm.value.endCityName = item.name;
  }
  onCityClear()
  cityName.value = ''
  showCityPicker.value = false
}

const onChange = ({ value, selectedOptions, tabIndex }) => {
      if (
        tabIndex === 1 &&
        selectedOptions[selectedOptions.length-1].children.length === 0
      ) {
        // 根据国际州id获取城市
        const params = {
            level: 'city',
            internationalFlag: 1,
            districtPopularId:value
          }
          cityApi.getCityList(params).then(res => {
            selectedOptions[selectedOptions.length-1].children = res.records
          })
        
      }
    };

const finishCityChose = ({ selectedOptions }: any) => {
  console.log('1111', selectedOptions);

  if (cityChoseType.value == 'begin') {
    teamForm.value.beginCityCode = selectedOptions[selectedOptions.length - 1].id;
    teamForm.value.beginCityName = selectedOptions[selectedOptions.length - 1].name;
  } else {
    teamForm.value.endCityCode = selectedOptions[selectedOptions.length - 1].id;
    teamForm.value.endCityName = selectedOptions[selectedOptions.length - 1].name;
  }

  showCityPicker.value = false;
};

// 时间选择
// 当前时间加十天
const tenDays = () => {
  // 创建一个新的Date对象，表示当前日期和时间
  const currentDate = new Date();

  // 使用setDate方法加上10天
  currentDate.setDate(currentDate.getDate() + 10);

  return currentDate
}

const showTimePicker = ref<boolean>(false);

const minDate = ref();
minDate.value = tenDays()
const maxDate = ref(new Date(2034, 0, 1));

const currentDate = ref<Array<string>>([]);

const choseTimeType = ref('');



const openTimePicker = (type: string) => {
  choseTimeType.value = type;
  currentDate.value = [];
  if (teamForm.value.beginDate) {
    const minDateArr = teamForm.value.beginDate.split('-');
    minDate.value = new Date(parseInt(minDateArr[0]), parseInt(minDateArr[1]) - 1, parseInt(minDateArr[2]));
  }
  if (teamForm.value.endDate) {
    const maxDateArr = teamForm.value.endDate.split('-');
    maxDate.value = new Date(parseInt(maxDateArr[0]), parseInt(maxDateArr[1]) - 1, parseInt(maxDateArr[2]));
  }

  if (type == 'begin') {
    minDate.value = tenDays();
    currentDate.value = teamForm.value.beginDate?.split('-');
  } else {
    maxDate.value = new Date(2034, 0, 1);
    currentDate.value = teamForm.value.endDate?.split('-');
  }

  showTimePicker.value = true;
};

const confirmTime = ({ selectedValues }: any) => {
  if (choseTimeType.value == 'begin') {
    teamForm.value.beginDate = selectedValues.join('-');
  } else {
    teamForm.value.endDate = selectedValues.join('-');
  }
  showTimePicker.value = false;
};

// 航程类型
const showHcPicker = ref(false);

const openHcPicker = () => {
  showHcPicker.value = true;
};
const hcColumns = ref([
  { text: '单程', value: 0 },
  { text: '往返', value: 1 },
]);

const confirmHc = ({ selectedOptions }: any) => {
  const tempObj: TTicket = {
    voyageType: selectedOptions[0].value,
    voyageText: selectedOptions[0].text,
  };

  teamForm.value.teamDestinePlaneTicket = { ...teamForm.value.teamDestinePlaneTicket, ...tempObj };

  showHcPicker.value = false;
};

// 出行时段
const showSdPicker = ref(false);

const openSdPicker = () => {
  showSdPicker.value = true;
};
const sdColumns = ref([
  { text: '上午', value: 0 },
  { text: '下午', value: 1 },
]);

const confirmSd = ({ selectedOptions }: any) => {
  const tempObj: TTicket = {
    travelPriod: selectedOptions[0].value,
    travelPriodText: selectedOptions[0].text,
  };

  teamForm.value.teamDestinePlaneTicket = { ...teamForm.value.teamDestinePlaneTicket, ...tempObj };

  showSdPicker.value = false;
};

// 递归将没有citycode的设置为adcode
const toAdcode = (list: any) => {
  list.forEach((item: any) => {
    if (item.citycode == '') {
      item.citycode = item.adcode;
    } else {
      toAdcode(item.districts);
    }
  });
};
// 递归将空数组设为null
const toNull = (list: any) => {
  list.forEach((item: any) => {
    if (item.districts.length == 0) {
      item.districts = null;
    } else {
      toNull(item.districts);
    }
  });
};

const save = () => {
  if (teamForm.value.id) {
    teamApi.updateTeam(teamForm.value).then((res) => {
      showSuccessToast('保存成功');
    });
  } else {
    teamApi.addTeam(teamForm.value).then((res) => {
      teamForm.value.id = res.id;
      teamForm.value.destineNo = res.destineNo;
      teamForm.value.gmtCreate = res.gmtCreate;
      showSuccessToast('保存成功');

    });
  }
};

const submitLoading = ref<boolean>(false)
const onSubmit = (values: any) => {
  console.log('submit', values);

  submitLoading.value = true


  teamApi.submitTeam(teamForm.value).then((res) => {
    console.log(999, res);

    router.push({ path: "/mobile/teamList" })
    submitLoading.value = false
  }).catch(() => {
    submitLoading.value = false
  })

};

onMounted(async () => {
  // 获取省市
  const res = await tripApi.district();
  // debugger
  // toAdcode(res.children);
  // toNull(res.children);
  cityDict.value[1].children = res.children;
});

watch(
  productList.value,
  (newValue) => {
    newValue[1].disabled = !newValue[0].plain;
    newValue[0].disabled = !newValue[1].plain;

    const tempObj: TTicket = {
      planeTicketType: newValue[0].disabled ? 1 : 0
    }

    teamForm.value.teamDestinePlaneTicket = { ...teamForm.value.teamDestinePlaneTicket, ...tempObj }

    teamForm.value.destineInfoArr = [];
    teamForm.value.destineInfo = ''
    newValue.forEach((item) => {
      if (!item.plain) {
        teamForm.value.destineInfoArr = [...teamForm.value.destineInfoArr, item.value];
      }
    });
    teamForm.value.destineInfo = teamForm.value.destineInfoArr.join(',');
  },
  { deep: true },
);
</script>

<style lang="less" scoped>
@import url(./components/mobile.less);
</style>