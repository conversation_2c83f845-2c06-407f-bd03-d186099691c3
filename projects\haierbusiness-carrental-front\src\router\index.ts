import { RouteRecordRaw } from 'vue-router';

const modules = import.meta.glob('/src/page/**/*.vue');

import { baseRouterConstructor } from '@haierbusiness-front/utils';

const routes = [{
    path: '/',
    redirect: '/ehaiEmpower/home'
},
{
    path: '/ehaiEmpower',
    redirect: '/ehaiEmpower/home',
    component: () => import('../page/index.vue'),
    children: [
        // 首页
        {
            path: '/ehaiEmpower/home',
            name: 'home',
            meta: {
                keepAlive: true //设置页面是否需要使用缓存
            },

            component: () => import('../page/ehaiEmpower/index.vue'),
        },
    ]
},


];
// const router = baseRouterConstructor("haierbusiness-portalIndex", modules, flag, undefined, routes)
const router = baseRouterConstructor("haierbusiness-carrental", modules, true, undefined, routes)

export default router;
