import { RouteRecordRaw } from 'vue-router';

const modules = import.meta.glob('/src/page/**/*.vue');

import { baseRouterConstructor } from '@haierbusiness-front/utils';

const whiteList = ['/travel/infoDetail', '/travel/lifeDetail', '/travel/bannerDetail', '/travel/adDetail', '/card-order/excitation', '/card-order/recharge']
let flag = true
const currentUrl = location.hash
const index = currentUrl.lastIndexOf('?')
const url = currentUrl.substring(1, index)

let routes:RouteRecordRaw[] = []

if(whiteList.indexOf(url)> -1) {
    flag = false
    routes = [
        {
            path: '/travel/infoDetail',
            component: () => import("../page/travel/infoDetail.vue")
        },
        {
            path: '/travel/bannerDetail',
            component: () => import('../page/travel/bannerDetail.vue')
        },
        {
            path: '/travel/adDetail',
            component: () => import('../page/travel/adDetail.vue')
        },
        {
            path: '/travel/lifeDetail',
            component: () => import('../page/travel/lifeDetail.vue')
        }
    ];
} 

const router = baseRouterConstructor("haierbusiness-portalIndex", modules, flag, undefined, routes)

export default router;
