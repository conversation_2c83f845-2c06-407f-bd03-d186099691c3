<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  message,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined,UpOutlined,DownOutlined } from '@ant-design/icons-vue';
import { specialCompetenciesApi } from '@haierbusiness-front/apis';
import {
  ISpecialCompetenciesFilter,
  ISpecialCompetencies,
  SpecialpermissionStatusMap,
  SpecialpermissionStatusEnum
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted, reactive, h, nextTick } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import EditDialog from './edit-dialog.vue'
import router from '../../router'
import type { MenuItemType, MenuInfo } from 'ant-design-vue/lib/menu/src/interface';
import Actions from '@haierbusiness-front/components/actions/Actions.vue';
// const router = useRouter()

const currentRouter = ref()

//生效时间
const operantTime = ref<[Dayjs, Dayjs]>()
watch(() => operantTime.value, (n: any) => {
  if (n) {
    searchParam.value.operantTimeStart = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
    searchParam.value.operantTimeEnd = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
  } else {
    searchParam.value.operantTimeStart = undefined
    searchParam.value.operantTimeEnd = undefined
  }
});

//申请时间
const gmtCreate = ref<[Dayjs, Dayjs]>()
watch(() => gmtCreate.value, (n: any) => {
  if (n) {
    searchParam.value.gmtCreateTimeStart = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
    searchParam.value.gmtCreateTimeEnd = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
  } else {
    searchParam.value.gmtCreateTimeStart = undefined
    searchParam.value.gmtCreateTimeEnd = undefined
  }
});



const columns: ColumnType[] = [
  {
    title: '订单号',
    dataIndex: 'mainCode',
    width: '250px',
    align: 'center'
  },
  {
    title: '会议名称',
    dataIndex: 'miceName',
    width: '220px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '特殊权限',
    dataIndex: 'type',
    width: '120px',
    align: 'center',
    ellipsis: true,

  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '80px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) =>
      genderOptions.find(item => item.value === text)?.label || '-'
  },
  {
    title: '经办人',
    dataIndex: 'operatorName',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '开通人',
    dataIndex: 'openName',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '生效时间',
    dataIndex: 'onTime',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '250px',
    fixed: 'right',
    align: 'center'
  },
];


// 创建时间范围
const createTimeRange = ref<[Dayjs, Dayjs]>();
watch(() => createTimeRange.value, (n: any) => {
  if (n) {
    searchParam.value.againTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00');
    searchParam.value.endTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59');
  } else {
    searchParam.value.againTime = undefined;
    searchParam.value.endTime = undefined;
  }
});
// 创建时间范围
const imPowerTime = ref<[Dayjs, Dayjs]>();
watch(() => imPowerTime.value, (n: any) => {
  if (n) {
    searchParam.value.imPowerTimeStart = dayjs(n[0]).format('YYYY-MM-DD 00:00:00');
    searchParam.value.imPowerTimeEnd = dayjs(n[1]).format('YYYY-MM-DD 23:59:59');
  } else {
    searchParam.value.imPowerTimeStart = undefined;
    searchParam.value.imPowerTimeEnd = undefined;
  }
});

const searchParam = ref<ISpecialCompetenciesFilter>({

})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(specialCompetenciesApi.list);

const reset = () => {
  searchParam.value = {}
  gmtCreate.value = undefined
  operantTime.value = undefined
  imPowerTime.value = undefined

  console.log('重置后的搜索参数:', searchParam.value);

  // 使用nextTick确保DOM更新后再调用接口
  nextTick(() => {
    // 重置后立即查询
    const params = {
      pageNum: 1,
      pageSize: 10
    };
    console.log('重置后调用接口参数:', params);
    listApiRun(params);
  });
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  // 确保使用最新的搜索参数和过滤参数，优先使用filterInputs中的非空值
  const params = {
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  };

  console.log('最终查询参数:', params);
  listApiRun(params);
};
const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } =
  useEditDialog<ISpecialCompetencies, ISpecialCompetencies>(specialCompetenciesApi, "特殊权限确认列表", () => listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum || 1,
    pageSize: data.value?.pageSize || 10,
  }))

const thisHandleEdit = (item: ISpecialCompetencies) => {
  // 跳转到详情页，传递ID和编辑模式参数
  currentRouter.value.push({
    path: '/bidman/specialCompetencies/details',
    query: {
      id: item.id,
    }
  })
}


// 删除
const { handleDelete } = useDelete(specialCompetenciesApi, () => listApiRun({
  ...searchParam.value,
  pageNum: data.value?.pageNum,
  pageSize: data.value?.pageSize,
}))

const genderOptions = [
  { label: '取消', value: 0 },
  { label: '审批中', value: 10 },
  { label: '审批通过', value: 20 },
  { label: '驳回', value: 30 },
  { label: '撤回', value: 40 },
]

const handleRevoke = async (record: any) => {
  console.log('撤销操作:', record);
  // 跳转到详情页，传递ID和编辑模式参数
  const processUrl = `https://businessmanagement-test.haier.net/hbweb/process/?code=${record.approvalCode}#/details`;
  window.open(processUrl, '_blank');
}

// 处理菜单点击事件
const handleMenuClick = (record: any, e: MenuInfo) => {
  const key = e.key as string;
  switch (key) {
    case 'revoke':
    if (record.state == 10) {
        handleRevoke(record);
      }
  }
};

// 计算菜单选项
const getMenuOptions = (record: any) => {
  const options: MenuItemType[] = [];
  if (record.state === 10) {
    options.push({
      key: 'revoke',
      label: '审批查看',
    });
  }

  return options;
};

//显示更多
const hasMore = ref(false)
const isExpanded = ref(false)
const toggleExpand = () => {
  hasMore.value = !hasMore.value
  isExpanded.value = !isExpanded.value
}

onMounted(async () => {
  currentRouter.value = await router
  listApiRun({
    pageNum: 1,
    pageSize: 10
  })
})

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="adviser">订单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.mainCode" placeholder="请输入订单号" style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="gender">会议名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.miceName" placeholder="请输入会议名称" style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="creator">特殊权限：</label>
          </h-col>
          <h-col :span="4">
            <h-select id="type" v-model:value="searchParam.type" placeholder="请选择类型" class="full-width" allow-clear
              style="margin-top: 10px;width: 100%;">
              <h-select-option v-for="(value, key) in SpecialpermissionStatusMap" :key="key" :value="Number(key)">
                {{ value }}
              </h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="creator">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchParam.state" placeholder="请选择状态" style="width: 100%" allow-clear>
              <h-select-option v-for="option in genderOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="adviser">经办人：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.operatorName" placeholder="请输入经办人" style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="gender">开通人：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.openName" placeholder="请输入开通人" style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="creator">生效时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="operantTime" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="creator">申请时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="gmtCreate" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>
        </h-row>
        <!-- 第三行 -->
        <h-row v-if="hasMore" :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="creator">授权时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="imPowerTime" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;margin-top: 20px;">
          <h-col :span="24" style="text-align: right;">
            <h-button @click="toggleExpand" type="link">
              <UpOutlined v-if="isExpanded" />
              <DownOutlined v-else />
              高级搜索
              <h-icon :type="isExpanded ? 'up' : 'down'" />
            </h-button>
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
        <!-- <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
            <h-button type="primary" @click="handleCreate()">
              <PlusOutlined /> 新增
            </h-button>
          </h-col>
        </h-row> -->
      </h-col>
      <h-col :span="24" style="margin-top: 20px;">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="thisHandleEdit(record)">查看</h-button>
              <h-button type="link" @click="handleDelete(record.id)">删除</h-button>
              <Actions :menu-options="getMenuOptions(record)" :on-menu-click="(e) => handleMenuClick(record, e)"
                v-if="getMenuOptions(record).length > 0"></Actions>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

    <div v-if="visible">
      <edit-dialog :show="visible" :data="editData" @cancel="onDialogClose" @ok="handleOk">
      </edit-dialog>
    </div>

  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}
</style>
