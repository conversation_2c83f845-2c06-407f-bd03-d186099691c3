<template>
  <div class="order-list" style="min-height: 100vh;">
    <van-sticky :offset-top="0.1">
      <van-tabs v-model:active="defaultParams.orderStatusNew">
        <van-tab v-for="(item, index) in tabList" :name="item.value" :title="item.label" :key="index"> </van-tab>
      </van-tabs>

      <van-search class="list-search" shape="round" @search="reSearch" v-model="defaultParams.orderCode"
        @clear="reSearch" @click-left-icon="reSearch" :clearable="true" show-action placeholder="搜索我的订单">
        <template #action>
          <van-dropdown-menu class="mr-10" z-index="1111"  :close-on-click-outside="false" ref="menuRef">
            <!-- #1989fa -->
            <van-dropdown-item  ref="itemRef"
              :title-class="isChosedItem ? 'van-dropdown-menu__title--active' : ''">
              <template #title>
                <van-icon name="filter-o" />
                筛选
              </template>

              <van-cell-group title="按申请时间选择">
                <van-cell>
                  <template #value>
                    <!-- 快速选择时间 -->
                    <van-radio-group v-model="defaultParams.timeType">
                      <van-row justify="space-between">
                        <van-radio name="week" class="my-radio">
                          <template #icon="{ checked }">
                            <van-button class="btn-com" size="small" round
                              :type="checked ? 'primary' : 'default'">最近一周</van-button>
                          </template>
                        </van-radio>

                        <van-radio name="month" class="my-radio">
                          <template #icon="{ checked }">
                            <van-button class="btn-com" size="small" round
                              :type="checked ? 'primary' : 'default'">最近一个月</van-button>
                          </template>
                        </van-radio>

                        <van-radio name="year" class="my-radio">
                          <template #icon="{ checked }">
                            <van-button class="btn-com" size="small" round
                              :type="checked ? 'primary' : 'default'">最近一年</van-button>
                          </template>
                        </van-radio>
                      </van-row>
                    </van-radio-group>

                    <!-- 具体时间选择 -->
                    <van-row class="mt-10" justify="space-between">
                      <van-col :span="10">
                        <div  @click="openTimePicker('begin')" class="my-input">
                          <span>{{ defaultParams?.applicationTimes[0] || '选择起始时间' }}</span>
                          <van-icon v-if="defaultParams?.applicationTimes[0]" @click.stop="defaultParams.applicationTimes[0] = '';defaultParams.timeType = '';" name="close" />
                        </div>
                        <!-- <van-field class="input-border" @click="openTimePicker('begin')" clear-trigger="always"
                          v-model="defaultParams.applicationTimes[0]" clearable placeholder="选择起始时间"  /> -->
                      </van-col>
                      <van-col :span="4">
                        <van-divider :style="{ height: '100%', borderColor: '#000', padding: '0 16px', margin: '0' }" />
                      </van-col>
                      <van-col :span="10">
                        <div  @click="openTimePicker('end')" class="my-input">
                          <span>{{ defaultParams?.applicationTimes[1] || '选择终止时间' }}</span>
                          <van-icon v-if="defaultParams?.applicationTimes[1]" @click.stop="defaultParams.applicationTimes[1] = '';defaultParams.timeType = '';" name="close" />
                        </div>
                        <!-- <van-field class="input-border" @click="openTimePicker('end')" clear-trigger="always"
                          v-model="defaultParams.applicationTimes[1]" clearable placeholder="选择终止时间" /> -->
                      </van-col>
                    </van-row>
                  </template>
                </van-cell>
              </van-cell-group>
              <van-cell-group title="按就餐类型选择">
                <van-radio-group v-model="defaultParams.sceneType">
                  <van-cell>
                    <van-row justify="space-between">
                      <van-radio name="1" class="my-radio">
                        <template #icon="{ checked }">
                          <van-button class="btn-com" size="small" round
                            :type="checked ? 'primary' : 'default'">宴请</van-button>
                        </template>
                      </van-radio>
                      <van-radio name="2" class="my-radio">
                        <template #icon="{ checked }">
                          <van-button class="btn-com" size="small" round
                            :type="checked ? 'primary' : 'default'">外卖</van-button>
                        </template>
                      </van-radio>


                    </van-row>
                  </van-cell>
                </van-radio-group>
              </van-cell-group>

              <van-cell-group title="按有效时间选择">
                <van-cell>
                  <template #value>
                    <!-- 快速选择时间 -->
                    <van-radio-group v-model="defaultParams.yxTimeType">
                      <van-row justify="space-between">
                        <van-radio name="week" class="my-radio">
                          <template #icon="{ checked }">
                            <van-button class="btn-com" size="small" round
                              :type="checked ? 'primary' : 'default'">最近一周</van-button>
                          </template>
                        </van-radio>

                        <van-radio name="month" class="my-radio">
                          <template #icon="{ checked }">
                            <van-button class="btn-com" size="small" round
                              :type="checked ? 'primary' : 'default'">最近一个月</van-button>
                          </template>
                        </van-radio>

                        <van-radio name="year" class="my-radio">
                          <template #icon="{ checked }">
                            <van-button class="btn-com" size="small" round
                              :type="checked ? 'primary' : 'default'">最近一年</van-button>
                          </template>
                        </van-radio>
                      </van-row>
                    </van-radio-group>

                    <!-- 具体时间选择 -->
                    <van-row class="mt-10" justify="space-between">
                      <van-col :span="10">
                        
                        <div  @click="openYxTimePicker('begin')" class="my-input">
                          <span>{{ defaultParams?.estimatedMealTimeEnd[0] || '选择起始时间' }}</span>
                          <van-icon v-if="defaultParams?.estimatedMealTimeEnd[0]" @click.stop="defaultParams.estimatedMealTimeEnd[0] = '';defaultParams.yxTimeType = '';" name="close" />
                        </div>
                        
                        <!-- <van-field class="input-border"  @click="openYxTimePicker('begin')" clear-trigger="always" clearable
                          v-model="defaultParams.estimatedMealTimeEnd[0]" placeholder="选择起始时间" /> -->
                      </van-col>
                      <van-col :span="4">
                        <van-divider :style="{ height: '100%', borderColor: '#000', padding: '0 16px', margin: '0' }" />
                      </van-col>
                      <van-col :span="10">
                        <div  @click="openYxTimePicker('end')" class="my-input">
                          <span>{{ defaultParams?.estimatedMealTimeEnd[1] || '选择终止时间' }}</span>
                          <van-icon v-if="defaultParams?.estimatedMealTimeEnd[1]" @click.stop="defaultParams.estimatedMealTimeEnd[1] = ''; defaultParams.yxTimeType = '';" name="close" />
                        </div>
                        <!-- <van-field class="input-border"  @click="openYxTimePicker('end')" clear-trigger="always" clearable
                          v-model="defaultParams.estimatedMealTimeEnd[1]" placeholder="选择终止时间" /> -->
                      </van-col>
                    </van-row>
                  </template>
                </van-cell>
              </van-cell-group>
              <van-cell-group title="按商家查询">
                <van-search shape="round" v-model="defaultParams.restaurantName" :clearable="true" left-icon=""
                  placeholder="搜索商家名称" />
              </van-cell-group>


              <van-sticky position="bottom">
                <van-row justify="space-around" style="padding: 15px 0">
                  <van-button class="list-search-btn" size="small" type="default" round @click="reSet"> 清空 </van-button>
                  <van-button class="list-search-btn" size="small" type="primary" round @click="reSearch">
                    确认
                  </van-button>
                </van-row>
              </van-sticky>

              <!-- <a-affix :offset-bottom="0">
                <van-cell>
                  <van-row class="width100" justify="space-between">
                    <van-button round style="width: 48%" >重置</van-button>
                    <van-button round style="width: 48%" type="primary" >确定</van-button>
                  </van-row>
                </van-cell>
              </a-affix> -->

             
                
              
            </van-dropdown-item>
          </van-dropdown-menu>
        </template>
      </van-search>
    </van-sticky>


    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list  v-model:loading="orderLoading" :finished="orderFinished" :finished-text="orderList.length ? '没有更多了' : ''"
        @load="loadorderList" class="van-list-box empty-min-height">
        <div class="order-item flex " v-for="(item, index) in orderList" :key="index" @click.stop="goToDetail(item?.id)">
          <div class="order-item-right flex ">
            <div class="order-content cell-group-shadow mb-8">
              <van-cell-group style="background-color: rgba(0,0,0,0);">
                <van-cell > 
                  <template #title>
                    <div class="flex align-items-center justify-content-between ">
                      <div class="flex align-items-center mr-10">
                        <img v-if="item?.sceneType" class="icon-size mr-5" :src="item?.sceneType == 1 ? yqIcon : item?.sceneType == 2 ? wmIcon : ''" />
                        <span class="font-size-10 font-weight-600">{{ item?.orderCode }}</span>
                      </div>
                      <div class="width-80 font-size-10">
                        <van-text-ellipsis class="max-width-80" :content="item?.restaurantName" />
                      </div>
                    </div>
                  </template>
                </van-cell>

                <van-cell > 
                  <template #title>
                    <div class="flex  flex-column font-size-10 list-item-box" style="position:relative">
                      <div>经办人:{{loginUser?.nickName}}</div>
                      <div>预算金额:{{`${item.budgetAmount}元`}}</div>
                      <div>申请时间:{{item?.applicationTime}}</div>
                      <div>预计就餐日期:<span v-if="item?.estimatedMealTimeStart && item?.estimatedMealTimeEnd">{{ item?.estimatedMealTimeStart?.substring(0, 10) }} 至 {{ item?.estimatedMealTimeEnd?.substring(0, 10) }}</span></div>
                      <div style="position: absolute; top: 5px; right: 0;" :style="{ color:BanquetStateTagColorMap[item.orderStatus] }" >{{
                      item.orderStatus == 40 ? (item.orderEffective != 1 ? BanquetStatusEnumMobile[item.orderStatus] + '(未生效)' : BanquetStatusEnumMobile[item.orderStatus]) : BanquetStatusEnumMobile[item.orderStatus]
                      }}</div>
                    </div>
                  </template>
                </van-cell>

                
                <van-cell class="my-cell">
                  <template #value>
                    <div class="apply-btns">
                      <van-button plain round type="primary" size="small" :loading="approvalIndex == index"
                        v-if="item.orderStatus == 30 || item.orderStatus == 40 || item.orderStatus == 50"
                        @click.stop="goToApproval(item?.processCode, index)">审批信息</van-button>
                      <!-- <van-button plain round type="primary" size="small" v-if="item.orderStatus != 10"
                        @click.stop="goToDetail(item?.id)">详情</van-button> -->
                      <van-button plain round type="primary" size="small" :loading="budgetIndex == index"
                        v-if="item.orderStatus == 20 && item.orderEffective == 0"
                        @click.stop="goPay(item?.id, index)">申请预算</van-button>
                      <van-button plain round type="primary" size="small" :loading="payIndex == index"
                        v-if="(item.orderStatus == 40 || item.orderStatus == 50) && item.orderEffective == 1"
                        @click.stop="goTOMt(item, index)">去使用</van-button>
                      <van-button type="primary" round size="small" :loading="closeIndex == index"
                        v-if="(item.orderStatus == 20 || item.orderStatus == 30 || item.orderStatus == 40 || item.orderStatus == 50)"
                        @click.stop="applyClose(item, index)">关闭订单</van-button>
                      <van-button type="primary" round size="small" v-if="item.orderStatus == 10"
                        @click.stop="goToBook(item?.id)">继续提交</van-button>
                      <van-button type="primary" round size="small" v-if="item.orderStatus == 70 || item.orderStatus == 40"
                        @click.stop="goToBook(item?.id, 'more')">再来一单</van-button>
                    </div>
                  </template>
                </van-cell>


              </van-cell-group>
            </div>
          </div>
        </div>
        <van-empty  v-if="!orderLoading && orderList.length == 0" description="暂无数据" />
      </van-list>
    </van-pull-refresh>


    <van-sticky  position="bottom" :offset-bottom="0.1">
      <div class="bottom-order-btn" style="background: #fafbfd;">
        <van-button class="btn" style="width: 75% !important;" round type="primary"
          @click="goToBook2">创建就餐申请</van-button>
      </div>
    </van-sticky>

    <!-- 申请时间选择 -->
    <van-popup @click.stop v-model:show="showTimePicker" :close-on-click-overlay="false" position="bottom">
      <van-date-picker title="选择日期" v-model="currentDate" :min-date="minDate" :max-date="maxDate" @confirm="confirmTime"
        @cancel="showTimePicker = false" />
    </van-popup>
    <!-- 有效时间选择 -->
    <van-popup @click.stop v-model:show="showYxTimePicker" :close-on-click-overlay="false" position="bottom">
      <van-date-picker title="选择日期" v-model="currentDate" :min-date="minDate" :max-date="maxDate"
        @confirm="confirmYxTime" @cancel="showYxTimePicker = false" />
    </van-popup>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onActivated, computed, onMounted } from 'vue';

import {
  IUserListRequest,
  ROrderPayMentStateEnum,
  ROrderPayMentStateEnumColor,
  ROrderApprovalStateEnum,
  ROrderApprovalStateEnumColor,
  ROrderStateEnum,
  ROrderStateEnumColor,
  BanquetStatusEnumMobile,
  BanquetStateTagColorMap,
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { restaurantApi, banquetApi } from '@haierbusiness-front/apis';
import { BApplyListReq, BApplyListRes, BApplyListRecord, BanquetApplicationTypeEnum } from '@haierbusiness-front/common-libs';
import type { Ref } from 'vue';

import dayjs from 'dayjs';

import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { showConfirmDialog, showSuccessToast,showLoadingToast, showFailToast } from 'vant';

const store = applicationStore();

const { loginUser } = storeToRefs(store);

const router = getCurrentRouter();
const route = ref();


const mathNumber = (num1: number, num2: number) => {
  if (!num1 && !num2) {
    return 0
  }
  return numberCalculate(num1, num2, '-')
}

/** 封装一个公共方法numberCalculate() 用于浮点数运算 */
//num1 num2传入两个值 symbol +-*/符号
const numberCalculate = (num1: number, num2: number, symbol: string) => {
  var str1 = num1.toString(), str2 = num2.toString(), result, str1Length, str2Length
  try {
    //获取小数点后的精度
    str1Length = str1.split('.')[1].length
  }
  catch (error) {
    //解决整数没有小数点方法
    str1Length = 0
  }
  try {
    str2Length = str2.split('.')[1].length
  } catch (error) {
    str2Length = 0
  }
  // 取两个数的最小精度，即小数点后数字的最大长度
  var maxLen = Math.max(str1Length, str2Length)
  // step将两个数都转化为整数至少小数点后移多少位
  var step = Math.pow(10, maxLen)

  switch (symbol) {
    case "+":
      // toFixed()根据最小精度截取运算结果
      result = ((num1 * step + num2 * step) / step).toFixed(maxLen)
      break;
    case "-":
      result = ((num1 * step - num2 * step) / step).toFixed(maxLen)
      break;
    case "*":
      result = (((num1 * step) * (num2 * step)) / step / step).toFixed(maxLen)
      break;
    case "/":
      result = ((num1 * step) / (num2 * step)).toFixed(maxLen)
      break;
    default:
      break;
  }
  // 由于toFixed方法返回结果是字符串，还需要转回number输出
  return Number(result)
}

const goBack = () => {
  router.back(-1);
};

// 跳转申请页
const goToBook2 = () => {
  router.push({
    name: 'book'
  })
}

const wmIcon = new URL('@/assets/image/banquet/order/order-wm.png', import.meta.url).href
const yqIcon = new URL('@/assets/image/banquet/order/order-yq.png', import.meta.url).href

// tab
const tabList = [
  {
    label: '全部订单',
    value: '',
  },
  {
    label: '草稿',
    value: '10',
  },
  {
    label: '待占预算',
    value: '15',
  },
  {
    label: '审批中',
    value: '20',
  },
  {
    label: '待生效',
    value: '25',
  },
  {
    label: '已生效',
    value: '30',
  },
  {
    label: '已关闭',
    value: '40',
  },
];

const isChosedItem = computed(() => {
  if (defaultParams.value?.applicationTimes?.length > 0 || defaultParams.value?.estimatedMealTimeEnd?.length > 0 || defaultParams.value?.restaurantName || defaultParams.value?.sceneType) {
    return true
  }
  return false
})

// 下拉刷新
const refreshing = ref(false);

const onRefresh = () => {
      // 清空列表数据
      orderFinished.value = false;
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      orderLoading.value = true;
      defaultParams.value.pageNum = 0;
      loadorderList();
    };

// 订单列表
const orderLoading = ref<boolean>(false);
const orderFinished = ref<boolean>(false);
const orderList = ref<Array<BApplyListRecord>>([])
const orderTotal = ref<number>(0);
const defaultParams = ref<BApplyListReq>({
  timeType: '',
  yxTimeType: '',
  orderCode: '',
  pageNum: 0,
  pageSize: 20,
  orderStatusNew: '', // 订单状态
  sceneType: '', // 支付方式
  applicationTimes: [],  // 申请时间
  estimatedMealTimeEnd: [], // 有效时间
  approveStatus: '',
  orderStatus: '',
  orderEffective: '',
});


// 订单按钮功能
// 去支付
const budgetLoading = ref(false)
const budgetIndex = ref<number>()
const goPay = (id: number, index:number) => {
  budgetIndex.value = index
  const payParam = {
    id
  }
  banquetApi.goPay(payParam).then((res:any) => {
    console.log(res);
    window.location.href = res
    setTimeout(() => {
      budgetIndex.value = -1
    }, 2000);
  }).catch((err:any) => {
    setTimeout(() => {
      budgetIndex.value = -1
    }, 2000);
  })
}

const payIndex = ref<number>()
// 跳转美团餐厅支付
const goTOMt = (item: any, index:number) => {
  payIndex.value = index
  // 如果预选餐厅根据id 获取 budgetKey
  if (item.restaurantId) {
    getRestaurantDetail(item.restaurantId).then(res => {
      const params = {
        type: item.sceneType == 1 ? 'STAFF_INFO' : 'STAFF_WM',
        bizParam: {
          budgetKey: res.budgetKey,
          restaurantType: item.restaurantId ? 2 : 1,
          restaurantId: item.restaurantId,
          repastApplyExtraJson: {
            applyNo: item.mtApplyCode,
            externalApplyNo: item.orderCode
          },
        }
      }
      banquetApi.clientInvokeLogin(params).then(url => {
        window.location.href = url
        setTimeout(() => {
          payIndex.value = -1
        }, 3000);
      }).catch(e => {
        setTimeout(() => {
          payIndex.value = -1
        }, 3000);
      })
    }).catch(err => {
      setTimeout(() => {
          payIndex.value = -1
        }, 3000);
    })
  } else {
    const params = {
      type: item.sceneType == 1 ? 'STAFF_INFO' : 'STAFF_WM',
      bizParam: {
        repastApplyExtraJson: {
          applyNo: item.mtApplyCode,
          externalApplyNo: item.orderCode
        },
      }
    }
    banquetApi.clientInvokeLogin(params).then(url => {
      window.location.href = url
      setTimeout(() => {
          payIndex.value = -1
        }, 3000);
    }).catch(err => {
      setTimeout(() => {
          payIndex.value = -1
        }, 3000);
    })
  }



}

// 根据restaurantId查询详情
const getRestaurantDetail = (id: string) => {

  return banquetApi.getRestaurant({
    restaurantId: id
  })
}

const closeLoading = ref(false)
const closeIndex =ref<number>()
// 关闭订单
const applyClose = (item: any, index:number) => {
  closeIndex.value = index
  const payParam = {
    id: item.id
  }
  banquetApi.applyClose(payParam).then(res => {
    // 如果是宴请,并且是财智云预算,需要调用两次关闭,间隔2秒
    if(item.budgetSourceCode == 'CZY' && item.sceneType == 1) {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
      });
      setTimeout(() => {
        banquetApi.applyClose(payParam).then(res2 => {
          showSuccessToast('关闭成功!')
          setTimeout(() => {
            closeIndex.value = -1
          }, 1000);
          reSearch()

        }).catch(err2 => {
          closeIndex.value = -1
        })

      }, 2000);


    }else {
      showSuccessToast('关闭成功!')
      setTimeout(() => {
        closeIndex.value = -1
      }, 1000);
      reSearch()
    }

  }).catch(err => {
    closeIndex.value = -1
  })
}

// 再来一单
// 继续提交 
const goToBook = (id: number, type?: string) => {
  router.push({
    path: '/banquet/book',
    query: {
      id: id,
      type: type || ''
    }
  })
}

// 审批
const businessList = import.meta.env.VITE_BUSINESS_URL;

const approvalIndex = ref<number>()
const goToApproval = (processCode: number | string, index:number) => {
  
  if (!processCode) {
    showFailToast('暂无审批记录!');
    return
  }
  approvalIndex.value = index
  window.location.href = `${businessList}hbweb/process/?code=${processCode}#/details`
  
  setTimeout(() => {
    approvalIndex.value = -1
  }, 2000);
  // router.push({ path: '/banquet/apply/approval', query: { processCode: processCode } });
};
// 详情
const goToDetail = (id: string | number) => {
  router.push({ path: '/banquet/apply/detail', query: { id: id } });
};


const loadorderList = () => {
  defaultParams.value.pageNum++;
  banquetApi.applyList(defaultParams.value).then((res) => {
    if (refreshing.value) {
      orderList.value = [];
      refreshing.value = false;
    }
    // 加载状态结束
    orderLoading.value = false;
    orderList.value = [...orderList.value, ...res.records];
    orderTotal.value = res.total ?? 0;
    // 数据全部加载完成
    if (orderList.value.length >= orderTotal.value) {
      orderFinished.value = true;
    }
  });
};

onMounted(() => {
  route.value = getCurrentRoute()
  const routeStatus = route.value?.query?.status
  defaultParams.value.orderStatusNew = routeStatus
})

watch(
  () => defaultParams.value.timeType,
  (val: string | undefined) => {
    switch (val) {
      case 'week':
        defaultParams.value.applicationTimes[1] = dayjs().format('YYYY-MM-DD');
        defaultParams.value.applicationTimes[0] = dayjs().subtract(7, 'day').format('YYYY-MM-DD');
        break;

      case 'month':
        defaultParams.value.applicationTimes[1] = dayjs().format('YYYY-MM-DD');
        defaultParams.value.applicationTimes[0] = dayjs().subtract(1, 'month').format('YYYY-MM-DD');
        break;
      case 'year':
        defaultParams.value.applicationTimes[1] = dayjs().format('YYYY-MM-DD');
        defaultParams.value.applicationTimes[0] = dayjs().subtract(1, 'year').format('YYYY-MM-DD');
        break;

      default:
        break;
    }
  },
);

watch(
  () => defaultParams.value.yxTimeType,
  (val: string | undefined) => {
    switch (val) {
      case 'week':
        defaultParams.value.estimatedMealTimeEnd[1] = dayjs().format('YYYY-MM-DD');
        defaultParams.value.estimatedMealTimeEnd[0] = dayjs().subtract(7, 'day').format('YYYY-MM-DD');
        break;

      case 'month':
        defaultParams.value.estimatedMealTimeEnd[1] = dayjs().format('YYYY-MM-DD');
        defaultParams.value.estimatedMealTimeEnd[0] = dayjs().subtract(1, 'month').format('YYYY-MM-DD');
        break;
      case 'year':
        defaultParams.value.estimatedMealTimeEnd[1] = dayjs().format('YYYY-MM-DD');
        defaultParams.value.estimatedMealTimeEnd[0] = dayjs().subtract(1, 'year').format('YYYY-MM-DD');
        break;

      default:
        break;
    }
  },
);




watch(
  () => defaultParams.value.orderStatusNew,
  (newValue, oldValue) => {
    switch (newValue) {
      case '':
        defaultParams.value.approveStatus = ''
        defaultParams.value.orderStatus = ''
        defaultParams.value.orderEffective = ''
        break;
      case '10':
        defaultParams.value.approveStatus = ''
        defaultParams.value.orderStatus = 10
        defaultParams.value.orderEffective = ''
        break;

      case '15':
        defaultParams.value.approveStatus = ''
        defaultParams.value.orderStatus = 20
        defaultParams.value.orderEffective = ''
        break;
      case '20':

        defaultParams.value.approveStatus = 0
        defaultParams.value.orderStatus = ''
        defaultParams.value.orderEffective = ''
        break;
      case '25':
        defaultParams.value.approveStatus = ''
        defaultParams.value.orderStatus = '40'
        defaultParams.value.orderEffective = 0
        break;
      case '30':
        defaultParams.value.approveStatus = ''
        defaultParams.value.orderStatus = ''
        defaultParams.value.orderEffective = 1
        break;
      case '40':

        defaultParams.value.approveStatus = ''
        defaultParams.value.orderStatus = 70
        defaultParams.value.orderEffective = ''

        break;

      default:
        break;
    }

    reSearch();
  },
);

// 清空
const reSet = () => {
  defaultParams.value = {
    timeType: '',
    yxTimeType: '',
    orderCode: '',
    pageNum: 0,
    pageSize: 20,
    orderStatusNew: '', // 订单状态
    sceneType: '', // 支付方式
    applicationTimes: [],  // 申请时间
    estimatedMealTimeEnd: [], // 有效时间
    approveStatus: '',
    orderStatus: '',
    orderEffective: '',
  };
};

// 搜索
const menuRef = ref(null);
const reSearch = () => {
  orderLoading.value = true
  orderFinished.value = false
  defaultParams.value.pageNum = 0;
  orderList.value = []
  loadorderList();
  menuRef.value.close();
};

// 申请时间选择相关
const showTimePicker = ref<boolean>(false);

const minDate = ref(new Date(2024, 0, 1));
const maxDate = ref(new Date(2026, 0, 1));
const choseTimeType = ref('');
const currentDate = ref<Array<string>>([]);

const openTimePicker = (type: string) => {
  choseTimeType.value = type;
  currentDate.value = [];
  minDate.value = new Date(2023, 0, 1);
  maxDate.value = new Date(2026, 0, 1);

  if (defaultParams.value.applicationTimes[0]) {
    const minDateArr = defaultParams.value.applicationTimes[0].split('-');
    minDate.value = new Date(parseInt(minDateArr[0]), parseInt(minDateArr[1]) - 1, parseInt(minDateArr[2]));
  }
  if (defaultParams.value.applicationTimes[1]) {
    const maxDateArr = defaultParams.value.applicationTimes[1].split('-');
    maxDate.value = new Date(parseInt(maxDateArr[0]), parseInt(maxDateArr[1]) - 1, parseInt(maxDateArr[2]));
  }

  if (type == 'begin') {
    
    if(defaultParams.value.applicationTimes[0]) {
      currentDate.value = defaultParams.value.applicationTimes[0]?.split('-');
    }else {
      currentDate.value = dayjs().format('YYYY-MM-DD').split('-')
    }
  } else {
    
    if(defaultParams.value.applicationTimes[1]) {
      currentDate.value = defaultParams.value.applicationTimes[1]?.split('-');
    }else {
      currentDate.value = dayjs().format('YYYY-MM-DD').split('-')
    }
    
  }

  showTimePicker.value = true;
};

const confirmTime = ({ selectedValues }: any) => {
  if (choseTimeType.value == 'begin') {
    defaultParams.value.applicationTimes[0] = selectedValues.join('-');
  } else {
    defaultParams.value.applicationTimes[1] = selectedValues.join('-');
  }

  if (defaultParams.value.applicationTimes[0] && defaultParams.value.applicationTimes[1]) {
    defaultParams.value.timeType = '';
  }

  showTimePicker.value = false;
};
// 有效时间选择相关
const showYxTimePicker = ref<boolean>(false);
const choseYxTimeType = ref('');

const openYxTimePicker = (type: string) => {
  choseYxTimeType.value = type;
  currentDate.value = [];
  minDate.value = new Date(2023, 0, 1);
  maxDate.value = new Date(2026, 0, 1);
  

  if (defaultParams.value.estimatedMealTimeEnd[0]) {
    const minDateArr = defaultParams.value.estimatedMealTimeEnd[0].split('-');
    minDate.value = new Date(parseInt(minDateArr[0]), parseInt(minDateArr[1]) - 1, parseInt(minDateArr[2]));
  }
  if (defaultParams.value.estimatedMealTimeEnd[1]) {
    const maxDateArr = defaultParams.value.estimatedMealTimeEnd[1].split('-');
    maxDate.value = new Date(parseInt(maxDateArr[0]), parseInt(maxDateArr[1]) - 1, parseInt(maxDateArr[2]));
  }

  if (type == 'begin') {
    if(defaultParams.value.estimatedMealTimeEnd[0]) {
      currentDate.value = defaultParams.value.estimatedMealTimeEnd[0]?.split('-');
    }else {
      currentDate.value = dayjs().format('YYYY-MM-DD').split('-')
    }
  } else {
    if (defaultParams.value.estimatedMealTimeEnd[1]) {
      currentDate.value = defaultParams.value.estimatedMealTimeEnd[1]?.split('-');
    }else {
      currentDate.value = dayjs().format('YYYY-MM-DD').split('-')
      
    }
  }

  showYxTimePicker.value = true;
};

const confirmYxTime = ({ selectedValues }: any) => {
  if (choseYxTimeType.value == 'begin') {
    defaultParams.value.estimatedMealTimeEnd[0] = selectedValues.join('-');
  } else {
    defaultParams.value.estimatedMealTimeEnd[1] = selectedValues.join('-');
  }

  if (defaultParams.value.estimatedMealTimeEnd[0] && defaultParams.value.estimatedMealTimeEnd[1]) {
    defaultParams.value.yxTimeType = '';
  }

  showYxTimePicker.value = false;
};




</script>

<style lang='less' scoped>
@import url(../common.less);

:deep(.van-dropdown-menu__bar) {
  //  background: var(--van-dropdown-menu-background);
  box-shadow: none;
}

.list-search {
  :deep(.van-search__action) {
    // padding: 0;
  }
}

.list-search-btn {
  width: 110px;
}

.my-radio {
  :deep(.van-radio__icon) {
    height: auto !important;
  }
}

.btn-com {
  width: 70px;
}

.input-border {
  border: 1px solid #dcdee0;
  border-radius: 20px;
  padding: 2px 10px;
}

.van-list-box {
  padding:  8px 10px;
}

.order-item {

  .order-item-left {
    width: 10px;
    height: 100%;
    background: url('@/assets/image/banquet/order/step.png') no-repeat;
    background-size: cover;

  }

  .order-item-right {
    flex: 1;
    flex-direction: column;

    .order-item-title {}

    .order-item-title-left {
      color: rgba(0, 0, 0, 0.5);
    }

    .order-content {
      :deep(.van-cell__title) {
        color: rgba(20, 21, 3, 0.6);
      }

      :deep(.van-cell__value) {
        color: rgba(20, 21, 3, 0.8);
      }

    }

    .order-title-cell {
      :deep(.van-cell__title) {
        color: rgba(0, 0, 0, 0.9) !important;
      }

    }

  }
}
.my-cell{
  padding-top: 5px ;
  padding-bottom: 5px;
}

.apply-btns {
  display: flex;
  justify-content: flex-end;
  :deep(.van-button) {
    margin-right: 4px;
  }

  :nth-last-child(1) {
    margin-right: 0;
  }
}

:deep(.van-search__action:active) {
  background-color: rgba(0, 0, 0, 0)
}

:deep(.large-value) {
  min-width: 70%;
}
.icon-size {
  height: 16px;
}
.width-80 {
  width: 80px;
}
.max-width-80 {
  // max-width: 80px;
}
:root:root {
  // --van-dropdown-menu-content-max-height: 100%;
}
:deep(.van-dropdown-item__content) {
  max-height: 100% !important;
}

.my-input {
  height: 24px;
    width: 100%;
    border: 1px solid #eee;
    font-size: 10px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px;
}
.mb-8 {
  margin-bottom: 8px;
}
</style>