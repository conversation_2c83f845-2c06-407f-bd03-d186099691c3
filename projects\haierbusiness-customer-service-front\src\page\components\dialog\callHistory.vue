<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  Switch as hSwitch,
  CheckboxGroup as hCheckboxGroup,
  Checkbox as hCheckbox,
  Image as hImage,
  Table as hTable,
  message,
  Button as hButton,
} from 'ant-design-vue';
import {
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
  PhoneFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { computed, ref, watch, onMounted } from 'vue';
import { callHistoryApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import type { Ref } from 'vue';
import { workOrderQuery } from '@haierbusiness-front/common-libs';
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil';
import { HeaderConstant } from '@haierbusiness-front/common-libs';
import WorkOrderList from './workOrderList.vue';
import type { UploadChangeParam, UploadFile } from 'ant-design-vue';
const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false);
import dayjs, { Dayjs } from 'dayjs';
import duration from 'dayjs/plugin/duration';
dayjs.extend(duration);
interface Props {
  show: boolean;
  id: number;
  isRelated: boolean;
  index:any,
}

const columns: ColumnType[] = [
  {
    title: '电话',
    dataIndex: 'phone',
    align: 'left',
  },
  {
    title: '联系人',
    dataIndex: 'user',
    align: 'center',
  },
  {
    title: '通话开始时间',
    dataIndex: 'callbegin',
    align: 'center',
  },
  {
    title: '通话结束时间',
    dataIndex: 'callend',
    align: 'center',
  },
  {
    title: '通话类型',
    dataIndex: 'callType',
    align: 'center',
  },
  {
    title: '通话时长/秒',
    dataIndex: 'callTime',
    align: 'center',
  },
  {
    title: '客服工号',
    dataIndex: 'personNum',
    align: 'center',
  },
  {
    title: '客服姓名',
    dataIndex: 'personName',
    align: 'center',
  },
  {
    title: '满意度',
    dataIndex: 'score',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '80',
    fixed: 'right',
    align: 'center',
  },
];
const tab = ref<any>([
  { value: 1, name: '全部' },
  { value: 2, name: '呼出' },
  { value: 3, name: '呼入' },
]);
const props = withDefaults(defineProps<Props>(), {
  show: false,
  index:null
});
const activeKey = ref<number>(1);
const indexData = ref<any>({});
const callHistoryList = ref<any>([]);
const from = ref();
const confirmLoading = ref(false);
const workOrderListShow = ref<boolean>(false);
const rowData = ref<any>({});
const emit = defineEmits(['cancel', 'ok', 'selectCallHistory']);

const visible = computed(() => props.show);
const dataSource = computed(
  () =>
    data.value?.records?.filter((item: any) => {
      return item;
    }) || [],
);

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(callHistoryApi.list, {
  defaultParams: [{}],
  manual: false,
});

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

// 获取通话时间
const getTime = (record: any) => {
  const seconds = dayjs(record.callend).diff(dayjs(record.callbegin), 'seconds');
  const duration = dayjs.duration(seconds, 'seconds');
  // 格式化为分钟格式
  const formatted = `${duration.minutes()}分${duration.seconds()}秒`;
  return seconds;
};
const getScore = (val: string | number) => {
  if (val == 0) {
    return '未评价';
  } else if (val == 1) {
    return '非常满意';
  } else if (val == 2) {
    return '满意';
  } else if (val == 3) {
    return '不满意';
  } else {
    return '未邀评';
  }
};

const handleTableChange = (pag: { current: number; pageSize: number },type:number|null, filters?: any, sorter?: any) => {
  listApiRun({
    callType:activeKey.value==2?7:activeKey.value==3?0:null,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

// 关联通话记录
const toCorrelation = (row: any) => {
  console.log()
  if (!props.isRelated) {
    emit('selectCallHistory', row,props.index);
  } else {
    rowData.value = row;
    workOrderListShow.value = true;
  }
};

// 关闭窗口
const workOrderListCancel = () => {
  workOrderListShow.value = false;
};
const changeTab = () => {
  if(activeKey.value==2){
   handleTableChange({ current: 1, pageSize: 10 });
  }else if (activeKey.value==3){
    handleTableChange({ current: 1, pageSize: 10 });
  }else{
    handleTableChange({ current: 1, pageSize: 10 });
  }
};

onMounted(() => {
  // handleTableChange(
  //   {current:1,pageSize:10}
  // )
});
</script>

<template>
  <!-- 工单明细弹窗 -->
  <h-modal
    v-model:visible="visible"
    :width="1100"
    :title="'关联通话记录'"
    @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading"
    :footer="false"
    :mask="false"
  >
    <a-tabs @change="changeTab" v-model:activeKey="activeKey">
      <a-tab-pane v-for="item in tab" :key="item.value" :tab="item.name">
        <h-table
          :columns="columns"
          :ellipsis="true"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="dataSource"
          :pagination="pagination"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'phone'">
            <span  v-if="record.calltype != 7" class="icon" :style="{'color':record.callbegin==record.callend?'red':'blue','font-weight':900}"><i style="font-size: 18px;" class="iconfont icon-huru"></i></span>
            <span v-else class="icon" :style="{'color':record.callbegin==record.callend?'red':'blue','font-weight':900}"><i style="font-size: 18px;" class="iconfont icon-huchu"></i></span>
              {{ record.calltype == 7 ? record.calleeno : record.callerno }}
            </template>
            <template v-if="column.dataIndex === 'personNum'">
              {{ record.agentCustomerVo.personNum }}
            </template>
            <template v-if="column.dataIndex === 'user'">
              {{ record.userVo.username  }}
            </template>
            <template v-if="column.dataIndex === 'personName'">
              {{ record.agentCustomerVo.personName }}
            </template>
            <template v-if="column.dataIndex === 'callTime'">
              {{ getTime(record) }}
            </template>
            <template v-if="column.dataIndex === 'callType'">
              {{ record.calltype==0?'呼入':record.calltype==7?'呼出':'转接' }}
            </template>
            <!-- 滿意度 score -->
            <template v-if="column.dataIndex === 'score'">
              {{ record.callbegin==record.callend?'未邀评':getScore(record.score) }}
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="toCorrelation(record)">关联</h-button>
            </template>
          </template>
        </h-table>
      </a-tab-pane>
    </a-tabs>
  </h-modal>
  <WorkOrderList
    v-if="workOrderListShow"
    @cancel="workOrderListCancel"
    :callHistoryInfo="rowData"
    :show="workOrderListShow"
  />
</template>

<style lang="less" scoped>
.important {
  color: red;
}
.imgItem {
  position: relative;
  display: inline-block;
  margin-right: 4px;
  .deleteIcon {
    position: absolute;
    right: 0px;
    z-index: 9999;
  }
}
.Detail {
  margin-left: 55px;
  margin-top: 15px;
  span {
    margin-right: 10px;
  }
  .title {
    font-weight: 600;
    font-size: 15px;
  }
}
.listItem {
  width: 180px;
}
.header {
  .listItem {
    margin-right: 10px;
    font-weight: bold;
  }
  .listItem1 {
    width: 60px;
    font-weight: bold;
  }
}
</style>
