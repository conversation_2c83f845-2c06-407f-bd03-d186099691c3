<script setup lang="ts">
import {
  Upload as HUpload,
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps
} from 'ant-design-vue';
import type { UploadChangeParam, UploadProps } from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { DownOutlined, ExclamationCircleOutlined, PlusOutlined, UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { payApi,fileApi } from '@haierbusiness-front/apis';
import {
  PayNotifyStateConstant,
  PayTypeChildConstant,
  PayMethodConstant,
  IHaierAccountBillInfo,
  IPaymentRecordListRequest,
  IPaymentRecordListResponse,
  IWyyB2bAccountRequest,
  IWyyB2bCancelRequest,
  IWyyB2bConfirmBalanceOrderBudgetRequest,
  IWyyB2bConfirmRequest,
  IWyyB2bListRequest,
  IWyyB2bMarkReadRequest,
  IWyyB2bRevokeConfirmRequest,
  PayStatusConstant,
  PayTypeConstant
} from '@haierbusiness-front/common-libs';
import { getCurrentRouter , errorModal, routerParam } from '@haierbusiness-front/utils';
import Eloading from '@haierbusiness-front/components/loading/ELoading.vue';
import dayjs, { Dayjs } from 'dayjs';
import { Ref, computed, getCurrentInstance, onMounted, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';

const router = getCurrentRouter()
const columns: ColumnType[] = [
  {
    title: '支付单号',
    dataIndex: 'code',
    fixed: 'left',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '业务单号',
    dataIndex: 'businessCode',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '支付人',
    dataIndex: 'owner',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '支付人姓名',
    dataIndex: 'ownerName',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '支付类型',
    dataIndex: 'payType',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '支付子类型',
    dataIndex: 'paySubtype',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '支付方式',
    dataIndex: 'paymentMethod ',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '支付状态',
    dataIndex: 'state',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '支付应用',
    dataIndex: 'applicationCode',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '支付平台单号',
    dataIndex: 'providerOrderCode',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '二级支付平台单号',
    dataIndex: 'secondProviderOrderCode',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '支付账户',
    dataIndex: 'accountCode',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '企业编码',
    dataIndex: 'enterprise',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '交易金额',
    dataIndex: 'amount',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '支付发起时间',
    dataIndex: 'payTime',
    width: '180px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '支付成功时间',
    dataIndex: 'successPayTime',
    width: '180px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '通知回调状态',
    dataIndex: 'notifyState',
    width: '180px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '已重试次数',
    dataIndex: 'notifyRetry',
    width: '180px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '通知失败错误',
    dataIndex: 'notifyErrorMessage',
    width: '180px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '预支付通知回调状态',
    dataIndex: 'preNotifyState',
    width: '180px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '预支付已重试次数',
    dataIndex: 'preNotifyRetry',
    width: '180px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '预支付通知失败错误',
    dataIndex: 'preNotifyErrorMessage',
    width: '180px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '支付回调地址',
    dataIndex: 'notifyUrl',
    width: '300px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '支付结束后跳转地址',
    dataIndex: 'callbackUrl',
    width: '300px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改人',
    dataIndex: 'lastModifiedBy',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改时间',
    dataIndex: 'gmtModified',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '180px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<IPaymentRecordListRequest>({ needBudget: false })
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(payApi.paymentRecordAllList, {
  defaultParams: [
    {
      needBudget: false
    }
  ],
  manual: false
});

const {
  data: exportListData,
  run: exportListApiRun,
  loading: exportListLoading,
} = useRequest(payApi.exportPaymentRecordAllList);

const reset = () => {
  successBeginAndEnd.value = undefined
  startBeginAndEnd.value = undefined
  searchParam.value = { needBudget: false }
}

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

// 新增表单相关
const accountNewForm = ref()
const confirmLoading = ref(false)
const visibleNew = ref(false)
const newAccountForm = ref<IWyyB2bAccountRequest>({})
const labelCol = { span: 10 }
const wrapperCol = { span: 10 }


const gotoDetails = (record: IPaymentRecordListResponse) => {
  router.push({ path: "/payman/payrecord/details", query: { record: routerParam(record) } })
}
const {
  data: businessDetailsUrlApiData,
  run: businessDetailsUrlApiRun,
  loading: businessDetailsUrlApiLoading,
} = useRequest(payApi.businessDetailsUrl);

const gotoBusinessDetails = (record: IPaymentRecordListResponse) => {
  payApi.businessDetailsUrl({ recordCode: record.code })
    .then(it => {
      if (it) {
        // window.location.href = it
        window.open(it)
      } else {
        message.warn("业务系统没有传递详情链接")
      }
    })
}
const confirmParam = ref<IWyyB2bConfirmRequest>({})
const confirmAccountLoading = ref(false)

const revokeConfirmParam = ref<IWyyB2bRevokeConfirmRequest>({})
const revokeConfirmLoading = ref(false)


const cancelParam = ref<IWyyB2bCancelRequest>({})
const cancelLoading = ref(false)


const confirmBudgetParam = ref<IWyyB2bConfirmBalanceOrderBudgetRequest>({})


const advancedSearchVisible = ref(false)
const gotoAdvancedSearch = () => {
  if (advancedSearchVisible.value) {
    advancedSearchVisible.value = false
  } else {
    advancedSearchVisible.value = true
  }
}

// 再次通知
const notifyAgain = (record: IPaymentRecordListResponse) => {
  payApi.refundNotifyAgain({ refundCode: record.code, state: record.state, notifyUrl: record.notifyUrl })
    .then(() => {
      message.success('操作成功！', 4)
    })
}

const startBeginAndEnd = ref<[Dayjs, Dayjs]>()
watch(() => startBeginAndEnd.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.startPayBegin = n[0]
    searchParam.value.startPayEnd = n[1]
  } else {
    searchParam.value.startPayBegin = undefined
    searchParam.value.startPayEnd = undefined
  }
});
const successBeginAndEnd = ref<[Dayjs, Dayjs]>()
watch(() => successBeginAndEnd.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.successPayBegin = n[0]
    searchParam.value.successPayEnd = n[1]
  } else {
    searchParam.value.successPayBegin = undefined
    searchParam.value.successPayEnd = undefined
  }
});

const childPayTypes = ref<Array<{code: string, name: string, parent: number}>>([])

const onPayTypeChange = () => {
  if (searchParam.value.payType) {
    childPayTypes.value = PayTypeChildConstant.toArray(searchParam.value.payType)
  } else {
    childPayTypes.value = []
  }
}

</script>

<template>
  <Eloading :loading="confirmLoading"></Eloading>
  <Eloading :loading="confirmAccountLoading"></Eloading>
  <Eloading :loading="revokeConfirmLoading"></Eloading>
  <Eloading :loading="cancelLoading"></Eloading>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">

    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchAccountCode">业务单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="searchAccountCode" v-model:value="searchParam.businessCode" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchState">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.state" style="width: 100%" allow-clear>
              <h-select-option :value="PayStatusConstant.CANCEL.type">{{
                PayStatusConstant.CANCEL.name
              }}
              </h-select-option>
              <h-select-option :value="PayStatusConstant.SAVE.type">{{
                PayStatusConstant.SAVE.name
              }}
              </h-select-option>
              <h-select-option :value="PayStatusConstant.ADVANCE.type">{{
                PayStatusConstant.ADVANCE.name
              }}
              </h-select-option>
              <h-select-option :value="PayStatusConstant.SUCCESS.type">{{
                PayStatusConstant.SUCCESS.name
              }}
              </h-select-option>
              <h-select-option :value="PayStatusConstant.ERROR.type">{{
                PayStatusConstant.ERROR.name
              }}
              </h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;">
            <label for="startBeginAndEnd">支付发起日期：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="startBeginAndEnd" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="paymentRecordCode">支付单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="paymentRecordCode" v-model:value="searchParam.paymentRecordCode" placeholder=""
              autocomplete="off" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="payType">支付类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.payType" style="width: 100%" @change="onPayTypeChange()" allow-clear>
              <h-select-option :value="PayTypeConstant.BUDGET.type">{{
                PayTypeConstant.BUDGET.name
              }}
              </h-select-option>
              <h-select-option :value="PayTypeConstant.COMPOSITION.type">{{
                PayTypeConstant.COMPOSITION.name
              }}
              </h-select-option>
              <h-select-option :value="PayTypeConstant.OFFLINE.type">{{
                PayTypeConstant.OFFLINE.name
              }}
              </h-select-option>
              <h-select-option :value="PayTypeConstant.WALLET.type">{{
                PayTypeConstant.WALLET.name
              }}
              </h-select-option>
              <h-select-option :value="PayTypeConstant.VIRTUAL_ACCOUNT.type">{{
                PayTypeConstant.VIRTUAL_ACCOUNT.name
              }}
              </h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="paySubtype">支付子类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" v-model:value="searchParam.paySubtype" style="width: 100%" allow-clear>
              <h-select-option v-for="(item, index) in childPayTypes" :key="index" :value="item.code">{{ item.name }}</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="username">用户账号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="username" v-model:value="searchParam.username" placeholder="" autocomplete="off" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="providerOrderCode">支付平台单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="providerOrderCode" v-model:value="searchParam.providerOrderCode" placeholder=""
              autocomplete="off" allow-clear />
          </h-col>
        </h-row>
        <template v-if="advancedSearchVisible">
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
            <h-col :span="2" style="text-align: right;">
              <label for="successBeginAndEnd">支付成功日期：</label>
            </h-col>
            <h-col :span="4">
              <h-range-picker v-model:value="successBeginAndEnd" value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%;" />
            </h-col>
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="nickName">用户昵称：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="nickName" v-model:value="searchParam.nickName" placeholder="" autocomplete="off" allow-clear />
            </h-col>
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="nickName">二级支付单号：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="nickName" v-model:value="searchParam.secondProviderOrderCode" placeholder="" autocomplete="off"
                allow-clear />
            </h-col>
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="applicationCode">业务应用code：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="applicationCode" v-model:value="searchParam.applicationCode" placeholder="" autocomplete="off"
                allow-clear />
            </h-col>
          </h-row>
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="notifyState">支付通知：</label>
            </h-col>
            <h-col :span="4">
              <h-select v-model:value="searchParam.notifyState" style="width: 100%" allow-clear>
                <h-select-option :value="PayNotifyStateConstant.SUCCESS.code">{{
                  PayNotifyStateConstant.SUCCESS.name
                }}
                </h-select-option>
                <h-select-option :value="PayNotifyStateConstant.RETRY.code">{{
                  PayNotifyStateConstant.RETRY.name
                }}
                </h-select-option>
                <h-select-option :value="PayNotifyStateConstant.ERROR.code">{{
                  PayNotifyStateConstant.ERROR.name
                }}
                </h-select-option>
              </h-select>
            </h-col>
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="preNotifyState">预支付通知：</label>
            </h-col>
            <h-col :span="4">
              <h-select v-model:value="searchParam.preNotifyState" style="width: 100%" allow-clear>
                <h-select-option :value="PayNotifyStateConstant.SUCCESS.code">{{
                  PayNotifyStateConstant.SUCCESS.name
                }}
                </h-select-option>
                <h-select-option :value="PayNotifyStateConstant.RETRY.code">{{
                  PayNotifyStateConstant.RETRY.name
                }}
                </h-select-option>
                <h-select-option :value="PayNotifyStateConstant.ERROR.code">{{
                  PayNotifyStateConstant.ERROR.name
                }}
                </h-select-option>
              </h-select>
            </h-col>
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="accountCode">支付账户ID：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="accountCode" v-model:value="searchParam.accountCode" placeholder="" autocomplete="off"
                allow-clear />
            </h-col>
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="paymentMethod">支付方式：</label>
            </h-col>
            <h-col :span="4">
              <h-select v-model:value="searchParam.paymentMethod" style="width: 100%" allow-clear>
                <h-select-option :value="PayMethodConstant.PAYMENT_CODE.code">{{
                  PayMethodConstant.PAYMENT_CODE.name
                }}
                </h-select-option>
                <h-select-option :value="PayMethodConstant.H5.code">{{
                  PayMethodConstant.H5.name
                }}
                </h-select-option>
                <h-select-option :value="PayMethodConstant.SCAN_CODE.code">{{
                    PayMethodConstant.SCAN_CODE.name
                  }}
                  </h-select-option>
              </h-select>
            </h-col>
          </h-row>
        </template>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button type="link" @click="gotoAdvancedSearch()">
              <UpOutlined v-if="advancedSearchVisible" />
              <DownOutlined v-else />
              高级搜索
            </h-button>
            <h-button type="primary" style="margin-right: 10px" :loading="exportListLoading"
              @click="exportListApiRun(searchParam);">
              <UploadOutlined />
              导出
            </h-button>
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ x: 1550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'code'">
              <h-button type="link" @click="gotoDetails(record)">{{ record.code }}</h-button>
            </template>
            <template v-if="column.dataIndex === 'businessCode'">
              <h-button type="link" @click="gotoBusinessDetails(record)">{{ record.businessCode }}</h-button>
            </template>
            <template v-if="column.dataIndex === 'payType'">
              {{ PayTypeConstant.ofType(record.payType)?.name }}
            </template>
            <template v-if="column.dataIndex === 'state'">
              <h-tag v-if="record.state === PayStatusConstant.CANCEL.type" color="default">{{
                PayStatusConstant.CANCEL.name
              }}
              </h-tag>
              <h-tag v-if="record.state === PayStatusConstant.SAVE.type" color="processing">{{
                PayStatusConstant.SAVE.name
              }}
              </h-tag>
              <h-tag v-if="record.state === PayStatusConstant.ADVANCE.type" color="warning">{{
                PayStatusConstant.ADVANCE.name
              }}
              </h-tag>
              <h-tag v-if="record.state === PayStatusConstant.SUCCESS.type" color="success">{{
                PayStatusConstant.SUCCESS.name
              }}
              </h-tag>
              <h-tag v-if="record.state === PayStatusConstant.ERROR.type" color="error">{{
                PayStatusConstant.ERROR.name
              }}
              </h-tag>
            </template>
            <template v-if="column.dataIndex === 'notifyState'">
              <h-tag v-if="record.notifyState === PayNotifyStateConstant.SUCCESS.code" color="success">{{
                PayNotifyStateConstant.SUCCESS.name
              }}
              </h-tag>
              <h-tag v-if="record.notifyState === PayNotifyStateConstant.RETRY.code" color="warning">{{
                PayNotifyStateConstant.RETRY.name
              }}
              </h-tag>
              <h-tag v-if="record.notifyState === PayNotifyStateConstant.ERROR.code" color="error">{{
                PayNotifyStateConstant.ERROR.name
              }}
              </h-tag>
            </template>
            <template v-if="column.dataIndex === 'preNotifyState'">
              <h-tag v-if="record.preNotifyState === PayNotifyStateConstant.SUCCESS.code" color="success">{{
                PayNotifyStateConstant.SUCCESS.name
              }}
              </h-tag>
              <h-tag v-if="record.preNotifyState === PayNotifyStateConstant.RETRY.code" color="warning">{{
                PayNotifyStateConstant.RETRY.name
              }}
              </h-tag>
              <h-tag v-if="record.preNotifyState === PayNotifyStateConstant.ERROR.code" color="error">{{
                PayNotifyStateConstant.ERROR.name
              }}
              </h-tag>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" v-if="record.notifyState === 2" @click="notifyAgain(record)">再次通知</h-button>
              <h-button type="link" v-if="record.state === 999">重新支付</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
