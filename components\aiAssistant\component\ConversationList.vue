:ConversationList.vue
<!-- ConversationList.vue -->
<script setup lang="ts">
import {MenuFoldOutlined, MenuUnfoldOutlined, PlusOutlined} from '@ant-design/icons-vue';
import {computed, defineEmits, defineProps, ref} from 'vue';

const props = defineProps({
  conversations: {
    type: Array as () => Array<{ chatId: string; subject: string; }>,
    required: true,
    default: () => []
  },
  currentChatId: {
    type: String,
    required: true,
    default: null
  },
  isNewConversation: {
    type: Boolean,
    required: true,
    default: false
  }
});
// 添加计算属性：返回倒序的对话列表
const reversedConversations = computed(() => {
  return [...props.conversations].reverse();
});
const emit = defineEmits(['create', 'switch']);

// 内部折叠状态
const isCollapsed = ref(false);

// 创建新对话
const createNewConversation = () => {
  emit('create');
};

// 切换对话
const switchConversation = (chatId: string) => {
  emit('switch', chatId);
};

// 折叠/展开对话列表
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};
</script>

<template>
  <div class="conversation-list" :class="{ 'collapsed': isCollapsed }">
    <div class="list-header">
      <div class="header-content" :class="{ 'vertical-layout': isCollapsed }">
        <!-- 折叠按钮 - 使用Ant图标 -->
        <button
            @click="toggleCollapse"
            class="collapse-btn"
            :class="{ 'collapsed': isCollapsed }"
        >
          <MenuFoldOutlined v-if="!isCollapsed" class="ant-icon"/>
          <MenuUnfoldOutlined v-else class="ant-icon"/>
        </button>

        <!-- 创建新对话按钮 - 使用Ant图标 -->
        <button
            @click="createNewConversation"
            class="new-chat-btn"
            :class="{ 'collapsed-btn': isCollapsed, 'disabled-btn': isNewConversation }"
            :disabled="isNewConversation"
        >
          <PlusOutlined class="ant-icon"/>
          <span class="btn-text">创建新对话</span>
        </button>
      </div>

      <!-- 历史对话记录标题 -->
      <div class="header-title">
        <span v-if="!isCollapsed" class="history-title">历史对话记录</span>
      </div>
    </div>

    <div class="conversation-scroll">
      <div
          v-for="conv in reversedConversations"
          :key="conv.chatId"
          class="conversation-item"
          :class="{ active: currentChatId === conv.chatId }"
          @click="switchConversation(conv.chatId)"
      >
        <div class="item-content">
          <div class="item-title">
            <span v-if="!isCollapsed">{{ conv.subject }}</span>
            <span v-else class="collapsed-title">{{ conv.subject.substring(0, 1) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.conversation-list {
  width: 180px;
  border-right: 1px solid #e8e8e8;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.collapsed {
    width: 60px;

    .conversation-item span:not(.collapsed-title) {
      display: none;
    }

    .collapsed-title {
      display: inline-block;
      font-weight: bold;
      background: #e6f7ff;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      text-align: center;
      line-height: 24px;
      transition: all 0.3s ease;
    }

    /* 确保内容居中 */

    .conversation-item .item-content {
      justify-content: center;
    }
  }

  .list-header {
    padding: 12px 12px 8px 2px;
    border-bottom: 1px solid #e8e8e8;
    background-color: #fff;
    transition: padding 0.3s ease;
  }

  .header-content {
    display: flex;
    gap: 10px;
    align-items: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &.vertical-layout {
      flex-direction: column;
      gap: 6px;
      align-items: center;
    }
  }

  .header-title {
    margin-top: 6px;
    margin-bottom: 6px;
    transition: margin-top 0.3s ease;

    .history-title {
      font-size: 12px;
      color: #999;
      font-weight: 500;
      transition: all 0.3s ease;
    }
  }

  /* 统一按钮样式 */

  .collapse-btn, .new-chat-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #1890ff;
    color: white;
    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);

    &:hover {
      background: #40a9ff;
      transform: translateY(-2px);
      box-shadow: 0 4px 10px rgba(24, 144, 255, 0.3);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .new-chat-btn.disabled-btn {
    background: #ccc !important;
    cursor: not-allowed;
    box-shadow: none;

    &:hover {
      background: #ccc !important;
      transform: none !important;
      box-shadow: none !important;
    }
  }
  /* 图标样式 */

  .ant-icon {
    font-size: 18px; /* 图标大小 */
    transition: all 0.3s ease;
  }

  /* 折叠按钮特定样式 */

  .collapse-btn {
    width: 36px;
    height: 36px;

    .ant-icon {
      font-size: 16px; /* 折叠按钮稍小 */
    }

    &.collapsed {
      background: #f0f0f0;
      color: #666;

      &:hover {
        background: #e6f7ff;
        color: #1890ff;
      }
    }
  }

  /* 创建按钮特定样式 */

  .new-chat-btn {
    height: 36px;
    padding: 0 12px;
    overflow: hidden;

    .ant-icon {
      font-size: 18px; /* 创建按钮较大 */
    }

    .btn-text {
      margin-left: 8px;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      opacity: 1;
      max-width: 100px;
      white-space: nowrap;
    }

    &.collapsed-btn {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      padding: 0;

      .btn-text {
        opacity: 0;
        max-width: 0;
        margin: 0;
      }
    }
  }

  .conversation-scroll {
    flex: 1;
    overflow-y: auto;
    padding: 6px 0;
    transition: padding 0.3s ease;
  }

  .conversation-item {
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;

    &:hover {
      background-color: #e6f7ff;
    }

    &.active {
      background: #e6f7ff;
      border-left: 3px solid #1890ff;
      font-weight: 600;
    }

    .item-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      transition: all 0.3s ease;
    }

    .item-title {
      color: #333;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      transition: all 0.3s ease;
    }
  }
}

/* 折叠状态下调整 */
.conversation-list.collapsed {
  .btn-text, .history-title {
    opacity: 0;
    max-width: 0;
    margin: 0;
  }

  .list-header {
    padding: 8px 8px 4px;
  }

  .conversation-scroll {
    padding: 2px 0;
  }

  .conversation-item {
    padding: 6px 8px;
  }
}
</style>
