<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
} from 'ant-design-vue';

import type { TableColumnsType } from 'ant-design-vue';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import {
  DownOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
} from '@ant-design/icons-vue';
import { reportVoPageResultApi,callCenterApi } from '@haierbusiness-front/apis';
import { IEnterpriseListRequest, IEnterprise } from '@haierbusiness-front/common-libs';
import { errorModal, routerParam } from '@haierbusiness-front/utils';
import dayjs, { Dayjs } from 'dayjs';
import { computed, onMounted, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useRouter } from 'vue-router';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables';
import EditDialog from '../components/dialog/edit-dialog.vue';
import EditDetailDialog from '../components/dialog/workOrderDetail.vue';

const router = useRouter();
const columns: TableColumnsType[] = [
  {
    title: '客服',
    dataIndex: 'personName',
    align: 'left',
  },
  {
    title: '坐席ID',
    dataIndex: 'agentId',
    align: 'left',
  },
  {
    title: '产品线',
    dataIndex: 'productName',
    align: 'left',
  },
  {
    title: '服务',
    children: [
      {
        title: '很满意',
        dataIndex: 'verySatisfied',
        align: 'left',
      },
      {
        title: '满意',
        dataIndex: 'satisfied',
        align: 'left',
      },
      {
        title: '不满意',
        dataIndex: 'unsatisfied',
        align: 'left',
      },
      {
        title: '无评价',
        dataIndex: 'noEvaluation',
        align: 'left',
      },
      {
        title: '呼入未接听',
        dataIndex: 'callInNoAnswer',
        align: 'left',
      },
      {
        title: '总未接听',
        dataIndex: 'totalCallNoAnswer',
        align: 'left',
      },
      {
        title: '呼入',
        dataIndex: 'callInNum',
        align: 'left',
      },
      {
        title: '呼出',
        dataIndex: 'callOutNum',
        align: 'left',
      },
      {
        title: '工单',
        dataIndex: 'workNum',
        align: 'left',
      },
      {
        title: '接听次数',
        dataIndex: 'answerCount',
        align: 'left',
      },
    ],
  },
];
const searchParam = ref<IEnterpriseListRequest>({});
const productList = ref<any>([]);
const manageParams = ref();
const editDetail = ref<any>({});
// 明细弹窗
const detailVisible = ref<boolean>(false);
const rangePresets = ref([
  { label: '最近一年', value: [dayjs().add(-365, 'd'), dayjs()] },
  { label: '最近半年', value: [dayjs().add(-180, 'd'), dayjs()] },
  { label: '最近30天', value: [dayjs().add(-30, 'd'), dayjs()] },
  { label: '最近7天', value: [dayjs().add(-7, 'd'), dayjs()] },
]);

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(reportVoPageResultApi.list, {
  defaultParams: [{}],
  manual: false,
});

const reset = () => {
  searchParam.value = {};
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize: 10,
  });
};

const dataSource = computed(
  () =>
    data.value?.customerReportVoList?.filter((item) => {
      if (item.iconUrl) {
        item.files = [
          {
            name: `${item.productName}`,
            thumbUrl: item.iconUrl,
          },
        ];
      }
      return item;
    }) || [],
);

const dataSourceTotal = computed(
  () =>
    data.value?.otherReportVo
);

// 修改日期
const changeDate = (val: any) => {
  if (val) {
    searchParam.value.startTime = val[0] + ' 00:00:00';
    searchParam.value.endTime = val[1] + ' 23:59:59';
  } else {
    searchParam.value.startTime = null;
    searchParam.value.endTime = null;
  }
};

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

// 新增表单相关
const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } = useEditDialog<
  IEnterprise,
  IEnterprise
>(reportVoPageResultApi, '工单', () =>
  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize,
  }),
);

const { handleDelete } = useDelete(reportVoPageResultApi, () =>
  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize,
  }),
);

const EditDetailDialogCancel = () => {
  detailVisible.value = false;
  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize,
  });
};

const download = () =>{
  reportVoPageResultApi.exportExcel(searchParam.value)
}

const saveWorkOrder = (res: any) => {
  visible.value = false;
  editDetail.value = res;
  detailVisible.value = true;
  listApiRun({
    pageNum: 1,
    pageSize: 10,
  });
};

// 获取产品线的数据
const getProductList = () => {
  callCenterApi.getProductList().then((res: any) => {
    productList.value = res;
  });
};

// 修改日期
// const changeDate = (val: any) => {
//   if (val) {
//     searchParam.value.startTime = val[0] + ' 00:00:00';
//     searchParam.value.endTime = val[1] + ' 23:59:59';
//   } else {
//     searchParam.value.startTime = null;
//     searchParam.value.endTime = null;
//   }
// };

// 编辑明细
const handleDateilEdit = (row: any) => {
  editDetail.value = row;
  detailVisible.value = true;
};
// 初始化
onMounted(async () => {
  getProductList();
});
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 30px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col style="text-align: right; padding-right: 10px; width: 80px">
            <label for="customerNum">客服工号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="customerNum" v-model:value="searchParam.customerNum" placeholder="" autocomplete="off" allow-clear />
          </h-col>
          <h-col style="text-align: right;padding-right: 10px;  width: 80px">
            <label for="mobile">产品线：</label>
          </h-col>
          <h-col :span="4">
            <h-select allowClear v-model:value="searchParam.productId" style="width: 100%">
              <h-select-option v-for="item in productList" :value="item.id">{{ item.name }}</h-select-option>
            </h-select>
          </h-col>
          <h-col style="text-align: right; padding-right: 10px; width: 100px">
            <label for="customerNum">通话日期：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker
              valueFormat="YYYY-MM-DD"
              :presets="rangePresets"
              @change="changeDate"
              v-model:value="searchParam.Date"
            />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
             <h-button style="margin-right: 10px" @click="download">导出</h-button>
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
            bordered
          :columns="columns"
          :ellipsis="true"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="dataSource"
          :pagination="null"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'personName'">
                {{record.agentCustomerVo.personName}}({{record.agentCustomerVo.personNum}})
            </template>
            <template v-if="column.dataIndex === 'agentId'">
                {{record.agentCustomerVo.agentId}}
            </template>
            <template v-if="column.dataIndex === 'productName'">
                {{record.agentCustomerVo.productName}}
            </template>
            <template v-if="column.dataIndex === 'verySatisfied'">
                {{record.verySatisfied}}
            </template>
          </template>
        <template #summary>
        <a-table-summary-row v-if="dataSource&&dataSource.length">
            <a-table-summary-cell></a-table-summary-cell>
            <a-table-summary-cell></a-table-summary-cell>
            <a-table-summary-cell style="font-weight:900;">合计</a-table-summary-cell>
            <a-table-summary-cell>
                <a-typography-text> {{data?.otherReportVo?.verySatisfied}}</a-typography-text>
            </a-table-summary-cell>
            <a-table-summary-cell>
                <a-typography-text> {{data?.otherReportVo?.satisfied}}</a-typography-text>
            </a-table-summary-cell>
            <a-table-summary-cell>
                <a-typography-text> {{data?.otherReportVo?.unsatisfied}}</a-typography-text>
            </a-table-summary-cell>
            <a-table-summary-cell>
                <a-typography-text> {{data?.otherReportVo?.noEvaluation}}</a-typography-text>
            </a-table-summary-cell>
            <a-table-summary-cell>
                <a-typography-text> {{data?.otherReportVo?.callInNoAnswer}}</a-typography-text>
            </a-table-summary-cell>
            <a-table-summary-cell>
                <a-typography-text> {{data?.otherReportVo?.totalCallNoAnswer}}</a-typography-text>
            </a-table-summary-cell>
          <a-table-summary-cell>
                <a-typography-text> {{data?.otherReportVo?.callInNum}}</a-typography-text>
            </a-table-summary-cell>
            <a-table-summary-cell>
                <a-typography-text> {{data?.otherReportVo?.callOutNum}}</a-typography-text>
            </a-table-summary-cell>
            <a-table-summary-cell>
                <a-typography-text> {{data?.otherReportVo?.workNum}}</a-typography-text>
            </a-table-summary-cell>            
            <a-table-summary-cell>
                <a-typography-text> {{data?.otherReportVo?.answerCount}}</a-typography-text>
            </a-table-summary-cell>
        </a-table-summary-row>
        </template>
        </h-table>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
