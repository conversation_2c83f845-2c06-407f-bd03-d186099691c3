<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Popover as hPopover,
  Card as hCard,
  Input as hInput,
  message,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  QuestionCircleOutlined,
  DeleteOutlined,
  MenuOutlined,
} from '@ant-design/icons-vue';
import { callCenterApi } from '@haierbusiness-front/apis';
import { angtListRes, AddressBookListParams } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables';
import EditDialog from './edit-dialog.vue';
import { getCurrentRouter, routerParam } from '@haierbusiness-front/utils';
// import router from '../../../router';
const router = getCurrentRouter();

const currentRouter = ref();

const addressBook = ref<angtListRes[]>([]);
const keyWord = ref<string>('');
const visible = ref<boolean>(false);
const editData = ref<angtListRes>({});
const productList = ref<any>([]);
const searchFrom = ref<any>({
  personNum: '',
  productId: null,
  agentId: '',
});
const columns = [
  {
    title: '坐席ID',
    dataIndex: 'agentId',
    width: '10%',
  },
  {
    title: '外呼坐席ID',
    dataIndex: 'outboundNumber',
    width: '10%',
  },
  {
    title: '分机号',
    dataIndex: 'agentNum',
    width: '10%',
  },
  {
    title: '工号',
    dataIndex: 'personNum',
    width: '10%',
  },
  {
    title: '姓名',
    dataIndex: 'personName',
    width: '20%',
  },
  {
    title: '产品线',
    dataIndex: 'productName',
    width: '20%',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '25%',
  },
];
// 获取坐席列表
const getAgentCustomerList = () => {
  const obj = Object.assign({ pageNum: 1, pageSize: 9999 }, searchFrom.value);
  callCenterApi.AgentCustomerList(obj).then((res: any) => {
    console.log(res);
    addressBook.value = res.list;
  });
};

const handleCreate = () => {
  visible.value = true;
};

// 保存或者修改
const handleOk = (object: angtListRes) => {
  callCenterApi.createOrUpdateAgentCustomer(object).then((res) => {
    message.success('保存成功');
    visible.value = false;
    getAgentCustomerList();
  });
};

// 编辑
const edit = (row: angtListRes) => {
  editData.value = JSON.parse(JSON.stringify(row));
  visible.value = true;
};
// 确认删除
const confirmDel = (row: angtListRes) => {
  callCenterApi.deleteAgentCustomer([row.id]).then((res: any) => {
    message.success('删除成功');
    getAgentCustomerList();
  });
};

const onDialogClose = () => {
  editData.value = {};
  visible.value = false;
};

const reset = () => {
  searchFrom.value.personNum = '';
  searchFrom.value.productId = null;
  searchFrom.value.agentId = '';
  getAgentCustomerList();
};

// 获取产品线的数据
const getProductList = () => {
  callCenterApi.getProductList().then((res: any) => {
    productList.value = res;
  });
};

// 初始化
onMounted(async () => {
  currentRouter.value = await router;
  getAgentCustomerList();
  getProductList();
});
</script>

<template>
  <div
    v-if="$route.matched.length < 3"
    style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto"
  >
    <h-card style="margin-bottom: 10px">
      <h-row :align="'middle'">
        <h-col :span="24">
          <h-row :align="'middle'" style="padding: 10px 10px 10px 10px">
            <h-col  style="text-align: right; padding-right: 10px;width:60px;">
              <label for="createTime">工号：</label>
            </h-col>
            <h-col :span="4" style="text-align: right">
              <h-input allowClear v-model:value="searchFrom.personNum" placeholder="请输入工号" />
            </h-col>
            <h-col  style="text-align: right; padding-right: 10px;width:80px;">
              <label for="createTime">产品线：</label>
            </h-col>
            <h-col :span="4">
              <h-select allowClear v-model:value="searchFrom.productId" style="width: 100%">
                <h-select-option v-for="item in productList" :value="item.id">{{ item.name }}</h-select-option>
              </h-select>
            </h-col>
            <h-col  style="text-align: right; padding-right: 10px;width:80px;">
              <label for="createTime">坐席ID：</label>
            </h-col>
            <h-col :span="4" style="text-align: right">
              <h-input allowClear v-model:value="searchFrom.agentId" placeholder="请输入坐席ID" />
            </h-col>
          </h-row>
          <h-row>
            <h-col :span="24" style="text-align: right">
              <h-button type="primary" style="margin-right: 10px" @click="handleCreate()">
                <PlusOutlined /> 新增
              </h-button>
              <h-button style="margin-right: 10px" @click="reset">重置</h-button>
              <h-button type="primary" @click="getAgentCustomerList()"> <SearchOutlined />查询 </h-button>
            </h-col>
          </h-row>
        </h-col>
      </h-row>
    </h-card>
    <h-table :size="'small'" :pagination="false" :columns="columns" :data-source="addressBook" bordered>
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'operation'">
          <div class="editable-row-operations">
            <span>
              <a @click="edit(record)">编辑</a>
              <a-popconfirm
                title="确定要删除吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="confirmDel(record)"
                @cancel="cancel"
              >
                <a class="ml10" href="#">删除</a>
              </a-popconfirm>
            </span>
          </div>
        </template>
        <template v-else>
          {{ text }}
        </template>
      </template>
    </h-table>
    <edit-dialog
      :labelList="productList"
      :knowCenterOptions="knowCenterOptions"
      :show="visible"
      :data="editData"
      @cancel="onDialogClose"
      @ok="handleOk"
    >
    </edit-dialog>
  </div>
  <router-view></router-view>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.ml10 {
  margin-left: 10px;
}

// .pagination {
//   width: 100%;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   margin-top: 20px;
// }
</style>
