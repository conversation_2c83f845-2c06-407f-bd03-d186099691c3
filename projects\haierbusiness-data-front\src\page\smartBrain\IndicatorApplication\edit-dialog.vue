<template>
  <h-modal
    v-model:visible="modalShow"
    :width="1200"
    @ok="confirm"
    @cancel="$emit('cancel')"
  >
    <h-card title="您以申请如下指标, 请确认后提交">
      <div class="reportBox" style="padding: 8px;height:70vh;overflow:auto;">
        <div v-for="(item, index) in props.data" :key="item.id" class="indexItemBox">
            <ReportItem @remove="removeReportItem" :showRemove="true" :reportInfo="item"></ReportItem>
        </div>
      </div>
    </h-card>
  </h-modal>
  <h-modal
    v-model:visible="visibleFormShow"
    title="申请指标"
    :footer="null"
    :width="800"
  >
    <div style="overflow:auto;" class="w-500">
        <h-form
          ref="from"
          :model="permission"
          :label-col="{ span: 7 }"
          :wrapper-col="{ span: 17 }"
          :rules="rules"
          @finish="handleOk"
        >
          <!-- <h-form-item label="数据类型" name="moduleType">
            <h-radio-group v-model:value="permission.moduleType">
              <h-radio :value="1">报表</h-radio>
              <h-radio :value="2">看板</h-radio>
              <h-radio :value="3">分析报告</h-radio>
            </h-radio-group>
          </h-form-item> -->
          <h-form-item label="申请数据维度" name="dataType">
            <h-radio-group v-model:value="permission.dataType" @change="onDataTypeChange">
              <h-radio :value="1">预算部门</h-radio>
              <h-radio :value="2">结算单位</h-radio>
              <!-- <h-radio :value="3">领域</h-radio>
              <h-radio :value="4">平台</h-radio>
              <h-radio :value="5">产业线</h-radio> -->
            </h-radio-group>
          </h-form-item>
          <h-form-item label="申请数据填报" name="applyType">
            <h-radio-group v-model:value="permission.applyType">
              <h-radio :value="1">手工录入</h-radio>
              <h-radio v-if="permission.dataType!=3 && permission.dataType!=4 && permission.dataType!=5" :value="2">模板上传</h-radio>
            </h-radio-group>
          </h-form-item>
      <template v-if="permission.applyType == 1">
          <h-form-item
            label="结算单位"
            name="accountCompanyCode"
            v-if="permission.dataType == 2"
          >
            <div style="display: flex">
              <div style="flex: 3">
                <h-select
                  placeholder="请搜索结算单位（支持多选）"
                  v-model:value="permission.accountCompanyCode"
                  mode="multiple"
                  show-search
                  :filter-option="filterOption"
                  @change="(value, option) => handleChange(value as string, option as Array<ApplyCompanyType>)"
                  @search="handleSearch"
                  show-arrow
                  :options="settleCompany"
                  :field-names="{ label: 'name', value: 'code' }"
                >
                </h-select>
              </div>
            </div>
          </h-form-item>
          <h-form-item
            label="预算部门"
            name="budgetDepartmentCode"
            v-if="permission.dataType == 1"
          >
            <h-select
              placeholder="请选择预算部门（支持多选）"
              :filter-option="filterOption"
              v-model:value="permission.budgetDepartmentCode"
              mode="multiple"
              show-search
              @change="(value, option) => handleChangeDepartment(value as string, option as Array<ApplyCompanyType>)"
              @search="handleSearchDepartment"
              show-arrow
              :options="settleDepartment"
              :field-names="{ label: 'name', value: 'code' }"
            >
            </h-select>
          </h-form-item>
          <h-form-item
            label="领域"
            name="fieldCode"
            v-if="permission.dataType == 3"
          >
            <h-select
              placeholder="请选择领域（支持多选）"
              :filter-option="filterOption"
              v-model:value="permission.fieldCode"
              mode="multiple"
              show-search
              @change="(value, option) => handleChangeArea(value as string, option as Array<ApplyCompanyType>)"
              @search="handleAreaSearch"
              show-arrow
              :options="areaList"
              :field-names="{ label: 'name', value: 'code' }"
            >
            </h-select>
          </h-form-item>

          <h-form-item
            label="产业线"
            name="plCode"
            v-if="permission.dataType == 5"
          >
            <h-select
              placeholder="请选择产业线（支持多选）"
              :filter-option="filterOption"
              v-model:value="permission.plCode"
              mode="multiple"
              show-search
              @change="(value, option) => handleChangePl(value as string, option as Array<ApplyCompanyType>)"
              @search="handlePlSearch"
              show-arrow
              :options="industryList"
              :field-names="{ label: 'name', value: 'code' }"
            >
            </h-select>
          </h-form-item>

          <h-form-item
            label="平台"
            name="ptCode"
            v-if="permission.dataType == 4"
          >
            <h-select
              placeholder="请选择平台（支持多选）"
              :filter-option="filterOption"
              v-model:value="permission.ptCode"
              mode="multiple"
              show-search
              @change="(value, option) => handleChangePt(value as string, option as Array<ApplyCompanyType>)"
              @search="handlePtSearch"
              show-arrow
              :options="platformList"
              :field-names="{ label: 'name', value: 'code' }"
            >
            </h-select>
          </h-form-item>
        </template>

        <template v-if="permission.applyType == 2">
          <h-form-item
            label="结算单位"
            name="accountCompanyCode"
            v-if="permission.dataType == 2"
          >
            <h-upload
              :file-list="fileList"
              :before-upload="beforeUpload"
              :max-count="1"
              @remove="handleRemove"
              @change="handleUploadChange"
            >
              <h-button> <upload-outlined></upload-outlined> 上传附件 </h-button>
            </h-upload>
            <a class="down" @click="downTemplate(1)">下载模板</a>
          </h-form-item>

          <h-form-item
            label="预算部门"
            name="budgetDepartmentCode"
            v-if="permission.dataType == 1"
          >
            <h-upload
              :file-list="fileList1"
              :before-upload="beforeYSUpload"
              @remove="handleYSRemove"
              :max-count="1"
              @change="handleYSUploadChange"
            >
              <h-button> <upload-outlined></upload-outlined> 上传附件 </h-button>
            </h-upload>
            <a class="down" @click="downTemplate(2)">下载模板</a>
          </h-form-item></template
        >
          <h-form-item label="使用人" name="employeeId">
              <user-select
                :value="permission.employeeName"
                placeholder="请选择用户"
                :params="{
                  pageNum: 1,
                  pageSize: 20,
                }"
                @change="(userInfo: any) =>  changeUser(userInfo)"
              ></user-select>
          </h-form-item>
          <h-form-item label="审批流" name="approvalFlowType">
            <h-radio-group v-model:value="permission.approvalFlowType">
              <h-radio :value="0">经办人</h-radio>
              <h-radio :value="1">使用人</h-radio>
            </h-radio-group>
          </h-form-item>
          <h-form-item label="权限有效起始日期" name="permissionValidTime">
            <h-range-picker
              :disabled-date="disabledDate"
              v-model:value="permission.permissionValidTime"
              value-format="YYYY-MM-DD"
            >
              <template #renderExtraFooter>
                <h-button @click="onFuture">未来一年</h-button>
              </template></h-range-picker
            >
          </h-form-item>
          <h-form-item label="申请原因" name="approveReason">
            <h-textarea
              v-model:value="permission.approveReason"
              placeholder="请输入申请原因"
              :rows="4"
            />
          </h-form-item>

          <h-row justify="center">
            <div class="flexCon">
              <h-space>
                <h-button @click="handleReset">重置</h-button>
                <h-button type="primary" html-type="submit" :loading="confirmLoading"
                  >申请</h-button
                >
              </h-space>
            </div>
            <h-checkbox v-model:checked="checked">
              <div class="checkbox">
                本人在此郑重承诺，在使用系统过程中，我将严格遵守海尔集团信息系统相关规定，如有违反，本人将配合有关部门对自身问题进行调查、还原接受集团根据相关规定做出来的通报、批评甚至开除等处理措施及决定，承担由此产生的责任和后果，并赔偿相应损失。
              </div>
            </h-checkbox>
          </h-row>
        </h-form>
    </div>
  </h-modal>
  <h-modal
    v-model:visible="visible"
    title="是否确认"
    @ok="handleSubmit"
    :confirmLoading="loading"
    okText="确认提交"
  >
    <div class="font">
      <b> 申请成功后，后续审批进度及审批结果，请留意邮箱邮件。或保存网址查看</b>
      <a target="_blank" :href="permissionUrl">{{ permissionUrl }}</a>
    </div>
  </h-modal>
</template>

<script setup lang="ts">
import {
  Modal as HModal,
  Upload as HUpload,
  Textarea as hTextarea,
  Card as hCard,
  Space as hSpace,
  Checkbox as hCheckbox,
  Button as hButton,
  Form as hForm,
  FormItem as hFormItem,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  SelectOption as hSelectOption,
} from "ant-design-vue";
import { SearchOutlined, UploadOutlined } from "@ant-design/icons-vue";
import ReportItem from '../components/reportItem.vue';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { message } from "ant-design-vue"; 
import { computed, ref, watch, onMounted } from "vue";
import type { Ref } from "vue";
import { businessType } from "../../report/columns";
import { getCurrentRouter, routerParam } from "@haierbusiness-front/utils";
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
const { loginUser } = storeToRefs(applicationStore(globalPinia));
const router = getCurrentRouter();
interface Props {
  show: boolean;
  data:any,
}
const permissionUrl = import.meta.env.VITE_JUMP_URL + 'hbweb/data/#/data/report/permission/index'

const props = withDefaults(defineProps<Props>(), {
  show: false
});

const modalShow = computed(() => props.show);
const visibleFormShow = ref<boolean>(false)
import type { UploadChangeParam, UploadProps } from "ant-design-vue";
import { ApplyCompanyType, ApplyType } from "@haierbusiness-front/common-libs";
import { smartBrainApi, reportApi } from "@haierbusiness-front/apis";
import dayjs, { Dayjs } from "dayjs";

const downTemplate = async (type) => {
  if (type == 1) reportApi.exportFggygList();
  if (type == 2) reportApi.exportYsList();
};
const onDataTypeChange = (e) => {
  permission.value.budgetDepartmentCode = [];
  permission.value.budgetDepartmentName = [];
  permission.value.accountCompanyCode = [];
  permission.value.accountCompanyName = [];
  permission.value.ptCode = [];
  permission.value.ptName = [];

  permission.value.plCode = [];
  permission.value.plName = [];

  permission.value.fieldCode = [];
  permission.value.fieldName = [];
  permission.value.settlementUnitExcel = [];
  permission.value.budgetDepartmentExcel = [];
  fileList.value = [];
  fileList1.value = [];
};
const fileList: any = ref([]);
const fileList1: any = ref([]);
const beforeYSUpload: UploadProps["beforeUpload"] = (file) => {
  fileList1.value = [file];
  return false;
};
const beforeUpload: UploadProps["beforeUpload"] = (file) => {
  fileList.value = [file];
  return false;
};
const handleYSRemove: UploadProps["onRemove"] = (file) => {
  const index = fileList1.value.indexOf(file);
  const newFileList = fileList1.value.slice();
  newFileList.splice(index, 1);
  fileList1.value = newFileList;
};
const handleRemove: UploadProps["onRemove"] = (file) => {
  const index = fileList.value.indexOf(file);
  const newFileList = fileList.value.slice();
  newFileList.splice(index, 1);
  fileList.value = newFileList;
};
const handleYSUploadChange = (info: UploadChangeParam) => {
  if (info.file.status !== "uploading") {
    permission.value.budgetDepartmentExcel = info.file;
  }
  if (info.file.status === "done") {
    message.success(`${info.file.name} file uploaded successfully`);
  } else if (info.file.status === "error") {
    message.error(`${info.file.name} file upload failed.`);
  }
};
const handleUploadChange = (info: UploadChangeParam) => {
  if (info.file.status !== "uploading") {
    permission.value.settlementUnitExcel = info.file;
  }
  if (info.file.status === "done") {
    message.success(`${info.file.name} file uploaded successfully`);
  } else if (info.file.status === "error") {
    message.error(`${info.file.name} file upload failed.`);
  }
};

const disabledDate = (current: Dayjs) => {
  return (
    (current && current < dayjs().startOf("day")) ||
    (current && current > dayjs().add(1, "year").startOf("day"))
  );
};

const yesterday = dayjs().subtract(1, "days");
const pattern = "YYYY-MM-DD";

const onFuture = () => {
  permission.value.permissionValidTime = [
    dayjs(yesterday).format(pattern),
    dayjs(yesterday).add(1, "year").format(pattern),
  ];
};

const emit = defineEmits(['cancel', 'ok', "remove"]);

const checked = ref(false);
const from = ref();
const confirmLoading = ref(false);
const loading = ref(false);
const businessTypeFilter = computed(() => {
  // let arr: any = businessType.filter(
  //     (item) =>
  //         item.text.indexOf('差旅') == -1
  // );
  // arr.push({ key: 'travel', text: '商旅' })

  if (permission.value.moduleType == 1) {
    //  return [{ key: 'travel', text: '商旅' }, { key: 'mealService', text: '餐务' }]
    return [{ key: "travel", text: "商旅" }];
  } else {
    return [
      { key: "travel", text: "商旅" },
      { key: "mealService", text: "餐务" },
      // { key: "meetingAffairs", text: "会务" },
    ];
  }
});

const confirm = () =>{
  visibleFormShow.value = true
}

const showTypeText = ref(false);
const handleTypeChange = (e: any) => {
  if (e === "travel") {
    showTypeText.value = true;
  } else {
    showTypeText.value = false;
  }
};

const handleChange = (value: string, option: Array<ApplyCompanyType>) => {
  let result = option.map((item) => {
    return item.name;
  });
  permission.value.accountCompanyName = result;
};
const rules = {
  permissionValidTime: [
    { required: true, message: "请选择权限有效日期", trigger: "change" },
  ],businessType: [
    { required: true, message: "必选项不能为空!", trigger: "change" },
  ],
  approveReason: [{ required: true, message: "请填写申请原因！", trigger: "blur" }],
};

const permission: Ref<any> = ref({
  moduleType: 4,
  applyType: 1,
  dataType: 1,
  approvalFlowType:0,
  accountCompanyCode: [],
  accountCompanyName: [],
  businessType: "",
  budgetDepartmentCode: [],
  budgetDepartmentName: [],
  areaCode:[],
  areaName:[],
  fieldCode: [],
  fieldName: [],
  plCode: [],
  plName: [],
  ptCode: [],
  ptName: [],
  // businessDataTime: [] as string[],
  permissionValidTime: [] as string[],
  approveReason: "",
  budgetDepartmentExcel: "",
  settlementUnitExcel: "",
});
const visible = ref(false);

const handleSubmit = async () => {
  try {
    loading.value = true;
    const forms = new FormData();
    if (permission.value.applyType == 1) {
      forms.append(
        "budgetDepartmentCode",
        JSON.stringify(permission.value.budgetDepartmentCode)
      );
      forms.append(
        "budgetDepartmentName",
        JSON.stringify(permission.value.budgetDepartmentName)
      );
      forms.append(
        "accountCompanyCode",
        JSON.stringify(permission.value.accountCompanyCode)
      );
      forms.append(
        "accountCompanyName",
        JSON.stringify(permission.value.accountCompanyName)
      );
      forms.append(
        "fieldCode",
        JSON.stringify(permission.value.fieldCode)
      );
      forms.append(
        "fieldName",
        JSON.stringify(permission.value.fieldName)
      );

      forms.append(
        "plCode",
        JSON.stringify(permission.value.plCode)
      );
      forms.append(
        "plName",
        JSON.stringify(permission.value.plName)
      );

      forms.append(
        "ptCode",
        JSON.stringify(permission.value.ptCode)
      );
      forms.append(
        "ptName",
        JSON.stringify(permission.value.ptName)
      );
    }
    forms.append("budgetDepartmentExcel", permission.value.budgetDepartmentExcel);
    forms.append("settlementUnitExcel", permission.value.settlementUnitExcel);
    forms.append("permissionValidTimeStart", permission.value.permissionValidTime[0]);
    forms.append("permissionValidTimeEnd", permission.value.permissionValidTime[1]);
    forms.append("permissionString", JSON.stringify(columns.value));
    forms.append("approveReason", permission.value.approveReason);
    // forms.append("businessType", permission.value.businessType);
    forms.append("businessType", 'brain');
    
    forms.append("moduleType", permission.value.moduleType);
    forms.append("approvalFlowType", permission.value.approvalFlowType);
    forms.append("employeeName", permission.value.employeeName);
    forms.append("employeeId", permission.value.employeeId);
    let applyReportIds:any = []
    props.data.forEach((item:any)=>{
      applyReportIds.push(item.id)
    })
    applyReportIds = applyReportIds.toString()
    forms.append("applyReportIds", applyReportIds);

    const data = await smartBrainApi.postPermissionApprove(forms);
    if (data) {
      message.success("申请成功");
      visible.value = false;
      visibleFormShow.value = false;
      router.push("/data/report/permission/index");
      emit("cancel")
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
  }
};
const changeUser = (userInfo: any) => {
  if(userInfo){
    permission.value.employeeId = userInfo.username;
    permission.value.employeeName = userInfo.nickName;
  }else{
    permission.value.employeeId = ''
    permission.value.employeeName = ''
  }
};
const handleOk = async () => {
  if (checked.value) {
    console.log(permission.value, "permission.valuepermission.value");
    if (
      permission.value.applyType == 1 &&
      permission.value.accountCompanyCode.length == 0 &&
      permission.value.budgetDepartmentCode.length == 0 &&
      permission.value.fieldCode.length == 0 &&
      permission.value.ptCode.length == 0 &&
      permission.value.plCode.length == 0
    ) {
      message.warning("预算部门,结算单位,领域,平台和产业线至少选一个!");
    } else if (
      permission.value.applyType == 2 &&
      permission.value.budgetDepartmentExcel == "" &&
      permission.value.settlementUnitExcel == ""
    ) {
      message.warning("预算部门和结算单位至少上传一个!");
    } else {
      visible.value = true;
    }
  } else {
    message.warning("请同意协议");
  }
};

//报表列表
const columns = computed(() => {
  const selected = businessType.find((item) => item.key == permission.value.businessType);
  return selected?.columns ?? [];
});

const handleReset = () => {
  showTypeText.value = false;
  from.value && from.value.resetFields();
};

const settleCompany = ref([] as Array<ApplyCompanyType>);
const filterOption = (input: string, option: any) => {
  return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const handleSearch = (val: string) => {
  getPowerByApprove(val,2);
};

const settleDepartment = ref([]);
const settleArea = ref([])
const handleSearchDepartment = (val: string) => {
  getPowerByApprove(val,1);
};
const handleChangeDepartment = (value: string, option: Array<ApplyCompanyType>) => {
  let result = option.map((item) => {
    return item.name;
  });
  permission.value.budgetDepartmentName = result;
};
// 删除指标
const removeReportItem = (item:any) =>{
  emit('remove',item)
}


const querySettleArea = async (keyword: string) => {
  //查询领域
  const data = await reportApi.queryApplySettleArea(keyword);
  if (data && data.length > 0) {
    settleArea.value = data;
  }
};

const handleSearchArea = (val: string) => {
  querySettleArea(val);
};


const handleChangeArea = (value: string, option: Array<ApplyCompanyType>) => {
  let result = option.map((item) => {
    return item.name;
  });
  permission.value.fieldName = result;
};

const handleChangePt = (value: string, option: Array<ApplyCompanyType>) => {
  let result = option.map((item) => {
    return item.name;
  });
  permission.value.ptName = result;
};

const handleChangePl = (value: string, option: Array<ApplyCompanyType>) => {
  let result = option.map((item) => {
    return item.name;
  });
  permission.value.plName = result;
};


// 查询领域、平台、产业线
const areaList= ref([]);
const platformList= ref([]);
const industryList= ref([]);

// 根据类型查询不同权限类型 3:领域 4:平台 5:产业线
const getPowerByApprove = async (name: string, permissionType: number) => {
  const data = await reportApi.getPowerAllByType(name,permissionType);
  switch (permissionType) {
    case 1:
      settleDepartment.value = data;
      break;
    case 2:
      settleCompany.value = data;
    case 3:
      areaList.value = data;
      break;
    case 4:
      platformList.value = data;
      break;
    case 5:
      industryList.value = data;
      break;
    default:
      break;
  }
};

const handleAreaSearch = (val: string) => {
  getPowerByApprove(val,3);
};

const handlePtSearch = (val: string) => {
  getPowerByApprove(val,4);
};

const handlePlSearch = (val: string) => {
  getPowerByApprove(val,5);
};

onMounted(() => {
  // querySettleArea()
  getPowerByApprove("青岛", 1);
  getPowerByApprove("青岛", 2);
  getPowerByApprove("", 3);
  getPowerByApprove("", 4);
  getPowerByApprove("", 5);

  // 默认使用人是登录人
  permission.value.employeeName = loginUser.value.nickName
  permission.value.employeeId = loginUser.value.username
});
</script>

<style lang="less" scoped>
.btnCon {
  min-height: 48px;
  border-bottom: 1px solid #f0f0f0;
}

.flexCon {
  display: flex;
}
.items-center {
  align-items: center;
  a {
    margin-left: 10px;
  }
}
.down {
  margin-top: 10px;
  display: block;
}

.w-500 {
  width: 600px;
  margin: 0 auto;
}

.checkbox {
  margin-top: 20px;
  font-size: 12px;
}

.font {
  color: #333;
  line-height: 2;
}
.reportBox{
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .indexItemBox{
    width: 49%;
  }
}
</style>
