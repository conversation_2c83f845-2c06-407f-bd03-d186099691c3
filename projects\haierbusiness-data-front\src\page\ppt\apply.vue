<template>
  <h-card title="分析报告导出" style="padding: 8px">
    <div class="w-500">
      <h-form
        ref="from"
        :model="permission"
        :label-col="{ span: 7 }"
        :wrapper-col="{ span: 17 }"
        :rules="rules"
        @finish="handleOk"
      >
        <h-form-item label="文件类型" name="downloadType">
          <h-radio-group v-model:value="permission.downloadType">
            <h-radio value="PPT">PPT</h-radio>
            <!-- <h-radio value="PDF">PDF</h-radio> -->
          </h-radio-group>
        </h-form-item>
        <h-form-item label="申请数据维度" name="dataType">
          <h-radio-group v-model:value="permission.dataType" @change="onDataTypeChange">
            <h-radio :value="1">预算部门</h-radio>
            <h-radio :value="2">结算单位</h-radio>
          </h-radio-group>
        </h-form-item>
        <h-form-item label="结算单位" name="accountCompanyCodeList" v-if="permission.dataType == 2">
          <h-select
            placeholder="请搜索结算单位（支持多选）"
            v-model:value="permission.accountCompanyCodeList"
            mode="multiple"
            show-search
            :filter-option="filterOption"
            @change="(value, option) => handleChange(value as string, option as Array<ApplyCompanyType>)"
            @search="handleSearch"
            show-arrow
            :options="settleCompany"
            :field-names="{ label: 'name', value: 'code' }"
          >
          </h-select>
        </h-form-item>
        <h-form-item
          label="预算部门"
          name="budgetDepartmentCodeList"
          v-if="permission.dataType == 1"
        >
          <h-select
            placeholder="请选择预算部门（支持多选）"
            :filter-option="filterOption"
            v-model:value="permission.budgetDepartmentCodeList"
            mode="multiple"
            show-search
            @change="(value, option) => handleChangeDepartment(value as string, option as Array<ApplyCompanyType>)"
            @search="handleSearchDepartment"
            show-arrow
            :options="settleDepartment"
            :field-names="{ label: 'name', value: 'code' }"
          >
          </h-select>
        </h-form-item>
        <h-form-item label="查询起始日期" name="permissionValidTime">
          <h-range-picker
            :disabled-date="disabledDate"
            v-model:value="permission.permissionValidTime"
            value-format="YYYY-MM-DD"
          >
            <template #renderExtraFooter>
              <h-button style="margin-right: 10px" @click="onOver(1, 'years')"
                >最近一年</h-button
              >
              <h-button style="margin-right: 10px" @click="onOver(6, 'month')"
                >最近半年</h-button
              >
              <h-button @click="onOver(3, 'month')">最近三个月</h-button>
            </template>
          </h-range-picker>
        </h-form-item>

        <h-row justify="center">
          <div class="flexCon">
            <h-space>
              <h-button @click="handleReset">重置</h-button>
              <h-button type="primary" html-type="submit" :loading="confirmLoading"
                >导出</h-button
              >
            </h-space>
          </div>
          <h-checkbox v-model:checked="checked">
            <div class="checkbox">
              本人在此郑重承诺，在使用系统过程中，我将严格遵守海尔集团信息系统相关规定，如有违反，本人将配合有关部门对自身问题进行调查、还原接受集团根据相关规定做出来的通报、批评甚至开除等处理措施及决定，承担由此产生的责任和后果，并赔偿相应损失。
            </div>
          </h-checkbox>
        </h-row>
      </h-form>
    </div>
  </h-card>
</template>

<script setup lang="ts">
import {
  Modal as HModal,
  Textarea as hTextarea,
  Card as hCard,
  Space as hSpace,
  Checkbox as hCheckbox,
  Button as hButton,
  Form as hForm,
  FormItem as hFormItem,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  SelectOption as hSelectOption,
} from 'ant-design-vue';
import { SearchOutlined } from '@ant-design/icons-vue';
import { checkUserGroups } from "@haierbusiness-front/utils/src/authorityUtil";

import { message } from 'ant-design-vue';
import { computed, ref, watch, onMounted } from "vue";
import type { Ref } from "vue";
import { getCurrentRouter, routerParam } from "@haierbusiness-front/utils";
const router = getCurrentRouter();
import { useSearch } from "../../composables/useSearch";
import { HeaderConstant } from "@haierbusiness-front/common-libs";
import { loadDataFromLocal } from "@haierbusiness-front/utils/src/storageUtil";

import {
  ApplyCompanyType,
  ApplyType,
  UserGroupSystemConstant,
} from "@haierbusiness-front/common-libs";
import { applyApi, reportApi } from "@haierbusiness-front/apis";
import dayjs, { Dayjs } from "dayjs";
const disabledDate = (current: Dayjs) => {
  return current && current > dayjs().startOf("day");
};

const yesterday = dayjs().subtract(1, "days");
const pattern = "YYYY-MM-DD";

const onOver = (number, type) => {
  permission.value.permissionValidTime = [
    dayjs(yesterday).format(pattern),
    dayjs(yesterday).subtract(number, type).format(pattern),
  ];
};

const checked = ref(false);
const from = ref();
const confirmLoading = ref(false);

const showTypeText = ref(false);

const handleChange = (value: string, option: Array<ApplyCompanyType>) => {
  let result = option.map((item) => {
    return item.name;
  });
  permission.value.accountCompanyName = result;
};
const rules = {
  permissionValidTime: [
    { required: true, message: "请选择权限有效日期", trigger: "change" },
  ],
};

const onDataTypeChange = (e) => {
  permission.value.budgetDepartmentCodeList = [];
  permission.value.budgetDepartmentName = [];
  permission.value.accountCompanyCodeList = [];
  permission.value.accountCompanyName = [];
};
const permission: Ref<any> = ref({
  dataType: 1,
  accountCompanyCode: [],
  accountCompanyName: [],
  budgetDepartmentCode: [],
  budgetDepartmentName: [],
  // businessDataTime: [] as string[],
  permissionValidTime: [
    dayjs().subtract(30, "days").format(pattern),
    yesterday.format(pattern),
  ] as string[],
  approveReason: "",
  downloadType: "PPT",
});
const visible = ref(false);

const BASE_URL: string = import.meta.env.VITE_API_BASE_URL;
const download = () => {
  confirmLoading.value = true;
  const { url, params } = applyApi.download({
    fileName: "商务服务分析报告",
    ...permission.value,
    accountCompanyCode: permission.value.value,
    accountCompanyName: null,
    budgetDepartmentName: null,
    budgetDepartmentCode: permission.value.budgetDepartmentCode,
    startDate: permission.value.permissionValidTime[0],
    endDate: permission.value.permissionValidTime[1],
  });

  reportApi
    .exportPPTList({
      ...params,
      downloadParams: JSON.stringify(params["downloadParams"]),
    })
    .then(() => {
      confirmLoading.value = false;
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};

const handleSubmit = async () => {
  try {
    confirmLoading.value = true;
    download();
  } catch (error) {
    confirmLoading.value = false;
  }
};

const handleOk = async () => {
  if (checked.value) {
    handleSubmit();
  } else {
    message.warning("请同意协议");
  }
};

const handleReset = () => {
  from.value && from.value.resetFields();
};

const settleCompany = ref([] as Array<ApplyCompanyType>);
const filterOption = (input: string, option: any) => {
  return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const handleSearch = (val: string) => {
  querySettleCompany({ type: "analysisReport", moduleType: 3, keyword: val });
};
const querySettleCompany = async (keyword: string) => {
  //查询结算单位
  const data = await reportApi.querySettleCompany(keyword);
  if (data && data.length > 0) {
    settleCompany.value = data;
  }
};

const settleDepartment = ref([]);
const querySettleDepartment = async (keyword: string) => {
  //查询部门
  const data = await reportApi.querySettleDepartment(keyword);
  if (data && data.length > 0) {
    settleDepartment.value = data;
  }
};

const handleSearchDepartment = (val: string) => {
  querySettleDepartment({ type: "analysisReport", moduleType: 3, keyword: val });
};
const handleChangeDepartment = (value: string, option: Array<ApplyCompanyType>) => {
  let result = option.map((item) => {
    return item.name;
  });
  permission.value.budgetDepartmentName = result;
};

onMounted(() => {
  getPowerByApprove("", 1);
  getPowerByApprove("", 2);
});

// 根据类型查询不同权限类型 permissionType 3:领域 4:平台 5:产业线
const getPowerByApprove = async (name: string, permissionType: number) => {
  const data = await reportApi.getPowerByApprove(name, permissionType, 3, 'travel');
  switch (permissionType) {
    case 1:
      settleDepartment.value = data;
      break;
    case 2:
      settleCompany.value = data;
    default:
      break;
  }
};
</script>

<style lang="less" scoped>
.btnCon {
  min-height: 48px;
  border-bottom: 1px solid #f0f0f0;
}

.flexCon {
  display: flex;
}

.w-500 {
  width: 500px;
  margin: 0 auto;
}

.checkbox {
  margin-top: 20px;
  font-size: 12px;
}

.font {
  color: #333;
  line-height: 2;
}
</style>
