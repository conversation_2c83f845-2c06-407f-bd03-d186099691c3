<script setup lang="ts">
import {
  Button as hButton,
  Card as hCard,
  Row as hRow,
  Col as hCol,
} from 'ant-design-vue';
import { ref, onMounted, h, computed, inject } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { meetingHandoversApi, } from '@haierbusiness-front/apis';
import { resolveParam, routerParam } from '@haierbusiness-front/utils';
const route = useRoute();
const router = useRouter();
const record = resolveParam(route.query.record as string);
console.log('record', record);
const hideBtn = record?.hideBtn || '';
const localUrl = window.location.href;
// 获取并设置frameModel
const frameModel = inject<any>('frameModel');
if (frameModel) {
  frameModel.value = hideBtn === '1' ? 1 : 0;
}
const loading = ref(false);
const detailData = ref<any>({});
const id = computed(() => record?.id || (route.query.id as string));

// 获取详情数据
const getDetail = async () => {
  console.log(id.value);

  if (!id.value) return;

  loading.value = true;
  try {
    const res = await meetingHandoversApi.details(id.value);
    detailData.value = res;
  } catch (error) {
    console.error('获取详情失败:', error);
  } finally {
    loading.value = false;
  }
};
// 获取状态文本
const getStateText = (state: any) => {
  if (state) {
    state = state == 10 ? '交接中' : state === 20 ? '已完成' : state === 30 ? '已驳回 ' : '已撤回';
  }
  return state
};
onMounted(() => {
  getDetail()
})
</script>
<template>
  <div class="container">
    <!-- 基本信息 -->
    <h-card :loading="loading">
      <div class="info-section">
        <h3><span></span>会议信息</h3>
        <h-row :gutter="[16, 16]">
          <h-col :span="6">
            <div class="info-item">
              <span class="label">经办人：</span>
              <span class="value">{{ detailData.connectBeforeName }}</span>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="info-item">
              <span class="label">经办人电话：</span>
              <span class="value">{{ detailData.connectBeforePhone }}</span>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="info-item">
              <span class="label">经办人邮箱：</span>
              <span class="value">{{ detailData.connectBeforeEmail }}</span>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="info-item">
              <span class="label">经办人工号：</span>
              <span class="value">{{ detailData.connectBeforeCode }}</span>
            </div>
          </h-col>
        </h-row>
      </div>
      <div class="info-section">
        <h3><span></span>承接人信息</h3>
        <h-row :gutter="[16, 16]">
          <h-col :span="6">
            <div class="info-item">
              <span class="label">承接人：</span>
              <span class="value">{{ detailData.handoverAfterName }}</span>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="info-item">
              <span class="label">承接人电话：</span>
              <span class="value">{{ detailData.handoverAfterPhone }}</span>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="info-item">
              <span class="label">承接人邮箱：</span>
              <span class="value">{{ detailData.handoverAfterEmail }}</span>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="info-item">
              <span class="label">承接人工号：</span>
              <span class="value">{{ detailData.handoverAfterCode }}</span>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="info-item">
              <span class="label">承接人直线：</span>
              <span class="value">{{ detailData.handoverLineName }}</span>
            </div>
          </h-col>
        </h-row>
      </div>
      <div class="info-section">
        <h3><span></span>承接信息</h3>
        <h-row :gutter="[16, 16]">
          <h-col :span="6">
            <div class="info-item">
              <span class="label">状态：</span>
              <span class="value">{{ getStateText(detailData.handoverState) }}</span>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="info-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ detailData.gmtCreate }}</span>
            </div>
          </h-col>
        </h-row>
      </div>
      <div class="info-section">
        <h-row :gutter="[16, 16]">
          <h-col :span="6">
            <div class="info-item">
              <span class="label">承接原因：</span>
              <span class="value">{{ detailData.handoverReason }}</span>
            </div>
          </h-col>
        </h-row>
      </div>
    </h-card>
  </div>


</template>
<style lang="scss" scoped>
.container {
  height: 100%;
  padding: 15px;
  background-color: #fff;
}

.info-section {
  margin-bottom: 24px;

  h3 {
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 500;
  }
}

.info-item {
  display: flex;
  align-items: flex-start;

  .label {
    color: rgba(0, 0, 0, 0.55);
    // min-width: 130px;
  }

  .value {
    color: #000;
    flex: 1;
    word-break: break-all;
  }
}

h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;

  span {
    display: inline-block;
    width: 4px;
    height: 20px;
    margin-right: 3px;
    background: #1868DB;
  }
}

.ant-card-bordered {
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0px 3px 12px 0px rgba(1, 12, 51, 0.07);
}
</style>