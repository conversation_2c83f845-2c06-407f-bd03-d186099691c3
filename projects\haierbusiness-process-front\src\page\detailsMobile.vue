<script setup lang="ts">
import { Row as hRow,Tag as hTag, Col as hCol, Tag, showFailToast, FloatingPanel, Step as hStep, Steps as hSteps, Button  as hButton, Field, Popover, showToast } from 'vant';
import 'vant/es/row/style'
import 'vant/es/col/style'
import 'vant/es/floating-panel/style'
import 'vant/es/cell/style'
import 'vant/es/cell-group/style'
import 'vant/es/tag/style'
import 'vant/es/step/style'
import 'vant/es/steps/style'
import 'vant/es/button/style'
import 'vant/es/cell/style'
import 'vant/es/cell-group/style'
import 'vant/es/field/style'
import 'vant/es/popover/style'
import finishIcon from '@/assets/image/finish.png';
import runningIcon from '@/assets/image/running.png';
import revokeIcon from '@/assets/image/revoke.png';
import rejectIcon from '@/assets/image/reject.png';
import logo from '@/assets/image/logo.png';
import { Avatar as hAvatar, Tooltip as hTooltip } from 'ant-design-vue';
import { CheckCircleOutlined, MinusCircleOutlined, StopOutlined, DownOutlined, ExclamationCircleOutlined, PlusOutlined, UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { processApi } from '@haierbusiness-front/apis';
import { computed, ref, onMounted, IframeHTMLAttributes } from 'vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { ProcessStepStateConstant, ProcessStateConstant, IProcessRecordStep, ProcessStepOperatorStateConstant,ProcessNotificationMethodConstant, ProcessRecordStepConstant } from '@haierbusiness-front/common-libs';
import { copyValue } from '@haierbusiness-front/utils'

const store = applicationStore()
const { loginUser } = storeToRefs(store)

const avatarName = computed(() => {
  if (loginUser?.value?.nickName) {
    const nickName = loginUser.value.nickName
    if (nickName.length === 2) {
      return nickName
    } else if (nickName.length > 2 && nickName.length <= 5) {
      return nickName.substring(nickName.length - 2, nickName.length)
    } else {
      return nickName.substring(0, 1).toUpperCase()
    }
  } else {
    return "李白"
  }
});

const isCurrentUser = computed(() => {
  if (loginUser?.value) {
    return (loginUser?.value?.username === detailsApiData.value?.processRecord?.applicantUser) && (detailsApiData?.value?.processRecord?.state == ProcessStateConstant.APPROVAL.type)
  }
  else
    return false
})

const result = ref();
const todoId = computed(
  () => {
    const urlSearchParams = new URLSearchParams(window.location.search);
    const tId = urlSearchParams.get("todoId");
    const code = urlSearchParams.get("code");
    if (tId || code) {
      if (tId) {
        return parseInt(tId);
      }
    }
    else {
      showFailToast("待办不存在!")
    }
  }
)

const code = computed(
  () => {
    const urlSearchParams = new URLSearchParams(window.location.search);
    const code = urlSearchParams.get("code");
    if (code) {
      return code;
    }
  }
)

const remark = ref("");

const {
  data: executeApiData,
  run: executeApiRun,
  loading: executeApiLoading,
} = useRequest(
  processApi.execute,
  {
    onSuccess: () => {
      detailsApiRun({ todoId: todoId.value, code: code.value })
    }
  }
);

const {
  data: revokeData,
  run: revokeApiRun,
  loading: revokeApiLoading,
} = useRequest(
  processApi.revoke,
  {
    onSuccess: () => {
      detailsApiRun({ todoId: todoId.value, code: code.value })
    }
  }
);

// 短信重发
const {
  data: smsResendData,
  run: smsResend,
  loading: smsResendLoading,
} = useRequest(
  processApi.reSend,
  {
    onSuccess: () => {
      // detailsApiRun({ todoId: todoId.value, code: code.value })
      showToast('审批短信重新发送成功')
    }
  }
);

const rejectVisible = ref(false)

const showReject = () => {
  rejectVisible.value = true
  visible.value = false
  height.value = anchors[1]
}

const hideReject = () => {
  rejectVisible.value = false
}

const reject = () => {
  if(!remark.value) {
    showFailToast("请填写驳回意见!")
    return
  }
  result.value = 2
  executeApiRun({ todoId: todoId.value,processCode:detailsApiData.value?.processRecord?.code, result: 2, remark: remark.value })
  rejectVisible.value = false
}

const todoMethod = ref<any>([])

const {
  data: detailsApiData,
  run: detailsApiRun,
  loading: detailsApiLoading,
} = useRequest(
  processApi.details, {
  defaultParams: [
    {
      todoId: todoId.value,
      code: code.value
    }
  ],
  onSuccess: () => {
    todoMethod.value = computedMethod(detailsApiData.value?.processRecord?.todoMethod)
  },
  manual: false
}
);

const computedMethod = (num: number | undefined)=> {
    if(!num) {
        return
    }

    const list = ProcessNotificationMethodConstant.toNumberArray()
    console.log(list)
    // method
    const array: number[] = []
    list.map(item => {
        if((num & item!) != 0) {
            array.push(item!)
        }
    })
    return array
}

const current = ref<IProcessRecordStep>({})
const currentStep = computed(
  () => {
    let index = 0;
    if (detailsApiData.value?.processRecord?.steps) {
      for (const i of detailsApiData.value?.processRecord?.steps) {
        if(i.result == 2 || i.result == 5) {
          current.value = i;
          return index;
        }
        if (i.state == ProcessStepStateConstant.APPROVAL.type) {
          current.value = i;
          return index;
        }
        index++;
      }
      return index;
    }
  }
);

const currentApprove = computed(
  () => {
    if (current?.value?.operators) {
      for (let i of current?.value?.operators) {
        if (i.approverCode === loginUser.value?.username) {
          return true;
        }
      }
      return false
    }
    return false
  })


const fold = ref(false)

const src = computed(() => {
  return detailsApiData.value?.processRecord?.mobileOrderDetailsUrl || detailsApiData.value?.processRecord?.orderDetailsUrl
})

const scrollHeight = ref(document.body.scrollHeight)
const foldFooter = () => {
  if (fold.value) {
    fold.value = false
  } else if (!fold.value) {
    fold.value = true
  }
}

// 浮动面板

const anchors = [
  170,
  500
]
const visible = ref(true)

const height = ref(170);

const heightChange = (value: any) => {
  height.value = value.height
  if(value.height === anchors[1]) {
    visible.value = false
  } else {
    visible.value = true
    rejectVisible.value = false
  }
}

const getResult = (name: string, result: number | undefined, time: string) => {
  if(result || time) {
    name += ':'
    if(time) {
      name += time
    }
    if(result) {
      const resultStr = ProcessRecordStepConstant.ofType(result)
      if (resultStr) {
        name += '，' + resultStr.name
      }
    }
  }
  return name
}

const copy = (value: string) => {
  copyValue(value)
}

</script>

<template>
  <div :style="{ height: scrollHeight + 'px', backgroundColor: '#fffff' }" class="mobile">
    <h-row class="header">
      <h-col :span="8" :offset="1">
        <img :src="logo" class="logo-img" />
      </h-col>
      <h-col :span="15" style="text-align: right;">
        <h-avatar :title="loginUser?.username" size="large" 
        style="color: #f56a00; background-color: #fde3cf;user-select: none;margin-top: 4px;margin-right: 20px;">{{ avatarName }}</h-avatar>
      </h-col>
    </h-row>
    <h-row class="details_iframe_body">
      <iframe :src="src" frameborder="0" :class="{'details_iframe' : !detailsApiData?.processRecord?.mobileOrderDetailsUrl, 'details_iframe_mobile': detailsApiData?.processRecord?.mobileOrderDetailsUrl}"  id="details_iframe" scrolling="no"></iframe>
    </h-row>

    <floating-panel :anchors="anchors" :height="height" @heightChange="heightChange">
      <h-row>
        <h-col :span="24" style="background-color: #fff;margin-top: 1px;">
          <div style="margin-left: 10px;margin-top: 10px;padding-bottom: 10px; position: relative;min-height: 130px;display: flex;flex-direction: column;justify-content: space-between;"  :class="{'van-hairline--bottom' : !visible}">
            <h-row>
              <h-col :span="16">
                <h-row>
                  <tag v-if="detailsApiData?.processRecord?.state == ProcessStateConstant.CANCEL.type" type="primary">
                    {{ ProcessStateConstant.CANCEL.name }}
                  </tag>
                  <tag v-if="detailsApiData?.processRecord?.state == ProcessStateConstant.APPROVAL.type" type="primary">
                    {{ ProcessStateConstant.APPROVAL.name }}
                  </tag>
                  <tag v-if="detailsApiData?.processRecord?.state == ProcessStateConstant.REJECT.type" type="danger">
                    {{ ProcessStateConstant.REJECT.name }}
                  </tag>
                  <h-tooltip color="#ffffff" style="display: flex; width: 100%" trigger="click" placement="bottom">  
                    <template #title>
                      <div style="color: rgba(0, 0, 0, 0.85);">
                        {{ detailsApiData?.processRecord?.rejectedRemark }}
                      </div>
                    </template>
                    <tag v-if="detailsApiData?.processRecord?.state == ProcessStateConstant.REVOKE.type" type="danger" >
                      {{ ProcessStateConstant.REVOKE.name }}
                    </tag>
                  </h-tooltip>
                  <tag v-if="detailsApiData?.processRecord?.state == ProcessStateConstant.COMPLETE.type" type="success">
                    {{ ProcessStateConstant.COMPLETE.name }}
                  </tag>
                  <span style="font-weight: 600;font-size: 18px;vertical-align: super;margin-left: 5px;">{{ detailsApiData?.processRecord?.title }}</span>
                </h-row>
                <h-row>
                  <span style="font-weight: 600;font-size: 14px;vertical-align: super;color: color: rgba(0, 0, 0, 0.45);;">{{
                    detailsApiData?.processRecord?.code
                  }}</span>
                </h-row>
                <h-row>
                  <span style="font-weight: 600; font-size: 14px;vertical-align: super;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;" @click="copy(detailsApiData?.processRecord?.businessCode || '')">业务单号：{{
                    detailsApiData?.processRecord?.businessCode
                  }}</span>
                </h-row>
                
                <h-row v-if="detailsApiData?.processRecord?.description">
                  <span style="font-weight: 600;font-size: 14px;vertical-align: super;">申请原因：</span>
                  <span style="margin-left: 10px;margin-right: 40px; font-size: 14px;vertical-align: super;">{{
                    detailsApiData?.processRecord?.description
                  }}</span>
                </h-row>
              </h-col>
              <h-col :span="7" style="height: auto;text-align: center;">
                <template v-if="detailsApiData?.processRecord?.state == ProcessStateConstant.APPROVAL.type">
                  <template v-if="currentApprove">
                    <!-- && !code -->
                  <!-- <template v-if="true"> -->
                    <div class="btn">
                      <h-button type="primary" size="mini" class="sub-btn"
                        @click="result = 1; executeApiRun({ todoId: todoId, processCode:detailsApiData?.processRecord?.code,result: 1, remark: remark })"
                        :loading="executeApiLoading && result === 1">通过</h-button>
                      <h-button type="danger" size="mini" style="margin-left: 0px;" :class="{ opacity: rejectVisible }"
                        @click="showReject" class="sub-btn" :disabled="rejectVisible"
                        :loading="executeApiLoading && result === 2">驳回</h-button>
                    </div>
                  </template>
                  <template v-else>
                    <template v-if="isCurrentUser && code">
                      <h-button  v-if="todoMethod && todoMethod.indexOf(4) > -1" type="primary" size="mini" class="sub-btn"
                        @click="smsResend({processCode:code})"
                        :loading="smsResendLoading">短信重发</h-button>
                      <h-button type="danger" style="margin-left:0;" size="mini" class="sub-btn"
                        @click="revokeApiRun(code)"
                        :loading="revokeApiLoading">撤回</h-button>
                    </template>
                    <template v-else>
                      <img  :src="runningIcon" class="state-icon-img" style="margin-top: 20px;" />
                    </template>
                  </template>
                </template>
                <template v-else-if="detailsApiData?.processRecord?.state == ProcessStateConstant.REJECT.type">
                  <h-tooltip color="#ffffff" style="display: flex; width: 100%" trigger="click" placement="bottom">  
                    <template #title>
                      <div style="color: rgba(0, 0, 0, 0.85);">
                        {{ detailsApiData?.processRecord?.rejectedRemark }}
                      </div>
                    </template>
                    <img :src="rejectIcon" class="state-icon-img" />
                  </h-tooltip>
                </template>
                <template v-else-if="detailsApiData?.processRecord?.state == ProcessStateConstant.REVOKE.type">
                  <h-tooltip color="#ffffff" style="display: flex; width: 100%" trigger="click" placement="bottom">  
                    <template #title>
                      <div style="color: rgba(0, 0, 0, 0.85);">
                        {{ detailsApiData?.processRecord?.rejectedRemark }}
                      </div>
                    </template>
                    <img :src="revokeIcon" class="state-icon-img" />
                  </h-tooltip>
                </template>
                <template v-else>
                  <img :src="finishIcon" class="state-icon-img" style="margin-top: 20px;" />
                </template>
              </h-col>
              <h-col :span="23" v-if="rejectVisible" class="reject-text">
                <field
                  v-model="remark"
                  rows="2"
                  autosize
                  label="驳回意见"
                  type="textarea"
                  :autofocus="true"
                  maxlength="50"
                  placeholder="请输入驳回意见"
                  show-word-limit
                  border
                />
              </h-col>
              <h-col v-if="rejectVisible" :offset="9" :span="7" class="reject reject-c">
                <h-button type="default" size="mini" class="sub-btn"
                      @click="hideReject"
                      :loading="executeApiLoading && result === 2">取消</h-button>
              </h-col>
              <h-col v-if="rejectVisible" :span="7" class="reject">
                <h-button type="danger" size="mini" class="sub-btn"
                      @click="reject" 
                      :loading="executeApiLoading && result === 2">确认驳回</h-button>
              </h-col>
            </h-row>
            
            <div class="desc" v-if="visible">
              <span>上拉查看审批详情</span>
            </div>
            <!-- <template v-if="detailsApiData?.processRecord?.state == ProcessStateConstant.APPROVAL.type">
              <template v-if="!currentApprove">
                <img  :src="runningIcon" class="state-icon-img"  style="position: absolute; bottom: -40px; right: 16px"/>
              </template>
            </template>
            <template v-else>
              <img :src="finishIcon" class="state-icon-img" style="position: absolute; bottom: -40px; right: 16px" />
            </template> -->
          </div>
        </h-col>

        <h-col :span="24" style="background-color: #fff;">
          <div style="font-weight: 600;font-size: 18px;margin: 20px 0 0 10px;">流程（发起人: {{ detailsApiData?.processRecord?.applicantName }} ）</div>
          <h-steps :active="currentStep" direction="vertical">
            <h-step v-for="(i,index) in detailsApiData?.processRecord?.steps" :key="i.id" class="steps">
              <!-- <span slot="title">Finished</span> -->
              <template v-if="i?.operators?.length">
                <h-tooltip color="#ffffff" style="display: flex; width: 100%" placement="top" trigger="click">  
                  <template #title>
                    <div class="tooltip">
                      <template v-for="o in i.operators" :key="o.id">
                        <div :title="o.approverCode + '：' + o.approverRemark" style="color: rgba(0, 0, 0, 0.85);">{{o.approveUserType==1?'':'('+o.masterApproverName+'的代审)'}}{{ getResult(o.approverName || '', i.result, o.completeTime || '') }}
                          <CheckCircleOutlined v-if="o.state === ProcessStepOperatorStateConstant.PASS.type"
                            style="color: green;" />
                          <StopOutlined v-if="o.state === ProcessStepOperatorStateConstant.REJECT.type" style="color: red;" />
                          <MinusCircleOutlined v-if="o.state === ProcessStepOperatorStateConstant.ABSTENTION.type" />
                        </div>
                      </template>
                    </div>
                  </template>
                  <h-row >
                    <h-col :span="10">
                      <div style="font-weight: 600;">
                        {{ i.psName }}
                        <tag v-if="index == currentStep" type="primary">
                          当前节点
                        </tag>
                      </div>
                      <div style="color: rgba(0, 0, 0, 0.45); padding-top: 5px;">{{ i.operators[0].approverName + (i.operators.length > 1 ? ' ...' : '') }}<span v-if="false">  代审(郭振浩)</span></div>
                    </h-col>
                    <h-col :span="14">
                      <div style="width: 100%;height: 100%; display: flex;justify-content: right; align-items: center;color: #323233;">{{ i.startTime }}</div>
                    </h-col>
                  </h-row>
                </h-tooltip>
              </template>
            </h-step>
          </h-steps>
        </h-col>
      </h-row>
      
    </floating-panel>
  </div>
</template>

<style scoped lang="less">
.state-icon-img {
  width: 80px;
  height: 80px;
  transform: rotate(53deg);
  z-index: 10;
}

.header {
  height: 50px;
  background-color: #fff;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
  box-shadow: 0px 20px 80px 0px rgb(0 0 0 / 30%);

  .logo-img {
    margin-top: 12px;
    height: 20px;
  }
}

.details_iframe_body {
  height: calc(100% - 244px);
  background-color: #fff;
  overflow: auto;
  margin: 2px 0 2px 0;

  .details_iframe {
    min-width: 1000px;
  }

  .details_iframe_mobile {
    width: 100%;
  }
}

.footer {
  position: relative;
  height: 190px;
  border-top-right-radius: 12px;
  border-top-left-radius: 12px;
  box-shadow: 0px 20px 50px 0px rgb(0 0 0 / 30%);
}

.footer-fold {
  position: relative;
  height: 190px;
  border-top-right-radius: 12px;
  border-top-left-radius: 12px;
  box-shadow: 0px 20px 50px 0px rgb(0 0 0 / 10%);
}

.desc {
  display: flex;
  justify-content: center;
  width: 100%;
  

  span {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
  }
}

.opacity {
  opacity: 0.3;
}

.reject-text {
  border: 1px solid #ebedf0;
  margin-top: 10px;
}

.btn {
  display: flex;
  justify-content: space-around;
  flex-direction: column;
  align-items: flex-end;
  height: 100%;
}

.sub-btn {
  height: 30px;
  width: 70%;
}

.reject {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 10px;
  // justify-content: center;
}

.reject-c {
  justify-content: flex-start;
}

/* 修改滚动条样式 */
.details_iframe_body::-webkit-scrollbar {
  width: 5px;
  height: 7px;
  /*设置滚动条的宽度*/
}
/* 滚动区域的样式 */
.details_iframe_body::-webkit-scrollbar-thumb {
  border-radius: 10px;
  /*设置滚动条的圆角*/
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  /*设置内阴影*/
  background: hsla(210, 92%, 53%, 0.856);
  /*设置滚动条的颜色*/
}
/* 滚动条的背景样式 */
.details_iframe_body::-webkit-scrollbar-track {
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  border-radius: 0;
}



:root:root {
  --van-button-primary-background: #0073E5;
  --van-radio-checked-icon-color: #0073E5;
  --van-password-input-background: #F2F2F2;
}
</style>

<style>
  .steps .van-popover__wrapper {
    width: 100%;
  }

  .mobile .van-floating-panel__header {
    border-top: 1px #eff2f5 solid;
  }
</style>