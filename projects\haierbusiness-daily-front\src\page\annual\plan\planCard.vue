<script setup lang="ts">
import {
  Modal,
  Tag as hTag,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Table as hTable,
  RadioButton as hRadioButton,
  RadioGroup as hRadioGroup,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import {
  EyeOutlined,
  FormOutlined,
  SolutionOutlined,
  PlusOutlined,
  SyncOutlined,
  SearchOutlined,
  SaveOutlined,
  SubnodeOutlined,
  SafetyOutlined,
  RetweetOutlined,
  FundOutlined,
} from '@ant-design/icons-vue';
import {
  AnnualPlanStateConstant,
  AnnualPlanTypeStateConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
} from '@haierbusiness-front/common-libs';
import { getCurrentRouter, routerParam } from '@haierbusiness-front/utils';
import { ref, createVNode, PropType, watch, computed } from 'vue';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import { IAnnualPlanTypeListRequest } from '@haierbusiness-front/common-libs/src/daily';
import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import VChart from 'vue-echarts';

import { message } from 'ant-design-vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { BarChart, PieChart, LineChart } from 'echarts/charts';
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components';

const router = getCurrentRouter();
use([CanvasRenderer, PieChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent, BarChart, LineChart]);
const prop = defineProps({
  data: Object as PropType<IAnnualPlanListResponseDTO>,
  // 模式, 1: 小微模式 . 2: 平台主模式(管理员或平台主)
  type: Number,
});
// wait ：待生效 master ：主记录
const modelData = ref('master');
const data = computed(() => {
  if (modelData.value === 'master') {
    return prop?.data;
  } else {
    return prop?.data?.waitAnnualPlan;
  }
});
// 修改modelData
const changeModel = () => {
  if (modelData.value === 'master') {
    modelData.value = 'wait';
  } else {
    modelData.value = 'master';
  }
  updateChartsData();
};

const gotoProcess = () => {
  window.open(import.meta.env.VITE_PROCESS_URL.replace('_processCode_', data?.value?.annualProcessCode));
};
const gotoDetail = () => {
  router.push({ path: '/daily/annual-plan/detail', query: { id: routerParam(data?.value?.id) } });
};
const gotoResetting = () => {
  router.push({ path: '/daily/annual-plan/update', query: { id: routerParam(data?.value?.id), type: 'resetting' } });
};
const gotoUpdate = () => {
  router.push({ path: '/daily/annual-plan/update', query: { id: routerParam(data?.value?.id), type: 'edit' } });
};
const dataColor = {
  color: ['rgb(84,112,198)', 'rgb(250,200,88)', 'rgb(145,204,117)'],
};
// 基础数据
const itemCount = computed(() => data?.value?.itemCount || 0);
const stopItemCount = computed(() => data?.value?.stopItemCount || 0);
const completeItemCount = computed(() => data?.value?.completeItemCount || 0);
const inProgress = computed(() => itemCount.value - stopItemCount.value - completeItemCount.value);
const typeItemCountArr = computed(() => {
  if (data?.value?.typeItemCount) {
    const labelArr = [];
    // 已完成的
    const currentArr = [];
    // 所有的
    const valueArr = [];
    for (let typeItem of data?.value?.typeItemCount.split(',')) {
      labelArr.push(typeItem.split(':')?.[0]);
      currentArr.push(parseInt(typeItem.split(':')?.[1].split('/')?.[0]));
      valueArr.push(parseInt(typeItem.split(':')?.[1].split('/')?.[1]));
    }
    return [labelArr, currentArr, valueArr];
  } else {
    return undefined;
  }
});

const pieEchartsOption = ref({
  tooltip: {
    trigger: 'item',
  },
  ...dataColor,
  legend: {
    top: '25%',
    left: 'right',
    orient: 'verticalAlign',
  },
  series: [
    {
      left: 'left',
      center: ['25%', '50%'],
      name: 'Access From',
      type: 'pie',
      radius: ['50%', '80%'],
      label: {
        show: true,
        position: 'center',
        formatter: '0%',
        fontWeight: 'bold',
        fontSize: 16,
      },
      labelLine: {
        show: false,
      },
      data: [
        {
          value: 0,
          name: '进行中：0',
        },
        { value: 0, name: '已终止：0' },
        { value: 0, name: '已完成：0' },
      ],
    },
  ],
});
const barEchartsOption = ref({
  xAxis: {
    type: 'category',
    data: ['', '', '', ''],
    axisLabel: {
      interval: 0, //标识间隔多少个标题显示，这里标题过长会重叠遮挡，这里0设置强制显示
      rotate: 0, //旋转60度
    },
  },
  yAxis: {
    type: 'value',
  },
  minInterval: 1,
  series: [
    {
      type: 'bar',
      data: [0, 0, 0, 0],
      colorBy: 'data',
    },
  ],
});

const subscriptColorStyle = computed(() => {
  if (AnnualPlanStateConstant.WAIT.code === data?.value?.state) {
    return { 'background-color': 'rgb(0,115,229)' };
  }
  if (AnnualPlanStateConstant.REJECT.code === data?.value?.state) {
    return { 'background-color': 'rgb(255,77,79)' };
  }
  if (AnnualPlanStateConstant.APPROVAL.code === data?.value?.state) {
    return { 'background-color': 'rgb(250,173,20)' };
  }
  if (AnnualPlanStateConstant.WAIT_RUNNING.code === data?.value?.state) {
    return { 'background-color': 'rgb(0,115,229)' };
  }
  if (AnnualPlanStateConstant.RUNNING.code === data?.value?.state) {
    return { 'background-color': 'rgb(0,115,229)' };
  }
  if (AnnualPlanStateConstant.COMPLETED.code === data?.value?.state) {
    return { 'background-color': 'rgb(82,196,26)' };
  }
  if (AnnualPlanStateConstant.INCOMPLETE.code === data?.value?.state) {
    return { 'background-color': 'rgb(204,204,204)' };
  }
});
const updateChartsData = () => {
  // 设置pie图
  pieEchartsOption.value.series[0].data = [
    {
      value: inProgress.value,
      name: '进行中：' + inProgress.value,
    },
    { value: stopItemCount.value, name: '已终止：' + stopItemCount.value },
    { value: completeItemCount.value, name: '已完成：' + completeItemCount.value },
  ];
  pieEchartsOption.value.series[0].label.formatter =
    ((stopItemCount.value + completeItemCount.value) / itemCount.value / 0.01).toFixed(2) + '%';

  // 设置柱状图
  barEchartsOption.value.xAxis.data = (typeItemCountArr.value?.[0] || ['', '', '', '']) as string[];
  barEchartsOption.value.series[0].data = (typeItemCountArr.value?.[2] || [0, 0, 0, 0]) as number[];
};
{
  updateChartsData();
}

watch(() => prop.data, updateChartsData);

const showAction = ref(false);
const onMouseover = () => {
  showAction.value = true;
};
const onMouseout = () => {
  showAction.value = false;
};
</script>

<template>
  <div class="card">
    <div class="card-mask" @mouseenter="onMouseover" @mouseleave="onMouseout">
      <div v-if="showAction" style="height: 100%">
        <div style="display: flex; align-items: center; flex-wrap: wrap; justify-content: center; height: 100%">
          <div v-if="type === 1">
            <div style="position: absolute; top: 0; left: 0">
              <h-button
                v-if="
                  (data?.state === AnnualPlanStateConstant.WAIT_RUNNING.code ||
                    data?.state === AnnualPlanStateConstant.RUNNING.code) &&
                  data?.waitAnnualPlan
                "
                @click="changeModel()"
                style="
                  border-top-left-radius: 5px;
                  border-bottom-left-radius: 0;
                  border-top-right-radius: 0;
                  border-bottom-right-radius: 6px;
                "
              >
                <template #icon>
                  <RetweetOutlined />
                </template>
                切换重制版
              </h-button>
              <h-button
                v-if="modelData === 'wait'"
                @click="changeModel()"
                type="primary"
                style="
                  border-top-left-radius: 5px;
                  border-bottom-left-radius: 0;
                  border-top-right-radius: 0;
                  border-bottom-right-radius: 6px;
                "
              >
                <template #icon>
                  <RetweetOutlined />
                </template>
                切换生效版
              </h-button>
            </div>
            <div>
              <h-space
                style="display: flex; align-items: center; flex-wrap: wrap; justify-content: center; height: 100%"
              >
                <h-button
                  v-if="
                    data?.state === AnnualPlanStateConstant.WAIT.code ||
                    data?.state === AnnualPlanStateConstant.REJECT.code
                  "
                  @click="gotoUpdate"
                >
                  <template #icon>
                    <FormOutlined />
                  </template>
                  编辑
                </h-button>
                <h-button
                  v-if="
                    data?.state === AnnualPlanStateConstant.WAIT.code ||
                    data?.state === AnnualPlanStateConstant.REJECT.code
                  "
                  @click="gotoUpdate"
                >
                  <template #icon>
                    <SaveOutlined />
                  </template>
                  提交
                </h-button>
                <h-button
                  v-if="
                    (data?.state === AnnualPlanStateConstant.WAIT_RUNNING.code ||
                      data?.state === AnnualPlanStateConstant.RUNNING.code) &&
                    !data?.waitAnnualPlan
                  "
                  @click="gotoResetting()"
                  type="primary"
                  danger
                >
                  <template #icon>
                    <SyncOutlined />
                  </template>
                  重制
                </h-button>
                <h-button
                  v-if="
                    data?.state === AnnualPlanStateConstant.APPROVAL.code ||
                    data?.state === AnnualPlanStateConstant.REJECT.code
                  "
                  @click="gotoProcess"
                >
                  <template #icon>
                    <SolutionOutlined />
                  </template>
                  进度
                </h-button>
                <h-button @click="gotoDetail" type="primary">
                  <template #icon>
                    <EyeOutlined />
                  </template>
                  查看
                </h-button>
              </h-space>
            </div>
          </div>
          <div v-if="type === 2">
            <div style="margin-top: 10px">
              <h-space>
                <h-button @click="gotoDetail" type="primary">
                  <template #icon>
                    <EyeOutlined />
                  </template>
                  查看
                </h-button>
              </h-space>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div :style="subscriptColorStyle" class="subscript">
        <div style="margin-top: 8%">{{ data?.stateName }}</div>
      </div>
      <div style="height: 50%; width: 90%">
        <v-chart :option="pieEchartsOption" :autoresize="true" />
      </div>
      <div style="height: 75%">
        <v-chart :option="barEchartsOption" :autoresize="true" style="position: relative; top: -13%" />
      </div>
    </div>
    <div class="footer">
      <div v-if="type === 1">
        {{ data?.year }}
      </div>
      <div v-if="type === 2">
        {{ data?.deptName }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.subscript {
  position: absolute;
  width: 23%;
  height: 8%;
  right: 2.5%;
  color: white;
  font-size: 12px;
  text-align: center;
  vertical-align: middle;
  border-bottom-left-radius: 5px;
  border-top-right-radius: 5px;
}

.card {
  box-shadow: 0 0 2px rgba(189, 250, 255, 0.7);
  border: 0.5px solid #eaeaea;
  border-radius: 5px;
  text-align: center;
  height: calc(37vh);
  min-height: 290px;
  width: 95%;
  margin: 10px auto;

  &:hover {
    box-shadow: 0 0 10px rgba(189, 250, 255, 0.7);
    border: 0.5px solid rgba(189, 250, 255, 0.7);
  }

  .content {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    height: 90%;
  }

  .footer {
    padding-top: 2px;
    text-align: center;
    font-weight: 700;
    font-size: 16px;
    border-top: 0.5px solid #eaeaea;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    background-color: rgb(247, 249, 250);
    height: 10%;
  }
}

.card-mask {
  border-radius: 5px;
  cursor: pointer;
  position: absolute;
  height: calc(37vh);
  min-height: 290px;
  width: 95%;
  z-index: 10;
  &:hover {
    background-color: #424242c8;
    transition: all 0.3s;
    animation-name: gradually;
    animation-duration: 1s;
  }
}
</style>
