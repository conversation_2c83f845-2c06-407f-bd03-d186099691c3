type keys = "WAIT" | "HOLIDAY" | "LEAVE" | "OTHER" | "RUNNING" | "COMPLETED" | "NOT_ENTERED";

/**
 * 量化人状态
 * 10：等待录入
 * 21：休假（无需录入）
 * 22：离职（无需录入）
 * 23：其他（无需录入）
 * 30：录入中（不满足最小条数时）
 * 40：已录入完成（已达成最小条数的录入）
 *
 * <AUTHOR>
 * @since 2023/10/11 16:22
 */
export const QuantifierStateConstant = {

    /**
     * 等待录入
     */
    WAIT: {"code": 10, "desc": "等待录入"},

    /**
     * 休假（无需录入）
     */
    HOLIDAY: {"code": 21, "desc": "休假"},

    /**
     * 离职（无需录入）
     */
    LEAVE: {"code": 22, "desc": "离职"},

    /**
     * 其他（无需录入）
     */
    OTHER: {"code": 23, "desc": "其他"},

    /**
     * 录入中（不满足最小条数时）
     */
    RUNNING: {"code": 30, "desc": "录入中"},

    /**
     * 已录入完成（已达成最小条数的录入）
     */
    COMPLETED: {"code": 40, "desc": "已录入"},

    /**
     * 未录入
     */
    NOT_ENTERED: {"code": 99, "desc": "未录入"},

    ofCode: (code?: number): { "code": number, "desc": string } | null => {
        for (const key in QuantifierStateConstant) {
            const item = QuantifierStateConstant[key as keys];
            if (code === item.code) {
                return item;
            }
        }
        return null;
    }
}