import { loadEnv } from 'vite'

import vue from '@vitejs/plugin-vue'
import path from 'path';

const CWD = process.cwd();

export default ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);

  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
    },
    plugins: [vue()],
    build: {
      target: ['es2015']
    },
    server: {
      port: 5181,
      proxy: {
        "/hb/ai/api": {
          // target: "http://localhost:8080/hb",
          //  target: "https://businessmanagement-test.haier.net/hbweb/payman/hb",
          target: "http://localhost:9219",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb\/ai\/api/, ""),
        },
        // "/hb/data/api": {
        //   target: "http://************:9210/",
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(/^\/hb\/data\/api/, ""),
        // },
        "/hb": {
          target: "https://businessmanagement-test.haier.net/hbweb/data/hb",
          // target: "http://localhost:8080/hb",
          // target: "http://************:9210/hbweb/data/hb",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb/, ""),
        },
        "/upload": {
          target: "https://businessmanagement-test.haier.net/hbweb/upload",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/upload/, ""),
        },
      }, 
    }
  }
}
