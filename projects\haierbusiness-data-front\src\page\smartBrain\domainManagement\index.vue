<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
  Modal
} from "ant-design-vue";
import { ColumnType } from "ant-design-vue/lib/table/interface";
import { Key } from "ant-design-vue/lib/vc-table/interface";
import {
  DownOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined
} from "@ant-design/icons-vue";
import { domainApi } from "@haierbusiness-front/apis";
import {
  IEnterpriseListRequest,
  IEnterprise
} from "@haierbusiness-front/common-libs";
import { errorModal, routerParam } from "@haierbusiness-front/utils";
import dayjs, { Dayjs } from "dayjs";
import { computed, onMounted, ref, watch, h } from "vue";
import { DataType, usePagination, useRequest } from "vue-request";
import { useRouter } from "vue-router";
import { useEditDialog, useDelete } from "@haierbusiness-front/composables";
import EditDialog from './edit-dialog.vue'

const router = useRouter();
const columns: ColumnType[] = [
  {
    title: "领域名称",
    dataIndex: "areaName",
    width: "180px",
    align: "left"
  },
  {
    title: "法人公司名称",
    dataIndex: "accountCompanyName",
    width: "380px",
    align: "left"
  },
  {
    title: "法人公司编码",
    dataIndex: "accountCompanyCode",
    width: "200px",
    align: "left"
  },
  {
    title: "创建时间",
    dataIndex: "gmtCreate",
    width: "200px",
    align: "left"
  },
  {
    title: "创建人",
    dataIndex: "createName",
    width: "200px",
    align: "left"
  },

  {
    title: "修改时间",
    dataIndex: "gmtModified",
    width: "200px",
    align: "left"
  },

  {
    title: "修改人",
    dataIndex: "lastModifiedName",
    width: "200px",
    align: "left"
  },
 
  {
    title: "操作",
    dataIndex: "_operator",
    width: "250px",
    fixed: "right",
    align: "center"
  }
];
const searchParam = ref<any>({});
const typeList = ref<any>([]);
const manageParams = ref();
const editDetail = ref<any>({});
// 明细弹窗
const detailVisible = ref<boolean>(false);

const { data, run: listApiRun, loading, current, pageSize } = usePagination(
  domainApi.list,
  {
    defaultParams: [{}],
    manual: false
  }
);

const reset = () => {
  searchParam.value = {};
  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize
  });
};

const dataSource = computed(
  () =>
    data.value?.records?.filter(item => {
      if (item.iconUrl) {
        item.files = [
          {
            name: `${item.productName}`,
            thumbUrl: item.iconUrl
          }
        ];
      }
      return item;
    }) || []
);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: "center" }
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize
  });
};

// 新增表单相关
const {
  visible,
  editData,
  handleCreate,
  handleEdit,
  onDialogClose,
  handleOk
} = useEditDialog<IEnterprise, IEnterprise>(domainApi, "领域", () =>{

    listApiRun({
      ...searchParam.value,
      pageNum: data.value?.pageNum,
      pageSize: data.value?.pageSize
    })
    getTypeList()
  }

);

const { handleDelete } = useDelete(domainApi, () => {

  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize
  })
  getTypeList()
}

);

const EditDetailDialogCancel = () => {
  detailVisible.value = false;
  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize
  });
};

const saveWorkOrder = (res: any) => {
  visible.value = false;
  editDetail.value = res;
  detailVisible.value = true;
  listApiRun({
    pageNum: 1,
    pageSize: 10
  });
};

const rangePresets = ref([
  { label: "最近一年", value: [dayjs().add(-365, "d"), dayjs()] },
  { label: "最近半年", value: [dayjs().add(-180, "d"), dayjs()] }
]);

const {
  data: exportListData,
  run: exportListApiRun,
  loading: exportListLoading
} = useRequest(domainApi.exportList);

const showConfirm = (record: any) => {
  Modal.confirm({
    title: "确认要再次发送通知预警吗?",
    icon: h(ExclamationCircleOutlined),
    onOk() {
      domainApi.send({ id: record.id }).then(res => {
        listApiRun({
          pageNum: 1,
          pageSize: 10
        });
      });
    },
    onCancel() {
      console.log("Cancel");
    },
    class: "test"
  });
};

// 请求类型下拉
const getTypeList = () => {
  domainApi.getEarlyWarningTypeList().then(res => {
    typeList.value = res;
  });
};



// 编辑明细
const handleDateilEdit = (row: any) => {
  editDetail.value = row;
  detailVisible.value = true;
};
// 导出明细
onMounted(() => {
  getTypeList();
});
</script>

<template>
  <div
    style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto"
  >
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 30px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          
          <h-col style="text-align: right; padding-right: 10px;width:100px;">
            <label for="mobile">领域：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              show-search
              allowClear
              placeholder="请选择领域"
              v-model:value="searchParam.areaCode"
              optionFilterProp="name"
              :options="typeList"
              :fieldNames="{ label: 'name', value: 'code', options: 'options' }"
              style="width: 100%"
            >
              
            </h-select>
          </h-col>
          <h-col style="text-align: right; padding-right: 10px;width:100px;">
            <label for="mobile">法人公司名称:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="法人公司名称" allowClear v-model:value="searchParam.accountCompanyName" style="width: 100%" />
          </h-col>
          <h-col style="text-align: right; padding-right: 10px;width:100px;">
            <label for="mobile">法人公司编码:</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="法人公司编码" allowClear v-model:value="searchParam.accountCompanyCode" style="width: 100%" />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right">
           
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
            <h-button style="margin-left: 10px" @click="reset">重置</h-button>
            
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: left;">
            <h-button type="primary" @click="handleCreate">
              <PlusOutlined /> 新增
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :ellipsis="true"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="dataSource"
          :pagination="pagination"
          :scroll="{ y: 550 }"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'recipientName'">
              {{ record.recipientName }}({{ record.recipient }})
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="handleEdit(record)">编辑</h-button>
              <h-button type="link" @click="handleDelete(record.id)">删除</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>


  <div v-if="visible">
    <edit-dialog
        :show="visible"
        :data="editData"
        @cancel="onDialogClose"
        @ok="handleOk"
        :list="typeList"
    >
    </edit-dialog>
  </div>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
:deep(.ant-col) {
  color: #000;
}
</style>
