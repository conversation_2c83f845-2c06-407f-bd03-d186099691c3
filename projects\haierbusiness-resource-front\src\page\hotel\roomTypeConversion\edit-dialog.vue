<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  Switch as hSwitch,
  CheckboxGroup as hCheckboxGroup,
  Checkbox as hCheckbox,
  Image as hImage,
  message,
} from 'ant-design-vue';
import {
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { computed, onMounted, ref, watch } from 'vue';
import { hotelApi,addressApi } from '@haierbusiness-front/apis';
import type { Ref } from 'vue';
import { angtListRes, Datum } from '@haierbusiness-front/common-libs';
import {
  supplierType
} from '@haierbusiness-front/common-libs';
import { getEnumOptions } from '@haierbusiness-front/utils';
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil';
import { HeaderConstant } from '@haierbusiness-front/common-libs';
// import Echarts from "./echarts.vue"
import type { UploadChangeParam, UploadFile } from 'ant-design-vue';
// const extensions = [javascript(), oneDark];
const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false);

const region = ref<any>([])

// 获取品牌下拉列表
const BrandList = ref<any>([])
const getBrandList = ()=>{
  hotelApi.getHotelBrandProviderMapList({type:2}).then(res=>{
    BrandList.value = res.records
  })
}

// 供应商下拉值
const supplierTypeOptions = computed(()=>{
  return getEnumOptions(supplierType)
})



// 酒店下拉列表
const hotelList = ref<any[]>([]);

// 获取酒店列表
const getHotelList = (val: string) =>{
  setTimeout(()=>{
    hotelApi.getHotelList({name:val,pageNum:1,pageSize:10,cityId:indexData.value.regionarray&&indexData.value.regionarray.length>=2?indexData.value.regionarray[1]:null,regionId:indexData.value.regionarray&&indexData.value.regionarray.length>=3?indexData.value.regionarray[2]:null,}).then(res=>{
      hotelList.value = []
      if(res.records.length){
        res.records.forEach(item=>{
              hotelList.value.push({
                value:item.name,
                label:item.name
              })
            })
      }else{
        hotelList.value.push({
                value:val,
                label:val
              })
      }
    })
  },500)
}

const debounce = (fun,delay) =>{
	let timer;
	return function (){
		let context = this; // 注意 this 指向
		let args = arguments; // arguments中存着e
		// 清除定时器
		if (timer) clearTimeout(timer);
		
		timer = setTimeout(()=>{
			// 绑定this
			// fun.call(context,args);
			fun.apply(this, args);
		},delay);
	}
}

const handleSearch = debounce(async (val: string) => {
    await getHotelList(val)
}, 500)

const regionarrayChange =() =>{
  indexData.value.hotelName = null
}

const treeData = ref<any>([])

// 获取省市区 下拉
const getTrees = () =>{
  addressApi.getDistrictTrees({subdistrict:3,id:1}).then(res=>{
    console.log(res)
    treeData.value = res.children
  })
}

interface Props {
  show: boolean;
  data: angtListRes;
  labelList: Array<Datum>;
  knowCenterOptions: any;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: angtListRes = {
  personNum: '',
  personName: '',
  agentNum: '',
  agentId: '',
  productId: null,
};

const rules: Record<string, Rule[]> = {
  ruleName: [{ required: true, message: '请输入规则名称' }],
  providerCode: [{ required: true, message: '请选择供应商' }],
  hotelName: [{ required: false, message: '请输入酒店名称' }],
  regionarray: [{ required: false, message: '请选择酒店区域' }],
  // regularExpression: [{ required: false, message: '请输入酒店地址' }],
  brandId: [{ required: false, message: '请选择品牌' }],
  effectFlag: [{ required: true, message: '请选择是否生效' }],
  hotelRoomSourceName: [{ required: true, message: '请输入原房型名称' }],
  hotelRoomTargetName: [{ required: true, message: '请输入目标房型名称' }],
};

const indexData: Ref<angtListRes> = ref(props.data ? props.data : defaultData);

watch(props, (newValue) => {
  indexData.value = ({ ...newValue.data } as angtListRes) || defaultData;
});

const emit = defineEmits(['cancel', 'ok']);

const visible = computed(() => props.show);

const filterOption = (input: string, option: any) => {
  return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const handleOk = () => {
  from.value
    .validate()
    .then(() => {
      confirmLoading.value = true;
      if( indexData.value.regionarray&& indexData.value.regionarray.length>1){
        indexData.value.cityId = indexData.value.regionarray[1]
      }else{
        indexData.value.cityId = null
      }
      if( indexData.value.regionarray&& indexData.value.regionarray.length>2){
        indexData.value.regionId = indexData.value.regionarray[2]
      }else{
        indexData.value.regionId = null
      }
      if(indexData.value.regionarray){
        indexData.value.region = JSON.stringify(indexData.value.regionarray)
      }
      if(!indexData.value.brandId){
        indexData.value.brandName = null
      }
      if(!indexData.value.cityId){
        indexData.value.cityName = null
      }
      if(!indexData.value.providerCode){
        indexData.value.providerCodeName = null
      }
      if(!indexData.value.regionId){
        indexData.value.regionName = null
      }
      emit('ok', indexData.value, () => {
        confirmLoading.value = false;
      });
      confirmLoading.value = false;
      console.log(region,indexData.value)
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};
onMounted(()=>{
  getBrandList()
  getTrees()
})
</script>

<template>
  <h-modal
    v-model:visible="visible"
    :title="indexData.id ? '编辑房型转换规则' : '新增房型转换规则'"
    :width="600"
    @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading"
    forceRender
    @ok="handleOk"
  >
    <h-form
      v-if="visible"
      ref="from"
      :model="indexData"
      :label-col="{ span: 5 }"
      :wrapper-col="{ span: 17 }"
      :rules="rules"
    >
      <h-form-item label="规则名称" name="ruleName">
        <h-input v-model:value="indexData.ruleName" placeholder="请输入规则名称" style="width: 100%" />
      </h-form-item>
      <h-form-item label="供应商" name="providerCode">
        <h-select
          allow-clear
          placeholder="请选择供应商"
          v-model:value="indexData.providerCode"
          style="width: 100%"
        >
          <h-select-option v-for="item in supplierTypeOptions" :value="item.value">{{ item.label }}</h-select-option>
        </h-select>
      </h-form-item>
      <!-- 远程搜索酒店 -->
      <h-form-item label="区域" name="regionarray">
        <a-cascader
          change-on-select
          placeholder="请选择区域"
          @change="regionarrayChange()"
          :field-names="{ label: 'name', value: 'id', children: 'children' }"
          v-model:value="indexData.regionarray"
          :options="treeData"
        />
      </h-form-item>
      <h-form-item label="酒店" name="hotelName">
        <h-select
          v-model:value="indexData.hotelName"
          show-search
          placeholder="请输入酒店名称"
          :default-active-first-option="false"
          :show-arrow="false"
          :filter-option="false"
          :not-found-content="null"
          :options="hotelList"
          allow-clear
          @search="handleSearch"
        ></h-select>
      </h-form-item>
      <h-form-item label="正则表达式" name="regularExpression">
        <h-input
          placeholder="请输入正则表达式"
          v-model:value="indexData.regularExpression"
          style="width: 100%"
        />
      </h-form-item>
      <h-form-item label="品牌" name="brandId">
        <h-select
          placeholder="请选择品牌"
          ref="city"
          show-search
          :fieldNames="{label:'brandName',value:'id'}"
          :options="BrandList"
          :filter-option="filterOption"
          v-model:value="indexData.brandId"
          style="width: 100%"
          allow-clear
        />
      </h-form-item>
      <h-form-item label="原房型名称" name="hotelRoomSourceName">
        <h-input
          placeholder="请输入原房型名称"
          v-model:value="indexData.hotelRoomSourceName"
          style="width: 100%"
        />
      </h-form-item>
      <h-form-item label="目标房型名称" name="hotelRoomTargetName">
        <h-input
          placeholder="请输入目标房型名称"
          v-model:value="indexData.hotelRoomTargetName"
          style="width: 100%"
        />
      </h-form-item>
      <h-form-item label="是否生效" name="effectFlag">
        <h-select
          ref="select"
          v-model:value="indexData.effectFlag"
          style="width:100%;"
          placeholder="请选择是否生效"
          allow-clear
        >
          <h-select-option value="1">是</h-select-option>
          <h-select-option value="0">否</h-select-option>
        </h-select>
      </h-form-item>
      <h-form-item style="margin-left:40px;">注：正则表达式优先级最高，其次是名称，品牌，供应商，区域</h-form-item>
    </h-form>
  </h-modal>
</template>

<style lang="less" scoped>
.important {
  color: red;
}
.imgItem {
  position: relative;
  display: inline-block;
  margin-right: 4px;
  .deleteIcon {
    position: absolute;
    right: 0px;
    z-index: 9999;
  }
}
</style>
