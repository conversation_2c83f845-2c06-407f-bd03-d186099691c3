// 申请单保存接口
export interface BSaveParams {
  name?: string;
  code?: string;
  /*联系方式 */
  phone?: string;

  /*场景类型(可传入值：1 商务宴请) */
  sceneType: number;

  /*预计就餐时间 estimatedMealTime[0] 起始时间 estimatedMealTime[1]截止时间 */
  estimatedMealTime: string[];

  /*用餐地点省份编码 */
  mealLocationProvinceCode?: string;

  /*用餐地点省份 */
  mealLocationProvince?: string;

  /*用餐地点城市编码 */
  mealLocationCityCode?: string;

  /*用餐地点城市 */
  mealLocationCity?: string;

  /*美团商家信息 */
  restaurantInfo: {
    /*ID */
    restaurantId: string;

    /*餐厅名称 */
    restaurantName: string;
  };

  /*签字人工号 */
  signerCode?: string;

  /*签字人名称 */
  signerName?: string;

  /*申请事由 */
  banquetReason?: string;

  /*订单状态10保存草稿 20提交申请 */
  orderStatus: number;

  /*申请金额0.00 */
  budgetAmount: number;
  innerPerson: {
    /* */
    userCode?: string;

    /*用户名字 */
    userName: string;

    /* */
    phone?: string;

    /*是否海尔员工 */
    haierUser?: boolean;
  }[];
  outerPerson: {
    /* */
    userCode?: string;

    /*用户名字 */
    userName: string;

    /* */
    phone?: string;

    /*是否海尔员工 */
    haierUser?: boolean;
  }[];

  /*宴请人员信息 */
  banquetPersonInfos: {
    /* */
    userCode?: string;

    /*用户名字 */
    userName: string;

    /* */
    phone?: string;

    /*是否海尔员工 */
    haierUser?: boolean;
  }[];

  /*附件 */
  fileUrl?: string;
  fileName?: string;

  /*备注 */
  remark?: string;
}

// 申请单列表请求接口
export interface BApplyListReq {
  /* */
  pageNum?: number;

  /* */
  pageSize?: number;

  /*订单code */
  orderCode?: string;

  /*餐厅名字 */
  restaurantName?: string;


  /*订单状态 10待提交 20已提交 30支付中 40支付完成待消费 50消费中 60完成 70已关闭 */
  orderStatus: number | string;


  /*审批状态 0创建1通过2撤销3驳回/拒绝 */
  approveStatus: number | string;
  /*订单生效状态 0未生效1已生效2已失效 */
  orderEffective: number | string;

  /*申请时间0开始1结束 */
  applicationTimes: string[];

  /*场景 */
  sceneType?: number | string;

  timeType?: number | string;

  yxTimeType?: number | string;
  orderStatusNew?: number | string;

  /*有效时间 */
  estimatedMealTimeEnd: string[];

  /* */
  needPage?: boolean;
}
// 申请单列表响应
export interface BApplyListRes {
  /* */
  pageNum: number;

  /* */
  pageSize: number;

  /* */
  total: number;

  /* */
  totalPage: number;

  /* */
  records: Array<BApplyListRecord>;
}
export interface BApplyListRecord {

  processCode: string;
  /* */
  id: number;

  /*申请单号 */
  orderCode: string;

  /*场景类型 */
  sceneType: number;

  /*申请时间 */
  applicationTime: string;

  /*预计就餐时间 */
  estimatedMealTimeStart: string;

  /*推送美团时间 */
  mtStartTime: string;

  /*预计就餐截止时间 */
  estimatedMealTimeEnd: string;

  /*推送美团时间 */
  mtEndTime: string;

  /*用餐地点省份code */
  mealLocationProvinceCode: string;

  /*用餐地点省份 */
  mealLocationProvince: string;

  /*用餐地点城市code */
  mealLocationCityCode: string;

  /*用餐地点城市 */
  mealLocationCity: string;

  /*签字人工号 */
  signerCode: string;

  /*签字人名称 */
  signerName: string;

  /*订单状态 10待提交 20已提交 30支付中 40支付完成待消费 50消费中 60完成 70已关闭 */
  orderStatus: number | string;

  /*关闭原因（订单状态已关闭会有关闭原因）1.已到截止时间2.手动关闭3.取消支付4.支付失败5.审批驳回 */
  closeReason: number;

  /*审批状态 0创建1通过2撤销3驳回/拒绝 */
  approveStatus: number | string;
  /*订单生效状态 0未生效1已生效2已失效 */
  orderEffective: number | string;

  /*餐厅名字 */
  restaurantName: string;

  /*预算金额 */
  budgetAmount: number;

  /*实际支付金额 */
  actualPaymentAmount: number;

  /*申请事由 */
  banquetReason: string;

  /*文件url */
  fileUrl: string;

  /*文件名称 */
  fileName: string;

  /*备注 */
  remark: string;

  /*宴请人员信息 */
  persons: Array<BApplyPerson>;
  bookings: {
    /*主键ID */
    id: number;

    /*预订单号 */
    orderBookingCode: string;

    /*美团预订单号(订单ID) */
    mtBookingCode: string;

    /*关联申请单号 */
    orderCode: string;

    /*对账单号 */
    settleCode: string;

    /*签字人工号 */
    signerCode: string;

    /*签字人名称 */
    signerName: string;

    /*就餐时间 */
    mealTime: Record<string, unknown>;

    /*场景类型1宴请2外卖 */
    sceneType: number;

    /*场景类型1宴请2外卖 */
    sceneTypeStr: string;

    /*餐厅名字 */
    restaurantName: string;

    /*餐厅地址 */
    restaurantLocation: string;

    /*餐厅电话 */
    restaurantPhone: string;

    /*实际支付金额 */
    actualPaymentAmount: number;

    /*核对状态 0未核对1核对正常2核对异常 */
    checkStatusStr: string;

    /*核对状态 0未核对1核对正常2核对异常 */
    checkStatus: number;

    /*核对时间 */
    checkTime: Record<string, unknown>;

    /*核对人工号 */
    checkerCode: string;

    /*核对人名称 */
    checkerName: string;

    /*核对人信息 */
    checkerCodeInfo: string;

    /*核对备注 */
    checkRemark: string;

    /*关闭时间 */
    closeTime: Record<string, unknown>;

    /*关闭人工号 */
    closerCode: string;

    /*关闭人名称 */
    closerName: string;

    /*关闭人信息 */
    closerNameInfo: string;

    /*用餐地点省份编码 */
    mealLocationProvinceCode: string;

    /*用餐地点省份 */
    mealLocationProvince: string;

    /*用餐地点城市编码 */
    mealLocationCityCode: string;

    /*用餐地点城市 */
    mealLocationCity: string;

    /*签到人工号 */
    checkinPersonCode: string;

    /*签到人名称 */
    checkinPersonName: string;

    /*签到时间 */
    checkinTime: Record<string, unknown>;

    /*签到地址 */
    checkinLocation: string;

    /*支付人工号 */
    payerCode: string;

    /*支付人名称 */
    payerPersonName: string;

    /*支付时间 */
    payTime: Record<string, unknown>;

    /*水票信息 */
    waterTicketInformation: string;

    /*异常处理附件证明 */
    exceptionAttachment: string;

    /*异常处理附件证明文件名 */
    exceptionAttachmentName: string;

    /*异常处理结果 */
    exceptionResult: string;

    /*是否发生退款1是2否 */
    refundFlag: number;

    /*企业支付金额，单位元，精确到小数点后两位（不包含服务费） */
    entPayAmount: number;

    /*个人支付金额，单位元，精确到小数点后两位（不包含服务费） */
    staffPayAmount: number;
  }[];
  refunds: {
    /* */
    id: number;

    /* */
    refundOrderCode: string;

    /* */
    applicationOrderCode: string;

    /* */
    orderBookingCode: string;

    /* */
    refundAmount: number;

    /* */
    restaurantName: string;

    /* */
    signerCode: string;

    /* */
    signerName: string;

    /* */
    dealTime: Record<string, unknown>;

    /* */
    creator: string;

    /* */
    createTime: Record<string, unknown>;
  }[];
}

export interface BApplyPerson {
  /* */
  id: number;

  /* */
  applicationId: number;

  /* */
  userCode: string;

  /* */
  userName: string;

  /* */
  phone: string;

  /* */
  haierUser: boolean;
}

// 申请单详情
export interface BApplyDetailReq {
  id: number | string;
}

// 首页获取数量详情
export interface BHomeGetCountRes {
    /*待提交 */
    notSubmittedCount: number;

    /*已提交 */
    submittedCount: number;

    /*审批中 */
    processCount: number;

    /*已生效 */
    effectiveCount: number;

    /*已关闭 */
    closeCount: number;
}

export interface BRestaurantReq {
  restaurantId?:string;
}
//餐厅详情响应接口
export interface BRestaurantRes {
  /* */
  id: number;

  /* */
  budgetKey: string;

  /* */
  restaurantId: number;

  /* */
  restaurantName: string;

  /* */
  productType: string;

  /* */
  restaurantAddress: string;

  /* */
  restaurantProvinceId: string;

  /* */
  restaurantProvinceName: string;

  /* */
  restaurantCityId: string;

  /* */
  restaurantCityName: string;

  /* */
  restaurantDistrictId: string;

  /* */
  restaurantDistrictName: string;

  /* */
  restaurantServicePhone: string;

  /* */
  poiId: string;

  /* */
  longitude: string;

  /* */
  latitude: string;

  /* */
  createTime: string;

  /* */
  source: string;
};

