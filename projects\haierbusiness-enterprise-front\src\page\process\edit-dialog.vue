<script lang="ts" setup>
import { Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
     Input as hInput, Card as hCard } from 'ant-design-vue';
import { computed, ref, watch } from "vue";
import type { Ref } from "vue";
import {
  IProcessIno
} from '@haierbusiness-front/common-libs';
import { useRequest } from 'vue-request';
import { userApi, enterpriseApi } from '@haierbusiness-front/apis';
import Steps from './step.vue'

interface Props {
    show: boolean;
    data: IProcessIno | null;
}

// 查询企业
const {
  data: enterprises,
  run: userListApiRun
} = useRequest(enterpriseApi.list, {
  manual: false
})

const enterpriseSelect = computed(() => {
  return enterprises.value || []
})

const enterpriseCodeChange = (value: number) => {
  const enterprise = enterpriseSelect.value.find(o => o.id === value)
  if(enterprise) {
    process.value.enterpriseName = enterprise.name
  }
}

const props = withDefaults(defineProps<Props>(), {
    show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: IProcessIno = {
    name: '',
    state: 1,
    id: null,
    enterpriseCode: '',
    enterpriseName: '',
    additionable: 1,
    revocable: 1
};

const rules = {
    enterpriseCode: [{ required: true, message: "请选择企业！" }],
    name: [{ required: true, message: "请输入流程名称！" }],
    state: [{ required: true, message: "请选择状态！" }],
};

const process: Ref<IProcessIno> = ref(
  ({ ...props.data } as IProcessIno) || defaultData
);

watch(props, (newValue) => {
  process.value = ({ ...newValue.data } as IProcessIno) || defaultData;
});

const emit = defineEmits(["cancel", "ok"]);

const visible = computed(() => props.show);

const handleOk = () => {
confirmLoading.value = true;
from.value
    .validate()
    .then(() => {
    emit("ok", process.value, () => {
        confirmLoading.value = false;
    });
    })
    .catch(() => {
        confirmLoading.value = false;
    });
};

// 步骤


</script>

<template>
    <h-modal
      v-model:visible="visible"
      :title="process.id ? '编辑流程' : '新增流程'"
      :width="800"
      class="modal"
      @cancel="$emit('cancel')"
      :confirmLoading="confirmLoading"
      @ok="handleOk"
    >
        <h-card size="large" style="width: 100%" hoverable>
          <h-form
            ref="from"
            :model="process"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 18 }"
            :rules="rules"
          >
            <h-form-item label="企业编码" name="enterpriseCode">
              <h-select ref="select" v-model:value="process.enterpriseCode" @change="(value) =>  enterpriseCodeChange(value as number)">
                <h-select-option v-for="(item, index) in enterpriseSelect" :value="item.id" :key="index">{{ item.name }}</h-select-option>
              </h-select>
            </h-form-item>
            <h-form-item label="步骤名称" name="name">
              <h-input v-model:value="process.name" />
            </h-form-item>
            <h-form-item label="状态" name="state">
              <h-select v-model:value="process.state" allow-clear>
                <h-select-option :value="1">有效</h-select-option>
                <h-select-option :value="0">无效</h-select-option>
              </h-select>
            </h-form-item>
          </h-form>
        </h-card>
        
        <steps/>
    </h-modal>
</template>


<style lang="less" scoped>
.important {
color: red;
}

.formContent {
  background: rgb(236, 236, 236);
}
</style>

<style>
.modal .bwdv-modal-body {
  background-color: #f5f5f5;
}
</style>
  