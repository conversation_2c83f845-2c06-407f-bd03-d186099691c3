<!-- 顾问管理 -->

<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Input as hInput,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Image as hImage,
  Popconfirm as hPopconfirm,
  message
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { consultantApi } from '@haierbusiness-front/apis';
import {
  IConsultantFilter,
  IConsultant,
  ConsultantType
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted, h, reactive } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import EditDialog from './edit-dialog.vue'
import router from '../../router'
import ColumnFilter from '@haierbusiness-front/components/mice/search/ColumnFilter.vue';

const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router
  // 加载页面时获取数据
  listApiRun({
    pageNum: 1,
    pageSize: 10,
    ...searchParam.value
  })
})

const columns: ColumnType[] = [
  {
    title: '顾问姓名',
    dataIndex: 'nickName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '工号',
    dataIndex: 'username',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '照片',
    dataIndex: 'path',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => {
      return text ? h(hImage, {
        width: 80,
        height: 60,
        src: text,
        style: {
          objectFit: 'cover',
          borderRadius: '4px'
        },
        preview: false
      }) : '-';
    },
  },
  {
    title: '顾问介绍',
    dataIndex: 'description',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '工作年限',
    dataIndex: 'seniority',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      const stateObj = ConsultantType.ofType(text);
      return stateObj ? stateObj.desc : '-';
    },
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '150px',
    fixed: 'right',
    align: 'center'
  },
];

// 创建时间范围
const createTimeRange = ref<[Dayjs, Dayjs]>();
watch(() => createTimeRange.value, (n: any) => {
  if (n) {
    searchParam.value.gmtCreate = dayjs(n[0]).format('YYYY-MM-DD 00:00:00');
    searchParam.value.gmtCreateEnd = dayjs(n[1]).format('YYYY-MM-DD 23:59:59');
  } else {
    searchParam.value.gmtCreate = undefined;
    searchParam.value.gmtCreateEnd = undefined;
  }
});

const searchParam = ref<IConsultantFilter>({
  begin: undefined,
  end: undefined,
  nickName: undefined,
  createName: undefined
})
const {
  data,
  run: listApiRun,
  loading,
} = usePagination(consultantApi.list);

const reset = () => {
  searchParam.value = {
    begin: undefined,
    end: undefined,
    nickName: undefined,
    createName: undefined
  }
  createTimeRange.value = undefined
  listApiRun({
    pageNum: 1,
    pageSize: 10,
    ...searchParam.value
  })
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
) => {
  // 确保使用最新的搜索参数和过滤参数，优先使用filterInputs中的非空值
  const params = {
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  };

  console.log('最终查询参数:', params);
  listApiRun(params);
};
const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } =
  useEditDialog<IConsultant, IConsultant>(consultantApi, "顾问管理", () => listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum || 1,
    pageSize: data.value?.pageSize || 10,
  }))

const thisHandleEdit = (item: IConsultant) => {
  const currentData = {
    id: item.id,
    nickName: item.nickName,
    username: item.username,
    description: item.description,
    path: item.path
  };

  if ('type' in item) currentData['type'] = item['type'];
  if ('gender' in item) currentData['gender'] = item['gender'];
  if ('phone' in item) currentData['phone'] = item['phone'];
  if ('seniority' in item) currentData['seniority'] = item['seniority'];
  if ('picId' in item) currentData['picId'] = item['picId'];

  handleEdit(currentData);
}


const handleDisable = async (id: number) => {

  try {
    await consultantApi.userDisable(id);
    message.success('停用成功');
    listApiRun({
      ...searchParam.value,
      pageNum: data.value?.pageNum,
      pageSize: data.value?.pageSize,
    });
  } catch (error) {
    console.error('停用失败', error);
    message.error('停用失败');
  }
};

// 处理顾问状态变更
const handleStateChange = async (id: number, state: number) => {
  try {
    await consultantApi.userDisable({ id, state });
    message.success(state === 0 ? '启用成功' : '停用成功');
    listApiRun({
      ...searchParam.value,
      pageNum: data.value?.pageNum,
      pageSize: data.value?.pageSize,
    });
  } catch (error) {
    console.error(state === 0 ? '启用失败' : '停用失败', error);
    message.error(state === 0 ? '启用失败' : '停用失败');
  }
};

// 查询表格
const handleSearch = () => {
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize: 10,
  });
};

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="consultantName">顾问：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.nickName" placeholder="请输入顾问" allow-clear style="width: 100%" />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="consultantName">工号：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.username" placeholder="请输入工号" allow-clear style="width: 100%" />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="creator">创建人：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.createName" placeholder="请输入创建人" allow-clear style="width: 100%" />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="creator">顾问介绍：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.description" placeholder="请输入顾问介绍" allow-clear style="width: 100%" />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="consultantName">工作年限：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.seniority" placeholder="请输入工作年限" allow-clear style="width: 100%" />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="consultantName">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select style="width: 100%" v-model:value="searchParam.state" placeholder="请选择状态" allow-clear>
              <h-select-option :value="0">启用</h-select-option>
              <h-select-option :value="1">停用</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="creator">创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="createTimeRange" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleSearch">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
            <h-button type="primary" @click="handleCreate()">
              <PlusOutlined /> 新增
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ x: 1050, }" :loading="loading" @change="handleTableChange"
          :customRow="() => ({ style: { height: '80px' } })"
          :rowClassName="() => 'fixed-height-row'">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="thisHandleEdit(record)">编辑</h-button>
              <template v-if="record.state === 0">
                <h-popconfirm title="确认停用该顾问吗？" ok-text="确认" cancel-text="取消"
                  @confirm="handleStateChange(record.id, 1)">
                  <h-button type="link">停用</h-button>
                </h-popconfirm>
              </template>
              <template v-else>
                <h-popconfirm title="确认启用该顾问吗？" ok-text="确认" cancel-text="取消"
                  @confirm="handleStateChange(record.id, 0)">
                  <h-button type="link">启用</h-button>
                </h-popconfirm>
              </template>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

    <div v-if="visible">
      <edit-dialog :show="visible" :data="editData" @cancel="onDialogClose" @ok="handleOk">
      </edit-dialog>
    </div>

  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

// 确保表格行高一致
:deep(.ant-table-tbody > tr) {
  height: 80px !important;

  > td {
    height: 80px !important;
    vertical-align: middle !important;
    padding: 8px 16px !important;
  }
}

// 固定行高样式类
:deep(.fixed-height-row) {
  height: 80px !important;

  td {
    height: 80px !important;
    vertical-align: middle !important;
  }
}

// 确保图片容器不影响行高
:deep(.ant-image) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
</style>
