<template>
  <div style="
      background-color: #ffff;
      height: 100%;
      width: 100%;
      padding: 10px 10px 0px 10px;
      overflow: auto;
    ">

    <h-row :align="'middle'">

      <h-col :span="24" style="margin-bottom: 10px">

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_order_booking_code">预订单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="search_order_booking_code" v-model:value="searchParams.order_booking_code.value" placeholder=""
                     autocomplete="off"
                     allow-clear/>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_mt_booking_code">美团预订单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="search_mt_booking_code" v-model:value="searchParams.mt_booking_code.value" placeholder=""
                     autocomplete="off"
                     allow-clear/>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_order_code">申请单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="search_order_code" v-model:value="searchParams.order_code.value" placeholder=""
                     autocomplete="off"
                     allow-clear/>
          </h-col>


          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_create_time_booking">预订单同步时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="searchParams.create_time_booking.value" value-format="YYYY-MM-DD"
                            style="width: 100%"/>
          </h-col>

        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_scene_type">申请类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select
                placeholder=""
                v-model:value="searchParams.scene_type.value"
                :options="preData.sceneTypes"
                allow-clear
                style="width: 100%"
                :field-names="{ label: 'name', value: 'code' }"
            >
            </h-select>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_meal_location_city">用餐城市：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="search_meal_location_city" v-model:value="searchParams.meal_location_city_booking.value"
                     placeholder=""
                     autocomplete="off"
                     allow-clear/>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_restaurant_name">餐厅名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="search_restaurant_name" v-model:value="searchParams.restaurant_name.value" placeholder=""
                     autocomplete="off"
                     allow-clear/>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_order_status_booking_search">订单状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
                placeholder=""
                v-model:value="searchParams.order_status_booking_search.value"
                :options="preData.orderStatuses"
                allow-clear
                style="width: 100%"
                :field-names="{ label: 'name', value: 'code' }"
            >
            </h-select>
          </h-col>

        </h-row>


        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_budget_source_code">预算来源：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="search_budget_source_code" v-model:value="searchParams.budget_source_code.value" placeholder=""
                     autocomplete="off" allow-clear/>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_fee_item_name">费用科目：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="search_fee_item_name" v-model:value="searchParams.fee_item_name.value" placeholder=""
                     autocomplete="off"
                     allow-clear/>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_account_company_code">结算单位：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择结算单位" v-model:value="searchParams.account_company_code.value" show-search
                      :filter-option="filterOption" allow-clear @search="handleSearch" :options="settleCompany"
                      style="width: 100%" :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_pay_code">支付方式：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择支付方式" v-model:value="searchParams.second_business_type.value" show-search
                      :options="preData.secondBusinessTypes" style="width: 100%"
                      :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>

        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_settle_code">对帐单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="search_settle_code" v-model:value="searchParams.settle_code.value" placeholder=""
                     autocomplete="off"
                     allow-clear/>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_account_code">中台汇总单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="search_account_code" v-model:value="searchParams.account_code.value" placeholder=""
                     autocomplete="off"
                     allow-clear/>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_cvp_code">商互通账单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="search_cvp_code" v-model:value="searchParams.cvp_code.value" placeholder=""
                     autocomplete="off"
                     allow-clear/>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_signer_code">签单人工号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="search_signer_code" v-model:value="searchParams.signer_code.value" placeholder=""
                     autocomplete="off"
                     allow-clear/>
          </h-col>

        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="search_signer_name">签单人姓名：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="search_signer_name" v-model:value="searchParams.signer_name.value" placeholder=""
                     autocomplete="off"
                     allow-clear/>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="budget_department_code ">预算部门：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择预算部门" v-model:value="searchParams.budget_department_code.value" show-search
                      allow-clear
                      :filter-option="filterOption" @search="handleBudgetSearch" :options="settleDepartment"
                      style="width: 100%"
                      :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>
        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="field_code ">领域：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择领域" v-model:value="searchParams.field_code.value" show-search allow-clear
                      :filter-option="filterOption" @search="handleAreaSearch" :options="areaList" style="width: 100%"
                      :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="pt_code">平台：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择平台" v-model:value="searchParams.pt_code.value" show-search allow-clear
                      :filter-option="filterOption" @search="handlePtSearch" :options="platformList" style="width: 100%"
                      :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="pl_code">产业线：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择产业线" v-model:value="searchParams.pl_code.value" show-search allow-clear
                      :filter-option="filterOption" @search="handlePlSearch" :options="industryList" style="width: 100%"
                      :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>

          <h-col v-if="sjlyInnersShow" :span="2" style="text-align: right; padding-right: 10px">
            <label for="pl_code">内外部数据：</label>
          </h-col>
          <h-col v-if="sjlyInnersShow" :span="4">
            <h-select v-model:value="searchParams.sjly_inner.value" show-search allow-clear
                      :options="preData.sjlyInners" style="width: 100%"
                      :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>

        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button style="margin-right: 10px" @click="handleReset">重置</h-button>
            <h-button style="margin-right: 10px" type="primary" @click="doFilter">
              <SearchOutlined/>
              查询
            </h-button>
            <h-button
                type="primary"
                style="margin-right: 10px"
                v-if="!pagination.disabled"
                :loading="downloading"
                @click="download"
            >
              <UploadOutlined/>
              导出
            </h-button>
          </h-col>
        </h-row>

      </h-col>


      <h-col :span="24">
        <h-table
            :columns="columns"
            :row-key="(record) => record.order_code"
            :size="'small'"
            :data-source="data"
            :pagination="pagination"
            :scroll="{ y: 550, x: 6000 }"
            :loading="loading"
            @change="onPageChange"
        >
          <template #emptyText v-if="pagination.disabled">
            <div>暂无权限，<a @click="goApplyDetail">去申请</a></div>
          </template>

          <template #customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">

            <!-- 日期选择器 -->
            <div v-if="column.title.indexOf('时间')!=-1||column.title.indexOf('日期')!=-1" style="padding: 8px">

              <h-range-picker
                  v-model:value="searchParams[column.key].value"
                  value-format="YYYY-MM-DD"
                  style="width: 218px; margin-bottom: 8px;"
              />

              <div style="display: block">
                <a-button
                    type="primary"
                    size="small"
                    style="width: 90px; margin-right: 8px"
                    @click="doFilter"
                >
                  <template #icon>
                    <SearchOutlined/>
                  </template>
                  搜索
                </a-button>
                <a-button size="small" style="width: 90px" @click="handleReset">
                  重置
                </a-button>
              </div>
            </div>

            <!-- 文本搜索框 -->
            <div v-else style="padding: 8px">
              <h-input
                  ref="searchInput"
                  :placeholder="`搜索${column.title}`"
                  v-model:value="searchParams[column.key].value"
                  style="width: 188px; margin-bottom: 8px;"
                  allow-clear
                  @pressEnter="doFilter"
              />
              <div style="display: block">
                <a-button
                    type="primary"
                    size="small"
                    style="width: 90px; margin-right: 8px"
                    @click="doFilter"
                >
                  <template #icon>
                    <SearchOutlined/>
                  </template>
                  搜索
                </a-button>
                <a-button size="small" style="width: 90px" @click="handleReset">
                  重置
                </a-button>
              </div>
            </div>
          </template>

          <template #customFilterIcon="{ filtered }">
            <SearchOutlined :style="{ color: filtered ? '#108ee9' : undefined }"/>
          </template>

        </h-table>

      </h-col>
    </h-row>
  </div>
</template>

<script setup lang="ts">

import {
  Button as hButton,
  Col as hCol,
  Input as hInput,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  Table as hTable,
} from 'ant-design-vue';
import {onMounted, reactive, ref} from "vue";
import {ApplyCompanyType, ReportFilter, ReportType,} from "@haierbusiness-front/common-libs";

import {aggregatorsToColumn, banquetBookingColumns} from "../columns";
import {SearchOutlined, UploadOutlined} from "@ant-design/icons-vue";
import {reset} from "@haierbusiness-front/utils";
import {useSearch} from "@/composables/useSearch";
import {reportApi} from "@haierbusiness-front/apis";

const preData = {
  sceneTypes: [{
    code: '1',
    name: '宴请'
  }, {
    code: '2',
    name: '外卖'
  }],
  orderStatuses: [{
    code: '1',
    name: '待核销'
  }, {
    code: '2',
    name: '已完成'
  }, {
    code: '3',
    name: '部分退款'
  }, {
    code: '4',
    name: '全部退款'
  }],
  secondBusinessTypes: [{
    code: '010120',
    name: '买单'
  }, {
    code: '010110',
    name: '团购'
  }, {
    code: '010130',
    name: '扫一扫'
  }, {
    code: '010140',
    name: '付款码'
  }, {
    code: '010170',
    name: '安心付'
  }, {
    code: '010180',
    name: '用券买单'
  }, {
    code: '020000',
    name: '外卖'
  }],
  sjlyInners: reactive([{
    code: '1',
    name: '内部数据'
  }, {
    code: '2',
    name: '外部数据'
  }]),
}

// 点击查询
const doFilter = () => {

  // 1、查询条件如果定义了过滤方式，按定义过滤方式执行
  // 2、如果查询条件没有定义过滤方式，按默认过滤方式执行

  let defaultParams = {}
  let filters: any[] = []
  let keys = Object.keys(searchParams);

  keys.forEach((key) => {
    if (searchParams[key] && searchParams[key].value && searchParams[key].filter) {
      if (Array.isArray(searchParams[key].value)) {
        if (searchParams[key].value.length == 2) {
          filters.push({
            aggOperator: null,
            column: [key],
            sqlOperator: "GTE",
            values: [
              {
                value: searchParams[key].value[0],
                valueType: searchParams[key].valueType,
              },
            ],
          })
          filters.push({
            aggOperator: null,
            column: [key],
            sqlOperator: "LTE",
            values: [
              {
                value: searchParams[key].value[1],
                valueType: searchParams[key].valueType,
              },
            ],
          })
        }

      } else {
        filters.push({
          aggOperator: null,
          column: [key],
          sqlOperator: searchParams[key].filter,
          values: [
            {
              value: searchParams[key].value,
              valueType: searchParams[key].valueType,
            },
          ],
        })
      }
    } else if (searchParams[key] && searchParams[key].value) {
      if (!Array.isArray(searchParams[key].value))
        defaultParams[key] = searchParams[key].value
      else if (searchParams[key].value.length > 0)
        defaultParams[key] = searchParams[key].value
    }
  })

  searchKey.datartParams.defaultFilters = filters
  searchKey.datartParams.defaultFilters.push({
    aggOperator: null,
    column: ["sjly_inner"],
    sqlOperator: "EQ",
    values: [{
      value: "1",
      valueType: "STRING",
    }]
  })

  removeKey(finalSearchParams, ["datartParams", "fileName"]);
  Object.assign(finalSearchParams, searchKey, defaultParams)

  onFilterChange()
}

// 头部查询条件
const searchParams = reactive({
  order_booking_code: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  mt_booking_code: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  order_code: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  create_time_booking: {
    value: [] as string[],
  },
  scene_type: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  meal_location_city_booking: {
    value: null,
    valueType: "STRING",
    filter: 'LIKE'
  },
  restaurant_name: {
    value: null,
    valueType: "STRING",
    filter: 'LIKE'
  },
  order_status_booking_search: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },

  budget_source_code: {
    value: null,
    valueType: "STRING",
    filter: 'LIKE'
  },
  fee_item_name: {
    value: null,
    valueType: "STRING",
    filter: 'LIKE'
  },
  account_company_code: {
    value: null
  },
  second_business_type: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  settle_code: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  account_code: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  cvp_code: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  balance_payment_time: {
    value: [] as string[],
  },
  signer_code: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  signer_name: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  field_code: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  pt_code: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },
  pl_code: {
    value: null,
    valueType: "STRING",
    filter: 'EQ'
  },

  scene_type_name: {value: null, valueType: "STRING", filter: 'EQ'},
  checkin_time: {value: null, valueType: "STRING", filter: 'EQ'},
  meal_location_province_booking: {value: null, valueType: "STRING", filter: 'LIKE'},
  second_business_name: {value: null, valueType: "STRING", filter: 'EQ'},
  pay_type_name: {value: null, valueType: "STRING", filter: 'LIKE'},
  ent_pay_amount: {value: null, valueType: "STRING", filter: 'EQ'},
  realtime_service_fee: {value: null, valueType: "STRING", filter: 'EQ'},
  staff_pay_amount: {value: null, valueType: "STRING", filter: 'EQ'},
  order_origin_price: {value: null, valueType: "STRING", filter: 'EQ'},
  total_reduce_amount: {value: null, valueType: "STRING", filter: 'EQ'},
  order_final_pay: {value: null, valueType: "STRING", filter: 'EQ'},
  actual_payment_amount_booking: {value: null, valueType: "STRING", filter: 'EQ'},
  ent_final_pay: {value: null, valueType: "STRING", filter: 'EQ'},
  total_refund_amount: {value: null, valueType: "STRING", filter: 'EQ'},
  pay_code: {value: null, valueType: "STRING", filter: 'EQ'},
  budget_department_code: {value: null, valueType: "STRING", filter: 'EQ'},
  budget_department_name: {value: null, valueType: "STRING", filter: 'LIKE'},
  account_company_name: {value: null, valueType: "STRING", filter: 'EQ'},
  order_status_booking_show: {value: null, valueType: "STRING", filter: 'LIKE'},
  statement_status_name: {value: null, valueType: "STRING", filter: 'EQ'},
  restaurant_name_booking: {value: null, valueType: "STRING", filter: 'LIKE'},
  sjly_inner: {value: null, valueType: "STRING", filter: 'EQ'},
})

// 默认查询定义
const searchKey = reactive<ReportFilter>({
  datartParams: {
    moduleType: 1,
    type: "banquetBooking",
    viewId: "52b917a05b904bc1aae9ace37666a68f",
    aggregators: banquetBookingColumns,
    defaultFilters: [{
      aggOperator: null,
      column: ["sjly_inner"],
      sqlOperator: "EQ",
      values: [{
        value: "1",
        valueType: "STRING",
      }]
    }],
    orders: [{
      "column": [
        "create_time_booking"
      ],
      "operator": "DESC"
    }],
  },
  fileName: "异地宴请-预订单",
});

// 最终查询参数
const finalSearchParams = Object.assign({}, searchKey)

const {
  data,
  onFilterChange,
  pagination,
  loading,
  onPageChange,
  downloading,
  download
} = useSearch<ReportType, ReportFilter>(reportApi, finalSearchParams, "banquet");

const handleReset = () => {
  reset(searchKey, ["datartParams", 'fileName']);
  reset(finalSearchParams);
  resetValue(searchParams);
  doFilter();
};

const resetValue = (params: any) => {
  Object.keys(params).forEach((key) => {
    if ('sjly_inner' == key) {
      console.log('内外部处理')
      if(sjlyInnersShow){
        params[key].value = null;
      }
    } else {
      params[key].value = null;
    }
  });
  return params;
}
const removeKey = (params: any, ignore?: Array<string>) => {
  Object.keys(params).forEach((key) => {
    if (!ignore) delete params[key];
    if (ignore && ignore.filter((item) => item == key).length == 0) {
      delete params[key];
    }
  });
  return params;
}

const columns = aggregatorsToColumn(banquetBookingColumns);

// 预算部门
const settleDepartment = ref([]);
// 结算单位
const settleCompany = ref([] as Array<ApplyCompanyType>);

// 查询领域、平台、产业线
const areaList = ref([]);
const platformList = ref([]);
const industryList = ref([]);

// 结算单位过滤
const filterOption = (input: string, option: any) => {
  return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const handleBudgetSearch = (val: string) => {
  //管理员搜索
  getPowerByApprove(val, 1);
};

const handleSearch = (val: string) => {
  getPowerByApprove(val, 2);
};

const handleAreaSearch = (val: string) => {
  getPowerByApprove(val, 3);
};

const handlePtSearch = (val: string) => {
  getPowerByApprove(val, 4);
};

const handlePlSearch = (val: string) => {
  getPowerByApprove(val, 5);
};

const sjlyInnersShow = ref(false);

// 根据类型查询不同权限类型 permissionType 3:领域 4:平台 5:产业线
const getPowerByApprove = async (name: string, permissionType: number) => {
  const data = await reportApi.getPowerByApprove(name, permissionType, 1, 'banquetBooking');
  switch (permissionType) {
    case 1:
      settleDepartment.value = data;
      break;
    case 2:
      settleCompany.value = data;
      break;
    case 3:
      areaList.value = data;
      break;
    case 4:
      platformList.value = data;
      break;
    case 5:
      industryList.value = data;
      break;
      // case 6:
      //   budgetTypeList.value = data;
      //   break;
    case 99: {
      if (data && data.length > 1) {
        sjlyInnersShow.value = true;
        searchParams.sjly_inner.value = '1'
        searchKey.datartParams.defaultFilters = [{
          aggOperator: null,
          column: ["gngj"],
          sqlOperator: "EQ",
          values: [
            {
              value: "1",
              valueType: "STRING",
            },
          ],
        }]
      }
    }
      break;
    default:
      break;
  }
};

onMounted(() => {
  // getAreaList({ type: "hotel", moduleType: 1, keyword: "" })
  getPowerByApprove("", 1);
  getPowerByApprove("", 2);
  getPowerByApprove("", 3);
  getPowerByApprove("", 4);
  getPowerByApprove("", 5);
  // getPowerByApprove("", 6);
  getPowerByApprove("", 99);
});
</script>

<style scoped lang="less">

</style>