// 会议类型

type keys = 'DOMESTIC' | 'INTERNATIONAL'

export const DistrictTypeConstant = {
  DOMESTIC: { code: 0, desc: '国内' },
  INTERNATIONAL: { code: 1, desc: '国际' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in DistrictTypeConstant) {
      const item = DistrictTypeConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(DistrictTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return DistrictTypeConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
