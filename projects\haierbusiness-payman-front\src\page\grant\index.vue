<script setup lang="ts">
import {
  Upload as HUpload,
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps
} from 'ant-design-vue';
import type { UploadChangeParam, UploadProps } from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { DownOutlined, ExclamationCircleOutlined, PlusOutlined, UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { RechargeIssueApi,fileApi } from '@haierbusiness-front/apis';
import {
  PayTypeChildConstant,
  RtoPageParams,

} from '@haierbusiness-front/common-libs';
import { getCurrentRouter , errorModal, routerParam,getCurrentRoute } from '@haierbusiness-front/utils';
import Eloading from '@haierbusiness-front/components/loading/ELoading.vue';
import dayjs, { Dayjs } from 'dayjs';
import { Ref, computed, getCurrentInstance, onMounted, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';

const router = getCurrentRouter()
const route = ref(getCurrentRoute());
const columns: ColumnType[] = [
  {
    title: '订单编号',
    dataIndex: 'orderCode',
    fixed: 'left',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '操作人',
    dataIndex: 'applyName',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '操作时间',
    dataIndex: 'applyTime',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '总计金额',
    dataIndex: 'amountSum',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '实际下发金额',
    dataIndex: 'realAmount',
    width: '120px',
    align: 'center',
    ellipsis: true
  },  
  {
    title: '下发状态',
    dataIndex: 'rechargeStatus',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '250px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<any>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(RechargeIssueApi.list, {
  manual: false
});

const {
  data: exportListData,
  run: exportListApiRun,
  loading: exportListLoading,
} = useRequest(RechargeIssueApi.exportIssueBill);

const reset = () => {
  startBeginAndEnd.value = undefined
  searchParam.value = { }
  listApiRun({
    ...searchParam.value,
    pageNum:1,
    pageSize:10,
  });
}

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};


const gotoDetails = (record: RtoPageParams) => {
  router.push({ path: "/payman/grant/detail", query: { id: record.id } })
}


// 再次通知
const notifyAgain = (record: RtoPageParams) => {
  RechargeIssueApi.refundNotifyAgain({ refundCode: record.code, state: record.state, notifyUrl: record.notifyUrl })
    .then(() => {
      message.success('操作成功！', 4)
    })
}

const startBeginAndEnd = ref<[Dayjs, Dayjs]>()
watch(() => startBeginAndEnd.value, (n: any, o: any) => {
  if (n) {
      searchParam.value.startTime = dayjs(n[0]).format('YYYY-MM-DD') + " 00:00:00";
      searchParam.value.endTime = dayjs(n[1]).format('YYYY-MM-DD') + " 23:59:59";
    } else {
      searchParam.value.startTime = undefined;
      searchParam.value.endTime = undefined;
    }
});

const getStatus=(status:number)=>{
  if(status==10){
    return '待下发'
  }else if(status==20){
    return '下发中'
  }else if(status==30){
    return '下发失败'
  }else if(status==40){
    return '部分成功'
  }else if(status==90){
    return '下发成功'
  }
}


onMounted(()=>{
  searchParam.value.orderCode = route.value?.query?.orderCode;
  listApiRun({
    ...searchParam.value,
    pageNum:1,
    pageSize:10,
  });
})

</script>

<template>

  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">

    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="orderCode">订单编号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="orderCode" v-model:value="searchParam.orderCode" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="applyCodeOrName">操作人：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="applyCodeOrName" v-model:value="searchParam.applyCodeOrName" placeholder=""
              autocomplete="off" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="rechargeStatus">下发状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="rechargeStatus" v-model:value="searchParam.rechargeStatus" style="width: 100%" allow-clear>
              <h-select-option :value="10">待下发</h-select-option>
              <h-select-option :value="20">下发中</h-select-option>
              <h-select-option :value="30">下发失败</h-select-option>
              <h-select-option :value="40">部分成功</h-select-option>
              <h-select-option :value="90">已完成</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;">
            <label for="startBeginAndEnd">操作时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="startBeginAndEnd" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" />
          </h-col>
        </h-row>
      
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            
            <h-button type="primary" style="margin-right: 10px" :loading="exportListLoading"
              @click="exportListApiRun(searchParam);">
              <UploadOutlined />
              导出
            </h-button>
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ x: 1550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'rechargeStatus'">
              {{getStatus(record.rechargeStatus)}}
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="gotoDetails(record)">
                详情
              </h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
