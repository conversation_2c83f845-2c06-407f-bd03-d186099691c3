const sessionStorage = window.sessionStorage;
const localStorage = window.localStorage;

/**
 * 保存数据到本地
 * @param {*} obj 保存的对象
 * @param {String} name 保存的名字
 * @param {Boolean} [isSessionStorage] 是否保存到sessionStorage
 * @param {Number} [expireTime] 过期时间 ms
 */
export function saveDataToLocal(name: string, obj: any, isSessionStorage = true, expireTime?: number) {
    if (obj === null) {
        if (isSessionStorage) {
            sessionStorage.removeItem(name)
        } else {
            localStorage.removeItem(name)
        }
        return;
    }
    if (obj && (obj instanceof Array || obj instanceof Object)) {
        if (isSessionStorage) {
            sessionStorage.setItem(name, JSON.stringify(obj))
        } else {
            localStorage.setItem(name, JSON.stringify(obj))
        }
    } else {
        if (isSessionStorage) {
            sessionStorage.setItem(name, obj)
        } else {
            localStorage.setItem(name, obj)
        }
    }
    //存过期时间
    if (expireTime) {
        let expireTimeObjStr = JSON.stringify({
            time: new Date().getTime(),
            expireTime: expireTime
        });
        if (isSessionStorage) {
            sessionStorage.setItem(name + "ExpireTime", expireTimeObjStr)
        } else {
            localStorage.setItem(name + "ExpireTime", expireTimeObjStr)
        }
    }
}

/**
 * 从本地读取数据
 * @param {String} name 去取数据的名字
 * @param {Boolean} [isSessionStorage] 是否从sessionStorage读取
 */
export function loadDataFromLocal(name: string, isSessionStorage = true): any {
    let dataStr = null;
    let data = null;
    if (isSessionStorage) {
        dataStr = sessionStorage.getItem(name);
    } else {
        dataStr = localStorage.getItem(name);
    }
    if (dataStr && typeof dataStr == 'string') {
        //如果过期直接返回null
        let expireTimeObj = loadDataFromLocal(name + "ExpireTime", isSessionStorage);
        if (expireTimeObj) {
            if ((new Date().getTime() - expireTimeObj.time) >= expireTimeObj.expireTime) {
                removeStorageItem(name, isSessionStorage);
                return null;
            }
        }
        let first = dataStr.charAt(0);
        //如果是数组或对象
        if (first === '{' || first === '[') {
            data = JSON.parse(dataStr);
        } else {
            //尝试保持原始数据类型
            try {
                data = JSON.parse(dataStr);;
            } catch (e) {
                data = dataStr;
            }
        }
    } else {
        data = dataStr;
    }
    if (!data) {
        removeStorageItem(name, isSessionStorage);
    }
    return data;
}

/**
 * 删除key
 */
export function removeStorageItem(name: string, isSessionStorage = true) {
    if (isSessionStorage) {
        sessionStorage.removeItem(name);
        sessionStorage.removeItem(name + "ExpireTime");
    } else {
        localStorage.removeItem(name);
        localStorage.removeItem(name + "ExpireTime");
    }
}

/**
 * 清空Storage
 */
export function clearStorage(isSessionStorage = true) {
    if (isSessionStorage) {
        sessionStorage.clear();
    } else {
        localStorage.clear();
    }
}