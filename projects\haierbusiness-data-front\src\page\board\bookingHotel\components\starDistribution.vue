<template>
  <div background="rgba(0,0,0,0)" :id="id" :style="{ height: props.height + 'vh' }"></div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import * as echarts from "echarts";
import { queryStarDistribution } from "@haierbusiness-front/apis/src/data/board";
import { circle2 as cicleOptions, colors } from "../../data";
import { EventBus } from "../../eventBus";
import { useRouter, useRoute } from "vue-router";
const route = useRoute();
const props = defineProps({
  height: {
    type: Number,
    default: 33,
  },
});
const payTypeCheck = ref<string>("");
const id = ref("cicle-" + Date.now());
const loading = ref(false);
let chartDom, myChart: any;
onMounted(() => {
  chartDom = document.getElementById(id.value);
  myChart = echarts.init(chartDom as any, "dark");
  queryData();

  //饼状图点击事件
  myChart.on("click", (param: { from: string; name: string }) => {
    if (param.from != "order_hotel_star_level" && param.name != payTypeCheck.value) {
      EventBus.emit("refresh", {
        ...param,
        from: "order_hotel_star_level",
      });
    } else {
      payTypeCheck.value = "";
      EventBus.emit("refresh");
    }
  });
});
EventBus.on((event, params) => {
  if (event == "refresh") {
    if (!params) queryData();
    if (params && params.from != "order_hotel_star_level") queryData(params);
    if (params && params.from == "order_hotel_star_level") {
      queryData().then(() => {
        console.log(params, 11);
        payTypeCheck.value = params.data.name;
        myChart.dispatchAction({
          type: "highlight",
          seriesIndex: 0,
          dataIndex: params.dataIndex,
        });
      });
    }
  }
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
  loading.value = true;
  const data = await queryStarDistribution(
    params ? params.data.name : null,
    params ? params.from : null
  );
  loading.value = false;
  const rows: any = [];
  data.rows.forEach((item: any) => {
    rows.push({
      value: item[1],
      name: item[0],
    });
  });
  const { series }: any = cicleOptions;
  series[0].color = colors;
  series[0].data = rows;
  myChart.clear();

  myChart.setOption({
    ...cicleOptions,
    tooltip: { trigger: "item", formatter: "{b} {c} 间夜" },
  });
  return rows;
};
</script>
<style scoped lang="less"></style>
