import { post } from '../../request'

import {
    Result
} from '@haierbusiness-front/common-libs';
import { getCommonFilter, getCommonFilterSynchronism } from "./index";


const getTypeKey = (url: number | string) => {
    const resultMap: any = {
        "/data/board/booking-hotel": "localHotel",
        "/data/board/ordering-food": "localRestaurant",
        "/data/board/travel/internal": "domesticAirTickets",
        "/data/board/travel/external": "internationalAirfare",
        "/data/board/travel/hotel": "hotel",
        "/data/board/travel/taxi": "taxi",
        "/data/board/travel/train": "trainTicket",
        "/data/board/mice/offsite": "miceOffsite",
        "/data/board/mice/local": "miceLocal",
        "/data/board/travel/index": "travelOverview",
        default: "",
    };
    return resultMap[url] || resultMap.default;
};

const commonParams = {
    moduleType: 2,
    type: getTypeKey(window.location.hash.split('#')[1]),
}


const queryCommonData = (params): Promise<Result> => {
    return post("data/api/bi/common/data", {
        concurrencyControl: true,
        concurrencyControlMode: "DIRTYREAD",
        ...params,
    });
};
//会务青岛
const defaultFilters = (type) => [
    {
        aggOperator: null,
        column: ["status"],
        sqlOperator: "IN",
        values: [
            {
                value: "会议完成",
                valueType: "STRING",
            },
            {
                value: "会议执行中",
                valueType: "STRING",
            },
        ],
    },
    {
        aggOperator: null,
        column: ["type"],
        sqlOperator: "IN",
        values: [
            {
                value: type,
                valueType: "STRING",
            },
        ],
    },
];
//查询累计成交
export const queryLocalAccumulative = (
    { type },
    name,
    from
): Promise<Result> => {
    let filtersArr: any = defaultFilters(type);
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "gmt_create",
        budgetDepartmentKey: 'budget_department_code',
    });
    let functionColumns: any = [];
    functionColumns.push({
        alias: "reduce_price_ratio_avg",
        snippet: "SUM(reduce_amount)/SUM(bid_price)",
    });
    return queryCommonData({
        ...commonParams,
        viewId: "99186c758c30414fae907c2e14c69626",
        aggregators: [
            {
                alias: "订单数",
                column: ["code"],
                sqlOperator: "COUNT",
            },
            {
                alias: "中标金额",
                column: ["scheme_amount"],
                sqlOperator: "SUM",
            },
            {
                alias: "参会人次",
                column: ["person_num"],
                sqlOperator: "SUM",
            },
            {
                alias: "降费金额",
                column: ["reduce_amount"],
                sqlOperator: "SUM",
            },
            {
                alias: "降费率",
                column: ["reduce_price_ratio_avg"],
            },
            {
                alias: "经办部门",
                column: ["department_code"],
                sqlOperator: "COUNT_DISTINCT",
            },
            {
                alias: "结算单位",
                column: ["account_unit"],
                sqlOperator: "COUNT_DISTINCT",
            },
        ],
        filters,
        functionColumns,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};


//会议业务趋势同期
export const querySynchronismLocalBusinessTrend = (
    { functionColumns, type }: any,
    name: string | null,
    from: string | null,
    dateParams: any
): Promise<Result> => {
    let filtersArr: any = defaultFilters(type);
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilterSynchronism({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "gmt_create",
        budgetDepartmentKey: 'budget_department_code',
        dateParams,
    });
    return queryCommonData({
        ...commonParams,
        viewId: "99186c758c30414fae907c2e14c69626",
        aggregators: [
            {
                alias: "订单数",
                column: ["code"],
                sqlOperator: "COUNT",
            },
            {
                alias: "中标金额",
                column: ["scheme_amount"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "gmt_create_group",
                column: ["gmt_create_group"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns,
    });
};
//会议业务趋势
export const queryLocalBusinessTrend = (
    { functionColumns, type }: any,
    name: string | null,
    from: string | null
): Promise<Result> => {
    let filtersArr: any = defaultFilters(type);
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "gmt_create",
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        viewId: "99186c758c30414fae907c2e14c69626",
        aggregators: [
            {
                alias: "订单数",
                column: ["code"],
                sqlOperator: "COUNT",
            },
            {
                alias: "中标金额",
                column: ["scheme_amount"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "gmt_create_group",
                column: ["gmt_create_group"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
        functionColumns,
    });
};
//会议类型分布
export const queryLocalTypePercentage = (
    { type },
    name,
    from
): Promise<Result> => {
    let filtersArr: any = defaultFilters(type);
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "gmt_create",
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        viewId: "99186c758c30414fae907c2e14c69626",
        aggregators: [
            {
                alias: "订单数量",
                column: ["code"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "会务种类",
                column: ["co_type"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
//酒店星级分布
export const queryLocalStarPercentage = (
    { type },
    name,
    from
): Promise<Result> => {
    let filtersArr: any = defaultFilters(type);
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    filtersArr.push({
        aggOperator: null,
        column: ["scheme_type"],
        sqlOperator: "EQ",
        values: [
            {
                value: "1",
                valueType: "STRING",
            },
        ],
    });
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "co_gmt_create",
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        viewId: "a4029cef5ec34adaa5fdc93fa5d48919",
        aggregators: [
            {
                alias: "订单数量",
                column: ["co_code"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "酒店星级",
                column: ["supplier_level"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
//酒店排行
export const queryLocalHotelRank = ({ type }, name, from): Promise<Result> => {
    let filtersArr: any = defaultFilters(type);
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    filtersArr.push({
        aggOperator: null,
        column: ["supplier"],
        sqlOperator: "NE",
        values: [
            {
                value: "社会餐厅",
                valueType: "STRING",
            },
        ],
    });
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "co_gmt_create",
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        a: "酒店排行",
        viewId: "a4029cef5ec34adaa5fdc93fa5d48919",
        aggregators: [
            {
                alias: "酒店竞标金额",
                column: ["sub_scheme_amount"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "酒店名称",
                column: ["supplier"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
        orders: [
            {
                aggOperator: "SUM",
                column: ["sub_scheme_amount"],
                operator: "DESC",
            },
        ],
    });
};
//结算单位排行
export const queryLocalSettleRank = ({ type }, name, from): Promise<Result> => {
    let filtersArr: any = defaultFilters(type);
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "gmt_create",
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        viewId: "99186c758c30414fae907c2e14c69626",
        aggregators: [
            {
                alias: "中标金额",
                column: ["scheme_amount"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "结算单位名称",
                column: ["account_company_name"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
        orders: [
            {
                aggOperator: "SUM",
                column: ["scheme_amount"],
                operator: "DESC",
            },
        ],
    });
};
//会议费用分布
export const queryLocalCostPercentage = (
    { type },
    name,
    from
): Promise<Result> => {
    let filtersArr: any = defaultFilters(type);
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "gmt_create",
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        viewId: "2b72b9026c1446aa934e1fa9d13efb42",
        aggregators: [
            {
                alias: "子方案金额",
                column: ["sub_amount"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "子方案类型",
                column: ["sub_type"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
//保险业务趋势
export const queryLocalInranceTrend = (
    { functionColumns, type },
    name,
    from
): Promise<Result> => {
    let filtersArr: any = defaultFilters(type);
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                }
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "gmt_create",
        budgetDepartmentKey: 'budget_department_code',
    });
    filters.push(
        {
            aggOperator: null,
            column: ["insure_amount"],
            sqlOperator: "NOT_NULL",
            values: [],
        }
    )
    functionColumns.push({
        alias: "gmt_create_day",
        snippet: "AGG_DATE_DAY([gmt_create])",
    });
    return queryCommonData({
        ...commonParams,
        viewId: "99186c758c30414fae907c2e14c69626",
        aggregators: [
            {
                alias: "订单数",
                column: ["code"],
                sqlOperator: "COUNT",
            },
            {
                alias: "金额",
                column: ["insure_amount"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "按日group",
                column: ["gmt_create_day"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },

        functionColumns,
    });
};
//全流程跟踪
export const queryLocalProcessPercentage = (
    { type },
    name,
    from
): Promise<Result> => {
    let filtersArr: any = defaultFilters(type);
    filtersArr.shift();
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "gmt_create",
        budgetDepartmentKey: 'budget_department_code',
    });
    filters.push(
        {
            aggOperator: null,
            column: ["flow_status"],
            sqlOperator: "NOT_NULL",
            values: [],
        }
    );
    filters.push(
        {
            "aggOperator": null,
            "column": ["flow_status"],
            "sqlOperator": "NE",
            "values": [{ "value": " ", "valueType": "STRING" }]
        }
    )
    return queryCommonData({
        ...commonParams,
        viewId: "99186c758c30414fae907c2e14c69626",
        aggregators: [
            {
                alias: "中标金额",
                column: ["scheme_amount"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "会务状态",
                column: ["flow_status"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 100,
        },
    });
};
//会议城市排行
export const queryLocalCityRank = ({ type }, name, from): Promise<Result> => {
    let filtersArr: any = defaultFilters(type);
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "gmt_create",
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        viewId: "99186c758c30414fae907c2e14c69626",
        a: "会议城市",
        aggregators: [
            {
                alias: "订单数量",
                column: ["code"],
                sqlOperator: "COUNT",
            },
        ],
        groups: [
            {
                alias: "区名称",
                column: type == "青岛会议" ? ["county_name"] : ["city_name"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
        orders: [
            {
                aggOperator: "COUNT",
                column: ["code"],
                operator: "DESC",
            },
        ],
    });
};
//经办部门排行
export const queryLocalHandlingDepartmentRank = (
    { type },
    name,
    from
): Promise<Result> => {
    let filtersArr: any = defaultFilters(type);
    if (name) {
        filtersArr.push({
            aggOperator: null,
            column: [from],
            sqlOperator: "EQ",
            values: [
                {
                    value: name,
                    valueType: "STRING",
                },
            ],
        });
    }
    const filters = getCommonFilter({
        dataSourceKey: 'budget_source',
        defaultFilters: filtersArr,
        companyColumn: "account_company_code",
        dateColumn: "gmt_create",
        budgetDepartmentKey: 'budget_department_code',
    });
    return queryCommonData({
        ...commonParams,
        viewId: "99186c758c30414fae907c2e14c69626",
        aggregators: [
            {
                alias: "中标金额",
                column: ["scheme_amount"],
                sqlOperator: "SUM",
            },
        ],
        groups: [
            {
                alias: "部门名称",
                column: ["department"],
            },
        ],
        filters,
        pageInfo: {
            countTotal: true,
            pageSize: 10,
        },
        orders: [
            {
                aggOperator: "SUM",
                column: ["scheme_amount"],
                operator: "DESC",
            },
        ],
    });
};
