{"compilerOptions": {"forceConsistentCasingInFileNames": true, "target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "preserve", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "noEmit": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "../../apis/src/request.ts", "../../apis/src/pay/compositionPay.ts", "../../apis/src/pay/pay.ts", "../../apis/src/request.ts"], "references": [{"path": "./tsconfig.node.json"}]}