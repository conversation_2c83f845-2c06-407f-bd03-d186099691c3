<script setup lang="ts">
import {
  BreadcrumbItem as hBreadcrumbItem,
  Breadcrumb as hBreadcrumb,
  LayoutSider as hLayoutSider,
  LayoutFooter as hLayoutFooter,
  Layout<PERSON>ontent as hLayoutContent,
  LayoutHeader as hLayoutHeader,
  Layout as hLayout,
  MenuItem as hMenuItem,
  MenuItemGroup as hMenuItemGroup,
  SubMenu as hSubMenu,
  Menu as hMenu,
  Divider as hDivider,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Result as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Tabs as hTabs,
  message,
  RadioGroup as hRadioGroup,
  RadioButton as hRadioButton,
  Table as hTable,
  Modal as hModal,
  Tree as hTree,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Switch as hSwitch,
  Select  as hSelect,
  SelectOption as hSelectOption 
} from 'ant-design-vue';
import { onMounted, ref, computed, watch,createVNode } from 'vue';
import {
  UploadOutlined,
  UserOutlined,
  NotificationOutlined,
  AppstoreOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ExclamationCircleOutlined,
  SearchOutlined,
  BarsOutlined,
  ClusterOutlined
} from '@ant-design/icons-vue';
import EManage from '@haierbusiness-front/components/manange/EManange.vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { brandReq,DistrictProviderMapPageReq,supplierType,addressLevel,addressType,brandCategory,brandType } from '@haierbusiness-front/common-libs';
import { brandApi } from '@haierbusiness-front/apis';
import type { Rule } from 'ant-design-vue/es/form';
import { DataType, usePagination, useRequest } from 'vue-request';
import type { TreeProps } from 'ant-design-vue';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute, getEnumOptions } from "@haierbusiness-front/utils";
const supplierTypeOptions = computed(()=>{
  return getEnumOptions(supplierType)
})
const brandCategoryOptions = computed(()=>{
  return getEnumOptions(brandCategory)
})
const brandTypeOptions = computed(()=>{
  return getEnumOptions(brandType,true)
})
const addressTypeOptions = computed(()=>{
  return getEnumOptions(addressType,true)
})

const store = applicationStore();
const { resource } = storeToRefs(store);
const tabValue = ref<string>("1")
const modalTabValue = ref<string>("1")
const mappingBoxShow = ref<boolean>(false)
const addBoxShow = ref<boolean>(false)
const treeData = ref<brandReq[]>([])
const providerTreeData = ref<DistrictProviderMapPageReq[]>([])
const openBoxTitle = ref<string>("")
const brandId = ref<string>("") //国旅品牌品牌id
const confirmLoading = ref<boolean>(false)
const editBoxShow = ref<boolean>(false)
const rowData = ref<brandReq>({})
const mappingType = ref<string>('XC')
  const rules: Record<string, Rule[]> = {
    type: [{ required: true, message: '请选择品牌类型' }],

};
const columnsFormng:any = [
  {
    title: '供应商',
    dataIndex: 'providerCodeName',
    key: 'providerCodeName',
    ellipsis: true,
  },
  {
    title: '品牌名称',
    dataIndex: 'providerBrandName',
    key: 'providerBrandName',
  },
  {
    title: '集团名称',
    dataIndex: 'providerBrandGroupName',
    key: 'providerBrandGroupName',
  },
  {
    title: '操作',
    dataIndex: '_operator',
    align: 'center',
    width: '60px',
  },
]

const columnsp:any = [
  {
    title: '供应商',
    dataIndex: 'providerCodeName',
    key: 'providerCodeName',
    ellipsis: true,
  },
  {
    title: '品牌名称',
    dataIndex: 'providerBrandName',
    key: 'providerBrandName',
  },
  {
    title: '集团名称',
    dataIndex: 'providerBrandGroupName',
    key: 'providerBrandGroupName',
  },
  {
    title:'品牌code',
    dataIndex: 'providerBrandCode',
    key: 'providerBrandCode',
  },
  {
    title: '操作',
    dataIndex: '_operator',
    align: 'center',
    width: '60px',
  },
]

  const columns:any = [
  {
    title: '品牌名称',
    dataIndex: 'brandName',
    key: 'brandName',
    ellipsis: true,
    align:"center",
  },
  {
    title: '品牌分类',
    dataIndex: 'brandCategoryName',
    key: 'brandCategoryName',
    align:"center",
  },
  {
    title: '集团名称',
    dataIndex: 'brandGroupName',
    key: 'brandGroupName',
    align:"center",
  },
  {
    title: '携程映射',
    dataIndex: 'mappingProviderHotelBrandList',
    key: 'mappingProviderHotelBrandList',
    align:"center",
    width: '280px',
    customFilterDropdown: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    align: 'center',
    width: '180px',
  },
];

// 获取行政区划列表
const searchParam = ref<brandReq>({
  providerCodes:["XC", "AMAP", "QT", "MT", "YD", "RJ", "JJ", "HZ", "TXFC", "VETECH"]
})
const {
  data:dataList,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(brandApi.getHotelBrandList);

const dataSource = computed(() => dataList.value?.records || []);

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const pagination = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: dataList.value?.total,
    current: dataList.value?.pageNum,
    pageSize: dataList.value?.pageSize,
    style: { justifyContent: 'center' },
}));


// 获取供应商行政区划
const searchProviderParam = ref<DistrictProviderMapPageReq>({})
const {
  data:dataListProvider,
  run: listApiRunProvider,
  loading:loadingProvider,
  current:currentProvider,
  pageSize:pageSizeProvider,
} = usePagination(brandApi.getHotelBrandProviderMapList);

const dataSourceProvider = computed(() => dataListProvider.value?.records || []);

const handleProviderTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRunProvider({
    ...searchProviderParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const providerTableReset = () =>{
  searchProviderParam.value = {}
  listApiRunProvider({
    ...searchProviderParam.value,
    pageNum: currentProvider.value,
    pageSize: pageSizeProvider.value,
  });
}

const paginationProvider = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: dataListProvider.value?.total,
    current: dataListProvider.value?.pageNum,
    pageSize: dataListProvider.value?.pageSize,
    style: { justifyContent: 'center' },
}));

const mappingData = ref<any>([])

const toMappingMng = (row:brandReq) =>{
  openBoxTitle.value = row.brandName
  brandId.value = row.id
  mappingBoxShow.value = true
  // 获取已关联映射列表
  getMappingList(row.id)
}

const mappingListLoading = ref<boolean>(false)
// 获取已关联映射列表
const getMappingList= (mappingBrandId:string|number)=>{
  mappingListLoading.value = true
  brandApi.getHotelBrandProviderMapList({mappingBrandId}).then((res:any)=>{
    mappingData.value = res.records
    mappingListLoading.value = false
  })
}

const addMapping= () =>{
  addBoxShow.value = true
  // 请求供应商数据
  listApiRunProvider({
    ...searchProviderParam.value,
    pageNum: 1,
    pageSize:10,
  });
  getHotelBrandProviderMapList(1,10)
}

// 请求国旅品牌映射
const mappingLoading = ref<boolean>(false)
const getMappingBrandList =(code:string) =>{
  mappingLoading.value = true
  brandApi.getMappingBrandList({code:code}).then((res:any)=>{
    mappingBrandList.value = res
    mappingLoading.value = false
  })
  .catch(()=>{
    mappingLoading.value = false
  })
}
// 解除供应商映射
const delMappingAdress =  (row:any) =>{
  hModal.confirm({
    title: '确定要删除此映射吗？',
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {
      console.log(row,'-----')
      return new Promise((resolve, reject) => {
        brandApi.deleteMappingHotelBrand({hotelBrandProviderMapId:row.id,providerBrandCode:row.providerBrandCode}).then((res:any)=>{
          message.success('刪除成功')
          getMappingList(brandId.value)
          resolve()
        })
      }).catch(()=>{
        resolve()
      })
    },
    onCancel() {
      console.log('Cancel');
    },
  });
}

// 关联品牌
const association = (row:any) =>{
  hModal.confirm({
    title: '如果已有关联品牌，会覆盖关联,确定要关联此品牌吗？',
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {
      return new Promise((resolve, reject) => {
        brandApi.providerBrandMappingHotelBrand({providerBrandCode:row.providerBrandCode,brandId:brandId.value,providerCode:row.providerCode,providerBrandType:row.type}).then((res:any)=>{
          message.success('关联成功')
          getMappingList(brandId.value)
          listApiRun({
            ...searchParam.value,
            pageNum: 1,
            pageSize: 10,
          });
          getHotelBrandList(1,10)
          addBoxShow.value = false
          resolve()
        })
        .catch(()=>{
          resolve()
        })
      })
    },
    onCancel() {
      console.log('Cancel');
    },
  });
}


// 国旅tree相关
const childrenLoading = ref<boolean>(false)
const onLoadData: TreeProps['loadData'] = treeNode => {
  return new Promise<void>(resolve => {
    childrenLoading.value = true
    brandApi.getHotelBrandList({brandGroupId:treeNode.dataRef.id,providerCodes:["XC", "AMAP", "QT", "MT", "YD", "RJ", "JJ", "HZ", "TXFC", "VETECH"]}).then(res=>{
      treeNode.dataRef.childData = []
      res.records.forEach(item=>{
        item.childData = []
      })
      treeNode.dataRef.childData = res.records
      treeData.value = [...treeData.value];
      resolve()
      childrenLoading.value = false
    })
  });
}
// 展开tree
const onExpand = (expanded:boolean, record:brandReq) =>{
    // 当节点展开时，如果没有加载子节点，则进行加载
    if (expanded) {
      onLoadData({ dataRef: record });
    }
}
const treeListData = ref<any>({})
//获取tree一级列表
const getHotelBrandList = (pageNum:number|string,pageSize:number|string)=>{
  brandApi.getHotelBrandList({type:1,pageNum: pageNum?pageNum:1,pageSize:pageSize?pageSize:10,providerCodes:["XC", "AMAP", "QT", "MT", "YD", "RJ", "JJ", "HZ", "TXFC", "VETECH"]}).then((res:any)=>{
    treeListData.value = res
    res.records.forEach((item:any)=>{
      item.childData = []
    })
    treeData.value = res.records
    })
}
const treePagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: treeListData.value?.total,
  current: treeListData.value?.pageNum,
  pageSize: treeListData.value?.pageSize,
  style: { justifyContent: 'center' },
}));
const handleTreeTableChange = (
pag: { current: number; pageSize: number }
) => {
  getHotelBrandList(pag.current,pag.pageSize)
};

// 供应商tree相关
const providerChildrenLoading = ref<boolean>(false)
const onProviderLoadData: TreeProps['loadData'] = treeNode => {
  return new Promise<void>(resolve => {
    console.log(treeNode,"-------")
    providerChildrenLoading.value = true
    brandApi.getHotelBrandProviderMapList({providerBrandGroupId:treeNode.dataRef.id}).then(res=>{
      treeNode.dataRef.childData = []
      res.records.forEach((item:any)=>{
        item.childData = []
      })
      treeNode.dataRef.childData = res.records
      providerTreeData.value = [...providerTreeData.value];
      resolve()
      providerChildrenLoading.value = false
    })
  });
}
// 展开tree
const onExpandProvider = (expanded:boolean, record:brandReq) =>{
    // 当节点展开时，如果没有加载子节点，则进行加载
    if (expanded) {
      onProviderLoadData({ dataRef: record });
    }
}
const treeProviderListData = ref<any>({})
//获取tree一级列表
const getHotelBrandProviderMapList = (pageNum:number|string,pageSize:number|string)=>{
  brandApi.getHotelBrandProviderMapList({type:1,pageNum: pageNum?pageNum:1,pageSize:pageSize?pageSize:10}).then((res:any)=>{
    treeProviderListData.value = res
    res.records.forEach((item:any)=>{
      item.childData = []
    })
    providerTreeData.value = res.records
    })
}
const treeProviderPagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: treeProviderListData.value?.total,
  current: treeProviderListData.value?.pageNum,
  pageSize: treeProviderListData.value?.pageSize,
  style: { justifyContent: 'center' },
}));
const handleProviderTreeTableChange = (
pag: { current: number; pageSize: number }
) => {
  getHotelBrandProviderMapList(pag.current,pag.pageSize)
};

const glReset = () =>{
  searchParam.value = {providerCodes:["XC", "AMAP", "QT", "MT", "YD", "RJ", "JJ", "HZ", "TXFC", "VETECH"]}
  searchGlList()
}

const searchGlList= () =>{
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize: 10,
  });
  getHotelBrandList(1,10)
}

// 编辑国旅品牌
const editRow = (row:brandReq) =>{
  console.log(row.type)
  rowData.value = row
  editBoxShow.value = true
}

// 提交编辑
const from = ref();
const handleOk = ()=>{
  from.value
    .validate()
    .then(() => {
      confirmLoading.value = true;
      rowData.value.brandCategoryName = brandCategory[rowData.value.brandCategory]
      brandApi.update(rowData.value).then(res=>{
        message.success('更新成功')
        editBoxShow.value = false
        confirmLoading.value = false;
        listApiRun({
          ...searchParam.value,
          pageNum: 1,
          pageSize: 10,
        });
        getHotelBrandList(1,10)
      })
    })
    .catch(() => {
      confirmLoading.value = false;
    });
}

  
onMounted(()=>{
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize: 10,
  });
  getHotelBrandList(1,10)
})
</script>

<template>
  <div class="pageBox">
    <div class="headerBox">
      <h-row>
        <h-col :span="3">
          <h-radio-group v-model:value="tabValue" button-style="solid">
            <h-radio-button value="1">
              <BarsOutlined />平铺
            </h-radio-button>
            <h-radio-button value="2">
              <ClusterOutlined />树形
            </h-radio-button>
          </h-radio-group>
        </h-col>

        <h-col :span="6">
          <h-form-item label="品牌名称">
            <h-input
              allow-clear
              style="width: 75%"
              v-model:value="searchParam.brandName"
              placeholder="请输入品牌名称"
            />
          </h-form-item>
        </h-col>
        <h-col :span="6">
          <h-form-item label="品牌分类">
            <h-select
              ref="select"
              v-model:value="searchParam.brandCategory"
              style="width:75%"
              allow-clear
            >
              <h-select-option
                v-for="item in brandCategoryOptions"
                :value="item.value"
              >{{item.label}}</h-select-option>
            </h-select>
          </h-form-item>
        </h-col>

        <h-col :span="6">
          <h-form-item label="品牌类型">
            <h-select ref="select" v-model:value="searchParam.type" style="width:75%" allow-clear>
              <h-select-option v-for="item in brandTypeOptions" :value="item.value">{{item.label}}</h-select-option>
            </h-select>
          </h-form-item>
        </h-col>
      </h-row>
      <h-row>
        <h-col :span="24" style="text-align: right;">
          <h-button style="margin-right: 10px" @click="glReset">重置</h-button>
          <h-button type="primary" @click="searchGlList()">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </h-button>
        </h-col>
      </h-row>
    </div>
    <div v-if="tabValue=='1'" class="contentBox">
      <!-- :row-selection="rowSelection" -->
      <h-table
        :columns="columns"
        :size="'small'"
        :scroll="{ x: 1550 }"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange($event as any)"
      >
        <template #headerCell="{ column }">
          <template v-if="column.key === 'mappingProviderHotelBrandList'">
            <span style="color: #1890ff">{{supplierType[mappingType]}}映射</span>
          </template>
        </template>
        <template
          #customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
        >
          <div style="padding: 8px">
            <h-select ref="select" v-model:value="mappingType" style="width: 160px" allow-clear>
              <h-select-option
                v-for="item in supplierTypeOptions"
                :value="item.value"
              >{{item.label}}</h-select-option>
            </h-select>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'type'">{{ addressType[record.type] }}</template>
          <template v-if="column.dataIndex === 'level'">{{ addressLevel[record.level] }}</template>
          <template v-if="column.dataIndex === 'mappingProviderHotelBrandList'">
            <p
              v-show="item.providerCode == mappingType"
              v-for="item in record.mappingProviderHotelBrandList"
            >{{ item.mappingBrandName }}</p>
          </template>
          <template v-if="column.dataIndex === '_operator'">
            <h-button @click="editRow(record)" type="link">编辑</h-button>
            <h-button @click="toMappingMng(record)" type="link">映射管理</h-button>
          </template>
        </template>
      </h-table>
    </div>
    <!-- checkable -->
    <div v-else class="contentBox">
      <h-table
        :columns="columns"
        :dataSource="treeData"
        :size="'small'"
        :loadData="onLoadData"
        @expand="onExpand"
        :loading="loading"
        :pagination="treePagination"
        @change="handleTreeTableChange($event as any)"
        rowKey="id"
      >
        <template #headerCell="{ column }">
          <template v-if="column.key === 'mappingProviderHotelBrandList'">
            <span style="color: #1890ff">{{supplierType[mappingType]}}映射</span>
          </template>
        </template>
        <template
          #customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
        >
          <div style="padding: 8px">
            <h-select ref="select" v-model:value="mappingType" style="width: 160px" allow-clear>
              <h-select-option
                v-for="item in supplierTypeOptions"
                :value="item.value"
              >{{item.label}}</h-select-option>
            </h-select>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'mappingProviderHotelBrandList'">
            <p
              v-show="item.providerCode == mappingType"
              v-for="item in record.mappingProviderHotelBrandList"
            >{{ item.mappingBrandName }}</p>
          </template>
          <template v-if="column.dataIndex === '_operator'">
            <h-button @click="editRow(record)" type="link">编辑</h-button>
            <h-button @click="toMappingMng(record)" type="link">映射管理</h-button>
          </template>
        </template>
        <template #expandedRowRender="{record, index}">
          <h-table
            :columns="columns"
            :showHeader="false"
            :dataSource="record.childData"
            :loadData="onLoadData"
            @expand="onExpand"
            :pagination="false"
            :loading="childrenLoading"
            rowKey="id"
          >
            <template #headerCell="{ column }">
              <template v-if="column.key === 'mappingProviderHotelBrandList'">
                <span style="color: #1890ff">{{supplierType[mappingType]}}映射</span>
              </template>
            </template>
            <template
              #customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
            >
              <div style="padding: 8px">
                <h-select ref="select" v-model:value="mappingType" style="width: 160px" allow-clear>
                  <h-select-option
                    v-for="item in supplierTypeOptions"
                    :value="item.value"
                  >{{item.label}}</h-select-option>
                </h-select>
              </div>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'mappingProviderHotelBrandList'">
                <p
                  v-show="item.providerCode == mappingType"
                  v-for="item in record.mappingProviderHotelBrandList"
                >{{ item.mappingBrandName }}</p>
              </template>
              <template v-if="column.dataIndex === '_operator'">
                <h-button @click="editRow(record)" type="link">编辑</h-button>
                <h-button @click="toMappingMng(record)" type="link">映射管理</h-button>
              </template>
            </template>
          </h-table>
        </template>
      </h-table>
    </div>

    <!-- 维护映射关系 -->
    <h-modal
      width="600px"
      v-model:open="mappingBoxShow"
      :title="`维护【${openBoxTitle}】映射关系`"
      :footer="null"
    >
      <h-table
        :columns="columnsp"
        :size="'small'"
        :loading="mappingListLoading"
        :data-source="mappingData"
        :pagination="false"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === '_operator'">
            <h-button @click="delMappingAdress(record)" type="link">删除</h-button>
          </template>
        </template>
      </h-table>
      <h-button @click="addMapping" block style="margin-top:16px;">+ 新增映射</h-button>
    </h-modal>
    <!-- 新增映射 -->
    <h-modal
      width="1400px"
      v-model:open="addBoxShow"
      :title="`新增【${openBoxTitle}】映射关系`"
      :footer="null"
    >
      <h-row>
        <h-col :span="4">
          <h-radio-group v-model:value="modalTabValue" button-style="solid">
            <h-radio-button value="1">
              <BarsOutlined />平铺
            </h-radio-button>
            <h-radio-button value="2">
              <ClusterOutlined />树形
            </h-radio-button>
          </h-radio-group>
        </h-col>
        <h-col v-if="modalTabValue=='1'" :span="20">
          <h-form>
            <h-row>
              <h-col :span="5">
                <h-form-item label="是否未映射">
                  <h-switch
                    v-model:checked="searchProviderParam.brandMappingFlag"
                    checked-children="开"
                    un-checked-children="关"
                  />
                </h-form-item>
              </h-col>
              <h-col :span="6">
                <h-form-item label="品牌名称">
                  <h-input
                    allow-clear
                    style="width: 160px"
                    v-model:value="searchProviderParam.providerBrandName"
                    placeholder="请输入品牌名称"
                  />
                </h-form-item>
              </h-col>
              <h-col :span="6">
                <h-form-item label="供应商">
                  <h-select
                    ref="select"
                    v-model:value="searchProviderParam.providerCode"
                    style="width: 160px"
                    allow-clear
                  >
                    <h-select-option
                      v-for="item in supplierTypeOptions"
                      :value="item.value"
                    >{{item.label}}</h-select-option>
                  </h-select>
                </h-form-item>
              </h-col>
              <h-col :span="6">
                <h-form-item label="品牌类型">
                  <h-select
                    ref="select"
                    v-model:value="searchProviderParam.type"
                    style="width: 160px"
                    allow-clear
                  >
                    <h-select-option
                      v-for="item in brandTypeOptions"
                      :value="item.value"
                    >{{item.label}}</h-select-option>
                  </h-select>
                </h-form-item>
              </h-col>
            </h-row>
          </h-form>
          <h-row>
            <h-col :span="24" style="text-align: right;margin-bottom: 10px">
              <h-button style="margin-right: 10px" @click="providerTableReset">重置</h-button>
              <h-button
                type="primary"
                @click="handleProviderTableChange({ current: 1, pageSize: 10 })"
              >
                <template #icon>
                  <SearchOutlined />
                </template>
                查询
              </h-button>
            </h-col>
          </h-row>
        </h-col>
      </h-row>
      <div v-if="modalTabValue=='1'" class="modalBox">
        <h-table
          :columns="columnsFormng"
          :size="'small'"
          :loading="loadingProvider"
          :data-source="dataSourceProvider"
          @change="handleProviderTableChange($event as any)"
          :pagination="paginationProvider"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === '_operator'">
              <h-button @click="association(record)" type="link">关联</h-button>
            </template>
          </template>
        </h-table>
      </div>
      <div class="modalBox" v-else>
        <h-table
          :columns="columnsFormng"
          :dataSource="providerTreeData"
          :size="'small'"
          :loadData="onProviderLoadData"
          @expand="onExpandProvider"
          :loading="providerChildrenLoading"
          :pagination="treeProviderPagination"
          @change="handleProviderTreeTableChange($event as any)"
          rowKey="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === '_operator'">
              <h-button @click="association(record)" type="link">关联</h-button>
            </template>
          </template>
          <template #expandedRowRender="{record, index}">
            <h-table
              :columns="columnsFormng"
              :showHeader="false"
              :dataSource="record.childData"
              :loadData="onProviderLoadData"
              @expand="onExpandProvider"
              :pagination="false"
              rowKey="id"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === '_operator'">
                  <h-button @click="association(record)" type="link">关联</h-button>
                </template>
              </template>
            </h-table>
          </template>
        </h-table>
      </div>
    </h-modal>
    <!-- 编辑弹窗 -->
    <h-modal
      v-model:visible="editBoxShow"
      :title="'编辑品牌信息'"
      :width="600"
      :confirmLoading="confirmLoading"
      forceRender
      @ok="handleOk"
    >
      <h-form
        v-if="editBoxShow"
        ref="from"
        :model="rowData"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
        :rules="rules"
      >
        <h-form-item label="品牌名称" name="brandName">
          <h-input v-model:value="rowData.brandName" style="width: 100%" />
        </h-form-item>
        <h-form-item label="品牌分类" name="brandCategory">
          <h-select ref="select" v-model:value="rowData.brandCategory" allow-clear>
            <h-select-option v-for="item in brandCategoryOptions" :value="item.value">{{item.label}}</h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="类型" name="type">
          <h-select ref="select" v-model:value="rowData.type" allow-clear>
            <h-select-option :value="1">集团</h-select-option>
            <h-select-option :value="2">品牌</h-select-option>
          </h-select>
        </h-form-item>
      </h-form>
    </h-modal>
  </div>
</template>

<style scoped lang="less">
.pageBox {
  padding: 0 8px;
  .headerBox {
    width: 100%;
    // height: 60px;
    background: #fff;
    // display: flex;
    // align-items: center;
    padding: 12px;
  }
  .inputBox {
    display: flex;
    align-items: center;
    margin-left: 60px;
  }
  .searchBtn {
    float: right;
  }
  .contentBox {
    margin-top: 16px;
    background: #fff;
    height: calc(100vh - 200px);
  }
}
.modalHeaderBox {
  width: 100%;
  // display: flex;
}
:deep(.ant-tree-node-content-wrapper) {
  .btnBox {
    display: none;
  }
  &:hover {
    .btnBox {
      display: inline-block;
    }
  }
}
</style>
