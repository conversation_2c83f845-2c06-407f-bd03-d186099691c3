<script setup lang="ts">
import {
  Modal,
  Tag as hTag,
  Form as hForm,
  FormItem as hFormItem,
  But<PERSON> as hButton,
  Col as hCol,
  Row as hRow,
  Spin as hSpin,
  Table as hTable,
  DatePicker as hDatePicker,
  CollapsePanel as hCollapsePanel,
  Collapse as hCollapse,
  Steps as hSteps,
  Step as hStep,
  Divider as hDivider,
  FormInstance,
  message,
  TableColumnsType,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import {
  AnnualPlanTypeStateConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
  IHaierAccountBillInfo,
} from '@haierbusiness-front/common-libs';
import { getCurrentRouter, resolveParam } from '@haierbusiness-front/utils';
import { ref, createVNode, PropType, computed } from 'vue';

import dayjs, { Dayjs } from 'dayjs';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import {
  EvaluateControlConstant,
  IAnnualPlanDetailRequestDTO,
  IAnnualPlanDetailResponseDTO,
  IAnnualPlanItemDetailResponseDTO,
  IAnnualPlanTypeListRequest,
  IMonthPlanDetailItemResponseDTO,
  PlanTypeConstant,
} from '@haierbusiness-front/common-libs/src/daily';
import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
const { loginUser } = storeToRefs(applicationStore(globalPinia));

const router = getCurrentRouter();
const prop = defineProps({
  data: Object as PropType<IAnnualPlanItemDetailResponseDTO>,
  month: Number as PropType<Number>,
});

const values = computed(() => {
  const result: Map<number, IMonthPlanDetailItemResponseDTO[]> = new Map();
  for (let i of Object.keys(prop?.data?.monthPlanEnterItems as any)) {
    if ((prop?.data?.monthPlanEnterItems as any)[i].length > 0) {
      if(prop?.month && String(i) !== String(prop?.month)){
        continue
      }
      result.set(
        i as unknown as number,
        (prop?.data?.monthPlanEnterItems as any)[i].filter((it: any) => {
          return !it.isDeleted;
        }),
      );
    }
  }
  return result;
});
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px">
    <h-row :gutter="24">
      <h-col :span="20" :offset="2" style="margin-top: 20px" class="h-step">
        <h-steps progress-dot :current="values.size" direction="vertical">
          <h-step v-for="(domain, index) in values">
            <template #title>
              <div>{{ data?.year }} 年 {{ domain[0] }} 月</div>
            </template>
            <template #subTitle>
              <div style="margin-left: 20px" v-for="it in domain[1]">
                <h-tag v-if="it.type === 1" color="blue">{{ it.typeName }}</h-tag>
                <h-tag v-else-if="it.type === 2" color="green">{{ it.typeName }}</h-tag>
                <template v-if="it.planType === PlanTypeConstant.QUANTIFY.code">
                  {{ it.planValue }} / {{ it.planUnit }} ---- {{ it.principalUsername }}
                </template>
                <template v-if="it.planType === PlanTypeConstant.QUALITATIVE.code">
                  {{ it.planDesc }} ---- {{ it.principalUsername }}
                </template>
              </div>
            </template>
          </h-step>
          <h-step v-if="values && values.size > 0" title="流程关闭"> </h-step>
          <h-step v-else :status="'wait'" title="流程节点为空,请添加节点后查看！"> </h-step>
        </h-steps>
      </h-col>
    </h-row>
  </div>
</template>

<style lang="less">
.h-step {
  .ant-steps-item-subtitle {
    margin-inline-start: 0px;
  }
}

@import '../../../assets/css/main.less';
</style>
