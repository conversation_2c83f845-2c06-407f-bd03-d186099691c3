.mobile-box {
  min-height: 100vh;
  background-color: #f6f7f9;
}
.flex {
  display: flex;
}
.flex-column {
  flex-direction: column;
}
.align-items-center {
  align-items: center;
}
.justify-content-center {
  justify-content: center;
}
.justify-content-between {
  justify-content: space-between;
}
.flex-warp {
  flex-wrap: wrap;
}
.width100 {
  width: 100%;
}

.title {
  height: 60px;
  margin: 0 20px;
  .shu {
    display: inline-block;
    height: 14px;
    width: 3px;
    background-color: #0052d9;
    margin-right: 5px;
    border-radius: 4px;
  }
  .text {
    font-size: 18px;
  }
}
.title-mini {
  height: 40px;
  margin: 0 20px;
  
  .text {
    font-size: 14px;
    color: #878889;
  }
}
.mr-5 {
  margin-right: 5px !important;
}
.mt-5 {
  margin-top: 5px !important;
}
.ml-5 {
  margin-left: 5px !important;
}
.mt-10 {
  margin-top: 10px;
}
.mb-5 {
  margin-bottom: 5px !important;
}
.mr-10 {
  margin-right: 10px !important;
}
.mt-20 {
  margin-top: 20px;
}
.ml-10 {
  margin-left: 10px !important;
}
.ml-20 {
  margin-left: 20px !important;
}
.mb-10 {
  margin-bottom: 10px !important;
}
.mb-30 {
  margin-bottom: 30px !important;
}
.strong {
  font-weight: 600;
  font-size: 14px;
}
.mr-20 {
  margin-right: 20px;
}
.font-size-14 {
  font-size: 14px;
}
.font-size-13 {
  font-size: 13px;
}
.font-size-12 {
  font-size: 12px;
}
.color-disabled {
  color: #bbd6f1;
}
.color-eee {
  color: #c0c0c0;
}
.color-main {
  
  color: #0073e5;
}
.my_field_label {
  width: 100%;
  .left {

  }
  .right {
    font-size: 12px;
    color: #8c8c8c;
  }
}
:deep(.van-field__label) {
display: flex;
}
.out-pop {
  .main {
    flex: 1;
  }
  .img-name {
    width: 45px;
    height: 45px;
    
    color: #fff;
    font-size: 14px;
    border-radius: 45px;
  }
  .blue {
    background-color: #0073e5;
  }
  .yellow {
    background-color: #faad14;
  }
  .add-person {
    margin: 16px;
    margin-bottom: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
    font-size: 14px;
  }
  .user-name {
    text-align: left;
    font-size: 16px;
  }
  .phone {
    text-align: left;
    color: #8c8c8c;
  }
}
.out-person-checkbox {
  :deep(.van-checkbox__label) {
    display: flex;
  }
}

.img_empty {
  width: 120px;
  height: 120px;
}
.file-item {
  height: 40px;
  font-size: 14px;
}
.file-name {
  flex: 1;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.del-icon {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.budge-box {
  .user-text {
    color: #bfbfbf;
    font-size: 12px;
  }
  .money-text {
    color: #faad14;
    font-size: 16px;
  }
  .other-text {
    color: #8c8c8c;
    font-size: 12px;
  }
  .yuan {
    font-size: 16px;
  }
}
.city-list {
  width: 100%;
  .city-item {
    position: relative;
  }
  .item-time {
    border: 1px solid #eee;
    padding: 0 6px;
    font-size: 12px;
  }
  .shu {
    height: 30px;
    width: 1px;
    background: #0073e5;
    position: absolute;
    top: 24px;
    left: 6px;
  }
  .dashed {
    height: 30px;
    width: 1px;
    border-right: 1px dashed #bfbfbf;
    position: absolute;
    top: -31px;
    left: 6px;
  }
}
.add-color {
  color: #919191;
}
.range-font {
  font-size: 13px;
    color: #747676;
}
.money-title {
  padding: 4px 10px;
  background-color: #f6f7f9;
  border-radius: 20px;
}
.money-field {
  :deep(.van-field__label) {
    width: 100px;
  }
  :deep(.van-field__error-message) {
    text-align: right;
  }
}

.mobile-box {

  :deep(.van-field__error-message) {
    text-align: right;
  }
}
.color-orange {
  color: #faad14;
}
.text-left {
  :deep(.van-cell__value) {
    text-align: left !important;
  }
}
.weight600 {
  font-weight: 600;
}