import { download, get, post, filepost, originalGet } from '../request'

export const waterworkAreaLogicApi = {
    list: (params: any): Promise<void> => {
        return get('waterworks/api/tregionCost/page', params)
    },
    listAll: (params: any): Promise<void> => {
        return get('waterworks/api/tregionCost/list', params)
    },
    detail: (id: any): Promise<void> => {
        return get(`waterworks/api/tregionCost/detail/${id}`)
    },
    add: (params: any): Promise<void> => {
        return post('waterworks/api/tregionCost/save', params)
    },
    update: (params: object): Promise<void> => {
        return post(`waterworks/api/tregionCost/update`, params);
    },
    export: (params: any): Promise<void> => {
        return download('waterworks/api/tregionCost/export', params)
    },
    delete: (ids: string): Promise<void> => {
        return post(`waterworks/api/tregionCost/delete/${ids}`);
    },
}