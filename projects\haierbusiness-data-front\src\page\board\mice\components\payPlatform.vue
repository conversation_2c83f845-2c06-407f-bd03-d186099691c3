<template>
    <div v-loading="loading" background="rgba(0,0,0,0)" :style="{height:props.height+'vh'}">
        <div :id="circleId" :style="{height:props.height-12+'vh'}"></div>  
        <div class="tips">
            <div class="tips-main">
                <div class="tip" v-for="(row,index) in payTypeDetailRows" :key="row.name">
                    <span class="tip-dot" :style="{borderColor:colors[index]}"></span>
                    <span class="tip-percent">{{ (row.value/payTypeDetailTotal*100).toFixed(0) }}%</span>
                    <span class="tip-title">{{ row.name }}</span>
                </div>
            </div>
        </div>  
    </div>
</template>
<script setup lang="ts">
    import { onMounted, ref } from "vue";
    import { circle,colors } from "../../data"
    import * as echarts from "echarts";
    import { queryOrderingFoodPayPlatform } from "@haierbusiness-front/apis/src/data/board";
    import { EventBus } from "../../eventBus";
    const props = defineProps({
        height:Number,
        type:{
            type:String,
            default:"青岛会议"
        }
    })
    const loading = ref(false);
    const circleId = ref("circle-"+Date.now());
    const payTypeDetailRows = ref([]);
    const payTypeDetailTotal = ref(0);
    let circleChartDom,circleChart;
    onMounted(()=>{
        circleChartDom = document.getElementById(circleId.value);
        circleChart = echarts.init(circleChartDom as any,"dark");
        // queryCircle();
    })
    EventBus.on((event)=>{
        if(event=="refresh"){
            queryCircle();
        }
    })
    const queryCircle = async ()=>{
        loading.value = true;
        const data  = await queryOrderingFoodPayPlatform();
        loading.value = false;
        const payTypeDetailData = [];
        let total = 0;
        data.rows.forEach(item=>{
            total+=item[1];
            payTypeDetailData.push({
                name:item[0],
                value:item[1]
            })
        })
        payTypeDetailTotal.value = total;
        payTypeDetailRows.value = payTypeDetailData;
        const { series } = circle;
        series[0].color = colors;
        series[0].data = payTypeDetailData;
        series[0].radius = ["50%", "80%"];
        circleChart.setOption(circle)
    }

    
</script>
<style scoped lang="less">
    .tips{
        display: flex;
        justify-content: center;
    }
    .tips-main{
        display:flex;
        flex-wrap: wrap;
        width:260px
    }
    .tip{
        width: 130px;
        &-dot{
            display: inline-block;
            width: 10px;
            height: 10px;
            border: 3px solid #FFD700;
            border-radius: 50%;
        }
        &-percent{
            font-size: 20px;
            margin: 0 5px 0 7px;
        }
        &-title{
            font-size: 12px;
        }
    }
    @media screen and (max-width:1500px){
        .tips-main{
            width:200px
        }
        .tip{
            width: 100px;
            &-dot{
                display: inline-block;
                width: 6px;
                height: 6px;
                border: 2px solid #FFD700;
                border-radius: 50%;
            }
            &-percent{
                font-size: 14px;
                margin: 0 3px 0 5px;
            }
            &-title{
                font-size: 10px;
            }
        }
    }
</style>