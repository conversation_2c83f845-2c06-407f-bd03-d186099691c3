<script setup lang="ts">
import {Modal, Tag as hTag, <PERSON><PERSON> as hButton, Col as hCol, Row as hRow, Table as hTable} from 'ant-design-vue';
import {ColumnType} from 'ant-design-vue/lib/table/interface';
import {PlusOutlined, SearchOutlined} from '@ant-design/icons-vue';
import {
  AnnualPlanTypeStateConstant,
  IAnnualPlanTypeListResponse,
  IDailyDeptListRequestDTO, IDailyDeptResponse
} from '@haierbusiness-front/common-libs';
import {getCurrentRouter} from '@haierbusiness-front/utils';
import {ref, createVNode} from 'vue';
import {dailyDeptApi} from '@haierbusiness-front/apis/src/daily';
import {IAnnualPlanTypeListRequest} from '@haierbusiness-front/common-libs/src/daily';
import typeModal from './typeModal.vue';
import { message } from 'ant-design-vue';
const router = getCurrentRouter()

const columns: ColumnType[] = [
  {
    title: '部门编码',
    dataIndex: 'code',
    width: '180px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '部门名称',
    dataIndex: 'name',
    width: '180px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '小微主工号',
    dataIndex: 'managerCode',
    width: '180px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '小微主',
    dataIndex: 'managerName',
    width: '180px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '220px',
    fixed: 'right',
    align: 'center'
  },
];

const searchListParam = ref<IDailyDeptListRequestDTO>({})
const tm = ref()
const listLoading = ref(false)
const listData = ref<IDailyDeptResponse[]>([])

const listApiRun = () => {
  listLoading.value = true
  dailyDeptApi.list(searchListParam.value)
      .then((it) => {
        listData.value = it
      })
      .finally(() => {
        listLoading.value = false
      })
}
listApiRun()
const type = ref()
const typeData = ref<IAnnualPlanTypeListResponse>()

const addDept = () => {
  type.value = 2
  console.log("?????????????" , tm)
  tm?.value.show()
}

const showDept = (data: IAnnualPlanTypeListResponse) => {
  type.value = 1
  typeData.value = data
  tm?.value.show()
}

const updateDept = (data: IAnnualPlanTypeListResponse) => {
  type.value = 3
  typeData.value = data
  tm?.value.show()
}

const deleteDept = (data: IAnnualPlanTypeListResponse) => {
  dailyDeptApi.delete(data)
      .then(() => {
        message.success('删除成功！')
        listApiRun()
      })
      .finally(() => {})
}

const updateDailyTypeStateLoading = ref(false)
const updateDailyTypeState = (data: IAnnualPlanTypeListResponse) => {
  Modal.confirm({
    title: '修改状态',
    content: createVNode('div', {style: 'color:red;max-height:450px;overflow-y: auto;'}, '确认生效后目标类型不可再修改！'),
    maskClosable: true,
    onOk() {
      updateDailyTypeStateLoading.value = true
      type.value = 3
      data.state = AnnualPlanTypeStateConstant.VALID.code
      typeData.value = data
      tm?.value.directSubmitChangeForm()
      updateDailyTypeStateLoading.value = false
    },
  })
}

</script>

<template>
  <type-modal ref="tm" @listApiRun="listApiRun()" :type="type"
              :type-data="typeData"
              :exist-years="listData.map((it)=>it.year)"></type-modal>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
            <h-button type="primary" @click="addDept">
              <PlusOutlined/>
              新增日清部门
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :pagination="false"
                 :data-source="listData" :scroll="{ y: 550 }" :loading="listLoading">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'state'">
              <h-tag v-if="record.state === AnnualPlanTypeStateConstant.IN_VALID.code" color="warning">{{
                  AnnualPlanTypeStateConstant.IN_VALID.desc
                }}
              </h-tag>
              <h-tag v-if="record.state === AnnualPlanTypeStateConstant.VALID.code" color="success">{{
                  AnnualPlanTypeStateConstant.VALID.desc
                }}
              </h-tag>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="showDept(record)">查看</h-button>
              <h-button type="link" @click="updateDept(record)">编辑</h-button>
              <a-popconfirm
                title="确认要删除?"
                ok-text="是"
                cancel-text="否"
                @confirm="deleteDept(record)"
                @cancel="cancel"
              >
                <h-button type="link">删除</h-button>
              </a-popconfirm>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
