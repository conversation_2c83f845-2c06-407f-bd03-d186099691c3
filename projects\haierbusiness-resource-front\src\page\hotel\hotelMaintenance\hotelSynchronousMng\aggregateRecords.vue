<script setup lang="ts">
import {
  BreadcrumbItem as hBreadcrumbItem,
  Breadcrumb as hBreadcrumb,
  RangePicker as hRangePicker,
  LayoutSider as hLayoutSider,
  LayoutFooter as hLayoutFooter,
  LayoutContent as hLayout<PERSON>ontent,
  Layout<PERSON>eader as hLayoutHeader,
  Layout as hLayout,
  MenuItem as hMenuItem,
  MenuItemGroup as hMenuItemGroup,
  SubMenu as hSubMenu,
  <PERSON>u as hMenu,
  Divider as hDivider,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Result as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Tabs as hTabs,
  message,
  RadioGroup as hRadioGroup,
  RadioButton as hRadioButton,
  Table as hTable,
  Modal as hModal,
  Tree as hTree,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Switch as hSwitch,
  Select  as hSelect,
  SelectOption as hSelectOption,
  Image as hImage,
  Progress as hProgress,
} from 'ant-design-vue';
import { onMounted, ref, computed, watch,createVNode  } from 'vue';
import {
  UploadOutlined,
  UserOutlined,
  NotificationOutlined,
  AppstoreOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';
import EManage from '@haierbusiness-front/components/manange/EManange.vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { hotelApi,addressApi } from '@haierbusiness-front/apis';
import { GetMappingHotelListRes,HotelSyncRecordRes,GetHotelSyncRes,asyncStatus,supplierType } from '@haierbusiness-front/common-libs'
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute, getEnumOptions } from "@haierbusiness-front/utils";
import { DataType, usePagination, useRequest } from 'vue-request';
import type { TreeProps } from 'ant-design-vue';
const route = ref(getCurrentRoute());
const router = getCurrentRouter()
const store = applicationStore();
const { resource } = storeToRefs(store);


// 根据id 获取同步信息 getHotelSync
const info = ref<GetHotelSyncRes>({})
const getInfo = ()=>{
  hotelApi.getHotelSync({hotelSyncId:route.value.query.id}).then((res:GetHotelSyncRes)=>{
    info.value = res
  })
}

const supplierTypeOptions = computed(()=>{
  return getEnumOptions(supplierType)
})

const columnsFormng = [
  {
    title: '供应商',
    dataIndex: 'providerCodeName',
    key: 'providerCodeName',
    align:"center",
  },
  {
    title: '开始时间',
    dataIndex: 'startTime',
    align:"center",
    key: 'startTime',
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    key: 'endTime',
    align:"center",
    ellipsis: true,
  },
  {
    title: '聚合数量',
    dataIndex: 'mappingNumber',
    align:"center",
    key: 'mappingNumber',
  },
  {
    title: '状态',
    dataIndex: 'takeEffectFlag',
    key: 'takeEffectFlag',
    align:"center",
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '180px',
    fixed: 'right',
    align: 'center'
  },
]
const reset = () =>{
  searchParam.value = {
    providerCode:route.value.query.providerCode
  }
  listApiRun({
    ...searchParam.value,
    pageNum: current.value,
    pageSize:pageSize.value,
  });
}
// 酒店分页
const searchParam = ref<HotelSyncRecordRes>({
  providerCode:route.value.query.providerCode
})
const {
  data:dataList,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(hotelApi.hotelMappingRecordPage);

const dataSource = computed(() => dataList.value?.records || []);

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  const obj = JSON.parse(JSON.stringify(searchParam.value))
  if(obj.startTimeRange&&obj.startTimeRange.length){
    obj.startTimeRange[0] = obj.startTimeRange[0] + " 00:00:00"
    obj.startTimeRange[1] = obj.startTimeRange[1] + " 23:59:59"
  }
  listApiRun({
    ...obj,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const pagination = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: dataList.value?.total,
    current: dataList.value?.pageNum,
    pageSize: dataList.value?.pageSize,
    style: { justifyContent: 'center' },
}));

// 获取国内城市下拉列表
const hotelList = ref<any>([])
const getDistrictList = ()=>{
  addressApi.getDistrictList({code:'CN',level:'city'}).then(res=>{
    hotelList.value = res.records
  })
}
const filterOption = (input: string, option: any) => {
  if(option.name){
    return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }else{
    return option.brandName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }
};

// 获取区域
const areaList = ref<any>([])
const getAreaList = (value:number)=>{
  addressApi.getDistrictList({code:'CN',level:'district',cityId:value}).then(res=>{
    areaList.value = res.records
  })
}

// 选择完城市 获取区域
const cityChange = (value:number) =>{
  console.log(value)
  if(value){
    getAreaList(value)
  }else{
    areaList.value = []
  }
}

// 获取品牌下拉列表
const BrandList = ref<any>([])
const getBrandList = ()=>{
  hotelApi.getHotelBrandProviderMapList({type:2}).then(res=>{
    BrandList.value = res.records
  })
}

const gotoDetails = (record: any) => {
  router.push({ path: "/hotelMaintenance/aggregateDetails", query: { id: record.id} })
}


onMounted(()=>{
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize:10,
  });
  getInfo()
  getDistrictList()
  getBrandList()
})

</script>

<template>
  <div class="pageBox">
    <div class="headerBox">
        <h-form :labelCol="{span: 6, offset: 1}" style="width:100%;">
        <h-row>
          <!-- <h-col :span="7">
            <h-form-item label="供应商">
              <h-select
                ref="select"
                v-model:value="searchParam.providerCode"
                style="width:100%;"
                allow-clear
              >
              <h-select-option v-for="item in supplierTypeOptions" :value="item.value">{{item.label}}</h-select-option>
              </h-select>
            </h-form-item>
          </h-col> -->
          <h-col :span="7" style="text-align: right;">
            <h-form-item label="开始时间">
              <h-range-picker v-model:value="searchParam.startTimeRange" value-format="YYYY-MM-DD" style="width: 100%;" />
            </h-form-item>
          </h-col>
          <h-col :span="7">
            <h-form-item label="是否已生效">
              <h-radio-group v-model:value="searchParam.takeEffectFlag">
                <h-radio-button :value="true">是</h-radio-button>
                <h-radio-button :value="false">否</h-radio-button>
              </h-radio-group>
            </h-form-item>
          </h-col>
        </h-row>

        <h-row>
          <h-col :span="24" style="text-align: right;">
          <h-button style="margin-right: 10px" @click="reset">重置</h-button>
          <h-button
              type="primary"
              @click="handleTableChange({ current: 1, pageSize: 10 })"
            >
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-form>
        <!-- </h-row> -->
    </div>
    <div  class="contentBox">
      <!-- :row-selection="rowSelection" -->
      <h-table  :columns="columnsFormng" :size="'small'" :scroll="{y:500}" :data-source="dataSource" :loading="loading" :pagination="pagination" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'type'">
              {{ record.type == 1?'新增':record.type==2?'酒店信息修改':'酒店信息删除' }}
            </template>
            <template v-if="column.dataIndex === 'takeEffectFlag'">
              {{ record.takeEffectFlag?'已生效':'待生效' }}
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button @click="gotoDetails(record)" type="link">查看</h-button>
            </template>
          </template>
        </h-table>
    </div>
  </div>
</template>

<style scoped lang="less">
.pageBox {
  padding: 0 8px;
//   height: calc(100vh - 100px);
//   overflow:auto;
  .headerBox {
    width: 100%;
    // height: 60px;
    background: #fff;
    display: flex;
    align-items: center;
    padding: 24px;
  }
  .inputBox {
    display: flex;
    align-items: center;
    // margin-left: 60px;
  }
  .contentBox {
    margin-top: 16px;
    background: #fff;
    height: calc(100vh - 320px);
  }
}
.modalHeaderBox{
  width: 100%;
  // display: flex;
}
</style>
