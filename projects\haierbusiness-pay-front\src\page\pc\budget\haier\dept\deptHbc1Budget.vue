<script setup lang="ts">
import { computed, onMounted, PropType, reactive, ref, watch } from 'vue';
import {
  Table as hTable,
  Divider as hDivider,
  Select as hSelect,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  RadioButton as hRadioButton,
  Modal as hModal,
  Input as hInput,
  InputSearch as hInputSearch,
  Loading as hLoading,
  Space as hSpace,
  Button as hButton,
  Row as hRow,
  Col as hCol,
  Tabs as hTabs,
  TabPane as hTabPane,
  Image as hImage,
  message,
  TableProps,
  InputGroup as hInputGroup,
} from 'ant-design-vue';
import { SwapOutlined } from '@ant-design/icons-vue';
import { IPayData } from '@haierbusiness-front/common-libs/src/pay/model/basicModel';
import { budgetHaierPayApi } from '@haierbusiness-front/apis/src/pay/budgetHaierPay';
import {
  HaierBudgetSourceConstant,
  IUserInfo,
  IUserListRequest,
  PaySourceConstant,
  QueryBudgetInfoRes
} from '@haierbusiness-front/common-libs';
import { DataType, usePagination, useRequest } from 'vue-request';
import { userApi } from '@haierbusiness-front/apis';
import { Key, TablePaginationConfig } from 'ant-design-vue/lib/table/interface';
import { isMobile } from '@haierbusiness-front/utils';
import { Item } from 'ant-design-vue/es/menu';

const props = defineProps<{ budgetType?: string; param?: IPayData }>();

const emit = defineEmits<{
  (e: 'payComplete', isPayComplete: boolean): void;
}>();

// 预算主体
const budgetOrganization = ref();
const budgetOrganizationName = ref();
// 受益主体
const beneficialOrganization = ref();
const beneficialOrganizationName = ref();
// 法人
const legalPerson = ref();
const legalPersonName = ref();
// 成本中心
const costCenter = ref();
const costCenterName = ref();
// 执行主体
const performCode = ref();
const performName = ref();
const budgeterCode = ref();
const budgeterName = ref()
// 查询的列表
const searchDataList = ref([]);
// const leftAmt = ref();

const payLoading = ref(false);
const userLoading = ref(false);

// 费用科目
const {
  data: searchFeeItemsData,
  run: searchFeeItemsRun,
  loading: searchFeeItemsLoading,
} = useRequest(budgetHaierPayApi.searchFeeItems, {
  manual: false,
  defaultParams: [
    {
      applicationCode: props.param?.applicationCode,
      budgetSysCode: HaierBudgetSourceConstant.HBC.code,
      businessType: props.param?.businessType,
    },
  ],
});
const feeItemOptions = computed(() => {
  if (searchFeeItemsData.value) {
    return searchFeeItemsData.value.map((it) => {
      return { value: it.itemCode, label: it.itemName };
    });
  } else {
    return [];
  }
});

const feeItem = ref();
const feeItemName = ref();
watch(feeItem, (newValue, oldValue) => {
  feeItemName.value = feeItemOptions.value.find((it) => {
    return it.value === newValue;
  })?.label;
});

const onSearchCode = () => {
  //if (!feeItem.value) {
  //message.error('请选择费用科目!');
  //return;
  //}
  if (!budgeterCode.value) {
    message.error('请输入预算人!');
    return;
  }

  const reg = /^(^[0-9]*$)|(^[A-Za-z]+$)/;
  if (!reg.test(budgeterCode.value)) {
    visibleUserSearch.value = true;
    searchUserNameParams.value.nickName = budgeterCode.value;
    handleSearchUserRun({ current: 1, pageSize: 10 });
  } else {
    onSearch();
  }
};

const onSearchOk = () => {
  budgeterCode.value = userSelectedRowKeys.value[0];
  if (budgeterCode.value) {
    onSearch();
  }
  visibleUserSearch.value = false;
  userSelectedRowKeys.value = [];
  userSelectedRowRecord.value = {};
};

const feeItemChange = () =>{
  onSearch()
}

const onSearch = () => {
  if (!feeItem.value) {
    message.error('请选择费用科目!');
    return;
  }
  if (!budgeterCode.value) {
    message.error('请输入预算人!');
    return;
  }

  userLoading.value = true;
  // 查询前上次结果清空
  // 清空
  budgetOrganization.value = null;
  budgetOrganizationName.value = null;

  beneficialOrganization.value = null;
  beneficialOrganizationName.value = null;
  legalPerson.value = null;
  legalPersonName.value = null;
  costCenter.value = null;
  costCenterName.value = null;
  performCode.value = null;
  performName.value = null;

  budgetHaierPayApi
    .queryBudgetInfo({
      estimatorCode: budgeterCode.value,
      itemCode: feeItem.value,
    })
    .then((res: any) => {
      res.forEach((item:any,index:number)=>{
        item.index = index
      })
      searchDataList.value = res;
      if (res && res.length) {
        budgetOrganization.value = res[0].budgetOrganization;
        budgetOrganizationName.value = res[0].budgetOrganizationName;
        beneficialOrganization.value = res[0].beneficialOrganization;
        beneficialOrganizationName.value = res[0].beneficialOrganizationName;
        legalPerson.value = res[0].legalPerson;
        legalPersonName.value = res[0].legalPersonName;
        costCenter.value = res[0].costCenter;
        costCenterName.value = res[0].costCenterName;
        performCode.value = res[0].performCode;
        performName.value = res[0].performName;
      }
    })
    .finally(() => {
      userLoading.value = false;
    });
};

const payComplete = () => {
  emit('payComplete', true);
};

const pay = () => {
  if (!feeItem.value) {
    message.error('请选择费用科目!');
    return;
  }
  if (!budgeterCode.value) {
    message.error('请输入预算人!');
    return;
  }
  if (!budgetOrganization.value) {
    message.error("暂无预算主体，请点击搜索预算主体!")
    return;
  }
  if (!performCode.value) {
    message.error("暂无执行主体，请点击搜索执行主体!")
    return;
  }
  if (!legalPerson.value) {
    message.error("暂无出账法人，请点击搜索出账法人!")
    return;
  }
  payLoading.value = true;
  budgetHaierPayApi
    .occupyBudget(
      {
        haierBudgetType: props.budgetType,
        feeItem: feeItem.value,
        feeItemName: feeItemName.value,
        budgeterCode: budgeterCode.value,
        budgeterName: budgeterName.value,
        budgetDepartmentCode: budgetOrganization.value,
        budgetDepartmentName:budgetOrganizationName.value,
        performCode:performCode.value,
        performName: performName.value,
        accountCompanyCode: legalPerson.value,
        accountCompanyName: legalPersonName.value,
        beneficialCode: beneficialOrganization.value,
        beneficialName: beneficialOrganizationName.value,
        costCenter:costCenter.value,
        costCenterName:costCenterName.value,
        
        // - 通用参数
        orderCode: props.param?.orderCode,
        applicationCode: props.param?.applicationCode,
        payTypes: props.param?.payTypes,
        username: props.param?.username,
        providerCode: props.param?.providerCode,
        amount: Number(props.param?.amount),
        orderDetailsUrl: props.param?.orderDetailsUrl,
        notifyUrl: props.param?.notifyUrl,
        callbackUrl: props.param?.callbackUrl,
        description: props.param?.description,
        businessType: props.param?.businessType,
        payload: props.param?.payload,
        paySource: isMobile() ? PaySourceConstant.MOBILE.type : PaySourceConstant.PC.type,
        paymentMethod: 2
      },
      {
        applicationCode: props.param?.applicationCode,
        excludes:
          'paySource,haierBudgetType,feeItem,feeItemName,budgeterCode,budgeterName,budgetDepartmentCode,budgetDepartmentName,performCode,performName,accountCompanyCode,accountCompanyName,beneficialCode,beneficialName,costCenter,costCenterName,paymentMethod',
        nonce: props.param?.hbNonce,
        timestamp: props.param?.hbTimestamp,
        sign: props.param?.sign,
      },
    )
    .then((it) => {
      payComplete();
    })
    .finally(() => {
      payLoading.value = false;
    });
};

// 结算单位
const {
  data: searchUserData,
  run: searchUserRun,
  loading: searchUserLoading,
  current: searchUserCurrent,
  pageSize: searchUserPageSize,
  totalPage: searchUserTotalPage,
} = usePagination(userApi.list, {
  onSuccess(data, params) {
    if (data && data.records && data.records.length > 0) {
      const record = data.records[0];
      const username = record.username || '';
      userSelectedRowKeys.value = [username];
      userSelectedRowRecord.value = record;
    }
  },
});
const searchUserNameParams = ref<IUserListRequest>({});
const visibleUserSearch = ref(false);
const handleSearchUserRun = (pag: { current: number; pageSize: number }) => {
  searchUserRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
    ...searchUserNameParams.value,
    enterpriseCode: 'haier',
  });
};

const userSelectedRowKeys = ref<Key[]>([1]);
const userSelectedRowRecord = ref<IUserInfo>();

const userDepartmentsPagination = computed<TablePaginationConfig>(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: searchUserData.value?.total,
  current: searchUserData.value?.pageNum,
  pageSize: searchUserData.value?.pageSize,
  size: 'default',
  style: { justifyContent: 'center' },
}));
const userTableColumns = [
  {
    title: '',
    dataIndex: '',
    width: '5%',
  },
  {
    title: '工号',
    dataIndex: 'username',
    width: '35%',
  },
  {
    title: '姓名',
    dataIndex: 'nickName',
    width: '60%',
  },
];

const subjectTableColumns = [
  {
    title: '',
    dataIndex: '',
    width: '5%',
  },
  {
    title: '预算主体',
    dataIndex: 'budgetOrganizationName'
  },
  {
    title: '受益主体',
    dataIndex: 'beneficialOrganizationName'
  },
    {
    title: '法人',
    dataIndex: 'legalPersonName'
  },
  {
    title: '成本中心',
    dataIndex: 'costCenterName'
  },
    {
    title: '执行主体',
    dataIndex: 'performName'
  },
];

const onSearchItem = () => {
  console.log(1111);
};

const selectUserSelection: TableProps['rowSelection'] = {
  type: 'radio',
  selectedRowKeys: userSelectedRowKeys as any,
  onChange: (selectedRowKeys: Key[], selectedRows: DataType[]) => {
    userSelectedRowKeys.value = selectedRowKeys;
    userSelectedRowRecord.value = selectedRows[0] as unknown as IUserInfo;
  },
};

const SubjectSelectedRowKeys = ref<Key[]>([0]);
const SubjectSelectedRowRecord = ref<QueryBudgetInfoRes[]>();

const selectSubjectSelection: TableProps['rowSelection'] = {
  type: 'radio',
  selectedRowKeys: SubjectSelectedRowKeys as any,
  onChange: (selectedRowKeys: Key[], selectedRows: DataType[]) => {
    SubjectSelectedRowKeys.value = selectedRowKeys;
    SubjectSelectedRowRecord.value = selectedRows as unknown as QueryBudgetInfoRes[];
  },
};

// 切换主体 
const switchModalShow = ref<boolean>(false)
// 确定切换
const onSwitchOk = () =>{
  if(SubjectSelectedRowRecord.value?.length){
    budgetOrganization.value = SubjectSelectedRowRecord.value[0].budgetOrganization;
    budgetOrganizationName.value = SubjectSelectedRowRecord.value[0].budgetOrganizationName;
    beneficialOrganization.value = SubjectSelectedRowRecord.value[0].beneficialOrganization;
    beneficialOrganizationName.value = SubjectSelectedRowRecord.value[0].beneficialOrganizationName;
    legalPerson.value = SubjectSelectedRowRecord.value[0].legalPerson;
    legalPersonName.value = SubjectSelectedRowRecord.value[0].legalPersonName;
    costCenter.value = SubjectSelectedRowRecord.value[0].costCenter;
    costCenterName.value = SubjectSelectedRowRecord.value[0].costCenterName;
    performCode.value = SubjectSelectedRowRecord.value[0].performCode;
    performName.value = SubjectSelectedRowRecord.value[0].performName;
  }
  switchModalShow.value = false;
  SubjectSelectedRowRecord.value = [];
}
// 点击切换
const onSwitch =()=>{ 
  if(searchDataList.value.length){
    switchModalShow.value = true
  }else{
    message.error('暂无可切换数据!');
  }
}

</script>
<template>
  <h-modal v-model:visible="visibleUserSearch" :title="'用户查询选择'" style="width: 1000px" @ok="onSearchOk">
    <h-row :align="'middle'">
      <h-col :span="24">
        <h-table
          :columns="userTableColumns"
          :row-key="(record) => record.username"
          size="small"
          :row-selection="selectUserSelection"
          :data-source="searchUserData?.records"
          :pagination="userDepartmentsPagination"
          :loading="searchUserLoading"
          @change="handleSearchUserRun($event as any)"
        >
        </h-table>
      </h-col>
    </h-row>
  </h-modal>
  <!-- 切换主体等信息 -->
  <h-modal v-model:visible="switchModalShow" :title="'切换主体'" style="width: 1000px" @ok="onSwitchOk">
    <h-row :align="'middle'">
      <h-col :span="24">
        <h-table
          :columns="subjectTableColumns"
          :row-key="(record) => record.index"
          size="small"
          :row-selection="selectSubjectSelection"
          :data-source="searchDataList"
          :pagination="false"
        >
        </h-table>
      </h-col>
    </h-row>
  </h-modal>
  <h-row style="margin-top: 4vh; text-align: right" :align="'middle'">
    <h-col span="2" style="font-size: 12px"> 预算人： </h-col>
    <h-col span="6" style="text-align: left">
      <h-input-search
        v-model:value="budgeterCode"
        placeholder="工号/姓名"
        :size="'large'"
        class="budget-input"
        :loading="userLoading"
        enter-button
        @search="onSearchCode"
      />
    </h-col>
    <h-col span="2" style="font-size: 12px"> 执行主体： </h-col>
    <h-col span="6" style="text-align: left">
      <h-input
        v-model:value="performName"
        :disabled="true"
        placeholder=""
        :size="'large'"
        :title="performCode + '/' + performName"
        class="budget-input-readonly"
      />
    </h-col>
    <h-col span="2" style="font-size: 12px"> 费用科目： </h-col>
    <h-col span="6" style="text-align: left">
      <h-select v-model:value="feeItem" :size="'large'" class="budget-input" @change="feeItemChange" :options="feeItemOptions"></h-select>
    </h-col>
  </h-row>
  <h-row style="margin-top: 4vh; text-align: right" :align="'middle'">
    <h-col span="2" style="font-size: 12px"> 预算主体： </h-col>
    <h-col span="6" style="text-align: left">
      <!-- <h-input v-model:value="budgetOrganizationName" :disabled="true" placeholder="" :size="'large'" /> -->
    <h-input-group compact>
      <h-input
        v-model:value="budgetOrganizationName"
        :size="'large'"
        :title="budgetOrganization + '/' + budgetOrganizationName"
        class="budget-input-readonly budgetOrganizationNameinput"
        :disabled="true"
      />
      <h-tooltip title="切换主体">
        <h-button @click="onSwitch" style="width:48px;"  :size="'large'">
          <template #icon><SwapOutlined /></template>
        </h-button>
      </h-tooltip>
    </h-input-group>
    </h-col>
    <h-col span="2" style="font-size: 12px"> 受益主体： </h-col>
    <h-col span="6" style="text-align: left">
      <h-input
        v-model:value="beneficialOrganizationName"
        :disabled="true"
        placeholder=""
        :size="'large'"
        :title="beneficialOrganization + '/' + beneficialOrganizationName"
        class="budget-input-readonly"
      />
    </h-col>
    <h-col span="2" style="font-size: 12px"> 出账法人： </h-col>
    <h-col span="6" style="text-align: left">
      <h-input
        v-model:value="legalPersonName"
        :disabled="true"
        placeholder=""
        :size="'large'"
        :title="legalPerson + '/' + legalPersonName"
        class="budget-input-readonly"
      />
    </h-col>
  </h-row>
  <h-row style="margin-top: 4vh; text-align: right" :align="'middle'">
    <h-col span="2" style="font-size: 12px"> 成本中心： </h-col>
    <h-col span="6" style="text-align: left">
      <h-input
        v-model:value="costCenterName"
        :disabled="true"
        placeholder=""
        :size="'large'"
        :title="costCenter + '/' + costCenterName"
        class="budget-input-readonly"
      />
    </h-col>
  </h-row>
  <h-row>
    <h-divider></h-divider>
  </h-row>
  <h-row style="line-height: 14vh" :align="'middle'">
    <h-col span="2" offset="11">
      <h-button type="primary" style="width: 100%" @click="pay" :loading="payLoading" size="large"
        >&nbsp;提&nbsp;&nbsp;交 &nbsp;
      </h-button>
    </h-col>
  </h-row>
</template>

<style scoped lang="less">
.budget-input {
  width: 12vw;
}

.budget-input-readonly {
  width: 12vw;
  background-color: rgb(245, 245, 245);
  color: #2e2e2e;
}
.budgetOrganizationNameinput{
  width: 182px;
  @media (max-width: 1600px) {
      width: 134px;
  }
}
</style>
