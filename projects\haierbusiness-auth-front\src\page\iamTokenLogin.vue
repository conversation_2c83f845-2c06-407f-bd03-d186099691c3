<script setup lang="ts">
import { loginApi } from '@haierbusiness-front/apis';
import { PropType, ref } from 'vue';

import { message } from 'ant-design-vue';
import { ILoginResult, IIamTokenLogin } from '@haierbusiness-front/common-libs';


const props = defineProps({
    param: Object as PropType<IIamTokenLogin>
});

const emit = defineEmits<{
    (e: 'loginSuccess', result: ILoginResult): void
}>()

const loginSuccess = (result: ILoginResult) => {
    emit('loginSuccess', result)
};

const loading = ref(false);

(() => {
    const iamToken = props.param?.urlSearch.get("iam_token");
    if (!iamToken) {
        message.error("iamToken不能为空!")
        return;
    }

    loading.value = true;
    loginApi.haierIamTokenLogin({
        token: iamToken
    }).then(it => {
        loginSuccess({ data: it })
    }).finally(() => {
        loading.value = false;
    })
})()
</script>

<template></template>
