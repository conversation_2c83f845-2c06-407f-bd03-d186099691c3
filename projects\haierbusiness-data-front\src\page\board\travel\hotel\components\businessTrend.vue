<template>
  <div :style="{ height: height + 'vh', position: 'relative' }" background="rgba(0,0,0,0)" v-loading="loading">
    <el-select
      v-if="props.dateType == 1 && !router.currentRoute.value.fullPath.includes('/data/board/travel/index')"
      v-model="panel"
      class="bigscreen"
      placeholder="选择同期对比"
      filterable
      remote
      multiple
      collapse-tags
      clearable
      remote-show-suffix
      no-data-text="暂无数据"
      @change="pageChange"
    >
      <el-option v-for="item in pageList" :label="item.label" :value="item.value" />
    </el-select>
    <bar-line
      :from="props.from ? props.from : ''"
      :height="height"
      v-if="loaded"
      :legend="legend"
      :x-axis="xAxis"
      :y-axis="yAxis"
      :series="series"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import BarLine from '../../../components/barLine.vue';
import { queryHotelTrend, querySynchronismHotelTrend } from '@haierbusiness-front/apis/src/data/board/travel';
import { EventBus } from '../../../eventBus';

import dayjs from 'dayjs';
import { numberToChinese } from '../../../../../utils/numberToChinese';
import { useBoardStore } from '@haierbusiness-front/utils/src/store/board';
import { errorModal, getCurrentRouter, routerParam } from '@haierbusiness-front/utils';
const router = getCurrentRouter();
const panel = ref([]);
const pageList = ref([] as Array<{ label: string; value: string }>);
const chartArr = ref([] as any);

const pageChange = (value: any) => {
  const store = useBoardStore();
  loading.value = true;
  const { date } = store;
  legend = ['销售金额', '间夜'];
  chartArr.value = [
    {
      name: '销售金额',
      type: 'bar',
      color: 'rgba(0,240,255,0.4)',
      itemStyle: {
        borderColor: '#00F0FF',
      },
      data: barData,
      selectedMode: 'single', //鼠标点击是否突出该区域
    },
    {
      name: '间夜',
      type: 'line',
      yAxisIndex: 1,
      color: 'rgba(255,215,0,1)',
      smooth: true,
      symbol: 'none',
      data: lineData,
    },
  ] as any;
  let requestLength = 0;

  if (panel.value.length > 0) {
    panel.value.forEach(async (item) => {
      let arr = date.map((each) => {
        return each.split('-')[0] - item + '-' + each.split('-')[1] + '-' + each.split('-')[2];
      });
      const functionColumns = getFunctionColumns();

      const res = await querySynchronismHotelTrend(
        {
          functionColumns,
        },
        paramsData.value && paramsData.value.data ? paramsData.value.data.name : null,
        paramsData.value && paramsData.value.from ? paramsData.value.from : null,
        arr,
      );

      chartArr.value.push({
        name: '同期' + numberToChinese(item) + '年销售金额',
        type: 'bar',

        data: res.rows.map((e) => e[2]),
        selectedMode: 'single', //鼠标点击是否突出该区域
      });
      legend.push('同期' + numberToChinese(item) + '年销售金额');
      chartArr.value.push({
        name: '同期' + numberToChinese(item) + '年间夜',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        symbol: 'none',
        data: res.rows.map((e) => e[1]),
      });
      legend.push('同期' + numberToChinese(item) + '年间夜');
      requestLength = requestLength + 1;
      if (requestLength == panel.value.length) {
        series.value = chartArr.value;
        loading.value = false;
      }
    });
  } else {
    series.value = chartArr.value;
    loading.value = false;
  }
};

onMounted(() => {
  let thisYear = dayjs().subtract(1, 'year').format('YYYY'); // 获取当前年份
  let startYear = 2019; // 起始年份
  for (let i = startYear; i <= thisYear; i++) {
    pageList.value.push({
      label: '同期' + numberToChinese(thisYear - i + 1) + '年',
      value: thisYear - i + 1,
    });
  }
});

const props = defineProps({
  dateType: Number,
  height: {
    type: Number,
    default: 30,
  },
  from: {
    type: String,
    default: '',
  },
});
const loaded = ref(false);
const loading = ref(false);
let legend = ['销售金额', '间夜'];
const xAxis = ref([]);
const yAxis = [
  {
    type: 'value',
    name: '万元',
    splitNumber: 5,
    axisLabel: {
      formatter(value) {
        return value / 10000;
      },
    },
  },
  {
    type: 'value',
    name: '间夜',
    splitNumber: 5,
  },
];
const series = ref([]);
onMounted(() => {
  queryData();
});
const paramsData: any = ref({});
EventBus.on((event, params) => {
  if (event == 'refresh') {
    if (!params) queryData();
    if (params && params.from != 'date') {
      paramsData.value = params;
      queryData();
    }
  }
});
const getFunctionColumns = () => {
  if (props.dateType == 0) {
    return [
      {
        alias: 'dd_ydsj_group',
        snippet: 'AGG_DATE_YEAR([dd_ydsj])',
      },
    ];
  }
  if (props.dateType == 1) {
    return [
      {
        alias: 'dd_ydsj_group',
        snippet: 'AGG_DATE_MONTH([dd_ydsj])',
      },
    ];
  }
  return [
    {
      alias: 'dd_ydsj_group',
      snippet: 'AGG_DATE_DAY([dd_ydsj])',
    },
  ];
};
let barData: any = [];
let lineData: any = [];
let xData: any = [];
const queryData = async () => {
  const functionColumns = getFunctionColumns();
  const data = await queryHotelTrend(
    {
      functionColumns,
    },
    paramsData.value && paramsData.value.data ? paramsData.value.data.name : null,
    paramsData.value && paramsData.value.from ? paramsData.value.from : null,
  );
  barData = [];
  lineData = [];
  xData = [];
  data.rows.forEach((item, index) => {
    xData.push(item[0]);
    barData.push(item[2] || 0);
    // barData.push((item[2]/10000).toFixed(0));
    lineData.push(item[1] || 0);
  });
  xAxis.value = xData;
  series.value = [
    {
      name: '销售金额',
      type: 'bar',
      color: 'rgba(0,240,255,0.4)',
      selectedMode: props.from ? '' : 'single', //鼠标点击是否突出该区域
      itemStyle: {
        borderColor: '#00F0FF',
      },
      data: barData,
    },
    {
      name: '间夜',
      type: 'line',
      yAxisIndex: 1,
      color: '#FFD700',
      smooth: true,
      symbol: 'none',
      data: lineData,
    },
  ] as any;
  loaded.value = true;
};
watch(
  () => props.dateType,
  () => {
    queryData();
    panel.value = [];
  },
);
</script>

<style lang="less" scoped>
.bigscreen {
  position: absolute;
  top: -15.5%;
  right: 25%;
  z-index: 999;
  width: 180px;
}

@media screen and (max-width: 1500px) {
  .bigscreen {
    position: absolute;
    top: -16.5%;
    right: 25%;
    z-index: 999;
    width: 180px;
  }
}
</style>
