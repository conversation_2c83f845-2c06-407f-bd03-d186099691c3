<template>
    <div>
        <div background="rgba(0,0,0,0)" :id="id" :style="{ height: props.height + 'vh' }"></div>
    </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import * as echarts from "echarts";
import { queryPricePercentage } from "@haierbusiness-front/apis/src/data/board/travel";
import { circle2 as cicleOptions, colors } from "../../../data";
import { EventBus } from "../../../eventBus";
const props = defineProps({
    height: {
        type: Number,
        default: 25,
    },
});
const id = ref("cicle-" + Date.now());
const loading = ref(false);
let chartDom, myChart;
const payTypeCheck = ref<string>("");
onMounted(() => {
    queryData();
    chartDom = document.getElementById(id.value);
    myChart = echarts.init(chartDom as any, "dark");

    myChart.on("click", (param) => {
        if (
            param.from != "price_distribution" &&
            param.name != payTypeCheck.value
        ) {
            payTypeCheck.value = param.name;
            EventBus.emit("refresh", {
                ...param,
                from: "price_distribution",
            });
        } else {
            payTypeCheck.value = "";
            EventBus.emit("refresh");
        }
    });
});
EventBus.on((event, params) => {
    if (event == "refresh") {
        if (!params) queryData();
        if (params && params.from != "price_distribution") queryData(params);

        if (params && params.from == "price_distribution") {
            queryData().then(() => {
                myChart.dispatchAction({
                    type: "highlight",
                    seriesIndex: 0,
                    dataIndex: params.dataIndex,
                });
            });
        }
    }
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
    loading.value = true;
    const data = await queryPricePercentage(
        params ? params.data.name : null,
        params ? params.from : null
    );
    loading.value = false;
    const rows: any = [];
    data.rows.forEach((item, index) => {
        rows.push({
            value: item[1],
            name: item[0],
        });
    });

    const { series } = cicleOptions;
    series[0].color = colors;
    series[0].data = rows;
    myChart.clear();
    myChart.setOption({
        ...cicleOptions,
        tooltip: { trigger: "item", formatter: "{b} {c} 间夜" },
    });
};
</script>
<style scoped lang="less"></style>
