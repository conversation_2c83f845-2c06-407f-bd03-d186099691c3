{"name": "haierbusiness-data-front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --open --mode development", "build-dev": "vite build --mode development", "build-test": "vite build --mode test", "build": "vite build --mode production", "preview": "vite preview", "generate": "plop"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/l7": "^2.17.11", "@antv/l7-maps": "^2.17.11", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/theme-one-dark": "^6.1.2", "@haierbusiness-front/apis": "workspace:^", "@haierbusiness-front/common-libs": "workspace:^", "@haierbusiness-front/components": "workspace:^", "@haierbusiness-front/composables": "workspace:^", "@haierbusiness-front/utils": "workspace:^", "@types/qs": "^6.9.7", "@vueuse/core": "^9.13.0", "ant-design-vue": "4.x", "axios": "^1.4.0", "dayjs": "1.11.9", "easy-circular-progress": "^1.0.4", "echarts": "^5.4.3", "element-plus": "^2.3.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "moment": "^2.29.4", "pinia": "^2.1.6", "qs": "^6.11.2", "vue": "^3.3.4", "vue-codemirror": "^6.1.1", "vue-grid-layout": "3.0.0-beta1", "vue-request": "2.0.0-rc.4", "vue-router": "^4.2.4", "vue-seamless-scroll": "^1.1.23", "vue3-seamless-scroll": "^2.0.1"}, "devDependencies": {"@types/lodash": "^4.14.197", "@types/node": "^18.17.11", "@types/nprogress": "^0.2.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vitejs/plugin-vue": "^4.3.3", "less": "^4.2.0", "nprogress": "^0.2.0", "plop": "^3.1.1", "typescript": "^4.9.5", "vite": "^4.4.9", "vue-tsc": "^1.8.8"}}