<script setup>
  import { ref } from 'vue';
  import TourImg from '@/assets/image/home/<USER>';
  import SiteImg from '@/assets/image/home/<USER>';
  import GiftImg from '@/assets/image/home/<USER>';
  import HotelImg from '@/assets/image/home/<USER>';
  import ProgramImg from '@/assets/image/home/<USER>';

  const rowTopData = ref([
    {
      id: '1',
      icon: TourImg,
      name: '产品推荐',
    },
    {
      id: '2',
      icon: SiteImg,
      name: '场地搭建',
    },
  ])

  const rowBottomData = ref([
    {
      id: '3',
      icon: GiftImg,
      name: '礼品采购',
    },
    {
      id: '4',
      icon: HotelImg,
      name: '直签酒店',
    },
    {
      id: '5',
      icon: ProgramImg,
      name: '会中服务程序',
    },
  ])
</script>

<template>
  <div class="service">
    <div class="service-top">
      <div v-for="item of rowTopData" :key="item.id" class="service-top-item">
        <img :src="item.icon" />
        <div class="info">
          <span class="name">{{ item.name }}</span>
          <span class="arrow" />
        </div>
      </div>
    </div>
    <div class="service-bottom">
      <div v-for="item of rowBottomData" :key="item.id" class="service-bottom-item">
        <img :src="item.icon" />
        <div class="info">
          <span class="name">{{ item.name }}</span>
          <span class="arrow" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .service {
    .service-top, .service-bottom {
      display: flex;
      justify-content: space-between;
    }
    .service-bottom {
      margin-top: 28px;
    }
    .service-top-item, .service-bottom-item {
      cursor: pointer;
      position: relative;
      width: 688px;
      height: 298px;
      &:hover {
        .info {
          .arrow {
            background-position: left 0px bottom 0px;
          }
        }
      }
      >img {
        width: 100%;
        height: 100%;
      }
      .info {
        position: absolute;
        right: 19px;
        bottom: 17px;
        display: flex;
        align-items: center;
        .name {
          font-weight: 500;
          font-size: 20px;
          color: #FFFFFF;
          line-height: 28px;
          margin-right: 11px;
        }
        .arrow {
          display: block;
          width: 26px;
          height: 26px;
          background-image: url(@/assets/image/home/<USER>
          background-size: 52px 52px;
          background-repeat: no-repeat;
          position: relative;
          flex-shrink: 0;
          background-position: right 0px top 0px;
          transition: 0.3s ease-out;
        }
      }
    }
    .service-bottom-item {
      width: 450px;
      height: 248px;
    }
  }
</style>
