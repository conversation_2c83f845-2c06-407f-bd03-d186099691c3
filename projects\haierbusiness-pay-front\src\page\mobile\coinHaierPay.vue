<script setup lang="ts">
import {onMounted, ref, watch} from 'vue'
import {
  Cell,
  CellGroup,
  Icon,
  NumberKeyboard,
  PasswordInput,
  Popup,
  Radio,
  RadioGroup,
  showFailToast,
  showSuccessToast
} from 'vant';
import 'vant/es/toast/style'
import 'vant/es/cell/style'
import 'vant/es/cell-group/style'
import 'vant/es/radio-group/style'
import 'vant/es/radio/style'
import 'vant/es/icon/style'
import 'vant/es/password-input/style'
import 'vant/es/number-keyboard/style'
import {IPayData} from '@haierbusiness-front/common-libs/src/pay/model/basicModel';
import {coinHaierPayApi} from '@haierbusiness-front/apis';
import wallet from '@/assets/image/coinHaier/wallet.png';
import {isMobile} from "@haierbusiness-front/utils";
import {PaySourceConstant} from "@haierbusiness-front/common-libs";


const code = ref()
const showKeyboard = ref()
const countDown = ref(60)
const sending = ref(false)

/**
 * 账户
 */
const accountCode = ref();

/**
 * 余额
 */
const balance = ref();

/**
 * 手机号
 */
const phone = ref();

interface Props {
  checked: string
  show: boolean
  param: IPayData
}

const props = withDefaults(defineProps<Props>(), {
  checked: '',
  show: false
})

const checked = ref(props.checked || '')
const show = ref(props.show || false)

watch(props, (newValue) => {
  checked.value = props.checked || ''
  show.value = props.show || false

  // 显示的时候判断状态，是否发送短信
  if (show.value && countDown.value === 60) {
    send()
  }
})

const emit = defineEmits(["setChecked", "setAccount", "payComplete", "setShow"]);

const startCountdown = () => {
  let interval = window.setInterval(function () {
    if (countDown.value-- <= 0) {
      countDown.value = 60;
      sending.value = false;
      window.clearInterval(interval);
    }
  }, 1000);
}

const send = () => {
  sending.value = true;
  countDown.value = countDown.value - 1;
  sendSms(
      () => {
        startCountdown();
      }
  )
}

const sendSms = (success: () => void) => {
  coinHaierPayApi.sendCaptcha(
      {
        orderCode: props.param?.orderCode,
        applicationCode: props.param?.applicationCode,
        payTypes: props.param?.payTypes,
        username: props.param?.username,
        providerCode: props.param?.providerCode,
        amount: Number(props.param?.amount),
        orderDetailsUrl: props.param?.orderDetailsUrl,
        notifyUrl: props.param?.notifyUrl,
        callbackUrl: props.param?.callbackUrl,
        description: props.param?.description,
        payload: props.param?.payload,
        paySource: isMobile() ? PaySourceConstant.MOBILE.type : PaySourceConstant.PC.type,
      },
      {
        applicationCode: props.param?.applicationCode,
        excludes: "paySource",
        nonce: props.param?.hbNonce,
        timestamp: props.param?.hbTimestamp,
        sign: props.param?.sign,
      }
  ).then(it => {
    success()
  })
}

const searchAccount = () => {
  // 加载钱包账户基础数据
  coinHaierPayApi.searchAccount(
  ).then(it => {
    if (it) {
      accountCode.value = it.id;
      phone.value = it.phone;
      balance.value = it.balance;
      emit('setAccount', it.id)
    }
  })
}

watch(code, (newValue) => {
  if (code.value && code.value.length === 6) {
    confirmPay()
  }
})

const confirmPay = () => {
  let regexp = new RegExp(/^\d{6}$/);
  if (!regexp.test(code.value)) {
    showFailToast("验证码格式错误!");
    code.value = ''
    return;
  }
  coinHaierPayApi.pay(
      {
        orderCode: props.param?.orderCode,
        captcha: code.value,
        paymentMethod: 2
      }
  ).then(it => {
    showSuccessToast('支付成功')
    emit('setShow', false)
    emit('payComplete', true)
  }).finally(() => {
    setTimeout(() => {
      code.value = ''
    }, 500);
  })
}

onMounted(async () => {
  await searchAccount()
})

</script>

<template>
<div class="composition-con">
  <radio-group v-model="checked">
    <cell-group inset>
      <cell :border="false" clickable @click="$emit('setChecked', '4')">
        <template #title>
          <div class="wallet">
            <div class="type-con">
              <img :width="20" :height="20" :src="wallet" />
              <div class="pay-title">超市福利积分支付</div>
            </div>
            <div v-if="accountCode" class="pay-title balance">余额:{{ balance }}元</div>
          </div>
        </template>
        <template #right-icon>
          <radio name="4" />
        </template>
      </cell>
      <popup
          v-model:show="show"
          position="bottom"
          round
          @closed="$emit('setShow', false)"
          :style="{ height: 550 + 'px' }"
      >
        <div class="send-code-title">
          <div class="close">
            <icon name="cross" size="22px" @click="show = false" />
          </div>
          <div class="title">请输入验证码</div>
          <div class="space"></div>
        </div>
        <div class="send-code-money">
          <div class="">福利积分账号</div>
          <div class="money">
            <span class="rmb">No.</span>
            <span class="balance">{{ accountCode }}</span>
          </div>
        </div>
        <div class="line"></div>
        <div class="phone">
          <div class="phone-desc">
            短信已发送至{{ phone }}
          </div>
        
        </div>
        <div class="code-container">
          <password-input
              class="code"
              :value="code"
              :gutter="'10px'"
              :mask="false"
              :focused="showKeyboard"
              @focus="showKeyboard = true"
          />
          <number-keyboard
              :style="{ height: 250 + 'px' }"
              v-model="code"
              :show="showKeyboard"
              @blur="showKeyboard = false"
          />
        </div>
        <div class="count-down-container">
          <div class="count-down">
            <div class="send-code-btn" v-if="!sending" @click="send">
              重新发送
            </div>
            <div class="send-code-btn" v-else>
              {{countDown}}秒后可重新发送
            </div>
          </div>
        </div>
      </popup>
    </cell-group>
  </radio-group>
</div>
</template>

<style scoped lang="less">
.composition-con {
  padding-top: 10px;
}

.wallet {
  display: flex;
  flex-direction: column;

  .balance {
    padding-left: 25px;
    font-size: 12px;
    color: #969799;
  }
}

.type-con {
  display: flex;
  flex-direction: row;
  align-items: center;


  .pay-title {
    display: flex;
    padding-left: 5px;
  }

  .no-img {
    display: flex;
    padding-left: 25px;
    // font-size: 14px;
  }


}


.send-code-title {
  display: flex;
  width: 100%;
  padding: 16px 16px;
  flex-direction: row;
  justify-content: space-between;

  .close {
    display: flex;
  }

  .title {
    display: flex;
    font-size: 14px;
    font-weight: bold;
  }

  .space {
    width: 22px;
    height: 22px;
  }
}

.send-code-money {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .money {
    display: flex;
    font-size: 28px;
    padding-top: 5px;
    font-weight: bold;
    flex-direction: row;
    align-items: baseline;
    line-height: 28px;

    .rmb {
      font-size: 18px;
    }

    .balance {
      padding-left: 5px;
    }
  }

}

.line {
  padding-top: 10px;
  margin: 0 16px;
  border-bottom: 1px solid #ebedf0;
}

.phone {
  display: flex;
  width: 100%;
  padding-top: 15px;
  color: #6479ff;
  justify-content: center;

  .phone-desc {
    display: flex;
    width: calc(80%);
    flex-direction: row;
  }
}

.code-container {
  display: flex;
  width: 100%;
  padding-top: 15px;
  justify-content: center;

  .code {
    width: 80%;
  }

}

.count-down-container {
  display: flex;
  width: 100%;
  padding-top: 15px;
  color: #6479ff;
  justify-content: center;

  .count-down {
    display: flex;
    width: calc(80%);
    flex-direction: row-reverse;
  }
}
</style>