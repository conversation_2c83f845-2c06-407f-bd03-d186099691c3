// BillFileTypeConstant

type keys =
  | 'DEMAND_SUBMIT_NORMAL'
  | 'DEMAND_SUBMIT_REJECTED'
  | 'DEMAND_SUBMIT_APPROVAL_REJECTED'
  | 'DEMAND_RECEIVE_NORMAL'
  | 'DEMAND_PRE_INTERACT_NORMAL'
  | 'DEMAND_CONFIRM_NORMAL_REJECTED'
  | 'DEMAND_PUSH_CANCEL'
  | 'DEMAND_CONFIRM_NORMAL'
  | 'DEMAND_APPROVAL_NORMAL'
  | 'DEMAND_PUSH_NORMAL'
  | 'DEMAND_RE_APPROVAL_NORMAL_REJECTED'
  | 'SCHEME_SUBMIT_AGAIN_PUSH'
  | 'DEMAND_RE_APPROVAL_NORMAL'
  | 'SCHEME_SUBMIT_NORMAL'
  | 'SCHEME_SUBMIT_LOCK_RELEASE_HOUR'
  | 'SCHEME_OPEN_INTERACT_HOUR'
  | 'SCHEME_UPPER_LIMIT'
  | 'SCHEME_APPROVAL_NORMAL'
  | 'SCHEME_RE_APPROVAL_NORMAL_REJECTED'
  | 'SCHEME_RE_APPROVAL_NORMAL'
  | 'SCHEME_CONFIRM_NORMAL'
  | 'BID_PUSH_NORMAL'
  | 'BIDDING_NORMAL'
  | 'BID_RESULT_CONFIRM_NORMAL'
  | 'COST_APPROVAL_NORMAL'
  | 'MICE_EXECUTION_NORMAL'
  | 'MICE_COMPLETED_NORMAL'
  | 'BILL_CONFIRM_NORMAL'
  | 'BILL_APPROVAL_NORMAL'
  | 'BILL_RE_APPROVAL_NORMAL'
  | 'PAYMENT_CONFIRM_NORMAL'
  | 'PLATFORM_INVOICE_ENTRY_NORMAL'
  | 'VENDOR_INVOICE_ENTRY_NORMAL'
  | 'INVOICE_CONFIRM_NORMAL'
  | 'PLATFORM_RECEIPT_UPLOAD_NORMAL'
  | 'PLATFORM_INVOICE_CONFIRM_NORMAL'
  | 'SETTLEMENT_PENDING_NORMAL'
  | 'SETTLEMENT_RECORDED_NORMAL'
  | 'SETTLEMENT_COMPLETED_NORMAL'
  | 'REPEAL' | 'BID_RESULT_PASS_IN' | 'MICE_PENDING_NORMAL';

export const OrderListConstant = {
  // 需求提报相关
  DEMAND_SUBMIT_NORMAL: { code: 100, desc: '需求提报', color: '#1868DB' },
  DEMAND_SUBMIT_REJECTED: { code: 110, desc: '需求接收驳回', color: '#1868DB' },
  DEMAND_SUBMIT_APPROVAL_REJECTED: { code: 120, desc: '需求审批驳回', color: '#1868DB' },

  // 需求接收和交互
  DEMAND_RECEIVE_NORMAL: { code: 200, desc: '需求接单', color: '#1868DB' },
  DEMAND_PRE_INTERACT_NORMAL: { code: 300, desc: '需求事先交互', color: '#1868DB' },
  DEMAND_CONFIRM_NORMAL_REJECTED: { code: 310, desc: '用户需求确认驳回', color: '#1868DB' },
  DEMAND_PUSH_CANCEL: { code: 320, desc: '需求发布取消', color: '#1868DB' },

  // 需求确认
  DEMAND_CONFIRM_NORMAL: { code: 400, desc: '需求确认', color: '#1868DB' },

  // 需求审批和发布
  DEMAND_APPROVAL_NORMAL: { code: 500, desc: '需求审批', color: '#1868DB' },
  DEMAND_PUSH_NORMAL: { code: 600, desc: '需求发布', color: '#1868DB' },
  DEMAND_RE_APPROVAL_NORMAL_REJECTED: { code: 620, desc: '需求发布审批驳回', color: '#1868DB' },
  SCHEME_SUBMIT_AGAIN_PUSH: { code: 630, desc: '方案提报撤回重新发布', color: '#1868DB' },

  // 需求再审批
  DEMAND_RE_APPROVAL_NORMAL: { code: 700, desc: '需求发布复核', color: '#1868DB' },

  // 方案相关
  SCHEME_SUBMIT_NORMAL: { code: 800, desc: '方案提报', color: '#FAAD14' },
  SCHEME_SUBMIT_LOCK_RELEASE_HOUR: { code: 810, desc: '酒店锁定释放小时数', color: '#FAAD14' },
  SCHEME_OPEN_INTERACT_HOUR: { code: 820, desc: '方案互动开始时间配置', color: '#FAAD14' },
  SCHEME_UPPER_LIMIT: { code: 830, desc: '各供应商可提报方案数量上限', color: '#FAAD14' },

  // 方案审批
  SCHEME_APPROVAL_NORMAL: { code: 900, desc: '方案审核', color: '#FAAD14' },
  SCHEME_RE_APPROVAL_NORMAL_REJECTED: { code: 910, desc: '方案复审驳回', color: '#FAAD14' },

  // 方案复审和确认
  SCHEME_RE_APPROVAL_NORMAL: { code: 1000, desc: '方案复审', color: '#FAAD14' },
  SCHEME_CONFIRM_NORMAL: { code: 1100, desc: '方案确认', color: '#FAAD14' },

  // 竞价相关
  BID_PUSH_NORMAL: { code: 1200, desc: '竞价推送', color: '#4E00CC' },
  BIDDING_NORMAL: { code: 1300, desc: '竞价中', color: '#4E00CC' },
  BID_RESULT_CONFIRM_NORMAL: { code: 1400, desc: '费用支付', color: '#4E00CC' },
  BID_RESULT_PASS_IN: { code: 1410, desc: '竞价流标', color: '#4E00CC' },

  // 费用和执行
  COST_APPROVAL_NORMAL: { code: 1500, desc: '费用审批', color: '#4E00CC' },
  MICE_PENDING_NORMAL: { code: 1550, desc: '会议待执行', color: '#4E00CC' },
  MICE_EXECUTION_NORMAL: { code: 1600, desc: '会议执行中', color: '#FF6A00' },
  MICE_COMPLETED_NORMAL: { code: 1700, desc: '会议完成', color: '#FF6A00' },

  // 账单相关
  BILL_CONFIRM_NORMAL: { code: 1800, desc: '账单确认', color: '#FF6A00' },
  BILL_APPROVAL_NORMAL: { code: 1900, desc: '账单审批', color: '#FF6A00' },
  BILL_RE_APPROVAL_NORMAL: { code: 2000, desc: '账单复审', color: '#FF6A00' },

  // 支付和发票
  PAYMENT_CONFIRM_NORMAL: { code: 2100, desc: '财务收款确认', color: '#52C41A' },
  PLATFORM_INVOICE_ENTRY_NORMAL: { code: 2200, desc: '平台收款发票录入', color: '#52C41A' },
  VENDOR_INVOICE_ENTRY_NORMAL: { code: 2300, desc: '服务商发票录入', color: '#52C41A' },
  INVOICE_CONFIRM_NORMAL: { code: 2400, desc: '发票确认', color: '#52C41A' },
  PLATFORM_RECEIPT_UPLOAD_NORMAL: { code: 2500, desc: '上传平台收款凭证', color: '#52C41A' },
  PLATFORM_INVOICE_CONFIRM_NORMAL: { code: 2600, desc: '平台发票确认', color: '#52C41A' },

  // 结算相关
  SETTLEMENT_PENDING_NORMAL: { code: 2700, desc: '结算中（已推送）已推送账单/最终节点', color: '#52C41A' },
  SETTLEMENT_RECORDED_NORMAL: { code: 2800, desc: '结算中（已记账）', color: '#52C41A' },
  SETTLEMENT_COMPLETED_NORMAL: { code: 2900, desc: '已结算', color: '#52C41A' },
  REPEAL: { code: 2910, desc: '已作废', color: '#52C41A' },

  // 保持原有的工具方法
  ofType: (type?: number): { code: number; desc: string; color?: string } | null => {
    for (const key in OrderListConstant) {
      const item = OrderListConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return {
      code: 0,
      desc: '',
      color: '',
    };
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(OrderListConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return OrderListConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
