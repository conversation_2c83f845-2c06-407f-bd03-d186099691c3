// 关联账单项类型定义
export interface RelatedBillItem {
  id: string;
  sequenceNumber: number;
  date: string;
  project: string;
  category: string;
  contractPrice: number;
  contractQuantity: number;
  billPrice: number;
  billQuantity: number;
  relatedBill: string;
}

// 组件属性类型定义
export interface RelatedBillDialogProps {
  visible: boolean;
  billType: 'invoice' | 'waterBill'; // 发票或水单
  billData?: any; // 当前账单数据
}

// 组件事件类型定义
export interface RelatedBillDialogEmits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', data: RelatedBillItem[]): void;
}

// 筛选表单类型定义
export interface FilterForm {
  hotelPlan: string;
  project: string;
  planDate: string;
}

// 项目选项类型定义
export interface ProjectOption {
  value: string;
  label: string;
}

// 分页配置类型定义
export interface PaginationConfig {
  current: number;
  pageSize: number;
  total: number;
  showSizeChanger: boolean;
  showQuickJumper: boolean;
  showTotal: (total: number, range: number[]) => string;
}
