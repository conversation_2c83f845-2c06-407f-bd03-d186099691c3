import { onMounted, reactive, ref} from "vue"
import type { Ref } from "vue"
import type { PaginationProps } from 'ant-design-vue'
import type { MergeStatusType, OrderSearchable } from "@haierbusiness-front/common-libs"
import { removeEmptyFiled } from '@haierbusiness-front/utils'

export const useOrderSearch = <T, K extends {}>(api : OrderSearchable<T>, searchKey: K ) => {

    const pagination = reactive<PaginationProps>({
        current: 1,
        total: 0,
        pageSize: 10
    })

    const data = <Ref<Array<T>>>ref([])
    const from = ref()
    const loading = ref(false)
    const mergeStatus = ref<Array<MergeStatusType>>()

    const onFilterChange = () => {
        pagination.current = 1
        fetchData()
    }

    const fetchData = () => {
        loading.value = true
        removeEmptyFiled(searchKey)
        api.list({
            ...searchKey,
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
        }).then(res => {
            data.value = res.data
            pagination.total = res.total
            if (res.mergeStatus) {
                mergeStatus.value = res.mergeStatus
            }
        }).finally(() => {
            loading.value = false;
        })
    }

    // onMounted(fetchData)

    const onPageChange = (currentPagination: PaginationProps) => {
        pagination.pageSize = currentPagination.pageSize
        pagination.current = currentPagination.current
        fetchData()
    }

    const handleReset = () => {
        from.value && from.value.resetFields()
        fetchData()
    }

    const onTimeChange = (dateRange: string[]) => {
        const times:string[] = []
        times.push(dateRange[0] + ' 00:00:00')
        times.push(dateRange[1] + ' 23:59:59')
        return times
        // searchKey.createTime = times
    }

    return {
        data,
        pagination,
        loading,
        fetchData,
        onPageChange,
        from,
        handleReset,
        onTimeChange,
        onFilterChange,
        mergeStatus
    };
}