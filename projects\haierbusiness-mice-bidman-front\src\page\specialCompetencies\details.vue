<script lang="ts" setup>
import {
  Button as hButton,
  Card as hCard,
  Row as hRow,
  Col as hCol,
} from 'ant-design-vue';
import { computed, onMounted, ref, watch,inject } from "vue";
import type { Ref } from "vue";
import {
  ISpecialCompetencies
} from '@haierbusiness-front/common-libs';
import { specialCompetenciesApi } from '@haierbusiness-front/apis';
import { useRoute, useRouter } from 'vue-router';
import { resolveParam, routerParam } from '@haierbusiness-front/utils';
const route = useRoute();
const router = useRouter();
const record = resolveParam(route.query.record as string);
console.log('record', record);
const hideBtn = record?.hideBtn || '';
const localUrl = window.location.href;
// 获取并设置frameModel
const frameModel = inject<any>('frameModel');
if (frameModel) {
    frameModel.value = hideBtn === '1' ? 1 : 0;
}
const id = computed(() => record?.id || (route.query.id as string));
const confirmLoading = ref(false);

const specialCompetencies = ref<ISpecialCompetencies>({})

// 获取详情数据
const getDetail = async () => {
  if (!id.value) return;

  confirmLoading.value = true;
  try {
    const res = await specialCompetenciesApi.details(id.value);
    specialCompetencies.value = res;
  } catch (error) {
    console.error('获取详情失败:', error);
  } finally {
    confirmLoading.value = false;
  }
};
// 获取状态文本
const getStateText = (state: any) => {
  if (state) {
    state = state == 10 ? '审批中' : state === 20 ? '审批完成' : '审批驳回';
  }
  return state
};

onMounted(() => {
  getDetail()
})

</script>

<template>
  <div class="container">
    <h-card :loading="confirmLoading">
    <div class="info-section">
      <h3><span></span>特殊权限信息</h3>
      <h-row :gutter="[16, 16]">
        <h-col :span="6">
          <div class="info-item">
            <span class="label">订单号：</span>
            <span class="value">{{ specialCompetencies.mainCode }}</span>
          </div>
        </h-col>
        <h-col :span="6">
          <div class="info-item">
            <span class="label">会议名称：</span>
            <span class="value">{{ specialCompetencies.miceName }}</span>
          </div>
        </h-col>
        <h-col :span="6">
          <div class="info-item">
            <span class="label">会议特殊权限：</span>
            <span class="value">{{ specialCompetencies.type }}</span>
          </div>
        </h-col>
        <h-col :span="6">
          <div class="info-item">
            <span class="label">状态：</span>
            <span class="value">{{ getStateText(specialCompetencies.state) }}</span>
          </div>
        </h-col>
      </h-row>
    </div>
    <div class="info-section" v-if="specialCompetencies.handoverReason">
      <h-row :gutter="[16, 16]">
        <h-col :span="6">
          <div class="info-item">
            <span class="label">驳回原因：</span>
            <span class="value">{{ specialCompetencies.handoverReason }}</span>
          </div>
        </h-col>
      </h-row>
    </div>
  </h-card>
  <h-card :loading="confirmLoading">
    <div class="info-section">
      <h3><span></span>开通信息</h3>
      <h-row :gutter="[16, 16]">
        <h-col :span="6">
          <div class="info-item">
            <span class="label">经办人：</span>
            <span class="value">{{ specialCompetencies.operatorName }}</span>
          </div>
        </h-col>
        <h-col :span="6">
          <div class="info-item">
            <span class="label">开通人：</span>
            <span class="value">{{ specialCompetencies.openName }}</span>
          </div>
        </h-col>
        <h-col :span="6">
          <div class="info-item">
            <span class="label">授权时间：</span>
            <span class="value">{{ specialCompetencies.imPowerTime }}</span>
          </div>
        </h-col>
        <h-col :span="6">
          <div class="info-item">
            <span class="label">申请时间：</span>
            <span class="value">{{ specialCompetencies.gmtCreate }}</span>
          </div>
        </h-col>
        <h-col :span="6">
          <div class="info-item">
            <span class="label">生效时间：</span>
            <span class="value">{{ specialCompetencies.onTime }}</span>
          </div>
        </h-col>
      </h-row>
    </div>
  </h-card>
  </div>
  
</template>


<style lang="less" scoped>
.container{
  height: 100%;
  padding: 15px;
  background-color: #fff;
}
.important {
  color: red;
}

:deep(.ant-form-item-required::before) {
  display: none !important;
}
h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;

  span {
    display: inline-block;
    width: 4px;
    height: 20px;
    margin-right: 3px;
    background: #1868DB;
  }
}

.ant-card-bordered {
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0px 3px 12px 0px rgba(1, 12, 51, 0.07);
}
.info-section {
  margin-bottom: 24px;

  h3 {
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 500;
  }
}

.info-item {
  display: flex;
  align-items: flex-start;

  .label {
    color: rgba(0, 0, 0, 0.55);
    // min-width: 130px;
  }

  .value {
    color: #000;
    flex: 1;
    word-break: break-all;
  }
}
</style>