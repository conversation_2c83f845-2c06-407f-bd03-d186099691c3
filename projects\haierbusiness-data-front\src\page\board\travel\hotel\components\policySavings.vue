<template>
  <div :style="{ height: height + 'vh' }" background="rgba(0,0,0,0)">
    <bar-line
      :from="'supplier_name'"
      :height="height"
      v-if="loaded"
      :legend="legend"
      :x-axis="xAxis"
      :y-axis="yAxis"
      :series="series"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import BarLine from "../../../components/barLine.vue";
import { queryPolicySavings } from "@haierbusiness-front/apis/src/data/board/travel";
import { EventBus } from "../../../eventBus";
const props = defineProps({
  dateType: Number,
  height: {
    type: Number,
    default: 30,
  },
  from: {
    type: String,
    default: "",
  },
});
const loaded = ref(false);
const loading = ref(false);
const legend = ["节省金额"];
const xAxis = ref([]);
const yAxis = [
  {
    type: "value",
    name: "万元",
    splitNumber: 5,
    axisLabel: {
      formatter(value) {
        return value / 10000;
      },
    },
  },
];
const series = ref([]);
onMounted(() => {
  queryData();
});
const paramsData: any = ref({});
EventBus.on((event, params) => {
  if (event == "refresh") {
    if (!params) queryData();
    if (params && params.from != "supplier_name") {
      paramsData.value = params;
      queryData();
    }
  }
});
const queryData = async () => {
  loading.value = true;
  const data = await queryPolicySavings(
    paramsData.value && paramsData.value.data ? paramsData.value.data.name : null,
    paramsData.value && paramsData.value.from ? paramsData.value.from : null
  );
  loading.value = false;
  const barData: any = [];
  const lineData: any = [];
  const xData: any = [];
  data.rows.forEach((item, index) => {
    xData.push(item[0]);
    barData.push(item[1] || 0);
    // barData.push((item[2]/10000).toFixed(0));
  });
  xAxis.value = xData;
  series.value = [
    {
      name: "节省金额",
      type: "bar",
      color: "rgba(0,240,255,0.4)",
      selectedMode: props.from ? "" : "single", //鼠标点击是否突出该区域
      itemStyle: {
        borderColor: "#00F0FF",
      },
      data: barData,
    },
  ] as any;
  loaded.value = true;
};
watch(() => props.dateType, queryData);
</script>
