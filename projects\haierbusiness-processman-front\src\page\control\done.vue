<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { DownOutlined, ExclamationCircleOutlined, PlusOutlined, UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { processApi } from '@haierbusiness-front/apis';
import {
  IProcessRecordListRequest,
  PayTypeConstant,
  IUserInfo,
  IUserListRequest
} from '@haierbusiness-front/common-libs';
import { errorModal, routerParam } from '@haierbusiness-front/utils';
import Eloading from '@haierbusiness-front/components/loading/ELoading.vue';
import dayjs, { Dayjs } from 'dayjs';
import { computed, onMounted, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useRouter } from "vue-router";
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue'

const router = useRouter()
const columns: ColumnType[] = [
  {
    title: '审批记录Code',
    dataIndex: 'recordCode',
    fixed: 'left',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '业务单号',
    dataIndex: 'businessCode',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '审批人记录ID',
    dataIndex: 'currentOperatorId',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '待办状态',
    dataIndex: 'resultState',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '标题',
    dataIndex: 'title',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '描述',
    dataIndex: 'description',
    width: '2000px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '所属人工号',
    dataIndex: 'username',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '申请人工号',
    dataIndex: 'applicantUser',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'approveState',
    width: '150px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '审批完成时间',
    dataIndex: 'completeTime',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '企业编码',
    dataIndex: 'enterpriseCode',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '企业',
    dataIndex: 'enterpriseName',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '发起审批应用',
    dataIndex: 'applicationCode',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改人',
    dataIndex: 'lastModifiedBy',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改时间',
    dataIndex: 'gmtModified',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '180px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<IProcessRecordListRequest>({ approveState: 2 })
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(processApi.list, {
  defaultParams: [
    {
        approveState: 2
    }
  ],
  manual: false
})

const reset = () => {
  searchParam.value = { approveState: 2 }
}

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
  /* stateAccountApiRun(
    searchParam.value
  )
  cvpErrorAccountApiRun(
    searchParam.value
  )
  pushCvpErrorAccountApiRun(
    searchParam.value
  ) */
};

// 用户选择
const params = ref<IUserListRequest>({
    pageNum: 1,
    pageSize: 20
})

const name = ref('')

const userNameChange = (userInfo: IUserInfo) => {
    searchParam.value.applicantUser = userInfo?.username ?? ''
    name.value = userInfo?.nickName ?? ''
}

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">

    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="searchAccountCode">审批记录Code：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="searchAccountCode" v-model:value="searchParam.code" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="applicantUser">申请人：</label>
          </h-col>
          <h-col :span="4">
              <user-select :value="name" placeholder="审批人" :params="params" @change="(userInfo: IUserInfo) => userNameChange(userInfo)" style="width: 100%;" />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'payType'">
              {{ PayTypeConstant.ofType(record.payType)?.name }}
            </template>
            <template v-if="column.dataIndex === '_operator'">
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
