<script setup lang="ts">
import {
  Modal,
  Tag as hTag,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  Row as hRow,
  Card as hCard,
  Table as hTable,
  FormItem as hFormItem,
  Textarea as hTextarea,
  DatePicker as hDatePicker,
  CollapsePanel as h<PERSON>ollapsePanel,
  Collapse as hCollapse,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import {
  IAnnualPlanSaveOrUpdateRequestDTO,
  AnnualPlanTypeStateConstant,
  IAnnualPlanListRequestDTO,
  IAnnualPlanListResponseDTO,
  IAnnualPlanTypeListResponse,
  IHaierAccountBillInfo,
} from '@haierbusiness-front/common-libs';
import { getCurrentRouter, resolveParam } from '@haierbusiness-front/utils';
import { ref, createVNode, PropType, computed } from 'vue';

import dayjs, { Dayjs } from 'dayjs';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import { IAnnualPlanTypeListRequest } from '@haierbusiness-front/common-libs/src/daily';
import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
import updateYearTarget from './updateYearTarget.vue';

const { loginUser } = storeToRefs(applicationStore(globalPinia));
const collapseActiveKey = ref([1]);

const prop = defineProps({
  formParam: Object as PropType<IAnnualPlanSaveOrUpdateRequestDTO>,
  type: String as PropType<String>,
});
const isDisabled = ()=>{
  return prop?.type === 'adjust' || prop?.type === 'summarize'|| prop?.type === 'evaluate'|| prop?.type === 'planform-evaluate'
}
</script>

<template>
  <h-row :align="'middle'" style="margin: 5px 24px">
    <h-col :span="24" style="text-align: left">
      <h-collapse
        v-model:activeKey="collapseActiveKey"
        style="background-color: rgb(250, 250, 250)"
        :collapsible="'icon'"
      >
        <h-collapse-panel key="1">
          <template #header>
            <div style="font-size: 15px; font-weight: 500; margin-left: 10px">年度目标&定位</div>
          </template>
          <h-row>
            <h-col :span="21" offset="2" style="text-align: left">
              <h-form-item
                name="target"
                label="年度目标"
                :rules="{
                  required: true,
                  message: '请输入年度目标!',
                }"
              >
                <h-textarea :rules="{ required: true }" v-model:value="formParam!!.target" :rows="3" allow-clear :disabled="isDisabled()"/>
              </h-form-item>
            </h-col>
          </h-row>
          <h-row>
            <h-col :span="21" offset="2" style="text-align: left">
              <h-form-item
                name="orientation"
                label="年度定位"
                :rules="{
                  required: true,
                  message: '请输入年度定位!',
                }"
              >
                <h-textarea :rules="{ required: true }" v-model:value="formParam!!.orientation" :rows="3" allow-clear :disabled="isDisabled()"/>
              </h-form-item>
            </h-col>
          </h-row>
        </h-collapse-panel>
      </h-collapse>
    </h-col>
  </h-row>
</template>

<style scoped lang="less">
@import '../../../assets/css/main.less';
</style>
