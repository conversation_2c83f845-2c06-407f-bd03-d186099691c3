import { download, get, post, filepost, originalGet } from '../request'

export const waterworkWarehouseApi = {
    list: (params: any): Promise<void> => {
        return get('waterworks/api/tstore/page', params)
    },
    listAll: (params: any): Promise<void> => {
        return get('waterworks/api/tstore/list', params)
    },
    add: (params: any): Promise<void> => {
        return post('waterworks/api/tstore/save', params)
    },
    update: (params: object): Promise<void> => {
        return post(`waterworks/api/tstore/update`, params);
    },
    export: (params: any): Promise<void> => {
        return download('waterworks/api/tstore/export', params)
    },
    delete: (ids: string): Promise<void> => {
        return post(`waterworks/api/tstore/delete/${ids}`);
    },
}