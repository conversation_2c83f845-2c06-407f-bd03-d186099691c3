<!-- ListTable.vue -->
<template>
  <h-table
    v-bind="tableProps"
    :loading="loading"
    size="middle"
    @change="handleTableChange($event as any)"
  >
    <template v-for="(_, name) in $slots" #[name]="slotProps">
      <slot :name="name" v-bind="slotProps" />
    </template>
  </h-table>
</template>

<script setup lang="ts">
import { Table as hTable } from 'ant-design-vue'
import { computed, ref, onMounted, nextTick } from 'vue'
import { usePagination } from 'vue-request'

interface Props {
  api: any
  columns?: any[]
  searchParam?: any
}

const props = withDefaults(defineProps<Props>(), {
  api: null,
  columns: () => [],
  searchParam: () => {
    return {}
  },
})

const selectedItems = ref([])
const selection = ref([])

const { data, run: apiListRun, loading, current, pageSize } = usePagination(props.api)

const tableProps = computed(() => ({
  rowKey: 'id',
  scroll: { x: 'max-content' },
  dataSource: data.value?.records || [],
  pagination: {
    showSizeChanger: true,
    showQuickJumper: true,
    total: data.value?.total,
    current: data.value?.pageNum,
    pageSize: data.value?.pageSize,
    style: { justifyContent: 'center' },
  },
  columns: props.columns,
  // 支持多选
  rowSelection: {
    selectedRowKeys: selectedItems.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      // console.log(selectedRowKeys, selectedRows)
      selectedItems.value = selectedRowKeys
      selection.value = selectedRows
      // console.log(selectedItems.value)
    },
  },
}))

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any
) => {
  apiListRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
    ...props.searchParam,    
  })
}

const getList = async () => {
  selectedItems.value = []
  selection.value = []
  await apiListRun({
    pageNum: current,
    pageSize: pageSize,
    ...props.searchParam,
  })
}

const resetPageNum = async () => {
  current.value = 1
  getList()
}

onMounted(async () => {
  getList()
})

defineExpose({
  selection,
  getList,
  resetPageNum,
})
</script>

<style scoped></style>
