// 话务中心用  请勿随意修改
import { defineStore,createPinia  } from "pinia"
const password = import.meta.env.VITE_PASSWORD

type SocketState = {
    ws: WebSocket | undefined,
    health: boolean
    heartTimeOut: number, 
    lockReconnect: boolean, //避免重连
    timerReconnect: any,
    timerHeart: any,
    timerServerHeart: any,
    handClose: boolean,
    msg: string,
    url: string,
    seatNumber:string,
    phoneNumber:string,
    isBusyStatus:boolean,
}

export const useSocketStore = defineStore("socket", {
    state: (): SocketState => ({
        ws: undefined,
        health: false, // 健康检查，首次启动必须加载
        heartTimeOut: 30000, //监测心跳时间 10秒
        lockReconnect: false, //避免重连
        timerReconnect: undefined,
        timerHeart: undefined,
        timerServerHeart: undefined,
        handClose: false,
        msg: '',
        url: '',
        seatNumber:"",
        phoneNumber:"",//当前接入的电话号码
        isBusyStatus:false,
    }),
    actions: {
        healthCheck(url: string) {
            var newWindow = window.open("about:blank")
            newWindow && (newWindow.location.href = url)
        },

        connection(url: string,func?:any,phoneNumber?:string){
            if("WebSocket" in window){
                this.url = url
                this.createWebSocket(url,func,phoneNumber)
            }
            else{
                console.log("您的浏览器不支持websocket通信")
            }    
        },
        //初始化
        createWebSocket(url:string,func?:any,phoneNumber?:string) {
            try{
                if(this.ws?.readyState!=1){
                    this.ws = new WebSocket(url)  // 
                    this.initWebsocket(func,phoneNumber)
                }
            }
            catch(e){
                console.log("catch eeeee=", e)
                this.reConnection()
            }
        },

        initWebsocket(func:any,phoneNumber?:string) {
            if(this.ws) {
                //建立连接
                this.ws.onopen = (e)=>{
                    //webSocket业务订阅——可以有多个业务
                    // this.ws!.send("hello server");
                    console.log("连接成功")
                    // 是否连接成功
                    this.lockReconnect = true
                    // 当前连接的号码
                    if(phoneNumber){
                        func(phoneNumber)
                        this.phoneNumber = phoneNumber
                    }else{
                        this.ws && this.ws.send(JSON.stringify(
                            {
                                msgType: 'operation',
                                subType: 'onlineagent/syncagentinfo',
                                version: 'V1',
                                sequenceNo: this.getRandomCode(32),
                                msgContent: {
                                  password: password,
                                },
                              }
                        ))
                    }
                    // this.ws && this.ws.send(`{"msgType":"operation","subType":"onlineagent/syncagentinfo","version":"V1","sequenceNo":"ft352lcg4n40000000","msgContent":{"password":'2wsx@WSX'}}`);
                    //连接成功后，需要开启监测心跳
                    this.heartCheck()
                }

                this.ws.onmessage = (messages)=>{
                    console.log(messages.data)
                    let msg = messages.data
                    if(msg.includes("{")){
                        msg = JSON.parse(msg)
                    }
                    this.msg = msg

                }
                this.ws.onerror = (e)=>{ //连不上时onerror 和 onclose 监听同时会捕捉到
                    console.log("连接失败-------------------------------------------")
                    this.lockReconnect = false
                    if(this.timerHeart){
                        clearTimeout(this.timerHeart)
                    }
                    // 连失败需要重连
                    this.reConnection()
                }
            
                this.ws.onclose = (e)=>{
                    console.log(e,"ws关闭------------")
                    // this.lockReconnect = false
                    if(this.timerHeart){
                        clearTimeout(this.timerHeart)
                    }
                    //是否正常关闭  正常关闭不需要重连， 否则需要重连
                    if(e.reason!='normal_close'){
                        this.lockReconnect = false
                        this.isBusyStatus = false
                        this.reConnection()
                    }else{
                        this.lockReconnect = false
                        this.isBusyStatus = false
                    }
                }
            }
            
        },

        clearTimer(){
            this.timerReconnect && clearTimeout(this.timerReconnect)
            this.timerHeart && clearTimeout(this.timerHeart)
        },

        //重连
        reConnection() {
            console.log("重新连接")
            if(this.lockReconnect){
                return
            }
            this.lockReconnect = true
            if(this.timerReconnect) {
                clearTimeout(this.timerReconnect)
            } 
        
            //没连上会一直重连， 设置迟延，避免请求过多
            this.timerReconnect = setTimeout(() => { //setTimeout 到点了执行
                this.connection(this.ws?.url ?? this.url)
                this.lockReconnect = false
            }, 5000);
        },

        //心跳
        heartCheck(){
            console.log("监测心跳")
            if(this.timerHeart){
                clearTimeout(this.timerHeart)
            }
            this.timerHeart = setTimeout(() => {
                this.ws && this.ws.send('{"msgType":"heartbeat","subType":"heartbeat","version":"V1","sequenceNo":"0000","msgContent":{}}');
            }, this.heartTimeOut); //30秒
        },
     
        //发送消息
        sendMsg(data: any){
            console.log("发送消息",data)
            if(this.ws && this.ws.readyState === WebSocket.OPEN){
                this.ws.send(JSON.stringify(data))
            }
        },
     
        //关闭连接 手动关闭
        closeWs(){
            console.log("手动关闭ws")
            this.handClose = true
            this.clearTimer()
            this.ws && this.ws.close()
        },

        // 序列码
        getRandomCode(length: number){
            return Number(Math.random().toString().substr(3, length) + Date.now()).toString(36);
        }
     
    },
    persist:{
        storage:sessionStorage//自定义存储位
    }
})





