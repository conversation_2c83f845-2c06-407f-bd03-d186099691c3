import {
    RtoPageParams,
    RtoPageRes
} from '@haierbusiness-front/common-libs'
import { download, get, post,downloadPost } from '../request'

export const InvoicingApi = {
    
    /**
     * 开票信息 
     */
    list: (params: RtoPageParams): Promise<RtoPageRes> => {
        return post('incentive/api/rechargeBill/page', params)
    },
    /**
     * 完善开票信息 
     */
    saveOrUpdate: (params: RtoPageParams): Promise<RtoPageRes> => {
        return post('incentive/api/rechargeBill/saveOrUpdate', params)
    },
    /**
     * 替换方案报告
     */
    fileUpdate: (params: RtoPageParams): Promise<RtoPageRes> => {
            return post('incentive/api//rechargeBill/fileUpdate', params)
        },
    /**
     * 获取开票详情 
     */
    info: (params: RtoPageParams): Promise<RtoPageRes> => {
        return get('incentive/api/rechargeBill/get', params)
    },
    /**
     * 开票信息导出
     */
    exportRechargeBill: (params: RtoPageParams): Promise<void> => {
        return downloadPost('incentive/api/rechargeBill/export', params)
    },
    /**
     * 开票信息导入
     */
    InvoicingImport: (params: FormData): Promise<any> => {
        return post('/incentive/api/rechargeBill/import', params);
      },
    /**
     * 开票信息导入模版
     */
    downloadTemplate: (params: RtoPageParams): Promise<void> => {
        return downloadPost('incentive/api/rechargeBill/downloadTemplate', params)
    },

    

}