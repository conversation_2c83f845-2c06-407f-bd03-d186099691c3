import { downloadPost, get, post } from '../request'
import { 
    BPolicyReq,
    BPolicyRes,
    BPolicyListReq,
    BPolicyListRes,
    IPageResponse,
    Result,
    MiceResult
 } from '@haierbusiness-front/common-libs'

// 新增政策信息
export const banquetPolicyApi = {
    list: (params: BPolicyListReq): Promise<IPageResponse<BPolicyListRes>> => {
        return post('banquet/api/admin/policies/page', params)
    },
    get: (id: number): Promise<BPolicyReq> => {
        return get('banquet/api/admin/policies/get', {
            id
        })
    },
    saveOrUpdate: (params: BPolicyReq): Promise<BPolicyRes> => {
        return post('banquet/api/admin/policies/saveOrUpdate', params)
    },
    exportList: (params: BPolicyListReq): Promise<IPageResponse<BPolicyListRes>> => {
        return downloadPost('banquet/api/admin/policies/export', params)
    },
}
