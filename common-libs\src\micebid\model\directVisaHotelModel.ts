import { IPageRequest } from "../../basic";

export class IDirectVisaHotelFilter extends IPageRequest {
    begin?:string
    end?:string
}


export class IDirectVisaHotel {
    id?: number | null
    //询价单code
    code?:string
    //酒店code(中台)
    platformHotelCode?:string
    //酒店名称(中台)
    platformHotelName?:string
    //酒店地址(中台)
    platformHotelAddress?:string
    //酒店星级(中台)
    platformHotelStar?:number
    //酒店区域(中台)
    platformHotelArea?:string
    creator?:string
    createTime?: string
    updater?: string
    updateTime?: string
}