<template>
  <div
    style="
      background-color: #ffff;
      height: 100%;
      width: 100%;
      padding: 10px 10px 0px 10px;
      overflow: auto;
    "
  >
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="employeeId">用户账号：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="employeeId"
              v-model:value="searchKey.employeeId"
              placeholder=""
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="employeeName">用户姓名：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="employeeName"
              v-model:value="searchKey.employeeName"
              placeholder=""
              autocomplete="off"
              allow-clear
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="permissionStatus">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchKey.permissionStatus" placeholder="状态">
              <h-select-option value="VALID">正常</h-select-option>
              <h-select-option value="INVALID">停用</h-select-option>
            </h-select>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="gmtCreate">创建日期：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker
              v-model:value="searchKey.gmtCreate"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </h-col>
        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="accountCompanyCode">结算单位：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              placeholder="请选择结算单位"
              v-model:value="searchKey.accountCompanyCode"
              show-search
              :filter-option="filterOption"
              @search="handleSearch"
              :options="settleCompany"
              style="width: 100%"
              :field-names="{ label: 'name', value: 'code' }"
            >
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="budgetDepartmentCode">预算部门：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              placeholder="请选择预算部门"
              v-model:value="searchKey.budgetDepartmentCode"
              show-search
              :filter-option="filterOption"
              @search="handleBudgetSearch"
              :options="settleDepartment"
              style="width: 100%"
              :field-names="{ label: 'name', value: 'code' }"
            >
            </h-select>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="fieldCode">领域：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              placeholder="请选择领域"
              v-model:value="searchKey.fieldCode"
              show-search
              :filter-option="filterOption"
              @search="handleAreaSearch"
              :options="areaList"
              style="width: 100%"
              :field-names="{ label: 'name', value: 'code' }"
            >
            </h-select>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="ptCode">平台：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              placeholder="请选择平台"
              v-model:value="searchKey.ptCode"
              show-search
              :filter-option="filterOption"
              @search="handlePtSearch"
              :options="platformList"
              style="width: 100%"
              :field-names="{ label: 'name', value: 'code' }"
            >
            </h-select>
          </h-col>

          
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px"> 
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="plCode">产业线：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              placeholder="请选择产业线"
              v-model:value="searchKey.plCode"
              show-search
              :filter-option="filterOption"
              @search="handlePlSearch"
              :options="industryList"
              style="width: 100%"
              :field-names="{ label: 'name', value: 'code' }"
            >
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button style="margin-right: 10px" @click="handleReset">重置</h-button>
            <h-button style="margin-right: 10px" type="primary" @click="onFilterChange">
              <SearchOutlined />
              查询
            </h-button>
            <!-- <h-button
              type="primary"
              style="margin-right: 10px"
              v-if="!pagination.disabled"
              :loading="downloading"
              @click="download"
            >
              <UploadOutlined />
              导出
            </h-button> -->
          </h-col>
        </h-row>

        <!-- <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: left;">
            <h-button type="primary" @click="handleCreate">
              <PlusOutlined /> 新增员工
            </h-button>
          </h-col>
        </h-row> -->
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="(columns as any)"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="data"
          :pagination="pagination"
          :scroll="{ y: 550, x: 2000 }"
          :loading="loading"
          @change="onPageChange"
        >
          <template #bodyCell="{ column, index, text, record }">
            <template v-if="column.dataIndex === 'businessType'">
              {{
                text == "travel"
                  ? "商旅"
                  : text == "mealService"
                  ? "餐务"
                  : text == "meetingAffairs"
                  ? "会务"
                  : ""
              }}
              <a v-if="!text" @click="showModal(record)">指标</a>
              <!-- {{ businessType.find((item) => item.key == text).text }} -->
            </template>
            <template v-else-if="column.dataIndex === 'moduleType'">
              {{ text == 1 ? "报表" : text == 2 ? "看板" : text == 3 ? "分析报告" : text == 4?'指标':"" }}
            </template>
            <template v-else-if="column.dataIndex === 'permissionValidTimeStart'">
              {{ dayjs(text).format("YYYY-MM-DD ") }}
            </template>
            <template v-else-if="column.dataIndex === 'permissionValidTimeEnd'">
              {{ dayjs(text).format("YYYY-MM-DD ") }}
            </template>
            <template v-else-if="column.dataIndex === 'gmtCreate'">
              {{ dayjs(text).format("YYYY-MM-DD ") }}
            </template>
            <template v-else-if="column.dataIndex === 'accountCompanyName'">
              <div v-if="text && JSON.parse(text).length > 0">
                <div v-for="(item, i) in JSON.parse(text)" :key="i">
                  <div v-if="i < 6">{{ item }}</div>
                </div>
                <div v-if="JSON.parse(text).length > 6">...</div>
              </div>
              <div v-else>-</div>
            </template>
            <template v-else-if="column.dataIndex === 'budgetDepartmentName'">
              <div v-if="text && JSON.parse(text).length > 0">
                <div v-for="(item, i) in JSON.parse(text)" :key="i">
                  <div v-if="i < 6">{{ item }}</div>
                </div>
                <div v-if="JSON.parse(text).length > 6">...</div>
              </div>
              <div v-else>-</div>
            </template>
            <template v-else-if="column.dataIndex === 'areaName'">
              <div v-if="text && JSON.parse(text).length > 0">
                <div v-for="(item, i) in JSON.parse(text)" :key="i">
                  <div v-if="i < 6">{{ item }}</div>
                </div>
                <div v-if="JSON.parse(text).length > 6">...</div>
              </div>
              <div v-else>-</div>
            </template>

            <template v-else-if="column.dataIndex === 'fieldName'">
              <div v-if="text && JSON.parse(text).length > 0">
                <div v-for="(item, i) in JSON.parse(text)" :key="i">
                  <div v-if="i < 6">{{ item }}</div>
                </div>
                <div v-if="JSON.parse(text).length > 6">...</div>
              </div>
              <div v-else>-</div>
            </template>

            <template v-else-if="column.dataIndex === 'ptName'">
              <div v-if="text && JSON.parse(text).length > 0">
                <div v-for="(item, i) in JSON.parse(text)" :key="i">
                  <div v-if="i < 6">{{ item }}</div>
                </div>
                <div v-if="JSON.parse(text).length > 6">...</div>
              </div>
              <div v-else>-</div>
            </template>

            <template v-else-if="column.dataIndex === 'plName'">
              <div v-if="text && JSON.parse(text).length > 0">
                <div v-for="(item, i) in JSON.parse(text)" :key="i">
                  <div v-if="i < 6">{{ item }}</div>
                </div>
                <div v-if="JSON.parse(text).length > 6">...</div>
              </div>
              <div v-else>-</div>
            </template>
            <template v-else-if="column.dataIndex === 'approveStatus'">
              {{ getApproveStatus(text) }}
            </template>

            <template v-else-if="column.dataIndex === 'operation'">
              <h-button type="link" @click="goDetail(record.processCode, record.id)"
                >查看详情</h-button
              >
            </template>
          </template>

          <template #emptyText v-if="pagination.disabled">
            <div>暂无权限，<a @click="goApplyDetail">去申请</a></div>
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>
  <h-modal
    v-model:visible="modalShow"
    title="申请指标"
    :width="1200"
    :footer="null"
  >
    <h-card>
      <div class="reportBox" style="padding: 8px;height:70vh;overflow:auto;">
        <div v-for="(item, index) in reportList" :key="item.id" class="indexItemBox">
            <ReportItem :reportInfo="item"></ReportItem>
        </div>
      </div>
    </h-card>
  </h-modal>
  <!-- <div v-if="visible">
  <edit-dialog
        :show="visible"
        :data="editData"
        @cancel="onDialogClose"
        @ok="handleOk"
    >
    </edit-dialog> 
  </div> -->
</template>

<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  Button as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
} from 'ant-design-vue';
import { reactive, ref, computed, onMounted } from "vue";
import { PlusOutlined, SearchOutlined, UploadOutlined } from '@ant-design/icons-vue';
import dayjs from "dayjs";
import { checkUserGroups } from "@haierbusiness-front/utils/src/authorityUtil";
import ReportItem from '../../smartBrain/components/reportItem.vue';
import { useSearch } from "../../../composables/useSearch";
import {
  ReportFilter,
  ReportType,
  ApplyCompanyType,
} from "@haierbusiness-front/common-libs";
import { reportApi, permissionApi } from "@haierbusiness-front/apis";
import { getCurrentRouter, routerParam } from "@haierbusiness-front/utils";
const router = getCurrentRouter();

const columns = [
  { title: "申请单号", dataIndex: "id", width: 200 },
  { title: "用户账号", dataIndex: "employeeId", width: 150 },
  { title: "用户姓名", dataIndex: "employeeName", width: 150 },
  { title: "数据类型", dataIndex: "moduleType", width: 150 },
  { title: "结算单位", dataIndex: "accountCompanyName", width: 300 },
  { title: "预算部门", dataIndex: "budgetDepartmentName", width: 300 },
  { title: "领域", dataIndex: "fieldName", width: 300 },
  { title: "平台", dataIndex: "ptName", width: 300 },
  { title: "产业", dataIndex: "plName", width: 300 },

  { title: "业务类型", dataIndex: "businessType", width: 150 },
  { title: "创建时间", dataIndex: "gmtCreate", width: 150 },
  { title: "创建时间", dataIndex: "createTime", width: 150 },
  // { title: "业务数据开始时间", dataIndex: "businessDataTimeStart" },
  // { title: "业务数据结束时间", dataIndex: "businessDataTimeEnd" },
  { title: "权限有效开始时间", dataIndex: "permissionValidTimeStart", width: 150 },
  { title: "权限有效结束时间", dataIndex: "permissionValidTimeEnd", width: 150 },
  { title: "申请原因", dataIndex: "approveReason", width: 150 },
  { title: "审核状态", dataIndex: "approveStatus", width: 150 },
  { title: "审批意见", dataIndex: "approveRemark" },
  { title: "操作", dataIndex: "operation", fixed: "right", width: 100 },
];
// 获取退款状态
const getApproveStatus = (status: number | string) => {
  const resultMap: any = {
    0: "取消",
    10: "审批中",
    20: "审批通过",
    30: "审批驳回",
    40: "审批撤回",
    default: "",
  };
  return resultMap[status] || resultMap.default;
};
const reportList = ref([])
const modalShow = ref<boolean>(false)
const searchKey = reactive<any>({
  budgetDepartmentCode: null,
  accountCompanyCode: null,
  employeeId: null,
  employeeName: null,
  businessType: null,
  permissionStatus: null,
  gmtCreate: [] as string[],
});
const goApplyDetail = () => {
  router.push("/data/report/permission/apply");
};
const handleReset = () => {
  Object.keys(searchKey).forEach((key) => {
    delete searchKey[key];
  });
  onFilterChange();
};

const {
  data,
  pagination,
  loading,
  onPageChange,
  downloading,
  download,
  onFilterChange,
} = useSearch<ReportType, ReportFilter>(permissionApi, searchKey);

const goDetail = (processCode: string | number, id: string) => {
  console.log(import.meta.env.VITE_JUMP_URL + "hbweb/process/?code=" + processCode + "#/details","点击详情点击详情点击详情点击详情")
  location.href =
    import.meta.env.VITE_JUMP_URL + "hbweb/process/?code=" + processCode + "#/details";
  // router.push("/data/report/permission/detail?id=" + id);
};
const settleCompany = ref([] as Array<ApplyCompanyType>);

const filterOption = (input: string, option: any) => {
  return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const settleDepartment = ref([]);


// 查询领域、平台、产业线
const areaList= ref([]);
const platformList= ref([]);
const industryList= ref([]);

// 根据类型查询不同权限类型 3:领域 4:平台 5:产业线
const getPowerByApprove = async (name: string, permissionType: number) => {
  const data = await reportApi.getPowerAllByType(name,permissionType);
  switch (permissionType) {
    case 1:
      settleDepartment.value = data;
      break;
    case 2:
      settleCompany.value = data;
      break;
    case 3:
      areaList.value = data;
      break;
    case 4:
      platformList.value = data;
      break;
    case 5:
      industryList.value = data;
      break;
    default:
      break;
  }
};


const handleBudgetSearch = (val: string) => {
  getPowerByApprove(val,1);
};

const handleSearch = (val: string) => {
  getPowerByApprove(val,2);
};


const handleAreaSearch = (val: string) => {
  getPowerByApprove(val,3);
};

const handlePtSearch = (val: string) => {
  getPowerByApprove(val,4);
};

const handlePlSearch = (val: string) => {
  getPowerByApprove(val,5);
};




const showModal=(row:any)=>{
  reportList.value = row.reportLabelCenterVos
  modalShow.value = true
}

onMounted(() => {
  getPowerByApprove("青岛", 1);
  getPowerByApprove("青岛", 2);
  getPowerByApprove("", 3);
  getPowerByApprove("", 4);
  getPowerByApprove("", 5);
});
</script>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
.reportBox{
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .indexItemBox{
    width: 49%;
  }
}
</style>
