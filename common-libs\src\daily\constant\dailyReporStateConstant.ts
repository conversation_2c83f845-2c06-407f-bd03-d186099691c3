type keys = "WAIT" | "RUNNING" | "FINISH";

/**
 * 日清主表状态
 * <p>
 * 10：待提交 （录入状态）
 * 20：平台评价 （提交后）
 * 30：已结束
 *
 * <AUTHOR>
 * @since 2023/10/31 14:06
 */
export const DailyReportStateConstant = {
    /**
     * 待提交 （录入状态）
     */
    WAIT: {"code": 10, "desc": "待提交"},

    /**
     * 平台评价（提交后）
     */
    RUNNING: {"code": 20, "desc": "平台评价"},

    /**
     * 已结束
     */
    FINISH: {"code": 30, "desc": "已结束"},


    ofCode: (code?: number): { "code": number, "desc": string } | null => {
        for (const key in DailyReportStateConstant) {
            const item = DailyReportStateConstant[key as keys];
            if (code === item.code) {
                return item;
            }
        }
        return null;
    }
}