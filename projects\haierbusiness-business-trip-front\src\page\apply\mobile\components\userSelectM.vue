<template>
  <div>
    <van-field
      required
      readonly
      is-link
      input-align="right"
      name="picker"
      :label="props.label"
      :placeholder="props.palceholder"
      @click="showMainPersonList = true"
      v-model="props.value"
    >
    
  </van-field>
    <!-- 业务申请人弹窗 -->
    <van-popup v-model:show="showMainPersonList" position="bottom">
      <div class="out-pop" style="height: 100vh">
        <van-sticky>
          <div style="background: #fff; padding-bottom: 10px">
            <van-nav-bar title="选择人员" left-arrow @click-left="closeMainPersonPop">
              <template #left>
                <van-icon name="arrow-left" color="#000" size="24" />
              </template>
              <template #right>
                <div class="color-main"></div>
              </template>
            </van-nav-bar>

            <van-cell-group inset style="margin: 10px ; background-color: #fff">
              <van-field
                @update:model-value="searchMainPerson"
                v-model="searchValue"
                style="background: #f6f7f9"
                placeholder="输入工号、姓名"
                
              />
            </van-cell-group>
          </div>
        </van-sticky>
        <van-list
          v-model:loading="mainPersonLoading"
          :finished="mainPersonFinished"
          :finished-text="mainPersonList.length ? '没有更多了' : ''"
          @load="onLoadMainPerson"
        >
          <div
            v-if="mainPersonList.length == 0 && mainPersonFinished"
            style="height: 66vh"
            class="flex align-items-center justify-content-center"
          >
            <img class="img_empty" src="../../../../assets/image/trip/empty.jpg" alt="" />
          </div>
          <template v-else>
            <van-cell v-for="(item, index) in mainPersonList" :key="index" @click="choseMainPerson(item)">
              <div class="flex align-items-center ">
                <div
                  :class="index % 3 == 0 ? 'blue' : 'yellow'"
                  class="mr-10 ml-10 flex align-items-center justify-content-center img-name"
                >
                  {{ item?.nickName ? item?.nickName.slice(-2) : '未知' }}
                </div>
                <div class="main">
                  <div class="user-name color-main">{{ item.nickName }}({{ item.username }})</div>
                  <div class="phone">{{ item.enterpriseName || '未知' }}</div>
                </div>
              </div>
            </van-cell>
          </template>
        </van-list>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import type { Ref } from 'vue';
import { createVNode, onMounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import { userApi } from '@haierbusiness-front/apis';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ITraveler, ICreatTrip } from '@haierbusiness-front/common-libs';
import { debounce, values } from 'lodash';

const store = applicationStore();

const { loginUser } = storeToRefs(store);

// props参数

interface Props {
  palceholder?: string; // 人员
  label?: string;
  value?: string;
}
const props = defineProps<Props>();

const emit = defineEmits(['chose']);



// 业务申请人相关
const searchValue = ref('');

const showMainPersonList = ref(false);
const mainPersonLoading = ref(false);
const mainPersonFinished = ref(false);
const mainPersonTotal = ref(0);
const mainPersonList = ref<Array<ITraveler>>([]);


const defaultParams: IUserListRequest = {
  pageNum: 0,
  pageSize: 20,
};

const params: Ref<IUserListRequest> = ref(defaultParams);
const closeMainPersonPop = () => {
  showMainPersonList.value = false;
};
const onLoadMainPerson = () => {
  params.value.pageNum++;
  userApi.list(params.value).then((res) => {
    // 加载状态结束
    mainPersonLoading.value = false;
    mainPersonList.value = [...mainPersonList.value, ...res.records];
    mainPersonTotal.value = res.total ?? 0;
    // 数据全部加载完成
    if (mainPersonList.value.length >= mainPersonTotal.value) {
      mainPersonFinished.value = true;
    }
  });
};

const searchMainPerson = debounce((val: string) => {
  params.value.keyWord = val;
  params.value.pageNum = 0;
  params.value.pageSize = 20;
  mainPersonFinished.value = false;
  mainPersonList.value = [];
  onLoadMainPerson();
});

const choseMainPerson = (item: ITraveler) => {

  showMainPersonList.value = false;

  emit('chose', item)
};
</script>

<style scoped lang='less'>
.flex {
  display: flex;
}
.flex-column {
  flex-direction: column;
}
.align-items-center {
  align-items: center;
}
.justify-content-center {
  justify-content: center;
}
.justify-content-between {
  justify-content: space-between;
}
.flex-warp {
  flex-wrap: wrap;
}

.mr-10 {
  margin-right: 10px !important;
}
.mr-20 {
  margin-right: 20px;
}
.color-main {
    color: #0073e5;
  }
.out-pop {
  .main {
    flex: 1;
  }
  .img-name {
    width: 40px;
    height: 40px;
    
    color: #fff;
    font-size: 10px;
    border-radius: 40px;
  }
  
  .blue {
    background-color: #0073e5;
  }
  .yellow {
    background-color: #faad14;
  }
  .add-person {
    margin: 16px;
    margin-bottom: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
    font-size: 12px;
  }
  .user-name {
    text-align: left;
    font-size: 16px;
  }
  .phone {
    text-align: left;
    color: #8c8c8c;
  }
}
</style>