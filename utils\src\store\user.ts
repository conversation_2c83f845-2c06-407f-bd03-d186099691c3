import { IUserInfo } from '@haierbusiness-front/common-libs';
import { defineStore } from 'pinia'
import { toRaw } from 'vue'

interface ISelectUserType extends IUserInfo {
    cacheKey?: string
}

type UserState = {
    selectUsers: Array<ISelectUserType>;
}

export const userStore = defineStore('user', {
    state: (): UserState => {
        return {
            selectUsers: []
        }
    },
    actions: {
        setSelectUsers(cacheKey: string, user: IUserInfo) {
            const selectUser = this.selectUsers.find(o => o.id === user.id)
            if(!selectUser) {
                const userInfo = {
                    ...user,
                    cacheKey,
                }
                
                this.selectUsers = [userInfo, ...this.selectUsers]
            }
        },
        getSelectUsersByCacheKey(cacheKey: string): Array<IUserInfo>{
            const users = this.selectUsers.filter(o => o.cacheKey === cacheKey)
            if(users && users.length > 0) {
                const newUsers = JSON.parse(JSON.stringify(users)) as Array<ISelectUserType>
                newUsers.map(item => {
                    delete item.cacheKey;
                })
                return newUsers
            }
                
            return []
        }
    }
})