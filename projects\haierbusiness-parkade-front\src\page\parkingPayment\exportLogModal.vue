<script lang="ts" setup>
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Modal as hModal,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
} from "ant-design-vue";
import { ColumnType } from "ant-design-vue/lib/table/interface";
import { Key } from "ant-design-vue/lib/vc-table/interface";
import {
  DownOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
} from "@ant-design/icons-vue";
import { payApi, parkadeApi } from "@haierbusiness-front/apis";
import {
  VirtualAccountTypeConstant,
  HaierBudgetSourceConstant,
  VirtualScopeConstanty,
  ParkadeRecordReq,
  ParkadeExportLogReq,
  PayStatusConstant,
  PayTypeConstant,
  VirtualAccountChangeTypeConstant,
} from "@haierbusiness-front/common-libs";
import { errorModal, routerParam } from "@haierbusiness-front/utils";
import Eloading from "@haierbusiness-front/components/loading/Eloading.vue";
import dayjs, { Dayjs } from "dayjs";
import { computed, onMounted, ref, watch } from "vue";
import { DataType, usePagination, useRequest } from "vue-request";
import { useRouter } from "vue-router";

const router = useRouter();


interface Props {
    type: string;
    businessId?: string;
}

const props = withDefaults(defineProps<Props>(), {
  type: '10',
  businessId: ''
});

const exportLogSearch = ref<ParkadeExportLogReq>({})

const { data:exportLogData , run: exportLogApiRun, loading: exportLogLoading, current: exportLogCurrent, pageSize: exportLogPageSize } = usePagination(
  parkadeApi.exportLogList
);

const exportLogPagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: exportLogData.value?.total,
  current: exportLogData.value?.pageNum,
  pageSize: exportLogData.value?.pageSize,
  style: { justifyContent: "center" },
}));

const resetExportLogSearch = () => {
  exportLogSearch.value = {};
};

const exportLogDataSource = computed(() => exportLogData.value?.records || []);

const handleExportLogChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any
) => {
  exportLogApiRun({
    ...exportLogSearch.value,
    type: props.type,
    businessId: props.businessId,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};




const visible = ref<boolean>(false);
watch(visible, (newValue) => {
    if(newValue) {
        exportLogApiRun({
            ...exportLogSearch.value,
            type: props.type,
            businessId: props.businessId,
            pageNum: 1,
            pageSize: 10,
        });

    }
});

const columns: ColumnType[] = [
  {
    title: "经办人工号",
    dataIndex: "userCode",
    fixed: "left",
    width: "240px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "经办人姓名",
    dataIndex: "userName",
    fixed: "left",
    width: "240px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "操作时间",
    dataIndex: "operationTime",
    width: "240px",
    align: "center",
    ellipsis: true,
  },
  {
    title: "文件名称",
    dataIndex: "fileName",
    width: "240px",
    align: "center",
    ellipsis: true,
  },

 
];
const show = () => {
  visible.value = true;
};
defineExpose({
  show,
});



</script>

<template>
  <h-modal v-model:open="visible" title="导出记录"  :footer="null" :width="1200" >
    <h-col :span="24" style="margin-bottom: 10px">
      <h-row :align="'middle'" style="padding: 10px 0;">
        <h-col :span="2" style="text-align: right; padding-right: 10px">
          <label for="orderCode">经办人工号:</label>
        </h-col>
        <h-col :span="3">
          <h-input
            id="orderCode"
            v-model:value="exportLogSearch.userCode"
            placeholder="经办人工号"
            autocomplete="off"
            allow-clear
          />
        </h-col>
        <h-col :span="2" style="text-align: right; padding-right: 10px">
          <label for="carSystemCode">经办人姓名:</label>
        </h-col>
        <h-col :span="3">
          <h-input
            id="carSystemCode"
            v-model:value="exportLogSearch.userName"
            placeholder="经办人姓名"
            autocomplete="off"
            allow-clear
          />
        </h-col>
        <h-col :span="3" style="text-align: right; padding-right: 10px">
          <label for="enterpriseName">开始导出时间:</label>
        </h-col>
        <h-col :span="4">
            <h-date-picker v-model:value="exportLogSearch.operationTimeStart" format="YYYY-MM-DD HH:mm:ss" :show-time="{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
        </h-col>

        <h-col :span="3" style="text-align: right; padding-right: 10px">
          <label for="enterpriseName">截止导出时间:</label>
        </h-col>
        <h-col :span="4">
          <h-date-picker v-model:value="exportLogSearch.operationTimeEnd" format="YYYY-MM-DD HH:mm:ss" :show-time="{ defaultValue: dayjs('23:59:59', 'HH:mm:ss') }" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
        </h-col>
      </h-row>
     
      <h-row :align="'middle'" >
        <h-col :span="24" style="text-align: right">
         
          
          <h-button style="margin-right: 10px" @click="resetExportLogSearch">重置</h-button>
          <h-button
            style="margin-right: 10px"
            type="primary"
            @click="handleExportLogChange({ current: 1, pageSize: 10 })"
          >
          
            <SearchOutlined />
            查询
          </h-button>
        
        </h-col>
      </h-row>
    </h-col>
    <h-col :span="24">
      <h-table
        :columns="columns"
        :row-key="(record) => record.id"
        :size="'small'"
        :data-source="exportLogDataSource"
        :pagination="exportLogPagination"
        :scroll="{ y: 400, x: 600 }"
        style="min-height: 400px;"
        :loading="exportLogLoading"
        @change="handleExportLogChange($event as any)"
      >
        <template #bodyCell="{ text, column, record }">
          <template v-if="column.dataIndex === 'source'">
            {{ text == 1 ? "福利积分" : text == 2 ? "代金券" : "附件凭证" }}
          </template>

          <template v-if="column.dataIndex === 'rechargeStatus'">
            <a-tag v-if="text == '1'" color="#87d068">充值成功</a-tag>
            <a-tag v-if="text == '0'" color="#2db7f5">充值失败</a-tag>
          </template>

          <template v-if="column.dataIndex === 'collectStatus'">
            {{ text == 10 ? '未汇总' : text == 20 ? '已汇总': '汇总失败' }}
          </template>
          <template v-if="column.dataIndex === 'type'">
            {{ VirtualAccountChangeTypeConstant.ofType(text)?.desc }}
          </template>

          

          <template v-if="column.dataIndex === 'accountUserId'">
            {{ `${record.accountUser}(${record.accountUserId})` }}
          </template>

         
        </template>
      </h-table>
    </h-col>
  </h-modal>
</template>



<style scoped lang="less"></style>