<script setup lang="ts">
import { Carousel as hCarousel, message } from 'ant-design-vue';
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
import { ref, computed, onMounted } from 'vue';
import { useRequest, usePagination } from 'vue-request';
import { providerAdvertisementApi } from '@haierbusiness-front/apis';
import router from '../../router'

const currentRouter = ref()
onMounted(async () => {
    currentRouter.value = await router
})

// 优选
const {
  data: providerData,
  run: providerListApiRun,
  loading: providerLoading,
} = usePagination(providerAdvertisementApi.query, {
  defaultParams: [
    {
      showStatus: 1
    }
  ],
  manual: false
});

const providerDataSource = computed(() => providerData.value?.records || []);

const isYouxuanHover = ref(false)

const setIsYouxuanHover = (value: boolean) => {
  isYouxuanHover.value = value
}

const gotoUrlOrDetail = (data: {jumpLinkPc?: string, id?: number}, url: string) => {
  if (data.jumpLinkPc) {
    window.open(data.jumpLinkPc)
  } else {
    if(!data.id) {
      message.error('未对应信息！')
      return
    }
    const thisUrl = currentRouter.value.resolve({
      path: url,
    })
    window.open(thisUrl.href + '?id=' + data.id)
  }
}

</script>

<template>
    <div class="youxuan">
        <div class="card-header">
            <div class="card-title">
            优选
            </div>
            <div class="card-more">

            </div>
        </div>
        <div class="youxuan-con">
            <h-carousel :autoplay="true" arrows >
            <template #prevArrow>
                <div :class="isYouxuanHover ? 'custom-slick-arrow' : ''" style="left: 10px; z-index: 1" @mousemove="setIsYouxuanHover(true)">
                <LeftOutlined />
                </div>
            </template>
            <template #nextArrow>
                <div :class="isYouxuanHover ? 'custom-slick-arrow' : ''" style="right: 10px" @mousemove="setIsYouxuanHover(true)">
                <RightOutlined />
                </div>
            </template>
            <div class="advertisement pointer" v-for="(item, index) in providerDataSource" @mousemove="setIsYouxuanHover(true)" @mouseleave="setIsYouxuanHover(false)" :key="index" @click="gotoUrlOrDetail(item, '/travel/adDetail')" >
                <img :src="item.imgUrl" class="img" >
            </div>
            </h-carousel>
        </div>
    </div>
</template>


<style scoped lang="less">
.pointer {
  cursor: pointer;
}

.card-header {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;

    .card-title {
      color: #000;
      font-size: 24px;
      font-weight: 600;
      line-height: 20px;
      display: flex;
    }

    .card-more {
      display: flex;
      color: #3983E5;
      font-size: 14px;
      font-weight: 400;
      line-height: 14px;
      cursor: pointer;
    
    }
}

.youxuan {
    height: 290px;
    width: 360px;

    .youxuan-con {
        margin-top: 16px;
        width: 360px;
        height: 247px;
        border-radius: 12px;

        .advertisement {
            position: relative;
            width: 360px;
            height: 247px;
            border-radius: 12px;

            .img {
                width: 100%;
                height: 100%;
                border-radius: 12px;
                padding-left: 1px;
            }

            .desc {
                position: absolute;
                width: 198px;
                height: 73px;
                left: 17px;
                bottom: 17px;
                background: rgba(0, 0, 0, 0.35);
                padding: 10px 12px 11px 12px;
                color: #FFF;

                .banner-title {
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 20px;
                }

                .banner-desc {
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 20px;
                    margin-top: 7px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                }

                .discounts {
                    font-size: 20px;
                }
            }
        }
    }
}

</style>

<style>
.youxuan-con .slick-arrow.custom-slick-arrow {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: #fff;
  background-color: rgba(31, 45, 61, 0.11);
  opacity: 0.8;
  z-index: 1;
}

.youxuan-con .custom-slick-arrow:before
{
  display: none;
}
</style>