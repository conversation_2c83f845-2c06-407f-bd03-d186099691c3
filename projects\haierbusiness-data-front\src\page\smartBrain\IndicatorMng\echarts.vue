<template>
  <div v-loading="loading" background="rgba(0,0,0,0)" id="container" :style="{ height: '100%', width: '100%' }"></div>
</template>
<script setup lang="ts">
import { onMounted, ref, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';
import { queryStarDistribution } from '@haierbusiness-front/apis/src/data/board';
import { circle2 as cicleOptions, colorsforSmart } from '../../../page/board/data';
import { EventBus } from '../../../page/board/eventBus';
import { smartBrainApi } from '@haierbusiness-front/apis';
import { useRouter, useRoute } from 'vue-router';
const route = useRoute();
const props = defineProps({
  height: {
    type: Number,
    default: 33,
  },
  echartsJson: {
    type: String,
    default: '',
  },
});
const payTypeCheck = ref<string>('');
// const id = ref("cicle-" + Date.now());
const loading = ref(false);
let chartDom: any, myChart: any;
onMounted(() => {
  chartDom = document.getElementById('container');
  myChart = echarts.init(chartDom as any, 'dark');
  window.addEventListener('resize', resize);
  getChrat(props.echartsJson);
});
const getChrat = (Json: string) => {
  if(isJSON(Json)){
    const echartsJson = JSON.parse(Json);
    echartsJson.series[0].color = colorsforSmart;
    echartsJson.legend.textStyle={ //图例文字的样式
      color: "#000"
    }
    if(echartsJson.yAxis){
      echartsJson.yAxis.forEach((item: any) => {
        if (item.name == '万元') {
          item.axisLabel = {
            formatter(value) {
              return value / 10000;
            },
            color:"#333941"
          };
        }else{
          item.axisLabel={
            color: "#333941"
          }
        }
      });
      echartsJson.xAxis.forEach((item:any)=>{
          item.axisLabel={
            color: "#333941",
            rotate: 30
          }
      })
    }else{
      echartsJson.label.fontSize = 12
      echartsJson.label.color = '#000'
    }
    echartsJson.toolbox={
      show: true,
      feature: {
        saveAsImage: {}
      }
    }
    if (!echartsJson.series[0].data) {
      echartsJson.series[0].data = [
          { value: 1048, name: 'Search Engine' },
          { value: 735, name: 'Direct' },
          { value: 580, name: 'Email' },
          { value: 484, name: 'Union Ads' },
          { value: 300, name: 'Video Ads' }
      ];
    }
    myChart.setOption({
      ...echartsJson,
    });
  }
  // else{
  //   var currentOptions = myChart.getOptions();
  //   myChart.clear()
  //   myChart.setOption(currentOptions);
  // }
};
const isJSON =(str:any)=>{
  try {
    JSON.parse(str);
  } catch (e) {
    // 转换出错，抛出异常
    return false;
  }
  return true;
}
watch(
  () => props.echartsJson,
  (newVal) => {
    if (newVal) {
      getChrat(newVal);
    }
  },
);
const resize = () => {
  myChart.resize();
};
</script>
<style scoped lang="less">
:deep(.el-loading-mask) {
  /* 设置背景颜色为半透明的白色 */
  background-color: rgba(255, 255, 255, 0.5);
}
#container{
  background: #fff;
}
</style>
