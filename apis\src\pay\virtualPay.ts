import {
    IPageResponse,
    IPayHeader,
    IPayResponse,
    IPaymentVirtualAccount,
    IPaymentVirtualAccountChange,
    IPaymentVirtualAccountTypeMap,
    IPaymentVirtualAccountUserMap,
    IQueryVirtualAccountsRequest,
    IQueryVirtualAccountsResponse,
    IVirtualAccountsListRequest,
    IVirtualChangeListRequest,
    IVirtualPayRequest,
    IVirtualRechargeRequest,
    VirtualAccountTypeConstant,
    IVirtualAuthorizedUserListRequest,
    IVirtualAccountUser,
    IConfigUserByApp,
    Result,
    ISendCodeCaptcha
} from '@haierbusiness-front/common-libs'
import { get, post, download } from '../request'

export const virtualPayApi = {

    /**
     * 查询可用公司支付账户
     */
    queryVirtualAccounts: (params: IQueryVirtualAccountsRequest): Promise<IQueryVirtualAccountsResponse[]> => {
        return get<IQueryVirtualAccountsResponse[]>('pay/api/virtual/accounts', params).then(
            data => {
                data?.forEach(
                    it => {
                        it._typeName = VirtualAccountTypeConstant.ofType(it.type)?.desc
                    }
                )
                return data
            }
        )
    },

    /**
     * 导出-充值记录
     */
     exportRechargeOrderList: (params: IQueryVirtualAccountsRequest): Promise<void> => {
        return download('pay/api/virtual/rechargeOrder/export', params)
    },

    /**
     * 确认支付
     */
    pay: (params: IVirtualPayRequest, header: IPayHeader): Promise<IPayResponse> => {
        return post('pay/api/virtual/pay', params,
            {
                "hb-nonce": header.nonce,
                "hb-timestamp": header.timestamp,
                "hb-sign": header.sign,
                "hb-excludes": header.excludes,
                "hb-application-code": header.applicationCode,
            })
    },

    /**
     * 查询支付账户
     */
    list: (params: IVirtualAccountsListRequest): Promise<IPageResponse<IPaymentVirtualAccount>> => {
        return get('pay/api/virtual/account/list', params)
    },
    getTradeUnionAccountPage: (params: IVirtualAccountsListRequest): Promise<IPageResponse<IPaymentVirtualAccount>> => {
        return get('pay/api/virtual/tradeUnionAccount/getTradeUnionAccountPage', params)
    },

    /**
    * 查询账户权限
    */
    accountTypes: (param: string): Promise<IPaymentVirtualAccountTypeMap[]> => {
        return get('pay/api/virtual/account/types', { accountNo: param })
    },

    /**
    * 查询账户用户
    */
    accountUsers: (param: string): Promise<IPaymentVirtualAccountUserMap[]> => {
        return get('pay/api/virtual/account/users', { accountNo: param })
    },


    /**
    * 重试
    */
    voucherRechargeRetry: (param: string): Promise<IPaymentVirtualAccountUserMap[]> => {
        return get('pay/api/virtual/rechargeOrder/voucherRechargeRetry', { code: param })
    },
    /**
       * 取消
       */
    voucherRechargeOrderCancel: (param: string): Promise<IPaymentVirtualAccountUserMap[]> => {
        return get('pay/api/virtual/rechargeOrder/voucherRechargeOrderCancel', { code: param })
    },

    /**
     * 查询支付变动
     */
    changeList: (params: IVirtualChangeListRequest): Promise<IPageResponse<IPaymentVirtualAccountChange>> => {
        return get('pay/api/virtual/account/change/list', params)
    },

    /**
     * 充值管理
     */
    rechargeOrderList: (params: IVirtualChangeListRequest): Promise<IPageResponse<IPaymentVirtualAccountChange>> => {
        return get('pay/api/virtual/rechargeOrder/list', params)
    },

    /**
     * 查询账户已授权用户列表-按用户分组
     */
    authorizedUserList: (params: IVirtualAuthorizedUserListRequest): Promise<IPageResponse<IVirtualAccountUser>> => {
        return get('pay/api/virtual/account/users-group-user', params)
    },

    /**
     * 查询账户已授权用户列表-按用户分组
     */
    authorizedAppUserList: (params: IVirtualAuthorizedUserListRequest): Promise<IPageResponse<IVirtualAccountUser>> => {
        return get('pay/api/virtual/account/users-flat', params)
    },

    configUserByApp: (params: IConfigUserByApp): Promise<Result> => {
        return post('pay/api/virtual/account/config-user-by-app', params)
    },

    /**
     * 支付充值
     */
    recharge: (params: IVirtualRechargeRequest): Promise<void> => {
        return get('pay/api/virtual/account/recharge', params)
    },

    save: (params: IPaymentVirtualAccount): Promise<Result> => {
        return post('pay/api/virtual/account/save', params)
    },

    edit: (params: IPaymentVirtualAccount): Promise<Result> => {
        return post('pay/api/virtual/account/update', params)
    },

    
    /**
      * 发送支付验证码
      */
    sendCodeCaptcha: (params: ISendCodeCaptcha): Promise<string> => {
        return post('pay/api/virtual/sms/captcha',params)
    },

        /**
     * 导出-充值记录
     */
        exportTradeUnionAccount: (params: IQueryVirtualAccountsRequest): Promise<void> => {
            return download('pay/api/virtual/tradeUnionAccount/exportTradeUnionAccount', params)
        },
}
