<script lang="ts" setup name="EditDialog">
import {
  Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
  Input as hInput, Textarea as hTextarea, InputNumber as hInputNumber, DatePicker as hDatePicker,
  Checkbox as hCheckbox, CheckboxGroup as hCheckboxGroup, Cascader as hCascader
} from 'ant-design-vue';
import { computed, ref, watch, onMounted } from "vue";
import {
  IAnnualMeetingBudget,
  hotelLevelConstant
} from '@haierbusiness-front/common-libs';
import { annualMeetingBudgetApi } from '@haierbusiness-front/apis';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { bitValueDecompose } from '@haierbusiness-front/utils';
import cityChose from "@haierbusiness-front/components/cityChose/index.vue";

// 配置dayjs使用中文
dayjs.locale('zh-cn');

interface Props {
  show: boolean;
  data: IAnnualMeetingBudget | null;
  readonly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  readonly: false,
});

const from = ref();
const confirmLoading = ref(false);
const loading = ref(false);

const defaultData: IAnnualMeetingBudget = {
  name: '',
  miceTime: '',
  day: undefined,
  budget: undefined,
  place: '',
  hotelLevel: undefined,
  isCloth: false,
  item: [],
  personTotal: undefined,
  description: '',
  id: null
};

// 需求项目选项
const itemOptions = [
  { label: '住宿', value: 1 },
  { label: '餐饮', value: 2 },
  { label: '会场', value: 4 },
  { label: '用车', value: 8 }
];

// 选中的城市名称
const selectedCityName = ref('');

// 处理城市选择变更
const handleCityChange = (city: any) => {
  // 更新选中的城市名称显示
  selectedCityName.value = city?.name || '';
  // 更新表单中的place字段值
  annualMeetingBudget.value.place = city?.name || '';
};

const rules = {
  name: [{ required: true, message: '请输入会议名称', trigger: 'blur' },
  { max: 200, message: '最多输入200个字符', trigger: 'blur' }],
  miceTime: [{ required: true, message: '请选择会议时间', trigger: 'change' }],
  day: [{ required: true, message: '请输入会议天数', trigger: 'blur' }],
  budget: [{ required: true, message: '请输入预算金额', trigger: 'blur' }],
  place: [{ required: true, message: '请选择会议地点', trigger: 'change' }],
  hotelLevel: [{ required: true, message: '请选择酒店星级', trigger: 'change' }],
  description: [
  { required: true, message: '请输入会议描述', trigger: 'change' },
    { max: 200, message: '最多输入200个字符', trigger: 'blur' }]
};

const annualMeetingBudget = ref({ ...defaultData });

// 获取详情数据
const fetchDetail = (id: number) => {
  if (!id) return;

  loading.value = true;
  annualMeetingBudgetApi.get(id)
    .then(res => {
      // 处理日期格式，将年月日格式转为年月格式
      if (res.miceTime) {
        res.miceTime = dayjs(res.miceTime).format('YYYY-MM');
      }
      // 处理item字段，使用bitValueDecompose方法将数字转为位值数组
      if (res.item === null || res.item === undefined) {
        res.item = [];
      } else if (typeof res.item === 'number') {
        try {
          // 将数字解析为位值数组
          const itemArr = bitValueDecompose(res.item);
          if (Array.isArray(itemArr)) {
            res.item = itemArr;
          } else {
            res.item = [];
          }
        } catch (error) {
          console.error('解析item值错误:', error);
          res.item = [];
        }
      }

      annualMeetingBudget.value = res;
      // 如果有place字段，更新selectedCityName
      if (res.place) {
        selectedCityName.value = res.place;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 监听props变化，如果有id则获取详情
watch(() => props.data, (newValue) => {
  if (newValue && newValue.id) {
    fetchDetail(newValue.id);
  } else {
    annualMeetingBudget.value = { ...defaultData };
  }
}, { immediate: true });

const emit = defineEmits(["cancel", "ok"]);

const visible = computed(() => props.show);

const handleOk = () => {
  confirmLoading.value = true;
  from.value
    .validate()
    .then(() => {
      // 创建表单数据副本，避免直接修改原始数据
      const formData = { ...annualMeetingBudget.value };

      // 处理日期格式，将年月格式转为年月日格式，添加"-01"作为日
      if (formData.miceTime) {
        formData.miceTime = `${formData.miceTime}-01`;
      }

      // 确保编辑时id参数正确传递
      if (props.data && props.data.id) {
        formData.id = props.data.id;
      }

      emit("ok", formData, () => {
        confirmLoading.value = false;
      });
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};
</script>

<template>
  <h-modal v-model:visible="visible" :title="props.readonly ? '查看年度预算' : (annualMeetingBudget.id ? '编辑年度预算' : '新增年度预算')"
    :width="600" @cancel="$emit('cancel')" :confirmLoading="confirmLoading" @ok="handleOk"
    :footer="props.readonly ? null : undefined">
    <h-form ref="from" :model="annualMeetingBudget" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :rules="rules"
      :hide-required-mark="true">
      <!-- 会议月份 -->
      <h-form-item label="会议月份" name="miceTime">
        <h-date-picker v-model:value="annualMeetingBudget.miceTime" style="width: 100%" picker="month" format="YYYY-MM"
          value-format="YYYY-MM" placeholder="请选择会议月份" :disabled="props.readonly" />
      </h-form-item>

      <!-- 会议天数 -->
      <h-form-item label="会议天数" name="day">
        <h-input-number v-model:value="annualMeetingBudget.day" style="width: 100%" placeholder="请输入会议天数" :min="1"
          addon-after="天" :disabled="props.readonly" />
      </h-form-item>

      <!-- 会议名称 -->
      <h-form-item label="会议名称" name="name">
        <h-input v-model:value="annualMeetingBudget.name" placeholder="请输入会议名称，最多200字符" :maxlength="200" show-count
          :disabled="props.readonly" />
      </h-form-item>

      <!-- 预算金额 -->
      <h-form-item label="预算金额" name="budget">
        <h-input-number v-model:value="annualMeetingBudget.budget" style="width: 100%" placeholder="请输入预算金额" :min="0"
          addon-after="元" :disabled="props.readonly" />
      </h-form-item>

      <!-- 会议地点 -->
      <h-form-item label="会议地点" name="place">
        <!-- 查看模式：使用普通input展示 -->
        <h-input v-if="props.readonly" v-model:value="annualMeetingBudget.place" 
          placeholder="请选择会议地点" :disabled="true" />
        <!-- 编辑模式：使用城市选择组件 -->
        <city-chose v-else :showInternational="false" :value="selectedCityName" @chosedCity="handleCityChange"
          :width="'100%'" placeholder="请选择会议地点">
        </city-chose>
      </h-form-item>

      <!-- 参会人数 -->
      <h-form-item label="参会人数" name="personTotal">
        <h-input-number v-model:value="annualMeetingBudget.personTotal" style="width: 100%" placeholder="请输入参会人数"
          addon-after="人左右" :min="1" :disabled="props.readonly" />
      </h-form-item>

      <!-- 酒店星级 -->
      <h-form-item label="酒店星级" name="hotelLevel">
        <h-select v-model:value="annualMeetingBudget.hotelLevel" placeholder="请选择酒店星级" style="width: 100%"
          :disabled="props.readonly">
          <h-select-option v-for="item in hotelLevelConstant.toArray()" :key="item.code" :value="item.code">
            {{ item.desc }}
          </h-select-option>
        </h-select>
      </h-form-item>

      <!-- 是否布展 -->
      <h-form-item label="是否布展" name="isCloth">
        <h-select v-model:value="annualMeetingBudget.isCloth" placeholder="请选择是否布展" style="width: 100%"
          :disabled="props.readonly">
          <h-select-option :value="true">是</h-select-option>
          <h-select-option :value="false">否</h-select-option>
        </h-select>
      </h-form-item>

      <!-- 需求项目 -->
      <h-form-item label="需求项目" name="item">
        <h-checkbox-group v-model:value="annualMeetingBudget.item" :disabled="props.readonly">
          <h-checkbox v-for="option in itemOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </h-checkbox>
        </h-checkbox-group>
      </h-form-item>

      <!-- 会议描述 -->
      <h-form-item label="会议描述" name="description">
        <h-textarea v-model:value="annualMeetingBudget.description" placeholder="请输入会议描述，最多200字符" :maxlength="200"
          :auto-size="{ minRows: 3, maxRows: 5 }" show-count :disabled="props.readonly" />
      </h-form-item>
    </h-form>
  </h-modal>
</template>

<style lang="less" scoped>
.important {
  color: red;
}
</style>