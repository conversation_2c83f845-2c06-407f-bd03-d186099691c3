import {
    MiceFilter, MiceType, OrderListResult
} from '@haierbusiness-front/common-libs'
import { get, originalPost } from '../../request'

export const miceListApi = {
    list: (filter: MiceFilter): Promise<OrderListResult<MiceType>> => {
        return originalPost(`/businesstravel/haiermice/mice/conference/order-center/list-for-json?pageNum=${filter.pageNum}&pageSize=${filter.pageSize}`, {
            params: {
                'model.coCode': filter.coCode,
                'model.coName': filter.coName,
                'model.coCity': filter.coCity,
                'model.coIncharge': filter.coIncharge,
                pageNum: filter.pageNum,
                pageSize: filter.pageSize,
            }
        })
    }
}