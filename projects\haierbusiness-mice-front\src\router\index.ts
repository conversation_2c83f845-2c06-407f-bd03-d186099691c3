import { RouteRecordRaw } from 'vue-router';

const modules = import.meta.glob('/src/page/**/*.vue');

import { baseRouterConstructor } from '@haierbusiness-front/utils';

const routes: RouteRecordRaw[] = [
    {
        path: '/',
        component: () => import("../page/index.vue"),
        children: [
            {
                path: '/bill/upload',
                component: () => import("../page/bill/toUpload.vue")
            },
            {
                path: '/bill/view',
                component: () => import("../page/bill/billView.vue")
            },
            {
                path: '/mice/conferenceDetail',
                component: () => import('../page/mice/conferenceDetail.vue')
            },
            {
                path: '/mice/processInformation',
                component: () => import('../page/mice/processInformation.vue')
            }
        ]
    },
    
];


const router = baseRouterConstructor("haierbusiness-mice", modules, false, undefined, routes)


export default router;
