// 使用时间

type keys = 'HALF' | 'ALL';

export const UsageTimeTypeConstant = {
  HALF: { code: 0, desc: '半天（4小时/50公里）' },
  ALL: { code: 1, desc: '全天（8小时/100公里）' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in UsageTimeTypeConstant) {
      const item = UsageTimeTypeConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(UsageTimeTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return UsageTimeTypeConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
