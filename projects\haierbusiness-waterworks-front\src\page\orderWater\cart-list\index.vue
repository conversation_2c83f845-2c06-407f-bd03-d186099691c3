<script setup lang="ts">
import {
  Empty,
  But<PERSON> as hButton,
  Checkbox as hCheckbox,
  InputNumber as hInputNumber,
  Modal as hModal,
  Spin as hSpin,
  message,
} from 'ant-design-vue'
import { storeToRefs } from 'pinia'
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '@/store/goods-cart'

const router = useRouter()
const cartStore = useCartStore()
const { deleteFromCart } = cartStore
const { goodsList, isLoading } = storeToRefs(cartStore)
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE

// 选中商品ID数组
const checkedIds = ref<number[]>([])

/**
 * 全选状态相关
 * */

// 全选状态，根据选中ID数组和商品列表长度计算的
const checkedAll = computed({
  get: () => {
    return (
      goodsList.value?.length > 0 && checkedIds.value?.length === goodsList.value?.length
    )
  },
  set: (value: boolean) => {
    if (value) {
      checkedIds.value = goodsList.value?.map((item) => item.id)
    } else {
      checkedIds.value = []
    }
  },
})

// 计算选中商品数量
const checkedCount = computed(() => {
  return checkedIds.value?.length
})

// 计算选中商品总价
const totalPrice = computed(() => {
  return goodsList.value
    .filter((item) => checkedIds.value.includes(item.id))
    .reduce((total, item) => total + item.price * item.amount, 0)
})

// 删除商品
const handleDelete = async (id: number) => {
  await deleteFromCart(id)
  message.success('删除成功')
}

// 删除所有选中的商品
const handleDeleteAll = async () => {
  await new Promise((resolve, reject) => {
    hModal.confirm({
      title: '提示',
      content: '确定要删除选中商品吗?',
      onOk() {
        resolve(null)
      },
      onCancel() {
        reject()
      },
    })
  })
  if (checkedIds.value?.length > 0) {
    await deleteFromCart(checkedIds.value.join(','))
    message.success('删除成功')
  } else {
    message.warning('请选择至少一个商品')
  }
}

// 检查商品是否被选中
const isItemChecked = (id: number) => {
  return checkedIds.value.includes(id)
}

// 单个商品选中状态的切换
const toggleItemCheck = (id: number) => {
  const index = checkedIds.value.indexOf(id)
  if (index === -1) {
    checkedIds.value.push(id)
  } else {
    checkedIds.value.splice(index, 1)
  }
}

// 切换全选状态
const toggleAllCheck = () => {
  console.log('toggleAllCheck')
  if (checkedAll.value) {
    checkedIds.value = []
  } else {
    checkedIds.value = goodsList.value.map((item) => item.id)
  }
}

const handleSubmit = () => {
  console.log('选中的ID数组', checkedIds.value)
  console.log(
    '选中的商品列表',
    goodsList.value.filter((item) => checkedIds.value.includes(item.id))
  )
  console.log('选中的商品总价', totalPrice.value)
  cartStore.setOrderList(
    goodsList.value.filter((item) => checkedIds.value.includes(item.id))
  )
  router.push('/order-check')
  // 选中的数据传递给全局状态
}
</script>

<template>
  <div class="cart-list">
    <div class="table">
      <div class="tr tr-border">
        <div style="width: 60px"></div>
        <div class="th flex-3" style="justify-content: center">商品信息</div>
        <div class="th flex-1">单价（元）</div>
        <div class="th flex-1">数量</div>
        <div class="th flex-1" style="justify-content: center">操作</div>
      </div>

      <template v-if="isLoading">
        <div
          class="tr"
          style="
            height: 500px;
            display: flex;
            align-items: center;
            justify-content: center;
          "
        >
          <h-spin />
        </div>
      </template>
      <template v-else-if="goodsList?.length > 0">
        <div class="tr" v-for="(item, index) in goodsList" :key="index">
          <div class="td center" style="width: 60px">
            <h-checkbox
              :checked="isItemChecked(item.id)"
              @change="() => toggleItemCheck(item.id)"
            />
          </div>
          <div class="td flex-3">
            <img :src="item.productUrl" alt="" />
            <span style="margin-left: 16px">{{ item.productNm }}</span>
          </div>
          <div class="td flex-1 text-blue">￥ {{ item.price }}</div>
          <div class="td flex-1 number-controls">
            <h-button
              class="decrease-btn"
              :disabled="item.amount <= 1"
              @click="item.amount > 1 && item.amount--"
            >
              -
            </h-button>
            <h-input-number
              :controls="false"
              :step="1"
              :min="1"
              :precision="0"
              v-model:value="item.amount"
              style="width: 80px; text-align: center"
              @blur="
                () => {
                  if (item.amount === null || isNaN(item.amount)) {
                    item.amount = 1 // 重置为最小值
                  }
                }
              "
            />
            <h-button class="increase-btn" @click="item.amount++">+</h-button>
          </div>
          <div class="td flex-1 border-left">
            <h-button type="link" @click="handleDelete(item.id)">删除</h-button>
          </div>
        </div>
      </template>

      <template v-else>
        <a-empty :image="simpleImage" />
      </template>

      <div class="tr tr-border" style="margin-bottom: 24px">
        <div class="td center" style="width: 150px">
          <h-checkbox :checked="checkedAll" @change="toggleAllCheck" />
          <span style="margin-left: 16px">全选</span>
          <span>
            <h-button type="link" size="small" @click="handleDeleteAll">删除</h-button>
          </span>
        </div>
        <div class="flex-1 bottom-info">
          <div>
            已选商品 <span class="text-blue">{{ checkedCount }}</span> 件
          </div>
          <div>
            合计（元）：￥
            <span class="text-blue">{{ totalPrice }}</span>
          </div>
          <h-button class="submit-btn" size="small" type="primary" @click="handleSubmit"
            >结算</h-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.cart-list {
  width: 100%;
  height: 100%;
  .table {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
    .tr-border {
      background-color: #f2f9ff;
    }

    .tr {
      width: 100%;
      display: flex;
      border: 1px solid #8cbbec;
    }

    .th {
      display: flex;
      align-items: center;
      font-size: 14px;
      padding: 12px;
    }
    .td {
      display: flex;
      align-items: center;
      font-size: 13px;
      padding: 12px;
      img {
        width: 100px;
        height: 100px;
        object-fit: cover;
      }
    }
    .border-left {
      border-left: 1px solid #eef2f7;
    }
    .number-controls {
      gap: 8px;
      .decrease-btn,
      .increase-btn {
        height: 30px;
        width: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      :deep(.ant-input-number-input) {
        text-align: center;
        width: 80px;
      }
    }
    .bottom-info {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 48px;
    }

    .submit-btn {
      width: 100px;
      height: 100%;
      align-items: center;
      text-align: center;
    }
  }
}
.text-blue {
  color: #2a82db;
}
.flex-1 {
  flex: 1;
}
.flex-2 {
  flex: 2;
}
.flex-3 {
  flex: 3;
}
.center {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
