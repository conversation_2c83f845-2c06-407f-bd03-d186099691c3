<script setup>
  import { ref } from 'vue';
  import ProcessCard from './ProcessCard.vue';
  import ProcessRightImg from '@/assets/image/home/<USER>';
  import NoteBookImg from '@/assets/image/home/<USER>';
  import ClockImg from '@/assets/image/home/<USER>';
  import FileImg from '@/assets/image/home/<USER>';
  import HammerImg from '@/assets/image/home/<USER>';
  import MoneyImg from '@/assets/image/home/<USER>';

  const data = ref([
    {
      icon: NoteBookImg,
      name: '需求极速提报',
      desc: '智能表单，需求一键复制 轻松提报'
    },
    {
      icon: ClockImg,
      name: '顾问10分钟内响应',
      desc: '专业会议顾问为您全程服务 一单到底'
    },
    {
      icon: FileImg,
      name: '服务商方案交互',
      desc: '酒店、旅行社等多种类型服务商共同参与方案'
    },
    {
      icon: HammerImg,
      name: '方案竞价',
      desc: '针对方案二次竞价，保证方案价格的最优'
    },
    {
      icon: MoneyImg,
      name: '审核结算',
      desc: '会后智能账单审核，会务数据分析，保障安全'
    },
  ])
</script>

<template>
  <div class="process" >
    <template class="process-item" v-for="(item, index) of data" :key="index">
      <process-card :name="item.name" :desc="item.desc" :icon="item.icon" :tag="`STEP ${index + 1}`" />
      <img :src="ProcessRightImg" v-if="index < data.length - 1" />
    </template>
  </div>
</template>

<style scoped lang="less">
  .process {
    display: flex;
    align-items: center;
    justify-content: space-around;
    >img {
      width: 20px;
      height: 14px;
    }
  }
</style>
