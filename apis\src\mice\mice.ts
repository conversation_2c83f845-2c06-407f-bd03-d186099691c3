import {
    Mi<PERSON><PERSON><PERSON>er, IConferenceOrder, Result
} from '@haierbusiness-front/common-libs'
import { get, originalPost } from '../request'


export const miceApi ={
    list: (code: string, flag: number = 0): Promise<Result> => {
        return originalPost(`/businesstravel/haiermice/mice/conference/order/getOne?code=${code}&flag=${flag}`, {
            params: {
                
            }
        })
    },
    getStepInfoByMeeting: (meetingNo: string): Promise<Result> => {
        return originalPost(`/hb/pay/api/haier/budget/dept/hsh1/support/getStepInfoByMeetingNo`, {
            meetingNo
        })
    }
}