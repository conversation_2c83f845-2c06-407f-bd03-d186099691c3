
// 三字码城市接口返回数据
export interface CityResponse {
  pageSize: number;
  pageNum: number;
  total: number;
  key?: number;
  records:Array<Category>
}
export interface Category {
  label?: string;
  children?:Array<CityItem>
}
export interface CityItem {
  id?: string;
  citycode: string;
  name?: string;
  syName?: string;
  provinceName?: string;
  syId?: string;
  airport?: string;
  trainStation?: string;

}

// 国内城市数据
export interface CityOption {
  key: string|number,
  label: string,
  children: Array<CityDetail>
}
export interface CityDetail  {
  label: string,
  children: Array<CityItem>
}


// 获取城市列表请求数据
export interface CityParams {
  /**
   * 国家:country 省份:province 城市:city 区/县:district 街道:street,示例值(qingdao)
   */
  level?:string;

  /**
   * 是否国际城市0否1是,示例值(1)
   */
  internationalFlag?:string;

  /**
   * 城市名称,示例值(青岛)
   */
  name?:string;


   pageSize?:number;
   pageNum?:number;
}

// 国内城市数据
export interface CityOption {
  key: string|number,
  label: string,
  children: Array<CityDetail>
}


export interface airportParams {
  cityIds: string;
  isNeedNearby?: boolean;
}

export interface CityTreeParams {
  id: number,
  providerCode?: string;
  subdistrict?: number
}