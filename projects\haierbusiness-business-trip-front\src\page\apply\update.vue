<script lang="ts" setup>
import { computed, onMounted, onUnmounted, reactive, ref, watch, watchEffect, toRefs } from 'vue';
import type { Ref, UnwrapRef } from 'vue';
import outPersonModal from './components/outPersonModal.vue';
import budgetModal from './components/budgetModal.vue';
import budgetDetailModal from './components/budgetDetailModal.vue';
import travelStandardsModal from './components/travelStandardsModal.vue';
import budgetModalShow from './components/budgetModalShow.vue';

import baseInfo from './components/baseInfo.vue';
import planBudge from './components/planBudge.vue';
import fileUpload from './components/fileUpload.vue';
import { getCurrentRoute } from "@haierbusiness-front/utils";

import 'animate.css';
import {
  Anchor as hAnchor,
  Button as hButton,
  Spin as hSpin,
  Textarea as hTextarea,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  message as hMessage,
  UploadChangeParam,
  Select as hSelect,
  RangePicker as hRangePicker,
  DatePicker as hDatePicker,
  Table as hTable,
  SelectOption as hSelectOption,
  Checkbox as hCheckbox,
  Form as hForm,
  FormItem as hFormItem,
  Row as hRow,
  Col as hCol,
  Dropdown as hDropdown,
  Menu as hMenu,
  MenuItem as hMenuItem,
  Popconfirm as hPopconfirm,
  Modal as hModal

} from 'ant-design-vue';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { AnchorProps, SelectProps } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ICreatTrip } from '@haierbusiness-front/common-libs';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';

import { tripApi } from '@haierbusiness-front/apis';
import { useRequest } from 'vue-request';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);
const route = ref(getCurrentRoute());

const store = applicationStore();
const { loginUser } = storeToRefs(store);
const planBudgeRef = ref('');

const creatTripParma = ref<ICreatTrip>({
  tripList: [],
  travelerList: [
    {
      travelUserName: loginUser.value.nickName,
      travelUserSyId: loginUser.value?.username,
      travelUserDeptName: loginUser.value?.departmentName,
      travelUserDeptId: loginUser.value?.departmentCode,
      travelUserNo: loginUser.value?.username,
      username: loginUser.value?.username,
      travelUserType: '0',
      mainFlag: '1',
    },
  ],
  fileList: [],
  travelReason: undefined,
  travelReserveFlag: 1,
  travelUserName: loginUser.value.nickName,
});

const anchorItems = [
  {
    key: '1',
    href: '#base-info',
    title: '基本信息',
  },
  {
    key: '2',
    href: '#plan-budget',
    title: '行程计划与费用预算',
  },
  {
    key: '3',
    href: '#file',
    title: '附件',
  },
];

// 用户选择
const params = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});
// 出差人
const name = ref('');

const handleClick: AnchorProps['onClick'] = (e, link) => {
  e.preventDefault();
  console.log(link);
};


// 上传附件结束


const showRemind  = ref<boolean>(false)
const remind = ref<boolean>(false)
const animateRemind= () => {
    remind.value = true;
      setTimeout(() => {
        remind.value = false;
      }, 1000);
    }

const changeChecked = (checkedValue: object) => {
  showRemind.value = !checkedValue.target.checked
}

//#endregion

// 打开预算弹窗
const budget = ref();
const openBudgetModal = () => {
  budget?.value.show();
};

// 打开预算明细弹窗
const budgetDetail = ref();
interface IbudgetDetail {
  amountSum: string;
  travelUserName: string;
  travelUserNo: string;
  travelUserSyId: string;
}
const budgetDetailData = ref<Array<IbudgetDetail>>([]);
const openBudgetDetailModal = (data: Array<IbudgetDetail>) => {
  budgetDetailData.value = data;
  budgetDetail?.value.show();
};

// 差旅标准弹窗
const travelStandards = ref();
const openTravelStandardslModal = () => {

  let codeList = creatTripParma.value?.travelerList?.filter((item:any) => item.travelUserType == 0).map(item2 => item2.travelUserNo)
  let nameList = creatTripParma.value?.travelerList?.filter((item:any) => item.travelUserType == 0).map(item2 => item2.travelUserName)

  // 判断经办人的工号是否在出差人工号列表里面
  if(codeList.indexOf(loginUser.value?.username)== -1 ) {
    codeList = [loginUser.value?.username, ...codeList]
    nameList = [loginUser.value?.nickName, ...nameList]
  }

  travelStandards?.value.show(codeList, nameList);
};

// 外部联系人弹窗
const outPerson = ref();
const outPersonOpen = () => {
  outPerson?.value.show();
};
// 创建申请单请求参数


// 变更
const {
  data: applyChangeData,
  run: applyChangeRun,
  loading: applyChangeLoading,
} = useRequest(tripApi.applyChange, {
  defaultParams: [creatTripParma.value],
});
interface FormState {
  checked: boolean;
}

const formChecked: UnwrapRef<FormState> = ref({
  checked: false,
});

const baseInfoCom = ref();
const businessList = import.meta.env.VITE_BUSINESS_INDEX_URL;


// 行程重复
const repeatDialog = ref<boolean>(false)
const confirmLoading = ref<boolean>(false)
const conflictRecords = ref<any>([])

// 确认有重复的行程数据然后提交
const confirmRepeat = () => {
  confirmLoading.value = true

  spinning.value = true
  creatTripParma.value.conflictConfirmed = 1
  tripApi.applyChange(creatTripParma.value).then((res) => {
     // 提交后弹出流程弹窗
    hMessage.success('变更成功,2s后自动跳转列表页!');
    const url = businessList + '#' + '/card-order/trip';
    repeatDialog.value = false
    setTimeout(() => {
        spinning.value = false
        confirmLoading.value = false
        window.open(url, "_self");
    }, 2000);
  }).catch(err => {
    spinning.value = false
    confirmLoading.value = false
    repeatDialog.value = false
  })
}

const cancelRepeat = () => {
  spinning.value = false
  repeatDialog.value = false
}


// 提交变更单
const addApplyForm = () => {
  confirmVisable.value = false
  spinning.value = true
  creatTripParma.value.conflictConfirmed = 0

  tripApi.applyChange(creatTripParma.value).then((res) => {


    // 如果有异常数据 展示确认弹窗
    if(res.conflictRecords) {
      repeatDialog.value = true
      conflictRecords.value = JSON.parse(res.conflictRecords)
    }else {
      hMessage.success('变更成功,2s后自动跳转列表页!');
      const url = businessList + '#' + '/card-order/trip';
      setTimeout(() => {
        spinning.value = false
        window.open(url, "_self");
      }, 2000);
    }
    
  }).catch(err => {
    spinning.value = false

  })
};

//预算详情弹窗

const budgetDetailDialog = ref();
const openBudgetDetailShow = (res) => {
  budgetDetailDialog?.value.show(res || {});
};


const confirmVisable= ref<boolean>(false)

const beforeSubmit = async () => {

  // 提交前表单验证
  baseInfoCom.value
    .onSubmit()
    .then(async () => {
      if (formChecked.value.checked) {
        // 如果自费不用验证费用
        if (creatTripParma?.value.travelReserveFlag) {
          let planRes = await planBudgeRef.value.onSubmit();
          if (!planRes) {
            document?.getElementById('plan-budget')?.scrollIntoView();
            return;
          }
          
          let isEditMoney = planBudgeRef.value.isEditPlaneMoney();
          if(isEditMoney) {
            confirmVisable.value=  true
            return
          }
        }

        addApplyForm()

      } else {
        showRemind.value = true
        animateRemind()
        document?.getElementById('affix-bottom')?.scrollIntoView();
      }
    })
    .catch(() => {
      document?.getElementById('base-info')?.scrollIntoView();
    });

  
}
const cancel = () => {
  confirmVisable.value=  false
}

// 展示隐私政策
const yszcDialog = ref(false)
const showYszc = () => {
  yszcDialog.value = true;
}

const handleOk = () => {
  yszcDialog.value = false;
  formChecked.value.checked = true
  showRemind.value = false
}

const pdfUrl = new URL('@/assets/slyszc.pdf', import.meta.url).href

const showPdf = () => {
  window.open(pdfUrl)
}

// 保存申请单
const timer = ref<number>();
const lastSubmitTime = ref<string>();

const goToDetail = () => {
  const url = businessList + '#' + '/card-order/trip';
  window.open(url,"_self");
}

const applyNo = route.value?.query?.applyNo;

onMounted(async () => {
  creatTripParma.value = await tripApi.queryChangeDetailByApplyNo(applyNo);
  // 数据回显处理
  // 根据登陆人初始化数据
  baseInfoCom.value.chosedPerson = creatTripParma.value.travelerList?.filter((item) => item.travelUserType == '0' && item.mainFlag =='1')[0];
  baseInfoCom.value.chosedInPersenList = creatTripParma.value.travelerList?.filter((item) => item.travelUserType == '0' && item.mainFlag !='1');

  creatTripParma.value.travelUserName = creatTripParma.value.travelerList.filter(
    (item) => item.mainFlag == '1'
  )[0].travelUserName;
  creatTripParma.value.outPersonId = [];
  creatTripParma.value.travelerList.forEach((item) => {
    if (item.travelUserType == '1') {
      creatTripParma.value.outPersonId.push(item.travelUserSyId);
    }

    item.personIdList = [];
    item.personIdList = [...item.personIdList, item.travelUserSyId];
  });

  // 如果来源是 HWORK 把最新的数据赋值
  if(creatTripParma.value?.originApp == "HWORK") {
    if(creatTripParma.value.tripListHw) {
      creatTripParma.value.tripList = JSON.parse(JSON.stringify(creatTripParma.value.tripListHw))
    }
  }

  creatTripParma.value.tripList.forEach((item, index) => {
    if (index == 0) {
      planBudgeRef.value.cityList.push({
        cityCode: item.beginCityCode,
        city: item.beginCityName,
        syId: item.beginCityCodeSy,
        date: item.beginDate,
      });
    }
    planBudgeRef.value.cityList.push({
      cityCode: item.endCityCode,
      city: item.endCityName,
      syId: item.endCityCodeSy,
      date: item.endDate,
    });

    item?.tripDetailMapList?.forEach((tripMap) => {
      tripMap.personIdList = tripMap.travelApplyTripDetailList.map((tt) => tt.travelUserSyId);
    });
  });

  creatTripParma.value?.fileList?.forEach((file) => {
    file.name = file.fileName;
    file.thumbUrl = file.filePath;
  });

  // 两分钟自动保存
  // timer.value = setInterval(() => {
  //   applyChange();
  // }, 1000 * 60 * 2);
});

onUnmounted(() => {
  // clearInterval(timer.value);
});
const spinning = ref<boolean>(false)

</script>

<template>
  <h-spin size="large"  :spinning="spinning">
    <div class="container">
      <div class="row flex">
        <div class="main-title">
          <!-- <img src="../../assets/image/trip/title.png" alt="" /> -->
          <span>出差申请单变更</span>
        </div>
        <div class="apply-con flex">
          <!-- 基本信息 -->
          <base-info
            id="base-info"
            ref="baseInfoCom"
            :creatTripParma="creatTripParma"
            :isChange="true"
            @outPersonOpen="outPersonOpen"
            @showStandardOpen="openTravelStandardslModal"
          ></base-info>
          <!-- 行程与费用 -->
          <div id="plan-budget" class="whole-line">
            <plan-budge
              ref="planBudgeRef"
              :isChange="true"
              :creatTripParma="creatTripParma"
              @openBudgetModal="openBudgetModal"
              @openBudgetDetailShow="openBudgetDetailShow"

              @showStandardOpen="openTravelStandardslModal"
              @showBudgetDetailModal="(data) => openBudgetDetailModal(data)"
            ></plan-budge>
          </div>
          <!-- 附件 -->
          <div id="file" class="whole-line block-con">
            <file-upload :creatTripParma="creatTripParma"></file-upload>
          </div>
        </div>

        <div class="anchor-con flex">
          <h-anchor :items="anchorItems" @click="handleClick" />
        </div>

        <!-- 差旅标准弹窗 -->
        <travel-standards-modal ref="travelStandards" />
        <!-- 预算归属明细弹窗 -->
        <budgetModalShow ref="budgetDetailDialog" />
        <!--新增外部联系人 -->
        <out-person-modal ref="outPerson" />

        <!-- 预算归属弹窗 -->
        <budget-modal :source="creatTripParma.originApp" ref="budget" />

        <!-- 预算明细弹窗 -->
        <budget-detail-modal :tableData="budgetDetailData" ref="budgetDetail" />
      </div>

      <a-affix :offset-bottom="0" id="affix-bottom" class="affix-bottom">
        <div class="save-box flex">
          <div class="box-center">
            <div class="save-box-left font-size-14">
              <h-checkbox class="font-size-14 flex-center" v-model:checked="formChecked.checked" @change="changeChecked" required name="date1">
                <span class="font-size-14 font-color">已阅读并同意</span>
                <h-button type="link" @click.stop="showYszc">乘机提醒/商旅系统隐私政策</h-button>
              </h-checkbox>
              <span v-if="showRemind" :class="remind ? 'animate__animated animate__shakeX' : ''" class=" font-size-12 error-text">请阅读并勾选系统隐私政策</span>
            </div>
            <div class="save-box-right flex">
              <div class="auto-text font-size-14 mr-20 font-color" v-if="lastSubmitTime">
                <check-circle-two-tone two-tone-color="#52c41a" />
                {{ dayjs(lastSubmitTime).fromNow() }}自动保存
              </div>
              <div class="save-btns">
                <h-popconfirm
                  title="确定取消编辑并返回列表页吗?"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="goToDetail"
                >
                  <h-button size="small" class="my-button  mr-10">取消</h-button>
                </h-popconfirm>
                <a-popconfirm title="费用预算金额可根据实际需求调整!" :open="confirmVisable" ok-text="继续提交" cancel-text="返回修改"  @confirm="addApplyForm" @cancel="cancel">
                  <h-button size="small" class="my-button " type="primary"  @click="beforeSubmit">变更</h-button>
                </a-popconfirm>
              </div>
            </div>
          </div>
        </div>
      </a-affix>
    </div>
    <h-modal v-model:open="yszcDialog" title="乘机提醒/商旅系统隐私政策" @ok="handleOk">
      <p style="text-align: center;">文明乘机提醒</p>
      <p style="text-indent: 3ch;">在航空器上强占座位、辱骂殴打他人、妨碍机组正常履行职责、霸占航空器、破坏机上设施设备等行为，扰乱公共秩序、危害公共安全。构成违反治安管理行为的，公安机关将依法进行处罚；情节严重的，可能被追究刑事责任，请您遵规守法、文明乘机！</p>
      <p style="text-align: center;">随身行李告知</p>
      <p style="text-indent: 3ch;">为确保飞行安全和航班准点运行，请您携带符合标准的随身行李乘机（随身行李限额为：头等舱旅客限带2件，每件不得超过10公斤；豪华公务舱、公务舱/超级经济舱、经济舱旅客限带1件，每件不得超过8公斤；国内航班每件行李体积不得超过20cm×40cm×55cm；国际/地区航班每件行李体积不得超过25cm×45cm×56cm且三边之和小于等于115cm。） 超过规定的随身行李需重新安排安检及托运，将导致行李无法与您同机抵达，由此产生的相关损失将由您自行承担。请您在乘机前再次确认携带的随身行李符合件数、重量和尺寸标准，感谢您的支持和配合，祝您旅途愉快！</p>

      <div>
        <ExclamationCircleOutlined />
        我已阅读并同意<h-button type="link" @click.stop="showPdf">商旅系统隐私政策.pdf</h-button>

      </div>
    </h-modal>

    <h-modal v-model:open="repeatDialog" :maskClosable="false" title="行程重复提醒" cancelText="重新修改" okText="继续提交" :confirm-loading="confirmLoading" @ok="confirmRepeat" @cancel="cancelRepeat">
      <p style="text-align: left;">在同一时间段您有多个相同的行程,请您确认后再提交!</p>

      <div style="max-height: 400px; overflow-y: scroll;">
        <p  v-for="item,index in conflictRecords" :key="index">{{ `${index + 1 }、${item}` }}</p>
      </div>

    </h-modal>
</h-spin>
</template>

<style lang="less" scoped>
@import url(./components/trip.less);
</style>