<template>
  <div>
    <van-field
      required
      readonly
      :is-link="true"
      input-align="right"
      name="picker"
      :label="props.label"
      @click="showSelectBox"
      error-message-align="right"
    >
    <!--       @click-right-icon.stop="onSwitch"
      right-icon="exchange" -->
    <template #input>
      <div>{{ props.value || props.palceholder || '请选择' }}</div>
    </template>
  </van-field>
    <!-- 业务申请人弹窗 -->
    <van-popup v-model:show="showMainPersonList" position="bottom">
      <div class="out-pop" style="height: 100vh">
        <van-sticky>
          <div style="background: #fff; padding-bottom: 10px">
            <van-nav-bar :title="'选择'+props.label"  left-arrow @click-left="closeMainPersonPop">
              <template #left>
                <van-icon name="arrow-left" color="#000" size="24" />
              </template>
              <template #right>
                <div class="color-main"></div>
              </template>
            </van-nav-bar>
            <van-cell-group inset style="margin: 10px ; background-color: #fff">
              <van-field
                @update:model-value="searchMainPerson"
                v-model="searchValue"
                style="background: #f6f7f9;padding-left: 20px;"
                placeholder="输入工号、姓名"
              />
            </van-cell-group>
          </div>
        </van-sticky>
        <van-list
          v-model:loading="mainPersonLoading"
          :finished="mainPersonFinished"
          :finished-text="mainPersonList.length ? '没有更多了' : ''"
          @load="onLoadMainPerson"
        >
          <div
            v-if="mainPersonList.length == 0 && mainPersonFinished"
            style="height: 66vh"
            class="flex align-items-center justify-content-center"
          >
            <img class="img_empty" src="../assets/image/empty.jpg" alt="" />
          </div>
          <template v-else>
            <van-cell v-for="(item, index) in mainPersonList" :key="index" @click="choseMainPerson(item)">
              <div class="flex align-items-center ">
                <div
                  :class="index % 3 == 0 ? 'blue' : 'yellow'"
                  class="mr-10 ml-10 flex align-items-center justify-content-center img-name"
                >
                  {{ item?.nickName ? item?.nickName.slice(-2) : '未知' }}
                </div>
                <div class="main">
                  <div class="user-name color-main">{{ item.nickName }}({{ item.username }})</div>
                  <div v-if="props?.type !='xw'" class="phone">{{ item.enterpriseName || '未知' }}</div>
                </div>
              </div>
            </van-cell>
          </template>
        </van-list>
      </div>
    </van-popup>
    <!-- 选择主体弹窗 -->
    <van-popup v-model:show="showMainInfoBox" position="bottom">
      <div class="out-pop" style="height: 100vh">
        <van-sticky>
          <div style="background: #fff; padding-bottom: 10px">
            <van-nav-bar :title="'选择主体信息'"  left-arrow @click-left="closeMainInfoBox">
              <template #left>
                <van-icon name="arrow-left" color="#000" size="24" />
              </template>
              <template #right>
                <div class="color-main"></div>
              </template>
            </van-nav-bar>
          </div>
        </van-sticky>
        <van-list>
          <div
            v-if="userAbCode.length == 0"
            style="height: 66vh"
            class="flex align-items-center justify-content-center"
          >
          <van-empty description="暂无主体信息" />
          </div>
          <template v-else>
            <van-radio-group  v-model="selectCode">
            <van-cell v-for="(item, index) in userAbCode" :key="index" @click="choseMainInfo(item)">
              <template #icon>
                <van-radio
                  :name="item.performGroupCode"
                  style="width:30px;"
                />
              </template>
              <div class="flex ">
                  <div class="user-name color-main">
                    <p>执行主体：{{ item.performGroupCode}} </p>
                    <p>预算主体：{{ item.budgetGroupCode }} </p>
                    <p>来源系统： {{  item.systemCode}}</p>
                    </div>
              </div>
            </van-cell>
            <div v-if="type=='hbc2'" style="margin-top:20px;font-size:12px;color:brown;padding:10px 20px;">集团平台部门请选择ORG，智家选择BCC，其他领域根据实际情况选择对应组织</div>
          </van-radio-group >
          </template>
        </van-list>
      </div>
    </van-popup>
    <van-action-sheet v-model:show="selctSheetShow" :actions="actions" @select="onSelect" />
  </div>
</template>

<script setup lang="ts">
import type { Ref } from 'vue';
import { createVNode, onMounted, reactive, ref, watch, watchEffect, toRefs, computed } from 'vue';
import {
  showFailToast,
  Button as VanButton,
  Form as VanForm,
  Field as VanField,
  CellGroup as VanCellGroup,
  showDialog,
  showSuccessToast,
  NoticeBar,
  Popup as VanPopup,
  showToast
} from "vant";
import { userApi, organizationCenterApi, budgetHaierPayApi } from '@haierbusiness-front/apis';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { IUserListRequest, IUserInfo, ICity, ITripInfo, ITraveler, ICreatTrip,IAbCodeResponse } from '@haierbusiness-front/common-libs';
import { debounce, values } from 'lodash';
import { emit } from 'process';
import { DataType, usePagination, useRequest } from 'vue-request';

const store = applicationStore();

const { loginUser } = storeToRefs(store);

const actions = [
      { name: '选择预算人' },
      { name: '选择预算主体' },
    ];
// props参数

interface Props {
  palceholder?: string; // 人员
  label?: string;
  value?: string;
  type?:string;
}
const props = defineProps<Props>();

const emit = defineEmits(['chose','choseMainInfo']);

const onSwitch =()=>{
  console.log(111111,"99999")
  
}

const selectCode = ref<string>('')

// 业务申请人相关
const searchValue = ref('');
const showMainInfoBox = ref(false);
const showMainPersonList = ref(false);
const mainPersonLoading = ref(false);
const mainPersonFinished = ref(false);
const selctSheetShow = ref(false)
const mainPersonTotal = ref(0);
const mainPersonList = ref<Array<ITraveler>>([]);


const defaultParams: IUserListRequest = {
  pageNum: 0,
  pageSize: 20,
};

const params: Ref<IUserListRequest> = ref(defaultParams);
const closeMainPersonPop = () => {
  showMainPersonList.value = false;
};
const closeMainInfoBox = () => {
  showMainInfoBox.value = false;
};
const onLoadMainPerson = (type:number) => {
  params.value.pageNum++;
  // 其他预算使用公用人员列表接口
  // 小微预算采用小微预算接口
  if(props.type == 'xw'){

    budgetHaierPayApi.queryXwfinBudgeters(params.value).then(res => {
      res?.records?.forEach(item => {
        item.nickName = item.userName;
        item.username = item.userCode;
      })
      // 加载状态结束
      mainPersonLoading.value = false;
      if(type){
        mainPersonList.value= res.records
      }else{
        mainPersonList.value = [...mainPersonList.value, ...res.records];
      }
      mainPersonTotal.value = res.total ?? 0;
      // 数据全部加载完成
      if (mainPersonList.value.length >= mainPersonTotal.value) {
        mainPersonFinished.value = true;
      }
    })
  }else {
    userApi.list(params.value).then((res) => {
      // 加载状态结束
      mainPersonLoading.value = false;
      if(type){
        mainPersonList.value= res.records
      }else{
        mainPersonList.value = [...mainPersonList.value, ...res.records];
      }
      mainPersonTotal.value = res.total ?? 0;
      // 数据全部加载完成
      if (mainPersonList.value.length >= mainPersonTotal.value) {
        mainPersonFinished.value = true;
      }
    });
  }

};

const searchMainPerson = debounce((val: string) => {
  params.value.keyWord = val;
  params.value.pageNum = 0;
  params.value.pageSize = 20;
  mainPersonFinished.value = false;
  mainPersonList.value = [];
  onLoadMainPerson(1);
});

// 获取主体信息
const getAbCodeByUser = async (userName:string) => {
  if(!userName) {
    return showToast('请补充预算信息！')
  }
  if(props.type == 'xw') {
    await searchAbCodeByUserRun({ userName:userName, systemCode:'XW' })
  }else {
    await searchAbCodeByUserRun({ userName:userName })
  }
  emit('choseMainInfo', {})
  selectCode.value = ""
  showMainInfoBox.value = true
}

// 按人查询A码和B码
const {
  data: userAbCodeData,
  run: searchAbCodeByUserRun,
  loading: searchUserLoading
} = useRequest(organizationCenterApi.getAbCode);

const userAbCode = computed(() => {
  if(userAbCodeData.value && userAbCodeData.value.length > 0) {
    let list = [] as IAbCodeResponse[]
    userAbCodeData.value.map((item, index) => {
      const data: IAbCodeResponse = {
        ...item,
        id: index
      }
      list = [...list, data]
    })
    return list
  } else {
    return [] as IAbCodeResponse[]
  }
})

const choseMainPerson = (item: ITraveler) => {
  // 根据传递的type 如果type = hbc2
  if(props.type == 'hbc2' || props.type == 'xw'){
    // 选择支付主体
    console.log(item)
    getAbCodeByUser(item.username)
  }
  showMainPersonList.value = false;
  emit('chose', item)
};

// 选择主体信息
const choseMainInfo = (item: any) => {
  // 根据传递的type 如果type = hbc2
  showMainInfoBox.value = false;
  selectCode.value = item.performGroupCode
  emit('choseMainInfo', item)
};

// 选择弹窗
const showSelectBox = ()=>{
  if(props.type != 'hbc2'){
    showMainPersonList.value = true
  }else{
    if(!props.value){
      showMainPersonList.value = true
    }else{
      // 选择操作方式
      selctSheetShow.value = true
    }
  }
}
const onSelect = (item:any) =>{
  if(item.name=='选择预算人'){
    showMainPersonList.value = true
  }else{
    showMainInfoBox.value = true
  }
  selctSheetShow.value  = false;
  
}


</script>

<style scoped lang='less'>
.flex {
  display: flex;
}
.flex-column {
  flex-direction: column;
}
.align-items-center {
  align-items: center;
}
.justify-content-center {
  justify-content: center;
}
.justify-content-between {
  justify-content: space-between;
}
.flex-warp {
  flex-wrap: wrap;
}

.img_empty {
  width: 150px;
}

.mr-10 {
  margin-right: 10px !important;
}
.mr-20 {
  margin-right: 20px;
}
.color-main {
    color: #0073e5;
  }
.out-pop {
  .main {
    flex: 1;
  }
  .img-name {
    width: 30px;
    height: 30px;
    
    color: #fff;
    font-size: 10px;
    border-radius: 30px;
  }
  
  .blue {
    background-color: #0073e5;
  }
  .yellow {
    background-color: #faad14;
  }
  .add-person {
    margin: 16px;
    margin-bottom: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
    font-size: 12px;
  }
  .user-name {
    text-align: left;
    font-size: 12px;
  }
  .phone {
    text-align: left;
    color: #8c8c8c;
  }
}
p{
  margin:0;
  padding:0;
}
</style>