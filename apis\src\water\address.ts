import { download, get, post, filepost, originalGet } from '../request'

export const waterworkAddressApi = {
    list: (params: any): Promise<void> => {
        return get('waterworks/api/tuserAddress/page', params)
    },
    listAll: (params: any): Promise<void> => {
        return get('waterworks/api/tuserAddress/list', params)
    },
    add: (params: any): Promise<void> => {
        return post('waterworks/api/tuserAddress/save', params)
    },
    update: (params: object): Promise<void> => {
        return post(`waterworks/api/tuserAddress/update`, params);
    },
    export: (params: any): Promise<void> => {
        return download('waterworks/api/tuserAddress/export', params)
    },
    delete: (ids: string): Promise<void> => {
        return post(`waterworks/api/tuserAddress/delete/${ids}`);
    },
}