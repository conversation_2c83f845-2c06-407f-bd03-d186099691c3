<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  Input as hInput,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { noticeContractExpirationApi, tagApi } from '@haierbusiness-front/apis';
import {
  INoticeContractExpirationFilter,
  INoticeContractExpiration
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'

import EditDialog from './edit-dialog.vue'
import router from '../../router'
// const router = useRouter()

const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router
  listApiRun({
    pageNum: 1,
    pageSize: 10
  })

})


//获取中台业务类型
const {
  data: businessTypeList,
  run: businessTypeApiRun,
  loading: businessTypeLoading,
} = useRequest(tagApi.getBusinessTypeList, {
  manual: false,
  onSuccess: (data) => console.log('接口返回数据:', data)
});

const columns: ColumnType[] = [
  {
    title: '业务类型',
    dataIndex: 'businessType',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) =>
      businessTypeList.value?.find((item: {
        key: any; value: any;
      }) => item.key === text)?.value || '-'
  },
  {
    title: '通知人',
    dataIndex: 'nickName',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ record }) => {
      return {
        children: record.sceneContractNotifierDTOList
          .map((item: { nickName: string; userName: string; }) => `${item.nickName}(${item.userName})`)
          .join(', '),
      }
    }
  },

  {
    title: '邮箱',
    dataIndex: 'email',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ record }) => ({
      children: record.sceneContractNotifierDTOList
        .map(item => item.email)
        .join(', '),
      attrs: {
        style: 'display: inline-block; max-width: 100%'
      }
    })
  },

  {
    title: '提前通知天数',
    dataIndex: 'expiredNoticeDay',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<INoticeContractExpirationFilter>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(noticeContractExpirationApi.list);

const reset = () => {
  searchParam.value = {}
  listApiRun({
    pageNum: 1,
    pageSize: 10
  })

}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};
const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } =
  useEditDialog<INoticeContractExpiration, INoticeContractExpiration>(noticeContractExpirationApi, "合同到期通知人管理", () => listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum || 1,
    pageSize: data.value?.pageSize || 10,
  }))

const thisHandleEdit = (item: INoticeContractExpiration) => {
  const currentData = {
    ...item
  };
  handleEdit({ ...currentData });
}


// 删除
const { handleDelete } = useDelete(noticeContractExpirationApi, () => listApiRun({
  ...searchParam.value,
  pageNum: data.value?.pageNum,
  pageSize: data.value?.pageSize,
}))

const beginAndEnd = ref<[Dayjs, Dayjs]>()
watch(() => beginAndEnd.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.begin = n[0]
    searchParam.value.end = n[1]
  } else {
    searchParam.value.begin = undefined
    searchParam.value.end = undefined
  }
});



</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <a-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="businessType">业务类型：</label>
          </a-col>
          <a-col :span="4">
            <a-select style="width: 100%" v-model:value="searchParam.businessType" allow-clear placeholder="请选择业务类型">
              <a-select-option v-for="(item, index) in businessTypeList" :key="index" :value="item.key">{{
                item.value
              }}</a-select-option>
            </a-select>
          </a-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="nickName">通知人：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.userName" placeholder="支持名称、工号、邮箱查询" />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
            <h-button type="primary" @click="handleCreate()">
              <PlusOutlined /> 新增
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="thisHandleEdit(record)">编辑</h-button>

              <h-button type="link" @click="handleDelete(record.id)">删除</h-button>
            </template>

          </template>
        </h-table>
      </h-col>
    </h-row>

    <div v-if="visible">
      <edit-dialog :show="visible" :data="editData" @cancel="onDialogClose" @ok="handleOk">
      </edit-dialog>
    </div>

  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.ellipsis-container {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
