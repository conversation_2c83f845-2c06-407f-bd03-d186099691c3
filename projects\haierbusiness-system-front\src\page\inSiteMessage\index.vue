<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Textarea as hTextarea,
  message,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { CopyOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { inSiteMessageApi } from '@haierbusiness-front/apis';
import {
  IInSiteMessageFilter,
  IInSiteMessage
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted, onUnmounted, nextTick, h } from 'vue';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import { DataType, usePagination, useRequest } from 'vue-request';
import EditDialog from './edit-dialog.vue'
import router from '../../router'
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
const { loginUser } = storeToRefs(applicationStore());
// const router = useRouter()

const currentRouter = ref()
const columns: ColumnType[] = [
  {
    title: '来自',
    dataIndex: 'subject',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '接收人',
    dataIndex: 'nickName',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '内容',
    dataIndex: 'content',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '相关链接',
    dataIndex: 'applicationCode',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ record }) => {
      // const link = getFullInquiryLink(record);
      return h('div', { style: { display: 'flex', alignItems: 'center', justifyContent: 'space-between' } }, [
        h('span', { style: { flex: 1, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' } }, record.applicationCode),
        h(hButton, {
          type: 'link',
          icon: h(CopyOutlined),
          onClick: (e: Event) => {
            e.stopPropagation();
            copyLink(record.applicationCode);
          }
        })
      ]);
    },
  },
  {
    title: '发送时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'isRead',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      const state = options.find(item => item.value == text);
      return state ? state.label : '';
    }
  },
  {
    title: '已读时间',
    dataIndex: 'readTime',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text, record }) => {
      return record.isRead == 0 ? text : '-'; // 未读状态显示占位符
    }
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center'
  },
];

// // 获取完整链接
// const getFullInquiryLink = (record: IInSiteMessageFilter) => {//import.meta.env.VITE_BUSINESS_URL ||
//   const baseUrl = import.meta.env.VITE_MICE_BIDMAN_URL;
//   return `${baseUrl}#/bidman/priceInquiry/inquiryOrderForm?id=${record.id}&code=${record.code}`;
// };

// 添加复制链接的方法
const copyLink = (link: string) => {
  navigator.clipboard.writeText(link).then(() => {
    message.success('链接已复制到剪贴板');
  }).catch(err => {
    console.error('复制失败:', err);
    message.error('复制失败');
  });
};

const searchParam = ref<IInSiteMessageFilter>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(inSiteMessageApi.list);

const reset = () => {
  searchParam.value = {}
  readTime.value = undefined
  gmtCreate.value = undefined
  // 使用nextTick确保DOM更新后再调用接口
  nextTick(() => {
    // 重置后立即查询
    const params = {
      pageNum: 1,
      pageSize: 10
    };
    console.log('重置后调用接口参数:', params);
    listApiRun(params);
  });
}

const options = [
  { value: 0, label: '已读' },
  { value: 1, label: '未读' },
]

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};
const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } =
  useEditDialog<IInSiteMessage, IInSiteMessage>(inSiteMessageApi, "站内信", () => listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum || 1,
    pageSize: data.value?.pageSize || 10,
  }))

const thisHandleEdit = (item: IInSiteMessage) => {
  const currentData = {
    ...item
  };
  handleEdit({ ...currentData });
  if (item.isRead == 1) {
    readStatus(Number(item.id))
  }
}

//改变已读状态
const readStatus = async (id: number) => {
  if (!id) return;

  loading.value = true;
  try {
    await inSiteMessageApi.state(id);
  } catch (error) {
    console.error('改变状态失败:', error);
  } finally {
    loading.value = false;
    listApiRun({ pageNum: 1, pageSize: 10 })
  }
};


// 删除
const { handleDelete } = useDelete(inSiteMessageApi, () => listApiRun({
  ...searchParam.value,
  pageNum: data.value?.pageNum,
  pageSize: data.value?.pageSize,
}))

//发送时间
const gmtCreate = ref<[Dayjs, Dayjs]>()
watch(() => gmtCreate.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.startTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
    searchParam.value.endTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
  } else {
    searchParam.value.startTime = undefined
    searchParam.value.endTime = undefined
  }
});
//已读时间
const readTime = ref<[Dayjs, Dayjs]>()
watch(() => readTime.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.readStartTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
    searchParam.value.readEndTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
  } else {
    searchParam.value.readStartTime = undefined
    searchParam.value.readEndTime = undefined
  }
});

const pollingInterval = ref()
const pollTime = 60000 // 1分钟轮询间隔

onMounted(async () => {
  currentRouter.value = await router
  // 首次立即执行
  listApiRun({ pageNum: 1, pageSize: 10 })

  // 启动轮询
  pollingInterval.value = setInterval(() => {
    listApiRun({ pageNum: 1, pageSize: 10 })
  }, pollTime)
})

onUnmounted(() => {
  // 组件卸载时清除定时器
  clearInterval(pollingInterval.value)
})

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createTime">来自：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="operator" v-model:value="searchParam.subject" placeholder="请输入" allow-clear
              :maxlength="500" />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createTime">接收人：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="operator" v-model:value="searchParam.userName" placeholder="请输入接收人" allow-clear
              :maxlength="500" />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createTime">内容：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="operator" v-model:value="searchParam.content" placeholder="请输入内容" allow-clear
              :maxlength="500" />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createTime">发送时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="gmtCreate" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createTime">已读时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="readTime" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 1200 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="thisHandleEdit(record)">查看</h-button>
              <h-button type="link" @click="handleDelete(record.id)">删除</h-button>
            </template>
            <!-- <template v-if="column.dataIndex === 'userName'">

            </template> -->
          </template>
        </h-table>
      </h-col>
    </h-row>

    <div v-if="visible">
      <edit-dialog :show="visible" :data="editData" @cancel="onDialogClose" @ok="handleOk">
      </edit-dialog>
    </div>

  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}
</style>
