// 交通类型

type keys = 'TRAIN' | 'PLANE';

export const TrafficTypeConstant = {
  TRAIN: { code: 0, desc: '火车' },
  PLANE: { code: 1, desc: '机票' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in TrafficTypeConstant) {
      const item = TrafficTypeConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(TrafficTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return TrafficTypeConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
