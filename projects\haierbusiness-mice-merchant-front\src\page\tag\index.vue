<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { tagApi } from '@haierbusiness-front/apis';
import {
  ITagFilter,
  ITag
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import EditDialog from './edit-dialog.vue'
import router from '../../router'
// const router = useRouter()

const currentRouter = ref()

onMounted(async () => {
    currentRouter.value = await router
})

const columns: ColumnType[] = [
  {
    title: '业务类型',
    dataIndex: 'businessType',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '标签名称',
    dataIndex: 'tagName',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '描述',
    dataIndex: 'description',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<ITagFilter>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(tagApi.page);

const reset = () => {
  searchParam.value = {}
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};
const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } =
  useEditDialog<ITag, ITag>(tagApi, "标签", () => listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum || 1,
    pageSize: data.value?.pageSize || 10,
  }))

const thisHandleEdit = (item: ITag) => {
  const currentData = {
    ...item
  };
  handleEdit({ ...currentData });
}


// 删除
const { handleDelete } = useDelete(tagApi, () => listApiRun({
  ...searchParam.value,
  pageNum: data.value?.pageNum,
  pageSize: data.value?.pageSize,
}))



</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="tagName">标签名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="tagName" v-model:value="searchParam.tagName" placeholder="" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="businessType">业务类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select style="width: 100%" v-model:value="searchParam.businessType" allow-clear>
              <h-select-option value="1" >订餐</h-select-option>
              <h-select-option value="2" >订房</h-select-option>
              <h-select-option value="3" >会展</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="state">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select style="width: 100%" v-model:value="searchParam.state" allow-clear>
              <h-select-option :value="1">是</h-select-option>
              <h-select-option :value="0">否</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
            <h-button type="primary" @click="handleCreate()">
              <PlusOutlined /> 新增
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'businessType'">
              {{ record.businessType === '1' ? '订餐' : record.businessType === '2' ? '订房' : '会展' }}
            </template>
            <template v-if="column.dataIndex === 'state'">
              {{ record.state === 1 ? '是' : '否' }}
            </template>
            <template v-if="column.dataIndex === '_operator'">
                <h-button type="link"  @click="thisHandleEdit(record)">编辑</h-button>
              
              <h-button type="link" @click="handleDelete(record.id)">删除</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    
    <div v-if="visible">
        <edit-dialog
            :show="visible"
            :data="editData"
            @cancel="onDialogClose"
            @ok="handleOk"
        >
        </edit-dialog>
    </div>

  </div>
</template>

<style scoped lang="less">

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

</style>
