import {
    IAbCodeRequest,
    IAbCodeResponse,
    IBillAndCostCentersResponse,
    IAbCodeDeptResponse
} from '@haierbusiness-front/common-libs'
import {get, post} from '../request'

export const organizationCenterApi = {
    getAbCode: (params: IAbCodeRequest): Promise<Array<IAbCodeResponse>> => {
        return get('pay/api/haier/org/getAbCodeByUsers', params)
    },
    getAbCodeByDept: (workName: string): Promise<Array<IAbCodeDeptResponse>> => {
        return get('pay/api/haier/org/getAbCodeByDepts', {
            workName
        })
    },

    getBillAndCostCenters: (performCode: string, costItemCode: string): Promise<Array<IBillAndCostCentersResponse>> => {
        return get('pay/api/haier/org/queryBillAndCostCenters', {
            performCode,
            costItemCode
        })
    },
    // 查询研发项目
    getQueryProject: (params: any): Promise<Array<IAbCodeDeptResponse>> => {
        return get('pay/api/haier/budget/dept/hbc2/support/queryProject', params)
    }

}