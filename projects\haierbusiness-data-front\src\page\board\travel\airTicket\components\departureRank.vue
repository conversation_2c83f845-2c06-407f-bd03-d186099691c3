<template>
    <div style="height: 35.3vh;">
        <Rank background="rgba(0,0,0,0)" :data="rankData"
            unit="人次" :base="5" />
    </div>
</template>
<script setup lang="ts">
import Rank from "../../../components/rank.vue";
import { queryDepartureRank } from "@haierbusiness-front/apis/src/data/board/travel";
import { onMounted, ref } from "vue";
import { EventBus } from "../../../eventBus";
const props = defineProps({
    gngj: {
        type: [String, Number],
        default: "1",
    },
});
const rankData = ref(
    [] as Array<{
        name: string;
        value: string | number;
    }>
);
const loading = ref(false);
onMounted(() => {
    queryData();
})
EventBus.on((event, params) => {
    if (event == "refresh") queryData(params ? params : null);
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
    loading.value = true;
    const data = await queryDepartureRank(
        { gngj: props.gngj },
        params ? params.data.name : null,
        params ? params.from : null
    );
    loading.value = false;
    const rows = [] as Array<{
        name: string;
        value: string | number;
    }>;
    data.rows.forEach((item) => {
        rows.push({
            name: item[0],
            value: item[1],
        });
    });
    // rows.sort((a,b)=>b.value-a.value);
    rankData.value = rows;
};
</script>
