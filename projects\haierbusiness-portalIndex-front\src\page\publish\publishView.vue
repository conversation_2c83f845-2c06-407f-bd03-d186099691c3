// 顾问列表页面
<script lang="ts" setup>
import { <PERSON><PERSON>, Modal, Drawer, message, Collapse, CollapsePanel } from 'ant-design-vue';
import Advisors from '@haierbusiness-front/components/mice/advisors/index.vue';
import { ref, reactive, computed } from 'vue';
import MeetingConsultantDrawer from '@haierbusiness-front/components/meetingConsultantDrawer/index.vue';

import { miceBidManOrderListApi } from '@haierbusiness-front/apis';
import { TestData } from '@haierbusiness-front/common-libs';
import { useRouter, useRoute } from 'vue-router';
// 状态变量定义
const editModalOpen = ref(false); // 编辑抽屉是否打开
const testData = ref<TestData[]>([]); // 顾问列表数据
const testDataSum = ref<TestData[]>([]); // 顾问列表数据备份
const open = ref(false); // 驳回弹窗是否打开
const reason = ref(''); // 驳回原因

// 处理驳回操作
const handleReject = () => {
  open.value = true;
  console.log('驳回');
};
const route = useRoute();
// 确认驳回
const handleOk = async () => {
  const res = await miceBidManOrderListApi.receive_reject({
    miceId: route.query.miceId,
    demandRejectReason: reason.value,
  });
  console.log(res);
  if (res.success) {
    message.success('驳回成功');
    open.value = false;
    router.push({ path: '/bidman/orderList/index', query: { status: '0' } });
  }
};

// 分配人员
const assignPerson = async (item) => {
  const res = await miceBidManOrderListApi.userAssign({
    username: item.username,
    id: route.query.miceId,
  });
  if (res.success) {
    message.success('分配成功');
    editModalOpen.value = false;
  }
};
const router = useRouter();
// 处理发布
const handlePublish = () => {
  router.push({
    path: '/bidman/publish/index',
    query: {
      record: JSON.stringify({ miceId: route.query.miceId, miceDemandId: route.query.miceDemandId, status: '1' }),
    },
  });
  console.log('需求发布');
};
const rejectPush = async () => {
  if (reason.value) {
    const res = await miceBidManOrderListApi.cancelPush({
      miceId: route.query.miceId,
      demandRejectReason: reason.value,
    });
    if (res.success) {
      message.success('驳回成功');
      open.value = false;
      router.push({ path: '/bidman/orderList/index', query: { status: '0' } });
    }
  } else {
    message.error('驳回原因不能为空！');
  }
};

// 搜索关键词
const searchKeyword = (value) => {
  console.log(value);
  if (value)
    testData.value = testDataSum.value.filter((item) => {
      console.log(item);
      if (item.username.includes(value) || item.name.includes(value)) return item;
    });
  else testData.value = testDataSum.value;
};
</script>
<template>
  <!-- 顾问列表页面主容器 -->
  <div>
    <!-- 顾问列表组件 -->
    <Advisors preview-source="demandOne">
      <!-- 头部插槽：备忘录和日志按钮 -->
      <template #header>
        <Button size="small" type="primary" style="margin-right: 10px">备忘录</Button>
        <Button size="small" type="primary" style="margin-right: 10px">日志</Button>
      </template>
      <!-- 底部插槽：操作按钮组 -->
      <template #footer>
        <Button size="small" style="margin-right: 10px" type="primary" @click="open = true" danger>需求驳回</Button>
        <Button size="small" style="margin-right: 10px" type="primary" @click="handlePublish">需求发布</Button>
      </template>
    </Advisors>

    <!-- 驳回原因弹窗 -->
    <a-modal v-model:open="open" title="驳回原因" @ok="rejectPush">
      <a-textarea v-model:value="reason" :rows="4" :maxLength="200" placeholder="请输入驳回原因" />
    </a-modal>

    <!-- 会务顾问抽屉组件 -->
    <meeting-consultant-drawer
      title="会务顾问"
      v-model="editModalOpen"
      :items="testData"
      @search="searchKeyword"
      @assign="assignPerson"
    />
  </div>
</template>
<style lang="less" scoped>
:deep(.ant-btn-default) {
  padding: 3px 8px;
  height: 32px;
  width: 80px;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #4e5969;
  line-height: 22px;
  text-align: center;
  font-style: normal;
}
:deep(.ant-btn-primary) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 22px;
  text-align: center;
  font-style: normal;
  padding: 3px 8px;
  height: 32px;
  width: 80px;
  border-radius: 2px;
}
.reject-btn {
  background: #f5222d;
}
</style>
