<script setup lang="ts">
import { Popconfirm } from 'ant-design-vue';
import {
  Textarea as hTextarea,
  <PERSON><PERSON> as hModal,
  Popconfirm as hPopconfirm,
  Select as hSelect,
  SelectOption as hSelectOption,
  Checkbox as hCheckbox,
  CheckboxGroup as hCheckboxGroup,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  Switch as hSwitch,
  Input as hInput,
  Form as hForm,
  FormItem as hFormItem,
  Dropdown as hDropdown,
  InputSearch as InputSearch,
  Avatar as hAvatar,
  ListItemMeta as hListItemMeta,
  ListItem as hListItem,
  List as hList,
  Tree as hTree,
  BreadcrumbItem as hBreadcrumbItem,
  Breadcrumb as hBreadcrumb,
  LayoutSider as hLayoutSider,
  LayoutFooter as hLayoutFooter,
  LayoutContent as hLayoutContent,
  LayoutHeader as hLayoutHeader,
  Layout as hLayout,
  MenuItem as hMenuItem,
  MenuItemGroup as hMenuItemGroup,
  SubMenu as hSubMenu,
  <PERSON><PERSON> as hMenu,
  <PERSON><PERSON><PERSON> as hDivider,
  Space as hSpace,
  <PERSON><PERSON> as hButton,
  <PERSON> as hCol,
  <PERSON>sult as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Tabs as hTabs,
  message,
  TreeProps,
} from 'ant-design-vue';
import { UnwrapRef, onMounted, reactive, ref, toRaw, watch } from 'vue';
import {
  ExclamationCircleOutlined,
  ArrowDownOutlined,
  ArrowUpOutlined,
  EnterOutlined,
  DeleteOutlined,
  FileOutlined,
  FileTextOutlined,
  UngroupOutlined,
  FolderOutlined,
  HomeOutlined,
  NodeExpandOutlined,
  DownOutlined,
  FrownOutlined,
  UploadOutlined,
  UserOutlined,
  NotificationOutlined,
  HolderOutlined,
  AppstoreOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons-vue';
import { resourceApi, applicationApi } from '@haierbusiness-front/apis';
import {
  IApplicationInfo,
  IResourceInfoTreeResponse,
  IResourceTreeNode,
  ResourceTypeConstant,
} from '@haierbusiness-front/common-libs';
import IconSelector from '@haierbusiness-front/components/IconSelector/IconSelector.vue';
import lodash from 'lodash';
import { DataNode } from 'ant-design-vue/lib/tree';

const resource = ref<IResourceTreeNode[]>([]);
const generateKey = (resource: IResourceTreeNode[]) => {
  for (let i of resource) {
    i.key = i.id;
    if (i.children && i.children.length > 0) {
      generateKey(i.children);
    }
  }
};
const searchTrees = (applicationCode?: string) => {
  resourceApi
    .searchTrees({
      applicationCode: applicationCode,
      parentId: 0,
      //"types": [ResourceTypeConstant.PAGE.type, ResourceTypeConstant.PAGE_MENU.type, ResourceTypeConstant.PAGE_GROUP.type, ResourceTypeConstant.WIDGET.type, ResourceTypeConstant.MANAGE_APPLICATION.type]
    })
    .then((it) => {
      resource.value = [
        {
          id: 0,
          name: 'ROOT',
          type: 0,
          children: [...it],
        },
      ];
      generateKey(resource.value);
    });
};

const selectedApplicationKeys = ref();
const selectApplicationCode = ref();
const selectApplicationName = ref();
const applicationList = ref<IApplicationInfo[] | undefined>([]);
applicationApi.list({}).then((it) => {
  if (it && it.records && it.records.length > 0) {
    applicationList.value = it.records;
    onSelectApplication({ key: applicationList.value[0].id });
    selectedApplicationKeys.value = [it.records[0].id];
    searchTrees(it.records[0].applicationCode);
  }
});
// 选择应用
const onSelectApplication = (obj: any) => {
  if (applicationList.value) {
    for (let i of applicationList.value) {
      if (obj.key === i.id) {
        selectApplicationCode.value = i.applicationCode;
        selectApplicationName.value = i.applicationName;
        searchTrees(selectApplicationCode.value);
      }
    }
  }
};

const onSelect: TreeProps['onSelect'] = (selectedKeys, info) => {
  selectTreeItem(info.node.dataRef as IResourceInfoTreeResponse);
};
const selectedTreeKeys = ref();

// 用于展示与修改的
const treeItemForm = ref<IResourceInfoTreeResponse>({ id: 0, name: 'ROOT' });
// 选中的
const selectedTreeItem = ref<IResourceInfoTreeResponse>({});
// 当选中某一节点时的父节点
const parentTreeItem = ref<IResourceInfoTreeResponse>({ id: 0, name: 'ROOT' });

// 新增相关
// 新增到之前
const beforeNew = (treeItem: IResourceInfoTreeResponse) => {
  saveTitle.value = '新增到之前';
  if (treeItem.sort != undefined && treeItem.sort != null) {
    const sort = treeItem.sort;
    let newSort = sort;
    // 查找比当前值小的最大值
    if (parentTreeItem.value.children) {
      for (let i of parentTreeItem.value.children) {
        if (newSort !== sort) {
          if (i.sort != undefined && i.sort != null && i.sort < sort && i.sort > newSort) {
            newSort = i.sort;
          }
        } else {
          if (i.sort != undefined && i.sort != null && i.sort < newSort) {
            newSort = i.sort;
          }
        }
      }
    }
    if (newSort !== sort) {
      const step = parseInt(((sort - newSort) / 2).toFixed(0));
      console.log('step', step);
      if (step <= 1) {
        // 小于1 需要重新排序
      }
      newTreeItemForm.value.sort = sort - step;
    } else {
      newTreeItemForm.value.sort = sort - 1000;
    }
  } else {
    newTreeItemForm.value.sort = 0;
  }
  newTreeItemForm.value.parentId = parentTreeItem.value.id;
  newTreeItemForm.value.applicationCode = selectApplicationCode.value;
  newTreeItemForm.value.applicationName = selectApplicationName.value;
  newTreeItemForm.value.priority = 100;
  visibleNewItem.value = true;
};
// 新增到之中
const inNew = (treeItem: IResourceInfoTreeResponse) => {
  saveTitle.value = '插入子节点';
  if (treeItem.children && treeItem.children?.length > 0) {
    let max = 0;
    for (let i of treeItem.children) {
      if (i.sort && i.sort > max) {
        max = i.sort;
      }
    }
    newTreeItemForm.value.sort = max + 1000;
  } else {
    newTreeItemForm.value.sort = 0;
  }
  newTreeItemForm.value.parentId = treeItem.id;
  newTreeItemForm.value.applicationCode = selectApplicationCode.value;
  newTreeItemForm.value.applicationName = selectApplicationName.value;
  newTreeItemForm.value.priority = 100;
  visibleNewItem.value = true;
};
// 新增到之后
const afterNew = (treeItem: IResourceInfoTreeResponse) => {
  saveTitle.value = '新增到之后';
  if (treeItem.sort != undefined && treeItem.sort != null) {
    const sort = treeItem.sort;
    let newSort = sort;
    // 查找比当前值大的最小值
    if (parentTreeItem.value.children) {
      for (let i of parentTreeItem.value.children) {
        if (newSort !== sort) {
          if (i.sort != undefined && i.sort != null && i.sort > sort && i.sort < newSort) {
            newSort = i.sort;
          }
        } else {
          if (i.sort != undefined && i.sort != null && i.sort > newSort) {
            newSort = i.sort;
          }
        }
      }
    }
    if (newSort !== sort) {
      const step = parseInt(((newSort - sort) / 2).toFixed(0));
      console.log('step', step);
      if (step <= 1) {
        // 小于1 需要重新排序
      }
      newTreeItemForm.value.sort = sort + step;
    } else {
      newTreeItemForm.value.sort = sort + 1000;
    }
  } else {
    newTreeItemForm.value.sort = 0;
  }
  newTreeItemForm.value.parentId = parentTreeItem.value.id;
  newTreeItemForm.value.applicationCode = selectApplicationCode.value;
  newTreeItemForm.value.applicationName = selectApplicationName.value;
  newTreeItemForm.value.priority = 100;
  visibleNewItem.value = true;
};

const newTreeItemForm = ref<IResourceInfoTreeResponse>({ keepAlive: 1 });
const visibleNewItem = ref<boolean>(false);
const confirmLoading = ref();
const saveTitle = ref('');
const onContextMenuClick = (key: number, treeItem: IResourceInfoTreeResponse) => {
  newTreeItemForm.value = {};
  if (key === 1) {
    beforeNew(treeItem);
  } else if (key === 2) {
    inNew(treeItem);
  } else if (key === 3) {
    afterNew(treeItem);
  } else if (key === 4) {
    visibleDeleteItem.value = true;
  }
};

const treeForm = ref();
const handleOk = () => {
  treeForm.value.validate().then(() => {
    confirmLoading.value = true;
    resourceApi
      .save({
        name: newTreeItemForm.value.name,
        keepAlive: newTreeItemForm.value.keepAlive,
        description: newTreeItemForm.value.description,
        url: newTreeItemForm.value.url,
        sort: newTreeItemForm.value.sort,
        position: newTreeItemForm.value.position,
        type: newTreeItemForm.value.type,
        applicationCode: newTreeItemForm.value.applicationCode,
        applicationName: newTreeItemForm.value.applicationName,
        priority: newTreeItemForm.value.priority,
        parentId: newTreeItemForm.value.parentId,
        treePath: newTreeItemForm.value.treePath,
        menuIcon: newTreeItemForm.value.menuIcon,
      })
      .then((it) => {
        message.success('操作成功！');
        visibleNewItem.value = false;
        searchTrees(selectApplicationCode.value);
        newTreeItemForm.value = {};
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  });
};

const labelCol = { span: 5 };
const wrapperCol = { span: 14 };
const selectTreeItem = (data: IResourceInfoTreeResponse) => {
  selectedTreeItem.value = data;
  const recursionResult = recursionResource(data.parentId || 0, resource.value);
  parentTreeItem.value = recursionResult ? recursionResult : parentTreeItem.value;
  treeItemForm.value = lodash.cloneDeep(selectedTreeItem.value);
};

// 重置修改中资源内容
const resetTreeItemForm = () => {
  treeItemForm.value = lodash.cloneDeep(selectedTreeItem.value);
};
const recursionResource = (
  parentId: number,
  resource: IResourceInfoTreeResponse[],
): IResourceInfoTreeResponse | undefined => {
  let result: IResourceInfoTreeResponse | undefined = {};
  let resultFlag = false;
  for (let i of resource) {
    if (i.id === parentId) {
      result = i;
      resultFlag = true;
    }
  }
  if (resultFlag) {
    return result;
  }
  for (let i of resource) {
    if (i.children && i.children.length > 0) {
      result = recursionResource(parentId, i.children);
      if (result) {
        return result;
      }
    }
  }
};

// 删除相关
const generateDeleteIds = (data: IResourceInfoTreeResponse[]): (number | undefined)[] => {
  const result = [];
  for (let i of data) {
    result.push(i.id);
    if (i.children && i.children.length > 0) {
      result.push(...generateDeleteIds(i.children));
    }
  }
  return result;
};

const visibleDeleteItem = ref<boolean>(false);
const deleteLoading = ref();
const handleDeleteOk = () => {
  deleteLoading.value = true;
  const ids = generateDeleteIds([selectedTreeItem.value]);
  resourceApi
    .delete({
      ids: ids,
    })
    .then((it) => {
      message.success('删除成功！');
      visibleDeleteItem.value = false;
      searchTrees(selectApplicationCode.value);
    })
    .finally(() => {
      deleteLoading.value = false;
    });
};
// 修改相关
const formConfirm = () => {
  return resourceApi
    .batchUpdate([
      {
        id: treeItemForm.value.id,
        name: treeItemForm.value.name,
        keepAlive: treeItemForm.value.keepAlive,
        description: treeItemForm.value.description,
        url: treeItemForm.value.url,
        sort: treeItemForm.value.sort,
        position: treeItemForm.value.position,
        type: treeItemForm.value.type,
        applicationCode: treeItemForm.value.applicationCode,
        applicationName: treeItemForm.value.applicationName,
        priority: treeItemForm.value.priority,
        parentId: treeItemForm.value.parentId,
        treePath: treeItemForm.value.treePath,
        menuIcon: treeItemForm.value.menuIcon,
      },
    ])
    .then((it) => {
      message.success('更新成功！');
      searchTrees(selectApplicationCode.value);
    });
};

const formCancel = () => {
  console.log('formCancel!', selectedTreeItem.value);
};

const visibleChange = (obj: any) => {
  selectedTreeKeys.value = [obj.node.key];
  selectTreeItem(obj.node.dataRef);
};

</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%">
    <h-modal
      v-model:open="visibleDeleteItem"
      title="删除资源"
      :confirm-loading="deleteLoading"
      @ok="handleDeleteOk"
      ok-text="删除"
      ok-type="danger"
    >
      <div style="text-align: center">确认删除资源“{{ selectedTreeItem.name }}”?</div>
    </h-modal>
    <h-modal v-model:open="visibleNewItem" :title="saveTitle" :confirm-loading="confirmLoading" @ok="handleOk">
      <h-form ref="treeForm" :model="newTreeItemForm" :label-col="labelCol" :wrapper-col="wrapperCol">
        <h-form-item label="父资源">
          <h-input :value="parentTreeItem.name" :disabled="true" />
        </h-form-item>
        <h-form-item label="资源类型" name="type" :rules="[{ required: true, message: '请选择资源类型!' }]">
          <h-select ref="select" v-model:value="newTreeItemForm.type" style="width: 100%">
            <h-select-option :value="ResourceTypeConstant.PAGE.type"
              >{{ ResourceTypeConstant.PAGE.name }}
            </h-select-option>
            <h-select-option :value="ResourceTypeConstant.PAGE_MENU.type"
              >{{ ResourceTypeConstant.PAGE_MENU.name }}
            </h-select-option>
            <h-select-option :value="ResourceTypeConstant.PAGE_GROUP.type"
              >{{ ResourceTypeConstant.PAGE_GROUP.name }}
            </h-select-option>
            <h-select-option :value="ResourceTypeConstant.WIDGET.type"
              >{{ ResourceTypeConstant.WIDGET.name }}
            </h-select-option>
            <h-select-option :value="ResourceTypeConstant.INTERFACE.type"
              >{{ ResourceTypeConstant.INTERFACE.name }}
            </h-select-option>
            <h-select-option :value="ResourceTypeConstant.MANAGE_APPLICATION.type"
              >{{ ResourceTypeConstant.MANAGE_APPLICATION.name }}
            </h-select-option>
            <h-select-option :value="ResourceTypeConstant.APPLICATION.type"
              >{{ ResourceTypeConstant.APPLICATION.name }}
            </h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="资源名称" name="name" :rules="[{ required: true, message: '请输入资源名称!' }]">
          <h-input v-model:value="newTreeItemForm.name" />
        </h-form-item>

        <h-form-item label="排序" name="sort">
          <h-input v-model:value="newTreeItemForm.sort" :disabled="true" />
        </h-form-item>
        <template v-if="newTreeItemForm.type === ResourceTypeConstant.PAGE_GROUP.type">
          <h-form-item label="图标" name="position">
            <IconSelector v-model:iconName="newTreeItemForm.menuIcon" />
          </h-form-item>
        </template>
        <template v-if="newTreeItemForm.type === ResourceTypeConstant.PAGE.type">
          <h-form-item label="URL" name="url" :rules="[{ required: true, message: '请填写资源URL!' }]">
            <h-input v-model:value="newTreeItemForm.url" />
          </h-form-item>
          <h-form-item label="目标构件" name="position" :rules="[{ required: true, message: '请填写目标构件路径!' }]">
            <h-input v-model:value="newTreeItemForm.position" />
          </h-form-item>
          <h-form-item label="缓存" name="keepAlive" :rules="[{ required: true, message: '请选择是否开启缓存!' }]">
            <h-select ref="select" v-model:value.prop="newTreeItemForm.keepAlive" style="width: 100%" allow-clear>
              <h-select-option :value="1">需要</h-select-option>
              <h-select-option :value="0">不需要</h-select-option>
            </h-select>
          </h-form-item>
        </template>
        <template v-if="newTreeItemForm.type === ResourceTypeConstant.PAGE_MENU.type">
          <h-form-item label="URL" name="url" :rules="[{ required: true, message: '请填写资源URL!' }]">
            <h-input v-model:value="newTreeItemForm.url" />
          </h-form-item>
          <h-form-item label="目标构件" name="position" :rules="[{ required: true, message: '请填写目标构件路径!' }]">
            <h-input v-model:value="newTreeItemForm.position" />
          </h-form-item>
          <h-form-item label="图标" name="position" :rules="[{ required: true, message: '请选择图标!' }]">
            <IconSelector v-model:iconName="newTreeItemForm.menuIcon" />
          </h-form-item>
          <h-form-item label="缓存" name="keepAlive" :rules="[{ required: true, message: '请选择是否开启缓存!' }]">
            <h-select ref="select" v-model:value.prop="newTreeItemForm.keepAlive" style="width: 100%" allow-clear>
              <h-select-option :value="1">需要</h-select-option>
              <h-select-option :value="0">不需要</h-select-option>
            </h-select>
          </h-form-item>
        </template>
        <template v-if="newTreeItemForm.type === ResourceTypeConstant.INTERFACE.type">
          <h-form-item label="URL" name="url" :rules="[{ required: true, message: '请填写资源URL!' }]">
            <h-input v-model:value="newTreeItemForm.url" />
          </h-form-item>
        </template>
        <template v-if="newTreeItemForm.type === ResourceTypeConstant.WIDGET.type">
          <h-form-item label="组件名称" name="url" :rules="[{ required: true, message: '请填写组件名称!' }]">
            <h-input v-model:value="newTreeItemForm.url" />
          </h-form-item>
        </template>
        <template v-if="newTreeItemForm.type === ResourceTypeConstant.MANAGE_APPLICATION.type">
          <h-form-item label="URL" name="url" :rules="[{ required: true, message: '请填写资源URL!' }]">
            <h-input v-model:value="newTreeItemForm.url" />
          </h-form-item>
          <h-form-item label="目标构件" name="position" :rules="[{ required: true, message: '请填写目标构件路径!' }]">
            <h-input v-model:value="newTreeItemForm.position" />
          </h-form-item>
        </template>
        <template v-if="newTreeItemForm.type === ResourceTypeConstant.APPLICATION.type">
          <h-form-item label="URL" name="url" :rules="[{ required: true, message: '请填写资源URL!' }]">
            <h-input v-model:value="newTreeItemForm.url" />
          </h-form-item>
        </template>
        <h-form-item label="资源描述" name="description">
          <h-textarea v-model:value="newTreeItemForm.description" />
        </h-form-item>
      </h-form>
    </h-modal>
    <h-row style="height: 100%">
      <h-col :span="3" style="height: 100%">
        <div style="height: 100%">
          <div
            style="
              height: 40px;
              border-right: 10px solid #f0f2f5;
              padding: 5px 20px 10px 20px;
              font-size: 18px;
              font-weight: 600;
              border-bottom: 1px solid #f0f0f0;
            "
          >
            应用
          </div>
          <div style="height: calc(100% - 40px); border-right: 10px solid #f0f2f5; overflow-y: auto">
            <h-menu
              v-if="applicationList"
              v-model:selectedKeys="selectedApplicationKeys"
              @click="onSelectApplication"
              style="height: 100%; width: 99%; border: none; overflow-x: hidden; overflow-y: auto"
              mode="inline"
            >
              <h-menu-item :key="i.id" v-for="i of applicationList">
                {{ i.applicationName }}
              </h-menu-item>
            </h-menu>
          </div>
        </div>
      </h-col>
      <h-col :span="13" style="height: 100%">
        <div
          style="
            height: 40px;
            border-right: 10px solid #f0f2f5;
            padding: 5px 20px 10px 20px;
            font-size: 18px;
            font-weight: 600;
            border-bottom: 1px solid #f0f0f0;
          "
        >
          <h-row>
            <h-col :span="4" style="font-size: 18px">资源树</h-col>
            <h-col :span="20" class="hint">
              <span class="hint-icon">
                <ExclamationCircleOutlined />
              </span>
              <span>选择资源右键进行操作</span>
            </h-col>
          </h-row>
        </div>
        <div style="height: calc(100% - 40px); border-right: 10px solid #f0f2f5; overflow-y: auto; padding: 15px">
          <h-tree
            v-if="resource.length"
            :defaultExpandAll="true"
            :show-line="{ showLeafIcon: false }"
            :show-icon="true"
            :tree-data="resource as DataNode[]"
            @select="onSelect"
            @rightClick="visibleChange"
            v-model:selectedKeys="selectedTreeKeys"
          >
            <template #icon="{ dataRef }">
              <template v-if="dataRef.type === 0">
                <HomeOutlined />
              </template>
              <template v-if="dataRef.type === ResourceTypeConstant.INTERFACE.type"
                ><!-- 接口 -->
                <NodeExpandOutlined />
              </template>
              <template v-if="dataRef.type === ResourceTypeConstant.PAGE.type"
                ><!-- 页面 -->
                <FileOutlined />
              </template>
              <template v-if="dataRef.type === ResourceTypeConstant.PAGE_MENU.type"
                ><!-- 页面(菜单) -->
                <FileTextOutlined />
              </template>
              <template v-if="dataRef.type === ResourceTypeConstant.PAGE_GROUP.type"
                ><!-- 页面组 -->
                <FolderOutlined />
              </template>
              <template v-if="dataRef.type === ResourceTypeConstant.WIDGET.type"
                ><!-- 组件 -->
                <UngroupOutlined />
              </template>
              <template v-if="dataRef.type === ResourceTypeConstant.MANAGE_APPLICATION.type"
                ><!-- 应用（管理） -->
                <HolderOutlined />
              </template>
              <template v-if="dataRef.type === ResourceTypeConstant.APPLICATION.type"
                ><!-- 应用 -->
                <AppstoreOutlined />
              </template>
            </template>
            <template #title="{ dataRef }">
              <h-dropdown :trigger="['contextmenu']" placement="bottomLeft">
                <span>{{ dataRef.name }}</span>
                <template #overlay>
                  <h-menu @click="({ key: menuKey }) => onContextMenuClick(menuKey as number, dataRef)">
                    <template v-if="dataRef.type === 0">
                      <h-menu-item :key="2"> <EnterOutlined />&nbsp;&nbsp;&nbsp;插入子节点&nbsp;&nbsp; </h-menu-item>
                    </template>
                    <template v-if="dataRef.type === ResourceTypeConstant.PAGE.type"
                      ><!-- 页面 -->
                      <h-menu-item :key="1"> <ArrowUpOutlined />&nbsp;&nbsp;&nbsp;新增到之前&nbsp;&nbsp; </h-menu-item>
                      <h-menu-item :key="2"> <EnterOutlined />&nbsp;&nbsp;&nbsp;插入子节点&nbsp;&nbsp; </h-menu-item>
                      <h-menu-item :key="3">
                        <ArrowDownOutlined />&nbsp;&nbsp;&nbsp;新增到之后&nbsp;&nbsp;
                      </h-menu-item>
                      <h-menu-item :key="4"> <DeleteOutlined />&nbsp;&nbsp;&nbsp;删除当前节点&nbsp;&nbsp; </h-menu-item>
                    </template>
                    <template v-if="dataRef.type === ResourceTypeConstant.PAGE_MENU.type"
                      ><!-- 页面(菜单) -->
                      <h-menu-item :key="1"> <ArrowUpOutlined />&nbsp;&nbsp;&nbsp;新增到之前&nbsp;&nbsp; </h-menu-item>
                      <h-menu-item :key="2"> <EnterOutlined />&nbsp;&nbsp;&nbsp;插入子节点&nbsp;&nbsp; </h-menu-item>
                      <h-menu-item :key="3">
                        <ArrowDownOutlined />&nbsp;&nbsp;&nbsp;新增到之后&nbsp;&nbsp;
                      </h-menu-item>
                      <h-menu-item :key="4"> <DeleteOutlined />&nbsp;&nbsp;&nbsp;删除当前节点&nbsp;&nbsp; </h-menu-item>
                    </template>
                    <template v-if="dataRef.type === ResourceTypeConstant.PAGE_GROUP.type"
                      ><!-- 页面组 -->
                      <h-menu-item :key="1"> <ArrowUpOutlined />&nbsp;&nbsp;&nbsp;新增到之前&nbsp;&nbsp; </h-menu-item>
                      <h-menu-item :key="2"> <EnterOutlined />&nbsp;&nbsp;&nbsp;插入子节点&nbsp;&nbsp; </h-menu-item>
                      <h-menu-item :key="3">
                        <ArrowDownOutlined />&nbsp;&nbsp;&nbsp;新增到之后&nbsp;&nbsp;
                      </h-menu-item>
                      <h-menu-item :key="4"> <DeleteOutlined />&nbsp;&nbsp;&nbsp;删除当前节点&nbsp;&nbsp; </h-menu-item>
                    </template>
                    <template v-if="dataRef.type === ResourceTypeConstant.WIDGET.type"
                      ><!-- 组件 -->
                      <h-menu-item :key="1"> <ArrowUpOutlined />&nbsp;&nbsp;&nbsp;新增到之前&nbsp;&nbsp; </h-menu-item>
                      <h-menu-item :key="2"> <EnterOutlined />&nbsp;&nbsp;&nbsp;插入子节点&nbsp;&nbsp; </h-menu-item>
                      <h-menu-item :key="3">
                        <ArrowDownOutlined />&nbsp;&nbsp;&nbsp;新增到之后&nbsp;&nbsp;
                      </h-menu-item>
                      <h-menu-item :key="4"> <DeleteOutlined />&nbsp;&nbsp;&nbsp;删除当前节点&nbsp;&nbsp; </h-menu-item>
                    </template>
                    <template v-if="dataRef.type === ResourceTypeConstant.INTERFACE.type"
                      ><!-- 接口 -->
                      <h-menu-item :key="1"> <ArrowUpOutlined />&nbsp;&nbsp;&nbsp;新增到之前&nbsp;&nbsp; </h-menu-item>
                      <h-menu-item :key="3">
                        <ArrowDownOutlined />&nbsp;&nbsp;&nbsp;新增到之后&nbsp;&nbsp;
                      </h-menu-item>
                      <h-menu-item :key="4"> <DeleteOutlined />&nbsp;&nbsp;&nbsp;删除当前节点&nbsp;&nbsp; </h-menu-item>
                    </template>
                    <template v-if="dataRef.type === ResourceTypeConstant.MANAGE_APPLICATION.type"
                      ><!-- 应用（管理） -->
                      <h-menu-item :key="1"> <ArrowUpOutlined />&nbsp;&nbsp;&nbsp;新增到之前&nbsp;&nbsp; </h-menu-item>
                      <h-menu-item :key="2"> <EnterOutlined />&nbsp;&nbsp;&nbsp;插入子节点&nbsp;&nbsp; </h-menu-item>
                      <h-menu-item :key="3">
                        <ArrowDownOutlined />&nbsp;&nbsp;&nbsp;新增到之后&nbsp;&nbsp;
                      </h-menu-item>
                      <h-menu-item :key="4"> <DeleteOutlined />&nbsp;&nbsp;&nbsp;删除当前节点&nbsp;&nbsp; </h-menu-item>
                    </template>
                    <template v-if="dataRef.type === ResourceTypeConstant.APPLICATION.type"
                      ><!-- 应用 -->
                      <h-menu-item :key="1"> <ArrowUpOutlined />&nbsp;&nbsp;&nbsp;新增到之前&nbsp;&nbsp; </h-menu-item>
                      <h-menu-item :key="2"> <EnterOutlined />&nbsp;&nbsp;&nbsp;插入子节点&nbsp;&nbsp; </h-menu-item>
                      <h-menu-item :key="3">
                        <ArrowDownOutlined />&nbsp;&nbsp;&nbsp;新增到之后&nbsp;&nbsp;
                      </h-menu-item>
                      <h-menu-item :key="4"> <DeleteOutlined />&nbsp;&nbsp;&nbsp;删除当前节点&nbsp;&nbsp; </h-menu-item>
                    </template>
                  </h-menu>
                </template>
              </h-dropdown>
            </template>
            <template #switcherIcon="{ switcherCls }">
              <down-outlined :class="switcherCls" />
            </template>
          </h-tree>
        </div>
      </h-col>
      <h-col :span="8" style="height: 100%">
        <div
          style="
            height: 40px;
            border-right: 10px solid #f0f2f5;
            padding: 5px 20px 10px 20px;
            font-size: 18px;
            font-weight: 600;
            border-bottom: 1px solid #f0f0f0;
          "
        >
          资源详情
        </div>
        <div style="height: calc(100% - 40px); border-right: 10px solid #f0f2f5; overflow-y: auto; padding: 40px">
          <h-form :model="treeItemForm" :label-col="labelCol" :wrapper-col="wrapperCol">
            <template v-if="treeItemForm.id != 0">
              <h-form-item label="父资源">
                <h-input :value="parentTreeItem.name" :disabled="true" />
              </h-form-item>
              <h-form-item label="资源类型" name="type" :rules="[{ required: true, message: '请选择资源类型!' }]">
                <h-select ref="select" v-model:value="treeItemForm.type" style="width: 100%">
                  <h-select-option :value="ResourceTypeConstant.PAGE.type"
                    >{{ ResourceTypeConstant.PAGE.name }}
                  </h-select-option>
                  <h-select-option :value="ResourceTypeConstant.PAGE_MENU.type"
                    >{{ ResourceTypeConstant.PAGE_MENU.name }}
                  </h-select-option>
                  <h-select-option :value="ResourceTypeConstant.PAGE_GROUP.type"
                    >{{ ResourceTypeConstant.PAGE_GROUP.name }}
                  </h-select-option>
                  <h-select-option :value="ResourceTypeConstant.WIDGET.type"
                    >{{ ResourceTypeConstant.WIDGET.name }}
                  </h-select-option>
                  <h-select-option :value="ResourceTypeConstant.INTERFACE.type"
                    >{{ ResourceTypeConstant.INTERFACE.name }}
                  </h-select-option>
                  <h-select-option :value="ResourceTypeConstant.MANAGE_APPLICATION.type"
                    >{{ ResourceTypeConstant.MANAGE_APPLICATION.name }}
                  </h-select-option>
                  <h-select-option :value="ResourceTypeConstant.APPLICATION.type"
                    >{{ ResourceTypeConstant.APPLICATION.name }}
                  </h-select-option>
                </h-select>
              </h-form-item>

              <h-form-item label="资源名称" name="name" :rules="[{ required: true, message: '请输入资源名称!' }]">
                <h-input v-model:value="treeItemForm.name" />
              </h-form-item>

              <h-form-item label="排序" name="sort">
                <h-input v-model:value="treeItemForm.sort" :disabled="true" />
              </h-form-item>

              <template v-if="treeItemForm.type === ResourceTypeConstant.PAGE_GROUP.type">
                <h-form-item
                  label="图标"
                  name="menuIcon"
                >
                  <IconSelector v-model:iconName="treeItemForm.menuIcon" />
                </h-form-item>
              </template>
              <template v-if="treeItemForm.type === ResourceTypeConstant.PAGE.type">
                <h-form-item label="URL" name="url" :rules="[{ required: true, message: '请填写资源URL!' }]">
                  <h-input v-model:value="treeItemForm.url" />
                </h-form-item>
                <h-form-item
                  label="目标构件"
                  name="position"
                  :rules="[{ required: true, message: '请填写目标构件路径!' }]"
                >
                  <h-input v-model:value="treeItemForm.position" />
                </h-form-item>
                <h-form-item
                  label="缓存"
                  name="keepAlive"
                  :rules="[{ required: true, message: '请选择是否开启缓存!' }]"
                >
                  <h-select ref="select" v-model:value.prop="treeItemForm.keepAlive" style="width: 100%" allow-clear>
                    <h-select-option :value="1">需要</h-select-option>
                    <h-select-option :value="0">不需要</h-select-option>
                  </h-select>
                </h-form-item>
              </template>
              <template v-if="treeItemForm.type === ResourceTypeConstant.PAGE_MENU.type">
                <h-form-item label="URL" name="url" :rules="[{ required: true, message: '请填写资源URL!' }]">
                  <h-input v-model:value="treeItemForm.url" />
                </h-form-item>
                <h-form-item
                  label="目标构件"
                  name="position"
                  :rules="[{ required: true, message: '请填写目标构件路径!' }]"
                >
                  <h-input v-model:value="treeItemForm.position" />
                </h-form-item>
                <h-form-item
                  label="图标"
                  name="menuIcon"
                >
                  <IconSelector v-model:iconName="treeItemForm.menuIcon" />
                </h-form-item>
                <h-form-item
                  label="缓存"
                  name="keepAlive"
                  :rules="[{ required: true, message: '请选择是否开启缓存!' }]"
                >
                  <h-select ref="select" v-model:value.prop="treeItemForm.keepAlive" style="width: 100%" allow-clear>
                    <h-select-option :value="1">需要</h-select-option>
                    <h-select-option :value="0">不需要</h-select-option>
                  </h-select>
                </h-form-item>
              </template>
              <template v-if="treeItemForm.type === ResourceTypeConstant.INTERFACE.type">
                <h-form-item label="URL" name="url" :rules="[{ required: true, message: '请填写资源URL!' }]">
                  <h-input v-model:value="treeItemForm.url" />
                </h-form-item>
              </template>
              <template v-if="treeItemForm.type === ResourceTypeConstant.MANAGE_APPLICATION.type">
                <h-form-item label="URL" name="url" :rules="[{ required: true, message: '请填写资源URL!' }]">
                  <h-input v-model:value="treeItemForm.url" />
                </h-form-item>
                <h-form-item
                  label="目标构件"
                  name="position"
                  :rules="[{ required: true, message: '请填写目标构件路径!' }]"
                >
                  <h-input v-model:value="treeItemForm.position" />
                </h-form-item>
              </template>
              <template v-if="treeItemForm.type === ResourceTypeConstant.APPLICATION.type">
                <h-form-item label="URL" name="url" :rules="[{ required: true, message: '请填写资源URL!' }]">
                  <h-input v-model:value="treeItemForm.url" />
                </h-form-item>
                <h-form-item
                  label="目标构件"
                  name="position"
                  :rules="[{ required: true, message: '请填写目标构件路径!' }]"
                >
                  <h-input v-model:value="treeItemForm.position" />
                </h-form-item>
              </template>
              <template v-if="treeItemForm.type === ResourceTypeConstant.WIDGET.type">
                <h-form-item label="组件名称" name="url" :rules="[{ required: true, message: '请填写组件名称!' }]">
                  <h-input v-model:value="treeItemForm.url" />
                </h-form-item>
              </template>
              <h-form-item label="资源描述" name="description">
                <h-textarea v-model:value="treeItemForm.description" />
              </h-form-item>
            </template>
            <template v-else>
              <h-form-item label="资源名称">
                <h-input v-model:value="treeItemForm.name" />
              </h-form-item>
            </template>
            <h-form-item :wrapper-col="{ span: 14, offset: 5 }">
              <h-popconfirm title="确认提交?" @confirm="formConfirm" @cancel="formCancel">
                <h-button type="primary">提交</h-button>
              </h-popconfirm>
              <h-button style="margin-left: 10px" @click="resetTreeItemForm">重置</h-button>
            </h-form-item>
          </h-form>
        </div>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.hint {
  padding-top: 6px;
  font-size: 10px;
  font-weight: 400;
  color: red;
  text-align: right;

  .hint-icon {
    font-size: 13px;
    font-weight: 700;
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}
</style>
