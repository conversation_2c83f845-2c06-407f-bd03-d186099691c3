import {
  IPayHeader,
  IPayRequest,
  IPayResponse,
  ITravelCoinHaierAccountResponse,
  ITravelCoinHaierBalanceResponse,
  ITravelCoinHaierPayRequest,
} from '@haierbusiness-front/common-libs';
import { errorHttpMessageHandle, get, post } from '../request';

export const travelCoinHaierPayApi = {
  /**
   * 支付
   */
  pay: (params: ITravelCoinHaierPayRequest): Promise<IPayResponse> => {
    return post('pay/api/travelcoin/haier/pay', params);
  },

  /**
   * 查询福利积分账户信息
   */
  searchAccount: (): Promise<ITravelCoinHaierAccountResponse> => {
    return get('pay/api/travelcoin/haier/account', undefined, undefined, (error) => {
      return errorHttpMessageHandle(error);
    });
  },

  /**
   * 查询福利积分余额
   */
  searchBalance: (): Promise<ITravelCoinHaierBalanceResponse> => {
    return get('pay/api/travelcoin/haier/balance');
  },

  /**
   * 发送支付验证码
   */
  sendCaptcha: (params: IPayRequest, header: IPayHeader): Promise<string> => {
    return post('pay/api/travelcoin/haier/sms/captcha', params, {
      'hb-nonce': header.nonce,
      'hb-timestamp': header.timestamp,
      'hb-sign': header.sign,
      'hb-application-code': header.applicationCode,
      'hb-excludes': header.excludes,
    });
  },
};
