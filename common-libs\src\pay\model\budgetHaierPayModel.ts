import {IPayData} from "./basicModel";
import {IPayRequest} from "./payModel";


export interface IBudgetHaierTypesRequest {
    /**
     * 应用code
     */
    applicationCode?: string;

    /**
     * 业务子编码
     */
    businessType?: string
}

export interface IBudgetHaierXw{
  pageNum?: number;
  pageSize?: number;
}

export interface IBudgetHaierTypesResponse {
    /**
     * 应用code
     */
    applicationCode?: string;
    budgetType?: string;
    budgetTypeName?: string;
    budgetTypeDesc?: string;
}


export interface IHaierQueryBudgetInfoRequest {
    /**
     * 应用code
     */
    estimatorCode?: string;
    /**
     * 系统来源编码 BCC/GEMS/RRSGEMS
     */
    itemCode?: string;
}
// 响应接口
export interface QueryBudgetInfoRes {
      /*预算主体编码 */
      budgetOrganization: string;
  
      /*预算主体名称 */
      budgetOrganizationName: string;
  
      /*受益主体编码 */
      beneficialOrganization: string;
  
      /*受益主体名称 */
      beneficialOrganizationName: string;
  
      /*法人编码 */
      legalPerson: string;
  
      /*法人名称 */
      legalPersonName: string;
  
      /*成本中心 */
      costCenter: string;
  
      /*成本中心名称 */
      costCenterName: string;
  
      /*执行主体编码 */
      performCode: string;
  
      /*执行主体名称 */
      performName: string;
    
  }
// 响应接口
export interface QueryApproveDetailRes {
    /* */
    data: {
      /*申请人工号 */
      applicantCode: string;
  
      /*申请人姓名 */
      applicantName: string;
  
      /*系统来源 */
      applicationName: string;
  
      /*预算系统 */
      budgetSysCode: string;
  
      /*业务订单号 */
      businessOrderNo: string;
  
      /*支付订单号 */
      payCode: string;
  
      /*申请金额 */
      applyAmount: number;
  
      /*预算类型 */
      feeItemName: string;
  
      /*预算主体 */
      budgetDepartmentName: string;
  
      /*执行主体 */
      performName: string;
  
      /*受益主体 */
      beneficialName: string;
  
      /*出账法人（结算单位名称） */
      legalPersonName: string;
    };
  
    /* */
    code: string;
  
    /* */
    message: string;
  
    /* */
    success: boolean;
  }

export interface IHaierBudgetFeeItemQueryRequest {
    /**
     * 应用code
     */
    applicationCode?: string;
    /**
     * 系统来源编码 BCC/GEMS/RRSGEMS
     */
    budgetSysCode?: string;

    // 子编码
    businessType?: string
}

export interface IHaierApplicationBudgetFeeItemInfo {
    id?: number
    applicationCode?: string
    /**
     * 预算类型
     */
    budgetType?: string
    /**
     * item_code
     */
    itemCode?: string
    /**
     * item_name
     */
    itemName?: string
}

export interface IBudgetHaierQueryRequest {
    /**
     * 预算类型
     */
    haierBudgetType?: string;
    /**
     * 应用code
     */
    applicationCode?: string;
    /**
     * 预算人
     */
    budgeterCode?: string;
    /**
     * 预算部门编码
     */
    budgetDepartmentCode?: string;
    /**
     * 费用项目编码
     */

    feeItem?: string;
}

export interface IBudgetHaierQueryResponse {
    /**
     * 应用code
     */
    applicationCode?: string;
    /**
     * 剩余预算
     **/
    leftAmt?: number;

    /**
     * 费用项目编码
     */
    feeItem?: string;
    feeItemName?: string;

    /**
     * 用户在GEMS所属部门 -- 预算部门
     **/
    entityCode?: string;

    entityName?: string;

    /**
     * 部门所属预算单元名称
     **/
    unitCode?: string;

    unitName?: string;

    /**
     * 用户所属付款公司编码 -- 结算单位
     **/
    companyCode?: string;

    companyName?: string;
}

export interface IBudgetHaierOccupyRequest extends IPayRequest {
    /**
     * 应用code
     */
    haierBudgetType?: string;

    /**
     * 预算人工号
     */
    budgeterCode?: string;

    budgeterName?: string;
    /**
     * 预算部门
     */
    budgetDepartmentCode?: string;

    budgetDepartmentName?: string;

    /**
     * 结算单位
     */
    accountCompanyCode?: string;

    accountCompanyName?: string;

    /**
     * 研发项目
     */
    projectCode?: string;

    projectName?: string;

    /**
     * WBS
     */
    wbsCode?: string;

    wbsName?: string;

    /**
     * 立项
     */
    itemCode?: string;

    itemName?: string;

    /**
     * 费用项目编码
     */
    feeItem?: string;

    /**
     * 费用项目
     */
    feeItemName?: string;


    /**
     * 销售类型,1：内销；2：外销；
     */
    saleType?: number;

    /**
     * 预算单元-gems
     */
    unitCode?: string;

    /**
     * 预算单元名称
     */
    unitName?: string;

    /**
     * 地产项目
     */
    dcProjectCode?: string;

    dcProjectName?: string;

    /**
     * 地产分期
     */
    dcItemCode?: string | null;

    dcItemName?: string;

    /**
     * 执行主体
     */
    performCode?:string;
    performName?:string;

    /**
     * 受益主体
     */

    beneficialCode?:string;
    beneficialName?:string;

    /**
     * 成本中心
     */

    costCenter?:string;
    costCenterName?:string;
    extJsonParam?:string;
    
    /**
     * 财务组织
     */
    financialCode?: string
    financialName?: string

    /**
     * hbc预算经理
     */
    budgetManager?: string

    /**
     * 是否按部门占用预算 0否 1是
     */
    isQueryDept?: number

    /**
     * 月度账户id（hbc预算查询返回）
     */
    accountCode?: string

    /**
     * 业务子类型
     */
    businessType?: string

    /**
     * 系统编码
     */
    budgetSystemCode?: string

    processId?: string

    startApproveFlag?: string

    enterpriseCode?: string
}

export interface IHaierPaymentBudget {
    id?: number


    /**
     * 支付单号
     */
    paymentCode?: string

    /**
     * 预算系统单号
     */
    budgetCode?: string

    /**
     * 预算来源，不同企业预算来源不同，海尔：DEPT_RRS_1；DEPT_BCC_1；
     */
    source?: string

    /**
     * 系统来源编码 BCC/GEMS/RRSGEMS
     */
    budgetSysCode?: string

    /**
     * 占用的预算金额，单位元
     */
    amount?: string

    /**
     * 类型
     * 1: 占用
     * 2: 确认
     */
    type?: number

    /**
     * 是否最后一次确认
     * 1:是
     * 0:否
     */
    lastTime?: boolean

    /**
     * 预算人工号
     */
    budgeterCode?: string

    /**
     * 预算人
     */
    budgeterName?: string

    /**
     * 业务申请人
     */
    applicantCode?: string

    applicantName?: string

    /**
     * 预算部门
     */
    budgetDepartmentCode?: string

    budgetDepartmentName?: string

    /**
     * 结算单位
     */
    accountCompanyCode?: string

    accountCompanyName?: string

    /**
     * 客户编码
     */
    customCode?: string

    customName?: string

    /**
     * 研发项目
     */
    projectCode?: string

    projectName?: string

    /**
     * 供应商
     */
    providerCode?: string
    providerOverseasCode?: string

    providerName?: string

    /**
     * WBS
     */
    wbsCode?: string

    wbsName?: string

    /**
     * 立项
     */
    itemCode?: string

    itemName?: string

    /**
     * 费用项目编码
     */
    feeItem?: string

    /**
     * 费用项目
     */
    feeItemName?: string


    /**
     * 销售类型,1：内销；2：外销；
     */
    saleType?: number

    /**
     * 预算单元-gems
     */
    unitCode?: string

    /**
     * 预算单元名称
     */
    unitName?: string

    /**
     * 地产项目
     */
    dcProjectCode?: string

    dcProjectName?: string

    /**
     * 地产分期
     */
    dcItemCode?: string

    dcItemName?: string

    /**
     * 业务单记录，通常是XXX订单
     */
    businessCode?: string

    /**
     * 发起支付应用编码
     */
    applicationCode?: string

    /**
     * 合同号
     */
    contractCode?: string

    contractName?: string

    gmtCreate?: string

    gmtModified?: string

    createBy?: string

    lastModifiedBy?: string
}

export interface IBudgetHaierHBC2QueryRequest {

    applicationCode?: string

    budgeterCode?: string

    budgetDepartmentCode?: string

    isQueryDept?: number

    haierBudgetType?: string

    businessType?: string
}

export interface IBudgetHaierHBC2Response {
    /* */
    leftAmt?: number

    /* */
    feeItem?: string

    /* */
    feeItemName?: string

    /* */
    entityCode?: string

    /* */
    entityName?: string

    /* */
    unitCode?: string

    /* */
    unitName?: string

    /* */
    companyCode?: string

    /* */
    companyName?: string

    /* */
    projectCode?: string

    /* */
    projectName?: string

    systemCode?: string

    accountCode?: string

    id?: number
}