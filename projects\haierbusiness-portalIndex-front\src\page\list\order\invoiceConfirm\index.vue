<template>
  <div class="page-container">
    <div class="content-wrapper">
      <!-- 发票上传信息区域 -->
      <div class="scheme_info">
        <div class="interact_mice_title">
          <div class="interact_mice_name_img mr8"></div>
          <div class="interact_mice_name mr24">
            {{ demandInfo.miceName || '' }}
          </div>
        </div>
        <div class="interact_mice_num mt12">
          <span class="mr10">会议编号：{{ demandInfo.mainCode }}</span>
          <img @click="getCopy(demandInfo.mainCode)" src="@/assets/image/scheme/copy_blue.png" width="16" />
        </div>

        <a-row class="interact_mice_info mt24">
          <a-col :span="12">
            <span class="mice_info_title mice_info_person_img">会议人数：</span>
            <span class="mice_info_value">
              {{ demandInfo.personTotal ? demandInfo.personTotal + '人' : '-' }}
            </span>
          </a-col>
          <a-col :span="12">
            <span class="mice_info_title mice_info_type_img">会议类型：</span>
            <span class="mice_info_value">
              {{ demandInfo.miceType ? MiceTypeConstant.ofType(demandInfo.miceType)?.desc || '' : '-' }}
            </span>
          </a-col>

          <a-col :span="12" class="mt12">
            <span class="mice_info_title mice_info_time_img">需求开始时间：</span>
            <span class="mice_info_value">
              {{ demandInfo.startDate || '' }}
            </span>
          </a-col>
          <a-col :span="12" class="mt12">
            <span class="mice_info_title mice_info_time_img">需求结束时间：</span>
            <span class="mice_info_value">
              {{ demandInfo.endDate || '' }}
            </span>
          </a-col>
        </a-row>
      </div>

      <!-- 模式切换按钮（仅用于测试）
      <div class="mode-switch" style="margin-bottom: 20px">
        <h-button :type="type === 1 ? 'primary' : 'default'" @click="type = 1" style="margin-right: 12px">
          发票确认模式
        </h-button>
        <h-button :type="type === 2 ? 'primary' : 'default'" @click="type = 2"> 退款确认模式 </h-button>
      </div> -->

      <!-- 平台发票上传表格 -->
      <div class="invoice-content">
        <!-- type = 1: 发票确认 -->
        <div v-if="type === 1" class="invoice-section">
          <h3 class="section-title">发票确认</h3>

          <!-- 联单总金额 -->
          <div class="total-amount-section">
            <span class="total-amount-label">联单总金额：</span>
            <span class="total-amount-value">{{ getTotalAmount() }}元</span>
          </div>

          <!-- 发票明细表格 -->
          <div class="invoice-detail-table">
            <div class="table-header">
              <div class="col-date">发票日期</div>
              <div class="col-number">发票号</div>
              <div class="col-amount">发票金额</div>
            </div>
            <div class="table-body">
              <div v-for="(item, index) in invoiceList" :key="item.key" class="table-row">
                <!-- 发票日期 -->
                <div class="col-date">
                  <span class="display-text">
                    {{ item.invoiceDate ? item.invoiceDate.format('YYYY.M.D') : '-' }}
                  </span>
                </div>
                <!-- 发票号 -->
                <div class="col-number">
                  <span class="display-text">
                    {{ item.invoiceNumber || '-' }}
                  </span>
                </div>
                <!-- 发票金额 -->
                <div class="col-amount">
                  <span class="display-text">
                    {{ item.invoiceAmount ? item.invoiceAmount.toLocaleString() : '-' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- type = 2: 退款确认 -->
        <div v-else-if="type === 2" class="refund-section">
          <h3 class="section-title">退款确认</h3>

          <div class="refund-content">
            <!-- 左侧：退款凭证 -->
            <div class="refund-voucher">
              <h4 class="voucher-title">退款凭证</h4>
              <div class="voucher-upload-area">
                <div class="upload-placeholder">
                  <div class="upload-icon">📄</div>
                  <div class="upload-text">点击上传退款凭证</div>
                  <div class="upload-hint">支持 PDF、JPG、PNG 格式，大小不超过 10MB</div>
                </div>
              </div>
            </div>

            <!-- 右侧：已上传文件回显 -->
            <div class="uploaded-files">
              <h4 class="files-title">已上传文件</h4>
              <div class="files-list">
                <div v-for="(file, index) in uploadedFiles" :key="index" class="file-item">
                  <div class="file-icon">📎</div>
                  <div class="file-info">
                    <div class="file-name">{{ file.name }}</div>
                    <div class="file-meta">
                      <span class="file-size">{{ formatFileSize(file.size) }}</span>
                      <span class="file-time">{{ file.uploadTime }}</span>
                    </div>
                  </div>
                  <div class="file-actions">
                    <button class="action-btn preview-btn" @click="previewFile(file)">预览</button>
                    <button class="action-btn delete-btn" @click="deleteFile(index)">删除</button>
                  </div>
                </div>

                <!-- 空状态 -->
                <div v-if="uploadedFiles.length === 0" class="empty-files">
                  <div class="empty-icon">📁</div>
                  <div class="empty-text">暂无已上传文件</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 固定底部按钮区域 -->
    <div class="footer-container">
      <h-button @click="handleReject" style="margin-right: 10px"> 驳回 </h-button>
      <h-button type="primary" @click="handleSubmit"> 提交 </h-button>
    </div>

    <!-- 驳回原因对话框 -->
    <h-modal v-model:open="rejectModalVisible" :title="title" :width="600" :footer="null">
      <div class="reject-form">
        <div class="form-item-horizontal">
          <label class="form-label-left">驳回原因：</label>
          <div class="form-input-right">
            <h-textarea
              v-model:value="rejectReason"
              placeholder="请输入驳回原因"
              :rows="4"
              :maxlength="200"
              show-count
            />
          </div>
        </div>

        <!-- 自定义按钮区域 -->
        <div class="modal-footer">
          <h-button @click="cancelReject" style="margin-right: 12px"> 取消 </h-button>
          <h-button type="primary" @click="confirmReject"> 提交 </h-button>
        </div>
      </div>
    </h-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import dayjs, { Dayjs } from 'dayjs';
import {
  Button as hButton,
  Input as hInput,
  InputNumber as hInputNumber,
  DatePicker as hDatePicker,
  Modal as hModal,
  message,
  Row as aRow,
  Col as aCol,
} from 'ant-design-vue';
const { TextArea: hTextarea } = hInput;
import { DeleteOutlined } from '@ant-design/icons-vue';
import { useRouter, useRoute } from 'vue-router';
import { DemandSubmitObj, ProcessNode, MiceTypeConstant, InvoiceConfirmParams, InvoiceConfirmStatusConstant } from '@haierbusiness-front/common-libs';
import { invoiceConfirmApi } from '@haierbusiness-front/apis';

// 发票项目接口
interface InvoiceItem {
  key: string;
  invoiceDate: Dayjs | null;
  invoiceNumber: string;
  invoiceAmount: number | null;
}

// 上传文件接口
interface UploadedFile {
  name: string;
  size: number;
  uploadTime: string;
  url?: string;
  type?: string;
}

const router = useRouter();
const route = useRoute();

// 页面类型：1-发票确认，2-退款确认
const type = ref(1);

// 复制文本到剪贴板
const getCopy = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    message.success('复制成功！');
  } catch (err) {
    message.success('复制失败');
  }
};

// 需求信息数据
const demandInfo = ref({
  miceName: '',
  mainCode: '',
  personTotal: null,
  miceType: null,
  startDate: '',
  endDate: '',
});

// 发票列表数据
const invoiceList = ref<InvoiceItem[]>([]);

// 驳回相关数据
const rejectModalVisible = ref(false);
const rejectReason = ref('');

// 退款相关数据
const uploadedFiles = ref<UploadedFile[]>([
  {
    name: '退款凭证_20250217.pdf',
    size: 1024 * 1024 * 2.5, // 2.5MB
    uploadTime: '2025-02-17 14:30:25',
    url: '/path/to/file1.pdf',
    type: 'pdf',
  },
  {
    name: '银行回单_20250216.jpg',
    size: 1024 * 512, // 512KB
    uploadTime: '2025-02-16 16:45:12',
    url: '/path/to/file2.jpg',
    type: 'image',
  },
]);

// 添加发票
const addInvoice = () => {
  const newKey = (invoiceList.value.length + 1).toString();
  invoiceList.value.push({
    key: newKey,
    invoiceDate: null,
    invoiceNumber: '',
    invoiceAmount: null,
  });
  message.success('已添加新发票行');
};

// 删除发票
const deleteInvoice = (index: number) => {
  invoiceList.value.splice(index, 1);
  message.success('发票已删除');
};

// 计算总金额
const getTotalAmount = () => {
  const total = invoiceList.value.reduce((sum, item) => {
    return sum + (item.invoiceAmount || 0);
  }, 0);
  return total.toLocaleString(); // 格式化数字，添加千分位分隔符
};

// 退款相关方法
// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 预览文件
const previewFile = (file: UploadedFile) => {
  console.log('预览文件:', file);
  message.info(`预览文件: ${file.name}`);
  // 这里可以实现文件预览逻辑
};

// 删除文件
const deleteFile = (index: number) => {
  const fileName = uploadedFiles.value[index].name;
  uploadedFiles.value.splice(index, 1);
  message.success(`已删除文件: ${fileName}`);
};

// 获取格式化的提交数据
const getSubmitData = () => {
  return {
    demandInfo: {
      miceName: demandInfo.value.miceName,
      mainCode: demandInfo.value.mainCode,
      personTotal: demandInfo.value.personTotal,
      miceType: demandInfo.value.miceType,
      startDate: demandInfo.value.startDate,
      endDate: demandInfo.value.endDate,
    },
    invoiceList: invoiceList.value.map((item, index) => ({
      序号: index + 1,
      发票日期: item.invoiceDate ? item.invoiceDate.format('YYYY-MM-DD') : '',
      发票号: item.invoiceNumber,
      发票金额: item.invoiceAmount,
      原始数据: {
        key: item.key,
        invoiceDate: item.invoiceDate,
        invoiceNumber: item.invoiceNumber,
        invoiceAmount: item.invoiceAmount,
      },
    })),
    统计信息: {
      发票总数: invoiceList.value.length,
      发票总金额: invoiceList.value.reduce((sum, item) => sum + (item.invoiceAmount || 0), 0),
      已填写完整的发票数: invoiceList.value.filter(
        (item) => item.invoiceDate && item.invoiceNumber && item.invoiceAmount,
      ).length,
    },
  };
};

// 获取提交数据 - 后端期望的对象格式
const getApiSubmitData = (): InvoiceConfirmParams => {
  // 获取路由参数中的 miceId 和 id
  const { miceId, id } = route.query;

  return {
    id: id ? parseInt(id as string) : 0, // 发票id
    miceId: miceId ? parseInt(miceId as string) : 0, // 会务订单id
  };
};

// 处理驳回操作
const handleReject = () => {
  rejectModalVisible.value = true;
  rejectReason.value = '';
};

// 确认驳回
const confirmReject = async () => {
  if (!rejectReason.value.trim()) {
    message.warning('请输入驳回原因');
    return;
  }

  try {
    const params = getApiSubmitData()
    // 这里调用驳回API
    await invoiceConfirmApi.submit(params);
    console.log('驳回原因:', rejectReason.value);
    message.success('驳回成功');
    rejectModalVisible.value = false;
    router.back();
  } catch (error) {
    console.error('驳回失败:', error);
    message.error('驳回失败，请重试');
  }
};

// 取消驳回
const cancelReject = () => {
  rejectModalVisible.value = false;
  rejectReason.value = '';
};

// 处理提交操作
const handleSubmit = async () => {
  // 获取路由参数验证
  const { miceId, id } = route.query;
  
  if (!id) {
    message.warning('缺少发票ID参数');
    return;
  }
  
  if (!miceId) {
    message.warning('缺少会务订单ID参数');
    return;
  }

  // 获取提交数据
  const submitData = getApiSubmitData();

  // 打印提交的数据结构
  console.log('=== 提交给后端的数据 ===');
  console.log('发票ID:', submitData.id);
  console.log('会务订单ID:', submitData.miceId);

  try {
    // 调用发票确认API
    const response = await invoiceConfirmApi.submit(submitData);

    if (response.success) {
      message.success('发票确认提交成功！');
      // 可以选择返回上一页或刷新数据
      router.back();
    } else {
      message.error(response.message || '提交失败，请重试');
    }
  } catch (error) {
    console.error('提交发票确认失败:', error);
    message.error('提交失败，请检查网络连接后重试');
  }
};

const title = ref('')

onMounted(() => {
  // 组件挂载后的初始化逻辑
  console.log('Invoice uploader mounted');

  // 获取路由参数
  const { mainCode, miceId, record, type: routeType} = route.query;

  // 设置页面类型
  if (routeType) {
    type.value = parseInt(routeType as string) || 1;
    title.value = type.value == 1 ? '发票驳回':'退款驳回'
  }

  // 如果有 record 参数，解析完整的订单数据
  if (record) {
    try {
      const orderData = JSON.parse(decodeURIComponent(record as string));
      console.log('获取到的完整订单数据:', orderData);

      // 使用订单数据初始化 demandInfo
      demandInfo.value = {
        miceName: orderData.miceName || '',
        mainCode: orderData.mainCode || '',
        personTotal: orderData.personTotal || null,
        miceType: orderData.miceType || null,
        startDate: orderData.startDate || '',
        endDate: orderData.endDate || '',
      };
    } catch (error) {
      console.error('解析订单数据失败:', error);
      // 如果解析失败，使用基本参数
      if (mainCode) {
        demandInfo.value.mainCode = mainCode as string;
      }
    }
  } else {
    // 如果没有 record，使用基本参数
    if (mainCode) {
      demandInfo.value.mainCode = mainCode as string;
    }
  }

  // 添加示例发票数据用于展示
  invoiceList.value = [
    {
      key: '1',
      invoiceDate: dayjs('2025-02-17'),
      invoiceNumber: '10010220',
      invoiceAmount: 100000,
    },
  ];

  console.log('初始化后的需求信息:', demandInfo.value);
});
</script>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #fff;
  position: relative;
}

.content-wrapper {
  flex: 1;
  padding: 20px;
  // overflow-y: auto;
  margin-bottom: 60px;
  /* 给底部按钮留出空间 */
}

.invoice-content {
  margin-top: 24px;
}

.invoice-section {
  background: #fff;
  border-radius: 6px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #1d2129;
  margin: 0 0 20px 0;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;
}

.invoice-table-container {
  margin-bottom: 16px;
}

.add-invoice-container {
  display: flex;
  justify-content: flex-start;
}

.add-invoice-btn {
  width: 200px;
  height: 40px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: all 0.3s;
}

.add-invoice-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

/* 联单总金额样式 */
.total-amount-section {
  margin-bottom: 20px;
  padding: 16px 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #1890ff;
}

.total-amount-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.total-amount-value {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

/* 发票明细表格样式 */
.invoice-detail-table {
  width: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  margin-bottom: 0;
  overflow: hidden;

  .table-header {
    display: flex;
    background-color: #f5f5f5;
    font-weight: 500;
    font-size: 14px;
    color: #333;
    border-bottom: 1px solid #e8e8e8;

    > div {
      padding: 12px 16px;
      text-align: center;
      border-right: 1px solid #e8e8e8;

      &:last-child {
        border-right: none;
      }
    }

    .col-date {
      flex: 1;
      min-width: 120px;
    }
    .col-number {
      flex: 1;
      min-width: 150px;
    }
    .col-amount {
      flex: 1;
      min-width: 120px;
    }
    .col-operation {
      width: 80px;
      flex-shrink: 0;
    }
  }

  .table-body {
    .table-row {
      display: flex;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      &.add-row {
        border-bottom: none;

        .add-button-full-width {
          width: 100%;
          padding: 12px 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 60px;
          cursor: pointer;
          border-bottom: none;

          &:hover {
            background-color: #f5f5f5;
          }

          .demand_add {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #1890ff;
            font-size: 14px;

            .demand_add_img {
              width: 16px;
              height: 16px;
              background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMVY4TTE1IDhIOE04IDE1VjhNMSA4SDgiIHN0cm9rZT0iIzE4OTBGRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+')
                no-repeat center;
              background-size: contain;
            }
          }
        }
      }

      > div {
        padding: 12px 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 50px;
        border-right: 1px solid #f0f0f0;

        &:last-child {
          border-right: none;
        }
      }

      .col-date {
        flex: 1;
        min-width: 120px;
      }
      .col-number {
        flex: 1;
        min-width: 150px;
      }
      .col-amount {
        flex: 1;
        min-width: 120px;
      }
      .col-operation {
        width: 80px;
        flex-shrink: 0;
      }
    }
  }
}

/* 表格输入框样式 */
.table-input {
  width: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 4px;

  &:focus,
  &:hover {
    border-color: #1890ff;
  }

  .ant-input {
    border: none;
    box-shadow: none;
    padding: 4px 8px;
  }

  .ant-picker-input > input {
    border: none;
    box-shadow: none;
    padding: 4px 8px;
  }

  .ant-input-number-input {
    border: none;
    box-shadow: none;
    padding: 4px 8px;
  }
}

.mr8 {
  margin-right: 8px;
}

/* 全局样式覆盖 */
:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-picker) {
  width: 100%;
}

/* 表格内输入框样式 */
:deep(.ant-table-tbody .ant-input) {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 4px 8px;
}

:deep(.ant-table-tbody .ant-input:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 表格行高度调整 */
:deep(.ant-table-tbody > tr > td) {
  padding: 8px 12px;
}

.footer-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 24px;
  border-top: 1px solid #f0f0f0;
  background-color: #fff;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

/* 发票上传信息区域样式 */
.scheme_info {
  padding: 24px 32px;
  width: 100%;
  height: 40%;
  background: url('@/assets/image/scheme/mice_bgc.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border-radius: 6px;

  .interact_mice_title {
    display: flex;
    align-items: center;

    .interact_mice_name_img {
      width: 28px;
      height: 28px;
      background-image: url('@/assets/image/scheme/mice_name.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .interact_mice_name {
      font-family: PingFangSCSemibold, PingFangSCSemibold;
      font-weight: normal;
      font-size: 20px;
      color: #1d2129;
      line-height: 28px;
    }

    .interact_mice_type {
      width: 108px;
      height: 28px;
      line-height: 28px;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #ffffff;
      background: #1868db;
      border-radius: 4px;
    }
  }

  .interact_mice_num {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #86909c;
    line-height: 20px;

    img {
      cursor: pointer;
    }
  }

  .interact_mice_info {
    width: 100%;
    font-size: 14px;
    color: #86909c;
    line-height: 20px;

    .mice_info_title {
      display: inline-block;
      text-indent: 26px;
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center left;
    }

    .mice_info_person_img {
      background-image: url('@/assets/image/scheme/mice_person.png');
    }

    .mice_info_type_img {
      background-image: url('@/assets/image/scheme/mice_type.png');
    }

    .mice_info_time_img {
      background-image: url('@/assets/image/scheme/mice_time.png');
    }

    .mice_info_value {
      color: #1d2129;
    }
  }
}

/* 通用样式类 */
.mt12 {
  margin-top: 12px;
}

.mt24 {
  margin-top: 24px;
}

.mr10 {
  margin-right: 10px;
}

.mr24 {
  margin-right: 24px;
}

/* 驳回对话框样式 */
.reject-form {
  padding: 20px 0;
}

.form-item-horizontal {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;
}

.form-label-left {
  width: 80px;
  flex-shrink: 0;
  font-size: 14px;
  color: #333;
  line-height: 32px;
  text-align: left;
}

.form-input-right {
  flex: 1;
  margin-left: 12px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
  margin-top: 20px;
  border-top: 1px solid #f0f0f0;
}

/* 显示文本样式 */
.display-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

/* 退款确认样式 */
.refund-section {
  background: #fff;
  border-radius: 6px;
  padding: 24px;
  padding-bottom: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.refund-content {
  display: flex;
  gap: 24px;
  margin-top: 20px;
}

.refund-voucher,
.uploaded-files {
  flex: 1;
  min-height: 400px;
}

.voucher-title,
.files-title {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.voucher-upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  padding: 40px 20px;
  text-align: center;
  background-color: #fafafa;
  transition: all 0.3s;
  cursor: pointer;
}

.voucher-upload-area:hover {
  border-color: #1890ff;
  background-color: #f0f8ff;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.upload-icon {
  font-size: 48px;
  color: #d9d9d9;
}

.upload-text {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

.upload-hint {
  font-size: 12px;
  color: #999;
}

.files-list {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  min-height: 300px;
  background-color: #fafafa;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fff;
  transition: all 0.3s;
}

.file-item:hover {
  background-color: #5faef7;
}

.file-item:last-child {
  border-bottom: none;
}

.file-icon {
  font-size: 20px;
  margin-right: 12px;
  color: #1890ff;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
  word-break: break-all;
}

.file-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #999;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.action-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.delete-btn:hover {
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.empty-files {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 14px;
}
</style>
