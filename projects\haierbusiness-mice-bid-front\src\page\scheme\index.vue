<script lang="ts" setup>
import Scheme from '@haierbusiness-front/components/mice/scheme/index.vue';
import SchemeView from '@haierbusiness-front/components/mice/scheme/view.vue';
import { useRoute } from 'vue-router';
const route = useRoute();
</script>

<template>
  <div>
    <SchemeView v-if="route.path === '/bidman/scheme/confirm/view'" orderSource="user"> </SchemeView>
    <Scheme v-if="route.path === '/bidman/scheme/confirm'" orderSource="user"> </Scheme>
  </div>
</template>
