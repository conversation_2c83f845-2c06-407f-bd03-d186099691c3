import { loadEnv } from 'vite'

import vue from '@vitejs/plugin-vue'
import path from 'path';

const CWD = process.cwd();

export default ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);

  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
    },
    plugins: [vue()],
    build:{
      target:['es2015']
    },
    server: {
      port: 5190,
      proxy: {
        "/hb/hotel-mapping": {
          target: "https://businessmanagement-test.haier.net/hbweb/hotel-mapping/hb/hotel-mapping",
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`/hb/hotel-mapping`), ''),
        },
        "/hb/common": {
          target: "https://businessmanagement-test.haier.net/hbweb/hotel-mapping/hb/common",
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`/hb/hotel-mapping`), ''),
        },
        "/hb": {
          target: "https://businessmanagement-test.haier.net/hbweb/hotel-analysis/hb",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb/, ""),
        },
      },
      
    }
  }
}
