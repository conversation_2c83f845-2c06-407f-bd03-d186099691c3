<script setup lang="ts">
// 票务预定
import {
  Form as hForm,
  FormItem as hFormItem,
  Select as hSelect,
  SelectOption as hSelectOption,
  Input as hInput,
  InputNumber as hInputNumber,
  DatePicker as hDatePicker,
  RangePicker as hRangePicker,
  Textarea as hTextarea,
  Button as hButton,
  Row as hRow,
  Col as hCol,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  Modal as hModal,
  Tooltip as hTooltip,
  Collapse as hCollapse,
  CollapsePanel as hCollapsePanel,
  Upload as hUpload,
  Spin as hSpin,
  Table as hTable,
  message,
  Upload,
} from 'ant-design-vue';
import { onMounted, ref, reactive, watch, defineProps, defineEmits, toRaw, defineExpose } from 'vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import dayjs, { Dayjs } from 'dayjs';
import type { UploadProps } from 'ant-design-vue';
import {
  TrafficTypeConstant,
  CityItem,
  TrafficsArr,
  trafficCalcsArr,
  trafficCalcDetailsArr,
} from '@haierbusiness-front/common-libs';

import { fileApi } from '@haierbusiness-front/apis';

import cityChose from '@haierbusiness-front/components/cityChose/index.vue';

const props = defineProps({
  cacheStr: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['demandTrafficFunc']);

const demandPlanFormRef = ref();
const uploadLoading = ref<boolean>(false);
const budgetFormRef = ref();
const isLt50M = ref<boolean>(true);

// 日程安排表单
const formState = reactive<TrafficsArr>({
  demandTotalPrice: '', // 需求总金额
  fileList: [],
  paths: [],

  calcTotalPrice: null, // 自动测算总价

  // 需求交通预算信息
  trafficCalcs: [],
});

// 需求交通预算信息
const trafficCalcsParams = ref<trafficCalcsArr>({
  miceDemandTrafficId: null, // 需求交通id
  totalPrice: null, // 总金额
  calcDate: null, // 测算时间

  // 需求交通预算测算信息
  trafficCalcDetails: [],
});
// 需求交通预算测算信息
const trafficCalcDetailsParams = ref<trafficCalcDetailsArr>({
  miceDemandTrafficId: null,
  miceDemandTrafficCalcId: null,
  type: null, // 交通类型
  numberCode: null, // 航班号/车次号
  departureCityId: null, // 出发地城市id
  departureCityName: '', // 出发地城市名称
  departureSiteCode: null, // 出发地机场/车站代码
  departureSiteName: null, // 出发地机场/车站名称

  arrivalCityId: null, // 到达地城市id
  arrivalCityName: null, // 到达地城市名称
  arrivalSiteCode: null, // 到达地机场/车站代码
  arrivalSiteName: null, // 到达地机场/车站名称

  departureDate: null, // 出发时间
  arrivalDate: null, // 到达时间

  num: null, // 行程数量
  unitPrice: null, // 行程单价
  totalPrice: null, // 总费用
});
let budgetForm = reactive<trafficCalcDetailsArr>({});

const budgetShow = ref<Boolean>(false); // 弹窗
const budgetAddShow = ref<Boolean>(false); // 弹窗
const budgetLoading = ref<Boolean>(false); //
const saveLoading = ref<Boolean>(false); //
const dataSource = ref<Array>([]);
const columns = [
  {
    title: '序号',
    dataIndex: 'serialNumber',
  },
  {
    title: '交通类型',
    dataIndex: 'type',
  },
  {
    title: '出发地',
    dataIndex: 'departureCityName',
  },
  {
    title: '目的地',
    dataIndex: 'arrivalCityName',
  },
  {
    title: '出行日期',
    dataIndex: 'departureDate',
  },
  {
    title: '出行人数',
    dataIndex: 'num',
  },
  {
    title: '预计投入金额',
    dataIndex: 'totalPrice',
  },
  {
    title: '操作',
    dataIndex: 'action_',
  },
  {
    title: '删除',
    dataIndex: 'del_',
  },
];

// 校验
const rules = {
  demandTotalPrice: [{ required: true, message: '请填写预计投入金额', trigger: 'change' }],
  fileList: [
    {
      required: true,
      message: '请上传系统外会议证明',
      trigger: 'blur',
    },
  ],
};
const budgetRules = {
  type: [{ required: true, message: '请选择交通类型', trigger: 'change' }],
  num: [{ required: true, message: '请填写出行人数', trigger: 'change' }],
  departureCityName: [{ required: true, message: '请选择出发城市', trigger: 'change' }],
  arrivalCityName: [{ required: true, message: '请选择到达城市', trigger: 'change' }],
  departureDate: [{ required: true, message: '请选择出行日期', trigger: 'change' }],
};

// 提交
const onSubmit = async () => {
  let isVerifyPassed = false;

  await demandPlanFormRef.value
    .validate()
    .then(() => {
      emit('demandTrafficFunc', { ...formState, trafficCalcDetails: [...dataSource.value] });

      isVerifyPassed = true;
    })
    .catch((err) => {
      isVerifyPassed = false;
    });

  return isVerifyPassed;
};

// 暂存
const tempSave = () => {
  emit('demandTrafficFunc', { ...formState, trafficCalcDetails: [...dataSource.value] });
};

defineExpose({ onSubmit, tempSave });

// 上传附件 - 删除
const handleRemove: UploadProps['onRemove'] = (file) => {
  formState.paths = [];

  formState.fileList.forEach((j) => {
    if (j.name !== file.name) {
      const params = {
        name: j.name,
        url: j.filePath,
      };

      formState.paths.push(JSON.stringify(params));
    }
  });

  console.log('%c [ 附件 ]-106', 'font-size:13px; background:pink; color:#bf2c9f;', formState);
};

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  isLt50M.value = file.size / 1024 / 1024 < 50;

  if (!isLt50M.value) {
    message.error('文件最大不超过50M！');
    return Upload.LIST_IGNORE;
  }

  return isLt50M.value;
};

// 上传附件
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;

      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框

      formState.paths = [];

      formState.fileList.forEach((j) => {
        const params = {
          name: j.name,
          url: j.filePath,
        };

        formState.paths.push(JSON.stringify(params));
      });

      console.log('%c [ 上传附件 ]-124', 'font-size:13px; background:pink; color:#bf2c9f;', formState);
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 交通预算测算
const calculationBtn = () => {
  budgetShow.value = true;

  // TODO - 接口
  // budgetLoading.value = true;
  // budgetLoading.value = false;
};
// 添加交通预算
const addBudget = () => {
  budgetAddShow.value = true;

  budgetForm = JSON.parse(JSON.stringify(trafficCalcDetailsParams));
};
// 添加交通预算
const budgetAddSub = () => {
  console.log('%c [ 添加交通预算 ]-242', 'font-size:13px; background:pink; color:#bf2c9f;', budgetForm);

  budgetFormRef.value
    .validate()
    .then(() => {
      dataSource.value.push(budgetForm);

      budgetAddShow.value = false;
    })
    .catch((err) => {});
};

// 交通预算计算器
const handleBudget = () => {
  saveLoading.value = true;
};
// 历史测算记录
const budgetHistory = () => {
  console.log('%c [ 历史测算记录 ]-214', 'font-size:13px; background:pink; color:#bf2c9f;', '历史测算记录');
};
const closeModal = () => {
  budgetShow.value = false;
  budgetAddShow.value = false;
};

// 选择出发城市
const chooseBeginCity = (city: CityItem) => {
  budgetForm.departureCityId = city.citycode;
  budgetForm.departureCityName = city.name;
  budgetFormRef.value.validateFields('departureCityName');
};
const chooseEndCity = (city: CityItem) => {
  budgetForm.arrivalCityId = city.citycode;
  budgetForm.arrivalCityName = city.name;
  budgetFormRef.value.validateFields('arrivalCityName');
};

// 不可选日期
const disabledDate = (current: Dayjs) => {
  // Can not select days before today and today
  return current && current < dayjs().endOf('day');
};

// 实时查询
const realTimeQuery = (obj: trafficCalcDetailsArr, index: Number) => {
  console.log('%c [ obj,index ]-292', 'font-size:13px; background:pink; color:#bf2c9f;', obj, index);
};
// 删除
const removeBudget = (index: Number) => {
  dataSource.value.splice(index, 1);
};

onMounted(async () => {
  // TODO
  dataSource.value = [
    {
      type: 1, // 交通类型
      numberCode: null, // 航班号/车次号
      departureCityId: null, // 出发地城市id
      departureCityName: '青岛', // 出发地城市名称
      departureSiteCode: null, // 出发地机场/车站代码
      departureSiteName: null, // 出发地机场/车站名称
      arrivalCityId: null, // 到达地城市id
      arrivalCityName: '北京', // 到达地城市名称
      arrivalSiteCode: null, // 到达地机场/车站代码
      arrivalSiteName: null, // 到达地机场/车站名称
      departureDate: '2025-04-16', // 出发时间
      arrivalDate: null, // 到达时间
      num: '10', // 行程数量
      unitPrice: null, // 行程单价
      totalPrice: null, // 总费用
    },
    {
      type: 0, // 交通类型
      numberCode: null, // 航班号/车次号
      departureCityId: null, // 出发地城市id
      departureCityName: '青岛', // 出发地城市名称
      departureSiteCode: null, // 出发地机场/车站代码
      departureSiteName: null, // 出发地机场/车站名称
      arrivalCityId: null, // 到达地城市id
      arrivalCityName: '北京', // 到达地城市名称
      arrivalSiteCode: null, // 到达地机场/车站代码
      arrivalSiteName: null, // 到达地机场/车站名称
      departureDate: '2025-04-17', // 出发时间
      arrivalDate: null, // 到达时间
      num: '10', // 行程数量
      unitPrice: null, // 行程单价
      totalPrice: null, // 总费用
    },
    {
      type: 0, // 交通类型
      numberCode: null, // 航班号/车次号
      departureCityId: null, // 出发地城市id
      departureCityName: '青岛', // 出发地城市名称
      departureSiteCode: null, // 出发地机场/车站代码
      departureSiteName: null, // 出发地机场/车站名称
      arrivalCityId: null, // 到达地城市id
      arrivalCityName: '北京', // 到达地城市名称
      arrivalSiteCode: null, // 到达地机场/车站代码
      arrivalSiteName: null, // 到达地机场/车站名称
      departureDate: '2025-04-18', // 出发时间
      arrivalDate: null, // 到达时间
      num: '10', // 行程数量
      unitPrice: null, // 行程单价
      totalPrice: null, // 总费用
    },
  ];

  // formState.demandTotalPrice = '198888';

  // 反显
  if (props.cacheStr) {
    const cacheObj = JSON.parse(props.cacheStr);

    if (cacheObj.traffic && Object.keys(cacheObj.traffic).length > 0) {
      formState.demandTotalPrice = cacheObj.traffic.demandTotalPrice;
      formState.calcTotalPrice = cacheObj.traffic.calcTotalPrice;
      formState.paths = cacheObj.traffic.paths || [];

      // 拓展活动-上传资料-反显

      if (formState.paths && formState.paths.length > 0) {
        formState.fileList = [];

        formState.paths.forEach((g) => {
          let gObj = {};
          let isJson = true;
          try {
            gObj = JSON.parse(g);
          } catch (error) {
            isJson = false;
          }

          if (!isJson) return;

          formState.fileList.push({ name: gObj.name, filePath: gObj.url });
        });
      }
    }
  }
});
</script>

<template>
  <!-- 票务预定 -->
  <div class="demand_ticket demand_pad24">
    <div class="demand_title">
      <div class="demand_border"></div>
      <span>票务预定</span>
    </div>

    <h-form
      class="mt20"
      ref="demandPlanFormRef"
      :model="formState"
      :labelCol="{ style: { width: '117px' } }"
      hideRequiredMark
      :rules="rules"
    >
      <div class="plan_col_list mb20">
        <div class="plan_col_title">交通</div>

        <h-row :gutter="16" class="mt20">
          <h-col :span="8">
            <h-form-item label="预计投入金额：" name="demandTotalPrice">
              <h-input-number
                v-model:value="formState.demandTotalPrice"
                @click="calculationBtn"
                placeholder="请填写预计投入金额"
                addon-after="/元"
                readonly
                allow-clear
                :min="1"
                :max="999999"
                :precision="2"
                style="width: 100%"
              />
            </h-form-item>
          </h-col>

          <h-col :span="16">
            <div class="calculation_flex">
              <div class="calculation_btn" @click="calculationBtn">
                <div class="calculation_btn_img mr10"></div>
                <span>交通预算测算</span>
              </div>

              <div v-show="formState.demandTotalPrice" class="calc_tip ml15">当前金额为全价票测算，非优惠价格</div>
            </div>
          </h-col>

          <h-col :span="16">
            <h-form-item label="系统外会议证明：" name="fileList">
              <h-upload
                v-model:fileList="formState.fileList"
                :custom-request="uploadRequest"
                :multiple="true"
                :max-count="10"
                :before-upload="beforeUpload"
                @remove="handleRemove"
              >
                <!-- accept=".rar, .zip, .pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx" -->
                <h-button>
                  <upload-outlined></upload-outlined>
                  上传附件
                </h-button>
              </h-upload>

              <div :class="['support_extend_tip', 'mt8', isLt50M ? '' : 'err_color']">
                <!-- 支持扩展名：.rar, .zip, .pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx -->
                文件最大不超过50M
              </div>
            </h-form-item>
          </h-col>
        </h-row>
      </div>
    </h-form>

    <!-- 交通预算测算 - 弹窗 -->
    <h-modal v-model:open="budgetShow" title="交通预算计算器" width="900px">
      <h-spin :spinning="budgetLoading">
        <h-table :data-source="dataSource" :columns="columns">
          <template #bodyCell="{ column, text, record, index }">
            <!-- 序号 -->
            <span v-if="column.dataIndex === 'serialNumber'">{{ index + 1 }}</span>
            <!-- 交通类型 -->
            <span v-if="column.dataIndex === 'type'">
              {{ TrafficTypeConstant.ofType(record.type)?.desc }}
            </span>

            <!-- 操作 -->
            <template v-if="column.dataIndex === 'action_'">
              <a @click="realTimeQuery(record, index)"> 实时查询 </a>
            </template>

            <!-- 删除 -->
            <template v-if="column.dataIndex === 'del_'">
              <div class="budget_del" @click="removeBudget(index)"></div>
            </template>
          </template>
        </h-table>
      </h-spin>

      <template #footer>
        <div class="budget_save">
          <div class="">
            <h-button type="dashed" @click="addBudget">增加行程</h-button>
          </div>
          <div class="">
            <span class="mr20" v-show="formState.demandTotalPrice">
              预计投入金额合计:{{ formState.demandTotalPrice }}元
            </span>
            <!-- <h-button @click="budgetHistory">历史测算记录</h-button>
            <h-button type="primary" :loading="saveLoading" @click="handleBudget">保存本次测算</h-button> -->
            <h-button @click="closeModal">关闭</h-button>
          </div>
        </div>
      </template>
    </h-modal>
    <!-- 交通预算测算 - 添加 - 弹窗 -->
    <h-modal
      v-model:open="budgetAddShow"
      title="交通预算添加"
      width="600px"
      @ok="budgetAddSub"
      @cancel="budgetAddShow = false"
    >
      <h-form class="mt24" ref="budgetFormRef" :model="budgetForm" :rules="budgetRules">
        <h-row :gutter="12">
          <h-col :span="24">
            <h-form-item label="交通类型：" name="type">
              <h-select v-model:value="budgetForm.type" placeholder="请选择交通类型" allow-clear>
                <h-select-option v-for="item in TrafficTypeConstant.toArray()" :key="item.code" :value="item.code">
                  {{ item.desc }}
                </h-select-option>
              </h-select>
            </h-form-item>
          </h-col>

          <h-col :span="24">
            <h-form-item label="出发地：" name="departureCityName">
              <city-chose
                placeholder="请选择出发城市"
                :value="budgetForm.departureCityName"
                :bordered="true"
                :showInternational="true"
                width="100%"
                @chosedCity="chooseBeginCity"
              ></city-chose>
            </h-form-item>
          </h-col>

          <h-col :span="24">
            <h-form-item label="目的地：" name="arrivalCityName">
              <city-chose
                placeholder="请选择到达城市"
                :value="budgetForm.arrivalCityName"
                :bordered="true"
                :showInternational="true"
                width="100%"
                @chosedCity="chooseEndCity"
              ></city-chose>
            </h-form-item>
          </h-col>

          <h-col :span="24">
            <h-form-item label="出行日期：" name="departureDate">
              <h-date-picker
                style="width: 200px"
                v-model:value="budgetForm.departureDate"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
              />
            </h-form-item>
          </h-col>

          <h-col :span="24">
            <h-form-item label="出行人数：" name="num">
              <h-input-number
                v-model:value="budgetForm.num"
                placeholder="请填写出行人数"
                allow-clear
                :min="1"
                :max="999999"
                style="width: 100%"
              />
            </h-form-item>
          </h-col>
        </h-row>
      </h-form>
    </h-modal>
  </div>
</template>

<style scoped lang="less">
.demand_ticket {
  .plan_col_list {
    padding: 20px 24px 0;
    background: #f6f9fc;
    border-radius: 8px;
    border: 1px solid #e5e6e8;

    position: relative;

    .plan_col_title {
      background: url('@/assets/image/demand/demand_traffic.png');
      background-repeat: no-repeat;
      background-size: 18px 18px;
      background-position: left center;

      text-indent: 28px;
      font-weight: 500;
      font-size: 18px;
      line-height: 25px;
      color: #1d2129;
    }

    .calculation_flex {
      display: flex;
      align-items: center;
      .calculation_btn {
        margin-top: -2px;
        width: 138px;
        height: 36px;
        background: linear-gradient(180deg, #35a1ef 0%, #1868db 100%);
        box-shadow: 0px 2px 8px 0px rgba(0, 103, 216, 0.1);
        border-radius: 4px;

        font-weight: 500;
        color: #ffffff;
        text-shadow: 0px 2px 8px rgba(0, 103, 216, 0.1);
        cursor: pointer;

        display: flex;
        justify-content: center;
        align-items: center;

        .calculation_btn_img {
          width: 14px;
          height: 20px;
          background: url('@/assets/image/demand/demand_calculation.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }
      }

      .calc_tip {
        color: #86909c;
        line-height: 22px;
      }
    }

    .support_extend_tip {
      color: #86909c;
      line-height: 22px;
    }

    .err_color {
      color: #ff4d4f;
    }
  }
}
</style>
<style>
.budget_save {
  display: flex;
  justify-content: space-between;
}
.budget_del {
  cursor: pointer;
  width: 16px;
  height: 16px;
  background: url('@/assets/image/demand/demand_del.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;

  &:hover {
    width: 16px;
    height: 16px;
    background: url('@/assets/image/demand/demand_del_red.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}
</style>
