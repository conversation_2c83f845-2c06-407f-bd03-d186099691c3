import { cacheApi } from '@haierbusiness-front/apis';

/**
 * 存储
 * type:local-本地, 不传默认走接口
 * params - 参数
 */
export const saveDataBy = async (params: any, type: string) => {
  if (type === 'local') {
    // 本地
    localStorage.setItem(params.key, params.cacheValue);

    return true;
  } else {
    // 接口
    // applicationCode - 应用标志 - 必传
    // key - 业务应用code - 必传
    // cacheValue - 缓存value - 必传
    // permissionCode - 权限code(可选) - 非必传
    // async - 是否开启异步保存 - 非必传
    // dataType - 数据结构, 默认为json, 可存储为json、xml等其他 - 非必传
    const res = await cacheApi.demandCommonCacheSave({ ...params });

    return res;
  }
};

/**
 * 取参
 * type:local-本地, 不传默认走接口
 * params - 参数
 */
export const getDataBy = async (params: any, type: string) => {
  if (type === 'local') {
    // 本地
    return localStorage.getItem(params.key);
  } else {
    // 接口
    // applicationCode - 应用标志 - 必传
    // key - 业务应用code - 必传
    // permissionCode - 权限code(可选) - 非必传
    const res = await cacheApi.demandCommonCacheQuery({ ...params });

    return res?.cacheValue;
  }
};

/**
 * 删除
 */
export const delData = async (params: any) => {
  // 删除本地
  localStorage.removeItem(params.key);

  // 接口 - 删除
  // applicationCode - 应用标志 - 必传
  // key - 业务应用code - 必传
  // permissionCode - 权限code(可选) - 非必传
  cacheApi.demandCommonCacheRemove({ ...params });
};
