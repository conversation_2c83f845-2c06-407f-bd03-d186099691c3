const modules = import.meta.glob('/src/page/**/*.vue');

import { baseRouterConstructor } from '@haierbusiness-front/utils';
const router = baseRouterConstructor("haierbusiness-process", modules).then(it => {
    it.addRoute({
        path: '/',
        redirect: '/index'
    })
    it.addRoute({
        path: '/index',
        component: () => import('../page/index.vue')
    })
    it.addRoute({
        path: '/details',
        component: () => import('../page/details.vue')
    })
    it.addRoute({
        path: '/detailsPcSt',
        component: () => import('../page/detailsPcSt.vue')
    })
    return it
})

export default router;
