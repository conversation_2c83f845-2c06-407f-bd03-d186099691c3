<script setup lang="ts">
import {
  BreadcrumbItem as hBreadcrumbItem,
  Breadcrumb as hBreadcrumb,
  LayoutSider as hLayoutSider,
  LayoutFooter as hLayoutFooter,
  Layout<PERSON>ontent as hLayoutContent,
  Layout<PERSON>eader as hLayoutHeader,
  RangePicker as hRangePicker,
  Layout as hLayout,
  MenuItem as hMenuItem,
  MenuItemGroup as hMenuItemGroup,
  SubMenu as hSubMenu,
  <PERSON>u as hMenu,
  Divider as hDivider,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Result as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Tabs as hTabs,
  message,
  RadioGroup as hRadioGroup,
  RadioButton as hRadioButton,
  Table as hTable,
  Modal as hModal,
  Tree as hTree,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Switch as hSwitch,
  Select  as hSelect,
  SelectOption as hSelectOption,
  Image as hImage,
  Progress as hProgress,
} from 'ant-design-vue';
import { onMounted, ref, computed, watch,createVNode,toRefs  } from 'vue';
import {
  UploadOutlined,
  UserOutlined,
  NotificationOutlined,
  AppstoreOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';
import EManage from '@haierbusiness-front/components/manange/EManange.vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { hotelApi,addressApi } from '@haierbusiness-front/apis';
import { GetMappingHotelListRes,HotelSyncRecordRes,GetHotelSyncRes,asyncStatus,supplierType } from '@haierbusiness-front/common-libs'
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute, getEnumOptions } from "@haierbusiness-front/utils";
import { DataType, usePagination, useRequest } from 'vue-request';
import type { TreeProps } from 'ant-design-vue';
const route = ref(getCurrentRoute());
const store = applicationStore();
const { resource } = storeToRefs(store);

const supplierTypeOptions = computed(()=>{
  return getEnumOptions(supplierType)
})

// 根据id 获取同步信息 getHotelSync
const info = ref<GetHotelSyncRes>({})
const getInfo = ()=>{
  hotelApi.getHotelSyncMappingRecord({hotelSyncId:route.value.query.id}).then((res:any)=>{
    info.value = res
  })
}
const addBoxShow = ref<boolean>(false)
const scoreBoxShow = ref<boolean>(false)
const hotelRow = ref<any>({})
const boxTitle = ref<string>('')
// 当前明细id
const openid = ref<string>("")
const hotelId = ref<string>("")
const addMapping= (row:HotelSyncRecordRes) =>{
    boxTitle.value = row.name
    openid.value = row.id
    hotelRow.value = row
    addBoxShow.value = true
    listHotelApiRun({
      ...searchHotelParam.value,
      pageNum: 1,
      pageSize:10,
    });
  }

// 打开评分弹窗
const showScoreBox= (row:HotelSyncRecordRes) =>{
    boxTitle.value = row.name
    openid.value = row.id
    // scoreBoxShow.value = true
    hotelId.value = row.hotelId
    searchScoreParam.value.hotelMappingRecordId = row.hotelMappingRecordId
    searchScoreParam.value.providerId = row.hotelId
    listHotelScoreApiRun({
      ...searchScoreParam.value,
      pageNum: 1,
      pageSize:10,
    });
  }

  // 酒店分页
const searchHotelParam = ref<any>({hotelSyncId:route.value.query.id })
const {
  data:hodelList,
  run: listHotelApiRun,
  loading:hodelListLoding,
  current:hodelcurrent,
  pageSize:hotelpageSize,
} = usePagination(hotelApi.getHotelList);

const dataHotelSource = computed(() => hodelList.value?.records || []);

const handleTableHotelChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listHotelApiRun({
    ...searchHotelParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const Hotelpagination = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: hodelList.value?.total,
    current: hodelList.value?.pageNum,
    pageSize: hodelList.value?.pageSize,
    style: { justifyContent: 'center' },
}));

const resetHotel = () =>{
  searchHotelParam.value = {
  }
  listHotelApiRun({
    ...searchHotelParam.value,
    pageNum: hodelcurrent.value,
    pageSize:hotelpageSize.value,
  });
}


// 评分记录分页
const searchScoreParam = ref<any>({ hotelSyncId:route.value.query.id})
const {
  data:hotelScoreList,
  run: listHotelScoreApiRun,
  loading:hotelScoreListLoding,
  current:hotelScorecurrent,
  pageSize:hotelScorepageSize,
} = usePagination(hotelApi.getHotelMergeRecordPage);

const dataHotelScoreSource = computed(() => hotelScoreList.value?.records || []);

const handleTableHotelScoreChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listHotelScoreApiRun({
    ...searchScoreParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};
const hotelScorepagination = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: hotelScoreList.value?.total,
    current: hotelScoreList.value?.pageNum,
    pageSize: hotelScoreList.value?.pageSize,
    style: { justifyContent: 'center' },
}));


const resetScore = () =>{
  searchScoreParam.value = {
    providerId:hotelId.value
  }
  listHotelScoreApiRun({
    ...searchScoreParam.value,
    pageNum: hotelScorecurrent.value,
    pageSize:hotelScorepageSize.value,
  });
}

  // 关联酒店
  const association = (row:any) =>{
    hotelApi.getMappingHotelByProviderCode({providerHotelCode:hotelRow.value.hotelId,providerCode:hotelRow.value.providerCode}).then(res=>{
      hModal.confirm({
      title:  res?`当前供应商酒店已经关联了${res.name}，确认关联会覆盖之前的关联，确定关联此酒店吗？`:'确定要关联此酒店吗？',
      icon: createVNode(ExclamationCircleOutlined),
      onOk() {
        return new Promise((resolve, reject) => {
          hotelApi.hotelMergeMappingHotel({hotelMergeId:openid.value,mappingHotelName:row.name,mappingHotelPhone:row.phone,mappingHotelAddress:row.address,mappingHitHotelId:row.code}).then((res:any)=>{
            message.success('关联成功')
            addBoxShow.value = false
            listApiRun({
                ...searchParam.value,
                pageNum: current.value,
                pageSize:pageSize.value,
              });
              expandedRow.value = []
            resolve()
          }).catch(()=>{
            resolve()
          })
        })
      },
      onCancel() {
        console.log('Cancel');
      },
    });
    })
  }

  
  // 关联酒店
  const associationIts = (row:any) =>{
    hotelApi.getMappingHotelByProviderCode({providerHotelCode:row.providerId,providerCode:row.providerCode}).then(res=>{
      hModal.confirm({
      title:res?`当前供应商酒店已经关联了${res.name}，确认关联会覆盖之前的关联，确定关联此酒店吗？`:'确定要关联此酒店吗？',
      icon: createVNode(ExclamationCircleOutlined),
      onOk() {
        return new Promise((resolve, reject) => {
            
          hotelApi.hotelMergeMappingHotel({hotelMergeId:openid.value,mappingHotelName:row.itsName,mappingHotelPhone:row.itsPhone,mappingHotelAddress:row.itsAddress,mappingHitHotelId:row.itsId}).then((res:any)=>{
            message.success('关联成功')
            scoreBoxShow.value = false
            listApiRun({
                ...searchParam.value,
                pageNum: current.value,
                pageSize:pageSize.value,
              });
              expandedRow.value = []
            resolve()
          }).catch(()=>{
            resolve()
          })
        })
      },
      onCancel() {
        console.log('Cancel');
      },
    });
    })
  }

  // 去除疑似
  const delSuspected = (row:any) =>{
      hModal.confirm({
      title:"去除之后将会添加到聚合失败列表，确定要去除此条疑似数据吗?",
      icon: createVNode(ExclamationCircleOutlined),
      onOk() {
        return new Promise((resolve, reject) => {
            
          hotelApi.removeSuspectedCases({hotelMergeId:row.id}).then((res:any)=>{
            message.success('去除成功')
            scoreBoxShow.value = false
            listApiRun({
                ...searchParam.value,
                pageNum: current.value,
                pageSize:pageSize.value,
              });
              expandedRow.value = []
            resolve()
          }).catch(()=>{
            resolve()
          })
        })
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  }


  const sumbit=()=>{
    hModal.confirm({
      title: '确定提交吗？',
      icon: createVNode(ExclamationCircleOutlined),
      onOk() {
        return new Promise((resolve, reject) => {
            
          hotelApi.hotelMappingRecordSubmit({}).then((res:any)=>{
            message.success('提交成功')
            resolve()
          }).catch(()=>{
            resolve()
          })
        })
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  }

const columns = [
  {
    title: '酒店名称',
    dataIndex: 'name',
    align:"center",
    width:"200px",
    ellipsis: true,
    key: 'name',
  }, 
   {
    title: '酒店编码',
    dataIndex: 'code',
    align:"center",
    ellipsis: true,
    width:"200px",
    key: 'name',
  },
  {
    title: '酒店地址',
    dataIndex: 'address',
    align:"center",
    width:"250px",
    ellipsis: true,
    key: 'address',
  },
  {
    title: '酒店电话',
    dataIndex: 'phone',
    align:"center",
    ellipsis: true,
    width:"250px",
    key: 'phone',
  },
  {
    title: '区域',
    dataIndex: 'regionName',
    align:"center",
    width:"120px",
    key: 'regionName',
    ellipsis: true,
  },
  {
    title: '品牌',
    dataIndex: 'brandName',
    align:"center",
    width:"120px",
    ellipsis: true,
    key: 'brandName',
  },
  {
    title: '经纬度',
    dataIndex: 'lon',
    align:"center",
    width:"250px",
    ellipsis: true,
    key: 'lon',
  },
  {
    title: '操作',
    dataIndex: '_operator',
    fixed: 'right',
    align: 'center',
    width: '200px',
  },
]; 

const columnsFormng = [
  {
    title: '供应商酒店名称',
    dataIndex: 'name',
    align:"center",
    key: 'name',
    width:"250px",
  },
  {
    title: '命中国旅酒店',
    dataIndex: 'yingshe',
    align:"center",
     width:"250px",
    key: 'yingshe',
  },
  {
    title: '映射成功条件',
    dataIndex: 'mergeSuccessCondition',
    align:"center",
    key: 'mergeSuccessCondition',
     width:"250px",
  },
  {
    title: '是否有疑似',
    dataIndex: 'suspectedFlag',
    key: 'suspectedFlag',
    align:"center",
    width:"100px",
  },
  {
    title: '地址',
    dataIndex: 'address',
    key: 'address',
    align:"center",
    width:"250px",
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    align:"center",
    width:"120px",
    key: 'phone',
  },
  {
    title: '品牌',
    dataIndex: 'brandName',
    key: 'brandName',
    align:"center",
    width:"120px",
  },
  {
    title: '城市',
    dataIndex: 'cityName',
    align:"center",
    key: 'cityName',
    width:"120px",
  },
  {
    title: '区域',
    dataIndex: 'regionName',
    align:"center",
    key: 'regionName',
    width:"120px",
  },
  // {
  //   title: '经纬度',
  //   dataIndex: 'jingweidu',
  //   align:"center",
  //   key: 'jingweidu',
  //   ellipsis: true,
  // },
  {
    title: '操作',
    dataIndex: '_operator',
    width:"260px",
    align:"center",
    fixed:"right",
    key: 'gdLon',
  }
]
// 判断评分
const getRowBackGroundNameScore=(row:any)=>{
  if(row.nameScore<0.75){
    return { class: "table-color-error" }
  }
}

const getRowBackGroundaddressScore=(row:any)=>{
  if(row.addressScore<0.75){
    return { class: "table-color-error" }
  }
}

// 评分列表
const columnsForScore:any = [
  {
    title: '酒店名称',
    dataIndex: 'name',
    width:"350px",
    align:"center",
    ellipsis: true,
    key: 'name',
  },
  {
    title: '名称得分',
    dataIndex: 'nameScore',
    key: 'nameScore',
    width:"60px",
    ellipsis: true,
    align:"center",
    customCell: getRowBackGroundNameScore
  },
  {
    title: '地址',
    dataIndex: 'address',
    key: 'address',
    align:"center",
    width:"350px",
    ellipsis: true,
  },
  {
    title: '地址得分',
    dataIndex: 'addressScore',
    align:"center",
    ellipsis: true,
    width:"60px",
    key: 'addressScore',
    customCell: getRowBackGroundaddressScore
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    align:"center",
    ellipsis: true,
    width:"200px",
    key: 'phone',
  },
  {
    title: '手机号得分',
    dataIndex: 'phoneScore',
    align:"center",
    width:"60px",
    ellipsis: true,
    key: 'phoneScore',
  },
  // 疑似原因 mergeSuccessCondition
  {
    title: '疑似原因',
    dataIndex: 'mergeSuccessCondition',
    align:"center",
    width:"320px",
    ellipsis: true,
    key: 'mergeSuccessCondition',
  },
  {
    title: '品牌',
    dataIndex: 'providerBrandName',
    key: 'providerBrandName',
    align:"center",
    width:"120px",
    ellipsis: true,
  },
  {
    title: '区域',
    dataIndex: 'itsRegionName',
    align:"center",
    key: 'itsRegionName',
    width:"120px",
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'mergeSuccessFlag',
    align:"center",
    key: 'mergeSuccessFlag',
      width:"120px",
    ellipsis: true,
  },
  {
    title: '经纬度',
    dataIndex: 'jingweidu',
    align:"center",
    key: 'jingweidu',
    width:"350px",
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width:"120px",
    align:"center",
    key: 'gdLon',
    fixed: 'right',
  }
]
const reset = () =>{
  searchParam.value = {
    hotelSyncId:route.value.query.id
  }
  listApiRun({
    ...searchParam.value,
    pageNum: current.value,
    pageSize:pageSize.value,
  });
}



// 酒店分页
const searchParam = ref<HotelSyncRecordRes>({
  hotelSyncId:route.value.query.id
})
const {
  data:dataList,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(hotelApi.gethotelMergePageList);

// const dataSource = computed(() => dataList.value?.records || []);
const dataSource = ref<any>([])
watch(dataList,(value)=>{
  dataSource.value = value.records
})
const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const pagination = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: dataList.value?.total,
    current: dataList.value?.pageNum,
    pageSize: dataList.value?.pageSize,
    style: { justifyContent: 'center' },
}));

// 获取国内城市下拉列表
const hotelList = ref<any>([])
const getDistrictList = ()=>{
  addressApi.getDistrictList({code:'CN',level:'city'}).then(res=>{
    hotelList.value = res.records
  })
}
const filterOption = (input: string, option: any) => {
  if(option.name){
    return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }else{
    return option.brandName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }
};

// 获取区域
const areaList = ref<any>([])
const getAreaList = (value:number)=>{
  addressApi.getDistrictList({code:'CN',level:'district',cityId:value}).then(res=>{
    areaList.value = res.records
  })
}

// 选择完城市 获取区域
const cityChange = (value:number) =>{
  console.log(value)
  if(value){
    getAreaList(value)
  }else{
    areaList.value = []
  }
}

// 获取品牌下拉列表
const BrandList = ref<any>([])
const getBrandList = ()=>{
  hotelApi.getHotelBrandProviderMapList({type:2}).then(res=>{
    BrandList.value = res.records
  })
}

const rowClassNameFun = (record,index) =>{
  if (record.suspectedFlag) {
        return 'table-color-dust'
    }
}

const searchList = (value:any)=>{
  expandedRow.value = []
  searchParam.value.sort = value
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize:10,
  });
}
// 展开tree
const onExpand = (expanded:boolean, record:any) =>{
  console.log(1111,2222)
    // 当节点展开时，如果没有加载子节点，则进行加载
    if(expandedRow.value.includes(record.id)){
      expanded = false
    }
    if (expanded) {
      // showScoreBox(record);
      onLoadData({ dataRef: record });
    }
    if (expanded) {
        expandedRow.value.push(record.id)
      } else {
        var expandedRowKeys = expandedRow.value.filter(RowKey => RowKey !== record.id)
        expandedRow.value = expandedRowKeys
      }
}
// 点击行
const getRowEvent  = (record:any) =>{
  return {
    onClick: () => {
        // 处理单击事件，例如：显示记录详情、修改记录等
        console.log('Clicked row:', record);
        onExpand(true,record)
        // 你可以在这里调用方法或进行其他操作
      },
    };
}
const numberkey = ref<any>("123645")
const expandedRow = ref<string[]>([])
const spinning = ref<boolean>(false)
const onLoadData: TreeProps['loadData'] = treeNode => {
  return new Promise<void>(resolve => {
    // console.log(treeNode,"treeNode")
    boxTitle.value = treeNode.dataRef.name
    openid.value = treeNode.dataRef.id
    // scoreBoxShow.value = true
    hotelId.value = treeNode.dataRef.hotelId
    searchScoreParam.value.hotelMappingRecordId = treeNode.dataRef.hotelMappingRecordId
    searchScoreParam.value.providerId = treeNode.dataRef.hotelId
    spinning.value = true
    hotelApi.getHotelMergeRecordPage({
      ...searchScoreParam.value,
      pageNum: 1,
      pageSize:10,
    }).then(res=>{
      console.log(res.records,"***----")
      treeNode.dataRef.childData = []
      res.records.forEach(item=>{
        item.childData = []
      })
      treeNode.dataRef.childData = res.records
      console.log(dataSource.value,"-------------")
      dataSource.value = [...dataSource.value];
      // numberkey.value = Math.floor(Math.random() * 1000000);
      console.log(treeNode.dataRef,"-------------")
      resolve()
      spinning.value = false
    })
  });
}

onMounted(()=>{
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize:10,
  });
  getInfo()
  getDistrictList()
  getBrandList()
})

</script>

<template>
  <div class="pageBox">
    <div class="headerBox">
      <h-form :labelCol="{span:6, offset: 1}" style="width:100%;">
        <h-row>
          <h-col :span="6">
            <h-form-item label="名称">
              <h-input allow-clear v-model:value="searchParam.name" placeholder="酒店名称" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="城市">
              <h-select
                ref="city"
                @change="cityChange"
                show-search
                :fieldNames="{label:'name',value:'id'}"
                :options="hotelList"
                :filter-option="filterOption"
                v-model:value="searchParam.cityId"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="区域">
              <h-select
                ref="city"
                :disabled="!searchParam.cityId"
                show-search
                :fieldNames="{label:'name',value:'id'}"
                :options="areaList"
                :filter-option="filterOption"
                v-model:value="searchParam.regionId"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="品牌">
              <h-select
                ref="city"
                show-search
                :fieldNames="{label:'brandName',value:'id'}"
                :options="BrandList"
                :filter-option="filterOption"
                v-model:value="searchParam.brandId"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
        </h-row>
        <h-row>
          <h-col :span="6">
            <h-form-item label="地址">
              <h-input allow-clear v-model:value="searchParam.address" placeholder="地址" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="电话">
              <h-input allow-clear v-model:value="searchParam.phone" placeholder="电话" />
            </h-form-item>
          </h-col>
          <!-- <h-col :span="6">
            <h-form-item label="合并状态">
              <h-select
                ref="select"
                v-model:value="searchParam.sort"
                style="width:100%;"
                allow-clear
              >
              <h-select-option :value="30">合并成功</h-select-option>
              <h-select-option :value="20">疑似合并</h-select-option>
              <h-select-option :value="10">合并失败</h-select-option>
              </h-select>
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="是否已聚合">
              <h-radio-group v-model:value="searchParam.mappingSuccessFlag">
                <h-radio-button :value="true">是</h-radio-button>
                <h-radio-button :value="false">否</h-radio-button>
              </h-radio-group>
            </h-form-item>
          </h-col> -->
        </h-row>
        <h-row>
        <h-col :span="20" style="text-align: left;line-height: 32px;font-weight:600;">
          <span :span="2">供应商:{{info.providerCodeName}}</span>
          <span style="margin-left:20px;" :span="4">开始时间：{{ info.startTime}}</span>
          <span style="margin-left:20px;" :span="4">结束时间：{{ info.startTime}}</span>
          <a-button color="default" @click="searchList()" style="margin-left:20px;color:blue;  cursor: pointer;" :span="4">聚合数量：{{ info.mappingNumber}}</a-button>
          <a-button color="default" @click="searchList(30)" style="margin-left:20px;color:green;  cursor: pointer;" :span="4">成功数量：{{ info.mappingSuccessNumber}}</a-button>
          <a-button color="default" @click="searchList(20)" style="margin-left:20px;color:#9e479f;  cursor: pointer;" :span="4">疑似数量：{{ info.mappingSuspectedNumber}}</a-button>
          <a-button color="default" @click="searchList(10)" style="margin-left:20px;color:red;  cursor: pointer;" :span="4">未聚合数量：{{ info.mappingFailNumber}}</a-button>
        </h-col>
          <h-col :span="4" style="text-align: right;">
            <!-- <h-button type="primary" style="margin-right: 10px" @click="sumbit">确认提交</h-button> -->
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-form>
      <!-- </h-row> -->
    </div>
    <div class="contentBox">
      <!--         :row-selection="rowSelection" -->
      <h-table
        :columns="columnsFormng"
        :size="'small'"
        :data-source="dataSource"
        :rowClassName="rowClassNameFun"
        :scroll="{ x: 1550,y:'calc(100vh - 440px)' }"
        :loading="loading"
        
        @expand="onExpand"
        :loadData="onLoadData"
        :expandedRowKeys="expandedRow"
        rowKey="id"
        :pagination="pagination"
        @change="handleTableChange($event as any)"
        :customRow="getRowEvent"
      >
        <template #bodyCell="{ column, record }">
          <template
            v-if="column.dataIndex === 'type'"
          >{{ record.type == 1?'新增':record.type==2?'酒店信息修改':'酒店信息删除' }}</template>
          <template v-if="column.dataIndex === 'jingweidu'">
            <p>经度:{{ record.longitude }}</p>
            <p>纬度:{{ record.latitude }}</p>
          </template>
          <template v-if="column.dataIndex === 'name'">
            <div :title="record.name" class="multi-line-ellipsis">
              {{ record.name }}
            </div>
          </template>
          <template v-if="column.dataIndex === 'address'">
            <div :title="record.address" class="multi-line-ellipsis">
              {{ record.address }}
            </div>
          </template>
          <template v-if="column.dataIndex === 'suspectedFlag'">
          <a-tag v-if="record.suspectedFlag" color="red">有疑似</a-tag>
          <a-tag v-else color="green">无</a-tag>
          </template>
          <template v-if="column.dataIndex === 'yingshe'">
            <div :title="record.mappingHotelName" class="multi-line-ellipsis">
              {{ record.mappingHotelName }}
            </div>
          </template>
          <template v-if="column.dataIndex === '_operator'">
            <h-button  v-if="record.suspectedFlag" @click.stop="delSuspected(record)" type="link">去除疑似</h-button>
            <h-button  v-if="!record.mergeSuccessFlag" @click.stop="addMapping(record)" type="link">关联</h-button>
            <h-button @click.stop="onExpand(true,record)" type="link">评分记录</h-button>
          </template>
        </template>
        <template #expandedRowRender="{record, index}">
          <a-spin v-if="record.childData&&record.childData.length" :spinning="spinning&&record.id==openid ">
            <a-card style="margin-bottom:10px;" v-for="(item,index) in record.childData" size="small">
              <!-- <template #extra><a v-if="!item.mergeSuccessFlag"  @click="associationIts(item)">关联</a></template> -->
              <!-- <a-descriptions size="small"  :labelStyle="{ width: '100px' }" :contentStyle="{ width: '200px' }" bordered>
                  <a-descriptions-item label="供应商酒店名称">{{ item.providerName }}</a-descriptions-item>
                  <a-descriptions-item label="国旅酒店名称">{{ item.itsName }}</a-descriptions-item>
                  <a-descriptions-item label="名称得分"><div class="score" :style="{color:item.nameScore<=0.75?'red':'green'}">{{ item.nameScore }}</div></a-descriptions-item>
                  <a-descriptions-item label="供应商酒店地址">{{ item.providerAddress }}</a-descriptions-item>
                  <a-descriptions-item label="国旅酒店地址">{{ item.itsAddress }}</a-descriptions-item>
                  <a-descriptions-item label="地址得分"><div class="score" :style="{color:item.addressScore<=0.75?'red':'green'}">{{ item.addressScore }}</div></a-descriptions-item>
                  <a-descriptions-item label="供应商酒店手机号">{{ item.providerPhone }}</a-descriptions-item>
                  <a-descriptions-item label="国旅酒店手机号">{{ item.itsPhone }}</a-descriptions-item>
                  <a-descriptions-item label="手机号得分"><div class="score" :style="{color:item.phoneScore<=0.75?'red':'green'}">{{ item.phoneScore }}</div></a-descriptions-item>
                  <a-descriptions-item  label="品牌">
                    {{ item.providerBrandName }}
                  </a-descriptions-item>
                  <a-descriptions-item  label="区域">
                    {{ item.itsRegionName }}
                  </a-descriptions-item>
                  <a-descriptions-item  label="状态">
                    <div>{{ item.mergeSuccessFlag?"聚合成功":"未聚合" }}</div>
                  </a-descriptions-item>
                  <a-descriptions-item :span="3" v-if="item.bdLon" label="经纬度"> 经度：{{item.bdLon}}   纬度：{{item.bdLat}}</a-descriptions-item>
                  <a-descriptions-item :span="3" label="疑似原因">
                    {{ item.suspectedRenark}}
                  </a-descriptions-item>
              </a-descriptions> -->
              <div class="tableHead">
                <div class="index">
                  {{ index+1 }} / {{ record.childData.length }}
                </div>
                <div class="mergeSuccessFlag">
                  <a-tag v-if="item.mergeSuccessFlag"  color="green">
                    {{ item.mergeSuccessFlag?"聚合成功":"未聚合" }}
                  </a-tag>
                  <a-tag v-else color="red">
                    {{ item.mergeSuccessFlag?"聚合成功":"未聚合" }}
                  </a-tag>
                </div>
                <div class="suspectedRemark">
                  <div v-if="item.mergeSuccessFlag">
                    {{ item.mergeSuccessCondition}}
                  </div>
                  <div v-else>
                    【疑似原因】 {{ item.suspectedRemark}}
                  </div>
                </div>
                <div class="btn">
                  <a-button v-if="!item.mergeSuccessFlag" size="small" type="primary" @click="associationIts(item)">关联</a-button>
                </div>
              </div>
              <table border="1"> 
                <thead> 
                  <!-- <div>
                    {{ item.mergeSuccessFlag?"聚合成功":"未聚合" }}
                    <span>
                      {{ item.suspectedRemark}}
                    </span>
                  </div> -->
                  <tr>
                    <td width="40"></td>
                    <th width="270">供应商信息</th>
                    <th width="270">国旅数据</th>
                    <th width="60">评分</th>
                  </tr>
                </thead>
                <tbody>
                  <tr style="display:none;">
                    <td width="60"></td>
                    <td width="250">供应商信息</td>
                    <td width="250">国旅数据</td>
                    <td width="60">评分</td>
                  </tr>
                  <tr>
                    <td style="text-align:center;font-weight: 900;">名称</td>
                    <td>{{ item.providerName }}</td>
                    <td>{{ item.itsName }}</td>
                    <td><div class="score" :style="{color:item.nameScore<=0.75?'red':'green'}">{{ item.nameScore }}</div></td>
                  </tr>
                  <tr>
                    <td style="text-align:center;font-weight: 900;">地址</td>
                    <td>{{ item.providerAddress }}</td>
                    <td>{{ item.itsAddress }}</td>
                    <td><div class="score" :style="{color:item.addressScore<=0.75?'red':'green'}">{{ item.addressScore }}</div></td>
                  </tr>
                  <tr>
                    <td style="text-align:center;font-weight: 900;">电话</td>
                    <td>{{ item.providerPhone }}</td>
                    <td>{{ item.itsPhone }}</td>
                    <td><div class="score" :style="{color:item.phoneScore<=0.75?'red':'green'}">{{ item.phoneScore }}</div></td>
                  </tr>
                  <tr>
                    <td style="text-align:center;font-weight: 900;">区域</td>
                    <td>{{ item.providerRegionName }}</td>
                    <td>{{ item.itsRegionName }}</td>
                    <td>/</td>
                  </tr>
                  <tr>
                    <td style="text-align:center;font-weight: 900;">品牌</td>
                    <td>{{ item.providerBrandName }}</td>
                    <td>{{ item.itsBrandName }}</td>
                    <td>/</td>
                  </tr>
                </tbody>
              </table>
            </a-card>
          </a-spin>
        </template>
      </h-table>
    </div>

    <!-- 关联 -->
    <h-modal
      width="1400px"
      v-model:open="addBoxShow"
      :title="'新增【'+boxTitle+'】的映射关系'"
      :footer="null"
      @ok="handleOk"
    >
      <h-form :labelCol="{span:5, offset: 1}">
        <h-row>
          <h-col :span="6">
            <h-form-item label="名称">
              <h-input v-model:value="searchHotelParam.name" placeholder="名称" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="城市">
              <h-select
                ref="city"
                @change="cityChange"
                show-search
                :fieldNames="{label:'name',value:'id'}"
                :options="hotelList"
                :filter-option="filterOption"
                v-model:value="searchHotelParam.cityId"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="区域">
              <h-select
                ref="city"
                :disabled="!searchHotelParam.cityId"
                show-search
                :fieldNames="{label:'name',value:'id'}"
                :options="areaList"
                :filter-option="filterOption"
                v-model:value="searchHotelParam.regionId"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="酒店编码">
              <h-input v-model:value="searchHotelParam.code" placeholder="酒店编码" />
            </h-form-item>
          </h-col>
        </h-row>
        <h-row>
          <h-col :span="6">
            <h-form-item label="地址">
              <h-input v-model:value="searchHotelParam.address" placeholder="地址" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="电话">
              <h-input v-model:value="searchHotelParam.phone" placeholder="电话" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="品牌">
              <h-select
                ref="city"
                show-search
                :fieldNames="{label:'brandName',value:'id'}"
                :options="BrandList"
                :filter-option="filterOption"
                v-model:value="searchHotelParam.brandId"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
        </h-row>
        <h-row>
          <h-col :span="24" style="text-align: right;margin-bottom: 10px">
            <h-button style="margin-right: 10px" @click="resetHotel">重置</h-button>
            <h-button type="primary" @click="handleTableHotelChange({ current: 1, pageSize: 10 })">
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-form>
      <div class="modalBox">
        <h-table
          :columns="columns"
          :size="'small'"
          :scroll="{ x: 1550,y:400 }"
          :data-source="dataHotelSource"
          :loading="hodelListLoding"
          :pagination="Hotelpagination"
          @change="handleTableHotelChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'image'">
              <h-image :width="200" :height="100" :src="record.image" />
            </template>
            <template v-if="column.dataIndex === 'phone'">
              <p style="word-break: break-all;">{{ record.phone }}</p>
            </template>
            <template v-if="column.dataIndex === 'lon'">
              <div
                v-if="record.gdLon"
              >经度：{{record.gdLon}} <br> 纬度：{{record.gdLat}}</div>
              <div
                v-else-if="record.gLon"
              >经度：{{record.gLon}} <br> 纬度：{{record.gLat}}</div>
              <div
                v-else-if="record.bdLon"
              >经度：{{record.bdLon}} <br> 纬度：{{record.bdLat}}</div>
            </template>
            <!-- 映射mappingHotelList -->
            <template v-if="column.dataIndex === 'mappingHotelList'">
              <p v-for="item in record.mappingHotelList">{{ item.providerCodeName }}:{{ item.name }}</p>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button  @click="association(record)" type="link">关联</h-button>
            </template>
          </template>
        </h-table>
      </div>
    </h-modal>
    <!-- 评分 -->
    <h-modal
      width="1600px"
      v-model:open="scoreBoxShow"
      :title="'【'+boxTitle+'】的聚合评分'"
      :footer="null"
    >
      <h-form :labelCol="{span:5, offset: 1}">
        <h-row>
          <!-- <h-col :span="6">
            <h-form-item label="供应商">
              <h-select ref="select" allow-clear v-model:value="searchScoreParam.providerCode">
                <h-select-option v-for="item in supplierTypeOptions" :value="item.value">{{item.label}}</h-select-option>
              </h-select>
            </h-form-item>
          </h-col> -->
          <h-col :span="6">
            <h-form-item label="名称">
              <h-input allow-clear v-model:value="searchScoreParam.providerName" placeholder="名称" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="城市">
              <h-select
                ref="city"
                @change="cityChange"
                show-search
                :fieldNames="{label:'name',value:'id'}"
                :options="hotelList"
                :filter-option="filterOption"
                v-model:value="searchScoreParam.providerCityId"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="区域">
              <h-select
                ref="city"
                :disabled="!searchScoreParam.providerCityId"
                show-search
                :fieldNames="{label:'name',value:'id'}"
                :options="areaList"
                :filter-option="filterOption"
                v-model:value="searchScoreParam.providerRegionId"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
            <h-col :span="6">
              <h-form-item label="地址">
                <h-input allow-clear v-model:value="searchScoreParam.providerAddress" placeholder="地址" />
              </h-form-item>
            </h-col>
        </h-row>
        <h-row>
          <!-- <h-col :span="6">
            <h-form-item label="酒店编码">
              <h-input allow-clear v-model:value="searchScoreParam.providerId" placeholder="酒店编码" />
            </h-form-item>
          </h-col> -->
          <h-col :span="6">
            <h-form-item label="电话">
              <h-input allow-clear v-model:value="searchScoreParam.providerPhone" placeholder="电话" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="品牌">
              <h-select
                ref="city"
                show-search
                :fieldNames="{label:'brandName',value:'id'}"
                :options="BrandList"
                :filter-option="filterOption"
                v-model:value="searchScoreParam.providerBrandId"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
        </h-row>
        <h-row>
          <h-col :span="24" style="text-align: right;margin-bottom: 10px">
            <h-button style="margin-right: 10px" @click="resetScore">重置</h-button>
            <h-button
              type="primary"
              @click="handleTableHotelScoreChange({ current: 1, pageSize: 10 })"
            >
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-form>
      <div class="modalBox">
        <h-table
          :columns="columnsForScore"
          :size="'small'"
          :rowClassName="rowClassNameFun"
          :scroll="{y:500}"
          :data-source="dataHotelScoreSource"
          :loading="hotelScoreListLoding"
          :pagination="hotelScorepagination"
          @change="handleTableHotelScoreChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'name'">
              <p :title="record.providerName" v-if="record.providerName">供应商：{{ record.providerName }}</p>
              <p :title="record.itsName" v-if="record.itsName">国旅：{{ record.itsName }}</p>
            </template>
            <template v-if="column.dataIndex === 'address'">
              <p :title="record.providerAddress" v-if="record.providerAddress">供应商：{{ record.providerAddress }}</p>
              <p :title="record.itsAddress" v-if="record.itsAddress">国旅：{{ record.itsAddress }}</p>
            </template>
            <template v-if="column.dataIndex === 'phone'">
              <p :title="record.providerPhone" v-if="record.providerPhone">供应商：{{ record.providerPhone }}</p>
              <p :title="record.itsPhone" v-if="record.itsPhone">国旅：{{ record.itsPhone }}</p>
            </template>
            <template v-if="column.dataIndex === 'mergeSuccessFlag'">
              <div>{{ record.mergeSuccessFlag?"合并成功":"合并失败" }}</div>
            </template>
            <template v-if="column.dataIndex === 'jingweidu'">
              <div
                v-if="record.providerLongitude"
              >经度：{{record.providerLongitude}} <br> 纬度：{{record.providerLatitude}}</div>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button v-if="!record.mergeSuccessFlag" @click="associationIts(record)" type="link">关联</h-button>
            </template>
          </template>
        </h-table>
      </div>
    </h-modal>
  </div>
</template>

<style scoped lang="less">
.pageBox {
  padding: 0 8px;
  .headerBox {
    width: 100%;
    // height: 60px;
    background: #fff;
    display: flex;
    align-items: center;
    padding: 24px;
  }
  .inputBox {
    display: flex;
    align-items: center;
    // margin-left: 60px;
  }
  .contentBox {
    margin-top: 16px;
    background: #fff;
    height: calc(100vh - 350px);
  }
}
.modalHeaderBox {
  width: 100%;
  // display: flex;
}
::v-deep .table-color-error {
  background-color: rgba(233, 61, 181, 0.2)!important;
}
.score{
  font-weight: 600;
}

table {
  border-collapse: collapse; /* 合并表格边框 */
  width:1100px; /* 设置表格宽度 */
}

.tableHead{
  width: 1100px;
  display: flex;
  justify-content: space-between;
  border:1px solid rgba(187,187,187,0.35);
  border-bottom: 0;
  padding: 3px;
  .index{
    width:70px;
    text-align: center;
    display: inline-block;
    font-weight: 600;
  }
  .mergeSuccessFlag{
    width:120px;
    text-align: center;
    display: inline-block;
  }
  .suspectedRemark{
    flex:1;
    display: inline-block;
    font-weight: 600;
  }
  .btn{
    width:80px;
    display: inline-block;
    text-align: right;
  }
}

thead{
  font-weight: 900;
  border:1px solid rgba(187,187,187,0.35);
}

table th, table td {
  padding: 3px; /* 设置单元格内边距 */
  text-align: left; /* 设置文本对齐方式 */
  border:1px solid rgba(187,187,187,0.35);
}

table th {
  background-color: #f2f2f2; /* 设置表头背景颜色 */
}

table tr:nth-child(even) {
  background-color: #f9f9f9; /* 设置偶数行背景颜色 */
}
</style>
