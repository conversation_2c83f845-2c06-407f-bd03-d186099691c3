<script lang="ts" setup>
  defineProps({
    title: {
      type: String,
      isRequired: true,
    },
    contentStyle: Object
  })
</script>

<template>
  <div class="common-box">
    <div class="common-box-header">
      <div class="title">{{title}}</div>
      <div class="options">
        <slot name="options"></slot>
      </div>
    </div>
    <div class="common-box-content" :style="contentStyle">
      <slot />
    </div>
  </div>
</template>

<style scoped lang="less">
  .common-box {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    .common-box-header {
      width: 100%;
      margin-bottom: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title {
        font-size: 16px;
        font-weight: 700;
      }
    }
  }
</style>
