<template>
    <div background="rgba(0,0,0,0)" :id="id" :style="{ height: props.height + 'vh' }"></div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import * as echarts from "echarts";
import { queryNonMinimumPrice } from "@haierbusiness-front/apis/src/data/board/travel";
import { circle2 as cicleOptions, colors } from "../../../data";
import { EventBus } from "../../../eventBus";
const props = defineProps({
    height: {
        type: Number,
        default: 35,
    },
});
const id = ref("cicle-" + Date.now());
const loading = ref(false);
let chartDom, myChart;
const payTypeCheck = ref<string>("");

onMounted(() => {
    queryData();

    chartDom = document.getElementById(id.value);
    myChart = echarts.init(chartDom as any, "dark");
    myChart.on("click", (param) => {
        if (param.from != "sfzdj" && param.name != payTypeCheck.value) {
            payTypeCheck.value = param.name;
            EventBus.emit("refresh", {
                ...param,
                from: "sfzdj",
            });
        } else {
            payTypeCheck.value = "";
            EventBus.emit("refresh");
        }
    });

});

EventBus.on((event, params) => {
    if (event == "refresh") {
        if (!params) queryData();
        if (params && params.from != "zklmc") queryData(params);

        if (params && params.from == "zklmc") {
            queryData().then(() => {
                myChart.dispatchAction({
                    type: "highlight",
                    seriesIndex: 0,
                    dataIndex: params.dataIndex,
                });
            });
        }
    }
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
    loading.value = true;
    const data = await queryNonMinimumPrice(
        params ? params.data.name : null,
        params ? params.from : null
    );
    loading.value = false;
    const rows = [] as Array<{
        name: string;
        value: string | number;
        money?: string | number;
    }>;
    const tooltipArray = ['是', '否'];
    tooltipArray.forEach((tooltipString) => {
        data.rows.forEach((item, index) => {
            if (tooltipString == item[0]) {
                rows.push({ value: item[1], name: item[0], money: item[2] });
                return;
            }
        });
    });
    const { series } = cicleOptions;
    series[0].color = colors;
    series[0].data = rows;
    myChart.clear();
    myChart.setOption({
        ...cicleOptions, tooltip: {
            trigger: "item", formatter: (each: any) => {
                return `损失金额：${each.data.money} <br> 数量：${each.data.value}`
            }
        }
    });
};
</script>
<style scoped lang="less"></style>
