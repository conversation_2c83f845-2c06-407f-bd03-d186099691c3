
<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="code">员工号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="code" v-model:value="searchKey.employeeId" placeholder="" autocomplete="off" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="name">员工姓名：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="name" v-model:value="searchKey.employeeName" placeholder="" autocomplete="off" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createTime">创建日期：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="searchKey.createTime" value-format="YYYY-MM-DD" style="width: 100%;" />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="handleReset">重置</h-button>
            <h-button type="primary" @click="onFilterChange">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: left; display: flex; align-items: center;">
            <h-button type="primary" @click="handleCreate" style="margin-right:10px">
              <PlusOutlined /> 新增员工
            </h-button>
            <h-upload name="file" :headers="obj" :action="action" @change="handleChange">
              <h-button>
                <upload-outlined></upload-outlined>
                导入
              </h-button>

              <template #itemRender="{ file, actions }">
              </template>
            </h-upload>
            <div style="margin-left:10px">
              <a @click="vipApi.download()">下载模板</a>
            </div>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="data"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="onPageChange">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'state'">
              <h-tag v-if="record.state === 1" color="success">正常</h-tag>
              <h-tag v-else color="error">冻结</h-tag>
            </template>
            <template v-if="column.dataIndex === 'operation'">
              <h-button type="link" @click="handleEdit(record)">编辑</h-button>
              <h-button type="link" @click="handleDelete(record.id)">删除</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>

  <div v-if="visible">
    <edit-dialog :show="visible" :data="editData" @cancel="onDialogClose" @ok="handleOk">
    </edit-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  Upload as hUpload,
  Badge as hBadge,
  Progress as hProgress,
  Button as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps
} from 'ant-design-vue';
import { reactive, ref, computed } from 'vue'
import { PlusOutlined, SearchOutlined, UploadOutlined } from '@ant-design/icons-vue'
import type { UploadChangeParam } from 'ant-design-vue';
import { HeaderConstant } from '@haierbusiness-front/common-libs';
import { reset } from '@haierbusiness-front/utils/src/commonUtil'


import { useSearch } from "../../composables/useSearch"
import {
  VipFilter,
  VipType
} from '@haierbusiness-front/common-libs';
import { vipApi } from '@haierbusiness-front/apis';

import EditDialog from './edit-dialog.vue'
import { useEditDialog } from "../../composables/useEditDialog"
import dayjs, { Dayjs } from 'dayjs'
import { useDelete } from "../../composables/useDelete"
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil'

const action = '/hb/data/api/datart/filter-roster/import'

const fileList = ref([])
const handleChange = (info: UploadChangeParam) => {
  if (info.file.status !== 'uploading') {
    console.log(info.file, info.fileList);
  }
  if (info.file.status === 'done') {
    message.success(`${info.file.name} file uploaded successfully`);

    onFilterChange()
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} file upload failed.`);
  }
};
const obj = { 'hb-token': loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false) }



const columns = [
  { title: "员工号", dataIndex: "employeeId" },
  { title: "员工姓名", dataIndex: "employeeName" },
  { title: "创建时间", dataIndex: "createTime" },
  { title: "操作", dataIndex: "operation" },
]

const searchKey = reactive({
  employeeId: '',
  employeeName: '',
  createTime: [] as any,
})


const handleReset = () => {
  reset(searchKey)
  onFilterChange()
}
const { data, onFilterChange, pagination, download, loading, onPageChange } = useSearch<VipType, VipFilter>(vipApi, searchKey)


const {
  visible,
  editData,
  handleCreate,
  handleEdit,
  onDialogClose,
  handleOk,
} = useEditDialog<VipType, VipFilter>(vipApi, "vip", onFilterChange)

const { handleDelete } = useDelete(vipApi, onFilterChange)

</script>


<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
