<!-- 保证金详情 -->

<script setup lang="ts">
import { ref, watch } from 'vue';
import { Modal as aModal, Spin as aSpin, Tooltip } from 'ant-design-vue';
import { bondApi } from '@haierbusiness-front/apis';
import { BondDetail } from '@haierbusiness-front/common-libs';
import { FileTypeConstant } from '@haierbusiness-front/common-libs';
import { formatNumberThousands } from '@haierbusiness-front/utils';

const props = defineProps<{
  visible: boolean;
  id?: number;
}>();

const emit = defineEmits(['cancel']);

const detail = ref<BondDetail | null>(null);
const loading = ref(false);

watch(
  () => props.id,
  async (newId) => {
    if (newId && props.visible) {
      loading.value = true;
      try {
        const res = await bondApi.get(newId);
        detail.value = res;
      } catch (error) {
        console.error('获取详情失败:', error);
      } finally {
        loading.value = false;
      }
    }
  },
  { immediate: true },
);

watch(
  () => props.visible,
  async (visible) => {
    if (visible && props.id) {
      loading.value = true;
      try {
        const res = await bondApi.get(props.id);
        detail.value = res;
      } catch (error) {
        console.error('获取详情失败:', error);
      } finally {
        loading.value = false;
      }
    }
  },
);
</script>

<template>
  <a-modal :visible="visible" title="详情" @cancel="$emit('cancel')" :footer="null" width="500px">
    <a-spin :spinning="loading">
      <div v-if="detail">
        <div class="detail-item">
          <span class="detail-label">单号：</span>
          <span class="detail-value">{{ detail.recordNo }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">操作类别：</span>
          <span class="detail-value">{{
            detail.type === 1 ? '保证金缴纳' : detail.type === 2 ? '保证金退款' : '-'
          }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">金额：</span>
          <span class="detail-value">{{ formatNumberThousands(detail.amount) }} 元</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">创建时间：</span>
          <span class="detail-value">{{ detail.gmtCreate }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">收款单号：</span>
          <span class="detail-value">{{ detail.sapReceiveNo || '-' }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">核对人：</span>
          <span class="detail-value">{{ detail.receiveName || '-' }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">核对时间：</span>
          <span class="detail-value">{{ detail.receiveTime || '-' }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">状态：</span>
          <span class="detail-value">{{
            detail.state === 10 ? '已完成' : detail.state === 11 ? '已驳回' : detail.state === 20 ? '核对中' : '-'
          }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">备注：</span>
          <Tooltip :title="detail.remark" placement="topLeft">
            <span class="remark-text">
              {{ detail.remark || '-' }}
            </span>
          </Tooltip>
        </div>
        <!-- <div class="detail-item">
          <span class="detail-label">消息：</span>
          <span class="detail-value">{{ detail.message || '-' }}</span>
        </div> -->

        <div v-if="detail.type === 1" class="detail-item">
          <span class="detail-label">{{ FileTypeConstant.PAY_PROVE.desc }}：</span>
          <div class="detail-value">
            <div v-if="detail.attachList && detail.attachList.length">
              <div
                v-for="(item, index) in detail.attachList.filter(
                  (item) => item.type === FileTypeConstant.PAY_PROVE.code,
                )"
                :key="index"
              >
                <a :href="item.path" target="_blank">{{ FileTypeConstant.PAY_PROVE.desc }} {{ index + 1 }}</a>
              </div>
            </div>
            <div v-else-if="detail.payPath && detail.payPath.length">
              <div v-for="(url, index) in detail.payPath" :key="index">
                <a :href="url" target="_blank">{{ FileTypeConstant.PAY_PROVE.desc }} {{ index + 1 }}</a>
              </div>
            </div>
            <span v-else>无</span>
          </div>
        </div>

        <div v-if="detail.type === 1" class="detail-item">
          <span class="detail-label">{{ FileTypeConstant.RECEIPT.desc }}：</span>
          <div class="detail-value">
            <div v-if="detail.attachList && detail.attachList.length">
              <div
                v-for="(item, index) in detail.attachList.filter((item) => item.type === FileTypeConstant.RECEIPT.code)"
                :key="index"
              >
                <a :href="item.path" target="_blank">{{ FileTypeConstant.RECEIPT.desc }} {{ index + 1 }}</a>
              </div>
            </div>
            <div v-else-if="detail.receiptPath && detail.receiptPath.length">
              <div v-for="(url, index) in detail.receiptPath" :key="index">
                <a :href="url" target="_blank">{{ FileTypeConstant.RECEIPT.desc }} {{ index + 1 }}</a>
              </div>
            </div>
            <span v-else>无</span>
          </div>
        </div>

        <div v-if="detail.type === 2" class="detail-item">
          <span class="detail-label">{{ FileTypeConstant.RECEIPT.desc }}：</span>
          <div class="detail-value">
            <div
              v-if="
                detail.attachList &&
                detail.attachList.filter((item) => item.type === FileTypeConstant.RECEIPT.code).length
              "
            >
              <div
                v-for="(item, index) in detail.attachList.filter((item) => item.type === FileTypeConstant.RECEIPT.code)"
                :key="'receipt-' + index"
              >
                <a :href="item.path" target="_blank">{{ FileTypeConstant.RECEIPT.desc }} {{ index + 1 }}</a>
              </div>
            </div>
            <span v-else>无</span>
          </div>
        </div>

        <div v-if="detail.type === 2" class="detail-item">
          <span class="detail-label">{{ FileTypeConstant.REFUND_APPLY.desc }}：</span>
          <div class="detail-value">
            <div
              v-if="
                detail.attachList &&
                detail.attachList.filter((item) => item.type === FileTypeConstant.REFUND_APPLY.code).length
              "
            >
              <div
                v-for="(item, index) in detail.attachList.filter(
                  (item) => item.type === FileTypeConstant.REFUND_APPLY.code,
                )"
                :key="'refund-' + index"
              >
                <a :href="item.path" target="_blank">{{ FileTypeConstant.REFUND_APPLY.desc }} {{ index + 1 }}</a>
              </div>
            </div>
            <div v-else-if="detail.refundPath && detail.refundPath.length">
              <div v-for="(url, index) in detail.refundPath" :key="index">
                <a :href="url" target="_blank">退款凭证 {{ index + 1 }}</a>
              </div>
            </div>
            <span v-else>无</span>
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<style scoped>
.detail-item {
  margin-bottom: 12px;
  display: flex;
}

.detail-label {
  width: 100px;
  text-align: right;
  color: #666;
  padding-right: 12px;
}

.detail-value {
  flex: 1;
}
.remark-text {
  display: inline-block;
  max-width: 330px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
</style>