<script lang="ts" setup>
import {computed, createVNode, onMounted, ref, watch} from 'vue';

import Pdfh5 from "pdfh5";
import "pdfh5/css/pdfh5.css";

import type {UploadProps} from 'ant-design-vue';
import {
  Affix as hAffix,
  <PERSON><PERSON> as h<PERSON><PERSON><PERSON>,
  <PERSON><PERSON> as hButton,
  Checkbox as hCheckbox,
  Col as hCol,
  Divider as hDivider,
  Drawer as hDrawer,
  Form as hForm,
  FormItem as hFormItem,
  Image as hImage,
  ImagePreviewGroup as hImagePreviewGroup,
  Input as hInput,
  message,
  Modal,
  Pagination as hPagination,
  Radio as hRadio,
  RadioButton as hRadioButton,
  RadioGroup as hRadioGroup,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Space as hSpace,
  Spin as hSpin,
  Tag as hTag,
  Textarea as hTextarea,
  Tooltip as hTooltip,
  Upload as hUpload,
  Upload
} from 'ant-design-vue';
import dayjs, {Dayjs} from 'dayjs';
import {CloseOutlined, PlusOutlined, SwapRightOutlined,} from '@ant-design/icons-vue';
import type {Rule} from 'ant-design-vue/es/form';
import {
  IHelperListRes,
  IHelperReq,
  IHelperSearchParam,
  IHelperStateTagColorMap,
  IHelperStatusEnum,
  IHelperThingsTypeEnum,
  IHelperThingsTypeTagColorMap,
  IHelperWeightTypeEnum,
  UserFlightTicketDTO,
  UserTicketResponseDTO
} from '@haierbusiness-front/common-libs';

import {fileApi, helperApi} from '@haierbusiness-front/apis';
import {applicationStore} from '@haierbusiness-front/utils/src/store/applicaiton';
import {storeToRefs} from 'pinia';

import relativeTime from 'dayjs/plugin/relativeTime';
import {getCurrentRoute, getCurrentRouter, getEnumOptions} from '@haierbusiness-front/utils';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';
import {showDialog, showSuccessToast, showToast} from "vant";

const route = ref(getCurrentRoute());
const router = getCurrentRouter();

const id = route.value?.query?.id;

dayjs.extend(relativeTime);

const store = applicationStore();
const {loginUser} = storeToRefs(store);
console.log('🚀 ~ loginUser:', loginUser);

const labelCol = {span: 6};
const wrapperCol = {span: 18};

const searchKey = ref<IHelperSearchParam>({
  pageNum: 1,
  pageSize: 6,
  total: 0,
  piggybackStatus: 10,
})

const helperList = ref<Array<IHelperListRes>>([])

// 物品类型
const thingsList = computed(() => {
  return getEnumOptions(IHelperThingsTypeEnum, true);
});
// 物品重量
const weightList = computed(() => {
  return getEnumOptions(IHelperWeightTypeEnum, true);
});

// 发布需求--------------
const showDrawer = ref(false)
const addDemand = () => {

  showDialog({
    title: '声明',
    messageAlign: 'left',
    confirmButtonText: "我知道了",
    message: `       本平台仅为集团内员工提供信息交流的平台，帮助匹配带物需求与同行程资源，是否达成带物意向由员工相互沟通确定，本平台对带物双方均不加以控制，亦不介入带物的过程，带物过程中产生的一切纠纷由员工自行协商解决，与本平台无关，本平台不参与调解。发布需求即同意并遵守本声明。`,
  }).then(() => {
    showDrawer.value = true;
  });

}

// 隐私政策、服务协议
const fwxypdf = new URL('@/assets/fwxy.pdf', import.meta.url).href
const yszcpdf = new URL('@/assets/yszc.pdf', import.meta.url).href

const fwxypdfRef = ref(null);
const yszcpdfRef = ref(null);

const showYszcPopup = ref(false)
const showFwxyPopup = ref(false)

const LoadYszcPdf = function () {

  const pdfh5Yszc = new Pdfh5(yszcpdfRef.value, {
    pdfurl: yszcpdf,
  });

  pdfh5Yszc.on("complete", (status, msg, time) => {
    showYszcPopup.value = true
  });
};

const LoadFwxyPdf = function () {

  const pdfh5Fwxy = new Pdfh5(fwxypdfRef.value, {
    pdfurl: fwxypdf,
  });
  pdfh5Fwxy.on("complete", (status, msg, time) => {
    showFwxyPopup.value = true
  });
}

const labelColAdd = {span: 4};
const wrapperColAdd = {span: 20};

const closeDrawer = () => {
  formState.value = {
    timeRange: [],
    files: []
  }
  showDrawer.value = false
}

const formState = ref<IHelperReq>({
  timeRange: [],
  files: []
})
const rules: Record<string, Rule[]> = {
  creatorPhone: [{required: true, message: '请输入手机号', trigger: 'change'}, {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入有效的电话号码',
    trigger: 'blur'
  }],
  timeRange: [{required: true, message: '请选择时间', trigger: 'change'}],
  fromCityName: [{required: true, message: '请选择出发城市', trigger: 'change'}],
  destCityName: [{required: true, message: '请选择送达城市', trigger: 'change'}],
  pickupAddress: [{required: true, message: '请输入详细出发地址', trigger: 'change'}],
  destAddress: [{required: true, message: '请输入详细送达地址', trigger: 'change'}],
  objectType: [{required: true, message: '请选择物品类型', trigger: 'change'}],
  objectWeightRange: [{required: true, message: '请选择物品重量', trigger: 'change'}],
  remember: [{required: true, message: '请阅读并接受《隐私政策》和《服务协议》', trigger: 'change'}],
};

const rangePresets = ref([
  {label: '最近一周', value: [dayjs().add(-7, 'd'), dayjs()]},
  {label: '最近14天', value: [dayjs().add(-14, 'd'), dayjs()]},
  {label: '最近一月', value: [dayjs().add(-30, 'd'), dayjs()]},
  {label: '最近三月', value: [dayjs().add(-90, 'd'), dayjs()]},
]);
type RangeValue = [Dayjs, Dayjs];

const onRangeChange = (dates: RangeValue, dateStrings: string[]) => {
  if (dates) {
    formState.value.expectTimeFrom = dateStrings[0]
    formState.value.expectTimeTo = dateStrings[1]
    console.log('From: ', dateStrings[0], ', to: ', dateStrings[1]);
  } else {
    console.log('Clear');
    formState.value.expectTimeFrom = ''
    formState.value.expectTimeTo = ''
  }
};

const onSearchRangeChange = (dates: RangeValue, dateStrings: string[]) => {
  if (dates) {
    searchKey.value.expectTimeFrom = dateStrings[0]
    searchKey.value.expectTimeTo = dateStrings[1]
    console.log('From: ', dateStrings[0], ', to: ', dateStrings[1]);
  } else {
    console.log('Clear');
    searchKey.value.expectTimeFrom = ''
    searchKey.value.expectTimeTo = ''
  }
};

const chosedBeginCity = (city: any) => {
  if (!city) {
    formState.value.fromCityName = '';
    formState.value.fromCityCode = '';
  }
  formState.value.fromCityName = city.name;
  formState.value.fromCityCode = city.areaCode;

}

const chosedEndCity = (city: any) => {
  if (!city) {
    formState.value.destCityName = '';
    formState.value.destCityCode = '';
  }
  formState.value.destCityName = city.name;
  formState.value.destCityCode = city.areaCode;

}

const formRef = ref()
const addLoading = ref<boolean>(false)
const showNews = ref<boolean>(false)
const showNewsModal = () => {
  showNews.value = true
}
const dwxzOk = () => {
  formState.value.remember = true;
}
const onSubmit = () => {

  formRef.value
      .validate()
      .then(() => {
        if (!formState.value.remember) {
          showToast('请阅读并同意《隐私政策》和《服务协议》')
          return
        }

        addLoading.value = true
        formState.value.createUserName = loginUser.value?.nickName
        // if (formState.value.id) {
        //   formState.value.piggybackStatus = 10
        //   helperApi.update(formState.value).then((res: any) => {
        //     addLoading.value = false
        //     closeDrawer()
        //     getList(1)
        //   }).catch(() => {
        //     addLoading.value = false
        //   })

        // }else {
        //   helperApi.create(formState.value).then((res: any) => {
        //     addLoading.value = false
        //     closeDrawer()
        //     getList(1)
        //   }).catch(() => {
        //     addLoading.value = false
        //   })
        // }
        formState.value.id = ''
        formState.value.piggybackStatus = 10
        helperApi.create(formState.value).then((res: any) => {
          addLoading.value = false
          closeDrawer()
          showSuccessToast('发布成功')
          getList(1)
        }).catch(() => {
          addLoading.value = false
        })

      })
      .catch((error: any) => {
      });
};

const tabValue = ref(1)

const checkedVal = ref<boolean>(false)
watch(
    checkedVal,
    (newVal, oldVal) => {
      searchKey.value.onlyMatchFlag = newVal ? 1 : 0
      getList(1)
    }
)

watch(
    tabValue,
    (newVal, oldVal) => {
      if (newVal !== oldVal) {
        searchKey.value = {
          pageNum: 1,
          pageSize: 6,
          total: 0,
          piggybackStatus: '',

        }
        switch (newVal) {
          case 1:
            searchKey.value.createUser = ''
            searchKey.value.acceptUser = ''
            searchKey.value.piggybackStatus = 10

            break;
          case 2:
            searchKey.value.createUser = loginUser.value?.username
            searchKey.value.acceptUser = ''
            searchKey.value.piggybackStatus = ''

            break;
          case 3:
            searchKey.value.createUser = ''
            searchKey.value.acceptUser = loginUser.value?.username
            searchKey.value.piggybackStatus = ''
            break;

          default:
            break;
        }
        if (checkedVal.value == true) {
          checkedVal.value = false
        } else {
          getList(1)

        }
      }
    }
)
const uploadLoading = ref(false);
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

const upload = (options: any) => {
  uploadLoading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);
  fileApi
      .upload(formData)
      .then((it) => {
        options.file.fileUrl = baseUrl + it.path;
        options.file.url = baseUrl + it.path;
        options.file.fileName = options.file.name;
        options.onProgress(100);
        options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
      })
      .finally(() => {
        uploadLoading.value = false;
      });
};

const acceptedFileTypes = 'image/*'
const beforeUpload = (file: UploadProps['fileList'][number]) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
  if (!isJpgOrPng) {
    message.error('请上传图片类型文件!');
  }

  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('图片大小不能超过 2MB!');
  }

  return (isJpgOrPng && isLt2M) || Upload.LIST_IGNORE
};


// 发布需求--------------

// 需求列表相关-------------
// 0 默认 1 asc 2 desc
const demandStatus = ref(0)
const deliveryTimeStatus = ref(0)
const createTimeStatus = ref(0)

// 改变排序
const handleStatusChange = () => {
  setOrderList()
  getList(1)
}
watch(demandStatus, handleStatusChange)
watch(deliveryTimeStatus, handleStatusChange)
watch(createTimeStatus, handleStatusChange)

const setOrderList = () => {
  const orderByItem = [
    {
      fieldName: 'piggybackStatus',
      order: demandStatus.value == 0 ? '' : demandStatus.value == 1 ? 'asc' : 'desc'
    },
    {
      fieldName: 'expectTimeTo',
      order: deliveryTimeStatus.value == 0 ? '' : deliveryTimeStatus.value == 1 ? 'asc' : 'desc'
    },
    {
      fieldName: 'createTime',
      order: createTimeStatus.value == 0 ? '' : createTimeStatus.value == 1 ? 'asc' : 'desc'
    }
  ]
  searchKey.value.orderByItemList = orderByItem
}

const setDemandStatus = () => {
  if (demandStatus.value != 2) {
    demandStatus.value++
  } else {
    demandStatus.value = 0
  }
}

const setDeliveryTimeStatus = () => {
  if (deliveryTimeStatus.value != 2) {
    deliveryTimeStatus.value++
  } else {
    deliveryTimeStatus.value = 0
  }
}

const setCreateTimeStatus = () => {
  if (createTimeStatus.value != 2) {
    createTimeStatus.value++
  } else {
    createTimeStatus.value = 0
  }
}

const listLoading = ref(false)
const handleReset = () => {
  searchKey.value = {
    pageNum: 1,
    pageSize: 6,
    piggybackStatus: 10,
    total: 0
  }
  getList(1)
}
const getList = (page?: number) => {
  if (page) {
    searchKey.value.pageNum = page
  }
  listLoading.value = true
  helperApi.page(searchKey.value).then((res: any) => {
    helperList.value = res.records
    searchKey.value.total = res.total
    listLoading.value = false
  })
}

const sizeChange = (page: number) => {
  getList(page)
}

// 联系接受人
const contactAcceptor = (item: any) => {
  helperApi.contactAcceptor(item.id).then(res => {
    window.open(feishuUrl + '?openId=' + res)
  })
}

// 联系咨询人
const contactService = (item: any) => {
  helperApi.getContactsOpenid(item.id).then(res => {
    window.open(feishuUrl + '?openId=' + res)
  })
}

// 取消需求

const cancelAccept = (detail: any) => {
  helperApi.cancelAccept(detail.id).then(res => {
    message.success('取消需求成功')
    detailVisible.value = false
    getList()
  })
}
const deleteAccept = (detail: any) => {
  helperApi.deleteById(detail.id).then(res => {
    message.success('删除需求成功')
    getList()
  })
}

// 再次发布
const copyAccept = (item: any) => {
  addLoading.value = true
  helperApi.getById(item.id).then((res: any) => {
    formState.value = res
    // 初始化数据
    formState.value.timeRange = [formState.value.expectTimeFrom, formState.value.expectTimeTo]
    formState.value.objectWeightRange = String(formState.value.objectWeightRange)
    formState.value.objectType = String(formState.value.objectType)
    // formState.value.id = ''
    formState.value.files?.forEach(item => {
      item.url = item.fileUrl
      item.name = item.fileName

    })
    addDemand()
    addLoading.value = false
  })

}

const feishuUrl = import.meta.env.VITE_FEI_SHU_URL;

// 联系发布人 / 接受需求
// acceptStatus 联系10 接受20  acceptUserCode 当前人工号
const acceptDemand = (item: any, type: number) => {
  const params = {
    piggybackId: item.id,
    acceptStatus: type,
    acceptUserCode: loginUser.value?.username,
    acceptUserName: loginUser.value?.nickName

  }
  if (type == 20) {
    Modal.confirm({
      title: '确认接受此带物需求吗?',
      content: createVNode('div', {style: 'color:red;'}, '请确认已与需求发布人沟通达成一致;接受需求后,如果行程发生变化,请主动与需求发布人沟通'),
      onOk() {
        acceptRequirement(params, type)
      },
      onCancel() {
        console.log('Cancel');
      },
      class: 'test',
    });
  } else {
    acceptRequirement(params, type)
  }

}

// 联系发布人 / 接受需求
const acceptRequirement = (params: any, type: number) => {
  helperApi.acceptRequirement(params).then(res => {
    if (type == 20) {
      getList()
      message.success('接受需求成功')
      detailVisible.value = false
    } else {
      window.open(feishuUrl + '?openId=' + res)
    }
  })
}

// 删除一条已取消的沟通记录
const deleteRecord = (item: any) => {
  const params = {
    piggybackId: item.id,
    acceptUserCode: loginUser.value?.username
  }
  helperApi.deleteRecord(params).then(res => {
    message.success('删除成功')
    getList()
  })
}

const detailVisible = ref(false)
// 展示详情
const showDetail = (item: any) => {
  getDetail(item.id)
  detailVisible.value = true

}
const closeDetailDrawer = () => {
  detailVisible.value = false

}

// 需求列表相关-----------

// 需求详情 ---------
const detail = ref<IHelperReq>()
const detailLoading = ref(false)

const getDetail = (id: string) => {
  detailLoading.value = true
  helperApi.getById(id).then((res: any) => {
    res.acceptObj = res?.acceptRecords.find((item: any) => item.acceptStatus == 20)
    detail.value = res
    detailLoading.value = false
  }).catch((err: any) => {
    detailLoading.value = false
  })
}

// 需求详情 ---------

// 获取用户机票信息
const userTicketsList = ref<UserFlightTicketDTO>([])
const userTickets = () => {
  helperApi.userTickets().then((res: UserTicketResponseDTO) => {
    userTicketsList.value = [...res.userFlightTicketDTOList, ...res.userTrainTicketDTOList]
  })
}


onMounted(() => {
  userTickets()
  getList(1)
});
</script>

<template>
  <div class="container">
    <div class="row flex">
      <div class="apply-con flex">
        <!-- 背景图 -->
        <h-row class="banner-img" justify="center" align="middle">
        </h-row>
        <!-- tab -->
        <h-row style="padding: 15px 0 ">
          <h-col :span="18">
            <h-radio-group v-model:value="tabValue" button-style="solid" size="large">
              <h-space wrap>
                <h-radio-button class="white-btn" size="large" :value="1">带物需求</h-radio-button>
                <h-radio-button class="white-btn" size="large" :value="2">我发布的</h-radio-button>
                <h-radio-button class="white-btn" size="large" :value="3">我接受的</h-radio-button>
              </h-space>
            </h-radio-group>
          </h-col>
          <h-col :span="6" style="text-align: right;">
            <h-button @click="addDemand" style="width: 160px; background-color: rgba(0, 115, 229, 1);" type="primary"
                      shape="round" size="large">
              <template #icon>
                <PlusOutlined/>
              </template>
              发布需求
            </h-button>
          </h-col>
        </h-row>

        <!-- 我的行程 -->
        <h-row v-if="tabValue == 1" class="my-trip-trip">
          <h-col :span="12" class="my-trip-item" v-for="(trip,index) in userTicketsList" :key="index">
            <!-- 出发城市 -->
            <div class="trip-item">
              <div class="item-address">
                {{ trip?.jpCfcityMc || trip?.cfcsmc }}
              </div>
              <div class="item-time">
                {{
                  trip?.jpCfsj ? dayjs(trip.jpCfsj).format(' M月D日 HH:mm') : dayjs(trip.cfrq).format(' M月D日 HH:mm')
                }}
              </div>
            </div>
            <!-- 图标 -->
            <SwapRightOutlined class="city-to" style="font-size: 30px"/>

            <!-- 到达城市 -->
            <div class="trip-item">
              <div class="item-address">
                {{ trip?.jpDdcityMc || trip?.ddcsmc }}
              </div>
              <div class="item-time">
                {{
                  trip?.jpDdsj ? dayjs(trip.jpDdsj).format(' M月D日 HH:mm') : dayjs(trip.ddrq).format(' M月D日 HH:mm')
                }}
              </div>
            </div>

          </h-col>
        </h-row>


        <!-- 条件查询 -->
        <h-row v-if="tabValue == 1" style="margin-bottom: 20px">
          <h-form ref="from" :model="searchKey" @finish="onReFilterChange" style="width: 100%" :label-col="labelCol"
                  :wrapper-col="wrapperCol">
            <div style="background-color: #fff; padding: 20px;">
              <h-row>
                <h-col :span="5">
                  <h-form-item :labelCol="{span:8}" label="出发城市" name="fromCityName">
                    <h-input autocomplete="off" v-model:value="searchKey.fromCityName" placeholder="出发城市"
                             allow-clear/>
                  </h-form-item>
                </h-col>

                <h-col :span="5">
                  <h-form-item :labelCol="{span:8}" label="到达城市" name="destCityName">
                    <h-input autocomplete="off" v-model:value="searchKey.destCityName" placeholder="到达城市"
                             allow-clear/>
                  </h-form-item>
                </h-col>

                <h-col :span="8">
                  <h-form-item label="送达时间" name="expectTimeTo">
                    <!-- <h-date-picker v-model:value="searchKey.expectTimeTo" value-format="YYYY-MM-DD" @change="onTimeChange"
                      style="width: 100%" /> -->
                    <h-range-picker v-model:value="searchKey.timeRange" style="width: 100%"
                                    :placeholder="['开始时间', '结束时间']" valueFormat="YYYY-MM-DD" format="YYYY-MM-DD"
                                    @change="onSearchRangeChange"/>
                  </h-form-item>
                </h-col>

                <h-col :span="6">
                  <h-form-item has-feedback label="物品类型" name="type">
                    <h-select v-model:value="searchKey.objectType" placeholder="物品类型">
                      <h-select-option value="">全部</h-select-option>
                      <h-select-option v-for="(item, index) in thingsList" :key="index" :value="item.value">{{
                          item.label
                        }}
                      </h-select-option>
                    </h-select>
                  </h-form-item>
                </h-col>
              </h-row>

              <h-row style="display: flex; background-color: #fff; justify-content: flex-end">
                <h-col
                    style="display: flex; align-items: center; font-size: 14px; color: #8c8c8c; justify-content: center;"
                    class="mr-20">
                  共{{ searchKey.total }}条
                </h-col>
                <h-col>
                  <h-space>
                    <h-button style="width: 94px; " shape="round" @click="handleReset">
                      重置
                    </h-button>

                    <h-button style="width: 94px; background-color: rgba(0, 115, 229, 1);" type="primary"
                              @click="getList(1)" shape="round">
                      查询
                    </h-button>
                  </h-space>
                </h-col>

              </h-row>
            </div>


            <!-- 只看出差行程匹配信息 -->
            <h-row v-if="userTicketsList.length > 0 && tabValue == 1" style="margin-top: 20px">
              <h-col :span="24">
                <a-checkbox v-model:checked="checkedVal">只看出差行程匹配信息</a-checkbox>
              </h-col>
            </h-row>

          </h-form>
        </h-row>


        <!-- 排序 -->
        <h-space style="margin-bottom: 10px" v-else>
          <h-button type="text" @click="setDemandStatus">
            <div class="order-btn-box">
              <span class="order-btn-text" :class="{ 'active': demandStatus != 0 }">需求状态</span>
              <span class="order-btn">
                <span :class="{ 'active': demandStatus == 1 }" class="order-btn-top"></span>
                <span :class="{ 'active': demandStatus == 2 }" class="order-btn-bottom"></span>
              </span>
            </div>
          </h-button>
          <h-button type="text" @click="setDeliveryTimeStatus">
            <div class="order-btn-box">
              <span class="order-btn-text" :class="{ 'active': deliveryTimeStatus != 0 }">送达时间</span>
              <span class="order-btn">
                <span :class="{ 'active': deliveryTimeStatus == 1 }" class="order-btn-top"></span>
                <span :class="{ 'active': deliveryTimeStatus == 2 }" class="order-btn-bottom"></span>
              </span>
            </div>
          </h-button>
          <h-button type="text" @click="setCreateTimeStatus">
            <div class="order-btn-box">
              <span class="order-btn-text" :class="{ 'active': createTimeStatus != 0 }">发布时间</span>
              <span class="order-btn">
                <span :class="{ 'active': createTimeStatus == 1 }" class="order-btn-top"></span>
                <span :class="{ 'active': createTimeStatus == 2 }" class="order-btn-bottom"></span>
              </span>
            </div>
          </h-button>
        </h-space>
        <!-- 列表 -->
        <h-spin :spinning="listLoading">
          <h-row class="box-height" style="  align-content: flex-start; "
                 v-if="helperList && helperList.length > 0">
            <h-row v-for="item, index in helperList" :key="index" class="helper-list " justify="space-between"
                   :class="item.piggybackStatus == 30 ? 'helper-list-act':''"
                   @click="showDetail(item)">
              <h-col>
                <h-row style="margin-bottom: 14px;">
                  <h-col>
                    <h-tag :color="IHelperThingsTypeTagColorMap[item.objectType]" :bordered="false">
                      {{ IHelperThingsTypeEnum[item.objectType] }}
                    </h-tag>
                  </h-col>
                  <h-col class="item-time">
                    期望送达:{{ dayjs(item.expectTimeFrom).format(' M月D日 HH:mm') }} - {{
                      dayjs(item.expectTimeTo).format('M月D日 HH:mm')
                    }}
                  </h-col>
                </h-row>
                <h-row>
                  <h-col class="city-font mr-20">
                    {{ item.fromCityName }}
                  </h-col>
                  <h-col class="mr-20 city-to">
                    <SwapRightOutlined class="city-to" style="font-size: 30px"/>
                  </h-col>
                  <h-col class="city-font">
                    {{ item.destCityName }}
                  </h-col>
                </h-row>
              </h-col>
              <h-col>
                <h-row v-if="tabValue != 1" :style="{color: IHelperStateTagColorMap[item.piggybackStatus]}"
                       style="margin-bottom: 14px; justify-content: flex-end;">
                  {{ IHelperStatusEnum[item.piggybackStatus] }}
                </h-row>
                <h-row style="height: 32px; min-width: 10px">
                  <h-space>
                    <!-- 待物需求 -->
                    <!-- 自己发布的不展示操作按钮 -->
                    <template v-if="tabValue == 1 && item.createUser != loginUser?.username">
                      <h-button class="list-btn-big" shape="round" @click.stop="acceptDemand(item, 10)">
                        <div class="flex-center">
                          <span class="icon-chart mr-5"></span>
                          联系发布人
                        </div>
                      </h-button>
                      <h-button @click.stop="acceptDemand(item,20)"
                                style="width: 94px; background-color: rgba(0, 115, 229, 1);" type="primary"
                                shape="round">
                        接受需求
                      </h-button>
                    </template>

                    <template v-if="tabValue == 2">
                      <h-button v-if="item.piggybackStatus == 20" class="list-btn-big" shape="round"
                                @click.stop="contactAcceptor(item)">
                        <div class="flex-center">
                          <span class="icon-chart mr-5"></span>
                          联系接受人
                        </div>
                      </h-button>

                      <a-badge :numberStyle="{width:'12px', height: '12px'}" :offset="[-5, 5]" :dot="true"
                               v-if="item.piggybackStatus == 10 && (item?.piggybackAcceptRecords?.length > 0)">
                        <h-button class="list-btn-large" shape="round"
                        >
                          <div class="flex-center">
                            <span class="icon-chart-white mr-5"></span>
                            已有{{ item?.piggybackAcceptRecords?.length || 0 }}人咨询
                          </div>
                        </h-button>
                      </a-badge>

                      <template v-if="item.piggybackStatus == 30 || item.piggybackStatus == 40">
                        <a-popconfirm
                            title="确认要删除这条数据吗?"
                            ok-text="确认"
                            cancel-text="取消"
                            @confirm="deleteAccept(item)"
                        >
                          <h-button @click.stop="" class="list--btn-small" shape="round">
                            删除
                          </h-button>
                        </a-popconfirm>

                        <h-button @click.stop="copyAccept(item)" class="list--btn-small" shape="round">
                          再次发布
                        </h-button>
                      </template>
                    </template>

                    <template v-if="tabValue == 3">
                      <h-button v-if="item.piggybackStatus == 30" class="list--btn-small" shape="round"
                                @click.stop="deleteRecord(item)">
                        删除
                      </h-button>
                      <h-button v-else class="list-btn-big" shape="round" @click.stop="acceptDemand(item, 10)">
                        <div class="flex-center">
                          <span class="icon-chart mr-5"></span>
                          联系发布人
                        </div>
                      </h-button>

                    </template>


                  </h-space>
                </h-row>

              </h-col>
            </h-row>
          </h-row>
          <!-- 暂无数据 -->
          <h-row class="empty-box box-height" v-else>
            <div class="empty">
              <h-row style="color: #595959;font-weight: bold;  font-size: 16px;margin-bottom:20px"
                     justify="center">暂未匹配到带物需求
              </h-row>
              <h-row class="mb-10" style="color: #8C8C8C; font-size: 14px" justify="center">
                您可直接发布带物需求
              </h-row>
              <h-row style="color: #8C8C8C" justify="center">
                平台会为您快速匹配顺路创客
              </h-row>
            </div>
          </h-row>
          <h-row justify="center" v-if="helperList && helperList.length > 0">
            <h-pagination v-model:current="searchKey.pageNum" :show-total="total => `共 ${total} 条`"
                          :page-size="searchKey.pageSize" @change="sizeChange" :total="searchKey.total"/>
          </h-row>
        </h-spin>
      </div>
    </div>

    <!-- 发布需求 -->
    <h-drawer :destroyOnClose="true" :bodyStyle="{ padding: 0 }" :maskClosable="false" :width="750" :closable="false"
              placement="right"
              :open="showDrawer" @close="closeDrawer">
      <template #title>
        <el-row style="display: flex; justify-content: space-between;">
          <el-col>
            发布需求
          </el-col>
          <el-col>
            <CloseOutlined @click="closeDrawer"/>
          </el-col>
        </el-row>
      </template>
      <h-spin :spinning="addLoading">

        <h-alert message="发布带物需求，为您匹配顺路同事，协助携带物品" type="info" show-icon/>
        <div style="padding: 20px;">
          <h-form :label-col="labelColAdd" :wrapper-col="wrapperColAdd" :model="formState" :rules="rules" ref="formRef"
                  :label-align="'left'">
            <!-- 基本信息 -->
            <h-row class="row-title">基本信息</h-row>
            <h-form-item label="联系电话" name="creatorPhone">
              <h-input v-model:value="formState.creatorPhone" autocomplete="off" placeholder="请输入"
                       style="width: 200px"/>
            </h-form-item>
            <h-form-item label="期望送达时间" name="timeRange">
              <h-range-picker v-model:value="formState.timeRange" style="width: 100%" show-time
                              :placeholder="['开始时间', '结束时间']" valueFormat="YYYY-MM-DD HH:mm:ss"
                              format="YYYY-MM-DD HH:mm:ss"
                              @change="onRangeChange"/>

            </h-form-item>

            <h-divider/>
            <!-- 地址信息 -->
            <h-row class="row-title flex-center">
              <span class="mr-5"
                    style="font-weight: 200; display: flex; align-items: center; font-size: 10px; justify-content: center; width: 16px; height: 16px;background:#52B4FF; border-radius: 16px; color: #fff; ">发</span>
              <span style="color: #2793F2;">出发地址</span>
            </h-row>

            <h-form-item label="出发城市" name="fromCityName">
              <city-chose :value="formState.fromCityName" @chosedCity="chosedBeginCity" placeholder="请选择"
                          :showInternational="false">
              </city-chose>
            </h-form-item>
            <h-form-item label="详细地址" name="pickupAddress">
              <h-textarea placeholder="请输入到门牌号，通邮地址" autocomplete="off"
                          v-model:value="formState.pickupAddress" show-count :maxlength="500"/>

              <!-- <h-input v-model:value="formState.pickupAddress" autocomplete="off" placeholder="请输入到门牌号，通邮地址" /> -->
            </h-form-item>

            <h-row class="row-title flex-center">
              <span class="mr-5"
                    style="font-weight: 200; display: flex; align-items: center; font-size: 10px; justify-content: center; width: 16px; height: 16px;background:#FAAD14; border-radius: 16px; color: #fff; ">达</span>
              <span style="color: #FAAD14;">送达地址</span>
            </h-row>

            <h-form-item label="送达城市" name="destCityName">
              <city-chose :value="formState.destCityName" @chosedCity="chosedEndCity" placeholder="请选择"
                          :showInternational="false">
              </city-chose>
            </h-form-item>
            <h-form-item label="详细地址" name="destAddress">
              <h-textarea placeholder="请输入到门牌号，通邮地址" autocomplete="off" v-model:value="formState.destAddress"
                          show-count :maxlength="500"/>

              <!-- <h-input v-model:value="formState.destAddress" autocomplete="off" placeholder="请输入到门牌号，通邮地址" /> -->
            </h-form-item>

            <h-divider/>

            <!-- 物品信息 -->
            <h-row class="row-title">物品信息</h-row>
            <h-form-item label="物品类型" name="objectType">
              <h-radio-group v-model:value="formState.objectType">
                <h-radio v-for="(item, index) in thingsList" :key="index" :value="item.value">{{ item.label }}</h-radio>
              </h-radio-group>
            </h-form-item>

            <h-form-item label="物品重量" name="objectWeightRange">
              <h-radio-group v-model:value="formState.objectWeightRange">
                <h-radio v-for="(item, index) in weightList" :key="index" :value="item.value">{{ item.label }}</h-radio>
              </h-radio-group>
            </h-form-item>

            <h-form-item label="物品描述" name="objectDesc">
              <h-textarea placeholder="请简短描述物品尺寸、运送注意事项等信息" v-model:value="formState.objectDesc"
                          show-count :maxlength="500"/>

              <!-- <h-input v-model:value="formState.objectDesc" autocomplete="off" placeholder="请简短描述物品尺寸、运送注意事项等信息" /> -->
            </h-form-item>

            <h-form-item label="图片附件">
              <h-upload :accept="acceptedFileTypes" v-model:file-list="formState.files" :before-upload="beforeUpload"
                        name="file" list-type="picture-card" :custom-request="upload">
                <div v-if="formState.files.length < 9">
                  <PlusOutlined/>
                  <div style="margin-top: 8px">上传图片</div>
                </div>
              </h-upload>

              <div class="upload-tip">图片大小不能超过2MB</div>
            </h-form-item>

            <h-divider/>

            <h-row style="color: #595959">
              温馨提示：带物请保证合法合规，不要让您的同事承担带物的法律风险，也别忘了感谢愿意帮助您的同事~
            </h-row>

            <h-divider/>

            <h-affix :offset-bottom="0">
              <h-row
                  style="display: flex; justify-content: space-between; align-items: center; height: 70px; background-color: #fff">
                <h-col>
<!--                  <h-checkbox v-model:checked="formState.remember">我已阅读并接受-->
<!--                    <span @click.stop="LoadYszcPdf" class="dwxz">《隐私政策》</span>-->
<!--                    <span class="tipsContent">和</span>-->
<!--                    <span @click.stop="LoadFwxyPdf" class="dwxz">《服务协议》</span>-->
<!--                  </h-checkbox>-->

                  <van-checkbox class="checkBox" iconSize="14px" v-model="formState.remember"> <span class="tipsContent">已阅读并接受</span> <span @click.stop="LoadYszcPdf" class="dwxz">《隐私政策》</span> <span class="tipsContent">和</span> <span @click.stop="LoadFwxyPdf" class="dwxz">《服务协议》</span> </van-checkbox>

                </h-col>
                <h-col>
                  <h-button type="primary" size="large" shape="round" @click="onSubmit">立即发布</h-button>
                </h-col>
              </h-row>
            </h-affix>

          </h-form>
        </div>
      </h-spin>

    </h-drawer>
    <!-- 需求详情 -->
    <h-drawer :bodyStyle="{ padding: 0, background: '#F6F7F9' }" :maskClosable="true" :width="750" :closable="false"
              placement="right" :open="detailVisible" @close="closeDetailDrawer">
      <template #title>
        <el-row style="display: flex; justify-content: space-between;">
          <el-col>
            需求详情
          </el-col>
          <el-col>
            <CloseOutlined @click="closeDetailDrawer"/>
          </el-col>
        </el-row>
      </template>
      <h-spin :spinning="detailLoading">
        <!-- <h-row v-if="tabValue==1" class="detail-row-info flex-center justify-content-between">
          <h-col>与您8月2日 青岛-北京 的行程</h-col>
          <h-col>匹配度:90%</h-col>
        </h-row> -->
        <h-row v-if="tabValue==2 && detail?.piggybackStatus == 10"
               class="detail-row-info flex-center bg-yellow justify-content-between">
          <h-col>正在为您寻找顺路创客，请保持通讯畅通…</h-col>
        </h-row>
        <div class="detail-box">
          <!-- 行程 -->
          <h-row>
            <h-col :span="11" class="detail-title">
              <h-row class="city mb-10">{{ detail?.fromCityName }}</h-row>
              <h-tooltip>
                <template #title>{{ detail?.pickupAddress }}</template>
                <div class="address">{{ detail?.pickupAddress }}</div>
              </h-tooltip>
            </h-col>
            <h-col :span="2" class="flex-center justify-content-center">
              <SwapRightOutlined style="font-size: 30px;"/>
            </h-col>
            <h-col :span="11" class="detail-title">
              <h-row class="city mb-10">{{ detail?.destCityName }}</h-row>
              <h-tooltip>
                <template #title>{{ detail?.destAddress }}</template>
                <div class="address">{{ detail?.destAddress }}</div>
              </h-tooltip>
            </h-col>
          </h-row>
          <!-- 需求 -->
          <div class="detail-content">
            <h-row class="detail-content-item">
              <h-col :span="4">发布时间:</h-col>
              <h-col :span="20">{{ detail?.createTime }}</h-col>
            </h-row>
            <h-row class="detail-content-item">
              <h-col :span="4">联系电话:</h-col>
              <h-col :span="20">{{ detail?.creatorPhone }}</h-col>
            </h-row>
            <h-row class="detail-content-item">
              <h-col :span="4">期望送达时间:</h-col>
              <h-col :span="20">{{ dayjs(detail?.expectTimeFrom).format(' M月D日 HH:mm') }} - {{
                  dayjs(detail?.expectTimeTo).format('M月D日 HH:mm')
                }}
              </h-col>
            </h-row>

            <h-row class="detail-content-item">
              <h-col :span="4">需求发布人:</h-col>
              <h-col :span="20" @click="acceptDemand(detail, 10)">{{
                  `${detail?.createUserName}(${detail?.createUser})`
                }}<span class="icon-chart ml-5 pointer"></span></h-col>
            </h-row>

            <h-row class="detail-content-item">
              <h-col :span="4">物品类型:</h-col>
              <h-col :span="20">{{ IHelperThingsTypeEnum[detail?.objectType] }}</h-col>
            </h-row>

            <h-row class="detail-content-item">
              <h-col :span="4">物品重量:</h-col>
              <h-col :span="20">{{ IHelperWeightTypeEnum[detail?.objectWeightRange] }}</h-col>
            </h-row>

            <h-row class="detail-content-item">
              <h-col :span="4">物品描述:</h-col>
              <h-col :span="20" style="word-break:break-all;">{{ detail?.objectDesc }}</h-col>

            </h-row>

            <h-row class="detail-content-item">
              <h-col :span="4">物品图片:</h-col>
              <h-col :span="20">
                <h-image-preview-group>
                  <h-space style="flex-wrap: wrap;">
                    <h-image class=" mr-10" v-for="item,index in detail?.files" :key="index" :width="80" :height="80"
                             :src="item.fileUrl"/>
                  </h-space>
                </h-image-preview-group>
              </h-col>
            </h-row>


          </div>

          <!-- 需求接受人 只能发布人查看 -->
          <div class="detail-content" v-if="loginUser?.username == detail?.createUser">
            <h-row class="detail-content-item">
              <h-col :span="4">需求接受人:</h-col>
              <h-col :span="20" class="pointer" v-if="detail?.acceptObj?.acceptUserCode">
                <span class="flex-center" style="height: 20px" @click="contactAcceptor(detail)">
                  <span style="margin-right: 5px; line-height: 20px;">
                    {{ `${detail?.acceptObj?.acceptUserName}(${detail?.acceptObj?.acceptUserCode})` }}
                  </span>
                  <span class="icon-chart pointer"></span>
                </span>
              </h-col>
              <h-col :span="20" v-else>
                <span style="color: #FF8133">匹配中</span>
              </h-col>
            </h-row>
          </div>

          <!-- 咨询记录 只能发布人查看-->
          <div class="detail-content" v-if="loginUser?.username == detail?.createUser">
            <h-row class="detail-content-item">
              <h-col :span="4">咨询记录</h-col>
            </h-row>
            <h-row class="detail-content-item pointer" @click="contactService(item)"
                   v-for="item,index in detail?.acceptRecords" :key="index">
              <h-col :span="8">{{ item.createTime }}</h-col>
              <h-col :span="16">
                <span class="flex-center" style="height: 20px">
                  <span style="margin-right: 5px; line-height: 20px;">
                    {{ `${item.acceptUserName}(${item.acceptUserCode})` }}
                  </span>
                  <span class="icon-chart pointer"></span>
                </span>
              </h-col>
            </h-row>
          </div>
        </div>

      </h-spin>
      <template #footer>
        <el-row style="display: flex; height: 60px; align-items: center; justify-content: flex-end;">
          <h-space style="display: flex; ">
            <h-button v-if="(tabValue==1 ||tabValue==3 ) && detail?.createUser != loginUser?.username"
                      class="list-btn-big" shape="round" @click.stop="acceptDemand(detail, 10)">
              <div class="flex-center">
                <span class="icon-chart pointer mr-5"></span>
                联系发布人
              </div>
            </h-button>

            <h-button @click.stop="acceptDemand(detail,20)"
                      v-if="tabValue==1  && detail?.createUser != loginUser?.username"
                      style="width: 94px; background-color: rgba(0, 115, 229, 1);" type="primary" shape="round">
              接受需求
            </h-button>
            <h-button @click.stop="cancelAccept(detail)"
                      v-if="tabValue==2 && detail?.piggybackStatus !=30 && detail?.piggybackStatus !=40"
                      style="width: 94px; background-color: #F5222D; color: #fff;" type="primary" shape="round">
              取消需求
            </h-button>
          </h-space>
        </el-row>

      </template>
    </h-drawer>


    <!-- 隐私政策 -->
    <van-popup :lazy-render="false" v-model:show="showYszcPopup" position="bottom" :style="{ height: '100%' }">
      <van-nav-bar
          title="创客帮平台隐私政策"
          left-arrow
          fixed
          :z-index="1000"
      >
        <template #left>
          <van-icon name="arrow-left" color="#000" @click="showYszcPopup=false" size="20"/>
        </template>
      </van-nav-bar>
      <div ref="yszcpdfRef" style="height:100%;"></div>
    </van-popup>

    <!-- 服务协议 -->
    <van-popup :lazy-render="false" v-model:show="showFwxyPopup" position="bottom" :style="{ height: '100%' }">
      <van-nav-bar
          title="创客帮平台服务协议"
          left-arrow
          fixed
          :z-index="1000"
      >
        <template #left>
          <van-icon name="arrow-left" color="#000" @click="showFwxyPopup=false" size="20"/>
        </template>
      </van-nav-bar>
      <div ref="fwxypdfRef" style="height:100%;"></div>
    </van-popup>
  </div>
</template>

<style lang="less" scoped>
@import url(./helper.less);

.dwxz {
  color: #0073E5;
}
</style>
