<script setup lang="ts">
import {Modal, Tag as hTag, <PERSON><PERSON> as hButton, Col as hCol, Row as hRow, Table as hTable} from 'ant-design-vue';
import {ColumnType} from 'ant-design-vue';
import {PlusOutlined, SearchOutlined} from '@ant-design/icons-vue';
import {AnnualPlanTypeStateConstant, IAnnualPlanTypeListResponse} from '@haierbusiness-front/common-libs';
import {getCurrentRouter} from '@haierbusiness-front/utils';
import {ref,createVNode} from 'vue';
import {dailyTypeApi} from '@haierbusiness-front/apis/src/daily/type/type';
import {IAnnualPlanTypeListRequest} from '@haierbusiness-front/common-libs/src/daily';
import typeModal from './typeModal.vue';

const router = getCurrentRouter()

const columns: ColumnType[] = [
  {
    title: '年度',
    dataIndex: 'year',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '类型',
    dataIndex: 'name',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '220px',
    fixed: 'right',
    align: 'center'
  },
];

const searchListParam = ref<IAnnualPlanTypeListRequest>({})
const tm = ref<typeModal>()
const listLoading = ref(false)
const listData = ref<IAnnualPlanTypeListResponse[]>([])

const listApiRun = () => {
  listLoading.value = true
  dailyTypeApi.list(searchListParam.value)
      .then((it) => {
        listData.value = it.reduce((acc: IAnnualPlanTypeListResponse[], item) => {
          let found = acc.find(i => i.year === item.year);
          if (found) {
            found.id += " / " + item.id;
            found.name += " / " + item.name;
            found.description += " / " + (item.description || "");
          } else {
            acc.push(item);
          }
          return acc;
        }, []);
      })
      .finally(() => {
        listLoading.value = false
      })
}
listApiRun()
const type = ref()
const typeData = ref<IAnnualPlanTypeListResponse>()

const addDailyType = () => {
  type.value = 2
  tm?.value.show()
}

const showDailyType = (data: IAnnualPlanTypeListResponse) => {
  type.value = 1
  typeData.value = data
  tm?.value.show()
}

const updateDailyType = (data: IAnnualPlanTypeListResponse) => {
  type.value = 3
  typeData.value = data
  tm?.value.show()
}

const updateDailyTypeStateLoading = ref(false)
const updateDailyTypeState = (data: IAnnualPlanTypeListResponse) => {
  Modal.confirm({
    title: '修改状态',
    content: createVNode('div', { style: 'color:red;max-height:450px;overflow-y: auto;' }, '确认生效后目标类型不可再修改！'),
    maskClosable: true,
    onOk() {
      updateDailyTypeStateLoading.value = true
      type.value = 3
      data.state = AnnualPlanTypeStateConstant.VALID.code
      typeData.value = data
      console.log(typeData.value)
      tm?.value.directSubmitChangeForm()
      updateDailyTypeStateLoading.value = false
    },
  })
}
</script>

<template>
  <type-modal ref="tm" @listApiRun="listApiRun()" :type="type"
              :type-data="typeData"
              :exist-years="listData.map((it)=>it.year)"></type-modal>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
            <h-button type="primary" @click="addDailyType">
              <PlusOutlined/>
              新增年度类型
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :pagination="false"
                 :data-source="listData" :scroll="{ y: 550 }" :loading="listLoading">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'state'">
              <h-tag v-if="record.state === AnnualPlanTypeStateConstant.IN_VALID.code" color="warning">{{
                  AnnualPlanTypeStateConstant.IN_VALID.desc
                }}
              </h-tag>
              <h-tag v-if="record.state === AnnualPlanTypeStateConstant.VALID.code" color="success">{{
                  AnnualPlanTypeStateConstant.VALID.desc
                }}
              </h-tag>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="showDailyType(record)">查看</h-button>
              <h-button type="link" v-if="record.state === AnnualPlanTypeStateConstant.IN_VALID.code"
                        @click="updateDailyType(record)">编辑
              </h-button>
              <h-button type="link" v-if="record.state === AnnualPlanTypeStateConstant.IN_VALID.code"
                        @click="updateDailyTypeState(record)">生效
              </h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
