
import { get, post } from '../request'
import { IPageResponse, PermissionFilter, PermissionType, Result } from '@haierbusiness-front/common-libs'


export const permissionApi = {

    list: (params: PermissionFilter): Promise<IPageResponse<PermissionType>> => {
        return get('data/api/datart/permission-approve/page', params)
    },

    save: (params: PermissionFilter): Promise<Result> => {
        return post('data/api/datart/filter-roster/create', params)
    },

    edit: (params: PermissionFilter): Promise<Result> => {
        return post('data/api/datart/filter-roster/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('data/api/datart/filter-roster/delete', { id })
    },

}